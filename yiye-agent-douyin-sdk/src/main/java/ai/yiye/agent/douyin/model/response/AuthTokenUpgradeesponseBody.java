package ai.yiye.agent.douyin.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 授权小程序接口调用凭据响应实体
 */

@Data
public class AuthTokenUpgradeesponseBody {

    //授权小程序接口调用凭据
    @JsonProperty("authorizer_access_token")
    private String authorizerAccessToken;

    //有效期 秒
    @JsonProperty("expires_in")
    private Integer expiresIn;

    //刷新令牌，用于刷新已授权用户的 authorizer_access_token
    @JsonProperty("authorizer_refresh_token")
    private String authorizerRefreshToken;

    //authorizer_refresh_token 的有效期，单位：秒
    @JsonProperty("refresh_expires_in")
    private Integer refreshExpiresIn;

    //授权小程序 appid
    @JsonProperty("authorizer_appid")
    private String authorizerAppid;

    //授权小程序在授权跳转页勾选的权限
    @JsonProperty("authorize_permission")
    private List<AuthorizePermissionResponseBody> authorizePermission;

}
