package ai.yiye.agent.douyin.model.response.version;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class AuditPackageVersionResponseBody extends CommonPackageVersionResponseBody{

    //申请的宿主端中，有哪些宿主端通过审核
    @JsonProperty("approvedApps")
    private List<String> approvedApps;

    //申请的宿主端中，有哪些宿主端没有通过审核
    @JsonProperty("notApprovedApps")
    private List<String> notApprovedApps;

    //当前审核版本是否是被下架时的那个版本，如果是则不能发布
    @JsonProperty("is_illegal_version")
    private Boolean isIllegalVersion;

    //是否已发布，0:否，1:是
    @JsonProperty("has_publish")
    private Integer hasPublish;

    //审核不通过原因
    @JsonProperty("reason")
    private String reason;

    //当前审核状态，0:审核中，1:通过，2:不通过，3：撤回审核
    @JsonProperty("status")
    private Integer status;
}
