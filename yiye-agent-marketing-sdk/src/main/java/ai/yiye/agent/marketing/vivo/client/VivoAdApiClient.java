package ai.yiye.agent.marketing.vivo.client;

import ai.yiye.agent.domain.AdvertiserAccount;
import ai.yiye.agent.marketing.MarketingThreadPool;
import ai.yiye.agent.marketing.ResponseValidator;
import ai.yiye.agent.marketing.aspect.StatisticTime;
import ai.yiye.agent.marketing.encode.JsonContentType;
import ai.yiye.agent.marketing.exception.MarketingApiException;
import ai.yiye.agent.marketing.vivo.VivoApiModule;
import ai.yiye.agent.marketing.vivo.response.VivoAdPaginationResponseBody;
import ai.yiye.agent.marketing.vivo.response.VivoApiResponse;
import ai.yiye.agent.marketing.vivo.response.VivoPageInfo;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Flux;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static ai.yiye.agent.marketing.ResponseValidator.checkResponse;

public interface VivoAdApiClient {

    Logger log = LoggerFactory.getLogger(VivoAdApiClient.class);
    int DEFAULT_PAGE_SIZE = 100;
    String PAGE_NAME = "pageIndex";
    String PAGE_SIZE_NAME = "pageSize";


    @RequestLine("GET /account/fetch?access_token={accessToken}&advertiser_id={accountId}")
    VivoApiResponse accountInfoInternal(@Param("accessToken") String accessToken,
                                        @Param("accountId") String accountId);


    //通用分页调用
    @JsonContentType
    @Headers("Content-Type: application/json")
    @RequestLine("POST /{module}?access_token={accessToken}&advertiser_id={accountId}")
    JSONObject getResponseInternal(
        @Param("module") String module,
        @Param("accountId") Object accountId,
        @Param("accessToken") String accessToken,
        @RequestBody Map<String, Object> params);

    //通用分页调用
    @JsonContentType
    @Headers("Content-Type: application/json")
    @RequestLine("POST /{module}?access_token={accessToken}&advertiser_id={accountId}")
    VivoApiResponse getResponseInternal(
        @Param("module") String module,
        @Param("accountId") Object accountId,
        @Param("accessToken") String accessToken,
        @RequestBody Object object);


    @StatisticTime
    default List<Object> getAll(VivoApiModule module, AdvertiserAccount account, Map<String, Object> params) {
        return this.getAll(module, account.getAccountId(), account.getAccessToken(), params);
    }

    /**
     * 获取分页接口所有数据
     *
     * @param module      模块名
     * @param accountId   id
     * @param accessToken token
     * @return 模块所有数据
     */
    default List<Object> getAll(VivoApiModule module, Object accountId, String accessToken, Map<String, Object> params) {
        // try to access first page
        VivoAdPaginationResponseBody body = this.get(module, 1, DEFAULT_PAGE_SIZE, accountId, accessToken, params);
        JSONArray result = body.getList();
        VivoPageInfo pageInfo = body.getPageInfo();
        if (pageInfo == null && !pageInfo.hasNext()) {
            return result;
        }

        List<CompletableFuture<JSONArray>> futures = IntStream
            .rangeClosed(2, pageInfo.getPageCount())
            .mapToObj(page -> CompletableFuture.supplyAsync(() ->
                get(module, page, DEFAULT_PAGE_SIZE, accountId, accessToken, params).getList(), MarketingThreadPool.THREAD_POOL)
            )
            .collect(Collectors.toList());
        // join results
        result.addAll(futures.stream().map(CompletableFuture::join).flatMap(Collection::stream).collect(Collectors.toList()));
        return result;
    }

    default VivoAdPaginationResponseBody get(VivoApiModule module, Integer page, Integer pageSize, Object accountId, String accessToken, Map<String, Object> params) {
        // 重新new一次map，否则公用同一个map，多个CompletableFuture之间参数会被污染
        Map<String, Object> realParamMap = new HashMap<>();
        if (params != null) {
            realParamMap.putAll(params);
        }
        realParamMap.put(PAGE_NAME, page);
        realParamMap.put(PAGE_SIZE_NAME, pageSize);
        VivoApiResponse vivoApiResponse = new VivoApiResponse();
        try {
            JSONObject response = this.getResponseInternal(module.getPath(), accountId, accessToken, realParamMap);
            vivoApiResponse = response.toJavaObject(VivoApiResponse.class);
            checkResponse(vivoApiResponse);
            return vivoApiResponse.getData().toJavaObject(VivoAdPaginationResponseBody.class);
        } catch (Exception e) {
            log.error("VivoAdApiClient调用get方法异常, module= {}, 投放账户id = {}, errorCode = {}, errorMessage = {}", module.name(), accountId,
                Optional.ofNullable(vivoApiResponse).map(r -> r.getCode()).orElse(null), e);
        }
        return new VivoAdPaginationResponseBody();
    }

    @StatisticTime
    default Flux<List<Object>> getPageAll(VivoApiModule module, AdvertiserAccount account, Map<String, Object> params) {
        return this.getPageAll(module, account.getAccountId(), account.getAccessToken(), params);
    }

    /**
     * 响应式处理分页
     *
     * @param module      模块名
     * @param accountId   id
     * @param accessToken token
     * @return 模块所有数据
     */
    default Flux<List<Object>> getPageAll(VivoApiModule module, Object accountId, String accessToken, Map<String, Object> params) {
        return Flux.create(fluxSink -> {
            Optional.ofNullable(module.getFields()).ifPresent(fields -> params.put("fields", fields));
            VivoAdPaginationResponseBody body = get(module, 1, DEFAULT_PAGE_SIZE, accountId, accessToken, params);
            JSONArray result = body.getList();
            VivoPageInfo pageInfo = body.getPageInfo();
            fluxSink.next(result);
            if (null != pageInfo && pageInfo.getPageCount() > 1) {
                Flux.range(2, pageInfo.getPageCount() - 1)
                    .subscribe(page -> {
                        try {
                            fluxSink.next(get(module, page, DEFAULT_PAGE_SIZE, accountId, accessToken, params).getList());
                        } catch (Throwable throwable) {
//                            fluxSink.error(throwable);
                        }
                    });
            }
        });
    }

    @StatisticTime
    default Flux<List<Object>> getPageAllVideos(VivoApiModule module, AdvertiserAccount account, Map<String, Object> params) {
        return this.getPageAllVideos(module, account.getAccountId(), account.getAccessToken(), params);
    }

    /**
     * 分部处理接口
     *
     * @param module      模块名
     * @param accountId   id
     * @param accessToken token
     * @return 模块所有数据
     */
    default Flux<List<Object>> getPageAllVideos(VivoApiModule module, Object accountId, String accessToken, Map<String, Object> params) {
        Integer pageCount = this.getPageCount(VivoApiModule.video, accountId, accessToken, params);
        return Flux.create(fluxSink -> {
            Flux.range(1, pageCount + 1)
                .subscribe(page -> {
                    try {
                        JSONObject obj = this.getJsonObject(module, page, DEFAULT_PAGE_SIZE, accountId, accessToken, params);
                        JSONArray array = null;
                        if (obj != null && (array = obj.getJSONArray("data")) != null) {
                            fluxSink.next(array.toJavaList(Object.class));
                        }
                    } catch (Throwable throwable) {
//                            fluxSink.error(throwable);
                    }
                });
        });
    }


    @StatisticTime
    default List<Object> getPageAllReport(VivoApiModule module, AdvertiserAccount account, Map<String, Object> params) {
        List<Object> response = new ArrayList<>();
        this.getPageAllReport(module, account, params, response);
        return response;
    }

    /**
     * 由于vivo的报表接口不会返回分页信息, 只能通过lastId去查找下一页的数据，故此处需要递归循环获取报表数据
     *
     * @param module
     * @param account
     * @param params
     * @param response
     */
    default void getPageAllReport(VivoApiModule module, AdvertiserAccount account, Map<String, Object> params, List<Object> response) {
        JSONObject obj = this.getJsonObject(module, null, DEFAULT_PAGE_SIZE, account.getAccountId(), account.getAccessToken(), params);
        if (obj != null && obj.getJSONObject("data") != null
            && obj.getJSONObject("data").getJSONArray("items") != null
            && obj.getJSONObject("data").getJSONArray("items").size() > 0) {
            response.addAll(obj.getJSONObject("data").getJSONArray("items"));
            if (!StringUtils.isEmpty(obj.getJSONObject("data").getString("lastId"))) {
                params.put("lastId", obj.getJSONObject("data").getString("lastId"));
                this.getPageAllReport(module, account, params, response);
            }
        }
        return;
    }

    default JSONObject getJsonObject(VivoApiModule module, Integer page, Integer pageSize, Object accountId, String accessToken, Map<String, Object> params) {
        // 重新new一次map，否则公用同一个map，多个CompletableFuture之间参数会被污染
        Map<String, Object> realParamMap = new HashMap<>();
        realParamMap.putAll(params);
        realParamMap.put(PAGE_NAME, page);
        realParamMap.put(PAGE_SIZE_NAME, pageSize);
        JSONObject json = null;
        try {
            json = getResponseInternal(module.getPath(), accountId, accessToken, realParamMap);
            if (Objects.nonNull(json) && json.getInteger("code") != null && json.getInteger("code") > ResponseValidator.VIVO_NORMAL_RESPONSE_CODE) {
                throw new MarketingApiException(json.getString("message"));
            }

        } catch (Exception e) {
            log.warn("VivoAdApiClient调用get方法异常，module= {}, 投放账户id = {}, errorCode = {}, errorMessage = {}", module.name(), accountId,
                Optional.ofNullable(json).map(r -> r.getString("code")).orElse(null),
                Optional.ofNullable(json).map(r -> r.getString("message")).orElse(""), e);
        }
        return json;
    }

    /**
     * 通过视频物料接口查询分页信息
     *
     * @param module
     * @param accountId
     * @param accessToken
     * @param params
     * @return
     */
    default Integer getPageCount(VivoApiModule module, Object accountId, String accessToken, Map<String, Object> params) {
        Map<String, Object> actualParams = new HashMap<>();
        actualParams.putAll(params);
        actualParams.put(PAGE_NAME, 1);
        actualParams.put(PAGE_SIZE_NAME, DEFAULT_PAGE_SIZE);
        try {
            JSONObject json = this.getResponseInternal(module.getPath(), accountId, accessToken, actualParams);
            if (json != null) {
                VivoApiResponse response = json.toJavaObject(VivoApiResponse.class);
                VivoAdPaginationResponseBody body = response.getData().toJavaObject(VivoAdPaginationResponseBody.class);
                if (body != null && body.getPageInfo() != null) {
                    return body.getPageInfo().getPageCount();
                }
            }

        } catch (Exception e) {
            log.error("获取[{}]分页信息出错: {}", module.name(), e);
        }
        return 0;
    }


    default JSONObject accountInfo(String accessToken, String accountId) {
        VivoApiResponse response = accountInfoInternal(accessToken, accountId);
        checkResponse(response);
        return response.getData();
    }

    default VivoApiResponse uploadData(VivoApiModule vivoApiModule, String accountId, String accessToken, Object object) {
        return getResponseInternal(vivoApiModule.getPath(), accountId, accessToken, object);
    }


}
