package ai.yiye.agent.marketing.tencent.client;

import ai.yiye.agent.domain.AdvertiserAccount;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.domain.marketing.data.AdvertiserPaggabelParams;
import ai.yiye.agent.marketing.MarketingAudienceThreadPool;
import ai.yiye.agent.marketing.MarketingThreadPool;
import ai.yiye.agent.marketing.aspect.StatisticTime;
import ai.yiye.agent.marketing.encode.FileContentType;
import ai.yiye.agent.marketing.encode.FormContentType;
import ai.yiye.agent.marketing.encode.JsonContentType;
import ai.yiye.agent.marketing.exception.MarketingApiException;
import ai.yiye.agent.marketing.tencent.TencentApiModule;
import ai.yiye.agent.marketing.tencent.request.TencentAudienceRequestBody;
import ai.yiye.agent.marketing.tencent.response.PageInfo;
import ai.yiye.agent.marketing.tencent.response.TencentAdApiResponse;
import ai.yiye.agent.marketing.tencent.response.TencentAdPaginationResponseBody;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import feign.Headers;
import feign.Param;
import feign.QueryMap;
import feign.RequestLine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static ai.yiye.agent.marketing.ResponseValidator.checkResponse;
import static ai.yiye.agent.marketing.ResponseValidator.checkResponseCode;

/**
 * 腾讯广告接口Client
 *
 * <AUTHOR>
 */
public interface TencentAdApiClient {

    int DEFAULT_PAGE_SIZE = 50;

    Logger log = LoggerFactory.getLogger(TencentAdApiClient.class);

    /**
     * module/get接口
     *
     * @param module      模块名
     * @param page        页码
     * @param pageSize    每页数据量
     * @param accountId   广告主id
     * @param accessToken 广告主token
     * @return 响应
     */
    @RequestLine("GET /{module}/get?account_id={accountId}&access_token={accessToken}&page={page}&page_size={pageSize}")
    TencentAdApiResponse<TencentAdPaginationResponseBody> getResponseInternal(
        @Param("module") TencentApiModule module,
        @Param("page") Integer page,
        @Param("pageSize") Integer pageSize,
        @Param("accountId") Object accountId,
        @Param("accessToken") String accessToken,
        @QueryMap Map<String, Object> params);

    /**
     * module/get接口(获取单个实体)
     *
     * @param module      模块名
     * @param accountId   广告主id
     * @param accessToken 广告主token
     * @return 响应
     */
    @RequestLine("GET /{module}/get?account_id={accountId}&access_token={accessToken}")
    TencentAdApiResponse<JSONObject> getSingleResponseInternal(
        @Param("module") TencentApiModule module,
        @Param("accountId") Object accountId,
        @Param("accessToken") String accessToken);

    /**
     * module/get接口(获取单个实体)
     *
     * @param module      模块名
     * @param accountId   广告主id
     * @param accessToken 广告主token
     * @param params      查询参数
     * @return 响应
     */
    @RequestLine("GET /{module}/get?account_id={accountId}&access_token={accessToken}")
    TencentAdApiResponse<JSONObject> getSingleResponse(
        @Param("module") TencentApiModule module,
        @Param("accountId") Object accountId,
        @Param("accessToken") String accessToken,
        @QueryMap Map<String, Object> params);

    /**
     * module/get接口 (get方法获取targeting参数)
     *
     * @param module      模块名
     * @param accountId   广告主id
     * @param accessToken 广告主token
     * @return 响应
     */
    @RequestLine("GET /{module}/get?account_id={accountId}&access_token={accessToken}")
    TencentAdApiResponse<TencentAdPaginationResponseBody> getSingleResponseInternal(
        @Param("module") TencentApiModule module,
        @Param("accountId") Object accountId,
        @Param("accessToken") String accessToken,
        @QueryMap Map<String, Object> params);

    /**
     * module/add接口 - 表单提交
     *
     * @param module      模块名
     * @param accessToken 广告主token
     * @param body        请求体
     * @return 响应
     */
    @FormContentType
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @RequestLine("POST /{module}/add?access_token={accessToken}")
    TencentAdApiResponse<JSONObject> addFormInternal(@Param("module") TencentApiModule module, @Param("accessToken") String accessToken, Object body);

    /**
     * module/update接口 - 更新表单
     *
     * @param module      模块名
     * @param accessToken 广告主token
     * @param body        请求体
     * @return 响应
     */
    @FormContentType
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @RequestLine("POST /{module}/update?access_token={accessToken}")
    TencentAdApiResponse<JSONObject> updateFormInternal(@Param("module") TencentApiModule module, @Param("accessToken") String accessToken, Object body);

    /**
     * module/add接口 - 表单提交
     *
     * @param module      模块名
     * @param accessToken access-token
     * @param body        请求体
     * @return
     */
    @FormContentType
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @RequestLine("POST /{module}/add?access_token={accessToken}")
    TencentAdApiResponse<JSONObject> addFormInternal(@Param("module") String module, @Param("accessToken") String accessToken, Object body);

    /**
     * module/delete接口
     *
     * @param module      模块名
     * @param accessToken 广告主token
     * @param body        请求体
     * @return 响应
     */
    @FormContentType
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @RequestLine("POST /{module}/delete?access_token={accessToken}")
    TencentAdApiResponse<JSONObject> deleteFormInternal(@Param("module") TencentApiModule module, @Param("accessToken") String accessToken, Object body);

    /**
     * module/add接口 - JSON提交
     *
     * @param module      模块名
     * @param accessToken 广告主token
     * @param body        请求体
     * @return 响应
     */
    @JsonContentType
    @Headers("Content-Type: application/json")
    @RequestLine("POST /{module}/add?access_token={accessToken}")
    TencentAdApiResponse<JSONObject> addJsonInternal(@Param("module") TencentApiModule module, @Param("accessToken") String accessToken, Object body);


    @JsonContentType
    @Headers("Content-Type: application/json")
    @RequestLine("POST /{module}/get?access_token={accessToken}")
    TencentAdApiResponse<JSONObject> getJsonInternal(@Param("module") TencentApiModule module, @Param("accessToken") String accessToken, Object body);

    /**
     * 腾讯，微信新建人群包
     *
     * @param requestBody
     * @param accessToken
     * @return
     */
    @RequestLine("POST /{module}/add?access_token={accessToken}")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    TencentAdApiResponse<JSONObject> addTencentAudiences(@Param("module") TencentApiModule module,
                                                         TencentAudienceRequestBody requestBody,
                                                         @Param("accessToken") String accessToken);


    @FileContentType
    @RequestLine("POST /{module}/add?access_token={accessToken}")
    TencentAdApiResponse<JSONObject> uploadTencentAudiencesFile(@Param("module") TencentApiModule module,
                                                                @Param("account_id") String accountId,
                                                                @Param("user_id_type") String userIdType,
                                                                @Param("audience_id") String audienceId,
                                                                @Param("file") MultipartFile file,
                                                                @Param("accessToken") String accessToken);

    @FileContentType
    @RequestLine("POST /{module}/add?access_token={accessToken}")
    TencentAdApiResponse<JSONObject> uploadVideoInternal(@Param("module") TencentApiModule module,
                                                         @Param("account_id") String accountId,
                                                         @Param("signature") String signature,
                                                         @Param("video_file") MultipartFile file,
                                                         @Param("accessToken") String accessToken);

    /**
     * 腾讯 / 微信 - 更新广告组出价
     */
    @JsonContentType
    @Headers("Content-Type: application/json")
    @RequestLine("POST /adgroups/update_bid_amount?access_token={accessToken}")
    JSONObject updateAdgroupsBidAmount(@Param("accessToken") String accessToken, Object body);

    /**
     * module/get接口
     *
     * @param module      模块名
     * @param accountId   广告主id
     * @param page        页码
     * @param pageSize    每页数据量
     * @param accessToken 广告主token
     * @return 响应data
     */
    default TencentAdPaginationResponseBody get(TencentApiModule module, Integer page, Integer pageSize, Object accountId, String accessToken, Map<String, Object> params) {
        TencentAdApiResponse<TencentAdPaginationResponseBody> response = null;
        try {
            log.info("TencentAdApiClient调用get方法, module: {}, page: {}, pageSize: {}, accountId: {}, accessToken: {}, params: {}", module, page, pageSize, accountId, accessToken, params);
            response = getResponseInternal(module, page, pageSize, accountId, accessToken, params);
            checkResponse(response);
            return response.getData();
        } catch (Exception e) {
            log.warn("TencentAdApiClient调用get方法异常，module= {}, 投放账户id = {}, page= {}, pageSize= {}, traceId = {}", module.name(), accountId, page, pageSize, Optional.ofNullable(response).map(r -> r.getTraceId()).orElse(""), e);
            e.printStackTrace();
        }
        return new TencentAdPaginationResponseBody();
    }

    /**
     * module/get接口
     *
     * @param module      模块名
     * @param accountId   广告主id
     * @param page        页码
     * @param pageSize    每页数据量
     * @param accessToken 广告主token
     * @return 响应data
     */
    default TencentAdPaginationResponseBody getNotTryCatch(TencentApiModule module, Integer page, Integer pageSize, Object accountId, String accessToken, Map<String, Object> params) {
        TencentAdApiResponse<TencentAdPaginationResponseBody> response = getResponseInternal(module, page, pageSize, accountId, accessToken, params);
        checkResponseCode(response);
        return Objects.nonNull(response.getData()) ? response.getData() : new TencentAdPaginationResponseBody();
    }

    /**
     * module/get接口
     *
     * @param module      模块名
     * @param accountId   广告主id
     * @param accessToken 广告主token
     * @return 响应data
     */
    default TencentAdPaginationResponseBody get(TencentApiModule module, String accountId, String accessToken, Map<String, Object> params) {
        return get(module, null, null, accountId, accessToken, params);
    }

    /**
     * module/get接口
     *
     * @param module      模块名
     * @param accountId   广告主id
     * @param accessToken 广告主token
     * @return 响应data
     */
    default JSONObject getOne(TencentApiModule module, Object accountId, String accessToken) {
        TencentAdApiResponse<JSONObject> response = null;
        try {
            response = getSingleResponseInternal(module, accountId, accessToken);
            checkResponse(response);
        } catch (Exception e) {
            log.warn("TencentAdApiClient调用getOne方法异常，module= {}, 投放账户id = {}, traceId = {}", module.name(), accountId, Optional.ofNullable(response).map(r -> r.getTraceId()).orElse(""), e);
            e.printStackTrace();
        }
        return response.getData();
    }

    /**
     * module/get接口
     *
     * @param module      模块名
     * @param accountId   广告主id
     * @param accessToken 广告主token
     * @param param
     * @return 响应data
     */
    default JSONObject getOne(TencentApiModule module, Object accountId, String accessToken, Map<String, Object> param) {
        TencentAdApiResponse<JSONObject> response = null;
        try {
            response = getSingleResponse(module, accountId, accessToken, param);
            checkResponse(response);
        } catch (Exception e) {
            log.warn("TencentAdApiClient调用getOne方法异常，module= {}, 投放账户id = {}, traceId = {}", module.name(), accountId, Optional.ofNullable(response).map(r -> r.getTraceId()).orElse(""), e);
            e.printStackTrace();
        }
        return response.getData();
    }


    /**
     * 获取分页接口所有数据
     *
     * @param module 模块名
     * @return 模块所有数据
     */

    @StatisticTime
    default List<Object> getAll(TencentApiModule module, AdvertiserAccount account, Map<String, Object> params) {
        return this.getAll(module, account.getAccountId(), account.getAccessToken(), params);
    }

    @StatisticTime
    default List<Object> getAllNotTryCatch(TencentApiModule module, AdvertiserAccount account, Map<String, Object> params) {
        return this.getAllNotTryCatch(module, account.getAccountId(), account.getAccessToken(), params);
    }


    /**
     * 获取涉众的相关数据
     * @param module
     * @param account
     * @param params
     * @return
     */
    default void getAudienceAll(TencentApiModule module, AdvertiserAccount account, Map<String, Object> params, Consumer<List<Object>> resultConsumer) {
        TencentAdPaginationResponseBody paginationResponseBody = get(module, 1, DEFAULT_PAGE_SIZE, account.getAccountId(), account.getAccessToken(), params);
        JSONArray result = paginationResponseBody.getList();
        PageInfo pageInfo = paginationResponseBody.getPageInfo();
        resultConsumer.accept(result);

        if (pageInfo == null || !pageInfo.hasNext()) {
            return;
        }
        IntStream.rangeClosed(2, pageInfo.getTotalPage())
            .mapToObj(page -> CompletableFuture.runAsync(() -> {
                // 将页数进行附加
                JSONArray list = get(module, page, DEFAULT_PAGE_SIZE, account.getAccountId(), account.getAccessToken(), params).getList();
                resultConsumer.accept(list);
            }, MarketingAudienceThreadPool.THREAD_POOL).exceptionally(e -> {
                log.error("", e);
                return null;
            })).collect(Collectors.toList());
        // join results
    }

    default List<Object> getAll(TencentApiModule module, Object accountId, String accessToken, Map<String, Object> params) {

        // try to access first page
        TencentAdPaginationResponseBody paginationResponseBody = get(module, 1, DEFAULT_PAGE_SIZE, accountId, accessToken, params);
        JSONArray result = paginationResponseBody.getList();
        PageInfo pageInfo = paginationResponseBody.getPageInfo();

        if (pageInfo == null || !pageInfo.hasNext()) {
            return result;
        }

        List<CompletableFuture<JSONArray>> futures = IntStream
            .rangeClosed(2, pageInfo.getTotalPage())
            .mapToObj(page -> CompletableFuture.supplyAsync(() -> get(module, page, DEFAULT_PAGE_SIZE, accountId, accessToken, params).getList(), MarketingThreadPool.THREAD_POOL).exceptionally(e -> {
                log.error("", e);
                return new JSONArray();
            }))
            .collect(Collectors.toList());

        // join results
        result.addAll(futures.stream().map(CompletableFuture::join).flatMap(Collection::stream).collect(Collectors.toList()));

        return result;
    }

    default List<Object> getAllNotTryCatch(TencentApiModule module, Object accountId, String accessToken, Map<String, Object> params) {
        // try to access first page
        TencentAdPaginationResponseBody paginationResponseBody = getNotTryCatch(module, 1, DEFAULT_PAGE_SIZE, accountId, accessToken, params);
        JSONArray result = paginationResponseBody.getList();
        PageInfo pageInfo = paginationResponseBody.getPageInfo();
        if (pageInfo == null || !pageInfo.hasNext()) {
            return result;
        }
        List<CompletableFuture<JSONArray>> futures = IntStream
            .rangeClosed(2, pageInfo.getTotalPage())
            .mapToObj(page -> CompletableFuture.supplyAsync(() -> getNotTryCatch(module, page, DEFAULT_PAGE_SIZE, accountId, accessToken, params).getList(), MarketingThreadPool.THREAD_POOL).exceptionally(e -> {
                log.error("", e);
                return new JSONArray();
            }))
            .collect(Collectors.toList());
        // join results
        result.addAll(futures.stream().map(CompletableFuture::join).flatMap(Collection::stream).collect(Collectors.toList()));
        return result;
    }


    default <T> List<T> getAll(TencentApiModule module, AdvertiserPaggabelParams paggabelParams, HashMap<String, Object> params, BiFunction<AdvertiserPaggabelParams, List<Object>, List<T>> invoker) {
        String accountId = paggabelParams.getAdvertiserAccount().getAccountId();
        String accessToken = paggabelParams.getAdvertiserAccount().getAccessToken();
        TencentAdPaginationResponseBody paginationResponseBody = get(module, 1, 100, accountId, accessToken, params);
        List<T> result = invoker.apply(paggabelParams, paginationResponseBody.getList());
        PageInfo pageInfo = paginationResponseBody.getPageInfo();

        // 进行数据调取
        List<CompletableFuture<List<T>>> futures = IntStream.rangeClosed(2, pageInfo.getTotalPage())
            .mapToObj(page -> CompletableFuture.supplyAsync(() ->
                invoker.apply(paggabelParams, get(module, page, 100, accountId, accessToken, params).getList()), MarketingThreadPool.THREAD_POOL))
            .collect(Collectors.toList());
        // 数据合并
        result.addAll(futures.stream().map(CompletableFuture::join).flatMap(Collection::stream).collect(Collectors.toList()));
        return result;
    }


    @StatisticTime
    default void consumeAll(TencentApiModule module, AdvertiserAccount account, HashMap<String, Object> params, Consumer<List<Object>> resultConsumer) {
        this.consumeAll(module, account.getAccountId(), account.getAccessToken(), params, resultConsumer, true);
    }

    default void consumeAll(TencentApiModule module, Object accountId, String accessToken, HashMap<String, Object> params, Consumer<List<Object>> resultConsumer) {
        this.consumeAll(module, accountId, accessToken, params, resultConsumer, true);
    }

    /**
     * 消费分页接口数据
     *
     * @param module         模块名
     * @param accountId      广告主id
     * @param accessToken    广告主token
     * @param resultConsumer 结果集消费者
     */
    default void consumeAll(TencentApiModule module, Object accountId, String accessToken, HashMap<String, Object> params, Consumer<List<Object>> resultConsumer, boolean isSync) {

        // try to access first page
//        try {
            TencentAdPaginationResponseBody paginationResponseBody = get(module, 1, DEFAULT_PAGE_SIZE, accountId, accessToken, params);
            JSONArray result = paginationResponseBody.getList();

            try {
                resultConsumer.accept(result);
            } catch (Exception e) {
                log.error("module:{} accountId:{} page:{} pageSize:{} ", module, accountId, 1, DEFAULT_PAGE_SIZE, e);
            }


            PageInfo pageInfo = paginationResponseBody.getPageInfo();
            if (pageInfo == null || !pageInfo.hasNext()) {
//                resultConsumer.accept(result);
                return;
            }

            if (isSync) {
                IntStream.rangeClosed(2, pageInfo.getTotalPage())
                    .mapToObj(page -> CompletableFuture.runAsync(() -> {
                        JSONArray list = get(module, page, DEFAULT_PAGE_SIZE, accountId, accessToken, params).getList();
                        resultConsumer.accept(list);
                    }, MarketingThreadPool.THREAD_POOL)
                        .exceptionally(e -> {
                            log.error("module:{} accountId:{} page:{} pageSize:{} {}", module, accountId, page, DEFAULT_PAGE_SIZE, e);
                            return null;
                        }))
                    .collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
            } else {
                IntStream.rangeClosed(2, pageInfo.getTotalPage())
                    .mapToObj(page -> {
                        JSONArray list = null;
                        try {
                            list = get(module, page, DEFAULT_PAGE_SIZE, accountId, accessToken, params).getList();
                            resultConsumer.accept(list);
                        } catch (Exception e) {
                            log.error("module:{} accountId:{} page:{} pageSize:{} {}", module, accountId, page, DEFAULT_PAGE_SIZE, e);
                            e.printStackTrace();
                        } finally {
                            return list;
                        }
                    }).collect(Collectors.toList());
            }

/*
            List<CompletableFuture<JSONArray>> futures = IntStream
                .rangeClosed(2, pageInfo.getTotalPage())
                .mapToObj(page -> CompletableFuture.supplyAsync(() -> get(module, page, DEFAULT_PAGE_SIZE, accountId, accessToken, params).getList(), MarketingThreadPool.THREAD_POOL).exceptionally(e -> {
                    log.error("", e);
                    return new JSONArray();
                }))
                .collect(Collectors.toList());
            // join results
            result.addAll(futures.stream().map(CompletableFuture::join).flatMap(Collection::stream).collect(Collectors.toList()));
            Lists.partition(result, 400).stream().forEach(data -> {
                resultConsumer.accept(result);
            });
*/


//        } catch (Exception e) {
//            log.warn("TencentAdApiClient调用consumeAll方法异常，module ={}, 投放账户id = {}", module.name(), accountId, e);
//            e.printStackTrace();
//        }
        // join results
        //  result.addAll(futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
    }


    @StatisticTime
    default void consumeAllPage(TencentApiModule module, AdvertiserAccount account, HashMap<String, Object> params, Consumer<List<Object>> resultConsumer) {
        this.consumeAllPage(module, account.getAccountId(), account.getAccessToken(), params, resultConsumer);
    }


    /**
     * 消费分页接口数据
     *
     * @param module         模块名
     * @param accountId      广告主id
     * @param accessToken    广告主token
     * @param resultConsumer 结果集消费者
     */
    default void consumeAllPage(TencentApiModule module, Object accountId, String accessToken, HashMap<String, Object> params, Consumer<List<Object>> resultConsumer) {

        try {
            TencentAdPaginationResponseBody paginationResponseBody = get(module, 1, DEFAULT_PAGE_SIZE, accountId, accessToken, params);
            JSONArray result = paginationResponseBody.getList();
            PageInfo pageInfo = paginationResponseBody.getPageInfo();
            if (Objects.nonNull(pageInfo) && pageInfo.hasNext()) {
                IntStream.rangeClosed(2, pageInfo.getTotalPage())
                    .forEach(page -> {
                        result.addAll(get(module, page, DEFAULT_PAGE_SIZE, accountId, accessToken, params).getList());
                    });
            }
            resultConsumer.accept(result);
        } catch (Exception e) {
            log.warn("TencentAdApiClient调用consumeAllPage方法异常，module ={}, 投放账户id = {}", module.name(), accountId, e);
            e.printStackTrace();
            throw e;
        }

    }


    /**
     * module/add接口 - 表单提交
     *
     * @param module      模块名
     * @param accessToken 广告主token
     * @param body        请求体
     * @return 响应
     */
    default JSONObject add(TencentApiModule module, String accessToken, Object body) {
        TencentAdApiResponse<JSONObject> response = null;
        try {
            response = addFormInternal(module, accessToken, body);
            checkResponse(response);
        } catch (MarketingApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("TencentAdApiClient调用add方法异常，module= {}, 投放账户id = {}, traceId = {}", module.name(), ((Map) body).get("account_id"), Optional.ofNullable(response).map(r -> r.getTraceId()).orElse(""), e);
            e.printStackTrace();
            throw e;
        }
        return response.getData();
    }

    /**
     * module/update接口 - 更新表单
     *
     * @param module      模块名
     * @param accessToken 广告主token
     * @param body        请求体
     * @return 响应
     */
    default JSONObject update(TencentApiModule module, String accessToken, Object body) {
        TencentAdApiResponse<JSONObject> response = null;
        try {
            response = updateFormInternal(module, accessToken, body);
            checkResponse(response);
        } catch (MarketingApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("TencentAdApiClient调用update方法异常，module= {}, 投放账户id = {}, traceId = {}", module.name(), ((Map) body).get("account_id"), Optional.ofNullable(response).map(r -> r.getTraceId()).orElse(""), e);
            e.printStackTrace();
            throw e;
        }
        return response.getData();
    }

    /**
     * module/delete接口
     *
     * @param module      模块名
     * @param accessToken 广告主token
     * @param body        请求体
     * @return 响应
     */
    default JSONObject delete(TencentApiModule module, String accessToken, Object body) {
        TencentAdApiResponse<JSONObject> response = deleteFormInternal(module, accessToken, body);
        checkResponse(response);
        return response.getData();
    }

    @StatisticTime
    default JSONObject getJson(TencentApiModule module, AdvertiserAccount account, Object body) {
        return this.getJson(module, account.getAccessToken(), body);
    }

    default JSONObject getJson(TencentApiModule module, String accessToken, Object body) {
        TencentAdApiResponse<JSONObject> response = null;
        try {
            response = getJsonInternal(module, accessToken, body);
            checkResponse(response);
            return response.getData();
        } catch (Exception e) {
            log.warn("TencentAdApiClient调用getJson方法异常，module= {}, 投放账户id = {}, traceId = {}", module.name(), ((Map) body).get("account_id"), Optional.ofNullable(response).map(r -> r.getTraceId()).orElse(""), e);
            e.printStackTrace();
        }
        return new JSONObject();
    }

    // ======================================= TODO 当前为了版本兼容 暂时将agentId进行放开 =======================================================

    /**
     * module/get接口
     *
     * @param module      模块名
     * @param accountId   广告主id
     * @param page        页码
     * @param pageSize    每页数据量
     * @param accessToken 广告主token
     * @return 响应data
     */
    default TencentAdPaginationResponseBody get(String agentId, TencentApiModule module, Integer page, Integer pageSize, Object accountId, String accessToken, Map<String, Object> params) {
        TencentAdApiResponse<TencentAdPaginationResponseBody> response = null;
        try {
            response = getResponseInternal(module, page, pageSize, accountId, accessToken, params);
            checkResponse(response);
            return response.getData();
        } catch (MarketingApiException e) {
            throw e;
        } catch (Exception e) {
            log.warn("TencentAdApiClient调用get方法异常，module= {},agentId = {}, 投放账户id = {}, traceId = {}", module.name(), agentId, accountId, Optional.ofNullable(response).map(r -> r.getTraceId()).orElse(""), e);
            e.printStackTrace();
        }
        return new TencentAdPaginationResponseBody();
    }


    @StatisticTime
    default List<Object> getAll(AgentConf agentConf, TencentApiModule module, AdvertiserAccount account, Map<String, Object> params) {
        return this.getAll(agentConf.getAgentId(), module, account.getAccountId(), account.getAccessToken(), params);
    }

    default List<Object> getAll(String agentId, TencentApiModule module, Object accountId, String accessToken, Map<String, Object> params) {

        // try to access first page
        TencentAdPaginationResponseBody paginationResponseBody = get(agentId, module, 1, DEFAULT_PAGE_SIZE, accountId, accessToken, params);
        JSONArray result = paginationResponseBody.getList();
        PageInfo pageInfo = paginationResponseBody.getPageInfo();

        if (pageInfo == null || !pageInfo.hasNext()) {
            return result;
        }

        List<CompletableFuture<JSONArray>> futures = IntStream
            .rangeClosed(2, pageInfo.getTotalPage())
            .mapToObj(page -> CompletableFuture.supplyAsync(() -> get(agentId, module, page, DEFAULT_PAGE_SIZE, accountId, accessToken, params).getList(), MarketingThreadPool.THREAD_POOL).exceptionally(e -> {
                log.error("", e);
                return new JSONArray();
            }))
            .collect(Collectors.toList());

        // join results
        result.addAll(futures.stream().map(CompletableFuture::join).flatMap(Collection::stream).collect(Collectors.toList()));

        return result;
    }


    default <T> List<T> getAll(String agentId, TencentApiModule module, AdvertiserPaggabelParams paggabelParams, HashMap<String, Object> params, BiFunction<AdvertiserPaggabelParams, List<Object>, List<T>> invoker) {
        String accountId = paggabelParams.getAdvertiserAccount().getAccountId();
        String accessToken = paggabelParams.getAdvertiserAccount().getAccessToken();
        TencentAdPaginationResponseBody paginationResponseBody = get(agentId, module, 1, 100, accountId, accessToken, params);
        List<T> result = invoker.apply(paggabelParams, paginationResponseBody.getList());
        PageInfo pageInfo = paginationResponseBody.getPageInfo();

        // 进行数据调取
        List<CompletableFuture<List<T>>> futures = IntStream.rangeClosed(2, pageInfo.getTotalPage())
            .mapToObj(page -> CompletableFuture.supplyAsync(() ->
                invoker.apply(paggabelParams, get(agentId, module, page, 100, accountId, accessToken, params).getList()), MarketingThreadPool.THREAD_POOL))
            .collect(Collectors.toList());
        // 数据合并
        result.addAll(futures.stream().map(CompletableFuture::join).flatMap(Collection::stream).collect(Collectors.toList()));
        return result;
    }


    @StatisticTime
    default void consumeAll(AgentConf agentConf, TencentApiModule module, AdvertiserAccount account, HashMap<String, Object> params, Consumer<List<Object>> resultConsumer) {
        this.consumeAll(agentConf.getAgentId(), module, account.getAccountId(), account.getAccessToken(), params, resultConsumer, true);
    }

    default void consumeAll(AgentConf agentConf, TencentApiModule module, AdvertiserAccount account, HashMap<String, Object> params, Consumer<List<Object>> resultConsumer, boolean isSync) {
        this.consumeAll(agentConf.getAgentId(), module, account.getAccountId(), account.getAccessToken(), params, resultConsumer, isSync);
    }

    /**
     * 消费分页接口数据
     *
     * @param module         模块名
     * @param accountId      广告主id
     * @param accessToken    广告主token
     * @param resultConsumer 结果集消费者
     */
    default void consumeAll(String agentId, TencentApiModule module, Object accountId, String accessToken, HashMap<String, Object> params, Consumer<List<Object>> resultConsumer, boolean isSync) {

        // try to access first page
        try {
            TencentAdPaginationResponseBody paginationResponseBody = get(agentId, module, 1, DEFAULT_PAGE_SIZE, accountId, accessToken, params);
            JSONArray result = paginationResponseBody.getList();
            resultConsumer.accept(result);

            PageInfo pageInfo = paginationResponseBody.getPageInfo();
            if (pageInfo == null || !pageInfo.hasNext()) {
                return;
            }

            if (isSync) {
                IntStream.rangeClosed(2, pageInfo.getTotalPage())
                    .mapToObj(page -> CompletableFuture.runAsync(() -> {
                        JSONArray list = get(agentId, module, page, DEFAULT_PAGE_SIZE, accountId, accessToken, params).getList();
                        resultConsumer.accept(list);
                    }, MarketingThreadPool.THREAD_POOL)
                        .exceptionally(e -> {
                            log.error("", e);
                            return null;
                        }))
                    .collect(Collectors.toList());
            } else {
                IntStream.rangeClosed(2, pageInfo.getTotalPage())
                    .mapToObj(page -> {
                        JSONArray list = null;
                        try {
                            list = get(agentId, module, page, DEFAULT_PAGE_SIZE, accountId, accessToken, params).getList();
                            resultConsumer.accept(list);
                        } catch (Exception e) {
                            log.error("", e);
                        } finally {
                            return list;
                        }
                    }).collect(Collectors.toList());
            }

        } catch (Exception e) {
            log.warn("TencentAdApiClient调用consumeAll方法异常，module ={}，agentId = {}, 投放账户id = {}", module.name(), agentId, accountId, e);
            e.printStackTrace();
            throw e;
        }
        // join results
        //  result.addAll(futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
    }


    @StatisticTime
    default void consumeAllPage(AgentConf agentConf, TencentApiModule module, AdvertiserAccount account, HashMap<String, Object> params, Consumer<List<Object>> resultConsumer) {
        this.consumeAll(agentConf.getAgentId(), module, account.getAccountId(), account.getAccessToken(), params, resultConsumer, true);
    }


    @StatisticTime
    default JSONObject getJson(AgentConf agentConf, TencentApiModule module, AdvertiserAccount account, Object body) {
        return this.getJson(agentConf.getAgentId(), module, account.getAccessToken(), body);
    }

    default JSONObject getJson(String agentId, TencentApiModule module, String accessToken, Object body) {
        TencentAdApiResponse<JSONObject> response = null;
        try {
            response = getJsonInternal(module, accessToken, body);
            checkResponse(response);
            return response.getData();
        } catch (MarketingApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("TencentAdApiClient调用getJson方法异常，module= {}, agentId = {}, 投放账户id = {}, traceId = {}", module.name(), agentId, ((Map) body).get("account_id"), Optional.ofNullable(response).map(r -> r.getTraceId()).orElse(""), e);
            e.printStackTrace();
        }
        return new JSONObject();
    }

    // ======================================= TODO 当前为了版本兼容 暂时将agentId进行放开 =======================================================

    /**
     * module/add接口 - JSON提交
     *
     * @param module      模块名
     * @param accessToken 广告主token
     * @param body        请求体
     * @return 响应
     */
    default JSONObject addJson(TencentApiModule module, String accessToken, Object body) {
        TencentAdApiResponse<JSONObject> response = null;
        try {
            response = addJsonInternal(module, accessToken, body);
            checkResponse(response);
        } catch (MarketingApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("TencentAdApiClient调用getJson方法异常，module= {}, 投放账户id = {}, traceId = {}", module.name(), ((Map) body).get("account_id"), Optional.ofNullable(response).map(r -> r.getTraceId()).orElse(""), e);
            e.printStackTrace();
            throw e;
        }
        return response.getData();
    }


    default JSONObject addJsonNotTryCatch(TencentApiModule module, String accessToken, Object body) {
        TencentAdApiResponse<JSONObject> response = addJsonInternal(module, accessToken, body);
        checkResponseCode(response);
        return response.getData();
    }

    default JSONObject addTencentAudienceBundle(TencentApiModule customAudiences, TencentAudienceRequestBody requestBody, String accessToken) {
        TencentAdApiResponse<JSONObject> response = null;
        try {
            response = addTencentAudiences(customAudiences, requestBody, accessToken);
            checkResponse(response);
        } catch (MarketingApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("TencentAdApiClient调用addTencentAudienceBundle方法异常，module= {}, 投放账户id = {}, traceId = {}", customAudiences.name(), requestBody.getAccountId(), Optional.ofNullable(response).map(r -> r.getTraceId()).orElse(""), e);
            e.printStackTrace();
            throw e;
        }
        return response.getData();
    }

    default JSONObject uploadTencentAudienceBundleFile(TencentApiModule customAudienceFiles, String accountId, String userIdType, String audienceId, MultipartFile file, String accessToken) {
        TencentAdApiResponse<JSONObject> tencentAdApiResponse = null;
        try {
            tencentAdApiResponse = uploadTencentAudiencesFile(customAudienceFiles, accountId, userIdType, audienceId, file, accessToken);
            checkResponse(tencentAdApiResponse);
        } catch (MarketingApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("TencentAdApiClient调用addTencentAudienceBundle方法异常，module= {}, 投放账户id = {}, traceId = {}", customAudienceFiles.name(), accountId, Optional.ofNullable(tencentAdApiResponse).map(r -> r.getTraceId()).orElse(""), e);
            e.printStackTrace();
            throw e;
        }
        return tencentAdApiResponse.getData();
    }

    default JSONObject uploadVideo(String accountId, String signature, MultipartFile file, String accessToken) {
        TencentAdApiResponse<JSONObject> tencentAdApiResponse = null;
        try {
            tencentAdApiResponse = uploadVideoInternal(TencentApiModule.videos, accountId, signature, file, accessToken);
            checkResponse(tencentAdApiResponse);
        } catch (MarketingApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("TencentAdApiClient调用uploadVideo方法异常，module= {}, 投放账户id = {}, traceId = {}", TencentApiModule.videos.name(), accountId, Optional.ofNullable(tencentAdApiResponse).map(r -> r.getTraceId()).orElse(""), e);
            e.printStackTrace();
            throw e;
        }
        return tencentAdApiResponse.getData();
    }
}
