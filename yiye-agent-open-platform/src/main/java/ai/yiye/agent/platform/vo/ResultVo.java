package ai.yiye.agent.platform.vo;

import ai.yiye.agent.domain.enumerations.platform.PlatformResultEnum;
import lombok.Data;

/**
 * @author: xin
 * @Date: 2023/11/29 11:11
 * @description <响应vo>
 */
@Data
public class ResultVo<T> {
    private Integer code;
    private String message;
    private T data;
    private String requestId;
    private Long requestAt;

    public static ResultVo success() {
        ResultVo resultVo = new ResultVo()
            .setCode(PlatformResultEnum.SUCCESS.getCode())
            .setMessage(PlatformResultEnum.SUCCESS.getMessage());
        return resultVo;
    }

    public static <T> ResultVo<T> success(T data) {
        ResultVo<T> resultVo = new ResultVo<T>()
            .setData(data)
            .setCode(PlatformResultEnum.SUCCESS.getCode())
            .setMessage(PlatformResultEnum.SUCCESS.getMessage());
        return resultVo;
    }

    public static ResultVo error(PlatformResultEnum platformResultEnum) {
        ResultVo resultVo = new ResultVo<>()
            .setCode(platformResultEnum.getCode())
            .setMessage(platformResultEnum.getMessage());
        return resultVo;
    }

    public static ResultVo error(Integer code, String message) {
        ResultVo resultVo = new ResultVo<>()
            .setCode(code)
            .setMessage(message);
        return resultVo;
    }
}
