package ai.yiye.agent.platform.dto;

import ai.yiye.agent.domain.enumerations.LandingPageWechatCustomerContactStatus;
import ai.yiye.agent.domain.enumerations.OnlineStatusType;
import ai.yiye.agent.platform.serialize.InstantToTimestampSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.time.Instant;

/**
 * @author: xin
 * @Date: 2023/11/29 11:21
 * @description <客服信息>
 */
@Data
public class CustomerServiceDto {

    //项目下客服ID（一叶系统内客服ID）
    private Long id;

    //项目下客服名称（一叶系统内自定义设置的客服名称）
    private String userName;

    //项目下客服头像（一叶系统内自定义设置的客服头像）
    private String userAvatar;

    ///客服成员昵称
    private String name;

    //客服成员密文userid
    private String userid;

    //权重值
    private Integer weight;

    //页面内加粉渠道二维码生成状态
    private LandingPageWechatCustomerContactStatus contactStatus;

    //客服在线状态
    private OnlineStatusType onlineStatus;

    //创建时间 序列化为毫秒时间戳
    @JsonSerialize(using = InstantToTimestampSerializer.class)
    private Instant createdAt;


}
