package ai.yiye.agent.platform.service;

import ai.yiye.agent.domain.dto.platform.ChannelQiyetuiMessageDto;
import ai.yiye.agent.domain.enumerations.LandingPageReviewStatus;
import ai.yiye.agent.platform.dto.AdvertiserAccountGroupDto;
import ai.yiye.agent.platform.dto.AgentOpenPlatformMessageDto;
import ai.yiye.agent.platform.enums.CallbackMessageType;
import brave.Tracer;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * @author: xin
 * @Date: 2023/11/30 11:23
 * @description <功能描述>
 */
@Slf4j
@Service
public class MessageService {

    @Resource
    private RestTemplate platFormRestTemplate;

    @Resource
    public Tracer tracer;

    /**
     * 发送回调消息
     *
     * @param advertiserAccountGroupDtos
     */
    public void sendMessage(AdvertiserAccountGroupDto advertiserAccountGroupDtos) {
        String token = advertiserAccountGroupDtos.getToken();
        String callBackUrl = advertiserAccountGroupDtos.getCallBackUrl();
        advertiserAccountGroupDtos.setToken(null).setCallBackUrl(null);
        String jsonResult = JSONObject.toJSONString(advertiserAccountGroupDtos);
        send(token, callBackUrl, jsonResult, CallbackMessageType.CUSTOMER_SERVICE);
    }

    /**
     * 发送渠道审核失败信息
     *
     * @param channelQiyetuiMessageDto
     */
    public void sendChannelAuditMessage(ChannelQiyetuiMessageDto channelQiyetuiMessageDto) {
        String token = channelQiyetuiMessageDto.getToken();
        String callBackUrl = channelQiyetuiMessageDto.getUrl();
        channelQiyetuiMessageDto.setToken(null).setUrl(null);
        LandingPageReviewStatus reviewStatus = channelQiyetuiMessageDto.getReviewStatus();
        CallbackMessageType messageType = null;
        if (LandingPageReviewStatus.AUDIT_FAILURE.equals(reviewStatus)) {
            messageType = CallbackMessageType.AUDIT_FAILURE;
        } else if (LandingPageReviewStatus.PRELIMINARY_AUDIT.equals(reviewStatus)) {
            messageType = CallbackMessageType.PRELIMINARY_AUDIT_FAILURE;
        }
        String jsonResult = JSONObject.toJSONString(channelQiyetuiMessageDto);
        send(token, callBackUrl, jsonResult, messageType);
    }

    /**
     * 发送
     *
     * @param token
     * @param callBackUrl
     * @param jsonResult
     */
    public void send(String token, String callBackUrl, String jsonResult, CallbackMessageType messageType) {
        String traceId = tracer.currentSpan().context().traceIdString();
        AES aes = new AES(Mode.ECB, Padding.PKCS5Padding, token.getBytes());
        // 加密并进行Base转码
        String encrypt = aes.encryptBase64(jsonResult);
        HttpHeaders headers = new HttpHeaders();
        AgentOpenPlatformMessageDto agentOpenPlatformMessageDto =
            new AgentOpenPlatformMessageDto()
                .setRequestId(traceId).setEchostr(encrypt).setTimestamp(System.currentTimeMillis())
                .setMessageType(messageType);
        HttpEntity<AgentOpenPlatformMessageDto> httpEntity = new HttpEntity(agentOpenPlatformMessageDto, headers);
        try {
            String result = platFormRestTemplate.postForObject(callBackUrl, httpEntity, String.class);
            log.info("token:{},url:{},发送回调消息成功!客户端回调内容:{}", token, callBackUrl, result);
        } catch (RestClientException e) {
            log.error("token:{},url:{},发送回调消息失败!", token, callBackUrl, e);
        }
    }
}
