package ai.yiye.agent.platform.service;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.web.exception.PlatformException;
import ai.yiye.agent.domain.LandingPageWechatCustomerService;
import ai.yiye.agent.domain.UserOperationLogDetail;
import ai.yiye.agent.domain.dto.platform.WechatCustomerServiceGroupDto;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.enumerations.platform.PlatformResultEnum;
import ai.yiye.agent.platform.dto.AgentKfRequestDto;
import ai.yiye.agent.platform.mapper.LandingPageWechatCustomerServiceMapper;
import ai.yiye.agent.platform.sender.LandingPageWechatCustomerServiceSender;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: xin
 * @Date: 2023/11/29 10:48
 * @description <客服信息>
 */
@Slf4j
@Service
public class LandingPageWechatCustomerServiceService extends ServiceImpl<LandingPageWechatCustomerServiceMapper, LandingPageWechatCustomerService> {

    @Resource
    private UserOperationLogDetailService userOperationLogDetailService;

    @Resource
    private LandingPageWechatCustomerServiceSender landingPageWechatCustomerServiceSender;

    @Resource
    private RedisTemplate<String, Object> objectRedisTemplate;


    /**
     * 更改客服上下线状态
     *
     * @param agentKfRequestDto
     */
    public void updateCustomersOnline(AgentKfRequestDto agentKfRequestDto) {
        List<String> userids = agentKfRequestDto.getUserids();
        OnlineStatusType onlineStatus = agentKfRequestDto.getOnlineStatus();
        if (CollectionUtils.isEmpty(userids) || userids.size() > 100) {
            throw new PlatformException(PlatformResultEnum.PARAM_ERROR);
        }
        //查询客服userid中是否存在兜底客服
        List<LandingPageWechatCustomerService> list = list(Wrappers.lambdaQuery(LandingPageWechatCustomerService.class)
            .in(LandingPageWechatCustomerService::getWechatUserId, userids));
        if (CollectionUtils.isEmpty(list)) {
            throw new PlatformException(PlatformResultEnum.OPEN_USERID_ERROR);
        }
        //兜底客服
        long count = list.stream().filter(e -> e.getQrCodeWeight() == 0).count();
        if (count > 0) {
            throw new PlatformException(PlatformResultEnum.FINAL_CUSTOMER_SERVICE_ERROR);
        }
        update(Wrappers.lambdaUpdate(LandingPageWechatCustomerService.class)
            .set(LandingPageWechatCustomerService::getOnlineStatus, onlineStatus)
            .in(LandingPageWechatCustomerService::getWechatUserId, userids));
        //筛选出本次变动状态的客服
        List<LandingPageWechatCustomerService> collect = list.stream().filter(e -> !onlineStatus.equals(e.getOnlineStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            //异步记录操作日志
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String clientIP = ServletUtil.getClientIP(request);
            agentKfRequestDto.setLandingPageWechatCustomerServices(collect)
                .setIp(clientIP);
            landingPageWechatCustomerServiceSender.saveCustomerServiceLog(agentKfRequestDto);
            //判断客服分组状态
            WechatCustomerServiceGroupDto wechatCustomerServiceGroupDto = new WechatCustomerServiceGroupDto();
            Set<Long> customerServiceIds = collect.stream().map(LandingPageWechatCustomerService::getId).collect(Collectors.toSet());
            wechatCustomerServiceGroupDto.setWechatCustomerServiceIds(customerServiceIds);
            landingPageWechatCustomerServiceSender.sendLandingPageCustomerServiceChange(wechatCustomerServiceGroupDto);
            //查询客服分组
            Set<Long> groupIds = searchGroupIdsByCustomerServiceIds(customerServiceIds);
            Optional.ofNullable(groupIds).ifPresent(e -> {
                e.stream().forEach(m -> {
                    //清除客服分组缓存
                    String key = RedisConstant.LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_GROUP_SHOW_SERVICE_INFO_1 + m;
                    objectRedisTemplate.delete(key);
                });
            });
        }

    }

    /**
     * 通过客服ID集合查询客服分组集合
     *
     * @param customerIds
     * @return
     */
    public Set<Long> searchGroupIdsByCustomerServiceIds(Set<Long> customerIds) {
        Set<Long> longs = getBaseMapper().searchGroupIdsByCustomerServiceIds(customerIds);
        return longs;
    }


    public void saveLog(AgentKfRequestDto agentKfRequestDto) {
        List<LandingPageWechatCustomerService> list = agentKfRequestDto.getLandingPageWechatCustomerServices();
        OnlineStatusType onlineStatus = agentKfRequestDto.getOnlineStatus();
        String ip = agentKfRequestDto.getIp();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String desc = OnlineStatusType.ENABLE.equals(onlineStatus) ? "客服上线" : "客服下线";
        CompareOperActionEnum operAction = OnlineStatusType.ENABLE.equals(onlineStatus) ? CompareOperActionEnum.CUSTOMER_SERVICE_ONLINE :
            CompareOperActionEnum.CUSTOMER_SERVICE_OFFLINE;
        List<UserOperationLogDetail> collect = list.stream().map(landingPageWechatCustomerService -> {
            UserOperationLogDetail userOperationLogDetail = new UserOperationLogDetail();
            userOperationLogDetail.setOperId(landingPageWechatCustomerService.getId())
                .setOperName(landingPageWechatCustomerService.getWechatUserName())
                .setOperUserName("API接口")
                .setOperDesc(desc)
                .setOperIp(ip)
                .setOperAction(operAction)
                .setOperationRole(OperationRole.COMMON)
                .setAdvertiserAccountGroupId(landingPageWechatCustomerService.getAdvertiserAccountGroupId())
                .setOperLevel(UserOperationLogDetailActionLevel.CUSTOMER_SERVICE)
                .setOperOpreands(UserOperationLogDetailOperands.WECHAT_CUSTOMER_SERVICE)
                .setOperTime(Instant.now());
            JSONObject operDescJsonBySn = LandingPageWechatCustomerService.getOperDescJsonBySn("", "", "");
            userOperationLogDetail.setOperDescJson(operDescJsonBySn);
            return userOperationLogDetail;
        }).collect(Collectors.toList());
        userOperationLogDetailService.saveBatch(collect);
    }
}
