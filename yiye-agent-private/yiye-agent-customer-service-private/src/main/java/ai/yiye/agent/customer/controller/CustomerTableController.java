package ai.yiye.agent.customer.controller;

import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.customer.config.CustomerConfig;
import ai.yiye.agent.customer.mapper.CustomerMapper;
import ai.yiye.agent.customer.mapper.SubmitDataMapper;
import ai.yiye.agent.domain.dto.CreateTableParams;
import ai.yiye.agent.domain.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @Date 2025/2/25 9:42
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("customer/table")
public class CustomerTableController {

    @Resource
    private CustomerMapper customerMapper;
    @Resource
    private CustomerConfig customerConfig;

    @Resource
    private SubmitDataMapper submitDataMapper;

    @PostMapping("create")
    public Result<String> createTable(@RequestParam("agentId") String agentId, @RequestParam("param") String date) {
        TenantContextHolder.set(agentId);
        log.info("账户{},customer分表,date:{}", agentId, date);
        createTablePartition(customerConfig.getCustomerPartitionTableName(), date);
        return Result.success("");
    }

    @PostMapping("submit/create")
    public Result<String> createSubmitTable(@RequestParam("agentId") String agentId, @RequestParam("param") String date) {
        TenantContextHolder.set(agentId);
        log.info("账户{},submit分表,date:{}", agentId, date);
        createTablePartition(customerConfig.getSubmitDataPartitionTableName(), date);
        return Result.success("");
    }

    //创建分区
    private void createTablePartition(String originalTableName, String date) {
        LocalDateTime startOfMonth = null;
        LocalDateTime startOfNextMonth = null;
        //指定日期
        if (StringUtils.isNotBlank(date)) {
            startOfMonth = LocalDateTime.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            startOfMonth = startOfMonth.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
            startOfNextMonth = startOfMonth.plusMonths(1);
        } else {
            // 获取下月开始时间
            startOfMonth = LocalDateTime.now()
                .plusMonths(1) //增加一个月
                .withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
            // 获取下下个月的开始时间
            startOfNextMonth = LocalDateTime.now()
                .plusMonths(2) // 增加两个月
                .withDayOfMonth(1) // 设置为下个月的第一天
                .withHour(0) // 设置小时为 0
                .withMinute(0) // 设置分钟为 0
                .withSecond(0) // 设置秒为 0
                .withNano(0); // 设置纳秒为 0
        }
        int year = startOfMonth.getYear();
        int month = startOfMonth.getMonth().getValue();
        String tableName = originalTableName + "_" + year + "_" + month;
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentMonthDate = startOfMonth.format(formatter);
        String nextMonthDate = startOfNextMonth.format(formatter);
        CreateTableParams createTableParams = new CreateTableParams(originalTableName, tableName, String.valueOf(month), currentMonthDate, nextMonthDate);
        try {
            //客资表按月分分区
            customerMapper.createTableSubmit(createTableParams);
        } catch (Exception ex) {
            log.error("客资表按月分分区-任务调度异常", ex);
        }
    }

}
