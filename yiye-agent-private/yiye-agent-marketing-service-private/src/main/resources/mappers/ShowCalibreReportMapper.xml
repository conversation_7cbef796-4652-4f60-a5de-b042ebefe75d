<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.yiye.agent.marketing.mapper.ShowCalibreReportMapper">
    <sql id="old_result_column">
        <if test="map.containsKey('viewNum')">
            view_num,
        </if>
        <if test="map.containsKey('clickNum')">
            click_num,
        </if>
        <if test="map.containsKey('cost')">
            cost,
        </if>
        <if test="map.containsKey('thousandImpressAvgPrice')">
            thousand_impress_avg_price,
        </if>
        <if test="map.containsKey('avgPrice')">
            avg_price,
        </if>
        <if test="map.containsKey('landingPagePv')">
            landing_page_pv,
        </if>
        <if test="map.containsKey('landingPageUv')">
            landing_page_uv,
        </if>
        <if test="map.containsKey('fillCountNum')">
            fill_count_num,
        </if>
        <if test="map.containsKey('orderNum')">
            order_num,
        </if>
        <if test="map.containsKey('orderFinishNum')">
            order_finish_num,
        </if>
        <if test="map.containsKey('landingAvgStay')">
            landing_avg_stay,
        </if>
        <if test="map.containsKey('formAppointmentNum')">
            form_appointment_num,
        </if>
        <if test="map.containsKey('formAppointmentPersonCount')">
            form_appointment_person_count,
        </if>
        <if test="map.containsKey('orderAmount')">
            order_amount,
        </if>
        <if test="map.containsKey('orderUnitPrice')">
            order_unit_price,
        </if>
        <if test="map.containsKey('orderROI')">
            order_roi,
        </if>
        <if test="map.containsKey('paymentNum')">
            payment_num,
        </if>
        <if test="map.containsKey('paymentAmount')">
            payment_amount,
        </if>
        <if test="map.containsKey('firstPaymentPersonNum')">
            first_payment_person_num,
        </if>
        <if test="map.containsKey('officialFocusNum')">
            official_focus_num,
        </if>
        <if test="map.containsKey('saleClueNum')">
            sale_clue_num,
        </if>
        <if test="map.containsKey('saleCluePersonNum')">
            sale_clue_person_num,
        </if>
        <if test="map.containsKey('validClueNum')">
            valid_clue_num,
        </if>
        <if test="map.containsKey('validCluePersonNum')">
            valid_clue_person_num,
        </if>
        <if test="map.containsKey('officialFocusCount')">
            official_focus_count,
        </if>
        <if test="map.containsKey('officialFocusCount1')">
            official_focus_num,
        </if>
        <if test="map.containsKey('validClueCount')">
            valid_clue_count,
        </if>
        <if test="map.containsKey('callLinkCount')">
            call_link_count,
        </if>
        <if test="map.containsKey('personWechatLinkCount')">
            person_wechat_link_count,
        </if>
        <if test="map.containsKey('appointmentCount')">
            appointment_count,
        </if>
        <if test="map.containsKey('auditionCount')">
            audition_count,
        </if>
        <if test="map.containsKey('auditionedClassCount')">
            auditioned_class_count,
        </if>
        <if test="map.containsKey('trialCount')">
            trial_count,
        </if>
        <if test="map.containsKey('paymentDepositCount')">
            payment_deposit_count,
        </if>
        <if test="map.containsKey('payCount')">
            pay_count,
        </if>
        <if test="map.containsKey('convertCount')">
            convert_count,
        </if>
        <if test="map.containsKey('registerCount')">
            register_count,
        </if>
        <if test="map.containsKey('activationCount')">
            activation_count,
        </if>
        <if test="map.containsKey('appDownloadFinishCount')">
            app_download_finish_count,
        </if>
        <if test="map.containsKey('appInstallCount')">
            app_install_count,
        </if>
        <if test="map.containsKey('appActivationNum')">
            app_activation_num,
        </if>
        <if test="map.containsKey('appRegisterNum')">
            app_register_num,
        </if>
        <if test="map.containsKey('appRetainedPersonNum')">
            app_retained_person_num,
        </if>
        <if test="map.containsKey('appPayNum')">
            app_pay_num,
        </if>
        <if test="map.containsKey('appPayAmount')">
            app_pay_amount,
        </if>
        <if test="map.containsKey('lengthOfStay')">
            length_of_stay,
        </if>
        <if test="map.containsKey('followNum')">
            follow_num,
        </if>
        <if test="map.containsKey('clickRate')">
            click_rate,
        </if>
        <if test="map.containsKey('fillCountRate')">
            fill_count_rate,
        </if>
        <if test="map.containsKey('orderCountRate')">
            order_count_rate,
        </if>
        <if test="map.containsKey('fillCountCost')">
            fill_count_cost,
        </if>
        <if test="map.containsKey('orderFinishRate')">
            order_finish_rate,
        </if>
        <if test="map.containsKey('orderCountCost')">
            order_count_cost,
        </if>
        <if test="map.containsKey('orderFinishCost')">
            order_finish_cost,
        </if>
        <if test="map.containsKey('formOrderConvertRate')">
            form_order_convert_rate,
        </if>
        <if test="map.containsKey('formAppointmentRate')">
            form_appointment_rate,
        </if>
        <if test="map.containsKey('buttonFormConvert')">
            button_form_convert,
        </if>
        <if test="map.containsKey('formAppointmentCost')">
            form_appointment_cost,
        </if>
        <if test="map.containsKey('saleClueConvertRate')">
            sale_clue_convert_rate,
        </if>
        <if test="map.containsKey('validClueConvertRate')">
            valid_clue_convert_rate,
        </if>
        <if test="map.containsKey('appDownloadRate')">
            app_download_rate,
        </if>
        <if test="map.containsKey('appInstallRate')">
            app_install_rate,
        </if>
        <if test="map.containsKey('appClickActivationRate')">
            app_click_activation_rate,
        </if>
        <if test="map.containsKey('appDownloadActivationRate')">
            app_download_activation_rate,
        </if>
        <if test="map.containsKey('appDownloadActivationCost')">
            app_download_activation_cost,
        </if>
        <if test="map.containsKey('appRegisterRate')">
            app_register_rate,
        </if>
        <if test="map.containsKey('appActivationRegisterRate')">
            app_activation_register_rate,
        </if>
        <if test="map.containsKey('appRetainedRate')">
            app_retained_rate,
        </if>
        <if test="map.containsKey('paymentCost')">
            payment_cost,
        </if>
        <if test="map.containsKey('focusCost')">
            focus_cost,
        </if>
        <if test="map.containsKey('officialFocusCost1')">
            official_focus_cost1,
        </if>
        <if test="map.containsKey('saleClueCost')">
            sale_clue_cost,
        </if>
        <if test="map.containsKey('validClueCost1')">
            valid_clue_cost1,
        </if>
        <if test="map.containsKey('officialFocusCost2')">
            official_focus_cost2,
        </if>
        <if test="map.containsKey('validClueCost2')">
            valid_clue_cost2,
        </if>
        <if test="map.containsKey('callLinkCost')">
            call_link_cost,
        </if>
        <if test="map.containsKey('personWechatLinkCost')">
            person_wechat_link_cost,
        </if>
        <if test="map.containsKey('appointmentCost')">
            appointment_cost,
        </if>
        <if test="map.containsKey('tryListenCost')">
            try_listen_cost,
        </if>
        <if test="map.containsKey('auditionedClassCost')">
            auditioned_class_cost,
        </if>
        <if test="map.containsKey('trialCost')">
            trial_cost,
        </if>
        <if test="map.containsKey('paymentDepositCost')">
            payment_deposit_cost,
        </if>
        <if test="map.containsKey('payCost')">
            pay_cost,
        </if>
        <if test="map.containsKey('convertCost')">
            convert_cost,
        </if>
        <if test="map.containsKey('registerCost')">
            register_cost,
        </if>
        <if test="map.containsKey('activationCost')">
            activation_cost,
        </if>
        <if test="map.containsKey('appDownloadCost')">
            app_download_cost,
        </if>
        <if test="map.containsKey('appInstallCost')">
            app_install_cost,
        </if>
        <if test="map.containsKey('appRegisterCost')">
            app_register_cost,
        </if>
        <if test="map.containsKey('appRetainedCost')">
            app_retained_cost,
        </if>
        <if test="map.containsKey('appPayCost')">
            app_pay_cost,
        </if>
        <if test="map.containsKey('convertNum')">
            convert_num,
        </if>
        <if test="map.containsKey('targetConvertRate')">
            target_convert_rate,
        </if>
        <if test="map.containsKey('targetConvertCost')">
            target_convert_cost,
        </if>
        <if test="map.containsKey('deepConvertNum')">
            deep_convert_num,
        </if>
        <if test="map.containsKey('deepConvertRate')">
            deep_convert_rate,
        </if>
        <if test="map.containsKey('deepConvertCost')">
            deep_convert_cost,
        </if>
        <if test="map.containsKey('placeOrderNum')">
            place_order_num,
        </if>
        <if test="map.containsKey('placeOrderRate')">
            place_order_rate,
        </if>
        <if test="map.containsKey('placeOrderCost')">
            place_order_cost,
        </if>
        <if test="map.containsKey('accountName')">
            mda.account_name as account_name,
        </if>
        <if test="map.containsKey('campaignName')">
            mdc.campaign_name as campaign_name,
        </if>
        <if test="map.containsKey('creativeType')">
            mas.describe as creative_type,
        </if>
        <if test="map.containsKey('promotionGoal')">
            mas.describe as promotion_goal,
        </if>
        <if test="map.containsKey('adgroupName')">
            mdap.name as adgroup_name,
        </if>
        <if test="map.containsKey('adgroupStatus')">
            mass.describe as adgroup_status,
        </if>
        <if test="map.containsKey('creativeName')">
            mdce.creative_name as creative_name,
        </if>
        <if test="map.containsKey('balance')">
            COALESCE (mda.balance, 0) as balance,
        </if>
        <if test="map.containsKey('campaignStatus')">
            mdc.status as campaign_status,
        </if>
        <if test="map.containsKey('optimizationGoal')">
            mct.description as optimization_goal,
        </if>
        <if test="map.containsKey('identifyQrCodeNum')">
            identify_qr_code_num as identify_qr_code_num,
        </if>
        <if test="map.containsKey('identifyQrCodeRate')">
            identify_qr_code_rate as identify_qr_code_rate,
        </if>
        <if test="map.containsKey('identifyQrcodeCost')">
            identify_qrcode_cost as identify_qrcode_cost,
        </if>
        <if test="map.containsKey('addWorkWechatNum')">
            add_work_wechat_num as add_work_wechat_num,
        </if>
        <if test="map.containsKey('addWorkWechatRate')">
            add_work_wechat_rate as add_work_wechat_rate,
        </if>
        <if test="map.containsKey('addWorkWechatCost')">
            add_work_wechat_cost as add_work_wechat_cost,
        </if>
        <if test="map.containsKey('clueFillNum')">
            clue_fill_num as clue_fill_num,
        </if>
        <if test="map.containsKey('clueFillRate')">
            clue_fill_rate as clue_fill_rate,
        </if>
        <if test="map.containsKey('clueFillCost')">
            clue_fill_cost as clue_fill_cost,
        </if>
        <if test="map.containsKey('clueConnectNum')">
            clue_connect_num as clue_connect_num,
        </if>
        <if test="map.containsKey('clueConnectRate')">
            clue_connect_rate as clue_connect_rate,
        </if>
        <if test="map.containsKey('clueConnectCost')">
            clue_connect_cost as clue_connect_cost,
        </if>
        <if test="map.containsKey('clueEffectiveNum')">
            clue_effective_num as clue_effective_num,
        </if>
        <if test="map.containsKey('clueEffectiveRate')">
            clue_effective_rate as clue_effective_rate,
        </if>
        <if test="map.containsKey('clueEffectiveCost')">
            clue_effective_cost as clue_effective_cost,
        </if>
        <if test="map.containsKey('signClassNum')">
            sign_class_num as sign_class_num,
        </if>
        <if test="map.containsKey('signClassRate')">
            sign_class_rate as sign_class_rate,
        </if>
        <if test="map.containsKey('signClassCost')">
            sign_class_cost as sign_class_cost,
        </if>
        <if test="map.containsKey('totalConvertRate')">
            total_convert_rate total_convert_rate,
        </if>
        <if test="map.containsKey('topPvWinA')">
            top_pv_win_a top_pv_win_a,
        </if>
        <if test="map.containsKey('topPvWinP')">
            top_pv_win_p top_pv_win_p,
        </if>
        <if test="map.containsKey('topPageViews')">
            top_page_views top_page_views,
        </if>
        <if test="map.containsKey('topPClicks')">
            top_p_clicks top_p_clicks,
        </if>
        <if test="map.containsKey('topPay')">
            top_pay top_pay,
        </if>
        <!-- 1.155.0 电商商品购买数 -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessNum')">
            online_shop_buy_goods_success_num online_shop_buy_goods_success_num,
        </if>
        <!-- 1.155.0 电商商品成交金额 -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessAmount')">
            online_shop_buy_goods_success_amount online_shop_buy_goods_success_amount,
        </if>
        <!-- 1.155.0 电商商品单笔购买成本 -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessCost')">
            online_shop_buy_goods_success_cost online_shop_buy_goods_success_cost,
        </if>
        <!-- 1.155.0 电商商品成交ROI -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessRoi')">
            online_shop_buy_goods_success_roi online_shop_buy_goods_success_roi,
        </if>
        <!-- 1.197.0 电商商品成交ROI -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessRoi')">
            online_shop_buy_goods_success_roi online_shop_buy_goods_success_roi,
        </if>
        <!-- 1.197.0 -->
        <if test="map.containsKey('identifyGroupQrCodeNum')">
            identify_group_qr_code_num,
        </if>
        <if test="map.containsKey('addWorkWechatGroupNum')">
            add_work_wechat_group_num,
        </if>
        <if test="map.containsKey('officialIdentifyQrCodeNum')">
            official_identify_qr_code_num,
        </if>

        <if test="map.containsKey('identifyGroupQrCodeRate')">
            identify_group_qr_code_rate,
        </if>
        <if test="map.containsKey('addWorkWechatGroupRate')">
            add_work_wechat_group_rate,
        </if>
        <if test="map.containsKey('officialIdentifyQrCodeRate')">
            official_identify_qr_code_rate,
        </if>

        <if test="map.containsKey('identifyGroupQrCodeCost')">
            identify_group_qr_code_cost,
        </if>
        <if test="map.containsKey('addWorkWechatGroupCost')">
            add_work_wechat_group_cost,
        </if>
        <if test="map.containsKey('enterpriseIncreaseIncomingCost')">
            enterprise_increase_incoming_cost,
        </if>
        <!-- 1.197.0 结束-->

        <!-- 1.198.0 公众号历史文章页PV      -->
        <if test="map.containsKey('wechatOfficialArticlePageViewNum')">
            wechat_official_article_page_view_num wechat_official_article_page_view_num,
        </if>

    </sql>
    <sql id="result_column">
        <if test="map.containsKey('viewNum')">
            view_num,
        </if>
        <if test="map.containsKey('clickNum')">
            click_num,
        </if>
        <if test="map.containsKey('cost')">
            cost,
        </if>
        <if test="map.containsKey('thousandImpressAvgPrice')">
            thousand_impress_avg_price,
        </if>
        <if test="map.containsKey('avgPrice')">
            avg_price,
        </if>
        <if test="map.containsKey('landingPagePv')">
            landing_page_pv,
        </if>
        <if test="map.containsKey('landingPageUv')">
            landing_page_uv,
        </if>
        <if test="map.containsKey('fillCountNum')">
            fill_count_num,
        </if>
        <if test="map.containsKey('orderNum')">
            order_num,
        </if>
        <if test="map.containsKey('orderFinishNum')">
            order_finish_num,
        </if>
        <if test="map.containsKey('landingAvgStay')">
            landing_avg_stay,
        </if>
        <if test="map.containsKey('formAppointmentNum')">
            form_appointment_num,
        </if>
        <if test="map.containsKey('formAppointmentPersonCount')">
            form_appointment_person_count,
        </if>
        <if test="map.containsKey('orderAmount')">
            order_amount,
        </if>
        <if test="map.containsKey('orderUnitPrice')">
            order_unit_price,
        </if>
        <if test="map.containsKey('orderROI')">
            order_roi,
        </if>
        <if test="map.containsKey('paymentNum')">
            payment_num,
        </if>
        <if test="map.containsKey('paymentAmount')">
            payment_amount,
        </if>
        <if test="map.containsKey('firstPaymentPersonNum')">
            first_payment_person_num,
        </if>
        <if test="map.containsKey('officialFocusNum')">
            official_focus_num,
        </if>
        <if test="map.containsKey('saleClueNum')">
            sale_clue_num,
        </if>
        <if test="map.containsKey('saleCluePersonNum')">
            sale_clue_person_num,
        </if>
        <if test="map.containsKey('validClueNum')">
            valid_clue_num,
        </if>
        <if test="map.containsKey('validCluePersonNum')">
            valid_clue_person_num,
        </if>
        <if test="map.containsKey('officialFocusCount')">
            official_focus_count,
        </if>
        <if test="map.containsKey('officialFocusCount1')">
            official_focus_num,
        </if>
        <if test="map.containsKey('validClueCount')">
            valid_clue_count,
        </if>
        <if test="map.containsKey('callLinkCount')">
            call_link_count,
        </if>
        <if test="map.containsKey('personWechatLinkCount')">
            person_wechat_link_count,
        </if>
        <if test="map.containsKey('appointmentCount')">
            appointment_count,
        </if>
        <if test="map.containsKey('auditionCount')">
            audition_count,
        </if>
        <if test="map.containsKey('auditionedClassCount')">
            auditioned_class_count,
        </if>
        <if test="map.containsKey('trialCount')">
            trial_count,
        </if>
        <if test="map.containsKey('paymentDepositCount')">
            payment_deposit_count,
        </if>
        <if test="map.containsKey('payCount')">
            pay_count,
        </if>
        <if test="map.containsKey('convertCount')">
            convert_count,
        </if>
        <if test="map.containsKey('registerCount')">
            register_count,
        </if>
        <if test="map.containsKey('activationCount')">
            activation_count,
        </if>
        <if test="map.containsKey('appDownloadFinishCount')">
            app_download_finish_count,
        </if>
        <if test="map.containsKey('appInstallCount')">
            app_install_count,
        </if>
        <if test="map.containsKey('appActivationNum')">
            app_activation_num,
        </if>
        <if test="map.containsKey('appRegisterNum')">
            app_register_num,
        </if>
        <if test="map.containsKey('appRetainedPersonNum')">
            app_retained_person_num,
        </if>
        <if test="map.containsKey('appPayNum')">
            app_pay_num,
        </if>
        <if test="map.containsKey('appPayAmount')">
            app_pay_amount,
        </if>
        <if test="map.containsKey('lengthOfStay')">
            length_of_stay,
        </if>
        <if test="map.containsKey('followNum')">
            follow_num,
        </if>
        <if test="map.containsKey('clickRate')">
            click_rate,
        </if>
        <if test="map.containsKey('fillCountRate')">
            fill_count_rate,
        </if>
        <if test="map.containsKey('orderCountRate')">
            order_count_rate,
        </if>
        <if test="map.containsKey('fillCountCost')">
            fill_count_cost,
        </if>
        <if test="map.containsKey('orderFinishRate')">
            order_finish_rate,
        </if>
        <if test="map.containsKey('orderCountCost')">
            order_count_cost,
        </if>
        <if test="map.containsKey('orderFinishCost')">
            order_finish_cost,
        </if>
        <if test="map.containsKey('formOrderConvertRate')">
            form_order_convert_rate,
        </if>
        <if test="map.containsKey('formAppointmentRate')">
            form_appointment_rate,
        </if>
        <if test="map.containsKey('buttonFormConvert')">
            button_form_convert,
        </if>
        <if test="map.containsKey('formAppointmentCost')">
            form_appointment_cost,
        </if>
        <if test="map.containsKey('saleClueConvertRate')">
            sale_clue_convert_rate,
        </if>
        <if test="map.containsKey('validClueConvertRate')">
            valid_clue_convert_rate,
        </if>
        <if test="map.containsKey('appDownloadRate')">
            app_download_rate,
        </if>
        <if test="map.containsKey('appInstallRate')">
            app_install_rate,
        </if>
        <if test="map.containsKey('appClickActivationRate')">
            app_click_activation_rate,
        </if>
        <if test="map.containsKey('appDownloadActivationRate')">
            app_download_activation_rate,
        </if>
        <if test="map.containsKey('appDownloadActivationCost')">
            app_download_activation_cost,
        </if>
        <if test="map.containsKey('appRegisterRate')">
            app_register_rate,
        </if>
        <if test="map.containsKey('appActivationRegisterRate')">
            app_activation_register_rate,
        </if>
        <if test="map.containsKey('appRetainedRate')">
            app_retained_rate,
        </if>
        <if test="map.containsKey('paymentCost')">
            payment_cost,
        </if>
        <if test="map.containsKey('focusCost')">
            focus_cost,
        </if>
        <if test="map.containsKey('officialFocusCost1')">
            official_focus_cost1,
        </if>
        <if test="map.containsKey('saleClueCost')">
            sale_clue_cost,
        </if>
        <if test="map.containsKey('validClueCost1')">
            valid_clue_cost1,
        </if>
        <if test="map.containsKey('officialFocusCost2')">
            official_focus_cost2,
        </if>
        <if test="map.containsKey('validClueCost2')">
            valid_clue_cost2,
        </if>
        <if test="map.containsKey('callLinkCost')">
            call_link_cost,
        </if>
        <if test="map.containsKey('personWechatLinkCost')">
            person_wechat_link_cost,
        </if>
        <if test="map.containsKey('appointmentCost')">
            appointment_cost,
        </if>
        <if test="map.containsKey('tryListenCost')">
            try_listen_cost,
        </if>
        <if test="map.containsKey('auditionedClassCost')">
            auditioned_class_cost,
        </if>
        <if test="map.containsKey('trialCost')">
            trial_cost,
        </if>
        <if test="map.containsKey('paymentDepositCost')">
            payment_deposit_cost,
        </if>
        <if test="map.containsKey('payCost')">
            pay_cost,
        </if>
        <if test="map.containsKey('convertCost')">
            convert_cost,
        </if>
        <if test="map.containsKey('registerCost')">
            register_cost,
        </if>
        <if test="map.containsKey('activationCost')">
            activation_cost,
        </if>
        <if test="map.containsKey('appDownloadCost')">
            app_download_cost,
        </if>
        <if test="map.containsKey('appInstallCost')">
            app_install_cost,
        </if>
        <if test="map.containsKey('appRegisterCost')">
            app_register_cost,
        </if>
        <if test="map.containsKey('appRetainedCost')">
            app_retained_cost,
        </if>
        <if test="map.containsKey('appPayCost')">
            app_pay_cost,
        </if>
        <if test="map.containsKey('convertNum')">
            convert_num,
        </if>
        <if test="map.containsKey('targetConvertRate')">
            target_convert_rate,
        </if>
        <if test="map.containsKey('targetConvertCost')">
            target_convert_cost,
        </if>
        <if test="map.containsKey('deepConvertNum')">
            deep_convert_num,
        </if>
        <if test="map.containsKey('deepConvertRate')">
            deep_convert_rate,
        </if>
        <if test="map.containsKey('deepConvertCost')">
            deep_convert_cost,
        </if>
        <if test="map.containsKey('placeOrderNum')">
            place_order_num,
        </if>
        <if test="map.containsKey('placeOrderRate')">
            place_order_rate,
        </if>
        <if test="map.containsKey('placeOrderCost')">
            place_order_cost,
        </if>
        <if test="map.containsKey('accountName')">
            mda.account_name as account_name,
        </if>
        <if test="map.containsKey('campaignName')">
            mdc.campaign_name as campaign_name,
        </if>
        <if test="map.containsKey('creativeType')">
            mas.describe as creative_type,
        </if>
        <if test="map.containsKey('promotionGoal')">
            mas.describe as promotion_goal,
        </if>
        <if test="map.containsKey('adgroupName')">
            mdap.name as adgroup_name,
        </if>
        <if test="map.containsKey('adgroupStatus')">
            mass.describe as adgroup_status,
        </if>
        <if test="map.containsKey('creativeName')">
            mdce.creative_name as creative_name,
        </if>
        <if test="map.containsKey('balance')">
            COALESCE (mda.balance, 0) as balance,
        </if>
        <if test="map.containsKey('campaignStatus')">
            mdc.status as campaign_status,
        </if>
        <if test="map.containsKey('optimizationGoal')">
            mct.description as optimization_goal,
        </if>
        <if test="map.containsKey('identifyQrCodeNum')">
            identify_qr_code_num as identify_qr_code_num,
        </if>
        <if test="map.containsKey('identifyQrCodeRate')">
            identify_qr_code_rate as identify_qr_code_rate,
        </if>
        <if test="map.containsKey('identifyQrcodeCost')">
            identify_qrcode_cost as identify_qrcode_cost,
        </if>
        <if test="map.containsKey('addWorkWechatNum')">
            add_work_wechat_num as add_work_wechat_num,
        </if>
        <if test="map.containsKey('addWorkWechatRate')">
            add_work_wechat_rate as add_work_wechat_rate,
        </if>
        <if test="map.containsKey('addWorkWechatCost')">
            add_work_wechat_cost as add_work_wechat_cost,
        </if>
        <if test="map.containsKey('clueFillNum')">
            clue_fill_num as clue_fill_num,
        </if>
        <if test="map.containsKey('clueFillRate')">
            clue_fill_rate as clue_fill_rate,
        </if>
        <if test="map.containsKey('clueFillCost')">
            clue_fill_cost as clue_fill_cost,
        </if>
        <if test="map.containsKey('clueConnectNum')">
            clue_connect_num as clue_connect_num,
        </if>
        <if test="map.containsKey('clueConnectRate')">
            clue_connect_rate as clue_connect_rate,
        </if>
        <if test="map.containsKey('clueConnectCost')">
            clue_connect_cost as clue_connect_cost,
        </if>
        <if test="map.containsKey('clueEffectiveNum')">
            clue_effective_num as clue_effective_num,
        </if>
        <if test="map.containsKey('clueEffectiveRate')">
            clue_effective_rate as clue_effective_rate,
        </if>
        <if test="map.containsKey('clueEffectiveCost')">
            clue_effective_cost as clue_effective_cost,
        </if>
        <if test="map.containsKey('signClassNum')">
            sign_class_num as sign_class_num,
        </if>
        <if test="map.containsKey('signClassRate')">
            sign_class_rate as sign_class_rate,
        </if>
        <if test="map.containsKey('signClassCost')">
            sign_class_cost as sign_class_cost,
        </if>
        <if test="map.containsKey('totalConvertRate')">
            total_convert_rate total_convert_rate,
        </if>
        <if test="map.containsKey('topPvWinA')">
            top_pv_win_a top_pv_win_a,
        </if>
        <if test="map.containsKey('topPvWinP')">
            top_pv_win_p top_pv_win_p,
        </if>
        <if test="map.containsKey('topPageViews')">
            top_page_views top_page_views,
        </if>
        <if test="map.containsKey('topPClicks')">
            top_p_clicks top_p_clicks,
        </if>
        <if test="map.containsKey('topPay')">
            top_pay top_pay,
        </if>
        <!-- 1.155.0 电商商品购买数 -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessNum')">
            online_shop_buy_goods_success_num online_shop_buy_goods_success_num,
        </if>
        <!-- 1.155.0 电商商品成交金额 -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessAmount')">
            online_shop_buy_goods_success_amount online_shop_buy_goods_success_amount,
        </if>
        <!-- 1.155.0 电商商品单笔购买成本 -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessCost')">
            online_shop_buy_goods_success_cost online_shop_buy_goods_success_cost,
        </if>
        <!-- 1.155.0 电商商品成交ROI -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessRoi')">
            online_shop_buy_goods_success_roi online_shop_buy_goods_success_roi,
        </if>
        <!-- 1.179.0 投放面板优化-->
        <if test="map.containsKey('orderTransactionAmount')">
            order_transaction_amount order_transaction_amount,
        </if>

        <if test="map.containsKey('orderTransactionRoi')">
            order_transaction_roi order_transaction_roi,
        </if>

        <if test="map.containsKey('enterpriseIncreaseIncomingRate')">
            enterprise_increase_incoming_rate enterprise_increase_incoming_rate,
        </if>

        <if test="map.containsKey('officialIdentifyQrCodeNum')">
            official_identify_qr_code_num official_identify_qr_code_num,
        </if>

        <if test="map.containsKey('officialIdentifyQrCodeRate')">
            official_identify_qr_code_rate official_identify_qr_code_rate,
        </if>

        <if test="map.containsKey('enterpriseIncreaseIncomingCost')">
            enterprise_increase_incoming_cost enterprise_increase_incoming_cost,
        </if>

        <if test="map.containsKey('followOfficialAccountNum')">
            follow_official_account_num follow_official_account_num,
        </if>

        <if test="map.containsKey('followOfficialAccountRate')">
            follow_official_account_rate follow_official_account_rate,
        </if>

        <if test="map.containsKey('followOfficialAccountIncomingRate')">
            follow_official_account_incoming_rate follow_official_account_incoming_rate,
        </if>

        <if test="map.containsKey('followOfficialAccountCost')">
            follow_official_account_cost follow_official_account_cost,
        </if>

        <if test="map.containsKey('onlineShopBuyGoodsSuccessRate')">
            online_shop_buy_goods_success_rate online_shop_buy_goods_success_rate,
        </if>

        <if test="map.containsKey('playNum')">
            play_num play_num,
        </if>
        <if test="map.containsKey('validPlayNum')">
            valid_play_num valid_play_num,
        </if>
        <if test="map.containsKey('videoPlay25Num')">
            video_play25_num video_play25_num,
        </if>
        <if test="map.containsKey('videoPlay50Num')">
            video_play50_num video_play50_num,
        </if>
        <if test="map.containsKey('videoPlay75Num')">
            video_play75_num video_play75_num,
        </if>
        <if test="map.containsKey('videoPlay100Num')">
            video_play100_num video_play100_num,
        </if>
        <if test="map.containsKey('thumbUpCount')">
            praise_num thumb_up_count,
        </if>
        <if test="map.containsKey('discussCount')">
            comment_num discuss_count,
        </if>
        <if test="map.containsKey('shareCount')">
            share_num share_count,
        </if>
        <if test="map.containsKey('trafficRatio')">
            traffic_ratio,
        </if>

        <if test="map.containsKey('workWechatOpenConsultationNum')">
            work_wechat_open_consultation_num,
        </if>
        <if test="map.containsKey('workWechatOpenConsultationRate')">
            work_wechat_open_consultation_rate,
        </if>
        <if test="map.containsKey('workWechatOpenConsultationCost')">
            work_wechat_open_consultation_cost,
        </if>
        <if test="map.containsKey('workWechatQuotationConsultationNum')">
            work_wechat_quotation_consultation_num,
        </if>
        <if test="map.containsKey('workWechatQuotationConsultationRate')">
            work_wechat_quotation_consultation_rate,
        </if>
        <if test="map.containsKey('workWechatQuotationConsultationCost')">
            work_wechat_quotation_consultation_cost,
        </if>
        <if test="map.containsKey('workWechatOrderFilledNum')">
            work_wechat_order_filled_num,
        </if>
        <if test="map.containsKey('workWechatOrderFilledRate')">
            work_wechat_order_filled_rate,
        </if>
        <if test="map.containsKey('workWechatOrderFilledCost')">
            work_wechat_order_filled_cost,
        </if>
        <!-- 1.197.0 -->
        <if test="map.containsKey('identifyGroupQrCodeNum')">
            identify_group_qr_code_num,
        </if>
        <if test="map.containsKey('addWorkWechatGroupIncomingRate')">
            add_work_wechat_group_incoming_rate,
        </if>
        <if test="map.containsKey('addWorkWechatGroupNum')">
            add_work_wechat_group_num,
        </if>
        <if test="map.containsKey('identifyGroupQrCodeRate')">
            identify_group_qr_code_rate,
        </if>
        <if test="map.containsKey('addWorkWechatGroupRate')">
             add_work_wechat_group_rate,
        </if>
        <if test="map.containsKey('identifyGroupQrCodeCost')">
            identify_group_qr_code_cost,
        </if>
        <if test="map.containsKey('addWorkWechatGroupCost')">
            add_work_wechat_group_cost,
        </if>
        <if test="map.containsKey('enterpriseIncreaseIncomingCost')">
            enterprise_increase_incoming_cost,
        </if>
        <!-- 1.197.0 结束-->
        <!-- 1.198.0 公众号历史文章页PV      -->
        <if test="map.containsKey('wechatOfficialArticlePageViewNum')">
            wechat_official_article_page_view_num,
        </if>
    </sql>
    <sql id="base_column">
        <if test="map.containsKey('viewNum')">
            COALESCE(sum(view_num), 0) as view_num,
        </if>
        <if test="map.containsKey('clickNum')">
            COALESCE(sum(click_num), 0) as click_num,
        </if>
        <if test="map.containsKey('cost')">
            COALESCE(sum(cost), 0) / 100 as cost,
        </if>
        <if test="map.containsKey('thousandImpressAvgPrice')">
            case when COALESCE(sum(view_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC)
            THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(view_num), 0) * 10 end as thousand_impress_avg_price,
        </if>
        <if test="map.containsKey('avgPrice')">
            case when COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC)
            THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(click_num), 0) / 100 end as avg_price,
        </if>
        <if test="map.containsKey('landingPagePv')">
            COALESCE(sum(landing_page_pv), 0) as landing_page_pv,
        </if>
        <if test="map.containsKey('landingPageUv')">
            COALESCE(sum(landing_page_uv), 0) as landing_page_uv,
        </if>
        <if test="map.containsKey('fillCountNum')">
            COALESCE(sum(fill_count_num), 0) as fill_count_num,
        </if>
        <if test="map.containsKey('orderNum')">
            COALESCE(sum(order_num), 0) as order_num,
        </if>
        <if test="map.containsKey('orderFinishNum')">
            COALESCE(sum(order_finish_num), 0) as order_finish_num,
        </if>
        <if test="map.containsKey('landingAvgStay')">
            case when COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(length_of_stay), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(length_of_stay), 0) / COALESCE(sum(landing_page_pv), 0) end as landing_avg_stay,
        </if>
        <if test="map.containsKey('formAppointmentNum')">
            COALESCE(sum(form_appointment_num), 0) as form_appointment_num,
        </if>
        <if test="map.containsKey('formAppointmentPersonCount')">
            COALESCE(sum(form_appointment_person_count), 0) as form_appointment_person_count,
        </if>
        <if test="map.containsKey('orderAmount')">
            COALESCE(sum(order_amount), 0) / 100 as order_amount,
        </if>
        <if test="map.containsKey('orderUnitPrice')">
            case when COALESCE(sum(order_amount), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(place_order_num), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(order_amount), 0) / COALESCE(sum(place_order_num), 0) / 100 end as order_unit_price,
        </if>
        <if test="map.containsKey('orderROI')">
            case when COALESCE(sum(order_amount), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            (COALESCE(sum(order_amount), 0)/100) / (COALESCE(sum(cost), 0)/100) end as order_roi,
        </if>
        <if test="map.containsKey('paymentNum')">
            COALESCE(sum(payment_num), 0) as payment_num,
        </if>
        <if test="map.containsKey('paymentAmount')">
            COALESCE(sum(payment_amount), 0) / 100 as payment_amount,
        </if>
        <if test="map.containsKey('firstPaymentPersonNum')">
            COALESCE(sum(first_payment_person_num), 0) as first_payment_person_num,
        </if>
        <if test="map.containsKey('officialFocusNum')">
            COALESCE(sum(official_focus_num), 0) as official_focus_num,
        </if>
        <if test="map.containsKey('saleClueNum')">
            COALESCE(sum(sale_clue_num), 0) as sale_clue_num,
        </if>
        <if test="map.containsKey('saleCluePersonNum')">
            COALESCE(sum(sale_clue_person_num), 0) as sale_clue_person_num,
        </if>
        <if test="map.containsKey('validClueNum')">
            COALESCE(sum(valid_clue_num), 0) as valid_clue_num,
        </if>
        <if test="map.containsKey('validCluePersonNum')">
            COALESCE(sum(valid_clue_person_num), 0) as valid_clue_person_num,
        </if>
        <if test="map.containsKey('officialFocusCount')">
            COALESCE(sum(official_focus_count), 0) as official_focus_count,
        </if>
        <if test="map.containsKey('officialFocusCount1')">
            COALESCE(sum(official_focus_num), 0) as official_focus_num,
        </if>
        <if test="map.containsKey('validClueCount')">
            COALESCE(sum(valid_clue_count), 0) as valid_clue_count,
        </if>
        <if test="map.containsKey('callLinkCount')">
            COALESCE(sum(call_link_count), 0) as call_link_count,
        </if>
        <if test="map.containsKey('personWechatLinkCount')">
            COALESCE(sum(person_wechat_link_count), 0) as person_wechat_link_count,
        </if>
        <if test="map.containsKey('appointmentCount')">
            COALESCE(sum(appointment_count), 0) as appointment_count,
        </if>
        <if test="map.containsKey('auditionCount')">
            COALESCE(sum(audition_count), 0) as audition_count,
        </if>
        <if test="map.containsKey('auditionedClassCount')">
            COALESCE(sum(auditioned_class_count), 0) as auditioned_class_count,
        </if>
        <if test="map.containsKey('trialCount')">
            COALESCE(sum(trial_count), 0) as trial_count,
        </if>
        <if test="map.containsKey('paymentDepositCount')">
            COALESCE(sum(payment_deposit_count), 0) as payment_deposit_count,
        </if>
        <if test="map.containsKey('payCount')">
            COALESCE(sum(pay_count), 0) as pay_count,
        </if>
        <if test="map.containsKey('convertCount')">
            COALESCE(sum(convert_count), 0) as convert_count,
        </if>
        <if test="map.containsKey('registerCount')">
            COALESCE(sum(register_count), 0) as register_count,
        </if>
        <if test="map.containsKey('activationCount')">
            COALESCE(sum(activation_count), 0) as activation_count,
        </if>
        <if test="map.containsKey('appDownloadFinishCount')">
            COALESCE(sum(app_download_finish_count), 0)as app_download_finish_count,
        </if>
        <if test="map.containsKey('appInstallCount')">
            COALESCE(sum(app_install_count), 0) as app_install_count,
        </if>
        <if test="map.containsKey('appActivationNum')">
            COALESCE(sum(app_activation_count), 0) as app_activation_num,
        </if>
        <if test="map.containsKey('appRegisterNum')">
            COALESCE(sum(app_register_count), 0) as app_register_num,
        </if>
        <if test="map.containsKey('appRetainedPersonNum')">
            COALESCE(sum(app_retained_person_num), 0) as app_retained_person_num,
        </if>
        <if test="map.containsKey('appPayNum')">
            COALESCE(sum(app_pay_num), 0) as app_pay_num,
        </if>
        <if test="map.containsKey('appPayAmount')">
            COALESCE(sum(app_pay_amount), 0) as app_pay_amount,
        </if>
        <if test="map.containsKey('lengthOfStay')">
            COALESCE(sum(length_of_stay), 0) as length_of_stay,
        </if>
        <if test="map.containsKey('followNum')">
            COALESCE(sum(follow_num), 0) as follow_num,
        </if>
        <!-- 1.155.0 电商商品购买数 -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessNum')">
            COALESCE(sum(online_shop_buy_goods_product_count), 0) as online_shop_buy_goods_success_num,
        </if>
        <!-- 1.155.0 电商商品成交金额 -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessAmount')">
            COALESCE(sum(online_shop_buy_goods_order_price), 0) as online_shop_buy_goods_success_amount,
        </if>


        <if test="map.containsKey('clickRate')">
            case when COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(view_num), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(click_num), 0) / COALESCE(sum(view_num), 0) *100 end as click_rate,
        </if>
        <if test="map.containsKey('fillCountRate')">
            case when COALESCE(sum(fill_count_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(fill_count_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as fill_count_rate,
        </if>
        <if test="map.containsKey('orderCountRate')">
            case when COALESCE(sum(order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0
            AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(order_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as order_count_rate,
        </if>
        <if test="map.containsKey('fillCountCost')">
            case when COALESCE(sum(fill_count_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(fill_count_num), 0) / 100 end as fill_count_cost,
        </if>
        <if test="map.containsKey('orderFinishRate')">
            case when COALESCE(sum(order_finish_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) =
            CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(order_finish_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as order_finish_rate,
        </if>
        <if test="map.containsKey('orderCountCost')">
            case when COALESCE(sum(order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC)
            THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(order_num), 0) / 100 end as order_count_cost,
        </if>
        <if test="map.containsKey('orderFinishCost')">
            case when COALESCE(sum(order_finish_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(order_finish_num), 0) / 100 end as order_finish_cost,
        </if>
        <if test="map.containsKey('formOrderConvertRate')">
            case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(form_appointment_num), 0)
            = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(place_order_num), 0) / COALESCE(sum(form_appointment_num), 0) *100 end as
            form_order_convert_rate,
        </if>
        <if test="map.containsKey('formAppointmentRate')">
            case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(form_appointment_num), 0) / COALESCE(sum(click_num), 0) *100 end as form_appointment_rate,
        </if>
        <if test="map.containsKey('buttonFormConvert')">
            case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(button_count), 0) =
            CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(form_appointment_num), 0) / COALESCE(sum(button_count), 0) * 100 end as button_form_convert,
        </if>
        <if test="map.containsKey('formAppointmentCost')">
            case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0
            AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(form_appointment_num), 0) / 100 end as form_appointment_cost,
        </if>
        <if test="map.containsKey('saleClueConvertRate')">
            case when COALESCE(sum(sale_clue_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(sale_clue_num), 0) / COALESCE(sum(click_num), 0) *100 end as sale_clue_convert_rate,
        </if>
        <if test="map.containsKey('validClueConvertRate')">
            case when COALESCE(sum(valid_clue_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(valid_clue_num), 0) / COALESCE(sum(click_num), 0) *100 end as valid_clue_convert_rate,
        </if>
        <if test="map.containsKey('appDownloadRate')">
            case when COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) =
            CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_download_finish_count), 0) / COALESCE(sum(click_num), 0) *100 end as app_download_rate,
        </if>
        <if test="map.containsKey('appInstallRate')">
            case when COALESCE(sum(app_install_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0
            AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_install_count), 0) / COALESCE(sum(click_num), 0) *100 end as app_install_rate,
        </if>
        <if test="map.containsKey('appClickActivationRate')">
            case when COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_activation_count), 0) / COALESCE(sum(click_num), 0) *100 end as app_click_activation_rate,
        </if>
        <if test="map.containsKey('appDownloadActivationRate')">
            case when COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) or
            COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_activation_count), 0) / COALESCE(sum(app_download_finish_count), 0) *100 end as
            app_download_activation_rate,
        </if>
        <if test="map.containsKey('appDownloadActivationCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0
            AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(app_activation_count), 0) / 100 end as app_download_activation_cost,
        </if>
        <if test="map.containsKey('appRegisterRate')">
            case when COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_register_count), 0) / COALESCE(sum(click_num), 0) *100 end as app_register_rate,
        </if>
        <if test="map.containsKey('appActivationRegisterRate')">
            case when COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count),
            0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_register_count), 0) / COALESCE(sum(app_activation_count), 0) *100 end as
            app_activation_register_rate,
        </if>
        <if test="map.containsKey('appRetainedRate')">
            case when COALESCE(sum(app_retained_person_num), 0) = CAST (0 AS NUMERIC) or
            COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_retained_person_num), 0) / COALESCE(sum(app_activation_count), 0) *100 end as
            app_retained_rate,
        </if>
        <if test="map.containsKey('paymentCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(payment_num), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(payment_num), 0) / 100 end as payment_cost,
        </if>
        <if test="map.containsKey('focusCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(follow_num), 0) = CAST (0 AS NUMERIC)
            THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(follow_num), 0) / 100 end as focus_cost,
        </if>
        <if test="map.containsKey('officialFocusCost1')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_num), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(official_focus_num), 0) / 100 end as official_focus_cost1,
        </if>
        <if test="map.containsKey('saleClueCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(sale_clue_num), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(sale_clue_num), 0) / 100 end as sale_clue_cost,
        </if>
        <if test="map.containsKey('validClueCost1')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(valid_clue_num), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(valid_clue_num), 0) / 100 end as valid_clue_cost1,
        </if>
        <if test="map.containsKey('officialFocusCost2')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_count), 0) = CAST (0
            AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(official_focus_count), 0) / 100 end as official_focus_cost2,
        </if>
        <if test="map.containsKey('validClueCost2')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(valid_clue_count), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(valid_clue_count), 0) / 100 end as valid_clue_cost2,
        </if>
        <if test="map.containsKey('callLinkCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(call_link_count), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(call_link_count), 0) / 100 end as call_link_cost,
        </if>
        <if test="map.containsKey('personWechatLinkCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(person_wechat_link_count), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(person_wechat_link_count), 0) / 100 end as person_wechat_link_cost,
        </if>
        <if test="map.containsKey('appointmentCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(appointment_count), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(appointment_count), 0) / 100 end as appointment_cost,
        </if>
        <if test="map.containsKey('tryListenCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(audition_count), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(audition_count), 0) / 100 end as try_listen_cost,
        </if>
        <if test="map.containsKey('auditionedClassCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(auditioned_class_count), 0) = CAST (0
            AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(auditioned_class_count), 0) / 100 end as auditioned_class_cost,
        </if>
        <if test="map.containsKey('trialCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(trial_count), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(trial_count), 0) / 100 end as trial_cost,
        </if>
        <if test="map.containsKey('paymentDepositCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(payment_deposit_count), 0) = CAST (0
            AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(payment_deposit_count), 0) / 100 end as payment_deposit_cost,
        </if>
        <if test="map.containsKey('payCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(pay_count), 0) = CAST (0 AS NUMERIC)
            THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(pay_count), 0) / 100 end as pay_cost,
        </if>
        <if test="map.containsKey('convertCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_count), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(convert_count), 0) / 100 end as convert_cost,
        </if>
        <if test="map.containsKey('registerCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(register_count), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(register_count), 0) / 100 end as register_cost,
        </if>
        <if test="map.containsKey('activationCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(activation_count), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(activation_count), 0) / 100 end as activation_cost,
        </if>
        <if test="map.containsKey('appDownloadCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_download_finish_count), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(app_download_finish_count), 0) / 100 end as app_download_cost,
        </if>
        <if test="map.containsKey('appInstallCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_install_count), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(app_install_count), 0) / 100 end as app_install_cost,
        </if>
        <if test="map.containsKey('appRegisterCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_register_count), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(app_register_count), 0) / 100 end as app_register_cost,
        </if>
        <if test="map.containsKey('appRetainedCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_retained_person_num), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(app_retained_person_num), 0) / 100 end as app_retained_cost,
        </if>
        <if test="map.containsKey('appPayCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_pay_num), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(app_pay_num), 0) / 100 end as app_pay_cost,
        </if>
        <if test="map.containsKey('identifyQrCodeNum')">
            COALESCE(sum(identify_qr_code_num), 0) as identify_qr_code_num,
        </if>
        <if test="map.containsKey('identifyQrCodeRate')">
            case when COALESCE(sum(identify_qr_code_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0)
            = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(identify_qr_code_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as
            identify_qr_code_rate,
        </if>
        <if test="map.containsKey('identifyQrcodeCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(identify_qr_code_num), 0) = CAST (0
            AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(identify_qr_code_num), 0) / 100 end as identify_qrcode_cost,
        </if>
        <if test="map.containsKey('addWorkWechatNum')">
            COALESCE(sum(add_work_wechat_num), 0) as add_work_wechat_num,
        </if>
        <if test="map.containsKey('addWorkWechatRate')">
            case when COALESCE(sum(add_work_wechat_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) =
            CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(add_work_wechat_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as add_work_wechat_rate,
        </if>
        <if test="map.containsKey('addWorkWechatCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(add_work_wechat_num), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(add_work_wechat_num), 0) / 100 end as add_work_wechat_cost,
        </if>
        <if test="map.containsKey('convertNum')">
            COALESCE(sum(convert_num), 0) as convert_num,
        </if>
        <if test="map.containsKey('targetConvertRate')">
            case when COALESCE(sum(convert_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(convert_num), 0) / COALESCE(sum(click_num), 0) * 100 end as target_convert_rate,
        </if>
        <if test="map.containsKey('targetConvertCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_num), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(convert_num), 0) / 100 end as target_convert_cost,
        </if>
        <if test="map.containsKey('deepConvertNum')">
            COALESCE(sum(deep_convert_num), 0) as deep_convert_num,
        </if>
        <if test="map.containsKey('deepConvertRate')">
            case when COALESCE(sum(deep_convert_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0
            AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(deep_convert_num), 0) / COALESCE(sum(click_num), 0) * 100 end as deep_convert_rate,
        </if>
        <if test="map.containsKey('deepConvertCost')">
            case when COALESCE(sum(deep_convert_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(deep_convert_num), 0) / 100 end as deep_convert_cost,
        </if>
        <if test="map.containsKey('placeOrderNum')">
            COALESCE(sum(place_order_num), 0) as place_order_num,
        </if>
        <if test="map.containsKey('placeOrderRate')">
            case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0
            AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(place_order_num), 0) / COALESCE(sum(click_num), 0) * 100 end as place_order_rate,
        </if>
        <if test="map.containsKey('placeOrderCost')">
            case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(place_order_num), 0) / 100 end as place_order_cost,
        </if>
        <if test="map.containsKey('clueFillNum')">
            COALESCE(sum(clue_fill_num), 0) as clue_fill_num,
        </if>
        <if test="map.containsKey('clueFillRate')">
            case when COALESCE(sum(clue_fill_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(clue_fill_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as clue_fill_rate,
        </if>
        <if test="map.containsKey('clueFillCost')">
            case when COALESCE(sum(clue_fill_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(clue_fill_num), 0) / 100 end as clue_fill_cost,
        </if>
        <if test="map.containsKey('clueConnectNum')">
            COALESCE(sum(clue_connect_num), 0) as clue_connect_num,
        </if>
        <if test="map.containsKey('clueConnectRate')">
            case when COALESCE(sum(clue_connect_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(clue_fill_num), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(clue_connect_num), 0) / COALESCE(sum(clue_fill_num), 0) *100 end as clue_connect_rate,
        </if>
        <if test="map.containsKey('clueConnectCost')">
            case when COALESCE(sum(clue_connect_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(clue_connect_num), 0) / 100 end as clue_connect_cost,
        </if>
        <if test="map.containsKey('clueEffectiveNum')">
            COALESCE(sum(clue_effective_num), 0) as clue_effective_num,
        </if>
        <if test="map.containsKey('clueEffectiveRate')">
            case when COALESCE(sum(clue_connect_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(clue_effective_num), 0) =
            CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(clue_effective_num), 0) / COALESCE(sum(clue_connect_num), 0) *100 end as clue_effective_rate,
        </if>
        <if test="map.containsKey('clueEffectiveCost')">
            case when COALESCE(sum(clue_effective_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(clue_effective_num), 0) / 100 end as clue_effective_cost,
        </if>
        <if test="map.containsKey('signClassNum')">
            COALESCE(sum(sign_class_num), 0) as sign_class_num,
        </if>
        <if test="map.containsKey('signClassRate')">
            case when COALESCE(sum(sign_class_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(clue_effective_num), 0) =
            CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(sign_class_num), 0) / COALESCE(sum(clue_effective_num), 0) *100 end as sign_class_rate,
        </if>
        <if test="map.containsKey('signClassCost')">
            case when COALESCE(sum(sign_class_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS
            NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(sign_class_num), 0) / 100 end as sign_class_cost,
        </if>
        <if test="map.containsKey('totalConvertRate')">
            case when COALESCE(sum(sign_class_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(sign_class_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as total_convert_rate,
        </if>
        <!-- 1.155.0 电商商品单笔购买成本 -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(online_shop_buy_goods_product_count), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(online_shop_buy_goods_product_count), 0) /100 end as online_shop_buy_goods_success_cost,
        </if>
        <!-- 1.155.0 电商商品成交ROI -->
        <if test="map.containsKey('onlineShopBuyGoodsSuccessRoi')">
            case when COALESCE(sum(online_shop_buy_goods_order_price), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(online_shop_buy_goods_order_price), 0) / COALESCE(sum(cost), 0) *100 end as online_shop_buy_goods_success_roi,
        </if>


        <if test="map.containsKey('officialIdentifyQrCodeRate')">
            case when COALESCE(sum(official_identify_qr_code_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(official_identify_qr_code_num), 0) / COALESCE(sum(landing_page_pv), 0) *100
            end as official_identify_qr_code_rate,
         </if>

        <if test="map.containsKey('officialIdentifyQrCodeCost')">
            case when COALESCE(sum(official_identify_qr_code_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(official_identify_qr_code_num), 0) *100
            end as official_identify_qr_code_cost,
        </if>

        <!-- 1.197.0 新增参数 -->
        <if  test="map.containsKey('identifyGroupQrCodeNum')">
            COALESCE(sum(identify_group_qr_code_num), 0) as identify_group_qr_code_num,
        </if>
        <if  test="map.containsKey('addWorkWechatGroupNum')">
            COALESCE(sum(add_work_wechat_group_num), 0) as add_work_wechat_group_num,
        </if>

        <if test="map.containsKey('identifyGroupQrCodeRate')">
            case when COALESCE(sum(identify_group_qr_code_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(identify_group_qr_code_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as identify_group_qr_code_rate,
        </if>
        <if test="map.containsKey('identifyGroupQrCodeCost')">
            case when COALESCE(sum(identify_group_qr_code_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) /100 / COALESCE(sum(identify_group_qr_code_num), 0)  end as identify_group_qr_code_cost,
        </if>
        <if test="map.containsKey('addWorkWechatGroupRate')">
            case when COALESCE(sum(add_work_wechat_group_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(add_work_wechat_group_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as add_work_wechat_group_rate,
        </if>
        <if test="map.containsKey('addWorkWechatGroupCost')">
            case when COALESCE(sum(add_work_wechat_group_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) /100  / COALESCE(sum(add_work_wechat_group_num), 0) end as add_work_wechat_group_cost,
        </if>


        <if test="map.containsKey('addWorkWechatGroupIncomingRate')">
            case when COALESCE(sum(add_work_wechat_group_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(add_work_wechat_group_num), 0) / COALESCE(sum(convert_num), 0) *100
            end as add_work_wechat_group_incoming_rate,
        </if>


        <!--  公众号历史文章页PV      -->
        <if test="map.containsKey('wechatOfficialArticlePageViewNum')">
            COALESCE(sum(wechat_official_article_page_view_num), 0) as wechat_official_article_page_view_num,
        </if>
    </sql>

    <sql id="all_column">
        COALESCE(sum(view_num), 0) as view_num,
        case when COALESCE(sum(convert_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(convert_num), 0) / COALESCE(sum(click_num), 0) * 100
        end
        as target_convert_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(convert_num), 0) / 100
        end
        as target_convert_cost,
        case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(place_order_num), 0) / COALESCE(sum(click_num), 0) * 100
        end
        as place_order_rate,
        case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(place_order_num), 0) / 100
        end
        as place_order_cost,
        COALESCE(sum(deep_convert_num), 0) as deep_convert_num,
        case when COALESCE(sum(deep_convert_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(deep_convert_num), 0) / COALESCE(sum(click_num), 0) * 100
        end
        as deep_convert_rate,
        case when COALESCE(sum(deep_convert_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(deep_convert_num), 0) / 100
        end
        as deep_convert_cost,
        COALESCE(sum(length_of_stay), 0) as length_of_stay,
        COALESCE(sum(convert_num), 0) as convert_num,
        COALESCE(sum(click_num), 0) as click_num,
        COALESCE(sum(cost), 0) / 100 as cost,
        case when COALESCE(sum(view_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(view_num), 0) * 10
        end
        as thousand_impress_avg_price,
        case when COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(click_num), 0) / 100
        end
        as avg_price,
        COALESCE(sum(landing_page_pv), 0) as landing_page_pv,
        COALESCE(sum(landing_page_uv), 0) as landing_page_uv,
        COALESCE(sum(fill_count_num), 0) as fill_count_num,
        COALESCE(sum(order_num), 0) as order_num,
        COALESCE(sum(order_finish_num), 0) as order_finish_num,
        case when COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(length_of_stay), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(length_of_stay), 0) / COALESCE(sum(landing_page_pv), 0)
        end
        as landing_avg_stay,
        COALESCE(sum(form_appointment_num), 0) as form_appointment_num,
        COALESCE(sum(form_appointment_person_count), 0) as form_appointment_person_count,
        COALESCE(sum(order_amount), 0) / 100 as order_amount,
        case when COALESCE(sum(order_amount), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(order_amount), 0) / COALESCE(sum(place_order_num), 0) / 100
        end
        as order_unit_price,
        case when COALESCE(sum(order_amount), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        (COALESCE(sum(order_amount), 0)/100) / (COALESCE(sum(cost), 0)/100)
        end
        as order_roi,
        COALESCE(sum(payment_num), 0) as payment_num,
        COALESCE(sum(payment_amount), 0) / 100 as payment_amount,
        COALESCE(sum(first_payment_person_num), 0) as first_payment_person_num,
        COALESCE(sum(official_focus_num), 0) as official_focus_num,
        COALESCE(sum(sale_clue_num), 0) as sale_clue_num,
        COALESCE(sum(sale_clue_person_num), 0) as sale_clue_person_num,
        COALESCE(sum(valid_clue_num), 0) as valid_clue_num,
        COALESCE(sum(valid_clue_person_num), 0) as valid_clue_person_num,
        COALESCE(sum(official_focus_count), 0) as official_focus_count,
        COALESCE(sum(valid_clue_count), 0) as valid_clue_count,
        COALESCE(sum(call_link_count), 0) as call_link_count,
        COALESCE(sum(person_wechat_link_count), 0) as person_wechat_link_count,
        COALESCE(sum(appointment_count), 0) as appointment_count,
        COALESCE(sum(audition_count), 0) as audition_count,
        COALESCE(sum(auditioned_class_count), 0) as auditioned_class_count,
        COALESCE(sum(trial_count), 0) as trial_count,
        COALESCE(sum(payment_deposit_count), 0) as payment_deposit_count,
        COALESCE(sum(pay_count), 0) as pay_count,
        COALESCE(sum(convert_count), 0) as convert_count,
        COALESCE(sum(register_count), 0) as register_count,
        COALESCE(sum(activation_count), 0) as activation_count,
        COALESCE(sum(app_download_finish_count), 0) as app_download_finish_count,
        COALESCE(sum(app_install_count), 0) as app_install_count,
        COALESCE(sum(app_activation_count), 0) as app_activation_num,
        COALESCE(sum(app_register_count), 0) as app_register_num,
        COALESCE(sum(app_retained_person_num), 0) as app_retained_person_num,
        COALESCE(sum(app_pay_num), 0) as app_pay_num,
        COALESCE(sum(app_pay_amount), 0) as app_pay_amount,
        COALESCE(sum(place_order_num), 0) as place_order_num,
        COALESCE(sum(button_count), 0) as buttonCount,
        COALESCE(sum(follow_num), 0) as follow_num,
        COALESCE(sum(top_pay), 0) as top_pay,
        COALESCE(sum(top_p_clicks), 0) as top_p_clicks,
        COALESCE(sum(top_page_views), 0) as top_page_views,
        COALESCE(avg(top_pv_win_a)*100, 0) as top_pv_win_a,
        COALESCE(avg(top_pv_win_p)*100, 0) as top_pv_win_p,
        <!--  公众号历史文章页PV      -->
        COALESCE(sum(wechat_official_article_page_view_num), 0) as wechat_official_article_page_view_num,
        <!-- 1.155.0 电商商品购买数 -->
        COALESCE(sum(online_shop_buy_goods_product_count), 0) as online_shop_buy_goods_success_num ,
        COALESCE ( SUM ( online_shop_buy_goods_order_price ), 0 ) AS online_shop_buy_goods_success_amount,
        case when COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(view_num), 0) = CAST (0 AS NUMERIC)
        THEN 0 ELSE
        COALESCE(sum(click_num), 0) / COALESCE(sum(view_num), 0) *100
        end
        as click_rate,
        case when COALESCE(sum(fill_count_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0
        AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(fill_count_num), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end
        as fill_count_rate,
        case when COALESCE(sum(order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS
        NUMERIC) THEN 0 ELSE
        COALESCE(sum(order_num), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end
        as order_count_rate,
        case when COALESCE(sum(fill_count_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(fill_count_num), 0) / 100
        end
        as fill_count_cost,
        case when COALESCE(sum(order_finish_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(order_finish_num), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end
        as order_finish_rate,
        case when COALESCE(sum(order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(order_num), 0) / 100
        end
        as order_count_cost,
        case when COALESCE(sum(order_finish_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(order_finish_num), 0) / 100
        end
        as order_finish_cost,
        case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(place_order_num), 0) / COALESCE(sum(form_appointment_num), 0) *100
        end
        as form_order_convert_rate,
        case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(form_appointment_num), 0) / COALESCE(sum(click_num), 0) *100
        end
        as form_appointment_rate,
        case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(button_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(form_appointment_num), 0) / COALESCE(sum(button_count), 0) *100
        end
        as button_form_convert,
        case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(form_appointment_num), 0) / 100
        end
        as form_appointment_cost,
        case when COALESCE(sum(sale_clue_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(sale_clue_num), 0) / COALESCE(sum(click_num), 0) *100
        end
        as sale_clue_convert_rate,
        case when COALESCE(sum(valid_clue_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(valid_clue_num), 0) / COALESCE(sum(click_num), 0) *100
        end
        as valid_clue_convert_rate,
        case when COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_download_finish_count), 0) / COALESCE(sum(click_num), 0) *100
        end
        as app_download_rate,
        case when COALESCE(sum(app_install_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_install_count), 0) / COALESCE(sum(click_num), 0) *100
        end
        as app_install_rate,
        case when COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_activation_count), 0) / COALESCE(sum(click_num), 0) *100
        end
        as app_click_activation_rate,
        case when COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_activation_count), 0) / COALESCE(sum(app_download_finish_count), 0) *100
        end
        as app_download_activation_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(app_activation_count), 0) / 100
        end
        as app_download_activation_cost,
        case when COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_register_count), 0) / COALESCE(sum(click_num), 0) *100
        end
        as app_register_rate,
        case when COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_register_count), 0) / COALESCE(sum(app_activation_count), 0) *100
        end
        as app_activation_register_rate,
        case when COALESCE(sum(app_retained_person_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_retained_person_num), 0) / COALESCE(sum(app_activation_count), 0) *100
        end
        as app_retained_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(payment_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(payment_num), 0) / 100
        end
        as payment_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(follow_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(follow_num), 0) / 100
        end
        as focus_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(official_focus_num), 0) / 100
        end
        as official_focus_cost1,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(sale_clue_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(sale_clue_num), 0) / 100
        end
        as sale_clue_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(valid_clue_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(valid_clue_num), 0) / 100
        end
        as valid_clue_cost1,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(official_focus_count), 0) / 100
        end
        as official_focus_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(valid_clue_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(valid_clue_count), 0) / 100
        end
        as valid_clue_cost2,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(call_link_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(call_link_count), 0) / 100
        end
        as call_link_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(person_wechat_link_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(person_wechat_link_count), 0) / 100
        end
        as person_wechat_link_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(appointment_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(appointment_count), 0) / 100
        end
        as appointment_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(audition_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(audition_count), 0) / 100
        end
        as try_listen_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(auditioned_class_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(auditioned_class_count), 0) / 100
        end
        as auditioned_class_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(trial_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(trial_count), 0) / 100
        end
        as trial_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(payment_deposit_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(payment_deposit_count), 0) / 100
        end
        as payment_deposit_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(pay_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(pay_count), 0) / 100
        end
        as pay_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(convert_count), 0) / 100
        end
        as convert_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(register_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(register_count), 0) / 100
        end
        as register_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(activation_count), 0) / 100
        end
        as activation_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(app_download_finish_count), 0) / 100
        end
        as app_download_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_install_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(app_install_count), 0) / 100
        end
        as app_install_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(app_register_count), 0) / 100
        end
        as app_register_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_retained_person_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(app_retained_person_num), 0) / 100
        end
        as app_retained_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_pay_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(app_pay_num), 0) / 100
        end
        as app_pay_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(official_focus_count), 0) / 100
        end
        as official_focus_cost2,

        COALESCE(sum(identify_qr_code_num), 0) as identify_qr_code_num,
        case when COALESCE(sum(identify_qr_code_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(identify_qr_code_num), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end
        as identify_qr_code_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(identify_qr_code_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(identify_qr_code_num), 0) / 100
        end
        as identify_qrcode_cost,
        COALESCE(sum(add_work_wechat_num), 0) as add_work_wechat_num,
        case when COALESCE(sum(add_work_wechat_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(add_work_wechat_num), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end
        as add_work_wechat_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(add_work_wechat_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(add_work_wechat_num), 0) / 100
        end
        as add_work_wechat_cost,
        COALESCE(sum(clue_fill_num), 0) as clue_fill_num,
        case when COALESCE(sum(clue_fill_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(clue_fill_num), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end
        as clue_fill_rate,
        case when COALESCE(sum(clue_fill_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(clue_fill_num), 0) / 100
        end
        as clue_fill_cost,
        COALESCE(sum(clue_connect_num), 0) as clue_connect_num,
        case when COALESCE(sum(clue_connect_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(clue_fill_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(clue_connect_num), 0) / COALESCE(sum(clue_fill_num), 0) *100
        end
        as clue_connect_rate,
        case when COALESCE(sum(clue_connect_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(clue_connect_num), 0) / 100
        end
        as clue_connect_cost,
        COALESCE(sum(clue_effective_num), 0) as clue_effective_num,
        case when COALESCE(sum(clue_connect_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(clue_effective_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(clue_effective_num), 0) / COALESCE(sum(clue_connect_num), 0) *100
        end
        as clue_effective_rate,
        case when COALESCE(sum(clue_effective_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(clue_effective_num), 0) / 100
        end
        as clue_effective_cost,
        COALESCE(sum(sign_class_num), 0) as sign_class_num,
        case when COALESCE(sum(sign_class_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(clue_effective_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(sign_class_num), 0) / COALESCE(sum(clue_effective_num), 0) *100
        end
        as sign_class_rate,
        case when COALESCE(sum(sign_class_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(sign_class_num), 0) / 100
        end
        as sign_class_cost,
        case when COALESCE(sum(sign_class_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(sign_class_num), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end
        as total_convert_rate,
        <!-- 1.155.0 电商商品单笔购买成本 -->
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(online_shop_buy_goods_order_num), 0) = CAST
            (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(online_shop_buy_goods_order_num), 0) /100 end as online_shop_buy_goods_success_cost,
        <!-- 1.155.0 电商购买roi -->
        case when COALESCE(sum(online_shop_buy_goods_order_price), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(online_shop_buy_goods_order_price), 0) / COALESCE(sum(cost), 0) *100
        end
        as online_shop_buy_goods_success_roi,
        <!-- 1.179.0 投放面板优化 新增字段-->
        COALESCE (SUM (order_transaction_price), 0 ) AS order_transaction_amount,
        case when COALESCE(sum(order_transaction_price), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(order_transaction_price), 0) / COALESCE(sum(cost), 0) *100
        end
        as order_transaction_roi,
        case when COALESCE(sum(add_work_wechat_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(add_work_wechat_num), 0) / COALESCE(sum(convert_num), 0) *100
        end
        as enterprise_increase_incoming_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_identify_qr_code_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(official_identify_qr_code_num), 0) /100
        end
        as enterprise_increase_incoming_cost,
        COALESCE (SUM (official_focus_count), 0 ) AS follow_official_account_num,
        case when COALESCE(sum(official_focus_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(official_focus_count), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end
        as follow_official_account_rate,
        case when COALESCE(sum(official_focus_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(official_focus_count), 0) / COALESCE(sum(convert_num), 0) *100
        end
        as follow_official_account_incoming_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(official_focus_count), 0) / 100
        end
        as follow_official_account_cost,
        case when COALESCE(sum(online_shop_buy_goods_product_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(online_shop_buy_goods_product_count), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end
        as online_shop_buy_goods_success_rate,
        <!-- 1.197.0 新增字段-->
        COALESCE (SUM (identify_group_qr_code_num), 0 ) AS identify_group_qr_code_num,
        COALESCE (SUM (add_work_wechat_group_num), 0 ) AS add_work_wechat_group_num,
        COALESCE (SUM (official_identify_qr_code_num), 0 ) AS official_identify_qr_code_num,

        case when COALESCE(sum(identify_group_qr_code_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(identify_group_qr_code_num), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end as identify_group_qr_code_rate,

        case when COALESCE(sum(identify_group_qr_code_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0)/100 / COALESCE(sum(identify_group_qr_code_num), 0)
        end as identify_group_qr_code_cost,

        case when COALESCE(sum(add_work_wechat_group_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(add_work_wechat_group_num), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end as add_work_wechat_group_rate,
        case when COALESCE(sum(add_work_wechat_group_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0)/100 / COALESCE(sum(add_work_wechat_group_num), 0)
        end as add_work_wechat_group_cost,

        case when COALESCE(sum(add_work_wechat_group_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(add_work_wechat_group_num), 0) / COALESCE(sum(convert_num), 0) *100
        end as add_work_wechat_group_incoming_rate,

        case when COALESCE(sum(official_identify_qr_code_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(official_identify_qr_code_num), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end as official_identify_qr_code_rate,

        case when COALESCE(sum(official_identify_qr_code_num), 0) = CAST (0 AS NUMERIC)  THEN 0 ELSE
        COALESCE(sum(cost), 0)/100 / COALESCE(sum(official_identify_qr_code_num), 0)
        end as official_identify_qr_code_cost,


        <!-- 1.197.0新增参数结束 -->

        COALESCE (SUM (play_num), 0 ) AS play_num,
        COALESCE (SUM (valid_play_num), 0 ) AS valid_play_num,
        COALESCE (SUM (video_play25_num), 0 ) AS video_play25_num,
        COALESCE (SUM (video_play50_num), 0 ) AS video_play50_num,
        COALESCE (SUM (video_play75_num), 0 ) AS video_play75_num,
        COALESCE (SUM (video_play100_num), 0 ) AS video_play100_num,
        COALESCE (SUM (praise_num), 0 ) AS praise_num,
        COALESCE (SUM (comment_num), 0 ) AS comment_num,
        COALESCE (SUM (share_num), 0 ) AS share_num,
        case when COALESCE(sum(positive_sample_pv), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(positive_sample_pv), 0) / COALESCE(sum(landing_page_pv), 0) *100
        end
        as traffic_ratio,

        COALESCE (SUM (work_wechat_open_consultation_num), 0 ) AS work_wechat_open_consultation_num,
        case when COALESCE(sum(work_wechat_open_consultation_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(add_work_wechat_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(work_wechat_open_consultation_num), 0) / COALESCE(sum(add_work_wechat_num), 0) *100
        end
        as work_wechat_open_consultation_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(work_wechat_open_consultation_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(work_wechat_open_consultation_num), 0) /100
        end
        as work_wechat_open_consultation_cost,

        COALESCE (SUM (work_wechat_quotation_consultation_num), 0 ) AS work_wechat_quotation_consultation_num,
        case when COALESCE(sum(work_wechat_quotation_consultation_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(add_work_wechat_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(work_wechat_quotation_consultation_num), 0) / COALESCE(sum(add_work_wechat_num), 0) *100
        end
        as work_wechat_quotation_consultation_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(work_wechat_quotation_consultation_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(work_wechat_quotation_consultation_num), 0) /100
        end
        as work_wechat_quotation_consultation_cost,

        COALESCE (SUM (work_wechat_order_filled_num), 0 ) AS work_wechat_order_filled_num,
        case when COALESCE(sum(work_wechat_order_filled_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(add_work_wechat_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(work_wechat_order_filled_num), 0) / COALESCE(sum(add_work_wechat_num), 0) *100
        end
        as work_wechat_order_filled_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(work_wechat_order_filled_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(work_wechat_order_filled_num), 0) /100
        end
        as work_wechat_order_filled_cost
    </sql>

    <sql id="condition_sql">
        <if test="vo.maxViewNum != null and vo.minViewNum == null">
            and (a.view_num &lt;= #{vo.maxViewNum} or a.view_num is null)
        </if>
        <if test="vo.maxViewNum == null and vo.minViewNum != null">
            and a.view_num &gt;= #{vo.minViewNum}
        </if>
        <if test="vo.maxViewNum != null and vo.minViewNum != null">
            and a.view_num between #{vo.minViewNum} and #{vo.maxViewNum}
        </if>
        <if test="vo.maxCost != null and vo.minCost == null">
            and a.cost &lt;= #{vo.maxCost} or a.cost is null
        </if>
        <if test="vo.maxCost == null and vo.minCost != null">
            and a.cost &gt;= #{vo.minCost}
        </if>
        <if test="vo.maxCost != null and vo.minCost != null">
            and a.cost between #{vo.minCost} and #{vo.maxCost}
        </if>
        <if test="vo.maxConvertNum != null and vo.minConvertNum == null">
            and (a.convert_num &lt;= #{vo.maxConvertNum} or a.convert_num is null)
        </if>
        <if test="vo.maxConvertNum == null and vo.minConvertNum != null">
            and a.convert_num &gt;= #{vo.minConvertNum}
        </if>
        <if test="vo.maxConvertNum != null and vo.minConvertNum != null">
            and a.convert_num between #{vo.minConvertNum} and #{vo.maxConvertNum}
        </if>
        <if test="vo.maxTargetConvertCost != null and vo.minTargetConvertCost == null">
            and (a.target_convert_cost &lt;= #{vo.maxTargetConvertCost} or a.target_convert_cost is null)
        </if>
        <if test="vo.maxTargetConvertCost == null and vo.minTargetConvertCost != null">
            and a.target_convert_cost &gt;= #{vo.minTargetConvertCost}
        </if>
        <if test="vo.maxTargetConvertCost != null and vo.minTargetConvertCost != null">
            and a.target_convert_cost between #{vo.minTargetConvertCost} and #{vo.maxTargetConvertCost}
        </if>
        <if test="vo.maxDeepConvertNum != null and vo.minDeepConvertNum == null">
            and (a.deep_convert_num &lt;= #{vo.maxDeepConvertNum} or a.deep_convert_num is null)
        </if>
        <if test="vo.maxDeepConvertNum == null and vo.minDeepConvertNum != null">
            and a.deep_convert_num &gt;= #{vo.minDeepConvertNum}
        </if>
        <if test="vo.maxDeepConvertNum != null and vo.minDeepConvertNum != null">
            and a.deep_convert_num between #{vo.minDeepConvertNum} and #{vo.maxDeepConvertNum}
        </if>
        <if test="vo.maxDeepConvertCost != null and vo.minDeepConvertCost == null">
            and (a.deep_convert_cost &lt;= #{vo.maxDeepConvertCost} or a.deep_convert_cost is null)
        </if>
        <if test="vo.maxDeepConvertCost == null and vo.minDeepConvertCost != null">
            and a.deep_convert_cost &gt;= #{vo.minDeepConvertCost}
        </if>
        <if test="vo.maxDeepConvertCost != null and vo.minDeepConvertCost != null">
            and a.deep_convert_cost between #{vo.minDeepConvertCost} and #{vo.maxDeepConvertCost}
        </if>
    </sql>

    <sql id="accountListCondition">
        <if test="vo.accountId != null and vo.accountId != ''">
            and account_id = #{vo.accountId}
        </if>
        <if test="vo.accountIds != null and vo.accountIds.size() > 0">
            and account_id in
            <foreach collection="vo.accountIds" item="account" separator="," open="(" close=")">
                #{account}
            </foreach>
        </if>
    </sql>


    <select id="listAccountByCondition" resultType="ai.yiye.agent.domain.vo.LaunchManageVo">
        with accountData as (
        select
        <include refid="all_column"/>
        ,account_id, platform_id
        from marketing_data_solidification_ad_show_report
        where 1 = 1
        <if test="vo.advertiserGroupId != null">
            and account_id in
            (
            select maa.account_id from marketing_advertiser_account_group_rel maagr left join
            marketing_advertiser_account maa on
            maagr.advertiser_account_id = maa.id where maagr.advertiser_account_group_id = #{vo.advertiserGroupId} and
            maa.account_id is not null
            )
        </if>
        <if test="vo.startTime != null and vo.endTime != null">
            and data_time between to_timestamp(#{vo.startTime}, 'yyyy-MM-dd hh24:mi:ss') and to_timestamp(#{vo.endTime},
            'yyyy-MM-dd hh24:mi:ss')
        </if>
        <if test="vo.platformId != null and vo.platformId.size != 0">
            and platform_id in
            <foreach collection="vo.platformId" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        group by account_id, platform_id
        )
        select
        <include refid="result_column"/>
        mda.platform_id as platform_id, mda.account_id as account_id,mda.valid,
        (case
        when mda.budget_mode is null
        THEN ''
        when mda.budget_mode  = 'BUDGET_MODE_INFINITE'
        THEN '不限'
        when mda.budget_mode  = 'BUDGET_MODE_DAY'
        THEN concat('日预算',':',round((COALESCE(mda.budget, 0) :: NUMERIC /100),2))
        ELSE '' end) as account_budget,
        mda.pull_status
        from
        accountData a right join
        (select platform_id,account_id,account_name,balance,valid,
        budget_mode,
        budget,
        pull_status
         from marketing_advertiser_account
        where agency=false
        <if test="vo.platformId != null and vo.platformId.size != 0">
            and platform_id in
            <foreach collection="vo.platformId" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="vo.advertiserGroupId != null">
            and account_id in
            (
            select maa.account_id from marketing_advertiser_account_group_rel maagr left join
            marketing_advertiser_account maa on
            maagr.advertiser_account_id = maa.id where maagr.advertiser_account_group_id = #{vo.advertiserGroupId} and
            maa.account_id is not null
            )
        </if>
        <if test="vo.nameOrId != null and vo.nameOrId != ''">
            and (account_id like CONCAT('%', #{vo.nameOrId}, '%') or account_name like CONCAT('%', #{vo.nameOrId},
            '%'))
        </if>
        ) mda on a.account_id = mda.account_id
        <!-- 上钻筛选-->
        <if test="vo.campaignId!=null and vo.campaignId!=''">
            join (select account_id,platform_id from marketing_data_campaign where campaign_id = #{vo.campaignId}) mdc
            on
            mdc.account_id = mda.account_id and mdc.platform_id = mda.platform_id
        </if>
        <if test="vo.campaignName!=null and vo.campaignName!=''">
            join (select account_id,platform_id from marketing_data_campaign where campaign_name = #{vo.campaignName})
            mdc
            on
            mdc.account_id = mda.account_id and mdc.platform_id = mda.platform_id
        </if>
        <if test="vo.adgroupId!=null and vo.adgroupId!=''">
            join (select account_id,platform_id from marketing_data_adgroup where adgroup_id = #{vo.adgroupId}) mdag on
            mdag.account_id = mda.account_id and mdag.platform_id = mda.platform_id
        </if>
        <if test="vo.adgroupName!=null and vo.adgroupName!=''">
            join (select account_id,platform_id from marketing_data_adgroup where name = #{vo.adgroupName}) mdag on
            mdag.account_id = mda.account_id and mdag.platform_id = mda.platform_id
        </if>
        <if test="vo.creativeId!=null and vo.creativeId!=''">
            join (select account_id,platform_id from marketing_data_creative where creative_id = #{vo.creativeId}) mdcv
            on
            mdcv.account_id = mda.account_id and mdcv.platform_id = mda.platform_id
        </if>
        where 1 = 1

        <!-- 多个账户id筛选 -->
        <if test="vo.accountId != null and vo.accountId != ''">
            and mda.account_id = #{vo.accountId}
        </if>
        <if test="vo.accountName != null and vo.accountName != ''">
            and mda.account_name = #{vo.accountName}
        </if>
        <if test="vo.accountIds != null and vo.accountIds.size() > 0">
            and mda.account_id in
            <foreach collection="vo.accountIds" item="account" separator="," open="(" close=")">
                #{account}
            </foreach>
        </if>
        <if test="vo.sort != null and vo.sort != '' and vo.order != null and vo.order != ''">
            order by ${vo.sort} ${vo.order}
            <if test="vo.order == 'desc'">
                nulls last
            </if>
            <if test="vo.order == 'asc'">
                nulls first
            </if>
        </if>
    </select>

    <select id="listPlanByCondition" resultType="ai.yiye.agent.domain.vo.LaunchManageVo">
        with planData as (
        select
        <include refid="all_column"/>
        ,campaign_id, platform_id
        from marketing_data_solidification_ad_show_report
        where 1 = 1
        <if test="vo.advertiserGroupId != null">
            and account_id in
            (
            select maa.account_id from marketing_advertiser_account_group_rel maagr left join
            marketing_advertiser_account maa on
            maagr.advertiser_account_id = maa.id where maagr.advertiser_account_group_id = #{vo.advertiserGroupId} and
            maa.account_id is not null
            )
        </if>
        <if test="vo.startTime != null and vo.endTime != null">
            and data_time between to_timestamp(#{vo.startTime}, 'yyyy-MM-dd hh24:mi:ss') and to_timestamp(#{vo.endTime},
            'yyyy-MM-dd hh24:mi:ss')
        </if>
        <if test="vo.platformId != null and vo.platformId.size != 0">
            and platform_id in
            <foreach collection="vo.platformId" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        group by campaign_id, platform_id
        )
        select
        <include refid="result_column"/>
        mda.platform_id as platform_id, mdc.campaign_id as campaign_id, mda.account_id as account_id,mda.valid as valid,
        round((COALESCE(mdc.daily_budget, 0) :: NUMERIC /100) ,2) as ad_group_budget,
        mdc.budget_mode,
        (case
        when mda.budget_mode is null
        THEN ''
        when mda.budget_mode  = 'BUDGET_MODE_INFINITE'
        THEN '不限'
        when mda.budget_mode  = 'BUDGET_MODE_DAY'
        THEN concat('日预算',':',round((COALESCE(mda.budget, 0) :: NUMERIC /100),2))
        ELSE '' end) as account_budget,
        mda.pull_status
        from planData a
        right join (
        select campaign_id,account_id,platform_id,campaign_name,status,promotion_goal,
        daily_budget,
        budget_mode
               from
        marketing_data_campaign
        where 1=1
        <if test="vo.campaignStatusList != null">
            and status in
            <foreach collection="vo.campaignStatusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="vo.campaignId != null">
            and campaign_id = #{vo.campaignId}
        </if>
        <if test="vo.campaignName != null">
            and campaign_name = #{vo.campaignName}
        </if>
        <!-- 多个账户id筛选 -->
        <include refid="accountListCondition"/>
        <if test="vo.promotionGoal != null and vo.promotionGoal.size != 0">
            and promotion_goal in
            <foreach collection="vo.promotionGoal" item="goal" open="(" close=")" separator=",">
                #{goal}
            </foreach>
        </if>
        <if test="vo.nameOrId != null and vo.nameOrId != ''">
            and (cast(campaign_id as varchar) like CONCAT('%', #{vo.nameOrId}, '%') or campaign_name like
            CONCAT('%', #{vo.nameOrId}, '%'))
        </if>

        ) mdc
        on a.campaign_id = mdc.campaign_id
        join (
        select account_id,account_name,platform_id,valid,
               budget,budget_mode,
               pull_status,
               balance
        from marketing_advertiser_account
        where agency=false
        <if test="vo.advertiserGroupId != null">
            and account_id in
            (
            select maa.account_id from marketing_advertiser_account_group_rel maagr left join
            marketing_advertiser_account maa on
            maagr.advertiser_account_id = maa.id where maagr.advertiser_account_group_id = #{vo.advertiserGroupId} and
            maa.account_id is not null
            )
        </if>
        <if test="vo.platformId != null and vo.platformId.size != 0">
            and platform_id in
            <foreach collection="vo.platformId" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        ) mda
        on mdc.account_id = mda.account_id
        left join marketing_advertiser_screen mas
        on mdc.promotion_goal = mas.field_value and mas.field =
        'PROMOTIONAL_OBJECTIVES' and mdc.platform_id = mas.platform_id
        <!-- 上钻筛选-->
        <if test="vo.adgroupId!=null and vo.adgroupId!=''">
            join (select campaign_id,platform_id from marketing_data_adgroup where adgroup_id = #{vo.adgroupId}) mdag on
            mdag.campaign_id = mdc.campaign_id and mdag.platform_id = mdc.platform_id
        </if>
        <if test="vo.adgroupName!=null and vo.adgroupName!=''">
            join (select campaign_id,platform_id from marketing_data_adgroup where name = #{vo.adgroupName}) mdag on
            mdag.campaign_id = mdc.campaign_id and mdag.platform_id = mdc.platform_id
        </if>
        <if test="vo.creativeId!=null and vo.creativeId!=''">
            join (select campaign_id,platform_id from marketing_data_creative where creative_id = #{vo.creativeId}) mdcv
            on
            mdcv.campaign_id = mdc.campaign_id and mdcv.platform_id = mdc.platform_id
        </if>
        <if test="vo.creativeName!=null and vo.creativeName!=''">
            join (select campaign_id,platform_id from marketing_data_creative where creative_name = #{vo.creativeName})
            mdcv
            on
            mdcv.campaign_id = mdc.campaign_id and mdcv.platform_id = mdc.platform_id
        </if>
        where 1 = 1
        <if test="vo.accountName != null and vo.accountName != ''">
            and mda.account_name = #{vo.accountName}
        </if>
        <if test="vo.sort != null and vo.sort != '' and vo.order != null and vo.order != ''">
            order by ${vo.sort} ${vo.order}
            <if test="vo.order == 'desc'">
                nulls last
            </if>
            <if test="vo.order == 'asc'">
                nulls first
            </if>
        </if>
    </select>

    <select id="listAdvertiseByCondition" resultType="ai.yiye.agent.domain.vo.LaunchManageVo">
        with advertiseData as (
        select
        <include refid="all_column"/>
        , adgroup_id, platform_id
        from marketing_data_solidification_ad_show_report
        where 1 = 1
        <if test="vo.advertiserGroupId != null">
            and account_id in
            (
            select maa.account_id from marketing_advertiser_account_group_rel maagr left join
            marketing_advertiser_account maa on
            maagr.advertiser_account_id = maa.id where maagr.advertiser_account_group_id = #{vo.advertiserGroupId} and
            maa.account_id is not null
            )
        </if>
        <if test="vo.startTime != null and vo.endTime != null">
            and data_time between to_timestamp(#{vo.startTime}, 'yyyy-MM-dd hh24:mi:ss') and to_timestamp(#{vo.endTime},
            'yyyy-MM-dd hh24:mi:ss')
        </if>
        <if test="vo.platformId != null and vo.platformId.size != 0">
            and platform_id in
            <foreach collection="vo.platformId" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        group by adgroup_id, platform_id
        )

        select
        <include refid="result_column"/>
        mda.platform_id as platform_id, mdap.adgroup_id as adgroup_id, mda.account_id as account_id,
        mdc.campaign_id as campaign_id, mdap.id as yiye_adgroup_id,mda.valid as valid,
        (case mdap.billing_event
        when null then ''
        when 'oCPM' then concat(mdap.billing_event,':',round((COALESCE(mdap.cpa_bid, 0) :: NUMERIC /100),2))
        when 'oCPC' then concat(mdap.billing_event,':',round((COALESCE(mdap.cpa_bid, 0) :: NUMERIC /100),2))
        ELSE concat(mdap.billing_event,':',round((COALESCE(mdap.bid, 0) :: NUMERIC /100),2)) end
        ) ad_bid,
        (
            case mdap.deep_bid_type
            when null then null
            when 'DEEP_BID_MIN' then concat(mdap.deep_bid_type,':',round((COALESCE(mdap.deep_cpabid, 0) :: NUMERIC /100),2))
            else null
            end
        ) as deep_conversion_bidding,
        round((COALESCE(mdap.budget, 0) :: NUMERIC/100),2) as advertising_budget,
        round((COALESCE(mdc.daily_budget, 0) :: NUMERIC/100),2) as ad_group_budget,
        (case
        when mda.budget_mode is null
        THEN ''
        when mda.budget_mode  = 'BUDGET_MODE_INFINITE'
        THEN '不限'
        when mda.budget_mode  = 'BUDGET_MODE_DAY'
        THEN concat('日预算',':',round((COALESCE(mda.budget, 0) :: NUMERIC/100),2))
        ELSE '' end) as account_budget,
        mda.pull_status
        from advertiseData a
        right join (
        select account_id,platform_id,adgroup_id,status,optimization_goal,id,campaign_id,name,
                bid,
                cpa_bid,
                billing_event,
                deep_bid_type,
                deep_cpabid,
                budget
        from marketing_data_adgroup
        where 1=1
        <if test="vo.campaignId != null">
            and campaign_id = #{vo.campaignId}
        </if>
        <if test="vo.adgroupId != null">
            and adgroup_id = #{vo.adgroupId}
        </if>
        <if test="vo.adgroupName != null">
            and name = #{vo.adgroupName}
        </if>
        <!-- 多个账户id筛选 -->
        <include refid="accountListCondition"/>
        <if test="vo.nameOrId != null and vo.nameOrId != ''">
            and (cast(adgroup_id as varchar) like CONCAT('%', #{vo.nameOrId}, '%') or name like CONCAT('%',
            #{vo.nameOrId}, '%'))
        </if>

        <if test="vo.adgroupStatusList!=null">
            and ad_status in
            <foreach collection="vo.adgroupStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        ) mdap on a.adgroup_id = mdap.adgroup_id
        left join marketing_advertiser_screen mass on mdap.status = mass.field_value
        and mdap.platform_id = mass.platform_id and mass.field = 'ADVERTISE_STATUS'
        <if test="map.containsKey('optimizationGoal')">
            left join marketing_convert_type mct on mdap.optimization_goal = mct.name and mdap.platform_id =
            mct.platform_id
        </if>
        join (
        select campaign_id, account_id,promotion_goal,platform_id,campaign_name,status,
        daily_budget
        from marketing_data_campaign
        where 1=1
        <if test="vo.campaignStatusList != null">
            and status in
            <foreach collection="vo.campaignStatusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="vo.promotionGoal != null and vo.promotionGoal.size != 0">
            and promotion_goal in
            <foreach collection="vo.promotionGoal" item="goal" open="(" close=")" separator=",">
                #{goal}
            </foreach>
        </if>
        <if test="vo.campaignId != null and vo.campaignId != ''">
            and campaign_id = #{vo.campaignId}
        </if>
        ) mdc on mdap.campaign_id = mdc.campaign_id
        join (
        select account_id,platform_id,account_name,valid,
        budget_mode,
        budget,
        pull_status,
        balance
        from marketing_advertiser_account
        where agency=false
        <if test="vo.advertiserGroupId != null">
            and account_id in
            (
            select maa.account_id from marketing_advertiser_account_group_rel maagr left join
            marketing_advertiser_account maa on
            maagr.advertiser_account_id = maa.id where maagr.advertiser_account_group_id = #{vo.advertiserGroupId} and
            maa.account_id is not null
            )
        </if>
        <if test="vo.platformId != null and vo.platformId.size != 0">
            and platform_id in
            <foreach collection="vo.platformId" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        ) mda on mdc.account_id = mda.account_id
        left join marketing_advertiser_screen mas on mdc.promotion_goal = mas.field_value and mas.field =
        'PROMOTIONAL_OBJECTIVES' and mdc.platform_id = mas.platform_id
        <!-- 上钻筛选-->
        <if test="vo.creativeId!=null and vo.creativeId!=''">
            join (select adgroup_id,platform_id from marketing_data_creative where creative_id = #{vo.creativeId}) mdcv
            on
            mdcv.adgroup_id = mdap.adgroup_id and mdcv.platform_id = mdap.platform_id
        </if>
        where 1=1
        <if test="vo.accountName != null and vo.accountName != ''">
            and mda.account_name = #{vo.accountName}
        </if>
        <include refid="condition_sql"/>
        <if test="vo.sort != null and vo.sort != '' and vo.order != null and vo.order != ''">
            order by ${vo.sort} ${vo.order}
            <if test="vo.order == 'desc'">
                nulls last
            </if>
            <if test="vo.order == 'asc'">
                nulls first
            </if>
        </if>
    </select>
    <select id="listOverViewByCondition" resultType="ai.yiye.agent.domain.vo.AdvertiserAccountGroupReport">
        with allCountDayData as (
        select
        <include refid="base_column"/>
        day_time
        from marketing_data_solidification_ad_show_report where account_id in
        <foreach collection="accountIds" open="(" close=")" separator="," item="accountId">
            #{accountId}
        </foreach>
        and
        data_time between to_timestamp(#{startDay}, 'yyyy-MM-dd') and to_timestamp(#{endDay}, 'yyyy-MM-dd')
        group by day_time order by day_time
        ),
        groupTarget as (
        select * from marketing_advertiser_account_group_target_daily_report
        where advertiser_account_group_id = #{groupId} and day_at between to_timestamp(#{startDay}, 'yyyy-MM-dd') and to_timestamp(#{endDay}, 'yyyy-MM-dd') order by day_at
        )
        select
        <include refid="old_result_column"/>
        d.day_time, g.*
        from allCountDayData d
        FULL OUTER JOIN groupTarget g on d.day_time = g.day_at

    </select>
    <select id="getByConditions" resultType="ai.yiye.agent.domain.vo.AdvertiserAccountGroupReportVo">
        select
        <include refid="all_column"/>
        from marketing_data_solidification_ad_show_report where account_id in
        <foreach collection="accountIds" open="(" close=")" separator="," item="accountId">
            #{accountId}
        </foreach>
        and
        data_time between to_timestamp(#{startDay}, 'yyyy-MM-dd') and to_timestamp(#{endDay}, 'yyyy-MM-dd')
    </select>
</mapper>
