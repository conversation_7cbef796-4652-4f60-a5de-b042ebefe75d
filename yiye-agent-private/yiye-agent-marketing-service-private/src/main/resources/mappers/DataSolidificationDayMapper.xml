<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.yiye.agent.marketing.mapper.DataSolidificationDayMapper">
    <!--维护（增删）此处字段，【result_one_column】sql脚本也需要维护一份-->
    <sql id="result_column">
        <if test="map.containsKey('viewNum')">
            COALESCE(view_num, 0) as view_num,
        </if>
        <if test="map.containsKey('clickNum')">
            COALESCE(click_num, 0) as click_num,
        </if>
        <if test="map.containsKey('cost')">
            round(COALESCE(cost, 0), 2) as cost,
        </if>
        <if test="map.containsKey('thousandImpressAvgPrice')">
            round(COALESCE(thousand_impress_avg_price, 0), 2) as thousand_impress_avg_price,
        </if>
        <if test="map.containsKey('avgPrice')">
            round(COALESCE(avg_price, 0), 2) as avg_price,
        </if>
        <if test="map.containsKey('landingPagePv')">
            COALESCE(landing_page_pv, 0) as landing_page_pv,
        </if>
        <if test="map.containsKey('landingPageUv')">
            COALESCE(landing_page_uv, 0) as landing_page_uv,
        </if>
        <if test="map.containsKey('fillCountNum')">
            COALESCE(fill_count_num, 0) as fill_count_num,
        </if>
        <if test="map.containsKey('orderNum')">
            COALESCE(order_num, 0) as order_num,
        </if>
        <if test="map.containsKey('orderFinishNum')">
            COALESCE(order_finish_num, 0) as order_finish_num,
        </if>
        <if test="map.containsKey('landingAvgStay')">
            COALESCE(landing_avg_stay, 0) as landing_avg_stay,
        </if>
        <if test="map.containsKey('formAppointmentNum')">
            COALESCE(form_appointment_num, 0) as form_appointment_num,
        </if>
        <if test="map.containsKey('formAppointmentPersonCount')">
            COALESCE(form_appointment_person_count, 0) as form_appointment_person_count,
        </if>
        <if test="map.containsKey('orderAmount')">
            round(COALESCE(order_amount, 0), 2) as order_amount,
        </if>
        <if test="map.containsKey('orderUnitPrice')">
            round(COALESCE(order_unit_price, 0), 2) as order_unit_price,
        </if>
        <if test="map.containsKey('orderROI')">
            round(COALESCE(order_roi, 0), 2) as order_roi,
        </if>
        <if test="map.containsKey('paymentNum')">
            COALESCE(payment_num, 0) as payment_num,
        </if>
        <if test="map.containsKey('paymentAmount')">
            round(COALESCE(payment_amount, 0), 2) as payment_amount,
        </if>
        <if test="map.containsKey('firstPaymentPersonNum')">
            COALESCE(first_payment_person_num, 0) as first_payment_person_num,
        </if>
        <if test="map.containsKey('officialFocusNum')">
            COALESCE(official_focus_num, 0) as official_focus_num,
        </if>
        <if test="map.containsKey('saleClueNum')">
            COALESCE(sale_clue_num, 0) as sale_clue_num,
        </if>
        <if test="map.containsKey('saleCluePersonNum')">
            COALESCE(sale_clue_person_num, 0) as sale_clue_person_num,
        </if>
        <if test="map.containsKey('validClueNum')">
            COALESCE(valid_clue_num, 0) as valid_clue_num,
        </if>
        <if test="map.containsKey('validCluePersonNum')">
            COALESCE(valid_clue_person_num, 0) as valid_clue_person_num,
        </if>
        <if test="map.containsKey('officialFocusCount')">
            COALESCE(official_focus_count, 0) as official_focus_count,
        </if>
        <if test="map.containsKey('officialFocusCount1')">
            COALESCE(official_focus_num, 0) as official_focus_num,
        </if>
        <if test="map.containsKey('validClueCount')">
            COALESCE(valid_clue_count, 0) as valid_clue_count,
        </if>
        <if test="map.containsKey('callLinkCount')">
            COALESCE(call_link_count, 0) as call_link_count,
        </if>
        <if test="map.containsKey('personWechatLinkCount')">
            COALESCE(person_wechat_link_count, 0) as person_wechat_link_count,
        </if>
        <if test="map.containsKey('appointmentCount')">
            COALESCE(appointment_count, 0) as appointment_count,
        </if>
        <if test="map.containsKey('auditionCount')">
            COALESCE(audition_count, 0) as audition_count,
        </if>
        <if test="map.containsKey('auditionedClassCount')">
            COALESCE(auditioned_class_count, 0) as auditioned_class_count,
        </if>
        <if test="map.containsKey('trialCount')">
            COALESCE(trial_count, 0) as trial_count,
        </if>
        <if test="map.containsKey('paymentDepositCount')">
            COALESCE(payment_deposit_count, 0) as payment_deposit_count,
        </if>
        <if test="map.containsKey('payCount')">
            COALESCE(pay_count, 0) as pay_count,
        </if>
        <if test="map.containsKey('convertCount')">
            COALESCE(convert_count, 0) as convert_count,
        </if>
        <if test="map.containsKey('registerCount')">
            COALESCE(register_count, 0) as register_count,
        </if>
        <if test="map.containsKey('activationCount')">
            COALESCE(activation_count, 0) as activation_count,
        </if>
        <if test="map.containsKey('appDownloadFinishCount')">
            COALESCE(app_download_finish_count, 0) as app_download_finish_count,
        </if>
        <if test="map.containsKey('appInstallCount')">
            COALESCE(app_install_count, 0) as app_install_count,
        </if>
        <if test="map.containsKey('appActivationNum')">
            COALESCE(app_activation_num, 0) as app_activation_num,
        </if>
        <if test="map.containsKey('appRegisterNum')">
            COALESCE(app_register_num, 0) as app_register_num,
        </if>
        <if test="map.containsKey('appRetainedPersonNum')">
            COALESCE(app_retained_person_num, 0) as app_retained_person_num,
        </if>
        <if test="map.containsKey('appPayNum')">
            COALESCE(app_pay_num, 0) as app_pay_num,
        </if>
        <if test="map.containsKey('appPayAmount')">
            round(COALESCE(app_pay_amount, 0), 2) as app_pay_amount,
        </if>
        <if test="map.containsKey('lengthOfStay')">
            COALESCE(length_of_stay, 0) as length_of_stay,
        </if>
        <if test="map.containsKey('followNum')">
            COALESCE(follow_num, 0) as follow_num,
        </if>
        <if test="map.containsKey('clickRate')">
            round(COALESCE(click_rate, 0), 2) as click_rate,
        </if>
        <if test="map.containsKey('fillCountRate')">
            round(COALESCE(fill_count_rate, 0), 2) as fill_count_rate,
        </if>
        <if test="map.containsKey('orderCountRate')">
            round(COALESCE(order_count_rate, 0), 2) as order_count_rate,
        </if>
        <if test="map.containsKey('fillCountCost')">
            round(COALESCE(fill_count_cost, 0), 2) as fill_count_cost,
        </if>
        <if test="map.containsKey('orderFinishRate')">
            round(COALESCE(order_finish_rate, 0), 2) as order_finish_rate,
        </if>
        <if test="map.containsKey('orderCountCost')">
            round(COALESCE(order_count_cost, 0), 2) as order_count_cost,
        </if>
        <if test="map.containsKey('orderFinishCost')">
            round(COALESCE(order_finish_cost, 0), 2) as order_finish_cost,
        </if>
        <if test="map.containsKey('formOrderConvertRate')">
            round(COALESCE(form_order_convert_rate, 0), 2) as form_order_convert_rate,
        </if>
        <if test="map.containsKey('formAppointmentRate')">
            round(COALESCE(form_appointment_rate, 0), 2) as form_appointment_rate,
        </if>
        <if test="map.containsKey('buttonFormConvert')">
            round(COALESCE(button_form_convert, 0), 2) as button_form_convert,
        </if>
        <if test="map.containsKey('formAppointmentCost')">
            round(COALESCE(form_appointment_cost, 0), 2) as form_appointment_cost,
        </if>
        <if test="map.containsKey('saleClueConvertRate')">
            round(COALESCE(sale_clue_convert_rate, 0), 2) as sale_clue_convert_rate,
        </if>
        <if test="map.containsKey('validClueConvertRate')">
            round(COALESCE(valid_clue_convert_rate, 0), 2) as valid_clue_convert_rate,
        </if>
        <if test="map.containsKey('appDownloadRate')">
            round(COALESCE(app_download_rate, 0), 2) as app_download_rate,
        </if>
        <if test="map.containsKey('appInstallRate')">
            round(COALESCE(app_install_rate, 0), 2) as app_install_rate,
        </if>
        <if test="map.containsKey('appClickActivationRate')">
            round(COALESCE(app_click_activation_rate, 0), 2) as app_click_activation_rate,
        </if>
        <if test="map.containsKey('appDownloadActivationRate')">
            round(COALESCE(app_download_activation_rate, 0), 2) as app_download_activation_rate,
        </if>
        <if test="map.containsKey('appDownloadActivationCost')">
            round(COALESCE(app_download_activation_cost, 0), 2) as app_download_activation_cost,
        </if>
        <if test="map.containsKey('appRegisterRate')">
            round(COALESCE(app_register_rate, 0), 2) as app_register_rate,
        </if>
        <if test="map.containsKey('appActivationRegisterRate')">
            round(COALESCE(app_activation_register_rate, 0), 2) as app_activation_register_rate,
        </if>
        <if test="map.containsKey('appRetainedRate')">
            round(COALESCE(app_retained_rate, 0), 2) as app_retained_rate,
        </if>
        <if test="map.containsKey('paymentCost')">
            round(COALESCE(payment_cost, 0), 2) as payment_cost,
        </if>
        <if test="map.containsKey('focusCost')">
            round(COALESCE(focus_cost, 0), 2) as focus_cost,
        </if>
        <if test="map.containsKey('officialFocusCost1')">
            round(COALESCE(official_focus_cost1, 0), 2) as official_focus_cost1,
        </if>
        <if test="map.containsKey('saleClueCost')">
            round(COALESCE(sale_clue_cost, 0), 2) as sale_clue_cost,
        </if>
        <if test="map.containsKey('validClueCost1')">
            round(COALESCE(valid_clue_cost1, 0), 2) as valid_clue_cost1,
        </if>
        <if test="map.containsKey('officialFocusCost2')">
            round(COALESCE(official_focus_cost2, 0), 2) as official_focus_cost2,
        </if>
        <if test="map.containsKey('validClueCost2')">
            round(COALESCE(valid_clue_cost2, 0), 2) as valid_clue_cost2,
        </if>
        <if test="map.containsKey('callLinkCost')">
            round(COALESCE(call_link_cost, 0), 2) as call_link_cost,
        </if>
        <if test="map.containsKey('personWechatLinkCost')">
            round(COALESCE(person_wechat_link_cost, 0), 2) as person_wechat_link_cost,
        </if>
        <if test="map.containsKey('appointmentCost')">
            round(COALESCE(appointment_cost, 0), 2) as appointment_cost,
        </if>
        <if test="map.containsKey('tryListenCost')">
            round(COALESCE(try_listen_cost, 0), 2) as try_listen_cost,
        </if>
        <if test="map.containsKey('auditionedClassCost')">
            round(COALESCE(auditioned_class_cost, 0), 2) as auditioned_class_cost,
        </if>
        <if test="map.containsKey('trialCost')">
            round(COALESCE(trial_cost, 0), 2) as trial_cost,
        </if>
        <if test="map.containsKey('paymentDepositCost')">
            round(COALESCE(payment_deposit_cost, 0), 2) as payment_deposit_cost,
        </if>
        <if test="map.containsKey('payCost')">
            round(COALESCE(pay_cost, 0), 2) as pay_cost,
        </if>
        <if test="map.containsKey('convertCost')">
            round(COALESCE(convert_cost, 0), 2) as convert_cost,
        </if>
        <if test="map.containsKey('registerCost')">
            round(COALESCE(register_cost, 0), 2)  as register_cost,
        </if>
        <if test="map.containsKey('activationCost')">
            round(COALESCE(activation_cost, 0), 2) as activation_cost,
        </if>
        <if test="map.containsKey('appDownloadCost')">
            round(COALESCE(app_download_cost, 0), 2) app_download_cost,
        </if>
        <if test="map.containsKey('appInstallCost')">
            round(COALESCE(app_install_cost, 0), 2) as app_install_cost,
        </if>
        <if test="map.containsKey('appRegisterCost')">
            round(COALESCE(app_register_cost, 0), 2) as app_register_cost,
        </if>
        <if test="map.containsKey('appRetainedCost')">
            round(COALESCE(app_retained_cost, 0), 2) as app_retained_cost,
        </if>
        <if test="map.containsKey('appPayCost')">
            round(COALESCE(app_pay_cost, 0), 2) as app_pay_cost,
        </if>
        <if test="map.containsKey('identifyQrCodeNum')">
            COALESCE(identify_qr_code_num, 0) as identify_qr_code_num,
        </if>
        <if test="map.containsKey('identifyQrCodeRate')">
            round(COALESCE(identify_qr_code_rate, 0), 2) as identify_qr_code_rate,
        </if>
        <if test="map.containsKey('identifyQrcodeCost')">
            round(COALESCE(identify_qr_code_cost, 0), 2) as identify_qr_code_cost,
        </if>
        <if test="map.containsKey('addWorkWechatNum')">
            COALESCE(add_work_wechat_num, 0) as add_work_wechat_num,
        </if>
        <if test="map.containsKey('addWorkWechatRate')">
            round(COALESCE(add_work_wechat_rate, 0), 2) as add_work_wechat_rate,
        </if>
        <if test="map.containsKey('addWorkWechatCost')">
            round(COALESCE(add_work_wechat_cost, 0), 2) as add_work_wechat_cost,
        </if>
        <if test="map.containsKey('convertNum')">
            COALESCE(convert_num, 0) as convert_num,
        </if>
        <if test="map.containsKey('targetConvertRate')">
            round(COALESCE(target_convert_rate, 0), 2) as target_convert_rate,
        </if>
        <if test="map.containsKey('targetConvertCost')">
            round(COALESCE(target_convert_cost, 0), 2) as target_convert_cost,
        </if>
        <if test="map.containsKey('deepConvertNum')">
            COALESCE(deep_convert_num, 0) as deep_convert_num,
        </if>
        <if test="map.containsKey('deepConvertRate')">
            round(COALESCE(deep_convert_rate, 0), 2) as deep_convert_rate,
        </if>
        <if test="map.containsKey('deepConvertCost')">
            round(COALESCE(deep_convert_cost, 0), 2) as deep_convert_cost,
        </if>
        <if test="map.containsKey('placeOrderNum')">
            COALESCE(place_order_num, 0) as place_order_num,
        </if>
        <if test="map.containsKey('placeOrderRate')">
            round(COALESCE(place_order_rate, 0), 2) as place_order_rate,
        </if>
        <if test="map.containsKey('placeOrderCost')">
            round(COALESCE(place_order_cost, 0), 2) as place_order_cost,
        </if>
        <if test="map.containsKey('clueFillNum')">
            COALESCE(clue_fill_num, 0) as clue_fill_num,
        </if>
        <if test="map.containsKey('clueFillRate')">
            round(COALESCE(clue_fill_rate, 0), 2) as clue_fill_rate,
        </if>
        <if test="map.containsKey('clueFillCost')">
            round(COALESCE(clue_fill_cost, 0), 2) as clue_fill_cost,
        </if>
        <if test="map.containsKey('clueConnectNum')">
            COALESCE(clue_connect_num, 0) as clue_connect_num,
        </if>
        <if test="map.containsKey('clueConnectRate')">
            round(COALESCE(clue_connect_rate, 0), 2) as clue_connect_rate,
        </if>
        <if test="map.containsKey('clueConnectCost')">
            round(COALESCE(clue_connect_cost, 0), 2) as clue_connect_cost,
        </if>
        <if test="map.containsKey('clueEffectiveNum')">
            COALESCE(clue_effective_num, 0) as clue_effective_num,
        </if>
        <if test="map.containsKey('clueEffectiveRate')">
            round(COALESCE(clue_effective_rate, 0), 2) as clue_effective_rate,
        </if>
        <if test="map.containsKey('clueEffectiveCost')">
            round(COALESCE(clue_effective_cost, 0), 2) as clue_effective_cost,
        </if>
        <if test="map.containsKey('signClassNum')">
            COALESCE(sign_class_num, 0) as sign_class_num,
        </if>
        <if test="map.containsKey('signClassRate')">
            round(COALESCE(sign_class_rate, 0), 2) as sign_class_rate,
        </if>
        <if test="map.containsKey('signClassCost')">
            round(COALESCE(sign_class_cost, 0), 2) as sign_class_cost,
        </if>
        <if test="map.containsKey('totalConvertRate')">
            round(COALESCE(total_convert_rate, 0), 2) as total_convert_rate,
        </if>
    </sql>

    <!--只返回单列：此处字段与【result_column】sql脚本一致，只是结尾没有逗号分割-->
    <sql id="result_one_column">
        <if test="map.containsKey('viewNum')">
            COALESCE(view_num, 0) as view_num
        </if>
        <if test="map.containsKey('clickNum')">
            COALESCE(click_num, 0) as click_num
        </if>
        <if test="map.containsKey('cost')">
            COALESCE(cost, 0) as cost
        </if>
        <if test="map.containsKey('thousandImpressAvgPrice')">
            COALESCE(thousand_impress_avg_price, 0) as thousand_impress_avg_price
        </if>
        <if test="map.containsKey('avgPrice')">
            COALESCE(avg_price, 0) as avg_price
        </if>
        <if test="map.containsKey('landingPagePv')">
            COALESCE(landing_page_pv, 0) as landing_page_pv
        </if>
        <if test="map.containsKey('landingPageUv')">
            COALESCE(landing_page_uv, 0) as landing_page_uv
        </if>
        <if test="map.containsKey('fillCountNum')">
            COALESCE(fill_count_num, 0) as fill_count_num
        </if>
        <if test="map.containsKey('orderNum')">
            COALESCE(order_num, 0) as order_num
        </if>
        <if test="map.containsKey('orderFinishNum')">
            COALESCE(order_finish_num, 0) as order_finish_num
        </if>
        <if test="map.containsKey('landingAvgStay')">
            COALESCE(landing_avg_stay, 0) as landing_avg_stay
        </if>
        <if test="map.containsKey('formAppointmentNum')">
            COALESCE(form_appointment_num, 0) as form_appointment_num
        </if>
        <if test="map.containsKey('formAppointmentPersonCount')">
            COALESCE(form_appointment_person_count, 0) as form_appointment_person_count
        </if>
        <if test="map.containsKey('orderAmount')">
            COALESCE(order_amount, 0) as order_amount
        </if>
        <if test="map.containsKey('orderUnitPrice')">
            COALESCE(order_unit_price, 0) as order_unit_price
        </if>
        <if test="map.containsKey('orderROI')">
            COALESCE(order_roi, 0) as order_roi
        </if>
        <if test="map.containsKey('paymentNum')">
            COALESCE(payment_num, 0) as payment_num
        </if>
        <if test="map.containsKey('paymentAmount')">
            COALESCE(payment_amount, 0) as payment_amount
        </if>
        <if test="map.containsKey('firstPaymentPersonNum')">
            COALESCE(first_payment_person_num, 0) as first_payment_person_num
        </if>
        <if test="map.containsKey('officialFocusNum')">
            COALESCE(official_focus_num, 0) as official_focus_num
        </if>
        <if test="map.containsKey('saleClueNum')">
            COALESCE(sale_clue_num, 0) as sale_clue_num
        </if>
        <if test="map.containsKey('saleCluePersonNum')">
            COALESCE(sale_clue_person_num, 0) as sale_clue_person_num
        </if>
        <if test="map.containsKey('validClueNum')">
            COALESCE(valid_clue_num, 0) as valid_clue_num
        </if>
        <if test="map.containsKey('validCluePersonNum')">
            COALESCE(valid_clue_person_num, 0) as valid_clue_person_num
        </if>
        <if test="map.containsKey('officialFocusCount')">
            COALESCE(official_focus_count, 0) as official_focus_count
        </if>
        <if test="map.containsKey('officialFocusCount1')">
            COALESCE(official_focus_num, 0) as official_focus_num
        </if>
        <if test="map.containsKey('validClueCount')">
            COALESCE(valid_clue_count, 0) as valid_clue_count
        </if>
        <if test="map.containsKey('callLinkCount')">
            COALESCE(call_link_count, 0) as call_link_count
        </if>
        <if test="map.containsKey('personWechatLinkCount')">
            COALESCE(person_wechat_link_count, 0) as person_wechat_link_count
        </if>
        <if test="map.containsKey('appointmentCount')">
            COALESCE(appointment_count, 0) as appointment_count
        </if>
        <if test="map.containsKey('auditionCount')">
            COALESCE(audition_count, 0) as audition_count
        </if>
        <if test="map.containsKey('auditionedClassCount')">
            COALESCE(auditioned_class_count, 0) as auditioned_class_count
        </if>
        <if test="map.containsKey('trialCount')">
            COALESCE(trial_count, 0) as trial_count
        </if>
        <if test="map.containsKey('paymentDepositCount')">
            COALESCE(payment_deposit_count, 0) as payment_deposit_count
        </if>
        <if test="map.containsKey('payCount')">
            COALESCE(pay_count, 0) as pay_count
        </if>
        <if test="map.containsKey('convertCount')">
            COALESCE(convert_count, 0) as convert_count
        </if>
        <if test="map.containsKey('registerCount')">
            COALESCE(register_count, 0) as register_count
        </if>
        <if test="map.containsKey('activationCount')">
            COALESCE(activation_count, 0) as activation_count
        </if>
        <if test="map.containsKey('appDownloadFinishCount')">
            COALESCE(app_download_finish_count, 0) as app_download_finish_count
        </if>
        <if test="map.containsKey('appInstallCount')">
            COALESCE(app_install_count, 0) as app_install_count
        </if>
        <if test="map.containsKey('appActivationNum')">
            COALESCE(app_activation_num, 0) as app_activation_num
        </if>
        <if test="map.containsKey('appRegisterNum')">
            COALESCE(app_register_num, 0) as app_register_num
        </if>
        <if test="map.containsKey('appRetainedPersonNum')">
            COALESCE(app_retained_person_num, 0) as app_retained_person_num
        </if>
        <if test="map.containsKey('appPayNum')">
            COALESCE(app_pay_num, 0) as app_pay_num
        </if>
        <if test="map.containsKey('appPayAmount')">
            COALESCE(app_pay_amount, 0) as app_pay_amount
        </if>
        <if test="map.containsKey('lengthOfStay')">
            COALESCE(length_of_stay, 0) as length_of_stay
        </if>
        <if test="map.containsKey('followNum')">
            COALESCE(follow_num, 0) as follow_num
        </if>
        <if test="map.containsKey('clickRate')">
            COALESCE(click_rate, 0) as click_rate
        </if>
        <if test="map.containsKey('fillCountRate')">
            COALESCE(fill_count_rate, 0) as fill_count_rate
        </if>
        <if test="map.containsKey('orderCountRate')">
            COALESCE(order_count_rate, 0) as order_count_rate
        </if>
        <if test="map.containsKey('fillCountCost')">
            COALESCE(fill_count_cost, 0) as fill_count_cost
        </if>
        <if test="map.containsKey('orderFinishRate')">
            COALESCE(order_finish_rate, 0) as order_finish_rate
        </if>
        <if test="map.containsKey('orderCountCost')">
            COALESCE(order_count_cost, 0) as order_count_cost
        </if>
        <if test="map.containsKey('orderFinishCost')">
            COALESCE(order_finish_cost, 0) as order_finish_cost
        </if>
        <if test="map.containsKey('formOrderConvertRate')">
            COALESCE(form_order_convert_rate, 0) as form_order_convert_rate
        </if>
        <if test="map.containsKey('formAppointmentRate')">
            COALESCE(form_appointment_rate, 0) as form_appointment_rate
        </if>
        <if test="map.containsKey('buttonFormConvert')">
            COALESCE(button_form_convert, 0) as button_form_convert
        </if>
        <if test="map.containsKey('formAppointmentCost')">
            COALESCE(form_appointment_cost, 0) as form_appointment_cost
        </if>
        <if test="map.containsKey('saleClueConvertRate')">
            COALESCE(sale_clue_convert_rate, 0) as sale_clue_convert_rate
        </if>
        <if test="map.containsKey('validClueConvertRate')">
            COALESCE(valid_clue_convert_rate, 0) as valid_clue_convert_rate
        </if>
        <if test="map.containsKey('appDownloadRate')">
            COALESCE(app_download_rate, 0) as app_download_rate
        </if>
        <if test="map.containsKey('appInstallRate')">
            COALESCE(app_install_rate, 0) as app_install_rate
        </if>
        <if test="map.containsKey('appClickActivationRate')">
            COALESCE(app_click_activation_rate, 0) as app_click_activation_rate
        </if>
        <if test="map.containsKey('appDownloadActivationRate')">
            COALESCE(app_download_activation_rate, 0) as app_download_activation_rate
        </if>
        <if test="map.containsKey('appDownloadActivationCost')">
            COALESCE(app_download_activation_cost, 0) as app_download_activation_cost
        </if>
        <if test="map.containsKey('appRegisterRate')">
            COALESCE(app_register_rate, 0) as app_register_rate
        </if>
        <if test="map.containsKey('appActivationRegisterRate')">
            COALESCE(app_activation_register_rate, 0) as app_activation_register_rate
        </if>
        <if test="map.containsKey('appRetainedRate')">
            COALESCE(app_retained_rate, 0) as app_retained_rate
        </if>
        <if test="map.containsKey('paymentCost')">
            COALESCE(payment_cost, 0) as payment_cost
        </if>
        <if test="map.containsKey('focusCost')">
            COALESCE(focus_cost, 0) as focus_cost
        </if>
        <if test="map.containsKey('officialFocusCost1')">
            COALESCE(official_focus_cost1, 0) as official_focus_cost1
        </if>
        <if test="map.containsKey('saleClueCost')">
            COALESCE(sale_clue_cost, 0) as sale_clue_cost
        </if>
        <if test="map.containsKey('validClueCost1')">
            COALESCE(valid_clue_cost1, 0) as valid_clue_cost1
        </if>
        <if test="map.containsKey('officialFocusCost2')">
            COALESCE(official_focus_cost2, 0) as official_focus_cost2
        </if>
        <if test="map.containsKey('validClueCost2')">
            COALESCE(valid_clue_cost2, 0) as valid_clue_cost2
        </if>
        <if test="map.containsKey('callLinkCost')">
            COALESCE(call_link_cost, 0) as call_link_cost
        </if>
        <if test="map.containsKey('personWechatLinkCost')">
            COALESCE(person_wechat_link_cost, 0) as person_wechat_link_cost
        </if>
        <if test="map.containsKey('appointmentCost')">
            COALESCE(appointment_cost, 0) as appointment_cost
        </if>
        <if test="map.containsKey('tryListenCost')">
            COALESCE(try_listen_cost, 0) as try_listen_cost
        </if>
        <if test="map.containsKey('auditionedClassCost')">
            COALESCE(auditioned_class_cost, 0) as auditioned_class_cost
        </if>
        <if test="map.containsKey('trialCost')">
            COALESCE(trial_cost, 0) as trial_cost
        </if>
        <if test="map.containsKey('paymentDepositCost')">
            COALESCE(payment_deposit_cost, 0) as payment_deposit_cost
        </if>
        <if test="map.containsKey('payCost')">
            COALESCE(pay_cost, 0) as pay_cost
        </if>
        <if test="map.containsKey('convertCost')">
            COALESCE(convert_cost, 0) as convert_cost
        </if>
        <if test="map.containsKey('registerCost')">
            COALESCE(register_cost, 0)  as register_cost
        </if>
        <if test="map.containsKey('activationCost')">
            COALESCE(activation_cost, 0) as activation_cost
        </if>
        <if test="map.containsKey('appDownloadCost')">
            COALESCE(app_download_cost, 0) app_download_cost
        </if>
        <if test="map.containsKey('appInstallCost')">
            COALESCE(app_install_cost, 0) as app_install_cost
        </if>
        <if test="map.containsKey('appRegisterCost')">
            COALESCE(app_register_cost, 0)as app_register_cost
        </if>
        <if test="map.containsKey('appRetainedCost')">
            COALESCE(app_retained_cost, 0) as app_retained_cost
        </if>
        <if test="map.containsKey('appPayCost')">
            COALESCE(app_pay_cost, 0) as app_pay_cost
        </if>
        <if test="map.containsKey('identifyQrCodeNum')">
            COALESCE(identify_qr_code_num, 0) as identify_qr_code_num
        </if>
        <if test="map.containsKey('identifyQrCodeRate')">
            round(COALESCE(identify_qr_code_rate, 0), 2) as identify_qr_code_rate
        </if>
        <if test="map.containsKey('identifyQrcodeCost')">
            round(COALESCE(identify_qr_code_cost, 0), 2) as identify_qr_code_cost
        </if>
        <if test="map.containsKey('addWorkWechatNum')">
            COALESCE(add_work_wechat_num, 0) as add_work_wechat_num
        </if>
        <if test="map.containsKey('addWorkWechatRate')">
            round(COALESCE(add_work_wechat_rate, 0), 2) as add_work_wechat_rate
        </if>
        <if test="map.containsKey('addWorkWechatCost')">
            round(COALESCE(add_work_wechat_cost, 0), 2) as add_work_wechat_cost
        </if>
        <if test="map.containsKey('convertNum')">
            COALESCE(convert_num, 0) as convert_num
        </if>
        <if test="map.containsKey('targetConvertRate')">
            round(COALESCE(target_convert_rate, 0), 2) as target_convert_rate
        </if>
        <if test="map.containsKey('targetConvertCost')">
            round(COALESCE(target_convert_cost, 0), 2) as target_convert_cost
        </if>
        <if test="map.containsKey('deepConvertNum')">
            COALESCE(deep_convert_num, 0) as deep_convert_num
        </if>
        <if test="map.containsKey('deepConvertRate')">
            round(COALESCE(deep_convert_rate, 0), 2) as deep_convert_rate
        </if>
        <if test="map.containsKey('deepConvertCost')">
            round(COALESCE(deep_convert_cost, 0), 2) as deep_convert_cost
        </if>
        <if test="map.containsKey('placeOrderNum')">
            COALESCE(place_order_num, 0) as place_order_num
        </if>
        <if test="map.containsKey('placeOrderRate')">
            round(COALESCE(place_order_rate, 0), 2) as place_order_rate
        </if>
        <if test="map.containsKey('placeOrderCost')">
            round(COALESCE(place_order_cost, 0), 2) as place_order_cost
        </if>
        <if test="map.containsKey('clueFillNum')">
            COALESCE(clue_fill_num, 0) as clue_fill_num
        </if>
        <if test="map.containsKey('clueFillRate')">
            round(COALESCE(clue_fill_rate, 0), 2) as clue_fill_rate
        </if>
        <if test="map.containsKey('clueFillCost')">
            round(COALESCE(clue_fill_cost, 0), 2) as clue_fill_cost
        </if>
        <if test="map.containsKey('clueConnectNum')">
            COALESCE(clue_connect_num, 0) as clue_connect_num
        </if>
        <if test="map.containsKey('clueConnectRate')">
            round(COALESCE(clue_connect_rate, 0), 2) as clue_connect_rate
        </if>
        <if test="map.containsKey('clueConnectCost')">
            round(COALESCE(clue_connect_cost, 0), 2) as clue_connect_cost
        </if>
        <if test="map.containsKey('clueEffectiveNum')">
            COALESCE(clue_effective_num, 0) as clue_effective_num
        </if>
        <if test="map.containsKey('clueEffectiveRate')">
            round(COALESCE(clue_effective_rate, 0), 2) as clue_effective_rate
        </if>
        <if test="map.containsKey('clueEffectiveCost')">
            round(COALESCE(clue_effective_cost, 0), 2) as clue_effective_cost
        </if>
        <if test="map.containsKey('signClassNum')">
            COALESCE(sign_class_num, 0) as sign_class_num
        </if>
        <if test="map.containsKey('signClassRate')">
            round(COALESCE(sign_class_rate, 0), 2) as sign_class_rate
        </if>
        <if test="map.containsKey('signClassCost')">
            round(COALESCE(sign_class_cost, 0), 2) as sign_class_cost
        </if>
        <if test="map.containsKey('totalConvertRate')">
            COALESCE(total_convert_rate, 0) as total_convert_rate
        </if>
    </sql>

    <sql id="base_column">
        <if test="map.containsKey('viewNum')">
            COALESCE(sum(view_num), 0) as view_num,
        </if>
        <if test="map.containsKey('clickNum')">
            COALESCE(sum(click_num), 0) as click_num,
        </if>
        <if test="map.containsKey('cost')">
            COALESCE(sum(cost), 0) / 100  as cost,
        </if>
        <if test="map.containsKey('thousandImpressAvgPrice')">
            case when COALESCE(sum(view_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(view_num), 0) * 10 end as thousand_impress_avg_price,
        </if>
        <if test="map.containsKey('avgPrice')">
            case when COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(click_num), 0) / 100 end as avg_price,
        </if>
        <if test="map.containsKey('landingPagePv')">
            COALESCE(sum(landing_page_pv), 0) as landing_page_pv,
        </if>
        <if test="map.containsKey('landingPageUv')">
            COALESCE(sum(landing_page_uv), 0) as landing_page_uv,
        </if>
        <if test="map.containsKey('fillCountNum')">
            COALESCE(sum(fill_count_num), 0) as fill_count_num,
        </if>
        <if test="map.containsKey('orderNum')">
            COALESCE(sum(order_num), 0) as order_num,
        </if>
        <if test="map.containsKey('orderFinishNum')">
            COALESCE(sum(order_finish_num), 0) as order_finish_num,
        </if>
        <if test="map.containsKey('landingAvgStay')">
            case when COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(length_of_stay), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(length_of_stay), 0) / COALESCE(sum(landing_page_pv), 0) end as landing_avg_stay,
        </if>
        <if test="map.containsKey('formAppointmentNum')">
            COALESCE(sum(form_appointment_num), 0) as form_appointment_num,
        </if>
        <if test="map.containsKey('formAppointmentPersonCount')">
            COALESCE(sum(form_appointment_person_count), 0) as form_appointment_person_count,
        </if>
        <if test="map.containsKey('orderAmount')">
            COALESCE(sum(order_amount), 0) / 100 as order_amount,
        </if>
        <if test="map.containsKey('orderUnitPrice')">
            case when COALESCE(sum(order_amount), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(order_amount), 0) / COALESCE(sum(place_order_num), 0) / 100 end as order_unit_price,
        </if>
        <if test="map.containsKey('orderROI')">
            case when COALESCE(sum(order_amount), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            (COALESCE(sum(order_amount), 0)/100) / (COALESCE(sum(cost), 0)/100) end as order_roi,
        </if>
        <if test="map.containsKey('paymentNum')">
            COALESCE(sum(payment_num), 0) as payment_num,
        </if>
        <if test="map.containsKey('paymentAmount')">
            COALESCE(sum(payment_amount), 0) / 100 as payment_amount,
        </if>
        <if test="map.containsKey('firstPaymentPersonNum')">
            COALESCE(sum(first_payment_person_num), 0) as first_payment_person_num,
        </if>
        <if test="map.containsKey('officialFocusNum')">
            COALESCE(sum(official_focus_num), 0) as official_focus_num,
        </if>
        <if test="map.containsKey('saleClueNum')">
            COALESCE(sum(sale_clue_num), 0) as sale_clue_num,
        </if>
        <if test="map.containsKey('saleCluePersonNum')">
            COALESCE(sum(sale_clue_person_num), 0) as sale_clue_person_num,
        </if>
        <if test="map.containsKey('validClueNum')">
            COALESCE(sum(valid_clue_num), 0) as valid_clue_num,
        </if>
        <if test="map.containsKey('validCluePersonNum')">
            COALESCE(sum(valid_clue_person_num), 0) as valid_clue_person_num,
        </if>
        <if test="map.containsKey('officialFocusCount')">
            COALESCE(sum(official_focus_count), 0) as official_focus_count,
        </if>
        <if test="map.containsKey('officialFocusCount1')">
            COALESCE(sum(official_focus_num), 0) as official_focus_num,
        </if>
        <if test="map.containsKey('validClueCount')">
            COALESCE(sum(valid_clue_count), 0) as valid_clue_count,
        </if>
        <if test="map.containsKey('callLinkCount')">
            COALESCE(sum(call_link_count), 0) as call_link_count,
        </if>
        <if test="map.containsKey('personWechatLinkCount')">
            COALESCE(sum(person_wechat_link_count), 0) as person_wechat_link_count,
        </if>
        <if test="map.containsKey('appointmentCount')">
            COALESCE(sum(appointment_count), 0) as appointment_count,
        </if>
        <if test="map.containsKey('auditionCount')">
            COALESCE(sum(audition_count), 0) as audition_count,
        </if>
        <if test="map.containsKey('auditionedClassCount')">
            COALESCE(sum(auditioned_class_count), 0) as auditioned_class_count,
        </if>
        <if test="map.containsKey('trialCount')">
            COALESCE(sum(trial_count), 0) as trial_count,
        </if>
        <if test="map.containsKey('paymentDepositCount')">
            COALESCE(sum(payment_deposit_count), 0) as payment_deposit_count,
        </if>
        <if test="map.containsKey('payCount')">
            COALESCE(sum(pay_count), 0) as pay_count,
        </if>
        <if test="map.containsKey('convertCount')">
            COALESCE(sum(convert_count), 0) as convert_count,
        </if>
        <if test="map.containsKey('registerCount')">
            COALESCE(sum(register_count), 0) as register_count,
        </if>
        <if test="map.containsKey('activationCount')">
            COALESCE(sum(activation_count), 0) as activation_count,
        </if>
        <if test="map.containsKey('appDownloadFinishCount')">
            COALESCE(sum(app_download_finish_count), 0)as app_download_finish_count,
        </if>
        <if test="map.containsKey('appInstallCount')">
            COALESCE(sum(app_install_count), 0) as app_install_count,
        </if>
        <if test="map.containsKey('appActivationNum')">
            COALESCE(sum(app_activation_count), 0) as app_activation_num,
        </if>
        <if test="map.containsKey('appRegisterNum')">
            COALESCE(sum(app_register_count), 0) as app_register_num,
        </if>
        <if test="map.containsKey('appRetainedPersonNum')">
            COALESCE(sum(app_retained_person_num), 0) as app_retained_person_num,
        </if>
        <if test="map.containsKey('appPayNum')">
            COALESCE(sum(app_pay_num), 0) as app_pay_num,
        </if>
        <if test="map.containsKey('appPayAmount')">
            COALESCE(sum(app_pay_amount), 0) as app_pay_amount,
        </if>
        <if test="map.containsKey('lengthOfStay')">
            COALESCE(sum(length_of_stay), 0) as length_of_stay,
        </if>
        <if test="map.containsKey('followNum')">
            COALESCE(sum(follow_num), 0) as follow_num,
        </if>
        <if test="map.containsKey('clickRate')">
            case when COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(view_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(click_num), 0) / COALESCE(sum(view_num), 0) *100 end as click_rate,
        </if>
        <if test="map.containsKey('fillCountRate')">
            case when COALESCE(sum(fill_count_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(fill_count_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as fill_count_rate,
        </if>
        <if test="map.containsKey('orderCountRate')">
            case when COALESCE(sum(order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(order_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as order_count_rate,
        </if>
        <if test="map.containsKey('fillCountCost')">
            case when COALESCE(sum(fill_count_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(fill_count_num), 0) / 100  end as fill_count_cost,
        </if>
        <if test="map.containsKey('orderFinishRate')">
            case when COALESCE(sum(order_finish_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(order_finish_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as order_finish_rate,
        </if>
        <if test="map.containsKey('orderCountCost')">
            case when COALESCE(sum(order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(order_num), 0) / 100 end as order_count_cost,
        </if>
        <if test="map.containsKey('orderFinishCost')">
            case when COALESCE(sum(order_finish_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(order_finish_num), 0) / 100  end as order_finish_cost,
        </if>
        <if test="map.containsKey('formOrderConvertRate')">
            case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(place_order_num), 0) / COALESCE(sum(form_appointment_num), 0) *100 end as form_order_convert_rate,
        </if>
        <if test="map.containsKey('formAppointmentRate')">
            case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(form_appointment_num), 0) / COALESCE(sum(click_num), 0) *100 end as form_appointment_rate,
        </if>
        <if test="map.containsKey('buttonFormConvert')">
            case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(button_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(form_appointment_num), 0) / COALESCE(sum(button_count), 0) * 100 end as button_form_convert,
        </if>
        <if test="map.containsKey('formAppointmentCost')">
            case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(form_appointment_num), 0) / 100 end as form_appointment_cost,
        </if>
        <if test="map.containsKey('saleClueConvertRate')">
            case when COALESCE(sum(sale_clue_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(sale_clue_num), 0) / COALESCE(sum(click_num), 0) *100 end as sale_clue_convert_rate,
        </if>
        <if test="map.containsKey('validClueConvertRate')">
            case when COALESCE(sum(valid_clue_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(valid_clue_num), 0) / COALESCE(sum(click_num), 0) *100 end as valid_clue_convert_rate,
        </if>
        <if test="map.containsKey('appDownloadRate')">
            case when COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_download_finish_count), 0) / COALESCE(sum(click_num), 0) *100 end as app_download_rate,
        </if>
        <if test="map.containsKey('appInstallRate')">
            case when COALESCE(sum(app_install_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_install_count), 0) / COALESCE(sum(click_num), 0) *100 end as app_install_rate,
        </if>
        <if test="map.containsKey('appClickActivationRate')">
            case when COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_activation_count), 0) / COALESCE(sum(click_num), 0) *100 end as app_click_activation_rate,
        </if>
        <if test="map.containsKey('appDownloadActivationRate')">
            case when COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_activation_count), 0) / COALESCE(sum(app_download_finish_count), 0) *100 end as app_download_activation_rate,
        </if>
        <if test="map.containsKey('appDownloadActivationCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(app_activation_count), 0) / 100 end as app_download_activation_cost,
        </if>
        <if test="map.containsKey('appRegisterRate')">
            case when COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_register_count), 0) / COALESCE(sum(click_num), 0) *100 end as app_register_rate,
        </if>
        <if test="map.containsKey('appActivationRegisterRate')">
            case when COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_register_count), 0) / COALESCE(sum(app_activation_count), 0) *100 end as app_activation_register_rate,
        </if>
        <if test="map.containsKey('appRetainedRate')">
            case when COALESCE(sum(app_retained_person_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_retained_person_num), 0) / COALESCE(sum(app_activation_count), 0) *100 end as app_retained_rate,
        </if>
        <if test="map.containsKey('paymentCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(payment_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(payment_num), 0) / 100 end as payment_cost,
        </if>
        <if test="map.containsKey('focusCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(follow_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(follow_num), 0) / 100 end as focus_cost,
        </if>
        <if test="map.containsKey('officialFocusCost1')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(official_focus_num), 0) / 100 end as official_focus_cost1,
        </if>
        <if test="map.containsKey('saleClueCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(sale_clue_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(sale_clue_num), 0) / 100 end as sale_clue_cost,
        </if>
        <if test="map.containsKey('validClueCost1')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(valid_clue_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(valid_clue_num), 0) / 100 end as valid_clue_cost1,
        </if>
        <if test="map.containsKey('officialFocusCost2')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(official_focus_count), 0) / 100 end as official_focus_cost2,
        </if>
        <if test="map.containsKey('validClueCost2')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(valid_clue_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(valid_clue_count), 0) / 100 end as valid_clue_cost2,
        </if>
        <if test="map.containsKey('callLinkCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(call_link_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(call_link_count), 0) / 100 end as call_link_cost,
        </if>
        <if test="map.containsKey('personWechatLinkCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(person_wechat_link_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(person_wechat_link_count), 0) / 100 end as person_wechat_link_cost,
        </if>
        <if test="map.containsKey('appointmentCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(appointment_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(appointment_count), 0) / 100 end as appointment_cost,
        </if>
        <if test="map.containsKey('tryListenCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(audition_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(audition_count), 0) / 100 end as try_listen_cost,
        </if>
        <if test="map.containsKey('auditionedClassCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(auditioned_class_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(auditioned_class_count), 0) / 100 end as auditioned_class_cost,
        </if>
        <if test="map.containsKey('trialCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(trial_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(trial_count), 0) / 100 end as trial_cost,
        </if>
        <if test="map.containsKey('paymentDepositCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(payment_deposit_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(payment_deposit_count), 0) / 100 end as payment_deposit_cost,
        </if>
        <if test="map.containsKey('payCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(pay_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(pay_count), 0) / 100 end as pay_cost,
        </if>
        <if test="map.containsKey('convertCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(convert_count), 0) / 100 end as convert_cost,
        </if>
        <if test="map.containsKey('registerCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(register_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(register_count), 0) / 100 end as register_cost,
        </if>
        <if test="map.containsKey('activationCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(activation_count), 0) / 100 end as activation_cost,
        </if>
        <if test="map.containsKey('appDownloadCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(app_download_finish_count), 0) / 100 end as app_download_cost,
        </if>
        <if test="map.containsKey('appInstallCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_install_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(app_install_count), 0) / 100 end as app_install_cost,
        </if>
        <if test="map.containsKey('appRegisterCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(app_register_count), 0) / 100 end as app_register_cost,
        </if>
        <if test="map.containsKey('appRetainedCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_retained_person_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(app_retained_person_num), 0) / 100 end as app_retained_cost,
        </if>
        <if test="map.containsKey('appPayCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_pay_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(app_pay_num), 0) / 100 end as app_pay_cost,
        </if>
        <if test="map.containsKey('identifyQrCodeNum')">
            COALESCE(sum(identify_qr_code_num), 0) as identify_qr_code_num,
        </if>
        <if test="map.containsKey('identifyQrCodeRate')">
            case when COALESCE(sum(identify_qr_code_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(identify_qr_code_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as identify_qr_code_rate,
        </if>
        <if test="map.containsKey('identifyQrcodeCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(identify_qr_code_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(identify_qr_code_num), 0) / 100 end as identify_qr_code_cost,
        </if>
        <if test="map.containsKey('addWorkWechatNum')">
            COALESCE(sum(add_work_wechat_num), 0) as add_work_wechat_num,
        </if>
        <if test="map.containsKey('addWorkWechatRate')">
            case when COALESCE(sum(add_work_wechat_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(add_work_wechat_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as add_work_wechat_rate,
        </if>
        <if test="map.containsKey('addWorkWechatCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(add_work_wechat_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(add_work_wechat_num), 0) / 100 end as add_work_wechat_cost,
        </if>
        <if test="map.containsKey('convertNum')">
            COALESCE(sum(convert_num), 0) as convert_num,
        </if>
        <if test="map.containsKey('targetConvertRate')">
            case when COALESCE(sum(convert_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(convert_num), 0) / COALESCE(sum(click_num), 0) * 100 end as target_convert_rate,
        </if>
        <if test="map.containsKey('targetConvertCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(convert_num), 0) / 100 end as target_convert_cost,
        </if>
        <if test="map.containsKey('deepConvertNum')">
            COALESCE(sum(deep_convert_num), 0) as deep_convert_num,
        </if>
        <if test="map.containsKey('deepConvertRate')">
            case when COALESCE(sum(deep_convert_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(deep_convert_num), 0) / COALESCE(sum(click_num), 0) * 100 end as deep_convert_rate,
        </if>
        <if test="map.containsKey('deepConvertCost')">
            case when COALESCE(sum(deep_convert_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(deep_convert_num), 0) / 100 end as deep_convert_cost,
        </if>
        <if test="map.containsKey('placeOrderNum')">
            COALESCE(sum(place_order_num), 0) as place_order_num,
        </if>
        <if test="map.containsKey('placeOrderRate')">
            case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(place_order_num), 0) / COALESCE(sum(click_num), 0) * 100 end as place_order_rate,
        </if>
        <if test="map.containsKey('placeOrderCost')">
            case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(place_order_num), 0) / 100 end as place_order_cost,
        </if>
        <if test="map.containsKey('clueFillNum')">
            COALESCE(sum(clue_fill_num), 0) as clue_fill_num,
        </if>
        <if test="map.containsKey('clueFillRate')">
            case when COALESCE(sum(clue_fill_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(clue_fill_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as clue_fill_rate,
        </if>
        <if test="map.containsKey('clueFillCost')">
            case when COALESCE(sum(clue_fill_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(clue_fill_num), 0) / 100 end as clue_fill_cost,
        </if>
        <if test="map.containsKey('clueConnectNum')">
            COALESCE(sum(clue_connect_num), 0) as clue_connect_num,
        </if>
        <if test="map.containsKey('clueConnectRate')">
            case when COALESCE(sum(clue_connect_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(clue_fill_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(clue_connect_num), 0) / COALESCE(sum(clue_fill_num), 0) *100 end as clue_connect_rate,
        </if>
        <if test="map.containsKey('clueConnectCost')">
            case when COALESCE(sum(clue_connect_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(clue_connect_num), 0) / 100 end as clue_connect_cost,
        </if>
        <if test="map.containsKey('clueEffectiveNum')">
            COALESCE(sum(clue_effective_num), 0) as clue_effective_num,
        </if>
        <if test="map.containsKey('clueEffectiveRate')">
            case when COALESCE(sum(clue_connect_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(clue_effective_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(clue_effective_num), 0) / COALESCE(sum(clue_connect_num), 0) *100 end as clue_effective_rate,
        </if>
        <if test="map.containsKey('clueEffectiveCost')">
            case when COALESCE(sum(clue_effective_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(clue_effective_num), 0) / 100 end as clue_effective_cost,
        </if>
        <if test="map.containsKey('signClassNum')">
            COALESCE(sum(sign_class_num), 0) as sign_class_num,
        </if>
        <if test="map.containsKey('signClassRate')">
            case when COALESCE(sum(sign_class_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(clue_effective_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(sign_class_num), 0) / COALESCE(sum(clue_effective_num), 0) *100 end as sign_class_rate,
        </if>
        <if test="map.containsKey('signClassCost')">
            case when COALESCE(sum(sign_class_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(sign_class_num), 0) / 100 end as sign_class_cost,
        </if>
        <if test="map.containsKey('totalConvertRate')">
            case when COALESCE(sum(sign_class_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(sign_class_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as total_convert_rate,
        </if>
    </sql>

    <sql id="all_column">
        COALESCE(sum(length_of_stay), 0) as length_of_stay,
        COALESCE(sum(view_num), 0) as view_num,
        COALESCE(sum(click_num), 0) as click_num,
        COALESCE(sum(cost), 0) / 100 as cost,
        case when COALESCE(sum(view_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(view_num), 0) * 10 end as thousand_impress_avg_price,
        case when COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(click_num), 0) / 100 end as avg_price,
        COALESCE(sum(landing_page_pv), 0) as landing_page_pv,
        COALESCE(sum(landing_page_uv), 0) as landing_page_uv,
        COALESCE(sum(fill_count_num), 0) as fill_count_num,
        COALESCE(sum(order_num), 0) as order_num,
        COALESCE(sum(order_finish_num), 0) as order_finish_num,
        case when COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(length_of_stay), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(length_of_stay), 0) / COALESCE(sum(landing_page_pv), 0) end as landing_avg_stay,
        COALESCE(sum(form_appointment_num), 0) as form_appointment_num,
        COALESCE(sum(form_appointment_person_count), 0) as form_appointment_person_count,
        COALESCE(sum(order_amount), 0) / 100 as order_amount,
        case when COALESCE(sum(order_amount), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(order_amount), 0) / COALESCE(sum(place_order_num), 0) / 100 end as order_unit_price,
        case when COALESCE(sum(order_amount), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        (COALESCE(sum(order_amount), 0)/100) / (COALESCE(sum(cost), 0)/100) end as order_roi,
        COALESCE(sum(payment_num), 0) as payment_num,
        COALESCE(sum(payment_amount), 0) / 100 as payment_amount,
        COALESCE(sum(first_payment_person_num), 0) as first_payment_person_num,
        COALESCE(sum(official_focus_num), 0) as official_focus_num,
        COALESCE(sum(sale_clue_num), 0) as sale_clue_num,
        COALESCE(sum(sale_clue_person_num), 0) as sale_clue_person_num,
        COALESCE(sum(valid_clue_num), 0) as valid_clue_num,
        COALESCE(sum(valid_clue_person_num), 0) as valid_clue_person_num,
        COALESCE(sum(official_focus_count), 0) as official_focus_count,
        COALESCE(sum(valid_clue_count), 0) as valid_clue_count,
        COALESCE(sum(call_link_count), 0) as call_link_count,
        COALESCE(sum(person_wechat_link_count), 0) as person_wechat_link_count,
        COALESCE(sum(appointment_count), 0) as appointment_count,
        COALESCE(sum(audition_count), 0) as audition_count,
        COALESCE(sum(auditioned_class_count), 0) as auditioned_class_count,
        COALESCE(sum(trial_count), 0) as trial_count,
        COALESCE(sum(payment_deposit_count), 0) as payment_deposit_count,
        COALESCE(sum(pay_count), 0) as pay_count,
        COALESCE(sum(convert_count), 0) as convert_count,
        COALESCE(sum(register_count), 0) as register_count,
        COALESCE(sum(activation_count), 0) as activation_count,
        COALESCE(sum(app_download_finish_count), 0) as app_download_finish_count,
        COALESCE(sum(app_install_count), 0) as app_install_count,
        COALESCE(sum(app_activation_count), 0) as app_activation_num,
        COALESCE(sum(app_register_count), 0) as app_register_num,
        COALESCE(sum(app_retained_person_num), 0) as app_retained_person_num,
        COALESCE(sum(app_pay_num), 0) as app_pay_num,
        COALESCE(sum(app_pay_amount), 0) as app_pay_amount,
        COALESCE(sum(place_order_num), 0) as place_order_num,
        COALESCE(sum(button_count), 0) as buttonCount,
        COALESCE(sum(follow_num), 0) as follow_num,
        case when COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(view_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(click_num), 0) / COALESCE(sum(view_num), 0) *100 end as click_rate,
        case when COALESCE(sum(fill_count_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(fill_count_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as fill_count_rate,
        case when COALESCE(sum(order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(order_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as order_count_rate,
        case when COALESCE(sum(fill_count_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(fill_count_num), 0) / 100 end as fill_count_cost,
        case when COALESCE(sum(order_finish_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(order_finish_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as order_finish_rate,
        case when COALESCE(sum(order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(order_num), 0) / 100 end as order_count_cost,
        case when COALESCE(sum(order_finish_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(order_finish_num), 0) / 100 end as order_finish_cost,
        case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(place_order_num), 0) / COALESCE(sum(form_appointment_num), 0) *100 end as form_order_convert_rate,
        case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(form_appointment_num), 0) / COALESCE(sum(click_num), 0) *100 end as form_appointment_rate,
        case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(button_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(form_appointment_num), 0) / COALESCE(sum(button_count), 0) *100 end as button_form_convert,
        case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(form_appointment_num), 0) / 100 end as form_appointment_cost,
        case when COALESCE(sum(sale_clue_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(sale_clue_num), 0) / COALESCE(sum(click_num), 0) *100 end as sale_clue_convert_rate,
        case when COALESCE(sum(valid_clue_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(valid_clue_num), 0) / COALESCE(sum(click_num), 0) *100 end as valid_clue_convert_rate,
        case when COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_download_finish_count), 0) / COALESCE(sum(click_num), 0) *100 end as app_download_rate,
        case when COALESCE(sum(app_install_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_install_count), 0) / COALESCE(sum(click_num), 0) *100 end as app_install_rate,
        case when COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_activation_count), 0) / COALESCE(sum(click_num), 0) *100 end as app_click_activation_rate,
        case when COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_activation_count), 0) / COALESCE(sum(app_download_finish_count), 0) *100 end as app_download_activation_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(app_activation_count), 0) / 100 end as app_download_activation_cost,
        case when COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_register_count), 0) / COALESCE(sum(click_num), 0) *100 end as app_register_rate,
        case when COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_register_count), 0) / COALESCE(sum(app_activation_count), 0) *100 end as app_activation_register_rate,
        case when COALESCE(sum(app_retained_person_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(app_retained_person_num), 0) / COALESCE(sum(app_activation_count), 0) *100 end as app_retained_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(payment_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(payment_num), 0) / 100 end as payment_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(follow_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(follow_num), 0) / 100 end as focus_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(official_focus_num), 0) / 100 end as official_focus_cost1,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(sale_clue_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(sale_clue_num), 0) / 100 end as sale_clue_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(valid_clue_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(valid_clue_num), 0) / 100 end as valid_clue_cost1,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(official_focus_count), 0) / 100 end as official_focus_cost2,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(valid_clue_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(valid_clue_count), 0) / 100 end as valid_clue_cost2,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(call_link_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(call_link_count), 0) / 100 end as call_link_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(person_wechat_link_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(person_wechat_link_count), 0) / 100 end as person_wechat_link_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(appointment_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(appointment_count), 0) / 100 end as appointment_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(audition_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(audition_count), 0) / 100 end as try_listen_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(auditioned_class_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(auditioned_class_count), 0) / 100 end as auditioned_class_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(trial_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(trial_count), 0) / 100 end as trial_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(payment_deposit_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(payment_deposit_count), 0) / 100 end as payment_deposit_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(pay_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(pay_count), 0) / 100 end as pay_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(convert_count), 0) / 100 end as convert_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(register_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(register_count), 0) / 100 end as register_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(activation_count), 0) / 100 end as activation_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(app_download_finish_count), 0) / 100 end as app_download_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_install_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(app_install_count), 0) / 100 end as app_install_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(app_register_count), 0) / 100 end as app_register_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_retained_person_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(app_retained_person_num), 0) / 100 end as app_retained_cost,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_pay_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(app_pay_num), 0) / 100 end as app_pay_cost,
        COALESCE(sum(identify_qr_code_num), 0) as identify_qr_code_num,
        case when COALESCE(sum(identify_qr_code_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(identify_qr_code_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as identify_qr_code_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(identify_qr_code_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(identify_qr_code_num), 0) / 100 end as identify_qr_code_cost,
        COALESCE(sum(add_work_wechat_num), 0) as add_work_wechat_num,
        case when COALESCE(sum(add_work_wechat_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(add_work_wechat_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as add_work_wechat_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(add_work_wechat_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(add_work_wechat_num), 0) / 100 end as add_work_wechat_cost,
        COALESCE(sum(convert_num), 0) as convert_num,
        case when COALESCE(sum(convert_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(convert_num), 0) / COALESCE(sum(click_num), 0) * 100 end as target_convert_rate,
        case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(convert_num), 0) / 100 end as target_convert_cost,
        COALESCE(sum(deep_convert_num), 0) as deep_convert_num,
        case when COALESCE(sum(deep_convert_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(deep_convert_num), 0) / COALESCE(sum(click_num), 0) * 100 end as deep_convert_rate,
        case when COALESCE(sum(deep_convert_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(deep_convert_num), 0) / 100 end as deep_convert_cost,
        COALESCE(sum(place_order_num), 0) as place_order_num,
        case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(place_order_num), 0) / COALESCE(sum(click_num), 0) * 100 end as place_order_rate,
        case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(place_order_num), 0) / 100 end as place_order_cost,
        COALESCE(sum(clue_fill_num), 0) as clue_fill_num,
        case when COALESCE(sum(clue_fill_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(clue_fill_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as clue_fill_rate,
        case when COALESCE(sum(clue_fill_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(clue_fill_num), 0) / 100 end as clue_fill_cost,
        COALESCE(sum(clue_connect_num), 0) as clue_connect_num,
        case when COALESCE(sum(clue_connect_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(clue_fill_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(clue_connect_num), 0) / COALESCE(sum(clue_fill_num), 0) *100 end as clue_connect_rate,
        case when COALESCE(sum(clue_connect_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(clue_connect_num), 0) / 100 end as clue_connect_cost,
        COALESCE(sum(clue_effective_num), 0) as clue_effective_num,
        case when COALESCE(sum(clue_connect_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(clue_effective_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(clue_effective_num), 0) / COALESCE(sum(clue_connect_num), 0) *100 end as clue_effective_rate,
        case when COALESCE(sum(clue_effective_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(clue_effective_num), 0) / 100 end as clue_effective_cost,
        COALESCE(sum(sign_class_num), 0) as sign_class_num,
        case when COALESCE(sum(sign_class_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(clue_effective_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(sign_class_num), 0) / COALESCE(sum(clue_effective_num), 0) *100 end as sign_class_rate,
        case when COALESCE(sum(sign_class_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(cost), 0) / COALESCE(sum(sign_class_num), 0) / 100 end as sign_class_cost,
        case when COALESCE(sum(sign_class_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
        COALESCE(sum(sign_class_num), 0) / COALESCE(sum(landing_page_pv), 0) *100 end as total_convert_rate
    </sql>

    <select id="listByConditions" resultType="ai.yiye.agent.domain.vo.AdvertiserAccountGroupDataReport">
        with allCountDayData as (
        select
        <include refid="base_column"/>
        day_time
        from marketing_data_solidification_ad_report_day where account_id in
        <foreach collection="accountIds" open="(" close=")" separator="," item="accountId">
            #{accountId}
        </foreach>
        and
        day_time between to_timestamp(#{startDay}, 'yyyy-MM-dd') and to_timestamp(#{endDay}, 'yyyy-MM-dd')
        group by day_time order by day_time
        ),
        groupTarget as (
        select * from marketing_advertiser_account_group_target_daily_report
        where advertiser_account_group_id = #{groupId} and day_at between to_timestamp(#{startDay}, 'yyyy-MM-dd') and to_timestamp(#{endDay}, 'yyyy-MM-dd') order by day_at
        )
        select
        <include refid="result_column"/>
        d.day_time, g.*
        from allCountDayData d
        FULL OUTER JOIN groupTarget g on d.day_time = g.day_at
    </select>

    <select id="getByConditions" resultType="ai.yiye.agent.domain.vo.AdvertiserAccountGroupReportVo">
        select
        <include refid="all_column"/>
        from marketing_data_solidification_ad_report_day where account_id in
        <foreach collection="accountIds" open="(" close=")" separator="," item="accountId">
            #{accountId}
        </foreach>
        and
        day_time between to_timestamp(#{startDay}, 'yyyy-MM-dd') and to_timestamp(#{endDay}, 'yyyy-MM-dd')
    </select>
    <!-- 素材分析详情-图片视频投放数据自定义列 -->
    <select id="selectCreativeReportCreative" resultType="ai.yiye.agent.marketing.vo.DataSolidificationVO">
        select
        max(mdm.signature) as signature,
        max(act.account_id) as account_id,
        max(act.account_name) as account_name,
        max(mdmcr.creative_id) as creativeId,
        max(c.creative_name) as creativeName,
        max(act.platform_id) as platform_id,
        max(act.platform_name) as platformName,
        <include refid="commonSolidification">
        </include>
        ad.ad_id
        from marketing_data_material mdm
        join marketing_data_material_creative_rel mdmcr
        ON mdm.image_id = mdmcr.image_id and mdm.material_id = mdmcr.material_id and
        mdm.platform_id = mdmcr.platform_id
        JOIN marketing_data_creative c ON mdmcr.creative_id = c.creative_id AND
        c.advertiser_account_id = mdm.advertiser_account_id and
        c.platform_id = mdm.platform_id
        JOIN marketing_data_advertise ad
        ON mdmcr.creative_id = ad.adcreative_id and mdmcr.platform_id = ad.platform_id and ad.account_id = c.account_id
        JOIN marketing_advertiser_account act
        ON act.ID = c.advertiser_account_id and act.platform_id = c.platform_id
        <if test="creativeForm.signature != null">
            and mdm.signature = #{creativeForm.signature}
        </if>
        <if test="creativeForm.accountType.name == 'EMP'">
            and act.id = #{creativeForm.systemAccountId}
        </if>
        <if test="creativeForm.accountType.name == 'PMP'">
            and act.id in (select advertiser_account_id from marketing_advertiser_account_group_rel where advertiser_account_group_id = #{creativeForm.systemAccountId})
        </if>
        join marketing_data_solidification mds
        on mdm.account_id = mds.account_id and mdm.platform_id = mds.platform_id and
        mds.creative_id = mdmcr.creative_id and mdm.advertiser_account_id = mds.advertiser_account_id
        <where>
            <if test="creativeForm.signature != null">
                and mdm.signature = #{creativeForm.signature}
            </if>
            <if test="creativeForm.startTime != null and creativeForm.startTime != '' and creativeForm.endTime != null and creativeForm.endTime != ''">
                and mds.day_time between to_timestamp(#{creativeForm.startTime}, 'yyyy-MM-dd') and to_timestamp(#{creativeForm.endTime}, 'yyyy-MM-dd')
            </if>
        </where>
        group by ad.ad_id
    </select>

    <!-- 素材分析详情-文案投放数据自定义列 -->
    <select id="selectCreativeReportByText" resultType="ai.yiye.agent.marketing.vo.DataSolidificationVO">
        select
        max(mdmtcr.copy_md5)                                                         AS copyMd5,
        max(c.creative_id)                                                     AS creativeId,
        max(act.account_id) as account_id,
        max(act.account_name) as account_name,
        max(c.creative_name) as creativeName,
        max(act.platform_id) as platform_id,
        max(act.platform_name) as platformName,
        <include refid="commonSolidification">
        </include>
        ad.ad_id
        from marketing_data_creative c
        join marketing_data_material_text_creative_rel mdmtcr
        on c.creative_id = mdmtcr.creative_id and c.platform_id = mdmtcr.platform_id
        join marketing_data_advertise ad
        on c.creative_id = ad.adcreative_id and c.platform_id = ad.platform_id and c.account_id = ad.account_id
        JOIN marketing_advertiser_account act on act.id = c.advertiser_account_id and act.platform_id = c.platform_id
        <if test="creativeForm.copyMd5 != null">
            and mdmtcr.copy_md5 = #{creativeForm.copyMd5}
        </if>
        <if test="creativeForm.accountType.name == 'EMP'">
            and act.id = #{creativeForm.systemAccountId}
        </if>
        <if test="creativeForm.accountType.name == 'PMP'">
            and act.id in (select advertiser_account_id from marketing_advertiser_account_group_rel where advertiser_account_group_id = #{creativeForm.systemAccountId})
        </if>
        JOIN marketing_data_solidification mds
        ON c.account_id = mds.account_id AND c.platform_id = mds.platform_id AND
        c.creative_id = mds.creative_id AND
        c.advertiser_account_id = mds.advertiser_account_id
        <where>
            <if test="creativeForm.copyMd5 != null">
                and mdmtcr.copy_md5 = #{creativeForm.copyMd5}
            </if>
            <if test="creativeForm.startTime != null and creativeForm.startTime != '' and creativeForm.endTime != null and creativeForm.endTime != ''">
                and mds.day_time between to_timestamp(#{creativeForm.startTime}, 'yyyy-MM-dd') and to_timestamp(#{creativeForm.endTime}, 'yyyy-MM-dd')
            </if>
        </where>
        group by ad.ad_id
    </select>

    <!-- 素材分析-图片视频-数据概况 -->
    <select id="getMaterialBySignature" resultType="ai.yiye.agent.marketing.vo.DataSolidificationVO">
        select
        count(distinct c.creative_id) as useCount,
        COALESCE(max(mdm.brightness_std),0) as brightnessStdNum,
        COALESCE(max(mdm.brightness),0) as brightnessNum,
        COALESCE(max(mdm.saturation),0) as saturationNum,
        COALESCE(max(mdm.lightness),0) as lightnessNum,
        <include refid="commonDataOverview">
        </include>
        from marketing_data_material mdm
        join marketing_data_material_creative_rel mdmcr
        ON mdm.image_id = mdmcr.image_id and mdm.material_id = mdmcr.material_id and
        mdm.platform_id = mdmcr.platform_id
        JOIN marketing_data_creative c ON mdmcr.creative_id = c.creative_id AND
        c.advertiser_account_id = mdm.advertiser_account_id and
        c.platform_id = mdm.platform_id
        JOIN marketing_data_advertise ad
        ON mdmcr.creative_id = ad.adcreative_id and mdmcr.platform_id = ad.platform_id and ad.account_id = c.account_id
        JOIN marketing_advertiser_account act
        ON act.ID = c.advertiser_account_id and act.platform_id = c.platform_id
        <if test="creativeForm.signature != null">
            and mdm.signature = #{creativeForm.signature}
        </if>
        <if test="creativeForm.accountType.name == 'EMP'">
            and act.id = #{creativeForm.systemAccountId}
        </if>
        <if test="creativeForm.accountType.name == 'PMP'">
            and act.id in (select advertiser_account_id from marketing_advertiser_account_group_rel where advertiser_account_group_id = #{creativeForm.systemAccountId})
        </if>
        join marketing_data_solidification mds
        on mdm.account_id = mds.account_id and mdm.platform_id = mds.platform_id and
        mdmcr.creative_id = mds.creative_id and mdm.advertiser_account_id = mds.advertiser_account_id
        <where>
            <if test="creativeForm.signature != null">
                and mdm.signature = #{creativeForm.signature}
            </if>
            <if test="creativeForm.startTime != null and creativeForm.startTime != '' and creativeForm.endTime != null and creativeForm.endTime != ''">
                and mds.day_time between to_timestamp(#{creativeForm.startTime}, 'yyyy-MM-dd') and to_timestamp(#{creativeForm.endTime}, 'yyyy-MM-dd')
            </if>
        </where>
    </select>
    <!-- 素材分析-文案-数据概况 -->
    <select id="getMaterialTextBySignature" resultType="ai.yiye.agent.marketing.vo.DataSolidificationVO">
        select
        count(distinct c.creative_id) as useCount,
        max(c.creative_id)                                                     AS creativeId,
        <include refid="commonDataOverview">
        </include>
        from marketing_data_creative c
        join marketing_data_material_text_creative_rel mdmtcr
        on c.creative_id = mdmtcr.creative_id and c.platform_id = mdmtcr.platform_id
        join marketing_data_advertise ad
        on c.creative_id = ad.adcreative_id and c.platform_id = ad.platform_id and c.account_id = ad.account_id
        JOIN marketing_advertiser_account act on act.id = c.advertiser_account_id and act.platform_id = c.platform_id
        <if test="creativeForm.copyMd5 != null">
            and mdmtcr.copy_md5 = #{creativeForm.copyMd5}
        </if>
        <if test="creativeForm.accountType.name == 'EMP'">
            and act.id = #{creativeForm.systemAccountId}
        </if>
        <if test="creativeForm.accountType.name == 'PMP'">
            and act.id in (select advertiser_account_id from marketing_advertiser_account_group_rel where advertiser_account_group_id = #{creativeForm.systemAccountId})
        </if>
        JOIN marketing_data_solidification_day mds
        ON c.account_id = mds.account_id AND c.platform_id = mds.platform_id AND
        c.creative_id = mds.creative_id AND
        c.advertiser_account_id = mds.advertiser_account_id
        <where>
            <if test="creativeForm.copyMd5 != null">
                and mdmtcr.copy_md5 = #{creativeForm.copyMd5}
            </if>
            <if test="creativeForm.startTime != null and creativeForm.startTime != '' and creativeForm.endTime != null and creativeForm.endTime != ''">
                and mds.day_time between to_timestamp(#{creativeForm.startTime}, 'yyyy-MM-dd') and to_timestamp(#{creativeForm.endTime}, 'yyyy-MM-dd')
            </if>
        </where>
    </select>

    <!-- 素材分析-数据概况105项指标 -->
    <sql id="commonDataOverview">
        COALESCE(sum(view_num),0) as view_num,
           COALESCE(sum(click_num),0) as click_num,
            COALESCE(sum(cost)/100,0) as cost,
            case when COALESCE(sum(view_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(view_num), 0) * 10 end as thousand_impress_avg_price,
            case when COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(click_num), 0) end as avg_price,
            COALESCE(sum(landing_page_pv),0) as landing_page_pv,
            COALESCE(sum(landing_page_uv),0) as landing_page_uv,
            COALESCE(sum(fill_count_num),0) as fill_count_num,
            COALESCE(sum(order_num),0) as order_num,
            COALESCE(sum(order_finish_num),0) as order_finish_num,
            case when COALESCE(sum(view_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(length_of_stay), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(length_of_stay), 0) / COALESCE(sum(view_num), 0) end as landing_avg_stay,
            COALESCE(sum(form_appointment_num),0) as form_appointment_num,
            COALESCE(sum(form_appointment_person_count),0) as formAppointmentPersonCount,
            COALESCE(sum(order_amount),0)/100 as order_amount,
            case when COALESCE(sum(order_amount), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(order_amount), 0) / COALESCE(sum(place_order_num), 0) end as order_unit_price,
            case when COALESCE(sum(order_amount), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            (COALESCE(sum(order_amount), 0)/100) / (COALESCE(sum(cost), 0)/100) end as order_roi,
            COALESCE(sum(payment_num),0) as payment_num,
            COALESCE(sum(payment_amount), 0) / 100 as payment_amount,
            COALESCE(sum(first_payment_person_num),0) as first_payment_person_num,
            COALESCE(sum(official_focus_num),0) as official_focus_num,
            COALESCE(sum(sale_clue_num),0) as sale_clue_num,
            COALESCE(sum(sale_clue_person_num),0) as sale_clue_person_num,
            COALESCE(sum(valid_clue_num),0) as valid_clue_num,
            COALESCE(sum(valid_clue_person_num),0) as valid_clue_person_num,
            COALESCE(sum(official_focus_count),0) as official_focus_count,
            COALESCE(sum(valid_clue_count),0) as valid_clue_count,
            COALESCE(sum(call_link_count),0) as call_link_count,
            COALESCE(sum(person_wechat_link_count),0) as person_wechat_link_count,
            COALESCE(sum(appointment_count),0) as appointment_count,
            COALESCE(sum(audition_count),0) as audition_count,
            COALESCE(sum(auditioned_class_count),0) as auditioned_class_count,
            COALESCE(sum(trial_count),0) as trial_count,
            COALESCE(sum(payment_deposit_count),0) as payment_deposit_count,
            COALESCE(sum(pay_count),0) as pay_count,
            COALESCE(sum(convert_count),0) as convert_count,
            COALESCE(sum(register_count),0) as register_count,
            COALESCE(sum(activation_count),0) as activation_count,
            COALESCE(sum(app_download_finish_count),0) as app_download_finish_count,
            COALESCE(sum(app_install_count),0) as app_install_count,
            COALESCE(sum(app_activation_count),0) as appActivationNum,
            COALESCE(sum(app_register_count),0) as appRegisterNum,
            COALESCE(sum(app_retained_person_num),0) as app_retained_person_num,
            COALESCE(sum(app_pay_num),0) as app_pay_num,
            COALESCE(sum(app_pay_amount),0) as app_pay_amount,
            COALESCE(sum(share_num),0) as shareCount,
            COALESCE(sum(comment_num),0) as discussCount,
            COALESCE(sum(praise_num),0) as thumbUpCount,
            COALESCE(sum(button_count),0) as button_count,
            COALESCE(sum(video_play25_num),0) as video_play25_num,
            COALESCE(sum(video_play50_num),0) as video_play50_num,
            COALESCE(sum(video_play75_num),0) as video_play75_num,
            COALESCE(sum(video_play100_num),0) as video_play100_num,
            COALESCE(sum(play_num),0) as play_num,
            COALESCE(sum(average_play_time),0) as avgPlayTime,
            COALESCE(sum(valid_play_num),0) as valid_play_num,
            COALESCE(sum(follow_num), 0) as follow_num,
            case when COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(view_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(click_num), 0)*100 / COALESCE(sum(view_num), 0) end as click_rate,
            case when COALESCE(sum(fill_count_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(fill_count_num), 0)*100 / COALESCE(sum(landing_page_pv), 0) end as fill_count_rate,
            case when COALESCE(sum(order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(order_num), 0)*100 / COALESCE(sum(landing_page_pv), 0) end as order_count_rate,
            case when COALESCE(sum(fill_count_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(fill_count_num), 0) end as fill_count_cost,
            case when COALESCE(sum(order_finish_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(order_finish_num), 0)*100 / COALESCE(sum(landing_page_pv), 0) end as order_finish_rate,
            case when COALESCE(sum(order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(order_num), 0) end as order_count_cost,
            case when COALESCE(sum(order_finish_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(order_finish_num), 0) end as order_finish_cost,
            case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(place_order_num), 0) / COALESCE(sum(form_appointment_num), 0) end as form_order_convert_rate,
            case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(form_appointment_num), 0)*100 / COALESCE(sum(click_num), 0) end as form_appointment_rate,
            case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(button_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(form_appointment_num), 0) / COALESCE(sum(button_count), 0) end as button_form_convert,
            case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(form_appointment_num), 0) end as form_appointment_cost,
            case when COALESCE(sum(sale_clue_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(sale_clue_num), 0)*100 / COALESCE(sum(click_num), 0) end as sale_clue_convert_rate,
            case when COALESCE(sum(valid_clue_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(valid_clue_num), 0)*100 / COALESCE(sum(click_num), 0) end as valid_clue_convert_rate,
            case when COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_download_finish_count), 0)*100 / COALESCE(sum(click_num), 0) end as app_download_rate,
            case when COALESCE(sum(app_install_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_install_count), 0)*100 / COALESCE(sum(click_num), 0) end as app_install_rate,
            case when COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_activation_count), 0)*100 / COALESCE(sum(click_num), 0) end as app_click_activation_rate,
            case when COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_activation_count), 0)*100 / COALESCE(sum(app_download_finish_count), 0) end as app_download_activation_rate,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(app_activation_count), 0) end as app_download_activation_cost,
            case when COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_register_count), 0)*100 / COALESCE(sum(click_num), 0) end as app_register_rate,
            case when COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_register_count), 0)*100 / COALESCE(sum(app_activation_count), 0) end as app_activation_register_rate,
            case when COALESCE(sum(app_retained_person_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_retained_person_num), 0)*100 / COALESCE(sum(app_activation_count), 0) end as app_retained_rate,
            case COALESCE(sum(view_num),0)
            when 0 then 0
            else COALESCE(sum(valid_play_num),0) * 100 / COALESCE(sum(view_num),0)
            end AS validPlayRate,
            case COALESCE(sum(valid_play_num),0)
            when 0 then 0
            else sum(cost)/100 / COALESCE(sum(valid_play_num),0)
            end AS validPlayCost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(payment_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(payment_num), 0) end as payment_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(follow_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(follow_num), 0) end as focus_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(official_focus_num), 0) end as official_focus_cost1,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(sale_clue_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(sale_clue_num), 0) end as sale_clue_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(valid_clue_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(valid_clue_num), 0) end as valid_clue_cost1,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(official_focus_count), 0) end as officialFocusCost2,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(valid_clue_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(valid_clue_count), 0) end as valid_clue_cost2,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(call_link_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(call_link_count), 0) end as call_link_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(person_wechat_link_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(person_wechat_link_count), 0) end as person_wechat_link_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(appointment_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(appointment_count), 0) end as appointment_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(audition_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(audition_count), 0) end as try_listen_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(auditioned_class_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(auditioned_class_count), 0) end as auditioned_class_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(trial_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(trial_count), 0) end as trial_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(payment_deposit_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(payment_deposit_count), 0) end as payment_deposit_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(pay_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(pay_count), 0) end as pay_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(convert_count), 0) end as convert_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(register_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(register_count), 0) end as register_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(activation_count), 0) end as activation_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(app_download_finish_count), 0) end as app_download_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_install_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(app_install_count), 0) end as app_install_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(app_register_count), 0) end as app_register_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_retained_person_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(app_retained_person_num), 0) end as app_retained_cost,
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_pay_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(app_pay_num), 0) end as app_pay_cost,
            case sum(share_num)
            when 0 then 0
            else coalesce(round(sum(cost / 100) / sum(share_num), 2), 0)
            end AS shareCost,
            case sum(comment_num)
            when 0 then 0
            else sum(cost / 100) / sum(comment_num)
            end AS discussCost,
            case sum(praise_num)
            when 0 then 0
            else sum(cost / 100) / sum(praise_num)
            end AS thumbUpCost
    </sql>

    <!-- 素材分析-自定义列查询 -->
    <sql id="commonSolidification">
        <if test="map.containsKey('viewNum')">
            COALESCE(sum(view_num),0) as view_num,
        </if>
        <if test="map.containsKey('clickNum')">
            COALESCE(sum(click_num),0) as click_num,
        </if>
        <if test="map.containsKey('cost')">
            COALESCE(sum(cost)/100,0) as cost,
        </if>
        <if test="map.containsKey('thousandImpressAvgPrice')">
            case when COALESCE(sum(view_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0) / COALESCE(sum(view_num), 0) * 10 end as thousand_impress_avg_price,
        </if>
        <if test="map.containsKey('avgPrice')">
            case when COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(click_num), 0) end as avg_price,
        </if>
        <if test="map.containsKey('landingPagePv')">
            COALESCE(sum(landing_page_pv),0) as landing_page_pv,
        </if>
        <if test="map.containsKey('landingPageUv')">
            COALESCE(sum(landing_page_uv),0) as landing_page_uv,
        </if>
        <if test="map.containsKey('fillCountNum')">
            COALESCE(sum(fill_count_num),0) as fill_count_num,
        </if>
        <if test="map.containsKey('orderNum')">
            COALESCE(sum(order_num),0) as order_num,
        </if>
        <if test="map.containsKey('orderFinishNum')">
            COALESCE(sum(order_finish_num),0) as order_finish_num,
        </if>
        <if test="map.containsKey('landingAvgStay')">
            case when COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(length_of_stay), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(length_of_stay), 0) / COALESCE(sum(landing_page_pv), 0) end as landing_avg_stay,
        </if>
        <if test="map.containsKey('formAppointmentNum')">
            COALESCE(sum(form_appointment_num),0) as form_appointment_num,
        </if>
        <if test="map.containsKey('formAppointmentPersonCount')">
            COALESCE(sum(form_appointment_person_count),0) as formAppointmentPersonCount,
        </if>
        <if test="map.containsKey('orderAmount')">
            COALESCE(sum(order_amount),0)/100 as order_amount,
        </if>
        <if test="map.containsKey('orderUnitPrice')">
            case when COALESCE(sum(order_amount), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(order_amount), 0) / COALESCE(sum(place_order_num), 0) end as order_unit_price,
        </if>
        <if test="map.containsKey('orderROI')">
            case when COALESCE(sum(order_amount), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            (COALESCE(sum(order_amount), 0)/100) / (COALESCE(sum(cost), 0)/100) end as order_roi,
        </if>
        <if test="map.containsKey('paymentNum')">
            COALESCE(sum(payment_num),0) as payment_num,
        </if>
        <if test="map.containsKey('paymentAmount')">
            COALESCE(sum(payment_amount), 0) / 100 as payment_amount,
        </if>
        <if test="map.containsKey('firstPaymentPersonNum')">
            COALESCE(sum(first_payment_person_num),0) as first_payment_person_num,
        </if>
        <if test="map.containsKey('officialFocusNum')">
            COALESCE(sum(official_focus_num),0) as official_focus_num,
        </if>
        <if test="map.containsKey('saleClueNum')">
            COALESCE(sum(sale_clue_num),0) as sale_clue_num,
        </if>
        <if test="map.containsKey('saleCluePersonNum')">
            COALESCE(sum(sale_clue_person_num),0) as sale_clue_person_num,
        </if>
        <if test="map.containsKey('validClueNum')">
            COALESCE(sum(valid_clue_num),0) as valid_clue_num,
        </if>
        <if test="map.containsKey('validCluePersonNum')">
            COALESCE(sum(valid_clue_person_num),0) as valid_clue_person_num,
        </if>
        <if test="map.containsKey('officialFocusCount')">
            COALESCE(sum(official_focus_count),0) as official_focus_count,
        </if>
        <if test="map.containsKey('validClueCount')">
            COALESCE(sum(valid_clue_count),0) as valid_clue_count,
        </if>
        <if test="map.containsKey('callLinkCount')">
            COALESCE(sum(call_link_count),0) as call_link_count,
        </if>
        <if test="map.containsKey('personWechatLinkCount')">
            COALESCE(sum(person_wechat_link_count),0) as person_wechat_link_count,
        </if>
        <if test="map.containsKey('appointmentCount')">
            COALESCE(sum(appointment_count),0) as appointment_count,
        </if>
        <if test="map.containsKey('auditionCount')">
            COALESCE(sum(audition_count),0) as audition_count,
        </if>
        <if test="map.containsKey('auditionedClassCount')">
            COALESCE(sum(auditioned_class_count),0) as auditioned_class_count,
        </if>
        <if test="map.containsKey('trialCount')">
            COALESCE(sum(trial_count),0) as trial_count,
        </if>
        <if test="map.containsKey('paymentDepositCount')">
            COALESCE(sum(payment_deposit_count),0) as payment_deposit_count,
        </if>
        <if test="map.containsKey('payCount')">
            COALESCE(sum(pay_count),0) as pay_count,
        </if>
        <if test="map.containsKey('convertCount')">
            COALESCE(sum(convert_count),0) as convert_count,
        </if>
        <if test="map.containsKey('registerCount')">
            COALESCE(sum(register_count),0) as register_count,
        </if>
        <if test="map.containsKey('activationCount')">
            COALESCE(sum(activation_count),0) as activation_count,
        </if>
        <if test="map.containsKey('appDownloadFinishCount')">
            COALESCE(sum(app_download_finish_count),0) as app_download_finish_count,
        </if>
        <if test="map.containsKey('appInstallCount')">
            COALESCE(sum(app_install_count),0) as app_install_count,
        </if>
        <if test="map.containsKey('appActivationNum')">
            COALESCE(sum(app_activation_count),0) as appActivationNum,
        </if>
        <if test="map.containsKey('appRegisterNum')">
            COALESCE(sum(app_register_count),0) as appRegisterNum,
        </if>
        <if test="map.containsKey('appRetainedPersonNum')">
            COALESCE(sum(app_retained_person_num),0) as app_retained_person_num,
        </if>
        <if test="map.containsKey('appPayNum')">
            COALESCE(sum(app_pay_num),0) as app_pay_num,
        </if>
        <if test="map.containsKey('appPayAmount')">
            COALESCE(sum(app_pay_amount),0) as app_pay_amount,
        </if>
        <if test="map.containsKey('shareCount')">
            COALESCE(sum(share_num),0) as shareCount,
        </if>
        <if test="map.containsKey('discussCount')">
            COALESCE(sum(comment_num),0) as discussCount,
        </if>
        <if test="map.containsKey('thumbUpCount')">
            COALESCE(sum(praise_num),0) as thumbUpCount,
        </if>
        <if test="map.containsKey('buttonCount')">
            COALESCE(sum(button_count),0) as buttonCount,
        </if>
        <if test="map.containsKey('videoPlay25Num')">
            COALESCE(sum(video_play25_num),0) as video_play25_num,
        </if>
        <if test="map.containsKey('videoPlay50Num')">
            COALESCE(sum(video_play50_num),0) as video_play50_num,
        </if>
        <if test="map.containsKey('videoPlay75Num')">
            COALESCE(sum(video_play75_num),0) as video_play75_num,
        </if>
        <if test="map.containsKey('videoPlay100Num')">
            COALESCE(sum(video_play100_num),0) as video_play100_num,
        </if>
        <if test="map.containsKey('playNum')">
            COALESCE(sum(play_num),0) as play_num,
        </if>
        <if test="map.containsKey('avgPlayTime')">
            COALESCE(sum(average_play_time),0) as avgPlayTime,
        </if>
        <if test="map.containsKey('validPlayNum')">
            COALESCE(sum(valid_play_num),0) as valid_play_num,
        </if>
        <if test="map.containsKey('followNum')">
            COALESCE(sum(follow_num), 0) as follow_num,
        </if>
        <if test="map.containsKey('clickRate')">
            case when COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(view_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(click_num), 0)*100 / COALESCE(sum(view_num), 0) end as click_rate,
        </if>
        <if test="map.containsKey('fillCountRate')">
            case when COALESCE(sum(fill_count_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(fill_count_num), 0)*100 / COALESCE(sum(landing_page_pv), 0) end as fill_count_rate,
        </if>
        <if test="map.containsKey('orderCountRate')">
            case when COALESCE(sum(order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(order_num), 0)*100 / COALESCE(sum(landing_page_pv), 0) end as order_count_rate,
        </if>
        <if test="map.containsKey('fillCountCost')">
            case when COALESCE(sum(fill_count_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(fill_count_num), 0) end as fill_count_cost,
        </if>
        <if test="map.containsKey('orderFinishRate')">
            case when COALESCE(sum(order_finish_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(landing_page_pv), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(order_finish_num), 0)*100 / COALESCE(sum(landing_page_pv), 0) end as order_finish_rate,
        </if>
        <if test="map.containsKey('orderCountCost')">
            case when COALESCE(sum(order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(order_num), 0) end as order_count_cost,
        </if>
        <if test="map.containsKey('orderFinishCost')">
            case when COALESCE(sum(order_finish_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(order_finish_num), 0) end as order_finish_cost,
        </if>
        <if test="map.containsKey('formOrderConvertRate')">
            case when COALESCE(sum(place_order_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(place_order_num), 0) / COALESCE(sum(form_appointment_num), 0) end as form_order_convert_rate,
        </if>
        <if test="map.containsKey('formAppointmentRate')">
            case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(form_appointment_num), 0)*100 / COALESCE(sum(click_num), 0) end as form_appointment_rate,
        </if>
        <if test="map.containsKey('buttonFormConvert')">
            case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(button_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(form_appointment_num), 0) / COALESCE(sum(button_count), 0) end as button_form_convert,
        </if>
        <if test="map.containsKey('formAppointmentCost')">
            case when COALESCE(sum(form_appointment_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(form_appointment_num), 0) end as form_appointment_cost,
        </if>
        <if test="map.containsKey('saleClueConvertRate')">
            case when COALESCE(sum(sale_clue_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(sale_clue_num), 0)*100 / COALESCE(sum(click_num), 0) end as sale_clue_convert_rate,
        </if>
        <if test="map.containsKey('validClueConvertRate')">
            case when COALESCE(sum(valid_clue_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(valid_clue_num), 0)*100 / COALESCE(sum(click_num), 0) end as valid_clue_convert_rate,
        </if>
        <if test="map.containsKey('appDownloadRate')">
            case when COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_download_finish_count), 0)*100 / COALESCE(sum(click_num), 0) end as app_download_rate,
        </if>
        <if test="map.containsKey('appInstallRate')">
            case when COALESCE(sum(app_install_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_install_count), 0)*100 / COALESCE(sum(click_num), 0) end as app_install_rate,
        </if>
        <if test="map.containsKey('appClickActivationRate')">
            case when COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_activation_count), 0)*100 / COALESCE(sum(click_num), 0) end as app_click_activation_rate,
        </if>
        <if test="map.containsKey('appDownloadActivationRate')">
            case when COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_activation_count), 0)*100 / COALESCE(sum(app_download_finish_count), 0) end as app_download_activation_rate,
        </if>
        <if test="map.containsKey('appDownloadActivationCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(app_activation_count), 0) end as app_download_activation_cost,
        </if>
        <if test="map.containsKey('appRegisterRate')">
            case when COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(click_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_register_count), 0)*100 / COALESCE(sum(click_num), 0) end as app_register_rate,
        </if>
        <if test="map.containsKey('appActivationRegisterRate')">
            case when COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_register_count), 0)*100 / COALESCE(sum(app_activation_count), 0) end as app_activation_register_rate,
        </if>
        <if test="map.containsKey('appRetainedRate')">
            case when COALESCE(sum(app_retained_person_num), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(app_retained_person_num), 0)*100 / COALESCE(sum(app_activation_count), 0) end as app_retained_rate,
        </if>
        <if test="map.containsKey('validPlayRate')">
            case COALESCE(sum(view_num),0)
            when 0 then 0
            else COALESCE(sum(valid_play_num),0)*100 / COALESCE(sum(view_num),0)
            end AS validPlayRate,
        </if>
        <if test="map.containsKey('validPlayCost')">
            case sum(valid_play_num)
            when 0 then 0
            else sum(cost)/100 / sum(valid_play_num)
            end AS validPlayCost,
        </if>
        <if test="map.containsKey('paymentCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(payment_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(payment_num), 0) end as payment_cost,
        </if>
        <if test="map.containsKey('focusCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(follow_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(follow_num), 0) end as focus_cost,
        </if>
        <if test="map.containsKey('officialFocusCost1')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(official_focus_num), 0) end as official_focus_cost1,
        </if>
        <if test="map.containsKey('saleClueCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(sale_clue_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(sale_clue_num), 0) end as sale_clue_cost,
        </if>
        <if test="map.containsKey('validClueCost1')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(valid_clue_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(valid_clue_num), 0) end as valid_clue_cost1,
        </if>
        <if test="map.containsKey('officialFocusCost2')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(official_focus_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(official_focus_count), 0) end as officialFocusCost2,
        </if>
        <if test="map.containsKey('validClueCost2')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(valid_clue_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(valid_clue_count), 0) end as valid_clue_cost2,
        </if>
        <if test="map.containsKey('callLinkCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(call_link_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(call_link_count), 0) end as call_link_cost,
        </if>
        <if test="map.containsKey('personWechatLinkCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(person_wechat_link_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(person_wechat_link_count), 0) end as person_wechat_link_cost,
        </if>
        <if test="map.containsKey('appointmentCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(appointment_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(appointment_count), 0) end as appointment_cost,
        </if>
        <if test="map.containsKey('tryListenCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(audition_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(audition_count), 0) end as try_listen_cost,
        </if>
        <if test="map.containsKey('auditionedClassCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(auditioned_class_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(auditioned_class_count), 0) end as auditioned_class_cost,
        </if>
        <if test="map.containsKey('trialCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(trial_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(trial_count), 0) end as trial_cost,
        </if>
        <if test="map.containsKey('paymentDepositCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(payment_deposit_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(payment_deposit_count), 0) end as payment_deposit_cost,
        </if>
        <if test="map.containsKey('payCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(pay_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(pay_count), 0) end as pay_cost,
        </if>
        <if test="map.containsKey('convertCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(convert_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(convert_count), 0) end as convert_cost,
        </if>
        <if test="map.containsKey('registerCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(register_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(register_count), 0) end as register_cost,
        </if>
        <if test="map.containsKey('activationCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(activation_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(activation_count), 0) end as activation_cost,
        </if>
        <if test="map.containsKey('appDownloadCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_download_finish_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(app_download_finish_count), 0) end as app_download_cost,
        </if>
        <if test="map.containsKey('appInstallCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_install_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(app_install_count), 0) end as app_install_cost,
        </if>
        <if test="map.containsKey('appRegisterCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_register_count), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost)/100, 0) / COALESCE(sum(app_register_count), 0) end as app_register_cost,
        </if>
        <if test="map.containsKey('appRetainedCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_retained_person_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(app_retained_person_num), 0) end as app_retained_cost,
        </if>
        <if test="map.containsKey('appPayCost')">
            case when COALESCE(sum(cost), 0) = CAST (0 AS NUMERIC) or COALESCE(sum(app_pay_num), 0) = CAST (0 AS NUMERIC) THEN 0 ELSE
            COALESCE(sum(cost), 0)/100 / COALESCE(sum(app_pay_num), 0) end as app_pay_cost,
        </if>
        <if test="map.containsKey('shareCost')">
            case sum(share_num)
            when 0 then 0
            else coalesce(round(sum(cost / 100) / sum(share_num), 2), 0)
            end AS shareCost,
        </if>
        <if test="map.containsKey('discussCost')">
            case sum(comment_num)
            when 0 then 0
            else sum(cost / 100) / sum(comment_num)
            end AS discussCost,
        </if>
        <if test="map.containsKey('thumbUpCost')">
            case sum(praise_num)
            when 0 then 0
            else sum(cost / 100) / sum(praise_num)
            end AS thumbUpCost,
        </if>

    </sql>


</mapper>
