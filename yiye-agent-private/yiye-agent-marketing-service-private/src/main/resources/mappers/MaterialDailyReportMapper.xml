<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.yiye.agent.marketing.mapper.MaterialDailyReportMapper">

    <select id="getValiditySummaryReport" resultType="ai.yiye.agent.marketing.vo.validity.ValidityReport">
        with sumRpt AS (select id,
                               sum(cost / 100) over (partition by signature)   AS materialCost,
                               sum(cost / 100) over (partition by creative_id) AS creativeCost
                        from marketing_data_material_daily_report)
        select coalesce(round(sum(cost / 100 * ${type.ratio}), 2), 0)       AS cost,
               count(distinct mml.signature)                                AS materialCount,
               coalesce(round(sum(view_num * ${type.ratio}), 2), 0)         AS viewNum,
               coalesce(round(sum(click_num * ${type.ratio}), 2), 0)        AS clickNum,
               case sum(view_num)
                   when 0 then 0
                   else coalesce(round(sum(click_num) / sum(view_num), 4), 0) * 100
                   end                                                      AS clickRate,
               case sum(click_num)
                   when 0 then 0
                   else coalesce(round(sum(cost / 100) / sum(click_num), 2), 0)
                   end                                                      AS avgClickCost,
               coalesce(round(sum(convert_num * ${type.ratio}), 2), 0)      AS convertNum,
               case sum(convert_num)
                   when 0 then 0
                   else coalesce(round(sum(cost / 100) / sum(convert_num), 2), 0)
                   end                                                      AS avgConvertCost,
               coalesce(round(sum(register_num * ${type.ratio}), 2), 0)     AS registerNum,
               coalesce(round(sum(active_num * ${type.ratio}), 2), 0)       AS activeNum,
               coalesce(round(sum(pay_behavior_num * ${type.ratio}), 2), 0) AS payBehaviorNum,
               case sum(active_num)
                   when 0 then 0
                   else coalesce(round(sum(cost / 100) / sum(active_num), 2), 0)
                   end                                                      AS avgActiveCost,
               coalesce(round(sum(play_num * ${type.ratio}), 2), 0)         AS playNum,
               coalesce(round(sum(video_play100_num), 2), 0)                AS videoPlay100Num
        from marketing_material_designer_team_rel rel
                 JOIN marketing_data_material_daily_report rpt ON rpt.signature = rel.signature
                 JOIN marketing_advertiser_account acc ON rpt.advertiser_account_id = acc.id
                 JOIN sumRpt ON rpt.id = sumRpt.id
                 JOIN marketing_material_library mml on rel.signature = mml.signature
                 JOIN management_designer_team team ON rel.team_id = team.id
                 JOIN management_designer d ON rel.designer_id = d.id
                 JOIN management_role mr on rel.role_id = mr.id
                 JOIN marketing_material_account_relation relation
                      ON mml.id = relation.material_id AND rpt.advertiser_account_id = relation.system_account_id AND
                         relation.account_type = 0
            ${ew.customSqlSegment}
    </select>

    <select id="getValidityTeamSummaryReport" resultType="ai.yiye.agent.marketing.vo.validity.ValidityReport">
        with sumRpt AS (select id,
                               sum(cost / 100) over (partition by signature)   AS materialCost,
                               sum(cost / 100) over (partition by creative_id) AS creativeCost
                        from marketing_data_material_daily_report),
             rel AS (
                 select team_id, signature, team_ratio
                 from marketing_material_designer_team_rel
                 group by team_id, signature, team_ratio
             )
        select coalesce(round(sum(cost / 100 * ${type.ratio}), 2), 0)       AS cost,
               count(distinct mml.signature)                                AS materialCount,
               coalesce(round(sum(view_num * ${type.ratio}), 2), 0)         AS viewNum,
               coalesce(round(sum(click_num * ${type.ratio}), 2), 0)        AS clickNum,
               case sum(view_num)
                   when 0 then 0
                   else coalesce(round(sum(click_num) / sum(view_num), 4), 0) * 100
                   end                                                      AS clickRate,
               case sum(click_num)
                   when 0 then 0
                   else coalesce(round(sum(cost / 100) / sum(click_num), 2), 0)
                   end                                                      AS avgClickCost,
               coalesce(round(sum(convert_num * ${type.ratio}), 2), 0)      AS convertNum,
               case sum(convert_num)
                   when 0 then 0
                   else coalesce(round(sum(cost / 100) / sum(convert_num), 2), 0)
                   end                                                      AS avgConvertCost,
               coalesce(round(sum(register_num * ${type.ratio}), 2), 0)     AS registerNum,
               coalesce(round(sum(active_num * ${type.ratio}), 2), 0)       AS activeNum,
               coalesce(round(sum(pay_behavior_num * ${type.ratio}), 2), 0) AS payBehaviorNum,
               case sum(active_num)
                   when 0 then 0
                   else coalesce(round(sum(cost / 100) / sum(active_num), 2), 0)
                   end                                                      AS avgActiveCost,
               coalesce(round(sum(play_num * ${type.ratio}), 2), 0)         AS playNum,
               coalesce(round(sum(video_play100_num), 2), 0)                AS videoPlay100Num
        from rel
                 JOIN marketing_data_material_daily_report rpt ON rpt.signature = rel.signature
                 JOIN marketing_advertiser_account acc ON rpt.advertiser_account_id = acc.id
                 JOIN sumRpt ON rpt.id = sumRpt.id
                 JOIN marketing_material_library mml on rel.signature = mml.signature
                 JOIN management_designer_team team ON rel.team_id = team.id
                 JOIN marketing_material_account_relation relation
                      ON mml.id = relation.material_id AND rpt.advertiser_account_id = relation.system_account_id AND
                         relation.account_type = 0
            ${ew.customSqlSegment}
    </select>

    <!--  @formatter:off -->
    <select id="getValidityDataReport" resultType="ai.yiye.agent.marketing.vo.validity.ValidityReport">
        with designers AS (select designer_id, string_agg(distinct team_name, '、') AS teamName
        from management_designer_team_rel
        group by designer_id),
        teams AS (select team_id, string_agg(role_name || '-' || designer_name, '; ') AS designerName
        from management_designer_team_rel
        group by team_id),
        sumRpt AS (select id,
        sum(cost / 100) over (partition by signature) AS materialCost,
        sum(cost / 100) over (partition by creative_id) AS creativeCost
        from marketing_data_material_daily_report)
        select
        <choose>
            <when test="dimension == 'designer' ">
                d.id AS designerId,
                d.name AS designerName,
                designers.teamName AS teams,
            </when>
            <when test="dimension == 'team' ">
                team.id AS teamId,
                team.name AS teamName,
                teams.designerName AS designers,
            </when>
            <when test="dimension == 'day' ">
                date(day_time) AS day,
            </when>
            <when test="dimension == 'month' ">
                to_char(day_time, 'yyyy-MM') AS month,
            </when>
            <when test="dimension == 'industry' ">
                industry,
            </when>
            <when test="dimension == 'platform' ">
                acc.platform_id AS platformId,
                acc.platform_name AS platformName,
            </when>
            <when test="dimension == 'role' ">
                mr.id AS roleId,
                mr.name AS roleName,
            </when>
        </choose>
        count(distinct mml.signature) AS materialCount,
        coalesce(round(sum(cost / 100 * ${type.ratio}), 2), 0) AS cost,
        coalesce(round(sum(view_num * ${type.ratio}), 2), 0) AS viewNum,
        coalesce(round(sum(click_num * ${type.ratio}), 2), 0) AS clickNum,
        case sum(view_num)
        when 0 then 0
        else coalesce(round(sum(click_num) / sum(view_num), 4), 0) * 100
        end AS clickRate,
        case sum(click_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(click_num), 2), 0)
        end AS avgClickCost,
        coalesce(round(sum(convert_num * ${type.ratio}), 2), 0) AS convertNum,
        case sum(convert_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(convert_num), 2), 0)
        end AS avgConvertCost,
        coalesce(round(sum(register_num * ${type.ratio}), 2), 0) AS registerNum,
        coalesce(round(sum(active_num * ${type.ratio}), 2), 0) AS activeNum,
        coalesce(round(sum(pay_behavior_num * ${type.ratio}), 2), 0) AS payBehaviorNum,
        case sum(active_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(active_num), 2), 0)
        end AS avgActiveCost,
        coalesce(round(sum(play_num * ${type.ratio}), 2), 0) AS playNum,
        coalesce(round(sum(video_play100_num * ${type.ratio}), 2), 0) AS videoPlay100Num,
        case sum(view_num)
        when 0 then 0
        else coalesce(round(sum(cost) * 10 / sum(view_num), 2), 0)
        end AS cpm,
        case sum(click_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(click_num), 2), 0)
        end AS cpc,
        coalesce(round(sum(order_num * ${type.ratio}), 2), 0) AS orderNum,
        case sum(order_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(order_num), 2), 0)
        end AS avgOrderCost,
        case sum(click_num)
        when 0 then 0
        else coalesce(round(sum(order_num) / sum(click_num), 4), 0) * 100
        end AS orderRate,
        case sum(click_num)
        when 0 then 0
        else coalesce(round(sum(convert_num) / sum(click_num), 4), 0) * 100
        end AS convertRate,
        coalesce(round(sum(deep_convert_num * ${type.ratio}), 2), 0) AS deepConvertNum,
        case sum(click_num)
        when 0 then 0
        else coalesce(round(sum(deep_convert_num) / sum(click_num), 4), 0) * 100
        end AS deepConvertRate,
        case sum(deep_convert_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(deep_convert_num), 2), 0)
        end AS avgDeepConvertCost,
        coalesce(round(sum(share_num * ${type.ratio}), 2), 0) AS shareNum,
        case sum(share_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(share_num), 2), 0)
        end AS avgShareCost,
        coalesce(round(sum(praise_num * ${type.ratio}), 2), 0) AS praiseNum,
        case sum(praise_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(praise_num), 2), 0)
        end AS avgPraiseCost,
        coalesce(round(sum(comment_num * ${type.ratio}), 2), 0) AS commentNum,
        case sum(comment_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(comment_num), 2), 0)
        end AS avgCommentCost,
        case sum(click_num)
        when 0 then 0
        else coalesce(round(sum(register_num) / sum(click_num), 4), 0) * 100
        end AS registerRate,
        case sum(register_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(register_num), 2), 0)
        end AS avgRegisterCost,
        coalesce(round(sum(download_num * ${type.ratio}), 2), 0) AS downloadNum,
        case sum(download_num)
        when 0 then 0
        else coalesce(round(sum(active_num) / sum(download_num), 4), 0) * 100
        end AS activeRate,
        case sum(active_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(active_num), 2), 0)
        end AS avgActiveCost,
        coalesce(round(sum(cost * ${type.ratio} / 100), 2), 0) AS payBehaviorCost,
        case sum(pay_behavior_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(pay_behavior_num), 2), 0)
        end AS avgPayBehaviorCost,
        coalesce(round(sum(valid_play_num * ${type.ratio}), 2), 0) AS validPlayNum,
        case sum(valid_play_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(valid_play_num), 2), 0)
        end AS avgValidPlayCost,
        case sum(view_num)
        when 0 then 0
        else coalesce(round(sum(valid_play_num) / sum(view_num), 4), 0) * 100
        end AS validPlayRate,
        coalesce(round(sum(video_play25_num * ${type.ratio}), 2), 0) AS videoPlay25Num,
        coalesce(round(sum(video_play50_num * ${type.ratio}), 2), 0) AS videoPlay50Num,
        coalesce(round(sum(video_play75_num * ${type.ratio}), 2), 0) AS videoPlay75Num,
        coalesce(round(sum(average_play_time * ${type.ratio}), 2), 0) AS avgPlayTime
        from marketing_material_designer_team_rel rel
        JOIN marketing_data_material_daily_report rpt ON rpt.signature = rel.signature
        JOIN marketing_advertiser_account acc ON rpt.advertiser_account_id = acc.id
        JOIN sumRpt ON rpt.id = sumRpt.id
        JOIN marketing_material_library mml on rel.signature = mml.signature
        JOIN management_role mr on rel.role_id = mr.id
        JOIN marketing_material_account_relation relation ON mml.id = relation.material_id AND rpt.advertiser_account_id
        = relation.system_account_id AND relation.account_type = 0
        JOIN management_designer_team team ON rel.team_id = team.id
        JOIN teams ON team.id = teams.team_id
        JOIN management_designer d ON rel.designer_id = d.id
        JOIN designers ON d.id = designers.designer_id
        ${ew.customSqlSegment}
    </select>
    <!--  @formatter:on -->

    <!--  @formatter:off -->
    <select id="getValidityTeamDataReport" resultType="ai.yiye.agent.marketing.vo.validity.ValidityReport">
        with rel AS (select team_id, signature, team_ratio
        from marketing_material_designer_team_rel
        group by team_id, signature, team_ratio),
        teams AS (select team_id, string_agg(role_name || '-' || designer_name, '; ') AS designerName
        from management_designer_team_rel
        group by team_id),
        sumRpt AS (select id,
        sum(cost / 100) over (partition by signature) AS materialCost,
        sum(cost / 100) over (partition by creative_id) AS creativeCost
        from marketing_data_material_daily_report)
        select
        <choose>
            <when test="dimension == 'team' ">
                team.id AS teamId,
                team.name AS teamName,
                teams.designerName AS designers,
            </when>
            <when test="dimension == 'day' ">
                date(day_time) AS day,
            </when>
            <when test="dimension == 'month' ">
                to_char(day_time, 'yyyy-MM') AS month,
            </when>
            <when test="dimension == 'industry' ">
                industry,
            </when>
            <when test="dimension == 'platform' ">
                acc.platform_id AS platformId,
                acc.platform_name AS platformName,
            </when>
        </choose>
        count(distinct mml.signature) AS materialCount,
        coalesce(round(sum(cost / 100 * ${type.ratio}), 2), 0) AS cost,
        coalesce(round(sum(view_num * ${type.ratio}), 2), 0) AS viewNum,
        coalesce(round(sum(click_num * ${type.ratio}), 2), 0) AS clickNum,
        case sum(view_num)
        when 0 then 0
        else coalesce(round(sum(click_num) / sum(view_num), 4), 0) * 100
        end AS clickRate,
        case sum(click_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(click_num), 2), 0)
        end AS avgClickCost,
        coalesce(round(sum(convert_num * ${type.ratio}), 2), 0) AS convertNum,
        case sum(convert_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(convert_num), 2), 0)
        end AS avgConvertCost,
        coalesce(round(sum(register_num * ${type.ratio}), 2), 0) AS registerNum,
        coalesce(round(sum(active_num * ${type.ratio}), 2), 0) AS activeNum,
        coalesce(round(sum(pay_behavior_num * ${type.ratio}), 2), 0) AS payBehaviorNum,
        case sum(active_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(active_num), 2), 0)
        end AS avgActiveCost,
        coalesce(round(sum(play_num * ${type.ratio}), 2), 0) AS playNum,
        coalesce(round(sum(video_play100_num * ${type.ratio}), 2), 0) AS videoPlay100Num,
        case sum(view_num)
        when 0 then 0
        else coalesce(round(sum(cost) * 10 / sum(view_num), 2), 0)
        end AS cpm,
        case sum(click_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(click_num), 2), 0)
        end AS cpc,
        coalesce(round(sum(order_num * ${type.ratio}), 2), 0) AS orderNum,
        case sum(order_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(order_num), 2), 0)
        end AS avgOrderCost,
        case sum(click_num)
        when 0 then 0
        else coalesce(round(sum(order_num) / sum(click_num), 4), 0) * 100
        end AS orderRate,
        case sum(click_num)
        when 0 then 0
        else coalesce(round(sum(convert_num) / sum(click_num), 4), 0) * 100
        end AS convertRate,
        coalesce(round(sum(deep_convert_num * ${type.ratio}), 2), 0) AS deepConvertNum,
        case sum(click_num)
        when 0 then 0
        else coalesce(round(sum(deep_convert_num) / sum(click_num), 4), 0) * 100
        end AS deepConvertRate,
        case sum(deep_convert_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(deep_convert_num), 2), 0)
        end AS avgDeepConvertCost,
        coalesce(round(sum(share_num * ${type.ratio}), 2), 0) AS shareNum,
        case sum(share_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(share_num), 2), 0)
        end AS avgShareCost,
        coalesce(round(sum(praise_num * ${type.ratio}), 2), 0) AS praiseNum,
        case sum(praise_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(praise_num), 2), 0)
        end AS avgPraiseCost,
        coalesce(round(sum(comment_num * ${type.ratio}), 2), 0) AS commentNum,
        case sum(comment_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(comment_num), 2), 0)
        end AS avgCommentCost,
        case sum(click_num)
        when 0 then 0
        else coalesce(round(sum(register_num) / sum(click_num), 4), 0) * 100
        end AS registerRate,
        case sum(register_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(register_num), 2), 0)
        end AS avgRegisterCost,
        coalesce(round(sum(download_num * ${type.ratio}), 2), 0) AS downloadNum,
        case sum(download_num)
        when 0 then 0
        else coalesce(round(sum(active_num) / sum(download_num), 4), 0) * 100
        end AS activeRate,
        case sum(active_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(active_num), 2), 0)
        end AS avgActiveCost,
        coalesce(round(sum(cost * ${type.ratio} / 100), 2), 0) AS payBehaviorCost,
        case sum(pay_behavior_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(pay_behavior_num), 2), 0)
        end AS avgPayBehaviorCost,
        coalesce(round(sum(valid_play_num * ${type.ratio}), 2), 0) AS validPlayNum,
        case sum(valid_play_num)
        when 0 then 0
        else coalesce(round(sum(cost / 100) / sum(valid_play_num), 2), 0)
        end AS avgValidPlayCost,
        case sum(view_num)
        when 0 then 0
        else coalesce(round(sum(valid_play_num) / sum(view_num), 4), 0) * 100
        end AS validPlayRate,
        coalesce(round(sum(video_play25_num * ${type.ratio}), 2), 0) AS videoPlay25Num,
        coalesce(round(sum(video_play50_num * ${type.ratio}), 2), 0) AS videoPlay50Num,
        coalesce(round(sum(video_play75_num * ${type.ratio}), 2), 0) AS videoPlay75Num,
        coalesce(round(sum(average_play_time * ${type.ratio}), 2), 0) AS avgPlayTime
        from rel
        JOIN marketing_data_material_daily_report rpt ON rpt.signature = rel.signature
        JOIN marketing_advertiser_account acc ON rpt.advertiser_account_id = acc.id
        JOIN sumRpt ON rpt.id = sumRpt.id
        JOIN marketing_material_library mml on rel.signature = mml.signature
        JOIN marketing_material_account_relation relation ON mml.id = relation.material_id AND rpt.advertiser_account_id
        = relation.system_account_id AND relation.account_type = 0
        JOIN management_designer_team team ON rel.team_id = team.id
        JOIN teams ON team.id = teams.team_id
        ${ew.customSqlSegment}
    </select>
    <!--  @formatter:on -->

    <select id="getMaterialCount" resultType="long">
        with sumRpt AS (select id,
                               sum(cost / 100) over (partition by signature)   AS materialCost,
                               sum(cost / 100) over (partition by creative_id) AS creativeCost
                        from marketing_data_material_daily_report)
        select count(distinct mml.signature) AS materialCount
        from marketing_material_designer_team_rel rel
                 JOIN marketing_data_material_daily_report rpt ON rpt.signature = rel.signature
                 JOIN marketing_advertiser_account acc ON rpt.advertiser_account_id = acc.id
                 JOIN sumRpt ON rpt.id = sumRpt.id
                 JOIN marketing_material_library mml on rel.signature = mml.signature
                 JOIN marketing_data_creative mdc ON mdc.creative_id = rpt.creative_id
                 JOIN marketing_data_advertise mdad ON mdad.adcreative_id = mdc.creative_id
                 JOIN marketing_data_adgroup mda ON mda.adgroup_id = mdad.adgroup_id
                 JOIN management_designer_team team ON rel.team_id = team.id
                 JOIN management_designer d ON rel.designer_id = d.id
                 JOIN management_role mr on rel.role_id = mr.id
                 JOIN marketing_material_account_relation relation
                      ON mml.id = relation.material_id AND rpt.advertiser_account_id = relation.system_account_id AND
                         relation.account_type = 0
            ${ew.customSqlSegment}
    </select>

    <select id="getValidityDataDetail" resultType="ai.yiye.agent.marketing.vo.validity.ValidityReport">
        with sumRpt AS (select id,
                               sum(cost / 100) over (partition by signature)   AS materialCost,
                               sum(cost / 100) over (partition by creative_id) AS creativeCost
                        from marketing_data_material_daily_report),
            material_name as (
                     select string_agg(mml.material_name, '；') material_name, ad_id, day_time
                     from (
                              select unnest(mdmdr.material_group) sign, ad_id, day_time
                              from marketing_data_material_daily_report mdmdr
                          ) material
                              join marketing_material_library mml on mml.signature = material.sign
                     group by ad_id, day_time)
        select case
               when array_length(material_group, 1) > 1 then '组图（' || array_length(material_group, 1) || '）'
               end                                                                    AS material_style,
               date(rpt.day_time)                                                         AS day,
               d.name                                                                 AS designerName,
               team.name                                                              AS teamName,
               mr.name                                                                AS roleName,
               rpt.creative_id                                                        AS creativeId,
               mdc.creative_name                                                      AS creativeName,
               acc.platform_name                                                      AS platformName,
               acc.account_name                                                       AS accountName,
               c.campaign_name                                                        AS campaignName,
               mda.name                                                               AS adgroupName,
               COALESCE(mn.material_name,mml.material_name)                           AS materialName,
               acc.industry                                                           AS industry,
               mml.url                                                                AS materialUrl,
               case mml.material_type when 0 then '图片' when 1 then '视频' else '文案' END AS materialType,
               array_to_string(mml.creative_sign, ',')                                AS materialSign,
               coalesce(round(cost / 100 * rel.designer_ratio, 2), 0)                 AS cost,
               coalesce(round(view_num * rel.designer_ratio, 2), 0)                   AS viewNum,
               coalesce(round(click_num * rel.designer_ratio, 2), 0)                  AS clickNum,
               case view_num
                   when 0 then 0
                   else coalesce(round(click_num / view_num, 4), 0) * 100
                   end                                                                AS clickRate,
               case click_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / click_num, 2), 0)
                   end                                                                AS avgClickCost,
               coalesce(round(convert_num * rel.designer_ratio, 2), 0)                AS convertNum,
               case convert_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / convert_num, 2), 0)
                   end                                                                AS avgConvertCost,
               coalesce(round(register_num * rel.designer_ratio, 2), 0)               AS registerNum,
               coalesce(round(active_num * rel.designer_ratio, 2), 0)                 AS activeNum,
               coalesce(round(pay_behavior_num * rel.designer_ratio, 2), 0)           AS payBehaviorNum,
               case active_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / active_num, 2), 0)
                   end                                                                AS avgActiveCost,
               coalesce(round(play_num * rel.designer_ratio, 2), 0)                   AS playNum,
               coalesce(round(video_play100_num * rel.designer_ratio, 2), 0)          AS videoPlay100Num,
               case view_num
                   when 0 then 0
                   else coalesce(round(cost * 10 / view_num, 2), 0)
                   end                                                                AS cpm,
               case click_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / click_num, 2), 0)
                   end                                                                AS cpc,
               coalesce(round(order_num * rel.designer_ratio, 2), 0)                  AS orderNum,
               case order_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / order_num, 2), 0)
                   end                                                                AS avgOrderCost,
               case click_num
                   when 0 then 0
                   else coalesce(round(order_num / click_num, 4), 0) * 100
                   end                                                                AS orderRate,
               case click_num
                   when 0 then 0
                   else coalesce(round(convert_num / click_num, 4), 0) * 100
                   end                                                                AS convertRate,
               coalesce(round(deep_convert_num * rel.designer_ratio, 2), 0)           AS deepConvertNum,
               case click_num
                   when 0 then 0
                   else coalesce(round(deep_convert_num / click_num, 4), 0) * 100
                   end                                                                AS deepConvertRate,
               case deep_convert_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / deep_convert_num, 2), 0)
                   end                                                                AS avgDeepConvertCost,
               coalesce(round(share_num * rel.designer_ratio, 2), 0)                  AS shareNum,
               case share_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / share_num, 2), 0)
                   end                                                                AS avgShareCost,
               coalesce(round(praise_num * rel.designer_ratio, 2), 0)                 AS praiseNum,
               case praise_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / praise_num, 2), 0)
                   end                                                                AS avgPraiseCost,
               coalesce(round(comment_num * rel.designer_ratio, 2), 0)                AS commentNum,
               case comment_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / comment_num, 2), 0)
                   end                                                                AS avgCommentCost,
               case click_num
                   when 0 then 0
                   else coalesce(round(register_num / click_num, 4), 0) * 100
                   end                                                                AS registerRate,
               case register_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / register_num, 2), 0)
                   end                                                                AS avgRegisterCost,
               coalesce(round(download_num * rel.designer_ratio, 2), 0)               AS downloadNum,
               case download_num
                   when 0 then 0
                   else coalesce(round(active_num / download_num, 4), 0) * 100
                   end                                                                AS activeRate,
               case active_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / active_num, 2), 0)
                   end                                                                AS avgActiveCost,
               coalesce(round(cost * rel.designer_ratio / 100, 2), 0)                 AS payBehaviorCost,
               case pay_behavior_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / pay_behavior_num, 2), 0)
                   end                                                                AS avgPayBehaviorCost,
               coalesce(round(valid_play_num * rel.designer_ratio, 2), 0)             AS validPlayNum,
               case valid_play_num
                   when 0 then 0
                   else coalesce(round(cost / 100 / valid_play_num, 2), 0)
                   end                                                                AS avgValidPlayCost,
               case view_num
                   when 0 then 0
                   else coalesce(round(valid_play_num / view_num, 4), 0) * 100
                   end                                                                AS validPlayRate,
               coalesce(round(video_play25_num * rel.designer_ratio, 2), 0)           AS videoPlay25Num,
               coalesce(round(video_play50_num * rel.designer_ratio, 2), 0)           AS videoPlay50Num,
               coalesce(round(video_play75_num * rel.designer_ratio, 2), 0)           AS videoPlay75Num,
               coalesce(round(average_play_time * rel.designer_ratio, 2), 0)          AS avgPlayTime
        from marketing_material_designer_team_rel rel
                 JOIN marketing_data_material_daily_report rpt ON rpt.signature = rel.signature
                 JOIN sumRpt ON rpt.id = sumRpt.id
                 JOIN marketing_material_library mml on rel.signature = mml.signature
                 LEFT JOIN material_name mn on mn.day_time = rpt.day_time and mn.ad_id = rpt.ad_id
                 JOIN marketing_material_account_relation relation
                      ON mml.id = relation.material_id AND rpt.advertiser_account_id = relation.system_account_id AND
                         relation.account_type = 0
                 JOIN marketing_advertiser_account acc ON rpt.advertiser_account_id = acc.id
                 LEFT JOIN marketing_data_creative mdc ON mdc.creative_id = rpt.creative_id
                 JOIN marketing_data_advertise mdad ON rpt.ad_id = mdad.ad_id
                 JOIN marketing_data_adgroup mda ON mda.adgroup_id = rpt.adgroup_id
                 JOIN marketing_data_campaign c ON mda.campaign_id = c.campaign_id
                 JOIN management_designer d ON rel.designer_id = d.id
                 JOIN management_designer_team team ON rel.team_id = team.id
                 JOIN management_role mr ON rel.role_id = mr.id
            ${ew.customSqlSegment}
    </select>

    <!-- 优化师人效报表明细 -->
    <select id="getValidityDataOptimizerDetail" resultType="ai.yiye.agent.marketing.vo.validity.ValidityReport">
        with uu AS (select distinct u.username as username, u.id, u.role
                    FROM ucenter_user u
                             join marketing_advertiser_account m
                                  ON u.id = m.optimizer_id
                    WHERE role = 1),
             opt as (select optimizer_id,
                            count(*)         as operateNum,
                            max(created_at)  as lastOperateTime,
                            min(created_at)  as firstOperateTime,
                            account_id,
                            date(created_at) as dayTime
                     FROM marketing_data_operation
                     group by optimizer_id, account_id, dayTime),
             act as (
                 SELECT count(*) as accountNum,
                        m.optimizer_id
                 FROM ucenter_user u
                          join marketing_advertiser_account m
                               ON u.id = m.optimizer_id
                 group by m.optimizer_id),
             adg as (
                 SELECT count(1)         as adGroupNums
                      , adg.optimizer_id
                      , account_id
                      , date(created_at) as daytime
                 FROM marketing_data_adgroup adg
                 WHERE adg.optimizer_id > 0
                 group by adg.optimizer_id, account_id
                        , daytime),
             view as (
                 SELECT count(distinct ad_id) as viewOver0Ad, optimizer_id, account_id, date(day_time) as dayTime
                 FROM marketing_data_material_daily_report rpt
                 WHERE view_num > 0
                 group by rpt.optimizer_id, rpt.account_id, dayTime),
             account as (
                 select account_id, account_name
                 from marketing_advertiser_account
             ),
             report as (
                 SELECT max(optimizer_id)                             as optimizer_id,
                        max(account_id)                               as account_id,
                        max(signature)                                as signature,
                        date(day_time)                                as day_time,
                        coalesce(round(sum(cost / 100), 2), 0)        AS cost,
                        coalesce(round(sum(view_num), 2), 0)          AS viewNum,
                        coalesce(round(sum(click_num), 2), 0)         AS clickNum,
                        coalesce(round(sum(convert_num), 2), 0)       AS convertNum,
                        coalesce(round(sum(register_num), 2), 0)      AS registerNum,
                        coalesce(round(sum(active_num), 2), 0)        AS activeNum,
                        coalesce(round(sum(pay_behavior_num), 2), 0)  AS payBehaviorNum,
                        coalesce(round(sum(play_num), 2), 0)          AS playNum,
                        coalesce(round(sum(video_play100_num), 2), 0) AS videoPlay100Num,
                        coalesce(round(sum(order_num), 2), 0)         AS orderNum,
                        coalesce(round(sum(deep_convert_num), 2), 0)  AS deepConvertNum,
                        coalesce(round(sum(share_num), 2), 0)         AS shareNum,
                        coalesce(round(sum(praise_num), 2), 0)        AS praiseNum,
                        coalesce(round(sum(comment_num), 2), 0)       AS commentNum,
                        coalesce(round(sum(download_num), 2), 0)      AS downloadNum,
                        coalesce(round(sum(cost / 100), 2), 0)        AS payBehaviorCost,
                        coalesce(round(sum(valid_play_num), 2), 0)    AS validPlayNum,
                        coalesce(round(sum(video_play25_num), 2), 0)  AS videoPlay25Num,
                        coalesce(round(sum(video_play50_num), 2), 0)  AS videoPlay50Num,
                        coalesce(round(sum(video_play75_num), 2), 0)  AS videoPlay75Num,
                        coalesce(round(sum(average_play_time), 2), 0) AS avgPlayTime
                 FROM marketing_data_material_daily_report rpt
                 group by rpt.optimizer_id, rpt.day_time, rpt.account_id),
            material as (select distinct(signature) as signature,optimizer_id from marketing_material_library mml group by optimizer_id, signature)
        SELECT to_char(opt.dayTime, 'YYYY/mm/dd')                                                    as day,
               max(opt.optimizerId)                                                                  as optimizerId,
               max(opt.optimizerName)                                                                as optimizerName,
               max(opt.accountName)                                                                  as accountName,
               sum(opt.operateNum)                                                                   as operateNum,
               to_char(max(opt.firstOperateTime), 'YYYY/mm/dd hh/ss/mm')                             as firstOperateTime,
               to_char(max(opt.lastOperateTime), 'YYYY/mm/dd hh/ss/mm')                              as lastOperateTime,
               sum(act.accountNum)                                                                   as accountNum,
               sum(opt.newAdNum)                                                                     as newAdNum,
               sum(opt.viewOver0Ad)                                                                  as viewOver0Ad,
               sum(opt.newMaterialCount)                                                             as newMaterialCount,
               sum(opt.cost)                                                                         as cost,
               sum(opt.viewNum)                                                                      as viewNum,
               sum(opt.clickNum)                                                                     as clickNum,
               case sum(opt.viewNum)
                   when 0 then 0
                   else coalesce(round(sum(opt.clickNum) / sum(opt.viewNum), 4), 0) *
                        100 end                                                        AS clickRate,
               case sum(opt.clickNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) / sum(opt.clickNum), 2), 0) end       AS avgClickCost,
               sum(opt.convertNum)                                                     as convertNum,
               case sum(opt.convertNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) / sum(opt.convertNum), 2), 0) end     AS avgConvertCost,
               sum(opt.registerNum)                                                    as registerNum,
               sum(opt.activeNum)                                                      as activeNum,
               sum(opt.payBehaviorNum)                                                 as payBehaviorNum,
               case sum(opt.activeNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) / sum(opt.activeNum), 2), 0) end      AS avgActiveCost,
               sum(opt.playNum)                                                        as playNum,
               sum(opt.videoPlay100Num)                                                as videoPlay100Num,
               case sum(opt.viewNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) * 1000 / sum(opt.viewNum), 2), 0) end AS cpm,
               case sum(opt.clickNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) / sum(opt.clickNum), 2), 0) end       AS cpc,
               sum(opt.orderNum)                                                       as orderNum,
               case sum(opt.orderNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) / sum(opt.orderNum), 2), 0) end       AS avgOrderCost,
               case sum(opt.clickNum)
                   when 0 then 0
                   else coalesce(round(sum(opt.orderNum) / sum(opt.clickNum), 4), 0) *
                        100 end                                                        AS orderRate,
               case sum(opt.clickNum)
                   when 0 then 0
                   else coalesce(round(sum(opt.convertNum) / sum(opt.clickNum), 4), 0) *
                        100 end                                                        AS convertRate,
               sum(opt.deepConvertNum)                                                 as deepConvertNum,
               case sum(opt.clickNum)
                   when 0 then 0
                   else coalesce(round(sum(opt.deepConvertNum) / sum(opt.clickNum), 4), 0) *
                        100 end                                                        AS deepConvertRate,
               case sum(opt.deepConvertNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) / sum(opt.deepConvertNum), 2), 0) end AS avgDeepConvertCost,
               sum(opt.shareNum)                                                       as shareNum,
               case sum(opt.shareNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) / sum(opt.shareNum), 2), 0) end       AS avgShareCost,
               sum(opt.praiseNum)                                                      as praiseNum,
               case sum(opt.praiseNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) / sum(opt.praiseNum), 2), 0) end      AS avgPraiseCost,
               sum(opt.commentNum)                                                     as commentNum,
               case sum(opt.commentNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) / sum(opt.commentNum), 2), 0) end     AS avgCommentCost,
               case sum(opt.clickNum)
                   when 0 then 0
                   else coalesce(round(sum(opt.registerNum) / sum(opt.clickNum), 4), 0) *
                        100 end                                                        AS registerRate,
               case sum(opt.registerNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) / sum(opt.registerNum), 2), 0) end    AS avgRegisterCost,
               sum(opt.downloadNum)                                                    as downloadNum,
               case sum(opt.downloadNum)
                   when 0 then 0
                   else coalesce(round(sum(opt.activeNum) / sum(opt.downloadNum), 4), 0) *
                        100 end                                                        AS activeRate,
               sum(opt.payBehaviorCost)                                                as payBehaviorCost,
               case sum(opt.payBehaviorNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) / sum(opt.payBehaviorNum), 2), 0) end AS avgPayBehaviorCost,
               sum(opt.validPlayNum)                                                   as validPlayNum,
               case sum(opt.validPlayNum)
                   when 0 then 0
                   else coalesce(round(sum(cost) / sum(opt.validPlayNum), 2), 0) end   AS avgValidPlayCost,
               case sum(opt.viewNum)
                   when 0 then 0
                   else coalesce(round(sum(opt.validPlayNum) / sum(opt.viewNum), 4), 0) *
                        100 end                                                        AS validPlayRate,
               sum(opt.videoPlay25Num)                                                 as videoPlay25Num,
               sum(opt.videoPlay50Num)                                                 as videoPlay50Num,
               sum(opt.videoPlay75Num)                                                 as videoPlay75Num,
               sum(opt.avgPlayTime)                                                    as avgPlayTime
        FROM (
                 SELECT
                     uu.id                                                                               as optimizerId,
                     rpt.day_time                                                                        as dayTime,
                     max(uu.username)                                                                    as optimizerName,
                     max(account.account_name)                                                           as accountName,
                     coalesce(sum(opt.operateNum), 0)     as operateNum,
                     max(opt.firstOperateTime)            as firstOperateTime,
                     max(opt.lastOperateTime)             as lastOperateTime,
                     coalesce(sum(adg.adGroupNums), 0)    as newAdNum,
                     coalesce(sum(view.viewOver0Ad), 0)   as viewOver0Ad,
                     date(rpt.day_time)                   AS day,
                     count(material.signature)        AS newMaterialCount,
                     coalesce(round(sum(rpt.cost), 2), 0) AS cost,
                     sum(rpt.viewNum)                     as viewNum,
                     sum(rpt.clickNum)                    AS clickNum,
                     sum(rpt.convertNum)                  as convertNum,
                     sum(rpt.registerNum)                 as registerNum,
                     sum(rpt.activeNum)                   as activeNum,
                     sum(rpt.payBehaviorNum)              as payBehaviorNum,
                     sum(rpt.playNum)                     as playNum,
                     sum(rpt.videoPlay100Num)             as videoPlay100Num,
                     sum(rpt.orderNum)                    as orderNum,
                     sum(rpt.deepConvertNum)              as deepConvertNum,
                     sum(rpt.shareNum)                    as shareNum,
                     sum(rpt.praiseNum)                   as praiseNum,
                     sum(rpt.commentNum)                  as commentNum,
                     sum(rpt.downloadNum)                 as downloadNum,
                     sum(rpt.payBehaviorCost)             as payBehaviorCost,
                     sum(rpt.validPlayNum)                as validPlayNum,
                     sum(rpt.videoPlay25Num)              as videoPlay25Num,
                     sum(rpt.videoPlay50Num)              as videoPlay50Num,
                     sum(rpt.videoPlay75Num)              as videoPlay75Num,
                     sum(rpt.avgPlayTime)                 as avgPlayTime
                 FROM uu
                          JOIN report rpt
                               ON rpt.optimizer_id = uu.id
                          LEFT JOIN material
                                    ON rpt.signature = material.signature and material.optimizer_id = rpt.optimizer_id
                          LEFT JOIN opt
                                    ON rpt.optimizer_id = opt.optimizer_id and rpt.account_id = opt.account_id
                                        and rpt.day_time = opt.dayTime
                          left JOIN adg
                                    ON rpt.optimizer_id = adg.optimizer_id
                                        and rpt.account_id = adg.account_id
                                        and rpt.day_time = adg.daytime
                          JOIN view
                               ON rpt.optimizer_id = view.optimizer_id
                                   and rpt.account_id = view.account_id
                                   and rpt.day_time = view.dayTime
                          join account on rpt.account_id = account.account_id
                     ${ew.customSqlSegment}
                 group by optimizerId, day
                 order by cost desc
             ) opt
                 join act
                      ON opt.optimizerId = act.optimizer_id
        group by opt.optimizerId, opt.dayTime
    </select>

    <!-- 新建广告趋势 -->
    <select id="successTasks" resultType="ai.yiye.agent.marketing.vo.CreateAdGroupVO">
        with uu AS (select distinct u.username as username, u.id, u.role
                    FROM ucenter_user u
                             join marketing_advertiser_account m
                                  ON u.id = m.optimizer_id
                    WHERE role = 1),
             act as (
                 SELECT count(*) as accountNum,
                        m.optimizer_id
                 FROM ucenter_user u
                          join marketing_advertiser_account m
                               ON u.id = m.optimizer_id
                 group by m.optimizer_id
             ),
             adg as (
                 SELECT count(1)                          as adGroupNums
                      , adg.optimizer_id
                      , account_id
                      , to_char(created_at, 'yyyy-MM-dd') as daytime
                 FROM marketing_data_adgroup adg
                 WHERE adg.optimizer_id > 0
                 group by adg.optimizer_id, account_id, daytime
             ),
             report as (
                 SELECT max(optimizer_id)                   as optimizer_id,
                        max(account_id)                     as account_id,
                        to_char(rpt.day_time, 'yyyy-MM-dd') as month,
                        rpt.day_time                        as day_time
                 FROM marketing_data_material_daily_report rpt
                 group by rpt.optimizer_id, month, rpt.account_id, rpt.day_time)
        SELECT opt.day           AS month,
               sum(opt.newAdNum) as adGroupNums
        FROM (
                 SELECT uu.id                             as optimizerId,
                        max(uu.username)                  as optimizerName,
                        max(rpt.account_id)               as accountId,
                        to_char(date(rpt.month), 'MM-dd') as day,
                        coalesce(max(act.accountNum), 0)  as accountNum,
                        coalesce(sum(adg.adGroupNums), 0) as newAdNum
                 FROM uu
                          JOIN report rpt
                               ON rpt.optimizer_id = uu.id
                          join act
                               ON rpt.optimizer_id = act.optimizer_id
                          left JOIN adg
                                    ON rpt.optimizer_id = adg.optimizer_id
                                        and rpt.account_id = adg.account_id
                                        and rpt.month = adg.daytime
                          ${ew.customSqlSegment}
                 GROUP BY uu.id, rpt.month
             ) opt
        group by day
        order by day
    </select>

    <!-- 账户对比 -->
    <select id="accountContrast" resultType="ai.yiye.agent.marketing.vo.AccountContrastVO">
        with uu AS (select distinct u.username as username, u.id
                    FROM ucenter_user u
                             join marketing_advertiser_account m
                                  ON u.id = m.optimizer_id
                    WHERE role = 1
        ),
             account as (select account_id, account_name, optimizer_id from marketing_advertiser_account),
             act as (
                 SELECT count(*) as accountNum,
                        m.optimizer_id
                 FROM ucenter_user u
                          join marketing_advertiser_account m
                               ON u.id = m.optimizer_id
                 group by m.optimizer_id
             ),
             adg as (
                 SELECT count(1)         as adGroupNums
                      , adg.optimizer_id
                      , account_id
                      , date(created_at) as daytime
                 FROM marketing_data_adgroup adg
                 WHERE adg.optimizer_id > 0
                 group by adg.optimizer_id, account_id, daytime
             ),
             report as (
                 SELECT max(optimizer_id)                       as optimizer_id,
                        max(account_id)                         as account_id,
                        date(rpt.day_time)                      as month,
                        rpt.day_time                            as day_time,
                        coalesce(round(sum(cost / 100), 2), 0)  AS cost,
                        coalesce(round(sum(view_num), 2), 0)    AS viewNum,
                        coalesce(round(sum(click_num), 2), 0)   AS clickNum,
                        coalesce(round(sum(convert_num), 2), 0) AS convertNum
                 FROM marketing_data_material_daily_report rpt
                 group by rpt.optimizer_id, month, rpt.account_id, rpt.day_time)
        SELECT account.account_name as accountName,
               max(opt.accountId) as accountId,
               sum(opt.newAdNum)    as newAdNum,
               sum(opt.cost)        as cost,
               sum(opt.viewNum)     as viewNum,
               sum(opt.clickNum)    as clickNum,
               sum(opt.convertNum)  as convertNum
        FROM (
                 SELECT uu.id                                as optimizerId,
                        max(uu.username)                     as optimizerName,
                        max(rpt.account_id)                  as accountId,
                        date(rpt.month)                      as day,
                        coalesce(max(act.accountNum), 0)     as accountNum,
                        coalesce(sum(adg.adGroupNums), 0)    as newAdNum,
                        coalesce(round(sum(rpt.cost), 2), 0) AS cost,
                        sum(rpt.viewNum)                     as viewNum,
                        sum(rpt.clickNum)                    AS clickNum,
                        sum(rpt.convertNum)                  as convertNum
                 FROM uu
                          JOIN report rpt
                               ON rpt.optimizer_id = uu.id
                          join act
                               ON rpt.optimizer_id = act.optimizer_id
                          left JOIN adg
                                    ON rpt.optimizer_id = adg.optimizer_id
                                        and rpt.account_id = adg.account_id
                                        and rpt.month = adg.daytime
                 GROUP BY uu.id, rpt.month
             ) opt
                 join account on opt.optimizerId = account.optimizer_id and opt.accountId = account.account_id
            ${ew.customSqlSegment}
        group by accountName
    </select>

    <!-- 数据概况 -->
    <select id="getOptimizerDataReportVO" resultType="ai.yiye.agent.marketing.vo.validity.ValidityReport">
   with uu AS (select distinct u.username as username, u.id, u.role
               FROM ucenter_user u
                        join marketing_advertiser_account m
                             ON u.id = m.optimizer_id
               WHERE role = 1),
             opt as (select optimizer_id,
                            count(*)         as operateNum,
                            max(created_at)  as lastOperateTime,
                            min(created_at)  as firstOperateTime,
                            account_id,
                            date(created_at) as dayTime
                     FROM marketing_data_operation
                     group by optimizer_id, account_id, dayTime),
             act as (
                 SELECT count(*) as accountNum,
                        m.optimizer_id
                 FROM ucenter_user u
                          join marketing_advertiser_account m
                               ON u.id = m.optimizer_id
                 group by m.optimizer_id),
             adg as (
                 SELECT count(1)         as adGroupNums
                      , adg.optimizer_id
                      , account_id
                      , date(created_at) as daytime
                 FROM marketing_data_adgroup adg
                 WHERE adg.optimizer_id > 0
                 group by adg.optimizer_id, account_id
                        , daytime),
             view as (
                 SELECT count(distinct ad_id) as viewOver0Ad, optimizer_id, account_id, date(day_time) as dayTime
                 FROM marketing_data_material_daily_report rpt
                 WHERE view_num > 0
                 group by rpt.optimizer_id, rpt.account_id, dayTime),
             report as (
                 select max(optimizer_id)                                                             as optimizer_id,
                        max(account_id)                                                               as account_id,
                        max(signature)                                                                as signature,
                        date(day_time)                                                                as day_time,
                        coalesce(round(sum(cost / 100), 2), 0)                                        AS cost,
                        coalesce(round(sum(view_num), 2), 0)                                          AS viewNum,
                        coalesce(round(sum(click_num), 2), 0)                                         AS clickNum,
                        coalesce(round(sum(convert_num), 2), 0)                                       AS convertNum,
                        coalesce(round(sum(register_num), 2), 0)                                      AS registerNum,
                        coalesce(round(sum(active_num), 2), 0)                                        AS activeNum,
                        coalesce(round(sum(pay_behavior_num), 2), 0)                                  AS payBehaviorNum,
                        coalesce(round(sum(play_num), 2), 0)                                          AS playNum,
                        coalesce(round(sum(video_play100_num), 2), 0)                                 AS videoPlay100Num,
                        coalesce(round(sum(order_num), 2), 0)                                         AS orderNum,
                        coalesce(round(sum(deep_convert_num), 2), 0)                                  AS deepConvertNum,
                        coalesce(round(sum(share_num), 2), 0)                                         AS shareNum,
                        coalesce(round(sum(praise_num), 2), 0)                                        AS praiseNum,
                        coalesce(round(sum(comment_num), 2), 0)                                       AS commentNum,
                        coalesce(round(sum(download_num), 2), 0)                                      AS downloadNum,
                        coalesce(round(sum(cost / 100), 2), 0)                                        AS payBehaviorCost,
                        coalesce(round(sum(valid_play_num), 2), 0)                                    AS validPlayNum,
                        coalesce(round(sum(video_play25_num), 2), 0)                                  AS videoPlay25Num,
                        coalesce(round(sum(video_play50_num), 2), 0)                                  AS videoPlay50Num,
                        coalesce(round(sum(video_play75_num), 2), 0)                                  AS videoPlay75Num,
                        coalesce(round(sum(average_play_time), 2), 0)                                 AS avgPlayTime
                 from marketing_data_material_daily_report rpt
                 group by rpt.optimizer_id
                        , rpt.day_time
                        , rpt.account_id
             ),
        material as (select distinct(signature) as signature,optimizer_id from marketing_material_library mml group by optimizer_id, signature)
        SELECT
                coalesce(sum(opt.operateNum), 0)         as operateNum,
                coalesce(sum(act.accountNum), 0)         as accountNum,
                coalesce(sum(opt.newAdNum), 0)           as newAdNum,
                coalesce(sum(opt.viewOver0Ad), 0)        as viewOver0Ad,
                coalesce(sum(opt.newMaterialCount), 0)      as newMaterialCount,
                coalesce(sum(opt.cost), 0)               as cost,
                coalesce(sum(opt.viewNum), 0)            as viewNum,
                coalesce(sum(opt.clickNum), 0)           as clickNum,
                case sum(opt.viewNum)
                    when 0 then 0
                    else coalesce(round(sum(opt.clickNum) / sum(opt.viewNum), 4), 0) * 100 end                              AS clickRate,
                case sum(opt.clickNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) / sum(opt.clickNum), 2), 0) end                                  AS avgClickCost,
                coalesce(sum(opt.convertNum),0)         as convertNum,
                case sum(opt.convertNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) / sum(opt.convertNum), 2), 0) end                                AS avgConvertCost,
                coalesce(sum(opt.registerNum),0)        as registerNum,
                coalesce(sum(opt.activeNum),0)          as activeNum,
                coalesce(sum(opt.payBehaviorNum),0)     as payBehaviorNum,
                case sum(opt.activeNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) / sum(opt.activeNum), 2), 0) end                                 AS avgActiveCost,
                coalesce(sum(opt.playNum),0)            as playNum,
                coalesce(sum(opt.videoPlay100Num),0)    as videoPlay100Num,
                case sum(opt.viewNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) * 1000 / sum(opt.viewNum), 2), 0) end                                    AS cpm,
                case sum(opt.clickNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) / sum(opt.clickNum), 2), 0) end                                  AS cpc,
                coalesce(sum(opt.orderNum),0)           as orderNum,
                case sum(opt.orderNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) / sum(opt.orderNum), 2), 0) end                                  AS avgOrderCost,
                case sum(opt.clickNum)
                    when 0 then 0
                    else coalesce(round(sum(opt.orderNum) / sum(opt.clickNum), 4), 0) * 100 end                             AS orderRate,
                case sum(opt.clickNum)
                    when 0 then 0
                    else coalesce(round(sum(opt.convertNum) / sum(opt.clickNum), 4), 0) * 100 end                           AS convertRate,
                coalesce(sum(opt.deepConvertNum),0)     as deepConvertNum,
                case sum(opt.clickNum)
                    when 0 then 0
                    else coalesce(round(sum(opt.deepConvertNum) / sum(opt.clickNum), 4), 0) * 100 end                      AS deepConvertRate,
                case sum(opt.deepConvertNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) / sum(opt.deepConvertNum), 2), 0) end                           AS avgDeepConvertCost,
                coalesce(sum(opt.shareNum),0)           as shareNum,
                case sum(opt.shareNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) / sum(opt.shareNum), 2), 0) end                                  AS avgShareCost,
                coalesce(sum(opt.praiseNum),0)          as praiseNum,
                case sum(opt.praiseNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) / sum(opt.praiseNum), 2), 0) end                                 AS avgPraiseCost,
                coalesce(sum(opt.commentNum),0)         as commentNum,
                case sum(opt.commentNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) / sum(opt.commentNum), 2), 0) end                                AS avgCommentCost,
                case sum(opt.clickNum)
                    when 0 then 0
                    else coalesce(round(sum(opt.registerNum) / sum(opt.clickNum), 4), 0) * 100 end                          AS registerRate,
                case sum(opt.registerNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) / sum(opt.registerNum), 2), 0) end                               AS avgRegisterCost,
                coalesce(sum(opt.downloadNum),0)        as downloadNum,
                case sum(opt.downloadNum)
                    when 0 then 0
                    else coalesce(round(sum(opt.activeNum) / sum(opt.downloadNum), 4), 0) * 100 end                         AS activeRate,
                coalesce(sum(opt.payBehaviorCost),0)    as payBehaviorCost,
                case sum(opt.payBehaviorNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) / sum(opt.payBehaviorNum), 2), 0) end                           AS avgPayBehaviorCost,
                coalesce(sum(opt.validPlayNum),0)       as validPlayNum,
                case sum(opt.validPlayNum)
                    when 0 then 0
                    else coalesce(round(sum(cost) / sum(opt.validPlayNum), 2), 0) end                             AS avgValidPlayCost,
                case sum(opt.viewNum)
                    when 0 then 0
                    else coalesce(round(sum(opt.validPlayNum) / sum(opt.viewNum), 4), 0) * 100 end                         AS validPlayRate,
                coalesce(sum(opt.videoPlay25Num),0)     as videoPlay25Num,
                coalesce(sum(opt.videoPlay50Num),0)     as videoPlay50Num,
                coalesce(sum(opt.videoPlay75Num),0)     as videoPlay75Num,
                coalesce(sum(opt.avgPlayTime),0)        as avgPlayTime
        FROM (
                 SELECT max(uu.id)                           as optimizerId,
                        max(uu.username)                     as optimizerName,
                        coalesce(sum(opt.operateNum), 0)     as operateNum,
                        max(opt.firstOperateTime)            as firstOperateTime,
                        max(opt.lastOperateTime)             as lastOperateTime,
                        coalesce(sum(adg.adGroupNums), 0)    as newAdNum,
                        coalesce(sum(view.viewOver0Ad), 0)   as viewOver0Ad,
                        count(material.signature)        AS newMaterialCount,
                        coalesce(round(sum(rpt.cost), 2), 0) AS cost,
                        sum(rpt.viewNum)                     as viewNum,
                        sum(rpt.clickNum)                    AS clickNum,
                        sum(rpt.convertNum)                  as convertNum,
                        sum(rpt.registerNum)                 as registerNum,
                        sum(rpt.activeNum)                   as activeNum,
                        sum(rpt.payBehaviorNum)              as payBehaviorNum,
                        sum(rpt.playNum)                     as playNum,
                        sum(rpt.videoPlay100Num)             as videoPlay100Num,
                        sum(rpt.orderNum)                    as orderNum,
                        sum(rpt.deepConvertNum)              as deepConvertNum,
                        sum(rpt.shareNum)                    as shareNum,
                        sum(rpt.praiseNum)                   as praiseNum,
                        sum(rpt.commentNum)                  as commentNum,
                        sum(rpt.downloadNum)                 as downloadNum,
                        sum(rpt.payBehaviorCost)             as payBehaviorCost,
                        sum(rpt.validPlayNum)                as validPlayNum,
                        sum(rpt.videoPlay25Num)              as videoPlay25Num,
                        sum(rpt.videoPlay50Num)              as videoPlay50Num,
                        sum(rpt.videoPlay75Num)              as videoPlay75Num,
                        sum(rpt.avgPlayTime)                 as avgPlayTime
                 FROM uu
                          JOIN report rpt
                               ON rpt.optimizer_id = uu.id
                          LEFT JOIN material
                                    ON rpt.signature = material.signature and material.optimizer_id = rpt.optimizer_id
                          LEFT JOIN opt
                                    ON rpt.optimizer_id = opt.optimizer_id and rpt.account_id = opt.account_id
                                        and rpt.day_time = opt.dayTime
                          left JOIN adg
                                    ON rpt.optimizer_id = adg.optimizer_id
                                        and rpt.account_id = adg.account_id
                                        and rpt.day_time = adg.daytime
                          JOIN view
                               ON rpt.optimizer_id = view.optimizer_id
                                   and rpt.account_id = view.account_id
                                   and rpt.day_time = view.dayTime
                                               ${ew.customSqlSegment}
             ) opt
                 join act
                      ON opt.optimizerId = act.optimizer_id
    </select>

    <!-- 优化师人效报表 -->
    <select id="getOptimizerDataReport" resultType="ai.yiye.agent.marketing.vo.validity.ValidityReport">
        with uu AS (select distinct u.username as username, u.id, u.role
        FROM ucenter_user u
        join marketing_advertiser_account m
        ON u.id = m.optimizer_id
        WHERE role = 1),
        opt as (select optimizer_id,
        count(*)         as operateNum,
        max(created_at)  as lastOperateTime,
        min(created_at)  as firstOperateTime,
        account_id,
        date(created_at) as dayTime
        FROM marketing_data_operation
        group by optimizer_id, account_id, dayTime),
        act as (
        SELECT count(*) as accountNum,
        m.optimizer_id
        FROM ucenter_user u
        join marketing_advertiser_account m
        ON u.id = m.optimizer_id
        group by m.optimizer_id),
        adg as (
        SELECT count(1)         as adGroupNums
        , adg.optimizer_id
        , account_id
        , date(created_at) as daytime
        FROM marketing_data_adgroup adg
        WHERE adg.optimizer_id > 0
        group by adg.optimizer_id, account_id
        , daytime),
        view as (
        SELECT count(distinct ad_id) as viewOver0Ad, optimizer_id, account_id, date(day_time) as dayTime
        FROM marketing_data_material_daily_report rpt
        WHERE view_num > 0
        group by rpt.optimizer_id, rpt.account_id, dayTime),
        report as (
        select max(optimizer_id)                                                             as optimizer_id,
        max(account_id)                                                               as account_id,
        max(signature)                                                                as signature,
        date(day_time)                                                                as day_time,
        coalesce(round(sum(cost / 100), 2), 0)                                        AS cost,
        coalesce(round(sum(view_num), 2), 0)                                          AS viewNum,
        coalesce(round(sum(click_num), 2), 0)                                         AS clickNum,
        coalesce(round(sum(convert_num), 2), 0)                                       AS convertNum,
        coalesce(round(sum(register_num), 2), 0)                                      AS registerNum,
        coalesce(round(sum(active_num), 2), 0)                                        AS activeNum,
        coalesce(round(sum(pay_behavior_num), 2), 0)                                  AS payBehaviorNum,
        coalesce(round(sum(play_num), 2), 0)                                          AS playNum,
        coalesce(round(sum(video_play100_num), 2), 0)                                 AS videoPlay100Num,
        coalesce(round(sum(order_num), 2), 0)                                         AS orderNum,
        coalesce(round(sum(deep_convert_num), 2), 0)                                  AS deepConvertNum,
        coalesce(round(sum(share_num), 2), 0)                                         AS shareNum,
        coalesce(round(sum(praise_num), 2), 0)                                        AS praiseNum,
        coalesce(round(sum(comment_num), 2), 0)                                       AS commentNum,
        coalesce(round(sum(download_num), 2), 0)                                      AS downloadNum,
        coalesce(round(sum(cost / 100), 2), 0)                                        AS payBehaviorCost,
        coalesce(round(sum(valid_play_num), 2), 0)                                    AS validPlayNum,
        coalesce(round(sum(video_play25_num), 2), 0)                                  AS videoPlay25Num,
        coalesce(round(sum(video_play50_num), 2), 0)                                  AS videoPlay50Num,
        coalesce(round(sum(video_play75_num), 2), 0)                                  AS videoPlay75Num,
        coalesce(round(sum(average_play_time), 2), 0)                                 AS avgPlayTime
        from marketing_data_material_daily_report rpt
        group by rpt.optimizer_id
        , rpt.day_time
        , rpt.account_id
        ),
        material as (select distinct(signature) as signature,optimizer_id from marketing_material_library mml group by optimizer_id, signature)
        SELECT
        <if test="dimension == 'optimizer'">
            max(opt.optimizerId)                 as optimizerId,
            max(opt.optimizerName)      as optimizerName,
            sum(opt.operateNum)         as operateNum,
            to_char(max(opt.firstOperateTime),'YYYY/mm/dd hh/ss/mm')   as firstOperateTime,
            to_char(max(opt.lastOperateTime),'YYYY/mm/dd hh/ss/mm')    as lastOperateTime,
        </if>
        sum(opt.operateNum)         as operateNum,
        sum(act.accountNum)         as accountNum,
        sum(opt.newAdNum)           as newAdNum,
        sum(opt.viewOver0Ad)        as viewOver0Ad,
        <choose>
            <when test="dimension == 'optimizer_day' ">
                date(opt.day) AS day,
            </when>
            <when test="dimension == 'optimizer_month' ">
                opt.month as day,
            </when>
        </choose>
        sum(opt.newMaterialCount)      as newMaterialCount,
        sum(opt.cost)               as cost,
        sum(opt.viewNum)            as viewNum,
        sum(opt.clickNum)           as clickNum,
        case sum(opt.viewNum)
        when 0 then 0
        else coalesce(round(sum(opt.clickNum) / sum(opt.viewNum), 4), 0) * 100 end                              AS clickRate,
        case sum(opt.clickNum)
        when 0 then 0
        else coalesce(round(sum(cost) / sum(opt.clickNum), 2), 0) end                                  AS avgClickCost,
        sum(opt.convertNum)         as convertNum,
        case sum(opt.convertNum)
        when 0 then 0
        else coalesce(round(sum(cost) / sum(opt.convertNum), 2), 0) end                                AS avgConvertCost,
        sum(opt.registerNum)        as registerNum,
        sum(opt.activeNum)          as activeNum,
        sum(opt.payBehaviorNum)     as payBehaviorNum,
        case sum(opt.activeNum)
        when 0 then 0
        else coalesce(round(sum(cost) / sum(opt.activeNum), 2), 0) end                                 AS avgActiveCost,
        sum(opt.playNum)            as playNum,
        sum(opt.videoPlay100Num)    as videoPlay100Num,
        case sum(opt.viewNum)
        when 0 then 0
        else coalesce(round(sum(cost) * 1000 / sum(opt.viewNum), 2), 0) end                                    AS cpm,
        case sum(opt.clickNum)
        when 0 then 0
        else coalesce(round(sum(cost) / sum(opt.clickNum), 2), 0) end                                  AS cpc,
        sum(opt.orderNum)           as orderNum,
        case sum(opt.orderNum)
        when 0 then 0
        else coalesce(round(sum(cost) / sum(opt.orderNum), 2), 0) end                                  AS avgOrderCost,
        case sum(opt.clickNum)
        when 0 then 0
        else coalesce(round(sum(opt.orderNum) / sum(opt.clickNum), 4), 0) * 100 end                             AS orderRate,
        case sum(opt.clickNum)
        when 0 then 0
        else coalesce(round(sum(opt.convertNum) / sum(opt.clickNum), 4), 0) * 100 end                           AS convertRate,
        sum(opt.deepConvertNum)     as deepConvertNum,
        case sum(opt.clickNum)
        when 0 then 0
        else coalesce(round(sum(opt.deepConvertNum) / sum(opt.clickNum), 4), 0) * 100 end                      AS deepConvertRate,
        case sum(opt.deepConvertNum)
        when 0 then 0
        else coalesce(round(sum(cost) / sum(opt.deepConvertNum), 2), 0) end                           AS avgDeepConvertCost,
        sum(opt.shareNum)           as shareNum,
        case sum(opt.shareNum)
        when 0 then 0
        else coalesce(round(sum(cost) / sum(opt.shareNum), 2), 0) end                                  AS avgShareCost,
        sum(opt.praiseNum)          as praiseNum,
        case sum(opt.praiseNum)
        when 0 then 0
        else coalesce(round(sum(cost) / sum(opt.praiseNum), 2), 0) end                                 AS avgPraiseCost,
        sum(opt.commentNum)         as commentNum,
        case sum(opt.commentNum)
        when 0 then 0
        else coalesce(round(sum(cost) / sum(opt.commentNum), 2), 0) end                                AS avgCommentCost,
        case sum(opt.clickNum)
        when 0 then 0
        else coalesce(round(sum(opt.registerNum) / sum(opt.clickNum), 4), 0) * 100 end                          AS registerRate,
        case sum(opt.registerNum)
        when 0 then 0
        else coalesce(round(sum(cost) / sum(opt.registerNum), 2), 0) end                               AS avgRegisterCost,
        sum(opt.downloadNum)        as downloadNum,
        case sum(opt.downloadNum)
        when 0 then 0
        else coalesce(round(sum(opt.activeNum) / sum(opt.downloadNum), 4), 0) * 100 end                         AS activeRate,
        sum(opt.payBehaviorCost)    as payBehaviorCost,
        case sum(opt.payBehaviorNum)
        when 0 then 0
        else coalesce(round(sum(cost) / sum(opt.payBehaviorNum), 2), 0) end                           AS avgPayBehaviorCost,
        sum(opt.validPlayNum)       as validPlayNum,
        case sum(opt.validPlayNum)
        when 0 then 0
        else coalesce(round(sum(cost) / sum(opt.validPlayNum), 2), 0) end                             AS avgValidPlayCost,
        case sum(opt.viewNum)
        when 0 then 0
        else coalesce(round(sum(opt.validPlayNum) / sum(opt.viewNum), 4), 0) * 100 end                         AS validPlayRate,
        sum(opt.videoPlay25Num)     as videoPlay25Num,
        sum(opt.videoPlay50Num)     as videoPlay50Num,
        sum(opt.videoPlay75Num)     as videoPlay75Num,
        sum(opt.avgPlayTime)        as avgPlayTime
        FROM (
        SELECT
        <choose>
            <when test="dimension == 'optimizer'">
                uu.id as optimizerId,
            </when>
            <otherwise>
                max(uu.id) as optimizerId,
            </otherwise>
        </choose>
        max(uu.username)                     as optimizerName,
        coalesce(sum(opt.operateNum), 0)     as operateNum,
        max(opt.firstOperateTime)            as firstOperateTime,
        max(opt.lastOperateTime)             as lastOperateTime,
        coalesce(sum(adg.adGroupNums), 0)    as newAdNum,
        coalesce(sum(view.viewOver0Ad), 0)   as viewOver0Ad,
        <choose>
            <when test="dimension == 'optimizer_day' ">
                date(rpt.day_time) AS day,
            </when>
            <when test="dimension == 'optimizer_month' ">
                to_char(rpt.day_time, 'yyyy-MM') AS month,
            </when>
        </choose>
        count(material.signature)        AS newMaterialCount,
        coalesce(round(sum(rpt.cost), 2), 0) AS cost,
        sum(rpt.viewNum)                     as viewNum,
        sum(rpt.clickNum)                    AS clickNum,
        sum(rpt.convertNum)                  as convertNum,
        sum(rpt.registerNum)                 as registerNum,
        sum(rpt.activeNum)                   as activeNum,
        sum(rpt.payBehaviorNum)              as payBehaviorNum,
        sum(rpt.playNum)                     as playNum,
        sum(rpt.videoPlay100Num)             as videoPlay100Num,
        sum(rpt.orderNum)                    as orderNum,
        sum(rpt.deepConvertNum)              as deepConvertNum,
        sum(rpt.shareNum)                    as shareNum,
        sum(rpt.praiseNum)                   as praiseNum,
        sum(rpt.commentNum)                  as commentNum,
        sum(rpt.downloadNum)                 as downloadNum,
        sum(rpt.payBehaviorCost)             as payBehaviorCost,
        sum(rpt.validPlayNum)                as validPlayNum,
        sum(rpt.videoPlay25Num)              as videoPlay25Num,
        sum(rpt.videoPlay50Num)              as videoPlay50Num,
        sum(rpt.videoPlay75Num)              as videoPlay75Num,
        sum(rpt.avgPlayTime)                 as avgPlayTime
        FROM uu
        JOIN report rpt
        ON rpt.optimizer_id = uu.id
        LEFT JOIN material
        ON rpt.signature = material.signature and material.optimizer_id = rpt.optimizer_id
        LEFT JOIN opt
        ON rpt.optimizer_id = opt.optimizer_id and rpt.account_id = opt.account_id
        and rpt.day_time = opt.dayTime
        left JOIN adg
        ON rpt.optimizer_id = adg.optimizer_id
        and rpt.account_id = adg.account_id
        and rpt.day_time = adg.daytime
        JOIN view
        ON rpt.optimizer_id = view.optimizer_id
        and rpt.account_id = view.account_id
        and rpt.day_time = view.dayTime
        ${ew.customSqlSegment}
        ) opt
        join act
        ON opt.optimizerId = act.optimizer_id
        <if test="dimension == 'optimizer_day'">
            GROUP BY day
        </if>
        <if test="dimension == 'optimizer_month'">
            GROUP BY month
        </if>
        <if test="dimension == 'optimizer'">
            GROUP BY opt.optimizerId
        </if>
    </select>

</mapper>
