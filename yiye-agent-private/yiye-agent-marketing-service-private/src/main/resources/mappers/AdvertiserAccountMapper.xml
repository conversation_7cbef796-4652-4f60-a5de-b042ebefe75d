<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.yiye.agent.marketing.mapper.AdvertiserAccountMapper">
    <update id="resetOptimizerId">
        update marketing_advertiser_account a
        set optimizer_id = t.optimizer_id
        from (select distinct account.id, min(user_id) over (partition by advertiser_account_id) optimizer_id
        from marketing_advertiser_account account
        LEFT JOIN marketing_advertiser_account_rel rel ON account.id = rel.user_id
        where account.optimizer_id in
        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>) t
        where t.id = a.id;
    </update>

    <select id="pageSubPermission" resultType="ai.yiye.agent.domain.AdvertiserAccount">
        WITH filter_account AS (SELECT ac.id,
        ac.account_id,
        ac.account_name,
        array_remove(array_agg(distinct (aag.name || '--@-—' ||  aag.replace_operation)), null) as advertiser_account_group_names,
        array_remove(array_agg(distinct aag.id), null) as advertiser_account_group_ids,
        ac.platform_name,
        ac.account_status,
        ac.platform_id,
        ac.corporation_name,
        ac.target,
        ac.valid,
        ac.extract_flag,
        ac.optimizer_id,
        ac.system_status,
        ac.created_at,
        ac.updated_at,
        ac.balance,
        ac.industry,
        ac.rebates,
        ac.data_init_status,
        max(COALESCE(re.pv, 0))                                  pv,
        max(COALESCE(re.click, 0))                               click,
        max(COALESCE(re.click_rate, 0))                          click_rate,
        max(COALESCE(re.CONVERT, 0))                             CONVERT,
        max(COALESCE(re.spend, 0))                               spend,
        max(COALESCE(re.deep_convert, 0))                    deep_convert,
        max(COALESCE(re.convert_rate, 0))                        convert_rate,
        max(COALESCE(re.convert_cost, 0))                        convert_cost,
        max(COALESCE(re.deep_convert_rate, 0))                   deep_convert_rate,
        max(COALESCE(re.deep_convert_cost, 0))                   deep_convert_cost
        FROM marketing_advertiser_account ac
        LEFT JOIN
        (
        select
        t1.id as advertiser_account_id,
        t2.pv,
        t2.click,
        t2.deep_convert,
        t2.click_rate,
        t2.CONVERT,
        t2.spend,
        t2.convert_rate,
        t2.convert_cost,
        t2.deep_convert_rate,
        t2.deep_convert_cost
        from
        marketing_advertiser_account t1
        join
        (
        SELECT
        r.account_id as account_id,
        SUM(r.view_num)         AS pv,
        SUM(r.click_num)        AS click,
        sum(r.deep_convert_num) as deep_convert,
        CASE WHEN SUM(r.view_num) = CAST(0 AS NUMERIC) THEN 0 ELSE round((SUM(r.click_num) / SUM(r.view_num) * 100)::NUMERIC, 4) END AS click_rate,
        SUM(r.convert_num)      AS CONVERT,
        SUM(r.COST)      AS spend,
        CASE WHEN SUM(r.click_num) = CAST(0 AS NUMERIC) THEN 0 ELSE round((SUM(r.convert_num) / SUM(r.click_num) * 100)::NUMERIC, 4) END AS convert_rate,
        CASE WHEN SUM(r.convert_num) = CAST(0 AS NUMERIC) THEN 0 ELSE round((SUM(r.cost) / SUM(r.convert_num))::NUMERIC, 4) END AS convert_cost,
        CASE WHEN SUM(r.click_num) = CAST(0 AS NUMERIC) THEN 0 ELSE round((SUM(r.deep_convert_num) / SUM(r.click_num) * 100)::NUMERIC, 4) END AS deep_convert_rate,
        CASE WHEN SUM(r.deep_convert_num) = CAST(0 AS NUMERIC) THEN 0 ELSE round((SUM(r.cost) / SUM(r.deep_convert_num) / 100)::NUMERIC, 4) END AS deep_convert_cost
        FROM advertise_statistic_report r
        WHERE 1=1
        <if test="report.startTime != null and report.endTime != null">
            and r.statistic_time between to_timestamp(#{report.startTime}, 'yyyy-MM-dd hh24:mi:ss') and to_timestamp(#{report.endTime}, 'yyyy-MM-dd hh24:mi:ss')
        </if>
        and r.statistic_caliber = 0
        GROUP BY
        r.account_id
        ) t2 on t1.account_id = t2.account_id
        ) re
        ON ac.id = re.advertiser_account_id
        left join marketing_advertiser_account_group_rel maagr on ac.id = maagr.advertiser_account_id
        left join marketing_advertiser_account_group aag on maagr.advertiser_account_group_id = aag.id
        ${ew.customSqlSegment}
        group by ac.id
        ),
        account AS (
        select distinct id
        from (
        SELECT fa.id
        FROM filter_account fa
        JOIN marketing_advertiser_account_rel maaur ON maaur.advertiser_account_id = fa.id
        <if test="manager != null">where maaur.user_id in
            <foreach collection="
            manager.userManager" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        )fa),
        manager AS (
        SELECT array_to_json(array_agg(uu.username)) AS manager_name,
        maar.advertiser_account_id AS advertiser_account_id
        FROM marketing_advertiser_account fa
        JOIN marketing_advertiser_account_rel maar ON fa.id = maar.advertiser_account_id
        JOIN ucenter_user uu ON uu.id = maar.user_id
        GROUP BY advertiser_account_id
        )
        SELECT a.*,m.manager_name
        FROM filter_account a
        LEFT JOIN manager m ON m.advertiser_account_id = a.id
        where a.id in (select id from account)
        <if test="exist != null and exist and advertiserAccountGroupId != null">
            and #{advertiserAccountGroupId} = any (a.advertiser_account_group_ids)
        </if>
        <if test="exist != null and !exist and advertiserAccountGroupId != null">
            and not (#{advertiserAccountGroupId} = any (a.advertiser_account_group_ids))
        </if>
        order by
        <if test="report.sort != null and report.sort=='pv' and report.order != null">
            a.pv ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='click' and report.order != null">
            a.click ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='spend' and report.order != null">
            a.spend ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='clickRate' and report.order != null">
            a.click_rate ${report.order},
        </if>
          <if test="report.sort != null and report.sort=='convert' and report.order != null">
            a.convert ${report.order},
        </if>
          <if test="report.sort != null and report.sort=='convertRate' and report.order != null">
            a.convert_rate ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='convertCost' and report.order != null">
            a.convert_cost ${report.order},
        </if>
          <if test="report.sort != null and report.sort=='deepConvert' and report.order != null">
            a.deep_convert ${report.order},
        </if>
          <if test="report.sort != null and report.sort=='deepConvertRate' and report.order != null">
            a.deep_convert_rate ${report.order},
        </if>
          <if test="report.sort != null and report.sort=='deepConvertCost' and report.order != null">
            a.deep_convert_cost ${report.order},
        </if>
        a.account_id desc
    </select>


    <select id="pageAllPermission" resultType="ai.yiye.agent.domain.AdvertiserAccount">
        WITH filter_account AS (
        SELECT ac.id,
        ac.account_id,
        ac.account_name,
        array_remove(array_agg(distinct (aag.name || '--@-—' ||  aag.replace_operation)), null) as advertiser_account_group_names,
        array_remove(array_agg(distinct aag.id), null) as advertiser_account_group_ids,
        ac.platform_name,
        ac.account_status,
        ac.platform_id,
        ac.corporation_name,
        ac.target,
        ac.valid,
        ac.extract_flag,
        ac.optimizer_id,
        ac.system_status,
        ac.created_at,
        ac.updated_at,
        ac.balance,
        ac.industry,
        ac.rebates,
        ac.data_init_status,
        max(COALESCE(re.pv, 0))                                  pv,
        max(COALESCE(re.click, 0))                               click,
        max(COALESCE(re.click_rate, 0))                          click_rate,
        max(COALESCE(re.CONVERT, 0))                             CONVERT,
        max(COALESCE(re.spend, 0))                               spend,
        max(COALESCE(re.deep_convert, 0))                    deep_convert,
        max(COALESCE(re.convert_rate, 0))                        convert_rate,
        max(COALESCE(re.convert_cost, 0))                        convert_cost,
        max(COALESCE(re.deep_convert_rate, 0))                   deep_convert_rate,
        max(COALESCE(re.deep_convert_cost, 0))                   deep_convert_cost
        FROM marketing_advertiser_account ac
        LEFT JOIN
        (
        select
        t1.id as advertiser_account_id,
        t2.pv,
        t2.click,
        t2.deep_convert,
        t2.click_rate,
        t2.CONVERT,
        t2.spend,
        t2.convert_rate,
        t2.convert_cost,
        t2.deep_convert_rate,
        t2.deep_convert_cost
        from
        marketing_advertiser_account t1
        join
        (
        SELECT
        r.account_id as account_id,
        SUM(r.view_num)         AS pv,
        SUM(r.click_num)        AS click,
        sum(r.deep_convert_num) as deep_convert,
        CASE WHEN SUM(r.view_num) = CAST(0 AS NUMERIC) THEN 0 ELSE round((SUM(r.click_num) / SUM(r.view_num) * 100)::NUMERIC, 4) END AS click_rate,
        SUM(r.convert_num)      AS CONVERT,
        SUM(r.COST)       AS spend,
        CASE WHEN SUM(r.click_num) = CAST(0 AS NUMERIC) THEN 0 ELSE round((SUM(r.convert_num) / SUM(r.click_num) * 100)::NUMERIC, 4) END AS convert_rate,
        CASE WHEN SUM(r.convert_num) = CAST(0 AS NUMERIC) THEN 0 ELSE round((SUM(r.cost) / SUM(r.convert_num))::NUMERIC, 4) END AS convert_cost,
        CASE WHEN SUM(r.click_num) = CAST(0 AS NUMERIC) THEN 0 ELSE round((SUM(r.deep_convert_num) / SUM(r.click_num) * 100)::NUMERIC, 4) END AS deep_convert_rate,
        CASE WHEN SUM(r.deep_convert_num) = CAST(0 AS NUMERIC) THEN 0 ELSE round((SUM(r.cost) / SUM(r.deep_convert_num) / 100)::NUMERIC, 4) END AS deep_convert_cost
        FROM advertise_statistic_report r
        WHERE 1=1
        <if test="report.startTime != null and report.endTime != null">
            and r.statistic_time between to_timestamp(#{report.startTime}, 'yyyy-MM-dd hh24:mi:ss') and to_timestamp(#{report.endTime}, 'yyyy-MM-dd hh24:mi:ss')
        </if>
        and r.statistic_caliber = 0
        GROUP BY
        r.account_id
        ) t2 on t1.account_id = t2.account_id
        ) re
        ON ac.id = re.advertiser_account_id
        left join marketing_advertiser_account_group_rel maagr on ac.id = maagr.advertiser_account_id
        left join marketing_advertiser_account_group aag on maagr.advertiser_account_group_id = aag.id
        ${ew.customSqlSegment}
        group by ac.id
        ),
        manager AS (
        SELECT array_to_json(array_agg(uu.username)) AS manager_name,
        maar.advertiser_account_id AS advertiser_account_id
        FROM marketing_advertiser_account fa
        JOIN marketing_advertiser_account_rel maar ON fa.id = maar.advertiser_account_id
        JOIN ucenter_user uu ON uu.id = maar.user_id
        GROUP BY advertiser_account_id
        )
        SELECT a.*, m.manager_name
        FROM filter_account a
        LEFT JOIN manager m ON m.advertiser_account_id = a.id
        <where>
            <if test="exist != null and exist and advertiserAccountGroupId != null">
                #{advertiserAccountGroupId} = any (a.advertiser_account_group_ids)
            </if>
            <if test="exist != null and !exist and advertiserAccountGroupId != null">
                not (#{advertiserAccountGroupId} = any (a.advertiser_account_group_ids))
            </if>
        </where>
        order by
        <if test="report.sort != null and report.sort=='pv' and report.order != null">
            a.pv ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='click' and report.order != null">
            a.click ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='spend' and report.order != null">
            a.spend ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='clickRate' and report.order != null">
            a.click_rate ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='convert' and report.order != null">
            a.convert ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='convertRate' and report.order != null">
            a.convert_rate ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='convertCost' and report.order != null">
            a.convert_cost ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='deepConvert' and report.order != null">
            a.deep_convert ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='deepConvertRate' and report.order != null">
            a.deep_convert_rate ${report.order},
        </if>
        <if test="report.sort != null and report.sort=='deepConvertCost' and report.order != null">
            a.deep_convert_cost ${report.order},
        </if>
        a.created_at desc, a.account_id desc
    </select>

    <select id="getAdvertiserAccountIds" resultType="java.lang.Long">
        SELECT advertiser_account_id from marketing_advertiser_account_group_rel WHERE advertiser_account_group_id = #{accountGroupId}
    </select>

    <select id="getIndustry" resultType="java.lang.String">
        SELECT DISTINCT industry from marketing_advertiser_account ${ew.customSqlSegment}
    </select>

    <update id="updateIndustry">
        UPDATE marketing_advertiser_account SET industry = #{report.industry} WHERE id = #{report.id}
    </update>

    <select id="relate" resultType="ai.yiye.agent.marketing.vo.OptimizerVO">
        select distinct u.username as optimizerName,u.id from ucenter_user u join marketing_advertiser_account m on u.id = m.optimizer_id where role = 1 order by ${sort} ${order}
    </select>
    <select id="getAccountIdByUploadConfig" resultType="ai.yiye.agent.domain.AdvertiserAccount">
        select * from marketing_advertiser_account where account_id  in (select DISTINCT account_id from landing_page_upload_configuration)


    </select>

    <update id="batchUpdateSystemStatus">
        update marketing_advertiser_account set system_status = #{status}, extract_flag = #{extractFlag} ,
        is_extract = #{isExtract} where id in (
        <foreach collection="advertiserAccountIds" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <update id="updateAllSystemStatus">
        update marketing_advertiser_account set system_status = 0, extract_flag = false, is_extract = false
    </update>
    <update id="updateAdvertiserAccountBillingStop">
        update marketing_advertiser_account set billing_mode_status=0 where platform_id =#{account.platformId} and account_id in (
        <foreach collection="account.accountIds" item="id" index="index" separator=",">
            #{id}
        </foreach>
            )
    </update>
    <update id="updateAdvertiserAccountBillingStart">
        update marketing_advertiser_account set billing_mode_status=1 where platform_id =#{account.platformId} and account_id in (
        <foreach collection="account.accountIds" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </update>
    <update id="updateTokenByAuthUSerId">
        update marketing_advertiser_account set access_token =#{accessToken},refresh_token =#{refreshToken},updated_at =now() where auth_user_id=#{authUserId}
    </update>
    <update id="updateBaiDuTokenByAccountId">
        update marketing_advertiser_account set access_token =#{accessToken},refresh_token =#{refreshToken},valid = 1,updated_at =now() where account_id=#{accountId}
    </update>

    <select id="getAccountDetailByAuthUserId" resultType="ai.yiye.agent.domain.AdvertiserAccount">
        select auth_user_id,max(access_token) as access_token,max(refresh_token) as refresh_token,min(platform_id) as platform_id,min(valid) as valid from marketing_advertiser_account where platform_id = #{platformId} group by auth_user_id
    </select>
    <select id="selectBaiDuList" resultType="ai.yiye.agent.domain.AdvertiserAccount">
        select account_id,refresh_token,account_name,max(id) as id,max(platform_id) as platform_id from marketing_advertiser_account where platform_id in (5,6) and valid =1  group by account_id,refresh_token,account_name


    </select>

    <select id="getRelatedAccountCount" resultType="ai.yiye.agent.marketing.vo.AdvertiseVO">
        SELECT
        t1.advertiser_account_group_id as advertiser_account_group_id,
        COUNT ( 1 ) as count
        FROM
        marketing_advertiser_account_group_rel t1
        JOIN marketing_advertiser_account t2
        ON t1.advertiser_account_group_id IN
        <foreach collection="advertiserAccountGroupIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND t2.agency = FALSE
        AND t1.advertiser_account_id = t2.ID
        AND t2.account_name is not null
        AND t2.account_name != ''
        GROUP BY
        t1.advertiser_account_group_id;
    </select>

</mapper>
