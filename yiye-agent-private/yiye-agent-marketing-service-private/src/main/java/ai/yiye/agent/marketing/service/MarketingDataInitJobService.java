package ai.yiye.agent.marketing.service;


import ai.yiye.agent.domain.AdvertiserAccount;
import ai.yiye.agent.domain.MarketingDataInitJob;
import ai.yiye.agent.domain.User;
import ai.yiye.agent.domain.dto.AccountMessageDto;
import ai.yiye.agent.domain.enumerations.AdvertiserAccountDataInitFinishStatus;
import ai.yiye.agent.domain.enumerations.AdvertiserAccountDataInitStatus;
import ai.yiye.agent.domain.enumerations.AdvertiserAccountDataInitType;
import ai.yiye.agent.domain.enumerations.AdvertiserAccountSystemStatus;
import ai.yiye.agent.marketing.mapper.AdvertiserAccountMapper;
import ai.yiye.agent.marketing.mapper.MarketingDataInitJobMapper;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/23 16:48
 */

@Service
@DS("postgresql")
@Slf4j
public class MarketingDataInitJobService extends ServiceImpl<MarketingDataInitJobMapper, MarketingDataInitJob> {
    @Autowired
    private MarketingDataInitJobMapper marketingDataInitJobMapper;
    @Autowired
    private StreamBridge streamBridge;
    @Autowired
    private AdvertiserAccountMapper advertiserAccountMapper;


    /**
     * 数据拉取交换机
     */
    private static final String TOPIC_NAME = "yiye-agent-private.marketing-data-collect-base-data-init.exchange";

    public void saveInitDataJob(List<JSONObject> accountList, Integer initType, User user) {
        List<MarketingDataInitJob> collect = accountList.stream().map(s -> {
            return new MarketingDataInitJob().setAccountId(s.getString("accountId")).setPlatformId(s.getInteger("platformId")).setInitType(AdvertiserAccountDataInitType.getById(initType)).setEmail(user.getEmail()).setIsFinish(AdvertiserAccountDataInitFinishStatus.UNFINISHED);
        }).collect(Collectors.toList());
        collect.forEach(s -> marketingDataInitJobMapper.insertOrUpdate(s));
        accountList.forEach(account -> {
            String accountId = account.getString("accountId");
            Integer platformId = account.getInteger("platformId");
            AdvertiserAccount advertiserAccount = advertiserAccountMapper.selectOne(new LambdaQueryWrapper<AdvertiserAccount>().eq(AdvertiserAccount::getAccountId, accountId).eq(AdvertiserAccount::getPlatformId, platformId));
            if (advertiserAccount!=null) {
                advertiserAccountMapper.updateById(advertiserAccount.setExtractFlag(true).setSystemStatus(AdvertiserAccountSystemStatus.NORMAL).setDataInitStatus(AdvertiserAccountDataInitStatus.INITIALIZED_START));
                if (initType.equals(0)) {
                    log.info("==================<<初始化【1天】数据开始进行中...>>===================");
                    log.info("=======accountId: {}==========platformId: {}", account, platformId);
                    AccountMessageDto messageData = toDto(advertiserAccount.setInitDataType(AdvertiserAccountDataInitType.ONE_DAY), user.getEmail());
                    Optional.ofNullable(messageData)
                        .ifPresent(ms -> streamBridge.send(TOPIC_NAME, messageData));
                }
            }
        });
    }

    /**
     * 更新任务
     */
    public void updateInitDataJob(String accountId,Integer platformId){
        MarketingDataInitJob marketingDataInitJob = marketingDataInitJobMapper.selectOne(new LambdaQueryWrapper<MarketingDataInitJob>().eq(MarketingDataInitJob::getAccountId, accountId).eq(MarketingDataInitJob::getPlatformId, platformId));
        marketingDataInitJob.setIsFinish(AdvertiserAccountDataInitFinishStatus.FINISH);
        marketingDataInitJobMapper.updateById(marketingDataInitJob);
    }

    private AccountMessageDto toDto(AdvertiserAccount account,String email) {
        AccountMessageDto dto = new AccountMessageDto();
        dto.setAdvertiserAccount(account);
        dto.setAccountId(account.getAccountId());
        dto.setEmail(email);
        return dto;
    }
    private AccountMessageDto toDto(AdvertiserAccount account, LocalDateTime date) {
        AccountMessageDto dto = new AccountMessageDto();
        dto.setAccountId(account.getAccountId());
        dto.setAdvertiserAccount(account);
        List<LocalDateTime> list = new ArrayList<>();
        list.add(date);
        dto.setExtractDates(list);
        return dto;
    }
}
