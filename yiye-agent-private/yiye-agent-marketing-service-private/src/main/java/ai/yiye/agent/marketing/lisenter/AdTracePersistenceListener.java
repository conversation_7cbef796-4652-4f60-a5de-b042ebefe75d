package ai.yiye.agent.marketing.lisenter;

import ai.yiye.agent.autoconfigure.batch.BatchConsumer;
import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.security.jwt.JwtTokenGenerator;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AdTrace;
import ai.yiye.agent.domain.TraceAddress;
import ai.yiye.agent.domain.dto.BusinessTargetStrDto;
import ai.yiye.agent.domain.dto.TraceCountDto;
import ai.yiye.agent.marketing.service.AdTraceAddressService;
import ai.yiye.agent.marketing.service.AdTraceService;
import ai.yiye.agent.marketing.service.AdvertiserGroupService;
import ai.yiye.agent.marketing.service.TraceForwardParseService;
import ai.yiye.agent.marketing.util.HandlerThreadPool;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("AdTracePersistence")
public class AdTracePersistenceListener implements Consumer<AdTrace> {

    @Autowired
    private AdTraceService adTraceService;
    @Autowired
    private JwtTokenGenerator jwtTokenGenerator;
    @Autowired
    private AdTraceAddressService adTraceAddressService;
    @Autowired
    private AdvertiserGroupService advertiserGroupService;
    @Autowired
    private TraceForwardParseService traceForwardParseService;

    private final BatchConsumer<AdTrace> batchConsumer = new BatchConsumer<>("AdTracePersistence", this::saveBatch);
    @Autowired
    private RedisTemplate<String, Object> objectRedisTemplate;



    /**
     * 处理ad-trace消息.
     * 1. 消息内部+db人群归因.
     * 2. 入库.
     * 3. 同步私域.
     */
    @Override
    public void accept(AdTrace adTrace) {
        log.info("{}", adTrace);
        Optional<SFunction<AdTrace, String>> deviceFunction = Stream.<SFunction<AdTrace, String>>of(AdTrace::getIdfaMd5, AdTrace::getOaid, AdTrace::getImeiMd5)
            .filter(f -> Objects.nonNull(f.apply(adTrace)))
            .findFirst();
        if (!deviceFunction.isPresent()) {
            // 设备标识号均为空
            adTrace.setDeviceId(null);
        } /*else {
            // 使用库里已有的deviceId标识同一个人
            SFunction<AdTrace, String> f = deviceFunction.get();
            LambdaQueryWrapper<AdTrace> queryWrapper = new LambdaQueryWrapper<AdTrace>().select(AdTrace::getDeviceId).eq(f, f.apply(adTrace));
            AdTrace adTraceFromDb = adTraceService.findOne(adTrace.getYiyeAgentId(), queryWrapper);
            if (Objects.nonNull(adTraceFromDb)) {
                adTrace.setDeviceId(adTraceFromDb.getDeviceId());
            }
        }*/
        batchConsumer.accept(adTrace);
        TenantContextHolder.clearContext();

    }

    public void saveBatch(List<AdTrace> adTraces) {
        if (CollectionUtils.isEmpty(adTraces)) {
            return;
        }
        //这里加一个循环，对所有的adtrace增加广告数据。包括账户id和广告id进行定位到账户，返回广告的年龄str 学历str和性别
        for (int i = 0; i < adTraces.size(); i++) {
            try {
                AdTrace adTrace = adTraces.get(i);
                BusinessTargetStrDto businessTargetStrDto = advertiserGroupService.getTargetStr(adTrace.getAccountId(),adTrace.getAdgroupId());
                adTrace.setAge(businessTargetStrDto.getAgeStr());
                adTrace.setEducation(businessTargetStrDto.getEducation());
                adTrace.setSex(businessTargetStrDto.getSex());
                String uuid = adTrace.getUuid();
                String key=RedisConstant.TRACE_FORWARD+uuid;
                Object o = objectRedisTemplate.opsForValue().get(key);
                if(o==null){
                    TraceAddress one = adTraceAddressService.getOne(new LambdaQueryWrapper<TraceAddress>().eq(TraceAddress::getLinkId, uuid).last("limit 1"));
                    if(one==null){
                        continue;
                    }else{
                        adTrace.setTraceAddressId(one.getId());
                        objectRedisTemplate.opsForValue().set(RedisConstant.TRACE_FORWARD+uuid,JSONObject.toJSONString(one),1, TimeUnit.DAYS);
                    }
                }else{
                    TraceAddress traceAddress = JSON.parseObject(o.toString(), TraceAddress.class);
                    adTrace.setTraceAddressId(traceAddress.getId());
                }
            }catch (Exception e){
                log.error(e.getMessage(),e);
            }
        }

        //放在这里的好处是可以作为异步请求，一次入库
        adTraces
            .parallelStream()
            .filter(e -> StringUtils.isNotBlank(e.getYiyeAgentId()))
            .filter(e->StringUtils.isNotBlank(e.getAccountId()))
            .collect(Collectors.groupingBy(AdTrace::getYiyeAgentId))
            .forEach((agentId, adTracesToPersist) -> {
                HandlerThreadPool.execute(() -> {
                    TenantContextHolder.set(adTracesToPersist.get(0).getYiyeAgentId());
//                    AgentConf agentConf = agentConfService.findByAgentId(agentId);
                    try {
                        // 入库
                        adTraceService.saveBatch(adTracesToPersist);
//                        // 同步私域
//                        Lists.partition(adTracesToPersist, 30).stream().forEach(list -> {
//                            marketingRemote.saveAdTrace(URI.create(agentConf.getHost()), list, jwtTokenGenerator.generateToken(agentId));
//                        });
                    } catch (Exception e) {
                        log.error("adtrace save error !! agentId {} massage {} ", JSONObject.toJSONString(adTracesToPersist),e);
//                        log.error(e.getMessage(),e);
                    }
                });
            });
        //为每条链接进行计数
        Map<String, List<AdTrace>> collect = adTraces.stream().
            filter(e -> StringUtils.isNotBlank(e.getUuid())).
            collect(Collectors.groupingBy(AdTrace::getUuid));
        //分组后计数
        for (Map.Entry<String, List<AdTrace>> sites : collect.entrySet()) {
            TraceCountDto traceCountDto = new TraceCountDto();
            traceCountDto.setYiyeAgentId(sites.getValue().get(0).getYiyeAgentId());
            traceCountDto.setLinkId(sites.getKey());
            traceCountDto.setCount(sites.getValue().size());
            //计数计到redis中，然后用缓存计数
            traceForwardParseService.countTraceNum(traceCountDto);
        }


    }

}
