package ai.yiye.agent.marketing.service;

import ai.yiye.agent.autoconfigure.web.exception.ErrorConstants;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.*;
import ai.yiye.agent.domain.bo.AdvertiserAccountGroupReportBO;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.vo.AdvertiserAccountGroupReport;
import ai.yiye.agent.domain.vo.AdvertiserAccountGroupReportCliskhouse;
import ai.yiye.agent.domain.vo.AdvertiserAccountGroupReportVo;
import ai.yiye.agent.marketing.config.MarketingNewSearchConfig;
import ai.yiye.agent.marketing.dto.*;
import ai.yiye.agent.marketing.mapper.*;
import ai.yiye.agent.marketing.remote.LandingPageRemote;
import ai.yiye.agent.marketing.sender.LandingPageSender;
import ai.yiye.agent.marketing.service.readonly.SmsSendLogReadonlyService;
import ai.yiye.agent.marketing.util.CalculationUtil;
import ai.yiye.agent.marketing.util.TimeUtil;
import ai.yiye.agent.marketing.vo.AdvertiseVO;
import ai.yiye.agent.marketing.vo.ProjectMemberPermissionTreeVO;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@DS("postgresql")
public class AdvertiserAccountGroupService extends ServiceImpl<AdvertiserAccountGroupMapper, AdvertiserAccountGroup> {

    @Autowired
    private AdvertiserAccountService advertiserAccountService;

    @Autowired
    private AdvertiserAccountGroupMapper advertiserAccountGroupMapper;

    @Autowired
    private AdvertiserAccountGroupRelMapper advertiserAccountGroupRelMapper;

    @Autowired
    private AdvertiserAccountGroupUserRelMapper advertiserAccountGroupUserRelMapper;

    @Autowired
    private AdvertiserAccountGroupTargetFieldMapper advertiserAccountGroupTargetFieldMapper;

    @Autowired
    private GroupTargetDailyReportMapper groupTargetDailyReportMapper;

    @Autowired
    private DataSolidificationMapper dataSolidificationMapper;

    @Autowired
    private CustomerFieldService customerFieldService;

    @Autowired
    private CustomerCalFormulaService calFormulaService;

    @Autowired
    private LandingPageRemote landingPageRemote;

    @Autowired
    private MarketingNewSearchConfig marketingNewSearchConfig;

    public static final String newSearchOpenFlag = "new-search";

    @Resource
    private MarketingAdvertiserAccountPermissionService marketingAdvertiserAccountPermissionService;

    @Resource
    private AdvertiserProjectService advertiserProjectService;

    @Resource
    private AdvertiserAccountGroupRelService advertiserAccountGroupRelService;


    private final static DateTimeFormatter FORMATTER_LOCAL_DATE = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final static DateTimeFormatter FORMATTER_LOCAL_DATE_TIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Autowired
    private LaunchManageService launchManageService;

    @Resource
    private  ProjectOverviewLaunchService projectOverviewLaunchService;

    @Resource
    private ProjectOverviewLaunchClickhouseService projectOverviewLaunchClickhouseService;

    @Resource
    private MarketingProjectAdGapCostDeviationDayReportService marketingProjectAdGapCostDeviationDayReportService;
    @Autowired
    private SmsSendLogReadonlyService smsSendLogReadonlyService;
    @Autowired
    private LandingPageSender landingPageSender;
    @Autowired
    private AdvertiserAccountGroupChangeRecordService advertiserAccountGroupChangeRecordService;




    @Transactional(rollbackFor = Exception.class)
    public AdvertiserAccountGroup saveAdvertiserAccountGroup(AdvertiserAccountGroup accountGroup) {
        Integer count = baseMapper.selectCount(new LambdaQueryWrapper<AdvertiserAccountGroup>()
            .eq(AdvertiserAccountGroup::getName, accountGroup.getName()));
        if (count > 0) {
            throw new RestException(ErrorConstants.ERROR_ENTITY_REPEATED);
        }

        List<String> addedValue=new ArrayList<>();
        //需加上默认值
        addedValue.add(WhiteType.NOT_SHOW_TECHNICAL_SUPPORT.getValue());
        addedValue.add(WhiteType.UPLOAD_MANAGEMENT.getValue());
        addedValue.add(WhiteType.OPEN_WECHAT_ROBOT_CUSTOMER.getValue());
        addedValue.add(WhiteType.CUSTOMER_ACQUISITION.getValue());
        accountGroup.setWhiteTypes(addedValue.toArray(new String[]{}));

        baseMapper.insert(accountGroup);
        if (accountGroup.getManagerList() != null && !Arrays.asList(accountGroup.getManagerList()).isEmpty()) {
            Arrays.asList(accountGroup.getManagerList()).forEach(userId -> {
                AdvertiserAccountGroupUserRel advertiserAccountGroupUserRel = new AdvertiserAccountGroupUserRel();
                advertiserAccountGroupUserRel.setUserId(userId).setAdvertiserAccountGroupId(accountGroup.getId());
                advertiserAccountGroupUserRelMapper.insert(advertiserAccountGroupUserRel);
            });
        }
        //这里新增设置账户方法

        if (accountGroup.getAdvertiserAccountIds() != null && !Arrays.asList(accountGroup.getAdvertiserAccountIds()).isEmpty()) {
            Arrays.asList(accountGroup.getAdvertiserAccountIds()).forEach(id -> {
                AdvertiserAccountGroupRel advertiserAccountGroupRel = new AdvertiserAccountGroupRel();
                advertiserAccountGroupRel.setAdvertiserAccountGroupId(accountGroup.getId())
                    .setAdvertiserAccountId(id);
                advertiserAccountGroupRelMapper.saveOrUpdateGroupRelate(advertiserAccountGroupRel);
            });
        }


        return accountGroup;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteMarketingAdvertiserAccountGroup(Long id) {
        baseMapper.deleteById(id);
        // 移除项目与投放账户的关系
        advertiserAccountGroupRelMapper.delete(new LambdaQueryWrapper<AdvertiserAccountGroupRel>().eq(AdvertiserAccountGroupRel::getAdvertiserAccountGroupId, id));
        // 移除项目与人员的关系
        advertiserAccountGroupUserRelMapper.delete(new LambdaQueryWrapper<AdvertiserAccountGroupUserRel>().eq(AdvertiserAccountGroupUserRel::getAdvertiserAccountGroupId, id));
    }


    public void batchDeleteGroupUserRel(List<Long> ids) {
        advertiserAccountGroupUserRelMapper.delete(new LambdaQueryWrapper<AdvertiserAccountGroupUserRel>().in(AdvertiserAccountGroupUserRel::getUserId, ids));
    }

    @Transactional(rollbackFor = Exception.class)
    public AdvertiserAccountGroup updateAdvertiserAccountGroup(Long id, AdvertiserAccountGroup accountGroup) {
        log.info("修改项目管理的目标值,项目ID = {}", id);
        AdvertiserAccountGroup byId = this.getById(id);
        if (Objects.isNull(byId)) {
            throw new RestException(ErrorConstants.ERROR_CONDITION_UPDATE);
        }
        Integer count = baseMapper.selectCount(new LambdaQueryWrapper<AdvertiserAccountGroup>()
            .eq(AdvertiserAccountGroup::getName, accountGroup.getName())
            .ne(AdvertiserAccountGroup::getId, accountGroup.getId()));
        if (count > 0) {
            throw new RestException(ErrorConstants.ERROR_ENTITY_REPEATED);
        }

        // 判断是否存在重名的客户信息
        baseMapper.updateById(accountGroup);

        // 如果修改了目标，则更新缺口和成本值
        GroupTargetDailyReport report = getGroupTarget(accountGroup);
        List<GroupTargetDailyReport> list = new ArrayList<>();
        Optional.ofNullable(report).ifPresent(list::add);
        if (CollectionUtils.isNotEmpty(list)) {
            groupTargetDailyReportMapper.saveOrUpdateBatch(list);
        }
        return accountGroup;
    }

    /**
     * 修改目标值的时候，更新当天的数据
     * @param advertiserAccountGroupId 项目ID
     * @param accountGroup 项目信息
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AdvertiserAccountGroup updateAdvertiserAccountGroupNew(Long advertiserAccountGroupId, AdvertiserAccountGroup accountGroup) {
        log.info("2.0版本,修改项目管理的目标值,项目ID = {}", advertiserAccountGroupId);
        AdvertiserAccountGroup advertiserAccountGroup = this.getById(advertiserAccountGroupId);
        if (Objects.isNull(advertiserAccountGroup)) {
            throw new RestException(ErrorConstants.ERROR_CONDITION_UPDATE);
        }
        Integer count = baseMapper.selectCount(new LambdaQueryWrapper<AdvertiserAccountGroup>()
            .eq(AdvertiserAccountGroup::getName, accountGroup.getName())
            .ne(AdvertiserAccountGroup::getId, accountGroup.getId()));
        if (count > 0) {
            throw new RestException(ErrorConstants.ERROR_ENTITY_REPEATED);
        }

        // 判断是否存在重名的客户信息
        baseMapper.updateById(accountGroup);

        List<MarketingProjectAdGapCostDeviationDayReport> groupTargetDailyReports = new ArrayList<>();
        //这里要修改四份数据
        List<Integer> searchTypeList = new ArrayList<>();
        //旧查询曝光口径
        searchTypeList.add(0);
        //新查询曝光口径
        searchTypeList.add(1);
        //旧查询转化口径
        searchTypeList.add(2);
        //新查询转化口径
        searchTypeList.add(3);

        log.info("最新的accountGroup = {}", com.alibaba.fastjson.JSONObject.toJSON(accountGroup));
        for (Integer searchType : searchTypeList) {
            String  dayTime =  LocalDate.now().toString();
            MarketingProjectAdGapCostDeviationDayReport dayReport = marketingProjectAdGapCostDeviationDayReportService.getGroupTarget(advertiserAccountGroupId, accountGroup, searchType, dayTime);
            log.info("修改目标值的时候，更新当天项目{}统计{}的缺口与成本偏差，searchType = {}", advertiserAccountGroup.getId(), dayTime, searchType);
            if (Objects.nonNull(dayReport)) {
                dayReport.setSearchType(searchType);
                dayReport.setTarget(accountGroup.getTarget());
                groupTargetDailyReports.add(dayReport);
            }
            if (CollectionUtils.isNotEmpty(groupTargetDailyReports)) {
                marketingProjectAdGapCostDeviationDayReportService.saveOrUpdateBatch(groupTargetDailyReports);
            }
        }
        return accountGroup;
    }



    @Transactional(rollbackFor = Exception.class)
    public List<AdvertiserAccountGroup> saveAccountGroupBatch(List<AdvertiserAccountGroup> accountGroups) {
        for (AdvertiserAccountGroup advertiserAccountGroup : accountGroups) {
            this.saveOrUpdate(advertiserAccountGroup);
        }
        return accountGroups;
    }

    /**
     * 根据条件获取客户的列表
     *
     * @param page   分页参数
     * @param filter 查询条件
     * @return 分页客户列表
     */
    public IPage<AdvertiserAccountGroup> getAdvertiserAccountGroupPage(User user, IPage<AdvertiserAccountGroup> page, QueryWrapper<AdvertiserAccountGroup> filter) {
        //移除自带的排序
        filter.getExpression().getOrderBy().clear();
        if (UserRole.ROLE_ADMIN.equals(user.getRole())) {
            //管理员账户查询所有项目
            return advertiserAccountGroupMapper.selectAdvertiserAccountGroupPage(page, filter, null);
        } else {
            return advertiserAccountGroupMapper.selectAdvertiserAccountGroupPage(page, filter, user.getId());
        }
    }

    public List<AdvertiserAccountGroup> selectAdvertiserAccountGroupList() {
        return advertiserAccountGroupMapper.selectAdvertiserAccountGroupList();
    }

    /**
     * 查询项目列表及对应分配的用户ID集合
     * @return
     */
    public List<AdvertiserAccountGroup> selectAdvertiserAccountGroupUserIds() {
        return advertiserAccountGroupMapper.selectAdvertiserAccountGroupUserIds();
    }

    public JSONArray consumerAccountGroups(Long groupId) {
        QueryWrapper<AdvertiserAccountGroup> groupQueryWrapper;
        QueryWrapper<AdvertiserAccount> accountQueryWrapper;
        if (null == groupId || groupId == 0L) {
            groupQueryWrapper = new QueryWrapper<>();
            accountQueryWrapper = new QueryWrapper<>();
        } else {
            groupQueryWrapper = new QueryWrapper<AdvertiserAccountGroup>().eq("id", groupId);
            accountQueryWrapper = new QueryWrapper<AdvertiserAccount>().eq("advertiser_account_group_id", groupId);
        }
        // 获取特定的客户列表
        List<AdvertiserAccountGroup> accountGroups = baseMapper.selectList(groupQueryWrapper);
        // 获取特定的投放账号列表
        List<AdvertiserAccount> accounts = advertiserAccountService.list(accountQueryWrapper);
        Map<Long, List<AdvertiserAccount>> aMap = new LinkedHashMap<>();
        accounts.forEach(account -> {
            Long advertiserAccountGroupId = account.getAdvertiserAccountGroupId();
            if (null != advertiserAccountGroupId && 0L != advertiserAccountGroupId) {
                if (!aMap.containsKey(advertiserAccountGroupId)) {
                    aMap.put(advertiserAccountGroupId, new ArrayList<>(Collections.singletonList(account)));
                } else {
                    aMap.get(advertiserAccountGroupId).add(account);
                }
            }
        });
        accountGroups.forEach(accountGroup -> {
            Long accountGroupId = accountGroup.getId();
            if (aMap.containsKey(accountGroupId)) {
                accountGroup.setAccounts(aMap.get(accountGroupId));
            }
        });
        return JSONArray.parseArray(JSONArray.toJSONString(accountGroups));
    }

    public List<AdvertiseVO> listWithCount(Long id) {
        List<Long> collect = advertiserAccountGroupUserRelMapper.selectList(new LambdaQueryWrapper<AdvertiserAccountGroupUserRel>()
            .eq(AdvertiserAccountGroupUserRel::getUserId, id)
        ).stream().map(AdvertiserAccountGroupUserRel::getAdvertiserAccountGroupId).collect(Collectors.toList());
        return advertiserAccountGroupMapper.listWithCount(collect);
    }

    public List<AdvertiserAccount> selectGroupAccountRelate(Long advertiserAccountGroupId, Platform platformId, Boolean agency) {
        return advertiserAccountGroupRelMapper.selectAccountList(advertiserAccountGroupId, platformId, agency);
    }

    public List<AdvertiserAccountGroupRel> selectAccountGroupRelate(Long advertiserAccountGroupId) {
        return advertiserAccountGroupRelMapper.selectList(new LambdaQueryWrapper<AdvertiserAccountGroupRel>()
            .eq(AdvertiserAccountGroupRel::getAdvertiserAccountGroupId, advertiserAccountGroupId));
    }

    public List<AdvertiserAccountGroupUserRel> selectGroupUserRelate(Long advertiserAccountGroupId) {
        return advertiserAccountGroupUserRelMapper.selectList(new LambdaQueryWrapper<AdvertiserAccountGroupUserRel>()
            .eq(AdvertiserAccountGroupUserRel::getAdvertiserAccountGroupId, advertiserAccountGroupId));
    }

    public static Set<String> scan(StringRedisTemplate redisTemplate, String pattern) {
        return redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keysTmp = new HashSet<>();
            try (Cursor<byte[]> cursor = connection.scan(new ScanOptions.ScanOptionsBuilder().match(pattern).count(10000).build())) {
                while (cursor.hasNext()) {
                    keysTmp.add(new String(cursor.next()));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            return keysTmp;
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateUserRelate(AdvertiserGroupUserDto advertiserGroupUserDto) {

        advertiserAccountGroupUserRelMapper.delete(new LambdaQueryWrapper<AdvertiserAccountGroupUserRel>()
            .in(AdvertiserAccountGroupUserRel::getAdvertiserAccountGroupId, advertiserGroupUserDto.getAdvertiserAccountGroupIds()));

        advertiserGroupUserDto.getAdvertiserAccountGroupIds().forEach(id -> {
            AdvertiserAccountGroup advertiserAccountGroup = new AdvertiserAccountGroup();
            advertiserAccountGroup.setLeaderId(advertiserGroupUserDto.getLeaderId())
                .setId(id);
            //修改项目负责人
            advertiserAccountGroupMapper.updateLeaderById(advertiserAccountGroup);

            advertiserGroupUserDto.getUserIds().forEach(userId -> {
                AdvertiserAccountGroupUserRel advertiserAccountGroupUserRel = new AdvertiserAccountGroupUserRel();
                advertiserAccountGroupUserRel.setAdvertiserAccountGroupId(id)
                    .setUserId(userId);
                advertiserAccountGroupUserRelMapper.saveOrUpdateGroupUserRelate(advertiserAccountGroupUserRel);
            });
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAccountRelate(AdvertiserGroupUserDto advertiserGroupUserDto) {

        advertiserGroupUserDto.getAdvertiserAccountGroupIds().forEach(id -> advertiserGroupUserDto.getAdvertiserAccountIds().forEach(advertiserAccountId -> {
            AdvertiserAccountGroupRel advertiserAccountGroupRel = new AdvertiserAccountGroupRel();
            advertiserAccountGroupRel.setAdvertiserAccountGroupId(id)
                .setAdvertiserAccountId(advertiserAccountId);
            advertiserAccountGroupRelMapper.saveOrUpdateGroupRelate(advertiserAccountGroupRel);
        }));

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrDeleteAccountRelate(AdvertiserGroupUserDto advertiserGroupUserDto) {

        advertiserGroupUserDto.getAdvertiserAccountGroupIds().forEach(groupId -> advertiserAccountGroupRelMapper.delete(new LambdaQueryWrapper<AdvertiserAccountGroupRel>().eq(AdvertiserAccountGroupRel::getAdvertiserAccountGroupId, groupId)));

        advertiserGroupUserDto.getAdvertiserAccountGroupIds().forEach(id -> advertiserGroupUserDto.getAdvertiserAccountIds().forEach(advertiserAccountId -> {
            AdvertiserAccountGroupRel advertiserAccountGroupRel = new AdvertiserAccountGroupRel();
            advertiserAccountGroupRel.setAdvertiserAccountGroupId(id)
                .setAdvertiserAccountId(advertiserAccountId);
            advertiserAccountGroupRelMapper.saveOrUpdateGroupRelate(advertiserAccountGroupRel);
        }));

    }

    @Transactional(rollbackFor = Exception.class)
    public void unbindingGroupRelate(AdvertiserGroupUserDto advertiserGroupUserDto) {
        advertiserGroupUserDto.getAdvertiserAccountGroupIds().forEach(groupId -> advertiserAccountGroupRelMapper.delete(new LambdaQueryWrapper<AdvertiserAccountGroupRel>()
            .eq(AdvertiserAccountGroupRel::getAdvertiserAccountGroupId, groupId)
            .in(AdvertiserAccountGroupRel::getAdvertiserAccountId, advertiserGroupUserDto.getAdvertiserAccountIds())));
    }

    public List<TargetField> getTargetField(ProjectManageColumnType projectManageColumnType) {
        return advertiserAccountGroupTargetFieldMapper.selectList(new QueryWrapper<TargetField>().eq("project_manage_column_type",projectManageColumnType.ordinal()).eq("delete_status", DeleteStatus.NORMAL.getId()).orderByAsc("id"));
    }

    public  List<AdvertiserAccountGroupReport> getAccountGroupDetail(Long id, String month, User user, String calibreTypeEnum) {
        DecimalFormat df = new DecimalFormat("#.00");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat monthSdf = new SimpleDateFormat("yyyy-MM");

        // 根据传入的月份获取到日历表的第一个周一和最后一个周一
        Date date;
        try {
            date = monthSdf.parse(month);
        } catch (ParseException e) {
            log.error("{} 项目日历表处月份转换失败，message: {}", month, e.getMessage());
            return new ArrayList<>();
        }
        String startDay = sdf.format(TimeUtil.getFirstWeekMonday(date));
        String endDay = sdf.format(TimeUtil.getLastWeekMonday(date)) + " 23:59:59";

        // 根据userId获取用户勾选的自定义列集合
        List<MarketingCustomerField> marketingCustomerFieldList = customerFieldService.list((new LambdaQueryWrapper<MarketingCustomerField>()
            .eq(MarketingCustomerField::getType, CustomerFieldPageType.PMP_SUMMARY)
            .eq(MarketingCustomerField::getUserId, user.getId()).eq(MarketingCustomerField::getChecked, true)));
        if (CollectionUtils.isEmpty(marketingCustomerFieldList)) {
            marketingCustomerFieldList = customerFieldService.list((new LambdaQueryWrapper<MarketingCustomerField>()
                .eq(MarketingCustomerField::getType, CustomerFieldPageType.PMP_SUMMARY)
                .eq(MarketingCustomerField::getUserId, 0).eq(MarketingCustomerField::getChecked, true)));
        }
        Map<String, String> fields = marketingCustomerFieldList.stream().collect(Collectors.toMap(MarketingCustomerField::getField, MarketingCustomerField::getName,(k1, k2) -> k1));

        // 强行查出花费，后面要用
        fields.put("cost", "花费");
        List<String> collect = marketingCustomerFieldList.stream().map(MarketingCustomerField::getField).collect(Collectors.toList());

        // 根据项目id查询出所有的account_id，然后去marketing_data_solidification表中查询
        List<String> accountIds = advertiserAccountGroupRelMapper.ListAccountIdByGroupId(id);
        List<AdvertiserAccountGroupReport> advertiserAccountGroupReports = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(accountIds)) {
            advertiserAccountGroupReports = launchManageService.listOverViewByCondition(fields, accountIds, startDay, endDay, id, calibreTypeEnum);


//            advertiserAccountGroupReports = dataSolidificationDayMapper.listByConditions(fields, accountIds, startDay, endDay, id);
        }
        return advertiserAccountGroupReports;
    }
//        Map<String, AdvertiserAccountGroupReport> pgMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(advertiserAccountGroupReports)) {
//            pgMap = advertiserAccountGroupReports.stream().filter(a -> a.getDayTime() != null).collect(Collectors.toMap(s -> s.getDayTime().toString(), Function.identity()));
//        }

//        List<AdvertiserAccountGroupReport> list = landingPageRemote.getPageViewDataGroupByDay(id, startDay, endDay);
//        Map<String, AdvertiserAccountGroupReport> clickhoseMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(list)) {
//            list.forEach(s -> {
//                s.setDayTime(s.getClickhouseDayTime().toInstant());
//            });
//            clickhoseMap = list.stream().collect(Collectors.toMap(s -> s.getDayTime().toString(), Function.identity()));
//        }

        // 先构建最全的集合
//        Map<String, AdvertiserAccountGroupReport> allDataMap = new HashMap<>();
//        allDataMap.putAll(pgMap);
//        allDataMap.putAll(clickhoseMap);

//        for(Map.Entry entry: allDataMap.entrySet()) {
//            AdvertiserAccountGroupReport pgValue = pgMap.get(entry.getKey());
//            AdvertiserAccountGroupReport clickhouseValue = Optional.ofNullable(clickhoseMap.get(entry.getKey())).orElse(new AdvertiserAccountGroupReport());
//            if (pgValue != null) {
//                setProperties(pgValue, clickhouseValue, pgValue.getCost());
//                allDataMap.put(pgValue.getDayTime().toString(), pgValue);
//            }
//        }
//        advertiserAccountGroupReports = new ArrayList<>(allDataMap.values());
//        return getJsonData(advertiserAccountGroupReports, collect);
//    }

    public void setProperties(AdvertiserAccountGroupReport report1, AdvertiserAccountGroupReport report2, BigDecimal cost) {
        cost = cost == null ? BigDecimal.ZERO : cost;
        report1.setLandingPagePv(report2.getLandingPagePv() == null ? BigDecimal.ZERO : report2.getLandingPagePv());
        report1.setLandingPageUv(report2.getLandingPageUv() == null ? BigDecimal.ZERO : report2.getLandingPageUv());
        report1.setFillCountNum(report2.getFillCountNum() == null ? BigDecimal.ZERO : report2.getFillCountNum());
        report1.setFillCountRate(report2.getFillCountRate() == null ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : report2.getFillCountRate());
        report1.setOrderNum(report2.getOrderNum() == null ? BigDecimal.ZERO : report2.getOrderNum());
        report1.setOrderFinishNum(report2.getOrderFinishNum() == null ? BigDecimal.ZERO : report2.getOrderFinishNum());
        report1.setLandingAvgStay(report2.getLandingAvgStay() == null ? BigDecimal.ZERO : report2.getLandingAvgStay());
        report1.setIdentifyQrCodeNum(report2.getIdentifyQrCodeNum() == null ? BigDecimal.ZERO : report2.getIdentifyQrCodeNum());
        report1.setAddWorkWechatNum(report2.getAddWorkWechatNum() == null ? BigDecimal.ZERO : report2.getAddWorkWechatNum());
        report1.setFollowOfficialAccountNum(report2.getFollowOfficialAccountNum() == null ? BigDecimal.ZERO : report2.getFollowOfficialAccountNum());
        report1.setOnlineShopBuyGoodsSuccessNum(report2.getOnlineShopBuyGoodsSuccessNum() == null ? 0 : report2.getOnlineShopBuyGoodsSuccessNum());
        report1.setOrderCountRate(report2.getOrderCountRate() == null ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : report2.getOrderCountRate());
        report1.setOrderFinishRate(report2.getOrderFinishRate() == null ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : report2.getOrderFinishRate());
        report1.setIdentifyQrCodeRate(report2.getIdentifyQrCodeRate() == null ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : report2.getIdentifyQrCodeRate());
        report1.setAddWorkWechatRate(report2.getAddWorkWechatRate() == null ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : report2.getAddWorkWechatRate());
        report1.setFollowOfficialAccountRate(report2.getFollowOfficialAccountRate() == null ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : report2.getFollowOfficialAccountRate());
        report1.setOnlineShopBuyGoodsSuccessRate(report2.getOnlineShopBuyGoodsSuccessRate() == null ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : report2.getOnlineShopBuyGoodsSuccessRate());
        report1.setFillCountCost(report2.getFillCountNum() == null || BigDecimal.ZERO.equals(report2.getFillCountNum()) ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : cost.divide(report2.getFillCountNum(), 2, BigDecimal.ROUND_HALF_UP));
        report1.setOrderCountCost(report2.getOrderNum() == null || BigDecimal.ZERO.equals(report2.getOrderNum()) ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : cost.divide(report2.getOrderNum(), 2, BigDecimal.ROUND_HALF_UP));
        report1.setOrderFinishCost(report2.getOrderFinishNum() == null || BigDecimal.ZERO.equals(report2.getOrderFinishNum()) ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : cost.divide(report2.getOrderFinishNum(), 2, BigDecimal.ROUND_HALF_UP));
        report1.setIdentifyQrcodeCost(report2.getIdentifyQrCodeNum() == null || BigDecimal.ZERO.equals(report2.getIdentifyQrCodeNum()) ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : cost.divide(report2.getIdentifyQrCodeNum(), 2, BigDecimal.ROUND_HALF_UP));
        report1.setAddWorkWechatCost(report2.getAddWorkWechatNum() == null || BigDecimal.ZERO.equals(report2.getAddWorkWechatNum()) ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : cost.divide(report2.getAddWorkWechatNum(), 2, BigDecimal.ROUND_HALF_UP));
        report1.setFollowOfficialAccountCost(report2.getFollowOfficialAccountNum() == null || BigDecimal.ZERO.equals(report2.getFollowOfficialAccountNum()) ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : cost.divide(report2.getFollowOfficialAccountNum(), 2, BigDecimal.ROUND_HALF_UP));
        report1.setOnlineShopBuyGoodsSuccessCost(report2.getOnlineShopBuyGoodsSuccessNum() == null || BigDecimal.ZERO.equals(new BigDecimal(report2.getOnlineShopBuyGoodsSuccessNum())) ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : cost.divide(new BigDecimal(report2.getOnlineShopBuyGoodsSuccessNum()), 2, BigDecimal.ROUND_HALF_UP));
    }

    private JSONArray getJsonData(List<AdvertiserAccountGroupReport> advertiserAccountGroupReports, List<String> collect) {
        JSONArray jsonArray = new JSONArray();
        //获取公式的list
        Map<String, Long> collect1 = customerFieldService.list(new LambdaQueryWrapper<MarketingCustomerField>().eq(MarketingCustomerField::getType, CustomerFieldPageType.PMP_SUMMARY)
            .eq(MarketingCustomerField::getUserId, 0)).stream().filter(e -> e.getFormulaId() != null).collect(Collectors.toMap(MarketingCustomerField::getField, MarketingCustomerField::getFormulaId));

        for (AdvertiserAccountGroupReport advertiserAccountGroupReport : advertiserAccountGroupReports) {
            JSONObject jsonObject = JSONUtil.parseObj(advertiserAccountGroupReport);
            log.info("{}的数据为{}" ,jsonObject.getStr("dayTime"),jsonObject);

            // 对缺口和成本偏差进行设值
            setGapAndCostDeviation(jsonObject);

            Set<String> strings = jsonObject.keySet();
            Map<String, String> map = new HashMap<>();
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                map.put(entry.getKey(), entry.getValue().toString());
            }
            for (String s : collect) {
                CustomerCalFormula byId = calFormulaService.getById(collect1.get(s));
                if (!strings.contains(s) && byId != null) {
                    Double aDouble = CalculationUtil.obtainResult(byId.getTargetDetail(), map, byId.getCalculationFormula());
                    jsonObject.set(s, aDouble);
                }
            }
            JSONObject resultJsonObject = new JSONObject();
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                // 小数都转为String，解决数据传回前端.00丢失问题
                resultJsonObject.set(entry.getKey(), entry.getValue().toString());
            }
            jsonArray.add(resultJsonObject);
        }
        return jsonArray;
    }

    /**
     * 对缺口和成本偏差以及色值进行赋值
     *
     * @param jsonObject json参数
     */
    private void setGapAndCostDeviation(JSONObject jsonObject) {
        // 这里对缺口和成本的参数进行设置     gapBgColor costDeviationColor costDeviationBgColor
        Object gap = jsonObject.get("gap");
        Object costDeviation = jsonObject.get("costDeviation");
        jsonObject.set("gap", gap == null ? 0 : gap);
        jsonObject.set("costDeviation", costDeviation == null ? 0.00 : costDeviation);

        // 这个地方还需要确定，是不是除了dayTime还有其他非"数值"校验是否为0 的元素
        com.alibaba.fastjson.JSONObject jsonObjectClone = com.alibaba.fastjson.JSONObject.parseObject(jsonObject.toString())
            .fluentRemove("dayTime")
            .fluentRemove("dayAt")
            .fluentRemove("costDeviationColor")
            .fluentRemove("gapColor")
            .fluentRemove("gapBgColor")
            .fluentRemove("costDeviationBgColor");
        jsonObjectClone.fluentRemove("gap").fluentRemove("costDeviation");
        boolean b = jsonObjectClone.values().stream().allMatch(a -> Double.parseDouble(a.toString()) == 0);
        if (b) {
            jsonObject.set("gapColor", ColorType.BLACK.getValue());
            jsonObject.set("costDeviationColor", ColorType.BLACK.getValue());
            jsonObject.set("topLineColor", ColorType.GREY.getValue());
        } else {
            if (Integer.parseInt(jsonObject.get("gap").toString()) >= 0) {
                jsonObject.set("gapColor", ColorType.GREEN.getValue());
                if (Double.parseDouble(jsonObject.get("costDeviation").toString()) <= 0) {
                    jsonObject.set("costDeviationColor", ColorType.GREEN.getValue());
                    jsonObject.set("topLineColor", ColorType.GREEN.getValue());
                } else {
                    jsonObject.set("costDeviationColor", ColorType.RED.getValue());
                    jsonObject.set("topLineColor", ColorType.RED.getValue());
                }
            } else {
                jsonObject.set("gapColor", ColorType.RED.getValue());
                if (Double.parseDouble(jsonObject.get("costDeviation").toString()) <= 0) {
                    jsonObject.set("costDeviationColor", ColorType.GREEN.getValue());
                } else {
                    jsonObject.set("costDeviationColor", ColorType.RED.getValue());
                }
                jsonObject.set("topLineColor", ColorType.RED.getValue());
            }
            String gapBgColor = ColorType.GREEN.getValue().equals(jsonObject.getStr("gapColor")) ? ColorType.SHALLOW_GREEN.getValue() : ColorType.SHALLOW_RED.getValue();
            String costDeviationBgColor = ColorType.GREEN.getValue().equals(jsonObject.getStr("costDeviationColor")) ? ColorType.SHALLOW_GREEN.getValue() : ColorType.SHALLOW_RED.getValue();
            jsonObject.set("gapBgColor", gapBgColor);
            jsonObject.set("costDeviationBgColor", costDeviationBgColor);
        }
    }

    public AdvertiserAccountGroupReportVo getFieldDataByType(Long id, String month, String calibreTypeEnum) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat monthSdf = new SimpleDateFormat("yyyy-MM");

        // 根据传入的月份获取当月的第一天和最后一天
        Date date;
        try {
            date = monthSdf.parse(month);
        } catch (ParseException e) {
            log.error("{} 项目数据概况处，月份转换失败，message: {}", month, e.getMessage());
            return new AdvertiserAccountGroupReportVo();
        }
        String startDay = sdf.format(TimeUtil.getFirstDayOfMonth(date));
        String endDay = sdf.format(TimeUtil.getLastDayOfMonth(date)) + " 23:59:59";

        // 根据项目id查询出所有的account_id，然后去marketing_data_solidification表中查询
        List<String> accountIds = advertiserAccountGroupRelMapper.ListAccountIdByGroupId(id);
        AdvertiserAccountGroupReportVo advertiserAccountGroupReport = new AdvertiserAccountGroupReportVo();
        if (CollectionUtils.isNotEmpty(accountIds)) {

            advertiserAccountGroupReport=  launchManageService.getByConditions(accountIds, startDay, endDay,calibreTypeEnum);
//            advertiserAccountGroupReport = dataSolidificationDayMapper.getByConditions(accountIds, startDay, endDay);
        }

        // 行为转化下的字段数据需要从clickhouse中获取
//        AdvertiserAccountGroupReportCliskhouse pageViewData = landingPageRemote.getTargetData(id, startDay, endDay, advertiserAccountGroupReport.getCost() == null ? BigDecimal.ZERO : advertiserAccountGroupReport.getCost());
//        BeanUtils.copyProperties(pageViewData, advertiserAccountGroupReport);

        advertiserAccountGroupReport.setAccounts(accountIds.size());

        return advertiserAccountGroupReport;
    }

    /**
     * 获取项目概况-数据详情列表
     *
     * @param advertiserAccountGroupId 一叶账户下的项目ID
     * @param month                    查询的月份
     * @param calibreType              口径类型（对应枚举类是CalibreTypeEnum，PAGEVIEW表示曝光口径; CONVERSION表示转化口径）
     * @return                         数据详情列表
     */
    public AdvertiserAccountGroupReportVo getProjectOverviewDataDetail(Long advertiserAccountGroupId, String month, String calibreType, String newSearchFlag) throws ParseException {

       if(Objects.isNull(advertiserAccountGroupId) || StringUtils.isBlank(month) || StringUtils.isBlank(calibreType)){
           throw new RuntimeException("查询参数不合法");
       }
        SimpleDateFormat sdf = new SimpleDateFormat(TimeUtil.YYYY_MM_DD);
        SimpleDateFormat monthSdf = new SimpleDateFormat(TimeUtil.YYYY_MM);
        AdvertiserAccountGroupReportVo advertiserAccountGroupReport = new AdvertiserAccountGroupReportVo();
       //查询该项目下所有的广告账户ID
        List<String> accountIds = advertiserAccountGroupRelService.getAccountIdByGroupId(advertiserAccountGroupId);
        Date date =  monthSdf.parse(month);
        String startTime = sdf.format(TimeUtil.getFirstDayOfMonth(date));
        String endTime = sdf.format(TimeUtil.getLastDayOfMonth(date)) + " 23:59:59";
        boolean checkFlag = this.checkFlag(newSearchFlag);
        log.info("项目概况查询,新查询标识,checkFlag = {}", checkFlag);
        if (checkFlag) {
            //新查询
            advertiserAccountGroupReport = projectOverviewLaunchClickhouseService.getProjectOverviewDataDetail(accountIds, startTime, endTime , calibreType,advertiserAccountGroupId);
        }else {
            advertiserAccountGroupReport = projectOverviewLaunchService.getProjectOverviewDataDetail(accountIds, startTime, endTime, calibreType, advertiserAccountGroupId);
        }
        advertiserAccountGroupReport.setAccounts(accountIds.size());
        advertiserAccountGroupReport.setFollowOfficialAccountNum(advertiserAccountGroupReport.getOfficialFocusNum());
        advertiserAccountGroupReport.setOrderNum(advertiserAccountGroupReport.getOrderSubmitNum());
        return smsSendLogReadonlyService.countSendSmsNumFieldUser(advertiserAccountGroupReport, advertiserAccountGroupId, startTime, endTime);
    }

    /**
     * 判断新旧查询开关的标识
     * @param newSearchFlag 入参
     * @return true标识新查询，false走旧查询
     */
    public Boolean checkFlag(String newSearchFlag){
        if(StringUtils.isBlank(newSearchFlag)){
            return marketingNewSearchConfig.getAdvertiserAccountGroupNewSearch();
        }else {
            return Objects.equals(newSearchOpenFlag, newSearchFlag) ;
        }
    }



    public void saveOrUpdateAccountGroupTargetDayReport() {
        // 获取所有项目内容
        List<AdvertiserAccountGroup> advertiserAccountGroups = this.list();
        if (CollectionUtils.isEmpty(advertiserAccountGroups)) {
            return;
        }

        List<GroupTargetDailyReport> groupTargetDailyReports = new ArrayList<>();
        for (AdvertiserAccountGroup advertiserAccountGroup : advertiserAccountGroups) {
            GroupTargetDailyReport report = getGroupTarget(advertiserAccountGroup);
            if (report != null) {
                groupTargetDailyReports.add(report);
            }
        }

        if (CollectionUtils.isNotEmpty(groupTargetDailyReports)) {
            groupTargetDailyReportMapper.saveOrUpdateBatch(groupTargetDailyReports);
        }
    }

    private GroupTargetDailyReport getGroupTarget(AdvertiserAccountGroup advertiserAccountGroup) {
        JSONArray target = advertiserAccountGroup.getTarget();

        // 根据项目id查询出所有的account_id，然后去marketing_data_solidification表中查询
        List<String> accountIds = advertiserAccountGroupRelMapper.ListAccountIdByGroupId(advertiserAccountGroup.getId());
        if (target == null || target.isEmpty()) {
            return null;
        }

        List<GroupTargetDto> groupTargetDtos = target.toJavaList(GroupTargetDto.class);
        Map<String, String> map = groupTargetDtos.stream().collect(Collectors.toMap(GroupTargetDto::getField, GroupTargetDto::getName));
        map.put("cost", "花费");

        // 去表中查询对应的数量值和成本值
        String localDate = LocalDate.now().toString();
        String endDay = localDate + " 23:59:59";
        AdvertiserAccountGroupReport advertiserAccountGroupReport = new AdvertiserAccountGroupReport();
        if(CollectionUtils.isNotEmpty(accountIds)) {
            advertiserAccountGroupReport = Optional.ofNullable(dataSolidificationMapper.getNowData(map, accountIds, localDate))
                    .orElse(new AdvertiserAccountGroupReport());
        }
        AdvertiserAccountGroupReportBO bo = new AdvertiserAccountGroupReportBO();
        bo.setAdvertiserAccountGroupId(advertiserAccountGroup.getId()).setSearchType(null).setStartDate(localDate).setEndDay(endDay).setAdvertiserAccountGroupReport(advertiserAccountGroupReport);
        AdvertiserAccountGroupReportCliskhouse targetData = landingPageRemote.getAccountTargetData(bo);
        BeanUtils.copyProperties(targetData, advertiserAccountGroupReport);
        //对缺口和成本偏差进行计算
        GroupTargetDailyReport report = deailTarget(advertiserAccountGroupReport, groupTargetDtos);
        Instant dayTime = null;
        try {
            dayTime = TimeUtil.StringToInstant(localDate);
        } catch (ParseException e) {
            log.error("时间转化异常：{}, message:{}", localDate, e);
        }
        report.setTarget(target).setAdvertiserAccountGroupId(advertiserAccountGroup.getId()).setDayAt(dayTime);
        return report;
    }

    /**
     * 对缺口和成本偏差进行计算，并标定颜色
     *
     * @param advertiserAccountGroupReport 项目目标字段当前值
     * @param groupTargetDtos              项目目标预期值
     * @return 缺口和成本偏差
     */
    private GroupTargetDailyReport deailTarget(AdvertiserAccountGroupReport advertiserAccountGroupReport, List<GroupTargetDto> groupTargetDtos) {
        // 将advertiserAccountGroupReport转为map集合，方便操作
        Map<String, BigDecimal> reportMap = JSON.parseObject(JSON.toJSONString(advertiserAccountGroupReport), new TypeReference<Map<String, BigDecimal>>() {
        });
        GroupTargetDailyReport report = new GroupTargetDailyReport();
        for (GroupTargetDto groupTargetDto : groupTargetDtos) {
            BigDecimal realValue = Optional.ofNullable(reportMap.get(groupTargetDto.getField())).orElse(BigDecimal.ZERO);
            BigDecimal targetValue = groupTargetDto.getValue();
            // 如果是缺口值
            BigDecimal value = realValue.subtract(targetValue).setScale(2, BigDecimal.ROUND_HALF_UP);
            if ("COUNT".equals(groupTargetDto.getType())) {
                report.setGap(value);
            } else {
                report.setCostDeviation(value);
            }
        }

        // 缺口值大于等于0为完成
        if (report.getGap().compareTo(BigDecimal.ZERO) > -1) {
            report.setGapColor(ColorType.GREEN);
            report.setGapBgColor(ColorType.SHALLOW_GREEN);
        } else {
            report.setGapColor(ColorType.RED);
            report.setGapBgColor(ColorType.SHALLOW_RED);
        }

        // 成本偏差小于等于0为完成
        if (report.getCostDeviation().compareTo(BigDecimal.ZERO) < 1) {
            report.setCostDeviationColor(ColorType.GREEN);
            report.setCostDeviationBgColor(ColorType.SHALLOW_GREEN);
        } else {
            report.setCostDeviationColor(ColorType.RED);
            report.setCostDeviationBgColor(ColorType.SHALLOW_RED);
        }

        return report;
    }

    public IPage<AdvertiserAccount> getAccountsBykeyWord(IPage<AdvertiserAccount> page, String keyword) {
        return advertiserAccountGroupMapper.getAccountsBykeyWord(page, keyword);
    }

    public List<AdvertiserAccountGroup> getGroupByUser(Long userId) {
        return advertiserAccountGroupMapper.getGroupByUser(userId);
    }

    public List<AdvertiserAccount> getAdvertiserAccountByGroup(Long advertiserAccountGroupId, Boolean flag) {
        return advertiserAccountGroupRelMapper.getAdvertiserAccountByGroup(advertiserAccountGroupId, flag);
    }

    public List<AdvertiserAccountGroup> listUserAccountGroup(Long userId) {
        User user = advertiserAccountGroupMapper.getUserInfo(userId);
        if (UserRole.ROLE_ADMIN == user.getRole()) {
            return advertiserAccountGroupRelMapper.listUserAccountGroup(0L);
        } else {
            return advertiserAccountGroupRelMapper.listUserAccountGroup(userId);
        }


    }

    /**
     * 查出所有的项目及项目下的所有accountId列表
     * @return
     */
    public List<AccountGroupData> listAllData(Integer platformId) {
        List<AccountGroupData> accountGroupDatas = new ArrayList<>();
        List<AdvertiserAccountGroup> advertiserAccountGroups = advertiserAccountGroupMapper.selectList(new LambdaQueryWrapper<AdvertiserAccountGroup>());
        for (int i = 0; i < advertiserAccountGroups.size(); i++) {
            AccountGroupData accountGroupData = new AccountGroupData();
            accountGroupData.setAdvertiserAccountGroupId(advertiserAccountGroups.get(i).getId());
            accountGroupData.setAccountIds(advertiserAccountGroupMapper.listAccountIds(advertiserAccountGroups.get(i).getId(),platformId));
            accountGroupDatas.add(accountGroupData);
        }
        log.info("所有数据为{}",JSON.toJSONString(accountGroupDatas));
        return accountGroupDatas;


    }

    /**
     * 查看成员权限
     * @param dto 入参
     * @return 成员权限内容
     */
    public List<ProjectMemberPermissionTreeVO> getMemberPermissions(MemberPermissionDTO dto) {
        return  marketingAdvertiserAccountPermissionService.getMemberPermissions(dto);
    }

    /**
     * 编辑项目成员权限
     * @return 编辑结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean editMemberPermission(List<MemberPermissionEditDTO> dtos) {
        for (MemberPermissionEditDTO dto : dtos) {
            for (Long advertiserAccountGroupId : dto.getAdvertiserAccountGroupIds()) {
                for (MemberPermissionContentDTO memberPermissionContentDTO : dto.getMemberPermissionContent()) {
                    this.handle(advertiserAccountGroupId,memberPermissionContentDTO,dto);
                }
            }
        }
        return true;
    }

    public void handle(Long advertiserAccountGroupId , MemberPermissionContentDTO memberPermissionContentDTO, MemberPermissionEditDTO dto){

        if (Objects.isNull(advertiserAccountGroupId) ||  Objects.isNull(dto.getProjectModuleType()) || Objects.isNull(dto.getFunctionModuleType()) || Objects.isNull(memberPermissionContentDTO.getPermissionType())){
            throw new RestException("设置项目权限参数不能为空");
        }
        List<MarketingAdvertiserAccountPermission> marketingAdvertiserAccountPermissions = new ArrayList<>();
        //先清除旧权限
        marketingAdvertiserAccountPermissionService.clearUserPermission(advertiserAccountGroupId, dto, memberPermissionContentDTO);
        //重新授权
        memberPermissionContentDTO.getUserIds().forEach(e -> {
            if (Objects.nonNull(memberPermissionContentDTO.getPermissionType())) {

                MemberPermissionType item = memberPermissionContentDTO.getPermissionType();
                MarketingAdvertiserAccountPermission mission = new MarketingAdvertiserAccountPermission();
                mission.setPermission(item);
                mission.setAdvertiserAccountGroupId(advertiserAccountGroupId);
                mission.setAgentId(TenantContextHolder.get());
                mission.setProjectModule(dto.getProjectModuleType());
                mission.setFunctionalModule(dto.getFunctionModuleType());
                mission.setCreatedAt(Instant.now());
                mission.setUserId(e);
                marketingAdvertiserAccountPermissions.add(mission);
            }
        });
        //批量保存
        if (!marketingAdvertiserAccountPermissions.isEmpty()) {
            marketingAdvertiserAccountPermissionService.saveOrUpdateBatch(marketingAdvertiserAccountPermissions);
        }
    }


    /**
     * 2.0版本查询项目概况日历表数据
     * @param id
     * @param month 月份
     * @param user 用户信息
     * @param calibreTypeEnum 口径类型
     * @return  项目概况日历表数据
     */
    public  List<AdvertiserAccountGroupReport> getAccountGroupDetailNew(Long advertiserAccountGroupId, String month, User user, String calibreTypeEnum, String newSearchFlag) throws ParseException {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat monthSdf = new SimpleDateFormat("yyyy-MM");

        // 根据传入的月份获取到日历表的第一个周一和最后一个周一
        Date date;
        try {
            date = monthSdf.parse(month);
        } catch (ParseException e) {
            log.error("{} 项目日历表处月份转换失败，message: {}", month, e.getMessage());
            return new ArrayList<>();
        }
        String startDay = sdf.format(TimeUtil.getFirstWeekMonday(date));
        String endDay = sdf.format(TimeUtil.getLastWeekMonday(date)) + " 23:59:59";

        // 根据userId获取用户勾选的自定义列集合
        List<MarketingCustomerField> marketingCustomerFieldList = customerFieldService.list((new LambdaQueryWrapper<MarketingCustomerField>()
            .eq(MarketingCustomerField::getType, CustomerFieldPageType.PMP_SUMMARY)
            .eq(MarketingCustomerField::getUserId, user.getId()).eq(MarketingCustomerField::getChecked, true)));
        if (CollectionUtils.isEmpty(marketingCustomerFieldList)) {
            marketingCustomerFieldList = customerFieldService.list((new LambdaQueryWrapper<MarketingCustomerField>()
                .eq(MarketingCustomerField::getType, CustomerFieldPageType.PMP_SUMMARY)
                .eq(MarketingCustomerField::getUserId, 0).eq(MarketingCustomerField::getChecked, true)));
        }
        Map<String, String> fields = marketingCustomerFieldList.stream().collect(Collectors.toMap(MarketingCustomerField::getField, MarketingCustomerField::getName,(k1, k2) -> k1));

        // 强行查出花费，后面要用
        fields.put("cost", "花费");

        List<String> collect = marketingCustomerFieldList.stream().map(MarketingCustomerField::getField).collect(Collectors.toList());

        // 根据项目id查询出所有的account_id，然后去marketing_data_solidification表中查询
        List<String> accountIds = advertiserAccountGroupRelMapper.ListAccountIdByGroupId(advertiserAccountGroupId);
        List<AdvertiserAccountGroupReport> advertiserAccountGroupReports = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(accountIds)) {
            boolean checkFlag = this.checkFlag(newSearchFlag);
            log.info("获取项目概况下方日历表数据查询,新查询标识,checkFlag = {}", checkFlag);
            if (checkFlag) {
                advertiserAccountGroupReports = projectOverviewLaunchClickhouseService.listOverViewByCondition(fields, accountIds, startDay, endDay, advertiserAccountGroupId, calibreTypeEnum);
            }else{
                advertiserAccountGroupReports = projectOverviewLaunchService.listOverViewByCondition(fields, accountIds, startDay, endDay, advertiserAccountGroupId, calibreTypeEnum);
            }
        }
        return smsSendLogReadonlyService.countSendSmsNumDetails(advertiserAccountGroupReports, advertiserAccountGroupId, startDay, endDay);
    }

    /**
     * 将投放目标的值设置为空，1.242.0迭代删除部分不用的指标，需要重新初始化
     */
    public void targetClear(){
        this.lambdaUpdate().isNotNull(AdvertiserAccountGroup::getTarget).set(AdvertiserAccountGroup::getTarget,null).update();
    }

    public int openOrClosePmp(User user, Long id, OperationActionEnum operationAction, String ip) {
        int num = baseMapper.updateById(new AdvertiserAccountGroup().setId(id).setShowLandingPageStatus(operationAction));
        Instant nowTime = Instant.now();
        advertiserAccountGroupChangeRecordService.save(new AdvertiserAccountGroupChangeRecord()
            .setId(null)
            .setAdvertiserAccountGroupId(id)
            .setOperationAction(operationAction)
            .setOperationResult(operationAction)
            .setOperationUserId(user.getId())
            .setOperationUserIp(ip)
            .setCreatedAt(nowTime)
            .setUpdatedAt(nowTime)
        );
        //根据项目id清理落地页渠道是否可访问缓存
        landingPageSender.sendClearChannelCanShowCache(id);
        return num;
    }

}
