package ai.yiye.agent.marketing.service;

import ai.yiye.agent.autoconfigure.web.exception.ErrorConstants;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.domain.AdvertiserAccount;
import ai.yiye.agent.domain.AdvertiserAccountRel;
import ai.yiye.agent.marketing.form.AdvertiserAccountRelsForm;
import ai.yiye.agent.marketing.mapper.AdvertiserAccountMapper;
import ai.yiye.agent.marketing.mapper.AdvertiserAccountRelMapper;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@DS("postgresql")
public class AdvertiserAccountRelService extends ServiceImpl<AdvertiserAccountRelMapper, AdvertiserAccountRel> {

    @Autowired
    private AdvertiserAccountMapper accountMapper;

    /**
     * 删除投放账户关系
     *
     * @param accountRel 投放账户内部属性对象
     */
    @Transactional
    public void deleteAdvertiserAccountRels(AdvertiserAccountRel accountRel) {
        QueryWrapper<AdvertiserAccountRel> deleteWrapper = new QueryWrapper<AdvertiserAccountRel>();
        // 判断投放账号是否存在
        if (null != accountRel.getAdvertiserAccountId()) {
            deleteWrapper.eq("advertiser_account_id", accountRel.getAdvertiserAccountId());
        }
        // 判断删除的关联对象是否存在
        if (null != accountRel.getUserId()) {
            deleteWrapper.eq("user_id", accountRel.getUserId());
        }
        // 没有where条件？表明全量删除了
        if (!deleteWrapper.isEmptyOfWhere()) {
            baseMapper.delete(deleteWrapper);
        }
    }

    /**
     * 投放账户绑定用户
     *
     * @param id
     * @param accountRels
     * @return
     */
    @Transactional
    public List<AdvertiserAccountRel> saveAdvertiserAccountRels(Long id, List<AdvertiserAccountRel> accountRels) {
        if (null == accountMapper.selectById(id)) {
            throw new RestException(ErrorConstants.ERROR_ADVERTISER_ACCOUNT_NOT_EXISTS);
        }
        baseMapper.delete(new QueryWrapper<AdvertiserAccountRel>()
            .eq("advertiser_account_id", id)
        );
        // 将投放账户与用户/部门进行绑定
        accountRels.forEach(accountRel -> {
            accountRel.setAdvertiserAccountId(id);
            save(accountRel);
        });
        return accountRels;
    }

    /**
     * 根据用户ID进行批量保存关联数据
     *
     * @param userIds
     * @param accountRels
     * @return
     */
    @Transactional
    public List<AdvertiserAccountRel> batchSaveAdvertiserAccountRelsWithUser(List<Long> userIds, List<AdvertiserAccountRel> accountRels) {
        userIds.forEach(userId -> {
            baseMapper.delete(new QueryWrapper<AdvertiserAccountRel>()
                .eq("user_id", userId)
            );
            accountRels.forEach(accountRel -> {
                accountRel.setUserId(userId);
                save(accountRel);
            });
        });

        // 设置优化师
        List<Long> advertiserAccountIds = accountRels.parallelStream().map(AdvertiserAccountRel::getAdvertiserAccountId).collect(Collectors.toList());
        if (!userIds.isEmpty() && !advertiserAccountIds.isEmpty()) {
            Long optimizerId = userIds.get(0);
            accountMapper.update(new AdvertiserAccount().setOptimizerId(optimizerId), new UpdateWrapper<AdvertiserAccount>()
                .lambda()
                .in(AdvertiserAccount::getId, advertiserAccountIds)
                .isNull(AdvertiserAccount::getOptimizerId));
        }
        return accountRels;
    }

    public void deleteByUserIds(List<Long> userIds) {
        baseMapper.delete(new LambdaQueryWrapper<AdvertiserAccountRel>().in(AdvertiserAccountRel::getUserId, userIds));
    }

    public List<AdvertiserAccountRel> batchSaveAdvertiserAccountRelsWithAdvertiserAccount(AdvertiserAccountRelsForm form) {
        ArrayList<AdvertiserAccountRel> result = new ArrayList<>();
        List<Long> accountIds = form.getAdvertiserAccountId();
        List<Long> userIds = form.getUserIds();
        accountIds.forEach(id -> {
            this.remove(new QueryWrapper<AdvertiserAccountRel>().eq("advertiser_account_id", id));
        });
        for (Long accountId : accountIds) {
            AdvertiserAccountRel rel = new AdvertiserAccountRel().setAdvertiserAccountId(accountId);
            for (Long userId : userIds) {
                rel.setUserId(userId);
                this.save(rel);
                result.add(rel);
            }
        }
        return result;
    }

    /**
     * 根据投放账户Id获取绑定的人员以及部门信息
     *
     * @param id
     * @return
     */
    public List<AdvertiserAccountRel> listAdvertiserAccountRels(Long id) {
        return baseMapper.listAdvertiserAccountRels(id);
    }


    /**
     * 根据用户ID进行查询已经绑定的投放账户关系
     *
     * @param userId
     * @return
     */
    public List<AdvertiserAccountRel> listAdvertiserAccountRelsByUserId(Long userId) {
        return baseMapper.listByUserId(userId);
    }

    public List<AdvertiserAccountRel> listByUserId(Long userId) {
        return baseMapper.listByUserId(userId);
    }

    public List<Long> selectExistedAdvertiserIds(List<Long> advertiserAccountIds) {
        return baseMapper.selectExistedAdvertiserIds(advertiserAccountIds);
    }
}
