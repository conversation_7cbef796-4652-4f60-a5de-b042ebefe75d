package ai.yiye.agent.marketing.service;

import ai.yiye.agent.autoconfigure.mybatis.multidatasource.service.AgentConfService;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.domain.MarketingCustomerField;
import ai.yiye.agent.domain.User;
import ai.yiye.agent.domain.boss.BossAdvertiserAccountGroup;
import ai.yiye.agent.domain.enumerations.BaseStatusEnum;
import ai.yiye.agent.domain.enumerations.CustomerFieldPageType;
import ai.yiye.agent.domain.enumerations.DefaultDataType;
import ai.yiye.agent.domain.enumerations.WhiteType;
import ai.yiye.agent.domain.result.Result;
import ai.yiye.agent.marketing.baidu.CustomFieldConvert;
import ai.yiye.agent.marketing.constants.CustomerFieldConstant;
import ai.yiye.agent.marketing.dto.CustomerFieldListDto;
import ai.yiye.agent.marketing.mapper.CustomerFieldMapper;
import ai.yiye.agent.marketing.remote.BossBackendRemote;
import ai.yiye.agent.marketing.remote.LandingPageRemote;
import ai.yiye.agent.marketing.service.cache.UserColumnRedisService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wang
 * @version 1.0
 * @date 2021/5/7 17:14
 */
@Slf4j
@Service
@DS("postgresql")
public class CustomerFieldService extends ServiceImpl<CustomerFieldMapper, MarketingCustomerField> {

	@Autowired
	private CustomerFieldMapper customerFieldMapper;
	private static final Integer PLATFORM_ID_YIYE = 0;
	@Autowired
	private UserColumnRedisService userColumnRedisService;
	@Autowired
	private AgentConfService agentConfService;
	@Autowired
	private BossBackendRemote bossBackendRemote;

	@Autowired
	private LandingPageRemote landingPageRemote;

	private static final String QIYE_PERSONNEL_ID = "qiyePersonnelId";

	private static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_QR_CODE_BACKGROUND_URL = "landingPageWechatCustomerContactQrCodeBackgroundUrl";

    private static final String QIYETUI_PAGE_VIEW_NUM = "qiyetuiPageViewNum";

	private static final String EXTERNAL_USER_SEX_NAME = "externalUserSexName";

	private static final String WECHAT_CUSTOMER_ACQUISITION_LINK_STATUS = "wechatCustomerAcquisitionLinkStatus";

	private static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_QR_CODE = "landingPageWechatCustomerContactQrCode";

	private static final String OFFICIAL_ACCOUNT_QR_CODE_SEND_NUM = "officialAccountQrCodeSendNum";
	private static final String WHATSAPP_URL = "whatsappUrl";
	private static final String WHATSAPP_LANDING_PAGE_NAME = "whatsappLandingPageName";
	private static final String WHATSAPP_LANDING_PAGE_CHANNEL_NAME = "whatsappLandingPageChannelName";
	private static final String WHATSAPP_CUSTOMER_PHONE = "whatsappCustomerPhone";
	private static final String WHATSAPP_CUSTOMER_SERVICE_NAME = "whatsappCustomerServiceName";
	private static final String WHATSAPP_USER_NAME = "whatsappUserName";
	private static final String WHATSAPP_USER_PHONE = "whatsappUserPhone";

	private static final String OFFICIAL_ACCOUNT_QR_CODE_ADD_WORK_WECHAT_NUM = "officialAccountQrCodeAddWorkWechatNum";

	private static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_QR_CODE = "officialWechatCustomerContactQrCode";

	private static final String ROBOT_WECHAT_CUSTOMER_CONTACT_QR_CODE = "robotCustomerContactQrCode";

	private static final String ROBOT_WECHAT_CUSTOMER_ACQUISITION_LINK = "robotCustomerAcquisitionLink";

    private static final String DOUYIN_CUSTOMER_SOURCE = "douyinCustomerSource";

    private static final String DOUYIN_AUTH_PHONE = "douyinAuthPhone";

    private static final String DOUYIN_APPLET_VISIT_URL = "douyinAppletVisitUrl";

    //饿了么小程序自定义列屏蔽
    private static final String ELE_PV_NUM = "elePvNum";

    private static final String ELE_QR_CODE_VIEW_NUM = "eleQrCodeViewNum";

    private static final String ELE_IDENTIFY_WECHAT_QR_CODE_NUM = "eleIdentifyWechatQrCodeNum";

    private static final String ELE_ADD_WECHAT_SUCCESS_NUM = "eleAddWechatSuccessNum";

	//whatsapp 自定义列白名单字段
	private static String WHATSAPP_JUMP_NUM = "whatsappJumpNum";
	private static String WHATSAPP_ADD_FRIEND_SUCCESS_NUM = "whatsappAddFriendSuccessNum";
	private static String WHATSAPP_USER_OPEN_MOUTH_NUM = "whatsappUserOpenMouthNum";
	private static String WHATSAPP_JUMP_RATE = "whatsappJumpRate";
	private static String WHATSAPP_ADD_FRIEND_SUCCESS_RATE = "whatsappAddFriendSuccessRate";
	private static String WHATSAPP_USER_OPEN_MOUTH_RATE = "whatsappUserOpenMouthRate";

    //taobao dsp data
    private static final String TAOBAO_DSP_DATA = "taobaoDspData";
    private static final String TAOBAO_DSP_LANDING_PAGE_ID = "taobaoDspLandingPageId";
    private static final String TAOBAO_DSP_LANDING_PAGE_NAME = "taobaoDspLandingPageName";
    private static final String TAOBAO_DSP_LANDING_PAGE_CHANNEL_ID = "taobaoDspLandingPageChannelId";
    private static final String TAOBAO_DSP_LANDING_PAGE_CHANNEL_NAME = "taobaoDspLandingPageChannelName";
    private static final String TAOBAO_DSP_URL = "taobaoDspUrl";

    private static final String TAOBAO_PAGE_VIEW_NUM = "taobaoPageViewNum";
    private static final String TAOBAO_PAGE_VIEW_RATE = "taobaoPageViewRate";
    private static final String TAOBAO_ORDER_PAYMENT_NUM = "taobaoOrderPaymentNum";
    private static final String TAOBAO_ORDER_PAYMENT_RATE = "taobaoOrderPaymentRate";
    private static final String TAOBAO_PRODUCT_CLICK_NUM = "taobaoProductClickNum";
    private static final String TAOBAO_PRODUCT_CLICK_RATE = "taobaoProductClickRate";
    private static final String TAOBAO_FIRST_VISIT_VENUE_NUM = "taobaoFirstVisitVenueNum";
    private static final String TAOBAO_FIRST_VISIT_VENUE_RATE = "taobaoFirstVisitVenueRate";
    private static final String TAOBAO_RED_ENVELOPE_RECEIVE_NUM = "taobaoRedEnvelopeReceiveNum";
    private static final String TAOBAO_RED_ENVELOPE_RECEIVE_RATE = "taobaoRedEnvelopeReceiveRate";
    private static final String TAOBAO_CANCEL_ORDER_PAYMENT_NUM = "taobaoCancelOrderPaymentNum";
    private static final String TAOBAO_CANCEL_ORDER_PAYMENT_RATE = "taobaoCancelOrderPaymentRate";
    private static final String TAOBAO_HIGH_COMMISSION_ORDER_PAYMENT_NUM = "taobaoHighCommissionOrderPaymentNum";
    private static final String TAOBAO_HIGH_COMMISSION_ORDER_PAYMENT_RATE = "taobaoHighCommissionOrderPaymentRate";

	private static final String[] WHATSAPP_WHITE_COLUMN = new String[] {WHATSAPP_JUMP_NUM,WHATSAPP_ADD_FRIEND_SUCCESS_NUM,WHATSAPP_USER_OPEN_MOUTH_NUM,WHATSAPP_JUMP_RATE,WHATSAPP_ADD_FRIEND_SUCCESS_RATE,WHATSAPP_USER_OPEN_MOUTH_RATE};

	public CustomerFieldListDto customerColumnByPlatform(CustomerFieldPageType type, User user, Integer platformId) {
		CustomerFieldListDto customerFieldListDto = new CustomerFieldListDto();
		//这里增加一个redis缓存，减少pipe的可能性
		List<MarketingCustomerField> customerFieldList = userColumnRedisService.getUserColumnByUserId(user.getId(), platformId, type);
		if (CollectionUtils.isEmpty(customerFieldList)) {
			//redis 中没有，那么就查看数据库中
			customerFieldList = this.list(new LambdaQueryWrapper<MarketingCustomerField>().eq(MarketingCustomerField::getType, type).eq(MarketingCustomerField::getUserId, user.getId()).eq(MarketingCustomerField::getDefaultData, DefaultDataType.CUSTOMER_DATA));
			if (CollectionUtils.isEmpty(customerFieldList)) {
				List<MarketingCustomerField> list = this.list(new LambdaQueryWrapper<MarketingCustomerField>().eq(MarketingCustomerField::getType, type).eq(MarketingCustomerField::getDefaultData, DefaultDataType.DEFAULT_DATA));
				list.forEach(e -> e.setUserId(user.getId()).setDefaultData(DefaultDataType.CUSTOMER_DATA));
				this.saveBatch(list);
				customerFieldList = list;
			}
			customerFieldList = customerFieldList.stream().peek(s -> {
				Map<String, String> convertMap = CustomFieldConvert.getByPlatformId(platformId).getConvertMap();
				for (String key : convertMap.keySet()) {
					if (s.getField().equals(key)) {
						s.setName(convertMap.get(key));
					}
				}
			}).collect(Collectors.toList());
			userColumnRedisService.saveUserColumnByUserId(user.getId(), platformId, type, customerFieldList);
		}

		customerFieldListDto.setCustomerFields(customerFieldList);
		//默认字段
		List<MarketingCustomerField> defaultList = userColumnRedisService.getUserColumnDefault(0L, platformId, type);
		if (CollectionUtils.isEmpty(defaultList)) {
			List<MarketingCustomerField> list = this.list(new LambdaQueryWrapper<MarketingCustomerField>().eq(MarketingCustomerField::getType, type).eq(MarketingCustomerField::getDefaultData, DefaultDataType.DEFAULT_DATA));
			defaultList = list.stream().peek(s -> {
				Map<String, String> convertMap = CustomFieldConvert.getByPlatformId(platformId).getConvertMap();
				for (String key : convertMap.keySet()) {
					if (s.getField().equals(key)) {
						s.setName(convertMap.get(key));
					}
				}
			}).collect(Collectors.toList());
			userColumnRedisService.saveUserColumnDefault(user.getId(), platformId, type, defaultList);
		}
		customerFieldListDto.setDefaultField(defaultList);

		return customerFieldListDto;
	}

	public CustomerFieldListDto customerColumn(CustomerFieldPageType type, User user, Long advertiserAccountGroupId) {
		CustomerFieldListDto customerFieldListDto = new CustomerFieldListDto();
		List<MarketingCustomerField> customerFields = this.list(
				new LambdaQueryWrapper<MarketingCustomerField>()
						.eq(MarketingCustomerField::getType, type)
						.eq(MarketingCustomerField::getUserId, user.getId())
						.eq(MarketingCustomerField::getDefaultData, DefaultDataType.CUSTOMER_DATA)
						.orderByAsc(MarketingCustomerField::getFieldNo)
		);
		List<MarketingCustomerField> defaultField = this.list(
				new LambdaQueryWrapper<MarketingCustomerField>()
						.eq(MarketingCustomerField::getType, type)
						.eq(MarketingCustomerField::getDefaultData, DefaultDataType.DEFAULT_DATA)
		);
		if (CollectionUtils.isEmpty(customerFields)) {
			customerFieldListDto.setCustomerFields(defaultField);
		} else {
			// defaultField中的数据比customerFields中的数据多，所以需要合并
			Map<String, MarketingCustomerField> customerFieldMap = customerFields.stream()
					.collect(Collectors.toMap(MarketingCustomerField::getField, Function.identity()));
			defaultField.forEach(e -> {
				if (!customerFieldMap.containsKey(e.getField())) {
					customerFields.add(e);
				}
			});
			customerFieldListDto.setCustomerFields(customerFields);
		}
		customerFieldListDto.setDefaultField(defaultField);

		/**
		 * 将customerFields中缺少的部分字段从defaultField 中合并过来
		 */
		//根据白名单是否展示企业推ID
        // GMP客资列表不查询项目白名单配置
		BossAdvertiserAccountGroup bossAdvertiserAccountGroup = CustomerFieldPageType.GMP_CUSTOMER == type ? null : bossBackendRemote.getwhiteType(TenantContextHolder.get(), advertiserAccountGroupId);
		AgentConf.License agentLicense = bossBackendRemote.getCustomerAgentLicense(TenantContextHolder.get());

        if(agentLicense == null || !BaseStatusEnum.ENABLE.equals(agentLicense.getTabaoDspStatus())){
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_DSP_DATA}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_DSP_LANDING_PAGE_ID}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_DSP_LANDING_PAGE_NAME}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_DSP_LANDING_PAGE_CHANNEL_ID}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_DSP_LANDING_PAGE_CHANNEL_NAME}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_DSP_URL}, CustomerFieldPageType.PMP_CUSTOMER);

            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_PAGE_VIEW_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_PAGE_VIEW_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_ORDER_PAYMENT_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_ORDER_PAYMENT_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_PRODUCT_CLICK_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_PRODUCT_CLICK_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_FIRST_VISIT_VENUE_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_FIRST_VISIT_VENUE_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_RED_ENVELOPE_RECEIVE_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_RED_ENVELOPE_RECEIVE_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_CANCEL_ORDER_PAYMENT_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_CANCEL_ORDER_PAYMENT_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_HIGH_COMMISSION_ORDER_PAYMENT_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_HIGH_COMMISSION_ORDER_PAYMENT_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);

            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_PAGE_VIEW_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_PAGE_VIEW_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_ORDER_PAYMENT_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_ORDER_PAYMENT_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_PRODUCT_CLICK_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_PRODUCT_CLICK_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_FIRST_VISIT_VENUE_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_FIRST_VISIT_VENUE_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_RED_ENVELOPE_RECEIVE_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_RED_ENVELOPE_RECEIVE_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_CANCEL_ORDER_PAYMENT_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_CANCEL_ORDER_PAYMENT_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_HIGH_COMMISSION_ORDER_PAYMENT_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{TAOBAO_HIGH_COMMISSION_ORDER_PAYMENT_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE);
        }

		if (agentLicense == null || !BaseStatusEnum.ENABLE.equals(agentLicense.getFullScreenQRCodeStatus())) {
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_QR_CODE_BACKGROUND_URL}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_QR_CODE_BACKGROUND_URL}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
		}

        if (agentLicense == null || !BaseStatusEnum.ENABLE.equals(agentLicense.getQiyetuiPvStatistics())) {
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{QIYETUI_PAGE_VIEW_NUM}, CustomerFieldPageType.PMP_SUMMARY);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{QIYETUI_PAGE_VIEW_NUM}, CustomerFieldPageType.PMP_SUMMARY);
        }
		//企业推小程序
		if (!Objects.isNull(bossAdvertiserAccountGroup) && !WhiteType.WECHAT_QIYETUI.contains(bossAdvertiserAccountGroup.getWhiteTypes())) {
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{QIYE_PERSONNEL_ID}, CustomerFieldPageType.PMP_CUSTOMER);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{QIYE_PERSONNEL_ID}, CustomerFieldPageType.PMP_CUSTOMER);
		}

		if (!Objects.isNull(bossAdvertiserAccountGroup) && !WhiteType.WORK_WECHAT_GENDER_UPLOAD.contains(bossAdvertiserAccountGroup.getWhiteTypes())) {
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{EXTERNAL_USER_SEX_NAME}, CustomerFieldPageType.PMP_CUSTOMER);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{EXTERNAL_USER_SEX_NAME}, CustomerFieldPageType.PMP_CUSTOMER);
		}

		//获客链接状态字段白名单控制屏蔽
		if (!Objects.isNull(bossAdvertiserAccountGroup) && !WhiteType.CUSTOMER_ACQUISITION.contains(bossAdvertiserAccountGroup.getWhiteTypes())) {
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{WECHAT_CUSTOMER_ACQUISITION_LINK_STATUS}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{WECHAT_CUSTOMER_ACQUISITION_LINK_STATUS}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
		}

		if (!Objects.isNull(bossAdvertiserAccountGroup) && !WhiteType.ROBOT_CUSTOMER_LIVE_CODE.contains(bossAdvertiserAccountGroup.getWhiteTypes())) {
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{ROBOT_WECHAT_CUSTOMER_CONTACT_QR_CODE}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{ROBOT_WECHAT_CUSTOMER_CONTACT_QR_CODE}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
		}

        //字节小程序字段白名单控制屏蔽
        if (!Objects.isNull(bossAdvertiserAccountGroup) && !WhiteType.DOUYIN_APPLET.contains(bossAdvertiserAccountGroup.getWhiteTypes())) {
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{DOUYIN_CUSTOMER_SOURCE}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{DOUYIN_CUSTOMER_SOURCE}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{DOUYIN_AUTH_PHONE}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{DOUYIN_AUTH_PHONE}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{DOUYIN_APPLET_VISIT_URL}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{DOUYIN_APPLET_VISIT_URL}, CustomerFieldPageType.PMP_CUSTOMER);

            //字节小程序字段白名单控制屏蔽
            this.removeFieldNotInWhiteList(customerFieldListDto,type);

            //#43395 【YIYE_AGENT_V1.247.0】客资-自定义列未开通白名单展示了字节相关字段  https://ones.yiye.ai/project/#/team/WtsduTeT/task/EFZs7UMKqkNuEVkN
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_NAME}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_NAME}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_APPID}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_APPID}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_OPENID}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_OPENID}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_CUSTOMER_SOURCE}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_CUSTOMER_SOURCE}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_AUTH_PHONE}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_AUTH_PHONE}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_VISIT_URL}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_VISIT_URL}, CustomerFieldPageType.PMP_CUSTOMER);
        }

        //淘宝电影字段白名单控制屏蔽
        if (Objects.isNull(agentLicense) || !BaseStatusEnum.ENABLE.equals(agentLicense.getTaobaoMovieStatus())) {
            this.removeTaoBaoMovieFieldNotInWhiteList(customerFieldListDto, type);
        }

        //whatsapp白名单控制
        if (Objects.isNull(agentLicense) || !BaseStatusEnum.ENABLE.equals(agentLicense.getWhatsappCustomerServiceStatus())) {
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{WHATSAPP_USER_NAME}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{WHATSAPP_USER_NAME}, CustomerFieldPageType.PMP_CUSTOMER);

            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{WHATSAPP_USER_PHONE}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{WHATSAPP_USER_PHONE}, CustomerFieldPageType.PMP_CUSTOMER);

            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{WHATSAPP_CUSTOMER_SERVICE_NAME}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{WHATSAPP_CUSTOMER_SERVICE_NAME}, CustomerFieldPageType.PMP_CUSTOMER);

            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{WHATSAPP_CUSTOMER_PHONE}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{WHATSAPP_CUSTOMER_PHONE}, CustomerFieldPageType.PMP_CUSTOMER);

            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{WHATSAPP_LANDING_PAGE_NAME}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{WHATSAPP_LANDING_PAGE_NAME}, CustomerFieldPageType.PMP_CUSTOMER);

            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{WHATSAPP_LANDING_PAGE_CHANNEL_NAME}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{WHATSAPP_LANDING_PAGE_CHANNEL_NAME}, CustomerFieldPageType.PMP_CUSTOMER);

            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{WHATSAPP_URL}, CustomerFieldPageType.PMP_CUSTOMER);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{WHATSAPP_URL}, CustomerFieldPageType.PMP_CUSTOMER);

			//落地页 渠道 项目概况 字段屏蔽
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), WHATSAPP_WHITE_COLUMN, CustomerFieldPageType.PMP_LANDING_PAGE);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), WHATSAPP_WHITE_COLUMN, CustomerFieldPageType.PMP_LANDING_PAGE);
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), WHATSAPP_WHITE_COLUMN, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), WHATSAPP_WHITE_COLUMN, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);

			removeNotWhiteList(customerFieldListDto.getCustomerFields(), WHATSAPP_WHITE_COLUMN, CustomerFieldPageType.PMP_SUMMARY);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), WHATSAPP_WHITE_COLUMN, CustomerFieldPageType.PMP_SUMMARY);
        }

		//"微信公众号发送二维码加粉数"控制屏蔽
		boolean result = this.checkOfficialAccountQrCode();
		if (!result) {
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{OFFICIAL_ACCOUNT_QR_CODE_SEND_NUM}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{OFFICIAL_ACCOUNT_QR_CODE_SEND_NUM}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{OFFICIAL_ACCOUNT_QR_CODE_ADD_WORK_WECHAT_NUM}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{OFFICIAL_ACCOUNT_QR_CODE_ADD_WORK_WECHAT_NUM}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{OFFICIAL_WECHAT_CUSTOMER_CONTACT_QR_CODE}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{OFFICIAL_WECHAT_CUSTOMER_CONTACT_QR_CODE}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.OFFICIAL_ADD_CUSTOMER_NUM}, CustomerFieldPageType.PMP_SUMMARY);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.OFFICIAL_ADD_CUSTOMER_RATE}, CustomerFieldPageType.PMP_SUMMARY);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.OFFICIAL_ADD_CUSTOMER_COST}, CustomerFieldPageType.PMP_SUMMARY);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.OFFICIAL_ADD_CUSTOMER_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.OFFICIAL_ADD_CUSTOMER_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.OFFICIAL_ADD_CUSTOMER_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.OFFICIAL_ADD_CUSTOMER_RATE}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
		}

        //饿了么小程序字段白名单控制屏蔽
        if (Objects.nonNull(bossAdvertiserAccountGroup) && !WhiteType.OPEN_ELEME_MICRO.contains(bossAdvertiserAccountGroup.getWhiteTypes())) {
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{ELE_PV_NUM,ELE_QR_CODE_VIEW_NUM,ELE_IDENTIFY_WECHAT_QR_CODE_NUM,ELE_ADD_WECHAT_SUCCESS_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{ELE_PV_NUM,ELE_QR_CODE_VIEW_NUM,ELE_IDENTIFY_WECHAT_QR_CODE_NUM,ELE_ADD_WECHAT_SUCCESS_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE);
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{ELE_PV_NUM,ELE_QR_CODE_VIEW_NUM,ELE_IDENTIFY_WECHAT_QR_CODE_NUM,ELE_ADD_WECHAT_SUCCESS_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{ELE_PV_NUM,ELE_QR_CODE_VIEW_NUM,ELE_IDENTIFY_WECHAT_QR_CODE_NUM,ELE_ADD_WECHAT_SUCCESS_NUM}, CustomerFieldPageType.PMP_LANDING_PAGE_CHANNEL);

            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{ELE_PV_NUM,ELE_QR_CODE_VIEW_NUM,ELE_IDENTIFY_WECHAT_QR_CODE_NUM,ELE_ADD_WECHAT_SUCCESS_NUM}, CustomerFieldPageType.PMP_SUMMARY);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{ELE_PV_NUM,ELE_QR_CODE_VIEW_NUM,ELE_IDENTIFY_WECHAT_QR_CODE_NUM,ELE_ADD_WECHAT_SUCCESS_NUM}, CustomerFieldPageType.PMP_SUMMARY);


        }
/*        //腾讯官方小程序页内二维码展示
        if (!pmpEnterpriseQiyeInstall(advertiserAccountGroupId)){
            removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_QR_CODE}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
            removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_QR_CODE}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);

        }*/

		//是否开启【IP信息展示】
		//#30501 支持通过BOSS后台白名单控制访客细查及客资列表字段：IP、IP所属省份、IP所属城市是否展示 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PHVqcQyFALZZgKEh
		if (!Objects.isNull(bossAdvertiserAccountGroup) && !WhiteType.IS_IP_INFO_ENABLED.contains(bossAdvertiserAccountGroup.getWhiteTypes())) {
			//IP
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{"ip"}, CustomerFieldPageType.PMP_CUSTOMER);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{"ip"}, CustomerFieldPageType.PMP_CUSTOMER);
			//IP所属省份
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{"province"}, CustomerFieldPageType.PMP_CUSTOMER);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{"province"}, CustomerFieldPageType.PMP_CUSTOMER);
			//IP所属城市
			removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{"city"}, CustomerFieldPageType.PMP_CUSTOMER);
			removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{"city"}, CustomerFieldPageType.PMP_CUSTOMER);
		}

		//微信客服关联公众号
		if (CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE.equals(type)) {
			AgentConf agent = agentConfService.getOne(Wrappers.lambdaQuery(AgentConf.class)
					.select(AgentConf::getLicense).eq(AgentConf::getAgentId, TenantContextHolder.get()));
			AgentConf.License license = agent.getLicense();
			BaseStatusEnum anEnum = license.getAscribeQRCodeImageAfterFollowingOfficialAccountLicenseStatus();
			if (Objects.isNull(anEnum) || BaseStatusEnum.DISABLE.equals(anEnum)) {
				removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{"officialWechatCustomerContactAppId"}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
				removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{"officialWechatCustomerContactAppId"}, CustomerFieldPageType.PMP_WECHAT_CUSTOMER_SERVICE);
			}
		}
		return customerFieldListDto;
	}



    /**
     * 根据白名单，移除淘宝电影小程序的相关字段不展示
     * @param customerFieldListDto 自定义列集合
     * @param customerFieldPageType 页面类型
     */
    public void removeTaoBaoMovieFieldNotInWhiteList(CustomerFieldListDto customerFieldListDto, CustomerFieldPageType customerFieldPageType){
        log.info("淘宝电影跳转及订单归因上报白名单未开启，移除淘宝电影小程序的相关字段不展示");
        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.TAO_BAO_MOVIE_APPLET_JUMP_NUM}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.TAO_BAO_MOVIE_APPLET_JUMP_NUM}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.TAO_BAO_MOVIE_APPLET_JUMP_RATE}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.TAO_BAO_MOVIE_APPLET_JUMP_RATE}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.TAO_BAO_MOVIE_APPLET_ORDER_NUM}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.TAO_BAO_MOVIE_APPLET_ORDER_NUM}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.TAO_BAO_MOVIE_APPLET_ORDER_RATE}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.TAO_BAO_MOVIE_APPLET_ORDER_RATE}, customerFieldPageType);
    }


    /**
     * 根据白名单，移除字节小程序的相关字段不展示
     * @param customerFieldListDto 自定义列集合
     * @param customerFieldPageType 页面类型
     */
    public void removeFieldNotInWhiteList(CustomerFieldListDto customerFieldListDto, CustomerFieldPageType customerFieldPageType){

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.CLUE_FORM_SUBMIT_NUM}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.CLUE_FORM_SUBMIT_NUM}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.CLUE_FORM_SUBMIT_RATE}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.CLUE_FORM_SUBMIT_RATE}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_NATIVE_FORM_SUBMIT_NUM}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_NATIVE_FORM_SUBMIT_NUM}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_NATIVE_FORM_SUBMIT_RATE}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_NATIVE_FORM_SUBMIT_RATE}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.ACTIVE_MESSAGE_AUTHORIZATION_NUM}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.ACTIVE_MESSAGE_AUTHORIZATION_NUM}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.ACTIVE_MESSAGE_AUTHORIZATION_RATE}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.ACTIVE_MESSAGE_AUTHORIZATION_RATE}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.PHONE_NUMBER_RECIEVED_NUM}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.PHONE_NUMBER_RECIEVED_NUM}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.PHONE_NUMBER_RECIEVED_RATE}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.PHONE_NUMBER_RECIEVED_RATE}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.FORM_SUBMIT_TOTAL_NUM}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.FORM_SUBMIT_TOTAL_NUM}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.FORM_SUBMIT_TOTAL_RATE}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.FORM_SUBMIT_TOTAL_RATE}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_ORDER_SUBMIT_NUM}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_ORDER_SUBMIT_NUM}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_ORDER_SUBMIT_RATE}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_ORDER_SUBMIT_RATE}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_ORDER_SUBMIT_COST}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_ORDER_SUBMIT_COST}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_ORDER_FINISH_NUM}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_ORDER_FINISH_NUM}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_ORDER_FINISH_RATE}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_ORDER_FINISH_RATE}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_ORDER_FINISH_COST}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_ORDER_FINISH_COST}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_JUMP_NUM}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_JUMP_NUM}, customerFieldPageType);

        removeNotWhiteList(customerFieldListDto.getCustomerFields(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_JUMP_RATE}, customerFieldPageType);
        removeNotWhiteList(customerFieldListDto.getDefaultField(), new String[]{CustomerFieldConstant.DOUYIN_APPLET_JUMP_RATE}, customerFieldPageType);
    }

	private boolean pmpEnterpriseQiyeInstall(Long advertiserAccountGroupId) {
		try {
			Result<Boolean> booleanResult = landingPageRemote.pmpEnterpriseQiyeInstallStatus(advertiserAccountGroupId);
			Boolean result = booleanResult.getResult();
			if (Objects.nonNull(result)) {
				return result;
			}
		} catch (Exception e) {
			log.error("===>判断当前项目下是否安装与代开发应用一致的企业推应用失败,pmpId:{}", advertiserAccountGroupId);
		}
		return false;
	}


	private void removeNotWhiteList(List<MarketingCustomerField> customerFields, String[] columnNames, CustomerFieldPageType customerFieldPageType) {
		if (Objects.isNull(customerFieldPageType) || CollectionUtils.isEmpty(customerFields) || columnNames.length <= 0) {
			return;
		}
		Optional.of(customerFields).ifPresent(s -> {
			Iterator<MarketingCustomerField> iterator = s.iterator();
			while (iterator.hasNext()) {
				MarketingCustomerField o = iterator.next();
				if (Arrays.asList(columnNames).contains(o.getField()) && Objects.equals(customerFieldPageType, o.getType())) {
					iterator.remove();
				}
			}
		});
	}

	@Transactional
	public Boolean modifyByUser(List<MarketingCustomerField> marketingCustomerFields) {
		try {
			customerFieldMapper.deleteUserIdColumn(marketingCustomerFields.get(0).getType(), marketingCustomerFields.get(0).getUserId());
			userColumnRedisService.cacheClean(marketingCustomerFields);
			//不知道为什么，前端会传重复的数据过来，这边根据名字进行去重
			List<MarketingCustomerField> newList = new ArrayList<>();
			marketingCustomerFields.stream().filter(distinctByKey(MarketingCustomerField::getField))
					.forEach(newList::add);

			this.saveBatch(newList);

			newList = newList.stream().peek(s -> {
				Map<String, String> convertMap = CustomFieldConvert.getByPlatformId(1).getConvertMap();
				for (String key : convertMap.keySet()) {
					if (s.getField().equals(key)) {
						s.setName(convertMap.get(key));
					}
				}
			}).collect(Collectors.toList());
			userColumnRedisService.saveUserColumnByUserId(newList.get(0).getUserId(), 1, newList.get(0).getType(), newList);
			return true;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return false;
		}
	}

	public List<MarketingCustomerField> getValidityReportColumns(Integer type, Long userId) {
		return baseMapper.selectList(new LambdaQueryWrapper<MarketingCustomerField>().eq(MarketingCustomerField::getType, type).eq(MarketingCustomerField::getChecked, true).eq(MarketingCustomerField::getUserId, userId));
	}

	private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
		Map<Object, Boolean> seen = new ConcurrentHashMap<>();
		return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
	}

	public boolean deleteCustomerColumnByAgentIdAndCustomerFieldPageType(String agentId, CustomerFieldPageType customerFieldPageType) {
		log.info("开始根据自定义列类型删除用户非默认自定义配置 agentId={}；agentIdParam={}；customerFieldPageType={}；", TenantContextHolder.get(), agentId, customerFieldPageType);
		return this.remove(new LambdaQueryWrapper<MarketingCustomerField>().eq(MarketingCustomerField::getType, customerFieldPageType).eq(MarketingCustomerField::getDefaultData, DefaultDataType.CUSTOMER_DATA));
	}

	/**
	 * 判断用户是否开启“关注公众号后发送二维码图片归因”开关
	 *
	 * @return 判断结果
	 */
	public boolean checkOfficialAccountQrCode() {
		try {
			AgentConf agent = agentConfService.getOne(Wrappers.lambdaQuery(AgentConf.class).eq(AgentConf::getAgentId, TenantContextHolder.get()));
			if (TenantContextHolder.get() == null) {
				return false;
			}
			log.info("判断用户是否开启“关注公众号后发送二维码图片归因”开关, agent = [{}]", JSONObject.toJSONString(agent));
			return Objects.nonNull(agent) && Objects.nonNull(agent.getLicense()) && Objects.equals(BaseStatusEnum.ENABLE, agent.getLicense().getAscribeQRCodeImageAfterFollowingOfficialAccountLicenseStatus());
		} catch (Exception e) {
			log.error("判断用户是否开启“关注公众号后发送二维码图片归因”开关异常", e);
		}
		return false;
	}
}
