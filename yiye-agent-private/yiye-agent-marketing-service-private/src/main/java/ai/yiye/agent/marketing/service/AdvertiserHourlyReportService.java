package ai.yiye.agent.marketing.service;

import ai.yiye.agent.domain.enumerations.AccountType;
import ai.yiye.agent.domain.form.CreativeBaseForm;
import ai.yiye.agent.domain.form.CreativeForm;
import ai.yiye.agent.domain.marketing.data.AdvertiserHourlyReport;
import ai.yiye.agent.domain.marketing.data.AdvertiserMaterial;
import ai.yiye.agent.marketing.form.MaterialAnalyseForm;
import ai.yiye.agent.marketing.mapper.AdvertiserAccountMapper;
import ai.yiye.agent.marketing.mapper.AdvertiserHourlyReportMapper;
import ai.yiye.agent.marketing.mapper.MaterialAnalyseMapper;
import ai.yiye.agent.marketing.service.dto.AdCreativeReportDto;
import ai.yiye.agent.marketing.service.dto.AdvertiserReportDto;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020-07-10 15:56
 **/
@Service
@DS("postgresql")
public class AdvertiserHourlyReportService extends ServiceImpl<AdvertiserHourlyReportMapper, AdvertiserHourlyReport> {
    @Autowired
    private AdvertiserAccountMapper advertiserAccountMapper;

    @Autowired
    private MaterialAnalyseMapper analyseMapper;
    public List<AdvertiserReportDto> obtainAdvertiserReport(Wrapper<AdvertiserHourlyReport> wrapper, String startTime, String endTime) {
        return this.baseMapper.obtainAdvertiserReport(wrapper, startTime, endTime);
    }

    @DS("postgresql")
    public IPage<AdCreativeReportDto> selectCreativeReport(IPage<AdCreativeReportDto> page, CreativeForm creativeForm) {
        if (Objects.nonNull(creativeForm.getMaterialId())) {
            return this.baseMapper.selectCreativeReport(page, creativeForm);
        }
        return page.setRecords(Collections.emptyList());
    }

    @DS("postgresql")
    public AdCreativeReportDto selectCreativeAllReport(CreativeForm creativeForm) {
        if (Objects.nonNull(creativeForm.getMaterialId())) {
            return this.baseMapper.selectCreativeAllReport(creativeForm);
        }
        return new AdCreativeReportDto();
    }

    /**
     * 处理账号条件
     * @param materialForm 条件
     * @return false 为没有投放账号
     */
    public boolean parameterProcess(MaterialAnalyseForm materialForm,CreativeBaseForm creativeForm){
        materialForm.setSystemAccountId(creativeForm.getSystemAccountId());
        materialForm.setSignature(creativeForm.getSignature());
        materialForm.setAccountType(creativeForm.getAccountType());
        ArrayList<String> signatureList = new ArrayList<>();
        signatureList.add(creativeForm.getSignature());
        materialForm.setSignatures(signatureList);
        //CMP
        if (AccountType.PMP.equals(materialForm.getAccountType())) {
            //查询客户id下一叶投放账号id列表
            materialForm.setAdvertiserAccountIds(advertiserAccountMapper.getAdvertiserAccountIds(materialForm.getSystemAccountId()));
            return !CollectionUtils.isEmpty(materialForm.getAdvertiserAccountIds());
        }

        //EMP
        if (AccountType.EMP.equals(materialForm.getAccountType())) {
            //EMP一叶投放账号id取systemAccountId
            materialForm.setAdvertiserAccountIds(Arrays.asList(materialForm.getSystemAccountId()));
            return !CollectionUtils.isEmpty(materialForm.getAdvertiserAccountIds());
        }

        return false;
    }

    public List<AdvertiserMaterial> selectVideoCoverList(MaterialAnalyseForm materialForm){
        if(!CollectionUtils.isEmpty(materialForm.getSignatures())) {
            return this.analyseMapper.selectVideoCoverList(materialForm);
        }
        return Collections.emptyList();
    }
}
