package ai.yiye.agent.marketing.controller;

import ai.yiye.agent.autoconfigure.web.AbstractCrudController;
import ai.yiye.agent.domain.DynamicField;
import ai.yiye.agent.domain.LandingPage;
import ai.yiye.agent.domain.dto.DynamicFieldDto;
import ai.yiye.agent.marketing.service.CustomerCalFormulaService;
import ai.yiye.agent.marketing.service.DynamicFieldService;
import ai.yiye.agent.marketing.service.UploadStrategyPlatformConfigRelService;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: 业务转换动态字段
 * @author: wang<PERSON>long
 * @time: 2021/4/20 15:58
 */
@RestController
@RequestMapping("/dynamic-field")
public class DynamicFieldController extends AbstractCrudController<DynamicFieldService, DynamicField, Long> {
    protected DynamicFieldController(DynamicFieldService service) {
        super(service);
    }

    @Autowired
    private CustomerCalFormulaService customerCalFormulaService;

    @Autowired
    private DynamicFieldService dynamicFieldService;
    @Autowired
    private UploadStrategyPlatformConfigRelService configRelService;


    @RequestMapping(value = "/add-field/{fieldType}", method = RequestMethod.POST)
    public String addField(@RequestBody DynamicFieldDto dynamicFieldDto, @PathVariable("fieldType") Integer fieldType) {
        boolean result = false;
        if (fieldType == 1) {
            //这是一个统计类型的数据
            result = customerCalFormulaService.addFormula(dynamicFieldDto);
        } else {
            DynamicField dynamicField = new DynamicField();
            BeanUtil.copyProperties(dynamicFieldDto, dynamicField);
            List<DynamicField> list = dynamicFieldService.list(new LambdaQueryWrapper<DynamicField>().eq(DynamicField::getField, dynamicField.getField()));
            if (list.size() > 0) {
                return result + "";
            } else {
                this.save(dynamicField);
                configRelService.saveUploadStrategyRel(dynamicField);
                //给pg和clickhouse增加列
                result = service.addColumn(dynamicField);
            }
        }
        return result + "";
    }

    //    /**
    //     * 后链路自定字段义列 - 列表
    //     */
    //    @GetMapping("/collect/list")
    //    public List<DynamicField> list() {
    //        return dynamicFieldService.list(new LambdaQueryWrapper<DynamicField>().orderByDesc(DynamicField::getUpdatedAt));
    //    }

}
