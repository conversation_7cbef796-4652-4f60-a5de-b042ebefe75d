package ai.yiye.agent.marketing.service;

import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AdvertiserAccountGroup;
import ai.yiye.agent.domain.EnterpriseWechatCustomerRetentionDayReport;
import ai.yiye.agent.domain.EnterpriseWechatsPmpRel;
import ai.yiye.agent.domain.User;
import ai.yiye.agent.domain.dto.EnterpriseWechatCustomerRetentionDTO;
import ai.yiye.agent.domain.enumerations.EnterpriseWechatCustomerRetentionEnum;
import ai.yiye.agent.domain.enumerations.UserRole;
import ai.yiye.agent.domain.landingpage.EnterpriseWechat;
import ai.yiye.agent.domain.vo.EnterpriseWechatCusRetentionEnterpriseDayReportVO;
import ai.yiye.agent.domain.vo.EnterpriseWechatCustomerRetentionVO;
import ai.yiye.agent.marketing.dto.EnterpriseWechatCustomerRetainDTO;
import ai.yiye.agent.marketing.mapper.EnterpriseWechatCustomerRetentionDayReportMapper;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EnterpriseWechatCustomerRetentionDayReportService extends ServiceImpl<EnterpriseWechatCustomerRetentionDayReportMapper, EnterpriseWechatCustomerRetentionDayReport> {


    @Resource
    private EnterpriseWechatService enterpriseWechatService;

    @Resource
    private EnterpriseWechatCusRetentionEnterpriseDayReportService enterpriseWechatCusRetentionEnterpriseDayReportService;

    @Resource
    private EnterpriseWechatCustomerRetentionDayReportReadonlyService enterpriseWechatCustomerRetentionDayReportReadonlyService;


    @Resource
    private EnterpriseWechatsPmpRelService enterpriseWechatsPmpRelService;

    @Resource
    private AdvertiserAccountGroupService advertiserAccountGroupService;

    public IPage<EnterpriseWechat> getEnterpriseWechatList(IPage<EnterpriseWechat> page, EnterpriseWechatCustomerRetainDTO dto, User user) {
        String agentId = TenantContextHolder.get();
        Long userId = null;
        if (Objects.nonNull(user) && !UserRole.ROLE_ADMIN.equals(user.getRole())) {
            userId = user.getId();
        }
        IPage<EnterpriseWechat> enterpriseWechatVoPage = enterpriseWechatService.getEnterpriseWechatsByAgentIdAndPmpId(userId,agentId, dto, page);
        List<EnterpriseWechat> records = enterpriseWechatVoPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            log.info("该项目下没有企业微信自建应用代开发授权数据");
            return enterpriseWechatVoPage;
        }
        generalPmps(records);
        return enterpriseWechatVoPage;
    }


    /**
     * 获取 pmps （关联项目名称）数据
     *
     * @param records
     */
    private void generalPmps(List<EnterpriseWechat> records) {
        String agentId = TenantContextHolder.get();
        List<String> corpIds = records.stream().map(EnterpriseWechat::getCorpid).collect(Collectors.toList());
        for (EnterpriseWechat enterpriseWechat : records) {
            //企业微信自建应用代开发授权 项目关联信息
            LambdaQueryWrapper<EnterpriseWechatsPmpRel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(EnterpriseWechatsPmpRel::getEnterpriseWechatsId, enterpriseWechat.getId()).eq(EnterpriseWechatsPmpRel::getAgentId, agentId);
            //查询当前账号的pmp关联列表
            List<EnterpriseWechatsPmpRel> list = enterpriseWechatsPmpRelService.list(lambdaQueryWrapper);
            if (!CollectionUtils.isEmpty(list)) {
                List<Long> collect = list.stream().map(EnterpriseWechatsPmpRel::getAdvertiserAccountGroupId).collect(Collectors.toList());
                List<AdvertiserAccountGroup> advertiserAccountGroups = advertiserAccountGroupService.listByIds(collect);
                if (!CollectionUtils.isEmpty(advertiserAccountGroups)) {
                    List<String> names = advertiserAccountGroups.stream().map(AdvertiserAccountGroup::getName).collect(Collectors.toList());
                    enterpriseWechat.setPmps(names);
                }
            }
            //查询所有账户的所有PMP下的共享数量
            int pmpCount = enterpriseWechatsPmpRelService.count(new LambdaQueryWrapper<EnterpriseWechatsPmpRel>().eq(EnterpriseWechatsPmpRel::getEnterpriseWechatsId, enterpriseWechat.getId())
                .eq(EnterpriseWechatsPmpRel::getAgentId, agentId));
            enterpriseWechat.setPmpCount(pmpCount);
            enterpriseWechat.setAllowUserNum(Objects.nonNull(enterpriseWechat.getAllowUser()) ? enterpriseWechat.getAllowUser().length : 0);
        }
    }


    /**
     * 企业微信管理——用户留存分析
     *
     * @param dto
     * @return
     */
    public List<EnterpriseWechatCustomerRetentionVO> getEnterpriseWechatCustomerRetentionList(EnterpriseWechatCustomerRetentionDTO dto) {

        if (StringUtils.isBlank(dto.getStartTime()) || StringUtils.isBlank(dto.getEndTime())) {
            log.info("企业微信管理——用户留存分析,查询时间范围不合法, 返回空数组, 入参 = {}", JSONObject.toJSON(dto));
            return new ArrayList<>();
        }
        List<EnterpriseWechatCustomerRetentionVO> list = new ArrayList<>();
        //数据概况企业微信维度(不传的话，默认查询企业微信维度)
        if (!Objects.equals(dto.getEnterpriseWechatCustomerRetentionEnum(), EnterpriseWechatCustomerRetentionEnum.WECHAT_CUSTOMER_SERVICE)) {
            if (StringUtils.isBlank(dto.getCorpId())) {
                return new ArrayList<>();
            }
            list = enterpriseWechatCusRetentionEnterpriseDayReportService.getEnterpriseWechatCustomerRetentionList(dto);
        }

        if (!list.isEmpty()) {
            //如果关联表没有数据，查一下客服表里面有没有名称
            for (EnterpriseWechatCustomerRetentionVO vo : list) {
                if (StringUtils.isBlank(vo.getWechatUserName())) {
                    String userId = vo.getWechatCustomerServiceUserId();
                    String userName = this.baseMapper.queryWechatUserNameByUserId(vo.getWechatCustomerServiceUserId());
                    log.info("{}的用户名称为空，查询一下客服表, 返回的结果:{}", userId, userName);
                    if (StringUtils.isNotBlank(userName)) {
                        vo.setWechatUserName(userName);
                    }
                }
            }
        }
        return list;
    }


    /**
     * 以企业微信客服为维度，统计客户留存报表
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param monthStartTime 开始时间前30日的日期
     */
    public void solidEnterpriseWechatCusRetentionEnterpriseRecord(LocalDateTime startTime, LocalDateTime endTime, LocalDateTime monthStartTime) {
        String agentId = TenantContextHolder.get();
        log.info("以企业微信客服为维度，固化用户{}留存分析报表，入参，startTime:{},endTime:{},monthStartTime:{}", agentId, startTime, endTime, monthStartTime);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            if (Objects.isNull(startTime) || Objects.isNull(endTime) || Objects.isNull(monthStartTime)) {
                log.error("以企业微信客服为维度，固化用户留存分析报表，时间参数不合法，直接结束任务，startTime:{},endTime:{},monthStartTime:{}", startTime, endTime, monthStartTime);
            }
            List<EnterpriseWechatCusRetentionEnterpriseDayReportVO> list = enterpriseWechatCustomerRetentionDayReportReadonlyService.queryEnterpriseWechatCusRetentionRecord(startTime, endTime, monthStartTime);
            if (!list.isEmpty()) {
                Lists.partition(list, 200).forEach(t -> this.baseMapper.saveOrUpdateBatch(list));
            }
        } catch (Exception e) {
            log.error("以企业微信客服为维度，固化用户{}留存分析报表，异常， startTime = {}, endTime = {}, monthStartTime = {} ", agentId, startTime, endTime, monthStartTime, e);
        } finally {
            stopWatch.stop();
            log.info("==============【以企业微信客服为维度，固化用户{}留存分析报表-结束，耗时:{}S， startTime = {}, endTime = {}, monthStartTime = {}】==============", agentId, stopWatch.getTotalTimeSeconds(), startTime, endTime, monthStartTime);
        }
    }

    public IPage<EnterpriseWechatCustomerRetentionVO> getWechatCustomerServiceList(IPage<EnterpriseWechatCustomerRetentionVO> page, EnterpriseWechatCustomerRetentionDTO dto) {
        if (StringUtils.isBlank(dto.getStartTime()) || StringUtils.isBlank(dto.getEndTime())) {
            return new Page<>();
        }
        IPage<EnterpriseWechatCustomerRetentionVO> customerRetentionVOIPage = this.baseMapper.getEnterpriseWechatCustomerRetentionWithServiceList(dto, page);
        List<EnterpriseWechatCustomerRetentionVO> list = customerRetentionVOIPage.getRecords();
        if (!list.isEmpty()) {
            //如果关联表没有数据，查一下客服表里面有没有名称
            for (EnterpriseWechatCustomerRetentionVO vo : list) {
                if (StringUtils.isBlank(vo.getWechatUserName())) {
                    String userId = vo.getWechatCustomerServiceUserId();
                    String userName = this.baseMapper.queryWechatUserNameByUserId(vo.getWechatCustomerServiceUserId());
                    log.info("{}的用户名称为空，查询一下客服表, 返回的结果:{}", userId, userName);
                    if (StringUtils.isNotBlank(userName)) {
                        vo.setWechatUserName(userName);
                    }
                }
            }
        }
        return customerRetentionVOIPage;
    }

}
