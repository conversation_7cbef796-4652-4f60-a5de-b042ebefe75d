package ai.yiye.agent.marketing.controller;

import ai.yiye.agent.domain.dto.designert.MaterialDesignerTeamRelationDto;
import ai.yiye.agent.marketing.service.MaterialDesignerTeamRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 素材-团队角色信息占比
 * @author: zlk
 * @time: 2021/1/12
 */
@RestController
@RequestMapping(path = "/team-designer-ratio", produces = MediaType.APPLICATION_JSON_VALUE)
public class MaterialDesignerTeamRelationController {

    @Autowired
    private MaterialDesignerTeamRelationService materialDesignerTeamRelationService;

    /**
     * 新增与编辑-素材对应团队角色占比(单个素材)
     *
     * @param materialDesignerTeamRelationDto 素材-团队角色占比信息
     */

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public void saveOrUpdateMaterialDesignerTeamReal(@RequestBody MaterialDesignerTeamRelationDto materialDesignerTeamRelationDto) {
        materialDesignerTeamRelationService.saveOrUpdateMaterialDesignerTeamReal(materialDesignerTeamRelationDto);
    }

    @GetMapping(path = "{materialId}")
    public MaterialDesignerTeamRelationDto list(@PathVariable Long materialId) {
        return materialDesignerTeamRelationService.getDesignerTeamDetailed(materialId);
    }

}
