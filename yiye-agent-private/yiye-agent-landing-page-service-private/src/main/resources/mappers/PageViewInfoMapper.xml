<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.yiye.agent.landingpage.mapper.PageViewInfoMapper">

    <select id="getTargetData" resultType="ai.yiye.agent.domain.vo.AdvertiserAccountGroupReportCliskhouse">
        select count(*) as landing_page_pv,
        coalesce(count(distinct uid), 0) as landing_page_uv,
        coalesce(sum(fill ), 0) as fill_num,
        coalesce(sum(fill ) - sum(fill * widget_template_type), 0) as fill_count_num,
        case when sum(fill ) - sum(fill * widget_template_type) = 0 or count(*) = 0 THEN 0 ELSE (sum(fill ) - sum(fill *
        widget_template_type)) / count(*) *100 end as fill_count_rate,
        coalesce(sum(fill * widget_template_type), 0) as order_num,
        case when sum(fill * widget_template_type) = 0 or count(*) = 0 THEN 0 ELSE sum(fill * widget_template_type) /
        count(*) *100 end as order_count_rate,
        sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end ) as order_finish_num,

        case when sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end ) = 0 or
        count(*) = 0 THEN 0 ELSE sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end
        ) / count(*) *100 end as order_finish_rate,
        coalesce(sum(identify_qr_code_status), 0) as identify_qr_code_num,
        case when sum(identify_qr_code_status) = 0 or count(*) = 0 THEN 0 ELSE sum(identify_qr_code_status) / count(*)
        *100 end as identify_qr_code_rate,
        coalesce(sum(add_enterprise_wechat_status), 0) as add_work_wechat_num,
        case when sum(add_enterprise_wechat_status) = 0 or count(*) = 0 THEN 0 ELSE sum(add_enterprise_wechat_status) /
        count(*) *100 end as add_work_wechat_rate,
        coalesce(sum(follow_official_account_status), 0) as follow_official_account_num,
        case when sum(follow_official_account_status) = 0 or count(*) = 0 THEN 0 ELSE
        sum(follow_official_account_status) / count(*) *100 end as follow_official_account_rate,
        coalesce(sum(online_shop_buy_goods_product_count), 0) as online_shop_buy_goods_success_num,
        coalesce(case when count(1) = 0 then 0 else sum(online_shop_buy_goods_product_count) / count(1) ::NUMERIC * 100 end, 0)
        as online_shop_buy_goods_success_rate,

        case when sum(fill ) - sum(fill * widget_template_type) = 0 THEN 0 ELSE #{cost} / (sum(fill ) - sum(fill *
        widget_template_type)) end as fill_count_cost,
        case when sum(fill * widget_template_type) = 0 THEN 0 ELSE #{cost} / sum(fill * widget_template_type) end as
        order_count_cost,
        case when sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end ) = 0 THEN 0
        ELSE #{cost} / sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end ) end as
        order_finish_cost,
        case when sum(identify_qr_code_status) = 0 THEN 0 ELSE #{cost} / sum(identify_qr_code_status) end as
        identify_qr_code_cost,
        case when sum(add_enterprise_wechat_status) = 0 then 0 ELSE #{cost} / sum(add_enterprise_wechat_status) end as
        add_work_wechat_cost,
        case when sum(follow_official_account_status) = 0 then 0 ELSE #{cost} / sum(follow_official_account_status) end
        as follow_official_account_cost,
        case when sum(online_shop_buy_goods_product_count) = 0 then 0 ELSE #{cost} / sum(online_shop_buy_goods_product_count) end as
        online_shop_buy_goods_success_cost,

        CASE when count(*) = 0 then 0 else round(cast (sum(CASE when length_of_stay >= 120 then 120 else length_of_stay
        end) / count(*) as numeric), 1) end as landing_avg_stay
        from
        page_view_info
        <where>
            <if test="startDay != null">
                and created_at &gt;= to_timestamp(#{startDay}, 'yyyy-MM-dd hh24:mi:ss')
            </if>
            <if test="endDay != null">
                and created_at &lt;= to_timestamp(#{endDay}, 'yyyy-MM-dd hh24:mi:ss')
            </if>
            and advertiser_account_group_id = #{id}
        </where>
    </select>

    <select id="getAccountTargetData" resultType="ai.yiye.agent.domain.vo.AdvertiserAccountGroupReportCliskhouse">
        select count(*)                                                                    as landing_page_pv,
               coalesce(count(distinct uid), 0)                                            as landing_page_uv,
               coalesce(sum(fill), 0)                                                      as fill_num,
               coalesce(sum(fill) - sum(fill * widget_template_type), 0)                   as fill_count_num,
               case
                   when sum(fill) - sum(fill * widget_template_type) = 0 or count(*) = 0 THEN 0
                   ELSE
                       (sum(fill) - sum(fill * widget_template_type)) / count(*) * 100 end as fill_count_rate,
               coalesce(sum(fill * widget_template_type), 0)                               as order_num,
               case
                   when sum(fill * widget_template_type) = 0 or count(*) = 0 THEN 0
                   ELSE
                       sum(fill * widget_template_type) / count(*) * 100 end               as order_count_rate,
               sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end)
                                                                                           as order_finish_num,

               case
                   when sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end) = 0 or
                        count(*) = 0 THEN 0
                   ELSE
                               sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end) /
                               count(*) * 100 end                                          as
                                                                                              order_finish_rate,
               coalesce(sum(identify_qr_code_status), 0)                                   as identify_qr_code_num,
               case
                   when sum(identify_qr_code_status) = 0 or count(*) = 0 THEN 0
                   ELSE
                       sum(identify_qr_code_status) / count(*) * 100 end                   as identify_qr_code_rate,
               coalesce(sum(add_enterprise_wechat_status), 0)                              as add_work_wechat_num,
               case
                   when sum(add_enterprise_wechat_status) = 0 or count(*) = 0 THEN 0
                   ELSE
                       sum(add_enterprise_wechat_status) / count(*) * 100 end              as add_work_wechat_rate,
               coalesce(sum(follow_official_account_status), 0)                            as follow_official_account_num,
               case
                   when sum(follow_official_account_status) = 0 or count(*) = 0 THEN 0
                   ELSE
                       sum(follow_official_account_status) / count(*) * 100 end            as follow_official_account_rate,

               case
                   when sum(fill) - sum(fill * widget_template_type) = 0 THEN 0
                   ELSE
                       #{cost} / (sum(fill) - sum(fill * widget_template_type)) end        as fill_count_cost,
               case
                   when sum(fill * widget_template_type) = 0 THEN 0
                   ELSE
                       #{cost} / sum(fill * widget_template_type) end                      as order_count_cost,
               case
                   when sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end) = 0
                       THEN 0
                   ELSE
                           #{cost} / sum(CASE
                                             when payment_type is not null and order_status in (0, 1, 3) then 1
                                             else 0 end) end                               as
                                                                                              order_finish_cost,
               case
                   when sum(identify_qr_code_status) = 0 THEN 0
                   ELSE
                       #{cost} / sum(identify_qr_code_status) end                          as identify_qr_code_cost,
               case
                   when sum(add_enterprise_wechat_status) = 0 then 0
                   ELSE
                       #{cost} / sum(add_enterprise_wechat_status) end                     as add_work_wechat_cost,

               CASE
                   when count(*) = 0 then 0
                   else round(cast(sum(CASE
                                           when length_of_stay >= 120 then 120
                                           else length_of_stay
                       end) / count(*) as numeric), 1) end                                 as landing_avg_stay
        from page_view_info
        where created_at between to_timestamp(#{startDay}, 'yyyy-MM-dd hh24:mi:ss') and
            to_timestamp(#{endDay}, 'yyyy-MM-dd hh24:mi:ss')
          and advertiser_account_group_id = #{id}
    </select>

    <!--旧查询的曝光口径-->
    <select id="getAccountTargetDataNewView" resultType="ai.yiye.agent.domain.vo.AdvertiserAccountGroupReportCliskhouse">

        select
            count(*)  as landing_page_pv,

            coalesce(sum(fill), 0) as fill_num,

            coalesce(sum(fill) - sum(fill * widget_template_type), 0)                   as fill_count_num,

            case when sum(fill) - sum(fill * widget_template_type) = 0 or count(*) = 0 THEN 0   ELSE   (sum(fill) - sum(fill * widget_template_type)) / count(*) * 100 end as fill_count_rate,

            coalesce(sum(fill * widget_template_type), 0)  as order_num,  case  when sum(fill * widget_template_type) = 0 or count(*) = 0 THEN 0  ELSE   sum(fill * widget_template_type) / count(*) * 100      end  as order_count_rate,

            sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end)  as order_finish_num,

            coalesce(sum(identify_qr_code_status), 0)                                   as identify_qr_code_num,

            coalesce(sum(add_enterprise_wechat_status), 0)                              as add_work_wechat_num,

            coalesce(sum(follow_official_account_status), 0)                            as official_focus_count,

            case  when sum(fill) - sum(fill * widget_template_type) = 0 THEN 0  ELSE   #{advertiserAccountGroupReport.cost} / (sum(fill) - sum(fill * widget_template_type)) end        as fill_count_cost,

            case  when sum(fill * widget_template_type) = 0 THEN 0  ELSE   #{advertiserAccountGroupReport.cost} / sum(fill * widget_template_type) end  as order_count_cost,

            case   when sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end) = 0  THEN 0 ELSE  #{advertiserAccountGroupReport.cost} / sum(CASE when payment_type is not null and order_status in (0, 1, 3)            then 1  else 0 end) end        as       order_finish_cost,

            case  when sum(identify_qr_code_status) = 0 THEN 0  ELSE   #{advertiserAccountGroupReport.cost} / sum(identify_qr_code_status) end                          as identify_qr_code_cost,

            case when sum(add_enterprise_wechat_status) = 0 then 0  ELSE #{advertiserAccountGroupReport.cost} / sum(add_enterprise_wechat_status) end                   as add_work_wechat_cost,

            #{advertiserAccountGroupReport.viewNum} as view_num,
            #{advertiserAccountGroupReport.clickNum} as click_num,
            #{advertiserAccountGroupReport.convertCount} as convert_num,
            #{advertiserAccountGroupReport.deepConvertNum} as deep_convert_num,

            case when coalesce(#{advertiserAccountGroupReport.convertCount},0) = 0 then 0 else coalesce(#{advertiserAccountGroupReport.cost},0) / coalesce(#{advertiserAccountGroupReport.convertCount},0)   end       as  target_convert_cost,

            case when coalesce(#{advertiserAccountGroupReport.deepConvertNum},0) = 0 then 0 else coalesce(#{advertiserAccountGroupReport.cost},0) / coalesce(#{advertiserAccountGroupReport.deepConvertNum},0)   end       as  deep_convert_cost

        from page_view_info
        where created_at between to_timestamp(#{startDay}, 'yyyy-MM-dd hh24:mi:ss') and to_timestamp(#{endDay}, 'yyyy-MM-dd hh24:mi:ss')and advertiser_account_group_id = #{advertiserAccountGroupId}

    </select>


    <!--旧查询的转化口径-->
    <select id="getAccountTargetDataNewConvert" resultType="ai.yiye.agent.domain.vo.AdvertiserAccountGroupReportCliskhouse">

        select
            count(*)  as landing_page_pv,

            coalesce(sum(fill), 0) as fill_num,

            coalesce(sum(fill) - sum(fill * widget_template_type), 0)                   as fill_count_num,

            case when sum(fill) - sum(fill * widget_template_type) = 0 or count(*) = 0 THEN 0   ELSE   (sum(fill) - sum(fill * widget_template_type)) / count(*) * 100 end as fill_count_rate,

            coalesce(sum(fill * widget_template_type), 0)  as order_num,  case  when sum(fill * widget_template_type) = 0 or count(*) = 0 THEN 0  ELSE   sum(fill * widget_template_type) / count(*) * 100      end  as order_count_rate,

            sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end)  as order_finish_num,

            coalesce(sum(identify_qr_code_status), 0)                                   as identify_qr_code_num,

            coalesce(sum(add_enterprise_wechat_status), 0)                              as add_work_wechat_num,

            coalesce(sum(follow_official_account_status), 0)                            as official_focus_count,

            case  when sum(fill) - sum(fill * widget_template_type) = 0 THEN 0  ELSE   #{advertiserAccountGroupReport.cost} / (sum(fill) - sum(fill * widget_template_type)) end        as fill_count_cost,

            case  when sum(fill * widget_template_type) = 0 THEN 0  ELSE   #{advertiserAccountGroupReport.cost} / sum(fill * widget_template_type) end  as order_count_cost,

            case   when sum(CASE when payment_type is not null and order_status in (0, 1, 3) then 1 else 0 end) = 0  THEN 0 ELSE  #{advertiserAccountGroupReport.cost} / sum(CASE when payment_type is not null and order_status in (0, 1, 3)            then 1  else 0 end) end        as       order_finish_cost,

            case  when sum(identify_qr_code_status) = 0 THEN 0  ELSE   #{advertiserAccountGroupReport.cost} / sum(identify_qr_code_status) end                          as identify_qr_code_cost,

            case when sum(add_enterprise_wechat_status) = 0 then 0  ELSE #{advertiserAccountGroupReport.cost} / sum(add_enterprise_wechat_status) end                   as add_work_wechat_cost,

            #{advertiserAccountGroupReport.viewNum} as view_num,
            #{advertiserAccountGroupReport.clickNum} as click_num,
            #{advertiserAccountGroupReport.convertCount} as convert_num,
            #{advertiserAccountGroupReport.deepConvertNum} as deep_convert_num,

            case when coalesce(#{advertiserAccountGroupReport.convertCount},0) = 0 then 0 else coalesce(#{advertiserAccountGroupReport.cost},0) / coalesce(#{advertiserAccountGroupReport.convertCount},0)   end       as  target_convert_cost,

            case when coalesce(#{advertiserAccountGroupReport.deepConvertNum},0) = 0 then 0 else coalesce(#{advertiserAccountGroupReport.cost},0) / coalesce(#{advertiserAccountGroupReport.deepConvertNum},0)   end       as  deep_convert_cost

        from page_view_info
        where convert_data_time between to_timestamp(#{startDay}, 'yyyy-MM-dd hh24:mi:ss') and to_timestamp(#{endDay}, 'yyyy-MM-dd hh24:mi:ss')and advertiser_account_group_id = #{advertiserAccountGroupId}

    </select>



    <select id="stayLengthDetail" resultType="ai.yiye.agent.domain.pageview.StayAccessDepthDetailRespose">
        select
        CASE when length_of_stay >=0 and length_of_stay &lt;=3 then 0
        when length_of_stay >3 and length_of_stay &lt;=10 then 1
        when length_of_stay >10 and length_of_stay &lt;=15 then 2
        when length_of_stay >15 and length_of_stay &lt;=20 then 3
        when length_of_stay >20 and length_of_stay &lt;=30 then 4
        when length_of_stay >30 and length_of_stay &lt;=45 then 5
        when length_of_stay >45 and length_of_stay &lt;=60 then 6
        when length_of_stay >60 and length_of_stay &lt;=120 then 7
        when length_of_stay >120 and length_of_stay &lt;=600 then 8
        else 9
        end as level,
        sum(a.page_view_num) as page_view_num,
        sum(a.order_submit_num) as order_submit_num,
        sum(a.form_submit_num) as form_submit_num,
        sum(a.payment_num) as payment_num,
        sum(a.identify_qr_code_status_num) as identify_qr_code_status_num,
        sum(a.add_enterprise_wechat_status_num) as add_enterprise_wechat_status_num,
        sum(a.follow_official_account_num) as follow_official_account_num,
        sum(a.ad_upload_num) as ad_upload_num,
        sum(a.online_shop_buy_goods_product_count) as online_shop_buy_goods_success_num,
        COALESCE ( SUM ( A.identify_group_qr_code_num ), 0 ) AS identify_group_qr_code_num,
        COALESCE ( SUM ( A.add_work_wechat_group_num ), 0 ) AS add_work_wechat_group_num,
        COALESCE ( SUM ( A.income_line_num ), 0 ) AS income_line_num,
        COALESCE ( SUM ( a.official_identify_qr_code_num ), 0 ) AS official_identify_qr_code_num,
        <!--1.256.0-->
        COALESCE ( SUM ( A.douyin_applet_order_submit_num ), 0 ) AS douyin_applet_order_submit_num,
        COALESCE ( SUM ( A.douyin_applet_order_finish_num ), 0 ) AS douyin_applet_order_finish_num,
        sum(a.send_image_or_qr_code_num) as send_image_or_qr_code_num,
        sum(a.mini_program_news_num) as mini_program_news_num,
        sum(a.jump_to_super_red_envelope_num) as jump_to_super_red_envelope_num,
        sum(a.success_send_welcome_msg_num) as success_send_welcome_msg_num,
        sum(a.start_open_chat_num) as start_open_chat_num,
        sum(a.third_open_chat_num) as third_open_chat_num,
        sum(a.fifth_open_chat_num) as fifth_open_chat_num,
        sum(a.tenth_open_chat_num) as tenth_open_chat_num,
        coalesce (sum(a.number_of_orders_completed_for_coupon_num), 0) as number_of_orders_completed_for_coupon_num
        from
        (
        select
        page_view_info.length_of_stay,
        count(*) as page_view_num,
        sum(fill) as fill_num,
        <!--1.256.0迭代之前的计算逻辑 sum(fill * widget_template_type) as order_submit_num,-->
        sum(COALESCE(CASE WHEN page_view_info.widget_template_type = 1 and (page_view_info.order_template_type = 0 or page_view_info.order_template_type = 1002) THEN fill  ELSE 0 END, 0)) as order_submit_num,
        (sum(fill) - sum(fill * widget_template_type)) as form_submit_num,
        sum(CASE when page_view_info.payment_type is not null and (page_view_info.order_template_type = 0 or page_view_info.order_template_type = 1002) and page_view_info.order_status in (0, 1, 3) then 1 else 0 end) as payment_num,

        <!--1.256.0-->
        sum(COALESCE(CASE WHEN page_view_info.widget_template_type = 8 and page_view_info.order_template_type = 1 THEN fill  ELSE 0 END, 0)) as douyin_applet_order_submit_num ,
        sum(CASE when page_view_info.payment_type is not null and page_view_info.order_template_type = 1 and page_view_info.order_status in (0, 1, 3) then 1 else 0 end) as douyin_applet_order_finish_num ,

        sum(identify_qr_code_status) as identify_qr_code_status_num,
        sum(online_shop_buy_goods_product_count) as online_shop_buy_goods_product_count,
        sum(page_view_info.add_enterprise_wechat_status) as add_enterprise_wechat_status_num,
        sum(page_view_info.follow_official_account_status) as follow_official_account_num,
        sum(page_view_info.ad_upload_status) as ad_upload_num,
        sum(page_view_info.online_shop_buy_goods_product_count) as online_shop_buy_goods_success_num,
        sum(coalesce(page_view_info.identify_group_chat_qr_code_status,0)) as identify_group_qr_code_num,
        sum(coalesce(page_view_info.add_group_chat_status,0)) as add_work_wechat_group_num,
        sum(coalesce(page_view_info.into_wechat_customer_service_session_status,0)) as income_line_num,
        sum(coalesce(page_view_info.official_identify_qr_code_status,0)) as official_identify_qr_code_num,

        SUM(COALESCE(CASE WHEN page_view_info.send_qr_code_or_img_status = 1 and page_view_info.send_image_type != 6 THEN 1 ELSE 0 END, 0)) AS send_image_or_qr_code_num,
        SUM(COALESCE(CASE WHEN page_view_info.send_mini_program_card_status = 1  THEN 1 ELSE 0 END, 0)) AS mini_program_news_num,
        SUM(COALESCE(CASE WHEN page_view_info.jump_to_super_red_envelope_status = 1  THEN 1 ELSE 0 END, 0)) AS jump_to_super_red_envelope_num,
        SUM(COALESCE(CASE WHEN page_view_info.success_send_welcome_msg_status = 1  THEN 1 ELSE 0 END, 0)) AS success_send_welcome_msg_num,
        <!-- 1.306.0 开口次数统计-->
        SUM(COALESCE(CASE WHEN page_view_info.open_chat_num >= 1 THEN 1  ELSE 0 END, 0)) AS start_open_chat_num,
        SUM(COALESCE(CASE WHEN page_view_info.open_chat_num >= 3 THEN 1  ELSE 0 END, 0)) AS third_open_chat_num,
        SUM(COALESCE(CASE WHEN page_view_info.open_chat_num >= 5 THEN 1  ELSE 0 END, 0)) AS fifth_open_chat_num,
        SUM(COALESCE(CASE WHEN page_view_info.open_chat_num >= 10 THEN 1  ELSE 0 END, 0)) AS tenth_open_chat_num,

        sum(CASE when customer.payment_type is not null and customer.order_status in (0, 1, 3) and customer.use_coupon =
        true then 1 else 0 end)
        as number_of_orders_completed_for_coupon_num
        from page_view_info left join customer on page_view_info.customer_id = customer.id
        <where>
            <if test="landingPageId!=null and landingPageId!=''">
                AND page_view_info.landing_page_id = #{landingPageId}
            </if>
            <if test="channelId != null">
                and page_view_info.channel_id = #{channelId}
            </if>
            <if test="startTime != null and startTime != ''">
                AND page_view_info.created_at >= to_timestamp(#{startTime}, 'yyyy-MM-dd')
            </if>
            <if test="endTime != null and endTime != ''">
                AND to_timestamp(#{endTime}, 'yyyy-MM-dd') > page_view_info.created_at
            </if>
        </where>
        group by page_view_info.length_of_stay
        )
        a group by level
    </select>

    <select id="accessDepth" resultType="ai.yiye.agent.domain.pageview.StayAccessDepthDetailRespose">
        select
        CASE when access_depth = 0 then 0
        when access_depth >0 and access_depth &lt;=10 then 1
        when access_depth >10 and access_depth &lt;=20 then 2
        when access_depth >20 and access_depth &lt;=30 then 3
        when access_depth >30 and access_depth &lt;=40 then 4
        when access_depth >40 and access_depth &lt;=50 then 5
        when access_depth >50 and access_depth &lt;=60 then 6
        when access_depth >60 and access_depth &lt;=70 then 7
        when access_depth >70 and access_depth &lt;=80 then 8
        when access_depth >80 and access_depth &lt;=90 then 9
        else 10
        end as level,
        sum(a.page_view_num) as page_view_num,
        sum(a.order_submit_num) as order_submit_num,
        sum(a.form_submit_num) as form_submit_num,
        sum(a.payment_num) as payment_num,
        <!--1.256.0-->
        sum(a.douyin_applet_order_submit_num) as douyin_applet_order_submit_num,
        sum(a.douyin_applet_order_finish_num) as douyin_applet_order_finish_num,

        sum(a.identify_qr_code_status_num) as identify_qr_code_status_num,
        sum(a.add_enterprise_wechat_status_num) as add_enterprise_wechat_status_num,
        sum(a.follow_official_account_num) as follow_official_account_num,
        sum(a.ad_upload_num) as ad_upload_num,
        sum(a.online_shop_buy_goods_product_count) as online_shop_buy_goods_success_num,
        coalesce(sum(a.identify_group_qr_code_num),0) as identify_group_qr_code_num,
        coalesce(sum(a.add_work_wechat_group_num),0) as add_work_wechat_group_num,
        coalesce(sum(a.income_line_num),0) as income_line_num,
        coalesce(sum(a.official_identify_qr_code_num),0) as official_identify_qr_code_num,
        sum(a.send_image_or_qr_code_num) as send_image_or_qr_code_num,
        sum(a.mini_program_news_num) as mini_program_news_num,
        sum(a.jump_to_super_red_envelope_num) as jump_to_super_red_envelope_num,
        sum(a.success_send_welcome_msg_num) as success_send_welcome_msg_num,
        sum(a.start_open_chat_num) as start_open_chat_num,
        sum(a.third_open_chat_num) as third_open_chat_num,
        sum(a.fifth_open_chat_num) as fifth_open_chat_num,
        sum(a.tenth_open_chat_num) as tenth_open_chat_num,
        coalesce (sum(a.number_of_orders_completed_for_coupon_num), 0) as number_of_orders_completed_for_coupon_num
        from
        (
        select
            page_view_info.access_depth,
            count(*) as page_view_num,
            sum(fill) as fill_num,
            sum(COALESCE(CASE WHEN widget_template_type = 1 and (page_view_info.order_template_type = 0 or page_view_info.order_template_type = 1002) THEN fill * widget_template_type ELSE 0 END, 0)) as order_submit_num ,
            (sum(fill) - sum(fill * widget_template_type)) as form_submit_num,
            sum(CASE when page_view_info.payment_type is not null and (page_view_info.order_template_type = 0 or page_view_info.order_template_type = 1002) and page_view_info.order_status in (0, 1, 3) then 1 else 0 end) as payment_num,

            <!--1.256.0-->
            sum(COALESCE(CASE WHEN widget_template_type = 8 and page_view_info.order_template_type = 1 THEN fill  ELSE 0 END, 0)) as douyin_applet_order_submit_num ,
            sum(CASE when page_view_info.payment_type is not null and page_view_info.order_template_type = 1 and page_view_info.order_status in (0, 1, 3) then 1 else 0 end) as douyin_applet_order_finish_num ,

            sum(identify_qr_code_status) as identify_qr_code_status_num,
            sum(online_shop_buy_goods_product_count) as online_shop_buy_goods_product_count,
            sum(page_view_info.add_enterprise_wechat_status) as add_enterprise_wechat_status_num,
            sum(page_view_info.follow_official_account_status) as follow_official_account_num,
            sum(page_view_info.ad_upload_status) as ad_upload_num,
            sum(page_view_info.online_shop_buy_goods_product_count) as online_shop_buy_goods_success_num,
            sum(coalesce(page_view_info.identify_group_chat_qr_code_status,0)) as identify_group_qr_code_num,
            sum(coalesce(page_view_info.add_group_chat_status,0)) as add_work_wechat_group_num,
            sum(coalesce(page_view_info.into_wechat_customer_service_session_status,0)) as income_line_num,
            sum(coalesce(page_view_info.official_identify_qr_code_status,0)) as official_identify_qr_code_num,
            SUM(COALESCE(CASE WHEN page_view_info.send_qr_code_or_img_status = 1 and page_view_info.send_image_type != 6 THEN 1 ELSE 0 END, 0)) AS send_image_or_qr_code_num,
            SUM(COALESCE(CASE WHEN page_view_info.send_mini_program_card_status = 1  THEN 1 ELSE 0 END, 0)) AS mini_program_news_num,
            SUM(COALESCE(CASE WHEN page_view_info.jump_to_super_red_envelope_status = 1  THEN 1 ELSE 0 END, 0)) AS jump_to_super_red_envelope_num,
            SUM(COALESCE(CASE WHEN page_view_info.success_send_welcome_msg_status = 1  THEN 1 ELSE 0 END, 0)) AS success_send_welcome_msg_num,
            <!-- 1.306.0 开口次数统计-->
            SUM(COALESCE(CASE WHEN page_view_info.open_chat_num >= 1 THEN 1  ELSE 0 END, 0)) AS start_open_chat_num,
            SUM(COALESCE(CASE WHEN page_view_info.open_chat_num >= 3 THEN 1  ELSE 0 END, 0)) AS third_open_chat_num,
            SUM(COALESCE(CASE WHEN page_view_info.open_chat_num >= 5 THEN 1  ELSE 0 END, 0)) AS fifth_open_chat_num,
            SUM(COALESCE(CASE WHEN page_view_info.open_chat_num >= 10 THEN 1  ELSE 0 END, 0)) AS tenth_open_chat_num,


        sum(CASE when customer.payment_type is not null and customer.order_status in (0, 1, 3) and customer.use_coupon = true then 1 else 0 end) as number_of_orders_completed_for_coupon_num
        from
        page_view_info left join customer on page_view_info.customer_id = customer.id
        <where>
            <if test="landingPageId!=null and landingPageId!=''">
                AND page_view_info.landing_page_id = #{landingPageId}
            </if>
            <if test="channelId != null">
                and page_view_info.channel_id = #{channelId}
            </if>
            <if test="startTime != null and startTime != ''">
                AND page_view_info.created_at >= to_timestamp(#{startTime}, 'yyyy-MM-dd')
            </if>
            <if test="endTime != null and endTime != ''">
                AND to_timestamp(#{endTime}, 'yyyy-MM-dd') > page_view_info.created_at
            </if>
        </where>
        group by page_view_info.access_depth
        )
        a group by level
    </select>

    <select id="listLandingPageChannel" resultType="ai.yiye.agent.domain.LandingPageChannel">
        select * from landing_page_channels
        <where>
            <if test="landingPageQuery.landingPageId != null">
                and landing_page_id = #{landingPageQuery.landingPageId}
            </if>
            <if test="landingPageQuery.deleteStatus != null">
                and delete_status = #{landingPageQuery.deleteStatus}
            </if>
        </where>
    </select>

    <select id="getPgLandingPageChannel" resultType="ai.yiye.agent.domain.LandingPageChannel">
        <!--重复访客数 -->
        <!--with repeatVisitorNum as (
        select channel_id, count(*) as repeat_visitor_num
        from (
        select channel_id, uid, count(*)
        from page_view_info
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime">
                and created_at between #{landingPageQuery.startTime} and #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and landing_page_id = #{landingPageQuery.landingPageId}
            </if>
        </where>
        group by channel_id, uid
        having count(*) > 1) a
        group by channel_id
        ),-->
        <!--使用优惠券完成订单数 -->
        with numberOfOrdersCompletedForCouponNum as (
            select channel_id,
            sum(CASE when payment_type is not null and order_status in (0, 1, 3) and use_coupon = true then 1 else 0 end)
            as number_of_orders_completed_for_coupon_num,
            sum(CASE when douyin_auth_phone is not null then 1 else 0 end) as phone_number_recieved_num
            from customer
            <where>
                <if test="landingPageQuery.startTime != null and landingPageQuery.endTime != null">
                    and created_at >= #{landingPageQuery.startTime} and created_at &lt; #{landingPageQuery.endTime}
                </if>
                <if test="landingPageQuery.advertiserAccountGroupId != null">
                    and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
                </if>
                <if test="landingPageQuery.landingPageId != null">
                    and landing_page_id = #{landingPageQuery.landingPageId}
                </if>
            </where>
            group by channel_id
        ),
        <!--公众号助手发码加粉后入群数-->
        officialAssistantData as(
            SELECT
                follow_landing_page_id as landing_page_id,
                follow_channel_id as channel_id,
                count(1) as  add_group_after_follow_official_account_num
            FROM
                landing_page_wechat_official_account_customer
            <where>
                <if test="landingPageQuery.startTime != null and landingPageQuery.endTime">
                    and group_chat_join_time >= #{landingPageQuery.startTime} and group_chat_join_time &lt; #{landingPageQuery.endTime}
                </if>
                <if test="landingPageQuery.advertiserAccountGroupId != null">
                    and wechat_applet_user_pmp_id = #{landingPageQuery.advertiserAccountGroupId}
                </if>
                and followed_add_enterprise_wechat = 1
            </where>
            GROUP BY landing_page_id, channel_id
        ),

        officialAccountAddCustomer as (
        select
        wechat_official_account_landing_page_channel_id as wechat_official_account_landing_page_channel_id,
        count(1) as official_add_customer_num
        from customer
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime != null">
                and created_at >= #{landingPageQuery.startTime} and created_at &lt; #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and wechat_official_account_landing_page_id = #{landingPageQuery.landingPageId}
            </if>
        </where>
        group by wechat_official_account_landing_page_channel_id
        ),
        <!-- 饿了么数据 -->
        <!-- 饿了么回传事件数据 -->
        elmData as (
        select
        channel_id,
        sum(CASE when event_type = 0 then 1 else 0 end) as ele_pv_num,
        sum(CASE when event_type = 1 then 1 else 0 end) as ele_qr_code_view_num,
        sum(CASE when event_type = 2 then 1 else 0 end) as ele_identify_wechat_qr_code_num,
        sum(CASE when event_type = 3 then 1 else 0 end) as ele_add_wechat_success_num
        from landing_page_elm_event_callback_record
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime">
                and event_time >= #{landingPageQuery.startTime} and event_time &lt; #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and landing_page_id = #{landingPageQuery.landingPageId}
            </if>
            and event_type is not null
        </where>
        group by channel_id
        ),
        <!--pv基础数据 -->
        basePvData as (
        select
        pv.channel_id,
        count(*) as page_view_num,
        coalesce(sum(CASE when pv.length_of_stay >= 120 then 120 else pv.length_of_stay end ), 0) as
        total_length_of_stay,
        <!--1.247.0迭代之前的计算方式 coalesce(sum(pv.fill), 0) - coalesce(sum(pv.fill * pv.widget_template_type), 0) as form_submit_num,-->
        SUM(COALESCE(CASE WHEN pv.widget_template_type = 0 and (pv.form_type = 0 or pv.form_type is null)   THEN pv.fill  ELSE 0 END, 0))  AS form_submit_num,
        SUM(COALESCE(CASE WHEN pv.form_type = 1 THEN pv.fill  ELSE 0 END, 0)) AS douyin_applet_native_form_submit_num,
        SUM(COALESCE(CASE WHEN pv.form_type = 2 THEN pv.fill  ELSE 0 END, 0)) AS clue_form_submit_num,
        SUM(COALESCE(CASE WHEN pv.douyin_applet_authorization = 1 THEN 1 ELSE 0 END, 0)) AS active_message_authorization_num,
        SUM(COALESCE(CASE WHEN pv.add_group_type = 1 THEN 1  ELSE 0 END, 0)) AS add_group_after_add_customer_service_num,

        <!--1.247.0迭代之前的计算逻辑coalesce(sum(pv.fill * pv.widget_template_type), 0) as order_submit_num,-->
        SUM(COALESCE(CASE WHEN pv.widget_template_type = 1  and (pv.order_template_type = 0 or pv.order_template_type = 1002) THEN pv.fill  ELSE 0 END, 0)) AS order_submit_num,
        SUM(COALESCE(CASE WHEN pv.widget_template_type = 8  and pv.order_template_type = 1  THEN pv.fill  ELSE 0 END, 0)) AS douyin_applet_order_submit_num ,

        coalesce(case when count(1) = 0 then 0 else sum(pv.official_identify_qr_code_status) / count(1) ::NUMERIC * 100 end, 0) as official_identify_qr_code_rate,

        sum(CASE when pv.payment_type is not null and (pv.order_template_type = 0 or pv.order_template_type = 1002) and sd.order_status in (0, 1, 3) then 1 else 0 end) as payment_num,
        sum(CASE when pv.payment_type is not null and pv.order_template_type = 1  and sd.order_status in (0, 1, 3) then 1 else 0 end) as douyin_applet_order_finish_num ,

        coalesce(sum(pv.taobao_component_copy_status),0) AS taobao_component_copy_num,
        coalesce(sum(pv.douyin_applet_jump_status),0) AS douyin_applet_jump_num,
        SUM(COALESCE(CASE WHEN pv.applet_type = 0 and pv.applet_jump_status = 1 THEN 1  ELSE 0 END, 0)) AS tao_bao_movie_applet_jump_num,
        coalesce(sum(pv.tao_bao_movie_applet_order_status),0) AS  tao_bao_movie_applet_order_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(sum(pv.whatsapp_jump_status),0) as  whatsapp_jump_num,
        coalesce(sum(pv.whatsapp_add_friend_status),0) as  whatsapp_add_friend_success_num,
        coalesce(sum(pv.whatsapp_user_open_mouth_status),0) as  whatsapp_user_open_mouth_num,

        coalesce(SUM(pv.taobao_page_view_status), 0) as taobao_page_view_num,
        coalesce(SUM(pv.taobao_order_payment_status), 0) as taobao_order_payment_num,
        coalesce(SUM(pv.taobao_product_click_status), 0) as taobao_product_click_num,
        coalesce(SUM(pv.taobao_first_visit_venue_status), 0) as taobao_first_visit_venue_num,
        coalesce(SUM(pv.taobao_red_envelope_receive_status), 0) as taobao_red_envelope_receive_num,
        coalesce(SUM(pv.taobao_cancel_order_payment_status), 0) as taobao_cancel_order_payment_num,
        coalesce(SUM(pv.taobao_high_commission_order_payment_status), 0) as taobao_high_commission_order_payment_num,

        sum(pv.identify_qr_code_status) as identify_qr_code_num,
        sum(pv.add_enterprise_wechat_status) as add_work_wechat_num,
        sum(pv.follow_official_account_status) as follow_official_account_num,
        sum(pv.online_shop_buy_goods_product_count) as online_shop_buy_goods_success_num,
        SUM(pv.official_identify_qr_code_status)  as official_identify_qr_code_num,
        sum(pv.identify_group_chat_qr_code_status) as identify_group_qr_code_num,
        sum(pv.add_group_chat_status) as add_work_wechat_group_num,
        sum(pv.into_wechat_customer_service_session_status) as income_line_num,
        SUM(case when pv.wechat_official_history_article_status in(1,2) then 1 else 0 end) as wechat_official_article_page_view_num,
        SUM(COALESCE(CASE WHEN pv.send_qr_code_or_img_status = 1 and send_image_type != 6 THEN 1 ELSE 0 END, 0)) AS send_image_or_qr_code_num,
        SUM(COALESCE(CASE WHEN pv.send_mini_program_card_status = 1  THEN 1 ELSE 0 END, 0)) AS mini_program_news_num,
        SUM(COALESCE(CASE WHEN pv.jump_to_super_red_envelope_status = 1  THEN 1 ELSE 0 END, 0)) AS jump_to_super_red_envelope_num,
        SUM(COALESCE(CASE WHEN pv.success_send_welcome_msg_status = 1  THEN 1 ELSE 0 END, 0)) AS success_send_welcome_msg_num,
        SUM(COALESCE(CASE WHEN pv.ad_upload_status = 1  THEN 1 ELSE 0 END, 0)) AS ad_upload_num,
        <!-- 1.306.0 开口次数统计-->
        SUM(COALESCE(CASE WHEN pv.open_chat_num >= 1 THEN 1  ELSE 0 END, 0)) AS start_open_chat_num,
        SUM(COALESCE(CASE WHEN pv.open_chat_num >= 3 THEN 1  ELSE 0 END, 0)) AS third_open_chat_num,
        SUM(COALESCE(CASE WHEN pv.open_chat_num >= 5 THEN 1  ELSE 0 END, 0)) AS fifth_open_chat_num,
        SUM(COALESCE(CASE WHEN pv.open_chat_num >= 10 THEN 1  ELSE 0 END, 0)) AS tenth_open_chat_num


        from page_view_info pv left join submit_data sd on pv.submit_data_id = sd.id
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime">
                and pv.created_at >= #{landingPageQuery.startTime} and pv.created_at &lt; #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and pv.advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and pv.landing_page_id = #{landingPageQuery.landingPageId}
            </if>
        </where>
        group by pv.channel_id
        ),
        <!--渠道数据-->
        landingPageChannelData as (
        select * from landing_page_channels
        <where>
            <if test="landingPageQuery.landingPageId != null">
                and landing_page_id = #{landingPageQuery.landingPageId}
            </if>
            <if test="landingPageQuery.deleteStatus != null">
                and delete_status = #{landingPageQuery.deleteStatus}
            </if>
        </where>
        )
        select
        lp.*, noocfcn.*,
        offaac.*,
        pv.channel_id,
        coalesce(pv.page_view_num, 0) as page_view_num,
        coalesce(pv.total_length_of_stay, 0) as total_length_of_stay,
        coalesce(pv.form_submit_num, 0) as form_submit_num,
        coalesce(pv.douyin_applet_native_form_submit_num, 0) as douyin_applet_native_form_submit_num,
        coalesce(pv.clue_form_submit_num, 0) as clue_form_submit_num,
        coalesce(pv.form_submit_num, 0) +  coalesce(pv.douyin_applet_native_form_submit_num, 0) +  coalesce(pv.clue_form_submit_num, 0) as form_submit_total_num ,
        coalesce(pv.active_message_authorization_num, 0) as active_message_authorization_num,
        coalesce(pv.order_submit_num, 0) as order_submit_num,
        coalesce(pv.payment_num, 0) as payment_num,
        <!--1.256.0 字节小程序订单提交数-->
        coalesce(pv.douyin_applet_order_submit_num, 0) as douyin_applet_order_submit_num,
        <!--1.256.0 字节小程序订单完成数-->
        coalesce(pv.douyin_applet_order_finish_num, 0) as douyin_applet_order_finish_num,

        coalesce(pv.identify_qr_code_num, 0) as identify_qr_code_num,
        coalesce(pv.official_identify_qr_code_rate, 0) as official_identify_qr_code_rate,
        coalesce(pv.add_work_wechat_num, 0) as add_work_wechat_num,
        coalesce(pv.follow_official_account_num, 0) as follow_official_account_num,
        coalesce(pv.online_shop_buy_goods_success_num, 0) as online_shop_buy_goods_success_num,
        coalesce(pv.official_identify_qr_code_num, 0) as official_identify_qr_code_num,
        coalesce(pv.send_image_or_qr_code_num, 0) as send_image_or_qr_code_num,
        coalesce(pv.mini_program_news_num, 0) as mini_program_news_num,
        coalesce(pv.jump_to_super_red_envelope_num, 0) as jump_to_super_red_envelope_num,
        coalesce(pv.success_send_welcome_msg_num, 0) as success_send_welcome_msg_num,
        coalesce(pv.ad_upload_num, 0) as ad_upload_num,

        coalesce(elmD.ele_pv_num, 0) as ele_pv_num,
        coalesce(elmD.ele_qr_code_view_num, 0) as ele_qr_code_view_num,
        coalesce(elmD.ele_identify_wechat_qr_code_num, 0) as ele_identify_wechat_qr_code_num,
        coalesce(elmD.ele_add_wechat_success_num, 0) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(pv.whatsapp_jump_num,0) as  whatsapp_jump_num,
        coalesce(pv.whatsapp_add_friend_success_num,0) as  whatsapp_add_friend_success_num,
        coalesce(pv.whatsapp_user_open_mouth_num,0) as  whatsapp_user_open_mouth_num,
        coalesce(case when page_view_num = 0 then 0 else pv.whatsapp_jump_num / page_view_num ::NUMERIC * 100 end, 0) as whatsapp_jump_rate,
        coalesce(case when page_view_num = 0 then 0 else pv.whatsapp_add_friend_success_num / page_view_num ::NUMERIC * 100 end, 0) as whatsapp_add_friend_success_rate,
        coalesce(case when page_view_num = 0 then 0 else pv.whatsapp_user_open_mouth_num / page_view_num ::NUMERIC * 100 end, 0) as whatsapp_user_open_mouth_rate,

        coalesce(pv.add_work_wechat_group_num, 0) as add_work_wechat_group_num,
        coalesce(pv.identify_group_qr_code_num, 0) as identify_group_qr_code_num,
        coalesce(pv.income_line_num, 0) as income_line_num,
        coalesce(pv.wechat_official_article_page_view_num,0) as wechat_official_article_page_view_num,

        coalesce(pv.taobao_component_copy_num,0) as taobao_component_copy_num,
        coalesce(pv.douyin_applet_jump_num,0) as douyin_applet_jump_num,

        coalesce(oad.add_group_after_follow_official_account_num,0) as add_group_after_follow_official_account_num,
        coalesce(pv.add_group_after_add_customer_service_num,0) as add_group_after_add_customer_service_num,
        coalesce(case when page_view_num = 0 then 0 else add_group_after_follow_official_account_num / page_view_num ::NUMERIC * 100 end, 0) as add_group_after_follow_official_account_rate,
        coalesce(case when page_view_num = 0 then 0 else add_group_after_add_customer_service_num / page_view_num ::NUMERIC * 100 end, 0) as add_group_after_add_customer_service_rate,


        coalesce(pv.taobao_page_view_num, 0) as taobao_page_view_num,
        coalesce(pv.taobao_order_payment_num, 0) as taobao_order_payment_num,
        coalesce(pv.taobao_product_click_num, 0) as taobao_product_click_num,
        coalesce(pv.taobao_first_visit_venue_num, 0) as taobao_first_visit_venue_num,
        coalesce(pv.taobao_red_envelope_receive_num, 0) as taobao_red_envelope_receive_num,
        coalesce(pv.taobao_cancel_order_payment_num, 0) as taobao_cancel_order_payment_num,
        coalesce(pv.taobao_high_commission_order_payment_num, 0) as taobao_high_commission_order_payment_num,

        coalesce(pv.start_open_chat_num) as  start_open_chat_num,
        coalesce(pv.third_open_chat_num) as  third_open_chat_num,
        coalesce(pv.fifth_open_chat_num) as  fifth_open_chat_num,
        coalesce(pv.tenth_open_chat_num) as  tenth_open_chat_num,
        coalesce(case when page_view_num = 0 then 0 else pv.start_open_chat_num / page_view_num ::NUMERIC * 100 end, 0) as start_open_chat_rate,
        coalesce(case when page_view_num = 0 then 0 else pv.third_open_chat_num / page_view_num ::NUMERIC * 100 end, 0) as third_open_chat_rate,
        coalesce(case when page_view_num = 0 then 0 else pv.fifth_open_chat_num / page_view_num ::NUMERIC * 100 end, 0) as fifth_open_chat_rate,
        coalesce(case when page_view_num = 0 then 0 else pv.tenth_open_chat_num / page_view_num ::NUMERIC * 100 end, 0) as tenth_open_chat_rate,

        coalesce(case when page_view_num = 0 then 0 else pv.taobao_page_view_num / page_view_num ::NUMERIC * 100 end, 0) as taobao_page_view_rate,
        coalesce(case when page_view_num = 0 then 0 else pv.taobao_order_payment_num / page_view_num ::NUMERIC * 100 end, 0) as taobao_order_payment_rate,
        coalesce(case when page_view_num = 0 then 0 else pv.taobao_product_click_num / page_view_num ::NUMERIC * 100 end, 0) as taobao_product_click_rate,
        coalesce(case when page_view_num = 0 then 0 else pv.taobao_first_visit_venue_num / page_view_num ::NUMERIC * 100 end, 0) as taobao_first_visit_venue_rate,
        coalesce(case when page_view_num = 0 then 0 else pv.taobao_red_envelope_receive_num / page_view_num ::NUMERIC * 100 end, 0) as taobao_red_envelope_receive_rate,
        coalesce(case when page_view_num = 0 then 0 else pv.taobao_cancel_order_payment_num / page_view_num ::NUMERIC * 100 end, 0) as taobao_cancel_order_payment_rate,
        coalesce(case when page_view_num = 0 then 0 else pv.taobao_high_commission_order_payment_num / page_view_num ::NUMERIC * 100 end, 0) as taobao_high_commission_order_payment_rate,

        <!--点击跳转淘宝电影小程序数-->
        coalesce(pv.tao_bao_movie_applet_jump_num,0) as tao_bao_movie_applet_jump_num,
        <!--淘宝电影小程序订单数-->
        coalesce(pv.tao_bao_movie_applet_order_num,0) as tao_bao_movie_applet_order_num,
        <!--点击跳转淘宝电影小程序率-->
        coalesce(case when page_view_num = 0 then 0 else tao_bao_movie_applet_jump_num / page_view_num ::NUMERIC * 100 end, 0) as tao_bao_movie_applet_jump_rate,
        <!--淘宝电影小程序订单率-->
        coalesce(case when page_view_num = 0 then 0 else tao_bao_movie_applet_order_num / page_view_num ::NUMERIC * 100 end, 0) as tao_bao_movie_applet_order_rate,
        coalesce(case when page_view_num = 0 then 0 else total_length_of_stay / page_view_num ::NUMERIC end, 0) as  average_length_of_stay,

        coalesce(case when page_view_num = 0 then 0 else form_submit_num / page_view_num ::NUMERIC * 100 end, 0) as form_submit_rate,

        coalesce(case when page_view_num = 0 then 0 else taobao_component_copy_num / page_view_num ::NUMERIC * 100 end, 0) as taobao_component_copy_rate,
        coalesce(case when page_view_num = 0 then 0 else douyin_applet_jump_num / page_view_num ::NUMERIC * 100 end, 0) as douyin_applet_jump_rate,

        coalesce(case when page_view_num = 0 then 0 else douyin_applet_native_form_submit_num / page_view_num ::NUMERIC * 100 end, 0) as douyin_applet_native_form_submit_rate,
        coalesce(case when page_view_num = 0 then 0 else clue_form_submit_num / page_view_num ::NUMERIC * 100 end, 0) as clue_form_submit_rate,
        coalesce(case when page_view_num = 0 then 0 else  (form_submit_num +  douyin_applet_native_form_submit_num +clue_form_submit_num) / page_view_num ::NUMERIC * 100 end, 0) as form_submit_total_rate,
        coalesce(case when page_view_num = 0 then 0 else active_message_authorization_num / page_view_num ::NUMERIC * 100 end, 0) as active_message_authorization_rate,
        coalesce(case when page_view_num = 0 then 0 else phone_number_recieved_num / page_view_num ::NUMERIC * 100 end, 0) as phone_number_recieved_rate,

        coalesce(case when page_view_num = 0 then 0 else order_submit_num / page_view_num ::NUMERIC * 100 end, 0) as
        order_submit_rate,
        coalesce(case when order_submit_num = 0 then 0 else payment_num / order_submit_num ::NUMERIC * 100 end, 0) as
        payment_rate,
        <!--1.256.0 字节小程序订单提交率-->
        coalesce(case when page_view_num = 0 then 0 else douyin_applet_order_submit_num / page_view_num ::NUMERIC * 100 end, 0) as
        douyin_applet_order_submit_rate,
        <!--1.256.0 字节小程序订单完成率-->
        coalesce(case when douyin_applet_order_submit_num = 0 then 0 else douyin_applet_order_finish_num / douyin_applet_order_submit_num ::NUMERIC * 100 end, 0) as
        douyin_applet_order_finish_rate,

        coalesce(case when page_view_num = 0 then 0 else payment_num / page_view_num ::NUMERIC * 100 end, 0) as
        comprehensive_payment_rate,
        coalesce(case when order_submit_num = 0 then 0 else number_of_orders_completed_for_coupon_num / order_submit_num
        ::NUMERIC * 100 end, 0) as number_of_orders_completed_for_coupon_rate,
        coalesce(case when page_view_num = 0 then 0 else identify_qr_code_num / page_view_num ::NUMERIC * 100 end, 0) as
        identify_qr_code_rate,
        coalesce(case when page_view_num = 0 then 0 else add_work_wechat_num / page_view_num ::NUMERIC * 100 end, 0) as
        add_work_wechat_rate,
        coalesce(case when page_view_num = 0 then 0 else follow_official_account_num / page_view_num ::NUMERIC * 100
        end, 0) as follow_official_account_rate,
        coalesce(case when page_view_num = 0 then 0 else online_shop_buy_goods_success_num / page_view_num ::NUMERIC *
        100 end, 0) as online_shop_buy_goods_success_rate,

        coalesce(case when page_view_num = 0 then 0 else identify_group_qr_code_num / page_view_num ::NUMERIC *
        100 end, 0) as identify_group_qr_code_rate,
        coalesce(case when page_view_num = 0 then 0 else add_work_wechat_group_num / page_view_num ::NUMERIC *
        100 end, 0) as add_work_wechat_group_rate,
        coalesce(case when page_view_num = 0 then 0 else official_add_customer_num / page_view_num ::NUMERIC *
        100 end, 0) as official_add_customer_rate,

        coalesce(case when page_view_num = 0 then 0 else send_image_or_qr_code_num / page_view_num ::NUMERIC * 100 end, 0) as send_image_or_qr_code_rate,

        coalesce(case when page_view_num = 0 then 0 else mini_program_news_num / page_view_num ::NUMERIC * 100 end, 0) as mini_program_news_rate,

        coalesce(case when page_view_num = 0 then 0 else jump_to_super_red_envelope_num / page_view_num ::NUMERIC * 100 end, 0) as jump_to_super_red_envelope_rate,

        coalesce(case when page_view_num = 0 then 0 else success_send_welcome_msg_num / page_view_num ::NUMERIC * 100 end, 0) as success_send_welcome_msg_rate,

        coalesce(case when page_view_num = 0 then 0 else ad_upload_num / page_view_num ::NUMERIC * 100 end, 0) as ad_upload_rate,

        coalesce(case when page_view_num = 0 then 0 else income_line_num / page_view_num ::NUMERIC * 100 end, 0) as income_line_rate

        from landingPageChannelData lp
        left join officialAccountAddCustomer offaac on offaac.wechat_official_account_landing_page_channel_id = lp.id
        left join elmData elmD on elmD.channel_id = lp.id
        left join numberOfOrdersCompletedForCouponNum noocfcn on lp.id = noocfcn.channel_id
        left join basePvData pv on lp.id = pv.channel_id
        left join officialAssistantData oad on lp.id = oad.channel_id
        order by
        ${landingPageQuery.sortField} ${landingPageQuery.order}
        <if test="landingPageQuery.order !=null  and (landingPageQuery.sortField == 'number_of_orders_completed_for_coupon_num' or landingPageQuery.sortField == 'official_add_customer_num') and landingPageQuery.order == 'desc' "  >
            nulls last
        </if>
        <if test="landingPageQuery.order !=null and (landingPageQuery.sortField == 'number_of_orders_completed_for_coupon_num' or landingPageQuery.sortField == 'official_add_customer_num') and landingPageQuery.order == 'asc' "  >
            nulls first
        </if>
    </select>

    <select id="getLandingPageChannelFromIndicatorStatistics" resultType="ai.yiye.agent.domain.LandingPageChannel">
        <!--pv基础数据 -->
        WITH basePvData as (
        select
        pv.channel_id,
        SUM(page_view_num) AS page_view_num,
        SUM(total_length_of_stay) AS total_length_of_stay,
        SUM(form_submit_num) AS form_submit_num,
        SUM(order_submit_num) AS order_submit_num,
        SUM(official_identify_qr_code_num) AS official_identify_qr_code_num,
        SUM(payment_num) AS payment_num,
        SUM(identify_qr_code_num) AS identify_qr_code_num,
        SUM(add_work_wechat_num) AS add_work_wechat_num,
        SUM(follow_official_account_num) AS follow_official_account_num,
        SUM(online_shop_buy_goods_success_num) AS online_shop_buy_goods_success_num,
        SUM(identify_group_qr_code_num) AS identify_group_qr_code_num,
        SUM(add_work_wechat_group_num) AS add_work_wechat_group_num,
        SUM(wechat_official_article_page_view_num) AS wechat_official_article_page_view_num,
        SUM(number_of_orders_completed_for_coupon_num) as number_of_orders_completed_for_coupon_num
        from landing_page_indicator_statistics pv
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime">
                and pv.statistic_date between to_timestamp(#{landingPageQuery.startTime}, 'yyyy-MM-dd HH24:mi:ss') and  to_timestamp(#{landingPageQuery.endTime}, 'yyyy-MM-dd HH24:mi:ss')
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and pv.advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and pv.landing_page_id = #{landingPageQuery.landingPageId}
            </if>
        </where>
        group by pv.channel_id
        ),
        <!--渠道数据 -->
        landingPageChannelData as (
        select * from landing_page_channels
        <where>
            <if test="landingPageQuery.landingPageId != null">
                and landing_page_id = #{landingPageQuery.landingPageId}
            </if>
            <if test="landingPageQuery.deleteStatus != null">
                and delete_status = #{landingPageQuery.deleteStatus}
            </if>
        </where>
        )
        select
        lp.*,
        pv.channel_id,
        coalesce(pv.page_view_num, 0) as page_view_num,
        coalesce(pv.total_length_of_stay, 0) as total_length_of_stay,
        coalesce(pv.form_submit_num, 0) as form_submit_num,
        coalesce(pv.order_submit_num, 0) as order_submit_num,
        coalesce(pv.payment_num, 0) as payment_num,
        coalesce(pv.identify_qr_code_num, 0) as identify_qr_code_num,
        coalesce(pv.add_work_wechat_num, 0) as add_work_wechat_num,
        coalesce(pv.follow_official_account_num, 0) as follow_official_account_num,
        coalesce(pv.online_shop_buy_goods_success_num, 0) as online_shop_buy_goods_success_num,
        coalesce(pv.official_identify_qr_code_num, 0) as official_identify_qr_code_num,
        coalesce(pv.add_work_wechat_group_num, 0) as add_work_wechat_group_num,
        coalesce(pv.identify_group_qr_code_num, 0) as identify_group_qr_code_num,
        coalesce(pv.wechat_official_article_page_view_num,0) as wechat_official_article_page_view_num,
        coalesce(pv.number_of_orders_completed_for_coupon_num,0) as number_of_orders_completed_for_coupon_num,
        coalesce(case when page_view_num = 0 then 0 else official_identify_qr_code_num / page_view_num ::NUMERIC end, 0) as
        official_identify_qr_code_rate,
        coalesce(case when page_view_num = 0 then 0 else total_length_of_stay / page_view_num ::NUMERIC end, 0) as
        average_length_of_stay,
        coalesce(case when page_view_num = 0 then 0 else form_submit_num / page_view_num ::NUMERIC * 100 end, 0) as
        form_submit_rate,
        coalesce(case when page_view_num = 0 then 0 else order_submit_num / page_view_num ::NUMERIC * 100 end, 0) as
        order_submit_rate,
        coalesce(case when order_submit_num = 0 then 0 else payment_num / order_submit_num ::NUMERIC * 100 end, 0) as
        payment_rate,
        coalesce(case when page_view_num = 0 then 0 else payment_num / page_view_num ::NUMERIC * 100 end, 0) as
        comprehensive_payment_rate,
        coalesce(case when order_submit_num = 0 then 0 else number_of_orders_completed_for_coupon_num / order_submit_num
        ::NUMERIC * 100 end, 0) as number_of_orders_completed_for_coupon_rate,
        coalesce(case when page_view_num = 0 then 0 else identify_qr_code_num / page_view_num ::NUMERIC * 100 end, 0) as
        identify_qr_code_rate,
        coalesce(case when page_view_num = 0 then 0 else add_work_wechat_num / page_view_num ::NUMERIC * 100 end, 0) as
        add_work_wechat_rate,
        coalesce(case when page_view_num = 0 then 0 else follow_official_account_num / page_view_num ::NUMERIC * 100
        end, 0) as follow_official_account_rate,
        coalesce(case when page_view_num = 0 then 0 else online_shop_buy_goods_success_num / page_view_num ::NUMERIC *
        100 end, 0) as online_shop_buy_goods_success_rate,

        coalesce(case when page_view_num = 0 then 0 else identify_group_qr_code_num / page_view_num ::NUMERIC *
        100 end, 0) as identify_group_qr_code_rate,
        coalesce(case when page_view_num = 0 then 0 else add_work_wechat_group_num / page_view_num ::NUMERIC *
        100 end, 0) as add_work_wechat_group_rate
        from landingPageChannelData lp
        left join basePvData pv on lp.id = pv.channel_id
        order by
        ${landingPageQuery.sortField} ${landingPageQuery.order}
    </select>


    <select id="getTotalChannel" resultType="ai.yiye.agent.domain.LandingPageChannel">
        <!--重复访客数 -->
        <!--with repeatVisitorNum as (
        select channel_id, count(*) as repeat_visitor_num
        from (
        select channel_id, uid, count(*)
        from page_view_info
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime">
                and created_at between #{landingPageQuery.startTime} and #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and landing_page_id = #{landingPageQuery.landingPageId}
            </if>
        </where>
        group by channel_id, uid
        having count(*) > 1) a
        group by channel_id
        ),-->
        <!--使用优惠券完成订单数 -->
        with numberOfOrdersCompletedForCouponNum as (
        select channel_id,
        sum(CASE when payment_type is not null and order_status in (0, 1, 3) and use_coupon = true then 1 else 0 end)
        as number_of_orders_completed_for_coupon_num,
        sum(CASE when douyin_auth_phone is not null then 1 else 0 end) as phone_number_recieved_num
        from customer
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime != null">
                and created_at >= #{landingPageQuery.startTime} and created_at &lt; #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and landing_page_id = #{landingPageQuery.landingPageId}
            </if>
        </where>
        group by channel_id
        ),

        officialAccountAddCustomer as (
        select
        wechat_official_account_landing_page_channel_id as wechat_official_account_landing_page_channel_id,
        count(1) as official_add_customer_num
        from customer
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime != null">
                and created_at >= #{landingPageQuery.startTime} and created_at &lt; #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and wechat_official_account_landing_page_id = #{landingPageQuery.landingPageId}
            </if>
        </where>
        group by wechat_official_account_landing_page_channel_id
        ),
        <!--公众号助手发码加粉后入群数-->
        officialAssistantData as(
            SELECT
            follow_landing_page_id as landing_page_id,
            follow_channel_id as channel_id,
            count(1) as  add_group_after_follow_official_account_num
            FROM
            landing_page_wechat_official_account_customer
            <where>
                <if test="landingPageQuery.startTime != null and landingPageQuery.endTime">
                    and group_chat_join_time >= #{landingPageQuery.startTime} and group_chat_join_time &lt; #{landingPageQuery.endTime}
                </if>
                <if test="landingPageQuery.advertiserAccountGroupId != null">
                    and wechat_applet_user_pmp_id = #{landingPageQuery.advertiserAccountGroupId}
                </if>
                and followed_add_enterprise_wechat = 1
            </where>
            GROUP BY landing_page_id, channel_id
        ),
        <!-- 饿了么回传事件数据 -->
        elmData as (
        select
        channel_id,
        sum(CASE when event_type = 0 then 1 else 0 end) as ele_pv_num,
        sum(CASE when event_type = 1 then 1 else 0 end) as ele_qr_code_view_num,
        sum(CASE when event_type = 2 then 1 else 0 end) as ele_identify_wechat_qr_code_num,
        sum(CASE when event_type = 3 then 1 else 0 end) as ele_add_wechat_success_num
        from landing_page_elm_event_callback_record
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime">
                and event_time >= #{landingPageQuery.startTime} and event_time &lt; #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and landing_page_id = #{landingPageQuery.landingPageId}
            </if>
            and event_type is not null
        </where>
        group by channel_id
        ),
        <!--pv基础数据 -->
        basePvData as (
        select
        pv.channel_id,
        count(*) as page_view_num,
        <!--1.247.0迭代之前的计算逻辑  coalesce(sum(pv.fill), 0) - coalesce(sum(pv.fill * pv.widget_template_type), 0) as form_submit_num,-->
        SUM(COALESCE(CASE WHEN pv.widget_template_type = 0 and (pv.form_type = 0 or pv.form_type is null)   THEN pv.fill  ELSE 0 END, 0))  AS form_submit_num,
        SUM(COALESCE(CASE WHEN pv.form_type = 1 THEN pv.fill  ELSE 0 END, 0)) AS douyin_applet_native_form_submit_num,
        SUM(COALESCE(CASE WHEN pv.form_type = 2 THEN pv.fill  ELSE 0 END, 0)) AS clue_form_submit_num,
        SUM(COALESCE(CASE WHEN pv.douyin_applet_authorization = 1 THEN 1 ELSE 0 END, 0)) AS active_message_authorization_num,
        <!--1.247.0迭代之前的计算逻辑coalesce(sum(pv.fill * pv.widget_template_type), 0) as order_submit_num,-->
        SUM(COALESCE(CASE WHEN pv.widget_template_type = 1 and (pv.order_template_type = 0 or pv.order_template_type = 1002) THEN pv.fill  ELSE 0 END, 0)) AS order_submit_num,
        sum(CASE when pv.payment_type is not null and (pv.order_template_type = 0 or pv.order_template_type = 1002) and sd.order_status in (0, 1, 3) then 1 else 0 end) as payment_num,

        SUM(COALESCE(CASE WHEN pv.widget_template_type = 8 and pv.order_template_type = 1 THEN pv.fill  ELSE 0 END, 0)) AS douyin_applet_order_submit_num,
        sum(CASE when pv.payment_type is not null and pv.order_template_type = 1 and sd.order_status in (0, 1, 3) then 1 else 0 end) as douyin_applet_order_finish_num ,

        coalesce(sum(pv.taobao_component_copy_status),0) AS taobao_component_copy_num,
        coalesce(sum(pv.douyin_applet_jump_status),0) AS douyin_applet_jump_num,
        SUM(COALESCE(CASE WHEN pv.add_group_type = 1 THEN 1  ELSE 0 END, 0)) AS add_group_after_add_customer_service_num,
        SUM(COALESCE(CASE WHEN pv.applet_type = 0 and pv.applet_jump_status = 1 THEN 1  ELSE 0 END, 0)) AS tao_bao_movie_applet_jump_num,
        coalesce(sum(pv.tao_bao_movie_applet_order_status),0)  AS tao_bao_movie_applet_order_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(sum(pv.whatsapp_jump_status),0) as  whatsapp_jump_num,
        coalesce(sum(pv.whatsapp_add_friend_status),0) as  whatsapp_add_friend_success_num,
        coalesce(sum(pv.whatsapp_user_open_mouth_status),0) as  whatsapp_user_open_mouth_num,



        sum(pv.identify_qr_code_status) as identify_qr_code_num,
        sum(pv.add_enterprise_wechat_status) as add_work_wechat_num,
        sum(pv.follow_official_account_status) as follow_official_account_num,
        sum(pv.online_shop_buy_goods_product_count) as online_shop_buy_goods_success_num,
        SUM(pv.official_identify_qr_code_status)  as official_identify_qr_code_num,
        sum(pv.identify_group_chat_qr_code_status) as identify_group_qr_code_num,
        sum(pv.add_group_chat_status) as add_work_wechat_group_num,
        sum(pv.into_wechat_customer_service_session_status) as income_line_num,
        sum(case when pv.wechat_official_history_article_status in(1,2) then 1 else 0 end) as wechat_official_article_page_view_num,
        SUM(COALESCE(CASE WHEN pv.send_qr_code_or_img_status = 1 and send_image_type != 6 THEN 1 ELSE 0 END, 0)) AS send_image_or_qr_code_num,
        SUM(COALESCE(CASE WHEN pv.send_mini_program_card_status = 1  THEN 1 ELSE 0 END, 0)) AS mini_program_news_num,
        SUM(COALESCE(CASE WHEN pv.jump_to_super_red_envelope_status = 1  THEN 1 ELSE 0 END, 0)) AS jump_to_super_red_envelope_num,
        SUM(COALESCE(CASE WHEN pv.success_send_welcome_msg_status = 1  THEN 1 ELSE 0 END, 0)) AS success_send_welcome_msg_num,
        SUM(COALESCE(CASE WHEN pv.ad_upload_status = 1  THEN 1 ELSE 0 END, 0)) AS ad_upload_num,
        <!-- 1.306.0 开口次数统计-->
        SUM(COALESCE(CASE WHEN pv.open_chat_num >= 1 THEN 1  ELSE 0 END, 0)) AS start_open_chat_num,
        SUM(COALESCE(CASE WHEN pv.open_chat_num >= 3 THEN 1  ELSE 0 END, 0)) AS third_open_chat_num,
        SUM(COALESCE(CASE WHEN pv.open_chat_num >= 5 THEN 1  ELSE 0 END, 0)) AS fifth_open_chat_num,
        SUM(COALESCE(CASE WHEN pv.open_chat_num >= 10 THEN 1  ELSE 0 END, 0)) AS tenth_open_chat_num

        from page_view_info pv left join submit_data sd on pv.submit_data_id = sd.id
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime != null">
                and pv.created_at >= #{landingPageQuery.startTime} and pv.created_at &lt; #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and pv.advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and pv.landing_page_id = #{landingPageQuery.landingPageId}
            </if>
        </where>
        group by pv.channel_id
        ),
        <!--渠道数据-->
        landingPageChannelData as (
        select id, created_at, updated_at from landing_page_channels
        <where>
            <if test="landingPageQuery.landingPageId != null">
                and landing_page_id = #{landingPageQuery.landingPageId}
            </if>
            <if test="landingPageQuery.deleteStatus != null">
                and delete_status = #{landingPageQuery.deleteStatus}
            </if>
        </where>
        )
        select
        coalesce(case
        when sum(pv.page_view_num) = 0 then 0
        else sum(pv.official_identify_qr_code_num) / sum(pv.page_view_num) ::NUMERIC * 100 end,
        0)                                         as official_identify_qr_code_rate,
        sum(pv.wechat_official_article_page_view_num ) as wechat_official_article_page_view_num,
        sum(pv.official_identify_qr_code_num) as official_identify_qr_code_num,
        sum(number_of_orders_completed_for_coupon_num) as number_of_orders_completed_for_coupon_num,
        sum(page_view_num) as page_view_num,
        sum(offaac.official_add_customer_num) as official_add_customer_num,
        sum(form_submit_num) as form_submit_num,
        (sum(form_submit_num) + sum(douyin_applet_native_form_submit_num) + sum(clue_form_submit_num)) as form_submit_total_num,
        sum(phone_number_recieved_num) as phone_number_recieved_num,
        sum(douyin_applet_native_form_submit_num) as douyin_applet_native_form_submit_num,
        sum(clue_form_submit_num) as clue_form_submit_num,
        sum(active_message_authorization_num) as active_message_authorization_num,

        sum(elmD.ele_pv_num) as ele_pv_num,
        sum(elmD.ele_qr_code_view_num) as ele_qr_code_view_num,
        sum(elmD.ele_identify_wechat_qr_code_num) as ele_identify_wechat_qr_code_num,
        sum(elmD.ele_add_wechat_success_num) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        sum(pv.whatsapp_jump_num) as  whatsapp_jump_num,
        sum(pv.whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
        sum(pv.whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,
        case when sum(page_view_num)  = 0 then 0 else sum(pv.whatsapp_jump_num) / sum(page_view_num)  * 100 end as whatsapp_jump_rate,
        case when sum(page_view_num)  = 0 then 0 else sum(pv.whatsapp_add_friend_success_num) / sum(page_view_num)  * 100 end as whatsapp_add_friend_success_rate,
        case when sum(page_view_num)  = 0 then 0 else sum(pv.whatsapp_user_open_mouth_num) / sum(page_view_num) * 100 end as whatsapp_user_open_mouth_rate,

        sum(order_submit_num) as order_submit_num,
        sum(payment_num) as payment_num,
        <!--1.256.0-->
        sum(douyin_applet_order_submit_num) as douyin_applet_order_submit_num,
        sum(douyin_applet_order_finish_num) as douyin_applet_order_finish_num,

        sum(identify_qr_code_num) as identify_qr_code_num,
        sum(add_work_wechat_num) as add_work_wechat_num,
        sum(follow_official_account_num) as follow_official_account_num,
        sum(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
        sum(identify_group_qr_code_num) as identify_group_qr_code_num,
        sum(add_work_wechat_group_num) as add_work_wechat_group_num,
        sum(income_line_num) as income_line_num,
        sum(taobao_component_copy_num) as taobao_component_copy_num,
        sum(douyin_applet_jump_num) as douyin_applet_jump_num,
        sum(tao_bao_movie_applet_jump_num) as tao_bao_movie_applet_jump_num,
        sum(tao_bao_movie_applet_order_num) as tao_bao_movie_applet_order_num,
        sum(add_group_after_add_customer_service_num) as add_group_after_add_customer_service_num,
        sum(oad.add_group_after_follow_official_account_num) as add_group_after_follow_official_account_num,
        sum(pv.send_image_or_qr_code_num) as  send_image_or_qr_code_num,
        sum(pv.mini_program_news_num) as  mini_program_news_num,
        sum(pv.jump_to_super_red_envelope_num) as  jump_to_super_red_envelope_num,
        sum(pv.success_send_welcome_msg_num) as  success_send_welcome_msg_num,
        sum(pv.ad_upload_num) as  ad_upload_num,
        sum(pv.start_open_chat_num) as  start_open_chat_num,
        sum(pv.third_open_chat_num) as  third_open_chat_num,
        sum(pv.fifth_open_chat_num) as  fifth_open_chat_num,
        sum(pv.tenth_open_chat_num) as  tenth_open_chat_num,

        case when sum(page_view_num) = 0 then 0 else sum(start_open_chat_num) / sum(page_view_num) ::NUMERIC * 100 end as start_open_chat_rate,
        case when sum(page_view_num) = 0 then 0 else sum(third_open_chat_num) / sum(page_view_num) ::NUMERIC * 100 end as third_open_chat_rate,
        case when sum(page_view_num) = 0 then 0 else sum(fifth_open_chat_num) / sum(page_view_num) ::NUMERIC * 100 end as fifth_open_chat_rate,
        case when sum(page_view_num) = 0 then 0 else sum(tenth_open_chat_num) / sum(page_view_num) ::NUMERIC * 100 end as tenth_open_chat_rate,
        case when sum(page_view_num) = 0 then 0 else sum(jump_to_super_red_envelope_num) / sum(page_view_num) ::NUMERIC * 100 end as jump_to_super_red_envelope_rate,
        case when sum(page_view_num) = 0 then 0 else sum(success_send_welcome_msg_num) / sum(page_view_num) ::NUMERIC * 100 end as success_send_welcome_msg_rate,
        case when sum(page_view_num) = 0 then 0 else sum(ad_upload_num) / sum(page_view_num) ::NUMERIC * 100 end as ad_upload_rate,

        case when sum(page_view_num) = 0 then 0 else sum(send_image_or_qr_code_num) / sum(page_view_num) ::NUMERIC * 100 end as send_image_or_qr_code_rate,
        case when sum(page_view_num) = 0 then 0 else sum(mini_program_news_num) / sum(page_view_num) ::NUMERIC * 100 end as mini_program_news_rate,
        case when sum(page_view_num) = 0 then 0 else sum(income_line_num) / sum(page_view_num) ::NUMERIC * 100 end as income_line_rate,

        case when sum(page_view_num) = 0 then 0 else sum(add_group_after_add_customer_service_num) / sum(page_view_num) ::NUMERIC * 100 end as add_group_after_add_customer_service_rate,
        case when sum(page_view_num) = 0 then 0 else sum(add_group_after_follow_official_account_num) / sum(page_view_num) ::NUMERIC * 100 end as add_group_after_follow_official_account_rate,

        case when sum(page_view_num) = 0 then 0 else sum(tao_bao_movie_applet_jump_num) / sum(page_view_num) ::NUMERIC * 100 end as tao_bao_movie_applet_jump_rate,
        case when sum(page_view_num) = 0 then 0 else sum(tao_bao_movie_applet_order_num) / sum(page_view_num) ::NUMERIC * 100 end as tao_bao_movie_applet_order_rate,

        case when sum(page_view_num) = 0 then 0 else sum(taobao_component_copy_num) / sum(page_view_num) ::NUMERIC * 100 end as taobao_component_copy_rate,

        case when sum(page_view_num) = 0 then 0 else sum(douyin_applet_jump_num) / sum(page_view_num) ::NUMERIC * 100 end as douyin_applet_jump_rate,

        case when sum(page_view_num) = 0 then 0 else sum(form_submit_num) / sum(page_view_num) ::NUMERIC * 100 end as  form_submit_rate,

        case when sum(page_view_num) = 0 then 0 else sum(order_submit_num) / sum(page_view_num) ::NUMERIC * 100 end as order_submit_rate,

        case when sum(order_submit_num) = 0 then 0 else sum(payment_num) / sum(order_submit_num) ::NUMERIC * 100 end as payment_rate,
        case when sum(page_view_num) = 0 then 0 else sum(offaac.official_add_customer_num) / sum(page_view_num) ::NUMERIC * 100 end as
        official_add_customer_rate,
        <!--1.256.0-->
        case when sum(page_view_num) = 0 then 0 else sum(douyin_applet_order_submit_num) / sum(page_view_num) ::NUMERIC * 100 end as douyin_applet_order_submit_rate,

        case when sum(douyin_applet_order_submit_num) = 0 then 0 else sum(douyin_applet_order_finish_num) / sum(douyin_applet_order_submit_num) ::NUMERIC * 100 end as douyin_applet_order_finish_rate,

        case when sum(page_view_num) = 0 then 0 else sum(payment_num) / sum(page_view_num) ::NUMERIC * 100 end as
        comprehensive_payment_rate,
        case when sum(order_submit_num) = 0 then 0 else sum(number_of_orders_completed_for_coupon_num) /
        sum(order_submit_num) ::NUMERIC * 100 end as number_of_orders_completed_for_coupon_rate,
        case when sum(page_view_num) = 0 then 0 else sum(identify_qr_code_num) / sum(page_view_num) ::NUMERIC * 100 end
        as identify_qr_code_rate,

        case when sum(page_view_num) = 0 then 0 else sum(phone_number_recieved_num) / sum(page_view_num) ::NUMERIC * 100 end
        as phone_number_recieved_rate,
        case when sum(page_view_num) = 0 then 0 else sum(douyin_applet_native_form_submit_num) / sum(page_view_num) ::NUMERIC * 100 end
        as douyin_applet_native_form_submit_rate,
        case when sum(page_view_num) = 0 then 0 else sum(clue_form_submit_num) / sum(page_view_num) ::NUMERIC * 100 end
        as clue_form_submit_rate,
        case when sum(page_view_num) = 0 then 0 else sum(active_message_authorization_num) / sum(page_view_num) ::NUMERIC * 100 end
        as active_message_authorization_rate,
        case when sum(page_view_num) = 0 then 0 else (sum(form_submit_num) + sum(douyin_applet_native_form_submit_num) + sum(clue_form_submit_num))  / sum(page_view_num) ::NUMERIC * 100 end as
        form_submit_total_rate,

        case when sum(page_view_num) = 0 then 0 else sum(add_work_wechat_num) / sum(page_view_num) ::NUMERIC * 100 end
        as add_work_wechat_rate,
        case when sum(page_view_num) = 0 then 0 else sum(follow_official_account_num) / sum(page_view_num) ::NUMERIC *
        100 end as follow_official_account_rate,
        case when sum(page_view_num) = 0 then 0 else sum(online_shop_buy_goods_success_num) / sum(page_view_num)
        ::NUMERIC * 100 end as online_shop_buy_goods_success_rate,

        case when sum(page_view_num) = 0 then 0 else sum(identify_group_qr_code_num ) / sum(page_view_num)
        ::NUMERIC * 100 end as identify_group_qr_code_rate,
        case when sum(page_view_num) = 0 then 0 else sum(add_work_wechat_group_num ) / sum(page_view_num)
        ::NUMERIC * 100 end as add_work_wechat_group_rate

        from landingPageChannelData lp
        left join officialAccountAddCustomer offaac on offaac.wechat_official_account_landing_page_channel_id = lp.id
        left join elmData elmD on elmD.channel_id = lp.id
        left join numberOfOrdersCompletedForCouponNum noocfcn on lp.id = noocfcn.channel_id
        left join basePvData pv on lp.id = pv.channel_id
        left join officialAssistantData oad on lp.id = oad.channel_id
    </select>

    <select id="channelSource" resultType="ai.yiye.agent.landingpage.service.dto.LandingPageChannelDTO">
        <!--使用优惠券完成订单数 -->
        with numberOfOrdersCompletedForCouponNum as (
        select channel_id,
        sum(CASE when payment_type is not null and order_status in (0, 1, 3) and use_coupon = true then 1 else 0 end)
        as number_of_orders_completed_for_coupon_num
        from customer
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime != null">
                and created_at between to_timestamp(#{landingPageQuery.startTime}, 'yyyy-MM-dd HH24:mi:ss') and
                to_timestamp(#{landingPageQuery.endTime}, 'yyyy-MM-dd HH24:mi:ss')
            </if>
            <if test="landingPageQuery.advertiseAccountGroupId != null">
                and advertiser_account_group_id = #{landingPageQuery.advertiseAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and landing_page_id = #{landingPageQuery.landingPageId}
            </if>
        </where>
        group by channel_id
        ),
        <!--pv基础数据 -->
        basePvData as (
        select
        pv.channel_id,
        pv.url,
        count(*) as page_view_num,
        coalesce(sum(CASE when pv.length_of_stay >= 120 then 120 else pv.length_of_stay end ), 0) as
        total_length_of_stay,
        SUM(COALESCE(CASE WHEN pv.widget_template_type = 0 and (pv.form_type = 0 or pv.form_type is null)   THEN pv.fill  ELSE 0 END, 0))  AS form_submit_num,
        <!--1.247.0之前的计算方式coalesce(sum(pv.fill), 0) - coalesce(sum(pv.fill * pv.widget_template_type), 0) as form_submit_num,-->
       <!--1.247.0之前的计算方式coalesce(sum(pv.fill * pv.widget_template_type), 0) as order_submit_num,-->
        SUM(COALESCE(CASE WHEN pv.widget_template_type = 1 THEN pv.fill  ELSE 0 END, 0)) AS order_submit_num,
        sum(CASE when pv.payment_type is not null and sd.order_status in (0, 1, 3) then 1 else 0 end) as payment_num,
        sum(pv.identify_qr_code_status) as identify_qr_code_num,
        sum(pv.add_enterprise_wechat_status) as add_work_wechat_num,
        sum(pv.into_wechat_customer_service_session_status) as income_line_num,
        sum(pv.follow_official_account_status) as follow_official_account_num,
        sum(pv.online_shop_buy_goods_product_count) as online_shop_buy_goods_success_num
        from page_view_info pv left join submit_data sd on pv.submit_data_id = sd.id
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime != null">
                and pv.created_at between to_timestamp(#{landingPageQuery.startTime}, 'yyyy-MM-dd HH24:mi:ss') and
                to_timestamp(#{landingPageQuery.endTime}, 'yyyy-MM-dd HH24:mi:ss')
            </if>
            <if test="landingPageQuery.advertiseAccountGroupId != null">
                and pv.advertiser_account_group_id = #{landingPageQuery.advertiseAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and pv.landing_page_id = #{landingPageQuery.landingPageId}
            </if>
            <if test="landingPageQuery.name != null">
                and pv.url like concat('%',#{landingPageQuery.name},'%')
            </if>
        </where>
        group by pv.channel_id, pv.url
        )
        select
        pv.url, lpc.name,
        coalesce(case when page_view_num = 0 then 0 else total_length_of_stay / page_view_num ::NUMERIC end, 0) as
        average_length_of_stay,
        coalesce (number_of_orders_completed_for_coupon_num, 0) as number_of_orders_completed_for_coupon_num,
        coalesce(page_view_num,0) as page_view_num,
        coalesce(form_submit_num,0) as form_submit_num,
        coalesce(order_submit_num,0) as order_submit_num,
        coalesce(payment_num,0) as payment_num,
        coalesce(identify_qr_code_num,0) as identify_qr_code_num,
        coalesce(add_work_wechat_num,0) as add_work_wechat_num,
        coalesce(income_line_num,0) as income_line_num,
        coalesce(follow_official_account_num,0) as follow_official_account_num,
        coalesce(online_shop_buy_goods_success_num,0) as online_shop_buy_goods_success_num,
        case when page_view_num = 0 then 0 else form_submit_num / page_view_num ::NUMERIC * 100 end as
        form_submit_rate,
        case when page_view_num = 0 then 0 else order_submit_num / page_view_num ::NUMERIC * 100 end as
        order_submit_rate,
        case when order_submit_num = 0 then 0 else payment_num / order_submit_num ::NUMERIC * 100 end as
        payment_rate,
        case when page_view_num = 0 then 0 else payment_num / page_view_num ::NUMERIC * 100 end as
        comprehensive_payment_rate,
        case when order_submit_num = 0 then 0 else coalesce(number_of_orders_completed_for_coupon_num,0) /
        order_submit_num ::NUMERIC * 100 end as number_of_orders_completed_for_coupon_rate,
        case when page_view_num = 0 then 0 else coalesce(identify_qr_code_num,0) / page_view_num ::NUMERIC * 100 end
        as identify_qr_code_rate,
        case when page_view_num = 0 then 0 else coalesce(add_work_wechat_num,0) / page_view_num ::NUMERIC * 100 end
        as add_work_wechat_rate,
        case when page_view_num = 0 then 0 else round(coalesce(follow_official_account_num,0) / page_view_num ::NUMERIC *
        100, 2) end as follow_official_account_rate,
        case when page_view_num = 0 then 0 else round(coalesce(online_shop_buy_goods_success_num,0) / page_view_num ::NUMERIC *
        100, 2) end as online_shop_buy_goods_success_rate
        from basePvData pv
        left join numberOfOrdersCompletedForCouponNum noocfcn on pv.channel_id = noocfcn.channel_id
        left join landing_page_channels lpc on pv.channel_id = lpc.id
        order by
        ${landingPageQuery.sort} ${landingPageQuery.order}
    </select>

    <select id="channelSourceTotal" resultType="ai.yiye.agent.landingpage.service.dto.LandingPageChannelDTO">
        <!--使用优惠券完成订单数 -->
        with numberOfOrdersCompletedForCouponNum as (
        select
        landing_page_id,
        sum(CASE when payment_type is not null and order_status in (0, 1, 3) and use_coupon = true then 1 else 0 end)
        as number_of_orders_completed_for_coupon_num
        from customer
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime != null">
                and created_at between to_timestamp(#{landingPageQuery.startTime}, 'yyyy-MM-dd HH24:mi:ss') and
                to_timestamp(#{landingPageQuery.endTime}, 'yyyy-MM-dd HH24:mi:ss')
            </if>
            <if test="landingPageQuery.advertiseAccountGroupId != null">
                and advertiser_account_group_id = #{landingPageQuery.advertiseAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and landing_page_id = #{landingPageQuery.landingPageId}
            </if>
        </where>
        group by landing_page_id
        ),
        <!--pv基础数据 -->
        basePvData as (
        select
        pv.landing_page_id,
        coalesce(sum(CASE when pv.length_of_stay >= 120 then 120 else pv.length_of_stay end ), 0) as
        total_length_of_stay,
        count(*) as page_view_num,
        <!--1.247.0之前的计算逻辑coalesce(sum(pv.fill), 0) - coalesce(sum(pv.fill * pv.widget_template_type), 0) as form_submit_num,-->
        SUM(COALESCE(CASE WHEN pv.widget_template_type = 0 and (pv.form_type = 0 or pv.form_type is null)   THEN pv.fill  ELSE 0 END, 0))  AS form_submit_num,
        <!--1.247.0之前的计算逻辑coalesce(sum(pv.fill * pv.widget_template_type), 0) as order_submit_num,-->
        SUM(COALESCE(CASE WHEN pv.widget_template_type = 1 THEN pv.fill  ELSE 0 END, 0)) AS order_submit_num,
        sum(CASE when pv.payment_type is not null and sd.order_status in (0, 1, 3) then 1 else 0 end) as payment_num,
        sum(pv.identify_qr_code_status) as identify_qr_code_num,
        sum(pv.add_enterprise_wechat_status) as add_work_wechat_num,
        sum(pv.into_wechat_customer_service_session_status) as income_line_num,
        sum(pv.follow_official_account_status) as follow_official_account_num,
        sum(pv.online_shop_buy_goods_product_count) as online_shop_buy_goods_success_num
        from page_view_info pv left join submit_data sd on pv.submit_data_id = sd.id
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime != null">
                and pv.created_at between to_timestamp(#{landingPageQuery.startTime}, 'yyyy-MM-dd HH24:mi:ss') and
                to_timestamp(#{landingPageQuery.endTime}, 'yyyy-MM-dd HH24:mi:ss')
            </if>
            <if test="landingPageQuery.advertiseAccountGroupId != null">
                and pv.advertiser_account_group_id = #{landingPageQuery.advertiseAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and pv.landing_page_id = #{landingPageQuery.landingPageId}
            </if>
            <if test="landingPageQuery.name != null">
                and pv.url like concat('%',#{landingPageQuery.name},'%')
            </if>
        </where>
        group by pv.landing_page_id
        )
        select
        coalesce (sum(number_of_orders_completed_for_coupon_num), 0) as number_of_orders_completed_for_coupon_num,
        sum(page_view_num) as page_view_num,
        coalesce(case when sum(page_view_num) = 0 then 0 else sum(total_length_of_stay) / sum(page_view_num) ::NUMERIC
        end, 0) as
        average_length_of_stay,
        sum(form_submit_num) as form_submit_num,
        sum(order_submit_num) as order_submit_num,
        sum(payment_num) as payment_num,
        sum(identify_qr_code_num) as identify_qr_code_num,
        sum(add_work_wechat_num) as add_work_wechat_num,
        sum(income_line_num) as income_line_num,
        sum(follow_official_account_num) as follow_official_account_num,
        sum(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
        case when sum(page_view_num) = 0 then 0 else sum(form_submit_num) / sum(page_view_num) ::NUMERIC * 100 end as
        form_submit_rate,
        case when sum(page_view_num) = 0 then 0 else sum(order_submit_num) / sum(page_view_num) ::NUMERIC * 100 end as
        order_submit_rate,
        case when sum(order_submit_num) = 0 then 0 else sum(payment_num) / sum(order_submit_num) ::NUMERIC * 100 end as
        payment_rate,
        case when sum(page_view_num) = 0 then 0 else sum(payment_num) / sum(page_view_num) ::NUMERIC * 100 end as
        comprehensive_payment_rate,
        case when sum(order_submit_num) = 0 then 0 else sum(number_of_orders_completed_for_coupon_num) /
        sum(order_submit_num) ::NUMERIC * 100 end as number_of_orders_completed_for_coupon_rate,
        case when sum(page_view_num) = 0 then 0 else sum(identify_qr_code_num) / sum(page_view_num) ::NUMERIC * 100 end
        as identify_qr_code_rate,
        case when sum(page_view_num) = 0 then 0 else sum(add_work_wechat_num) / sum(page_view_num) ::NUMERIC * 100 end
        as add_work_wechat_rate,
        case when sum(page_view_num) = 0 then 0 else round(sum(follow_official_account_num) / sum(page_view_num)
        ::NUMERIC *
        100, 2) end as follow_official_account_rate,
        case when sum(page_view_num) = 0 then 0 else round(sum(online_shop_buy_goods_success_num) / sum(page_view_num)
        ::NUMERIC *
        100, 2) end as online_shop_buy_goods_success_rate
        from basePvData pv
        left join numberOfOrdersCompletedForCouponNum noocfcn on pv.landing_page_id = noocfcn.landing_page_id

    </select>

    <select id="channelSourceTotalNew" resultType="ai.yiye.agent.landingpage.service.dto.LandingPageChannelDTO">
    with
        basePvData as (
        SELECT
        pv.landing_page_id,
        SUM(page_view_num) page_view_num,
        sum(form_submit_num) form_submit_num,
        SUM(order_submit_num) as order_submit_num,
        SUM(payment_num) as payment_num,
        SUM(identify_qr_code_num) as identify_qr_code_num,
        SUM(add_work_wechat_num) as add_work_wechat_num,
        SUM(total_length_of_stay) as total_length_of_stay,
        SUM(follow_official_account_num) as follow_official_account_num,
        SUM(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
        SUM(number_of_orders_completed_for_coupon_num) as number_of_orders_completed_for_coupon_num,
        SUM(official_identify_qr_code_num) as official_identify_qr_code_num,
        SUM(identify_group_qr_code_num) as identify_group_qr_code_num,
        SUM(add_work_wechat_group_num) as add_work_wechat_group_num,
        SUM(wechat_official_article_page_view_num) as wechat_official_article_page_view_num
        FROM landing_page_indicator_statistics pv
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime != null">
                and pv.statistic_date between to_timestamp(#{landingPageQuery.startTime}, 'yyyy-MM-dd HH24:mi:ss') and
                to_timestamp(#{landingPageQuery.endTime}, 'yyyy-MM-dd HH24:mi:ss')
            </if>
            <if test="landingPageQuery.advertiseAccountGroupId != null">
                and pv.advertiser_account_group_id = #{landingPageQuery.advertiseAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and pv.landing_page_id = #{landingPageQuery.landingPageId}
            </if>
        </where>
        group by pv.landing_page_id
        )
        select
        coalesce (sum(number_of_orders_completed_for_coupon_num), 0) as number_of_orders_completed_for_coupon_num,
        sum(page_view_num) as page_view_num,
        coalesce(case when sum(page_view_num) = 0 then 0 else sum(total_length_of_stay) / sum(page_view_num) ::NUMERIC
        end, 0) as
        average_length_of_stay,
        sum(form_submit_num) as form_submit_num,
        sum(order_submit_num) as order_submit_num,
        sum(payment_num) as payment_num,
        sum(identify_qr_code_num) as identify_qr_code_num,
        sum(add_work_wechat_num) as add_work_wechat_num,
        sum(follow_official_account_num) as follow_official_account_num,
        sum(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
        case when sum(page_view_num) = 0 then 0 else sum(form_submit_num) / sum(page_view_num) ::NUMERIC * 100 end as
        form_submit_rate,
        case when sum(page_view_num) = 0 then 0 else sum(order_submit_num) / sum(page_view_num) ::NUMERIC * 100 end as
        order_submit_rate,
        case when sum(order_submit_num) = 0 then 0 else sum(payment_num) / sum(order_submit_num) ::NUMERIC * 100 end as
        payment_rate,
        case when sum(page_view_num) = 0 then 0 else sum(payment_num) / sum(page_view_num) ::NUMERIC * 100 end as
        comprehensive_payment_rate,
        case when sum(order_submit_num) = 0 then 0 else sum(number_of_orders_completed_for_coupon_num) /
        sum(order_submit_num) ::NUMERIC * 100 end as number_of_orders_completed_for_coupon_rate,
        case when sum(page_view_num) = 0 then 0 else sum(identify_qr_code_num) / sum(page_view_num) ::NUMERIC * 100 end
        as identify_qr_code_rate,
        case when sum(page_view_num) = 0 then 0 else sum(add_work_wechat_num) / sum(page_view_num) ::NUMERIC * 100 end
        as add_work_wechat_rate,
        case when sum(page_view_num) = 0 then 0 else round(sum(follow_official_account_num) / sum(page_view_num)
        ::NUMERIC *
        100, 2) end as follow_official_account_rate,
        case when sum(page_view_num) = 0 then 0 else round(sum(online_shop_buy_goods_success_num) / sum(page_view_num)
        ::NUMERIC *
        100, 2) end as online_shop_buy_goods_success_rate
        from basePvData pv

    </select>

    <select id="sumWechatCustomerServiceData" resultType="ai.yiye.agent.domain.LandingPageWechatCustomerService">
        select
            wechat_customer_service_id_1 as id,
            sum(identify_qr_code_status) as identify_qr_code_num,
            sum(wechat_user_id_matching_status) as landAddWorkWechatNum
        from (
            select
                wechat_customer_service_id as wechat_customer_service_id_1,identify_qr_code_status as identify_qr_code_status,wechat_user_id_matching_status as wechat_user_id_matching_status
            from
                page_view_info
            where wechat_customer_service_id is not null and wechat_customer_service_id in
            <foreach collection="collectIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="startTime != null and endTime != null">
                and created_at between to_timestamp(#{startTime}, 'yyyy-MM-dd hh24:mi:ss') and to_timestamp( #{endTime}, 'yyyy-MM-dd hh24:mi:ss')
            </if>
        ) a group by wechat_customer_service_id_1
    </select>

    <select id="sumWechatCustomerServiceDataTotal" resultType="ai.yiye.agent.domain.LandingPageWechatCustomerService">
        select COALESCE(sum(identify_qr_code_status), 0) as identify_qr_code_num,
        COALESCE(sum(wechat_user_id_matching_status), 0) as landAddWorkWechatNum from (
        select wechat_customer_service_id as wechat_customer_service_id_1,
               identify_qr_code_status as identify_qr_code_status,
               wechat_user_id_matching_status as wechat_user_id_matching_status
        from
        page_view_info
        where wechat_customer_service_id is not null and wechat_customer_service_id in
        <foreach collection="collectIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="startTime != null and endTime != null">
            and created_at between to_timestamp(#{startTime}, 'yyyy-MM-dd hh24:mi:ss') and to_timestamp( #{endTime},
            'yyyy-MM-dd hh24:mi:ss')
        </if>
        )a
    </select>

    <select id="currentLandingPageRptByGroupIds" resultType="ai.yiye.agent.domain.dto.LandingPageReportDto">
        <!--1.247.0增加客资相关的统计字段-->
        WITH customerData AS (
            SELECT
                advertiser_account_group_id,
                to_char(created_at,'yyyy-MM-dd') as dayTime,
                SUM ( COALESCE(CASE WHEN douyin_auth_phone IS NOT NULL THEN 1 ELSE 0 END,0) ) AS phone_number_recieved_num
            FROM customer
            <where>
                <if test="endTime != null and startTime != null">
                    created_at &gt;= #{startTime} and created_at &lt; #{endTime}
                </if>
            </where>
            GROUP BY advertiser_account_group_id,to_char(created_at,'yyyy-MM-dd')
        ),
        popUpDisplayNum AS(
            SELECT
                advertiser_account_group_id,
                to_char( created_at, 'yyyy-MM-dd' ) AS dayTime,
                SUM ( pop_up_display_num ) AS pop_up_display_num
            FROM
            page_view_indicator
            <where>
                event_type = 0
                <if test="endTime != null and startTime != null">
                    and  created_at &gt;= #{startTime} and created_at &lt; #{endTime}
                </if>
            </where>
            GROUP BY
            advertiser_account_group_id,
            to_char( created_at, 'yyyy-MM-dd')
        ),
        eleDataReport as (
            select
                to_char(event_time,'yyyy-MM-dd') as dayTime,
                advertiser_account_group_id,
                sum(case when event_type= 0 then 1 else 0 end) as ele_pv_num,
                sum(case when event_type= 1 then 1 else 0 end) as ele_qr_code_view_num,
                sum(case when event_type= 2 then 1 else 0 end) as ele_identify_wechat_qr_code_num,
                sum(case when event_type= 3 then 1 else 0 end) as ele_add_wechat_success_num
            from landing_page_elm_event_callback_record
            <where>
                <if test="endTime != null and startTime != null">
                    event_time &gt;= #{startTime} and event_time &lt; #{endTime}
                </if>
            </where>
            group by to_char(event_time,'yyyy-MM-dd'), advertiser_account_group_id
        ),
        whatsappMessagReport as (
            select to_char(created_at, 'yyyy-MM-dd') as dayTime,
            advertiser_account_group_id,
            sum(case when whatsapp_message_type = 3 then 1 else 0 end) as whatsapp_customer_prologue_num,
            sum(case when whatsapp_message_type = 4 then 1 else 0 end) as whatsapp_customer_send_message_num
            from whatsapp_callback_message
            <where>
                <if test="1 == 1 ">
                    and send_port_type = 2 and message_status in ('Delivered', 'Read')
                </if>
                <if test="endTime != null and startTime != null">
                    and created_at &gt;= #{startTime} and created_at &lt; #{endTime}
                </if>
            </where>
            group by to_char(created_at, 'yyyy-MM-dd'), advertiser_account_group_id
        ),
        <!--公众号助手发码加粉后入群数-->
        officialAssistantData as(
            SELECT
                wechat_applet_user_pmp_id as advertiser_account_group_id,
                count(1) as  add_group_after_follow_official_account_num,
                TO_CHAR( group_chat_join_time, 'yyyy-MM-dd' ) AS day_time
           FROM
                landing_page_wechat_official_account_customer
            <where>
                <if test="startTime != null and  endTime != null">
                    and group_chat_join_time >= #{startTime} and group_chat_join_time &lt; #{endTime}
                </if>
                and followed_add_enterprise_wechat = 1
            </where>
            GROUP BY TO_CHAR( group_chat_join_time, 'yyyy-MM-dd' ), wechat_applet_user_pmp_id
        )
        select count(*) as pv_num,
            coalesce(sum(identify_qr_code_status), 0) as identify_qr_code_num,
            coalesce(sum(flow_source_jump_page_status ), 0) as flow_source_jump_page_view_num,
            coalesce(sum(add_enterprise_wechat_status), 0) as add_work_wechat_num,
            coalesce(sum(follow_official_account_status), 0) as follow_official_account_num,
            COALESCE(SUM((CASE
            WHEN (landing_page_type = 2) THEN 1
            ELSE 0 END)), 0)                  qiye_pv_num,
            COALESCE(SUM((CASE
            WHEN (landing_page_type = 2 AND (parent_pid is not null and parent_pid != ''))
            THEN 1
            ELSE 0 END)), 0)                  qiye_mini_pv_num,
            SUM(case when wechat_official_history_article_status = 2 then 1 else 0 end) as  qiye_wechat_official_article_page_view_num,
             pv.advertiser_account_group_id,
             coalesce(sum(official_identify_qr_code_status),0) as   official_identify_qr_code_num,
             coalesce(sum(identify_group_chat_qr_code_status),0) as   identify_group_qr_code_num,
             coalesce(count(matching_success_outside_chat_id),0) as   add_work_wechat_group_num,
            SUM(COALESCE(CASE WHEN pv.widget_template_type = 0 and (pv.form_type = 0 or pv.form_type is null)   THEN pv.fill  ELSE 0 END, 0))  AS form_submit_num,
            SUM(COALESCE(CASE WHEN pv.form_type = 1 THEN pv.fill  ELSE 0 END, 0)) AS douyin_applet_native_form_submit_num,
            SUM(COALESCE(CASE WHEN pv.form_type = 2 THEN pv.fill  ELSE 0 END, 0)) AS clue_form_submit_num,
            SUM(COALESCE(CASE WHEN pv.douyin_applet_authorization = 1 THEN 1 ELSE 0 END, 0)) AS active_message_authorization_num,
            SUM(COALESCE(CASE WHEN pv.add_group_type = 1 THEN 1  ELSE 0 END, 0)) AS add_group_after_add_customer_service_num,
            SUM(COALESCE(CASE WHEN  pv.douyin_auth_phone is not null THEN 1 ELSE 0 END, 0 ) ) AS phone_number_recieved_num,
            <!--点击跳转淘宝电影小程序数-->
            SUM(COALESCE(CASE WHEN pv.applet_type = 0 and pv.applet_jump_status = 1 THEN 1  ELSE 0 END, 0)) AS tao_bao_movie_applet_jump_num,
            <!--淘宝电影小程序订单数-->
            coalesce(sum(pv.tao_bao_movie_applet_order_status),0)  AS tao_bao_movie_applet_order_num,

            <!--1.256.0新增订单数据-->
            SUM(COALESCE(CASE WHEN pv.widget_template_type = 1 and (pv.order_template_type = 0 or pv.order_template_type = 1002) THEN pv.fill  ELSE 0 END, 0)) AS order_submit_num,
            SUM(CASE when pv.payment_type is not null and (pv.order_template_type  = 0 or pv.order_template_type = 1002)  and pv.order_status in (0, 1, 3) then 1 else 0 end) as order_finish_num,
            SUM(COALESCE(CASE WHEN pv.widget_template_type = 8 and pv.order_template_type  = 1  THEN pv.fill  ELSE 0 END, 0)) AS douyin_applet_order_submit_num,
            SUM(CASE when pv.payment_type is not null and pv.order_template_type  = 1  and pv.order_status in (0, 1, 3) then 1 else 0 end) as douyin_applet_order_finish_num,
            COALESCE(SUM(online_shop_buy_goods_product_count), 0) as online_shop_buy_goods_success_num,
            <!--1.256.0结束-->
            <!-- 1.263.0 饿了么小程序对接 -->
            coalesce(MAX(ele.ele_pv_num),0)as ele_pv_num,
            coalesce(max(ele.ele_qr_code_view_num),0) as ele_qr_code_view_num,
            coalesce(max(ele.ele_identify_wechat_qr_code_num),0) as ele_identify_wechat_qr_code_num,
            coalesce(max(ele.ele_add_wechat_success_num),0) as ele_add_wechat_success_num,
            <!-- 1.265.0 whatsapp-->
            coalesce(sum(pv.whatsapp_jump_status),0) as  whatsapp_jump_num,
            coalesce(sum(pv.whatsapp_add_friend_status),0) as  whatsapp_add_friend_success_num,
            coalesce(sum(pv.whatsapp_user_open_mouth_status),0) as  whatsapp_user_open_mouth_num,
            coalesce(sum(case when pv.landing_page_type = 6 then 1 else 0 end ),0) as  overseas_pv_num,
            <!-- 1.267.0 whatsapp-->
            coalesce(max(whatsappMessagReport.whatsapp_customer_prologue_num),0) as whatsapp_customer_prologue_num,
            coalesce(max(whatsappMessagReport.whatsapp_customer_send_message_num),0) as whatsapp_customer_send_message_num,

            coalesce(MAX(pop.pop_up_display_num),0 ) as pop_up_display_num,
            coalesce(MAX(oad.add_group_after_follow_official_account_num),0 ) as add_group_after_follow_official_account_num,

            to_char(created_at,'yyyy-MM-dd') as dayTime
        from page_view_info pv
            LEFT JOIN customerData cd ON cd.dayTime = to_char(pv.created_at,'yyyy-MM-dd') and cd.advertiser_account_group_id = pv.advertiser_account_group_id
            LEFT JOIN popUpDisplayNum pop ON  pop.dayTime = to_char(pv.created_at,'yyyy-MM-dd') and pop.advertiser_account_group_id = pv.advertiser_account_group_id
            LEFT JOIN eleDataReport ele ON ele.dayTime = to_char(pv.created_at,'yyyy-MM-dd') and ele.advertiser_account_group_id = pv.advertiser_account_group_id
            LEFT JOIN whatsappMessagReport whatsappMessagReport on whatsappMessagReport.dayTime = to_char(pv.created_at,'yyyy-MM-dd') and whatsappMessagReport.advertiser_account_group_id = pv.advertiser_account_group_id
            LEFT JOIN officialAssistantData oad on oad.day_time = to_char(pv.created_at,'yyyy-MM-dd') and oad.advertiser_account_group_id = pv.advertiser_account_group_id
            <where>
                <if test="endTime != null and startTime != null">
                    pv.created_at &gt;= #{startTime} and pv.created_at &lt; #{endTime}
                </if>
            </where>
        group by pv.advertiser_account_group_id,to_char(pv.created_at,'yyyy-MM-dd')
    </select>

    <select id="sumQrcodeShowList" resultType="ai.yiye.agent.domain.LandingPageWechatCustomerService">
        select count(qr_code_show_wcs_id) as qr_code_show_num, qr_code_show_wcs_id as id from
        page_view_info
        where qr_code_show_wcs_id in
        <foreach collection="collectIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="startTime != null and endTime != null">
            and created_at between to_timestamp(#{startTime}, 'yyyy-MM-dd hh24:mi:ss') and to_timestamp( #{endTime},
            'yyyy-MM-dd hh24:mi:ss')
        </if>
        group by qr_code_show_wcs_id
    </select>
    <select id="sumQrcodeShowTotal" resultType="ai.yiye.agent.domain.LandingPageWechatCustomerService">
        select count(qr_code_show_wcs_id) as qr_code_show_num from
        page_view_info
        where qr_code_show_wcs_id in
        <foreach collection="collectIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="startTime != null and endTime != null">
            and created_at between to_timestamp(#{startTime}, 'yyyy-MM-dd hh24:mi:ss') and to_timestamp( #{endTime},
            'yyyy-MM-dd hh24:mi:ss')
        </if>
    </select>
    <select id="pgCountPv" resultType="java.lang.Long">
        select count(1) from page_view_info
        <where>
            <if test="wechatUnionId != null and wechatUnionId!=''">
                and (wechat_unionid = #{wechatUnionId} or wechat_applet_unionid = #{wechatUnionId})
            </if>
            <if test="wechatOpenId != null and wechatOpenId!=''">
                and wechat_openid = #{wechatOpenId}
            </if>
            <if test="uid != null and uid!=''">
                and uid = #{uid}
            </if>
            <if test="pid != null and pid!=''">
                and pid != #{pid}
            </if>
            <if test="landingPageId != null">
                and landing_page_id = #{landingPageId}
            </if>
            <if test="advertiserAccountGroupId != null">
                and advertiser_account_group_id = #{advertiserAccountGroupId}
            </if>
            <if test="startTime != null and startTime.trim()!=''">
                and created_at >= to_timestamp(#{startTime}, 'yyyy-MM-dd hh24:mi:ss')
            </if>
            <if test="endTime != null and endTime.trim()!=''">
                and to_timestamp(#{endTime}, 'yyyy-MM-dd hh24:mi:ss') >= created_at
            </if>
        </where>
    </select>
    <select id="selectQiyeRequestStatByTime" resultType="ai.yiye.agent.domain.dto.LandingPageReportDto">
        SELECT
            COALESCE(t1.advertiser_account_group_id, 0) as advertiser_account_group_id,
            to_char( t1.created_at, 'yyyy-MM-dd' ) AS day_time,
            COUNT(1) AS qiyeRequestNum,
            COALESCE(SUM((CASE WHEN t1.status = 0 THEN 1 ELSE 0 END)),0) as qiye_request_success_num,
            COALESCE(SUM((CASE WHEN t1.status = 1 THEN 1 ELSE 0 END)),0) as qiye_request_fail_num,
            COALESCE(SUM(CASE WHEN qiye_generate_scheme_type = 1 then 1 else 0 end),0) as qiye_wechat_official_article_request_num,
            COALESCE(SUM((CASE WHEN qiye_generate_scheme_type = 1 and t1.status = 0 THEN 1 ELSE 0 END)),0) as qiye_wechat_official_article_request_success_num,
            COALESCE(SUM((CASE WHEN qiye_generate_scheme_type = 1 and t1.status = 1 THEN 1 ELSE 0 END)),0) as qiye_wechat_official_article_request_fail_num
        FROM qiye_scheme_call_record t1
        <where>
            <if test="endTime != '' and startTime!=''">
                t1.created_at &gt;= to_timestamp(#{startTime}, 'yyyy-MM-dd hh24:mi:ss') and t1.created_at &lt;
                to_timestamp(#{endTime}, 'yyyy-MM-dd hh24:mi:ss')
            </if>
        </where>
        GROUP BY
        t1.advertiser_account_group_id,
        to_char( t1.created_at, 'yyyy-MM-dd' )
    </select>

    <select id="getTotalChannelFromIndicatorStatistics" resultType="ai.yiye.agent.domain.LandingPageChannel">
        <!--pv基础数据 -->
        with  basePvData as (
        select
        pv.channel_id,
        SUM(page_view_num) page_view_num,
        sum(form_submit_num) form_submit_num,
        SUM(order_submit_num) as order_submit_num,
        SUM(payment_num) as payment_num,
        SUM(identify_qr_code_num) as identify_qr_code_num,
        SUM(add_work_wechat_num) as add_work_wechat_num,
        SUM(follow_official_account_num) as follow_official_account_num,
        SUM(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
        SUM(number_of_orders_completed_for_coupon_num) as number_of_orders_completed_for_coupon_num,
        SUM(official_identify_qr_code_num) as official_identify_qr_code_num,
        SUM(identify_group_qr_code_num) as identify_group_qr_code_num,
        SUM(add_work_wechat_group_num) as add_work_wechat_group_num,
        SUM(wechat_official_article_page_view_num) as wechat_official_article_page_view_num
        from landing_page_indicator_statistics pv
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime != null">
                and pv.statistic_date between #{landingPageQuery.startTime} and #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and pv.advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageId != null">
                and pv.landing_page_id = #{landingPageQuery.landingPageId}
            </if>
        </where>
        group by pv.channel_id
        ),
        <!--渠道数据-->
        landingPageChannelData as (
        select id, created_at, updated_at from landing_page_channels
        <where>
            <if test="landingPageQuery.landingPageId != null">
                and landing_page_id = #{landingPageQuery.landingPageId}
            </if>
            <if test="landingPageQuery.deleteStatus != null">
                and delete_status = #{landingPageQuery.deleteStatus}
            </if>
        </where>
        )
        SELECT
        coalesce(case
        when sum(pv.page_view_num) = 0 then 0
        else sum(pv.official_identify_qr_code_num) / sum(pv.page_view_num) ::NUMERIC * 100 end,
        0)                                         as official_identify_qr_code_rate,
        sum(pv.wechat_official_article_page_view_num ) as wechat_official_article_page_view_num,
        sum(pv.official_identify_qr_code_num) as official_identify_qr_code_num,
        sum(number_of_orders_completed_for_coupon_num) as number_of_orders_completed_for_coupon_num,
        sum(page_view_num) as page_view_num,
        sum(form_submit_num) as form_submit_num,
        sum(order_submit_num) as order_submit_num,
        sum(payment_num) as payment_num,
        sum(identify_qr_code_num) as identify_qr_code_num,
        sum(add_work_wechat_num) as add_work_wechat_num,
        sum(follow_official_account_num) as follow_official_account_num,
        sum(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
        sum(identify_group_qr_code_num) as identify_group_qr_code_num,
        sum(add_work_wechat_group_num) as add_work_wechat_group_num,
        case when sum(page_view_num) = 0 then 0 else sum(form_submit_num) / sum(page_view_num) ::NUMERIC * 100 end as
        form_submit_rate,
        case when sum(page_view_num) = 0 then 0 else sum(order_submit_num) / sum(page_view_num) ::NUMERIC * 100 end as
        order_submit_rate,
        case when sum(order_submit_num) = 0 then 0 else sum(payment_num) / sum(order_submit_num) ::NUMERIC * 100 end as
        payment_rate,
        case when sum(page_view_num) = 0 then 0 else sum(payment_num) / sum(page_view_num) ::NUMERIC * 100 end as
        comprehensive_payment_rate,
        case when sum(order_submit_num)  = 0 then 0 else sum(number_of_orders_completed_for_coupon_num) /
        sum(order_submit_num) ::NUMERIC * 100 end as number_of_orders_completed_for_coupon_rate,
        case when sum(page_view_num) = 0 then 0 else sum(identify_qr_code_num) / sum(page_view_num) ::NUMERIC * 100 end
        as identify_qr_code_rate,
        case when sum(page_view_num) = 0 then 0 else sum(add_work_wechat_num) / sum(page_view_num) ::NUMERIC * 100 end
        as add_work_wechat_rate,
        case when sum(page_view_num) = 0 then 0 else sum(follow_official_account_num) / sum(page_view_num) ::NUMERIC *
        100 end as follow_official_account_rate,
        case when sum(page_view_num) = 0 then 0 else sum(online_shop_buy_goods_success_num) / sum(page_view_num)
        ::NUMERIC * 100 end as online_shop_buy_goods_success_rate,
        case when sum(page_view_num) = 0 then 0 else sum(identify_group_qr_code_num ) / sum(page_view_num)
        ::NUMERIC * 100 end as identify_group_qr_code_rate,
        case when sum(page_view_num) = 0 then 0 else sum(add_work_wechat_group_num ) / sum(page_view_num)
        ::NUMERIC * 100 end as add_work_wechat_group_rate
        FROM
        landingPageChannelData lp
        LEFT JOIN  basePvData pv ON lp.id = pv.channel_id
    </select>
    <select id="countPgEmpPv" resultType="java.lang.Long">
        select count(1) from page_view_info
        where created_at between #{startTime} and #{endTime}
          and advertiser_account_group_id = #{advertiserAccountGroupId};
    </select>

    <select id="currentLandingPageRptByGroupIdsNew" resultType="ai.yiye.agent.domain.dto.LandingPageReportDto">
        select
            advertiser_account_group_id as advertiser_account_group_id,
            day_time as day_time,
            sum(pv_num) as pv_num,
            sum(phone_number_recieved_num) as phone_number_recieved_num,
            sum(active_message_authorization_num) as active_message_authorization_num,
            sum(form_submit_num) as form_submit_num,
            sum(douyin_applet_native_form_submit_num) as douyin_applet_native_form_submit_num,
            sum(clue_form_submit_num) as clue_form_submit_num,
            sum(identify_qr_code_num) as identify_qr_code_num,
            sum(add_work_wechat_num) as add_work_wechat_num,
            sum(follow_official_account_num) as follow_official_account_num,
            sum(qiye_pv_num) as qiye_pv_num,
            sum(qiye_mini_pv_num) as qiye_mini_pv_num,
            sum(qiye_wechat_official_article_page_view_num) as qiye_wechat_official_article_page_view_num,
            sum(official_identify_qr_code_num) as official_identify_qr_code_num,
            sum(identify_group_qr_code_num) as identify_group_qr_code_num,
            sum(add_work_wechat_group_num) as add_work_wechat_group_num,
            sum(pop_up_display_num) as pop_up_display_num,
            <!-- 1.263.0 饿了么小程序对接 -->
            sum(ele_pv_num) as ele_pv_num,
            sum(ele_qr_code_view_num) as ele_qr_code_view_num,
            sum(ele_identify_wechat_qr_code_num) as ele_identify_wechat_qr_code_num,
            sum(ele_add_wechat_success_num) as ele_add_wechat_success_num,
            <!--1.265.0 whatsapp -->
            sum(overseas_pv_num) as  overseas_pv_num,
            sum(whatsapp_jump_num) as  whatsapp_jump_num,
            sum(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
            sum(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,
            <!--1.256.0-->
            sum(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
            sum(douyin_applet_order_submit_num) as douyin_applet_order_submit_num,
            sum(douyin_applet_order_finish_num) as douyin_applet_order_finish_num,
            sum(order_submit_num) as order_submit_num,
            sum(order_finish_num) as order_finish_num,
            sum(tao_bao_movie_applet_jump_num) as tao_bao_movie_applet_jump_num,
            sum(tao_bao_movie_applet_order_num) as tao_bao_movie_applet_order_num,
            sum(add_group_after_add_customer_service_num) as add_group_after_add_customer_service_num,
            sum(add_group_after_follow_official_account_num) as add_group_after_follow_official_account_num,
            sum(flow_source_jump_page_view_num) as flow_source_jump_page_view_num

        from
            (
                select
                    advertiser_account_group_id as advertiser_account_group_id,
                    toDate(created_day_at) as day_time,
                    toUInt64(COALESCE(sumIf(1, event_type = 0), 0)) as pv_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 7), 0)) as identify_qr_code_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 8), 0)) as add_work_wechat_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 9), 0)) as follow_official_account_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 0 and landing_page_type = 2),0)) as qiye_pv_num,

                    toUInt64(COALESCE(sumIf(1,event_type = 0 and landing_page_type = 2 and parent_pid IS NOT NULL), 0)) as qiye_mini_pv_num,
                    toUInt64(COALESCE(sumIf(1,event_type = 16 and wechat_official_history_article_status = 2 ), 0)) as qiye_wechat_official_article_page_view_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 6), 0)) as official_identify_qr_code_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 4), 0)) as identify_group_qr_code_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 5), 0)) as add_work_wechat_group_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 24), 0)) as pop_up_display_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 21), 0)) as phone_number_recieved_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 22), 0)) as active_message_authorization_num,
                    toUInt64(COALESCE(sumIf(1, event_type == 1 and form_type == 0), 0)) as form_submit_num,
                    toUInt64(COALESCE(sumIf(1, event_type == 1 and form_type == 1), 0)) as douyin_applet_native_form_submit_num,
                    toUInt64(COALESCE(sumIf(1, event_type == 1 and form_type == 2), 0)) as clue_form_submit_num,

                    <!--1.263.0-->
                    toUInt64(COALESCE(sumIf(1, event_type = 27), 0)) as ele_pv_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 28), 0)) as ele_qr_code_view_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 29), 0)) as ele_identify_wechat_qr_code_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 30), 0)) as ele_add_wechat_success_num,
                    <!--1.265.0 whatsapp -->
                    toUInt64(COALESCE(sumIf(1,event_type = 0 and landing_page_type = 6), 0)) as overseas_pv_num,
                    toUInt64(COALESCE(sumIf(1,event_type = 33),0)) as  whatsapp_jump_num,
                    toUInt64(COALESCE(sumIf(1,event_type = 34),0)) as  whatsapp_add_friend_success_num,
                    toUInt64(COALESCE(sumIf(1,event_type = 35),0))  as  whatsapp_user_open_mouth_num,
                    <!--1.256.0-->
                    toUInt64(COALESCE(sumIf(1, event_type = 2 and order_template_type = 1), 0)) as douyin_applet_order_submit_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 3 and order_template_type = 1), 0)) as douyin_applet_order_finish_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 11), 0)) as online_shop_buy_goods_success_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 2 and order_template_type == 0), 0)) as order_submit_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 3 and order_template_type == 0), 0)) as order_finish_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 31), 0))  as tao_bao_movie_applet_jump_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 32), 0))  as tao_bao_movie_applet_order_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 36), 0))  as add_group_after_add_customer_service_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 37), 0))  as add_group_after_follow_official_account_num,
                    toUInt64(COALESCE(sumIf(1, event_type = 0 and flow_source_jump_page_status == 1), 0))  as flow_source_jump_page_view_num
                from
                    page_view_event_info
                where
                    created_day_at &gt;= if(greater(yesterday(), toDate(#{startTime})), yesterday(), toDate(#{startTime}))
                    and created_day_at &lt; toDate(#{endTime})
                group by
                    advertiser_account_group_id,
                    toDate(created_day_at)
                union all
                select advertiser_account_group_id                     as advertiser_account_group_id,
                       day_time                                        as day_time,
                       sum(pv_num)                                     as pv_num,
                       sum(identify_qr_code_num)                       as identify_qr_code_num,
                       sum(add_work_wechat_num)                        as add_work_wechat_num,
                       sum(follow_official_account_num)                as follow_official_account_num,
                       sum(qiye_pv_num)                                as qiye_pv_num,
                       sum(qiye_mini_pv_num)                           as qiye_mini_pv_num,
                       sum(qiye_wechat_official_article_page_view_num) as qiye_wechat_official_article_page_view_num,
                       sum(official_identify_qr_code_num)              as official_identify_qr_code_num,
                       sum(identify_group_qr_code_num)                 as identify_group_qr_code_num,
                       sum(add_work_wechat_group_num)                  as add_work_wechat_group_num,
                       sum(pop_up_display_num)                         as pop_up_display_num,
                       sum(phone_number_recieved_num) as phone_number_recieved_num,
                       sum(active_message_authorization_num) as active_message_authorization_num,
                       sum(form_submit_num) as form_submit_num,
                       sum(douyin_applet_native_form_submit_num) as douyin_applet_native_form_submit_num,
                       sum(clue_form_submit_num) as clue_form_submit_num,
                        <!--1.263.0-->
                        sum(ele_pv_num) as ele_pv_num,
                        sum(ele_qr_code_view_num) as ele_qr_code_view_num,
                        sum(ele_identify_wechat_qr_code_num) as ele_identify_wechat_qr_code_num,
                        sum(ele_add_wechat_success_num) as ele_add_wechat_success_num,
                        <!--1.265.0 whatsapp -->
                        sum(overseas_pv_num) as  overseas_pv_num,
                        sum(whatsapp_jump_num) as  whatsapp_jump_num,
                        sum(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
                        sum(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,
                       <!--1.256.0-->
                       sum(douyin_applet_order_submit_num) as douyin_applet_order_submit_num,
                       sum(douyin_applet_order_finish_num) as douyin_applet_order_finish_num,
                       sum(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
                       sum(order_submit_num) as order_submit_num,
                       sum(payment_num) as order_finish_num,
                       sum(tao_bao_movie_applet_jump_num) as tao_bao_movie_applet_jump_num,
                       sum(tao_bao_movie_applet_order_num) as tao_bao_movie_applet_order_num,
                       sum(add_group_after_add_customer_service_num) as add_group_after_add_customer_service_num,
                       sum(add_group_after_follow_official_account_num) as add_group_after_follow_official_account_num,
                       sum(flow_source_jump_page_view_num) as flow_source_jump_page_view_num

                from (select advertiser_account_group_id                                      as advertiser_account_group_id,
                             landing_page_id                                                  as landing_page_id,
                             landing_page_channel_id                                          as landing_page_channel_id,
                             douyin_customer_source                                           as douyin_customer_source,
                             toDate(statistic_date)                                           as day_time,
                            toUInt64(sum(page_view_num * sign))                              as pv_num,
                            toUInt64(sum(identify_qr_code_num * sign))                       as identify_qr_code_num,
                            toUInt64(sum(add_work_wechat_num * sign))                        as add_work_wechat_num,
                            toUInt64(sum(follow_official_account_num * sign))                as follow_official_account_num,
                            toUInt64(sum(qiye_pv_num * sign))                                as qiye_pv_num,
                            toUInt64(sum(qiye_mini_pv_num * sign))                           as qiye_mini_pv_num,
                            toUInt64(sum(qiye_wechat_official_article_page_view_num * sign)) as qiye_wechat_official_article_page_view_num,
                            toUInt64(sum(official_identify_qr_code_num * sign))              as official_identify_qr_code_num,
                            toUInt64(sum(identify_group_qr_code_num * sign))                 as identify_group_qr_code_num,
                            toUInt64(sum(form_submit_num * sign))                            as form_submit_num,
                            toUInt64(sum(clue_form_submit_num * sign))                       as clue_form_submit_num,
                            toUInt64(sum(pop_up_display_num * sign))                         as pop_up_display_num,
                            toUInt64(sum(phone_number_recieved_num * sign))                  as phone_number_recieved_num,
                            toUInt64(sum(douyin_applet_native_form_submit_num * sign))       as douyin_applet_native_form_submit_num,
                            toUInt64(sum(active_message_authorization_num * sign))           as active_message_authorization_num,
                            toUInt64(sum(add_work_wechat_group_num * sign))                  as add_work_wechat_group_num,

                            <!--1.263.0-->
                            toUInt64(sum(ele_pv_num * sign)) as ele_pv_num,
                            toUInt64(sum(ele_qr_code_view_num * sign)) as ele_qr_code_view_num,
                            toUInt64(sum(ele_identify_wechat_qr_code_num * sign)) as ele_identify_wechat_qr_code_num,
                            toUInt64(sum(ele_add_wechat_success_num * sign)) as ele_add_wechat_success_num,
                            <!--1.265.0 whatsapp -->
                            toUInt64(sum(overseas_pv_num * sign)) as  overseas_pv_num,
                            toUInt64(sum(whatsapp_jump_num * sign)) as  whatsapp_jump_num,
                            toUInt64(sum(whatsapp_add_friend_success_num * sign)) as  whatsapp_add_friend_success_num,
                            toUInt64(sum(whatsapp_user_open_mouth_num * sign)) as  whatsapp_user_open_mouth_num,

                            <!--1.256.0-->
                            toUInt64(sum(douyin_applet_order_submit_num * sign))             as douyin_applet_order_submit_num,
                            toUInt64(sum(douyin_applet_order_finish_num * sign))             as douyin_applet_order_finish_num,
                            toUInt64(sum(online_shop_buy_goods_success_num * sign))          as online_shop_buy_goods_success_num,
                            toUInt64(sum(order_submit_num * sign))                           as order_submit_num,
                            toUInt64(sum(payment_num * sign))                                as payment_num,
                            toUInt64(sum(tao_bao_movie_applet_jump_num * sign))              as tao_bao_movie_applet_jump_num,
                            toUInt64(sum(tao_bao_movie_applet_order_num * sign))             as tao_bao_movie_applet_order_num,
                            toUInt64(sum(add_group_after_add_customer_service_num * sign))   as add_group_after_add_customer_service_num,
                            toUInt64(sum(add_group_after_follow_official_account_num * sign))  as add_group_after_follow_official_account_num,
                            toUInt64(sum(flow_source_jump_page_view_num * sign))  as flow_source_jump_page_view_num

                      from landing_page_day_statistics_new
                      where statistic_date &lt; if(greater(yesterday(), toDate(#{endTime})), toDate(#{endTime}), yesterday())
                        and statistic_date &gt;= toDate(#{startTime})
                      group by advertiser_account_group_id, landing_page_id, landing_page_channel_id, statistic_date,douyin_customer_source
                      having sum(sign) > 0)
                group by advertiser_account_group_id, day_time
            )
        group by
            advertiser_account_group_id,
            day_time
    </select>

    <update id ="updateParentSubmitDataIdByPid">
        update page_view_info set parent_submit_data_id = #{parentSubmitDataId} where pid = #{pid}
    </update>


    <!--曝光口径-->
    <select id="getAccountTargetDataFromCkView" resultType="ai.yiye.agent.domain.vo.AdvertiserAccountGroupReportCliskhouse">

        SELECT
            pvi.advertiser_account_group_id AS advertiser_account_group_id,
            pvi.created_day_at AS day_time,
            SUM(event_type = 0) AS landing_page_pv,
            SUM(event_type = 1) AS fill_count_num,
            SUM(event_type = 2) AS order_num,
            SUM(event_type = 3) AS order_finish_num,
            SUM(event_type = 6) AS official_identify_qr_code_num,
            SUM(event_type = 7) AS identify_qr_code_num,
            SUM(event_type = 8) AS add_work_wechat_num,
            SUM(event_type = 9) AS follow_official_account_num,
            SUM(event_type = 11) AS online_shop_buy_goods_order_num,
            SUM(CASE WHEN event_type = 3 THEN transaction_amount ELSE 0 END) AS order_transaction_amount,
            SUM(CASE WHEN event_type = 11 THEN transaction_amount ELSE 0 END) AS order_transaction_price,

           <!--计算成本字段-->
            CASE
                WHEN SUM(CASE WHEN event_type = 1 THEN 1 ELSE 0 END) = 0 THEN 0
                ELSE COALESCE(${advertiserAccountGroupReport.cost}, 0) / SUM(CASE WHEN event_type = 1 THEN 1 ELSE 0 END)
                END AS  fill_count_cost,

            CASE
                WHEN SUM(CASE WHEN event_type = 2 THEN 1 ELSE 0 END) = 0 THEN 0
                ELSE COALESCE(${advertiserAccountGroupReport.cost}, 0) / SUM(CASE WHEN event_type = 2 THEN 1 ELSE 0 END)
                END AS  order_count_cost,

            CASE
                WHEN SUM(CASE WHEN event_type = 3 THEN 1 ELSE 0 END) = 0 THEN 0
                ELSE COALESCE(${advertiserAccountGroupReport.cost}, 0) / SUM(CASE WHEN event_type = 3 THEN 1 ELSE 0 END)
                END AS  order_finish_cost,

            CASE
                WHEN SUM(CASE WHEN event_type = 7 THEN 1 ELSE 0 END) = 0 THEN 0
                ELSE COALESCE(${advertiserAccountGroupReport.cost}, 0) / SUM(CASE WHEN event_type = 7 THEN 1 ELSE 0 END)
                END AS  identify_qr_code_cost,

            CASE
                WHEN SUM(CASE WHEN event_type = 8 THEN 1 ELSE 0 END) = 0 THEN 0
                ELSE COALESCE(${advertiserAccountGroupReport.cost}, 0) / SUM(CASE WHEN event_type = 8 THEN 1 ELSE 0 END)
                END AS  add_work_wechat_cost,

            CASE
            WHEN SUM(CASE WHEN event_type = 9 THEN 1 ELSE 0 END) = 0 THEN 0
            ELSE COALESCE(${advertiserAccountGroupReport.cost}, 0) / SUM(CASE WHEN event_type = 9 THEN 1 ELSE 0 END)
            END AS  official_focus_cost2,

            ${advertiserAccountGroupReport.viewNum} as view_num,
            ${advertiserAccountGroupReport.clickNum} as click_num,
            ${advertiserAccountGroupReport.convertCount} as convert_num,
            ${advertiserAccountGroupReport.deepConvertNum} as deep_convert_num,

            CASE
                when coalesce(${advertiserAccountGroupReport.convertCount},0) = 0 then 0
                else coalesce(${advertiserAccountGroupReport.cost},0)  / coalesce(${advertiserAccountGroupReport.convertCount},0)
                END AS   target_convert_cost,

            CASE
                when coalesce(${advertiserAccountGroupReport.deepConvertNum},0) = 0 then 0
                else coalesce(${advertiserAccountGroupReport.cost},0) / coalesce(${advertiserAccountGroupReport.deepConvertNum},0)
                END AS  deep_convert_cost

        FROM page_view_event_info pvi
        WHERE
            pvi.advertiser_account_group_id = #{advertiserAccountGroupId}
          AND pvi.created_at BETWEEN #{startDay} AND #{endDay}
        GROUP BY
            pvi.advertiser_account_group_id,
            pvi.created_day_at;

    </select>

    <!--转化口径-->
    <select id="getAccountTargetDataFromCkConvert" resultType="ai.yiye.agent.domain.vo.AdvertiserAccountGroupReportCliskhouse">

        SELECT
        pvi.advertiser_account_group_id AS advertiser_account_group_id,
        toStartOfDay(pvi.convert_time) AS day_time,
        SUM(event_type = 0) AS landing_page_pv,
        SUM(event_type = 1) AS fill_count_num,
        SUM(event_type = 2) AS order_num,
        SUM(event_type = 3) AS order_finish_num,
        SUM(event_type = 6) AS official_identify_qr_code_num,
        SUM(event_type = 7) AS identify_qr_code_num,
        SUM(event_type = 8) AS add_work_wechat_num,
        SUM(event_type = 9) AS follow_official_account_num,
        SUM(event_type = 11) AS online_shop_buy_goods_order_num,
        SUM(CASE WHEN event_type = 3 THEN transaction_amount ELSE 0 END) AS order_transaction_amount,
        SUM(CASE WHEN event_type = 11 THEN transaction_amount ELSE 0 END) AS order_transaction_price,

        <!--计算成本字段-->
        CASE
        WHEN SUM(CASE WHEN event_type = 1 THEN 1 ELSE 0 END) = 0 THEN 0
        ELSE COALESCE(${advertiserAccountGroupReport.cost}, 0) / SUM(CASE WHEN event_type = 1 THEN 1 ELSE 0 END)
        END AS  fill_count_cost,

        CASE
        WHEN SUM(CASE WHEN event_type = 2 THEN 1 ELSE 0 END) = 0 THEN 0
        ELSE COALESCE(${advertiserAccountGroupReport.cost}, 0) / SUM(CASE WHEN event_type = 2 THEN 1 ELSE 0 END)
        END AS  order_count_cost,

        CASE
        WHEN SUM(CASE WHEN event_type = 3 THEN 1 ELSE 0 END) = 0 THEN 0
        ELSE COALESCE(${advertiserAccountGroupReport.cost}, 0) / SUM(CASE WHEN event_type = 3 THEN 1 ELSE 0 END)
        END AS  order_finish_cost,

        CASE
        WHEN SUM(CASE WHEN event_type = 7 THEN 1 ELSE 0 END) = 0 THEN 0
        ELSE COALESCE(${advertiserAccountGroupReport.cost}, 0) / SUM(CASE WHEN event_type = 7 THEN 1 ELSE 0 END)
        END AS  identify_qr_code_cost,

        CASE
        WHEN SUM(CASE WHEN event_type = 8 THEN 1 ELSE 0 END) = 0 THEN 0
        ELSE COALESCE(${advertiserAccountGroupReport.cost}, 0) / SUM(CASE WHEN event_type = 8 THEN 1 ELSE 0 END)
        END AS  add_work_wechat_cost,

        CASE
        WHEN SUM(CASE WHEN event_type = 9 THEN 1 ELSE 0 END) = 0 THEN 0
        ELSE COALESCE(${advertiserAccountGroupReport.cost}, 0) / SUM(CASE WHEN event_type = 9 THEN 1 ELSE 0 END)
        END AS  official_focus_cost2,

        ${advertiserAccountGroupReport.viewNum} as view_num,
        ${advertiserAccountGroupReport.clickNum} as click_num,
        ${advertiserAccountGroupReport.convertCount} as convert_num,
        ${advertiserAccountGroupReport.deepConvertNum} as deep_convert_num,

        CASE
        when coalesce(${advertiserAccountGroupReport.convertCount},0) = 0 then 0
        else coalesce(${advertiserAccountGroupReport.cost},0)  / coalesce(${advertiserAccountGroupReport.convertCount},0)
        END AS   target_convert_cost,

        CASE
        when coalesce(${advertiserAccountGroupReport.deepConvertNum},0) = 0 then 0
        else coalesce(${advertiserAccountGroupReport.cost},0) / coalesce(${advertiserAccountGroupReport.deepConvertNum},0)
        END AS  deep_convert_cost

        FROM page_view_event_info pvi
        WHERE
        pvi.advertiser_account_group_id = #{advertiserAccountGroupId}
        AND pvi.convert_time BETWEEN #{startDay} AND #{endDay}
        GROUP BY
        pvi.advertiser_account_group_id,
        toStartOfDay(pvi.convert_time);

    </select>


    <select id="getAdFlowSourceAnalysis"
            resultType="ai.yiye.agent.landingpage.controller.vo.AdFlowSourceAnalysisVO">

        SELECT
            SUM(COALESCE(CASE WHEN pv.flow_source = 3 THEN 1 ELSE 0 END, 0)) AS dou_yin,
            SUM(COALESCE(CASE WHEN pv.flow_source = 3 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS dou_yin_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 4 THEN 1 ELSE 0 END, 0)) AS dou_yin_lite,
            SUM(COALESCE(CASE WHEN pv.flow_source = 4 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS dou_yin_lite_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 5 THEN 1 ELSE 0 END, 0)) AS dou_yin_huoshan,
            SUM(COALESCE(CASE WHEN pv.flow_source = 5 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS dou_yin_huoshan_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 7 THEN 1 ELSE 0 END, 0)) AS fan_qie,
            SUM(COALESCE(CASE WHEN pv.flow_source = 7 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS fan_qie_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 1 THEN 1 ELSE 0 END, 0)) AS jin_ri_tou_tiao,
            SUM(COALESCE(CASE WHEN pv.flow_source = 1 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS jin_ri_tou_tiao_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 6 THEN 1 ELSE 0 END, 0)) AS xi_gua_shi_pin,
            SUM(COALESCE(CASE WHEN pv.flow_source = 6 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS xi_gua_shi_pin_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 10   THEN 1 ELSE 0 END, 0)) AS pango_lin,
            SUM(COALESCE(CASE WHEN pv.flow_source = 10 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS pango_lin_add,
            count(*) AS total,
            SUM(COALESCE(CASE WHEN pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS total_add,
            TO_CHAR(created_at, 'yyyy-MM-dd') AS day_time
        FROM page_view_info pv
        <where>

            pv.created_at >=  to_timestamp(#{landingPageReportFrom.startTime}, 'yyyy-MM-dd') and pv.created_at &lt; to_timestamp(#{landingPageReportFrom.endTime}, 'yyyy-MM-dd')

            <if test="landingPageReportFrom.landingPageId!=null and landingPageReportFrom.landingPageId!=''">
                AND pv.landing_page_id = #{landingPageReportFrom.landingPageId}
            </if>
            <if test="landingPageReportFrom.channelId != null">
                and pv.channel_id = #{landingPageReportFrom.channelId}
            </if>

        </where>
        GROUP BY day_time
        ORDER BY day_time;
    </select>

    <select id="getAdFlowSourceAnalysisTotal"
            resultType="ai.yiye.agent.landingpage.controller.vo.AdFlowSourceAnalysisVO">
        SELECT
            SUM(COALESCE(CASE WHEN pv.flow_source = 3 THEN 1 ELSE 0 END, 0)) AS dou_yin,
            SUM(COALESCE(CASE WHEN pv.flow_source = 3 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS dou_yin_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 4 THEN 1 ELSE 0 END, 0)) AS dou_yin_lite,
            SUM(COALESCE(CASE WHEN pv.flow_source = 4 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS dou_yin_lite_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 5 THEN 1 ELSE 0 END, 0)) AS dou_yin_huoshan,
            SUM(COALESCE(CASE WHEN pv.flow_source = 5 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS dou_yin_huoshan_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 7 THEN 1 ELSE 0 END, 0)) AS fan_qie,
            SUM(COALESCE(CASE WHEN pv.flow_source = 7 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS fan_qie_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 1 THEN 1 ELSE 0 END, 0)) AS jin_ri_tou_tiao,
            SUM(COALESCE(CASE WHEN pv.flow_source = 1 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS jin_ri_tou_tiao_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 6 THEN 1 ELSE 0 END, 0)) AS xi_gua_shi_pin,
            SUM(COALESCE(CASE WHEN pv.flow_source = 6 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS xi_gua_shi_pin_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 10  THEN 1 ELSE 0 END, 0)) AS pango_lin,
            SUM(COALESCE(CASE WHEN  pv.flow_source = 10 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS pango_lin_add,
            SUM(COALESCE(CASE WHEN pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS total_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 50 THEN 1 ELSE 0 END, 0)) AS kuai_shou,
            SUM(COALESCE(CASE WHEN pv.flow_source = 50 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS kuai_shou_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 51 THEN 1 ELSE 0 END, 0)) AS kuai_shou_lite,
            SUM(COALESCE(CASE WHEN pv.flow_source = 51 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS kuai_shou_lite_add,
            SUM(COALESCE(CASE WHEN pv.flow_source = 52 THEN 1 ELSE 0 END, 0)) AS kuai_shou_other,
            SUM(COALESCE(CASE WHEN pv.flow_source = 52 and pv.add_enterprise_wechat_status = 1 THEN 1 ELSE 0 END, 0)) AS kuai_shou_other_add,
            count(*) AS total
        FROM page_view_info pv
        <where>

            pv.created_at >=  to_timestamp(#{landingPageReportFrom.startTime}, 'yyyy-MM-dd') and pv.created_at &lt; to_timestamp(#{landingPageReportFrom.endTime}, 'yyyy-MM-dd')

            <if test="landingPageReportFrom.landingPageId!=null and landingPageReportFrom.landingPageId!=''">
                AND pv.landing_page_id = #{landingPageReportFrom.landingPageId}
            </if>
            <if test="landingPageReportFrom.channelId != null">
                and pv.channel_id = #{landingPageReportFrom.channelId}
            </if>

        </where>
    </select>

</mapper>


