<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.yiye.agent.landingpage.mapper.LandingPageDayStatisticsMapper">

    <resultMap id="LandingPageListVoResultMap" type="ai.yiye.agent.domain.landingpage.LandingPageListVo">
        <id property="id" column="lp.id" />
        <result property="useWechatCustomerContact" column="lp.use_wechat_customer_contact"/>
        <result property="name" column="lp.name" />
        <result property="title" column="lp.title" />
        <result property="bgColor" column="lp.bg_color" />
        <result property="token" column="lp.token" />
        <result property="wechatDate" column="lp.wechat_date" />
        <result property="wechatLinkContent" column="lp.wechat_link_content" />
        <result property="wechatLinkAddress" column="lp.wechat_link_address" />
        <result property="advertiserAccountId" column="lp.advertiser_account_id" />
        <result property="formUnreadCount" column="form_unread_count" />
        <result property="orderUnreadCount" column="order_unread_count" />
        <result property="createdAt" column="lp.created_at" />
        <result property="updatedAt" column="lp.updated_at" />
        <result property="creatorId" column="lp.creator_id" />
        <result property="version" column="lp.version" />
        <result property="landingPageWidth" column="lp.landing_page_width" />
        <result property="landingPageHeight" column="lp.landing_page_height" />
        <result property="landingPageSize" column="lp.landing_page_size" />
        <result property="bsId" column="lp.bs_id" />
        <result property="recoveryAt" column="lp.recovery_at" />
        <result property="recoveryId" column="lp.recovery_id" />
        <result property="recoveryName" column="lp.recovery_name" />
        <result property="wechatShareDesc" column="lp.wechat_share_desc" />
        <result property="wechatShareImagePath" column="lp.wechat_share_image_path" />
        <result property="support" column="lp.support" />
        <result property="bgPic" column="lp.bg_pic" />
        <result property="limitFilling" column="lp.limit_filling" />
        <result property="wechatAppId" column="lp.wechat_app_id" />
        <result property="showOpenId" column="lp.show_open_id" />
        <result property="status" column="lp.status" />
        <result property="landingPageGroupName" column="lp.landing_page_group_name" />
        <result property="landingPageGroupId" column="lp.landing_page_group_id" />
        <result property="access" column="lp.access" />
        <result property="convertCustomer" column="lp.convert_customer" />
        <result property="convertUrl" column="lp.convert_url" />
        <result property="convertType" column="lp.convert_type" />
        <result property="top" column="lp.top" />
        <result property="isGroupTop" column="lp.is_group_top" />
        <result property="fillNumNow" column="lp.fill_num_now" />
        <result property="fillAllNums" column="lp.fill_all_nums" />
        <result property="fillNums" column="lp.fill_nums" />
        <result property="fillRate" column="lp.fill_rate" />
        <result property="pv" column="lp.pv" />
        <result property="pvDay" column="lp.pv_day" />
        <result property="pvNow" column="lp.pv_now" />
        <result property="showFillData" column="lp.show_fill_data" />
        <result property="identifyQrCodeNum" column="identify_qr_code_num" />
        <result property="identifyQrCodeRate" column="identify_qr_code_rate" />
        <result property="addWorkWechatNum" column="add_work_wechat_num" />
        <result property="addWorkWechatRate" column="add_work_wechat_rate" />
        <result property="remainTime" column="lp.remain_time" />
        <result property="rename" column="lp.rename" />
        <result property="advertiserAccountGroupId" column="lp.advertiser_account_group_id" />
        <result property="platformId" column="lp.platform_id" />
        <result property="accountId" column="lp.account_id" />
        <result property="accountName" column="lp.account_name" />
        <result property="uploadConfigCopy" column="lp.upload_config_copy" />
        <result property="isJumpWechatApplet" column="lp.is_jump_wechat_applet" />
        <result property="wechatAppletLandingPageId" column="lp.wechat_applet_landing_page_id" />
        <result property="wechatAppletChannelId" column="lp.wechat_applet_channel_id" />
        <result property="wechatAppletChannelUrl" column="lp.wechat_applet_channel_url" />
        <result property="followOfficialAccountNum" column="follow_official_account_num" />
        <result property="officialIdentifyQrCodeNum" column="official_identify_qr_code_num" />
        <result property="officialIdentifyQrCodeRate" column="official_identify_qr_code_rate" />
        <result property="followOfficialAccountRate" column="follow_official_account_rate" />
        <result property="deleteStatus" column="lp.delete_status" />
        <result property="pageViewNum" column="page_view_num" />
        <result property="visitorNum" column="visitor_num" />
        <result property="formSubmitNum" column="form_submit_num" />
        <result property="orderSubmitNum" column="order_submit_num" />
        <result property="paymentNum" column="payment_num" />
        <result property="repeatVisitorRate" column="repeat_visitor_rate" />
        <result property="averageLengthOfStay" column="average_length_of_stay" />
        <result property="formSubmitRate" column="form_submit_rate" />
        <result property="orderSubmitRate" column="order_submit_rate" />
        <result property="paymentRate" column="payment_rate" />
        <result property="comprehensivePaymentRate" column="comprehensive_payment_rate" />
        <result property="numberOfOrdersCompletedForCouponRate" column="number_of_orders_completed_for_coupon_rate" />
        <result property="numberOfOrdersCompletedForCouponNum" column="number_of_orders_completed_for_coupon_num" />
        <result property="repeatVisitorNum" column="repeat_visitor_num" />
        <result property="landingPageContentEnum" column="lp.landingPageContentEnum" />
        <result property="onlineShopBuyGoodsSuccessNum" column="online_shop_buy_goods_success_num" />
        <result property="onlineShopBuyGoodsSuccessRate" column="online_shop_buy_goods_success_rate" />
        <result property="landingPageType" column="lp.landing_page_type"/>
        <result property="review" column="lp.review" />
        <result property="wechatCustomerServiceGroupId" column="lp.wechat_customer_service_group_id" />
        <result property="wechatOfficialAccountId" column="lp.wechat_official_account_id" />
        <result property="qrCodeExpireStr" column="lp.qr_code_expire_str" />
        <result property="qrCodeUrl" column="lp.qr_code_url" />
        <result property="landingPageWechatCustomerCityCodeId" column="lp.landing_page_wechat_customer_city_code_id" />
        <result property="identifyGroupQrCodeNum" column="identify_group_qr_code_num" />
        <result property="addWorkWechatGroupNum" column="add_work_wechat_group_num" />
        <result property="identifyGroupQrCodeRate" column="identify_group_qr_code_rate" />
        <result property="addWorkWechatGroupRate" column="add_work_wechat_group_rate" />
        <result property="wechatGroupChatGroupId" column="lp.wechat_group_chat_group_id" />
        <result property="wechatGroupChatGroupName" column="wechat_group_chat_group_name" />
        <result property="landingPageWechatGroupChatCityCodeId" column="lp.landing_page_wechat_group_chat_city_code_id" />
        <result property="landingPageWechatGroupChatCityCodeName" column="landing_page_wechat_group_chat_city_code_name" />
        <result property="wechatOfficialArticlePageViewNum" column="wechat_official_article_page_view_num" />
        <result property="landingPageSort" column="landing_page_sort"/>
        <result property="landingPageGroupSort" column="landing_page_group_sort"/>
    </resultMap>

    <resultMap id="channelsResultMap" type="ai.yiye.agent.domain.LandingPageChannel">
        <id property="id" column="id"/>
        <result property="landingPageId" column="landing_page_id"/>
        <result property="advertiserAccountGroupId" column="advertiser_account_group_id"/>
        <result property="url" column="url"/>
        <result property="channelParam" column="channel_param"/>
        <result property="name" column="name"/>
        <result property="remark" column="remark"/>
        <result property="total" column="total"/>
        <result property="unread" column="unread"/>
        <result property="appletOriginalId" column="applet_original_id"/>
        <result property="appletUrl" column="applet_url"/>
        <result property="review" column="review"/>
        <result property="reason" column="reason"/>
        <result property="qiyetuiPageId" column="qiyetui_page_id"/>
        <result property="appletApiUrl" column="applet_api_url"/>
        <result property="schemeApiUrl" column="scheme_api_url"/>
        <result property="appletName" column="applet_name"/>
        <result property="landingPageChannelType" column="landing_page_channel_type"/>
        <result property="landingPageType" column="landing_page_type"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="pageViewNum" column="page_view_num"/>
        <result property="visitorNum" column="visitor_num"/>
        <result property="fillNum" column="fill_num"/>
        <result property="fillRate" column="fill_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="averageLengthOfStay" column="average_length_of_stay" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="platformId" column="platform_id"/>
        <result property="accountId" column="account_id"/>
        <result property="accountName" column="account_name"/>
        <result property="valid" column="valid"/>
        <result property="promotionType" column="promotion_type"/>
        <result property="landingPageHasPopupCoupon" column="landing_page_has_popup_coupon"/>
        <result property="landingPageHasOpenId" column="landing_page_has_open_id"/>
        <result property="wechatOfficialAccountName" column="wechat_official_account_name"/>
        <result property="uploadConfigId" column="upload_config_id"/>
        <result property="ilynxPreviewPath" column="ilynx_preview_path"/>
        <result property="identifyQrCodeNum" column="identify_qr_code_num"/>
        <result property="identifyQrCodeRate" column="identify_qr_code_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
        <result property="addWorkWechatNum" column="add_work_wechat_num"/>
        <result property="addWorkWechatRate" column="add_work_wechat_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
        <result property="followOfficialAccountNum" column="follow_official_account_num"/>
        <result property="officialIdentifyQrCodeNum" column="official_identify_qr_code_num"/>
        <result property="officialIdentifyQrCodeRate" column="official_identify_qr_code_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="followOfficialAccountRate" column="follow_official_account_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="siteId" column="site_id"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="validateStatus" column="validate_status"/>
        <result property="validateMessage" column="validate_message"/>
        <result property="auditMessage" column="audit_message"/>
        <result property="deleteStatus" column="delete_status"/>
        <result property="formSubmitNum" column="form_submit_num" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>

        <result property="orderSubmitNum" column="order_submit_num" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="paymentNum" column="payment_num" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="repeatVisitorRate" column="repeat_visitor_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>

        <result property="formSubmitRate" column="form_submit_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="orderSubmitRate" column="order_submit_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="paymentRate" column="payment_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="comprehensivePaymentRate" column="comprehensive_payment_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="numberOfOrdersCompletedForCouponRate" column="number_of_orders_completed_for_coupon_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="repeatVisitorNum" column="repeat_visitor_num"/>
        <result property="numberOfOrdersCompletedForCouponNum" column="number_of_orders_completed_for_coupon_num" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="onlineShopBuyGoodsSuccessNum" column="online_shop_buy_goods_success_num"/>
        <result property="onlineShopBuyGoodsSuccessRate" column="online_shop_buy_goods_success_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result property="identifyGroupQrCodeNum" column="identify_group_qr_code_num"/>
        <result property="addWorkWechatGroupNum" column="add_work_wechat_group_num"/>
        <result property="identifyGroupQrCodeRate" column="identify_group_qr_code_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
        <result property="addWorkWechatGroupRate" column="add_work_wechat_group_rate" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
        <result property="landingPageTypes" column="landing_page_types"/>
        <result property="qiyetuiFlag" column="qiyetui_flag"/>
        <result property="landingPageDomain" column="landing_page_domain"/>
        <result property="wechatOfficialArticlePageViewNum" column="wechat_official_article_page_view_num"/>
        <result property="qiyeTuiCorpId" column="qiye_tui_corp_id"/>
        <result property="qiyeTuiWechatAppletName" column="qiye_tui_wechat_applet_name"/>
        <result property="corpid" column="corpid"/>
        <result property="tuiaAccountId" column="tuia_account_id"/>
        <result property="tuiaAccountName" column="tuia_account_name"/>
    </resultMap>

    <select id="pagelist" resultMap="LandingPageListVoResultMap">

        WITH basePvData AS (
        SELECT
            landing_page_id,
            SUM(page_view_num) page_view_num,
            SUM(form_submit_num) form_submit_num,
            SUM(douyin_applet_native_form_submit_num) douyin_applet_native_form_submit_num,
            SUM(clue_form_submit_num) clue_form_submit_num,
            SUM(phone_number_recieved_num) phone_number_recieved_num,
            SUM(active_message_authorization_num) active_message_authorization_num,
            SUM(order_submit_num) order_submit_num,
            SUM(income_line_num) income_line_num,
            SUM(payment_num) payment_num,
            SUM(identify_qr_code_num) identify_qr_code_num,
            SUM(add_work_wechat_num) add_work_wechat_num,
            SUM(follow_official_account_num) follow_official_account_num,
            SUM(online_shop_buy_goods_success_num) online_shop_buy_goods_success_num,
            SUM(official_identify_qr_code_num) official_identify_qr_code_num,

            SUM(ele_pv_num) ele_pv_num,
            SUM(ele_qr_code_view_num) ele_qr_code_view_num,
            SUM(ele_identify_wechat_qr_code_num) ele_identify_wechat_qr_code_num,
            SUM(ele_add_wechat_success_num) ele_add_wechat_success_num,

            SUM(whatsapp_jump_num) as  whatsapp_jump_num,
            SUM(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
            SUM(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,

            SUM(identify_group_qr_code_num) identify_group_qr_code_num,
            SUM(add_work_wechat_group_num) add_work_wechat_group_num,
            SUM(wechat_official_article_page_view_num) wechat_official_article_page_view_num,
            SUM(number_of_orders_completed_for_coupon_num) number_of_orders_completed_for_coupon_num,
            SUM(douyin_applet_order_submit_num) douyin_applet_order_submit_num,
            SUM(douyin_applet_order_finish_num) douyin_applet_order_finish_num,
            SUM(taobao_component_copy_num) taobao_component_copy_num,
            SUM(douyin_applet_jump_num) douyin_applet_jump_num,
            SUM(official_add_customer_num) official_add_customer_num,
            SUM(tao_bao_movie_applet_jump_num) tao_bao_movie_applet_jump_num,
            SUM(tao_bao_movie_applet_order_num) tao_bao_movie_applet_order_num,
            SUM(add_group_after_add_customer_service_num) add_group_after_add_customer_service_num,
            SUM(add_group_after_follow_official_account_num) add_group_after_follow_official_account_num,
            SUM(send_image_or_qr_code_num) send_image_or_qr_code_num,
            SUM(mini_program_news_num) mini_program_news_num,
            SUM(jump_to_super_red_envelope_num) jump_to_super_red_envelope_num,
            SUM(success_send_welcome_msg_num) success_send_welcome_msg_num,
            SUM(ad_upload_num) ad_upload_num,
            SUM(taobao_page_view_num) taobao_page_view_num,
            SUM(taobao_order_payment_num) taobao_order_payment_num,
            SUM(taobao_product_click_num) taobao_product_click_num,
            SUM(taobao_first_visit_venue_num) taobao_first_visit_venue_num,
            SUM(taobao_red_envelope_receive_num) taobao_red_envelope_receive_num,
            SUM(taobao_cancel_order_payment_num) taobao_cancel_order_payment_num,
            SUM(taobao_high_commission_order_payment_num) taobao_high_commission_order_payment_num,
            SUM(start_open_chat_num) start_open_chat_num,
            SUM(third_open_chat_num) third_open_chat_num,
            SUM(fifth_open_chat_num) fifth_open_chat_num,
            SUM(tenth_open_chat_num) tenth_open_chat_num,
            ifNull(sum(CASE when total_length_of_stay >= 120 then 120 else total_length_of_stay end), 0) as total_length_of_stay
        FROM (
        SELECT
            landing_page_id,
            SUM(page_view_num * sign) page_view_num,
            SUM(form_submit_num * sign) form_submit_num,
            SUM(douyin_applet_native_form_submit_num * sign) douyin_applet_native_form_submit_num,
            SUM(clue_form_submit_num * sign) clue_form_submit_num,
            SUM(phone_number_recieved_num * sign) phone_number_recieved_num,
            SUM(active_message_authorization_num * sign) active_message_authorization_num,
            SUM(order_submit_num * sign) order_submit_num,
            SUM(income_line_num * sign) income_line_num,
            SUM(payment_num * sign) payment_num,
            SUM(identify_qr_code_num * sign) identify_qr_code_num,
            SUM(add_work_wechat_num * sign) add_work_wechat_num,
            SUM(follow_official_account_num * sign) follow_official_account_num,
            SUM(online_shop_buy_goods_success_num * sign) online_shop_buy_goods_success_num,
            SUM(official_identify_qr_code_num * sign) official_identify_qr_code_num,

            SUM(ele_pv_num * sign) ele_pv_num,
            SUM(ele_qr_code_view_num * sign) ele_qr_code_view_num,
            SUM(ele_identify_wechat_qr_code_num * sign) ele_identify_wechat_qr_code_num,
            SUM(ele_add_wechat_success_num * sign) ele_add_wechat_success_num,

            SUM(whatsapp_jump_num * sign) as  whatsapp_jump_num,
            SUM(whatsapp_add_friend_success_num * sign) as  whatsapp_add_friend_success_num,
            SUM(whatsapp_user_open_mouth_num * sign) as  whatsapp_user_open_mouth_num,

            SUM(identify_group_qr_code_num * sign) identify_group_qr_code_num,
            SUM(add_work_wechat_group_num * sign) add_work_wechat_group_num,
            SUM(wechat_official_article_page_view_num * sign) wechat_official_article_page_view_num,
            SUM(number_of_orders_completed_for_coupon_num * sign) number_of_orders_completed_for_coupon_num,
            SUM(douyin_applet_order_submit_num * sign) douyin_applet_order_submit_num,
            SUM(douyin_applet_order_finish_num * sign) douyin_applet_order_finish_num,
            SUM(taobao_component_copy_num * sign) taobao_component_copy_num,
            SUM(douyin_applet_jump_num * sign) douyin_applet_jump_num,
            SUM(official_add_customer_num * sign) official_add_customer_num,
            SUM(tao_bao_movie_applet_jump_num * sign) tao_bao_movie_applet_jump_num,
            SUM(tao_bao_movie_applet_order_num * sign) tao_bao_movie_applet_order_num,
            SUM(add_group_after_add_customer_service_num * sign) add_group_after_add_customer_service_num,
            SUM(add_group_after_follow_official_account_num * sign) add_group_after_follow_official_account_num,
            SUM(send_image_or_qr_code_num * sign) send_image_or_qr_code_num,
            SUM(mini_program_news_num * sign) mini_program_news_num,
            SUM(jump_to_super_red_envelope_num * sign) jump_to_super_red_envelope_num,
            SUM(success_send_welcome_msg_num * sign) success_send_welcome_msg_num,
            SUM(ad_upload_num * sign) ad_upload_num,

            SUM(taobao_page_view_num * sign) taobao_page_view_num,
            SUM(taobao_order_payment_num * sign) taobao_order_payment_num,
            SUM(taobao_product_click_num * sign) taobao_product_click_num,
            SUM(taobao_first_visit_venue_num * sign) taobao_first_visit_venue_num,
            SUM(taobao_red_envelope_receive_num * sign) taobao_red_envelope_receive_num,
            SUM(taobao_cancel_order_payment_num * sign) taobao_cancel_order_payment_num,
            SUM(taobao_high_commission_order_payment_num * sign) taobao_high_commission_order_payment_num,
            SUM(start_open_chat_num * sign) start_open_chat_num,
            SUM(third_open_chat_num * sign) third_open_chat_num,
            SUM(fifth_open_chat_num * sign) fifth_open_chat_num,
            SUM(tenth_open_chat_num * sign) tenth_open_chat_num,

            ifNull(sum(CASE when total_length_of_stay * sign >= 120 then 120 else total_length_of_stay * sign end), 0) as total_length_of_stay
        FROM landing_page_day_statistics_new
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime!=null">
                and statistic_date BETWEEN #{landingPageQuery.startTime} and #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
        </where>
        GROUP BY advertiser_account_group_id, landing_page_id, landing_page_channel_id, statistic_date,douyin_customer_source
        having sum(sign) > 0)
        GROUP BY landing_page_id
        ),
        landingPageData as (select lp.*, lpg.name as landing_page_group_name,
        case when empty(lp.content) then 0 when lp.content = '[]' then 1 else 2 end as landingPageContentEnum
        from landing_page lp
        left join landing_page_group lpg on lp.landing_page_group_id = lpg.id
        <where>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and lp.advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            <if test="landingPageQuery.landingPageGroupId != null and landingPageQuery.landingPageGroupId != -1">
                and lp.landing_page_group_id = #{landingPageQuery.landingPageGroupId}
            </if>
            <if test="landingPageQuery.deleteStatus != null">
                and lp.delete_status = #{landingPageQuery.deleteStatus}
            </if>
            <if test="landingPageQuery.name != null and landingPageQuery.name != ''">
                and lp.name like concat('%',#{landingPageQuery.name},'%')
            </if>

            <if test="landingPageQuery.landingPageType != null and landingPageQuery.landingPageType.name() != 'FLOW_SOURCE_JUMP_PAGE'">
                and lp.landing_page_type = #{landingPageQuery.landingPageType}
            </if>

            <if test="landingPageQuery.landingPageType != null and landingPageQuery.landingPageType.name() == 'FLOW_SOURCE_JUMP_PAGE'">
                and lp.flow_source_jump_page_status = 1
            </if>
        </where>
        )<!--,
        填单未读数
        formUnread as (
        select landing_page_id, sum(unread) as form_unread_count from customer
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime">
                and created_at between #{landingPageQuery.startTime} and #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            and customer_type = 0
        </where>
        group by landing_page_id
        ),
        &lt;!&ndash;订单未读数&ndash;&gt;
        orderUnread as (
        select landing_page_id, sum(unread) as order_unread_count from customer
        <where>
            <if test="landingPageQuery.startTime != null and landingPageQuery.endTime">
                and created_at between #{landingPageQuery.startTime} and #{landingPageQuery.endTime}
            </if>
            <if test="landingPageQuery.advertiserAccountGroupId != null">
                and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
            </if>
            and customer_type = 1
        </where>
        group by landing_page_id
        )-->
        SELECT lp.*,
        <!--   fu.form_unread_count                                                                           as form_unread_count,
           ou.order_unread_count                                                                          as order_unread_count,-->
           case when lps.landing_page_top == 0 then -1 else lps.landing_page_top end as landing_page_sort,
           case
           when lps.landing_page_group_top == 0 then -1
           else lps.landing_page_group_top end as landing_page_group_sort,
           ifNull(pv.total_length_of_stay,0) as total_length_of_stay,
           ifNull(pv.page_view_num,0) as page_view_num,
           ifNull(pv.income_line_num,0) as income_line_num,

           ifNull(pv.form_submit_num,0) as form_submit_num,
           (ifNull(pv.form_submit_num,0) + ifNull(pv.douyin_applet_native_form_submit_num,0) + ifNull(pv.clue_form_submit_num,0)  ) as form_submit_total_num,
           ifNull(pv.douyin_applet_native_form_submit_num,0) as douyin_applet_native_form_submit_num,
           ifNull(pv.clue_form_submit_num,0) as clue_form_submit_num,
           ifNull(pv.phone_number_recieved_num,0) as phone_number_recieved_num,
           ifNull(pv.active_message_authorization_num,0) as active_message_authorization_num,

           ifNull(pv.douyin_applet_order_submit_num,0) as douyin_applet_order_submit_num,
           ifNull(pv.douyin_applet_order_finish_num,0) as douyin_applet_order_finish_num,
           ifNull(pv.taobao_component_copy_num,0) as taobao_component_copy_num,
           ifNull(pv.douyin_applet_jump_num,0) as douyin_applet_jump_num,

           ifNull(pv.order_submit_num,0) as order_submit_num,
           ifNull(pv.payment_num,0) as payment_num,
           ifNull(pv.identify_qr_code_num,0) as identify_qr_code_num,

           ifNull(pv.ele_pv_num,0) as ele_pv_num,
           ifNull(pv.ele_qr_code_view_num,0) as ele_qr_code_view_num,
           ifNull(pv.ele_identify_wechat_qr_code_num,0) as ele_identify_wechat_qr_code_num,
           ifNull(pv.ele_add_wechat_success_num,0) as ele_add_wechat_success_num,

           ifNull(pv.whatsapp_jump_num,0) as  whatsapp_jump_num,
           ifNull(pv.whatsapp_add_friend_success_num,0) as  whatsapp_add_friend_success_num,
           ifNull(pv.whatsapp_user_open_mouth_num,0) as  whatsapp_user_open_mouth_num,

           ifNull(pv.add_work_wechat_num,0) as add_work_wechat_num,
           ifNull(pv.follow_official_account_num,0) as follow_official_account_num,
           ifNull(pv.online_shop_buy_goods_success_num,0) as online_shop_buy_goods_success_num,
           ifNull(pv.official_identify_qr_code_num,0) as official_identify_qr_code_num,
           ifNull(pv.official_add_customer_num,0) as official_add_customer_num,
           ifNull(pv.identify_group_qr_code_num,0) as identify_group_qr_code_num,
           ifNull(pv.add_work_wechat_group_num,0) as add_work_wechat_group_num,
           ifNull(pv.wechat_official_article_page_view_num,0) as wechat_official_article_page_view_num,
           ifNull(pv.add_group_after_add_customer_service_num,0) as add_group_after_add_customer_service_num,
           ifNull(pv.add_group_after_follow_official_account_num,0) as add_group_after_follow_official_account_num,
           ifNull(pv.send_image_or_qr_code_num,0) as send_image_or_qr_code_num,
           ifNull(pv.mini_program_news_num,0) as mini_program_news_num,
           ifNull(pv.jump_to_super_red_envelope_num,0) as jump_to_super_red_envelope_num,
           ifNull(pv.success_send_welcome_msg_num,0) as success_send_welcome_msg_num,
            ifNull(pv.ad_upload_num,0) as ad_upload_num,

           ifNull(pv.taobao_page_view_num,0) as taobao_page_view_num,
           ifNull(pv.taobao_order_payment_num,0) as taobao_order_payment_num,
           ifNull(pv.taobao_product_click_num,0) as taobao_product_click_num,
           ifNull(pv.taobao_first_visit_venue_num,0) as taobao_first_visit_venue_num,
           ifNull(pv.taobao_red_envelope_receive_num,0) as taobao_red_envelope_receive_num,
           ifNull(pv.taobao_cancel_order_payment_num,0) as taobao_cancel_order_payment_num,
           ifNull(pv.taobao_high_commission_order_payment_num,0) as taobao_high_commission_order_payment_num,
           ifNull(pv.start_open_chat_num,0) as start_open_chat_num,
           ifNull(pv.third_open_chat_num,0) as third_open_chat_num,
           ifNull(pv.fifth_open_chat_num,0) as fifth_open_chat_num,
           ifNull(pv.tenth_open_chat_num,0) as tenth_open_chat_num,

          case
              when page_view_num = 0 then 0
              else ifNull(start_open_chat_num,0) / page_view_num * 100 end  as start_open_chat_rate,

          case
            when page_view_num = 0 then 0
            else ifNull(third_open_chat_num,0) / page_view_num * 100 end  as third_open_chat_rate,

          case
            when page_view_num = 0 then 0
            else ifNull(fifth_open_chat_num,0) / page_view_num * 100 end  as fifth_open_chat_rate,

          case
            when page_view_num = 0 then 0
            else ifNull(tenth_open_chat_num,0) / page_view_num * 100 end  as tenth_open_chat_rate,

          case
            when page_view_num = 0 then 0
            else ifNull(ad_upload_num,0) / page_view_num * 100 end  as ad_upload_rate,

           case
            when page_view_num = 0 then 0
            else ifNull(jump_to_super_red_envelope_num,0) / page_view_num * 100 end  as jump_to_super_red_envelope_rate,

           case
             when page_view_num = 0 then 0
             else ifNull(success_send_welcome_msg_num,0) / page_view_num * 100 end  as success_send_welcome_msg_rate,

            case
              when page_view_num = 0 then 0
              else ifNull(send_image_or_qr_code_num,0) / page_view_num * 100 end  as send_image_or_qr_code_rate,

            case
              when page_view_num = 0 then 0
              else ifNull(mini_program_news_num,0) / page_view_num * 100 end  as mini_program_news_rate,

            case
              when page_view_num = 0 then 0
              else ifNull(income_line_num,0) / page_view_num * 100 end  as income_line_rate,

            case
             when page_view_num = 0 then 0
             else ifNull(add_group_after_add_customer_service_num,0) / page_view_num * 100 end  as add_group_after_add_customer_service_rate,

            case
             when page_view_num = 0 then 0
             else ifNull(add_group_after_follow_official_account_num,0) / page_view_num * 100 end  as add_group_after_follow_official_account_rate,

            case
                when page_view_num = 0 then 0
                else ifNull(whatsapp_jump_num,0) / page_view_num * 100 end                  as whatsapp_jump_rate,
            case
                when page_view_num = 0 then 0
                else ifNull(whatsapp_add_friend_success_num,0) / page_view_num * 100 end                  as whatsapp_add_friend_success_rate,
            case
                when page_view_num = 0 then 0
                else ifNull(whatsapp_user_open_mouth_num,0) / page_view_num * 100 end                  as whatsapp_user_open_mouth_rate,

            pv.number_of_orders_completed_for_coupon_num as number_of_orders_completed_for_coupon_num,
           ifNull(pv.tao_bao_movie_applet_jump_num,0) as tao_bao_movie_applet_jump_num,
           ifNull(pv.tao_bao_movie_applet_order_num,0) as tao_bao_movie_applet_order_num,

           case
            when page_view_num = 0 then 0
            else tao_bao_movie_applet_jump_num / page_view_num * 100 end as tao_bao_movie_applet_jump_rate,

           case
            when page_view_num = 0 then 0
            else tao_bao_movie_applet_order_num / page_view_num * 100 end as tao_bao_movie_applet_order_rate,

           case
           when page_view_num = 0 then 0
           else official_identify_qr_code_num / page_view_num * 100 end as official_identify_qr_code_rate,

           case
           when page_view_num = 0 then 0
           else ifNull(form_submit_num,0) / page_view_num * 100 end as form_submit_rate,

           case
            when page_view_num = 0 then 0
            else ifNull(douyin_applet_native_form_submit_num,0) / page_view_num * 100 end as douyin_applet_native_form_submit_rate,
           case
            when page_view_num = 0 then 0
            else ifNull(clue_form_submit_num,0) / page_view_num * 100 end as clue_form_submit_rate,

           case
            when page_view_num = 0 then 0
            else ifNull(phone_number_recieved_num,0) / page_view_num * 100 end as phone_number_recieved_rate,

           case
            when page_view_num = 0 then 0
            else ifNull(taobao_component_copy_num,0) / page_view_num * 100 end as taobao_component_copy_rate,

           case
            when page_view_num = 0 then 0
            else ifNull(douyin_applet_jump_num,0) / page_view_num * 100 end as douyin_applet_jump_rate,

           case
            when page_view_num = 0 then 0
            else ifNull(douyin_applet_order_submit_num,0) / page_view_num * 100 end as douyin_applet_order_submit_rate,

           case
            when douyin_applet_order_submit_num = 0 then 0
            else ifNull(douyin_applet_order_finish_num,0) / douyin_applet_order_submit_num * 100 end as douyin_applet_order_finish_rate,

           case
            when page_view_num = 0 then 0
            else ifNull(active_message_authorization_num,0) / page_view_num * 100 end as active_message_authorization_rate,
           case
            when page_view_num = 0 then 0
            else (ifNull(form_submit_num,0) + ifNull(douyin_applet_native_form_submit_num,0) + ifNull(clue_form_submit_num,0)) / page_view_num * 100 end as form_submit_total_rate,

           case
           when page_view_num = 0 then 0
           else ifNull(order_submit_num,0) / page_view_num * 100 end as order_submit_rate,
           case when order_submit_num = 0 then 0 else ifNull(payment_num,0) / order_submit_num * 100 end as payment_rate,
           case when page_view_num = 0 then 0 else ifNull(payment_num,0) / page_view_num * 100 end as
           comprehensive_payment_rate,
           case
           when order_submit_num = 0 then 0
           else ifNull(number_of_orders_completed_for_coupon_num,0) / order_submit_num * 100 end as
           number_of_orders_completed_for_coupon_rate,
           case
           when page_view_num = 0 then 0
           else ifNull(identify_qr_code_num,0) / page_view_num * 100 end as identify_qr_code_rate,
           case
           when page_view_num = 0 then 0
           else ifNull(add_work_wechat_num,0) / page_view_num * 100 end as add_work_wechat_rate,
           case
           when page_view_num = 0 then 0
           else ifNull(follow_official_account_num,0) / page_view_num * 100 end as follow_official_account_rate,
           case
           when page_view_num = 0 then 0
           else ifNull(online_shop_buy_goods_success_num,0) / page_view_num * 100 end as
           online_shop_buy_goods_success_rate,
           case
           when page_view_num = 0 then 0
           else ifNull(identify_group_qr_code_num,0) / page_view_num * 100 end as identify_group_qr_code_rate,
           case
           when page_view_num = 0 then 0
           else ifNull(add_work_wechat_group_num,0) / page_view_num * 100 end as add_work_wechat_group_rate,
           case
            when page_view_num = 0 then 0
            else ifNull(official_add_customer_num,0) / page_view_num * 100 end as official_add_customer_rate,

           case when page_view_num = 0 then 0 else ifNull(taobao_page_view_num,0) / page_view_num * 100 end as  taobao_page_view_rate,
           case when page_view_num = 0 then 0 else ifNull(taobao_order_payment_num,0) / page_view_num * 100 end as  taobao_order_payment_rate,
           case when page_view_num = 0 then 0 else ifNull(taobao_product_click_num,0) / page_view_num * 100 end as  taobao_product_click_rate,
           case when page_view_num = 0 then 0 else ifNull(taobao_first_visit_venue_num,0) / page_view_num * 100 end as  taobao_first_visit_venue_rate,
           case when page_view_num = 0 then 0 else ifNull(taobao_red_envelope_receive_num,0) / page_view_num * 100 end as  taobao_red_envelope_receive_rate,
           case when page_view_num = 0 then 0 else ifNull(taobao_cancel_order_payment_num,0) / page_view_num * 100 end as  taobao_cancel_order_payment_rate,
           case when page_view_num = 0 then 0 else ifNull(taobao_high_commission_order_payment_num,0) / page_view_num * 100 end as  taobao_high_commission_order_payment_rate,

           case
           when page_view_num = 0 then 0
           else ifNull(total_length_of_stay,0) / page_view_num end as average_length_of_stay
           FROM landingPageData lp
           LEFT JOIN basePvData pv ON lp.id = pv.landing_page_id
           LEFT JOIN landing_page_sort lps ON lps.landing_page_id = lp.id
           <if test="landingPageQuery.advertiserAccountGroupId != null">
               and lps.advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
           </if>
           <if test="landingPageQuery.userId != null">
               and lps.user_id = #{landingPageQuery.userId}
           </if>
        <!-- LEFT JOIN formUnread fu ON fu.landing_page_id = lp.id
          LEFT JOIN orderUnread ou ON ou.landing_page_id = lp.id -->
          <where>
              <if test="landingPageQuery.advertiserAccountGroupId != null">
                  and lp.advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
              </if>
              <if test="landingPageQuery.landingPageGroupId != null and landingPageQuery.landingPageGroupId != -1">
                  and lp.landing_page_group_id = #{landingPageQuery.landingPageGroupId}
              </if>
              <if test="landingPageQuery.deleteStatus != null">
                  and lp.delete_status = #{landingPageQuery.deleteStatus}
              </if>
              <if test="landingPageQuery.name != null and landingPageQuery.name != ''">
                  and lp.name like concat('%',#{landingPageQuery.name},'%')
              </if>

              <if test="landingPageQuery.landingPageType != null ">
                  and lp.landing_page_type = #{landingPageQuery.landingPageType}
              </if>
          </where>
          order by
          <if test="landingPageQuery.landingPageGroupId == -1">
              landing_page_sort desc
          </if>
          <if test="landingPageQuery.landingPageGroupId != -1">
              landing_page_group_sort desc
          </if>
          <if test="landingPageQuery.sortField!=null and landingPageQuery.sortField!='updated_at' and landingPageQuery.sortField!='created_at'">
              ,${landingPageQuery.sortField} ${landingPageQuery.order}
              <if test="landingPageQuery.order !=null  and landingPageQuery.sortField == 'number_of_orders_completed_for_coupon_num' and landingPageQuery.order == 'desc' ">
                  nulls last
              </if>

              <if test="landingPageQuery.order !=null and landingPageQuery.sortField == 'number_of_orders_completed_for_coupon_num' and landingPageQuery.order == 'asc' ">
                  nulls first
              </if>
          </if>
          <if test="landingPageQuery.sortField!=null and landingPageQuery.sortField =='created_at'">
              ,lp.created_at ${landingPageQuery.order}
          </if>
          <if test="landingPageQuery.sortField!=null and landingPageQuery.sortField =='updated_at'">
              ,lp.updated_at ${landingPageQuery.order}
          </if>

            <if test="landingPageQuery.sortField!=null and landingPageQuery.sortField =='phoneNumberRecievedNum'">
                ,pv.phone_number_recieved_num ${landingPageQuery.order}
            </if>

            <if test="landingPageQuery.sortField!=null and landingPageQuery.sortField =='phoneNumberRecievedRate'">
                , phone_number_recieved_rate ${landingPageQuery.order}
            </if>
      </select>


      <select id="pageChannels" resultMap="channelsResultMap">
          WITH basePvData as (
          select
          pv.landing_page_channel_id,
          SUM(page_view_num) AS page_view_num,
          SUM ( income_line_num )  AS  income_line_num,
          SUM(total_length_of_stay) AS total_length_of_stay,
          SUM(form_submit_num) AS form_submit_num,
          SUM(douyin_applet_native_form_submit_num)  AS    douyin_applet_native_form_submit_num,
          SUM(clue_form_submit_num)                  AS    clue_form_submit_num,
          SUM(phone_number_recieved_num)             AS    phone_number_recieved_num,
          SUM(active_message_authorization_num)      AS    active_message_authorization_num,

          SUM(order_submit_num) AS order_submit_num,
          SUM(official_identify_qr_code_num) AS official_identify_qr_code_num,
          SUM(payment_num) AS payment_num,
          SUM(identify_qr_code_num) AS identify_qr_code_num,

          SUM(ele_pv_num) as ele_pv_num,
          SUM(ele_qr_code_view_num) as ele_qr_code_view_num,
          SUM(ele_identify_wechat_qr_code_num) as ele_identify_wechat_qr_code_num,
          SUM(ele_add_wechat_success_num) as ele_add_wechat_success_num,


          SUM(add_work_wechat_num) AS add_work_wechat_num,
          SUM(follow_official_account_num) AS follow_official_account_num,
          SUM(online_shop_buy_goods_success_num) AS online_shop_buy_goods_success_num,
          SUM(identify_group_qr_code_num) AS identify_group_qr_code_num,
          SUM(add_work_wechat_group_num) AS add_work_wechat_group_num,
          SUM(wechat_official_article_page_view_num) AS wechat_official_article_page_view_num,
          SUM(number_of_orders_completed_for_coupon_num) as number_of_orders_completed_for_coupon_num,
          SUM(douyin_applet_order_submit_num) as douyin_applet_order_submit_num,
          SUM(douyin_applet_order_finish_num) as douyin_applet_order_finish_num,
          SUM(official_add_customer_num) as official_add_customer_num,
          SUM(taobao_component_copy_num) AS taobao_component_copy_num,
          SUM(douyin_applet_jump_num) AS douyin_applet_jump_num,
          SUM(tao_bao_movie_applet_order_num) AS tao_bao_movie_applet_order_num,
          SUM(tao_bao_movie_applet_jump_num) AS tao_bao_movie_applet_jump_num,
          SUM(add_group_after_add_customer_service_num) AS add_group_after_add_customer_service_num,
          SUM(add_group_after_follow_official_account_num) AS add_group_after_follow_official_account_num,

          SUM(taobao_page_view_num) taobao_page_view_num,
          SUM(taobao_order_payment_num) taobao_order_payment_num,
          SUM(taobao_product_click_num) taobao_product_click_num,
          SUM(taobao_first_visit_venue_num) taobao_first_visit_venue_num,
          SUM(taobao_red_envelope_receive_num) taobao_red_envelope_receive_num,
          SUM(taobao_cancel_order_payment_num) taobao_cancel_order_payment_num,
          SUM(taobao_high_commission_order_payment_num) taobao_high_commission_order_payment_num,

          sum(whatsapp_jump_num) as  whatsapp_jump_num,
          sum(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
          sum(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,
          sum(send_image_or_qr_code_num) as  send_image_or_qr_code_num,
          sum(mini_program_news_num) as  mini_program_news_num,
          sum(jump_to_super_red_envelope_num) as  jump_to_super_red_envelope_num,
          sum(success_send_welcome_msg_num) as  success_send_welcome_msg_num,
          sum(ad_upload_num) as  ad_upload_num,
          sum(start_open_chat_num) as  start_open_chat_num,
          sum(third_open_chat_num) as  third_open_chat_num,
          sum(fifth_open_chat_num) as  fifth_open_chat_num,
          sum(tenth_open_chat_num) as  tenth_open_chat_num

          from
          (select
          pv.landing_page_channel_id,
          pv.advertiser_account_group_id,
          pv.landing_page_id,
          SUM(page_view_num * sign) AS page_view_num,
          SUM(income_line_num * sign)  AS  income_line_num,
          SUM(total_length_of_stay * sign) AS total_length_of_stay,
          SUM(form_submit_num * sign) AS form_submit_num,
          SUM(douyin_applet_native_form_submit_num * sign)  AS    douyin_applet_native_form_submit_num,
          SUM(clue_form_submit_num * sign)            AS          clue_form_submit_num,
          SUM(phone_number_recieved_num * sign)       AS          phone_number_recieved_num,
          SUM(active_message_authorization_num * sign) AS          active_message_authorization_num,
          SUM(order_submit_num * sign) AS order_submit_num,
          SUM(official_identify_qr_code_num * sign) AS official_identify_qr_code_num,
          SUM(payment_num * sign) AS payment_num,
          SUM(identify_qr_code_num * sign) AS identify_qr_code_num,
          SUM(add_work_wechat_num * sign) AS add_work_wechat_num,
          SUM(follow_official_account_num * sign) AS follow_official_account_num,
          SUM(online_shop_buy_goods_success_num * sign) AS online_shop_buy_goods_success_num,
          SUM(identify_group_qr_code_num * sign) AS identify_group_qr_code_num,

          SUM(ele_pv_num * sign) as ele_pv_num,
          SUM(ele_qr_code_view_num * sign) as ele_qr_code_view_num,
          SUM(ele_identify_wechat_qr_code_num * sign) as ele_identify_wechat_qr_code_num,
          SUM(ele_add_wechat_success_num * sign) as ele_add_wechat_success_num,

          SUM(taobao_page_view_num * sign) taobao_page_view_num,
          SUM(taobao_order_payment_num * sign) taobao_order_payment_num,
          SUM(taobao_product_click_num * sign) taobao_product_click_num,
          SUM(taobao_first_visit_venue_num * sign) taobao_first_visit_venue_num,
          SUM(taobao_red_envelope_receive_num * sign) taobao_red_envelope_receive_num,
          SUM(taobao_cancel_order_payment_num * sign) taobao_cancel_order_payment_num,
          SUM(taobao_high_commission_order_payment_num * sign) taobao_high_commission_order_payment_num,

          <!--1.265.0 whatsapp -->
          sum(whatsapp_jump_num) as  whatsapp_jump_num,
          sum(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
          sum(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,

          SUM(add_work_wechat_group_num * sign) AS add_work_wechat_group_num,
          SUM(wechat_official_article_page_view_num * sign) AS wechat_official_article_page_view_num,
          SUM(number_of_orders_completed_for_coupon_num * sign) as number_of_orders_completed_for_coupon_num,
          SUM(douyin_applet_order_submit_num * sign) as douyin_applet_order_submit_num,
          SUM(douyin_applet_order_finish_num * sign) as douyin_applet_order_finish_num,
          SUM(taobao_component_copy_num * sign) as taobao_component_copy_num,
          SUM(douyin_applet_jump_num * sign) as douyin_applet_jump_num,
          SUM(official_add_customer_num * sign) as official_add_customer_num,
          SUM(tao_bao_movie_applet_order_num * sign) as tao_bao_movie_applet_order_num,
          SUM(tao_bao_movie_applet_jump_num * sign) as tao_bao_movie_applet_jump_num,
          SUM(add_group_after_add_customer_service_num * sign) as add_group_after_add_customer_service_num,
          SUM(add_group_after_follow_official_account_num * sign) as add_group_after_follow_official_account_num,
          SUM(send_image_or_qr_code_num * sign) as send_image_or_qr_code_num,
          SUM(mini_program_news_num * sign) as mini_program_news_num,
          sum(jump_to_super_red_envelope_num * sign) as  jump_to_super_red_envelope_num,
          sum(success_send_welcome_msg_num * sign) as  success_send_welcome_msg_num,
          sum(ad_upload_num * sign) as  ad_upload_num,
          SUM(start_open_chat_num * sign) start_open_chat_num,
          SUM(third_open_chat_num * sign) third_open_chat_num,
          SUM(fifth_open_chat_num * sign) fifth_open_chat_num,
          SUM(tenth_open_chat_num * sign) tenth_open_chat_num
          from landing_page_day_statistics_new pv
          <where>
              <if test="landingPageQuery.startTime != null and landingPageQuery.endTime!=null">
                  and pv.statistic_date between #{landingPageQuery.startTime} and #{landingPageQuery.endTime}
              </if>
              <if test="landingPageQuery.advertiserAccountGroupId != null">
                  and pv.advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
              </if>
              <if test="landingPageQuery.landingPageId != null">
                  and pv.landing_page_id = #{landingPageQuery.landingPageId}
              </if>
          </where>
          group by advertiser_account_group_id, landing_page_id, landing_page_channel_id, statistic_date,douyin_customer_source
          HAVING sum(sign) > 0) pv group by pv.landing_page_channel_id
          ),
               landingPageChannelData as (
                   select * from landing_page_channels
                  <where>
                      <if test="landingPageQuery.landingPageId != null">
                          and landing_page_id = #{landingPageQuery.landingPageId}
                      </if>
                      <if test="landingPageQuery.deleteStatus != null">
                          and delete_status = #{landingPageQuery.deleteStatus}
                      </if>
                  </where>
               )
          select
              lp.*,
              pv.landing_page_channel_id,
              ifNull(pv.page_view_num, 0) as page_view_num,
              ifNull(pv.income_line_num, 0) as income_line_num,
              ifNull(pv.total_length_of_stay, 0) as total_length_of_stay,
              ifNull(pv.form_submit_num, 0) as form_submit_num,
              ifNull(pv.douyin_applet_native_form_submit_num, 0) as douyin_applet_native_form_submit_num,
              ifNull(pv.clue_form_submit_num, 0) as clue_form_submit_num,
              ifNull(pv.phone_number_recieved_num, 0) as phone_number_recieved_num,
              ifNull(pv.active_message_authorization_num, 0) as active_message_authorization_num,
              (ifNull ( pv.form_submit_num, 0 ) + ifNull ( pv.clue_form_submit_num, 0 ) + ifNull ( pv.douyin_applet_native_form_submit_num, 0 )) as form_submit_total_num,

              ifNull(pv.order_submit_num, 0) as order_submit_num,
              ifNull(pv.douyin_applet_order_submit_num, 0) as douyin_applet_order_submit_num,
              ifNull(pv.douyin_applet_order_finish_num, 0) as douyin_applet_order_finish_num,
              ifNull(pv.taobao_component_copy_num, 0) as taobao_component_copy_num,
              ifNull(pv.douyin_applet_jump_num, 0) as douyin_applet_jump_num,
              ifNull(pv.payment_num, 0) as payment_num,
              ifNull(pv.identify_qr_code_num, 0) as identify_qr_code_num,
              ifNull(pv.add_work_wechat_num, 0) as add_work_wechat_num,
              ifNull(pv.follow_official_account_num, 0) as follow_official_account_num,
              ifNull(pv.online_shop_buy_goods_success_num, 0) as online_shop_buy_goods_success_num,
              ifNull(pv.official_identify_qr_code_num, 0) as official_identify_qr_code_num,
              ifNull(pv.add_work_wechat_group_num, 0) as add_work_wechat_group_num,
              ifNull(pv.identify_group_qr_code_num, 0) as identify_group_qr_code_num,

              ifNull(pv.ele_pv_num, 0) as ele_pv_num,
              ifNull(pv.ele_qr_code_view_num, 0) as ele_qr_code_view_num,
              ifNull(pv.ele_identify_wechat_qr_code_num, 0) as ele_identify_wechat_qr_code_num,
              ifNull(pv.ele_add_wechat_success_num, 0) as ele_add_wechat_success_num,

              ifNull(pv.taobao_page_view_num, 0) taobao_page_view_num,
              ifNull(pv.taobao_order_payment_num, 0) taobao_order_payment_num,
              ifNull(pv.taobao_product_click_num, 0) taobao_product_click_num,
              ifNull(pv.taobao_first_visit_venue_num, 0) taobao_first_visit_venue_num,
              ifNull(pv.taobao_red_envelope_receive_num, 0) taobao_red_envelope_receive_num,
              ifNull(pv.taobao_cancel_order_payment_num, 0) taobao_cancel_order_payment_num,
              ifNull(pv.taobao_high_commission_order_payment_num, 0) taobao_high_commission_order_payment_num,

             <!--1.265.0 whatsapp -->
              ifNull(pv.whatsapp_jump_num, 0) as whatsapp_jump_num,
              ifNull(pv.whatsapp_add_friend_success_num, 0) as whatsapp_add_friend_success_num,
              ifNull(pv.whatsapp_user_open_mouth_num, 0) as whatsapp_user_open_mouth_num,
              ifNull(case when page_view_num = 0 then 0 else whatsapp_jump_num / page_view_num * 100 end, 0) as  whatsapp_jump_rate,
              ifNull(case when page_view_num = 0 then 0 else whatsapp_add_friend_success_num / page_view_num * 100 end, 0) as whatsapp_add_friend_success_rate,
              ifNull(case when page_view_num = 0 then 0 else whatsapp_user_open_mouth_num / page_view_num * 100 end, 0) as  whatsapp_user_open_mouth_rate,

              ifNull(pv.wechat_official_article_page_view_num,0) as wechat_official_article_page_view_num,
              ifNull(pv.number_of_orders_completed_for_coupon_num,0) as number_of_orders_completed_for_coupon_num,
              ifNull(pv.official_add_customer_num,0) as official_add_customer_num,
              ifNull(pv.tao_bao_movie_applet_order_num,0) as tao_bao_movie_applet_order_num,
              ifNull(pv.tao_bao_movie_applet_jump_num,0) as tao_bao_movie_applet_jump_num,

              ifNull(pv.send_image_or_qr_code_num,0) as send_image_or_qr_code_num,
              ifNull(pv.mini_program_news_num,0) as mini_program_news_num,
              ifNull(pv.jump_to_super_red_envelope_num,0) as jump_to_super_red_envelope_num,
              ifNull(pv.success_send_welcome_msg_num,0) as success_send_welcome_msg_num,
              ifNull(pv.ad_upload_num,0) as ad_upload_num,
              ifNull(pv.start_open_chat_num,0) as start_open_chat_num,
              ifNull(pv.third_open_chat_num,0) as third_open_chat_num,
              ifNull(pv.fifth_open_chat_num,0) as fifth_open_chat_num,
              ifNull(pv.tenth_open_chat_num,0) as tenth_open_chat_num,

              ifNull(pv.add_group_after_add_customer_service_num,0) as add_group_after_add_customer_service_num,
              ifNull(pv.add_group_after_follow_official_account_num,0) as add_group_after_follow_official_account_num,


              ifNull(case when page_view_num = 0 then 0 else start_open_chat_num / page_view_num * 100 end, 0) as  start_open_chat_rate,
              ifNull(case when page_view_num = 0 then 0 else third_open_chat_num / page_view_num * 100 end, 0) as  third_open_chat_rate,
              ifNull(case when page_view_num = 0 then 0 else fifth_open_chat_num / page_view_num * 100 end, 0) as  fifth_open_chat_rate,
              ifNull(case when page_view_num = 0 then 0 else tenth_open_chat_num / page_view_num * 100 end, 0) as  tenth_open_chat_rate,

              ifNull(case when page_view_num = 0 then 0 else add_group_after_add_customer_service_num / page_view_num * 100 end, 0) as  add_group_after_add_customer_service_rate,
              ifNull(case when page_view_num = 0 then 0 else add_group_after_follow_official_account_num / page_view_num * 100 end, 0) as  add_group_after_follow_official_account_rate,

              ifNull(case when page_view_num = 0 then 0 else official_identify_qr_code_num / page_view_num * 100 end, 0) as  official_identify_qr_code_rate,

              ifNull(case when page_view_num = 0 then 0 else total_length_of_stay / page_view_num end, 0) as average_length_of_stay,

              ifNull(case when page_view_num = 0 then 0 else form_submit_num / page_view_num * 100 end, 0) as  form_submit_rate,

              ifNull(case when page_view_num = 0 then 0 else douyin_applet_order_submit_num / page_view_num * 100 end, 0) as  douyin_applet_order_submit_rate,

              ifNull(case when douyin_applet_order_submit_num = 0 then 0 else douyin_applet_order_finish_num / douyin_applet_order_submit_num * 100 end, 0) as   douyin_applet_order_finish_rate,

              ifNull(case when taobao_component_copy_num = 0 then 0 else taobao_component_copy_num / page_view_num * 100 end, 0) astaobao_component_copy_rate,

              ifNull(case when douyin_applet_jump_num = 0 then 0 else douyin_applet_jump_num / page_view_num * 100 end, 0) as douyin_applet_jump_rate,

              ifNull(case when page_view_num = 0 then 0 else douyin_applet_native_form_submit_num / page_view_num * 100 end, 0) as  douyin_applet_native_form_submit_rate,

              ifNull(case when page_view_num = 0 then 0 else clue_form_submit_num / page_view_num * 100 end, 0) as  clue_form_submit_rate,

              ifNull(case when page_view_num = 0 then 0 else form_submit_total_num / page_view_num * 100 end, 0) as  form_submit_total_rate,

              ifNull(case when page_view_num = 0 then 0 else phone_number_recieved_num / page_view_num * 100 end, 0) as  phone_number_recieved_rate,

              ifNull(case when page_view_num = 0 then 0 else active_message_authorization_num / page_view_num * 100 end, 0) as  active_message_authorization_rate,

              ifNull(case when page_view_num = 0 then 0 else order_submit_num / page_view_num * 100 end, 0) as  order_submit_rate,

              ifNull(case when order_submit_num = 0 then 0 else payment_num / order_submit_num * 100 end, 0) as  payment_rate,

              ifNull(case when page_view_num = 0 then 0 else payment_num / page_view_num * 100 end, 0) as comprehensive_payment_rate,

              ifNull(case when order_submit_num = 0 then 0 else number_of_orders_completed_for_coupon_num / order_submit_num * 100 end, 0) as number_of_orders_completed_for_coupon_rate,

              ifNull(case when page_view_num = 0 then 0 else identify_qr_code_num / page_view_num * 100 end, 0) as identify_qr_code_rate,

              ifNull(case when page_view_num = 0 then 0 else add_work_wechat_num / page_view_num  * 100 end, 0) as  add_work_wechat_rate,

              ifNull(case when page_view_num = 0 then 0 else follow_official_account_num / page_view_num * 100  end, 0) as follow_official_account_rate,

              ifNull(case when page_view_num = 0 then 0 else online_shop_buy_goods_success_num / page_view_num * 100 end, 0) as online_shop_buy_goods_success_rate,

              ifNull(case when page_view_num = 0 then 0 else identify_group_qr_code_num / page_view_num *  100 end, 0) as identify_group_qr_code_rate,

              ifNull(case when page_view_num = 0 then 0 else add_work_wechat_group_num / page_view_num *  100 end, 0) as add_work_wechat_group_rate,

              ifNull(case when page_view_num = 0 then 0 else official_add_customer_num / page_view_num * 100 end, 0) as official_add_customer_rate,

              ifNull(case when page_view_num = 0 then 0 else tao_bao_movie_applet_order_num / page_view_num * 100 end, 0) as tao_bao_movie_applet_order_rate,

              ifNull(case when page_view_num = 0 then 0 else tao_bao_movie_applet_jump_num / page_view_num * 100 end, 0) as tao_bao_movie_applet_jump_rate,

              ifNull(case when page_view_num = 0 then 0 else taobao_page_view_num / page_view_num * 100 end, 0) taobao_page_view_rate,
              ifNull(case when page_view_num = 0 then 0 else taobao_order_payment_num / page_view_num * 100 end, 0) taobao_order_payment_rate,
              ifNull(case when page_view_num = 0 then 0 else taobao_product_click_num / page_view_num * 100 end, 0) taobao_product_click_rate,
              ifNull(case when page_view_num = 0 then 0 else taobao_first_visit_venue_num / page_view_num * 100 end, 0) taobao_first_visit_venue_rate,
              ifNull(case when page_view_num = 0 then 0 else taobao_red_envelope_receive_num / page_view_num * 100 end, 0) taobao_red_envelope_receive_rate,
              ifNull(case when page_view_num = 0 then 0 else taobao_cancel_order_payment_num / page_view_num * 100 end, 0) taobao_cancel_order_payment_rate,
              ifNull(case when page_view_num = 0 then 0 else taobao_high_commission_order_payment_num / page_view_num * 100 end, 0) taobao_high_commission_order_payment_rate,

             ifNull(case when page_view_num = 0 then 0 else send_image_or_qr_code_num / page_view_num * 100 end, 0) as  send_image_or_qr_code_rate,

             ifNull(case when page_view_num = 0 then 0 else mini_program_news_num / page_view_num * 100 end, 0) as  mini_program_news_rate,

             ifNull(case when page_view_num = 0 then 0 else income_line_num / page_view_num * 100 end, 0) as  income_line_rate,

             ifNull(case when page_view_num = 0 then 0 else jump_to_super_red_envelope_num / page_view_num * 100 end, 0) as  jump_to_super_red_envelope_rate,

             ifNull(case when page_view_num = 0 then 0 else success_send_welcome_msg_num / page_view_num * 100 end, 0) as  success_send_welcome_msg_rate,

             ifNull(case when page_view_num = 0 then 0 else ad_upload_num / page_view_num * 100 end, 0) as  ad_upload_rate

          from landingPageChannelData lp   left join basePvData pv on lp.id = pv.landing_page_channel_id
          order by
          ${landingPageQuery.sortField} ${landingPageQuery.order}
      </select>



      <select id="pagelistTotal" resultType="ai.yiye.agent.domain.landingpage.LandingPageListVo">
          WITH basePvData AS (
          SELECT
              landing_page_id,
              SUM(page_view_num)                             page_view_num,
              SUM(total_length_of_stay)                      total_length_of_stay,
              SUM(form_submit_num)                           form_submit_num,
              SUM(douyin_applet_native_form_submit_num)      douyin_applet_native_form_submit_num,
              SUM(clue_form_submit_num)                      clue_form_submit_num,
              SUM(active_message_authorization_num)          active_message_authorization_num,
              SUM(phone_number_recieved_num)                 phone_number_recieved_num,
              SUM(order_submit_num)                          order_submit_num,
              SUM(payment_num)                               payment_num,
              SUM(identify_qr_code_num)                      identify_qr_code_num,
              SUM(add_work_wechat_num)                       add_work_wechat_num,
              SUM(follow_official_account_num)               follow_official_account_num,
              SUM(online_shop_buy_goods_success_num)         online_shop_buy_goods_success_num,
              SUM(official_identify_qr_code_num)             official_identify_qr_code_num,

              SUM(ele_pv_num) ele_pv_num,
              SUM(ele_qr_code_view_num) ele_qr_code_view_num,
              SUM(ele_identify_wechat_qr_code_num) ele_identify_wechat_qr_code_num,
              SUM(ele_add_wechat_success_num) ele_add_wechat_success_num,
              sum(whatsapp_jump_num) as  whatsapp_jump_num,
              sum(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
              sum(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,

              SUM(taobao_page_view_num) as  taobao_page_view_num,
              SUM(taobao_order_payment_num) as  taobao_order_payment_num,
              SUM(taobao_product_click_num) as  taobao_product_click_num,
              SUM(taobao_first_visit_venue_num) as  taobao_first_visit_venue_num,
              SUM(taobao_red_envelope_receive_num) as  taobao_red_envelope_receive_num,
              SUM(taobao_cancel_order_payment_num) as  taobao_cancel_order_payment_num,
              SUM(taobao_high_commission_order_payment_num) as  taobao_high_commission_order_payment_num,

              SUM(identify_group_qr_code_num)                identify_group_qr_code_num,
              SUM(add_work_wechat_group_num)                 add_work_wechat_group_num,
              SUM(wechat_official_article_page_view_num)     wechat_official_article_page_view_num,
              SUM (income_line_num )                         income_line_num,
              SUM(number_of_orders_completed_for_coupon_num) number_of_orders_completed_for_coupon_num,
              SUM(douyin_applet_order_submit_num) douyin_applet_order_submit_num,
              SUM(douyin_applet_order_finish_num) douyin_applet_order_finish_num,
              SUM(taobao_component_copy_num) taobao_component_copy_num,
              SUM(douyin_applet_jump_num) douyin_applet_jump_num,
              SUM(official_add_customer_num) official_add_customer_num,
              SUM(tao_bao_movie_applet_jump_num) tao_bao_movie_applet_jump_num,
              SUM(tao_bao_movie_applet_order_num) tao_bao_movie_applet_order_num,
              SUM(add_group_after_add_customer_service_num) add_group_after_add_customer_service_num,
              SUM(add_group_after_follow_official_account_num) add_group_after_follow_official_account_num,
              SUM(send_image_or_qr_code_num) send_image_or_qr_code_num,
              SUM(mini_program_news_num) mini_program_news_num,
              SUM(ad_upload_num) ad_upload_num,
              SUM(jump_to_super_red_envelope_num) jump_to_super_red_envelope_num,
              SUM(success_send_welcome_msg_num) success_send_welcome_msg_num,
              SUM(start_open_chat_num) start_open_chat_num,
              SUM(third_open_chat_num) third_open_chat_num,
              SUM(fifth_open_chat_num) fifth_open_chat_num,
              SUM(tenth_open_chat_num) tenth_open_chat_num
          FROM (
          SELECT
              landing_page_id,
              statistic_date,
              advertiser_account_group_id,
              SUM(page_view_num * sign)                             page_view_num,
              SUM(total_length_of_stay * sign)                      total_length_of_stay,
              SUM(form_submit_num * sign)                           form_submit_num,
              SUM(douyin_applet_native_form_submit_num  * sign)     douyin_applet_native_form_submit_num,
              SUM(clue_form_submit_num  * sign)                     clue_form_submit_num,
              SUM(phone_number_recieved_num * sign)                 phone_number_recieved_num,
              SUM(active_message_authorization_num * sign)          active_message_authorization_num,
              SUM(order_submit_num * sign)                          order_submit_num,
              SUM(payment_num * sign)                               payment_num,
              SUM(identify_qr_code_num * sign)                      identify_qr_code_num,

              SUM(ele_pv_num * sign) ele_pv_num,
              SUM(ele_qr_code_view_num * sign) ele_qr_code_view_num,
              SUM(ele_identify_wechat_qr_code_num * sign) ele_identify_wechat_qr_code_num,
              SUM(ele_add_wechat_success_num * sign) ele_add_wechat_success_num,
              SUM(whatsapp_jump_num * sign) as whatsapp_jump_num,
              SUM(whatsapp_add_friend_success_num * sign) as whatsapp_add_friend_success_num,
              SUM(whatsapp_user_open_mouth_num * sign) as whatsapp_user_open_mouth_num,

              SUM(taobao_page_view_num * sign) as taobao_page_view_num,
              SUM(taobao_order_payment_num * sign) as taobao_order_payment_num,
              SUM(taobao_product_click_num * sign) as taobao_product_click_num,
              SUM(taobao_first_visit_venue_num * sign) as taobao_first_visit_venue_num,
              SUM(taobao_red_envelope_receive_num * sign) as taobao_red_envelope_receive_num,
              SUM(taobao_cancel_order_payment_num * sign) as taobao_cancel_order_payment_num,
              SUM(taobao_high_commission_order_payment_num * sign) as taobao_high_commission_order_payment_num,

              SUM(add_work_wechat_num * sign)                       add_work_wechat_num,
              SUM(follow_official_account_num * sign)               follow_official_account_num,
              SUM(online_shop_buy_goods_success_num * sign)         online_shop_buy_goods_success_num,
              SUM(official_identify_qr_code_num * sign)             official_identify_qr_code_num,
              SUM(identify_group_qr_code_num * sign)                identify_group_qr_code_num,
              SUM(add_work_wechat_group_num * sign)                 add_work_wechat_group_num,
              SUM(wechat_official_article_page_view_num * sign)     wechat_official_article_page_view_num,
              SUM(income_line_num * sign)                           income_line_num,
              SUM(number_of_orders_completed_for_coupon_num * sign) number_of_orders_completed_for_coupon_num,
              SUM(douyin_applet_order_submit_num * sign) douyin_applet_order_submit_num,
              SUM(douyin_applet_order_finish_num * sign) douyin_applet_order_finish_num,
              SUM(taobao_component_copy_num * sign) taobao_component_copy_num,
              SUM(douyin_applet_jump_num * sign) douyin_applet_jump_num,
              SUM(official_add_customer_num * sign) official_add_customer_num,
              SUM(tao_bao_movie_applet_jump_num  * sign ) tao_bao_movie_applet_jump_num,
              SUM(tao_bao_movie_applet_order_num  * sign ) tao_bao_movie_applet_order_num,
              SUM(add_group_after_add_customer_service_num  * sign ) add_group_after_add_customer_service_num,
              SUM(add_group_after_follow_official_account_num  * sign ) add_group_after_follow_official_account_num,
              SUM(send_image_or_qr_code_num  * sign ) send_image_or_qr_code_num,
              SUM(mini_program_news_num  * sign ) mini_program_news_num,
              SUM(ad_upload_num  * sign ) ad_upload_num,
              SUM(jump_to_super_red_envelope_num  * sign ) jump_to_super_red_envelope_num,
              SUM(success_send_welcome_msg_num  * sign ) success_send_welcome_msg_num,
              SUM(start_open_chat_num  * sign ) start_open_chat_num,
              SUM(third_open_chat_num  * sign ) third_open_chat_num,
              SUM(fifth_open_chat_num  * sign ) fifth_open_chat_num,
              SUM(tenth_open_chat_num  * sign ) tenth_open_chat_num
          FROM landing_page_day_statistics_new
          <where>
              <if test="landingPageQuery.startTime != null and landingPageQuery.endTime!=null">
                  and statistic_date BETWEEN #{landingPageQuery.startTime} and #{landingPageQuery.endTime}
              </if>
              <if test="landingPageQuery.advertiserAccountGroupId != null">
                  and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
              </if>
          </where>
          GROUP BY advertiser_account_group_id, landing_page_id, landing_page_channel_id, statistic_date,douyin_customer_source
          having sum(sign) > 0
          ) GROUP BY landing_page_id),
          landingPageData as (select lp.*, lpg.name as landing_page_group_name
          from landing_page lp
          left join landing_page_group lpg on lp.landing_page_group_id = lpg.id
          <where>
              <if test="landingPageQuery.advertiserAccountGroupId != null">
                  and lp.advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
              </if>
              <if test="landingPageQuery.landingPageGroupId != null and landingPageQuery.landingPageGroupId != -1">
                  and lp.landing_page_group_id = #{landingPageQuery.landingPageGroupId}
              </if>
              <if test="landingPageQuery.deleteStatus != null">
                  and lp.delete_status = #{landingPageQuery.deleteStatus}
              </if>
              <if test="landingPageQuery.name != null and landingPageQuery.name != ''">
                  and lp.name like concat('%',#{landingPageQuery.name},'%')
              </if>

              <if test="landingPageQuery.landingPageType != null ">
                  and lp.landing_page_type = #{landingPageQuery.landingPageType}
              </if>
          </where>
          )
          SELECT
          sum(pv.page_view_num)                                                                              as page_view_num,
          sum(pv.form_submit_num)                                                                            as form_submit_num,
          SUM(pv.douyin_applet_native_form_submit_num)                                                       as douyin_applet_native_form_submit_num,
          SUM(pv.clue_form_submit_num )                                                                      as clue_form_submit_num,
          SUM(pv.phone_number_recieved_num )                                                                 as phone_number_recieved_num,
          SUM(pv.active_message_authorization_num )                                                          as active_message_authorization_num,
          (sum(pv.form_submit_num) +  sum(pv.douyin_applet_native_form_submit_num) +  SUM(pv.clue_form_submit_num))   as form_submit_total_num,
          sum(pv.order_submit_num)                                                                           as order_submit_num,
          sum(pv.payment_num)                                                                                as payment_num,
          SUM(pv.income_line_num )                                                                           as income_line_num,
          sum(pv.identify_qr_code_num)                                                                       as identify_qr_code_num,

          sum(pv.ele_pv_num)                                                                                 as ele_pv_num,
          sum(pv.ele_qr_code_view_num)                                                                       as ele_qr_code_view_num,
          sum(pv.ele_identify_wechat_qr_code_num)                                                            as ele_identify_wechat_qr_code_num,
          sum(pv.ele_add_wechat_success_num)                                                                 as ele_add_wechat_success_num,
          SUM(pv.whatsapp_jump_num) as whatsapp_jump_num,
          SUM(pv.whatsapp_add_friend_success_num) as whatsapp_add_friend_success_num,
          SUM(pv.whatsapp_user_open_mouth_num) as whatsapp_user_open_mouth_num,
          case
          when sum(pv.page_view_num) = 0 then 0
          else sum(pv.whatsapp_jump_num) / sum(pv.page_view_num) * 100 end                       as whatsapp_jump_rate,
          case
          when sum(pv.page_view_num) = 0 then 0
          else sum(pv.whatsapp_add_friend_success_num) / sum(pv.page_view_num) * 100 end                       as whatsapp_add_friend_success_rate,
          case
          when sum(pv.page_view_num) = 0 then 0
          else sum(pv.whatsapp_user_open_mouth_num) / sum(pv.page_view_num) * 100 end                       as whatsapp_user_open_mouth_rate,

          SUM(pv.taobao_page_view_num) as taobao_page_view_num,
          SUM(pv.taobao_order_payment_num) as taobao_order_payment_num,
          SUM(pv.taobao_product_click_num) as taobao_product_click_num,
          SUM(pv.taobao_first_visit_venue_num) as taobao_first_visit_venue_num,
          SUM(pv.taobao_red_envelope_receive_num) as taobao_red_envelope_receive_num,
          SUM(pv.taobao_cancel_order_payment_num) as taobao_cancel_order_payment_num,
          SUM(pv.taobao_high_commission_order_payment_num) as taobao_high_commission_order_payment_num,

          sum(pv.add_work_wechat_num)                                                                        as add_work_wechat_num,
          sum(pv.follow_official_account_num)                                                                as follow_official_account_num,
          sum(pv.online_shop_buy_goods_success_num)                                                          as online_shop_buy_goods_success_num,
          sum(pv.official_identify_qr_code_num)                                                              as official_identify_qr_code_num,
          case
          when sum(pv.official_identify_qr_code_num) = 0 then 0
          else sum(pv.official_identify_qr_code_num) / sum(pv.page_view_num) * 100 end                         as official_identify_qr_code_rate,
          sum(pv.identify_group_qr_code_num)                                                                 as identify_group_qr_code_num,
          sum(pv.add_work_wechat_group_num)                                                                  as add_work_wechat_group_num,
          sum(pv.wechat_official_article_page_view_num)                                                      as wechat_official_article_page_view_num,
          sum(pv.number_of_orders_completed_for_coupon_num)                                                  as number_of_orders_completed_for_coupon_num,
          sum(pv.douyin_applet_order_submit_num)                                                  as douyin_applet_order_submit_num,
          sum(pv.douyin_applet_order_finish_num)                                                  as douyin_applet_order_finish_num,
          sum(pv.official_add_customer_num)                                                                  as official_add_customer_num,
          sum(pv.taobao_component_copy_num)                                                  as taobao_component_copy_num,
          sum(pv.douyin_applet_jump_num)                                                     as douyin_applet_jump_num,
          sum(pv.tao_bao_movie_applet_jump_num)                                              as tao_bao_movie_applet_jump_num,
          sum(pv.tao_bao_movie_applet_order_num)                                             as tao_bao_movie_applet_order_num,
          sum(pv.add_group_after_add_customer_service_num)                                             as add_group_after_add_customer_service_num,
          sum(pv.add_group_after_follow_official_account_num)                                             as add_group_after_follow_official_account_num,
          SUM(pv.send_image_or_qr_code_num) as send_image_or_qr_code_num,
          SUM(pv.mini_program_news_num) as mini_program_news_num,
          SUM(pv.ad_upload_num) as ad_upload_num,
          SUM(pv.jump_to_super_red_envelope_num) as jump_to_super_red_envelope_num,
          SUM(pv.success_send_welcome_msg_num) as success_send_welcome_msg_num,
          SUM(pv.start_open_chat_num) as start_open_chat_num,
          SUM(pv.third_open_chat_num) as third_open_chat_num,
          SUM(pv.fifth_open_chat_num) as fifth_open_chat_num,
          SUM(pv.tenth_open_chat_num) as tenth_open_chat_num,

          case when sum(pv.page_view_num) = 0 then 0 else sum(pv.start_open_chat_num) / sum(pv.page_view_num) * 100 end  as start_open_chat_rate,

          case when sum(pv.page_view_num) = 0 then 0 else sum(pv.third_open_chat_num) / sum(pv.page_view_num) * 100 end  as third_open_chat_rate,

          case when sum(pv.page_view_num) = 0 then 0 else sum(pv.fifth_open_chat_num) / sum(pv.page_view_num) * 100 end  as fifth_open_chat_rate,

          case when sum(pv.page_view_num) = 0 then 0 else sum(pv.tenth_open_chat_num) / sum(pv.page_view_num) * 100 end  as tenth_open_chat_rate,

          case
            when sum(pv.page_view_num) = 0 then 0
            else sum(pv.jump_to_super_red_envelope_num) / sum(pv.page_view_num) * 100 end  as jump_to_super_red_envelope_rate,

          case
            when sum(pv.page_view_num) = 0 then 0
            else sum(pv.success_send_welcome_msg_num) / sum(pv.page_view_num) * 100 end  as success_send_welcome_msg_rate,

          case when SUM(pv.taobao_page_view_num) = 0 then 0 else sum(pv.taobao_page_view_num) / sum(pv.page_view_num) * 100 end as taobao_page_view_rate,
          case when SUM(pv.taobao_order_payment_num) = 0 then 0 else sum(pv.taobao_order_payment_num) / sum(pv.page_view_num) * 100 end as taobao_order_payment_rate,
          case when SUM(pv.taobao_product_click_num) = 0 then 0 else sum(pv.taobao_product_click_num) / sum(pv.page_view_num) * 100 end as taobao_product_click_rate,
          case when SUM(pv.taobao_first_visit_venue_num) = 0 then 0 else sum(pv.taobao_first_visit_venue_num) / sum(pv.page_view_num) * 100 end as taobao_first_visit_venue_rate,
          case when SUM(pv.taobao_red_envelope_receive_num) = 0 then 0 else sum(pv.taobao_red_envelope_receive_num) / sum(pv.page_view_num) * 100 end as taobao_red_envelope_receive_rate,
          case when SUM(pv.taobao_cancel_order_payment_num) = 0 then 0 else sum(pv.taobao_cancel_order_payment_num) / sum(pv.page_view_num) * 100 end as taobao_cancel_order_payment_rate,
          case when SUM(pv.taobao_high_commission_order_payment_num) = 0 then 0 else sum(pv.taobao_high_commission_order_payment_num) / sum(pv.page_view_num) * 100 end as taobao_high_commission_order_payment_rate,

          case
            when sum(pv.page_view_num) = 0 then 0
            else sum(pv.send_image_or_qr_code_num) / sum(pv.page_view_num) * 100 end  as send_image_or_qr_code_rate,

          case
            when sum(pv.page_view_num) = 0 then 0
            else sum(pv.mini_program_news_num) / sum(pv.page_view_num) * 100 end  as mini_program_news_rate,

          case
            when sum(pv.page_view_num) = 0 then 0
            else sum(pv.ad_upload_num) / sum(pv.page_view_num) * 100 end  as ad_upload_rate,

          case
            when sum(pv.page_view_num) = 0 then 0
            else sum(pv.income_line_num) / sum(pv.page_view_num) * 100 end  as income_line_rate,

          case
            when sum(pv.page_view_num) = 0 then 0
            else sum(pv.add_group_after_add_customer_service_num) / sum(pv.page_view_num) * 100 end  as add_group_after_add_customer_service_rate,

          case
            when sum(pv.page_view_num) = 0 then 0
            else sum(pv.add_group_after_follow_official_account_num) / sum(pv.page_view_num) * 100 end  as add_group_after_follow_official_account_rate,

          case
              when sum(pv.page_view_num) = 0 then 0
              else sum(pv.tao_bao_movie_applet_jump_num) / sum(pv.page_view_num) * 100 end                       as tao_bao_movie_applet_jump_rate,

          case
              when sum(pv.page_view_num) = 0 then 0
              else sum(pv.tao_bao_movie_applet_order_num) / sum(pv.page_view_num) * 100 end                       as tao_bao_movie_applet_order_rate,

          case
              when sum(pv.page_view_num) = 0 then 0
              else sum(pv.taobao_component_copy_num) / sum(pv.page_view_num) * 100 end                       as taobao_component_copy_rate,

          case
              when sum(pv.page_view_num) = 0 then 0
              else sum(pv.douyin_applet_jump_num) / sum(pv.page_view_num) * 100 end                       as douyin_applet_jump_rate,

          case
            when sum(pv.page_view_num) = 0 then 0
            else sum(pv.douyin_applet_order_submit_num) / sum(pv.page_view_num) * 100 end                       as douyin_applet_order_submit_rate,

          case
            when sum(pv.douyin_applet_order_submit_num) = 0 then 0
            else sum(pv.douyin_applet_order_finish_num) / sum(pv.douyin_applet_order_submit_num) * 100 end      as douyin_applet_order_finish_rate,

          case
              when sum(pv.page_view_num) = 0 then 0
              else sum(pv.form_submit_num) / sum(pv.page_view_num) * 100 end                                       as form_submit_rate,

          case
              when sum(pv.page_view_num) = 0 then 0
              else sum(pv.douyin_applet_native_form_submit_num) / sum(pv.page_view_num) * 100 end                  as douyin_applet_native_form_submit_rate,

          case
              when sum(pv.page_view_num) = 0 then 0
              else sum(pv.clue_form_submit_num) / sum(pv.page_view_num) * 100 end                                  as clue_form_submit_rate,

          case
              when sum(pv.page_view_num) = 0 then 0
              else sum(pv.phone_number_recieved_num) / sum(pv.page_view_num) * 100 end                             as phone_number_recieved_rate,

          case
              when sum(pv.page_view_num) = 0 then 0
              else sum(pv.active_message_authorization_num) / sum(pv.page_view_num) * 100 end                      as active_message_authorization_rate,

          case
          when sum(pv.page_view_num) = 0 then 0
          else (sum(pv.form_submit_num) + sum(pv.douyin_applet_native_form_submit_num) + sum(pv.clue_form_submit_num)) / sum(pv.page_view_num) * 100 end  as form_submit_total_rate,

          case
          when sum(pv.page_view_num) = 0 then 0
          else sum(pv.order_submit_num) / sum(pv.page_view_num) * 100 end                                      as order_submit_rate,
          case
          when sum(pv.order_submit_num) = 0 then 0
          else sum(pv.payment_num) / sum(pv.order_submit_num) * 100 end                                        as payment_rate,
          case
          when sum(pv.page_view_num) = 0 then 0
          else sum(pv.payment_num) / sum(pv.page_view_num) * 100 end                                           as comprehensive_payment_rate,
          case
          when sum(pv.order_submit_num) = 0 then 0
          else sum(pv.number_of_orders_completed_for_coupon_num) / sum(pv.order_submit_num) *
          100 end                                                                                   as number_of_orders_completed_for_coupon_rate,
          case
          when sum(pv.page_view_num) = 0 then 0
          else sum(pv.identify_qr_code_num) / sum(pv.page_view_num) * 100 end                                  as identify_qr_code_rate,
          case
          when sum(pv.page_view_num) = 0 then 0
          else sum(pv.add_work_wechat_num) / sum(pv.page_view_num) * 100 end                                   as add_work_wechat_rate,
          case
          when sum(pv.page_view_num) = 0 then 0
          else sum(pv.follow_official_account_num) / sum(pv.page_view_num) * 100 end                           as follow_official_account_rate,
          case
          when sum(pv.page_view_num) = 0 then 0
          else sum(pv.online_shop_buy_goods_success_num) / sum(pv.page_view_num) * 100 end                     as online_shop_buy_goods_success_rate,
          case
          when sum(pv.page_view_num) = 0 then 0
          else sum(pv.identify_group_qr_code_num) / sum(pv.page_view_num) * 100 end                            as identify_group_qr_code_rate,
          case
          when sum(pv.page_view_num) = 0 then 0
          else sum(pv.add_work_wechat_group_num) / sum(pv.page_view_num) * 100 end                             as add_work_wechat_group_rate,
          case
          when sum(pv.page_view_num) = 0 then 0
          else sum(pv.official_add_customer_num) / sum(pv.page_view_num) * 100 end                             as official_add_customer_rate
          FROM landingPageData lp
          LEFT JOIN basePvData pv ON lp.id = pv.landing_page_id
          <where>
              <if test="landingPageQuery.advertiserAccountGroupId != null">
                  and lp.advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
              </if>
              <if test="landingPageQuery.landingPageGroupId != null and landingPageQuery.landingPageGroupId != -1">
                  and lp.landing_page_group_id = #{landingPageQuery.landingPageGroupId}
              </if>
              <if test="landingPageQuery.deleteStatus != null">
                  and lp.delete_status = #{landingPageQuery.deleteStatus}
              </if>
              <if test="landingPageQuery.name != null and landingPageQuery.name != ''">
                  and lp.name like concat('%',#{landingPageQuery.name},'%')
              </if>

              <if test="landingPageQuery.landingPageType != null ">
                  and lp.landing_page_type = #{landingPageQuery.landingPageType}
              </if>
          </where>
      </select>

      <select id="pageChannelsTotal" resultType="ai.yiye.agent.domain.LandingPageChannel">
          WITH basePvData as (
          select
              pv.landing_page_channel_id,
              SUM(page_view_num) AS page_view_num,
              SUM(income_line_num) AS income_line_num,
              SUM(total_length_of_stay) AS total_length_of_stay,
              SUM(form_submit_num) AS form_submit_num,

              SUM(clue_form_submit_num) AS clue_form_submit_num,
              SUM(douyin_applet_native_form_submit_num) AS douyin_applet_native_form_submit_num,
              SUM(phone_number_recieved_num) AS phone_number_recieved_num,
              SUM(active_message_authorization_num) AS active_message_authorization_num,

              SUM(order_submit_num) AS order_submit_num,
              SUM(official_identify_qr_code_num) AS official_identify_qr_code_num,
              SUM(payment_num) AS payment_num,
              SUM(identify_qr_code_num) AS identify_qr_code_num,
              SUM(add_work_wechat_num) AS add_work_wechat_num,
              SUM(follow_official_account_num) AS follow_official_account_num,
              SUM(online_shop_buy_goods_success_num) AS online_shop_buy_goods_success_num,
              SUM(identify_group_qr_code_num) AS identify_group_qr_code_num,

              SUM(ele_pv_num) as ele_pv_num,
              SUM(ele_qr_code_view_num) as ele_qr_code_view_num,
              SUM(ele_identify_wechat_qr_code_num) as ele_identify_wechat_qr_code_num,
              SUM(ele_add_wechat_success_num) as ele_add_wechat_success_num,

              sum(whatsapp_jump_num) as  whatsapp_jump_num,
              sum(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
              sum(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,

              sum(taobao_page_view_num) as taobao_page_view_num,
              sum(taobao_order_payment_num) as taobao_order_payment_num,
              sum(taobao_product_click_num) as taobao_product_click_num,
              sum(taobao_first_visit_venue_num) as taobao_first_visit_venue_num,
              sum(taobao_red_envelope_receive_num) as taobao_red_envelope_receive_num,
              sum(taobao_cancel_order_payment_num) as taobao_cancel_order_payment_num,
              sum(taobao_high_commission_order_payment_num) as taobao_high_commission_order_payment_num,

              SUM(add_work_wechat_group_num) AS add_work_wechat_group_num,
              SUM(wechat_official_article_page_view_num) AS wechat_official_article_page_view_num,
              SUM(number_of_orders_completed_for_coupon_num) as number_of_orders_completed_for_coupon_num,
              SUM(douyin_applet_order_submit_num) AS douyin_applet_order_submit_num,
              SUM(douyin_applet_order_finish_num) AS douyin_applet_order_finish_num,
              SUM(taobao_component_copy_num) AS taobao_component_copy_num,
              SUM(douyin_applet_jump_num) AS douyin_applet_jump_num,
              SUM(official_add_customer_num) AS official_add_customer_num,
              SUM(tao_bao_movie_applet_jump_num) AS tao_bao_movie_applet_jump_num,
              SUM(tao_bao_movie_applet_order_num) AS tao_bao_movie_applet_order_num,
              SUM(add_group_after_add_customer_service_num) AS add_group_after_add_customer_service_num,
              SUM(add_group_after_follow_official_account_num) AS add_group_after_follow_official_account_num,
              SUM(send_image_or_qr_code_num) AS send_image_or_qr_code_num,
              SUM(mini_program_news_num) AS mini_program_news_num,
              SUM(ad_upload_num) AS ad_upload_num,
              SUM(jump_to_super_red_envelope_num) AS jump_to_super_red_envelope_num,
              SUM(success_send_welcome_msg_num) AS success_send_welcome_msg_num,
              SUM(start_open_chat_num ) start_open_chat_num,
              SUM(third_open_chat_num ) third_open_chat_num,
              SUM(fifth_open_chat_num ) fifth_open_chat_num,
              SUM(tenth_open_chat_num ) tenth_open_chat_num
          from
          (select
              landing_page_channel_id,
              statistic_date,
              advertiser_account_group_id,
              landing_page_id,
              SUM(page_view_num * sign) AS page_view_num,
              SUM(income_line_num * sign) AS income_line_num,
              SUM(total_length_of_stay * sign) AS total_length_of_stay,

              SUM(form_submit_num * sign) AS form_submit_num,
              SUM(clue_form_submit_num * sign) AS clue_form_submit_num,
              SUM(douyin_applet_native_form_submit_num * sign) AS douyin_applet_native_form_submit_num,
              SUM(phone_number_recieved_num * sign) AS phone_number_recieved_num,
              SUM(active_message_authorization_num * sign) AS active_message_authorization_num,

              SUM(order_submit_num * sign) AS order_submit_num,
              SUM(official_identify_qr_code_num * sign) AS official_identify_qr_code_num,
              SUM(payment_num * sign) AS payment_num,
              SUM(identify_qr_code_num * sign) AS identify_qr_code_num,

              SUM(ele_pv_num * sign) as ele_pv_num,
              SUM(ele_qr_code_view_num * sign) as ele_qr_code_view_num,
              SUM(ele_identify_wechat_qr_code_num * sign) as ele_identify_wechat_qr_code_num,
              SUM(ele_add_wechat_success_num * sign) as ele_add_wechat_success_num,

              SUM(whatsapp_jump_num * sign) as whatsapp_jump_num,
              SUM(whatsapp_add_friend_success_num * sign) as whatsapp_add_friend_success_num,
              SUM(whatsapp_user_open_mouth_num * sign) as whatsapp_user_open_mouth_num,

              sum(taobao_page_view_num * sign) as taobao_page_view_num,
              sum(taobao_order_payment_num * sign) as taobao_order_payment_num,
              sum(taobao_product_click_num * sign) as taobao_product_click_num,
              sum(taobao_first_visit_venue_num * sign) as taobao_first_visit_venue_num,
              sum(taobao_red_envelope_receive_num * sign) as taobao_red_envelope_receive_num,
              sum(taobao_cancel_order_payment_num * sign) as taobao_cancel_order_payment_num,
              sum(taobao_high_commission_order_payment_num * sign) as taobao_high_commission_order_payment_num,

              SUM(add_work_wechat_num * sign) AS add_work_wechat_num,
              SUM(follow_official_account_num * sign) AS follow_official_account_num,
              SUM(online_shop_buy_goods_success_num * sign) AS online_shop_buy_goods_success_num,
              SUM(identify_group_qr_code_num * sign) AS identify_group_qr_code_num,
              SUM(add_work_wechat_group_num * sign) AS add_work_wechat_group_num,
              SUM(wechat_official_article_page_view_num * sign) AS wechat_official_article_page_view_num,
              SUM(number_of_orders_completed_for_coupon_num * sign) as number_of_orders_completed_for_coupon_num,
              SUM(douyin_applet_order_submit_num * sign) AS douyin_applet_order_submit_num,
              SUM(douyin_applet_order_finish_num * sign) AS douyin_applet_order_finish_num,
              SUM(taobao_component_copy_num * sign) AS taobao_component_copy_num,
              SUM(douyin_applet_jump_num * sign) AS douyin_applet_jump_num,
              SUM(official_add_customer_num * sign) AS official_add_customer_num,
              SUM(tao_bao_movie_applet_jump_num * sign) AS tao_bao_movie_applet_jump_num,
              SUM(tao_bao_movie_applet_order_num * sign) AS tao_bao_movie_applet_order_num,
              SUM(add_group_after_add_customer_service_num * sign) AS add_group_after_add_customer_service_num,
              SUM(add_group_after_follow_official_account_num * sign) AS add_group_after_follow_official_account_num,
              SUM(send_image_or_qr_code_num * sign) AS send_image_or_qr_code_num,
              SUM(mini_program_news_num * sign) AS mini_program_news_num,
              SUM(ad_upload_num * sign) AS ad_upload_num,
              SUM(jump_to_super_red_envelope_num * sign) AS jump_to_super_red_envelope_num,
              SUM(success_send_welcome_msg_num * sign) AS success_send_welcome_msg_num,
              SUM(start_open_chat_num * sign) start_open_chat_num,
              SUM(third_open_chat_num * sign) third_open_chat_num,
              SUM(fifth_open_chat_num * sign) fifth_open_chat_num,
              SUM(tenth_open_chat_num * sign) tenth_open_chat_num
          from landing_page_day_statistics_new
          <where>
              <if test="landingPageQuery.startTime != null and landingPageQuery.endTime!=null">
                  and statistic_date between #{landingPageQuery.startTime} and #{landingPageQuery.endTime}
              </if>
              <if test="landingPageQuery.advertiserAccountGroupId != null">
                  and advertiser_account_group_id = #{landingPageQuery.advertiserAccountGroupId}
              </if>
              <if test="landingPageQuery.landingPageId != null">
                  and landing_page_id = #{landingPageQuery.landingPageId}
              </if>
          </where>
          group by advertiser_account_group_id, landing_page_id, landing_page_channel_id, statistic_date,douyin_customer_source
          having sum(sign) > 0) pv
          group by pv.landing_page_channel_id
          ),
          landingPageChannelData as (
          select * from landing_page_channels
          <where>
              <if test="landingPageQuery.landingPageId != null">
                  and landing_page_id = #{landingPageQuery.landingPageId}
              </if>
              <if test="landingPageQuery.deleteStatus != null">
                  and delete_status = #{landingPageQuery.deleteStatus}
              </if>
          </where>
          )
          select
          sum(pv.page_view_num) as page_view_num,
          sum(pv.income_line_num) as income_line_num,
          sum(pv.total_length_of_stay) as total_length_of_stay,
          sum(pv.form_submit_num) as form_submit_num,

          sum(pv.clue_form_submit_num) as clue_form_submit_num,
          sum(pv.douyin_applet_native_form_submit_num) as douyin_applet_native_form_submit_num,
          (sum(pv.form_submit_num) + sum(pv.douyin_applet_native_form_submit_num) + sum(pv.clue_form_submit_num)) as form_submit_total_num,
          sum(pv.phone_number_recieved_num) as phone_number_recieved_num,
          sum(pv.active_message_authorization_num) as active_message_authorization_num,
          sum(pv.douyin_applet_order_submit_num) as douyin_applet_order_submit_num,
          sum(pv.douyin_applet_order_finish_num) as douyin_applet_order_finish_num,
          sum(pv.taobao_component_copy_num) as taobao_component_copy_num,
          sum(pv.douyin_applet_jump_num) as douyin_applet_jump_num,

          sum(pv.order_submit_num) as order_submit_num,
          sum(pv.payment_num) as payment_num,
          sum(pv.identify_qr_code_num) as identify_qr_code_num,

          SUM(pv.ele_pv_num) as ele_pv_num,
          SUM(pv.ele_qr_code_view_num) as ele_qr_code_view_num,
          SUM(pv.ele_identify_wechat_qr_code_num) as ele_identify_wechat_qr_code_num,
          SUM(pv.ele_add_wechat_success_num) as ele_add_wechat_success_num,
          SUM(pv.whatsapp_jump_num ) as whatsapp_jump_num,
          SUM(pv.whatsapp_add_friend_success_num ) as whatsapp_add_friend_success_num,
          SUM(pv.whatsapp_user_open_mouth_num ) as whatsapp_user_open_mouth_num,
          SUM(pv.add_group_after_add_customer_service_num ) as add_group_after_add_customer_service_num,
          SUM(pv.add_group_after_follow_official_account_num ) as add_group_after_follow_official_account_num,
          SUM(pv.send_image_or_qr_code_num ) as send_image_or_qr_code_num,
          SUM(pv.mini_program_news_num ) as mini_program_news_num,
          SUM(pv.ad_upload_num ) as ad_upload_num,
          SUM(pv.jump_to_super_red_envelope_num ) as jump_to_super_red_envelope_num,
          SUM(pv.success_send_welcome_msg_num ) as success_send_welcome_msg_num,

          sum(pv.taobao_page_view_num) as taobao_page_view_num,
          sum(pv.taobao_order_payment_num) as taobao_order_payment_num,
          sum(pv.taobao_product_click_num) as taobao_product_click_num,
          sum(pv.taobao_first_visit_venue_num) as taobao_first_visit_venue_num,
          sum(pv.taobao_red_envelope_receive_num) as taobao_red_envelope_receive_num,
          sum(pv.taobao_cancel_order_payment_num) as taobao_cancel_order_payment_num,
          sum(pv.taobao_high_commission_order_payment_num) as taobao_high_commission_order_payment_num,

          case when page_view_num  = 0 then 0 else add_group_after_add_customer_service_num / page_view_num * 100 end as add_group_after_add_customer_service_rate,
          case when page_view_num  = 0 then 0 else add_group_after_follow_official_account_num / page_view_num * 100 end as add_group_after_follow_official_account_rate,
          case when page_view_num  = 0 then 0 else whatsapp_jump_num / page_view_num * 100 end as whatsapp_jump_rate,
          case when page_view_num  = 0 then 0 else whatsapp_add_friend_success_num / page_view_num * 100 end as whatsapp_add_friend_success_rate,
          case when page_view_num  = 0 then 0 else whatsapp_user_open_mouth_num / page_view_num * 100 end as whatsapp_user_open_mouth_rate,

          sum(pv.add_work_wechat_num) as add_work_wechat_num,
          sum(pv.follow_official_account_num) as follow_official_account_num,
          sum(pv.online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
          sum(pv.official_identify_qr_code_num) as official_identify_qr_code_num,
          sum(pv.add_work_wechat_group_num) as add_work_wechat_group_num,
          sum(pv.identify_group_qr_code_num) as identify_group_qr_code_num,
          sum(pv.wechat_official_article_page_view_num) as wechat_official_article_page_view_num,
          sum(pv.number_of_orders_completed_for_coupon_num) as number_of_orders_completed_for_coupon_num,
          sum(pv.official_add_customer_num) as official_add_customer_num,
          sum(pv.tao_bao_movie_applet_jump_num) as tao_bao_movie_applet_jump_num,
          sum(pv.tao_bao_movie_applet_order_num) as tao_bao_movie_applet_order_num,
          sum(pv.start_open_chat_num) as  start_open_chat_num,
          sum(pv.third_open_chat_num) as  third_open_chat_num,
          sum(pv.fifth_open_chat_num) as  fifth_open_chat_num,
          sum(pv.tenth_open_chat_num) as  tenth_open_chat_num,

          CASE WHEN page_view_num = 0 THEN 0 ELSE start_open_chat_num / page_view_num * 100 END AS start_open_chat_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE third_open_chat_num / page_view_num * 100 END AS third_open_chat_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE fifth_open_chat_num / page_view_num * 100 END AS fifth_open_chat_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE tenth_open_chat_num / page_view_num * 100 END AS tenth_open_chat_rate,

          CASE WHEN page_view_num = 0 THEN 0 ELSE taobao_page_view_num / page_view_num * 100 END AS taobao_page_view_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE taobao_order_payment_num / page_view_num * 100 END AS taobao_order_payment_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE taobao_product_click_num / page_view_num * 100 END AS taobao_product_click_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE taobao_first_visit_venue_num / page_view_num * 100 END AS taobao_first_visit_venue_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE taobao_red_envelope_receive_num / page_view_num * 100 END AS taobao_red_envelope_receive_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE taobao_cancel_order_payment_num / page_view_num * 100 END AS taobao_cancel_order_payment_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE taobao_high_commission_order_payment_num / page_view_num * 100 END AS taobao_high_commission_order_payment_rate,

          CASE WHEN page_view_num = 0 THEN 0 ELSE send_image_or_qr_code_num / page_view_num * 100 END AS send_image_or_qr_code_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE mini_program_news_num / page_view_num * 100 END  AS mini_program_news_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE ad_upload_num / page_view_num * 100 END  AS ad_upload_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE income_line_num / page_view_num * 100 END AS income_line_rate,

          CASE WHEN page_view_num = 0 THEN 0 ELSE tao_bao_movie_applet_jump_num / page_view_num * 100 END AS tao_bao_movie_applet_jump_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE tao_bao_movie_applet_order_num / page_view_num * 100 END  AS tao_bao_movie_applet_order_rate,

          CASE WHEN page_view_num = 0 THEN 0 ELSE official_identify_qr_code_num / page_view_num * 100 END AS official_identify_qr_code_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE form_submit_num / page_view_num * 100 END  AS form_submit_rate,

          CASE WHEN page_view_num = 0 THEN 0 ELSE douyin_applet_order_submit_num / page_view_num * 100 END  AS douyin_applet_order_submit_rate,
          CASE WHEN douyin_applet_order_submit_num = 0 THEN 0 ELSE douyin_applet_order_finish_num / douyin_applet_order_submit_num * 100 END  AS douyin_applet_order_finish_rate,

          CASE WHEN page_view_num = 0 THEN 0 ELSE taobao_component_copy_num / page_view_num * 100 END  AS taobao_component_copy_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE douyin_applet_jump_num / page_view_num * 100 END  AS douyin_applet_jump_rate,

          CASE WHEN page_view_num = 0 THEN 0 ELSE clue_form_submit_num / page_view_num * 100 END  AS clue_form_submit_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE douyin_applet_native_form_submit_num / page_view_num * 100 END  AS douyin_applet_native_form_submit_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE phone_number_recieved_num / page_view_num * 100 END  AS phone_number_recieved_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE active_message_authorization_num / page_view_num * 100 END  AS active_message_authorization_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE form_submit_total_num / page_view_num * 100 END  AS form_submit_total_rate,

          CASE WHEN page_view_num = 0 THEN 0 ELSE order_submit_num / page_view_num * 100 END  AS order_submit_rate,
          CASE WHEN order_submit_num = 0 THEN 0 ELSE payment_num / order_submit_num * 100 END  AS payment_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE payment_num / page_view_num * 100 END  AS comprehensive_payment_rate,
          CASE WHEN order_submit_num = 0 THEN 0 ELSE number_of_orders_completed_for_coupon_num / order_submit_num * 100 END  AS number_of_orders_completed_for_coupon_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE identify_qr_code_num / page_view_num * 100 END  AS identify_qr_code_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE add_work_wechat_num / page_view_num * 100 END  AS add_work_wechat_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE follow_official_account_num / page_view_num * 100 END  AS follow_official_account_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE online_shop_buy_goods_success_num / page_view_num * 100 END  AS online_shop_buy_goods_success_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE identify_group_qr_code_num / page_view_num * 100 END  AS identify_group_qr_code_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE add_work_wechat_group_num / page_view_num * 100 END  AS add_work_wechat_group_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE official_add_customer_num / page_view_num * 100 END  AS official_add_customer_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE jump_to_super_red_envelope_num / page_view_num * 100 END  AS jump_to_super_red_envelope_rate,
          CASE WHEN page_view_num = 0 THEN 0 ELSE success_send_welcome_msg_num / page_view_num * 100 END  AS success_send_welcome_msg_rate
          from landingPageChannelData lp
          left join basePvData pv on lp.id = pv.landing_page_channel_id
      </select>



      <select id="dashboardFromIndicatorStatistics"
              resultType="ai.yiye.agent.domain.pageview.PageViewReportResponse">
          WITH baseData AS (
          SELECT
          landing_page_id,
          SUM ( number_of_orders_completed_for_coupon_num ) number_of_orders_completed_for_coupon_num,
          SUM ( form_submit_num ) form_submit_num,
          SUM ( page_view_num ) page_view_num,
          SUM ( total_length_of_stay ) total_length_of_stay,
          SUM ( order_submit_num ) order_submit_num,
          SUM ( payment_num ) payment_num,
          SUM ( identify_qr_code_num ) identify_qr_code_num,
          SUM ( add_work_wechat_num ) add_work_wechat_num,
          SUM ( follow_official_account_num ) follow_official_account_num,
          SUM ( online_shop_buy_goods_success_num ) online_shop_buy_goods_success_num,
          SUM ( official_identify_qr_code_num ) official_identify_qr_code_num,
          SUM ( identify_group_qr_code_num ) identify_group_qr_code_num,
          SUM ( add_work_wechat_group_num ) add_work_wechat_group_num,
          SUM ( income_line_num ) income_line_num,
          SUM ( wechat_official_article_page_view_num ) wechat_official_article_page_view_num,
          SUM ( douyin_applet_order_submit_num ) douyin_applet_order_submit_num,
          SUM ( douyin_applet_order_finish_num ) douyin_applet_order_finish_num,
          SUM ( ad_upload_num ) ad_upload_num,
          SUM ( jump_to_super_red_envelope_num ) jump_to_super_red_envelope_num,
          SUM ( success_send_welcome_msg_num ) success_send_welcome_msg_num,
          SUM ( send_image_or_qr_code_num ) send_image_or_qr_code_num,
          SUM ( mini_program_news_num ) mini_program_news_num,
          sum(start_open_chat_num) as  start_open_chat_num,
          sum(third_open_chat_num) as  third_open_chat_num,
          sum(fifth_open_chat_num) as  fifth_open_chat_num,
          sum(tenth_open_chat_num) as  tenth_open_chat_num
          FROM
          (
          SELECT
          landing_page_id,
          statistic_date,
          landing_page_channel_id,
          SUM ( number_of_orders_completed_for_coupon_num * sign) number_of_orders_completed_for_coupon_num,
          SUM ( form_submit_num * sign) form_submit_num,
          SUM ( page_view_num * sign) page_view_num,
          SUM ( total_length_of_stay * sign) total_length_of_stay,
          SUM ( order_submit_num * sign) order_submit_num,
          SUM ( payment_num * sign) payment_num,
          SUM ( identify_qr_code_num * sign) identify_qr_code_num,
          SUM ( add_work_wechat_num * sign) add_work_wechat_num,
          SUM ( follow_official_account_num * sign) follow_official_account_num,
          SUM ( online_shop_buy_goods_success_num * sign) online_shop_buy_goods_success_num,
          SUM ( official_identify_qr_code_num * sign) official_identify_qr_code_num,
          SUM ( identify_group_qr_code_num * sign) identify_group_qr_code_num,
          SUM ( add_work_wechat_group_num * sign) add_work_wechat_group_num,
          SUM ( income_line_num * sign) income_line_num,
          SUM ( wechat_official_article_page_view_num * sign) wechat_official_article_page_view_num,
          SUM ( douyin_applet_order_submit_num * sign) douyin_applet_order_submit_num,
          SUM ( douyin_applet_order_finish_num * sign) douyin_applet_order_finish_num,
          SUM ( ad_upload_num * sign) ad_upload_num,
          SUM ( jump_to_super_red_envelope_num * sign) jump_to_super_red_envelope_num,
          SUM ( success_send_welcome_msg_num * sign) success_send_welcome_msg_num,
          SUM ( send_image_or_qr_code_num * sign) send_image_or_qr_code_num,
          SUM ( mini_program_news_num * sign) mini_program_news_num,
          SUM(  start_open_chat_num * sign) start_open_chat_num,
          SUM(  third_open_chat_num * sign) third_open_chat_num,
          SUM(  fifth_open_chat_num * sign) fifth_open_chat_num,
          SUM(  tenth_open_chat_num * sign) tenth_open_chat_num
          FROM
          landing_page_day_statistics_new
          <where>
              <if test="lprf.startTime != null and lprf.endTime!=null">
                  and statistic_date BETWEEN #{lprf.startTime} and #{lprf.endTime}
              </if>
              <if test="lprf.landingPageId != null">
                  and landing_page_id = #{lprf.landingPageId}
              </if>
              <if test="lprf.channelId != null">
                  and landing_page_channel_id = #{lprf.channelId}
              </if>
          </where>
          GROUP BY
          advertiser_account_group_id, landing_page_id, landing_page_channel_id, statistic_date,douyin_customer_source
          having sum(sign) > 0
          )
          GROUP BY landing_page_id
          ) SELECT
          baseData.*,
          ifNull ( case when page_view_num = 0 then 0 else total_length_of_stay / page_view_num  end, 0 ) AS average_length_of_stay,
          ifNull ( case when page_view_num = 0 then 0 else form_submit_num / page_view_num  * 100 end, 0 ) AS form_submit_rate,
          ifNull ( case when page_view_num = 0 then 0 else order_submit_num / page_view_num  * 100 end, 0 ) AS order_submit_rate,
          ifNull ( case when order_submit_num = 0 then 0 else payment_num / order_submit_num  * 100 end, 0 ) AS payment_rate,
          ifNull ( case when order_submit_num = 0 then 0 else number_of_orders_completed_for_coupon_num /order_submit_num  * 100 end, 0 ) AS number_of_orders_completed_for_coupon_rate,
          ifNull ( case when page_view_num = 0 then 0 else identify_qr_code_num / page_view_num  * 100 end, 0 ) AS identify_qr_code_rate,
          ifNull ( case when page_view_num = 0 then 0 else add_work_wechat_num / page_view_num  * 100 end, 0 ) AS add_work_wechat_rate,
          ifNull ( case when page_view_num = 0 then 0 else follow_official_account_num / page_view_num  * 100 end, 0 ) AS follow_official_account_rate,
          ifNull ( case when page_view_num = 0 then 0 else online_shop_buy_goods_success_num / page_view_num  * 100 end, 0 ) AS online_shop_buy_goods_success_rate,
          ifNull ( case when page_view_num = 0 then 0 else official_identify_qr_code_num / page_view_num  * 100 end, 0 ) AS official_identify_qr_code_rate,
          ifNull ( case when page_view_num = 0 then 0 else identify_group_qr_code_num / page_view_num  * 100 end, 0 ) AS identify_group_qr_code_rate,
          ifNull ( case when page_view_num = 0 then 0 else add_work_wechat_group_num / page_view_num  * 100 end, 0 ) AS add_work_wechat_group_rate,
          ifNull ( case when page_view_num = 0 then 0 else order_submit_num / page_view_num  * 100 end, 0 ) AS comprehensive_payment_rate,
          ifNull ( case when page_view_num = 0 then 0 else douyin_applet_order_submit_num / page_view_num  * 100 end, 0 ) AS douyin_applet_order_submit_rate,
          ifNull ( case when page_view_num = 0 then 0 else douyin_applet_order_finish_num / page_view_num  * 100 end, 0 ) AS douyin_applet_order_finish_rate,
          ifNull ( case when page_view_num = 0 then 0 else ad_upload_num / page_view_num  * 100 end, 0 ) AS ad_upload_rate,
          ifNull ( case when page_view_num = 0 then 0 else jump_to_super_red_envelope_num / page_view_num  * 100 end, 0 ) AS jump_to_super_red_envelope_rate,
          ifNull ( case when page_view_num = 0 then 0 else success_send_welcome_msg_num / page_view_num  * 100 end, 0 ) AS success_send_welcome_msg_rate,
          ifNull ( case when page_view_num = 0 then 0 else send_image_or_qr_code_num / page_view_num  * 100 end, 0 ) AS send_image_or_qr_code_rate,
          ifNull ( case when page_view_num = 0 then 0 else mini_program_news_num / page_view_num  * 100 end, 0 ) AS mini_program_news_rate,
          ifNull ( case when page_view_num = 0 then 0 else income_line_num / page_view_num  * 100 end, 0 ) AS income_line_rate,
          ifNull ( case when page_view_num = 0 then 0 else start_open_chat_num / page_view_num  * 100 end, 0 ) AS start_open_chat_rate,
          ifNull ( case when page_view_num = 0 then 0 else third_open_chat_num / page_view_num  * 100 end, 0 ) AS third_open_chat_rate,
          ifNull ( case when page_view_num = 0 then 0 else fifth_open_chat_num / page_view_num  * 100 end, 0 ) AS fifth_open_chat_rate,
          ifNull ( case when page_view_num = 0 then 0 else tenth_open_chat_num / page_view_num  * 100 end, 0 ) AS tenth_open_chat_rate
          FROM
          baseData
      </select>


    <select id="getAdFlowSourceAnalysisBeForeYesterday"
            resultType="ai.yiye.agent.landingpage.controller.vo.AdFlowSourceAnalysisVO">
        SELECT
            toDate(statistic_date) as day_time,
            SUM ( jin_ri_tou_tiao ) jin_ri_tou_tiao,
            SUM ( jin_ri_tou_tiao_add ) jin_ri_tou_tiao_add,
            SUM ( dou_yin ) dou_yin,
            SUM ( dou_yin_add ) dou_yin_add,
            SUM ( dou_yin_lite ) dou_yin_lite,
            SUM ( dou_yin_lite_add ) dou_yin_lite_add,
            SUM ( dou_yin_huoshan ) dou_yin_huoshan,
            SUM ( dou_yin_huoshan_add ) dou_yin_huoshan_add,
            SUM ( xi_gua_shi_pin ) xi_gua_shi_pin,
            SUM ( xi_gua_shi_pin_add ) xi_gua_shi_pin_add,
            SUM ( fan_qie ) fan_qie,
            SUM ( fan_qie_add ) fan_qie_add,
            SUM ( pango_lin ) pango_lin,
            SUM ( pango_lin_add ) pango_lin_add,
            SUM ( total ) total,
            SUM ( total_add ) total_add
        FROM
        (
            SELECT
                statistic_date ,
                coalesce(SUM(CASE WHEN flow_source = 1 THEN page_view_num * sign ELSE 0 END),0) as jin_ri_tou_tiao,
                coalesce(SUM(CASE WHEN flow_source = 1 THEN add_friend_num * sign ELSE 0 END),0) as jin_ri_tou_tiao_add,
                coalesce(SUM(CASE WHEN flow_source = 3 THEN page_view_num * sign ELSE 0 END),0) as dou_yin,
                coalesce(SUM(CASE WHEN flow_source = 3 THEN add_friend_num * sign ELSE 0 END),0) as dou_yin_add,
                coalesce(SUM(CASE WHEN flow_source = 4 THEN page_view_num * sign ELSE 0 END),0) as dou_yin_lite,
                coalesce(SUM(CASE WHEN flow_source = 4 THEN add_friend_num * sign ELSE 0 END),0) as dou_yin_lite_add,
                coalesce(SUM(CASE WHEN flow_source = 5 THEN page_view_num * sign ELSE 0 END),0) as dou_yin_huoshan,
                coalesce(SUM(CASE WHEN flow_source = 5 THEN add_friend_num * sign ELSE 0 END),0) as dou_yin_huoshan_add,
                coalesce(SUM(CASE WHEN flow_source = 6 THEN page_view_num * sign ELSE 0 END),0) as xi_gua_shi_pin,
                coalesce(SUM(CASE WHEN flow_source = 6 THEN add_friend_num * sign ELSE 0 END),0) as xi_gua_shi_pin_add,
                coalesce(SUM(CASE WHEN flow_source = 7 THEN page_view_num * sign ELSE 0 END),0) as fan_qie,
                coalesce(SUM(CASE WHEN flow_source = 7 THEN add_friend_num * sign ELSE 0 END),0) as fan_qie_add,
                coalesce(SUM(CASE WHEN flow_source = 10  THEN page_view_num * sign ELSE 0 END),0) as pango_lin,
                coalesce(SUM(CASE WHEN flow_source = 10  THEN add_friend_num * sign ELSE 0 END),0) as pango_lin_add,
                coalesce(SUM(page_view_num * sign),0) as total,
                coalesce(SUM(add_friend_num * sign),0) as total_add
            FROM
                 landing_page_flow_source_day_statistics
            <where>
                <if test="lprf.startTime != null and lprf.endTime!=null">
                    and statistic_date BETWEEN #{lprf.startTime} and #{lprf.endTime}
                </if>
                <if test="lprf.landingPageId != null">
                    and landing_page_id = #{lprf.landingPageId}
                </if>
                <if test="lprf.channelId != null">
                    and landing_page_channel_id = #{lprf.channelId}
                </if>
            </where>
            GROUP BY statistic_date
            having sum(sign) > 0
        )
        GROUP BY  day_time
    </select>


    <select id="getAdFlowSourceAnalysisTotalBeForeYesterday"
            resultType="ai.yiye.agent.landingpage.controller.vo.AdFlowSourceAnalysisVO">

        SELECT
            coalesce(SUM(CASE WHEN flow_source = 1 THEN page_view_num * sign ELSE 0 END),0) as jin_ri_tou_tiao,
            coalesce(SUM(CASE WHEN flow_source = 1 THEN add_friend_num * sign ELSE 0 END),0) as jin_ri_tou_tiao_add,
            coalesce(SUM(CASE WHEN flow_source = 3 THEN page_view_num * sign ELSE 0 END),0) as dou_yin,
            coalesce(SUM(CASE WHEN flow_source = 3 THEN add_friend_num * sign ELSE 0 END),0) as dou_yin_add,
            coalesce(SUM(CASE WHEN flow_source = 4 THEN page_view_num * sign ELSE 0 END),0) as dou_yin_lite,
            coalesce(SUM(CASE WHEN flow_source = 4 THEN add_friend_num * sign ELSE 0 END),0) as dou_yin_lite_add,
            coalesce(SUM(CASE WHEN flow_source = 5 THEN page_view_num * sign ELSE 0 END),0) as dou_yin_huoshan,
            coalesce(SUM(CASE WHEN flow_source = 5 THEN add_friend_num * sign ELSE 0 END),0) as dou_yin_huoshan_add,
            coalesce(SUM(CASE WHEN flow_source = 6 THEN page_view_num * sign ELSE 0 END),0) as xi_gua_shi_pin,
            coalesce(SUM(CASE WHEN flow_source = 6 THEN add_friend_num * sign ELSE 0 END),0) as xi_gua_shi_pin_add,
            coalesce(SUM(CASE WHEN flow_source = 7 THEN page_view_num * sign ELSE 0 END),0) as fan_qie,
            coalesce(SUM(CASE WHEN flow_source = 7 THEN add_friend_num * sign ELSE 0 END),0) as fan_qie_add,
            coalesce(SUM(CASE WHEN flow_source = 10  THEN page_view_num * sign ELSE 0 END),0) as pango_lin,
            coalesce(SUM(CASE WHEN flow_source = 10 THEN add_friend_num * sign ELSE 0 END),0) as pango_lin_add,

            coalesce(SUM(CASE WHEN flow_source = 50  THEN page_view_num * sign ELSE 0 END),0) as kuai_shou,
            coalesce(SUM(CASE WHEN flow_source = 50 THEN add_friend_num * sign ELSE 0 END),0) as kuai_shou_add,
            coalesce(SUM(CASE WHEN flow_source = 51  THEN page_view_num * sign ELSE 0 END),0) as kuai_shou_lite,
            coalesce(SUM(CASE WHEN flow_source = 51 THEN add_friend_num * sign ELSE 0 END),0) as kuai_shou_lite_add,
            coalesce(SUM(CASE WHEN flow_source = 52  THEN page_view_num * sign ELSE 0 END),0) as kuai_shou_other,
            coalesce(SUM(CASE WHEN flow_source = 52 THEN add_friend_num * sign ELSE 0 END),0) as kuai_shou_other_add,
            coalesce(SUM( page_view_num * sign),0) as total,
            coalesce(SUM( add_friend_num * sign),0) as total_add
        FROM
        landing_page_flow_source_day_statistics
        <where>
            <if test="lprf.startTime != null and lprf.endTime!=null">
                and statistic_date BETWEEN #{lprf.startTime} and #{lprf.endTime}
            </if>
            <if test="lprf.landingPageId != null">
                and landing_page_id = #{lprf.landingPageId}
            </if>
            <if test="lprf.channelId != null">
                and landing_page_channel_id = #{lprf.channelId}
            </if>
        </where>
        having sum(sign) > 0

    </select>

</mapper>
