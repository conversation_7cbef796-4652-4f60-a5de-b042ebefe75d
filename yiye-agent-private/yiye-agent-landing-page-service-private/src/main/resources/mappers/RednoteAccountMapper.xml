<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.yiye.agent.landingpage.mapper.RednoteAccountMapper">

    <insert id="saveOrUpdateByUserId" useGeneratedKeys="true" keyProperty="account.id">
        INSERT INTO landing_page_rednote_account (
        user_id, role_type, corporation_name, app_id, platform_type, scope,
        access_token, access_token_expire_time, refresh_token, refresh_token_expire_time,
        approval_role_type, approval_advertisers, advertiser_id, nick_name, remark,
        is_bind_aurora, auth_status, authorized_at, created_at, updated_at
        )
        VALUES (
        #{account.userId}, #{account.roleType}, #{account.corporationName}, #{account.appId},
        #{account.platformType}, #{account.scope}, #{account.accessToken}, #{account.accessTokenExpireTime},
        #{account.refreshToken}, #{account.refreshTokenExpireTime}, #{account.approvalRoleType},
        #{account.approvalAdvertisers}, #{account.advertiserId}, #{account.nickName}, #{account.remark},
        #{account.isBindAurora}, #{account.authStatus}, NOW(), NOW(), NOW()
        )
        ON CONFLICT (user_id)
        DO UPDATE SET
        role_type = EXCLUDED.role_type,
        corporation_name = EXCLUDED.corporation_name,
        app_id = EXCLUDED.app_id,
        platform_type = EXCLUDED.platform_type,
        scope = EXCLUDED.scope,
        access_token = EXCLUDED.access_token,
        access_token_expire_time = EXCLUDED.access_token_expire_time,
        refresh_token = EXCLUDED.refresh_token,
        refresh_token_expire_time = EXCLUDED.refresh_token_expire_time,
        approval_role_type = EXCLUDED.approval_role_type,
        approval_advertisers = EXCLUDED.approval_advertisers,
        advertiser_id = EXCLUDED.advertiser_id,
        nick_name = EXCLUDED.nick_name,
        remark = EXCLUDED.remark,
        auth_status = EXCLUDED.auth_status,
        authorized_at = EXCLUDED.authorized_at,
        updated_at = NOW();
    </insert>

    <select id="pageRednoteAccounts" resultType="ai.yiye.agent.domain.vo.RednoteAccountPageVO">
        select ra.id, ra.user_id, ra.nick_name, ra.is_bind_aurora, ra.advertiser_id, ra.remark, ra.authorized_at,
        ra.created_at, ra.updated_at
        from landing_page_rednote_account_pmp_rel rapr
        inner join landing_page_rednote_account ra on rapr.rednote_account_id = ra.id
            and rapr.agent_id = #{agentId}
            and rapr.advertiser_account_group_id = #{param.advertiserAccountGroupId}
        order by ra.authorized_at desc
    </select>
</mapper>
