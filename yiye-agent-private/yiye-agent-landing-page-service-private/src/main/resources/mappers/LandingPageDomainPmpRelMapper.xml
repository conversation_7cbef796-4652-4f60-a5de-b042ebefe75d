<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.yiye.agent.landingpage.mapper.LandingPageDomainPmpRelMapper">


    <select id="getOldestDomain" resultType="java.lang.Long">
        select info.id from landing_page_domain_pmp_rel rel
            left join landing_page_domain_binding_info info on rel.landing_page_domain_id=info.id
        where rel.agent_id =#{agentId}
              and rel.advertiser_account_group_id=#{advertiserAccountGroupId} order by info.created_at asc limit 1


    </select>
</mapper>
