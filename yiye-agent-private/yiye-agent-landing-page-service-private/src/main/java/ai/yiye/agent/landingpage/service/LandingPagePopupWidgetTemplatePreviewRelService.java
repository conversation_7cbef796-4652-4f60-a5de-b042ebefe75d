package ai.yiye.agent.landingpage.service;

import ai.yiye.agent.domain.LandingPagePopupWidgetTemplatePreviewRel;
import ai.yiye.agent.domain.enumerations.OrderActionType;
import ai.yiye.agent.domain.enumerations.WidgetTemplateType;
import ai.yiye.agent.domain.enumerations.WidgetType;
import ai.yiye.agent.landingpage.mapper.LandingPagePopupWidgetTemplatePreviewRelMapper;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @program: yiye-agent-backend
 * @description: 落地页与组件模板关系service
 * @author: x<PERSON><PERSON>an
 * @create: 2020-06-12 10:35
 **/
@Service
@DS("postgresql")
public class LandingPagePopupWidgetTemplatePreviewRelService extends ServiceImpl<LandingPagePopupWidgetTemplatePreviewRelMapper, LandingPagePopupWidgetTemplatePreviewRel> {

    @Autowired
    private LandingPageWidgetTemplateService landingPageWidgetTemplateService;

    public void saveLandingPageWidgetTemplateRel(String previewToken, Long widgetTemplateId, JSONArray children) {
        children.forEach(a -> {
            JSONObject jsonObject = (JSONObject) a;
            JSONObject model = jsonObject.getJSONObject("model");

            String name = jsonObject.getString("name");
            LandingPagePopupWidgetTemplatePreviewRel landingPageWidgetTemplateRel = new LandingPagePopupWidgetTemplatePreviewRel();
            String uuid = jsonObject.getString("id");
            landingPageWidgetTemplateRel.setUuid(uuid);
            landingPageWidgetTemplateRel.setPreviewToken(previewToken)
                .setLandingPagePopupWidgetTemplateId(widgetTemplateId);

            Long formId = null;
            //保存 表单 拼接组件 落地页 的关系
            switch (name) {
                case "Form":
                    landingPageWidgetTemplateRel.setWtType(WidgetTemplateType.FORM_TYPE);
                    landingPageWidgetTemplateRel.setWidgetType(WidgetType.SPLICE);
                    formId = model.getLong("form");
                    break;
                case "OrderWidget":
                    landingPageWidgetTemplateRel.setWtType(WidgetTemplateType.ORDER_TYPE);
                    landingPageWidgetTemplateRel.setWidgetType(WidgetType.SPLICE);
                    formId = model.getLong("orderTemplate");
                    // 提取拼接组件内容与表单订单的关系
                    break;
                case "LogicalAtlas":
                    landingPageWidgetTemplateRel.setWtType(WidgetTemplateType.LOGICAL_ATLAS_TYPE);
                    landingPageWidgetTemplateRel.setWidgetType(WidgetType.SPLICE);
                    if (model.getString("logicalAtlasType").equals("template")) {
                        formId = Long.parseLong(model.getString("logicalAtlas"));
                    }
                    // 提取拼接组件内容与表单订单的关系
                    break;
            }

            if (Objects.nonNull(formId) && Objects.nonNull(landingPageWidgetTemplateService.getById(formId))) {
                landingPageWidgetTemplateRel.setLandingPageWidgetTemplateId(formId);
                // onFinish 0 跳转地址url  1 文字提示
                String actionType = model.getString("onFinish");
                landingPageWidgetTemplateRel.setActionType(actionType);
                if (OrderActionType.HINT.type().equals(actionType)) {
                    landingPageWidgetTemplateRel.setActionName(model.getString("hint"));
                } else if (OrderActionType.URL.type().equals(actionType)) {
                    landingPageWidgetTemplateRel.setActionTarget(model.getString("url"));
                }
                landingPageWidgetTemplateRel.setLandingPageWidgetTemplateId(formId);
                this.save(landingPageWidgetTemplateRel);
            }
        });
    }
}
