package ai.yiye.agent.landingpage.service;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.upload.FileUpload;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.*;
import ai.yiye.agent.domain.dto.*;
import ai.yiye.agent.domain.dto.platform.WechatCustomerServiceGroupDto;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.landingpage.*;
import ai.yiye.agent.domain.landingpage.dto.OfficialWechatCustomerContactDto;
import ai.yiye.agent.domain.pageview.PageViewInfo;
import ai.yiye.agent.domain.utils.CollectionUtil;
import ai.yiye.agent.domain.utils.UrlUtils;
import ai.yiye.agent.landingpage.config.IdentifyQrcodeCacheConfig;
import ai.yiye.agent.landingpage.config.LandingPageNewSearchConfig;
import ai.yiye.agent.landingpage.config.LandingPageWechatCustomerContactConfig;
import ai.yiye.agent.landingpage.config.WorkWechatDevelopConf;
import ai.yiye.agent.landingpage.contants.LandingPageConstant;
import ai.yiye.agent.landingpage.controller.vo.EnterpriseWechatAddContactRecordVO;
import ai.yiye.agent.landingpage.dto.*;
import ai.yiye.agent.landingpage.enums.*;
import ai.yiye.agent.landingpage.mapper.LandingPageWechatCustomerServiceMapper;
import ai.yiye.agent.landingpage.redis.AbnormalMonitorRedis;
import ai.yiye.agent.landingpage.redis.LandingPageWechatCustomerServiceRedis;
import ai.yiye.agent.landingpage.sender.*;
import ai.yiye.agent.landingpage.service.clickhouse.LandingPageWechatCustomerServiceClickhouseService;
import ai.yiye.agent.landingpage.service.readonly.CustomerReadOnlyService;
import ai.yiye.agent.landingpage.service.readonly.LandingPageWechatCustomerServiceReadonlyService;
import ai.yiye.agent.landingpage.service.readonly.OfficialAccountSendMessageRecordReadOnlyService;
import ai.yiye.agent.landingpage.service.readonly.PageViewInfoPgReadonlyService;
import ai.yiye.agent.landingpage.utils.CsvUtil;
import ai.yiye.agent.landingpage.utils.TimeUtils;
import ai.yiye.agent.landingpage.vo.ContactRemoteClearVO;
import ai.yiye.agent.landingpage.vo.WechatCustomerServiceSaveBatchVO;
import ai.yiye.agent.weixin.client.WorkWeixinApiClient;
import ai.yiye.agent.weixin.domain.ProviderTokenResposeBody;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static ai.yiye.agent.autoconfigure.web.exception.ErrorConstants.*;
import static ai.yiye.agent.landingpage.utils.Constants.FILE_UPLOAD;
import static java.util.regex.Pattern.compile;

@Slf4j
@Service
public class LandingPageWechatCustomerServiceService extends ServiceImpl<LandingPageWechatCustomerServiceMapper, LandingPageWechatCustomerService> {

    /**
     * 成功
     */
    public final Integer PASSCODE = 200;

    /**
     * 直接提示错误，关闭轮询框
     */
    public final Integer NOTPASSCODE = 50000;

    /**
     * 打开弹窗，可以选择操作：覆盖，跳过
     */
    public final Integer POPCODE = 50001;

    /**
     * 关闭弹窗，继续轮询（正在处理中）
     */
    public final Integer PROCESSING = 50002;

    public final String PASSSTRING = "成功";

    private static final Integer ONE_DAY = 1;

    private static final String PREFIX_CERTIFICATE = "landing-page/wechat-customer";
    private static final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Value("${landing-page.wechatCustomerService.page.maxSize:10000}")
    private Long pageMaxSize;   //size最大值

    @Autowired
    private LandingPageWechatCustomerServiceRedis landingPageWechatCustomerServiceRedis;
    @Autowired
    private LandingPageWechatCustomerServiceGroupRelService landingPageWechatCustomerServiceGroupRelService;
    @Autowired
    private PageViewInfoPgService pageViewInfoPgService;
    @Autowired
    private PageViewInfoPgReadonlyService pageViewInfoPgReadonlyService;
    @Autowired
    protected FileUpload fileUpload;
    @Autowired
    private WechatUploadRecordService wechatUploadRecordService;
    @Autowired
    private UploadSender uploadSender;
    @Resource
    private LandingPageWechatCustomerServiceAutoRuleService landingPageWechatCustomerServiceAutoRuleService;
    @Resource
    private IdentifyQrcodeRecordService identifyQrcodeRecordService;
    @Autowired
    private LandingPageWechatCustomerServiceService landingPageWechatCustomerServiceService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private CustomerReadOnlyService customerReadOnlyService;

    @Resource
    private OfficialAccountSendMessageRecordReadOnlyService officialAccountSendMessageRecordReadOnlyService;
    @Autowired
    private EnterpriseWechatService enterpriseWechatService;
    @Autowired
    private EnterpriseWechatsPmpRelService enterpriseWechatsPmpRelService;
    @Autowired
    private RedisTemplate<String, Object> objectRedisTemplate;

    @Autowired
    private RedisTemplate<String, Object> defaultObjectRedisTemplate;

    @Autowired
    private IdentifyQrcodeCacheConfig identifyQrcodeCacheConfig;

    @Autowired
    private EnterpriseWechatCustomerEventSender enterpriseWechatCustomerEventSender;

    @Autowired
    private LandingPageNewSearchConfig landingPageNewSearchConfig;


    @Autowired
    private EnterpriseWechatCustomerAcquisitionSender enterpriseWechatCustomerAcquisitionSender;

    @Autowired
    private UserOperationLogDetailActionSender userOperationLogDetailActionSender;

    @Autowired
    private LandingPageWechatCustomerContactSender landingPageWechatCustomerContactSender;

    @Resource
    private TrafficEngineCustomerServiceOperationSender trafficEngineCustomerServiceOperationSender;

    @Resource
    private LandingPageWechatCustomerContactConfig landingPageWechatCustomerContactConfig;

    @Resource
    private LandingPageWechatCustomerServiceAbnormalMonitorService landingPageWechatCustomerServiceAbnormalMonitorService;

    @Resource
    private AbnormalMonitorRedis abnormalMonitorRedis;

    @Resource
    private WorkWechatCustomerAcquisitionLinkService workWechatCustomerAcquisitionLinkService;

    @Resource
    private WorkWechatCustomerSensitiveInfoService workWechatCustomerSensitiveInfoService;

    @Autowired
    private LandingPageWorkWechatUserVisibleRangeService landingPageWorkWechatUserVisibleRangeService;

    @Resource
    private WorkWechatCustomerLicenseInfoService workWechatCustomerLicenseInfoService;

    @Resource
    private WorkWechatUserVisibleRangeCustomerContactService workWechatUserVisibleRangeCustomerContactService;

    @Resource
    private LandingPageSender landingPageSender;

    @Autowired
    private LandingPageWechatCustomerServiceReadonlyService landingPageWechatCustomerServiceReadonlyService;

    @Resource
    private OfficialCustomerContactSender officialCustomerContactSender;

    @Resource
    private EnterpriseWechatCustomerAcquisitionService enterpriseWechatCustomerAcquisitionService;

    @Resource
    private LandingPageWechatOfficialMaterialFileService landingPageWechatOfficialMaterialFileService;

    @Resource
    private WorkWechatUserVisibleRangeOfficialCustomerContactService workWechatUserVisibleRangeOfficialCustomerContactService;

    @Resource
    private LandingPageWechatCustomerServiceContactMeQRCodeRecordService landingPageWechatCustomerServiceContactMeQRCodeRecordService;

    @Resource
    private LandingPageWechatCustomerContactService landingPageWechatCustomerContactService;

    @Resource
    private LandingPageWechatCustomerContactPublicService landingPageWechatCustomerContactPublicService;

    @Resource
    private MultiplayerCodeSender multiplayerCodeSender;

    @Resource
    private OfficialWechatCustomerServiceUserService officialWechatCustomerServiceUserService;

    @Resource
    private OfficialWechatCustomerContactService officialWechatCustomerContactService;

    @Resource
    private RobotCustomerContactSender robotCustomerContactSender;

    @Resource
    private RobotDynamicCustomerContactGenerateSender robotDynamicCustomerContactGenerateSender;

    @Resource
    private LandingPageCustomerServiceSender landingPageCustomerServiceSender;

    @Autowired
    private LandingPageWechatCustomerServiceClickhouseService landingPageWechatCustomerServiceClickhouseService;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    protected WorkWechatDevelopConf workWechatDevelopConf;

    @Autowired
    private PageViewinfoSender pageViewinfoSender;

    @Resource
    private EnterpriseTempMaterialService enterpriseTempMaterialService;

    @Resource
    private LandingPageQrCodeClearMarkService landingPageQrCodeClearMarkService;


    @Resource
    private AgentConfService agentConfService;


    @Resource
    private WorkWeixinApiClient workWeixinApiClient;

    @Resource
    private EnterpriseWechatDevelopOauthService enterpriseWechatDevelopOauthService;

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_FORMAT).withZone(ZoneId.systemDefault());


    private static final List<MakeNonAdSourceCustomerTagEventType> ACQUISITION_LINK_ADD_LIST = Arrays.asList(
        MakeNonAdSourceCustomerTagEventType.CUSTOMER_ACQUISITION_LINK,
        MakeNonAdSourceCustomerTagEventType.ADD_THROUGH_CUSTOMER_ACQUISITION_LINK_STATE_ILLEGAL,
        MakeNonAdSourceCustomerTagEventType.ADD_THROUGH_CUSTOMER_ACQUISITION_LINK_WITHOUT_STATE
    );

    /**
     * 微信客服管理列表 - 分页查询
     */
    public IPage<LandingPageWechatCustomerService> getWechatCustomerServicePageList(IPage<LandingPageWechatCustomerService> page, LandingPageWechatCustomerServicePageDTO lpwcspDto) {
        if (StringUtils.isBlank(lpwcspDto.getStartTime()) || StringUtils.isBlank(lpwcspDto.getEndTime())) {
            return null;
        }
        List<String> userIdList = new ArrayList<>();
        if (!Objects.isNull(lpwcspDto.getSearchType())) {
            if (LandingPageWechatCustomerServiceSerchType.NAME.equals(lpwcspDto.getSearchType()) && org.springframework.util.CollectionUtils.isEmpty(lpwcspDto.getUserNames())) {
                return page;
            }
            if (!org.springframework.util.CollectionUtils.isEmpty(lpwcspDto.getUserPhones()) && LandingPageWechatCustomerServiceSerchType.PHONE.equals(lpwcspDto.getSearchType())) {
                List<WorkWechatCustomerSensitiveInfo> list = workWechatCustomerSensitiveInfoService.getListByUserPhones(lpwcspDto.getUserPhones());
                if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
                    userIdList.addAll(list.stream().map(WorkWechatCustomerSensitiveInfo::getUserId).collect(Collectors.toList()));
                }
                if (CollectionUtils.isEmpty(userIdList)) {
                    return page;
                }
            }
        }
        IPage<LandingPageWechatCustomerService> wechatCustomerServicePageList = landingPageWechatCustomerServiceReadonlyService.getWechatCustomerServicePageList(page, lpwcspDto, userIdList);
        List<LandingPageWechatCustomerService> records = wechatCustomerServicePageList.getRecords();

        //针对状态的几个字段，需要从主库中读取值，因为主库同步数据到只读库的过程，因为长事务的原因，存在偶发性延迟的情况（线上问题），从主库读取保证是最新的数据
        if (!records.isEmpty()) {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            List<Long> ids = records.stream().map(LandingPageWechatCustomerService::getId).collect(Collectors.toList());

            //传入对应的分组ID时， 根据id集合，去主库里面查询一下客服的分组ID是否存在-1的情况（解决线上将客服移出分组后，由于读写数据库同步延迟导致的，客户还能看到客服没有成功移出分组的现象）
            if (Objects.nonNull(lpwcspDto.getWechatCustomerServiceGroupId()) && !Objects.equals(lpwcspDto.getWechatCustomerServiceGroupId(), -1L)) {
                ids = landingPageWechatCustomerServiceGroupRelService.checkInGroup(ids);
                List<Long> idsTemp = new ArrayList<>(ids);
                records.removeIf(e -> !idsTemp.contains(e.getId()));
            }
            if (!ids.isEmpty()) {
                List<LandingPageWechatCustomerService> landingPageWechatCustomerServiceList = this.lambdaQuery()
                    .in(LandingPageWechatCustomerService::getId, ids).list();
                if (!landingPageWechatCustomerServiceList.isEmpty()) {
                    records = records.stream().map(
                        one -> landingPageWechatCustomerServiceList.stream().filter(e -> Objects.equals(one.getId(), e.getId()))
                            .findFirst()
                            .map(m -> {
                                LandingPageWechatCustomerContactDto customerContactMeQrCodeBackgroundConfig = landingPageWechatCustomerContactService.getCustomerContactMeQrCodeBackgroundConfig(m.getCorpId(), m.getWechatUserId());
                                one.setLandingPageWechatCustomerContactQrCodeBackgroundUrl(customerContactMeQrCodeBackgroundConfig != null ? customerContactMeQrCodeBackgroundConfig.getBackgroundUrl() : null);
                                one.setOnlineStatus(m.getOnlineStatus());
                                one.setAutoRuleStatus(m.getAutoRuleStatus());
                                one.setAbnormalMonitorStatus(m.getAbnormalMonitorStatus());
                                one.setNotSupportAutoOnline(m.getNotSupportAutoOnline());
                                one.setSupportAutoOnlineStatus(m.getSupportAutoOnlineStatus());
                                one.setSupportAutoOnline(m.getSupportAutoOnline());
                                one.setSpecifyOnlineTime(m.getSpecifyOnlineTime());
                                one.setOfflineStatus(m.getOfflineStatus());
                                one.setWechatCustomerAcquisitionLinkStatus(m.getWechatCustomerAcquisitionLinkStatus());
                                one.setLandingPageWechatCustomerContactStatus(m.getLandingPageWechatCustomerContactStatus());
                                one.setOfficialWechatCustomerContactStatus(m.getOfficialWechatCustomerContactStatus());
                                one.setWechatCustomerAcquisitionLinkVerify(m.getWechatCustomerAcquisitionLinkVerify());
                                one.setOfficialWechatCustomerContactVerify(m.getOfficialWechatCustomerContactVerify());
                                one.setRobotCustomerContactStatus(m.getRobotCustomerContactStatus());
                                one.setRobotCustomerContactVerify(m.getRobotCustomerContactVerify());
                                one.setRobotCustomerDynamicContactStatus(m.getRobotCustomerDynamicContactStatus());
                                one.setRobotCustomerDynamicContactVerify(m.getRobotCustomerDynamicContactVerify());
                                one.setQrCodeWeight(m.getQrCodeWeight());
                                one.setWechatUserName(m.getWechatUserName());
                                one.setLicenseExpireTime(m.getLicenseExpireTime());
                                //查询许可证是否即将过期
                                if (Objects.nonNull(m.getLicenseExpireTime())) {
                                    //不到48小时候即将过期
                                    Instant licenseExpireTime = m.getLicenseExpireTime();
                                    Instant now = Instant.now();
                                    if (licenseExpireTime.isAfter(now) && licenseExpireTime.isBefore(now.plus(workWechatDevelopConf.getLicenseExpireTimeHour(), ChronoUnit.HOURS))) {
                                        one.setLicenseStatus(LicenseStatus.ABOUT_TO_EXPIRE);
                                    }
                                }
                                return one;
                            }).orElse(null)
                    ).collect(Collectors.toList());
                    Set<String> userIds = records.stream().map(LandingPageWechatCustomerService::getWechatUserId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(userIds)) {
                        String cuttentAgentId = TenantContextHolder.get();
                        //判断公众号渠道二维码展示状态
                        List<WorkWechatUserVisibleRangeOfficialCustomerContact> list = workWechatUserVisibleRangeOfficialCustomerContactService.list(Wrappers.lambdaQuery(WorkWechatUserVisibleRangeOfficialCustomerContact.class)
                            .in(WorkWechatUserVisibleRangeOfficialCustomerContact::getUserId, userIds));
                        if (CollectionUtils.isNotEmpty(list)) {
                            Map<String, WorkWechatUserVisibleRangeOfficialCustomerContact> contactMap = list.stream().collect(Collectors.toMap(WorkWechatUserVisibleRangeOfficialCustomerContact::getUserId, Function.identity()));
                            records.forEach(m -> {
                                String wechatUserId = m.getWechatUserId();
                                //主要是公众号生成渠道二维码实时记录或者公众号绑定客服记录有数据 都需要走此逻辑
                                if (StringUtils.isNotBlank(wechatUserId)) {
                                    WorkWechatUserVisibleRangeOfficialCustomerContact workWechatUserVisibleRangeOfficialCustomerContact = contactMap.get(wechatUserId);
                                    //已经有账户生成了公众号渠道二维码
                                    if (Objects.nonNull(workWechatUserVisibleRangeOfficialCustomerContact)) {
                                        String agentId = workWechatUserVisibleRangeOfficialCustomerContact.getAgentId();
                                        m.setOfficialProhibitFlag(StringUtils.equals(agentId, cuttentAgentId) ? BaseStatusEnum.ENABLE : BaseStatusEnum.DISABLE);
                                    }
                                }
                            });
                        }

                        Map<String, OfficialWechatCustomerServiceUser> userMap = new HashMap<>();
                        //获取对应的公众号配置记录
                        Set<String> corpids = records.stream().filter(e -> StringUtils.isNotBlank(e.getWechatUserId()))
                            .map(LandingPageWechatCustomerService::getCorpId).collect(Collectors.toSet());
                        //查询公众号信息
                        List<OfficialWechatCustomerServiceUser> officialList = officialWechatCustomerServiceUserService.list(new LambdaQueryWrapper<OfficialWechatCustomerServiceUser>()
                            .select(
                                OfficialWechatCustomerServiceUser::getWechatUserId,
                                OfficialWechatCustomerServiceUser::getAppId,
                                OfficialWechatCustomerServiceUser::getNickName,
                                OfficialWechatCustomerServiceUser::getHeadImg,
                                OfficialWechatCustomerServiceUser::getSubjectType,
                                OfficialWechatCustomerServiceUser::getAgentId)
                            .in(OfficialWechatCustomerServiceUser::getCorpId, corpids)
                            .in(OfficialWechatCustomerServiceUser::getWechatUserId, userIds));
                        if (CollectionUtils.isNotEmpty(officialList)) {
                            userMap = officialList.stream()
                                .collect(Collectors.toMap(OfficialWechatCustomerServiceUser::getWechatUserId, Function.identity()));
                        }
                        Map<String, OfficialWechatCustomerServiceUser> finalUserMap = userMap;
                        records.forEach(m -> {
                            String wechatUserId = m.getWechatUserId();
                            OfficialWechatCustomerServiceUser officialWechatCustomerServiceUser = finalUserMap.get(wechatUserId);
                            //有账户此客服设置了公众号
                            if (Objects.nonNull(officialWechatCustomerServiceUser)) {
                                String agentId = officialWechatCustomerServiceUser.getAgentId();
                                m.setOfficialProhibitFlag(StringUtils.equals(agentId, cuttentAgentId) ? BaseStatusEnum.ENABLE : BaseStatusEnum.DISABLE);
                                if (StringUtils.equals(agentId, cuttentAgentId)) {
                                    String headImg = officialWechatCustomerServiceUser.getHeadImg();
                                    String nickName = officialWechatCustomerServiceUser.getNickName();
                                    String appId = officialWechatCustomerServiceUser.getAppId();
                                    OfficialWechatCustomerSubjectType subjectType = officialWechatCustomerServiceUser.getSubjectType();
                                    m.setOfficialNickName(nickName)
                                        .setOfficialHeadImg(headImg)
                                        .setOfficialAppId(appId)
                                        .setOfficialWechatSubjectType(subjectType);
                                }
                            }
                        });
                    }
                }
                stopWatch.stop();
                log.info("查询客服列表增加字段，遍历状态相关的字段结束 ，遍历的条数{}, 耗时 = {}ms", records.size(), stopWatch.getTotalTimeMillis());
            }
        }

        List<EnterpriseWechatsPmpRel> enterpriseWechatsPmpRelList = enterpriseWechatsPmpRelService.list(new LambdaQueryWrapper<EnterpriseWechatsPmpRel>()
            .eq(EnterpriseWechatsPmpRel::getAdvertiserAccountGroupId, lpwcspDto.getAdvertiserAccountGroupId())
            .eq(EnterpriseWechatsPmpRel::getAgentId, TenantContextHolder.get())
        );

        Map<String, EnterpriseWechat> enterpriseWechatMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(enterpriseWechatsPmpRelList)) {
            Set<Long> enterpriseWechatsIds = enterpriseWechatsPmpRelList.stream()
                .map(EnterpriseWechatsPmpRel::getEnterpriseWechatsId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(enterpriseWechatsIds)) {
                enterpriseWechatsIds = enterpriseWechatsIds.stream()
                    .filter(enterpriseWechatsId -> !Objects.isNull(enterpriseWechatsId)).collect(Collectors.toSet());
            }
            List<EnterpriseWechat> enterpriseWechatList = enterpriseWechatService.list(new LambdaQueryWrapper<EnterpriseWechat>()
                .select(EnterpriseWechat::getCorpid, EnterpriseWechat::getCorpName, EnterpriseWechat::getEnterpriseWechatType, EnterpriseWechat::getCustomRemarks)
                .in(EnterpriseWechat::getId, enterpriseWechatsIds)
            );
            for (EnterpriseWechat enterpriseWechat : enterpriseWechatList) {
                enterpriseWechatMap.put(enterpriseWechat.getCorpid(), enterpriseWechat);
            }
        }
        if (CollectionUtils.isNotEmpty(records)) {
            List<LandingPageWechatCustomerService> objects = new ArrayList<>();
            List<Long> ids = records.stream().map(LandingPageWechatCustomerService::getId).collect(Collectors.toList());
            //获取异常监测
            Map<Long, List<LandingPageWechatCustomerServiceAbnormalMonitor>> abnormalMonitorMap = landingPageWechatCustomerServiceAbnormalMonitorService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAbnormalMonitor>()
                    .in(LandingPageWechatCustomerServiceAbnormalMonitor::getLandingPageWechatCustomerServiceId, ids))
                .stream()
                .collect(Collectors.groupingBy(LandingPageWechatCustomerServiceAbnormalMonitor::getLandingPageWechatCustomerServiceId));

            //获取自动化规则
            Map<Long, List<LandingPageWechatCustomerServiceAutoRule>> autoRuleMap = landingPageWechatCustomerServiceAutoRuleService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>()
                    .in(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, ids)).stream()
                .collect(Collectors.groupingBy(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId));
            //获取配置了userid的客服
            List<String> userIds = records.stream().filter(e -> StringUtils.isNotBlank(e.getWechatUserId()))
                .map(LandingPageWechatCustomerService::getWechatUserId).collect(Collectors.toList());

            Long checkUserId = agentConfService.getCheckUserId(null);
            List<String> groupNames = null;
            if (Objects.nonNull(checkUserId)) {
                //获取可见分组列表
                groupNames = landingPageWechatCustomerServiceGroupRelService.getCustomerServiceGroupNamesByUserId(lpwcspDto.getAdvertiserAccountGroupId(), checkUserId);
            }
            for (int i = 0; i < records.size(); i++) {
                LandingPageWechatCustomerService landingPageWechatCustomerService = records.get(i);
                EnterpriseWechat enterpriseWechat = enterpriseWechatMap.get(landingPageWechatCustomerService.getCorpId());
                if (!Objects.isNull(enterpriseWechat)) {
                    landingPageWechatCustomerService.setEnterpriseWechatType(enterpriseWechat.getEnterpriseWechatType());
                    landingPageWechatCustomerService.setEnterpriseWechatName(enterpriseWechat.getCorpName());
                    landingPageWechatCustomerService.setEnterpriseWechatRemark(enterpriseWechat.getCustomRemarks());
                    landingPageWechatCustomerService.setCorpId(enterpriseWechat.getCorpid());
                }
                if (Objects.equals(landingPageWechatCustomerService.getAddWorkWechatRate(), "#DIV/0!")) {
                    landingPageWechatCustomerService.setAddWorkWechatRate("0.00%");
                }
                String names = landingPageWechatCustomerService.getGroupNames();
                if (StringUtils.isNotBlank(names) && !StringUtils.equals(names, "未分组") && Objects.nonNull(checkUserId)) {
                    try {
                        if (CollectionUtils.isNotEmpty(groupNames)) {
                            //分组名称都不为空 过滤
                            String[] split = names.split("；");
                            if (Objects.nonNull(split)) {
                                List<String> collect = Arrays.stream(split).filter(groupNames::contains).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(collect)) {
                                    landingPageWechatCustomerService.setGroupNames(String.join("；", collect));
                                } else {
                                    //需要过滤权限 且能见分组列表为空 则设置未分组
                                    landingPageWechatCustomerService.setGroupNames("未分组");
                                }
                            }
                        } else {
                            //需要过滤权限 且能见分组列表为空 则设置未分组
                            landingPageWechatCustomerService.setGroupNames("未分组");
                        }

                    } catch (Exception e) {
                        log.error("过滤分组名称异常", e);
                    }
                }
                List<String> abnormalMonitorContent = Lists.newArrayList();
                if (AbnormalMonitorStatus.ENABLE.equals(landingPageWechatCustomerService.getAbnormalMonitorStatus())) {
                    List<LandingPageWechatCustomerServiceAbnormalMonitor> abnormalMonitors = abnormalMonitorMap.get(landingPageWechatCustomerService.getId());
                    if (CollectionUtils.isNotEmpty(abnormalMonitors)) {
                        for (LandingPageWechatCustomerServiceAbnormalMonitor abnormalMonitor : abnormalMonitors) {
                            abnormalMonitorContent.add(LandingPageWechatCustomerServiceAbnormalMonitor.getCompareString(abnormalMonitor));
                        }
                    }
                    String supportAutoOnlineContent = LandingPageWechatCustomerService.getSupportAutoOnlineCompareString(landingPageWechatCustomerService);
                    if (StringUtils.isNotBlank(supportAutoOnlineContent)) {
                        landingPageWechatCustomerService.setSupportAutoOnlineContent(supportAutoOnlineContent.replace("（", "").replace("）", ""));
                    }
                }
                landingPageWechatCustomerService.setAbnormalMonitorContent(abnormalMonitorContent);

                //设置自动化规则的代码
                if (AutoRuleStatus.ENABLE.equals(landingPageWechatCustomerService.getAutoRuleStatus())) {

                    List<LandingPageWechatCustomerServiceAutoRule> oldList = autoRuleMap.get(landingPageWechatCustomerService.getId());
                    List<String> autoRuleList = new ArrayList<>();
                    oldList.sort((o1, o2) -> {
                        if (AutoRuleType.POINT_OF_TIME.equals(o1.getType()) && !AutoRuleType.POINT_OF_TIME.equals(o2.getType())) {
                            return -1;
                        }
                        if (!AutoRuleType.POINT_OF_TIME.equals(o1.getType()) && AutoRuleType.POINT_OF_TIME.equals(o2.getType())) {
                            return 1;
                        }
                        // 其他对象按照属性升序排列
                        return Integer.compare(o1.getType().getId(), o2.getType().getId());
                    });
                    for (LandingPageWechatCustomerServiceAutoRule landingPageWechatCustomerServiceAutoRule : oldList) {
                        String compareString = LandingPageWechatCustomerServiceAutoRule.getCompareStringWithdrawZero(landingPageWechatCustomerServiceAutoRule);
                        if (StringUtils.isNotBlank(compareString)) {
                            autoRuleList.add(compareString);
                        }
                    }
                    landingPageWechatCustomerService.setAutoRuleContent(autoRuleList);
                }

                objects.add(landingPageWechatCustomerService);
            }
            wechatCustomerServicePageList.setRecords(objects);
        }

        return wechatCustomerServicePageList;
    }

    /**
     * 分页查询
     */
    public IPage<LandingPageWechatCustomerService> pageList(IPage<LandingPageWechatCustomerService> page,
                                                            LandingPageWechatCustomerServicePageDTO lpwcspDto) {
        return getWechatCustomerServicePageList(page, lpwcspDto);
    }

    public LandingPageWechatCustomerService totalList(LandingPageWechatCustomerServicePageDTO lpwcspDto) {
        lpwcspDto.initTime();
        IPage<LandingPageWechatCustomerService> page = new Page<>();
        page.setSize(-1);

        Long userId = agentConfService.getCheckUserId(null);

        //新版本查询
        // return this.commonQueryTotalNew(page, lpwcspDto);
        // false -旧版本查询; true-新版本查询
        boolean openFlag = checkFlag(lpwcspDto.getNewSearchFlag());
        log.info("微信客服列表统计查询,新旧查询标识 newSearchFlag={},校验结果:{}", lpwcspDto.getNewSearchFlag(), openFlag);
        lpwcspDto.setUserId(userId);
        if (openFlag) {
            log.info("微信客服列表查询,走新查询，标识:{}", lpwcspDto.getNewSearchFlag());
            return this.commonQueryTotalNew(lpwcspDto);
        } else {
            return this.commonQueryTotal(page, userId, lpwcspDto);
        }
    }

    /**
     * 判断新旧查询开关的标识
     *
     * @param newSearchFlag 入参
     * @return true标识新查询，false走旧查询
     */
    public Boolean checkFlag(String newSearchFlag) {
        if (StringUtils.isBlank(newSearchFlag)) {
            return landingPageNewSearchConfig.getWechatCustomerNewSearch();
        } else {
            return Objects.equals(LandingPageConstant.newSearchFlag, newSearchFlag);
        }
    }

    /**
     * 新版本 通用查询总计
     */
    public LandingPageWechatCustomerService commonQueryTotalNew(LandingPageWechatCustomerServicePageDTO lpwcspDto) {
        return landingPageWechatCustomerServiceClickhouseService.commonQueryTotalNew(lpwcspDto);
    }

    /**
     * 通用查询总计
     */
    public LandingPageWechatCustomerService commonQueryTotal(IPage<LandingPageWechatCustomerService> page, Long userId,
                                                             LandingPageWechatCustomerServicePageDTO lpwcspDto) {
        landingPageWechatCustomerServiceReadonlyService.pageList(page, userId, lpwcspDto);
        if (!page.getRecords().isEmpty()) {
            List<LandingPageWechatCustomerService> records = page.getRecords();
            List<String> idList = records.stream().map(LandingPageWechatCustomerService::getId).map(String::valueOf)
                .collect(Collectors.toList());
            List<String> userIdList = records.stream().map(LandingPageWechatCustomerService::getWechatUserId)
                .collect(Collectors.toList());

            List<Long> lpwcsIds = records.stream().map(LandingPageWechatCustomerService::getId)
                .collect(Collectors.toList());
            List<String> wechatUserIds = records.stream()
                .filter(service -> StringUtils.isNotBlank(service.getWechatUserId()))
                .map(LandingPageWechatCustomerService::getWechatUserId).collect(Collectors.toList());
            //二维码长按识别数
            LandingPageWechatCustomerService lpwcspTotal = pageViewInfoPgReadonlyService.sumWechatCustomerServiceDataTotal(lpwcsIds, lpwcspDto.getStartTime(), lpwcspDto.getEndTime());
            //其他来源添加企业微信数
            EnterpriseWechatAddContactRecordVO ewacrVo = customerReadOnlyService.countAddWechatServiceSuccessNumTotal(wechatUserIds, lpwcspDto.getStartTime(), lpwcspDto.getEndTime(), lpwcspDto.getAdvertiserAccountGroupId());
            //二维码长按识别数
            Long identifyQrCodeNum = lpwcspTotal.getIdentifyQrCodeNum() == null ? 0L : lpwcspTotal.getIdentifyQrCodeNum();

            //公众号内二维码加粉数
            Long officialAccountQrCodeAddWorkWechatNum = customerReadOnlyService.countOfficialAccountQrCodeAddFriendNum(wechatUserIds, lpwcspDto.getStartTime(), lpwcspDto.getEndTime(), lpwcspDto.getAdvertiserAccountGroupId());

            //公众号内发送二维码次数
            Long officialAccountQrCodeSendNum = officialAccountSendMessageRecordReadOnlyService.countOfficialAccountQrCodeSendNum(lpwcsIds, lpwcspDto.getStartTime(), lpwcspDto.getEndTime(), lpwcspDto.getAdvertiserAccountGroupId());

            //一叶页面添加企业微信数
            Integer landAddWorkWechatNum = customerReadOnlyService.countLandAddWorkWechatNumTotal(idList, userIdList, lpwcspDto.getStartTime(), lpwcspDto.getEndTime(), lpwcspDto.getAdvertiserAccountGroupId());
            //二维码展示数
            LandingPageWechatCustomerService totalQrcodeShowNum = pageViewInfoPgReadonlyService.sumQrcodeShowTotal(lpwcsIds, lpwcspDto.getStartTime(), lpwcspDto.getEndTime());
            //其他来源添加企业微信数
            Long otherAddWorkWechatNum = ewacrVo.getNum() == null ? 0L : ewacrVo.getNum();
            //成功添加企业微信数 = 一叶页面添加企业微信数 + 其他来源添加企业微信数
            Long addWorkWechatNum = landAddWorkWechatNum + otherAddWorkWechatNum;
            //成功添加企业微信率 = ( 成功添加企业微信数 / 二维码长按识别数 ）* 100%
            String addWorkWechatRate;
            //成功添加企业微信率（数值、用作排序）
            BigDecimal addWorkWechatRateNum;
            if (identifyQrCodeNum == 0L) {
                //分母为0
                if (addWorkWechatNum == 0L) {
                    //分子为0
                    addWorkWechatRate = "0.00%";
                    addWorkWechatRateNum = new BigDecimal("0");
                } else {
                    //分子不为0
                    addWorkWechatRate = "0.00%";
                    addWorkWechatRateNum = new BigDecimal("0");
                }
            } else {
                Long coefficient = 100L;
                //分母不为0
                addWorkWechatRateNum = NumberUtil.div(NumberUtil.mul(addWorkWechatNum, coefficient), identifyQrCodeNum, 2);
                addWorkWechatRate = addWorkWechatRateNum + "%";
            }

            Long qrCodeShowNum = totalQrcodeShowNum.getQrCodeShowNum() == null ? 0L : totalQrcodeShowNum.getQrCodeShowNum();
            //二维码长按识别率 二维码长按识别数/二维码展示数*100%
            String qrcodeIdentifyRate;
            //成功添加企业微信率（数值、用作排序）
            BigDecimal qrcodeIdentifyRateNum;
            if (qrCodeShowNum == 0L) {
                //分母为0
                if (identifyQrCodeNum == 0L) {
                    //分子为0
                    qrcodeIdentifyRate = "0.00%";
                    qrcodeIdentifyRateNum = new BigDecimal("0");
                } else {
                    //分子不为0
                    qrcodeIdentifyRate = "0.00%";
                    qrcodeIdentifyRateNum = new BigDecimal("0");
                }
            } else {
                Long coefficient = 100L;
                //分母不为0
                qrcodeIdentifyRateNum = NumberUtil.div(NumberUtil.mul(identifyQrCodeNum, coefficient), qrCodeShowNum, 2);
                qrcodeIdentifyRate = qrcodeIdentifyRateNum + "%";
            }


            lpwcspTotal.setIdentifyQrCodeNum(identifyQrCodeNum)
                .setTotal(Long.valueOf(page.getRecords().size()))
                .setLandAddWorkWechatNum(Long.valueOf(landAddWorkWechatNum))
                .setOtherAddWorkWechatNum(otherAddWorkWechatNum)
                .setAddWorkWechatNum(addWorkWechatNum)
                .setAddWorkWechatRate(addWorkWechatRate)
                //.setAddWorkWechatRateNum(addWorkWechatRateNum)
                .setQrCodeShowNum(qrCodeShowNum)
                .setOfficialAccountQrCodeAddWorkWechatNum(officialAccountQrCodeAddWorkWechatNum)
                .setOfficialAccountQrCodeSendNum(officialAccountQrCodeSendNum)
                .setQrcodeIdentityRate(qrcodeIdentifyRate);
            return lpwcspTotal;
        }
        return null;
    }

    /**
     * 新增
     */
    public LandingPageWechatCustomerService saveData(String agentId, LandingPageWechatCustomerService lpwcs) {
        lpwcs.setId(null)
            .setWechatUserId(StringUtils.isBlank(StringUtils.trim(lpwcs.getWechatUserId())) ? null : lpwcs.getWechatUserId());
        if (StringUtils.isNotBlank(StringUtils.trim(lpwcs.getWechatUserId()))) {
            checkCorpId(lpwcs.getCorpId());
        } else {
            lpwcs.setCorpId(null);
        }
        lpwcs.setOnlineActionStatus(OnlineActionStatus.AUTO);
        lpwcs.setOnlineStatus(OnlineStatusType.DISABLE);
        this.checkRepeatNameUserIdCount(lpwcs); //校验名称和userid是否重复
        this.handleWechatCustomerServiceOnOffLinePointOfTime(lpwcs);
        this.setAcquisitionLinkInfo(lpwcs); //企微获客链接
        this.setSensitiveInfo(lpwcs); //设置企微客服敏感信息
        this.setLicenseInfo(lpwcs); //设置企微客服接口许可信息
        this.setCustomerContactInfo(lpwcs); //企微渠道动态二维码
        this.handleContactMeQrCode(null, lpwcs.getQrCodeConfigRecordId());
        this.save(lpwcs);            //新增数据
        this.createRelData(lpwcs);   //删除 & 创建关联
        this.saveAutoRule(lpwcs);    //保存自动化规则
        this.saveAbnormalMonitor(lpwcs);    //保存异常监测

        //发送客服分组客服变更通知
        landingPageCustomerServiceSender.sendCallBackMessage(new WechatCustomerServiceGroupDto().setWechatCustomerServiceIds(Sets.newHashSet(lpwcs.getId())));
        //新增客服通知互动广告，互动广告侧会筛选分组是否在白名单内
        if (StringUtils.isNotBlank(lpwcs.getWechatCustomerServiceGroupIds())) {
            List<String> ids = Arrays.asList(lpwcs.getWechatCustomerServiceGroupIds().split(","));
            if (ids.size() > 0) {
                List<Long> groupIds = ids.stream().map(e -> Convert.toLong(e)).collect(Collectors.toList());
                CustomerServiceOperationMessage onLineMessage = new CustomerServiceOperationMessage();
                onLineMessage.setCustomerServiceOperation(CustomerServiceOperation.ADD)
                    .setAgentId(agentId)
                    .setLandingPageWechatCustomerService(lpwcs)
                    .setLandingPageCustomerServiceGroupIds(groupIds);
                trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(onLineMessage);
                //发送变更项目客服分组多人活码队列
                CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
                dto.setAgentId(agentId)
                    .setType(CustomerServiceChangeEnum.EDIT_CUSTOMER_GROUP)
                    .setLandingPageCustomerServiceGroupIds(new HashSet<>(groupIds));
                multiplayerCodeSender.sendCustomerServiceChange(dto);

                //异步进行自定义排序编号处理
                landingPageCustomerServiceSender.sendMessageChangeOrderNum(new LandingPageWechatCustomerServiceGroupChangeDTO()
                    .setAdvertiserAccountGroupId(lpwcs.getAdvertiserAccountGroupId())
                    .setGroupChangeEventType(GroupChangeEventType.ADD_WECHAT_CUSTOMER_SERVICE)
                    .setNewLandingPageWechatCustomerServiceGroupIds(groupIds)
                    .setLandingPageWechatCustomerServiceId(Collections.singletonList(lpwcs.getId())));
            }
        }
        if (StringUtils.isNotBlank(lpwcs.getWechatUserId())) {
            abnormalMonitorRedis.reset(lpwcs.getWechatUserId(), lpwcs.getAdvertiserAccountGroupId(), LocalDateTime.now());
        }
        //用户自主上传图片的情况，进行素材上传，方便后续客服机器人发送固定客服二维码
        if (StringUtils.isNotBlank(lpwcs.getQrCodeImgUrl()) && Objects.equals(lpwcs.getQrCodeType(), LandingPageWechatCustomerServiceQRCodeType.IMAGE_UPLOAD)) {
            log.info("新增客服，保存固定二维码图片, lpwcs = {}", JSONObject.toJSONString(lpwcs));
            this.cacheQrCodeImg(lpwcs);
        }

        return lpwcs;
    }

    //设置企微获客链接信息
    private void setAcquisitionLinkInfo(LandingPageWechatCustomerService lpwcs) {
        if (StringUtils.isNotBlank(StringUtils.trim(lpwcs.getWechatUserId())) && StringUtils.isNotBlank(StringUtils.trim(lpwcs.getCorpId()))) {
            WorkWechatCustomerAcquisitionLink acquisitionLink = workWechatCustomerAcquisitionLinkService.getLink(lpwcs.getWechatUserId(), lpwcs.getCorpId());
            if (acquisitionLink != null) {
                lpwcs.setWechatCustomerAcquisitionLinkId(acquisitionLink.getWechatCustomerAcquisitionLinkId())
                    .setWechatCustomerAcquisitionLink(acquisitionLink.getWechatCustomerAcquisitionLink())
                    .setWechatCustomerAcquisitionLinkStatus(acquisitionLink.getWechatCustomerAcquisitionLinkStatus())
                    .setWechatCustomerAcquisitionLinkReason(acquisitionLink.getWechatCustomerAcquisitionLinkReason())
                    .setWechatCustomerAcquisitionLinkVerify(acquisitionLink.getWechatCustomerAcquisitionLinkVerify())
                    .setWechatCustomerAcquisitionLinkName(acquisitionLink.getWechatCustomerAcquisitionLinkName())
                    .setAcquisitionChooseEnum(acquisitionLink.getAcquisitionChooseEnum());
            } else {
                landingPageSender.sendAcquisitionLink(new WorkWechatCustomerAcquisitionLinkDTO()
                    .setCorpId(lpwcs.getCorpId())
                    .setUserId(lpwcs.getWechatUserId())
                    .setWechatCustomerAcquisitionLinkStatus(WechatCustomerAcquisitionLinkStatus.EMPTY)
                    .setWechatCustomerAcquisitionLinkVerify(SwitchStatus.CLOSE));
            }
        }
    }

    //设置企微客服敏感信息
    private void setSensitiveInfo(LandingPageWechatCustomerService lpwcs) {
        if (StringUtils.isNotBlank(StringUtils.trim(lpwcs.getWechatUserId())) && StringUtils.isNotBlank(StringUtils.trim(lpwcs.getCorpId()))) {
            WorkWechatCustomerSensitiveInfo sensitiveInfo = workWechatCustomerSensitiveInfoService.getSensitiveInfo(lpwcs.getWechatUserId(), lpwcs.getCorpId());
            if (sensitiveInfo != null) {
                lpwcs.setWechatMobile(sensitiveInfo.getWechatMobile())
                    .setWechatEmail(sensitiveInfo.getWechatEmail());
            }
        }
    }

    //设置企微客服接口许可信息
    private void setLicenseInfo(LandingPageWechatCustomerService lpwcs) {
        if (StringUtils.isNotBlank(StringUtils.trim(lpwcs.getWechatUserId())) && StringUtils.isNotBlank(StringUtils.trim(lpwcs.getCorpId()))) {
            WorkWechatCustomerLicenseInfo licenseInfo = workWechatCustomerLicenseInfoService.getLicenseInfo(lpwcs.getWechatUserId(), lpwcs.getCorpId());
            if (licenseInfo != null) {
                lpwcs.setLicenseStatus(licenseInfo.getLicenseStatus())
                    .setLicenseExpireTime(licenseInfo.getLicenseExpireTime());
            }
        }
    }

    //设置页面加粉渠道二维码生成状态及公众号渠道二维码
    private void setCustomerContactInfo(LandingPageWechatCustomerService lpwcs) {
        if (StringUtils.isNotBlank(StringUtils.trim(lpwcs.getWechatUserId())) && StringUtils.isNotBlank(StringUtils.trim(lpwcs.getCorpId()))) {
            WorkWechatUserVisibleRangeCustomerContact rangeCustomerContact = workWechatUserVisibleRangeCustomerContactService.getOneByCorpIdAndUserId(lpwcs.getCorpId(), lpwcs.getWechatUserId());
            if (Objects.nonNull(rangeCustomerContact)) {
                lpwcs.setLandingPageWechatCustomerContactVerify(rangeCustomerContact.getLandingPageWechatCustomerContactVerify())
                    .setLandingPageWechatCustomerContactStatus(rangeCustomerContact.getLandingPageWechatCustomerContactStatus())
                    .setLandingPageWechatCustomerContactConfigId(rangeCustomerContact.getLandingPageWechatCustomerContactConfigId())
                    .setLandingPageWechatCustomerContactQrCode(rangeCustomerContact.getLandingPageWechatCustomerContactQrCode())
                    .setLandingPageWechatCustomerContactFailureReason(rangeCustomerContact.getLandingPageWechatCustomerContactFailureReason());
            }
            WorkWechatUserVisibleRangeOfficialCustomerContact officialCustomerContact = workWechatUserVisibleRangeOfficialCustomerContactService.getOneByCorpIdAndUserId(lpwcs.getCorpId(), lpwcs.getWechatUserId());
            if (Objects.nonNull(officialCustomerContact)) {
                String agentId = TenantContextHolder.get();
                String contactAgentId = officialCustomerContact.getAgentId();
                if (StringUtils.isNoneBlank(agentId, contactAgentId) && StringUtils.equals(agentId, contactAgentId)) {
                    //agentId一致 说明二维码是由当前账户生成的 公众号二维码的同步只在同一账户内
                    lpwcs.setOfficialWechatCustomerContactState(officialCustomerContact.getOfficialWechatCustomerContactState())
                        .setOfficialWechatCustomerContactStatus(officialCustomerContact.getOfficialWechatCustomerContactStatus())
                        .setOfficialWechatCustomerContactQrCode(officialCustomerContact.getOfficialWechatCustomerContactQrCode())
                        .setOfficialWechatCustomerContactConfigId(officialCustomerContact.getOfficialWechatCustomerContactConfigId())
                        .setOfficialWechatCustomerContactBackgroundUrl(officialCustomerContact.getOfficialWechatCustomerContactBackgroundUrl())
                        .setOfficialWechatCustomerContactMaterialId(officialCustomerContact.getOfficialWechatCustomerContactMaterialId())
                        .setOfficialWechatCustomerContactQiniuPath(officialCustomerContact.getOfficialWechatCustomerContactQiniuPath())
                        .setOfficialWechatCustomerSubjectType(officialCustomerContact.getOfficialWechatCustomerSubjectType())
                        .setOfficialWechatCustomerContactAppId(officialCustomerContact.getOfficialWechatCustomerContactAppId())
                        .setOfficialWechatCustomerContactAppNickName(officialCustomerContact.getOfficialWechatCustomerContactAppNickName())
                        .setOfficialWechatCustomerContactVerify(officialCustomerContact.getOfficialWechatCustomerContactVerify())
                        .setOfficialWechatCustomerContactFailureReason(officialCustomerContact.getOfficialWechatCustomerContactFailureReason());
                }

            }
        }
    }

    /**
     * 名称和userid重复校验
     */
    public Integer checkRepeatNameUserIdCount(LandingPageWechatCustomerService landingPageWechatCustomerService) {
        if (null == landingPageWechatCustomerService) {
            return 9999;
        }
        int num;
        //微信客服名称不能重复
        if (null == landingPageWechatCustomerService.getId()) {
            num = this.count(new LambdaQueryWrapper<LandingPageWechatCustomerService>()
                .eq(LandingPageWechatCustomerService::getAdvertiserAccountGroupId, landingPageWechatCustomerService.getAdvertiserAccountGroupId())
                .eq(LandingPageWechatCustomerService::getWechatUserName, landingPageWechatCustomerService.getWechatUserName())
            );
        } else {
            num = this.count(new LambdaQueryWrapper<LandingPageWechatCustomerService>()
                .eq(LandingPageWechatCustomerService::getAdvertiserAccountGroupId, landingPageWechatCustomerService.getAdvertiserAccountGroupId())
                .eq(LandingPageWechatCustomerService::getWechatUserName, landingPageWechatCustomerService.getWechatUserName())
                .ne(LandingPageWechatCustomerService::getId, landingPageWechatCustomerService.getId())
            );
        }
        if (num >= 1) {
            throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_NAME_REPEATED);
        }
        if (StringUtils.isBlank(landingPageWechatCustomerService.getWechatUserId())) {
            return 9999;
        }
        //企业微信id为必填参数（由后端获取，为空将导致加粉、可见范围下拉选、等数据错乱）
        if (StringUtils.isNotBlank(landingPageWechatCustomerService.getWechatUserId()) && StringUtils.isBlank(StringUtils.trim(landingPageWechatCustomerService.getCorpId()))) {
            throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_WORK_WECHAT_IS_NOT);
        }
        //微信客服userId不能重复
        if (null == landingPageWechatCustomerService.getId() || 0L == landingPageWechatCustomerService.getId()) {
            //修复bug：#15382 【YIYE_AGENT_V1.119.0】当前pmp不显示其他pmp里面添加的好友客资
            num = this.count(new LambdaQueryWrapper<LandingPageWechatCustomerService>()
                .eq(LandingPageWechatCustomerService::getCorpId, landingPageWechatCustomerService.getCorpId())
                .eq(LandingPageWechatCustomerService::getAdvertiserAccountGroupId, landingPageWechatCustomerService.getAdvertiserAccountGroupId())
                .eq(LandingPageWechatCustomerService::getWechatUserId, landingPageWechatCustomerService.getWechatUserId())
            );
        } else {
            //修复bug：#15382 【YIYE_AGENT_V1.119.0】当前pmp不显示其他pmp里面添加的好友客资
            num = this.count(new LambdaQueryWrapper<LandingPageWechatCustomerService>()
                .eq(LandingPageWechatCustomerService::getCorpId, landingPageWechatCustomerService.getCorpId())
                .eq(LandingPageWechatCustomerService::getAdvertiserAccountGroupId, landingPageWechatCustomerService.getAdvertiserAccountGroupId())
                .eq(LandingPageWechatCustomerService::getWechatUserId, landingPageWechatCustomerService.getWechatUserId())
                .ne(LandingPageWechatCustomerService::getId, landingPageWechatCustomerService.getId())
            );
        }
        if (num >= 1) {
            throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_USERID_REPEATED);
        }
        return num;
    }

    /**
     * 获取企业微信id（有填写userId时，必须绑定企业微信，必须要有企业微信cropId）
     *
     * @param agentId
     * @param advertiserAccountGroupId
     * @return
     */
    public String getCorpid(String agentId, Long advertiserAccountGroupId, String wechatUserId) {
        //1.197.0 去除自建应用相关代码 只需查询代开发应用
        EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatNew(agentId, advertiserAccountGroupId);
        if (StringUtils.isNotBlank(wechatUserId) && (Objects.isNull(enterpriseWechat) || StringUtils.isBlank(StringUtils.trim(enterpriseWechat.getCorpid())))) {
            throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_WORK_WECHAT_IS_NOT);
        }
        return Objects.isNull(enterpriseWechat) ? null : StringUtils.trim(enterpriseWechat.getCorpid());
    }

    /**
     * 校验corpId
     *
     * @param corpId
     */
    public void checkCorpId(String corpId) {
        if (StringUtils.isBlank(corpId)) {
            throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_CORPID_NOT_NULL);
        }
        EnterpriseWechat ew = enterpriseWechatService.getByCorpId(corpId);
        if (ew == null) {
            throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_WORK_WECHAT_NOT_EXISTS);
        }
    }

    /**
     * 删除
     */
    public void deleteById(Long id) {
        this.removeById(id);
        landingPageWechatCustomerServiceGroupRelService.deleteRelDataByServiceId(id);       //删除关联关系
        landingPageWechatCustomerServiceAutoRuleService.remove(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>().eq(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, id));
    }

    /**
     * 编辑 / 修改
     */
    public LandingPageWechatCustomerService updateDataById(String agentId, LandingPageWechatCustomerService lpwcs) {
        LandingPageWechatCustomerService record = this.getById(lpwcs.getId());
        if (Objects.isNull(record)) {
            throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_IS_NOT_EXIST);
        }
        //旧分组id
        Set<Long> oldGroupIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIds(Lists.newArrayList(lpwcs.getId()))
            .stream().collect(Collectors.toSet());
        lpwcs.setOfflineStatus(record.getOfflineStatus());
        lpwcs.setAdvertiserAccountGroupId(record.getAdvertiserAccountGroupId());
        String oldWechatUserId = record.getWechatUserId();
        String newWechatUserId = lpwcs.getWechatUserId();
        String wechatUserId = StringUtils.isNotBlank(record.getWechatUserId()) ? record.getWechatUserId() : lpwcs.getWechatUserId();
        lpwcs.setWechatUserId(wechatUserId);
        if (StringUtils.isNotBlank(lpwcs.getWechatUserId())) {
            if (StringUtils.isEmpty(record.getCorpId())) {
                checkCorpId(lpwcs.getCorpId());
            } else {
                lpwcs.setCorpId(record.getCorpId());
            }
        }
        if (Objects.isNull(lpwcs.getOnlineStatus())) {
            lpwcs.setOnlineStatus(record.getOnlineStatus());
        }
        if (Objects.isNull(lpwcs.getAutoRuleStatus())) {
            lpwcs.setAutoRuleStatus(record.getAutoRuleStatus());
        }
        if (CollectionUtils.isEmpty(lpwcs.getAutoRule())) {
            List<LandingPageWechatCustomerServiceAutoRule> autoRule = landingPageWechatCustomerServiceAutoRuleService.list(
                new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>()
                    .eq(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, lpwcs.getId())
            );
            lpwcs.setAutoRule(autoRule);
        }
        if (ObjectUtils.isEmpty(lpwcs.getQrCodeType()) && ObjectUtils.isEmpty(lpwcs.getQrCodeConfigRecordId())) {
            lpwcs.setQrCodeConfigRecordId(record.getQrCodeConfigRecordId());
        }
        if (Objects.isNull(lpwcs.getSupportAutoOnlineStatus())) {
            lpwcs.setSupportAutoOnlineStatus(record.getSupportAutoOnlineStatus());
        }
        this.handleWechatCustomerServiceOnOffLinePointOfTime(lpwcs);
        this.checkRepeatNameUserIdCount(lpwcs); //校验名称和userid是否重复
        this.handleContactMeQrCode(record.getQrCodeConfigRecordId(), lpwcs.getQrCodeConfigRecordId());
        this.setAcquisitionLinkInfo(lpwcs); //企微获客链接
        this.setSensitiveInfo(lpwcs); //设置企微客服敏感信息
        this.setLicenseInfo(lpwcs); //设置企微客服接口许可信息
        this.setCustomerContactInfo(lpwcs); //渠道二维码
        this.update(
            lpwcs,
            Wrappers.lambdaUpdate(LandingPageWechatCustomerService.class)
                .set(ObjectUtils.isEmpty(lpwcs.getQrCodeConfigRecordId()), LandingPageWechatCustomerService::getQrCodeConfigRecordId, null)
                .eq(LandingPageWechatCustomerService::getId, lpwcs.getId())
        );//修改数据
        this.createRelData(lpwcs);       //删除 & 创建关联
        this.saveAutoRule(lpwcs);        //保存自动化规则
        this.saveAbnormalMonitor(lpwcs);    //保存异常监测

        //通知互动广告
        sendTrafficEngine(lpwcs, record);
        //发送变更项目客服分组多人活码队列
        sendCustomerServiceChange(agentId, lpwcs, oldGroupIds, oldWechatUserId, newWechatUserId);
        Integer oldQrCodeWeight = record.getQrCodeWeight();
        Integer qrCodeWeight = lpwcs.getQrCodeWeight();
        List<LandingPageWechatCustomerServiceAbnormalMonitor> abnormalMonitor = lpwcs.getAbnormalMonitor();
        if ((oldQrCodeWeight != null && oldQrCodeWeight == 0 && qrCodeWeight != null && qrCodeWeight != 0 && StringUtils.isNotBlank(lpwcs.getWechatUserId()))
            || (CollectionUtils.isNotEmpty(abnormalMonitor) && StringUtils.isNotBlank(lpwcs.getWechatUserId()))) {
            abnormalMonitorRedis.reset(lpwcs.getWechatUserId(), lpwcs.getAdvertiserAccountGroupId(), LocalDateTime.now());
        }
        //用户自主上传图片的情况，进行素材上传，方便后续客服机器人发送固定客服二维码
        if (StringUtils.isNotBlank(lpwcs.getQrCodeImgUrl()) && Objects.equals(lpwcs.getQrCodeType(), LandingPageWechatCustomerServiceQRCodeType.IMAGE_UPLOAD)) {
            this.cacheQrCodeImg(lpwcs);
        }
        return lpwcs;
    }

    /**
     * 发送变更项目客服分组多人活码队列
     *
     * @param agentId
     * @param lpwcs
     */
    private void sendCustomerServiceChange(String agentId, LandingPageWechatCustomerService lpwcs,
                                           Set<Long> oldGroupIds, String oldWechatUserId, String newWechatUserId) {
        //新分组id
        Set<Long> newGroupIds = Sets.newHashSet();
        if (StringUtils.isNotBlank(lpwcs.getWechatCustomerServiceGroupIds())) {
            List<String> ids = Arrays.asList(lpwcs.getWechatCustomerServiceGroupIds().split(","));
            if (CollectionUtils.isNotEmpty(ids)) {
                newGroupIds = ids.stream().map(Convert::toLong).collect(Collectors.toSet());
            }
        }
        //分组发生变更 或 无userId->有userId
        if (!oldGroupIds.equals(newGroupIds) || (StringUtils.isBlank(oldWechatUserId) && StringUtils.isNotBlank(newWechatUserId))) {
            oldGroupIds.addAll(newGroupIds);
            //发送变更项目客服分组多人活码队列
            CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
            dto.setAgentId(agentId)
                .setType(CustomerServiceChangeEnum.EDIT_CUSTOMER_GROUP)
                .setLandingPageCustomerServiceGroupIds(oldGroupIds);
            multiplayerCodeSender.sendCustomerServiceChange(dto);
        }
    }

    public void handleContactMeQrCode(Long oldConfigRecordId, Long newConfigRecordId) {
        if (ObjectUtils.nullSafeEquals(oldConfigRecordId, newConfigRecordId)) {
            return;
        }
        if (!ObjectUtils.isEmpty(oldConfigRecordId)) {
            LandingPageWechatCustomerServiceContactMeQRCodeRecord record = landingPageWechatCustomerServiceContactMeQRCodeRecordService.getById(oldConfigRecordId);
            if (record != null) {
                EnterpriseWechat enterpriseWechat = enterpriseWechatService.getByCorpId(record.getCorpId());
                if (enterpriseWechat != null && EnterpriseWechatStatus.NORMAL.equals(enterpriseWechat.getStatus())) {
                    landingPageWechatCustomerContactService.deleteContactMeQRCode(record.getConfigId(), enterpriseWechat.getAccessToken());
                }
                landingPageWechatCustomerServiceContactMeQRCodeRecordService.removeById(oldConfigRecordId);
            }
        }
        if (!ObjectUtils.isEmpty(newConfigRecordId)) {
            boolean result = landingPageWechatCustomerServiceContactMeQRCodeRecordService.update(
                Wrappers.lambdaUpdate(LandingPageWechatCustomerServiceContactMeQRCodeRecord.class)
                    .set(LandingPageWechatCustomerServiceContactMeQRCodeRecord::getUsageStatus, true)
                    .eq(LandingPageWechatCustomerServiceContactMeQRCodeRecord::getId, newConfigRecordId)
            );
            if (!result) {
                throw new RestException("企业微信客服联系我二维码已失效");
            }
        }
    }

    /**
     * 通知互动广告
     *
     * @param lpwcs
     */
    private void sendTrafficEngine(LandingPageWechatCustomerService lpwcs, LandingPageWechatCustomerService record) {
        //通知的前提是 userId不为空
        Optional.ofNullable(lpwcs.getWechatUserId()).ifPresent(e -> {
            String wechatCustomerServiceGroupIds = lpwcs.getWechatCustomerServiceGroupIds();
            if (StringUtils.isNotBlank(wechatCustomerServiceGroupIds)) {
                List<Long> groupIds = Arrays.asList(wechatCustomerServiceGroupIds.split(",")).stream().map(m -> Convert.toLong(m)).collect(Collectors.toList());
                CustomerServiceOperationMessage onLineMessage = new CustomerServiceOperationMessage();
                lpwcs.setAdvertiserAccountGroupId(record.getAdvertiserAccountGroupId());
                //原先的userId为空
                if (StringUtils.isBlank(record.getWechatUserId())) {
                    //新增
                    onLineMessage.setCustomerServiceOperation(CustomerServiceOperation.ADD)
                        .setAgentId(TenantContextHolder.get())
                        .setLandingPageWechatCustomerService(lpwcs)
                        .setLandingPageCustomerServiceGroupIds(groupIds);
                    trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(onLineMessage);
                } else {
                    String wechatUserName = Optional.ofNullable(lpwcs.getWechatUserName()).orElseGet(() -> {
                        return record.getWechatUserName();
                    });
                    lpwcs.setWechatUserName(wechatUserName);
                    //编辑权重，包含了编辑分组逻辑
                    onLineMessage.setCustomerServiceOperation(CustomerServiceOperation.EDIT_WEIGHT)
                        .setLandingPageWechatCustomerService(lpwcs)
                        .setAgentId(TenantContextHolder.get());
                    trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(onLineMessage);
                }
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void createRelData(LandingPageWechatCustomerService landingPageWechatCustomerService) {

        //查询原本的关联关系记录
        List<Long> oldLandingPageWechatCustomerServiceGroupIds = landingPageWechatCustomerServiceGroupRelService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceGroupRel>()
                .eq(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceId, landingPageWechatCustomerService.getId()))
            .stream().map(e -> e.getLandingPageWechatCustomerServiceGroupId()).collect(Collectors.toList());

        List<Long> removeRelGroupIds = this.getRemoveRelGroupIds(oldLandingPageWechatCustomerServiceGroupIds, landingPageWechatCustomerService);
        //删除移出去的分组的关联关系记录
        if (!removeRelGroupIds.isEmpty()) {
            log.info("编辑客服分组，landingPageWechatCustomerServiceId= {},被移出去的客服分组ID集合,removeRelGroupIds = {}", landingPageWechatCustomerService.getId(), removeRelGroupIds);
            landingPageWechatCustomerServiceGroupRelService.deleteRelDataByRelGroupIds(removeRelGroupIds, landingPageWechatCustomerService.getId());
        }
        //新增的客服分组ID集合
        List<Long> addList = this.newAddRelGroupIds(oldLandingPageWechatCustomerServiceGroupIds, landingPageWechatCustomerService);
        log.info("编辑客服分组，新增的客服分组ID集合,landingPageWechatCustomerServiceId= {},removeRelGroupIds = {}", landingPageWechatCustomerService.getId(), removeRelGroupIds);
        List<LandingPageWechatCustomerServiceGroupRel> landingPageWechatCustomerServiceGroupRelList = new ArrayList<>();
        for (Long groupId : addList) {
            landingPageWechatCustomerServiceGroupRelList.add(new LandingPageWechatCustomerServiceGroupRel()
                .setLandingPageWechatCustomerServiceGroupId(groupId)
                .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerService.getId())
                .setAdvertiserAccountGroupId(landingPageWechatCustomerService.getAdvertiserAccountGroupId()));
        }

//        //删除关联关系
//        landingPageWechatCustomerServiceGroupRelService.deleteRelDataByServiceId(landingPageWechatCustomerService.getId());
//        //设置关联关系数据
//        List<LandingPageWechatCustomerServiceGroupRel> landingPageWechatCustomerServiceGroupRelList = new ArrayList<>();
//        if (StringUtils.isNotBlank(landingPageWechatCustomerService.getWechatCustomerServiceGroupIds())) {
//            List<String> ids = Arrays.asList(landingPageWechatCustomerService.getWechatCustomerServiceGroupIds().split(","));
//            if (ids.size() > 0) {
//                for (String id : ids) { //选择了分组
//                    landingPageWechatCustomerServiceGroupRelList.add(new LandingPageWechatCustomerServiceGroupRel()
//                        .setLandingPageWechatCustomerServiceGroupId(Long.valueOf(id)).setLandingPageWechatCustomerServiceId(landingPageWechatCustomerService.getId())
//                        .setAdvertiserAccountGroupId(landingPageWechatCustomerService.getAdvertiserAccountGroupId()));
//                }
//            }
//        }
        //未分组，当一个分组也没有选的时候，要将这个客服设置到未分组中
        if (StringUtils.isBlank(landingPageWechatCustomerService.getWechatCustomerServiceGroupIds())) {
            landingPageWechatCustomerServiceGroupRelList.add(new LandingPageWechatCustomerServiceGroupRel()
                .setLandingPageWechatCustomerServiceGroupId(-1L)
                .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerService.getId())
                .setAdvertiserAccountGroupId(landingPageWechatCustomerService.getAdvertiserAccountGroupId()));
        }
        //清理缓存
        landingPageWechatCustomerServiceGroupRelList.forEach(landingPageWechatCustomerServiceGroupRel -> {
            landingPageWechatCustomerServiceRedis.deleteWechatServiceDataByGroupId(landingPageWechatCustomerServiceGroupRel.getLandingPageWechatCustomerServiceGroupId());
        });
        landingPageWechatCustomerServiceGroupRelService.saveBatch(landingPageWechatCustomerServiceGroupRelList);
    }

    /**
     * 获取移出去的客服分组ID
     */
    public List<Long> getRemoveRelGroupIds(List<Long> oldLandingPageWechatCustomerServiceGroupIds,
                                           LandingPageWechatCustomerService landingPageWechatCustomerService) {
        //移出的旧分组
        List<Long> reducedList = new ArrayList<>();
        if (StringUtils.isNotBlank(landingPageWechatCustomerService.getWechatCustomerServiceGroupIds()) && !oldLandingPageWechatCustomerServiceGroupIds.isEmpty()) {
            List<Long> newLandingPageWechatCustomerServiceGroupIds = Arrays.stream(landingPageWechatCustomerService.getWechatCustomerServiceGroupIds()
                .split(",")).map(Long::parseLong).collect(Collectors.toList());
            if (Objects.nonNull(newLandingPageWechatCustomerServiceGroupIds) && newLandingPageWechatCustomerServiceGroupIds.size() > 0 && Objects.nonNull(oldLandingPageWechatCustomerServiceGroupIds)) {
                reducedList = oldLandingPageWechatCustomerServiceGroupIds.stream()
                    .filter(e -> !newLandingPageWechatCustomerServiceGroupIds.contains(e)).collect(Collectors.toList());
            }
        } else if (StringUtils.isBlank(landingPageWechatCustomerService.getWechatCustomerServiceGroupIds()) && !oldLandingPageWechatCustomerServiceGroupIds.isEmpty()) {
            //把客服的分组全清掉的情况
            reducedList = oldLandingPageWechatCustomerServiceGroupIds;
        }
        return reducedList;
    }

    /**
     * 获取新增的客服分组ID集合
     *
     * @param oldLandingPageWechatCustomerServiceGroupIds 编辑前的客服分组ID集合
     * @param landingPageWechatCustomerService            编辑的客服信息
     * @return 新增的客服分组ID集合
     */
    public List<Long> newAddRelGroupIds(List<Long> oldLandingPageWechatCustomerServiceGroupIds,
                                        LandingPageWechatCustomerService landingPageWechatCustomerService) {
        List<Long> addList = new ArrayList<>();
        if (StringUtils.isNotBlank(landingPageWechatCustomerService.getWechatCustomerServiceGroupIds())) {
            //最新的客服分组ID集合，包含新旧的
            List<Long> newLandingPageWechatCustomerServiceGroupIds = Arrays.stream(landingPageWechatCustomerService.getWechatCustomerServiceGroupIds()
                .split(",")).map(Long::parseLong).collect(Collectors.toList());
            if (Objects.nonNull(oldLandingPageWechatCustomerServiceGroupIds) && oldLandingPageWechatCustomerServiceGroupIds.size() > 0) {
                addList = newLandingPageWechatCustomerServiceGroupIds.stream()
                    .filter(e -> !oldLandingPageWechatCustomerServiceGroupIds.contains(e)).collect(Collectors.toList());
            } else {
                addList = newLandingPageWechatCustomerServiceGroupIds;
            }
        }
        return addList;
    }

    /**
     * 保存自动化规则
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAutoRule(LandingPageWechatCustomerService landingPageWechatCustomerService) {
        Long landingPageWechatCustomerServiceId = landingPageWechatCustomerService.getId();
        List<LandingPageWechatCustomerServiceAutoRule> autoRule = landingPageWechatCustomerService.getAutoRule();
        if (landingPageWechatCustomerServiceId == null || CollectionUtils.isEmpty(autoRule)) {
            return;
        }
        //删除关联关系
        landingPageWechatCustomerServiceAutoRuleService.remove(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>()
            .eq(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, landingPageWechatCustomerServiceId));
        autoRule.forEach(tmp -> tmp.setId(null)
            .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerService.getId())
            .setAdvertiserAccountGroupId(landingPageWechatCustomerService.getAdvertiserAccountGroupId()));
        landingPageWechatCustomerServiceAutoRuleService.saveBatch(autoRule);
    }

    /**
     * 保存异常监测
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAbnormalMonitor(LandingPageWechatCustomerService landingPageWechatCustomerService) {
        Long landingPageWechatCustomerServiceId = landingPageWechatCustomerService.getId();
        List<LandingPageWechatCustomerServiceAbnormalMonitor> abnormalMonitor = landingPageWechatCustomerService.getAbnormalMonitor();
        if (landingPageWechatCustomerServiceId == null || CollectionUtils.isEmpty(abnormalMonitor)) {
            return;
        }
        //删除关联关系
        landingPageWechatCustomerServiceAbnormalMonitorService.remove(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAbnormalMonitor>()
            .eq(LandingPageWechatCustomerServiceAbnormalMonitor::getLandingPageWechatCustomerServiceId, landingPageWechatCustomerServiceId));
        abnormalMonitor.forEach(tmp -> tmp.setId(null)
            .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerService.getId())
            .setAdvertiserAccountGroupId(landingPageWechatCustomerService.getAdvertiserAccountGroupId()));
        landingPageWechatCustomerServiceAbnormalMonitorService.saveBatch(abnormalMonitor);
    }

    public List<LandingPageWechatCustomerServiceRedisDto> getByGroupId(Long wechatCustomerServiceGroupId) {
        return baseMapper.getByGroupId(wechatCustomerServiceGroupId);
    }

    /**
     * 根据【微信客服id】删除所有关联的【redis缓存】信息
     */
    public void deleteCacheByServiceId(Long serviceId) {
        log.info("根据【微信客服id】删除所有关联的【redis缓存】信息 agentId:[{}] serviceId:[{}]", TenantContextHolder.get(), serviceId);
        List<LandingPageWechatCustomerServiceGroupRel> landingPageWechatCustomerServiceGroupRelList = landingPageWechatCustomerServiceGroupRelService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceGroupRel>().eq(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceId, serviceId));
        if (CollectionUtils.isNotEmpty(landingPageWechatCustomerServiceGroupRelList)) {
            log.info("根据【微信客服id】删除所有关联的【redis缓存】信息 agentId:[{}] serviceId:[{}] relList:[{}]", TenantContextHolder.get(), serviceId, JSONObject.toJSONString(landingPageWechatCustomerServiceGroupRelList));
            landingPageWechatCustomerServiceGroupRelList.forEach(landingPageWechatCustomerServiceGroupRel -> {
                landingPageWechatCustomerServiceRedis.deleteWechatServiceDataByGroupId(landingPageWechatCustomerServiceGroupRel.getLandingPageWechatCustomerServiceGroupId());
            });
        }
    }

    /**
     * 根据【微信客服id】集合删除所有关联的【redis缓存】信息
     */
    public void deleteCacheByServiceIds(Collection<Long> serviceIds) {
        log.info("根据【微信客服id】集合删除所有关联的【redis缓存】信息 agentId:[{}] serviceIds:[{}]", TenantContextHolder.get(), serviceIds);
        List<LandingPageWechatCustomerServiceGroupRel> landingPageWechatCustomerServiceGroupRelList = landingPageWechatCustomerServiceGroupRelService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceGroupRel>().in(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceId, serviceIds));
        if (!landingPageWechatCustomerServiceGroupRelList.isEmpty()) {
            List<Long> ids = landingPageWechatCustomerServiceGroupRelList.stream()
                .map(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceGroupId).distinct()
                .collect(Collectors.toList());
            log.info("根据【微信客服id】集合删除所有关联的【redis缓存】信息 agentId:[{}] serviceIds:[{}] groupIds:[{}]", TenantContextHolder.get(), serviceIds, ids);
            ids.forEach(id -> landingPageWechatCustomerServiceRedis.deleteWechatServiceDataByGroupId(id));
        }
    }

    //批量操作,如果有多个分组
    public TemplateUploadResultDto operationBatch(WechatOperationType operation,
                                                  WechatBatchOperationDto wechatBatchOperationDto) {
        Long accountGroupId = wechatBatchOperationDto.getAccountGroupId();
        List<Long> wechatCustomerIds = wechatBatchOperationDto.getWechatCustomerIds();
        List<LandingPageWechatCustomerService> landingPageWechatCustomerServices = this.list(new LambdaQueryWrapper<LandingPageWechatCustomerService>().in(LandingPageWechatCustomerService::getId, wechatCustomerIds));
        Map<BatchEditCustomerServiceErrorType, List<String>> batchEditErrorCustomerServiceNameMap = new HashMap<>();
        switch (operation) {
            case EDITGROUP:     //编辑分组
                editGroupByBatch(landingPageWechatCustomerServices, wechatBatchOperationDto);
                break;
            case EDITWEIGHT:    //编辑权重
                editWeightByBatch(landingPageWechatCustomerServices, wechatBatchOperationDto, accountGroupId);
                break;
            case ONLINE:        //上线
                //先校验接口状态
                checkCustomerServiceInterFaceStatus(landingPageWechatCustomerServices, batchEditErrorCustomerServiceNameMap);
                onlineByBatch(landingPageWechatCustomerServices, batchEditErrorCustomerServiceNameMap);
                break;
            case OFFLINE:       //下线
                offlineByBatch(landingPageWechatCustomerServices, batchEditErrorCustomerServiceNameMap);
                break;
            case AUTORULE:      //编辑自动化规则
                editAutoRuleByBatch(landingPageWechatCustomerServices, wechatBatchOperationDto);
                break;
            case CLOSEAUTORULE: //关闭自动化规则
                closeAutoRuleByBatch(landingPageWechatCustomerServices);
                break;
            case DELETE:        //删除
                deleteByBatch(landingPageWechatCustomerServices);
                break;
            case REMOVEGROUP:   //移出分组
                removeGroupByBatch(landingPageWechatCustomerServices, wechatBatchOperationDto);
                break;
            case ABNORMALMONITOR:      //编辑异常监测
                editAbnormalMonitorByBatch(landingPageWechatCustomerServices, wechatBatchOperationDto);
                break;
            case CLOSEABNORMALMONITOR: //关闭异常监测
                closeAbnormalMonitorByBatch(landingPageWechatCustomerServices);
                break;
        }
        TemplateUploadResultDto templateUploadResultDto = new TemplateUploadResultDto();
        if (batchEditErrorCustomerServiceNameMap.keySet().size() == 0) {
            templateUploadResultDto.setCode(200);
            templateUploadResultDto.setMessage("");
            return templateUploadResultDto;
        }
        StringBuilder message = new StringBuilder();
        batchEditErrorCustomerServiceNameMap.forEach((k, v) -> {
            String customerServiceName = String.join("】、【", v);
            message.append("当前客服【");
            message.append(customerServiceName);
            templateUploadResultDto.setCode(200);
            if (k == BatchEditCustomerServiceErrorType.APPLICATION_INTERFACE_LICENSE_FAILED) {
                message.append("】获取应用的接口许可状态失败\n");
            } else if (k == BatchEditCustomerServiceErrorType.GET_MEMBER_ACTIVATE_DETAIL_FAILED) {
                message.append("】获取成员的激活详情失败\n");
            } else if (k == BatchEditCustomerServiceErrorType.INTERFACE_STATUS_NOT_ACTIVATE) {
                String content = "客服接口状态为未激活，该状态无法进行加粉归因上报";
                message.append("】").append(content).append("\n");
            } else if (k == BatchEditCustomerServiceErrorType.BOTTOM_CUSTOMER_SERVICE) {
                message.append("】设置为兜底客服，不可操作下线\n");
                templateUploadResultDto.setCode(500);
            } else if (k == BatchEditCustomerServiceErrorType.NOT_ALLOWED_MANUAL_ONLINE) {
                message.append("】按已设置的自动化上下线处于下线状态，不支持手动开启上线\n");
                templateUploadResultDto.setCode(500);
            } else if (k == BatchEditCustomerServiceErrorType.NOT_ALLOWED_MANUAL_OFFLINE) {
                templateUploadResultDto.setCode(500);
                message.append("】按已设置的自动化上下线处于上线状态，不支持手动开启下线\n");
            }
        });
        templateUploadResultDto.setMessage(message.toString());
        return templateUploadResultDto;
    }

    private void editGroupByBatch(List<LandingPageWechatCustomerService> customerServices,
                                  WechatBatchOperationDto wechatBatchOperationDto) {
        Set<Long> ids = customerServices.stream().map(LandingPageWechatCustomerService::getId)
            .collect(Collectors.toSet());
        List<Long> groupIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ids)) {
            //获取旧分组id
            groupIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIds(ids);
        }
        customerServices.forEach(lpwcs -> {
            String join = Joiner.on(",")
                .join(wechatBatchOperationDto.getWechatCustomerEditGroupId());   //多个微信客服与多个分组，移动分组只要走关系重构即可
            lpwcs.setWechatCustomerServiceGroupIds(join);
            this.createRelData(lpwcs);
            CustomerServiceOperationMessage inGroupMessage = new CustomerServiceOperationMessage();
            inGroupMessage.setCustomerServiceOperation(CustomerServiceOperation.MIGRATION_GROUPING)
                .setLandingPageWechatCustomerService(lpwcs)
                .setAgentId(TenantContextHolder.get())
                .setWechatCustomerInGroupId(wechatBatchOperationDto.getWechatCustomerEditGroupId());
            trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(inGroupMessage);
        });
        groupIds.addAll(wechatBatchOperationDto.getWechatCustomerEditGroupId());
        if (CollectionUtils.isNotEmpty(groupIds)) {
            CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
            dto.setAgentId(TenantContextHolder.get())
                .setType(CustomerServiceChangeEnum.EDIT_CUSTOMER_GROUP)
                .setLandingPageCustomerServiceGroupIds(new HashSet<>(groupIds));
            //发送变更项目客服分组多人活码队列
            multiplayerCodeSender.sendCustomerServiceChange(dto);
        }
        //通知客服分组变更事件
        if (CollectionUtils.isNotEmpty(groupIds)) {
            try {
                WechatCustomerServiceGroupDto wechatCustomerServiceGroupDto = new WechatCustomerServiceGroupDto();
                wechatCustomerServiceGroupDto.setGroupIds(Sets.newHashSet(groupIds));
                landingPageCustomerServiceSender.sendCallBackMessage(wechatCustomerServiceGroupDto);
            } catch (Exception e) {
                log.error("发送客服分组客服变更事件失败!", e);
            }
        }
    }

    private void editWeightByBatch(List<LandingPageWechatCustomerService> customerServices,
                                   WechatBatchOperationDto wechatBatchOperationDto, Long accountGroupId) {
        //上线列表
        Integer qrCodeWeight = wechatBatchOperationDto.getWechatCustomer().getQrCodeWeight();
        customerServices.forEach(lpwcs -> {
            Long lpwcsId = lpwcs.getId();
            Integer oldQrCodeWeight = lpwcs.getQrCodeWeight();
            lpwcs.setQrCodeWeight(qrCodeWeight);
            if (wechatBatchOperationDto.getWechatCustomer().getQrCodeWeight() == 0) {
                lpwcs.setOnlineStatus(OnlineStatusType.ENABLE).setOnlineActionStatus(OnlineActionStatus.AUTO);
                //上线修改通知-互动广告
                CustomerServiceOperationMessage onLineMessage = new CustomerServiceOperationMessage();
                onLineMessage.setCustomerServiceOperation(CustomerServiceOperation.ONLINE)
                    .setLandingPageWechatCustomerService(lpwcs)
                    .setAgentId(TenantContextHolder.get());
                trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(onLineMessage);
                //获取分组id
                List<Long> groupIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIds(Lists.newArrayList(lpwcsId));
                if (CollectionUtils.isNotEmpty(groupIds)) {
                    CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
                    dto.setAgentId(TenantContextHolder.get())
                        .setType(CustomerServiceChangeEnum.TOP_LINE_DOWNLINE)
                        .setLandingPageCustomerServiceGroupIds(new HashSet<>(groupIds));
                    //发送变更项目客服分组多人活码队列
                    multiplayerCodeSender.sendCustomerServiceChange(dto);
                }
            } else if (oldQrCodeWeight != null && oldQrCodeWeight == 0 && qrCodeWeight != null && qrCodeWeight != 0) {
                List<LandingPageWechatCustomerServiceAutoRule> autoRule = landingPageWechatCustomerServiceAutoRuleService.list(
                    new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>()
                        .eq(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, lpwcsId)
                );
                lpwcs.setAutoRule(autoRule);
                this.handleWechatCustomerServiceOnOffLinePointOfTime(lpwcs);
            }
            this.saveOrUpdate(lpwcs);
            //权重值修改通知-互动广告
            CustomerServiceOperationMessage qrCodeWeightMessage = new CustomerServiceOperationMessage();
            qrCodeWeightMessage.setCustomerServiceOperation(CustomerServiceOperation.EDIT_WEIGHT)
                .setLandingPageWechatCustomerService(lpwcs)
                .setAgentId(TenantContextHolder.get());
            trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(qrCodeWeightMessage);
            if (oldQrCodeWeight != null && oldQrCodeWeight == 0 && qrCodeWeight != null && qrCodeWeight != 0 && StringUtils.isNotBlank(lpwcs.getWechatUserId())) {
                abnormalMonitorRedis.reset(lpwcs.getWechatUserId(), lpwcs.getAdvertiserAccountGroupId(), LocalDateTime.now());
            }
        });
        //通知客服分组变更事件
        if (CollectionUtils.isNotEmpty(customerServices)) {
            try {
                Set<Long> customerServiceIds = customerServices.stream().map(LandingPageWechatCustomerService::getId)
                    .collect(Collectors.toSet());
                WechatCustomerServiceGroupDto wechatCustomerServiceGroupDto = new WechatCustomerServiceGroupDto();
                wechatCustomerServiceGroupDto.setWechatCustomerServiceIds(customerServiceIds);
                landingPageCustomerServiceSender.sendCallBackMessage(wechatCustomerServiceGroupDto);
            } catch (Exception e) {
                log.error("发送客服分组客服变更事件失败!", e);
            }
        }
    }


    /**
     * 检查接口状态
     */
    public void checkCustomerServiceInterFaceStatus(List<LandingPageWechatCustomerService> customerServices,
                                                    Map<BatchEditCustomerServiceErrorType, List<String>> batchEditErrorCustomerServiceNameMap) {

        List<String> customerServiceNames = new LinkedList<>();
        String userName = "";
        for (LandingPageWechatCustomerService lpwcs : customerServices) {
            try {
                if (StringUtils.isBlank(lpwcs.getWechatUserId()) || customerServices.size() > 1) {
                    userName = "部分";
                }
                customerServiceNames.add(lpwcs.getWechatUserName());
                String corpId = lpwcs.getCorpId();
                if (StringUtils.isBlank(corpId)) {
                    log.info("点击客服上线, 检查接口状态, 企业微信id为空, 不做处理");
                    continue;
                }
                EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatCacheByCorpId(corpId);
                if (enterpriseWechat == null) {
                    log.info("点击客服上线, 检查接口状态, 没有查询到企业微信信息, corpId = {}", corpId);
                    continue;
                }
                String userId = lpwcs.getWechatUserId();
                String key = RedisConstant.WECHAT_CUSTOMER_ONLINE_LICENSE_CHECK + corpId + ":" + userId;
                Object redisValue = objectRedisTemplate.opsForValue().get(key);
                log.info("点击客服上线, 开始检查接口状态, key = {}, redisValue = {}", key, redisValue);
                if (Objects.nonNull(redisValue) && redisValue instanceof WorkWechatCustomerLicenseInfoDTO) {
                    WorkWechatCustomerLicenseInfoDTO obj = (WorkWechatCustomerLicenseInfoDTO) redisValue;
                    if (Objects.equals(obj.getLicenseStatus(), LicenseStatus.ACTIVE)) {
                        continue;
                    }
                }
                String providerAccessToken = enterpriseWechatDevelopOauthService.getProviderAccessToken();
                log.info("点击客服上线, 开始检查接口状态, providerAccessToken = {}", providerAccessToken);
                ProviderTokenResposeBody providerToken = new ProviderTokenResposeBody();
                providerToken.setProviderAccessToken(providerAccessToken);
                if (providerToken == null || StringUtils.isBlank(providerAccessToken)) {
                    log.error("点击客服上线, 检查接口状态,ProviderAccessToken获取失败");
                    continue;
                }
                Map<String, Object> map = Maps.newHashMap();
                map.put("corpid", corpId);
                map.put("suite_id", enterpriseWechat.getSuiteId());
                JSONObject appLicenseInfo = workWeixinApiClient.getAppLicenseInfo(providerToken.getProviderAccessToken(), map);
                if (appLicenseInfo == null || appLicenseInfo.getIntValue("errcode") != 0
                    || appLicenseInfo.getLong("license_check_time") == null) {
                    batchEditErrorCustomerServiceNameMap.put(BatchEditCustomerServiceErrorType.APPLICATION_INTERFACE_LICENSE_FAILED, StringUtils.isNotBlank(userName) ? Collections.singletonList(userName) : customerServiceNames);
                }
                Instant licenseCheckTime = Instant.ofEpochSecond(appLicenseInfo.getLong("license_check_time"));
                Instant now = Instant.now();
                WorkWechatCustomerLicenseInfoDTO licenseInfoDTO = workWechatCustomerLicenseInfoService.getActiveInfoByUser(userId, corpId, providerToken.getProviderAccessToken(), licenseCheckTime, now);
                if (licenseInfoDTO == null) {
                    batchEditErrorCustomerServiceNameMap.put(BatchEditCustomerServiceErrorType.GET_MEMBER_ACTIVATE_DETAIL_FAILED, StringUtils.isNotBlank(userName) ? Collections.singletonList(userName) : customerServiceNames);
                } else {
                    if (Objects.equals(LicenseStatus.NOT_ACTIVE, licenseInfoDTO.getLicenseStatus())) {
                        batchEditErrorCustomerServiceNameMap.put(BatchEditCustomerServiceErrorType.INTERFACE_STATUS_NOT_ACTIVATE, StringUtils.isNotBlank(userName) ? Collections.singletonList(userName) : customerServiceNames);
                    }
                    objectRedisTemplate.opsForValue().set(key, licenseInfoDTO, 5, TimeUnit.MINUTES);
                }
            } catch (Exception e) {
                log.error("点击客服上线, 检查接口状态出现异常", e);
            }
        }
    }

    private void onlineByBatch(List<LandingPageWechatCustomerService> customerServices,
                               Map<BatchEditCustomerServiceErrorType, List<String>> batchEditErrorCustomerServiceNameMap) {
        customerServices.forEach(lpwcs -> {
            try {
                if (!OnlineStatusType.ENABLE.equals(lpwcs.getOnlineStatus())) {
                    landingPageWechatCustomerServiceService.checkOfflineRule(lpwcs.getId());        //校验是否符合下线规则
                    landingPageWechatCustomerServiceService.checkOnlinePointOfTimeAutoRule(lpwcs.getId(), lpwcs.getAutoRuleStatus());
                    lpwcs.setOnlineStatus(OnlineStatusType.ENABLE).setOnlineActionStatus(OnlineActionStatus.MANUAL);
                    this.saveOrUpdate(lpwcs);
                    //上线修改通知-互动广告
                    CustomerServiceOperationMessage onLineMessage = new CustomerServiceOperationMessage();
                    onLineMessage.setCustomerServiceOperation(CustomerServiceOperation.ONLINE)
                        .setLandingPageWechatCustomerService(lpwcs)
                        .setAgentId(TenantContextHolder.get());
                    trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(onLineMessage);
                    //获取分组id
                    List<Long> groupIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIds(Lists.newArrayList(lpwcs.getId()));
                    if (CollectionUtils.isNotEmpty(groupIds)) {
                        CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
                        dto.setAgentId(TenantContextHolder.get())
                            .setType(CustomerServiceChangeEnum.TOP_LINE_DOWNLINE)
                            .setLandingPageCustomerServiceGroupIds(new HashSet<>(groupIds));
                        //发送变更项目客服分组多人活码队列
                        multiplayerCodeSender.sendCustomerServiceChange(dto);
                    }
                    if (StringUtils.isNotBlank(lpwcs.getWechatUserId())) {
                        abnormalMonitorRedis.reset(lpwcs.getWechatUserId(), lpwcs.getAdvertiserAccountGroupId(), LocalDateTime.now());
                    }
                }
            } catch (RestException e) {
                if (ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_ONLINE_INVALID.equals(e.getMessage()) ||
                    ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_AUTO_RULE_EFFECTIVE.equals(e.getMessage())) {
                    if (!batchEditErrorCustomerServiceNameMap.containsKey(BatchEditCustomerServiceErrorType.NOT_ALLOWED_MANUAL_ONLINE)) {
                        batchEditErrorCustomerServiceNameMap.put(BatchEditCustomerServiceErrorType.NOT_ALLOWED_MANUAL_ONLINE, new LinkedList<>());
                    }
                    List<String> customerServiceNames = batchEditErrorCustomerServiceNameMap.get(BatchEditCustomerServiceErrorType.NOT_ALLOWED_MANUAL_ONLINE);
                    customerServiceNames.add(lpwcs.getWechatUserName());
                } else {
                    throw e;
                }
            }
        });
        //通知客服分组变更事件
        if (CollectionUtils.isNotEmpty(customerServices)) {
            try {
                //不能上线的客服-可能为兜底客服
                List<String> customerServiceNames = batchEditErrorCustomerServiceNameMap.get(BatchEditCustomerServiceErrorType.NOT_ALLOWED_MANUAL_ONLINE);
                if (CollectionUtils.isNotEmpty(customerServiceNames)) {
                    //只获取操作成功了的客服
                    customerServices = customerServices.stream()
                        .filter(e -> !customerServiceNames.contains(e.getWechatUserName()))
                        .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(customerServices)) {
                    log.info("本次操作上线客服，未有客服成功操作上线!");
                    return;
                }
                Set<Long> customerServiceIds = customerServices.stream().map(LandingPageWechatCustomerService::getId)
                    .collect(Collectors.toSet());
                WechatCustomerServiceGroupDto wechatCustomerServiceGroupDto = new WechatCustomerServiceGroupDto();
                wechatCustomerServiceGroupDto.setWechatCustomerServiceIds(customerServiceIds);
                landingPageCustomerServiceSender.sendCallBackMessage(wechatCustomerServiceGroupDto);
            } catch (Exception e) {
                log.error("发送客服分组客服变更事件失败!", e);
            }
        }
    }

    private void offlineByBatch(List<LandingPageWechatCustomerService> customerServices,
                                Map<BatchEditCustomerServiceErrorType, List<String>> batchEditErrorCustomerServiceNameMap) {
        customerServices.forEach(lpwcs -> {
            try {
                if (lpwcs.getQrCodeWeight() == 0) {
                    if (!batchEditErrorCustomerServiceNameMap.containsKey(BatchEditCustomerServiceErrorType.BOTTOM_CUSTOMER_SERVICE)) {
                        batchEditErrorCustomerServiceNameMap.put(BatchEditCustomerServiceErrorType.BOTTOM_CUSTOMER_SERVICE, new LinkedList<>());
                    }
                    List<String> customerServiceNames = batchEditErrorCustomerServiceNameMap.get(BatchEditCustomerServiceErrorType.BOTTOM_CUSTOMER_SERVICE);
                    customerServiceNames.add(lpwcs.getWechatUserName());
                    return;
                }
                if (OnlineStatusType.ENABLE.equals(lpwcs.getOnlineStatus())) {
                    landingPageWechatCustomerServiceService.checkOfflinePointOfTimeAutoRule(lpwcs.getId(), lpwcs.getAutoRuleStatus());
                    lpwcs.setOnlineStatus(OnlineStatusType.DISABLE).setOfflineStatus(OfflineStatus.MANUAL);
                    this.saveOrUpdate(lpwcs);
                    //下线通知-互动广告
                    CustomerServiceOperationMessage offlineMessage = new CustomerServiceOperationMessage();
                    offlineMessage.setCustomerServiceOperation(CustomerServiceOperation.OFFLINE)
                        .setAgentId(TenantContextHolder.get())
                        .setLandingPageWechatCustomerService(lpwcs);
                    trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(offlineMessage);
                    //获取分组id
                    List<Long> groupIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIds(Lists.newArrayList(lpwcs.getId()));
                    if (CollectionUtils.isNotEmpty(groupIds)) {
                        CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
                        dto.setAgentId(TenantContextHolder.get())
                            .setType(CustomerServiceChangeEnum.TOP_LINE_DOWNLINE)
                            .setLandingPageCustomerServiceGroupIds(new HashSet<>(groupIds));
                        //发送变更项目客服分组多人活码队列
                        multiplayerCodeSender.sendCustomerServiceChange(dto);
                    }
                }
            } catch (RestException e) {
                if (ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_AUTO_RULE_EFFECTIVE.equals(e.getMessage())) {
                    if (!batchEditErrorCustomerServiceNameMap.containsKey(BatchEditCustomerServiceErrorType.NOT_ALLOWED_MANUAL_OFFLINE)) {
                        batchEditErrorCustomerServiceNameMap.put(BatchEditCustomerServiceErrorType.NOT_ALLOWED_MANUAL_OFFLINE, new LinkedList<>());
                    }
                    List<String> customerServiceNames = batchEditErrorCustomerServiceNameMap.get(BatchEditCustomerServiceErrorType.NOT_ALLOWED_MANUAL_OFFLINE);
                    customerServiceNames.add(lpwcs.getWechatUserName());
                } else {
                    throw e;
                }
            }
        });
        //通知客服分组变更事件
        if (CollectionUtils.isNotEmpty(customerServices)) {
            try {
                //不能下线的客服
                List<String> customerServiceNames = batchEditErrorCustomerServiceNameMap.get(BatchEditCustomerServiceErrorType.NOT_ALLOWED_MANUAL_OFFLINE);
                //兜底客服
                List<String> buttonCustomerService = batchEditErrorCustomerServiceNameMap.get(BatchEditCustomerServiceErrorType.BOTTOM_CUSTOMER_SERVICE);
                List<String> result = new ArrayList<>();
                Optional.ofNullable(customerServiceNames).ifPresent(e -> result.addAll(e));
                Optional.ofNullable(buttonCustomerService).ifPresent(e -> result.addAll(e));
                if (CollectionUtils.isNotEmpty(result)) {
                    //只获取操作成功了的客服
                    customerServices = customerServices.stream().filter(e -> !result.contains(e.getWechatUserName()))
                        .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(customerServices)) {
                    log.info("本次操作下线客服，未有客服成功操作下线!");
                    return;
                }
                Set<Long> customerServiceIds = customerServices.stream().map(LandingPageWechatCustomerService::getId)
                    .collect(Collectors.toSet());
                WechatCustomerServiceGroupDto wechatCustomerServiceGroupDto = new WechatCustomerServiceGroupDto();
                wechatCustomerServiceGroupDto.setWechatCustomerServiceIds(customerServiceIds);
                landingPageCustomerServiceSender.sendCallBackMessage(wechatCustomerServiceGroupDto);
            } catch (Exception e) {
                log.error("发送客服分组客服变更事件失败!", e);
            }
        }
    }

    private void editAutoRuleByBatch(List<LandingPageWechatCustomerService> customerServices,
                                     WechatBatchOperationDto wechatBatchOperationDto) {
        customerServices.forEach(lpwcs -> {
            lpwcs.setAutoRule(wechatBatchOperationDto.getWechatCustomer().getAutoRule())
                .setAutoRuleStatus(AutoRuleStatus.ENABLE);
            this.handleWechatCustomerServiceOnOffLinePointOfTime(lpwcs);
            this.saveOrUpdate(lpwcs);
            this.saveAutoRule(lpwcs);
        });
    }

    private void closeAutoRuleByBatch(List<LandingPageWechatCustomerService> customerServices) {
        customerServices.forEach(lpwcs -> {
            lpwcs.setAutoRuleStatus(AutoRuleStatus.UNOPENED);
            this.saveOrUpdate(lpwcs);
        });
    }

    private void deleteByBatch(List<LandingPageWechatCustomerService> customerServices) {
        List<Long> groupIdsByServiceIds = null;
        if (CollectionUtils.isNotEmpty(customerServices)) {
            //查询客服关联的客服分组
            List<Long> customerServiceIds = customerServices.stream().map(LandingPageWechatCustomerService::getId)
                .collect(Collectors.toList());
            groupIdsByServiceIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIds(customerServiceIds);
        }

        customerServices.forEach(lpwcs -> {
            this.handleContactMeQrCode(lpwcs.getQrCodeConfigRecordId(), null);
            this.deleteById(lpwcs.getId());
            CustomerServiceOperationMessage offlineMessage = new CustomerServiceOperationMessage();
            offlineMessage.setCustomerServiceOperation(CustomerServiceOperation.DELETE)
                .setLandingPageWechatCustomerService(lpwcs)
                .setAgentId(TenantContextHolder.get());
            trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(offlineMessage);
            //清除机器人活码
            RobotCustomerContactDeleteDto deleteParam = new RobotCustomerContactDeleteDto();
            deleteParam.setCorpId(lpwcs.getCorpId())
                .setRobotCustomerContactConfigId(lpwcs.getRobotCustomerContactConfigId())
                .setRobotCustomerContactQiniuPath(lpwcs.getRobotCustomerContactQiniuPath());
            robotCustomerContactSender.sendDeleteQrCode(deleteParam);
        });
        //在执行完删除后,发送客服分组客服变更通知
        if (CollectionUtils.isNotEmpty(groupIdsByServiceIds)) {
            landingPageCustomerServiceSender.sendCallBackMessage(new WechatCustomerServiceGroupDto().setGroupIds(Sets.newHashSet(groupIdsByServiceIds)));
        }
    }

    private void removeGroupByBatch(List<LandingPageWechatCustomerService> customerServices,
                                    WechatBatchOperationDto wechatBatchOperationDto) {
        customerServices.forEach(lpwcs -> {
            List<LandingPageWechatCustomerServiceGroupRel> list = landingPageWechatCustomerServiceGroupRelService.list(
                new LambdaQueryWrapper<LandingPageWechatCustomerServiceGroupRel>()
                    .eq(LandingPageWechatCustomerServiceGroupRel::getAdvertiserAccountGroupId, wechatBatchOperationDto.getAccountGroupId())
                    .eq(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceId, lpwcs.getId())
            );    //移除分组需要先将之前的拿到，然后再去掉移除的，剩下的传入creatRelData中
            List<Long> collect = list.stream()
                .map(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceGroupId)
                .collect(Collectors.toList());
            String join = Joiner.on(",")
                .join(collect.stream().filter(e -> !e.equals(wechatBatchOperationDto.getWechatCustomerRemoveGroupId()))
                    .collect(Collectors.toList()));
            lpwcs.setWechatCustomerServiceGroupIds(join);
            this.createRelData(lpwcs);
            CustomerServiceOperationMessage removeMessage = new CustomerServiceOperationMessage();
            removeMessage.setCustomerServiceOperation(CustomerServiceOperation.MOVE_OUT_GROUPING)
                .setLandingPageWechatCustomerService(lpwcs)
                .setAgentId(TenantContextHolder.get())
                .setWechatCustomerRemoveGroupId(wechatBatchOperationDto.getWechatCustomerRemoveGroupId());
            trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(removeMessage);
        });
        //发送变更项目客服分组多人活码队列
        CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
        dto.setAgentId(TenantContextHolder.get())
            .setType(CustomerServiceChangeEnum.REMOVE_CUSTOMER_GROUP)
            .setLandingPageCustomerServiceGroupIds(Sets.newHashSet(wechatBatchOperationDto.getWechatCustomerRemoveGroupId()));
        multiplayerCodeSender.sendCustomerServiceChange(dto);
        //发送客服分组客服变更通知
        landingPageCustomerServiceSender.sendCallBackMessage(new WechatCustomerServiceGroupDto().
            setGroupIds(Sets.newHashSet(wechatBatchOperationDto.getWechatCustomerRemoveGroupId())));
    }

    private void editAbnormalMonitorByBatch(List<LandingPageWechatCustomerService> customerServices,
                                            WechatBatchOperationDto wechatBatchOperationDto) {
        customerServices.forEach(lpwcs -> {
            lpwcs.setAbnormalMonitor(wechatBatchOperationDto.getWechatCustomer().getAbnormalMonitor())
                .setNotSupportAutoOnline(wechatBatchOperationDto.getWechatCustomer().getNotSupportAutoOnline())
                .setSupportAutoOnlineStatus(wechatBatchOperationDto.getWechatCustomer().getSupportAutoOnlineStatus())
                .setSupportAutoOnline(wechatBatchOperationDto.getWechatCustomer().getSupportAutoOnline())
                .setSpecifyOnlineTime(wechatBatchOperationDto.getWechatCustomer().getSpecifyOnlineTime())
                .setAbnormalMonitorStatus(AbnormalMonitorStatus.ENABLE);
            this.saveOrUpdate(lpwcs);
            this.saveAbnormalMonitor(lpwcs);
            if (StringUtils.isNotBlank(lpwcs.getWechatUserId())) {
                abnormalMonitorRedis.reset(lpwcs.getWechatUserId(), lpwcs.getAdvertiserAccountGroupId(), LocalDateTime.now());
            }
        });
    }

    private void closeAbnormalMonitorByBatch(List<LandingPageWechatCustomerService> customerServices) {
        customerServices.forEach(lpwcs -> {
            lpwcs.setAbnormalMonitorStatus(AbnormalMonitorStatus.UNOPENED);
            this.saveOrUpdate(lpwcs);
        });
    }

    public static Boolean isNotNumber(String val) {
        if (StringUtils.isBlank(val)) {
            return false;
        }
        Pattern pattern = compile("[0-9]*");
        return !pattern.matcher(val).matches();
    }

    /**
     * 上传企业微信客服信息
     */
    public TemplateUploadResultDto templateUpload(String agentId, TemplateUploadDto templateUploadDto,
                                                  UploadTypeEnum uploadType, final Long advertiserAccountGroupId) {
        TemplateUploadResultDto templateUploadResultDto = new TemplateUploadResultDto().setCode(PASSCODE);

        String zipTotal = templateUploadDto.getWechatBatchZip();
        WechatUploadRecord one = this.wechatUploadRecordService.getOne(new LambdaQueryWrapper<WechatUploadRecord>().eq(WechatUploadRecord::getUrl, zipTotal)
            .eq(WechatUploadRecord::getUploadStatus, 1));
        if (one == null) {
            return templateUploadResultDto.setCode(PROCESSING).setMessage("处理中，请稍后...");
        }
        //上传完毕
        InputStream inputStream = new ByteArrayInputStream(wechatUploadRecordService.download(PREFIX_CERTIFICATE + "/" + wechatUploadRecordService.getOne(new LambdaQueryWrapper<WechatUploadRecord>().eq(WechatUploadRecord::getUrl, templateUploadDto.getWechatBatchCsv()))
            .getPath()));
        List<EnterpriseWechatImportDTO> csvList;
        try {
            csvList = CsvUtil.readXlsx(inputStream);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return templateUploadResultDto.setCode(NOTPASSCODE).setMessage("excel文件格式不正确");
        }
        if (CollectionUtils.isEmpty(csvList)) {
            return new TemplateUploadResultDto().setCode(NOTPASSCODE).setMessage("excel文件内最少添加1条信息");
        }
        //6.条数超出上线
        if (csvList.size() > 300) {
            return new TemplateUploadResultDto().setCode(NOTPASSCODE).setMessage("excel文件内最多添加300条信息");
        }
        //格式化excel数据：去除空值、首尾空格
        EnterpriseWechatImportDTO ewid;
        Iterator<EnterpriseWechatImportDTO> csvListIterator = csvList.iterator();
        while (csvListIterator.hasNext()) {
            ewid = csvListIterator.next();
            //String wcName = ewid.getWcName(), wcUserId = ewid.getWcUserId();
            String wcName = ewid.getWcName();
            //为空值，直接删除该行
            if (StringUtils.isBlank(StringUtils.trim(wcName))) {
                csvListIterator.remove();
                continue;
            }
            ewid.setWcName((StringUtils.trim(ewid.getWcName())))
                //修复bug：#15006 【YIYE_AGENT_V1.119.0】微信客服文档权重值不符合规范时批量上传成功
                .setQrCodeWeight((StringUtils.isBlank(StringUtils.trim(ewid.getQrCodeWeight()))) ? "1" : StringUtils.trim(ewid.getQrCodeWeight()))
            ;
        }
        //压缩包：图片数据
        List<WechatUploadRecord> zipList = wechatUploadRecordService.list(new LambdaQueryWrapper<WechatUploadRecord>().eq(WechatUploadRecord::getParentPackageId, one.getId()));
        WechatUploadRecord wur;
        Iterator<WechatUploadRecord> zipListIterator = zipList.iterator();
        while (zipListIterator.hasNext()) {
            wur = zipListIterator.next();
            String wcName = wur.getOriginalName();
            wur.setOriginalName(StringUtils.isNotBlank(StringUtils.trim(wcName)) ? StringUtils.trim((wcName.substring(0, wcName.lastIndexOf(".")))) : StringUtils.trim(wcName));
            if (StringUtils.isBlank(wur.getOriginalName())) {
                zipListIterator.remove();
            }
        }
        //执行动作
        switch (uploadType) {
            //上传操作
            case UPLOAD:
                //校验数据
                templateUploadResultDto = processWechatPicAndRel(agentId, csvList, zipList, advertiserAccountGroupId);
                //没有冲突数据，直接保存
                if (templateUploadResultDto.getCode().equals(PASSCODE)) {
                    csvList = csvList.parallelStream().filter(e -> StringUtils.isNotBlank(e.getWcName()))
                        .collect(Collectors.toList());
                    saveAll(agentId, csvList, zipList, templateUploadDto, advertiserAccountGroupId);
                }
                break;
            //覆盖操作
            case COVER:
                csvList = csvList.parallelStream().filter(e -> StringUtils.isNotBlank(e.getWcName()))
                    .collect(Collectors.toList());
                coverList(agentId, csvList, zipList, templateUploadDto, advertiserAccountGroupId);
                break;
            //跳过操作
            case SKIP:
                csvList = csvList.parallelStream().filter(e -> StringUtils.isNotBlank(e.getWcName()))
                    .collect(Collectors.toList());
                skipList(agentId, csvList, zipList, templateUploadDto, advertiserAccountGroupId);
                break;
            default:
                return templateUploadResultDto.setCode(NOTPASSCODE).setMessage("未知的操作类型！");
        }

        //清除缓存
        List<Long> wechatCustomerServiceGroupIds = templateUploadDto.getWechatCustomerServiceGroupIds();
        if (CollectionUtils.isNotEmpty(wechatCustomerServiceGroupIds)) {
            wechatCustomerServiceGroupIds.forEach(id -> landingPageWechatCustomerServiceRedis.deleteWechatServiceDataByGroupId(id));
            //对自定义排序编号进行处理
            log.info("批量上传导入微信客服,自定义排序编号初始化,wechatCustomerServiceGroupIds={}", wechatCustomerServiceGroupIds);
            landingPageCustomerServiceSender.sendMessageChangeOrderNum(new LandingPageWechatCustomerServiceGroupChangeDTO().setGroupChangeEventType(GroupChangeEventType.BATCH_UPLOAD)
                .setBatchUploadGroupIds(wechatCustomerServiceGroupIds));
        }
        return templateUploadResultDto;
    }


    //跳过 两种情况，userId重复，客服名称重复，这两种情况均是由csv的进行覆盖
    @Transactional(rollbackFor = Exception.class)
    public void skipList(String agentId, List<EnterpriseWechatImportDTO> csvList, List<WechatUploadRecord> zipList,
                         TemplateUploadDto templateUploadDto, Long accountGroupId) {
        String groupIds = templateUploadDto.getWechatCustomerServiceGroupIds().stream().map(String::valueOf)
            .collect(Collectors.joining(","));
        Map<String, WechatUploadRecord> collect = zipList.stream()
            .collect(Collectors.toMap(WechatUploadRecord::getOriginalName, Function.identity()));
        //翻转表格的顺序
        Collections.reverse(csvList);   //修复bug：#14951 【YIYE_AGENT_V1.119.0】客服上传成功没有按列表顺序展示
        //首先过滤出跳过的
        LandingPageWechatCustomerService one;
        for (EnterpriseWechatImportDTO importDTO : csvList) {
            one = this.getOne(new LambdaQueryWrapper<LandingPageWechatCustomerService>()
                .eq(LandingPageWechatCustomerService::getAdvertiserAccountGroupId, accountGroupId)
                .eq(LandingPageWechatCustomerService::getWechatUserName, importDTO.getWcName())
            );
            if (one == null) {
                one = new LandingPageWechatCustomerService().setWechatCustomerServiceGroupIds(groupIds)
                    .setAdvertiserAccountGroupId(accountGroupId).setWechatUserName(importDTO.getWcName());
                if (StringUtils.isBlank(importDTO.getQrCodeWeight())) {
                    one.setQrCodeWeight(1);
                } else {
                    one.setQrCodeWeight(Integer.parseInt(importDTO.getQrCodeWeight()));
                }
                one.setQrCodeImgUrl(collect.get(importDTO.getWcName()).getUrl())
                    .setOnlineStatus(OnlineStatusType.ENABLE);
                if (templateUploadDto.getAutoRule() != null) {
                    one.setAutoRuleStatus(AutoRuleStatus.ENABLE).setAutoRule(templateUploadDto.getAutoRule());
                } else {
                    one.setAutoRuleStatus(AutoRuleStatus.UNOPENED);
                }
                if (templateUploadDto.getAbnormalMonitor() != null) {
                    one.setAbnormalMonitorStatus(AbnormalMonitorStatus.ENABLE)
                        .setAbnormalMonitor(templateUploadDto.getAbnormalMonitor())
                        .setNotSupportAutoOnline(templateUploadDto.getNotSupportAutoOnline())
                        .setSupportAutoOnlineStatus(templateUploadDto.getSupportAutoOnlineStatus())
                        .setSupportAutoOnline(templateUploadDto.getSupportAutoOnline())
                        .setSpecifyOnlineTime(templateUploadDto.getSpecifyOnlineTime());
                } else {
                    one.setAbnormalMonitorStatus(AbnormalMonitorStatus.UNOPENED);
                }
                this.saveData(agentId, one.setCreatedAt(Instant.now()));
            }
        }
    }

    //覆盖, 两种情况，userId重复，客服名称重复，这两种情况均是由csv的进行覆盖
    @Transactional(rollbackFor = Exception.class)
    public void coverList(String agentId, List<EnterpriseWechatImportDTO> csvList, List<WechatUploadRecord> zipList,
                          TemplateUploadDto templateUploadDto, Long accountGroupId) {
        String groupIds = templateUploadDto.getWechatCustomerServiceGroupIds().stream().map(String::valueOf)
            .collect(Collectors.joining(","));
        Map<String, WechatUploadRecord> collect = zipList.stream()
            .collect(Collectors.toMap(WechatUploadRecord::getOriginalName, Function.identity()));
        //翻转表格的顺序
        Collections.reverse(csvList);   //修复bug：#14951 【YIYE_AGENT_V1.119.0】客服上传成功没有按列表顺序展示
        LandingPageWechatCustomerService one;
        for (EnterpriseWechatImportDTO importDTO : csvList) {
            one = this.getOne(new LambdaQueryWrapper<LandingPageWechatCustomerService>()
                .eq(LandingPageWechatCustomerService::getAdvertiserAccountGroupId, accountGroupId)
                .eq(LandingPageWechatCustomerService::getWechatUserName, importDTO.getWcName())
            );
            if (one == null) {
                one = new LandingPageWechatCustomerService().setAdvertiserAccountGroupId(accountGroupId)
                    .setWechatCustomerServiceGroupIds(groupIds).setWechatUserName(importDTO.getWcName());
                if (StringUtils.isBlank(importDTO.getQrCodeWeight())) {
                    one.setQrCodeWeight(1);
                } else {
                    one.setQrCodeWeight(Integer.parseInt(importDTO.getQrCodeWeight()));
                }
                one.setQrCodeImgUrl(collect.get(importDTO.getWcName()).getUrl())
                    .setOnlineStatus(OnlineStatusType.ENABLE);
                if (templateUploadDto.getAutoRule() != null) {
                    one.setAutoRuleStatus(AutoRuleStatus.ENABLE).setAutoRule(templateUploadDto.getAutoRule());
                } else {
                    one.setAutoRuleStatus(AutoRuleStatus.UNOPENED);
                }
                if (templateUploadDto.getAbnormalMonitor() != null) {
                    one.setAbnormalMonitorStatus(AbnormalMonitorStatus.ENABLE)
                        .setAbnormalMonitor(templateUploadDto.getAbnormalMonitor())
                        .setNotSupportAutoOnline(templateUploadDto.getNotSupportAutoOnline())
                        .setSupportAutoOnlineStatus(templateUploadDto.getSupportAutoOnlineStatus())
                        .setSupportAutoOnline(templateUploadDto.getSupportAutoOnline())
                        .setSpecifyOnlineTime(templateUploadDto.getSpecifyOnlineTime());
                } else {
                    one.setAbnormalMonitorStatus(AbnormalMonitorStatus.UNOPENED);
                }
                this.saveData(agentId, one.setCreatedAt(Instant.now()));
            } else {
                one.setWechatCustomerServiceGroupIds(groupIds).setAdvertiserAccountGroupId(accountGroupId)
                    .setWechatUserName(importDTO.getWcName());
                if (StringUtils.isBlank(importDTO.getQrCodeWeight())) {
                    one.setQrCodeWeight(1);
                } else {
                    one.setQrCodeWeight(Integer.parseInt(importDTO.getQrCodeWeight()));
                }
                one.setQrCodeImgUrl(collect.get(importDTO.getWcName()).getUrl())
                    .setOnlineStatus(OnlineStatusType.ENABLE);
                if (templateUploadDto.getAutoRule() != null) {
                    one.setAutoRuleStatus(AutoRuleStatus.ENABLE).setAutoRule(templateUploadDto.getAutoRule());
                } else {
                    one.setAutoRuleStatus(AutoRuleStatus.UNOPENED);
                }
                if (templateUploadDto.getAbnormalMonitor() != null) {
                    one.setAbnormalMonitorStatus(AbnormalMonitorStatus.ENABLE)
                        .setAbnormalMonitor(templateUploadDto.getAbnormalMonitor())
                        .setNotSupportAutoOnline(templateUploadDto.getNotSupportAutoOnline())
                        .setSupportAutoOnlineStatus(templateUploadDto.getSupportAutoOnlineStatus())
                        .setSupportAutoOnline(templateUploadDto.getSupportAutoOnline())
                        .setSpecifyOnlineTime(templateUploadDto.getSpecifyOnlineTime());
                } else {
                    one.setAbnormalMonitorStatus(AbnormalMonitorStatus.UNOPENED);
                }
                this.updateDataById(agentId, one.setCreatedAt(Instant.now()));
            }
        }
    }

    //直接保存
    @Transactional(rollbackFor = Exception.class)
    public void saveAll(String agentId, List<EnterpriseWechatImportDTO> csvList, List<WechatUploadRecord> zipList,
                        TemplateUploadDto templateUploadDto, Long accountGroupId) {
        //将导入的record转为键为客服名称，值为这个对象的map，方便这边的list进行查询
        zipList.forEach(e -> e.setOriginalName(e.getOriginalName()));
        Map<String, WechatUploadRecord> collect = zipList.stream()
            .collect(Collectors.toMap(WechatUploadRecord::getOriginalName, Function.identity()));
        String groupIds = templateUploadDto.getWechatCustomerServiceGroupIds().stream().map(String::valueOf)
            .collect(Collectors.joining(","));
        //翻转表格的顺序
        Collections.reverse(csvList);   //修复bug：#14951 【YIYE_AGENT_V1.119.0】客服上传成功没有按列表顺序展示
        for (int i = 0; i < csvList.size(); i++) {
            EnterpriseWechatImportDTO importDto = csvList.get(i);
            LandingPageWechatCustomerService landingPageWechatCustomerService = new LandingPageWechatCustomerService()
                .setAdvertiserAccountGroupId(accountGroupId).setWechatUserName(importDto.getWcName());
            if (StringUtils.isBlank(importDto.getQrCodeWeight())) {
                landingPageWechatCustomerService.setQrCodeWeight(1);
            } else {
                landingPageWechatCustomerService.setQrCodeWeight(Integer.parseInt(importDto.getQrCodeWeight()));
            }
            landingPageWechatCustomerService.setQrCodeImgUrl(collect.get(importDto.getWcName()).getUrl())
                .setOnlineStatus(OnlineStatusType.ENABLE).setWechatCustomerServiceGroupIds(groupIds);
            if (templateUploadDto.getAutoRule() != null) {
                landingPageWechatCustomerService.setAutoRuleStatus(AutoRuleStatus.ENABLE)
                    .setAutoRule(templateUploadDto.getAutoRule());
            } else {
                landingPageWechatCustomerService.setAutoRuleStatus(AutoRuleStatus.UNOPENED);
            }
            if (templateUploadDto.getAbnormalMonitor() != null) {
                landingPageWechatCustomerService.setAbnormalMonitorStatus(AbnormalMonitorStatus.ENABLE)
                    .setAbnormalMonitor(templateUploadDto.getAbnormalMonitor())
                    .setNotSupportAutoOnline(templateUploadDto.getNotSupportAutoOnline())
                    .setSupportAutoOnlineStatus(templateUploadDto.getSupportAutoOnlineStatus())
                    .setSupportAutoOnline(templateUploadDto.getSupportAutoOnline())
                    .setSpecifyOnlineTime(templateUploadDto.getSpecifyOnlineTime());
            } else {
                landingPageWechatCustomerService.setAbnormalMonitorStatus(AbnormalMonitorStatus.UNOPENED);
            }
            this.saveData(agentId, landingPageWechatCustomerService.setCreatedAt(Instant.now()));
        }
    }

    public String upload(MultipartFile files, UploadFileTypeEnum type) {

        String accessUrl;
        byte[] fileByteData;
        String originalFilename;
        String suffix;
        String path;
        originalFilename = files.getOriginalFilename();
        if (null == originalFilename || "".equals(originalFilename) || originalFilename.length() <= 0) {
            throw new RestException(FILE_UPLOAD);
        }
        suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        //文件路径
        try {
            fileByteData = files.getBytes();
        } catch (IOException e) {
            throw new RestException(FILE_UPLOAD);
        }
        WechatUploadRecord wechatUploadRecord = new WechatUploadRecord();
        String s = UuidUtils.generateUuid() + suffix;
        wechatUploadRecord.setPath(s);
        accessUrl = PREFIX_CERTIFICATE + "/" + s;
        try {
            path = fileUpload.uploadQiniuYun(fileByteData, accessUrl);
        } catch (Exception e) {
            throw new RestException(FILE_UPLOAD);
        }
        if (type.equals(UploadFileTypeEnum.ZIP)) {
            //zip包则找出他的file数量，并发送mq
            wechatUploadRecord.setOriginalName(files.getOriginalFilename());
            wechatUploadRecord.setUrl(path);
            wechatUploadRecordService.save(wechatUploadRecord);
            uploadSender.sendWechatUploadFile(wechatUploadRecord.getId());
        } else {
            wechatUploadRecord.setOriginalName(files.getOriginalFilename());
            wechatUploadRecord.setUrl(path);
            wechatUploadRecordService.save(wechatUploadRecord);
        }
        return path;

    }

    //这里写csv文件与zip包内图片的对比
    private TemplateUploadResultDto processWechatPicAndRel(String agentId, List<EnterpriseWechatImportDTO> csvList,
                                                           List<WechatUploadRecord> zipList,
                                                           final Long accountGroupId) {
        //首先校验查询EXCEL文件是否符合
        csvList = csvList.parallelStream().filter(e -> StringUtils.isNotBlank(e.getWcName()))
            .collect(Collectors.toList());
        TemplateUploadResultDto processWechatPicAndRelDto = examCsvList(csvList);
        if (!PASSCODE.equals(processWechatPicAndRelDto.getCode())) {
            return processWechatPicAndRelDto;
        }
        //查询二维码图片是否符合
        processWechatPicAndRelDto = examZipList(zipList);
        if (!PASSCODE.equals(processWechatPicAndRelDto.getCode())) {
            return processWechatPicAndRelDto;
        }
        //匹配两文件名称
        processWechatPicAndRelDto = matchCsvAndZip(csvList, zipList);
        if (!PASSCODE.equals(processWechatPicAndRelDto.getCode())) {
            return processWechatPicAndRelDto;
        }
        //与数据库的进行校验
        processWechatPicAndRelDto = matchDataToDb(agentId, csvList, accountGroupId);
        if (!PASSCODE.equals(processWechatPicAndRelDto.getCode())) {
            return processWechatPicAndRelDto;
        }
        processWechatPicAndRelDto.setCode(PASSCODE);
        return processWechatPicAndRelDto;
    }

    //首先校验查询EXCEL文件是否符合要求
    private TemplateUploadResultDto examCsvList(List<EnterpriseWechatImportDTO> csvList) {
        TemplateUploadResultDto processWechatPicAndRelDto = new TemplateUploadResultDto();
        //空行做过滤处理

        //无名称，有userId
        for (int i = 0; i < csvList.size(); i++) {
            if (StringUtils.isBlank(csvList.get(i).getWcName())) {
                processWechatPicAndRelDto.setCode(NOTPASSCODE);
                processWechatPicAndRelDto.setMessage("excel文件内包含部分客服名称未填写，请填写后重新上传");
                return processWechatPicAndRelDto;
            }
        }
        //权重数值不正确
        List<EnterpriseWechatImportDTO> collect1 = csvList.stream()
            .filter(e -> (StringUtils.isNotBlank(StringUtils.trim(e.getQrCodeWeight())) && !"0.0".equals(e.getQrCodeWeight())) && (e.getQrCodeWeight()
                .contains(".") || isNotNumber(e.getQrCodeWeight()) || Integer.valueOf(e.getQrCodeWeight()) < 0))
            .collect(Collectors.toList());
        if (collect1.size() > 0) {
            String message = collect1.stream().map(vo -> String.valueOf(vo.getWcName()))
                .collect(Collectors.joining("；"));
            processWechatPicAndRelDto.setMessage("excel文件内部分客服权重设值不正确，请输入正整数，客服名称分别是：" + message);
            processWechatPicAndRelDto.setCode(NOTPASSCODE);
            return processWechatPicAndRelDto;
        }
        //条数超出上线
        if (csvList.size() > 300) {
            processWechatPicAndRelDto.setCode(NOTPASSCODE);
            processWechatPicAndRelDto.setMessage("excel文件内最多添加300条信息");
            return processWechatPicAndRelDto;
        }
        //文件内名称重复
        Map<String, Long> countMap = csvList.stream()
            .collect(Collectors.groupingBy(EnterpriseWechatImportDTO::getWcName, Collectors.counting()));
        List<String> dupilcateList = countMap.keySet().stream().filter(key -> countMap.get(key) > 1).distinct()
            .collect(Collectors.toList());
        if (dupilcateList.size() > 0) {
            processWechatPicAndRelDto.setCode(NOTPASSCODE);
            String message = String.join("；", dupilcateList);
            processWechatPicAndRelDto.setMessage("excel文件内部分客服名称重复，重复客服名称分别是：" + message);
            return processWechatPicAndRelDto;
        }
        processWechatPicAndRelDto.setCode(PASSCODE);
        processWechatPicAndRelDto.setMessage(PASSSTRING);
        return processWechatPicAndRelDto;

    }


    //查询二维码图片是否符合
    private TemplateUploadResultDto examZipList(List<WechatUploadRecord> zipList) {
        TemplateUploadResultDto processWechatPicAndRelDto = new TemplateUploadResultDto();
        Map<String, Long> countMap2 = zipList.stream()
            .collect(Collectors.groupingBy(WechatUploadRecord::getOriginalName, Collectors.counting()));
        List<String> collect2 = countMap2.keySet().stream().filter(key -> countMap2.get(key) > 1).distinct()
            .collect(Collectors.toList());
        if (collect2.size() > 0) {
            processWechatPicAndRelDto.setCode(NOTPASSCODE);
            String message = String.join("；", collect2);
            processWechatPicAndRelDto.setMessage("部分二维码图片名称重复，二维码图片名称分别是：" + message);
            return processWechatPicAndRelDto;
        }
        //含其他格式文件过滤处理,已在listener做了处理
        processWechatPicAndRelDto.setCode(PASSCODE);
        processWechatPicAndRelDto.setMessage(PASSSTRING);
        return processWechatPicAndRelDto;
    }

    // 匹配两文件名称
    private TemplateUploadResultDto matchCsvAndZip(List<EnterpriseWechatImportDTO> csvList,
                                                   List<WechatUploadRecord> zipList) {
        TemplateUploadResultDto processWechatPicAndRelDto = new TemplateUploadResultDto();
        //二维码名称匹配客服
        List<String> collect = csvList.stream().map(EnterpriseWechatImportDTO::getWcName).collect(Collectors.toList());
        List<String> picList = zipList.stream().map(WechatUploadRecord::getOriginalName).collect(Collectors.toList());

        List<String> result = collect.stream().filter(p -> !picList.contains(p)).collect(Collectors.toList());
        List<String> result1 = picList.stream().filter(p -> !collect.contains(p)).collect(Collectors.toList());
        if (result.size() > 0) {
            processWechatPicAndRelDto.setCode(NOTPASSCODE);
            String message = String.join("；", result);
            //11.客服名称未匹配到对应二维码
            return processWechatPicAndRelDto.setMessage("部分客服未匹配到二维码，客服名称分别是：" + message);
        }
        if (result1.size() > 0) {
            processWechatPicAndRelDto.setCode(NOTPASSCODE);
            String message = String.join("；", result1);
            //10.二维码名称未匹配到对应客服
            processWechatPicAndRelDto.setMessage("部分二维码名称未匹配到对应名称的客服，二维码图片名称分别是：" + message);
            return processWechatPicAndRelDto;
        }
        processWechatPicAndRelDto.setCode(PASSCODE);
        processWechatPicAndRelDto.setMessage(PASSSTRING);
        return processWechatPicAndRelDto;
    }

    //与数据库的进行校验
    private TemplateUploadResultDto matchDataToDb(String agentId, List<EnterpriseWechatImportDTO> csvList,
                                                  final Long advertiserAccountGroupId) {
        TemplateUploadResultDto turDto = new TemplateUploadResultDto().setCode(PASSCODE);
        List<String> userNamesRestult = new ArrayList<>();
        List<LandingPageWechatCustomerService> userNameList = new ArrayList<>();            //重复数据：名称重复
        List<EnterpriseWechatImportDTO> allCfCanFgList = new ArrayList<>();                 //可覆盖：【客服昵称】相等
        final List<String> userNames = csvList.stream()
            .filter(e -> StringUtils.isNotBlank(StringUtils.trim(e.getWcName()))).collect(Collectors.toList()).stream()
            .map(EnterpriseWechatImportDTO::getWcName).collect(Collectors.toList());
        //根据excel中所有【客服名称】，查询数据
        if (CollectionUtils.isNotEmpty(userNames)) {
            userNameList = baseMapper.selectList(new LambdaQueryWrapper<LandingPageWechatCustomerService>().eq(LandingPageWechatCustomerService::getAdvertiserAccountGroupId, advertiserAccountGroupId)
                .in(LandingPageWechatCustomerService::getWechatUserName, userNames));
            userNamesRestult = userNameList.stream().map(LandingPageWechatCustomerService::getWechatUserName)
                .collect(Collectors.toList());
        }
        for (EnterpriseWechatImportDTO importDTO : csvList) {
            String wcName = importDTO.getWcName();
            if (userNamesRestult.contains(wcName)) {
                allCfCanFgList.add(importDTO);
            }
        }
        // ----------------------------- 1、一个都没有重复的，直接返回 -----------------------------
        if (userNameList.isEmpty()) {
            return turDto.setCode(PASSCODE);
        }
        if (!allCfCanFgList.isEmpty()) {
            return turDto.setCode(POPCODE)
                .setMessage("excel文件内包含部分客服名称已存在，客服名称分别是：" + allCfCanFgList.stream()
                    .map(EnterpriseWechatImportDTO::getWcName).collect(Collectors.joining("；")));
        }
        return turDto;
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    public LandingPageWechatCustomerService detail(Long id) {
        LandingPageWechatCustomerService service = this.getById(id);
        if (service != null) {
            //获取自动化规则
            List<LandingPageWechatCustomerServiceAutoRule> autoRule = landingPageWechatCustomerServiceAutoRuleService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>()
                .eq(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, id));
            service.setAutoRule(autoRule);
            //获取异常监测
            List<LandingPageWechatCustomerServiceAbnormalMonitor> abnormalMonitor = landingPageWechatCustomerServiceAbnormalMonitorService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAbnormalMonitor>()
                .eq(LandingPageWechatCustomerServiceAbnormalMonitor::getLandingPageWechatCustomerServiceId, id));
            service.setAbnormalMonitor(abnormalMonitor);
            Long checkUserId = agentConfService.getCheckUserId(null);
            LandingPageWechatCustomerService tmp = baseMapper.getGroupInfo(checkUserId, id);
            if (tmp != null) {
                service.setWechatCustomerServiceGroupIds(tmp.getWechatCustomerServiceGroupIds())
                    .setGroupNames(tmp.getGroupNames());
            }
        }
        return service;
    }

    /**
     * 自动下线
     *
     * @param ids
     */
    public void autoOffline(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        //更新数据库
        this.update(new LambdaUpdateWrapper<LandingPageWechatCustomerService>()
            .in(LandingPageWechatCustomerService::getId, ids)
            .set(LandingPageWechatCustomerService::getOnlineStatus, OnlineStatusType.DISABLE)
            .set(LandingPageWechatCustomerService::getOfflineStatus, OfflineStatus.AUTO)
            .set(LandingPageWechatCustomerService::getOfflineTime, Instant.now()));
        //清除缓存
        this.deleteCacheByServiceIds(ids);
        //自动下线的代码需要在这里增加
        userOperationLogDetailActionSender.sendSystemOnlineOperationLogSave(new SystemOnlineOperationLogDto().setOnlineStatusType(OnlineStatusType.DISABLE)
            .setIds(ids));

    }

    /**
     * 客服异常监测下线
     *
     * @param ids
     */
    public void abnormalMonitorOffline(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        //更新数据库
        this.update(new LambdaUpdateWrapper<LandingPageWechatCustomerService>()
            .in(LandingPageWechatCustomerService::getId, ids)
            .set(LandingPageWechatCustomerService::getOnlineStatus, OnlineStatusType.DISABLE)
            .set(LandingPageWechatCustomerService::getOfflineStatus, OfflineStatus.ABNORMAL_MONITOR)
            .set(LandingPageWechatCustomerService::getOfflineTime, Instant.now()));
        //发送客服分组客服变更通知
        landingPageCustomerServiceSender.sendCallBackMessage(new WechatCustomerServiceGroupDto().setWechatCustomerServiceIds(Sets.newHashSet(ids)));
        //清除缓存
        this.deleteCacheByServiceIds(ids);
        userOperationLogDetailActionSender.sendAbnormalMonitorLog(new AbnormalMonitorLogDto().setIds(ids));
    }

    /**
     * 客服信息
     *
     * @param id
     * @return
     */
    public LandingPageWechatCustomerService getCustomerService(Long id) {
        LandingPageWechatCustomerService service = this.getById(id);
        if (service != null) {
            //获取自动化规则
            List<LandingPageWechatCustomerServiceAutoRule> autoRule = landingPageWechatCustomerServiceAutoRuleService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>()
                .eq(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, id));
            service.setAutoRule(autoRule);
        }
        return service;
    }

    /**
     * 校验是否符合下线规则
     *
     * @param id
     */

    public void checkOfflineRule(Long id) {
        LandingPageWechatCustomerService service = this.getCustomerService(id);
        if (service == null || Objects.isNull(service.getAutoRuleStatus()) || AutoRuleStatus.UNOPENED.equals(service.getAutoRuleStatus())) {
            return;
        }
        //自动化规则
        List<LandingPageWechatCustomerServiceAutoRule> autoRules = service.getAutoRule();
        if (CollectionUtils.isEmpty(autoRules)) {
            return;
        }
        //过滤掉不符合条件（自动化规则状态=开启、自动化规则类型=成功添加企业微信数、自动化规则类型=长按二维码识别数、下线规则大于0）的数据
        for (LandingPageWechatCustomerServiceAutoRule autoRule : autoRules) {
            if (autoRule == null || (!AutoRuleType.ADD_WORKWECHAT_NUM.equals(autoRule.getType()) && !AutoRuleType.IDENTIFY_QRCODE_NUM.equals(autoRule.getType()))
                || autoRule.getOfflineNum() == null || autoRule.getOfflineNum().compareTo(new BigDecimal(0)) < 1) {
                //message.append("客服").append(service.getWechatUserName()).append("触达自动化规则下线/n");
                continue;
            }
            InspectionCycleTime time = TimeUtils.getTimeByAutoRule(autoRule.getInspectionCycle(), autoRule.getTimeFrame());
            if (time == null) {
                continue;
            }
            Long count;
            if (AutoRuleType.IDENTIFY_QRCODE_NUM.equals(autoRule.getType())) {
                count = identifyQrcodeRecordService.countByStatistic(service.getId(), time.getStartTime(), time.getEndTime());
            } else if (AutoRuleType.ADD_WORKWECHAT_NUM.equals(autoRule.getType()) && StringUtils.isNotBlank(service.getWechatUserId())) {
                if (SwitchStatus.OPEN.equals(autoRule.getCustomizeDataRangeStatus())) {
                    if (CustomizeDataRange.CURRENT_PROJECT.equals(autoRule.getCustomizeDataRange())) {
                        //不包含其他方式，项目层级
                        count = customerService.countByStatistic(service.getWechatUserId(), service.getAdvertiserAccountGroupId(), true, time.getStartTime(), time.getEndTime());
                    } else {
                        //不包含其他方式，账户层级
                        count = customerService.countByStatistic(service.getWechatUserId(), null, true, time.getStartTime(), time.getEndTime());
                    }
                } else {
                    //包含其他方式
                    count = customerService.countByStatistic(service.getWechatUserId(), service.getAdvertiserAccountGroupId(), false, time.getStartTime(), time.getEndTime());
                }
            } else {
                continue;
            }
            //大于等于设定值
            if (new BigDecimal(count).compareTo(autoRule.getOfflineNum()) > -1) {
                throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_ONLINE_INVALID);
            }
        }
    }

    public List<LandingPageWechatCustomerService> getUserIdUnMatchingList(String corpId, List<Long> pmpIds) {
        if (CollectionUtils.isEmpty(pmpIds)) {
            return new ArrayList<>();
        }
        return baseMapper.getUserIdUnMatchingList(corpId, pmpIds);
    }

    /**
     * 批量修改对应PMP的微信客服-企业微信id字段
     *
     * @param pmpIds
     */
    public int updateWechatUserIdAndCorpIdByPmpIds(List<Long> pmpIds, String corpId) {
        if (CollectionUtils.isEmpty(pmpIds)) {
            return 0;
        }
        return baseMapper.updateWechatUserIdAndCorpIdByPmpIds(pmpIds, corpId);
    }

    /**
     * 更新微信客服信息（转赠、授权、变更授权可见范围）
     */
    public void updatePmpCustomerServices(EnterpriseWechat ew) {
        if (Objects.isNull(ew) || StringUtils.isBlank(StringUtils.trim(ew.getCorpid()))) {
            return;
        }
        String agentId = TenantContextHolder.get();
        //获取该企业微信与之管理的pmp
        //只更新这些pmp下的user_id cropid
        List<EnterpriseWechatsPmpRel> ewprList = enterpriseWechatsPmpRelService.getPmpRelByEnterpriseWechat(ew, agentId);
        List<Long> pmpIds = ewprList.stream().map(EnterpriseWechatsPmpRel::getAdvertiserAccountGroupId)
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(pmpIds)) {
            //更新与企业微信可见范围的user_id 一致的客服的 user_id 与 cropId
            baseMapper.updateCustomerServiceCheckDesc(pmpIds, ew.getCorpid());
            try {
                //1.202.0 移除可见范围需将远程获客链接删除
                //查询需要更新与企业微信可见范围的user_id 不一致的客服
                List<LandingPageWechatCustomerService> changeUpdateWechatCustomerServices = baseMapper.getChangeUpdateWechatCustomerServices(pmpIds, ew.getCorpid());
                //删除远程获客链接
                CustomerAcquisitionDeleteDto customerAcquisitionDeleteDto = new CustomerAcquisitionDeleteDto();
                customerAcquisitionDeleteDto.setChangeUpdateWechatCustomerServices(changeUpdateWechatCustomerServices)
                    .setEnterpriseWechat(ew);
                enterpriseWechatCustomerAcquisitionSender.deleteCustomerAcquisition(customerAcquisitionDeleteDto);
                //删除企微客服敏感信息
//                CustomerSensitiveInfoDeleteDto customerSensitiveInfoDeleteDto = new CustomerSensitiveInfoDeleteDto();
//                customerSensitiveInfoDeleteDto.setChangeUpdateWechatCustomerServices(changeUpdateWechatCustomerServices)
//                    .setEnterpriseWechat(ew);
//                landingPageSender.deleteSensitiveInfo(customerSensitiveInfoDeleteDto);
                //删除企微客服接口许可信息
                CustomerLicenseInfoDeleteDto customerLicenseInfoDeleteDto = new CustomerLicenseInfoDeleteDto();
                customerLicenseInfoDeleteDto.setChangeUpdateWechatCustomerServices(changeUpdateWechatCustomerServices)
                    .setEnterpriseWechat(ew);
                landingPageSender.deleteLicenseInfo(customerLicenseInfoDeleteDto);
                //1.208.0 还需删除联系我二维码
                CustomerContactDeleteDto customerContactDeleteDto = new CustomerContactDeleteDto();
                customerContactDeleteDto.setChangeUpdateWechatCustomerServices(changeUpdateWechatCustomerServices)
                    .setEnterpriseWechat(ew);
                landingPageWechatCustomerContactSender.sendDeleteQrCode(customerContactDeleteDto);
                //1.226.0 删除公众号企微渠道联系我二维码
                OfficialCustomerContactDeleteDto officialCustomerContactDeleteDto = new OfficialCustomerContactDeleteDto();
                officialCustomerContactDeleteDto.setChangeUpdateWechatCustomerServices(changeUpdateWechatCustomerServices)
                    .setEnterpriseWechat(ew);
                officialCustomerContactSender.sendDeleteQrCode(officialCustomerContactDeleteDto);
                //1.230.0 删除机器人活码
                RobotCustomerContactBatchDeleteDto robotCustomerContactBatchDeleteDto = new RobotCustomerContactBatchDeleteDto();
                robotCustomerContactBatchDeleteDto.setChangeUpdateWechatCustomerServices(changeUpdateWechatCustomerServices);
                robotCustomerContactSender.sendBatchDeleteQrCode(robotCustomerContactBatchDeleteDto);

                //1.272.0删除机器人动态渠道二维码
                robotDynamicCustomerContactGenerateSender.sendDeleteRobotDynamicQrCode(customerContactDeleteDto);

                //1.212.0 通知互动广告侧移除可见范围
                Optional.ofNullable(changeUpdateWechatCustomerServices).ifPresent(m -> {
                    m.stream().forEach(e -> {
                        CustomerServiceOperationMessage onLineMessage = new CustomerServiceOperationMessage();
                        onLineMessage.setCustomerServiceOperation(CustomerServiceOperation.UNVISIBILITY_RANGE)
                            .setLandingPageWechatCustomerService(e)
                            .setAgentId(agentId);
                        trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(onLineMessage);
                    });
                });
                if (CollectionUtils.isNotEmpty(changeUpdateWechatCustomerServices)) {
                    for (LandingPageWechatCustomerService changeUpdateWechatCustomerService : changeUpdateWechatCustomerServices) {
                        landingPageWechatCustomerServiceService.handleContactMeQrCode(changeUpdateWechatCustomerService.getQrCodeConfigRecordId(), null);
                    }
                    Set<Long> ids = changeUpdateWechatCustomerServices.stream()
                        .map(LandingPageWechatCustomerService::getId).collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(ids)) {
                        //获取分组id
                        List<Long> groupIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIds(ids);
                        if (CollectionUtils.isNotEmpty(groupIds)) {
                            CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
                            dto.setAgentId(agentId)
                                .setType(CustomerServiceChangeEnum.UNVISIBILITY)
                                .setLandingPageCustomerServiceGroupIds(new HashSet<>(groupIds));
                            //发送变更项目客服分组多人活码队列
                            multiplayerCodeSender.sendCustomerServiceChange(dto);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查询需要更新与企业微信可见范围的user_id 不一致的客服异常,ew=>{}", JSONObject.toJSONString(ew), e);
            }
            //更新与企业微信可见范围的user_id 不一致的客服的 user_id 与 cropId 为null
            //1.202.0 移除可见范围还需将获客链接信息置空
            baseMapper.updateCustomerServiceUnCheckDesc(pmpIds, ew.getCorpid());
            log.info("更新微信客服部门信息-ew={}；pmpIds={}；", ew, pmpIds);
            for (Long pmpId : pmpIds) {
                landingPageWechatCustomerServiceService.updateWorkWechatUserDepartment(pmpId, ew.getCorpid());
            }
        }


    }

    public int updateUserIdAndCropIdByAgentId(String agentId) {
        try {
            TenantContextHolder.set(agentId);
            return baseMapper.updateUserIdAndCropIdByAgentId();
        } catch (Exception e) {
            log.error("更新当前账户微信客服的userId、cropId发生错误 ======>> agentId={}；message={}", agentId, e.getMessage());
        } finally {
            TenantContextHolder.clearContext();
        }
        return 0;
    }

    /**
     * 清理没有绑定 企业微信 对应 pmp下的 user_id
     *
     * @param pmpIds
     * @return
     */
    public int updateCustomerServiceExcludeEnterpriseWechat(List<Long> pmpIds) {
        return baseMapper.updateCustomerServiceExcludeEnterpriseWechat(pmpIds);
    }

    private static final String COLUMN_SEPARATOR = ",";

    /**
     * 头表映射
     */
    private List<String[]> cvsHeaderArr = new ArrayList<String[]>() {
        {
            add(new String[]{"queryDataArea", "日期"});
            add(new String[]{"wechatUserName", "名称"});
            add(new String[]{"wechatName", "对应企业微信成员名称"});
            add(new String[]{"wechatUserId", "对应企业微信成员账号（userid）"});
            add(new String[]{"departmentName", "对应企业微信成员账号所属部门"});
            add(new String[]{"groupNames", "所属分组"});
            add(new String[]{"qrCodeWeight", "权重"});
            add(new String[]{"qrCodeShowNum", "二维码展示数"});
            add(new String[]{"identifyQrCodeNum", "二维码长按识别数"});
            add(new String[]{"qrcodeIdentityRate", "二维码长按识别率"});
            add(new String[]{"landAddWorkWechatNum", "落地页链路成功添加企业微信数"});
            add(new String[]{"otherAddWorkWechatNum", "其他方式成功添加企业微信数"});
            add(new String[]{"addWorkWechatRate", "成功添加企业微信率"});
            add(new String[]{"officialAccountQrCodeAddWorkWechatNum", "公众号内二维码发送成功添加企业微信数"});
            add(new String[]{"officialAccountQrCodeSendNum", "公众号内二维码发送次数"});
            add(new String[]{"autoRuleContent", "自动化规则"});
            add(new String[]{"abnormalMonitorContent", "客服异常监测配置"});
            add(new String[]{"licenseStatus", "接口许可状态"});
            add(new String[]{"licenseExpireTime", "接口许可到期时间"});
        }
    };

    public String formatCustomerList(List<LandingPageWechatCustomerService> lpwcsList, String queryDataArea) {
        //字段排序
        StringBuilder sb = new StringBuilder();
        List<String> columns = new ArrayList<>();
        for (String[] strArr : cvsHeaderArr) {
            columns.add(strArr[0]);
            sb.append(strArr[1]).append(COLUMN_SEPARATOR);
        }
        sb.append("\r\n");
        log.info("导出微信客服信息 sb参数 ====== >> {}", sb);
        log.info("导出微信客服信息 columns参数 ====== >> {}", columns);
        if (lpwcsList != null && !lpwcsList.isEmpty()) {
            try {
                for (LandingPageWechatCustomerService landingPageWechatCustomerService : lpwcsList) {
                    for (String field : columns) {
                        StringBuilder columnValue = new StringBuilder();
                        getFieldSwitch(sb, landingPageWechatCustomerService, field, columnValue, queryDataArea, lpwcsList);
                        columnValue = StringUtils.isNotBlank(columnValue) ? new StringBuilder(columnValue.toString()
                            .replaceAll("\\r", "").replaceAll("\\n", "").replaceAll(",", "，")) : columnValue;
                        sb.append(columnValue).append(",");
                    }
                    sb.append("\r\n");
                }
            } catch (Exception e) {
                log.error("解析csv异常", e);
                throw new RestException(ERROR_CUSTOMER_FILE_EXPORT_FAIL);
            }
        }
        return sb.toString();
    }

    private void getFieldSwitch(StringBuilder sb, LandingPageWechatCustomerService lpwcs, String field,
                                StringBuilder columnValue,
                                String queryDataArea, List<LandingPageWechatCustomerService> lpwcsList) throws IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        switch (field) {
            case "queryDataArea":
                sb.append(queryDataArea);
                break;
            case "onlineStatus":
                sb.append(null == lpwcs.getOnlineStatus() ? "" : lpwcs.getOnlineStatus().getName());
                break;
            case "autoRuleStatus":
                sb.append(null == lpwcs.getAutoRuleStatus() ? "" : lpwcs.getAutoRuleStatus().getName());
                break;
            case "autoRuleContent":
                String autoRuleContent = this.getAutoRuleContent(lpwcs, lpwcsList);
                if (StringUtils.isNotBlank(autoRuleContent)) {
                    sb.append(autoRuleContent);
                }
                break;
            case "abnormalMonitorContent":
                List<String> abnormalMonitor = lpwcs.getAbnormalMonitorContent();
                AbnormalMonitorStatus abnormalMonitorStatus = lpwcs.getAbnormalMonitorStatus();
                if (!Objects.equals(abnormalMonitorStatus, AbnormalMonitorStatus.ENABLE) || Objects.isNull(abnormalMonitor) || (Objects.nonNull(abnormalMonitor) && abnormalMonitor.isEmpty())) {
                    sb.append("");
                } else {
                    sb.append(StringUtils.join(abnormalMonitor, ";"));
                }
                break;
            case "departmentName":
                //数组片接
                sb.append(Optional.ofNullable(lpwcs.getDepartmentName()).map(m -> StringUtils.join(m, "/"))
                    .orElse(""));
                break;
            case "licenseStatus":
                sb.append(null == lpwcs.getLicenseStatus() ? "" : lpwcs.getLicenseStatus().getName());
                break;
            case "licenseExpireTime":
                sb.append(Optional.ofNullable(lpwcs.getLicenseExpireTime()).map(dateTimeFormatter::format).orElse(""));
                break;
            default:
                Object value = PropertyUtils.getProperty(lpwcs, field);
                columnValue.append(Objects.nonNull(value) ? value.toString().replace(COLUMN_SEPARATOR, "，") : "");
                break;
        }
    }

    /**
     * 封装自动化上下线内容
     *
     * @param landingPageWechatCustomerService 客服信息
     * @return 自动化上下线内容
     */
    private String getAutoRuleContent(LandingPageWechatCustomerService landingPageWechatCustomerService, List<LandingPageWechatCustomerService> lpwcsList) {

        List<Long> ids = lpwcsList.stream().map(LandingPageWechatCustomerService::getId).collect(Collectors.toList());
        StringBuilder result = new StringBuilder();
        int i = 1;
        if (ids.isEmpty()) {
            return null;
        }
        //获取自动化规则
        Map<Long, List<LandingPageWechatCustomerServiceAutoRule>> autoRuleMap = landingPageWechatCustomerServiceAutoRuleService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>()
                .in(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, ids)).stream()
            .collect(Collectors.groupingBy(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId));

        if (AutoRuleStatus.ENABLE.equals(landingPageWechatCustomerService.getAutoRuleStatus())) {

            List<LandingPageWechatCustomerServiceAutoRule> oldList = autoRuleMap.get(landingPageWechatCustomerService.getId());
            oldList.sort((o1, o2) -> {
                if (AutoRuleType.POINT_OF_TIME.equals(o1.getType()) && !AutoRuleType.POINT_OF_TIME.equals(o2.getType())) {
                    return -1;
                }
                if (!AutoRuleType.POINT_OF_TIME.equals(o1.getType()) && AutoRuleType.POINT_OF_TIME.equals(o2.getType())) {
                    return 1;
                }
                // 其他对象按照属性升序排列
                return Integer.compare(o1.getType().getId(), o2.getType().getId());
            });
            for (LandingPageWechatCustomerServiceAutoRule landingPageWechatCustomerServiceAutoRule : oldList) {
                String compareString = LandingPageWechatCustomerServiceAutoRule.getCompareStringWithdrawZero(landingPageWechatCustomerServiceAutoRule);
                if (StringUtils.isNotBlank(compareString)) {
                    result.append(i).append("、").append(compareString).append("; ");
                    i++;
                }
            }
            return result.toString();
        }
        return null;
    }

    public int updateWorkWechatUserInfo(Long advertiserAccountGroupId) {
        return baseMapper.updateWorkWechatUserInfo(advertiserAccountGroupId);
    }

    /**
     * 不变更头像信息
     *
     * @param advertiserAccountGroupId 项目ID
     * @return 更新结果
     */
    public int updateWorkWechatUserInfoWithoutHeadImg(Long advertiserAccountGroupId) {
        return baseMapper.updateWorkWechatUserInfoWithoutHeadImg(advertiserAccountGroupId);
    }

    /**
     * 更新部门信息
     *
     * @param advertiserAccountGroupId
     * @return
     */
    public int updateWorkWechatUserDepartment(Long advertiserAccountGroupId, String corpId) {
        return baseMapper.updateWorkWechatUserDepartment(advertiserAccountGroupId, corpId);
    }

    /**
     * 数据库表landing_page_wechat_customer_service 该项目项 该企业微信下的人员信息 置空
     *
     * @param advertiserAccountGroupId
     * @param corpid
     */
    public void updateByPmpIdCorpId(Long advertiserAccountGroupId, String corpid) {
        baseMapper.updateByPmpIdCorpId(advertiserAccountGroupId, corpid);
    }

    /**
     * 根据企业微信ID，查找客服列表ID
     *
     * @param corpid
     * @param advertiserAccountGroupId
     * @return
     */
    public List<Long> getIdsByCorpId(String corpid, Long advertiserAccountGroupId) {
        return baseMapper.getIdsByCorpId(corpid, advertiserAccountGroupId);
    }

    public List<LandingPageWechatCustomerService> getByCorpId(String corpid, Long advertiserAccountGroupId) {
        return baseMapper.getByCorpId(corpid, advertiserAccountGroupId);
    }


    /**
     * 编辑 / 修改
     */
    @Transactional(rollbackFor = Exception.class)
    public LandingPageWechatCustomerService removeUserId(LandingPageWechatCustomerService lpwcs) {
        lpwcs.setWechatName("");
        lpwcs.setWechatUserId("");
        lpwcs.setWechatAvatar("");
        lpwcs.setCorpId("");
        this.saveOrUpdate(lpwcs);        //修改数据
        this.createRelData(lpwcs);       //删除 & 创建关联
        this.saveAutoRule(lpwcs);        //保存自动化规则
        return lpwcs;
    }


    public LandingPageWechatCustomerServiceRedisDto getWechatCustomerServiceByWeight(Long wechatCustomerServiceGroupId,
                                                                                     List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList) {
        long qrCodeWeightSum = landingPageWechatCustomerServiceList.stream()
            .mapToLong(LandingPageWechatCustomerServiceRedisDto::getQrCodeWeight).sum();
        if (qrCodeWeightSum <= 0) {
            landingPageWechatCustomerServiceList.forEach(service -> service.setQrCodeWeight(1));
            qrCodeWeightSum = landingPageWechatCustomerServiceList.stream()
                .mapToLong(LandingPageWechatCustomerServiceRedisDto::getQrCodeWeight).sum();
        } else {
            landingPageWechatCustomerServiceList = landingPageWechatCustomerServiceList.stream()
                .filter(service -> service.getQrCodeWeight() > 0).collect(Collectors.toList());
        }
        long showGroupNum = landingPageWechatCustomerServiceRedis.getShowGroupNum(wechatCustomerServiceGroupId);
        long shouIndex = showGroupNum % qrCodeWeightSum;
        long startArea = 0, endArea;
        long qrCodeWeight;
        LandingPageWechatCustomerServiceRedisDto customerService = null;
        for (LandingPageWechatCustomerServiceRedisDto landingPageWechatCustomerService : landingPageWechatCustomerServiceList) {
            qrCodeWeight = ((Objects.isNull(landingPageWechatCustomerService.getQrCodeWeight()) ? 0 : landingPageWechatCustomerService.getQrCodeWeight()) - 1) <= 0 ? 0 : (landingPageWechatCustomerService.getQrCodeWeight() - 1);
            endArea = startArea + qrCodeWeight;
            if ((shouIndex >= startArea) && (shouIndex <= endArea)) {
                customerService = landingPageWechatCustomerService;
                break;
            }
            startArea = startArea + qrCodeWeight + 1;
        }
        landingPageWechatCustomerServiceRedis.saveShowGroupNum(wechatCustomerServiceGroupId);
        return customerService;
    }

    /**
     * 动态二维码，城市区域码 同一访客
     *
     * @param advertiserAccountGroupId
     * @param wechatCustomerServiceGroupId
     * @param wechatAppletUnionid
     * @param wechatOpenid
     * @param qiyePersonnelId
     * @param qiyePendingId
     * @param wechatExternalUserid
     * @param landingPageWechatCustomerServiceList
     * @return
     */
    public LandingPageWechatCustomerServiceRedisDto getSameCustomer(Long advertiserAccountGroupId,
                                                                    Long wechatCustomerServiceGroupId,
                                                                    String wechatAppletUnionid, String wechatOpenid,
                                                                    String qiyePersonnelId,
                                                                    String qiyePendingId, String wechatExternalUserid,
                                                                    String uid,
                                                                    List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList,
                                                                    LandingPageType landingPageType) {
        Boolean flag = identifyQrcodeCacheConfig.isIdentifyQrcodeFlag();
        Long wechatCustomerServiceId = null;
        //开关打开 为新版二维码缓存获取
        if (!ObjectUtils.isEmpty(flag) && flag) {
            wechatCustomerServiceId = this.getWechatCustomerServiceId(advertiserAccountGroupId, wechatCustomerServiceGroupId, wechatAppletUnionid, wechatOpenid, qiyePersonnelId, qiyePendingId, wechatExternalUserid, uid, landingPageType);
        } else {
            wechatCustomerServiceId = this.getWechatCustomerServiceIdOld(advertiserAccountGroupId, wechatCustomerServiceGroupId, wechatAppletUnionid, wechatOpenid, qiyePersonnelId, qiyePendingId, wechatExternalUserid, uid, landingPageType);
        }
        LandingPageWechatCustomerServiceRedisDto serviceRedisDto = null;
        if (Objects.nonNull(wechatCustomerServiceId)) {
            log.info("获取的统一客服===>>>>>{}", wechatCustomerServiceId);
            Long finalWechatCustomerServiceId = wechatCustomerServiceId;
            serviceRedisDto = landingPageWechatCustomerServiceList.stream()
                .filter(service -> finalWechatCustomerServiceId.equals(service.getId())).findFirst().orElse(null);
        }
        return serviceRedisDto;
    }

    /**
     * 企微获客助手 获客链接 同一访客 同一二维码
     *
     * @param advertiserAccountGroupId
     * @param wechatCustomerServiceGroupId
     * @param landingPageWechatCustomerServiceList
     * @return
     */
    public LandingPageWechatCustomerServiceRedisDto getCustomerAcquisitionSameCustomer(Long advertiserAccountGroupId,
                                                                                       Long wechatCustomerServiceGroupId,
                                                                                       String uid,
                                                                                       List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList) {
        if (StringUtils.isBlank(uid)) {
            return null;
        }
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(RedisConstant.CUSTOMER_ACQUISITION_URL_LINK_KEY).append(advertiserAccountGroupId)
            .append(":uid:").append(uid)
            .append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
        String key = keyBuilder.toString();
        log.info("同一客户展示同一获客链接,key={}", key);
        Object redisValue = objectRedisTemplate.opsForValue().get(key);
        if (Objects.isNull(redisValue)) {
            return null;
        }
        Long wechatCustomerServiceId = JSON.parseObject(redisValue.toString(), QrCodeShowImageDto.class)
            .getWechatCustomerServiceId();
        LandingPageWechatCustomerServiceRedisDto serviceRedisDto = null;
        if (Objects.nonNull(wechatCustomerServiceId)) {
            log.info("===>获取的统一客服获客链接===>>>>>{}", wechatCustomerServiceId);
            Long finalWechatCustomerServiceId = wechatCustomerServiceId;
            serviceRedisDto = landingPageWechatCustomerServiceList.stream()
                .filter(service -> finalWechatCustomerServiceId.equals(service.getId())).findFirst().orElse(null);
        }
        return serviceRedisDto;
    }

    /**
     * 微信客服机器人发送获客链接， 同一访客 同一二维码
     *
     * @param advertiserAccountGroupId             项目ID
     * @param wechatCustomerServiceGroupId         微信客服分组ID
     * @param externalUserId                       外部联系人userId
     * @param landingPageWechatCustomerServiceList 微信客服分组
     * @return 获客链接 同一访客 同一二维码
     */
    public LandingPageWechatCustomerServiceRedisDto getCustomerAcquisitionSameCustomerForRobot(
        Long advertiserAccountGroupId, Long wechatCustomerServiceGroupId, String externalUserId,
        List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList) {
        log.info("微信客服机器人查询缓存的获客链接，入参，advertiserAccountGroupId：{}，wechatCustomerServiceGroupId：{}，externalUserId：{}", advertiserAccountGroupId, wechatCustomerServiceGroupId, externalUserId);
        if (StringUtils.isBlank(externalUserId)) {
            return null;
        }
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(RedisConstant.ROBOT_CUSTOMER_ACQUISITION_URL_LINK_KEY)
            .append(":externalUserid:").append(externalUserId)
            .append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
        String key = keyBuilder.toString();
        Object redisValue = objectRedisTemplate.opsForValue().get(key);
        log.info("微信客服机器人查询缓存的获客链接, key = {},externalUserId ={}, wechatCustomerServiceGroupId ={}，redisValue={}", key, externalUserId, wechatCustomerServiceGroupId, redisValue);
        if (Objects.isNull(redisValue)) {
            return null;
        }
        Long wechatCustomerServiceId = JSON.parseObject(redisValue.toString(), QrCodeShowImageDto.class)
            .getWechatCustomerServiceId();
        LandingPageWechatCustomerServiceRedisDto serviceRedisDto = null;
        if (Objects.nonNull(wechatCustomerServiceId)) {
            log.info("===>获取的统一客服获客链接===>>>>>{}", wechatCustomerServiceId);
            Long finalWechatCustomerServiceId = wechatCustomerServiceId;
            serviceRedisDto = landingPageWechatCustomerServiceList.stream()
                .filter(service -> finalWechatCustomerServiceId.equals(service.getId())).findFirst().orElse(null);
        }
        log.info("微信客服机器人查询缓存的获客链接返回的结果, key = {},wechatCustomerServiceId ={}, serviceRedisDto ={}", key, wechatCustomerServiceId, JSONObject.toJSONString(serviceRedisDto));
        return serviceRedisDto;
    }


    /**
     * 缓存客户对应二维码信息
     *
     * @param
     */
    public void saveIdentifyQrCodeInfo(QrCodeShowImageDto qrCodeShowImageDto) {
        String wechatAppletUnionid = qrCodeShowImageDto.getWechatAppletUnionid();
        String wechatOpenid = qrCodeShowImageDto.getWechatOpenid();
        String qiyePendingId = qrCodeShowImageDto.getQiyePendingId();
        String wechatExternalUserid = qrCodeShowImageDto.getWechatExternalUserid();
        Long wechatCustomerServiceGroupId = qrCodeShowImageDto.getWechatCustomerServiceGroupId();
        String uid = qrCodeShowImageDto.getUid();
        Boolean flag = identifyQrcodeCacheConfig.isIdentifyQrcodeFlag();
        if (!ObjectUtils.isEmpty(flag) && flag) {
            //#32846 企业推落地页同一访客展示固定二维码逻辑优化 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PHVqcQyFwDabFgaE
            if (Objects.nonNull(qrCodeShowImageDto.getLandingPageType()) && LandingPageType.QIYETUI_APPLET.equals(qrCodeShowImageDto.getLandingPageType()) && StringUtils.isNotBlank(qiyePendingId)) {
                StringBuilder keyBuilder = new StringBuilder();
                keyBuilder.append(RedisConstant.IDENTIFY_QRCODE_DATA_NEW_KEY)
                    .append(qrCodeShowImageDto.getAdvertiserAccountGroupId());
                keyBuilder.append(":qiyePendingId:").append(qiyePendingId);
                keyBuilder.append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
                String key = keyBuilder.toString();
                log.info("缓存长按二维码信息 key:[{}] qrCodeShowImageDto:[{}]", key, JSONObject.toJSONString(qrCodeShowImageDto));
                objectRedisTemplate.opsForValue()
                    .set(key, JSONObject.toJSONString(qrCodeShowImageDto), (ONE_DAY * identifyQrcodeCacheConfig.getDay()), TimeUnit.DAYS);
            }
            if (!ObjectUtils.isEmpty(wechatAppletUnionid) || !ObjectUtils.isEmpty(wechatOpenid) || !ObjectUtils.isEmpty(qiyePendingId) || !ObjectUtils.isEmpty(wechatExternalUserid)) {
                //获取小程序
                StringBuilder keyBuilder = new StringBuilder();
                keyBuilder.append(RedisConstant.IDENTIFY_QRCODE_DATA_NEW_KEY)
                    .append(qrCodeShowImageDto.getAdvertiserAccountGroupId());
                if (StringUtils.isNotBlank(StringUtils.trim(wechatAppletUnionid))) {
                    keyBuilder.append(":wechatAppletUnionid:").append(wechatAppletUnionid);
                } else if (StringUtils.isNotBlank(StringUtils.trim(wechatOpenid))) {
                    keyBuilder.append(":wechatOpenid:").append(wechatOpenid);
                } else if (StringUtils.isNotBlank(StringUtils.trim(qiyePendingId))) {
                    //企业推小程序qiyePendingId一定存在值，且同一个用户都是相同的值
                    keyBuilder.append(":qiyePendingId:").append(qiyePendingId);
                } else if (StringUtils.isNotBlank(StringUtils.trim(wechatExternalUserid))) {
                    keyBuilder.append(":wechatExternalUserid:").append(wechatExternalUserid);
                }
                keyBuilder.append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
                String key = keyBuilder.toString();
                log.info("缓存长按二维码信息 key:[{}] qrCodeShowImageDto:[{}]", key, JSONObject.toJSONString(qrCodeShowImageDto));
                objectRedisTemplate.opsForValue()
                    .set(key, JSONObject.toJSONString(qrCodeShowImageDto), (ONE_DAY * identifyQrcodeCacheConfig.getDay()), TimeUnit.DAYS);
            }
            if (StringUtils.isNotBlank(StringUtils.trim(uid))) {
                StringBuilder keyBuilder = new StringBuilder();
                keyBuilder.append(RedisConstant.IDENTIFY_QRCODE_DATA_NEW_KEY)
                    .append(qrCodeShowImageDto.getAdvertiserAccountGroupId());
                //#32496 公众号蓝链同一访客展示固定二维码逻辑优化 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PHVqcQyFIJFFsfEx
                keyBuilder.append(":uid:").append(uid);
                keyBuilder.append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
                String key = keyBuilder.toString();
                log.info("缓存长按二维码信息 key:[{}] qrCodeShowImageDto:[{}]", key, JSONObject.toJSONString(qrCodeShowImageDto));
                objectRedisTemplate.opsForValue()
                    .set(key, JSONObject.toJSONString(qrCodeShowImageDto), (ONE_DAY * identifyQrcodeCacheConfig.getDay()), TimeUnit.DAYS);
            }
        }
    }

    public void saveStateAndPidCache(QrCodeShowImageDto qrCodeShowImageDto) {
        //存储二维码 state参数 与  pid 的关系
        if (StringUtils.isNotBlank(StringUtils.trim(qrCodeShowImageDto.getPid()))) {
            StringBuilder keyBuilder = new StringBuilder();
            keyBuilder.append(RedisConstant.IDENTIFY_QRCODE_DATA_NEW_KEY);
            keyBuilder.append(":state:").append(qrCodeShowImageDto.getLandingPageWechatCustomerContactState());
            String key = keyBuilder.toString();
            log.info("存储二维码 state参数 与  pid 的关系 key:[{}] qrCodeShowImageDto:[{}]", key, JSONObject.toJSONString(qrCodeShowImageDto));
            final Long TIMES = workWechatDevelopConf.getSelfAttributionMatchingDateTime();
            objectRedisTemplate.opsForValue()
                .set(key, JSONObject.toJSONString(qrCodeShowImageDto), TIMES, TimeUnit.MINUTES);

            PageViewInfo pageViewInfo = new PageViewInfo();
            pageViewInfo
                .setLandingPageWechatCustomerContactState(qrCodeShowImageDto.getLandingPageWechatCustomerContactState())
                .setWechatCustomerServiceGroupId(qrCodeShowImageDto.getWechatCustomerServiceGroupId())
                .setWechatCustomerServiceId(qrCodeShowImageDto.getWechatCustomerServiceId())
                .setWechatCustomerServiceUserId(qrCodeShowImageDto.getWechatCustomerServiceUserId())
                .setEnterpriseWechatCorpId(qrCodeShowImageDto.getEnterpriseWechatCorpId())
                .setPid(qrCodeShowImageDto.getPid());
            // 更新pageview-info表中的state 参数
            pageViewinfoSender.changePageViewStateByPid(pageViewInfo);

        }
    }

    /**
     * 二维码曝光，异步进行二维码展示数统计
     * 每次曝光只展示一个二维码
     *
     * @param qrCodeShowImageDtoList 入参
     */
    public void sendEnterpriseWechatCustomerEventMessage(List<QrCodeShowImageDto> qrCodeShowImageDtoList) {
        try {
            if (!CollectionUtils.isEmpty(qrCodeShowImageDtoList)) {
                QrCodeShowImageDto qrCodeShowImageDto = qrCodeShowImageDtoList.get(0);
                WechatCustomerServiceEventDto wechatCustomerServiceEventDto = new WechatCustomerServiceEventDto();
                wechatCustomerServiceEventDto.setWechatCustomerServiceId(qrCodeShowImageDto.getWechatCustomerServiceId());
                wechatCustomerServiceEventDto.setWechatCustomerServiceCorpId(qrCodeShowImageDto.getEnterpriseWechatCorpId());
                wechatCustomerServiceEventDto.setWechatCustomerServiceUserId(qrCodeShowImageDto.getWechatCustomerServiceUserId());
                wechatCustomerServiceEventDto.setIndicatorStatisticEventEnum(IndicatorStatisticEventEnum.ENTERPRISE_WECHAT_QR_CODE_SHOW);
                wechatCustomerServiceEventDto.setQrCodeShowNum(1);
                wechatCustomerServiceEventDto.setAdvertiserAccountGroupId(qrCodeShowImageDto.getAdvertiserAccountGroupId());
                wechatCustomerServiceEventDto.setPageViewTime(LocalDateTime.now());
                wechatCustomerServiceEventDto.setConvertTime(LocalDateTime.now());
                wechatCustomerServiceEventDto.setStatisticDate(LocalDateTime.now());
                if (StringUtils.isNotBlank(qrCodeShowImageDto.getPid())) {
                    log.info("动态获取企业微信客服的二维码,pid = [{}]", qrCodeShowImageDto.getPid());
                    if (StringUtils.isNotBlank(qrCodeShowImageDto.getPid())) {
                        wechatCustomerServiceEventDto.setSearchPvFlag(1);
                        wechatCustomerServiceEventDto.setPid(qrCodeShowImageDto.getPid());
                    }
                }
                enterpriseWechatCustomerEventSender.resolveEnterpriseWechatCustomerQrCodeShowEventMessage(wechatCustomerServiceEventDto);
            }
        } catch (Exception e) {
            log.error("二维码曝光，异步进行二维码展示数统计异常", e);
        }
    }


    /**
     * 缓存客户对应获客助手链接信息
     *
     * @param
     */
    public void saveCustomerAcquisitionIdentifyQrCodeInfo(QrCodeShowImageDto qrCodeShowImageDto) {
        String uid = qrCodeShowImageDto.getUid();
        Long wechatCustomerServiceGroupId = qrCodeShowImageDto.getWechatCustomerServiceGroupId();
        if (StringUtils.isBlank(uid)) {
            return;
        }
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(RedisConstant.CUSTOMER_ACQUISITION_URL_LINK_KEY).append(qrCodeShowImageDto.getAdvertiserAccountGroupId())
            .append(":uid:").append(uid)
            .append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
        String key = keyBuilder.toString();
        log.info("缓存企微获客助手获客链接信息 key:[{}] qrCodeShowImageDto:[{}]", key, JSONObject.toJSONString(qrCodeShowImageDto));
        objectRedisTemplate.opsForValue()
            .set(key, JSONObject.toJSONString(qrCodeShowImageDto), (ONE_DAY * identifyQrcodeCacheConfig.getDay()), TimeUnit.DAYS);

    }

    /**
     * 微信客服机器人发送获客助手链接， 缓存客户对应获客助手链接信息
     *
     * @param qrCodeShowImageDto 参数
     */
    public void saveRobotCustomerAcquisitionIdentifyQrCodeInfo(QrCodeShowImageDto qrCodeShowImageDto) {
        String externalUserid = qrCodeShowImageDto.getWechatExternalUserid();
        Long wechatCustomerServiceGroupId = qrCodeShowImageDto.getWechatCustomerServiceGroupId();
        if (StringUtils.isBlank(externalUserid)) {
            return;
        }
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(RedisConstant.ROBOT_CUSTOMER_ACQUISITION_URL_LINK_KEY)
            .append(":externalUserid:").append(externalUserid)
            .append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
        String key = keyBuilder.toString();
        log.info("微信客服机器人发送获客助手链接, 缓存企微获客助手获客链接信息 key:[{}] qrCodeShowImageDto:[{}]", key, JSONObject.toJSONString(qrCodeShowImageDto));
        objectRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(qrCodeShowImageDto), (ONE_DAY * identifyQrcodeCacheConfig.getDay()), TimeUnit.DAYS);

    }

    /**
     * 获取微信客服id
     *
     * @param advertiserAccountGroupId
     * @param wechatAppletUnionid
     * @return
     */
    private Long getWechatCustomerServiceId(Long advertiserAccountGroupId, Long wechatCustomerServiceGroupId, String wechatAppletUnionid, String wechatOpenid, String qiyePersonnelId, String qiyePendingId, String wechatExternalUserid, String uid, LandingPageType landingPageType) {
        if (StringUtils.isBlank(wechatAppletUnionid) && StringUtils.isBlank(wechatOpenid) && StringUtils.isBlank(qiyePendingId) && StringUtils.isBlank(wechatExternalUserid)) {
            if (StringUtils.isNotBlank(uid)) {
                StringBuilder keyBuilder = new StringBuilder();
                keyBuilder.append(RedisConstant.IDENTIFY_QRCODE_DATA_NEW_KEY).append(advertiserAccountGroupId);
                //#32496 公众号蓝链同一访客展示固定二维码逻辑优化 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PHVqcQyFIJFFsfEx
                keyBuilder.append(":uid:").append(uid);
                keyBuilder.append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
                String key = keyBuilder.toString();
                Object redisValue = objectRedisTemplate.opsForValue().get(key);
                if (!Objects.isNull(redisValue)) {
                    return JSON.parseObject(redisValue.toString(), QrCodeShowImageDto.class).getWechatCustomerServiceId();
                }
            }
            return null;
        }
        //#32846 企业推落地页同一访客展示固定二维码逻辑优化 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PHVqcQyFwDabFgaE
        if (Objects.nonNull(landingPageType) && LandingPageType.QIYETUI_APPLET.equals(landingPageType) && StringUtils.isNotBlank(qiyePendingId)) {
            StringBuilder keyBuilder = new StringBuilder();
            keyBuilder.append(RedisConstant.IDENTIFY_QRCODE_DATA_NEW_KEY).append(advertiserAccountGroupId);
            //#32496 公众号蓝链同一访客展示固定二维码逻辑优化 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PHVqcQyFIJFFsfEx
            keyBuilder.append(":qiyePendingId:").append(qiyePendingId);
            keyBuilder.append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
            String key = keyBuilder.toString();
            Object redisValue = objectRedisTemplate.opsForValue().get(key);
            if (!Objects.isNull(redisValue)) {
                return JSON.parseObject(redisValue.toString(), QrCodeShowImageDto.class).getWechatCustomerServiceId();
            }
        }
        //原始版本
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(RedisConstant.IDENTIFY_QRCODE_DATA_NEW_KEY).append(advertiserAccountGroupId);
        if (StringUtils.isNotBlank(wechatAppletUnionid)) {
            keyBuilder.append(":wechatAppletUnionid:").append(wechatAppletUnionid);
        } else if (StringUtils.isNotBlank(wechatOpenid)) {
            keyBuilder.append(":wechatOpenid:").append(wechatOpenid);
        } else if (StringUtils.isNotBlank(qiyePendingId)) {
            keyBuilder.append(":qiyePendingId:").append(qiyePendingId);
        } else if (StringUtils.isNotBlank(wechatExternalUserid)) {
            keyBuilder.append(":wechatExternalUserid:").append(wechatExternalUserid);
        }
        keyBuilder.append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
        String key = keyBuilder.toString();
        Object redisValue = objectRedisTemplate.opsForValue().get(key);
        if (Objects.isNull(redisValue)) {
            return null;
        }
        return JSON.parseObject(redisValue.toString(), QrCodeShowImageDto.class).getWechatCustomerServiceId();
    }

    /**
     * 旧版获取微信客服id
     *
     * @param advertiserAccountGroupId
     * @param wechatAppletUnionid
     * @return
     */
    private Long getWechatCustomerServiceIdOld(Long advertiserAccountGroupId, Long wechatCustomerServiceGroupId, String wechatAppletUnionid, String wechatOpenid, String qiyePersonnelId, String qiyePendingId, String wechatExternalUserid, String uid, LandingPageType landingPageType) {
        if (StringUtils.isBlank(wechatAppletUnionid) && StringUtils.isBlank(wechatOpenid) && StringUtils.isBlank(qiyePendingId) && StringUtils.isBlank(wechatExternalUserid)) {
            if (StringUtils.isNotBlank(uid)) {
                StringBuilder keyBuilder = new StringBuilder();
                keyBuilder.append(RedisConstant.IDENTIFY_QRCODE_DATA_KEY).append(advertiserAccountGroupId).append(":");
                //#32496 公众号蓝链同一访客展示固定二维码逻辑优化 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PHVqcQyFIJFFsfEx
                keyBuilder.append(":uid:").append(uid);
                String key = keyBuilder.toString();
                Object redisValue = objectRedisTemplate.opsForValue().get(key);
                if (!Objects.isNull(redisValue)) {
                    return JSON.parseObject(redisValue.toString(), PageViewInfo.class).getWechatCustomerServiceId();
                }
            }
            return null;
        }
        //#32846 企业推落地页同一访客展示固定二维码逻辑优化 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PHVqcQyFwDabFgaE
        if (Objects.nonNull(landingPageType) && LandingPageType.QIYETUI_APPLET.equals(landingPageType) && StringUtils.isNotBlank(qiyePendingId)) {
            if (StringUtils.isNotBlank(uid)) {
                StringBuilder keyBuilder = new StringBuilder();
                keyBuilder.append(RedisConstant.IDENTIFY_QRCODE_DATA_KEY).append(advertiserAccountGroupId).append(":");
                //#32496 公众号蓝链同一访客展示固定二维码逻辑优化 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PHVqcQyFIJFFsfEx
                keyBuilder.append(":qiyePendingId:").append(qiyePendingId);
                String key = keyBuilder.toString();
                Object redisValue = objectRedisTemplate.opsForValue().get(key);
                if (!Objects.isNull(redisValue)) {
                    return JSON.parseObject(redisValue.toString(), PageViewInfo.class).getWechatCustomerServiceId();
                }
            }
        }
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(RedisConstant.IDENTIFY_QRCODE_DATA_KEY).append(advertiserAccountGroupId).append(":");
        if (StringUtils.isNotBlank(wechatAppletUnionid)) {
            keyBuilder.append("wechatAppletUnionid:").append(wechatAppletUnionid);
        } else if (StringUtils.isNotBlank(wechatOpenid)) {
            keyBuilder.append("wechatOpenid:").append(wechatOpenid);
        } else if (StringUtils.isNotBlank(qiyePendingId)) {
            keyBuilder.append("qiyePendingId:").append(qiyePendingId);
        } else if (StringUtils.isNotBlank(wechatExternalUserid)) {
            keyBuilder.append("wechatExternalUserid:").append(wechatExternalUserid);
        }
        String key = keyBuilder.toString();
        Object redisValue = objectRedisTemplate.opsForValue().get(key);
        if (Objects.isNull(redisValue)) {
            return null;
        }
        return JSON.parseObject(redisValue.toString(), PageViewInfo.class).getWechatCustomerServiceId();
    }

    /**
     * 检查是否存在按时间点控制自动化规则
     *
     * @param wechatCustomerServiceId 企业微信客服Id
     * @param autoRuleStatus          自动化规则状态
     */
    public void checkExistPointOfTimeAutoRule(Long wechatCustomerServiceId, AutoRuleStatus autoRuleStatus) {
        if (Objects.isNull(autoRuleStatus) || AutoRuleStatus.UNOPENED.equals(autoRuleStatus)) {
            return;
        }
        List<LandingPageWechatCustomerServiceAutoRule> autoRules = landingPageWechatCustomerServiceAutoRuleService.list(
            new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>()
                .eq(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, wechatCustomerServiceId)
                .eq(LandingPageWechatCustomerServiceAutoRule::getType, AutoRuleType.POINT_OF_TIME)
        );
        if (CollectionUtils.isNotEmpty(autoRules)) {
            throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_AUTO_RULE_EFFECTIVE);
        }
    }

    /**
     * 检查上线是否存在按时间点控制自动化规则
     *
     * @param wechatCustomerServiceId 企业微信客服Id
     * @param autoRuleStatus          自动化规则状态
     */
    public void checkOnlinePointOfTimeAutoRule(Long wechatCustomerServiceId, AutoRuleStatus autoRuleStatus) {
        if (Objects.isNull(autoRuleStatus) || AutoRuleStatus.UNOPENED.equals(autoRuleStatus)) {
            return;
        }
        List<LandingPageWechatCustomerServiceAutoRule> autoRules = landingPageWechatCustomerServiceAutoRuleService.list(
            new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>()
                .eq(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, wechatCustomerServiceId)
                .eq(LandingPageWechatCustomerServiceAutoRule::getType, AutoRuleType.POINT_OF_TIME)
        );
        LocalDateTime now = LocalDateTime.now();
        for (LandingPageWechatCustomerServiceAutoRule autoRule : autoRules) {
            if (autoRule.getInspectionCycle() == AutoRuleInspectionCycle.EVERY_DAY) {
                if (!this.isAutomationPointOfTimeEverydayOnline(now, autoRule.getOnlineTime(), autoRule.getOfflineTime())) {
                    throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_AUTO_RULE_EFFECTIVE);
                }
            } else if (autoRule.getInspectionCycle() == AutoRuleInspectionCycle.WEEKLY_CYCLE) {
                Long[] weeklyCycle = autoRule.getWeeklyCycle();
                if (!this.isAutomationPointOfTimeWeeklyCycleOnline(now, weeklyCycle)) {
                    throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_AUTO_RULE_EFFECTIVE);
                }
            }
        }
    }

    /**
     * 检查下线是否存在按时间点控制自动化规则
     *
     * @param wechatCustomerServiceId 企业微信客服Id
     * @param autoRuleStatus          自动化规则状态
     */
    public void checkOfflinePointOfTimeAutoRule(Long wechatCustomerServiceId, AutoRuleStatus autoRuleStatus) {
        if (Objects.isNull(autoRuleStatus) || AutoRuleStatus.UNOPENED.equals(autoRuleStatus)) {
            return;
        }
        List<LandingPageWechatCustomerServiceAutoRule> autoRules = landingPageWechatCustomerServiceAutoRuleService.list(
            new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>()
                .eq(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, wechatCustomerServiceId)
                .eq(LandingPageWechatCustomerServiceAutoRule::getType, AutoRuleType.POINT_OF_TIME)
        );
        LocalDateTime now = LocalDateTime.now();
        for (LandingPageWechatCustomerServiceAutoRule autoRule : autoRules) {
            if (autoRule.getInspectionCycle() == AutoRuleInspectionCycle.EVERY_DAY) {
                if (this.isAutomationPointOfTimeEverydayOnline(now, autoRule.getOnlineTime(), autoRule.getOfflineTime())) {
                    throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_AUTO_RULE_EFFECTIVE);
                }
            } else if (autoRule.getInspectionCycle() == AutoRuleInspectionCycle.WEEKLY_CYCLE) {
                Long[] weeklyCycle = autoRule.getWeeklyCycle();
                if (this.isAutomationPointOfTimeWeeklyCycleOnline(now, weeklyCycle)) {
                    throw new RestException(ERROR_LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_AUTO_RULE_EFFECTIVE);
                }
            }
        }
    }

    /**
     * 按时间点自动化规则进行上下线
     *
     * @param landingPageWechatCustomerService 企业微信客服
     */
    public void handleWechatCustomerServiceOnOffLinePointOfTime(LandingPageWechatCustomerService landingPageWechatCustomerService) {
        if ((landingPageWechatCustomerService.getQrCodeWeight() != null && landingPageWechatCustomerService.getQrCodeWeight() == 0)
            || AutoRuleStatus.UNOPENED.equals(landingPageWechatCustomerService.getAutoRuleStatus())) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        List<LandingPageWechatCustomerServiceAutoRule> autoRules = landingPageWechatCustomerService.getAutoRule();
        if (CollectionUtils.isNotEmpty(autoRules)) {
            OnlineStatusType onlineStatus = landingPageWechatCustomerService.getOnlineStatus();
            Long serviceId = landingPageWechatCustomerService.getId();
            for (LandingPageWechatCustomerServiceAutoRule autoRule : autoRules) {
                if (!AutoRuleType.POINT_OF_TIME.equals(autoRule.getType())) {
                    continue;
                }
                if (autoRule.getInspectionCycle() == AutoRuleInspectionCycle.EVERY_DAY) {
                    if (OnlineStatusType.DISABLE.equals(onlineStatus)
                        && this.isAutomationPointOfTimeEverydayOnline(now, autoRule.getOnlineTime(), autoRule.getOfflineTime())) {
                        if (OfflineStatus.ABNORMAL_MONITOR.equals(landingPageWechatCustomerService.getOfflineStatus()) && SwitchStatus.OPEN.equals(landingPageWechatCustomerService.getNotSupportAutoOnline())
                            && !SwitchStatus.OPEN.equals(landingPageWechatCustomerService.getSupportAutoOnlineStatus())) {
                            //因触发异常监测下线并勾选不支持自动上线
                            userOperationLogDetailActionSender.sendAutoOnlineFailLog(new AutoOnlineFailLogDto().setIds(Lists.newArrayList(serviceId)));
                        } else {
                            //当前时间处于按时间点控制（每天）上线时间且客服状态为下线则置客服状态为上线
                            landingPageWechatCustomerService.setOnlineStatus(OnlineStatusType.ENABLE).setOnlineActionStatus(OnlineActionStatus.AUTO);
                            //上线修改通知-互动广告
                            Optional.ofNullable(serviceId).ifPresent(id -> {
                                CustomerServiceOperationMessage onLineMessage = new CustomerServiceOperationMessage();
                                onLineMessage.setCustomerServiceOperation(CustomerServiceOperation.ONLINE)
                                    .setLandingPageWechatCustomerService(landingPageWechatCustomerService)
                                    .setAgentId(TenantContextHolder.get());
                                trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(onLineMessage);
                                //获取分组id
                                List<Long> groupIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIds(Lists.newArrayList(id));
                                if (CollectionUtils.isNotEmpty(groupIds)) {
                                    CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
                                    dto.setAgentId(TenantContextHolder.get())
                                        .setType(CustomerServiceChangeEnum.TOP_LINE_DOWNLINE)
                                        .setLandingPageCustomerServiceGroupIds(new HashSet<>(groupIds));
                                    //发送变更项目客服分组多人活码队列
                                    multiplayerCodeSender.sendCustomerServiceChange(dto);
                                }
                            });
                            if (StringUtils.isNotBlank(landingPageWechatCustomerService.getWechatUserId())) {
                                abnormalMonitorRedis.reset(landingPageWechatCustomerService.getWechatUserId(), landingPageWechatCustomerService.getAdvertiserAccountGroupId(), LocalDateTime.now());
                            }
                        }
                    } else if (OnlineStatusType.ENABLE.equals(onlineStatus)
                        && !this.isAutomationPointOfTimeEverydayOnline(now, autoRule.getOnlineTime(), autoRule.getOfflineTime())) {
                        //当前时间处于按时间点控制（每天）下线时间且客服状态为上线则置客服状态为下线
                        landingPageWechatCustomerService.setOnlineStatus(OnlineStatusType.DISABLE);
                        landingPageWechatCustomerService.setOfflineStatus(OfflineStatus.AUTO);
                        landingPageWechatCustomerService.setOfflineTime(Instant.now());
                        //下线修改通知-互动广告
                        Optional.ofNullable(serviceId).ifPresent(id -> {
                            CustomerServiceOperationMessage offlineMessage = new CustomerServiceOperationMessage();
                            offlineMessage.setCustomerServiceOperation(CustomerServiceOperation.OFFLINE)
                                .setLandingPageWechatCustomerService(landingPageWechatCustomerService)
                                .setAgentId(TenantContextHolder.get());
                            trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(offlineMessage);
                            //获取分组id
                            List<Long> groupIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIds(Lists.newArrayList(id));
                            if (CollectionUtils.isNotEmpty(groupIds)) {
                                CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
                                dto.setAgentId(TenantContextHolder.get())
                                    .setType(CustomerServiceChangeEnum.TOP_LINE_DOWNLINE)
                                    .setLandingPageCustomerServiceGroupIds(new HashSet<>(groupIds));
                                //发送变更项目客服分组多人活码队列
                                multiplayerCodeSender.sendCustomerServiceChange(dto);
                            }
                        });
                    }
                } else if (autoRule.getInspectionCycle() == AutoRuleInspectionCycle.WEEKLY_CYCLE) {
                    Long[] weeklyCycle = autoRule.getWeeklyCycle();
                    if (OnlineStatusType.DISABLE.equals(onlineStatus) && this.isAutomationPointOfTimeWeeklyCycleOnline(now, weeklyCycle)) {
                        if (OfflineStatus.ABNORMAL_MONITOR.equals(landingPageWechatCustomerService.getOfflineStatus()) && SwitchStatus.OPEN.equals(landingPageWechatCustomerService.getNotSupportAutoOnline())
                            && !SwitchStatus.OPEN.equals(landingPageWechatCustomerService.getSupportAutoOnlineStatus())) {
                            //因触发异常监测下线并勾选不支持自动上线
                            userOperationLogDetailActionSender.sendAutoOnlineFailLog(new AutoOnlineFailLogDto().setIds(Lists.newArrayList(serviceId)));
                        } else {
                            //当前时间处于按时间点控制（周循环）上线时间且客服状态为下线则置客服状态为上线
                            landingPageWechatCustomerService.setOnlineStatus(OnlineStatusType.ENABLE).setOnlineActionStatus(OnlineActionStatus.AUTO);
                            //上线修改通知-互动广告
                            Optional.ofNullable(serviceId).ifPresent(id -> {
                                CustomerServiceOperationMessage onLineMessage = new CustomerServiceOperationMessage();
                                onLineMessage.setCustomerServiceOperation(CustomerServiceOperation.ONLINE)
                                    .setLandingPageWechatCustomerService(landingPageWechatCustomerService)
                                    .setAgentId(TenantContextHolder.get());
                                trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(onLineMessage);
                                //获取分组id
                                List<Long> groupIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIds(Lists.newArrayList(id));
                                if (CollectionUtils.isNotEmpty(groupIds)) {
                                    CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
                                    dto.setAgentId(TenantContextHolder.get())
                                        .setType(CustomerServiceChangeEnum.TOP_LINE_DOWNLINE)
                                        .setLandingPageCustomerServiceGroupIds(new HashSet<>(groupIds));
                                    //发送变更项目客服分组多人活码队列
                                    multiplayerCodeSender.sendCustomerServiceChange(dto);
                                }
                            });
                            if (StringUtils.isNotBlank(landingPageWechatCustomerService.getWechatUserId())) {
                                abnormalMonitorRedis.reset(landingPageWechatCustomerService.getWechatUserId(), landingPageWechatCustomerService.getAdvertiserAccountGroupId(), LocalDateTime.now());
                            }
                        }
                    } else if (OnlineStatusType.ENABLE.equals(onlineStatus) && !this.isAutomationPointOfTimeWeeklyCycleOnline(now, weeklyCycle)) {
                        //当前时间处于按时间点控制（周循环）下线时间且客服状态为上线则置客服状态为下线
                        landingPageWechatCustomerService.setOnlineStatus(OnlineStatusType.DISABLE);
                        landingPageWechatCustomerService.setOfflineStatus(OfflineStatus.AUTO);
                        landingPageWechatCustomerService.setOfflineTime(Instant.now());
                        //下线修改通知-互动广告
                        Optional.ofNullable(serviceId).ifPresent(id -> {
                            CustomerServiceOperationMessage offlineMessage = new CustomerServiceOperationMessage();
                            offlineMessage.setCustomerServiceOperation(CustomerServiceOperation.OFFLINE)
                                .setLandingPageWechatCustomerService(landingPageWechatCustomerService)
                                .setAgentId(TenantContextHolder.get());
                            trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(offlineMessage);
                            //获取分组id
                            List<Long> groupIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIds(Lists.newArrayList(id));
                            if (CollectionUtils.isNotEmpty(groupIds)) {
                                CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
                                dto.setAgentId(TenantContextHolder.get())
                                    .setType(CustomerServiceChangeEnum.TOP_LINE_DOWNLINE)
                                    .setLandingPageCustomerServiceGroupIds(new HashSet<>(groupIds));
                                //发送变更项目客服分组多人活码队列
                                multiplayerCodeSender.sendCustomerServiceChange(dto);
                            }
                        });
                    }
                }
                return;
            }
        }
    }

    /**
     * 判断当前时间是否为自动化规则（周循环）客服在线时间
     *
     * @param now         当前时间
     * @param weeklyCycle 周循环配置
     * @return 是否为在线时间
     */
    public boolean isAutomationPointOfTimeWeeklyCycleOnline(LocalDateTime now, Long[] weeklyCycle) {
        long todayWeeklyCycle = weeklyCycle[now.getDayOfWeek().getValue() - 1];
        int n = now.getHour() * 2 + (now.getMinute() >= 30 ? 1 : 0);
        long pow = 1L << n;
        return (todayWeeklyCycle & pow) != 0L;
    }

    /**
     * 判断当前时间是否为自动化规则（每天）客服在线时间
     *
     * @param now        当前时间
     * @param onlineTime 周循环配置
     * @return 是否为在线时间
     */
    public boolean isAutomationPointOfTimeEverydayOnline(LocalDateTime now, String onlineTime, String offlineTime) {
        onlineTime = onlineTime + ":00";
        offlineTime = offlineTime + ":00";
        boolean flag = true;
        if (onlineTime.compareTo(offlineTime) > 0) {
            String temp = onlineTime;
            onlineTime = offlineTime;
            offlineTime = temp;
            flag = false;
        }
        String time = (now.getHour() < 10 ? "0" : "") + now.getHour() + ":" + now.getMinute() + ":" + now.getSecond();
        return (time.compareTo(onlineTime) >= 0 && time.compareTo(offlineTime) < 0) == flag;
    }

//    public static void main(String[] args) {
//        System.out.println(isAutomationPointOfTimeEverydayOnline(DateTimeUtil.stringToLocalDateTime("2024-10-16 08:00:00"), "08:00", "23:00"));
//        System.out.println(isAutomationPointOfTimeEverydayOnline(DateTimeUtil.stringToLocalDateTime("2024-10-16 23:01:30"), "08:00", "23:00"));
//        System.out.println(isAutomationPointOfTimeEverydayOnline(DateTimeUtil.stringToLocalDateTime("2024-10-16 23:00:30"), "08:00", "23:00"));
//        System.out.println(isAutomationPointOfTimeEverydayOnline(DateTimeUtil.stringToLocalDateTime("2024-10-16 23:00:00"), "08:00", "23:00"));
//        System.out.println(isAutomationPointOfTimeEverydayOnline(DateTimeUtil.stringToLocalDateTime("2024-10-16 08:00:00"), "23:00", "08:00"));
//        System.out.println(isAutomationPointOfTimeEverydayOnline(DateTimeUtil.stringToLocalDateTime("2024-10-16 23:01:30"), "23:00", "08:00"));
//        System.out.println(isAutomationPointOfTimeEverydayOnline(DateTimeUtil.stringToLocalDateTime("2024-10-16 23:00:30"), "23:00", "08:00"));
//        System.out.println(isAutomationPointOfTimeEverydayOnline(DateTimeUtil.stringToLocalDateTime("2024-10-16 23:00:00"), "23:00", "08:00"));
//    }

    /**
     * 企业微信客服下线
     *
     * @param wechatCustomerServiceIds 企业微信客服Id
     */
    public void autoOfflineWechatCustomerService(List<Long> wechatCustomerServiceIds) {
        if (CollectionUtils.isNotEmpty(wechatCustomerServiceIds)) {
            //兜底客服不下线
            List<LandingPageWechatCustomerService> landingPageWechatCustomerServices = landingPageWechatCustomerServiceService.list(
                new LambdaQueryWrapper<LandingPageWechatCustomerService>()
                    .ne(LandingPageWechatCustomerService::getQrCodeWeight, 0)
                    .in(LandingPageWechatCustomerService::getId, wechatCustomerServiceIds)
            );
            wechatCustomerServiceIds = landingPageWechatCustomerServices.stream().map(LandingPageWechatCustomerService::getId).collect(Collectors.toList());
            //自动下线
            landingPageWechatCustomerServiceService.autoOffline(wechatCustomerServiceIds);
            //客服触发自动化规则或异常监测下线消息推送
            landingPageSender.customerServiceOffline(new CustomerServiceOfflineDTO().setIds(wechatCustomerServiceIds));
        }
    }

    /**
     * 企业微信客服上线
     *
     * @param wechatCustomerServiceIds 企业微信客服Id
     */
    public void autoOnlineWechatCustomerService(List<Long> wechatCustomerServiceIds) {
        if (CollectionUtils.isNotEmpty(wechatCustomerServiceIds)) {
            List<LandingPageWechatCustomerService> wechatCustomerServices = landingPageWechatCustomerServiceService.list(new LambdaQueryWrapper<LandingPageWechatCustomerService>().select(LandingPageWechatCustomerService::getId, LandingPageWechatCustomerService::getOfflineStatus
                , LandingPageWechatCustomerService::getWechatUserId, LandingPageWechatCustomerService::getNotSupportAutoOnline, LandingPageWechatCustomerService::getSupportAutoOnlineStatus, LandingPageWechatCustomerService::getAdvertiserAccountGroupId).in(LandingPageWechatCustomerService::getId, wechatCustomerServiceIds));
            if (CollectionUtils.isNotEmpty(wechatCustomerServices)) {
                List<Long> autoOnlineFailIds = Lists.newArrayList();
                wechatCustomerServiceIds = Lists.newArrayList();
                List<LandingPageWechatCustomerService> wechatCustomerServiceList = Lists.newArrayList();
                for (LandingPageWechatCustomerService wechatCustomerService : wechatCustomerServices) {
                    if (OfflineStatus.ABNORMAL_MONITOR.equals(wechatCustomerService.getOfflineStatus()) && SwitchStatus.OPEN.equals(wechatCustomerService.getNotSupportAutoOnline())
                        && !SwitchStatus.OPEN.equals(wechatCustomerService.getSupportAutoOnlineStatus())) {
                        //因触发异常监测下线并勾选不支持自动上线的客服数据
                        autoOnlineFailIds.add(wechatCustomerService.getId());
                    } else {
                        //正常自动上线的客服数据
                        wechatCustomerServiceIds.add(wechatCustomerService.getId());
                        if (StringUtils.isNotBlank(wechatCustomerService.getWechatUserId())) {
                            wechatCustomerServiceList.add(wechatCustomerService);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(wechatCustomerServiceIds)) {
                    landingPageWechatCustomerServiceService.update(new LambdaUpdateWrapper<LandingPageWechatCustomerService>()
                        .in(LandingPageWechatCustomerService::getId, wechatCustomerServiceIds)
                        .set(LandingPageWechatCustomerService::getOnlineStatus, OnlineStatusType.ENABLE)
                        .set(LandingPageWechatCustomerService::getOnlineActionStatus, OnlineActionStatus.AUTO));
                    landingPageWechatCustomerServiceService.deleteCacheByServiceIds(wechatCustomerServiceIds);
                    userOperationLogDetailActionSender.sendSystemOnlineOperationLogSave(new SystemOnlineOperationLogDto().setOnlineStatusType(OnlineStatusType.ENABLE).setIds(wechatCustomerServiceIds));
                }
                if (CollectionUtils.isNotEmpty(autoOnlineFailIds)) {
                    userOperationLogDetailActionSender.sendAutoOnlineFailLog(new AutoOnlineFailLogDto().setIds(autoOnlineFailIds));
                }
                if (CollectionUtils.isNotEmpty(wechatCustomerServiceList)) {
                    List<String> keys = Lists.newArrayList();
                    for (LandingPageWechatCustomerService wechatCustomerService : wechatCustomerServiceList) {
                        LocalDateTime nowTime = LocalDateTime.now();
                        abnormalMonitorRedis.setTime(wechatCustomerService.getWechatUserId(), wechatCustomerService.getAdvertiserAccountGroupId(), nowTime, RedisConstant.ABNORMAL_MONITOR_TAKE_EFFECT_TIME);
                        keys.add(RedisConstant.ABNORMAL_MONITOR_FIRST_IDENTIFY_TIME + wechatCustomerService.getWechatUserId() + ":" + wechatCustomerService.getAdvertiserAccountGroupId());
                        keys.add(RedisConstant.ABNORMAL_MONITOR_LAST_IDENTIFY_TIME + wechatCustomerService.getWechatUserId() + ":" + wechatCustomerService.getAdvertiserAccountGroupId());
                        keys.add(RedisConstant.ABNORMAL_MONITOR_IDENTIFY_COUNT + wechatCustomerService.getWechatUserId() + ":" + wechatCustomerService.getAdvertiserAccountGroupId());
                        keys.add(RedisConstant.ABNORMAL_MONITOR_LAST_OFFICIAL_SEND_CODE_TIME + wechatCustomerService.getWechatUserId() + ":" + wechatCustomerService.getAdvertiserAccountGroupId());
                        keys.add(RedisConstant.ABNORMAL_MONITOR_OFFICIAL_SEND_CODE_COUNT + wechatCustomerService.getWechatUserId() + ":" + wechatCustomerService.getAdvertiserAccountGroupId());
                        keys.add(RedisConstant.ABNORMAL_MONITOR_LAST_ROBOT_SEND_CODE_TIME + wechatCustomerService.getWechatUserId() + ":" + wechatCustomerService.getAdvertiserAccountGroupId());
                        keys.add(RedisConstant.ABNORMAL_MONITOR_ROBOT_SEND_CODE_COUNT + wechatCustomerService.getWechatUserId() + ":" + wechatCustomerService.getAdvertiserAccountGroupId());
                        keys.add(RedisConstant.ABNORMAL_MONITOR_CUSTOMER_ACQUISITION_FRIEND_REQUEST_COUNT + wechatCustomerService.getWechatUserId() + ":" + wechatCustomerService.getAdvertiserAccountGroupId());
                        keys.add(RedisConstant.ABNORMAL_MONITOR_LAST_CUSTOMER_ACQUISITION_FRIEND_REQUEST_TIME + wechatCustomerService.getWechatUserId() + ":" + wechatCustomerService.getAdvertiserAccountGroupId());
                        keys.add(RedisConstant.ABNORMAL_MONITOR_CUSTOMER_ACQUISITION_CLICK_COUNT + wechatCustomerService.getWechatUserId() + ":" + wechatCustomerService.getAdvertiserAccountGroupId());
                        keys.add(RedisConstant.ABNORMAL_MONITOR_LAST_CUSTOMER_ACQUISITION_CLICK_TIME + wechatCustomerService.getWechatUserId() + ":" + wechatCustomerService.getAdvertiserAccountGroupId());
                    }
                    abnormalMonitorRedis.deleteCache(keys);
                }
            }
        }
    }

    public LandingPageWechatCustomerService getLastCustomerService() {
        return baseMapper.getLastCustomerService();
    }


    /**
     * 根据客服获取联系我二维码 - 新版，兼容私库公库
     */
    public LandingPageWechatCustomerContactDto getLandingPageWechatCustomerContact(long landingPageWechatCustomerServiceId,
                                                                                   String landingPageWechatCustomerServiceWechatUserId,
                                                                                   String corpId,
                                                                                   Long advertiserAccountGroupId,
                                                                                   LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerifyStatus) {
        String agentId = TenantContextHolder.get();
        boolean h5CustomrtContactFlag = landingPageWechatCustomerContactConfig.isH5CustomerContactFlag();
        List<String> h5CustomrtContacts = landingPageWechatCustomerContactConfig.getH5CustomerContacts();
        //实时生成开关打开，配置的账户如果是空的，那么全部切为实时生成，如果配置了账户，那么只有这个账户实时生成
        if (h5CustomrtContactFlag && (CollectionUtils.isEmpty(h5CustomrtContacts) || h5CustomrtContacts.contains(agentId))) {
            //实时生成
            LandingPageWechatCustomerContact landingPageWechatCustomerContact =
                landingPageWechatCustomerContactService.realTimeGenerateQrCode(null, landingPageWechatCustomerServiceWechatUserId, corpId, landingPageWechatCustomerContactVerifyStatus, true);
            if (Objects.nonNull(landingPageWechatCustomerContact)) {
                LandingPageWechatCustomerContactDto landingPageWechatCustomerContactDto = new LandingPageWechatCustomerContactDto();
                landingPageWechatCustomerContactDto.setCorpId(landingPageWechatCustomerContact.getCorpId())
                    .setQrCode(landingPageWechatCustomerContact.getQrCode())
                    .setState(landingPageWechatCustomerContact.getState());
                return landingPageWechatCustomerContactDto;
            }
        }
        //使用预生成二维码
        LandingPageWechatCustomerContactDto landingPageWechatCustomerContactDto =
            usePregeneration(landingPageWechatCustomerServiceId, landingPageWechatCustomerServiceWechatUserId, corpId, advertiserAccountGroupId, landingPageWechatCustomerContactVerifyStatus);
        return landingPageWechatCustomerContactDto;
    }


    private LandingPageWechatCustomerContactDto usePregeneration(long landingPageWechatCustomerServiceId,
                                                                 String landingPageWechatCustomerServiceWechatUserId,
                                                                 String corpId,
                                                                 Long advertiserAccountGroupId,
                                                                 LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerifyStatus) {
        int generateNum = landingPageWechatCustomerContactConfig.getGenerateNum();
        //私库key
        String key = RedisConstant.LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_QR_CODE_KEY + landingPageWechatCustomerServiceWechatUserId;
        //公库key
        String publicKey = RedisConstant.LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_QR_CODE_KEY + corpId + ":" + landingPageWechatCustomerServiceWechatUserId;
        String generateKey = RedisConstant.LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_QR_CODE_GENERATE_KEY + corpId + ":" + landingPageWechatCustomerServiceWechatUserId;
        //是否兼容私库
        Boolean publicFlag = landingPageWechatCustomerContactConfig.getPublicFlag();
        //先取私库，取不到值则取公库
        Object entries = (Objects.nonNull(publicFlag) && publicFlag) ?
            Optional.ofNullable(objectRedisTemplate.opsForList().leftPop(key)).orElseGet(() -> defaultObjectRedisTemplate.opsForList().leftPop(publicKey)) :
            defaultObjectRedisTemplate.opsForList().leftPop(publicKey);
        LandingPageWechatCustomerContactDto landingPageWechatCustomerContactDto = null;
        //当前客服缓存二维码数量 - 公库
        Long customerContactCacheSize = landingPageWechatCustomerServiceRedis.getCustomerContactCacheSize(corpId, landingPageWechatCustomerServiceWechatUserId);
        //当前客服缓存二维码数量 - 私库
        Long customerContactCacheSizePrivate = (Objects.nonNull(publicFlag) && publicFlag) ?
            landingPageWechatCustomerServiceRedis.getCustomerContactCacheSizePrivate(landingPageWechatCustomerServiceWechatUserId) : 0L;
        Long totalNum = customerContactCacheSizePrivate + customerContactCacheSize;
        //缓存为空,或者私库没有二维码，公库二维码少于50个，就需要补偿
        if (Objects.isNull(entries) || (totalNum <= landingPageWechatCustomerContactConfig.getCustomerContactCacheSize())) {
            log.warn("====>根据客服获取联系我二维码，缓存中值已被全部消耗或者剩余不到{}个,wechatUserId:{}", landingPageWechatCustomerContactConfig.getCustomerContactCacheSize(),
                landingPageWechatCustomerServiceWechatUserId);
            //是否正在生成中
            Object o = defaultObjectRedisTemplate.opsForValue().get(generateKey);
            generateNum = Objects.isNull(entries) ? generateNum : NumberUtil.sub(Convert.toLong(generateNum), customerContactCacheSize).intValue();
            if (Objects.isNull(o)) {
                //标识此客服二维码在生成中
                defaultObjectRedisTemplate.opsForValue().set(generateKey, true, 8, TimeUnit.SECONDS);
                //没有正在生成中的队列，需要重新补偿生成generateNum个
                IntStream.range(0, generateNum).forEach(i -> {
                    //生成新的二维码
                    generateContact(landingPageWechatCustomerServiceId, landingPageWechatCustomerServiceWechatUserId, corpId, advertiserAccountGroupId, landingPageWechatCustomerContactVerifyStatus);
                });
            }
            landingPageWechatCustomerContactDto = Objects.nonNull(entries) ? (LandingPageWechatCustomerContactDto) entries : null;
//            if (landingPageWechatCustomerContactDto != null && BaseStatusEnum.ENABLE.equals(landingPageWechatCustomerContactDto.getIsUseBackground()) && StringUtils.isBlank(landingPageWechatCustomerContactDto.getQrCodeWithBackground())) {
//                String qrCodeWithBackground = landingPageWechatCustomerServiceRedis.getCustomerContactMeQrCodeBackgroundCache(landingPageWechatCustomerContactDto.getId());
//                landingPageWechatCustomerContactDto.setQrCodeWithBackground(qrCodeWithBackground);
//            }
            //修改获取到的二维码的使用状态
            Optional.ofNullable(landingPageWechatCustomerContactDto).ifPresent(m -> {
                landingPageWechatCustomerContactSender.changeQrCodeUseStatus(m);
            });
            return landingPageWechatCustomerContactDto;
        }

        landingPageWechatCustomerContactDto = (LandingPageWechatCustomerContactDto) entries;
//        if (BaseStatusEnum.ENABLE.equals(landingPageWechatCustomerContactDto.getIsUseBackground()) && StringUtils.isBlank(landingPageWechatCustomerContactDto.getQrCodeWithBackground())) {
//            String qrCodeWithBackground = landingPageWechatCustomerServiceRedis.getCustomerContactMeQrCodeBackgroundCache(landingPageWechatCustomerContactDto.getId());
//            landingPageWechatCustomerContactDto.setQrCodeWithBackground(qrCodeWithBackground);
//        }
        //生成新的二维码
        generateContact(landingPageWechatCustomerServiceId, landingPageWechatCustomerServiceWechatUserId, corpId, advertiserAccountGroupId, landingPageWechatCustomerContactVerifyStatus);
        //修改获取到的二维码的使用状态
        landingPageWechatCustomerContactSender.changeQrCodeUseStatus(landingPageWechatCustomerContactDto);
        return landingPageWechatCustomerContactDto;
    }

    //生成新的二维码
    private void generateContact(long landingPageWechatCustomerServiceId, String landingPageWechatCustomerServiceWechatUserId, String corpId, Long advertiserAccountGroupId,
                                 LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerifyStatus) {
        //当前客服缓存二维码数量
        Long customerContactCacheSize = landingPageWechatCustomerServiceRedis.getCustomerContactCacheSize(corpId, landingPageWechatCustomerServiceWechatUserId);
        //规定的单个客服缓存中生成二维码的总量
        int generateNum = landingPageWechatCustomerContactConfig.getGenerateNum();
        if (customerContactCacheSize >= generateNum) {
            log.info("实时生成二维码,缓存中已存在此客服的缓存，数量为:{} 大于等于设置的最大值:{},无需新增多个联系我二维码!wechatUserId:{}", customerContactCacheSize, generateNum, landingPageWechatCustomerServiceWechatUserId);
            return;
        }
        //发起异步生成通知
        //生成新的二维码
        LandingPageWechatCustomerContactMessage landingPageWechatCustomerContactMessage = new LandingPageWechatCustomerContactMessage()
            .setLandingPageWechatCustomerServiceWechatUserId(landingPageWechatCustomerServiceWechatUserId)
            .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerServiceId)
            .setAdvertiserAccountGroupId(advertiserAccountGroupId)
            .setDetectionCustomerContactCompleteFlag(false)
            .setFailModifyStatus(false)
            .setCorpId(corpId)
            .setLandingPageWechatCustomerContactVerify(landingPageWechatCustomerContactVerifyStatus);
        landingPageWechatCustomerContactSender.sendGenerateQrCode(landingPageWechatCustomerContactMessage);
    }


    /**
     * 根据客服获取公众号联系我二维码
     */
    public OfficialWechatCustomerContactDto getOfficialWechatCustomerContact(long landingPageWechatCustomerServiceId,
                                                                             String landingPageWechatCustomerServiceWechatUserId,
                                                                             String corpId,
                                                                             Long advertiserAccountGroupId,
                                                                             LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerifyStatus) {

        String agentId = TenantContextHolder.get();
        boolean officialCustomrtContactFlag = landingPageWechatCustomerContactConfig.isOfficialCustomerContactFlag();
        List<String> officialCustomertContacts = landingPageWechatCustomerContactConfig.getOfficialCustomerContacts();
        //实时生成开关打开，配置的账户如果是空的，那么全部切为实时生成，如果配置了账户，那么只有这个账户实时生成
        if (officialCustomrtContactFlag && (CollectionUtils.isEmpty(officialCustomertContacts) || officialCustomertContacts.contains(agentId))) {
            log.info("===>实时生成公众号单人活码,wechatUserId:{},landingPageWechatCustomerContactVerifyStatus:{}", landingPageWechatCustomerServiceWechatUserId, landingPageWechatCustomerContactVerifyStatus);
            OfficialWechatCustomerContact officialWechatCustomerContact =
                officialWechatCustomerContactService.realGenerateQrCode(null, landingPageWechatCustomerServiceWechatUserId, corpId, landingPageWechatCustomerContactVerifyStatus, true);
            if (Objects.nonNull(officialWechatCustomerContact)) {
                OfficialWechatCustomerContactDto officialWechatCustomerContactDto = new OfficialWechatCustomerContactDto();
                officialWechatCustomerContactDto.setMaterialId(officialWechatCustomerContact.getMaterialId())
                    .setAppId(officialWechatCustomerContact.getAppId())
                    .setCorpId(officialWechatCustomerContact.getCorpId())
                    .setQrCode(officialWechatCustomerContact.getQrCode())
                    .setResultUrl(officialWechatCustomerContact.getResultUrl())
                    .setQiniuPath(officialWechatCustomerContact.getQiniuPath())
                    .setState(officialWechatCustomerContact.getState())
                    .setExpireAt(officialWechatCustomerContact.getExpireAt());
                return officialWechatCustomerContactDto;
            }
        }
        //使用预生成二维码
        OfficialWechatCustomerContactDto officialWechatCustomerContactDto =
            useOfficialPregeneration(landingPageWechatCustomerServiceId, landingPageWechatCustomerServiceWechatUserId, corpId, advertiserAccountGroupId, landingPageWechatCustomerContactVerifyStatus);
        return officialWechatCustomerContactDto;
    }

    /**
     * 使用预生成的公众号单人活码
     *
     * @param landingPageWechatCustomerServiceId
     * @param landingPageWechatCustomerServiceWechatUserId
     * @param corpId
     * @param advertiserAccountGroupId
     * @param landingPageWechatCustomerContactVerifyStatus
     * @return
     */
    private OfficialWechatCustomerContactDto useOfficialPregeneration(long landingPageWechatCustomerServiceId,
                                                                      String landingPageWechatCustomerServiceWechatUserId,
                                                                      String corpId,
                                                                      Long advertiserAccountGroupId,
                                                                      LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerifyStatus) {
        int generateNum = landingPageWechatCustomerContactConfig.getGenerateNum();
        //公库key
        String publicKey = RedisConstant.OFFICIAL_WECHAT_CUSTOMER_CONTACT_QR_CODE_KEY + corpId + ":" + landingPageWechatCustomerServiceWechatUserId;
        String generateKey = RedisConstant.OFFICIAL_WECHAT_CUSTOMER_CONTACT_QR_CODE_GENERATE_KEY + corpId + ":" + landingPageWechatCustomerServiceWechatUserId;
        //取公库队列值
        Object entries = defaultObjectRedisTemplate.opsForList().leftPop(publicKey);
        OfficialWechatCustomerContactDto officialWechatCustomerContactDto = null;
        //当前客服缓存二维码数量 - 公库
        Long customerContactCacheSize = landingPageWechatCustomerServiceRedis.getOfficialCustomerContactCacheSize(corpId, landingPageWechatCustomerServiceWechatUserId);
        //缓存为空,或者公库二维码少于50个，就需要补偿
        if (Objects.isNull(entries) || (customerContactCacheSize <= landingPageWechatCustomerContactConfig.getOfficialCustomerContactCacheSize())) {
            log.warn("====>根据客服获取公众号单人联系我二维码，缓存中值已被全部消耗或者剩余不到{}个,wechatUserId:{}", landingPageWechatCustomerContactConfig.getOfficialCustomerContactCacheSize(),
                landingPageWechatCustomerServiceWechatUserId);
            //是否正在生成中
            Object o = defaultObjectRedisTemplate.opsForValue().get(generateKey);
            generateNum = Objects.isNull(entries) ? generateNum : NumberUtil.sub(Convert.toLong(generateNum), customerContactCacheSize).intValue();
            if (Objects.isNull(o)) {
                //标识此客服二维码在生成中
                defaultObjectRedisTemplate.opsForValue().set(generateKey, true, 8, TimeUnit.SECONDS);
                //没有正在生成中的队列，需要重新补偿生成generateNum个
                IntStream.range(0, generateNum).forEach(i -> {
                    //生成新的二维码
                    generateOfficialContact(landingPageWechatCustomerServiceId, landingPageWechatCustomerServiceWechatUserId, corpId, advertiserAccountGroupId, landingPageWechatCustomerContactVerifyStatus);
                });
            }
            //队列缓存为空，直接返回空
            if (Objects.isNull(entries)) {
                return null;
            }
        }

        //能走到这证明队列缓存有值
        officialWechatCustomerContactDto = (OfficialWechatCustomerContactDto) entries;
        //生成新的二维码
        generateOfficialContact(landingPageWechatCustomerServiceId, landingPageWechatCustomerServiceWechatUserId, corpId, advertiserAccountGroupId, landingPageWechatCustomerContactVerifyStatus);
        //修改获取到的二维码的使用状态
        officialCustomerContactSender.changeQrCodeUseStatus(officialWechatCustomerContactDto);
        //去获取具体的信息
        String objectKey = RedisConstant.OFFICIAL_WECHAT_CUSTOMER_CONTACT_QR_CODE_OBJECT_KEY + corpId + ":" + officialWechatCustomerContactDto.getState();
        Object one = defaultObjectRedisTemplate.opsForValue().get(objectKey);
        //未过期的，正常使用
        if (Objects.nonNull(one)) {
            defaultObjectRedisTemplate.delete(objectKey);
            OfficialWechatCustomerContactDto result = (OfficialWechatCustomerContactDto) one;
            return result;
        }
        OfficialWechatCustomerContact officialWechatCustomerContact = BeanUtil.copyProperties(officialWechatCustomerContactDto, OfficialWechatCustomerContact.class);
        //已经过期,需要重新生成,兜底
        OfficialWechatCustomerContact uploadMaterial = landingPageWechatOfficialMaterialFileService.reUploadMaterial(officialWechatCustomerContact);
        officialWechatCustomerContactDto.setMaterialId(uploadMaterial.getMaterialId()).setExpireAt(uploadMaterial.getExpireAt());
        //异步修改此state已经改变的素材id
        officialCustomerContactSender.changeMaterialIdByState(officialWechatCustomerContactDto);
        return officialWechatCustomerContactDto;
    }

    //生成新的公众号联系我二维码
    private void generateOfficialContact(long landingPageWechatCustomerServiceId, String landingPageWechatCustomerServiceWechatUserId, String corpId, Long advertiserAccountGroupId,
                                         LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerifyStatus) {
        //当前客服缓存二维码数量
        Long customerContactCacheSize = landingPageWechatCustomerServiceRedis.getOfficialCustomerContactCacheSize(corpId, landingPageWechatCustomerServiceWechatUserId);
        //规定的单个客服缓存中生成二维码的总量
        int generateNum = landingPageWechatCustomerContactConfig.getGenerateNum();
        if (customerContactCacheSize >= generateNum) {
            log.info("实时生成公众号渠道二维码,缓存中已存在此客服的缓存，数量为:{} 大于等于设置的最大值:{},无需新增多个联系我二维码!wechatUserId:{}", customerContactCacheSize, generateNum, landingPageWechatCustomerServiceWechatUserId);
            return;
        }
        //发起异步生成通知
        //生成新的二维码
        OfficialWechatCustomerContactMessage officialWechatCustomerContactMessage = new OfficialWechatCustomerContactMessage()
            .setLandingPageWechatCustomerServiceWechatUserId(landingPageWechatCustomerServiceWechatUserId)
            .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerServiceId)
            .setAdvertiserAccountGroupId(advertiserAccountGroupId)
            .setDetectionCustomerContactCompleteFlag(false)
            .setFailModifyStatus(false)
            .setCorpId(corpId)
            .setOfficialWechatCustomerContactVerify(landingPageWechatCustomerContactVerifyStatus);
        officialCustomerContactSender.sendGenerateQrCode(officialWechatCustomerContactMessage);
    }

    public List<LandingPageWechatCustomerService> saveBatch(WechatCustomerServiceSaveBatchVO wechatCustomerServiceSaveBatchVo) throws InterruptedException {
        RLock lock = redissonClient.getLock(RedisConstant.WECHAT_CUSTOMER_SERVICE_SAVE_BATCH_LOCK + TenantContextHolder.get() + ":" + wechatCustomerServiceSaveBatchVo.getAdvertiserAccountGroupId());
        try {
            if (lock.tryLock(0, 10, TimeUnit.MINUTES)) {
                List<LandingPageWechatCustomerService> wechatCustomerServices = wechatCustomerServiceSaveBatchVo.getWechatCustomerServices();
                Long advertiserAccountGroupId = wechatCustomerServiceSaveBatchVo.getAdvertiserAccountGroupId();
                List<LandingPageWechatCustomerService> repeatUserIdWechatCustomerService = new LinkedList<>();
                CollectionUtil.groupingBy(wechatCustomerServices, LandingPageWechatCustomerService::getCorpId).forEach((corpId, items) -> {
                    List<LandingPageWechatCustomerService> repeatUserIdCustomerService = this.list(
                        Wrappers.lambdaQuery(LandingPageWechatCustomerService.class)
                            .select(LandingPageWechatCustomerService::getWechatUserId)
                            .eq(LandingPageWechatCustomerService::getAdvertiserAccountGroupId, advertiserAccountGroupId)
                            .eq(LandingPageWechatCustomerService::getCorpId, corpId)
                            .in(LandingPageWechatCustomerService::getWechatUserId, CollectionUtil.mapToList(items, LandingPageWechatCustomerService::getWechatUserId))
                    );
                    if (CollectionUtils.isNotEmpty(repeatUserIdCustomerService)) {
                        repeatUserIdWechatCustomerService.addAll(repeatUserIdCustomerService);
                    }
                });
                if (CollectionUtils.isNotEmpty(repeatUserIdWechatCustomerService)) {
                    throw new RestException(String.format("企业微信客服userId(%s)重复，请重新设置", repeatUserIdWechatCustomerService.stream().map(LandingPageWechatCustomerService::getWechatUserId).collect(Collectors.joining(","))));
                }
                List<LandingPageWechatCustomerService> repeatUserNameWechatCustomerService = this.list(
                    Wrappers.lambdaQuery(LandingPageWechatCustomerService.class)
                        .select(LandingPageWechatCustomerService::getWechatUserName)
                        .eq(LandingPageWechatCustomerService::getAdvertiserAccountGroupId, advertiserAccountGroupId)
                        .in(
                            LandingPageWechatCustomerService::getWechatUserName,
                            CollectionUtil.mapToList(wechatCustomerServices, LandingPageWechatCustomerService::getWechatUserName)
                        )
                );
                if (CollectionUtils.isNotEmpty(repeatUserNameWechatCustomerService)) {
                    throw new RestException(String.format("企业微信客服名称(%s)重复，请重新设置", repeatUserNameWechatCustomerService.stream().map(LandingPageWechatCustomerService::getWechatUserName).collect(Collectors.joining(","))));
                }
                List<Long> qrCodeConfigRecordIds = wechatCustomerServices.stream().map(LandingPageWechatCustomerService::getQrCodeConfigRecordId).filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(qrCodeConfigRecordIds)) {
                    int recordCount = landingPageWechatCustomerServiceContactMeQRCodeRecordService.getBaseMapper().update(
                        null, Wrappers.lambdaUpdate(LandingPageWechatCustomerServiceContactMeQRCodeRecord.class)
                            .set(LandingPageWechatCustomerServiceContactMeQRCodeRecord::getUsageStatus, true)
                            .in(LandingPageWechatCustomerServiceContactMeQRCodeRecord::getId, qrCodeConfigRecordIds)
                    );
                    if (recordCount != qrCodeConfigRecordIds.size()) {
                        throw new RestException("企业微信客服联系我二维码已失效");
                    }
                }
                wechatCustomerServices.forEach(item -> {
                    item.setOnlineStatus(OnlineStatusType.ENABLE).setOnlineActionStatus(OnlineActionStatus.AUTO);
                    item.setAutoRuleStatus(wechatCustomerServiceSaveBatchVo.getAutoRuleStatus());
                    item.setAutoRule(wechatCustomerServiceSaveBatchVo.getAutoRules());
                    this.setAcquisitionLinkInfo(item); //企微获客链接
                    this.setSensitiveInfo(item); //设置企微客服敏感信息
                    this.setLicenseInfo(item); //设置企微客服接口许可信息
                    this.setCustomerContactInfo(item);
                    this.handleWechatCustomerServiceOnOffLinePointOfTime(item);
                    item.setAbnormalMonitorStatus(wechatCustomerServiceSaveBatchVo.getAbnormalMonitorStatus());
                    item.setNotSupportAutoOnline(wechatCustomerServiceSaveBatchVo.getNotSupportAutoOnline())
                        .setSupportAutoOnlineStatus(wechatCustomerServiceSaveBatchVo.getSupportAutoOnlineStatus())
                        .setSupportAutoOnline(wechatCustomerServiceSaveBatchVo.getSupportAutoOnline())
                        .setSpecifyOnlineTime(wechatCustomerServiceSaveBatchVo.getSpecifyOnlineTime());
                    abnormalMonitorRedis.reset(item.getWechatUserId(), item.getAdvertiserAccountGroupId(), LocalDateTime.now());
                });
                this.saveBatch(wechatCustomerServices);
                List<Long> wechatCustomerServiceGroupIds = CollectionUtils.isEmpty(wechatCustomerServiceSaveBatchVo.getWechatCustomerServiceGroupIds()) ? new ArrayList<Long>() {{
                    add(-1L);
                }} : wechatCustomerServiceSaveBatchVo.getWechatCustomerServiceGroupIds();
                List<LandingPageWechatCustomerServiceAutoRule> autoRules = wechatCustomerServiceSaveBatchVo.getAutoRules() != null ? wechatCustomerServiceSaveBatchVo.getAutoRules() : Lists.newArrayList();
                List<LandingPageWechatCustomerServiceAbnormalMonitor> abnormalMonitors = wechatCustomerServiceSaveBatchVo.getAbnormalMonitors() != null ? wechatCustomerServiceSaveBatchVo.getAbnormalMonitors() : Lists.newArrayList();
                List<LandingPageWechatCustomerServiceGroupRel> landingPageWechatCustomerServiceGroupRelList = new LinkedList<>();
                List<LandingPageWechatCustomerServiceAutoRule> landingPageWechatCustomerServiceAutoRuleList = new LinkedList<>();
                List<LandingPageWechatCustomerServiceAbnormalMonitor> landingPageWechatCustomerServiceAbnormalMonitorList = new LinkedList<>();
                for (LandingPageWechatCustomerService wechatCustomerService : wechatCustomerServices) {
                    for (Long wechatCustomerServiceGroupId : wechatCustomerServiceGroupIds) {
                        landingPageWechatCustomerServiceGroupRelList.add(
                            new LandingPageWechatCustomerServiceGroupRel()
                                .setLandingPageWechatCustomerServiceGroupId(wechatCustomerServiceGroupId)
                                .setLandingPageWechatCustomerServiceId(wechatCustomerService.getId())
                                .setAdvertiserAccountGroupId(advertiserAccountGroupId)
                        );
                    }
                    for (LandingPageWechatCustomerServiceAutoRule item : autoRules) {
                        LandingPageWechatCustomerServiceAutoRule autoRule = new LandingPageWechatCustomerServiceAutoRule();
                        BeanUtils.copyProperties(item, autoRule);
                        autoRule.setLandingPageWechatCustomerServiceId(wechatCustomerService.getId());
                        landingPageWechatCustomerServiceAutoRuleList.add(autoRule);
                    }
                    for (LandingPageWechatCustomerServiceAbnormalMonitor item : abnormalMonitors) {
                        LandingPageWechatCustomerServiceAbnormalMonitor abnormalMonitor = new LandingPageWechatCustomerServiceAbnormalMonitor();
                        BeanUtils.copyProperties(item, abnormalMonitor);
                        abnormalMonitor.setLandingPageWechatCustomerServiceId(wechatCustomerService.getId());
                        landingPageWechatCustomerServiceAbnormalMonitorList.add(abnormalMonitor);
                    }
                }
                landingPageWechatCustomerServiceGroupRelService.saveBatch(landingPageWechatCustomerServiceGroupRelList);
                landingPageWechatCustomerServiceAutoRuleService.saveBatch(landingPageWechatCustomerServiceAutoRuleList);
                landingPageWechatCustomerServiceAbnormalMonitorService.saveBatch(landingPageWechatCustomerServiceAbnormalMonitorList);
                wechatCustomerServiceGroupIds.forEach(item -> {
                    landingPageWechatCustomerServiceRedis.deleteWechatServiceDataByGroupId(item);
                });
                if (CollectionUtils.isNotEmpty(wechatCustomerServiceSaveBatchVo.getWechatCustomerServiceGroupIds())) {
                    for (LandingPageWechatCustomerService wechatCustomerService : wechatCustomerServices) {
                        CustomerServiceOperationMessage onLineMessage = new CustomerServiceOperationMessage();
                        onLineMessage.setCustomerServiceOperation(CustomerServiceOperation.ADD);
                        onLineMessage.setAgentId(TenantContextHolder.get());
                        onLineMessage.setLandingPageWechatCustomerService(wechatCustomerService);
                        onLineMessage.setLandingPageCustomerServiceGroupIds(wechatCustomerServiceSaveBatchVo.getWechatCustomerServiceGroupIds());
                        trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(onLineMessage);
                    }
                    //发送变更项目客服分组多人活码队列
                    CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
                    dto.setAgentId(TenantContextHolder.get())
                        .setType(CustomerServiceChangeEnum.EDIT_CUSTOMER_GROUP)
                        .setLandingPageCustomerServiceGroupIds(new HashSet<>(wechatCustomerServiceSaveBatchVo.getWechatCustomerServiceGroupIds()));
                    multiplayerCodeSender.sendCustomerServiceChange(dto);
                    //重新初始化该分组的客服自定义编号
                    log.info("批量导入微信客服,自定义排序编号初始化,wechatCustomerServiceGroupIds={}", wechatCustomerServiceGroupIds);
                    landingPageCustomerServiceSender.sendMessageChangeOrderNum(new LandingPageWechatCustomerServiceGroupChangeDTO().setGroupChangeEventType(GroupChangeEventType.BATCH_UPLOAD)
                        .setBatchUploadGroupIds(wechatCustomerServiceGroupIds));

                }
                for (LandingPageWechatCustomerService lpwcs : wechatCustomerServices) {
                    //用户自主上传图片的情况，进行素材上传，方便后续客服机器人发送固定客服二维码
                    if (StringUtils.isNotBlank(lpwcs.getQrCodeImgUrl()) && Objects.equals(lpwcs.getQrCodeType(), LandingPageWechatCustomerServiceQRCodeType.IMAGE_UPLOAD)) {
                        log.info("批量新增企业微信客服，保存固定二维码图片, lpwcs = {}", lpwcs);
                        List<Long> ids = wechatCustomerServiceSaveBatchVo.getWechatCustomerServiceGroupIds();
                        if (!ids.isEmpty()) {
                            String idCollect = ids.stream().map(String::valueOf).collect(Collectors.joining(","));
                            lpwcs.setWechatCustomerServiceGroupIds(idCollect);
                            landingPageWechatCustomerServiceService.cacheQrCodeImg(lpwcs);
                        }
                    }
                }
                //微信客服管理批量导入客服时支持生成获客助手链接
                List<Long> ids = wechatCustomerServices.stream().map(LandingPageWechatCustomerService::getId).collect(Collectors.toList());
                EnterpriseWechatCustomerAcquisitionBatchDto enterpriseWechatCustomerAcquisitionBatchDto = wechatCustomerServiceSaveBatchVo.getEnterpriseWechatCustomerAcquisitionBatchDto();
                if (!CollectionUtils.isEmpty(ids) && !Objects.isNull(enterpriseWechatCustomerAcquisitionBatchDto)) {
                    enterpriseWechatCustomerAcquisitionService.batch(enterpriseWechatCustomerAcquisitionBatchDto.setIds(ids));
                }
                return wechatCustomerServices;
            } else {
                throw new RestException("客服正在批量导入中，请勿频繁点击");
            }
        } finally {
            lock.unlock();
        }
    }

    /**
     * 根据客服分组id 获取分组下所有上线状态 并且设置userid的客服信息
     *
     * @return
     */
    public List<LandingPageWechatCustomerService> onlineListByGroupId(Long wechatCustomerServiceGroupId) {
        return baseMapper.onlineListByGroupId(wechatCustomerServiceGroupId);
    }

    /**
     * 微信客服列表自定义排序
     */
    public Boolean customSort(LandingPageWechatCustomerServiceSortDTO serviceSortDTO) {

        RLock fairLock = null;
        boolean result = false;
        try {
            //对同一个客服进行加锁操作
            String lockKey = serviceSortDTO.getAdvertiserAccountGroupId() + "" + serviceSortDTO.getWechatCustomerServiceGroupId() + "" + serviceSortDTO.getWechatCustomerServiceId();
            fairLock = redissonClient.getFairLock(lockKey);
            //尝试加锁，最多等待10秒
            boolean res = fairLock.tryLock(10, 30, TimeUnit.SECONDS);
            if (!res) {
                log.info("操作客服自定义排序,上移操作获取锁失败,lockKey = [{}]", lockKey);
                throw new RestException("该客服正在操作中，请勿频繁点击");
            }
            //上移
            if (Objects.equals(serviceSortDTO.getMoveDirectionType(), MoveDirectionType.UP)) {
                //校验是否是分组里面最大的，如果是最大的，不支持上移
                Integer maxNum = this.queryOrderNum(serviceSortDTO, 1);
                if (!Objects.equals(serviceSortDTO.getSortNum(), maxNum)) {
                    result = this.resolveMoveUp(serviceSortDTO);
                } else {
                    log.info("该分组最大编号 maxNum = {},sortNum = {}, 不支持上移", maxNum, serviceSortDTO.getSortNum());
                    throw new RuntimeException("第一个客服不允许上移");
                }
            } else if (Objects.equals(serviceSortDTO.getMoveDirectionType(), MoveDirectionType.DOWN)) {
                //校验是否是分组里面最小的，如果是最小的，不支持下移
                Integer minNum = this.queryOrderNum(serviceSortDTO, 0);
                //下移
                if (!Objects.equals(serviceSortDTO.getSortNum(), minNum)) {
                    result = this.resolveMoveDown(serviceSortDTO);
                } else {
                    log.info("该分组最小编号 minNum = {},sortNum, 不支持下移", minNum, serviceSortDTO.getSortNum());
                    throw new RuntimeException("最后一个客服不允许下移");
                }
            }
            //排序成功，异步删除缓存信息
            if (result) {
                landingPageWechatCustomerContactSender.sendForClearWechatCustomerServiceGroupCache(serviceSortDTO.getWechatCustomerServiceGroupId());
            }
        } catch (Exception e) {
            log.error("微信客服列表自定义排序进行{}出现异常", serviceSortDTO.getMoveDirectionType(), e);
        } finally {
            try {
                if (fairLock != null) {
                    fairLock.unlock();
                }
            } catch (Exception e) {
                log.error("操作客服自定义排序-释放redis锁异常", e);
            }
        }
        return result;
    }


    /**
     * 处理上移排序逻辑
     *
     * @param serviceSortDTO 前端入参
     */
    public Boolean resolveMoveUp(LandingPageWechatCustomerServiceSortDTO serviceSortDTO) {
        int oldOrderNum = serviceSortDTO.getSortNum();
        int newOrderNum = oldOrderNum + serviceSortDTO.getStepSize();
        return this.baseMapper.resolveMoveUp(oldOrderNum, newOrderNum, serviceSortDTO);
    }

    /**
     * 处理下移排序逻辑
     *
     * @param serviceSortDTO 前端入参
     */
    public Boolean resolveMoveDown(LandingPageWechatCustomerServiceSortDTO serviceSortDTO) {
        int oldOrderNum = serviceSortDTO.getSortNum();
        int newOrderNum = oldOrderNum - serviceSortDTO.getStepSize();
        return this.baseMapper.resolveMoveDown(oldOrderNum, newOrderNum, serviceSortDTO);
    }

    /**
     * 查询分组组自定义排序的最大值|最小值
     *
     * @param serviceSortDTO
     * @param type           1-查最大值；0-查最小值
     * @return 返回对应的排序编号
     */
    public Integer queryOrderNum(LandingPageWechatCustomerServiceSortDTO serviceSortDTO, Integer type) {
        if (type == 1 || type == 0) {
            return this.baseMapper.queryOrderNumByGroupId(serviceSortDTO, type);
        } else {
            throw new RuntimeException("查询分组组自定义排序的最大值|最小值,type值只能是0或者1");
        }
    }

    /**
     * 编辑客服信息的时候，进行自定义排序编号处理
     *
     * @param landingPageWechatCustomerServiceGroupRelList 旧的客服分组ID
     * @param landingPageWechatCustomerService             客服信息
     */
    public void resolveOrderNumWhenEditGroup(List<LandingPageWechatCustomerServiceGroupRel> landingPageWechatCustomerServiceGroupRelList, LandingPageWechatCustomerService landingPageWechatCustomerService) {
        List<Long> oldLandingPageWechatCustomerServiceGroupIds = landingPageWechatCustomerServiceGroupRelList.stream().map(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceGroupId).collect(Collectors.toList());
        List<Long> newLandingPageWechatCustomerServiceGroupIds = StringUtils.isNotBlank(landingPageWechatCustomerService.getWechatCustomerServiceGroupIds()) ?
            Arrays.stream(landingPageWechatCustomerService.getWechatCustomerServiceGroupIds().split(",")).map(Long::parseLong).collect(Collectors.toList()) : null;
        if (!oldLandingPageWechatCustomerServiceGroupIds.isEmpty() || !newLandingPageWechatCustomerServiceGroupIds.isEmpty()) {
            landingPageCustomerServiceSender.sendMessageChangeOrderNum(new LandingPageWechatCustomerServiceGroupChangeDTO().setGroupChangeEventType(GroupChangeEventType.EDIT_GROUP)
                .setAdvertiserAccountGroupId(landingPageWechatCustomerService.getAdvertiserAccountGroupId())
                .setOldLandingPageWechatCustomerServiceGroupIds(oldLandingPageWechatCustomerServiceGroupIds)
                .setNewLandingPageWechatCustomerServiceGroupIds(newLandingPageWechatCustomerServiceGroupIds)
                .setLandingPageWechatCustomerServiceId(Collections.singletonList(landingPageWechatCustomerService.getId())));
        }
    }

    /**
     * 校验是否开启了固定二维码 且 缓存中是否存在二维码 存在则取出
     *
     * @param customerServiceId  客服id 删除时需要要用到
     * @param qrCodeShowImageDto 二维码展示信息
     * @return
     */
    public LandingPageWechatCustomerContactDto getLandingPageServiceFixedQrCodeCache(QrCodeShowImageDto qrCodeShowImageDto, Long customerServiceId) {
        Long landingPageId = qrCodeShowImageDto.getLandingPageId();
        String uid = qrCodeShowImageDto.getUid();
        if (FixedContactCodeFlag.ENABLE.equals(qrCodeShowImageDto.getFixedContactCodeFlag()) && Objects.nonNull(landingPageId)
            && landingPageId > 0 && StringUtils.isNotBlank(uid) && Objects.nonNull(customerServiceId)) {
            String key = RedisConstant.LANDING_PAGE_SERVICE_CONTACT_FIXED_QR_CODE_KEY + customerServiceId + ":" + landingPageId + ":" + uid;
            Object o = objectRedisTemplate.opsForValue().get(key);
            if (Objects.nonNull(o)) {
                log.info("落地页缓存中存在固定二维码,landingPageId:{},uid:{}", landingPageId, uid);
                return (LandingPageWechatCustomerContactDto) o;
            }
        }
        return null;
    }

    /**
     * 存储固定二维码到缓存
     *
     * @param landingPageWechatCustomerContactDto 获取到的二维码缓存
     * @param customerServiceId                   客服id
     * @param qrCodeShowImageDto
     */
    public void setLandingPageServiceFixedQrCodeCache(LandingPageWechatCustomerContactDto landingPageWechatCustomerContactDto,
                                                      Long customerServiceId,
                                                      QrCodeShowImageDto qrCodeShowImageDto) {
        if (Objects.isNull(landingPageWechatCustomerContactDto)) {
            return;
        }
        Long landingPageId = qrCodeShowImageDto.getLandingPageId();
        String uid = qrCodeShowImageDto.getUid();
        if (FixedContactCodeFlag.ENABLE.equals(qrCodeShowImageDto.getFixedContactCodeFlag()) && Objects.nonNull(landingPageId)
            && landingPageId > 0 && StringUtils.isNotBlank(uid) && Objects.nonNull(customerServiceId)) {
            log.info("落地页缓存固定二维码,landingPageId:{},uid:{},qrcode:{},state:{}", landingPageId, uid,
                landingPageWechatCustomerContactDto.getQrCode(), landingPageWechatCustomerContactDto.getState());
            //落地页纬度
            String key = RedisConstant.LANDING_PAGE_SERVICE_CONTACT_FIXED_QR_CODE_KEY + customerServiceId + ":" + landingPageId + ":" + uid;
            objectRedisTemplate.opsForValue().set(key, landingPageWechatCustomerContactDto, 29, TimeUnit.MINUTES);
            //存在加粉后删除二维码的操作因此需要用state存储一份缓存保证在删除时能得到落地页纬度的缓存key 清除固定的二维码
            String stateKey = RedisConstant.LANDING_PAGE_SERVICE_CONTACT_FIXED_STATE_QR_CODE_KEY + landingPageWechatCustomerContactDto.getState();
            objectRedisTemplate.opsForValue().set(stateKey, key, 29, TimeUnit.MINUTES);
        }
    }

    /**
     * 删除固定二维码缓存
     *
     * @param customerServiceId 客服id
     */
    public void cleanLandingPageCustomerServiceFixedQrCode(String agentId, Long customerServiceId,
                                                           LandingPageWechatCustomerService landingPageWechatCustomerService,
                                                           List<LandingPageWechatCustomerContact> list) {
        //如果是取消可见范围或者删除渠道二维码，联系我二维码由调用处处传递过来
        if (CollectionUtils.isEmpty(list) && Objects.nonNull(landingPageWechatCustomerService)) {
            LambdaQueryWrapper<LandingPageWechatCustomerContact> queryWrapper = new LambdaQueryWrapper<LandingPageWechatCustomerContact>()
                .select(LandingPageWechatCustomerContact::getLandingPageId, LandingPageWechatCustomerContact::getUid)
                .eq(LandingPageWechatCustomerContact::getLandingPageWechatCustomerServiceWechatUserId, landingPageWechatCustomerService.getWechatUserId())
                .eq(LandingPageWechatCustomerContact::getCorpId, landingPageWechatCustomerService.getCorpId())
                .isNotNull(LandingPageWechatCustomerContact::getUid)
                .isNotNull(LandingPageWechatCustomerContact::getLandingPageId)
                .orderByAsc(LandingPageWechatCustomerContact::getId);
            //分页查询循环清除
            Page<LandingPageWechatCustomerContact> objectPage = new Page<>(1, 1000);
            Page<LandingPageWechatCustomerContact> contactPage = landingPageWechatCustomerContactPublicService.page(objectPage, queryWrapper);
            //第一页先执行
            List<LandingPageWechatCustomerContact> records = contactPage.getRecords();
            if (CollectionUtils.isNotEmpty(records)) {
                execClean(records, customerServiceId);
            }
            long pages = contactPage.getPages();
            //超过一页的 循环执行
            for (int i = 2; i <= pages; i++) {
                objectPage.setCurrent(i);
                contactPage = landingPageWechatCustomerContactPublicService.page(objectPage, queryWrapper);
                List<LandingPageWechatCustomerContact> pageRecords = contactPage.getRecords();
                if (CollectionUtils.isNotEmpty(pageRecords)) {
                    execClean(pageRecords, customerServiceId);
                }
            }
        } else {
            //上层调用处传递使用的联系我二维码直接执行固定二维码清除
            execClean(list, customerServiceId);
        }
        log.info("删除固定二维码缓存,清除缓存成功! agentId:{}, customerServiceId:{}", agentId, customerServiceId);
    }

    //执行删除操作
    private void execClean(List<LandingPageWechatCustomerContact> list, Long customerServiceId) {
        //过滤出落地页ID和uid不为空的
        Set<String> collect = list.stream().filter(e -> Objects.nonNull(e.getLandingPageId()) && StringUtils.isNotBlank(e.getUid())).map(e -> {
            //拼接redis key
            return RedisConstant.LANDING_PAGE_SERVICE_CONTACT_FIXED_QR_CODE_KEY + customerServiceId + ":" + e.getLandingPageId() + ":" + e.getUid();
        }).collect(Collectors.toSet());
        //清除对应客服的固定联系我二维码缓存
        objectRedisTemplate.delete(collect);
    }


    /**
     * 基于自归因参数 清除落地页固定二维码缓存
     *
     * @param state
     */
    public void cleanLandingPageFixedQrcodeByState(String state) {
        String stateKey = RedisConstant.LANDING_PAGE_SERVICE_CONTACT_FIXED_STATE_QR_CODE_KEY + state;
        try {
            Object o = objectRedisTemplate.opsForValue().get(stateKey);
            if (Objects.nonNull(o)) {
                String key = String.valueOf(o);
                objectRedisTemplate.delete(key);
            }
        } catch (Exception e) {
            log.error("基于自归因参数 清除落地页固定二维码缓存失败,stateKey={}", stateKey, e);
        }
    }


    /**
     * 存储用户访客及二维码信息
     *
     * @param landingPageWechatCustomerContactDto
     * @param qrCodeShowImageDto
     */
    public void setLandingPageServiceAddLimitOnce(LandingPageWechatCustomerContactDto landingPageWechatCustomerContactDto, QrCodeShowImageDto qrCodeShowImageDto) {
        String wechatOpenid = qrCodeShowImageDto.getWechatOpenid();
        String wechatExternalUserid = qrCodeShowImageDto.getWechatExternalUserid();
        if (Objects.isNull(landingPageWechatCustomerContactDto) || StringUtils.isAllBlank(wechatOpenid, wechatExternalUserid)) {
            return;
        }
        log.info("渠道二维码获取-》缓存存储用户访客及二维码信息,wechatOpenid:{},wechatExternalUserid:{}", wechatOpenid, wechatExternalUserid);
        LandingPageAddLimitUserInfo landingPageAddLimitUserInfo = new LandingPageAddLimitUserInfo();
        landingPageAddLimitUserInfo.setLandingPageId(qrCodeShowImageDto.getLandingPageId())
            .setUid(qrCodeShowImageDto.getUid())
            .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.NOT_ADDED)
            .setAdvertiserAccountGroupId(qrCodeShowImageDto.getAdvertiserAccountGroupId())
            .setAgentId(TenantContextHolder.get())
            .setWechatOpenid(wechatOpenid)
            .setWechatExternalUserid(wechatExternalUserid)
            .setRobotCorpId(qrCodeShowImageDto.getRobotCorpId());
        //openId和外部联系人userid分开缓存 应对机器人蓝链和公众号蓝链
        Optional.of(wechatOpenid).filter(StringUtils::isNotBlank).ifPresent(e -> {
            String key = RedisConstant.LANDING_PAGE_SERVICE_ADD_LIMIT_ONCE_WECHAT_OPENID_KEY + e;
            landingPageWechatCustomerServiceRedis.saveAddLimitUserInfoCache(landingPageWechatCustomerContactDto.getState(), key, landingPageAddLimitUserInfo);
        });
        Optional.of(wechatExternalUserid).filter(StringUtils::isNotBlank).ifPresent(e -> {
            String key = RedisConstant.LANDING_PAGE_SERVICE_ADD_LIMIT_ONCE_WECHAT_EXTERNAL_USERID_KEY + e;
            landingPageWechatCustomerServiceRedis.saveAddLimitUserInfoCache(landingPageWechatCustomerContactDto.getState(), key, landingPageAddLimitUserInfo);
        });
    }

    /**
     * 代开发加粉回调 修改重复访客缓存中客资加粉状态
     *
     * @param adSourceEnterpriseWechatTagDTO
     */
    public void fixLandingPageServiceAddLimitOnce(AdSourceEnterpriseWechatTagDTO adSourceEnterpriseWechatTagDTO) {
        if (Objects.isNull(adSourceEnterpriseWechatTagDTO) || StringUtils.isBlank(adSourceEnterpriseWechatTagDTO.getState())) {
            return;
        }
        String state = adSourceEnterpriseWechatTagDTO.getState();
        String uid = adSourceEnterpriseWechatTagDTO.getUid();
        //用户获取该自归因参数二维码时存储的访客信息并修改为已加粉
        landingPageWechatCustomerServiceRedis.fixAddLimitUserInfoCacheByState(state, uid);
        //修改获客助手访客信息缓存 uid
        landingPageWechatCustomerServiceRedis.fixAcquisitionAddLimitUserCacheByState(state, uid, "uid");
        //修改获客助手访客信息缓存 device
        String url = adSourceEnterpriseWechatTagDTO.getUrl();
        if (StringUtils.isNotBlank(url)) {
            String device = UrlUtils.getDevice(url);
            if (StringUtils.isNotBlank(device)) {
                landingPageWechatCustomerServiceRedis.fixAcquisitionAddLimitUserCacheByState(state, device, "device");
            }
        }
    }

    /**
     * 成功加粉后，进行缓存标记，用于后续作为重复访客禁止添加的查询依据
     */
    public void recordLandingPageServiceAddLimitOnce(AdSourceEnterpriseWechatTagDTO adSourceEnterpriseWechatTagDTO) {
        try {
            if (Objects.isNull(adSourceEnterpriseWechatTagDTO)) {
                return;
            }
            //判断是否是非获客链接加粉
            MakeNonAdSourceCustomerTagEventType eventType = adSourceEnterpriseWechatTagDTO.getEventType();
            if (ACQUISITION_LINK_ADD_LIST.contains(eventType)) {
                log.info("通过获客链接成功加粉，不重复进行缓存标记");
                return;
            }
            String wechatOpenid = adSourceEnterpriseWechatTagDTO.getWechatOpenid();
            Long advertiserAccountGroupId = adSourceEnterpriseWechatTagDTO.getAdvertiserAccountGroupId();
            Long landingPageId = adSourceEnterpriseWechatTagDTO.getLandingPageId();
            String externalUserUserid = adSourceEnterpriseWechatTagDTO.getExternalUserUserid();
            LinkType linkType = adSourceEnterpriseWechatTagDTO.getLinkType();
            String userId = adSourceEnterpriseWechatTagDTO.getUserId();
            String pid = adSourceEnterpriseWechatTagDTO.getPid();
            String uid = adSourceEnterpriseWechatTagDTO.getUid();
            String wechatAppletUnionid = adSourceEnterpriseWechatTagDTO.getWechatAppletUnionid();
            String url = adSourceEnterpriseWechatTagDTO.getUrl();
            String referrer = adSourceEnterpriseWechatTagDTO.getReferrer();
            log.info("linkType = {}, wechatAppletUnionid = {}, pid = {}, uid = {}", linkType, wechatAppletUnionid, pid, uid);
            if (Objects.equals(eventType, MakeNonAdSourceCustomerTagEventType.LANDING_PAGE_ADD)) {
                //判断是否是公众号蓝链
                if (Objects.equals(linkType, LinkType.WECHAT_OFFICIAL_ACCOUNT_BLUE_URL)) {
                    landingPageWechatCustomerServiceRedis.recordOfficialBlueChainAddLimitOnce(advertiserAccountGroupId, landingPageId, wechatOpenid, externalUserUserid, userId, pid, uid);
                } else if (Objects.equals(linkType, LinkType.WECHAT_CUSTOMER_ROBOT_SERVICE)) {
                    //微信客服机器人蓝链
                    landingPageWechatCustomerServiceRedis.recordRobotCustomerServiceAddLimitOnce(advertiserAccountGroupId, landingPageId, externalUserUserid, userId, pid, uid);
                } else {
                    //判断是否是小程序链路
                    if (StringUtils.isNotBlank(wechatAppletUnionid)) {
                        landingPageWechatCustomerServiceRedis.recordWechatAppletAddLimitOnce(advertiserAccountGroupId, landingPageId, externalUserUserid, uid, userId, pid, wechatAppletUnionid);
                    }
                    //根据uid进行缓存
                    if (StringUtils.isNotBlank(uid)) {
                        landingPageWechatCustomerServiceRedis.recordDifferentUidCustomerServiceAddLimitOnce(advertiserAccountGroupId, landingPageId, externalUserUserid, uid, userId, pid);
                    }
                }
            } else {
                //判断是否是小程序链路
                if (StringUtils.isNotBlank(wechatAppletUnionid)) {
                    landingPageWechatCustomerServiceRedis.recordWechatAppletAddLimitOnce(advertiserAccountGroupId, landingPageId, externalUserUserid, uid, userId, pid, wechatAppletUnionid);
                }
            }
            //根据宏参数进行缓存
            landingPageWechatCustomerServiceRedis.recordDifferentMacroparameterCustomerServiceAddLimitOnce(advertiserAccountGroupId, landingPageId, externalUserUserid, userId, pid, url, referrer);
        } catch (Exception e) {
            log.error("成功加粉后，进行缓存标记，出现异常", e);
        }
    }


    /**
     * 缓存用户上传的二维码图片，用户机器人自动回复使用
     *
     * @param landingPageWechatCustomerService
     */
    public void cacheQrCodeImg(LandingPageWechatCustomerService landingPageWechatCustomerService) {
        LandingPageWechatCustomerServiceQRCodeType qrCodeType = landingPageWechatCustomerService.getQrCodeType();
        log.info("编辑落地页, 缓存用户上传的二维码图片，用户机器人自动回复使用, qrCodeType = {}", qrCodeType);
        if (StringUtils.isNotBlank(landingPageWechatCustomerService.getQrCodeImgUrl()) && Objects.nonNull(qrCodeType) && Objects.equals(qrCodeType, LandingPageWechatCustomerServiceQRCodeType.IMAGE_UPLOAD)) {
            landingPageSender.sendQrCodeImageForAutoAnswer(landingPageWechatCustomerService);
        }
    }

    /**
     * 对发送用户上传的固定二维码图片信息进行素材上传和缓存 ,便于消息自动回复
     *
     * @param landingPageWechatCustomerService 落地页信息
     */
    public void resolveQrCodeImageForAutoAnswer(LandingPageWechatCustomerService landingPageWechatCustomerService) throws IOException {

        if (Objects.isNull(landingPageWechatCustomerService)) {
            return;
        }
        String corpId = landingPageWechatCustomerService.getCorpId();

        //EnterpriseWechatTempMaterial material = robotCustomerContactService.drawImage(new RobotCustomerContactDto().setBackgroundUrl(qrCode), qrCode, path, enterpriseWechat);
        Instant instant = Instant.now();
        EnterpriseWechatTempMaterial material = new EnterpriseWechatTempMaterial();
        material.setCorpId(corpId)
            .setMaterialPath(landingPageWechatCustomerService.getQrCodeImgUrl())
            .setMaterialId(null)
            .setCreatedAt(instant)
            .setExpireAt(instant.plus(7, ChronoUnit.DAYS))
            .setMaterialType(EnterpriseTempMaterialType.IMAGE);
        enterpriseTempMaterialService.saveOrUpdate(material);

        log.info("对发送用户上传的固定二维码图片信息进行素材上传,返回的信息material = {}", material);
        Long mediaId = material.getId();
        //保存素材ID
        landingPageWechatCustomerServiceService.lambdaUpdate().set(LandingPageWechatCustomerService::getQrCodeImgMaterialId, mediaId).eq(LandingPageWechatCustomerService::getId, landingPageWechatCustomerService.getId()).update();
        //保存缓存信息
        landingPageWechatCustomerService.setQrCodeImgMaterialId(mediaId);
        String wechatCustomerServiceGroupIds = landingPageWechatCustomerService.getWechatCustomerServiceGroupIds();
        if (StringUtils.isNotBlank(wechatCustomerServiceGroupIds)) {
            String[] groupIds = wechatCustomerServiceGroupIds.split(",");
            if (groupIds.length > 0) {
                for (String groupId : groupIds) {
                    landingPageWechatCustomerServiceRedis.setWechatCustomerServiceFixQrCode(landingPageWechatCustomerService, Long.valueOf(groupId));
                }
            }
        }
    }

    /**
     * 获取缓存中客服的固定二维码图片的素材ID
     */
    public WechatCustomerServiceFixQrCodeInfo getWechatCustomerServiceFixQrCode(String corpId, Long wechatCustomerServiceGroupId, String externalUserId, String openKfid,
                                                                                List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList, Long wechatCustomerServiceRobotId) {
        LandingPageWechatCustomerServiceRedisDto serviceRedisDto = getSameCustomer(wechatCustomerServiceGroupId, externalUserId, openKfid, landingPageWechatCustomerServiceList, wechatCustomerServiceRobotId);
        if (serviceRedisDto != null) {
            //取分组缓存中的企业微信ID
            corpId = serviceRedisDto.getCorpId();
            log.info("获取缓存中客服的固定二维码图片的素材ID,同一访客，corpId = {}", corpId);
            WechatCustomerServiceFixQrCodeInfo info = landingPageWechatCustomerServiceRedis.getWechatCustomerServiceFixQrCodeInfo(corpId, wechatCustomerServiceGroupId, serviceRedisDto.getId());
            if (Objects.isNull(info)) {
                //如果没有结果，有可能是没有保存设置，客户只是操作了分组变动，需要重新初始化
                log.info("获取缓存中客服的固定二维码图片的素材ID,同一访客，corpId = {},没有结果，有可能是没有保存设置，客户只是操作了分组变动，需要重新初始化", corpId);
                LandingPageWechatCustomerService landingPageWechatCustomerService = this.lambdaQuery().eq(LandingPageWechatCustomerService::getId, serviceRedisDto.getId()).last(" limit 1").one();
                if (Objects.nonNull(landingPageWechatCustomerService) && StringUtils.isNotBlank(landingPageWechatCustomerService.getQrCodeImgUrl())) {
                    WechatCustomerServiceFixQrCodeInfo finalInfo = new WechatCustomerServiceFixQrCodeInfo();
                    finalInfo.setQrCodeImgUrl(landingPageWechatCustomerService.getQrCodeImgUrl());
                    log.info("获取微信客服机器人动态渠道二维码,不是同一访客，没有结果,查询数据库, finalInfo  = {}", corpId, JSONObject.toJSONString(finalInfo));
                    //异步投递消息进行初始化
                    landingPageWechatCustomerService.setWechatCustomerServiceGroupIds(String.valueOf(wechatCustomerServiceGroupId));
                    landingPageSender.sendQrCodeImageForAutoAnswer(landingPageWechatCustomerService);
                    return finalInfo;
                }
            }
            return info;
        } else {
            log.info("获取缓存中客服的固定二维码图片的素材ID,不是同一访客");
            //根据权重算法获得客服数据
            LandingPageWechatCustomerServiceRedisDto customerServiceRedisDto = landingPageWechatCustomerServiceService.getWechatCustomerServiceByWeight(wechatCustomerServiceGroupId, landingPageWechatCustomerServiceList);
            if (customerServiceRedisDto != null) {
                //存入缓存
                saveSameCustomer(customerServiceRedisDto, wechatCustomerServiceGroupId, externalUserId, openKfid, wechatCustomerServiceRobotId);

                corpId = customerServiceRedisDto.getCorpId();
                WechatCustomerServiceFixQrCodeInfo info = new WechatCustomerServiceFixQrCodeInfo();
                info.setQrCodeImgMaterialId(customerServiceRedisDto.getQrCodeImgMaterialId());
                info.setQrCodeImgUrl(customerServiceRedisDto.getQrCodeImgUrl());
                log.info("获取微信客服机器人动态渠道二维码,不是同一访客，corpId = {}, info  = {}", corpId, JSONObject.toJSONString(info));
                return info;
            }
        }
        return null;
    }

    /**
     * 保存同一访客缓存信息
     *
     * @param customerServiceRedisDto
     * @param wechatCustomerServiceGroupId
     * @param externalUserId
     * @param openKfid
     */
    private void saveSameCustomer(LandingPageWechatCustomerServiceRedisDto customerServiceRedisDto, Long wechatCustomerServiceGroupId, String externalUserId, String openKfid, Long wechatCustomerServiceRobotId) {
        log.info("获取微信客服机器人固定二维码,设置同一访客缓存信息 , wechatCustomerServiceGroupId = {}, externalUserId = {}, wechatCustomerServiceRobotId = {}", wechatCustomerServiceGroupId, externalUserId, wechatCustomerServiceRobotId);
        if (wechatCustomerServiceGroupId == null || StringUtils.isBlank(externalUserId) || StringUtils.isBlank(openKfid)) {
            return;
        }
        String robotId = Objects.nonNull(wechatCustomerServiceRobotId) ? String.valueOf(wechatCustomerServiceRobotId) : openKfid;
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(RedisConstant.ROBOT_FIX_QR_CODE_DATA_KEY).append(robotId)
            .append(":externalUserId:").append(externalUserId)
            .append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
        String key = keyBuilder.toString();
        objectRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(customerServiceRedisDto), (ONE_DAY * identifyQrcodeCacheConfig.getDay()), TimeUnit.DAYS);
    }


    /**
     * 查询同一访客缓存信息
     */
    private LandingPageWechatCustomerServiceRedisDto getSameCustomer(Long wechatCustomerServiceGroupId, String externalUserId, String openKfid, List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList
        , Long wechatCustomerServiceRobotId) {
        log.info("获取微信客服机器人固定二维码,查询同一访客缓存信息 , wechatCustomerServiceGroupId = {}, externalUserId = {}, wechatCustomerServiceRobotId = {}", wechatCustomerServiceGroupId, externalUserId, wechatCustomerServiceRobotId);
        if (wechatCustomerServiceGroupId == null || StringUtils.isBlank(externalUserId) || StringUtils.isBlank(openKfid)) {
            return null;
        }
        String robotId = Objects.nonNull(wechatCustomerServiceRobotId) ? String.valueOf(wechatCustomerServiceRobotId) : openKfid;
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(RedisConstant.ROBOT_FIX_QR_CODE_DATA_KEY).append(robotId)
            .append(":externalUserId:").append(externalUserId)
            .append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
        String key = keyBuilder.toString();
        log.info("获取微信客服机器人动态渠道二维码,查询同一访客缓存信息 , key = {}", key);
        Object redisValue = objectRedisTemplate.opsForValue().get(key);
        if (Objects.isNull(redisValue)) {
            return null;
        }
        Long wechatCustomerServiceId = JSON.parseObject(redisValue.toString(), LandingPageWechatCustomerServiceRedisDto.class).getId();
        LandingPageWechatCustomerServiceRedisDto serviceRedisDto = null;
        if (Objects.nonNull(wechatCustomerServiceId)) {
            serviceRedisDto = landingPageWechatCustomerServiceList.stream().filter(service -> wechatCustomerServiceId.equals(service.getId())).findFirst().orElse(null);
        }
        return serviceRedisDto;
    }

    /**
     * 查询客服对应的分组信息
     *
     * @return
     */
    public List<LandingPageWechatCustomerService> queryWechatCustomerServiceForFixQrCode() {
        return this.baseMapper.queryWechatCustomerServiceForFixQrCode();
    }


    /**
     * 查询配置了微信客服机器人活码的微信客服列表
     *
     * @return
     */
    public List<LandingPageWechatCustomerService> queryRobotDynamicWechatCustomerServiceQrCode() {
        return this.lambdaQuery().eq(LandingPageWechatCustomerService::getRobotCustomerDynamicContactStatus, LandingPageWechatCustomerContactStatus.GENERATED).list();
    }

    /**
     * 根据分组查询设置了微信活码的客服列表
     *
     * @param wechatCustomerServiceGroupId 客服分组ID
     */
    public List<LandingPageWechatCustomerService> getRobotLiveCodeCustomerService(Long wechatCustomerServiceGroupId, Long advertiserAccountGroupId) {
        return this.baseMapper.getRobotLiveCodeCustomerService(wechatCustomerServiceGroupId, advertiserAccountGroupId);
    }

    /**
     * 根据名称和企业微信ID查询微信客服的信息
     *
     * @param wechatName 企业微信可见范围的名称
     * @param corpid     企业微信id
     * @return
     */
    public LandingPageWechatCustomerService queryByWechatNameAndCorpId(String wechatName, String corpid) {
        log.info("根据微信名称和corpid查询客服的详情, wechatName = {}, corpid = {}", wechatName, corpid);
        if (StringUtils.isNotBlank(wechatName) && StringUtils.isNotBlank(corpid)) {
            return this.lambdaQuery().eq(LandingPageWechatCustomerService::getWechatName, wechatName).eq(LandingPageWechatCustomerService::getCorpId, corpid).last(" limit 1").one();
        }
        return null;
    }

    public void clearRobotCustomerDynamicContactQrCode(String corpId, String agentId) {
        try {
            log.info("取消授权-清除客服表微信客服机器人动态活码相关的配置, agentId = {}, corpId = {}", agentId, corpId);
            if (StringUtils.isNotBlank(corpId)) {
                this.lambdaUpdate().eq(LandingPageWechatCustomerService::getCorpId, corpId)
                    .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactBackgroundUrl, null)
                    .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactQiniuPath, null)
                    .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactVerify, null)
                    .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactMaterialId, null)
                    .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactState, null)
                    .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactStatus, LandingPageWechatCustomerContactStatus.UNGENERATED)
                    .update();
            }
        } catch (Exception e) {
            log.error("取消授权-清除客服表微信客服机器人动态活码相关的配置失败, agentId = {}, corpId = {}", agentId, corpId);
        }
    }

    public LandingPageWechatCustomerService updateName(LandingPageWechatCustomerService lpwcs) {
        this.lambdaUpdate()
            .set(LandingPageWechatCustomerService::getWechatUserName, lpwcs.getWechatUserName())
            .set(LandingPageWechatCustomerService::getWechatAvatar, lpwcs.getWechatAvatar())
            .eq(LandingPageWechatCustomerService::getId, lpwcs.getId())
            .update();
        return this.detail(lpwcs.getId());
    }

    /**
     * 查询需要删除的微信客服的userId
     *
     * @return
     */
    public List<ContactRemoteClearVO> queryRecordForDeleteRemoteQrCode(String agentId, boolean checkTableRecord, Set<String> exceptWechatUserId) {
        TenantContextHolder.set(agentId);
        log.info("查询需要删除的微信客服的userId, agentId = {}, checkTableRecord = {}, exceptWechatUserId = {}", TenantContextHolder.get(), checkTableRecord, exceptWechatUserId);
        if (Objects.isNull(checkTableRecord) || Boolean.TRUE.equals(checkTableRecord)) {
            if (Objects.isNull(exceptWechatUserId) || CollectionUtils.isEmpty(exceptWechatUserId)) {
                List<LandingPageQrCodeClearMark> list = landingPageQrCodeClearMarkService.list();
                List<String> exceptUserIdList = list.stream().map(LandingPageQrCodeClearMark::getWechatUserId).collect(Collectors.toList());
                if (!exceptUserIdList.isEmpty()) {
                    exceptWechatUserId = new HashSet<>(exceptUserIdList);
                    log.info("清除远端的微信客服的动态渠道二维码， 查询库里标记的不处理的客服, exceptWechatUserId = {}", exceptWechatUserId);
                }
            }
        }
        List<ContactRemoteClearVO> list = this.baseMapper.queryRecordForDeleteRemoteQrCode(exceptWechatUserId);
        return list;
    }

    /**
     * 根据userId和corpid查询客服记录
     */
    public List<LandingPageWechatCustomerService> queryCustomerServiceList(String userId, String corpId, String agentId) {
        TenantContextHolder.set(agentId);
        List<LandingPageWechatCustomerService> list = landingPageWechatCustomerServiceService.lambdaQuery().eq(LandingPageWechatCustomerService::getWechatUserId, userId)
            .eq(LandingPageWechatCustomerService::getCorpId, corpId).list();
        return list;
    }

    /**
     * 根据分组ID查询，这个分组下面是否有在线的客服
     *
     * @param landingPageWechatCustomerServiceGroupId 客服分组ID
     * @return
     */
    public int checkOnLineCustomerList(Long landingPageWechatCustomerServiceGroupId) {
        log.info("根据分组ID查询，这个分组下面是否有在线的客服, landingPageWechatCustomerServiceGroupId = {}", landingPageWechatCustomerServiceGroupId);
        int num = this.baseMapper.checkOnLineCustomerList(landingPageWechatCustomerServiceGroupId);
        log.info("根据分组ID查询，这个分组下面是否有在线的客服, landingPageWechatCustomerServiceGroupId = {}, num = {}", landingPageWechatCustomerServiceGroupId, num);
        return num;
    }

    @Deprecated
    public boolean updateRobotCustomerDynamicContactStatusById(Long id) {
        return this.lambdaUpdate().eq(LandingPageWechatCustomerService::getId, id)
            .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactStatus, LandingPageWechatCustomerContactStatus.IN_FORMATION)
            .update();
    }

    /**
     * 根据客服分组查询设置了微信客服机器人动态渠道二维码的客服集合
     *
     * @param wechatCustomerServiceGroupId 客服分组
     * @return
     */
    public List<LandingPageWechatCustomerService> queryUserIdForInitRobotDynamicQrCode(Long wechatCustomerServiceGroupId) {
        String agentId = TenantContextHolder.get();
        log.info("根据客服分组查询设置了微信客服机器人动态渠道二维码的客服集合, 分组ID：{}, agentId : {}", wechatCustomerServiceGroupId, agentId);
        if (Objects.nonNull(wechatCustomerServiceGroupId)) {
            return this.baseMapper.queryUserIdForInitRobotDynamicQrCode(wechatCustomerServiceGroupId);
        }
        return new ArrayList<>();
    }


    public void batchDeleteWechatCustomerService(WechatCustomerServiceDeleteDTO dto) {
        List<Long> ids = dto.getIds();
        for (Long id : ids) {
            try {
                //查出旧分组
                List<LandingPageWechatCustomerServiceGroupRel> landingPageWechatCustomerServiceGroupRelList = landingPageWechatCustomerServiceGroupRelService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceGroupRel>().eq(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceId, id));
                LandingPageWechatCustomerService landingPageWechatCustomerService = landingPageWechatCustomerServiceService.getById(id);
                landingPageWechatCustomerServiceService.handleContactMeQrCode(landingPageWechatCustomerService.getQrCodeConfigRecordId(), null);
                landingPageWechatCustomerServiceService.deleteById(id);
                log.info("根据id删除企业微信客服信息：agentId={}；id={}", TenantContextHolder.get(), id);
                //清理旧分组缓存
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(landingPageWechatCustomerServiceGroupRelList)) {
                    landingPageWechatCustomerServiceGroupRelList.forEach(landingPageWechatCustomerServiceGroupRel ->
                        landingPageWechatCustomerServiceRedis.deleteWechatServiceDataByGroupId(landingPageWechatCustomerServiceGroupRel.getLandingPageWechatCustomerServiceGroupId())
                    );
                    //发送客服分组客服变更通知
                    Set<Long> groupIds = landingPageWechatCustomerServiceGroupRelList.stream().map(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceGroupId).collect(Collectors.toSet());
                    landingPageCustomerServiceSender.sendCallBackMessage(new WechatCustomerServiceGroupDto().setGroupIds(groupIds));
                }
                String corpId = landingPageWechatCustomerService.getCorpId();
                if (StringUtils.isBlank(corpId)) {
                    continue;
                }
                //通知互动广告
                CustomerServiceOperationMessage offlineMessage = new CustomerServiceOperationMessage();
                offlineMessage.setCustomerServiceOperation(CustomerServiceOperation.DELETE)
                    .setLandingPageWechatCustomerService(landingPageWechatCustomerService)
                    .setAgentId(TenantContextHolder.get());
                trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(offlineMessage);
                //清除机器人活码
                RobotCustomerContactDeleteDto deleteParam = new RobotCustomerContactDeleteDto();
                deleteParam.setCorpId(landingPageWechatCustomerService.getCorpId())
                    .setRobotCustomerContactConfigId(landingPageWechatCustomerService.getRobotCustomerContactConfigId())
                    .setRobotCustomerContactQiniuPath(landingPageWechatCustomerService.getRobotCustomerContactQiniuPath());
                robotCustomerContactSender.sendDeleteQrCode(deleteParam);
                //公众号渠道二维码信息检测是否清除
                officialCustomerContactSender.sendCheckOfficialCustomerContactClean(landingPageWechatCustomerService);
                //清除机器人动态渠道二维码相关配置与记录
                robotDynamicCustomerContactGenerateSender.deleteRobotDynamicConfigAndRecord(landingPageWechatCustomerService);
            } catch (Exception e) {
                log.error("批量删除微信客服过程中，删除客服{}出现异常", id, e);
            }
        }
    }
}
