package ai.yiye.agent.landingpage.service;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.web.exception.ErrorConstants;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.landingpage.*;
import ai.yiye.agent.domain.result.ResultCodeEnum;
import ai.yiye.agent.domain.util.EnumUtil;
import ai.yiye.agent.douyin.client.OpenDouyinApiClient;
import ai.yiye.agent.douyin.client.OpenDouyinServiceApiClient;
import ai.yiye.agent.douyin.client.OpenDouyinUpgradeApiClient;
import ai.yiye.agent.douyin.model.request.ModifyServerDomainRequestBody;
import ai.yiye.agent.douyin.model.request.PackageAuditRequestBody;
import ai.yiye.agent.douyin.model.request.PackageUploadRequestBody;
import ai.yiye.agent.douyin.model.request.TpTokenRequestBody;
import ai.yiye.agent.douyin.model.response.*;
import ai.yiye.agent.douyin.model.response.version.AuditPackageVersionResponseBody;
import ai.yiye.agent.douyin.model.response.version.HostSideResponse;
import ai.yiye.agent.douyin.model.response.version.OnlinePackageVersionResponseBody;
import ai.yiye.agent.douyin.model.response.version.PackageVersionResponseBody;
import ai.yiye.agent.landingpage.config.AgentConf;
import ai.yiye.agent.landingpage.controller.vo.MiniDouyinResponseBody;
import ai.yiye.agent.landingpage.dto.DouyinHostNameDto;
import ai.yiye.agent.landingpage.dto.LandingPageDouyinAppletConfigPageDto;
import ai.yiye.agent.landingpage.dto.TpAuthDto;
import ai.yiye.agent.landingpage.dto.WechatOpenBindStatusDto;
import ai.yiye.agent.landingpage.mapper.LandingPageDouyinAppletConfigMapper;
import ai.yiye.agent.landingpage.redis.LandingPageDouyinAppletConfigRedis;
import ai.yiye.agent.landingpage.sender.DouyinMessageSender;
import ai.yiye.agent.landingpage.utils.JSONUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 抖音小程序参数 - 配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-05
 */
@Slf4j
@Service
@DS(DbConstants.POSTGRESQL_DEFAULT)  //设置成默认数据库
public class LandingPageDouyinAppletConfigService extends ServiceImpl<LandingPageDouyinAppletConfigMapper, LandingPageDouyinAppletConfig> {

    @Autowired
    private OpenDouyinApiClient openDouyinApiClient;

    @Autowired
    private OpenDouyinServiceApiClient openDouyinServiceApiClient;

    @Autowired
    private StringRedisTemplate defaultStringRedisTemplate;

    @Autowired
    private RedisTemplate<String, Object> defaultObjectRedisTemplate;

    @Autowired
    private LandingPageDouyinThirdPartyServicesService landingPageDouyinThirdPartyServicesService;

    @Autowired
    private LandingPageDouyinAppletTemplateService landingPageDouyinAppletTemplateService;

    @Autowired
    private LandingPageDouyinAppletPmpRelService landingPageDouyinAppletPmpRelService;

    @Autowired
    private LandingPageService landingPageService;

    @Autowired
    private AgentConf agentConf;

    @Autowired
    private DouyinMessageSender douyinMessageSender;

    @Resource
    private LandingPageDouyinAppletConfigRedis landingPageDouyinAppletConfigRedis;

    @Resource
    private OpenDouyinUpgradeApiClient openDouyinUpgradeApiClient;

    //一叶标识
    public final String YIYE = "-yiye";

    public final String DOUYIN_AUTH = "douyin:auth:";

    /**
     * 查询小程序基本信息并入库
     */
    @Transactional
    public void getAndSaveInfo(String statusCode, TpAuthDto tpAuthDto, AuthTokenResponseBody authTokenResponseBody, AuthTokenUpgradeesponseBody authTokenUpgradeesponseBody) {
        String authorizerAccessToken = authTokenResponseBody.getAuthorizerAccessToken();
        Integer expiresIn = authTokenResponseBody.getExpiresIn();
        Integer expiresInNew = authTokenUpgradeesponseBody.getExpiresIn();
        String componentAppid = tpAuthDto.getComponentAppid();
        DouyinCommonResponseBody<DouyinAppletResponseBody> douyinCommonResponseBody = openDouyinServiceApiClient.appletInfo(componentAppid, authorizerAccessToken);
        String key = DOUYIN_AUTH + statusCode;
        if (ResultCodeEnum.SUCCESS.getCode() != douyinCommonResponseBody.getErrno()) {
            log.error("===>授权小程序获取基本信息失败,{}", douyinCommonResponseBody.getMessage());
            defaultObjectRedisTemplate.opsForValue().set(key, "授权小程序获取基本信息失败", RedisConstant.LANDING_PAGE_DOU_YIN_TP_TOKEN_EXPIRES, TimeUnit.SECONDS);
            return;
        }
        Long advertiserAccountGroupId = tpAuthDto.getAdvertiserAccountGroupId();
        //小程序accessToken过期时间
        DateTime offset = DateUtil.offset(DateUtil.date(), DateField.SECOND, expiresIn);
        Instant expires = offset.toInstant();
        DateTime offsetNew = DateUtil.offset(DateUtil.date(), DateField.SECOND, expiresInNew);
        Instant expiresNew = offsetNew.toInstant();
        DouyinAppletResponseBody douyinAppletResponseBody = douyinCommonResponseBody.getData();
        log.info("===>授权小程序基本信息:{}", JSONUtil.bean2json(douyinAppletResponseBody));
        LandingPageDouyinAppletConfig landingPageDouyinAppletConfig = responseInfoToData(douyinAppletResponseBody, authTokenResponseBody, authTokenUpgradeesponseBody);
        List<LandingPageDouyinAppletConfig> list = this.list(new LambdaQueryWrapper<LandingPageDouyinAppletConfig>()
            .eq(LandingPageDouyinAppletConfig::getAgentId, TenantContextHolder.get())
            .eq(LandingPageDouyinAppletConfig::getDouyinAppletAppid, landingPageDouyinAppletConfig.getDouyinAppletAppid()));
        if (!CollectionUtils.isEmpty(list)) {
            this.update(new LambdaUpdateWrapper<LandingPageDouyinAppletConfig>()
                .set(LandingPageDouyinAppletConfig::getAuthTime, Instant.now())
                .set(LandingPageDouyinAppletConfig::getAccessToken, landingPageDouyinAppletConfig.getAccessToken())
                .set(LandingPageDouyinAppletConfig::getRefreshToken, landingPageDouyinAppletConfig.getRefreshToken())
                .set(LandingPageDouyinAppletConfig::getAccessTokenNew, landingPageDouyinAppletConfig.getAccessTokenNew())
                .set(LandingPageDouyinAppletConfig::getRefreshTokenNew, landingPageDouyinAppletConfig.getRefreshTokenNew())
                .set(LandingPageDouyinAppletConfig::getDouyinAppletName, landingPageDouyinAppletConfig.getDouyinAppletName())
                .set(LandingPageDouyinAppletConfig::getAppType, landingPageDouyinAppletConfig.getAppType())
                .set(LandingPageDouyinAppletConfig::getRemarks, landingPageDouyinAppletConfig.getRemarks())
                .set(LandingPageDouyinAppletConfig::getImgUrl, landingPageDouyinAppletConfig.getImgUrl())
                .set(LandingPageDouyinAppletConfig::getPrincipalName, landingPageDouyinAppletConfig.getPrincipalName())
                .set(LandingPageDouyinAppletConfig::getPermissions, landingPageDouyinAppletConfig.getPermissions())
                .set(LandingPageDouyinAppletConfig::getExpireTime, expires)
                .set(LandingPageDouyinAppletConfig::getExpireTimeNew, expiresNew)
                .set(LandingPageDouyinAppletConfig::getLandingPageId, tpAuthDto.getLandingPageId())
                .set(LandingPageDouyinAppletConfig::getLandingPageChannelId, tpAuthDto.getLandingPageChannelId())
                .set(LandingPageDouyinAppletConfig::getTemplateTypeId, tpAuthDto.getTemplateTypeId())
                .eq(LandingPageDouyinAppletConfig::getDouyinAppletAppid, landingPageDouyinAppletConfig.getDouyinAppletAppid())
                .eq(LandingPageDouyinAppletConfig::getAgentId, TenantContextHolder.get()));

            log.info("appId:{},当前授权抖音小程序在数据库中存在,不可重复授权!", landingPageDouyinAppletConfig.getDouyinAppletAppid());
            landingPageDouyinAppletConfigRedis.deleteCache(landingPageDouyinAppletConfig.getDouyinAppletAppid());
            defaultObjectRedisTemplate.opsForValue().set(key, "抖音小程序已完成授权，请勿重复授权 ", RedisConstant.LANDING_PAGE_DOU_YIN_TP_TOKEN_EXPIRES, TimeUnit.SECONDS);
            return;
        }
        landingPageDouyinAppletConfig.setAgentId(tpAuthDto.getAgentId())
            .setExpireTime(expires)
            .setExpireTimeNew(expiresNew)
            .setLandingPageId(tpAuthDto.getLandingPageId())
            .setLandingPageChannelId(tpAuthDto.getLandingPageChannelId())
            .setTemplateTypeId(tpAuthDto.getTemplateTypeId())
            .setAdvertiserAccountGroupId(advertiserAccountGroupId)
            .setComponentAppId(componentAppid)
            .setNavigateMiniPrograms(tpAuthDto.getNavigateMiniPrograms());
        //查询质量评级信息接口
        DouyinCommonResponseBody<QualityRatingResponseBody> qualityRatingResponseBodyDouyinCommonResponseBody = openDouyinServiceApiClient.qualityRating(componentAppid, authorizerAccessToken);
        if (ResultCodeEnum.SUCCESS.getCode() == qualityRatingResponseBodyDouyinCommonResponseBody.getErrno()) {
            QualityRatingResponseBody qualityRatingResponseBody = qualityRatingResponseBodyDouyinCommonResponseBody.getData();
            if (!ObjectUtils.isEmpty(qualityRatingResponseBody)) {
                Integer status = qualityRatingResponseBody.getStatus();
                QualityStatus qualityStatus = QualityStatus.getValueById(status);
                landingPageDouyinAppletConfig.setQualityStatus(qualityStatus);
            }
            landingPageDouyinAppletConfig.setQualityRating(ObjectUtils.isEmpty(qualityRatingResponseBody) ? null : qualityRatingResponseBody.getQualityRating());
        }

        //查询信用分分值接口
        DouyinCommonResponseBody<CreditScoreResponseBody> creditScoreResponseBodyDouyinCommonResponseBody = openDouyinServiceApiClient.creditScore(componentAppid, authorizerAccessToken);
        if (ResultCodeEnum.SUCCESS.getCode() == creditScoreResponseBodyDouyinCommonResponseBody.getErrno()) {
            CreditScoreResponseBody creditScoreResponseBody = creditScoreResponseBodyDouyinCommonResponseBody.getData();
            landingPageDouyinAppletConfig.setCreditScore(ObjectUtils.isEmpty(creditScoreResponseBody) ? null : creditScoreResponseBody.getCreditScore());
        }
        boolean save = save(landingPageDouyinAppletConfig);
        if (save) {
            log.info("===>小程序信息保存成功:{}", JSONUtil.bean2json(landingPageDouyinAppletConfig));
        }
        //创建小程序项目关联
        LandingPageDouyinAppletPmpRel landingPageDouyinAppletPmpRel = new LandingPageDouyinAppletPmpRel();
        landingPageDouyinAppletPmpRel.setLandingPageDouyinAppletId(landingPageDouyinAppletConfig.getId());
        landingPageDouyinAppletPmpRel.setAdvertiserAccountGroupId(advertiserAccountGroupId);
        landingPageDouyinAppletPmpRel.setAgentId(landingPageDouyinAppletConfig.getAgentId());
        landingPageDouyinAppletPmpRelService.save(landingPageDouyinAppletPmpRel);
        tpAuthDto.setDouyinAppletAppid(landingPageDouyinAppletConfig.getDouyinAppletAppid());
        //修改小程序服务器域名、提交/审核代码
        douyinMessageSender.sendSubmitCode(tpAuthDto);
        //抖音-启动webhook
        douyinMessageSender.douyinWebhook(tpAuthDto);
        landingPageDouyinAppletConfigRedis.deleteCache(landingPageDouyinAppletConfig.getDouyinAppletAppid());
    }

    public WechatOpenBindStatusDto authInfo(String statusCode) {
        WechatOpenBindStatusDto wechatOpenBindStatusDto = new WechatOpenBindStatusDto();
        String key = DOUYIN_AUTH + statusCode;
        Object o = defaultObjectRedisTemplate.opsForValue().get(key);
        Boolean delete = defaultObjectRedisTemplate.delete(key);
        if (Objects.isNull(o)) {
            log.info("抖音小程序授权无异常，逻辑已经处理，redis缓存的小程序信息已经清除");
            return null;
        }
        if (o instanceof String) {
            String s = String.valueOf(o);
            return wechatOpenBindStatusDto.setBindStatus(500).setMessage(s);
        }
        return null;
    }

    /**
     * 获取第三方接口调取凭证
     *
     * @param componentAppid
     * @return
     */
    public String getComponentAccessToken(String componentAppid) {
        String key = RedisConstant.LANDING_PAGE_DOU_YIN_TP_TOKEN + componentAppid;
        String s = defaultStringRedisTemplate.opsForValue().get(key);
        TpTokenRequestBody tpTokenRequestBody = new TpTokenRequestBody();
        tpTokenRequestBody.setComponentAppid(componentAppid);
        LandingPageDouyinThirdPartyServices thirdInfo = getThirdInfo();
        tpTokenRequestBody.setComponentAppsecret(thirdInfo.getComponentAppSecret());
        String componentAccessToken = Optional.ofNullable(s).filter(StringUtils::isNotBlank).orElseGet(() -> tpToken(tpTokenRequestBody));
        return componentAccessToken;
    }

    /**
     * 获取第三方应用接口调用凭据 --- 第三方应用
     *
     * @param tpTokenRequestBody
     * @return
     */
    public String tpToken(TpTokenRequestBody tpTokenRequestBody) {
        String componentTicket = defaultStringRedisTemplate.opsForValue().get(RedisConstant.LANDING_PAGE_DOU_YIN_COMPONENT_TICKET);
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(componentTicket)) {
            log.error("component_ticket不存在!");
            throw new RestException("component_ticket不存在");
        }
        tpTokenRequestBody.setComponentTicket(componentTicket);
        TpTokenResponseBody tpTokenResponseBody = openDouyinServiceApiClient.tpToken(tpTokenRequestBody.getComponentAppid(), tpTokenRequestBody.getComponentAppsecret(), tpTokenRequestBody.getComponentTicket());
        log.info("====获取第三方应用接口调用凭据:{}====", JSON.toJSONString(tpTokenResponseBody));
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(tpTokenResponseBody) || StringUtils.isBlank(tpTokenResponseBody.getComponentAccessToken())) {
            log.info("====获取第三方应用接口调用凭据失败,tpTokenRequestBody:{}====", JSONObject.toJSONString(tpTokenRequestBody));
            throw new RestException("获取第三方应用接口调用凭据失败!");
        }
        String key = RedisConstant.LANDING_PAGE_DOU_YIN_TP_TOKEN + tpTokenRequestBody.getComponentAppid();
        defaultStringRedisTemplate.opsForValue().set(key, tpTokenResponseBody.getComponentAccessToken(), RedisConstant.LANDING_PAGE_DOU_YIN_TP_TOKEN_EXPIRES, TimeUnit.SECONDS);
        return tpTokenResponseBody.getComponentAccessToken();
    }

    /**
     * 获取第三方接口调取凭证2.0
     *
     * @param componentAppid
     * @return
     */
    public String getComponentAccessTokenUpgrade(String componentAppid) {
        String key = RedisConstant.LANDING_PAGE_DOUYIN_TP_TOKEN_UPGRADE + componentAppid;
        String s = defaultStringRedisTemplate.opsForValue().get(key);
        TpTokenRequestBody tpTokenRequestBody = new TpTokenRequestBody();
        tpTokenRequestBody.setComponentAppid(componentAppid);
        LandingPageDouyinThirdPartyServices thirdInfo = getThirdInfo();
        tpTokenRequestBody.setComponentAppsecret(thirdInfo.getComponentAppSecret());
        String componentAccessToken = Optional.ofNullable(s).orElseGet(() -> tpTokenUpgrade(tpTokenRequestBody));
        return componentAccessToken;
    }

    /**
     * 获取第三方应用接口调用凭据2.0 --- 第三方应用
     *
     * @param tpTokenRequestBody
     * @return
     */
    public String tpTokenUpgrade(TpTokenRequestBody tpTokenRequestBody) {
        String componentTicket = defaultStringRedisTemplate.opsForValue().get(RedisConstant.LANDING_PAGE_DOU_YIN_COMPONENT_TICKET);
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(componentTicket)) {
            log.error("component_ticket不存在!");
            throw new RestException("component_ticket不存在");
        }
        tpTokenRequestBody.setComponentTicket(componentTicket);
        TpTokenResponseBody tpTokenResponseBody = openDouyinUpgradeApiClient.tpToken(tpTokenRequestBody.getComponentAppid(), tpTokenRequestBody.getComponentAppsecret(), tpTokenRequestBody.getComponentTicket());
        log.info("====获取第三方应用接口调用凭据2.0:{}====", JSON.toJSONString(tpTokenResponseBody));
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(tpTokenResponseBody) || StringUtils.isBlank(tpTokenResponseBody.getComponentAccessToken())) {
            log.info("====获取第三方应用接口调用凭据2.0失败,tpTokenRequestBody:{}====", JSONObject.toJSONString(tpTokenRequestBody));
            throw new RestException("获取第三方应用接口调用凭据2.0失败!");
        }
        String key = RedisConstant.LANDING_PAGE_DOUYIN_TP_TOKEN_UPGRADE + tpTokenRequestBody.getComponentAppid();
        defaultStringRedisTemplate.opsForValue().set(key, tpTokenResponseBody.getComponentAccessToken(), RedisConstant.LANDING_PAGE_DOU_YIN_TP_TOKEN_EXPIRES, TimeUnit.SECONDS);
        return tpTokenResponseBody.getComponentAccessToken();
    }


    /**
     * 获取ComponentAppid
     *
     * @return
     */
    public String getComponentAppid() {
        LandingPageDouyinThirdPartyServices thirdInfo = getThirdInfo();
        return ObjectUtils.isEmpty(thirdInfo) ? null : thirdInfo.getComponentAppid();
    }

    /**
     * 获取ComponentAppid
     *
     * @return
     */
    public LandingPageDouyinThirdPartyServices getThirdInfo() {
        String key = RedisConstant.LANDING_PAGE_DOU_YIN_COMPONENT_APPID;
        Long expires = RedisConstant.LANDING_PAGE_DOU_YIN_COMPONENT_APPID_EXPIRES;
        Object o = Optional.ofNullable(defaultObjectRedisTemplate.opsForValue().get(key)).orElseGet(() -> {
            LandingPageDouyinThirdPartyServices one = landingPageDouyinThirdPartyServicesService.getOne(new LambdaQueryWrapper<LandingPageDouyinThirdPartyServices>().last("limit 1").orderByDesc(LandingPageDouyinThirdPartyServices::getCreatedAt));
            defaultObjectRedisTemplate.opsForValue().set(key, one, expires, TimeUnit.SECONDS);
            return one;
        });
        if (ObjectUtils.isEmpty(o)) {
            return null;
        }
        LandingPageDouyinThirdPartyServices landingPageDouyinThirdPartyServices = (LandingPageDouyinThirdPartyServices) o;
        return landingPageDouyinThirdPartyServices;
    }


    /**
     * 提交审核代码
     *
     * @param tpAuthDto
     */
    public void submitAndAuditCode(TpAuthDto tpAuthDto) {
        String componentAppid = tpAuthDto.getComponentAppid();
        LandingPageDouyinAppletConfig douyinAppletConfig = getOne(new LambdaQueryWrapper<LandingPageDouyinAppletConfig>().eq(LandingPageDouyinAppletConfig::getDouyinAppletAppid, tpAuthDto.getDouyinAppletAppid()).orderByDesc(LandingPageDouyinAppletConfig::getId).last("limit 1"));
        //修改服务器域名
        modifyServer(tpAuthDto, douyinAppletConfig);
        //修改webview 域名
        modifyWebViewDomain(tpAuthDto, douyinAppletConfig);

 /*       //提交代码
        submitCode(tpAuthDto, douyinAppletConfig, landingPageDouyinAppletTemplate);
        //代码提审
        realAuditCode(tpAuthDto, douyinAppletConfig, landingPageDouyinAppletTemplate);*/
        //校验检查版本并发布
        checkVersion(tpAuthDto, douyinAppletConfig, componentAppid);

    }

    /**
     * 修改webview域名
     */
    public void modifyWebViewDomain(TpAuthDto tpAuthDto, LandingPageDouyinAppletConfig douyinAppletConfig){
        String douyinAppletAppid = douyinAppletConfig.getDouyinAppletAppid();
        try {
            String accessToken = douyinAppletConfig.getAccessToken();
            String componentAppid = tpAuthDto.getComponentAppid();
            ModifyServerDomainRequestBody modifyServerDomainRequestBody = new ModifyServerDomainRequestBody();
            String webviewHost = landingPageService.getWebviewDomain();
            log.info("修改webview域名， webviewHost = {}", webviewHost);
            if (StringUtils.isBlank(webviewHost)){
                log.info("修改webview域名, webviewHost为空，不进行webview域名修改");
                return;
            }
            modifyServerDomainRequestBody.setAction("set").setWebview(Arrays.asList(webviewHost));
            DouyinCommonResponseBody douyinCommonResponseBody = openDouyinServiceApiClient.modifyWebviewDomain(componentAppid, accessToken, modifyServerDomainRequestBody);
            log.info("==>修改webview域名:{}", JSONObject.toJSONString(douyinCommonResponseBody));
            Integer errno = douyinCommonResponseBody.getErrno();
            if (errno != 0) {
                log.error("==>抖音小程序:{}修改webview域名,{}", douyinAppletAppid, douyinCommonResponseBody.getMessage());
                throw new RestException("==>抖音小程序:" + douyinAppletAppid + "修改webview域名失败," + douyinCommonResponseBody.getMessage());
            }
        }catch (Exception e){
            log.error("抖音小程序:{}修改webview域名异常", douyinAppletAppid, e);
        }
    }

    //修改服务器域名
    public void modifyServer(TpAuthDto tpAuthDto, LandingPageDouyinAppletConfig douyinAppletConfig) {
        String accessToken = douyinAppletConfig.getAccessToken();
        String componentAppid = tpAuthDto.getComponentAppid();
        String douyinAppletAppid = douyinAppletConfig.getDouyinAppletAppid();
        ModifyServerDomainRequestBody modifyServerDomainRequestBody = new ModifyServerDomainRequestBody();
        modifyServerDomainRequestBody.setAction("set").setRequest(Arrays.asList(agentConf.getDomain(), agentConf.getOceanengineUrl(), agentConf.getTraceDomain()));
//            .setRequest(Arrays.asList("agent-sz-dev7.yiye.ai"));
        DouyinCommonResponseBody douyinCommonResponseBody = openDouyinServiceApiClient.modifyServerDomain(componentAppid, accessToken, modifyServerDomainRequestBody);
        log.info("==>代小程序修改服务器域名:{}", JSONObject.toJSONString(douyinCommonResponseBody));
        Integer errno = douyinCommonResponseBody.getErrno();
        if (errno != 0) {
            log.error("==>抖音小程序:{}修改服务器域名,{}", douyinAppletAppid, douyinCommonResponseBody.getMessage());
            throw new RestException("==>抖音小程序:" + douyinAppletAppid + "修改服务器域名失败," + douyinCommonResponseBody.getMessage());
        }
    }

    /**
     * 代码提交
     */
    public void submitCode(String auditVersion, TpAuthDto tpAuthDto, LandingPageDouyinAppletConfig douyinAppletConfig, LandingPageDouyinAppletTemplate landingPageDouyinAppletTemplate) {
        String accessToken = douyinAppletConfig.getAccessToken();
        String componentAppid = tpAuthDto.getComponentAppid();
        String douyinAppletAppid = douyinAppletConfig.getDouyinAppletAppid();
        JSONObject extJson = landingPageDouyinAppletTemplate.getExtJson();
        MinitDouyintExt.AppConfig appConfig = new MinitDouyintExt.AppConfig();
        MiniDouyinResponseBody miniDouyinResponseBody = landingPageService.homeDouyinPage(douyinAppletConfig);
        String homePage = miniDouyinResponseBody.getHomePage();
        String webviewHost = landingPageService.getDomainNotHavePath();
        appConfig.setAccount(douyinAppletConfig.getDouyinAppletName()).setApiHost(agentConf.getHost()).setTraceDomain(agentConf.getTraceHost())
//            .setApiHost("https://agent-sz-dev7.yiye.ai")
            .setDefaultPage(homePage).setWebviewHost(webviewHost).setAppName(douyinAppletConfig.getDouyinAppletName()).setAppId(douyinAppletAppid);
        log.info("抖音小程序主页地址为{}", homePage);
        log.info("抖音小程序homePage地址为{}", webviewHost);
        MinitDouyintExt minitDouyintExt = new MinitDouyintExt();
        minitDouyintExt.setAppConfig(appConfig);
        extJson.put("ext", minitDouyintExt);
        extJson.put("extAppid", tpAuthDto.getDouyinAppletAppid());
        extJson.put("navigateToMiniProgramAppIdList", douyinAppletConfig.getNavigateMiniPrograms());
        String templateUserVersion = landingPageDouyinAppletTemplate.getUserVersion();
        String userVersion = StringUtils.isNotBlank(auditVersion) ? increasing(auditVersion) : templateUserVersion.substring(0, templateUserVersion.lastIndexOf("-"));
        PackageUploadRequestBody packageUploadRequestBody = new PackageUploadRequestBody();
        packageUploadRequestBody.setExtJson(extJson.toJSONString());
        packageUploadRequestBody.setTemplateId(Integer.parseInt(landingPageDouyinAppletTemplate.getTemplateId()));
        //真实版本号+环境+yiye标识
        packageUploadRequestBody.setUserDesc(landingPageDouyinAppletTemplate.getUserVersion() + YIYE);
        //递增版本号
        packageUploadRequestBody.setUserVersion(userVersion);
        DouyinCommonResponseBody douyinCommonResponseBody = openDouyinServiceApiClient.packageUpload(componentAppid, accessToken, packageUploadRequestBody);
        log.info("==>代抖音小程序代码提交:{}", JSONObject.toJSONString(douyinCommonResponseBody));
        Integer errno = douyinCommonResponseBody.getErrno();
        if (errno != 0) {
            log.error("==>抖音小程序:{}代码提交失败,{}", douyinAppletAppid, douyinCommonResponseBody.getMessage());
            throw new RestException("==>抖音小程序:" + douyinAppletAppid + "代码提交失败," + douyinCommonResponseBody.getMessage());
        }
    }

    /**
     * 代码审核
     */
    public List<DouyinHostNameDto> auditCode(TpAuthDto tpAuthDto, LandingPageDouyinAppletConfig landingPageDouyinAppletConfig) {
        String componentAppid = tpAuthDto.getComponentAppid();
        String douyinAppletAppid = landingPageDouyinAppletConfig.getDouyinAppletAppid();
        //查询本次可审核的宿主端
        HostSideResponse hostSideResponse = packageAuditHosts(landingPageDouyinAppletConfig.getDouyinAppletAppid());
        List<String> hostNames = hostSideResponse.getHostNames();
        if (!CollectionUtils.isEmpty(hostNames)) {
            hostNames = hostNames.stream().filter(e -> !ObjectUtils.isEmpty(EnumUtil.getByStringCode(e, DouyinHostsType.class))).collect(Collectors.toList());
        }
        PackageAuditRequestBody packageAuditRequestBody = new PackageAuditRequestBody();
        packageAuditRequestBody.setHostNames(hostNames);
        DouyinCommonResponseBody douyinCommonResponseBody = openDouyinServiceApiClient.packageAudit(componentAppid, landingPageDouyinAppletConfig.getAccessToken(), packageAuditRequestBody);
        log.info("==>代授权小程序代码审核:{}", JSONObject.toJSONString(douyinCommonResponseBody));
        DouyinCodeAuditStatus status;
        String reason;
        Integer errno = douyinCommonResponseBody.getErrno();
        if (errno != 0) {
            log.error("==>抖音小程序:{}代码提审失败,{}", douyinAppletAppid, douyinCommonResponseBody.getMessage());
            status = DouyinCodeAuditStatus.FAILURE;
            if (errno == 11312) {
                reason = "小程序未进行隐私协议配置或更新，请前往平台进行更新";
            } else {
                reason = douyinCommonResponseBody.getMessage();
            }
        } else {
            status = DouyinCodeAuditStatus.IN_THE_REVIEW;
            reason = "";
        }
        return hostNames.stream().map(hostName -> {
            DouyinHostNameDto douyinHostNameDto = new DouyinHostNameDto();
            douyinHostNameDto.setName(hostName);
            douyinHostNameDto.setStatus(status);
            douyinHostNameDto.setReason(reason);
            return douyinHostNameDto;
        }).collect(Collectors.toList());
    }


    /**
     * 小程序重新提交审核
     *
     * @param landingPageDouyinAppletId
     */
    public void againAuditCode(Long landingPageDouyinAppletId) {
        String componentAppid = getComponentAppid();
        LandingPageDouyinAppletConfig landingPageDouyinAppletConfig = getById(landingPageDouyinAppletId);
        String agentId = TenantContextHolder.get();
        TpAuthDto tpAuthDto = new TpAuthDto();
        tpAuthDto.setComponentAppid(componentAppid);
        tpAuthDto.setAgentId(agentId);
        checkVersion(tpAuthDto, landingPageDouyinAppletConfig, componentAppid);
    }

    /**
     * 查询抖音小程序可审核的宿主端列表
     *
     * @param appId 小程序appId
     * @return
     */
    public HostSideResponse packageAuditHosts(String appId) {
        String componentAppid = getComponentAppid();
        LandingPageDouyinAppletConfig one = getOne(new LambdaQueryWrapper<LandingPageDouyinAppletConfig>().eq(LandingPageDouyinAppletConfig::getDouyinAppletAppid, appId));
        DouyinCommonResponseBody<HostSideResponse> responseBody = openDouyinServiceApiClient.packageAuditHosts(componentAppid, one.getAccessToken());
        return responseBody.getData();
    }

    /**
     * 获取当前抖音小程序版本信息
     *
     * @param appId 小程序appid
     * @return
     */
    public DouyinCommonResponseBody<PackageVersionResponseBody> getAppletPackageVersionInfo(String agentId, String appId) {
        String componentAppid = getComponentAppid();
        LandingPageDouyinAppletConfig one = getOne(new LambdaQueryWrapper<LandingPageDouyinAppletConfig>()
            .eq(LandingPageDouyinAppletConfig::getDouyinAppletAppid, appId)
            .eq(LandingPageDouyinAppletConfig::getAgentId, agentId));
        DouyinCommonResponseBody<PackageVersionResponseBody> body = openDouyinServiceApiClient.packageVersions(componentAppid, one.getAccessToken());
        return body;
    }

    /**
     * 修改用户自定义备注
     *
     * @param douyinAppletId
     * @param remark
     */
    public void updateCustomRemarks(Long douyinAppletId, String remark) {
        boolean update = update(new LambdaUpdateWrapper<LandingPageDouyinAppletConfig>().set(LandingPageDouyinAppletConfig::getCustomRemarks, remark).eq(LandingPageDouyinAppletConfig::getId, douyinAppletId));
        if (!update) {
            throw new RestException("小程序用户自定义备注修改失败!");
        }
    }

    /**
     * 版本号递增
     *
     * @param version
     * @return
     */
    private String increasing(String version) {
        String substring = version.substring(version.lastIndexOf(".") + 1);
        BigDecimal bigDecimal = new BigDecimal(substring);
        bigDecimal = bigDecimal.add(new BigDecimal(1));
        String result = version.substring(0, version.lastIndexOf(".") + 1);
        result += bigDecimal.toString();
        return result;
    }

    /**
     * 版本校验
     */
    private void checkVersion(TpAuthDto tpAuthDto, LandingPageDouyinAppletConfig douyinAppletConfig, String componentAppid) {
        //查询此模板类型最新版本
        LandingPageDouyinAppletTemplate landingPageDouyinAppletTemplate = landingPageDouyinAppletTemplateService.getOne(new LambdaQueryWrapper<LandingPageDouyinAppletTemplate>()
            .eq(LandingPageDouyinAppletTemplate::getTemplateTypeId, douyinAppletConfig.getTemplateTypeId())
            .eq(LandingPageDouyinAppletTemplate::getComponentAppId, componentAppid)
            .orderByDesc(LandingPageDouyinAppletTemplate::getId)
            .last("limit 1"));
        if (ObjectUtils.isEmpty(landingPageDouyinAppletTemplate)) {
            log.error("templateTypeId:{},componentAppId:{} 找不到模板！", douyinAppletConfig.getTemplateTypeId(), componentAppid);
            throw new RestException("小程序找不到对应模板!");
        }
        //查询当前小程序版本信息
        String douyinAppletAppid = douyinAppletConfig.getDouyinAppletAppid();
        DouyinCommonResponseBody<PackageVersionResponseBody> appletPackageVersionInfo = getAppletPackageVersionInfo(tpAuthDto.getAgentId(), douyinAppletAppid);
        PackageVersionResponseBody packageVersionResponseBody = appletPackageVersionInfo.getData();
        //响应参数为空 或者 审核版本不存在 需要重新提交代码且提审
        if (ObjectUtils.isEmpty(packageVersionResponseBody) || ObjectUtils.isEmpty(packageVersionResponseBody.getAudit())) {
            //提交代码 提审
            submitCodeAndAudit(null, tpAuthDto, douyinAppletConfig, landingPageDouyinAppletTemplate);
            return;
        }
        //最新模板版本
        String userVersion = landingPageDouyinAppletTemplate.getUserVersion();
        //当前小程序审核版本
        AuditPackageVersionResponseBody audit = packageVersionResponseBody.getAudit();
        String auditVersion = audit.getVersion();
        //当前小程序线上版本
        OnlinePackageVersionResponseBody current = packageVersionResponseBody.getCurrent();
        String currentRealVersion = (!ObjectUtils.isEmpty(current) && StringUtils.isNotBlank(current.getSummary()) && current.getSummary().contains(YIYE)) ? current.getSummary().replace(YIYE, "") : null;
        //当前线上审核通过的媒体端
        List<String> approvedApps = ObjectUtils.isEmpty(current) ? null : current.getApprovedApps();
        List<DouyinHostsType> douyinHostsTypeList = Arrays.stream(DouyinHostsType.values()).collect(Collectors.toList());
        List<String> strings = douyinHostsTypeList.stream().map(DouyinHostsType::getMessage).collect(Collectors.toList());
        boolean containsAll = CollectionUtils.isEmpty(approvedApps) ? false : approvedApps.containsAll(strings);
        //情况1：线上版本一致 审核成功的媒体端包含主要的三个媒体端，则直接修改状态为审核成功
        if (userVersion.equals(currentRealVersion) && containsAll) {
            log.info("appId:{}小程序版本提交检测=>各媒体端版本与最新模板版本保持一致，无需重新提交审核!", douyinAppletAppid);
            setHostNameSuccess(userVersion, douyinAppletConfig, douyinHostsTypeList);
            return;
        }
        //各媒体端循环检测
        List<DouyinAuthCodeAuditStatus> collect = douyinHostsTypeList.stream().map(e -> resubmit(e, userVersion, audit, current)).collect(Collectors.toList());
        //如果存在需要重新提交代码提审的 则全部重新提交代码审核
        List<DouyinAuthCodeAuditStatus> rearraignment = collect.stream().filter(e -> DouyinAuthCodeAuditStatus.REARRAIGNMENT.equals(e)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(rearraignment)) {
            //重新提交需要检测是否存在正在审核的版本 需要撤销审核的版本
            Integer status = audit.getStatus();
            if (status == 0) {
                revokeAudit(componentAppid, douyinAppletConfig.getDouyinAppletAppid(), douyinAppletConfig.getAccessToken());
            }
            //提交代码 提审
            submitCodeAndAudit(auditVersion, tpAuthDto, douyinAppletConfig, landingPageDouyinAppletTemplate);
            return;
        }
        //不存在需要代码提审的 只有发布代码的 则发布代码
        List<DouyinAuthCodeAuditStatus> needToRelease = collect.stream().filter(e -> DouyinAuthCodeAuditStatus.NEED_TO_RELEASE.equals(e)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(needToRelease)) {
            DouyinCommonResponseBody commonResponseBody = openDouyinServiceApiClient.packageRelease(tpAuthDto.getComponentAppid(), douyinAppletConfig.getAccessToken());
            Integer errno = commonResponseBody.getErrno();
            if (ResultCodeEnum.SUCCESS.getCode() != errno) {
                //发布失败则重新提交代码 提审发布
                //重新提交需要检测是否存在正在审核的版本 需要撤销审核的版本
                Integer status = audit.getStatus();
                if (status == 0) {
                    revokeAudit(componentAppid, douyinAppletConfig.getDouyinAppletAppid(), douyinAppletConfig.getAccessToken());
                }
                submitCodeAndAudit(auditVersion, tpAuthDto, douyinAppletConfig, landingPageDouyinAppletTemplate);
                return;
            }
            //发布成功则直接走下面 修改为审核通过状态
        }
        //以上两种都不存在的 则修改为审核通过状态即可
        setHostNameSuccess(userVersion, douyinAppletConfig, douyinHostsTypeList);
    }

    /**
     * 提交代码 提审
     *
     * @param tpAuthDto
     * @param douyinAppletConfig
     * @param landingPageDouyinAppletTemplate
     */
    private void submitCodeAndAudit(String auditVersion, TpAuthDto tpAuthDto, LandingPageDouyinAppletConfig douyinAppletConfig, LandingPageDouyinAppletTemplate landingPageDouyinAppletTemplate) {
        //提交代码
        submitCode(auditVersion, tpAuthDto, douyinAppletConfig, landingPageDouyinAppletTemplate);
        //代码提审
        realAuditCode(tpAuthDto, douyinAppletConfig, landingPageDouyinAppletTemplate);
    }

    /**
     * 撤回提审任务
     *
     * @param componentAppid
     * @param authorizerAccessToken
     */
    public void revokeAudit(String componentAppid, String douyinAppId, String authorizerAccessToken) {
        DouyinCommonResponseBody commonResponseBody = openDouyinServiceApiClient.revokeAudit(componentAppid, authorizerAccessToken);
        Integer errno = commonResponseBody.getErrno();
        if (ResultCodeEnum.SUCCESS.getCode() != errno) {
            log.error("===> douyinAppId:{} 版本校验->撤回提审任务失败!{}", douyinAppId, commonResponseBody.getMessage());
            throw new RestException("版本校验->撤回提审任务失败");
        }
    }

    /**
     * 每个媒体端检测是否需要重新提审发布
     * <p>
     * 返回是否需要重新提审发布
     *
     * @param hostName    当前要检测的媒体端
     * @param userVersion 最新版本号-迭代版本号+环境
     * @param audit       审核版本号
     * @param current     线上版本号
     */
    private DouyinAuthCodeAuditStatus resubmit(DouyinHostsType hostName, String userVersion, AuditPackageVersionResponseBody audit, OnlinePackageVersionResponseBody current) {
        String message = hostName.getMessage();
        //线上版本-递增版本号
        String currentVersion = ObjectUtils.isEmpty(current) ? null : current.getVersion();
        //线上版本-迭代版本号+环境+yiye标识
        String currentSummary = ObjectUtils.isEmpty(current) ? null : current.getSummary();
        //线上版本-真实迭代版本号+环境 (Summary需要包含一叶标识才能证明是一叶的模板)
        String currentRealVersion = (StringUtils.isNotBlank(currentSummary) && currentSummary.contains(YIYE)) ? currentSummary.replace(YIYE, "") : null;
        //审核版本-递增版本号
        String auditVersion = audit.getVersion();
        //审核版本-迭代版本号+环境+yiye标识
        String auditSummary = audit.getSummary();
        //审核版本-真实迭代版本号+环境
        String auditRealVersion = (StringUtils.isNotBlank(auditSummary) && auditSummary.contains(YIYE)) ? auditSummary.replace(YIYE, "") : null;
        //当前审核状态，0:审核中，1:通过，2:不通过，3：撤回审核
        Integer auditStatus = audit.getStatus();
        //当前线上审核通过的宿主端名称 抖音、抖音火山版、抖音lite
        List<String> currentApprovedApps = ObjectUtils.isEmpty(current) ? null : current.getApprovedApps();
        //当前审核版本包含的宿主端名称 抖音、抖音火山版、抖音lite
        List<String> auditApprovedApps = audit.getApprovedApps();
        //情况1.1：线上版本通过的包含此宿主端 且最新版本与线上版本一致
        if (!CollectionUtils.isEmpty(currentApprovedApps) && currentApprovedApps.contains(message) && userVersion.equals(currentRealVersion)) {
            return DouyinAuthCodeAuditStatus.NO_NEED_TO_OPERATE;
        }
        //情况2：审核版本包含此宿主端 且 审核版本号与本地一致 审核版本状态为审核成功
        if (!CollectionUtils.isEmpty(auditApprovedApps) && auditApprovedApps.contains(message) && userVersion.equals(auditRealVersion) && auditStatus == 1) {
            return DouyinAuthCodeAuditStatus.NEED_TO_RELEASE;
        }
        //不属于以上情况的 重新提交代码 提审
        return DouyinAuthCodeAuditStatus.REARRAIGNMENT;
    }

    /**
     * 设置hostName为true
     *
     * @param douyinAppletConfig
     */
    private void setHostNameSuccess(String userVersion, LandingPageDouyinAppletConfig douyinAppletConfig, List<DouyinHostsType> douyinHostsTypeList) {
        List<DouyinHostNameDto> list = douyinHostsTypeList.stream().map(e -> {
            DouyinHostNameDto douyinHostNameDto = new DouyinHostNameDto();
            douyinHostNameDto.setName(e.getCode());
            douyinHostNameDto.setStatus(DouyinCodeAuditStatus.SUCCESS);
            douyinHostNameDto.setReason("");
            return douyinHostNameDto;
        }).collect(Collectors.toList());
        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(list));
        LandingPageDouyinAppletConfig landingPageDouyinAppletConfig = new LandingPageDouyinAppletConfig();
        landingPageDouyinAppletConfig.setId(douyinAppletConfig.getId());
        landingPageDouyinAppletConfig.setHostNames(jsonArray);
        landingPageDouyinAppletConfig.setVersion(userVersion);
        updateById(landingPageDouyinAppletConfig);
    }

    /**
     * 响应实体转为数据库实体类
     *
     * @param douyinAppletResponseBody
     * @return
     */
    private LandingPageDouyinAppletConfig responseInfoToData(DouyinAppletResponseBody douyinAppletResponseBody, AuthTokenResponseBody authTokenResponseBody, AuthTokenUpgradeesponseBody authTokenUpgradeesponseBody) {
        LandingPageDouyinAppletConfig landingPageDouyinAppletConfig = new LandingPageDouyinAppletConfig();
        DouyinAppletSubjectAuditInfoDto subjectAuditInfo = douyinAppletResponseBody.getSubjectAuditInfo();
        landingPageDouyinAppletConfig.setDouyinAppletAppid(douyinAppletResponseBody.getAppId())
            .setAccessToken(authTokenResponseBody.getAuthorizerAccessToken())
            .setRefreshToken(authTokenResponseBody.getAuthorizerRefreshToken())
            .setAccessTokenNew(authTokenUpgradeesponseBody.getAuthorizerAccessToken())
            .setRefreshTokenNew(authTokenUpgradeesponseBody.getAuthorizerRefreshToken())
            .setDouyinAppletName(douyinAppletResponseBody.getAppName())
            .setAuditStatus(DouyinAuditStatus.AUDITABLE)
            .setAppType(douyinAppletResponseBody.getAppType())
            .setRemarks(douyinAppletResponseBody.getAppIntro())
            .setImgUrl(douyinAppletResponseBody.getAppIcon())
            .setPrincipalName(ObjectUtils.isEmpty(subjectAuditInfo) ? null : subjectAuditInfo.getSubjectName()).setSubjectType(ObjectUtils.isEmpty(subjectAuditInfo) ? null : subjectAuditInfo.getSubjectType());
        //权限集
        List<AuthorizePermissionResponseBody> authorizePermission = authTokenResponseBody.getAuthorizePermission();
        if (!CollectionUtils.isEmpty(authorizePermission)) {
            List<Integer> collect = authorizePermission.stream().map(AuthorizePermissionResponseBody::getId).collect(Collectors.toList());
            landingPageDouyinAppletConfig.setPermissions(collect.toArray(new Integer[]{}));
        }
        return landingPageDouyinAppletConfig;
    }

    public Object getwxacodeunlimit(Long advertiseAccountGroupId, String appname, String path, Integer width, String line_color, String background, Boolean set_icon) {
        List<LandingPageDouyinAppletPmpRel> confIds = landingPageDouyinAppletPmpRelService.list(new LambdaQueryWrapper<LandingPageDouyinAppletPmpRel>()
            .eq(LandingPageDouyinAppletPmpRel::getAdvertiserAccountGroupId, advertiseAccountGroupId)
        );
        if (CollectionUtils.isEmpty(confIds)) {
            throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_DOUYIN_APPLET_NOT_EXIST);
        }
        List<Long> ids = confIds.stream().map(LandingPageDouyinAppletPmpRel::getLandingPageDouyinAppletId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_DOUYIN_APPLET_NOT_EXIST);
        }
        LandingPageDouyinAppletConfig lpdac = baseMapper.selectOne(new LambdaQueryWrapper<LandingPageDouyinAppletConfig>()
            .eq(LandingPageDouyinAppletConfig::getAgentId, TenantContextHolder.get())
            .in(LandingPageDouyinAppletConfig::getAuditStatus, DouyinAuditStatus.SUCCESS, DouyinAuditStatus.SUCCESS_AUDITABLE)
            .in(LandingPageDouyinAppletConfig::getId, ids).orderByDesc(LandingPageDouyinAppletConfig::getCreatedAt)
            .last(" limit 1")
        );
        if (Objects.isNull(lpdac)) {
            throw new RestException(ErrorConstants.ERROR_NOT_HAVE_CAN_USE_WECHAT_APPLET_CONFIG);
        }
        //        JSONObject params = new JSONObject();
        //        params.put("access_token", landingPageDouyinAppletConfig.getAccessToken());
        //        params.put("appname", appname);
        //        params.put("path", path);
        //        params.put("width", width);
        //        params.put("line_color", line_color);
        //        params.put("background", background);
        //        params.put("set_icon", set_icon);
        //        Object result = OpenAppletCreateQrCodeApiClient.getDouYinQRCode(params);
        //        if (!Objects.isNull(result) && result instanceof String) {
        //            return result;
        //        }
        //        log.error("获取字节小程序二维码错误 result={}；landingPageDouyinAppletConfig={}；params={}；", result, JSONObject.toJSONString(landingPageDouyinAppletConfig), params);
        JSONObject params = new JSONObject();
        params.put("version", "current");
        params.put("path", path);
        JSONObject result = openDouyinUpgradeApiClient.microappAppQrcode(lpdac.getAccessTokenNew(), params);
        if (!Objects.isNull(result) && Objects.equals(0, result.getInteger("err_no"))) {
            JSONObject data = result.getJSONObject("data");
            if (Objects.isNull(data) || CollectionUtils.isEmpty(data.keySet())) {
                log.error("获取字节小程序二维码错误1.0.0.1 result={}；lpdac={}；params={}；", result, JSONObject.toJSONString(lpdac), params);
                throw new RestException(ErrorConstants.ERROR_GET_WECHAT_APPLET_PREVIEW_QR_CODE_IMAGE_ERROR);
            }
            return data.getString("qr_code_url");
        }
        log.error("获取字节小程序二维码错误1.0.0.2 result={}；lpdac={}；params={}；", result, JSONObject.toJSONString(lpdac), params);
        throw new RestException(ErrorConstants.ERROR_GET_WECHAT_APPLET_PREVIEW_QR_CODE_IMAGE_ERROR);
    }

    public Page<LandingPageDouyinAppletConfig> getPage(Page<LandingPageDouyinAppletConfig> page, LandingPageDouyinAppletConfigPageDto dto) {
        return baseMapper.getPage(page, dto);
    }

    /**
     * 根据agentId和pmpId获取字节小程序配置
     */
    public List<LandingPageDouyinAppletConfig> getByAgentIdAndPmpId(Long advertiserAccountGroupId) {
        return baseMapper.getByAgentIdAndPmpId(TenantContextHolder.get(), advertiserAccountGroupId);
    }

    /**
     * 获取重定向链接
     *
     * @param advertiserAccountGroupId
     * @return
     */
    public String getRedirectUrl(Long advertiserAccountGroupId, String statusCode) {
        String path = "/pmp/byte-mini-program/tiktok-micro-app?type=pmp&advertiserGroupId=" + advertiserAccountGroupId + "&statusCode=" + statusCode;
        return getRedirectUrl(path);
    }

    public String getRedirectUrl(String path) {
        String httpStatus = agentConf.getHttpStatus();
        String frontendDomain = agentConf.getFrontendDomain();
        String redirectUrl;
        if (agentConf.getIsFixed()) {
            redirectUrl = httpStatus + frontendDomain + path;
        } else {
            redirectUrl = httpStatus + TenantContextHolder.get() + "." + frontendDomain + path;
        }
        return redirectUrl;
    }

    private void realAuditCode(TpAuthDto tpAuthDto, LandingPageDouyinAppletConfig landingPageDouyinAppletConfig, LandingPageDouyinAppletTemplate landingPageDouyinAppletTemplate) {
        List<DouyinHostNameDto> hostNames = auditCode(tpAuthDto, landingPageDouyinAppletConfig);
        //修改小程序代码审核状态
        if (CollectionUtils.isEmpty(hostNames)) {
            throw new RestException("重新提交审核失败!");
        }
        LandingPageDouyinAppletConfig param = new LandingPageDouyinAppletConfig();
        param.setId(landingPageDouyinAppletConfig.getId())
            .setHostNames(JSONArray.parseArray(JSON.toJSONString(hostNames)))
            .setAuditStatus(DouyinAuditStatus.IN_THE_REVIEW)
            .setVersion(landingPageDouyinAppletTemplate.getUserVersion())
            .setTemplateVersionId(landingPageDouyinAppletTemplate.getId());
        boolean b = updateById(param);
        if (b) {
            log.info("==>抖音小程序:{}代码提交、提审完成，修改为审核中状态!", landingPageDouyinAppletConfig.getDouyinAppletAppid());
        }
    }

    /**
     * 修改跳转小程序配置
     */
    public void modifyNavigateMiniPrograms(LandingPageDouyinAppletConfig landingPageDouyinAppletConfig) {
        LandingPageDouyinAppletConfig originConfig = this.getById(landingPageDouyinAppletConfig.getId());
        if (!Arrays.equals(originConfig.getNavigateMiniPrograms(), landingPageDouyinAppletConfig.getNavigateMiniPrograms())) {
            this.lambdaUpdate()
                .set(LandingPageDouyinAppletConfig::getAuditStatus, DouyinAuditStatus.SUCCESS.equals(originConfig.getAuditStatus()) ? DouyinAuditStatus.SUCCESS_AUDITABLE : DouyinAuditStatus.AUDITABLE)
                .set(LandingPageDouyinAppletConfig::getNavigateMiniPrograms, landingPageDouyinAppletConfig.getNavigateMiniPrograms())
                .set(LandingPageDouyinAppletConfig::getUpdatedAt, Instant.now())
                .eq(LandingPageDouyinAppletConfig::getId, landingPageDouyinAppletConfig.getId())
                .update();
        }
    }

}
