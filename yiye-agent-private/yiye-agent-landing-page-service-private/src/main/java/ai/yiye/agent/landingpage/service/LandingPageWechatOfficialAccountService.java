package ai.yiye.agent.landingpage.service;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.common.util.CommonUtil;
import ai.yiye.agent.common.util.DateTimeUtil;
import ai.yiye.agent.domain.LandingPageChannel;
import ai.yiye.agent.domain.LandingPageWechatOfficialAccountCustomer;
import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.dto.*;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.landingpage.*;
import ai.yiye.agent.domain.landingpage.dto.LandingPageWechatOfficialAccountCacheDto;
import ai.yiye.agent.domain.landingpage.dto.LandingPageWechatOfficialAccountUserCacheDto;
import ai.yiye.agent.domain.landingpage.dto.OfficialWechatCustomerContactDto;
import ai.yiye.agent.domain.landingpage.dto.OfficialWechatCustomerServiceGroupCacheDto;
import ai.yiye.agent.domain.pageview.PageViewInfo;
import ai.yiye.agent.domain.utils.CustomerAdParamUtil;
import ai.yiye.agent.domain.utils.UrlUtils;
import ai.yiye.agent.landingpage.config.AgentConf;
import ai.yiye.agent.landingpage.controller.vo.WechatOfficialAccountListVO;
import ai.yiye.agent.landingpage.dto.*;
import ai.yiye.agent.landingpage.enums.CustomerServiceChangeEnum;
import ai.yiye.agent.landingpage.enums.exception.WechatAppletResultCode;
import ai.yiye.agent.landingpage.enums.exception.WechatOfficialAccountResultCode;
import ai.yiye.agent.landingpage.mapper.LandingPageWechatOfficialAccountMapper;
import ai.yiye.agent.landingpage.mapper.LandingPageWechatOfficialAccountRelMapper;
import ai.yiye.agent.landingpage.redis.*;
import ai.yiye.agent.landingpage.remote.BossBackendRemote;
import ai.yiye.agent.landingpage.sender.*;
import ai.yiye.agent.weixin.client.OpenWeixinApiClient;
import ai.yiye.agent.weixin.domain.BaseOfficialImageMessageDto;
import ai.yiye.agent.weixin.domain.BaseOfficialMessageDto;
import ai.yiye.agent.weixin.domain.BaseOfficialTxtMessageDto;
import ai.yiye.agent.weixin.domain.OpenCreateOpenResponseBody;
import ai.yiye.agent.weixin.exception.MarketingWeOpenApiException;
import ai.yiye.agent.weixin.mapper.OpenWechatMappers;
import ai.yiye.agent.weixin.mapper.WechatPropertyMapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.constant.WxCpConsts;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 公众号
 */
@Slf4j
@Service
@DS(DbConstants.POSTGRESQL_DEFAULT)  //设置成默认数据库
public class LandingPageWechatOfficialAccountService extends ServiceImpl<LandingPageWechatOfficialAccountMapper, LandingPageWechatOfficialAccount> {

    @Resource
    private UploadSender uploadSender;
    @Autowired
    private PageViewSender pageViewSender;
    @Resource
    private PageViewInfoRedis pageViewInfoRedis;
    @Resource
    private PageViewInfoPgService pageViewInfoPgService;
    @Resource
    private LandingPageChannelService landingPageChannelService;
    @Resource
    private OpenWeixinApiClient openWeixinApiClient;
    @Resource
    private LandingPageWechatOfficialAccountRelService landingPageWechatOfficialAccountRelService;
    @Resource
    private RedisTemplate<String, Object> objectRedisTemplate;
    @Autowired
    private WechatOfficialAccountRedis wechatOfficialAccountRedis;
    @Autowired
    private WechatOfficialAccountAssistantReplyService wechatOfficialAccountAssistantReplyService;
    @Autowired
    private WechatOfficialAccountAssistantReplyMessageService wechatOfficialAccountAssistantReplyMessageService;
    @Autowired
    private LandingPageWechatOfficialMaterialFileService landingPageWechatOfficialMaterialFileService;
    @Autowired
    private LandingPageWechatOfficialAccountUserService landingPageWechatOfficialAccountUserService;
    @Autowired
    private LandingPageSender landingPageSender;
    @Autowired
    private AdvertiseRedis advertiseRedis;
    @Autowired
    private WechatOfficialAccountAssistantRelService wechatOfficialAccountAssistantRelService;
    @Autowired
    private WechatOfficialAccountAssistantMenuNavigationService wechatOfficialAccountAssistantMenuNavigationService;
    @Autowired
    private CustomerUploadRecordServiceRedis customerUploadRecordServiceRedis;
    @Autowired
    private LandingPageWechatCustomerServiceRedis landingPageWechatCustomerServiceRedis;
    @Autowired
    private LandingPageWechatCustomerServiceService landingPageWechatCustomerServiceService;
    @Autowired
    private OfficialAccountSendMessageRecordRedis officialAccountSendMessageRecordRedis;
    @Autowired
    private OfficialWechatCustomerContactService officialWechatCustomerContactService;
    @Resource
    private OfficialAccountSendMessageRecordService officialAccountSendMessageRecordService;
    @Autowired
    private OfficialWechatCustomerServiceGroupService officialWechatCustomerServiceGroupService;

    @Resource
    private LandingPageWechatOfficialAccountService landingPageWechatOfficialAccountService;

    @Resource
    private BossBackendRemote bossBackendRemote;
    @Autowired
    private AgentConf agentConf;
    @Resource
    private MultiplayerCodeSender multiplayerCodeSender;
    @Autowired
    private CustomerSender customerSender;

    @Autowired
    private EnterpriseWechatRobotCustomerService enterpriseWechatRobotCustomerService;

    @Resource
    private OfficialMicromilligramService officialMicromilligramService;
    @Resource
    private TaokeApplicationRedis taokeApplicationRedis;
    @Resource
    private LandingPageWechatOfficialAccountRelMapper landingPageWechatOfficialAccountRelMapper;


    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(LandingPageWechatOfficialAccount wechatOfficialAccount, Long advertiserAccountGroupId) {
        this.saveOrUpdate(wechatOfficialAccount);
        //判断之前有没有关联关系，没有就新增一条关联关系
        Integer count = landingPageWechatOfficialAccountRelMapper.selectCount(new LambdaQueryWrapper<LandingPageWechatOfficialAccountRel>().eq(LandingPageWechatOfficialAccountRel::getWechatOfficialAccountId, wechatOfficialAccount.getId()).eq(LandingPageWechatOfficialAccountRel::getAdvertiserAccountGroupId, advertiserAccountGroupId));
        if (count == 0) {
            LandingPageWechatOfficialAccountRel rel = new LandingPageWechatOfficialAccountRel();
            rel.setWechatOfficialAccountId(wechatOfficialAccount.getId()).setAdvertiserAccountGroupId(advertiserAccountGroupId);
            landingPageWechatOfficialAccountRelMapper.insert(rel);
        }
    }

    /**
     * 分页
     *
     * @param page
     * @param dto
     * @return
     */
    public Page<LandingPageWechatOfficialAccount> getPage(Page<LandingPageWechatOfficialAccount> page, LandingPageWechatOfficialAccountPageDTO dto) {
        return baseMapper.getPage(page, dto);
    }

    /**
     * 获取公众号二维码图片url
     *
     * @param officialId
     * @return
     */
    public String getQrCodeUrlById(Long officialId) {
        if (ObjectUtils.isEmpty(officialId)) {
            return null;
        }
        //先查询缓存
        String key = RedisConstant.LANDING_OFFICIAL_QRCODE_CACHE_KEY + officialId;
        Object o = objectRedisTemplate.opsForValue().get(key);
        if (!ObjectUtils.isEmpty(o)) {
            String s = String.valueOf(o);
            if ("null".equals(s)) {
                return null;
            }
            return String.valueOf(o);
        }
        //查询url
        LandingPageWechatOfficialAccount landingPageWechatOfficialAccount = getById(officialId);
        if (ObjectUtils.isEmpty(landingPageWechatOfficialAccount)) {
            //查询不到结果 缓存插入null字符串 防止频繁穿透
            objectRedisTemplate.opsForValue().set(key, "null", 5, TimeUnit.MINUTES);
            return null;
        }
        String officialQrcodeUrl = landingPageWechatOfficialAccount.getOfficialQrcodeUrl();
        //存储缓存
        objectRedisTemplate.opsForValue().set(key, officialQrcodeUrl, 1, TimeUnit.HOURS);
        return officialQrcodeUrl;
    }

    /**
     * 获取公众号二维码图片url
     *
     * @param officialId
     * @return
     */
    public String getQrCode(Long officialId, Long advertiserAccountGroupId) {
        if (ObjectUtils.isEmpty(officialId) || ObjectUtils.isEmpty(advertiserAccountGroupId)) {
            return null;
        }
        //先查询缓存
        String key = RedisConstant.LANDING_OFFICIAL_PMP_QRCODE_CACHE_KEY + officialId + ":" + advertiserAccountGroupId;
        Object o = objectRedisTemplate.opsForValue().get(key);
        if (!ObjectUtils.isEmpty(o)) {
            String s = String.valueOf(o);
            if ("null".equals(s)) {
                return null;
            }
            return String.valueOf(o);
        }
        //查询url
        LandingPageWechatOfficialAccountRel rel = landingPageWechatOfficialAccountRelService.getOne(new LambdaQueryWrapper<LandingPageWechatOfficialAccountRel>()
            .eq(LandingPageWechatOfficialAccountRel::getWechatOfficialAccountId, officialId)
            .eq(LandingPageWechatOfficialAccountRel::getAdvertiserAccountGroupId, advertiserAccountGroupId)
            .last("limit 1"));
        if (ObjectUtils.isEmpty(rel)) {
            //查询不到结果 缓存插入null字符串 防止频繁穿透
            objectRedisTemplate.opsForValue().set(key, "null", 5, TimeUnit.MINUTES);
            return null;
        }
        String officialQrcodeUrl = rel.getPmpQrcodeUrl();
        if (StringUtils.isBlank(officialQrcodeUrl)) {
            //此处是为了兼容历史数据
            LandingPageWechatOfficialAccount landingPageWechatOfficialAccount = getById(officialId);
            if (ObjectUtils.isEmpty(landingPageWechatOfficialAccount)) {
                //查询不到结果 缓存插入null字符串 防止频繁穿透
                objectRedisTemplate.opsForValue().set(key, "null", 5, TimeUnit.MINUTES);
                return null;
            }
            officialQrcodeUrl = landingPageWechatOfficialAccount.getOfficialQrcodeUrl();
            //存储缓存
            objectRedisTemplate.opsForValue().set(key, officialQrcodeUrl, 1, TimeUnit.HOURS);
            return officialQrcodeUrl;
        }
        //存储缓存
        objectRedisTemplate.opsForValue().set(key, officialQrcodeUrl, 1, TimeUnit.HOURS);
        return officialQrcodeUrl;
    }

    /**
     * 添加修改公众号二维码
     *
     * @param dto 公众号信息
     * @return
     */
    public boolean addQrcode(LandingPageWechatOfficialAccountQrcodeDto dto) {
        String appId = dto.getAppId();
        String officialQrcodeUrl = dto.getOfficialQrcodeUrl();
        Long advertiserAccountGroupId = dto.getAdvertiserAccountGroupId();
        //参数是否正确
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(officialQrcodeUrl) || advertiserAccountGroupId == null) {
            return false;
        }
        //查询公众号是否存在
        LambdaQueryWrapper<LandingPageWechatOfficialAccount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LandingPageWechatOfficialAccount::getAppId, appId).last("limit 1");
        LandingPageWechatOfficialAccount wechatOfficialAccount = this.getOne(lambdaQueryWrapper);
        if (ObjectUtils.isEmpty(wechatOfficialAccount)) {
            return false;
        }
        Long id = wechatOfficialAccount.getId();
        try {
            landingPageWechatOfficialAccountRelService.update(new LambdaUpdateWrapper<LandingPageWechatOfficialAccountRel>()
                .eq(LandingPageWechatOfficialAccountRel::getWechatOfficialAccountId, id)
                .eq(LandingPageWechatOfficialAccountRel::getAdvertiserAccountGroupId, advertiserAccountGroupId)
                .set(LandingPageWechatOfficialAccountRel::getPmpQrcodeUrl, officialQrcodeUrl)
            );
            //修改公众号二维码缓存
            String key = RedisConstant.LANDING_OFFICIAL_PMP_QRCODE_CACHE_KEY + id + ":" + advertiserAccountGroupId;
            TenantContextHolder.clearContext();
            //清除旧缓存
            objectRedisTemplate.delete(key);
            //存储缓存
            objectRedisTemplate.opsForValue().set(key, officialQrcodeUrl, 1, TimeUnit.HOURS);
            return true;
        } catch (Exception e) {
            log.error("====official id:{} officialQrcodeUrl:{} update officialQrcodeUrl error!===", id, officialQrcodeUrl);
            log.error("e==>", e);
        }
        return false;
    }

    /**
     * 处理回调消息
     */
    public void processMessage(final WxMpXmlMessage wxMessage, final String wechatAppid) {
        final Instant nowTime = Instant.now();
        log.info("公众号-处理回调消息-wechatAppid:{},wxMessage:{}", wechatAppid, JSONObject.toJSONString(wxMessage));
        //没有事件类型直接返回
        if (Objects.isNull(wxMessage.getEvent())) {
            return;
        }
        final String event = wxMessage.getEvent().toLowerCase();
        final String fromUser = wxMessage.getFromUser();
        //从缓存中获取微信公众号信息
        LandingPageWechatOfficialAccountCacheDto officialAccountDto = wechatOfficialAccountRedis.getLandingPageWechatOfficialAccount(wechatAppid);
        if (null == officialAccountDto) {
            log.info("公众号-处理回调消息-查询不到当前公众号信息,wechatAppid:{}", wechatAppid);
            return;
        }
        final String agentId = officialAccountDto.getAgentId();
        //校验公众号所属账户是否可用
        ai.yiye.agent.domain.AgentConf agentConf = taokeApplicationRedis.getAgentConf(agentId);
        Integer status = agentConf.getStatus();
        if(!Objects.equals(1, status)){
            log.info("公众号-处理回调消息-公众号所属账户不可用，公众号：{}，所属账户：{}", wechatAppid, agentId);
            return;
        }
        TenantContextHolder.set(agentId);
        //关注公众号 / 取消关注公众号 事件，清理数据，重新绑定粉丝数据
        if (Arrays.asList(WxCpConsts.EventType.SUBSCRIBE, WxCpConsts.EventType.UNSUBSCRIBE).contains(event)) {
            log.info("公众号-处理回调消息-微信用户取消关注该公众号 wechatAppid={}；fromUser={}；", wechatAppid, fromUser);
            List<LandingPageWechatOfficialAccountUser> lpwoaUserList = landingPageWechatOfficialAccountUserService.list(new LambdaQueryWrapper<LandingPageWechatOfficialAccountUser>()
                .select(LandingPageWechatOfficialAccountUser::getId, LandingPageWechatOfficialAccountUser::getCreatedAt)
                .eq(LandingPageWechatOfficialAccountUser::getAppId, wechatAppid).eq(LandingPageWechatOfficialAccountUser::getOpenid, fromUser)
                //30天内的数据
                .ge(LandingPageWechatOfficialAccountUser::getCreatedAt, nowTime.minusMillis(TimeUnit.DAYS.toMillis(30)))
            );
            if (!CollectionUtils.isEmpty(lpwoaUserList)) {
                List<Long> ids = lpwoaUserList.stream().map(LandingPageWechatOfficialAccountUser::getId).collect(Collectors.toList());
                //取消关注事件，过滤出不需要删除的数据，修改数据状态为已删除
                if (!CollectionUtils.isEmpty(ids) && Objects.equals(WxCpConsts.EventType.UNSUBSCRIBE, event)) {
                    landingPageWechatOfficialAccountUserService.update(new LambdaUpdateWrapper<LandingPageWechatOfficialAccountUser>()
                        .in(LandingPageWechatOfficialAccountUser::getId, ids).set(LandingPageWechatOfficialAccountUser::getDeleteStatus, DeleteStatus.IS_DELETE)
                    );
                }
                //发送更新【公众号粉丝取消关注】信息队列
                customerSender.sendWechatOfficialCustomerUpdateFollowstatus(new LandingPageWechatOfficialAccountCustomer()
                    .setAppId(wechatAppid).setOpenid(fromUser).setUnFollowTime(nowTime)
                );
                //清理缓存
                wechatOfficialAccountRedis.clearWechatOfficialAccountUserInfo(wechatAppid, fromUser);
            }
            //【关注、取消关注】删除1小时内，所有历史已发送的异主体二维码
            officialAccountSendMessageRecordService.deleteSendMessageQrCode(fromUser);
        }
        //非关注事件/非点击菜单，直接事件忽略
        if ((!Arrays.asList(WxCpConsts.EventType.SUBSCRIBE, WxCpConsts.EventType.CLICK).contains(wxMessage.getEvent().toUpperCase()) && !Arrays.asList(WxCpConsts.EventType.SUBSCRIBE, WxCpConsts.EventType.CLICK).contains(wxMessage.getEvent().toLowerCase()))) {
            log.debug("公众号-处理回调消息-非关注事件非点击菜单事件忽略-wechatAppid:{},wxMessage:{}", wechatAppid, JSONObject.toJSONString(wxMessage));
            return;
        }
        //获取用户基本信息(UnionID机制)
        LandingPageWechatOfficialAccountUserCacheDto lpwoauCacheDto = landingPageWechatOfficialAccountUserService.getWechatOfficialAccountUserInfo(wechatAppid, fromUser, officialAccountDto, nowTime);
        if (Objects.isNull(lpwoauCacheDto)) {
            log.info("公众号回调兑换公众号粉丝用户信息异常 wechatAppid={}；lpwoau={}", wechatAppid, lpwoauCacheDto);
            return;
        }
        log.info("公众号回调自动回复消息用户缓存信息 lpwoauCacheDto={}；", JSONObject.toJSONString(lpwoauCacheDto));
        if (WxCpConsts.EventType.SUBSCRIBE.equalsIgnoreCase(wxMessage.getEvent())) {
            wechatOfficalAccountSubscribe(WechatOfficialAccountAssistantRecoverType.AUTOMATIC, wechatAppid, agentId, lpwoauCacheDto, wxMessage, officialAccountDto, nowTime);
            return;
        }
        if (WxCpConsts.EventType.CLICK.equalsIgnoreCase(wxMessage.getEvent())) {
            sendWechatOfficalAccountMessage(WechatOfficialAccountAssistantRecoverType.NAVIGATION, wechatAppid, agentId, lpwoauCacheDto, wxMessage);
        }
    }








    /**
     * 公众号点击事件回调 公众号菜单推送消息
     */
    public void sendWechatOfficalAccountMessage(final WechatOfficialAccountAssistantRecoverType recoverType,
                                                final String wechatAppid,
                                                final String agentId,
                                                final LandingPageWechatOfficialAccountUserCacheDto lpwoauCacheDto,
                                                final WxMpXmlMessage wxMessage
    ) {
        TenantContextHolder.set(agentId);
        final String matchingPid = lpwoauCacheDto.getMatchingPid();
        final Integer subscribeScene = lpwoauCacheDto.getSubscribeScene();
        //同一微信同一公众号用单位时间内频次限制
        final String openid = lpwoauCacheDto.getOpenid();
        log.info("公众号-处理回调消息-触发消息回复开始 ======>> agentId={}；wechatAppid={}；lpwoauiDto={}", agentId, wechatAppid, lpwoauCacheDto);
        List<WechatOfficialAccountAssistantReply> woaarList = wechatOfficialAccountAssistantReplyService.getWechatOfficialAccountAssistantReplyListByRecoverType(wechatAppid, recoverType, wxMessage.getEventKey());
        if (CollectionUtils.isEmpty(woaarList)) {
            log.info("公众号-处理回调消息-公众号点击菜单推送消息获取微信公众号助手自动回复表为空 ======>> agentId={}；wechatAppid={}；lpwoau={}", agentId, wechatAppid, lpwoauCacheDto);
            return;
        }
        boolean matchStatus = false;
        for (WechatOfficialAccountAssistantReply woaar : woaarList) {
            if (matchStatus) {
                continue;
            }
            //校验：自动回复规则校验-粉丝-关注渠道
            if (Objects.isNull(subscribeScene) || (!Objects.isNull(woaar.getFollowChannel()) && woaar.getFollowChannel().length > 0 && !Arrays.asList(woaar.getFollowChannel()).contains(subscribeScene))) {
                log.info("自动回复规则校验粉丝关注渠道不匹配 wechatAppid={}；openid={}；agentId={}；subscribeScene={}；lpwoauCacheDtoId={}；id={}；followChannel={}；", wechatAppid, openid, agentId, subscribeScene, lpwoauCacheDto.getId(), woaar.getId(), woaar.getFollowChannel());
                continue;
            }
            //校验：自动回复规则校验-公众号粉丝-广告来源，设定【关注来源】为不满足广告来源条件
            boolean continueStatus = true;
            if (Objects.equals(OfficialFollowSource.ADVERTISEMENT, woaar.getOfficialFollowSource()) && Objects.equals(woaar.getOfficialFollowSource().getValue(), lpwoauCacheDto.getOfficialFollowSource())) {
                //来源广告
                FollowSourcePlatform followSourcePlatform = FollowSourcePlatform.getByCode(lpwoauCacheDto.getFollowSourcePlatform());
                if (Objects.isNull(followSourcePlatform)) {
                    log.info("来源广告结果为空，数据异常，暂停发送消息 wechatAppid={}；openid={}；agentId={}；subscribeScene={}；lpwoauCacheDto={}；woaar={}；followSourcePlatform={}；", wechatAppid, openid, agentId, subscribeScene, JSONObject.toJSONString(lpwoauCacheDto), JSONObject.toJSONString(woaar), woaar.getFollowSourcePlatform());
                    return;
                }
                //校验：信息流广告、搜索广告
                if (followSourcePlatform.getPlatformList().contains(Platform.OCEAN_ENGINE) && !Objects.equals(woaar.getOceanEngineType().getId(), lpwoauCacheDto.getOceanEngineType())) {
                    log.info("校验信息流广告搜索广告不通过，暂停发送消息 wechatAppid={}；openid={}；agentId={}；subscribeScene={}；lpwoauCacheDto={}；woaar={}；followSourcePlatform={}；", wechatAppid, openid, agentId, subscribeScene, JSONObject.toJSONString(lpwoauCacheDto), JSONObject.toJSONString(woaar), woaar.getFollowSourcePlatform());
                    continueStatus = false;
                }
                List<Integer> checkList = new ArrayList<>(Arrays.asList(woaar.getFollowSourcePlatform()));
                if (Objects.equals(OceanEngineType.OCEAN_SEARCH.getId(), lpwoauCacheDto.getOceanEngineType())) {
                    List<Integer> codes = FollowSourcePlatform.getFollowSourcePlatformsByType(FollowSourcePlatform.DOUYIN.getType());
                    if (!CollectionUtils.isEmpty(codes)) {
                        checkList.addAll(codes);
                    }
                }
                if (!CollectionUtils.isEmpty(checkList) && checkList.contains(lpwoauCacheDto.getFollowSourcePlatform())) {
                    log.info("校验关注来源来源广告对应值不通过，暂停发送消息 wechatAppid={}；openid={}；agentId={}；subscribeScene={}；lpwoauCacheDto={}；woaar={}；followSourcePlatform={}；checkList={}；", wechatAppid, openid, agentId, subscribeScene, JSONObject.toJSONString(lpwoauCacheDto), JSONObject.toJSONString(woaar), woaar.getFollowSourcePlatform(), JSONObject.toJSONString(checkList));
                    continueStatus = false;
                }
            } else if (Objects.equals(OfficialFollowSource.NON_ADVERTISING, woaar.getOfficialFollowSource()) && Objects.equals(woaar.getOfficialFollowSource().getValue(), lpwoauCacheDto.getOfficialFollowSource())) {
                //非广告来源
                continueStatus = false;
            } else if (Objects.isNull(woaar.getOfficialFollowSource()) || Objects.equals(OfficialFollowSource.ALL, woaar.getOfficialFollowSource())) {
                //全部来源
                continueStatus = false;
            }
            if (continueStatus) {
                log.info("自动回复规则校验粉丝关注来源不匹配 wechatAppid={}；openid={}；agentId={}；subscribeScene={}；lpwoauCacheDto={}；woaar={}；followSourcePlatform={}；", wechatAppid, openid, agentId, subscribeScene, JSONObject.toJSONString(lpwoauCacheDto), JSONObject.toJSONString(woaar), woaar.getFollowSourcePlatform());
                continue;
            }
            matchStatus = true;
            //全部发送消息模板
            List<WechatOfficialAccountAssistantReplyMessage> woaarmList = wechatOfficialAccountAssistantReplyMessageService.getListByWechatOfficialAccountAssistantReplyId(wechatAppid, woaar.getId());
            if (CollectionUtils.isEmpty(woaarmList)) {
                continue;
            }
            OfficialAccountSendMessageRecord dynamicQrCodeOasmrCacheDto = null;
            //过滤是否包含【单人活码】相关数据，进行发送记录查询
            WechatOfficialAccountAssistantReplyMessage dynamicQrCode = woaarmList.stream().filter(e -> Objects.equals(OfficialAccountAssistantReplyMessageType.DYNAMIC_QR_CODE, e.getMessageType()) || Objects.equals(OfficialAccountAssistantReplyMessageCategory.USER_CLICK_AFTER_INCREASE_FOLLOWERS_REPLY, e.getMessageCategory())).findFirst().orElse(null);
            if (!Objects.isNull(dynamicQrCode)) {
                //最近一次发送【活码-客服动态渠道二维码】记录
                dynamicQrCodeOasmrCacheDto = officialAccountSendMessageRecordRedis.getSendMessageRecord(wechatAppid, openid, DateTimeUtil.long2Instant(lpwoauCacheDto.getCreatedAt()), OfficialAccountAssistantReplyMessageType.DYNAMIC_QR_CODE);
            }
            //筛选【加粉后用户点击回复】
            List<WechatOfficialAccountAssistantReplyMessage> userClickAfterIncreaseFollowersReplyList = woaarmList.stream().filter(e -> OfficialAccountAssistantReplyMessageCategory.USER_CLICK_AFTER_INCREASE_FOLLOWERS_REPLY.equals(e.getMessageCategory())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(userClickAfterIncreaseFollowersReplyList)) {
                log.info("公众号回复消息最近一次已发送记录 wechatAppid={}；openid={}；finsCreatedAt={}；oasmrCacheDto={}；", wechatAppid, openid, lpwoauCacheDto.getCreatedAt(), JSONObject.toJSONString(dynamicQrCodeOasmrCacheDto));
                //加粉成功，再次点击菜单，只发送：活码-客服动态渠道二维码-加粉后用户点击回复、活码-分组动态渠道二维码
                if (!Objects.isNull(dynamicQrCodeOasmrCacheDto)
                    && Objects.equals(AddEnterpriseWechatStatus.ADDED, dynamicQrCodeOasmrCacheDto.getAddEnterpriseWechatStatus())
                    && WechatOfficialAccountAssistantRecoverType.NAVIGATION.equals(recoverType)) {
                    woaarmList = userClickAfterIncreaseFollowersReplyList;
                    //未加粉成功，再次点击菜单，只发送，包含：客服动态渠道二维码、分组动态渠道二维码
                } else {
                    woaarmList = woaarmList.stream().filter(e -> !OfficialAccountAssistantReplyMessageCategory.USER_CLICK_AFTER_INCREASE_FOLLOWERS_REPLY.equals(e.getMessageCategory())).collect(Collectors.toList());
                }
            }
            int delayTime = 0;
            //发送过【加粉后点击回复】停止后续操作
            boolean stopContinueSendMessage = false;
            for (int i = 0; i < woaarmList.size(); i++) {
                if (stopContinueSendMessage) {
                    continue;
                }
                WechatOfficialAccountAssistantReplyMessage woaarmItem = woaarmList.get(i);
                final OfficialAccountAssistantReplyMessageType messageType = woaarmItem.getMessageType();
                BaseOfficialMessageDto bomDto = new BaseOfficialMessageDto().setTouser(openid).setMsgtype(messageType.getMsgtype());
                //文本消息
                if (OfficialAccountAssistantReplyMessageType.TXT.equals(messageType)) {
                    String chainId = CommonUtil.getUuidReplaceAll();
                    String message = StringUtils.isNotBlank(woaarmItem.getMessage()) ? woaarmItem.getMessage().replaceAll("@\\[CUSTOM_CONCATENATED_PARAMETER\\]", (
                        (UrlUtils.LINK_TYPE_PARAM + "=" + LinkType.WECHAT_OFFICIAL_ACCOUNT_BLUE_URL.getId()) +
                            "&openid=" + (StringUtils.isBlank(openid) ? "" : openid) +
                            "&unionid=" + (StringUtils.isBlank(lpwoauCacheDto.getUnionid()) ? "" : lpwoauCacheDto.getUnionid()) +
                            "&appid=" + (StringUtils.isBlank(lpwoauCacheDto.getUnionid()) ? "" : lpwoauCacheDto.getUnionid()) +
                            "&chainId=" + chainId +
                            "&parentPid=" + matchingPid)) : woaarmItem.getMessage();
                    BaseOfficialTxtMessageDto text = new BaseOfficialTxtMessageDto().setText(new BaseOfficialTxtMessageDto.Content().setContent(message));
                    BeanUtils.copyProperties(bomDto, text);
                    bomDto = text;
                    //传递给微信公众号蓝链，做下一步判断
                    if (StringUtils.isNotBlank(lpwoauCacheDto.getMatchingPid())) {
                        log.info("传递给微信公众号蓝链做下一步判断 wechatAppid={}；openid={}；agentId={}；matchingPid={}；chainId={}；", wechatAppid, openid, agentId, lpwoauCacheDto.getMatchingPid(), chainId);
                        advertiseRedis.setBlueChainInfo(chainId, new BlueChainDTO().setPid(lpwoauCacheDto.getMatchingPid()).setUrl(lpwoauCacheDto.getUrl()).setReferrer(lpwoauCacheDto.getReferrer()));
                    }
                    //图片消息
                } else if (OfficialAccountAssistantReplyMessageType.IMAGE.equals(messageType)) {
                    LandingPageWechatOfficialMaterialFile lpwomf = landingPageWechatOfficialMaterialFileService.getMaterialFileExpireTime(woaarmItem.getMaterialFileId());
                    BaseOfficialMessageDto image = new BaseOfficialImageMessageDto().setImage(new BaseOfficialImageMessageDto.Image().setMedia_id(lpwomf.getMediaId()));
                    BeanUtils.copyProperties(bomDto, image);
                    bomDto = image;
                    //加粉后用户点击回复
                } else if (Objects.equals(OfficialAccountAssistantReplyMessageCategory.USER_CLICK_AFTER_INCREASE_FOLLOWERS_REPLY, woaarmItem.getMessageCategory())) {
                    //加粉成功，再次点击菜单，只发送【加粉后用户点击回复】
                    if (!Objects.isNull(dynamicQrCodeOasmrCacheDto) && Objects.equals(AddEnterpriseWechatStatus.ADDED, dynamicQrCodeOasmrCacheDto.getAddEnterpriseWechatStatus()) && WechatOfficialAccountAssistantRecoverType.NAVIGATION.equals(recoverType)) {
                        //如果未开启，不发，直接无响应
                        if (!YesOrNoEnum.YES.equals(woaar.getUserClickAfterIncreaseFollowersReplyStatus())) {
                            continue;
                        }
                        log.info("1.0、规则①针对已添加企业微信客服的微信公众号粉丝用户如果再次点击公众号菜单如果回复内容配置了加粉后用户点击回复则发送设定的一条文本消息不做发送记录更新 wechatAppid={}；openid={}；", wechatAppid, openid);
                        woaarmItem = woaarmList.stream().filter(e -> Objects.equals(OfficialAccountAssistantReplyMessageCategory.USER_CLICK_AFTER_INCREASE_FOLLOWERS_REPLY, e.getMessageCategory())).collect(Collectors.toList()).stream().findFirst().orElse(null);
                        //如果未配置回复内容，不发，直接无响应
                        if (Objects.isNull(woaarmItem)) {
                            continue;
                        }
                        String chainId = CommonUtil.getUuidReplaceAll();
                        String message = StringUtils.isNotBlank(woaarmItem.getMessage()) ? woaarmItem.getMessage().replaceAll("@\\[CUSTOM_CONCATENATED_PARAMETER\\]", (
                            (UrlUtils.LINK_TYPE_PARAM + "=" + LinkType.WECHAT_OFFICIAL_ACCOUNT_BLUE_URL.getId()) +
                                "&openid=" + (StringUtils.isBlank(openid) ? "" : openid) +
                                "&unionid=" + (StringUtils.isBlank(lpwoauCacheDto.getUnionid()) ? "" : lpwoauCacheDto.getUnionid()) +
                                "&appid=" + (StringUtils.isBlank(lpwoauCacheDto.getUnionid()) ? "" : lpwoauCacheDto.getUnionid()) +
                                "&chainId=" + chainId +
                                "&parentPid=" + matchingPid)) : woaarmItem.getMessage();
                        bomDto.setMsgtype(messageType.getMsgtype());
                        BaseOfficialTxtMessageDto text = new BaseOfficialTxtMessageDto().setText(new BaseOfficialTxtMessageDto.Content().setContent(message));
                        BeanUtils.copyProperties(bomDto, text);
                        bomDto = text;
                        stopContinueSendMessage = true;
                        //传递给微信公众号蓝链，做下一步判断
                        if (StringUtils.isNotBlank(lpwoauCacheDto.getMatchingPid())) {
                            log.info("1.1传递给微信公众号蓝链做下一步判断 wechatAppid={}；openid={}；agentId={}；matchingPid={}；chainId={}；", wechatAppid, openid, agentId, lpwoauCacheDto.getMatchingPid(), chainId);
                            advertiseRedis.setBlueChainInfo(chainId, new BlueChainDTO().setPid(lpwoauCacheDto.getMatchingPid()).setUrl(lpwoauCacheDto.getUrl()).setReferrer(lpwoauCacheDto.getReferrer()));
                        }
                    }
                } else if (OfficialAccountAssistantReplyMessageType.DYNAMIC_QR_CODE.equals(messageType)) {
                    boolean sendMessageIsNull = true;
                    boolean saveSendMessage = true;
                    //取值时间范围为：关注成功后，最近一条记录
                    log.info("2、规则②根据自动回复消息中的客服分组id获取客服分组信息并获取组内条件上线状态为已启用动态渠道二维码公众号内加粉生成状态为已完成全部客服信息 wechatAppid={}；openid={}；", wechatAppid, openid);
                    final Long wechatCustomerServiceGroupId = woaarmItem.getWechatCustomerServiceGroupId();
                    List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList = landingPageWechatCustomerServiceRedis.getWechatServiceDataByGroupId(wechatCustomerServiceGroupId,
                        () -> landingPageWechatCustomerServiceService.getByGroupId(wechatCustomerServiceGroupId));
                    if (CollectionUtils.isEmpty(landingPageWechatCustomerServiceList)) {
                        continue;
                    }
                    landingPageWechatCustomerServiceList = landingPageWechatCustomerServiceList.stream()
                        .filter(e -> Objects.equals(LandingPageWechatCustomerContactStatus.GENERATED.getValue(), e.getOfficialWechatCustomerContactStatus())
                            && StringUtils.equals(wechatAppid, e.getOfficialWechatCustomerContactAppId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(landingPageWechatCustomerServiceList)) {
                        log.info("2.1、微信客服分组无对应已上线二维码数据 wechatAppid={}；openid={}；", wechatAppid, openid);
                        continue;
                    }
                    String state = null;
                    String mediaId = null;
                    Long wechatCustomerServiceId = null;
                    Long advertiserAccountGroupId = null;
                    OfficialWechatCustomerSubjectType subjectType = null;
                    log.info("3.0、规则③从规则②中找出同一用户优先展示固定客服二维码【客服未下线且未切换企微客服分组时】如果条件满足，如果是同主体直接去取同一个客服最新的二维码，如果取不出来，按权重去取新的客服新的二维码；如果是异主体，要判断第一个二维码发送出去长时间，10分钟以内该客服如果没下线，直接发送上一次素材id；10-15分钟以内取这个客服信的二维码素材id发送，并且把老的素材二维码删除掉； wechatAppid={}；openid={}；dynamicQrCodeOasmrCacheDto={}；wechatCustomerServiceGroupId={}；", wechatAppid, openid, JSONObject.toJSONString(dynamicQrCodeOasmrCacheDto), wechatCustomerServiceGroupId);
                    if (WechatOfficialAccountAssistantRecoverType.NAVIGATION.equals(recoverType) && !Objects.isNull(dynamicQrCodeOasmrCacheDto) && !Objects.isNull(wechatCustomerServiceGroupId)) {
                        OfficialAccountSendMessageRecord finalOasmrCacheDto = dynamicQrCodeOasmrCacheDto;
                        LandingPageWechatCustomerServiceRedisDto lpwcsrDto = landingPageWechatCustomerServiceList.stream().filter(e -> Objects.equals(e.getId(), finalOasmrCacheDto.getWechatCustomerServiceId())).collect(Collectors.toList()).stream().findFirst().orElse(null);
                        log.info("3.1校验同一用户是否发送过二维码记录并且过滤出记录中的客服id是否为上线且在分组内如果取不出来按权重去取新的客服新的二维码 wechatAppid={}；openid={}；finalOasmrCacheDto={}；lpwcsrDto={}；", wechatAppid, openid, JSONObject.toJSONString(finalOasmrCacheDto), JSONObject.toJSONString(lpwcsrDto));
                        if (!Objects.isNull(lpwcsrDto)) {
                            //同主体，且客服未下线且未切换企微客服分组，直接去取同一个客服最新的二维码，如果取不出来，按权重去取新的客服新的二维码
                            if (Objects.equals(OfficialWechatCustomerSubjectType.IDENTICAL.getValue(), lpwcsrDto.getOfficialWechatCustomerSubjectType())) {
                                OfficialWechatCustomerContactDto officialWechatCustomerContactDto = officialWechatCustomerContactService.getOneByAgentIdAndCustomerServiceId(wechatAppid, matchingPid, lpwcsrDto.getId());
                                if (!Objects.isNull(officialWechatCustomerContactDto) && !StringUtils.isBlank(officialWechatCustomerContactDto.getMaterialId())) {
                                    //设置参数
                                    wechatCustomerServiceId = lpwcsrDto.getId();
                                    advertiserAccountGroupId = lpwcsrDto.getAdvertiserAccountGroupId();
                                    subjectType = OfficialWechatCustomerSubjectType.IDENTICAL;
                                    state = officialWechatCustomerContactDto.getState();
                                    mediaId = officialWechatCustomerContactDto.getMaterialId();
                                    //组装发送数据
                                    BaseOfficialMessageDto image = new BaseOfficialImageMessageDto().setImage(new BaseOfficialImageMessageDto.Image().setMedia_id(officialWechatCustomerContactDto.getMaterialId()));
                                    BeanUtils.copyProperties(bomDto, image);
                                    bomDto = image;
                                    sendMessageIsNull = false;
                                    //微信公众号粉丝，关注未取关再次点击菜单未加粉成功，且本次发送的主体与上一次发送的主体不一致，发新码删除旧码
                                    if (!subjectType.equals(dynamicQrCodeOasmrCacheDto.getSubjectType())) {
                                        log.info("3.2微信公众号粉丝，关注未取关再次点击菜单未加粉成功，且本次发送的主体与上一次发送的主体不一致，发新码删除旧码 wechatAppid={}；openid={}；mediaId={}；", wechatAppid, openid, dynamicQrCodeOasmrCacheDto.getMediaId());
                                        officialWechatCustomerContactService.deleteCustomerContactByMaterialId(dynamicQrCodeOasmrCacheDto.getMediaId());
                                    }
                                }
                            }
                            //异主体，要判断第一个二维码发送出去多长时间，10分钟以内该客服如果没下线且在分组内，直接根据上一次记录的素材id发送旧的二维码图片；10-15分钟以内去取上次记录中这个客服id对应的新的渠道二维码图片发送，并且把老的素材二维码删除掉；
                            if (Objects.equals(OfficialWechatCustomerSubjectType.DISSIMILARITY.getValue(), lpwcsrDto.getOfficialWechatCustomerSubjectType())) {
                                long timeArea = Instant.now().getEpochSecond() - dynamicQrCodeOasmrCacheDto.getCreatedAt().getEpochSecond();
                                if (timeArea <= agentConf.getWxGzhQRTimeRange()) {
                                    log.info("8.0、异主体执行逻辑1从第一条活码自动回复规则发出后10分钟内如果公众号粉丝再次点击公众号菜单再次发送相同二维码 wechatAppid={}；openid={}；", wechatAppid, openid);
                                    //设置参数
                                    wechatCustomerServiceId = lpwcsrDto.getId();
                                    advertiserAccountGroupId = lpwcsrDto.getAdvertiserAccountGroupId();
                                    subjectType = OfficialWechatCustomerSubjectType.DISSIMILARITY;
                                    state = dynamicQrCodeOasmrCacheDto.getState();
                                    mediaId = dynamicQrCodeOasmrCacheDto.getMediaId();
                                    //组装发送数据
                                    BaseOfficialMessageDto image = new BaseOfficialImageMessageDto().setImage(new BaseOfficialImageMessageDto.Image().setMedia_id(dynamicQrCodeOasmrCacheDto.getMediaId()));
                                    BeanUtils.copyProperties(bomDto, image);
                                    bomDto = image;
                                    saveSendMessage = false;
                                    sendMessageIsNull = false;
                                    //单独保存发送记录：将第一次异主体二维码发送时间复制到发送记录创建时间，主要是为了兼容二维码发送数统计
                                    landingPageSender.sendOfficialAccountSendQrCodeMessage(new OfficialAccountQrCodeRecordDTO().setMessageType(OfficialAccountAssistantReplyMessageType.DYNAMIC_QR_CODE)
                                        .setAdvertiserAccountGroupId(advertiserAccountGroupId).setWechatCustomerServiceId(wechatCustomerServiceId).setWechatCustomerServiceGroupId(wechatCustomerServiceGroupId)
                                        .setAppId(wechatAppid).setOpenId(openid).setUnionId(lpwoauCacheDto.getUnionid())
                                        .setPid(matchingPid).setState(state).setSubjectType(subjectType).setCreatedAt(dynamicQrCodeOasmrCacheDto.getCreatedAt()).setMediaId(mediaId)
                                        .setOfficialReplyId(woaar.getId()).setOfficialReplyMessageId(woaarmItem.getId())
                                    );
                                } else {
                                    OfficialWechatCustomerContactDto officialWechatCustomerContactDto = officialWechatCustomerContactService.getOneByAgentIdAndCustomerServiceId(wechatAppid, matchingPid, lpwcsrDto.getId());
                                    if (!Objects.isNull(officialWechatCustomerContactDto) && !StringUtils.isBlank(officialWechatCustomerContactDto.getMaterialId())) {
                                        //设置参数
                                        wechatCustomerServiceId = lpwcsrDto.getId();
                                        advertiserAccountGroupId = lpwcsrDto.getAdvertiserAccountGroupId();
                                        subjectType = OfficialWechatCustomerSubjectType.DISSIMILARITY;
                                        state = officialWechatCustomerContactDto.getState();
                                        mediaId = officialWechatCustomerContactDto.getMaterialId();
                                        //组装发送数据
                                        BaseOfficialMessageDto image = new BaseOfficialImageMessageDto().setImage(new BaseOfficialImageMessageDto.Image().setMedia_id(officialWechatCustomerContactDto.getMaterialId()));
                                        BeanUtils.copyProperties(bomDto, image);
                                        bomDto = image;
                                        sendMessageIsNull = false;
                                        //发新码删除旧码
                                        log.info("8.1异主体发新码删除旧码 wechatAppid={}；openid={}；mediaId={}；", wechatAppid, openid, dynamicQrCodeOasmrCacheDto.getMediaId());
                                        officialWechatCustomerContactService.deleteCustomerContactByMaterialId(dynamicQrCodeOasmrCacheDto.getMediaId());
                                    }
                                }
                            }
                        }
                    }
                    if (sendMessageIsNull) {
                        log.info("4、规则④如果规则③条件不满足则执行此规则以客服分组为维度进行二维码展示数统计此处获取统计数时通过redis函数直接累加有效期与微信客服二维码展示接口一致并获取展示数结合微信客服权重值进行计算得到对应权重的客服信息 wechatAppid={}；openid={}；", wechatAppid, openid);
                        LandingPageWechatCustomerServiceRedisDto lpwcsrDto = landingPageWechatCustomerServiceService.getWechatCustomerServiceByWeight(wechatCustomerServiceGroupId, landingPageWechatCustomerServiceList);
                        if (!Objects.isNull(lpwcsrDto)) {
                            log.info("6.0、如果规则④中有结果并且是同一主体记录日志直接发送消息跳过后续逻辑 wechatAppid={}；openid={}；", wechatAppid, openid);
                            OfficialWechatCustomerContactDto officialWechatCustomerContactDto = officialWechatCustomerContactService.getOneByAgentIdAndCustomerServiceId(wechatAppid, matchingPid, lpwcsrDto.getId());
                            if (!Objects.isNull(officialWechatCustomerContactDto) && !StringUtils.isBlank(officialWechatCustomerContactDto.getMaterialId())) {
                                //设置参数
                                wechatCustomerServiceId = lpwcsrDto.getId();
                                advertiserAccountGroupId = lpwcsrDto.getAdvertiserAccountGroupId();
                                subjectType = OfficialWechatCustomerSubjectType.getEnumByValue(lpwcsrDto.getOfficialWechatCustomerSubjectType());
                                state = officialWechatCustomerContactDto.getState();
                                mediaId = officialWechatCustomerContactDto.getMaterialId();
                                //组装发送数据
                                BaseOfficialMessageDto image = new BaseOfficialImageMessageDto().setImage(new BaseOfficialImageMessageDto.Image().setMedia_id(officialWechatCustomerContactDto.getMaterialId()));
                                BeanUtils.copyProperties(bomDto, image);
                                bomDto = image;
                            } else {
                                log.info("6.1通过客服id查询公众号联系我二维码获取不到数据操作中断 wechatAppid={}；openid={}；", wechatAppid, openid);
                                continue;
                            }
                        } else {
                            log.info("5、如果规则④结果为空记录日志终止发送消息跳过后续逻辑 wechatAppid={}；openid={}；", wechatAppid, openid);
                            continue;
                        }
                    }
                    if (saveSendMessage) {
                        landingPageSender.sendOfficialAccountSendQrCodeMessage(new OfficialAccountQrCodeRecordDTO().setMessageType(OfficialAccountAssistantReplyMessageType.DYNAMIC_QR_CODE)
                            .setAdvertiserAccountGroupId(advertiserAccountGroupId).setWechatCustomerServiceId(wechatCustomerServiceId).setWechatCustomerServiceGroupId(wechatCustomerServiceGroupId)
                            .setAppId(wechatAppid).setOpenId(openid).setUnionId(lpwoauCacheDto.getUnionid())
                            .setPid(matchingPid).setState(state).setSubjectType(subjectType).setCreatedAt(DateTimeUtil.long2Instant(Instant.now().toEpochMilli() + 500)).setMediaId(mediaId)
                            .setOfficialReplyId(woaar.getId()).setOfficialReplyMessageId(woaarmItem.getId())
                        );
                    }
                    log.info("12、处理完以上逻辑将发送记录进行入库并存入缓存有效期2小时入库表为发送公众号消息记录表用于校验是否统一访客发送固定二维码 wechatAppid={}；openid={}；", wechatAppid, openid);
                } else if (OfficialAccountAssistantReplyMessageType.GROUP_DYNAMIC_QR_CODE.equals(messageType)) {
                    log.info("13.0.1、进入公众号助手发送活码分组动态渠道二维码 wechatAppid={}；openid={}；", wechatAppid, openid);
                    final Long wechatCustomerServiceGroupId = woaarmItem.getWechatCustomerServiceGroupId();
                    //获取发送记录对应的二维码配置信息
                    OfficialWechatCustomerServiceGroupCacheDto owcsgcDto = officialWechatCustomerServiceGroupService.getOfficialWechatCustomerServiceGroupCacheById(wechatAppid, wechatCustomerServiceGroupId);
                    if (Objects.isNull(owcsgcDto)) {
                        log.info("14、分组动态渠道二维码信息不存在 wechatAppid={}；openid={}；", wechatAppid, openid);
                        continue;
                    }
                    if (!StringUtils.equals(wechatAppid, owcsgcDto.getAppId())) {
                        log.info("15、回调公众号appid与分组动态渠道二维码appid不匹配操作终止 wechatAppid={}；openid={}；", wechatAppid, openid);
                        continue;
                    }
                    log.info("13.0.2 开始执行组装数据 wechatAppid={}；openid={}；agentId={}；wechatAppid={}；wechatCustomerServiceGroupId={}；matchingPid={}；owcsgcDto={}", wechatAppid, openid, agentId, wechatAppid, wechatCustomerServiceGroupId, matchingPid, JSONObject.toJSONString(owcsgcDto));
                    if (Objects.equals(1, owcsgcDto.getExplosionGroupFlag())) {
                        //开启防炸群按钮，查询【分组动态渠道二维码】【查询条件：这个公众号+二维码变更时间内+分组动态渠道二维码类型+分组动态渠道二维码id】二维码发送记录
                        OfficialAccountSendMessageRecord gdqcoCacheDto = officialAccountSendMessageRecordRedis.getSendMessageRecordGroupDynamicQrCode(wechatAppid, OfficialAccountAssistantReplyMessageType.GROUP_DYNAMIC_QR_CODE, wechatCustomerServiceGroupId);
                        if (!Objects.isNull(gdqcoCacheDto)) {
                            log.info("16、二维码变更时间：以首次发送时间为计时点击设定时间内发送同一二维码到达设定时间发送新二维码 wechatAppid={}；openid={}；", wechatAppid, openid);
                            //组装发送数据
                            BaseOfficialMessageDto image = new BaseOfficialImageMessageDto().setImage(new BaseOfficialImageMessageDto.Image().setMedia_id(gdqcoCacheDto.getMediaId()));
                            BeanUtils.copyProperties(bomDto, image);
                            bomDto = image;
                            landingPageSender.sendOfficialAccountSendQrCodeMessage(new OfficialAccountQrCodeRecordDTO().setMessageType(OfficialAccountAssistantReplyMessageType.GROUP_DYNAMIC_QR_CODE)
                                .setAdvertiserAccountGroupId(gdqcoCacheDto.getAdvertiserAccountGroupId()).setWechatCustomerServiceId(-1L).setWechatCustomerServiceGroupId(wechatCustomerServiceGroupId)
                                .setAppId(wechatAppid).setOpenId(openid).setUnionId(lpwoauCacheDto.getUnionid())
                                .setPid(matchingPid).setState(gdqcoCacheDto.getState()).setSubjectType(gdqcoCacheDto.getSubjectType())
                                .setCreatedAt(DateTimeUtil.long2Instant(Instant.now().toEpochMilli() + 500)).setMediaId(gdqcoCacheDto.getMediaId())
                                .setOfficialReplyId(woaar.getId()).setOfficialReplyMessageId(woaarmItem.getId())
                            );
                            log.info("17、处理完以上逻辑将发送记录进行入库并存入缓存有效期2小时入库表为发送公众号消息记录表用于校验是否统一访客发送固定二维码 wechatAppid={}；openid={}；", wechatAppid, openid);
                        } else {
                            log.info("18.0.1、开启防炸群开关首次发送分组动态渠道二维码 wechatAppid={}；openid={}；", wechatAppid, openid);
                            //获取新的【分组动态渠道二维码】数据，取出对应的素材id
                            OfficialWechatGroupContactDto owgcDto = officialWechatCustomerServiceGroupService.getOneByPidAndAppidAndServiceGroupId(agentId, wechatAppid, wechatCustomerServiceGroupId, matchingPid);
                            if (!Objects.isNull(owgcDto) && !StringUtils.isBlank(owgcDto.getMaterialId())) {
                                //组装发送数据
                                BaseOfficialMessageDto image = new BaseOfficialImageMessageDto().setImage(new BaseOfficialImageMessageDto.Image().setMedia_id(owgcDto.getMaterialId()));
                                BeanUtils.copyProperties(bomDto, image);
                                bomDto = image;
                                landingPageSender.sendOfficialAccountSendQrCodeMessage(new OfficialAccountQrCodeRecordDTO().setMessageType(OfficialAccountAssistantReplyMessageType.GROUP_DYNAMIC_QR_CODE)
                                    .setAdvertiserAccountGroupId(owgcDto.getAdvertiserAccountGroupId()).setWechatCustomerServiceId(-1L).setWechatCustomerServiceGroupId(wechatCustomerServiceGroupId)
                                    .setAppId(wechatAppid).setOpenId(openid).setUnionId(lpwoauCacheDto.getUnionid())
                                    .setPid(matchingPid).setState(owgcDto.getState()).setSubjectType(OfficialWechatCustomerSubjectType.getEnumByValue(owgcDto.getOfficialWechatCustomerSubjectType()))
                                    .setCreatedAt(DateTimeUtil.long2Instant(Instant.now().toEpochMilli() + 500)).setMediaId(owgcDto.getMaterialId())
                                    .setCodeChangeTimeNum(owcsgcDto.getCodeChange())
                                    .setOfficialReplyId(woaar.getId()).setOfficialReplyMessageId(woaarmItem.getId())
                                );
                                log.info("18.0.3、发送延时删除分组动态渠道二维码延时队列延时时间为设定配置中设定的二维码有效期 wechatAppid={}；openid={}；wechatCustomerServiceGroupId={}；matchingPid={}；owgcDto={}；", wechatAppid, openid, wechatCustomerServiceGroupId, matchingPid, JSONObject.toJSONString(owgcDto));
                                //发送延时删除【分组动态渠道二维码】延时队列，延时时间为设定配置中设定的【二维码有效期】
                                multiplayerCodeSender.deleteGroupDynamicQrCode(wechatAppid, openid, owgcDto.getMaterialId(), owgcDto.getConfigId(), (owgcDto.getCodeValidity() * 60 * 1000));
                            } else {
                                log.info("18.0.4、分组动态渠道二维码二维码获取不到数据操作中断 wechatAppid={}；openid={}；wechatCustomerServiceGroupId={}；matchingPid={}；", wechatAppid, openid, wechatCustomerServiceGroupId, matchingPid);
                                continue;
                            }
                        }
                    } else {
                        //未启防炸群按钮，查询【分组动态渠道二维码】【查询条件：这个公众号+二维码30分钟内+分组动态渠道二维码类型+分组动态渠道二维码id】二维码发送记录
                        OfficialAccountSendMessageRecord gdqcoCacheDto = officialAccountSendMessageRecordRedis.getSendMessageRecordGroupDynamicQrCode(wechatAppid, OfficialAccountAssistantReplyMessageType.GROUP_DYNAMIC_QR_CODE, wechatCustomerServiceGroupId);
                        if (!Objects.isNull(gdqcoCacheDto)) {
                            log.info("19、二维码变更时间：以首次发送时间为计时点击设定时间内发送同一二维码到达设定时间发送新二维码 wechatAppid={}；openid={}；", wechatAppid, openid);
                            //组装发送数据
                            BaseOfficialMessageDto image = new BaseOfficialImageMessageDto().setImage(new BaseOfficialImageMessageDto.Image().setMedia_id(gdqcoCacheDto.getMediaId()));
                            BeanUtils.copyProperties(bomDto, image);
                            bomDto = image;
                            landingPageSender.sendOfficialAccountSendQrCodeMessage(new OfficialAccountQrCodeRecordDTO().setMessageType(OfficialAccountAssistantReplyMessageType.GROUP_DYNAMIC_QR_CODE)
                                .setAdvertiserAccountGroupId(gdqcoCacheDto.getAdvertiserAccountGroupId()).setWechatCustomerServiceId(-1L).setWechatCustomerServiceGroupId(wechatCustomerServiceGroupId)
                                .setAppId(wechatAppid).setOpenId(openid).setUnionId(lpwoauCacheDto.getUnionid())
                                .setPid(matchingPid).setState(gdqcoCacheDto.getState()).setSubjectType(gdqcoCacheDto.getSubjectType())
                                .setCreatedAt(DateTimeUtil.long2Instant(Instant.now().toEpochMilli() + 500)).setMediaId(gdqcoCacheDto.getMediaId())
                                .setOfficialReplyId(woaar.getId()).setOfficialReplyMessageId(woaarmItem.getId())
                            );
                            log.info("20、处理完以上逻辑将发送记录进行入库并存入缓存有效期2小时入库表为发送公众号消息记录表用于校验是否统一访客发送固定二维码 wechatAppid={}；openid={}；", wechatAppid, openid);
                        } else {
                            log.info("21.0.1、获取新的【分组动态渠道二维码】数据，取出对应的素材id wechatAppid={}；openid={}；", wechatAppid, openid);
                            OfficialWechatGroupContactDto owgcDto = officialWechatCustomerServiceGroupService.getOneByPidAndAppidAndServiceGroupId(agentId, wechatAppid, wechatCustomerServiceGroupId, matchingPid);
                            if (!Objects.isNull(owgcDto) && !StringUtils.isBlank(owgcDto.getMaterialId())) {
                                //组装发送数据
                                BaseOfficialMessageDto image = new BaseOfficialImageMessageDto().setImage(new BaseOfficialImageMessageDto.Image().setMedia_id(owgcDto.getMaterialId()));
                                BeanUtils.copyProperties(bomDto, image);
                                bomDto = image;
                                landingPageSender.sendOfficialAccountSendQrCodeMessage(new OfficialAccountQrCodeRecordDTO().setMessageType(OfficialAccountAssistantReplyMessageType.GROUP_DYNAMIC_QR_CODE)
                                    .setAdvertiserAccountGroupId(owgcDto.getAdvertiserAccountGroupId()).setWechatCustomerServiceId(-1L).setWechatCustomerServiceGroupId(wechatCustomerServiceGroupId)
                                    .setAppId(wechatAppid).setOpenId(openid).setUnionId(lpwoauCacheDto.getUnionid())
                                    .setPid(matchingPid).setState(owgcDto.getState()).setSubjectType(OfficialWechatCustomerSubjectType.getEnumByValue(owgcDto.getOfficialWechatCustomerSubjectType()))
                                    .setCreatedAt(DateTimeUtil.long2Instant(Instant.now().toEpochMilli() + 500)).setMediaId(owgcDto.getMaterialId())
                                    .setCodeChangeTimeNum(30)
                                    .setOfficialReplyId(woaar.getId()).setOfficialReplyMessageId(woaarmItem.getId())
                                );
                                //发送延时删除【分组动态渠道二维码】延时队列，延时时间为固定时间30分钟
                                multiplayerCodeSender.deleteGroupDynamicQrCode(wechatAppid, openid, owgcDto.getMaterialId(), owgcDto.getConfigId(), (30 * 60 * 1000));
                            } else {
                                log.info("21分组动态渠道二维码二维码获取不到数据操作中断 wechatAppid={}；openid={}；", wechatAppid, openid);
                                continue;
                            }
                        }
                    }
                }
                //延时队列发送
                if (!Objects.isNull(bomDto) && OfficialAccountAssistantReplyDelayedPushEnum.ENABLE.equals(woaar.getDelayedPush()) && !Objects.isNull(woaarmItem.getDelayTime())) {
                    landingPageSender.sendDelayMessageToWechatOfficalAccount(wechatAppid, bomDto, ((delayTime += woaarmItem.getDelayTime()) * 1000));
                    //非延时队列发送
                } else if (!Objects.isNull(bomDto)) {
                    //如果是第一条消息 || 如果是媒体文件，立即发送；如果不是媒体文件，顺延
                    //landingPageSender.sendDelayMessageToWechatOfficalAccount(wechatAppid, bomDto, (OfficialAccountAssistantReplyMessageType.IMAGE.toString().equals(bomDto.getMsgtype()) ? 0 : (i * agentConf.getSendMaxWechatOfficialMessageDelayTime())));
                    landingPageSender.sendDelayMessageToWechatOfficalAccount(wechatAppid, bomDto, 0);
                }
            }
        }
        log.info("公众号-处理回调消息-回复公众号消息结束 wechatAppid={}；agentId={}；openid={}；", wechatAppid, agentId, openid);
    }

    /**
     * 关注公众成功回调处理 / 成功关注公众号回调 / 公众号关注成功回调
     */
    public void wechatOfficalAccountSubscribe(final WechatOfficialAccountAssistantRecoverType recoverType,
                                              final String wechatAppid,
                                              final String agentId,
                                              final LandingPageWechatOfficialAccountUserCacheDto lpwoauCacheDto,
                                              final WxMpXmlMessage wxMessage,
                                              final LandingPageWechatOfficialAccountCacheDto officialAccountDto,
                                              final Instant followTime
    ) {
        final String openid = lpwoauCacheDto.getOpenid();
        final String unionid = lpwoauCacheDto.getUnionid();
        final String externalUserid = lpwoauCacheDto.getExternalUserid();
        SubscribeSceneType subscribeSceneType = SubscribeSceneType.getEnumById(lpwoauCacheDto.getSubscribeScene());
        String qr_scene_str = lpwoauCacheDto.getQrSceneStr();
        String pid = null;
        if (StringUtils.isNotBlank(qr_scene_str)) {
            subscribeSceneType = Objects.equals(SubscribeSceneType.ADD_SCENE_QR_CODE, subscribeSceneType) ? SubscribeSceneType.ADD_SCENE_QR_CODE_CARRY_PARAM : subscribeSceneType;
            try {
                JSONObject qrJson = JSON.parseObject(qr_scene_str);
                pid = qrJson.getString("ExtInfo");
            } catch (Exception e) {
                log.warn("公众号-处理回调消息-解析qr_scene_str参数异常，qr_scene_str：{}", qr_scene_str, e);
            }
        } else {
            subscribeSceneType = Objects.equals(SubscribeSceneType.ADD_SCENE_QR_CODE, subscribeSceneType) ? SubscribeSceneType.ADD_SCENE_QR_CODE_NO_PARAM : subscribeSceneType;
        }
        if (Arrays.asList(SubscribeSceneType.ADD_SCENE_QR_CODE_CARRY_PARAM, SubscribeSceneType.ADD_SCENE_QR_CODE_NO_PARAM).contains(subscribeSceneType)) {
            //重新定义公众号关注来源
            landingPageWechatOfficialAccountUserService.update(new LambdaUpdateWrapper<LandingPageWechatOfficialAccountUser>()
                .eq(LandingPageWechatOfficialAccountUser::getId, lpwoauCacheDto.getId()).set(LandingPageWechatOfficialAccountUser::getSubscribeScene, subscribeSceneType)
            );
            wechatOfficialAccountRedis.clearWechatOfficialAccountUserInfo(wechatAppid, openid);
            lpwoauCacheDto.setSubscribeScene(!Objects.isNull(subscribeSceneType) ? subscribeSceneType.getId() : null);
        }
        TenantContextHolder.set(agentId);
        if (StringUtils.isNotBlank(pid)) {
            //匹配成功，获取pid，进行第一次上报请求
            if (pageViewInfoRedis.hasTasks(pid)) {
                log.info("微信公众号关注回调事件判断是否存在正在执行队列 ======>> agentId={}；wechatAppid={}；openid={}；unionid={}；pid={}；qr_scene_str={}", agentId, wechatAppid, openid, unionid, pid, qr_scene_str);
                //修复bug：#32472 【线上问题】关注公众号出现回调两次导致上报重复 https://ones.yiye.ai/project/#/team/WtsduTeT/task/XxxHuwKet9bM4OIV
                sendWechatOfficalAccountMessage(recoverType, wechatAppid, agentId, lpwoauCacheDto, wxMessage);
                return;
            }
            //上报【关注公众号】
            uploadSender.sendFollowOfficialAccount(new UploadDto().setPid(pid).setSubscribeScene(subscribeSceneType));
            log.info("公众号-处理回调消息-qr_scene_str链路-上报【关注公众号】，执行成功 ======>> agentId={}；wechatAppid={}；openid={}；unionid={}；pid={}；qr_scene_str={}", agentId, wechatAppid, openid, unionid, pid, qr_scene_str);
            // 查询pv 更新【关注公众号】状态
            pageViewSender.sendUpdateFollowOfficialAccountStatus(new PageViewMqDto().setPid(pid).setFollowOfficialAccountAppId(wechatAppid).setWechatOpenid(openid).setWechatUnionid(unionid).setUpdateFollowOfficialAccount(true));
            log.info("公众号-处理回调消息-qr_scene_str链路-更新【关注公众号】状态，执行成功 ======>> agentId={}；wechatAppid={}；openid={}；unionid={}；pid={}；qr_scene_str={}", agentId, wechatAppid, openid, unionid, pid, qr_scene_str);
//            PageViewInfo pageViewInfo = pageViewInfoPgService.getByPid(pid);
            PageViewInfo pageViewInfo = pageViewInfoPgService.lambdaQuery()
                .select(PageViewInfo::getPid, PageViewInfo::getLinkType, PageViewInfo::getOriginRobotId)
                .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoQueryTime()))
                .eq(PageViewInfo::getPid,pid)
                .orderByDesc(PageViewInfo::getCreatedAt)
                .last(" limit 1").one();

            if (Objects.nonNull(pageViewInfo) && LinkType.WECHAT_CUSTOMER_ROBOT_SERVICE.equals(pageViewInfo.getLinkType())) {
                enterpriseWechatRobotCustomerService.statisticWechatCustomerServiceRobotData(
                    pageViewInfo.getOriginRobotId(),
                    EnterpriseWechatRobotCustomerDataStatisticsField.FOLLOWING_OFFICIAL_ACCOUNT_COUNT,
                    null
                );
            }
        } else {
            PageViewInfo pageViewInfo = pageViewInfoRedis.matchingOfficialAccountPvInfoByOpenidOrUnionid(agentId, wechatAppid, openid, unionid, externalUserid);
            if (Objects.isNull(pageViewInfo) || StringUtils.isBlank(pageViewInfo.getPid())) {
                log.info("公众号-处理回调消息-pv曝光缓存数据不存在，操作终止 ======>> agentId={}；wechatAppid={}；openid={}；unionid={}；pageViewInfo={}", agentId, wechatAppid, openid, unionid, pageViewInfo);
                sendWechatOfficalAccountMessage(recoverType, wechatAppid, agentId, lpwoauCacheDto, wxMessage);
                return;
            }
            if (StringUtils.isBlank(pageViewInfo.getFollowOfficialAccountAppId()) || !wechatAppid.equals(pageViewInfo.getFollowOfficialAccountAppId())) {
                log.info("通过微信公众号关注回调消息回传的appid与redis缓存中的appid不匹配，匹配上报操作终止 ======>> agentId={}；wechatAppid={}；openid={}；unionid={}；pageViewInfo={}", agentId, wechatAppid, openid, unionid, pageViewInfo);
                sendWechatOfficalAccountMessage(recoverType, wechatAppid, agentId, lpwoauCacheDto, wxMessage);
                return;
            }
            //匹配成功，获取pid，进行第一次上报请求
            pid = pageViewInfo.getPid();
            //删除【关注公众号】pv曝光，缓存数据
            pageViewInfoRedis.deleteOfficialAccountPvInfoByOpenidOrUnionid(agentId, wechatAppid, openid, unionid, externalUserid);
            log.info("公众号-处理回调消息-匹配逻辑，执行成功 ======>> agentId={}；wechatAppid={}；openid={}；unionid={}；pageViewInfo={}", agentId, wechatAppid, openid, unionid, pageViewInfo);
            //上报【关注公众号】
            uploadSender.sendFollowOfficialAccount(new UploadDto().setPid(pid).setSubscribeScene(subscribeSceneType));
            log.info("公众号-处理回调消息-上报【关注公众号】，执行成功 ======>> agentId={}；wechatAppid={}；openid={}；unionid={}；pageViewInfo={}", agentId, wechatAppid, openid, unionid, pageViewInfo);
            // 查询pv 更新【关注公众号】状态
            pageViewSender.sendUpdateFollowOfficialAccountStatus(new PageViewMqDto().setPid(pid).setFollowOfficialAccountAppId(wechatAppid).setWechatOpenid(openid).setWechatUnionid(unionid));
            log.info("公众号-处理回调消息-更新【关注公众号】状态，执行成功 ======>> agentId={}；wechatAppid={}；openid={}；unionid={}；pageViewInfo={}", agentId, wechatAppid, openid, unionid, pageViewInfo);
            if (YesOrNoEnum.YES.equals(pageViewInfo.getIntoWechatCustomerServiceSessionStatus())) {
                enterpriseWechatRobotCustomerService.statisticWechatCustomerServiceRobotData(
                    pageViewInfo.getWechatCustomerServiceRobotId(),
                    EnterpriseWechatRobotCustomerDataStatisticsField.FOLLOWING_OFFICIAL_ACCOUNT_COUNT,
                    null
                );
            }
            if (LinkType.WECHAT_CUSTOMER_ROBOT_SERVICE.equals(pageViewInfo.getLinkType())) {
                enterpriseWechatRobotCustomerService.statisticWechatCustomerServiceRobotData(
                    pageViewInfo.getOriginRobotId(),
                    EnterpriseWechatRobotCustomerDataStatisticsField.FOLLOWING_OFFICIAL_ACCOUNT_COUNT,
                    null
                );
            }
        }
        if (StringUtils.isNotBlank(pid)) {
            TenantContextHolder.set(agentId);
            PageViewInfo pvInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoQueryTime()))
                .eq(PageViewInfo::getPid, pid).orderByDesc(PageViewInfo::getCreatedAt).last(" limit 1"));
            log.info("公众号处理回调消息匹配广告来源1.0.0.1 agentId={}；wechatAppid={}；openid={}；unionid={}；pid={}；pvInfo={}；tenantContextHolder={}；", agentId, wechatAppid, openid, unionid, pid, JSONObject.toJSONString(pvInfo), TenantContextHolder.get());
            if (!Objects.isNull(pvInfo)) {
                final Platform platform = Platform.getEnumById(pvInfo.getPlatformId());
                //公众号助手-广告来源
                final OfficialFollowSource officialFollowSource = getOfficialFollowSource(wechatAppid, pvInfo, platform, openid, subscribeSceneType);
                log.info("公众号处理回调消息匹配广告来源1.0.0.2 agentId={}；wechatAppid={}；openid={}；unionid={}；pid={}；officialFollowSource={}；", agentId, wechatAppid, openid, unionid, pid, officialFollowSource);
                //公众号助手-广告来源-对应值
                FollowSourcePlatform followSourcePlatform;
                if (Arrays.asList(Platform.OCEAN_ENGINE, Platform.KUAISHOU).contains(platform)) {
                    followSourcePlatform = FollowSourcePlatform.getFollowSourcePlatformByflowSource(pvInfo.getFlowSource());
                } else {
                    followSourcePlatform = FollowSourcePlatform.getFollowSourcePlatformByPlatform(platform);
                }
                //公众号助手-广告来源-巨量广告-广告投放位置（获取上一个页面）
                OceanEngineType oceanEngineType = OceanEngineType.OCEAN_MSG;
                String csite = UrlUtils.getParamsByColumn(UrlUtils.getPath(pvInfo.getUrl()), UrlUtils.OCEAN_ENGINE_URL_PARAM_CSITE);
                if (StringUtils.isNotBlank(csite) && "38016".equals(csite)) {
                    //长按关注的是：关注页
                    oceanEngineType = OceanEngineType.OCEAN_SEARCH;
                } else if (StringUtils.isBlank(csite) && StringUtils.isNotBlank(pvInfo.getFirstPagePid()) && !StringUtils.equals(pvInfo.getPid(), pvInfo.getFirstPagePid())) {
                    //长按关注的是：一跳页 或 中间页
                    PageViewInfo firstPvInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>().select(PageViewInfo::getUrl)
                        .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoQueryTime()))
                        .eq(PageViewInfo::getPid, pvInfo.getFirstPagePid())
                        .orderByAsc(PageViewInfo::getCreatedAt).last(" limit 1"));
                    if (!Objects.isNull(firstPvInfo) && "38016".equals(UrlUtils.getParamsByColumn(UrlUtils.getPath(firstPvInfo.getUrl()), UrlUtils.OCEAN_ENGINE_URL_PARAM_CSITE))) {
                        oceanEngineType = OceanEngineType.OCEAN_SEARCH;
                    }
                }
                //修改公众号粉丝信息为广告来源
                LandingPageWechatOfficialAccountUser lpwoau = new LandingPageWechatOfficialAccountUser()
                    .setMatchingPid(pid)
                    .setAdvertiserAccountGroupId(pvInfo.getAdvertiserAccountGroupId())
                    .setUid(pvInfo.getUid())
                    .setLandingPageId(pvInfo.getLandingPageId())
                    .setChannelId(pvInfo.getChannelId())
                    .setClickId(pvInfo.getClickId())
                    .setPlatform(platform)
                    .setUrl(pvInfo.getUrl())
                    .setReferrer(pvInfo.getReferrer())
                    .setOfficialFollowSource(officialFollowSource)
                    .setFollowSourcePlatform(Objects.isNull(followSourcePlatform) ? null : followSourcePlatform.getCode())
                    .setSubscribeScene(subscribeSceneType)
                    .setOceanEngineType(oceanEngineType)
                    .setAgentId(agentId)
                    .setDeleteStatus(DeleteStatus.NORMAL);
                landingPageWechatOfficialAccountUserService.updateColumnById(lpwoauCacheDto.getId(), lpwoau);
                wechatOfficialAccountRedis.clearWechatOfficialAccountUserInfo(wechatAppid, openid);
                lpwoauCacheDto.setMatchingPid(pid)
                    .setUid(pvInfo.getUid())
                    .setAgentId(lpwoauCacheDto.getAgentId())
                    .setAdvertiserAccountGroupId(pvInfo.getAdvertiserAccountGroupId())
                    .setLandingPageId(pvInfo.getLandingPageId())
                    .setChannelId(pvInfo.getChannelId())
                    .setClickId(pvInfo.getClickId())
                    .setPlatform(Objects.isNull(platform) ? null : platform.getId())
                    .setUrl(pvInfo.getUrl())
                    .setReferrer(pvInfo.getReferrer())
                    .setOfficialFollowSource(Objects.isNull(officialFollowSource) ? OfficialFollowSource.NON_ADVERTISING.getValue() : officialFollowSource.getValue())
                    .setFollowSourcePlatform(Objects.isNull(followSourcePlatform) ? null : followSourcePlatform.getCode())
                    .setSubscribeScene(!Objects.isNull(subscribeSceneType) ? subscribeSceneType.getId() : null)
                    .setOceanEngineType(oceanEngineType.getId())
                    .setAgentId(agentId)
                ;
                //新增【公众号粉丝】信息
                LandingPageWechatOfficialAccountCustomer lpwoac = new LandingPageWechatOfficialAccountCustomer()
                    .setAppId(wechatAppid)
                    .setNickName(officialAccountDto.getNickName())
                    .setMatchingPid(lpwoau.getMatchingPid())
                    .setFollowTime(followTime)
                    .setFollowStatus(FollowStatus.FOLLOW)
                    .setUnFollowTime(null)
                    .setSubscribeScene(lpwoau.getSubscribeScene())
                    .setOfficialFollowSource(lpwoau.getOfficialFollowSource())
                    .setFollowSourcePlatform(followSourcePlatform)
                    .setOpenid(openid)
                    .setUnionid(unionid)
                    .setAdvertiserAccountGroupId(pvInfo.getAdvertiserAccountGroupId())
                    .setFollowLandingPageId(pvInfo.getLandingPageId())
                    .setFollowChannelId(pvInfo.getChannelId())
                    .setFollowLandingPageUrl(pvInfo.getUrl())
                    .setCreatedAt(followTime)
                    .setPlatformId(pvInfo.getPlatformId())
                    .setUpdatedAt(followTime);
                CustomerAdParamUtil.setAdParam(lpwoac, pvInfo.getUrl(), pvInfo.getReferrer());
                try {
                    customerSender.sendAddWechatOfficialCustoer(lpwoac);
                } catch (Exception ex) {
                    log.info("发送新增关注公众号粉丝记录队列异常 lpwoac={}；", JSONObject.toJSONString(lpwoac), ex);
                }
            }
        }
        sendWechatOfficalAccountMessage(recoverType, wechatAppid, agentId, lpwoauCacheDto, wxMessage);
    }

    public OfficialFollowSource getOfficialFollowSource(String wechatAppid, PageViewInfo pvInfo, Platform platform, String openid, SubscribeSceneType subscribeSceneType) {
        String pid = pvInfo.getPid();
        //如果落地页渠道上设置的投放媒体为空，或为其他时，无需判断clickid是否为空，判断为非广告来源
        LandingPageChannel lpc = landingPageChannelService.getById(pvInfo.getChannelId());
        if (Objects.isNull(lpc.getLaunchPlatform()) || Platform.OTHER.equals(lpc.getLaunchPlatform())) {
            log.info("公众号处理回调消息匹配广告来源2.0.0.3 pid={}；wechatAppid={}；openid={}；launchPlatform={}；", pid, wechatAppid, openid, lpc.getLaunchPlatform());
            return OfficialFollowSource.NON_ADVERTISING;
        }
        //pv携带的clickid与落地页投放媒体设置的媒体不一致，判断为非广告来源
        Platform urlPlatform = UrlUtils.getPlatformSourcesByUrlOrRefer(pvInfo.getUrl(), pvInfo.getReferrer());
        if (Objects.isNull(urlPlatform) || !Objects.equals(lpc.getLaunchPlatform(), platform)) {
            log.info("公众号处理回调消息匹配广告来源2.0.0.4 pid={}；wechatAppid={}；openid={}；url={}；referrer={}；", pid, wechatAppid, openid, pvInfo.getUrl(), pvInfo.getReferrer());
            return OfficialFollowSource.NON_ADVERTISING;
        }
        //CLICKID历史上报失败（过去24小时内），判断为非广告来源
        if (customerUploadRecordServiceRedis.checkWoaTheSameClickidError(pvInfo.getClickId())) {
            log.info("公众号处理回调消息匹配广告来源2.0.0.5 pid={}；wechatAppid={}；openid={}；recordCodeList={}；", pid, wechatAppid, openid, pvInfo.getClickId());
            return OfficialFollowSource.NON_ADVERTISING;
        }
        //24小时内，同一渠道访问clickid相同但openid不一致的，全部判断为非广告来源
        final String redisKey = RedisConstant.OFFICIAL_FOLLOW_SOURCE_CACHE + pvInfo.getChannelId() + ":" + pvInfo.getClickId();
        Object objStr = objectRedisTemplate.opsForValue().get(redisKey);
        if (Objects.isNull(objStr)) {
            log.info("公众号处理回调消息匹配广告来源2.0.0.6-1 pid={}；wechatAppid={}；openid={}；objStr={}；", pid, wechatAppid, openid, objStr);
            objectRedisTemplate.opsForValue().set(redisKey, openid, 24L, TimeUnit.HOURS);
        }
        if (!Objects.isNull(objStr) && objStr instanceof String && !((String) objStr).contains(openid)) {
            log.info("公众号处理回调消息匹配广告来源2.0.0.6-2 pid={}；wechatAppid={}；openid={}；objStr={}；", pid, wechatAppid, openid, objStr);
            return OfficialFollowSource.NON_ADVERTISING;
        }
        //单个活码被多人关注，所有关注者标记为非广告来源，且不发送之后的公众号内动态渠道二维码，配置了非广告来源除外
        if (SubscribeSceneType.ADD_SCENE_QR_CODE_CARRY_PARAM.equals(subscribeSceneType)) {
            int count = landingPageWechatOfficialAccountUserService.count(new LambdaQueryWrapper<LandingPageWechatOfficialAccountUser>()
                //1小时内
                .ge(LandingPageWechatOfficialAccountUser::getCreatedAt, Instant.now().minusMillis(TimeUnit.HOURS.toMillis(1L)))
                .eq(LandingPageWechatOfficialAccountUser::getAppId, wechatAppid)
                //同一个码，自归因参数一样，所以pid也一样
                .eq(LandingPageWechatOfficialAccountUser::getMatchingPid, pid)
            );
            if (count > 0) {
                log.info("公众号处理回调消息匹配广告来源2.0.0.7-1 pid={}；wechatAppid={}；openid={}；objStr={}；subscribeSceneType={}；", pid, wechatAppid, openid, objStr, subscribeSceneType);
                return OfficialFollowSource.NON_ADVERTISING;
            }
        }
        return OfficialFollowSource.ADVERTISEMENT;
    }

    /**
     * 列表
     *
     * @param advertiserAccountGroupId
     * @param authStatus
     * @return
     */
    public List<WechatOfficialAccountListVO> list(Long advertiserAccountGroupId, WechatOfficialAccountAuthStatusEnum authStatus, String agentId) {
        return baseMapper.list(advertiserAccountGroupId, authStatus, agentId);
    }

    /**
     * 根据PMPid和appId获取授权成功的数量（生成页使用）
     *
     * @param advertiserAccountGroupId
     * @param appId
     * @return
     */
    public Long countByGenerate(Long advertiserAccountGroupId, String appId) {
        return baseMapper.countByGenerate(advertiserAccountGroupId, appId);
    }

    /**
     * 基于项目id和appid查询公众号
     *
     * @param advertiserAccountGroupId
     * @param appId
     * @return
     */
    public LandingPageWechatOfficialAccount getOneByPmpIdAndAppId(Long advertiserAccountGroupId, String appId) {
        return baseMapper.getOneByPmpIdAndAppId(advertiserAccountGroupId, appId);
    }

    /**
     * 获取同主体的开放平台id和其剩余绑定数
     *
     * @param principalName
     * @param agentId
     * @return
     */
    public List<OpenAppIdDto> getOpenInfo(String principalName, String componentAppId, String agentId) {
        return baseMapper.getOpenInfo(principalName, componentAppId, agentId);
    }

    /**
     * 获取同主体的开放平台id
     *
     * @param principalName
     * @param agentId
     * @return
     */
    public List<String> getOpenAppId(String principalName, String componentAppId, String agentId) {
        return baseMapper.getOpenAppId(principalName, componentAppId, agentId);
    }

    /**
     * 公众号与pmp解绑
     *
     * @param landingPageWechatOfficialAccountDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<LandingPageWechatOfficialAccountVo> unbindPmp(LandingPageWechatOfficialAccountDto landingPageWechatOfficialAccountDto) {
        List<Long> officialIds = landingPageWechatOfficialAccountDto.getOfficialIds();
        Long advertiserAccountGroupId = landingPageWechatOfficialAccountDto.getAdvertiserAccountGroupId();
        if (CollectionUtils.isEmpty(officialIds) || ObjectUtils.isEmpty(advertiserAccountGroupId)) {
            throw new RestException("参数错误!");
        }
        //同一agentId下
        String agentId = landingPageWechatOfficialAccountDto.getAgentId();
        LambdaQueryWrapper<LandingPageWechatOfficialAccount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LandingPageWechatOfficialAccount::getAgentId, agentId).in(LandingPageWechatOfficialAccount::getId, officialIds);
        List<LandingPageWechatOfficialAccount> landingPageWechatOfficialAccounts = this.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(landingPageWechatOfficialAccounts)) {
            throw new RestException("公众号不存在!");
        }
        Map<Long, LandingPageWechatOfficialAccount> configMap = landingPageWechatOfficialAccounts.stream().collect(Collectors.toMap(LandingPageWechatOfficialAccount::getId, Function.identity()));
        List<LandingPageWechatOfficialAccountVo> error = new ArrayList<>();
        officialIds.stream().forEach(e -> {
            LandingPageWechatOfficialAccount landingPageWechatOfficialAccount = configMap.get(e);
            this.unbind(landingPageWechatOfficialAccount, advertiserAccountGroupId, error);
        });
        return error;
    }


    /**
     * 解绑操作
     *
     * @param landingPageWechatOfficialAccount
     * @param advertiserAccountGroupId
     * @param error                            解绑失败的公众号
     */
    private void unbind(LandingPageWechatOfficialAccount landingPageWechatOfficialAccount, Long advertiserAccountGroupId, List<LandingPageWechatOfficialAccountVo> error) {
        LandingPageWechatOfficialAccountVo errorConfig = null;
        //公众号或项目不存在
        if (ObjectUtils.isEmpty(landingPageWechatOfficialAccount) || ObjectUtils.isEmpty(advertiserAccountGroupId)) {
            return;
        }
        Long id = landingPageWechatOfficialAccount.getId();
        String nickName = landingPageWechatOfficialAccount.getNickName();
        WechatOfficialAccountAuthStatusEnum wechatOfficialAccountAuthStatus = landingPageWechatOfficialAccount.getAuthStatus();
        //公众号状态为取消授权
        if (WechatOfficialAccountAuthStatusEnum.CANCEL.equals(wechatOfficialAccountAuthStatus)) {
            errorConfig = new LandingPageWechatOfficialAccountVo();
            errorConfig.setId(id).setWechatOfficialName(landingPageWechatOfficialAccount.getNickName()).setErrorMsg("公众号:【" + nickName + "】状态异常,不可解绑!");
            error.add(errorConfig);
            return;
        }
        //查询当前公众号与pmp关联数
        LambdaQueryWrapper<LandingPageWechatOfficialAccountRel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LandingPageWechatOfficialAccountRel::getWechatOfficialAccountId, id);
        int count = this.landingPageWechatOfficialAccountRelService.count(lambdaQueryWrapper);
        //小程序仅与一个pmp绑定
        if (count <= 1) {
            errorConfig = new LandingPageWechatOfficialAccountVo();
            errorConfig.setId(id).setWechatOfficialName(nickName).setErrorMsg("公众号:【" + nickName + "】仅与一个项目存在绑定关系!");
            error.add(errorConfig);
            return;
        }
        //查询参数加上项目ID，移除记录
        lambdaQueryWrapper.eq(LandingPageWechatOfficialAccountRel::getAdvertiserAccountGroupId, advertiserAccountGroupId);
        boolean b = this.landingPageWechatOfficialAccountRelService.remove(lambdaQueryWrapper);
        if (!b) {
            errorConfig = new LandingPageWechatOfficialAccountVo();
            errorConfig.setId(id).setWechatOfficialName(nickName).setErrorMsg("小程序:【" + nickName + "】解绑失败!");
            error.add(errorConfig);
        }
        //发送变更项目客服分组多人活码队列
        CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
        dto.setAgentId(landingPageWechatOfficialAccount.getAgentId())
            .setType(CustomerServiceChangeEnum.OFFICIAL_UNBINDING)
            .setAppId(landingPageWechatOfficialAccount.getAppId())
            .setAdvertiserAccountGroupId(advertiserAccountGroupId);
        multiplayerCodeSender.sendCustomerServiceChange(dto);
    }

    /**
     * 批量删除
     *
     * @param ids 公众号ID集合
     * @return
     */
    public List<LandingPageWechatOfficialAccountVo> removeAbnormal(List<Long> ids, Long advertiserAccountGroupId) {
        List<LandingPageWechatOfficialAccount> landingPageWechatOfficialAccounts = this.listByIds(ids);
        if (CollectionUtils.isEmpty(landingPageWechatOfficialAccounts)) {
            throw new RestException("找不到对应公众号!");
        }
        //不能删除的需返回，能删除的需要删除
        List<LandingPageWechatOfficialAccount> officialAccounts = landingPageWechatOfficialAccounts.stream().filter(e -> WechatOfficialAccountAuthStatusEnum.SUCCESS.equals(e.getAuthStatus())).collect(Collectors.toList());
        List<LandingPageWechatOfficialAccountVo> error = new ArrayList<>();
        if (!CollectionUtils.isEmpty(officialAccounts)) {
            List<LandingPageWechatOfficialAccountVo> collect = officialAccounts.stream().map(e -> {
                LandingPageWechatOfficialAccountVo fail = new LandingPageWechatOfficialAccountVo();
                String nickName = e.getNickName();
                fail.setId(e.getId());
                fail.setWechatOfficialName(nickName);
                fail.setErrorMsg("【" + nickName + "】为授权成功的公众号，不可删除!");
                return fail;
            }).collect(Collectors.toList());
            error.addAll(collect);
            //移除授权成功的公众号
            landingPageWechatOfficialAccounts.removeAll(officialAccounts);
        }
        //开始删除
        landingPageWechatOfficialAccounts.stream().forEach(e -> {
            //无需开启事务 开放平台无论解绑成功失败都删除
            try {
                this.removeOfficial(e);
                log.info("====删除公众号【" + e.getNickName() + "】记录及对应项目关联成功!=====");
                //解绑开放平台
                boolean unbindFlag = this.unbindOfficial(e);
                if (!unbindFlag) {
                    log.warn("公众号 【" + e.getNickName() + "】 解绑开放平台失败!");
                }
                //清除公众号助手相关
                cleanWechatOfficialAssistant(e, advertiserAccountGroupId);
            } catch (Exception exception) {
                String message = exception.getMessage();
                LandingPageWechatOfficialAccountVo landingPageWechatOfficialAccountVo = new LandingPageWechatOfficialAccountVo();
                landingPageWechatOfficialAccountVo.setId(e.getId());
                landingPageWechatOfficialAccountVo.setWechatOfficialName(e.getNickName());
                landingPageWechatOfficialAccountVo.setErrorMsg(message);
                error.add(landingPageWechatOfficialAccountVo);
            }
        });
        return error;
    }


    /**
     * 清除公众号助手相关
     */
    private void cleanWechatOfficialAssistant(LandingPageWechatOfficialAccount landingPageWechatOfficialAccount, Long advertiserAccountGroupId) {
        String appId = landingPageWechatOfficialAccount.getAppId();
        try {
            int count = wechatOfficialAccountAssistantRelService.count(
                Wrappers.lambdaQuery(WechatOfficialAccountAssistantRel.class)
                    .eq(WechatOfficialAccountAssistantRel::getAgentId, landingPageWechatOfficialAccount.getAgentId())
                    .eq(WechatOfficialAccountAssistantRel::getAdvertiserAccountGroupId, advertiserAccountGroupId)
                    .eq(WechatOfficialAccountAssistantRel::getAppId, appId)
            );
            if (count > 0) {
                landingPageWechatOfficialAccountService.addOfficialAccountAssistantNum(landingPageWechatOfficialAccount, advertiserAccountGroupId);
            }
            log.info("公众号:{}清除公众号助手及消息回复规则", appId);
            //清除公众号助手
            wechatOfficialAccountAssistantRelService.remove(new LambdaQueryWrapper<WechatOfficialAccountAssistantRel>()
                .eq(WechatOfficialAccountAssistantRel::getAppId, appId)
                .eq(WechatOfficialAccountAssistantRel::getAdvertiserAccountGroupId, advertiserAccountGroupId)
                .eq(WechatOfficialAccountAssistantRel::getAgentId, TenantContextHolder.get()));
            //清除消息回复规则及消息
            List<WechatOfficialAccountAssistantReply> list = wechatOfficialAccountAssistantReplyService.list(new LambdaQueryWrapper<WechatOfficialAccountAssistantReply>()
                .eq(WechatOfficialAccountAssistantReply::getAppId, appId));
            Optional.ofNullable(list).ifPresent(e -> {
                list.stream().forEach(m -> {
                    wechatOfficialAccountAssistantReplyService.deleteReply(m.getId());
                });
            });
            //清除菜单信息
            wechatOfficialAccountAssistantMenuNavigationService.remove(new LambdaQueryWrapper<WechatOfficialAccountAssistantMenuNavigation>()
                .eq(WechatOfficialAccountAssistantMenuNavigation::getAppId, appId));
        } catch (Exception e) {
            log.info("公众号:{}清除公众号助手及消息回复规则失败!", appId, e);
        }
    }

    /**
     * 删除公众号记录
     * 删除所有pmp关联
     * 解绑通过一叶系统绑定的微信开放平台
     *
     * @param landingPageWechatOfficialAccount
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeOfficial(LandingPageWechatOfficialAccount landingPageWechatOfficialAccount) {
        Long id = landingPageWechatOfficialAccount.getId();
        String nickName = landingPageWechatOfficialAccount.getNickName();
        boolean b = this.removeById(id);
        if (!b) {
            throw new RestException("删除公众号【" + nickName + "】 失败!");
        }
        //删除所有pmp关联
        LambdaQueryWrapper<LandingPageWechatOfficialAccountRel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LandingPageWechatOfficialAccountRel::getWechatOfficialAccountId, id);
        boolean remove = this.landingPageWechatOfficialAccountRelService.remove(lambdaQueryWrapper);
        if (!remove) {
            throw new RestException("移除公众号 【" + nickName + "】 项目关联失败!");
        }
    }

    /**
     * 与开放平台解绑
     * 解绑失败打印错误日志
     *
     * @param landingPageWechatOfficialAccount
     * @return
     */
    public boolean unbindOfficial(LandingPageWechatOfficialAccount landingPageWechatOfficialAccount) {
        try {
            Map<String, Object> queryMap = new HashMap<>();
            String openAppId = landingPageWechatOfficialAccount.getOpenAppId();
            if (StringUtils.isNotBlank(openAppId)) {
                queryMap.put("open_appid", openAppId);
                try {
                    JSONObject open = openWeixinApiClient.unbindOpen(landingPageWechatOfficialAccount.getAccessToken(), queryMap);
                    OpenCreateOpenResponseBody unwrap = new WechatPropertyMapper(open).mapProperty(OpenWechatMappers.WECHAT_OPEN_ID).unwrap(OpenCreateOpenResponseBody.class);
                    log.info("公众号-解绑微信开放平台账号接口-解绑成功-unwrap:{}", JSON.toJSONString(unwrap));
                    return true;
                } catch (MarketingWeOpenApiException e) {
                    String errorInfo = Objects.isNull(e.getCause()) ? e.getMessage() : e.getCause().getMessage();
                    log.warn("公众号-解绑微信开放平台账号接口-wechatOfficialAccount:[{}],异常信息:[{}]", JSON.toJSONString(landingPageWechatOfficialAccount), errorInfo, e);
                    WechatAppletResultCode resultCode = WechatAppletResultCode.getByCode(e.getErrcode());
                    if (resultCode != null) {
                        throw new RestException(resultCode.getMsg());
                    }
                    throw new RestException(String.format(WechatOfficialAccountResultCode.NOT_SUPPORT_UNBIND.getMsg(), landingPageWechatOfficialAccount.getNickName()));
                }
            }
            return false;
        } catch (Exception e) {
            log.error("e=>", e);
            return false;
        }
    }

    /**
     * 落地页编辑页-动态二维码-公众号列表
     *
     * @param advertiserAccountGroupId
     * @return
     */
    public List<LandingPageWechatOfficialAccount> qrcodeList(Long advertiserAccountGroupId, String agentId) {
        return baseMapper.qrcodeList(advertiserAccountGroupId, agentId);
    }

    /**
     * 根据公众号appid查询公众号信息
     *
     * @param appId 公众号的appid
     * @return 查询公众号信息
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public LandingPageWechatOfficialAccount getByAppid(String appId) {
        return this.getOne(
            Wrappers.lambdaQuery(LandingPageWechatOfficialAccount.class)
                .eq(LandingPageWechatOfficialAccount::getAppId, appId)
        );
    }

    /**
     * 微信公众号助手配额 + 1
     *
     * @param wechatOfficialAccount 公众号信息
     */
    public void addOfficialAccountAssistantNum(LandingPageWechatOfficialAccount wechatOfficialAccount, Long advertiserAccountGroupId) {
        log.info("增加微信公众号助手配额 ,advertiserAccountGroupId = {}", advertiserAccountGroupId);
        if (Objects.nonNull(wechatOfficialAccount)) {
            WechatOfficialAccountAssistantRelDto dto = new WechatOfficialAccountAssistantRelDto();
            dto.setAdvertiserAccountGroupId(advertiserAccountGroupId);
            dto.setAgentId(wechatOfficialAccount.getAgentId());
            dto.setAppId(wechatOfficialAccount.getAppId());
            bossBackendRemote.addOfficialAccountAssistantNum(dto);
        }
    }

    public List<LandingPageWechatOfficialAccount> getListByQrCode(Long advertiserAccountGroupId, String agentId, List<Long> ids) {
        return baseMapper.getListByQrCode(advertiserAccountGroupId, agentId, ids);
    }


    /**
     * 获取公众号个微归因推送信息
     *
     * @param appid
     * @return
     */
    public String getMicromilligramData(String appid) {
        if (StringUtils.isBlank(appid)) return null;
        return Optional.ofNullable(wechatOfficialAccountRedis.getMicromilligramDataCache(appid)).orElseGet(() -> {
            LandingPageWechatOfficialAccount one = this.getOne(Wrappers.lambdaQuery(LandingPageWechatOfficialAccount.class)
                .eq(LandingPageWechatOfficialAccount::getAppId, appid)
                .last("limit 1"));
            return Objects.isNull(one) ? "" : one.getMicromilligramDataUrl();
        });
    }

    /**
     * 校验个微归因配置及发送
     *
     * @param micromilligramDataDto
     */
    public void checkMicromilligramDataAndSend(MicromilligramDataDto micromilligramDataDto) {
        String appId = micromilligramDataDto.getAppId();
        //查询是否存在配置
        String micromilligramDataUrl = getMicromilligramData(appId);
        if (StringUtils.isBlank(micromilligramDataUrl)) return;
        String openId = micromilligramDataDto.getOpenId();
        String pvUrl = micromilligramDataDto.getPvUrl();
        JSONObject param = officialMicromilligramService.getParam(pvUrl);
        if (Objects.isNull(param)) {
            log.info("推送个微归因数据：pvUrl参数为空:pvUrl:{}", pvUrl);
            return;
        }
        String parentPid = param.getString("parentPid");
        String pageType = param.getString("pageType");
        //只推送企业推小程序的静默授权
        if (!StringUtils.equals(PageType.QIYETUI.toString(),pageType)){
            return;
        }
        String pid = micromilligramDataDto.getPid();
        //缓存获取空白页或者一跳页链接
        AdvertiseQiyeDTO advertiseQiyeDTO = advertiseRedis.getQiyeAdvertiseInfo(parentPid);
        if (Objects.nonNull(advertiseQiyeDTO) && StringUtils.isNoneBlank(openId, advertiseQiyeDTO.getUrl())) {
            log.info("推送公众号个微归因信息:openId:{},url:{}",openId,advertiseQiyeDTO.getUrl());
            officialMicromilligramService.sendMicromilligramData(appId,pid,micromilligramDataUrl, openId, advertiseQiyeDTO.getUrl());
        }
    }




}
