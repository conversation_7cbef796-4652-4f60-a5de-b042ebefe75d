package ai.yiye.agent.landingpage.service;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.web.exception.ErrorConstants;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.common.util.CommonUtil;
import ai.yiye.agent.common.util.DateTimeUtil;
import ai.yiye.agent.domain.*;
import ai.yiye.agent.domain.dto.*;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.landingpage.EnterpriseWechat;
import ai.yiye.agent.domain.landingpage.request.WechatCustomerCityCodeShowImageReq;
import ai.yiye.agent.domain.pageview.PageViewInfo;
import ai.yiye.agent.domain.util.EnumUtil;
import ai.yiye.agent.domain.utils.IPUtil;
import ai.yiye.agent.domain.utils.UrlUtils;
import ai.yiye.agent.domain.utils.WebUtils;
import ai.yiye.agent.domain.vo.AreaDesc;
import ai.yiye.agent.landingpage.config.AgentConf;
import ai.yiye.agent.landingpage.config.CustomerAcquisitionConfig;
import ai.yiye.agent.landingpage.config.EnterpriseWechatCustomerAcquisitionConfig;
import ai.yiye.agent.landingpage.config.LandingPageWechatCustomerContactConfig;
import ai.yiye.agent.landingpage.dto.AcquisitionLinkChangeLogDto;
import ai.yiye.agent.landingpage.dto.AcquisitionLinkCreateLogDto;
import ai.yiye.agent.landingpage.dto.CleanRemoteAcquisitionDto;
import ai.yiye.agent.landingpage.dto.LandingPageWechatCustomerServiceRedisDto;
import ai.yiye.agent.landingpage.enums.*;
import ai.yiye.agent.landingpage.redis.LandingPageWechatCustomerServiceRedis;
import ai.yiye.agent.landingpage.sender.EnterpriseWechatCustomerAcquisitionSender;
import ai.yiye.agent.landingpage.sender.LandingPageSender;
import ai.yiye.agent.landingpage.sender.UserOperationLogDetailActionSender;
import ai.yiye.agent.weixin.client.WorkWechatCustomerAcquisitionClient;
import ai.yiye.agent.weixin.domain.*;
import ai.yiye.agent.weixin.enums.EnterpriseWechatGlobalErrorCode;
import ai.yiye.agent.weixin.exception.MarketingApiException;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EnterpriseWechatCustomerAcquisitionService {

    @Resource
    private LandingPageWechatCustomerServiceService landingPageWechatCustomerServiceService;

    @Resource
    private LandingPageWechatCustomerServiceRedis landingPageWechatCustomerServiceRedis;

    @Resource
    private RedisTemplate<String, Object> objectRedisTemplate;

    @Resource
    private EnterpriseWechatCustomerAcquisitionConfig enterpriseWechatCustomerAcquisitionConfig;

    @Resource
    private WorkWechatCustomerAcquisitionClient workWechatCustomerAcquisitionClient;

    @Resource
    private EnterpriseWechatService enterpriseWechatService;

    @Resource
    private PageViewInfoPgService pageViewInfoPgService;

    @Resource
    private AgentConf agentConf;

    @Resource
    private EnterpriseWechatCustomerAcquisitionSender enterpriseWechatCustomerAcquisitionSender;

    @Resource
    private LandingPageWechatCustomerServiceGroupRelService landingPageWechatCustomerServiceGroupRelService;

    @Resource
    private WorkWechatCustomerAcquisitionLinkService workWechatCustomerAcquisitionLinkService;

    @Resource
    private CustomerAcquisitionLinkRecordService customerAcquisitionLinkRecordService;

    @Resource
    private LandingPageSender landingPageSender;

    @Resource
    private UserOperationLogDetailActionSender userOperationLogDetailActionSender;

    @Resource
    private CustomerService customerService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private CustomerAcquisitionConfig customerAcquisitionConfig;

    @Resource
    private CustomerAcquisitionCustomerRecordService customerAcquisitionCustomerRecordService;

    @Resource
    private LandingPageWechatCustomerCityCodeService landingPageWechatCustomerCityCodeService;

    @Resource
    private LandingPageWechatCustomerContactConfig landingPageWechatCustomerContactConfig;


    private static final String CUSTOMER_SERVICE_NAME_WILDCARD_CHARACTER = "{name}";

    /**
     * 一跳页点击获取获客链接
     *
     * @param qrCodeShowImageDto
     * @return
     */
    public EnterpriseWechatCustomerAcquisitionDto customerAcquisitionGet(QrCodeShowImageDto qrCodeShowImageDto) {
        Long wechatCustomerServiceGroupId = qrCodeShowImageDto.getWechatCustomerServiceGroupId();
        //参数校验
        checkCustomerAcquisitionParam(qrCodeShowImageDto);

        //查询对应客服列表信息
        List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList = landingPageWechatCustomerServiceRedis.getWechatServiceDataByGroupId(wechatCustomerServiceGroupId,
            () -> landingPageWechatCustomerServiceService.getByGroupId(wechatCustomerServiceGroupId));
        if (CollectionUtils.isEmpty(landingPageWechatCustomerServiceList)) {
            log.info("===>获取获客链接失败,客服列表为空 {}", JSONObject.toJSONString(qrCodeShowImageDto));
            return null;
        }

        //先查询是否存在相同clickId多次获取的情况 返回相同的链接
        final String url = qrCodeShowImageDto.getUrl();
        BaseStatusEnum checkClick = qrCodeShowImageDto.getCheckClick();
        String clickId = null;
        if (StringUtils.isNotBlank(url) && BaseStatusEnum.ENABLE.equals(checkClick)) {
            clickId = UrlUtils.getClickIdByPlatform(url, null);
            if (StringUtils.isNotBlank(clickId)) {
                String acquisitionLink = landingPageWechatCustomerServiceRedis.getAcquisitonLinkByClickId(clickId);
                if (StringUtils.isNotBlank(acquisitionLink)) {
                    return new EnterpriseWechatCustomerAcquisitionDto().setWechatCustomerAcquisitionLink(acquisitionLink);
                }
            }
        }


        //过滤出获客链接不为空的且状态为正常的
        landingPageWechatCustomerServiceList = landingPageWechatCustomerServiceList.stream().filter(e ->
            (StringUtils.isNotBlank(e.getWechatCustomerAcquisitionLink()) &&
                WechatCustomerAcquisitionLinkStatus.NORMAL.getValue().equals(e.getWechatCustomerAcquisitionLinkStatus()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(landingPageWechatCustomerServiceList)) {
            throw new RestException(ErrorConstants.WECHAT_CUSTOMER_ACQUISITION_LINK_EMPTY);
        }
        EnterpriseWechatCustomerAcquisitionDto enterpriseWechatCustomerAcquisitionDto = new EnterpriseWechatCustomerAcquisitionDto();
        //获取获客链接
        EnterpriseWechatCustomerAcquisitionDto customerAcquisitionDto = getCustomerAcquisitionLink(qrCodeShowImageDto, landingPageWechatCustomerServiceList);
        if (Objects.nonNull(customerAcquisitionDto)) {
            enterpriseWechatCustomerAcquisitionDto.setWechatCustomerAcquisitionLink(customerAcquisitionDto.getWechatCustomerAcquisitionLink());
            enterpriseWechatCustomerAcquisitionDto.setPrincipleName(customerAcquisitionDto.getPrincipleName());
            enterpriseWechatCustomerAcquisitionDto.setWechatUserName(customerAcquisitionDto.getWechatUserName());
            enterpriseWechatCustomerAcquisitionDto.setWechatAvatar(customerAcquisitionDto.getWechatAvatar());
            //存储获客助手访客缓存
            setAcquisitionAddLimitOnce(qrCodeShowImageDto, customerAcquisitionDto);
            //不为空说明已经开启需要验证
            if (StringUtils.isNotBlank(clickId)) {
                landingPageWechatCustomerServiceRedis.saveAcquisitonLinkByClickId(clickId, customerAcquisitionDto.getWechatCustomerAcquisitionLink());
            }
        }
        return enterpriseWechatCustomerAcquisitionDto;
    }

    /**
     * 存储获客助手访客缓存信息
     *
     * @param qrCodeShowImageDto
     * @param customerAcquisitionDto
     */
    private void setAcquisitionAddLimitOnce(QrCodeShowImageDto qrCodeShowImageDto, EnterpriseWechatCustomerAcquisitionDto customerAcquisitionDto) {
        String uid = qrCodeShowImageDto.getUid();
        String wechatCustomerAcquisitionLink = customerAcquisitionDto.getWechatCustomerAcquisitionLink();
        String customerChannel = customerAcquisitionDto.getCustomerChannel();
        if (StringUtils.isAnyBlank(uid, wechatCustomerAcquisitionLink, customerChannel)) {
            log.info("===>存储获客助手访客缓存信息失败,uid或wechatCustomerAcquisitionLink为空 uid:{},link:{}", uid, wechatCustomerAcquisitionLink);
            return;
        }
        log.info("获客助手获取-》缓存存储用户访客及获客助手信息,uid:{}", uid);
        LandingPageAddLimitUserInfo landingPageAddLimitUserInfo = new LandingPageAddLimitUserInfo();
        landingPageAddLimitUserInfo.setLandingPageId(qrCodeShowImageDto.getLandingPageId())
            .setUid(qrCodeShowImageDto.getUid())
            .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.NOT_ADDED)
            .setAdvertiserAccountGroupId(qrCodeShowImageDto.getAdvertiserAccountGroupId())
            .setLandingPageId(qrCodeShowImageDto.getLandingPageId())
            .setAgentId(TenantContextHolder.get());
        Optional.of(uid).filter(StringUtils::isNotBlank).ifPresent(e -> {
            String key = RedisConstant.LANDING_PAGE_SERVICE_ACQUISITION_ADD_ONLY_ONCE_KEY + e;
            landingPageWechatCustomerServiceRedis.saveAcquisitionAddLimitUserInfoCache(customerChannel, key, "uid", landingPageAddLimitUserInfo, e);
        });
        setDeviceUserDto(qrCodeShowImageDto, customerChannel, landingPageAddLimitUserInfo);

    }

    /**
     * 设置获客助手访客缓存信息
     *
     * @param qrCodeShowImageDto
     * @param customerChannel
     * @param landingPageAddLimitUserInfo
     */
    private void setDeviceUserDto(QrCodeShowImageDto qrCodeShowImageDto, String customerChannel, LandingPageAddLimitUserInfo landingPageAddLimitUserInfo) {
        //解析 url中的设备参数 并存储
        String url = qrCodeShowImageDto.getUrl();
        String device = UrlUtils.getDevice(url);
        if (StringUtils.isNotBlank(device)) {
            //账户维度也存一份
            String key = RedisConstant.LANDING_PAGE_SERVICE_ACQUISITION_ADD_ONLY_ONCE_KEY + device;
            landingPageWechatCustomerServiceRedis.saveAcquisitionAddLimitUserInfoCache(customerChannel, key, "device", landingPageAddLimitUserInfo, device);
        }
    }

    /**
     * 解析当前请求中的IP（已有工具类解析），获取省市信息
     * 通过省市信息查询到城市区域码，若无或者对应客服分组已下线，则使用兜底城市区域码
     * 省市区域码需存入redis，key为租户id:项目id:城市区域码id,value为hash数据（key=省编码:市编码，value=客服分组ID），CRUD对redis数据的维护
     * 通过城市区域码拿到对应的客服分组
     * 需判断是否为同一访客，通过id找到缓存中的客服id，并判断此客服id是否存在于分组中，若是则可以判定为同一访客，并展示对应二维码
     * 长按二维码存取不同id对应的redis数据
     * 非同一访客还是走之前的权重算法拿到客服id
     *
     * @param qrCodeShowImageDto
     * @return
     */
    public EnterpriseWechatCustomerAcquisitionDto customerAcquisitionCityCodeGet(QrCodeShowImageDto qrCodeShowImageDto, HttpServletRequest request) {
        log.info("获取获客助手城市区域码对应获客链接=========>>>>>入参 {}", JSON.toJSONString(qrCodeShowImageDto));
        String ipAddr = ServletUtil.getClientIP(request);
        AreaDesc area = IPUtil.invokeByIp(ipAddr);
        log.info("获客助手-解析的ip地址 {} 城市信息为 {} 请求参数: {}", ipAddr, JSON.toJSONString(area), JSON.toJSONString(qrCodeShowImageDto));
        AtomicLong atomicLong = new AtomicLong(0);
        List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList = getCityCodeCustomerService(qrCodeShowImageDto, area, atomicLong);
        long customerServiceGroupId = atomicLong.get();
        //无可用客服
        if (customerServiceGroupId <= 0 || CollectionUtil.isEmpty(landingPageWechatCustomerServiceList)) {
            log.info("获客助手-城市区域码-无可用客服=========>>>>>");
            return null;
        }
        //过滤出获客链接不为空的且状态为正常的
        landingPageWechatCustomerServiceList = landingPageWechatCustomerServiceList.stream().filter(e ->
            (StringUtils.isNotBlank(e.getWechatCustomerAcquisitionLink()) &&
                WechatCustomerAcquisitionLinkStatus.NORMAL.getValue().equals(e.getWechatCustomerAcquisitionLinkStatus()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(landingPageWechatCustomerServiceList)) {
            throw new RestException(ErrorConstants.WECHAT_CUSTOMER_ACQUISITION_LINK_EMPTY);
        }

        EnterpriseWechatCustomerAcquisitionDto enterpriseWechatCustomerAcquisitionDto = new EnterpriseWechatCustomerAcquisitionDto();
        //先查询是否存在相同clickId多次获取的情况 返回相同的链接
        String url = qrCodeShowImageDto.getUrl();
        BaseStatusEnum checkClick = qrCodeShowImageDto.getCheckClick();
        String clickId = null;
        if (StringUtils.isNotBlank(url) && BaseStatusEnum.ENABLE.equals(checkClick)) {
            clickId = UrlUtils.getClickIdByPlatform(url, null);
            if (StringUtils.isNotBlank(clickId)) {
                String acquisitionLink = landingPageWechatCustomerServiceRedis.getAcquisitonLinkByClickId(clickId);
                if (StringUtils.isNotBlank(acquisitionLink)) {
                    return enterpriseWechatCustomerAcquisitionDto.setWechatCustomerAcquisitionLink(acquisitionLink);
                }
            }
        }


        qrCodeShowImageDto.setWechatCustomerServiceGroupId(customerServiceGroupId);
        //获取获客链接
        EnterpriseWechatCustomerAcquisitionDto customerAcquisitionDto = getCustomerAcquisitionLink(qrCodeShowImageDto, landingPageWechatCustomerServiceList);
        log.info("获取城市区域码，customerAcquisitionDto = {}", JSONObject.toJSONString(customerAcquisitionDto));
        if (Objects.nonNull(customerAcquisitionDto)) {
            String customerAcquisitionLink = customerAcquisitionDto.getWechatCustomerAcquisitionLink();
            enterpriseWechatCustomerAcquisitionDto.setWechatCustomerAcquisitionLink(customerAcquisitionLink);
            enterpriseWechatCustomerAcquisitionDto.setWechatUserName(customerAcquisitionDto.getWechatUserName());
            enterpriseWechatCustomerAcquisitionDto.setWechatAvatar(customerAcquisitionDto.getWechatAvatar());
            enterpriseWechatCustomerAcquisitionDto.setPrincipleName(customerAcquisitionDto.getPrincipleName());
            //存储获客助手访客缓存
            setAcquisitionAddLimitOnce(qrCodeShowImageDto, customerAcquisitionDto);
            //不为空说明已经开启需要验证
            if (StringUtils.isNotBlank(clickId)) {
                landingPageWechatCustomerServiceRedis.saveAcquisitonLinkByClickId(clickId, customerAcquisitionDto.getWechatCustomerAcquisitionLink());
            }
        }
        return enterpriseWechatCustomerAcquisitionDto;
    }

    /**
     * 获取城市区域码对应客服分组
     *
     * @param qrCodeShowImageDto
     * @param area
     * @return
     */
    private List<LandingPageWechatCustomerServiceRedisDto> getCityCodeCustomerService(QrCodeShowImageDto qrCodeShowImageDto, AreaDesc area, AtomicLong atomicLong) {
        List<LandingPageWechatCustomerServiceRedisDto> wechatCustomers;
        Long customerServiceGroupId = null;
        WechatCustomerCityCodeShowImageReq wechatCustomerCityCodeShowImageReq = new WechatCustomerCityCodeShowImageReq();
        wechatCustomerCityCodeShowImageReq.setLandingPageWechatCustomerCityCodeId(qrCodeShowImageDto.getLandingPageWechatCustomerCityCodeId());
        if (Objects.nonNull(area) && Objects.nonNull(area.getCityId())) {
            //获取缓存中城市区域对应的客服分组
            Object customerServiceGroupIdObj = landingPageWechatCustomerCityCodeService.getCustomerGroupId(area, wechatCustomerCityCodeShowImageReq);
            customerServiceGroupId = Convert.toLong(customerServiceGroupIdObj, null);
            //匹配不到普通区域码，使用兜底区域码
            if (Objects.isNull(customerServiceGroupId)) {
                log.info("获客助手-匹配不到客服分组普通区域码，使用兜底区域码=========>>>>>");
                customerServiceGroupId = landingPageWechatCustomerCityCodeService.getEarsCustomerServiceGroupId(wechatCustomerCityCodeShowImageReq);
                wechatCustomers = landingPageWechatCustomerCityCodeService.getEarsWechatCustomer(customerServiceGroupId);
            } else {
                log.info("获客助手-匹配到客服分组普通区域码=========>>>>>{}", customerServiceGroupId);
                //通过客服分组ID，获取微信客服信息
                final Long finalCustomerServiceGroupId = customerServiceGroupId;
                List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList = landingPageWechatCustomerServiceRedis.getWechatServiceDataByGroupId(customerServiceGroupId,
                    () -> landingPageWechatCustomerServiceService.getByGroupId(finalCustomerServiceGroupId)
                );
                //该分组下的客服不存在或全部下线，启用兜底区域码
                if (CollectionUtils.isEmpty(landingPageWechatCustomerServiceList)) {
                    log.info("获客助手-该分组下的客服不存在或全部下线，使用兜底区域码=========>>>>>");
                    customerServiceGroupId = landingPageWechatCustomerCityCodeService.getEarsCustomerServiceGroupId(wechatCustomerCityCodeShowImageReq);
                    wechatCustomers = landingPageWechatCustomerCityCodeService.getEarsWechatCustomer(customerServiceGroupId);
                } else {
                    wechatCustomers = landingPageWechatCustomerServiceList;
                }
            }
        } else {
            //获取不到IP或解析失败的，走兜底
            log.info("获客助手-获取不到IP或解析失败，使用兜底区域码=========>>>>>");
            customerServiceGroupId = landingPageWechatCustomerCityCodeService.getEarsCustomerServiceGroupId(wechatCustomerCityCodeShowImageReq);
            wechatCustomers = landingPageWechatCustomerCityCodeService.getEarsWechatCustomer(customerServiceGroupId);
        }
        if (Objects.nonNull(customerServiceGroupId)) {
            atomicLong.set(customerServiceGroupId);
        }
        return wechatCustomers;
    }

    /**
     * 初始化客服获客链接
     */
    @Deprecated
    public void initCustomerAcquisition(EnterpriseWechatCustomerAcquisitionMessageDto enterpriseWechatCustomerAcquisitionMessageDto) {
        String agentId = enterpriseWechatCustomerAcquisitionMessageDto.getAgentId();
        Long advertiserAccountGroupId = enterpriseWechatCustomerAcquisitionMessageDto.getAdvertiserAccountGroupId();
        //查询有userid，没有获客链接的
        LambdaQueryWrapper<LandingPageWechatCustomerService> queryWrapper = new LambdaQueryWrapper<LandingPageWechatCustomerService>()
            .isNotNull(LandingPageWechatCustomerService::getWechatUserId)
            .isNull(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLink);
        List<LandingPageWechatCustomerService> serviceList = landingPageWechatCustomerServiceService.list(queryWrapper);
        if (CollectionUtils.isEmpty(serviceList)) {
            log.info("====>获客链接-无可初始化的客服列表!agentId:{},advertiserAccountGroupId:{}", agentId, advertiserAccountGroupId);
            return;
        }
        //查询当前代开发企微
        Set<String> coprids = serviceList.stream().map(LandingPageWechatCustomerService::getCorpId).filter(e -> StringUtils.isNotBlank(e)).collect(Collectors.toSet());
        Map<String, EnterpriseWechat> enterpriseWechatMap = coprids.stream().map(e -> {
            EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatCacheByCorpId(e);
            return enterpriseWechat;
        }).collect(Collectors.toMap(EnterpriseWechat::getCorpid, Function.identity()));
        if (CollectionUtils.isEmpty(enterpriseWechatMap)) {
            log.info("====>获客链接-当前账户项目下无可用代开发应用!agentId:{},advertiserAccountGroupId:{}", agentId, advertiserAccountGroupId);
            return;
        }
        List<LandingPageWechatCustomerService> list = new ArrayList<>();
        serviceList.stream().forEach(e -> {
            String wechatUserName = e.getWechatUserName();
            String wechatUserId = e.getWechatUserId();
            LandingPageWechatCustomerService landingPageWechatCustomerService = new LandingPageWechatCustomerService();
            landingPageWechatCustomerService.setId(e.getId());
            EnterpriseWechat enterpriseWechat = enterpriseWechatMap.get(e.getCorpId());
            if (Objects.isNull(enterpriseWechat)) {
                return;
            }
            try {
                CustomerAcquisitionRequestItemBody customerAcquisitionRequestItemBody = new CustomerAcquisitionRequestItemBody();
                customerAcquisitionRequestItemBody.setUserList(Arrays.asList(wechatUserId));
                CustomerAcquisitionRequestBody customerAcquisitionRequestBody = new CustomerAcquisitionRequestBody().setLinkName(wechatUserName).setRange(customerAcquisitionRequestItemBody);
                CustomerAcquisitionResponseBody customerAcquisition = workWechatCustomerAcquisitionClient.createCustomerAcquisition(enterpriseWechat.getAccessToken(), customerAcquisitionRequestBody);
                CustomerAcquisitionResponseItemBody link = customerAcquisition.getLink();
                String linkId = link.getLinkId();
                String url = link.getUrl();
                landingPageWechatCustomerService.setWechatCustomerAcquisitionLinkId(linkId);
                landingPageWechatCustomerService.setWechatCustomerAcquisitionLink(url);
                landingPageWechatCustomerService.setWechatCustomerAcquisitionLinkStatus(WechatCustomerAcquisitionLinkStatus.NORMAL);
                //增加缓存，记录这个获客链接是一叶生成的，后续防刷的时候需要判断，只对一叶生成的获客链接进行防刷校验
                landingPageWechatCustomerServiceRedis.cacheYiyeLinkId(linkId, agentId, e.getCorpId(), e.getId());
            } catch (Exception exception) {
                landingPageWechatCustomerService.setWechatCustomerAcquisitionLinkStatus(WechatCustomerAcquisitionLinkStatus.ANOMALY)
                    .setWechatCustomerAcquisitionLinkReason(exception.getMessage());
                log.error("===>客户开通获客助手白名单,初始化获客链接异常!agentId:{},advertiserAccountGroupId:{},landingPageWechatCustomerServiceId:{}",
                    agentId, advertiserAccountGroupId, e.getId(), exception);
            }
            list.add(landingPageWechatCustomerService);
        });
        if (!CollectionUtils.isEmpty(list)) {
            boolean b = landingPageWechatCustomerServiceService.updateBatchById(list);
            if (b) {
                log.info("===>客户开通获客助手白名单,初始化获客链接成功!agentId:{},advertiserAccountGroupId:{}", agentId, advertiserAccountGroupId);
            }
            List<Long> wechatCustomerServiceIds = list.stream().map(LandingPageWechatCustomerService::getId).collect(Collectors.toList());
            //清除客服分组缓存
            //查询此客服对应分组
            List<LandingPageWechatCustomerServiceGroupRel> wechatCustomerServiceRels = landingPageWechatCustomerServiceGroupRelService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceGroupRel>()
                .eq(LandingPageWechatCustomerServiceGroupRel::getAdvertiserAccountGroupId, advertiserAccountGroupId)
                .in(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceId, wechatCustomerServiceIds));
            if (CollectionUtils.isEmpty(wechatCustomerServiceRels)) {
                return;
            }
            //清除对应客服分组缓存
            wechatCustomerServiceRels.stream().forEach(e -> {
                if (!ObjectUtils.isEmpty(e.getLandingPageWechatCustomerServiceGroupId()) && e.getLandingPageWechatCustomerServiceGroupId() > 0) {
                    landingPageWechatCustomerServiceRedis.deleteWechatServiceDataByGroupId(e.getLandingPageWechatCustomerServiceGroupId());
                }
            });
        }
    }

    /**
     * 获取获客链接后修改pv信息
     *
     * @param enterpriseWechatCustomerAcquisitionMessageDto
     */
    public void customerAcquisitionUpdatePv(EnterpriseWechatCustomerAcquisitionMessageDto enterpriseWechatCustomerAcquisitionMessageDto) {
        String pid = enterpriseWechatCustomerAcquisitionMessageDto.getPid();
        String clickCustomerAcquisitonLinkId = enterpriseWechatCustomerAcquisitionMessageDto.getClickCustomerAcquisitonLinkId();
        String customerAcquisitonParamState = enterpriseWechatCustomerAcquisitionMessageDto.getCustomerAcquisitonParamState();
        String clickCustomerAcquisitonLink = enterpriseWechatCustomerAcquisitionMessageDto.getClickCustomerAcquisitonLink();
        JSONObject customerAcquisitonParam = enterpriseWechatCustomerAcquisitionMessageDto.getCustomerAcquisitonParam();
        Long wechatCustomerServiceId = enterpriseWechatCustomerAcquisitionMessageDto.getWechatCustomerServiceId();
        if (StringUtils.isBlank(pid)) {
            log.info("获取获客链接后修改pv信息,pid为空");
            return;
        }
        //修改pv信息
        Instant now = Instant.now();
        pageViewInfoPgService.update(new LambdaUpdateWrapper<PageViewInfo>()
            .set(PageViewInfo::getClickCustomerAcquisitonLinkId, clickCustomerAcquisitonLinkId)
            .set(PageViewInfo::getClickCustomerAcquisitonLink, clickCustomerAcquisitonLink)
            .set(PageViewInfo::getCustomerAcquisitonParamState, customerAcquisitonParamState)
            .set(PageViewInfo::getCustomerAcquisitonParam, customerAcquisitonParam)
            .set(PageViewInfo::getClickCustomerAcquisitonStatus, YesOrNoEnum.YES)
            .set(PageViewInfo::getWechatCustomerServiceId, wechatCustomerServiceId)
            .ge(PageViewInfo::getCreatedAt, now.minus(7, ChronoUnit.DAYS))
            .le(PageViewInfo::getCreatedAt, now)
            .eq(PageViewInfo::getPid, pid));
    }


    /**
     * 微信客服机器人发送获客助手链接后，修改对应pv的信息
     *
     * @param enterpriseWechatCustomerAcquisitionMessageDto 入参
     */
    public void robotCustomerAcquisitionUpdatePv(EnterpriseWechatCustomerAcquisitionMessageDto enterpriseWechatCustomerAcquisitionMessageDto) {
        if (Objects.nonNull(enterpriseWechatCustomerAcquisitionMessageDto)) {
            String pid = enterpriseWechatCustomerAcquisitionMessageDto.getPid();
            String clickCustomerAcquisitonLinkId = enterpriseWechatCustomerAcquisitionMessageDto.getClickCustomerAcquisitonLinkId();
            String customerAcquisitonParamState = enterpriseWechatCustomerAcquisitionMessageDto.getCustomerAcquisitonParamState();
            String clickCustomerAcquisitonLink = enterpriseWechatCustomerAcquisitionMessageDto.getClickCustomerAcquisitonLink();
            JSONObject customerAcquisitonParam = enterpriseWechatCustomerAcquisitionMessageDto.getCustomerAcquisitonParam();
            Long wechatCustomerServiceId = enterpriseWechatCustomerAcquisitionMessageDto.getWechatCustomerServiceId();
            if (StringUtils.isBlank(pid)) {
                log.info("获取获客链接后修改pv信息,pid为空");
                return;
            }
            //修改pv信息
            if (StringUtils.isNotBlank(pid)) {
                pageViewInfoPgService.update(new LambdaUpdateWrapper<PageViewInfo>()
                    .set(PageViewInfo::getClickCustomerAcquisitonLinkId, clickCustomerAcquisitonLinkId)
                    .set(PageViewInfo::getClickCustomerAcquisitonLink, clickCustomerAcquisitonLink)
                    .set(PageViewInfo::getCustomerAcquisitonParamState, customerAcquisitonParamState)
                    .set(PageViewInfo::getCustomerAcquisitonParam, customerAcquisitonParam)
                    .set(PageViewInfo::getWechatCustomerServiceId, wechatCustomerServiceId)
                    .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoUpdateTime()))
                    .eq(PageViewInfo::getPid, pid));
            }
        }
    }

    /**
     * 重新生成获客链接
     */
    public void afreshCustomerAcquisition(LandingPageWechatCustomerService wechatCustomerService, Long advertiserAccountGroupId,
                                          EnterpriseWechat enterpriseWechat, SwitchStatus linkVerify, User user, String ip, String linkName, AcquisitionChooseEnum acquisitionChooseEnum) {
        String wechatUserId = wechatCustomerService.getWechatUserId();
        if (StringUtils.isBlank(wechatUserId)) {
            return;
        }
        String agentId = TenantContextHolder.get();
        Boolean skipVerify = true;
        if (SwitchStatus.OPEN.equals(linkVerify)) {
            skipVerify = false;
        }
        String accessToken = enterpriseWechat.getAccessToken();
        //本身已经生成了获客链接，删除旧获客链接
        String wechatCustomerAcquisitionLinkId = wechatCustomerService.getWechatCustomerAcquisitionLinkId();
        if (StringUtils.isNotBlank(wechatCustomerAcquisitionLinkId)) {
            try {
                workWechatCustomerAcquisitionClient.deleteCustomerAcquisition(accessToken, new CustomerAcquisitionRequestBody().setLinkId(wechatCustomerAcquisitionLinkId));
                log.info("重新生成获客链接->删除原有的获客链接-》删除成功!linkId:{},wechatCustomerServiceId:{},advertiserAccountGroupId:{}",
                    wechatCustomerAcquisitionLinkId, wechatCustomerService.getId(), advertiserAccountGroupId);
            } catch (Exception e) {
                log.error("重新生成获客链接->删除原有的获客链接-》删除失败!linkId:{},wechatCustomerServiceId:{},advertiserAccountGroupId:{}",
                    wechatCustomerAcquisitionLinkId, wechatCustomerService.getId(), advertiserAccountGroupId, e);
            }
        }
        String wechatName = wechatCustomerService.getWechatName();
        //前端传递的linkName不为空且为自定义名称则使用前端传递字段，否则使用企业微信成员名称
        String name = (StringUtils.isNotBlank(linkName) && AcquisitionChooseEnum.CUSTOM.equals(acquisitionChooseEnum)) ? linkName : wechatName;
        if (StringUtils.isNotBlank(name)) {
            name = name.replace(CUSTOMER_SERVICE_NAME_WILDCARD_CHARACTER, wechatName);
            if (name.length() > 16) {
                //超过16个字符，裁剪前面16个字符
                name = name.substring(0, 16);
            }
        }
        WorkWechatCustomerAcquisitionLinkDTO dto = new WorkWechatCustomerAcquisitionLinkDTO();
        dto.setUserId(wechatUserId).setCorpId(enterpriseWechat.getCorpid())
            .setWechatCustomerAcquisitionLinkVerify(linkVerify)
            .setWechatCustomerAcquisitionLinkName(name)
            .setAcquisitionChooseEnum(acquisitionChooseEnum);
        AcquisitionLinkCreateLogDto createLogDto = new AcquisitionLinkCreateLogDto();
        createLogDto.setUserId(wechatUserId)
            .setCorpId(enterpriseWechat.getCorpid())
            .setVerify(linkVerify)
            .setOperUserId(user.getId())
            .setOperUserName(user.getUsername())
            .setOperationRole(user.getOperationRole())
            .setOperIp(ip);
        //生成获客链接
        try {
            CustomerAcquisitionRequestItemBody customerAcquisitionRequestItemBody = new CustomerAcquisitionRequestItemBody();
            customerAcquisitionRequestItemBody.setUserList(Arrays.asList(wechatUserId));
            //获客链接名称调整为企业微信成员名称
            CustomerAcquisitionRequestBody customerAcquisitionRequestBody = new CustomerAcquisitionRequestBody()
                .setLinkName(name)
                .setRange(customerAcquisitionRequestItemBody)
                .setSkipVerify(skipVerify);
            CustomerAcquisitionResponseBody customerAcquisition = workWechatCustomerAcquisitionClient.createCustomerAcquisition(enterpriseWechat.getAccessToken(), customerAcquisitionRequestBody);
            CustomerAcquisitionResponseItemBody link = customerAcquisition.getLink();
            String linkId = link.getLinkId();
            String url = link.getUrl();
            dto.setWechatCustomerAcquisitionLinkId(linkId)
                .setWechatCustomerAcquisitionLink(url)
                .setWechatCustomerAcquisitionLinkStatus(WechatCustomerAcquisitionLinkStatus.NORMAL);
            createLogDto.setCreateStatus(CreateStatus.SUCCESS);
        } catch (MarketingApiException exception) {
            dto.setWechatCustomerAcquisitionLinkStatus(WechatCustomerAcquisitionLinkStatus.ANOMALY);
            createLogDto.setCreateStatus(CreateStatus.FAIL);
            if (EnterpriseWechatGlobalErrorCode.API_HAVE_NO_AUTHORITY.getCode().equals(exception.getErrcode())) {
                dto.setWechatCustomerAcquisitionLinkReason("未获取获客助手创建权限");
                createLogDto.setReason("未获取获客助手创建权限");
            } else {
                dto.setWechatCustomerAcquisitionLinkReason(exception.getMessage());
                createLogDto.setReason(exception.getMessage());
            }
            log.error("===>重新生成获客链接->获客链接创建异常!agentId:{},advertiserAccountGroupId:{},landingPageWechatCustomerServiceId:{}",
                agentId, advertiserAccountGroupId, wechatCustomerService.getId(), exception);
        }
        landingPageSender.sendAcquisitionLink(dto);
        userOperationLogDetailActionSender.sendAcquisitionLinkCreateLog(createLogDto);
        //增加缓存，记录这个获客链接是一叶生成的，后续防刷的时候需要判断，只对一叶生成的获客链接进行防刷校验
        String cacheLinkId = dto.getWechatCustomerAcquisitionLinkId();
        landingPageWechatCustomerServiceRedis.cacheYiyeLinkId(cacheLinkId, agentId, enterpriseWechat.getCorpid(), wechatCustomerService.getId());
        if (!WechatCustomerAcquisitionLinkStatus.EMPTY.equals(wechatCustomerService.getWechatCustomerAcquisitionLinkStatus()) &&
            linkVerify != null && !linkVerify.equals(wechatCustomerService.getWechatCustomerAcquisitionLinkVerify())) {
            String operDesc = "客服获客助手链接添加方式<br>添加方式：" + (SwitchStatus.CLOSE.equals(wechatCustomerService.getWechatCustomerAcquisitionLinkVerify()) ? "免验证通过" : "需验证通过") + "->" + (SwitchStatus.CLOSE.equals(linkVerify) ? "免验证通过" : "需验证通过");
            AcquisitionLinkChangeLogDto changeLogDto = new AcquisitionLinkChangeLogDto();
            changeLogDto.setUserId(wechatUserId)
                .setCorpId(enterpriseWechat.getCorpid())
                .setOperDesc(operDesc)
                .setOperUserId(user.getId())
                .setOperUserName(user.getUsername())
                .setOperationRole(user.getOperationRole())
                .setOperIp(ip);
            userOperationLogDetailActionSender.sendAcquisitionLinkChangeLog(changeLogDto);
        }
    }

    public void batch(EnterpriseWechatCustomerAcquisitionBatchDto dto) {
        List<Long> ids = dto.getIds();
        Long advertiserAccountGroupId = dto.getAdvertiserAccountGroupId();
        SwitchStatus wechatCustomerAcquisitionLinkVerify = dto.getWechatCustomerAcquisitionLinkVerify();
        String linkName = dto.getWechatCustomerAcquisitionLinkName();
        if (CollectionUtils.isEmpty(ids) || advertiserAccountGroupId == null || wechatCustomerAcquisitionLinkVerify == null) {
            return;
        }
        List<LandingPageWechatCustomerService> wechatCustomerServices = landingPageWechatCustomerServiceService.listByIds(ids);
        if (CollectionUtils.isEmpty(wechatCustomerServices)) {
            return;
        }
        String agentId = TenantContextHolder.get();
        //获取设置了userid的客服
        Set<LandingPageWechatCustomerService> existUserIds = wechatCustomerServices.stream().filter(e -> StringUtils.isNotBlank(e.getWechatUserId())).collect(Collectors.toSet());
        Optional.ofNullable(existUserIds).filter(e -> !CollectionUtils.isEmpty(e)).orElseThrow(() -> new RestException("所选客服未设置企微成员信息!"));
        Set<String> corpIds = existUserIds.stream().map(LandingPageWechatCustomerService::getCorpId).collect(Collectors.toSet());
        //查询企微信息
        Map<String, EnterpriseWechat> enterpriseWechatMap = corpIds.stream().map(e -> {
            //查询企微缓存信息或数据库记录
            EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatCacheByCorpId(e);
            return enterpriseWechat;
        }).filter(e -> Objects.nonNull(e)).collect(Collectors.toMap(EnterpriseWechat::getCorpid, Function.identity()));
        if (CollectionUtils.isEmpty(enterpriseWechatMap)) {
            return;
        }
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) requestAttributes;
        HttpServletRequest request = servletRequestAttributes.getRequest();
        String ip = WebUtils.getIpAddr(request);
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User user = (User) authentication.getPrincipal();
        wechatCustomerServices.forEach(wechatCustomerService -> {
                EnterpriseWechat enterpriseWechat = enterpriseWechatMap.get(wechatCustomerService.getCorpId());
                if (Objects.isNull(enterpriseWechat)) {
                    return;
                }
                //生成获取链接
                afreshCustomerAcquisition(wechatCustomerService, advertiserAccountGroupId, enterpriseWechat, wechatCustomerAcquisitionLinkVerify, user, ip, linkName, dto.getAcquisitionChooseEnum());
            }
        );
    }

    /**
     * 移除可见范围的成员
     * 删除对应获客链接
     *
     * @param customerAcquisitionDeleteDto
     */
    public void deleteCustomerAcquisition(CustomerAcquisitionDeleteDto customerAcquisitionDeleteDto) {
        List<LandingPageWechatCustomerService> changeUpdateWechatCustomerServices = customerAcquisitionDeleteDto.getChangeUpdateWechatCustomerServices();
        if (CollectionUtils.isEmpty(changeUpdateWechatCustomerServices)) {
            return;
        }
        EnterpriseWechat enterpriseWechat = customerAcquisitionDeleteDto.getEnterpriseWechat();
        if (enterpriseWechat == null) {
            return;
        }
        List<String> userIds = changeUpdateWechatCustomerServices.stream().filter(e -> StringUtils.isNotBlank(e.getWechatUserId())).map(LandingPageWechatCustomerService::getWechatUserId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(userIds)) {
            workWechatCustomerAcquisitionLinkService.update(new LambdaUpdateWrapper<WorkWechatCustomerAcquisitionLink>()
                .set(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLinkId, null)
                .set(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLink, null)
                .set(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLinkReason, null)
                .set(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLinkStatus, WechatCustomerAcquisitionLinkStatus.EMPTY)
                .set(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLinkVerify, SwitchStatus.CLOSE)
                .eq(WorkWechatCustomerAcquisitionLink::getCorpId, enterpriseWechat.getCorpid())
                .in(WorkWechatCustomerAcquisitionLink::getUserId, userIds));
        }
        String accessToken = enterpriseWechat.getAccessToken();
        //过滤出有获客链接的客服
        changeUpdateWechatCustomerServices = changeUpdateWechatCustomerServices.stream().filter(e -> StringUtils.isNotBlank(e.getWechatCustomerAcquisitionLinkId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(changeUpdateWechatCustomerServices)) {
            return;
        }
        //删除远程获客链接
        changeUpdateWechatCustomerServices.stream().forEach(e -> {
            CustomerAcquisitionRequestBody customerAcquisitionRequestBody = new CustomerAcquisitionRequestBody().setLinkId(e.getWechatCustomerAcquisitionLinkId());
            workWechatCustomerAcquisitionClient.deleteCustomerAcquisition(accessToken, customerAcquisitionRequestBody);
            //保存操作日志
            saveAcquisitionLinkDeletedLogAfterRemoveVisitPermission(e);
        });
    }

    /**
     * 成员被移除可见范围, 保存删除获客链接操作的日志
     *
     * @param landingPageWechatCustomerService 客服信息
     */
    public void saveAcquisitionLinkDeletedLogAfterRemoveVisitPermission(LandingPageWechatCustomerService landingPageWechatCustomerService) {
        try {
            //保存删除获客链接的操作日志
            if (Objects.nonNull(landingPageWechatCustomerService)) {
                String userId = landingPageWechatCustomerService.getWechatUserId();
                String corpId = landingPageWechatCustomerService.getCorpId();
                String linkId = landingPageWechatCustomerService.getWechatCustomerAcquisitionLinkId();
                String operDesc = "成员被移除可见范围, 删除获客链接，linkId: " + linkId;
                User user = new User();
                user.setId(0L).setOperationRole(OperationRole.COMMON).setUsername("系统自动操作");
                this.saveAcquisitionLinkDeletedLog(userId, corpId, operDesc, user, null, Arrays.asList(landingPageWechatCustomerService), linkId, false);
            }
        } catch (Exception e) {
            log.error("成员被移除可见范围, 保存删除获客链接的操作日志异常, landingPageWechatCustomerService = {}", e, JSONObject.toJSONString(landingPageWechatCustomerService));
        }
    }


    /**
     * 校验参数
     *
     * @param qrCodeShowImageDto
     */
    private void checkCustomerAcquisitionParam(QrCodeShowImageDto qrCodeShowImageDto) {
        String pid = qrCodeShowImageDto.getPid();
        String uid = qrCodeShowImageDto.getUid();
        Long wechatCustomerServiceGroupId = qrCodeShowImageDto.getWechatCustomerServiceGroupId();
        //参数校验
        if (StringUtils.isBlank(pid) || StringUtils.isBlank(uid) || ObjectUtils.isEmpty(wechatCustomerServiceGroupId)) {
            throw new RestException(ErrorConstants.ERROR_CUSTOMER_ACQUISITION_PARAM);
        }
    }

    /**
     * 获取获客链接
     *
     * @param qrCodeShowImageDto
     * @param landingPageWechatCustomerServiceList
     * @return
     */
    @SneakyThrows
    private EnterpriseWechatCustomerAcquisitionDto getCustomerAcquisitionLink(QrCodeShowImageDto qrCodeShowImageDto, List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList) {
        log.info("点击动作类型:{}", qrCodeShowImageDto.getClickActionType());
        String pid = qrCodeShowImageDto.getPid();
        String uid = qrCodeShowImageDto.getUid();
        final String ua = qrCodeShowImageDto.getUa();
        final String url = qrCodeShowImageDto.getUrl();
        FrontComponentType componentType = qrCodeShowImageDto.getComponentType();
        Long advertiserAccountGroupId = qrCodeShowImageDto.getAdvertiserAccountGroupId();
        Long wechatCustomerServiceGroupId = qrCodeShowImageDto.getWechatCustomerServiceGroupId();
        String wechatCustomerAcquisitionLink = null;
        String wechatCustomerAcquisitionLinkId = null;
        Long wechatCustomerServiceId = null;
        String principalName = null;
        String wechatUserName = null;
        String wechatAvatar = null;
        //同一获客链接与之前动态二维码同一二维码区分开
        //同一客户展示同一获客链接
        LandingPageWechatCustomerServiceRedisDto serviceRedisDto = landingPageWechatCustomerServiceService.getCustomerAcquisitionSameCustomer(advertiserAccountGroupId, wechatCustomerServiceGroupId, uid, landingPageWechatCustomerServiceList);
        if (Objects.nonNull(serviceRedisDto)) {
            wechatCustomerAcquisitionLink = serviceRedisDto.getWechatCustomerAcquisitionLink();
            wechatCustomerAcquisitionLinkId = serviceRedisDto.getWechatCustomerAcquisitionLinkId();
            wechatCustomerServiceId = serviceRedisDto.getId();
            //查询客服归属企业的企业微信名称
            if (Objects.equals(qrCodeShowImageDto.getClickActionType(), ClickActionType.IDENTIFY_AROUSAL_QRCODE) || Objects.equals(qrCodeShowImageDto.getClickActionType(), ClickActionType.DIRECT_AROUSAL)) {
                principalName = this.getPrincipalNameByCorpId(serviceRedisDto.getCorpId());
                wechatUserName = serviceRedisDto.getWechatName();
                wechatAvatar = serviceRedisDto.getWechatAvatar();
            }
        } else {
            //根据权重算法获得客服数据
            log.info("获取获客链接,根据权重算法获得客服数据,客服分组ID = {}", wechatCustomerServiceGroupId);
            LandingPageWechatCustomerServiceRedisDto customerServiceRedisDto = landingPageWechatCustomerServiceService.getWechatCustomerServiceByWeight(wechatCustomerServiceGroupId, landingPageWechatCustomerServiceList);
            if (Objects.nonNull(customerServiceRedisDto)) {
                qrCodeShowImageDto.setWechatCustomerServiceId(customerServiceRedisDto.getId());
                qrCodeShowImageDto.setEnterpriseWechatCorpId(customerServiceRedisDto.getCorpId());
                qrCodeShowImageDto.setWechatCustomerServiceUserId(customerServiceRedisDto.getLandingPageWechatCustomerServiceWechatUserId());
                //存入缓存
                landingPageWechatCustomerServiceService.saveCustomerAcquisitionIdentifyQrCodeInfo(qrCodeShowImageDto);
                wechatCustomerAcquisitionLink = customerServiceRedisDto.getWechatCustomerAcquisitionLink();
                wechatCustomerAcquisitionLinkId = customerServiceRedisDto.getWechatCustomerAcquisitionLinkId();
                wechatCustomerServiceId = customerServiceRedisDto.getId();
                if (Objects.equals(qrCodeShowImageDto.getClickActionType(), ClickActionType.IDENTIFY_AROUSAL_QRCODE) || Objects.equals(qrCodeShowImageDto.getClickActionType(), ClickActionType.DIRECT_AROUSAL)) {
                    wechatUserName = customerServiceRedisDto.getWechatName();
                    principalName = this.getPrincipalNameByCorpId(customerServiceRedisDto.getCorpId());
                    wechatAvatar = customerServiceRedisDto.getWechatAvatar();
                }
            }
        }
        EnterpriseWechatCustomerAcquisitionDto customerAcquisitionDto = new EnterpriseWechatCustomerAcquisitionDto();
        if (StringUtils.isNotBlank(wechatCustomerAcquisitionLink)) {
            String stateUid = CustomerCquisitionPrefixEnum.IS_USE.getPrefix() + RandomUtil.randomString(10);
            String key = RedisConstant.LANDING_PAGE_WECHAT_CUSTOMER_ACQUISITION_STATE_CONFIG + stateUid;
            //将参数缓存至redis
            CustomerAcquisitionStateDto customerAcquisitionStateDto = new CustomerAcquisitionStateDto().setPid(pid).setComponentType(componentType);
            objectRedisTemplate.opsForValue().set(key, customerAcquisitionStateDto, enterpriseWechatCustomerAcquisitionConfig.getHour(), TimeUnit.HOURS);
            if (wechatCustomerServiceId != null) {
                objectRedisTemplate.opsForValue().set(RedisConstant.LANDING_PAGE_WECHAT_CUSTOMER_ACQUISITION_STATE_SERVICE_ID + stateUid, wechatCustomerServiceId.toString(), enterpriseWechatCustomerAcquisitionConfig.getHour(), TimeUnit.HOURS);
            }
            //判断是否携带http
            if (!wechatCustomerAcquisitionLink.startsWith("http://") && !wechatCustomerAcquisitionLink.startsWith("https://")) {
                wechatCustomerAcquisitionLink = "https://" + wechatCustomerAcquisitionLink;
            }
            //拼接参数
            wechatCustomerAcquisitionLink = wechatCustomerAcquisitionLink + "?customer_channel=" + stateUid;

            //#52848 获客助手针对今日头条进行跳转优化//https://ones.yiye.ai/project/#/team/WtsduTeT/task/PHVqcQyFHRkcvA4I
            if (StringUtils.isNotBlank(ua) && StringUtils.isNotBlank(url)) {
                boolean isIosType = CommonUtil.checkSystemType(ua, SystemType.IOS.getKey());
                Platform platform = UrlUtils.getPlatformSourcesByUrlOrRefer(url, null);
                FlowSource flowSource = FlowSource.getFlowSourceByUaStr(platform, qrCodeShowImageDto.getUa(), agentConf.getFsDynamicFilterMap());
                if (isIosType && !Objects.isNull(flowSource) && Arrays.asList(FlowSource.JIN_RI_TOU_TIAO, FlowSource.JIN_RI_TOU_TIAO_JI_SU_BAN).contains(flowSource)) {
                    wechatCustomerAcquisitionLink = "weixin://biz/ww/profile/" + URLEncoder.encode(wechatCustomerAcquisitionLink, CommonUtil.UTF_8);
                }
            }

            customerAcquisitionDto.setCustomerChannel(stateUid);
            //修改pv信息
            EnterpriseWechatCustomerAcquisitionMessageDto enterpriseWechatCustomerAcquisitionMessageDto = new EnterpriseWechatCustomerAcquisitionMessageDto()
                .setPid(pid)
                .setWechatCustomerServiceId(wechatCustomerServiceId)
                .setClickCustomerAcquisitonLink(wechatCustomerAcquisitionLink)
                .setClickCustomerAcquisitonLinkId(wechatCustomerAcquisitionLinkId)
                .setCustomerAcquisitonParamState(stateUid)
                .setCustomerAcquisitonParam(JSONObject.parseObject(JSONObject.toJSONString(customerAcquisitionStateDto)));
            enterpriseWechatCustomerAcquisitionSender.modifyCustomerAcquisitionPvInfo(enterpriseWechatCustomerAcquisitionMessageDto);
        }
        log.info("获取获客链接, principalName = {}, wechatUserName = {}, url={}", principalName, wechatUserName, url);
        customerAcquisitionDto
            .setWechatCustomerAcquisitionLink(wechatCustomerAcquisitionLink)
            .setPrincipleName(principalName)
            .setWechatUserName(wechatUserName)
            .setWechatAvatar(wechatAvatar);
        return customerAcquisitionDto;
    }

    /**
     * 获取城市区域码情况下获取企业微信名称
     *
     * @param landingPageWechatCustomerCityCodeId 城市区域码
     * @param wechatCustomerServiceId             客服ID
     * @return 企业微信名称
     */
    public String getPrincipalNameByCustomerServiceId(Long landingPageWechatCustomerCityCodeId, Long wechatCustomerServiceId) {
        String principalName = null;
        log.info("获取城市区域码情况下获取企业微信名称,landingPageWechatCustomerCityCodeId = {},wechatCustomerServiceId = {}", landingPageWechatCustomerCityCodeId, wechatCustomerServiceId);
        if (Objects.nonNull(landingPageWechatCustomerCityCodeId) && Objects.nonNull(wechatCustomerServiceId)) {
            String key = RedisConstant.LANDING_PAGE_CITY_CODE_CORP_ID + landingPageWechatCustomerCityCodeId;
            String result = (String) objectRedisTemplate.opsForValue().get(key);
            if (StringUtils.isNotBlank(result)) {
                log.info("从缓存中获取城市区域码情况下获取企业微信名称,landingPageWechatCustomerCityCodeId = {},wechatCustomerServiceId = {}", landingPageWechatCustomerCityCodeId, wechatCustomerServiceId);
                principalName = result;
            } else {
                log.info("从数据库中获取城市区域码情况下获取企业微信名称,landingPageWechatCustomerCityCodeId = {},wechatCustomerServiceId = {}", landingPageWechatCustomerCityCodeId, wechatCustomerServiceId);
                LandingPageWechatCustomerService landingPageWechatCustomerService = landingPageWechatCustomerServiceService.getById(wechatCustomerServiceId);
                if (Objects.nonNull(landingPageWechatCustomerService)) {
                    principalName = this.getPrincipalNameByCorpId(landingPageWechatCustomerService.getCorpId());
                    if (StringUtils.isNotBlank(principalName)) {
                        objectRedisTemplate.opsForValue().set(key, principalName, 2, TimeUnit.HOURS);
                    }
                }
            }
        }
        return principalName;
    }

    /**
     * 微信客服机器人，获取获客助手链接
     *
     * @param qrCodeShowImageDto                   入参
     * @param landingPageWechatCustomerServiceList 微信客服列表
     * @return 获客助手链接
     */
    @SneakyThrows
    public RobotCustomerAcquisitionLinkDTO getCustomerAcquisitionLinkForRobot(QrCodeShowImageDto qrCodeShowImageDto, List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList, String externalUserId, String corpId) {

        log.info("微信客服机器人，欢迎语菜单获取获客助手链接, qrCodeShowImageDto = {}", JSONObject.toJSON(qrCodeShowImageDto));
        if (Objects.nonNull(qrCodeShowImageDto) && !landingPageWechatCustomerServiceList.isEmpty()) {
            RobotCustomerAcquisitionLinkDTO dto = new RobotCustomerAcquisitionLinkDTO();
            Long advertiserAccountGroupId = qrCodeShowImageDto.getAdvertiserAccountGroupId();
            Long wechatCustomerServiceGroupId = qrCodeShowImageDto.getWechatCustomerServiceGroupId();
            String wechatExternalUserid = qrCodeShowImageDto.getWechatExternalUserid();
            String wechatCustomerAcquisitionLink = null;
            String wechatCustomerAcquisitionLinkId = null;
            Long wechatCustomerServiceId = null;
            String landingPageWechatCustomerServiceWechatUserId = null;
            String pid = qrCodeShowImageDto.getPid();
            LandingPageWechatCustomerServiceRedisDto serviceRedisDto = landingPageWechatCustomerServiceService.getCustomerAcquisitionSameCustomerForRobot(advertiserAccountGroupId, wechatCustomerServiceGroupId, wechatExternalUserid, landingPageWechatCustomerServiceList);
            if (Objects.nonNull(serviceRedisDto)) {
                wechatCustomerAcquisitionLink = serviceRedisDto.getWechatCustomerAcquisitionLink();
                wechatCustomerAcquisitionLinkId = serviceRedisDto.getWechatCustomerAcquisitionLinkId();
                wechatCustomerServiceId = serviceRedisDto.getId();
                landingPageWechatCustomerServiceWechatUserId = serviceRedisDto.getLandingPageWechatCustomerServiceWechatUserId();
            } else {
                log.info("微信客服机器人，获取获客助手链接,根据权重算法获得客服数据,wechatCustomerServiceGroupId = {}", wechatCustomerServiceGroupId);
                //根据权重算法获得客服数据
                LandingPageWechatCustomerServiceRedisDto customerServiceRedisDto = landingPageWechatCustomerServiceService.getWechatCustomerServiceByWeight(wechatCustomerServiceGroupId, landingPageWechatCustomerServiceList);
                if (Objects.nonNull(customerServiceRedisDto)) {
                    qrCodeShowImageDto.setWechatCustomerServiceId(customerServiceRedisDto.getId());
                    qrCodeShowImageDto.setWechatCustomerServiceUserId(customerServiceRedisDto.getLandingPageWechatCustomerServiceWechatUserId());
                    qrCodeShowImageDto.setEnterpriseWechatCorpId(customerServiceRedisDto.getCorpId());
                    qrCodeShowImageDto.setWechatCustomerAcquisitionLink(customerServiceRedisDto.getWechatCustomerAcquisitionLink());
                    //存入缓存
                    landingPageWechatCustomerServiceService.saveRobotCustomerAcquisitionIdentifyQrCodeInfo(qrCodeShowImageDto);
                    wechatCustomerAcquisitionLink = customerServiceRedisDto.getWechatCustomerAcquisitionLink();
                    wechatCustomerAcquisitionLinkId = customerServiceRedisDto.getWechatCustomerAcquisitionLinkId();
                    landingPageWechatCustomerServiceWechatUserId = customerServiceRedisDto.getLandingPageWechatCustomerServiceWechatUserId();
                    wechatCustomerServiceId = customerServiceRedisDto.getId();
                }
            }
            if (StringUtils.isNotBlank(wechatCustomerAcquisitionLink)) {
                String stateUid = RobotCustomerAcquisitionPrefixEnum.IS_USE_FOR_ROBOT.getPrefix() + RandomUtil.randomString(10);
                String key = RedisConstant.ROBOT_WECHAT_CUSTOMER_ACQUISITION_STATE_CONFIG + stateUid;
                //将参数缓存至redis
                CustomerAcquisitionStateDto customerAcquisitionStateDto = new CustomerAcquisitionStateDto().setPid(pid).setExternalUserId(externalUserId).setCorpId(corpId);
                objectRedisTemplate.opsForValue().set(key, customerAcquisitionStateDto, enterpriseWechatCustomerAcquisitionConfig.getHour(), TimeUnit.HOURS);
                //判断是否携带http
                if (!wechatCustomerAcquisitionLink.startsWith("http://") && !wechatCustomerAcquisitionLink.startsWith("https://")) {
                    wechatCustomerAcquisitionLink = "https://" + wechatCustomerAcquisitionLink;
                }
                //拼接参数
                wechatCustomerAcquisitionLink = wechatCustomerAcquisitionLink + "?customer_channel=" + stateUid;
                //修改pv信息
                EnterpriseWechatCustomerAcquisitionMessageDto enterpriseWechatCustomerAcquisitionMessageDto = new EnterpriseWechatCustomerAcquisitionMessageDto()
                    .setPid(pid)
                    .setWechatCustomerServiceId(wechatCustomerServiceId)
                    .setClickCustomerAcquisitonLink(wechatCustomerAcquisitionLink)
                    .setClickCustomerAcquisitonLinkId(wechatCustomerAcquisitionLinkId)
                    .setCustomerAcquisitonParamState(stateUid);
                dto.setWechatCustomerAcquisitionLink(wechatCustomerAcquisitionLink);
                dto.setWechatCustomerServiceId(wechatCustomerServiceId);
                dto.setState(stateUid);
                dto.setLandingPageWechatCustomerServiceWechatUserId(landingPageWechatCustomerServiceWechatUserId);
                enterpriseWechatCustomerAcquisitionSender.modifyCustomerAcquisitionPvInfoForRobot(enterpriseWechatCustomerAcquisitionMessageDto);
            }
            return dto;
        }
        return null;
    }


    /**
     * 根据企业微信corpId获取企业微信名称
     *
     * @param corpId 企业微信corpId
     * @return 企业微信名称
     */
    public String getPrincipalNameByCorpId(String corpId) {
        try {
            log.info("根据企业微信corpId获取企业微信名称, corpId = {}", corpId);
            if (StringUtils.isNotBlank(corpId)) {
                return enterpriseWechatService.getPrincipalNameByCorpId(corpId);
            }
        } catch (Exception e) {
            log.error("根据企业微信corpId获取企业微信名称, corpId = {}", corpId, e);
        }
        return null;
    }

    /**
     * 检查获客链接是否异常
     */
    public void checkCustomerAcquisition(CustomerAcquisitionDTO customerAcquisitionDTO) {

        try {
            //检查state的合法性
            log.info("customerAcquisitionDTO = {}", JSONObject.toJSONString(customerAcquisitionDTO));
            if (StringUtils.isNotBlank(customerAcquisitionDTO.getState())) {

                //先检查是否是不需要校验的state参数
                if (StringUtils.isNotBlank(customerAcquisitionDTO.getState())) {
                    boolean result = this.checkOtherState(customerAcquisitionDTO.getState());
                    if (result) {
                        //直接跳过,不进行后续的检查
                        return;
                    }
                }

                Boolean checkState = this.checkState(customerAcquisitionDTO);
                log.info("state=[{}],检查state的合法性，结果 = [{}]", customerAcquisitionDTO.getState(), checkState);
                if (checkState) {
                    //调用企业微信接口，判断是否有用过这个获客链接加过粉的记录
                    //Boolean checkRepeat = this.checkCustomerByLink(customerAcquisitionDTO);
                    customerAcquisitionDTO.setStateIsLegal(true);
                    Boolean checkRepeat = this.checkRepeatCustomerStateNull(customerAcquisitionDTO);
                    log.info("state参数[{}]合法，判断是否存在重复添加同一个获客链接的情况，结果:[{}],[{}]", customerAcquisitionDTO.getState(), checkRepeat, (checkRepeat ? "该获客链接已经存在重复加粉客资" : "该获客链接不存在重复加粉客资"));
                } else {
                    //state参数不合法，直接重新创建
                    log.info("state参数[{}]不合法,直接重新创建", customerAcquisitionDTO.getState());
                    this.createNewCustomerAcquisitionLink(customerAcquisitionDTO);
                }
            } else {
                //state为空
                log.info("获客链接防刷,state = [{}],state参数为空", customerAcquisitionDTO.getState());
                this.checkRepeatCustomerStateNull(customerAcquisitionDTO);
            }
        } catch (Exception e) {
            log.error("检查获客链接防刷异常", e);
        }
    }

    /**
     * 调用企业微信接口，判断是否存在重复加粉的情况
     */
    public Boolean checkRepeatCustomerStateNull(CustomerAcquisitionDTO customerAcquisitionDTO) {

        String agentId = TenantContextHolder.get();
        //根据获客链接ID查询加粉的客户列表
        List<LandingPageWechatCustomerService> lpwcsList = customerAcquisitionDTO.getLpwcsList();
        log.info("agentId = [{}], state = [{}], lpwcsList =[{}]", agentId, customerAcquisitionDTO.getState(), JSONObject.toJSONString(lpwcsList));

        List<LandingPageWechatCustomerService> finalList = new ArrayList<>();

        if (!lpwcsList.isEmpty()) {
            //去重
            List<LandingPageWechatCustomerService> distinctLpwcsList = lpwcsList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(LandingPageWechatCustomerService::getWechatUserId))),
                ArrayList::new));

            log.info("调取企业微信接口,distinctLpwcsList = {}", JSONObject.toJSONString(distinctLpwcsList));

            if (!distinctLpwcsList.isEmpty()) {
                distinctLpwcsList.stream().filter(item -> StringUtils.isNotBlank(item.getWechatCustomerAcquisitionLinkId())).forEach(item -> {
                    String key = RedisConstant.CUSTOMER_ACQUISITION_LINK_CHECK_PREFIX.concat(agentId).concat(":").concat(item.getWechatCustomerAcquisitionLinkId());
                    //查询缓存中是否已经处理过该获客链接
                    String value = stringRedisTemplate.opsForValue().get(key);
                    if (StringUtils.isNotBlank(value) && Objects.equals(value, "alreadyResolved")) {
                        //24h内已经删除成功过，不再进行处理
                        log.info("checkRepeatCustomerStateNull,获客链接ID:[{}],24h内已经删除成功过，不再进行处理", item.getWechatCustomerAcquisitionLinkId());
                        return;
                    }
                    EnterpriseWechat weChatConfig = this.getAccessToken(item.getCorpId());
                    if (Objects.isNull(weChatConfig)) {
                        log.info("agentId = [{}], corpId = {},advertiserAccountGroupId = [{}],获取的企业微信配置 weChatConfig = [{}]", agentId, item.getCorpId(), item.getAdvertiserAccountGroupId(), JSONObject.toJSONString(weChatConfig));
                        return;
                    }
                    log.info("state参数为空,获取token,weChatConfig=[{}]", JSONObject.toJSONString(weChatConfig));
                    Boolean res = this.checkExistCustomer(item, weChatConfig);
                    if (res) {
                        finalList.add(item);
                    }
                });
                log.info("finalList集合,size = [{}]", finalList.size());
                if (finalList.size() > 0) {
                    customerAcquisitionDTO.setLpwcsList(finalList);
                    this.createNewCustomerAcquisitionLink(customerAcquisitionDTO);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断是否存在重复加粉的情况
     *
     * @param customerService  微信客服信息
     * @param enterpriseWechat 企业微信信息
     * @return 判断的结果, true表示存在重复, false表示没有重复
     */
    public Boolean checkExistCustomer(LandingPageWechatCustomerService customerService, EnterpriseWechat enterpriseWechat) {
        RLock fairLock = null;
        try {
            String wechatCustomerAcquisitionLinkId = customerService.getWechatCustomerAcquisitionLinkId();
            if (StringUtils.isNotBlank(wechatCustomerAcquisitionLinkId)) {
                //重复加粉的key
                String repeatKey = RedisConstant.CUSTOMER_ACQUISITION_REPEAT_KEY + wechatCustomerAcquisitionLinkId;

                String key = RedisConstant.CUSTOMER_ACQUISITION_BRUSH_DETECTION_KEY + wechatCustomerAcquisitionLinkId;
                //获取锁 加锁是为了避免出现并发问题
                fairLock = redissonClient.getFairLock(wechatCustomerAcquisitionLinkId);
                //尝试加锁，最多等待10秒,10分钟过期
                boolean res = fairLock.tryLock(10, 30, TimeUnit.SECONDS);
                if (!res) {
                    log.info("获客链接防刷检测，获取锁失败,linkId = [{}]", wechatCustomerAcquisitionLinkId);
                    throw new RestException("获客链接防刷检测，获取锁失败,获客链接:".concat(wechatCustomerAcquisitionLinkId));
                }
                String repeatValue = stringRedisTemplate.opsForValue().get(repeatKey);
                //判断是否已经校验过了
                if (StringUtils.isNotBlank(repeatValue) && Objects.equals(repeatValue, "EXISTS")) {
                    log.info("获客链接ID = [{}],已经校验过存在重复加粉情况,直接返回true", wechatCustomerAcquisitionLinkId);
                    return true;
                }
                String agentId = TenantContextHolder.get();
                String nextCursor = null;

                //数据库不存在的情况下，调用企业微信API，查询全部的加粉信息
                do {
                    log.info("调用企业微信查询获客链接客户列表,分页大小 = [{}],nextCursor = [{}],linkId = [{}]", customerAcquisitionConfig.getPageSize(), nextCursor, customerService.getWechatCustomerAcquisitionLinkId());
                    CustomerAcquisitionCustomerResponseBody responseBody = workWechatCustomerAcquisitionClient.customerAcquisitionCustomerList(enterpriseWechat.getAccessToken(), new CustomerAcquisitionListRequestBody().setLinkId(customerService.getWechatCustomerAcquisitionLinkId()).setLimit(customerAcquisitionConfig.getPageSize()).setCursor(nextCursor));
                    log.info("linkId = [{}],查询接口,responseBody = [{}]", customerService.getWechatCustomerAcquisitionLinkId(), JSONObject.toJSONString(responseBody));
                    if (Objects.nonNull(responseBody)) {
                        nextCursor = responseBody.getNextCursor();
                        log.info("linkId = [{}],接口返回的nextCursor = [{}]", customerService.getWechatCustomerAcquisitionLinkId(), nextCursor);
                        if (!responseBody.getCustomerList().isEmpty()) {
                            //进行入库，有唯一键防重
                            if (StringUtils.isNotBlank(nextCursor)) {
                                //查询数据库是否存在改下标，已经存在的，说明是历史加粉数据，后面的数据不需要再入库，企业微信获客链接最新加粉的数据，会排在头部,有改动的时候，加粉观察一下现象
                                Boolean nextCursorExistsFlag = customerAcquisitionCustomerRecordService.checkNextCursorExists(wechatCustomerAcquisitionLinkId, nextCursor);
                                if (!nextCursorExistsFlag) {
                                    customerAcquisitionCustomerRecordService.addCustomerRecord(responseBody, enterpriseWechat, agentId, customerService.getWechatCustomerAcquisitionLinkId());
                                } else {
                                    //下标存在的话,结束循环
                                    nextCursor = null;
                                }
                            }
                        }
                    }
                } while (StringUtils.isNotBlank(nextCursor));

                //查询数据库
                Boolean checkFirstRes = customerAcquisitionCustomerRecordService.queryRepeatCustomer(customerService.getWechatUserId(), customerService.getWechatCustomerAcquisitionLinkId());
                log.info("查询数据库,checkFirstRes = [{}]", checkFirstRes);
                if (checkFirstRes) {
                    //说明历史数据已经存在重复的
                    stringRedisTemplate.opsForValue().set(repeatKey, "EXISTS", 7, TimeUnit.DAYS);
                    return true;
                }
            }
        } catch (MarketingApiException e) {
            String linkId = Objects.nonNull(customerService) ? customerService.getWechatCustomerAcquisitionLinkId() : null;
            log.info("获客链接防刷检api异常,linkId = [{}]", linkId, e);
            Integer errcode = e.getErrcode();
            if (Objects.nonNull(errcode)) {
                EnterpriseWechatGlobalErrorCode enterpriseWechatGlobalErrorCode = EnumUtil.getByCode(errcode, EnterpriseWechatGlobalErrorCode.class);
                Optional.of(enterpriseWechatGlobalErrorCode).ifPresent(error -> {
                    if (EnterpriseWechatGlobalErrorCode.INVALID_PARAMETER.equals(error)) {
                        log.info("参数不合法,请检查linkId是否已经失效! linkeId:{}", linkId);
                    }
                });
            }
        } catch (Exception e) {
            log.error("获客链接防刷检,linkId = [{}],出现异常", Objects.nonNull(customerService) ? customerService.getWechatCustomerAcquisitionLinkId() : null, e);
        } finally {
            try {
                if (fairLock != null) {
                    fairLock.unlock();
                }
            } catch (Exception e) {
                log.error("获客链接防刷检测-释放redis锁异常，customerService={}", JSONObject.toJSONString(customerService), e);
            }
        }
        return false;
    }


    /**
     * 检查只有一页情况下，是否存在重复加粉
     *
     * @param customerList    待检查的加粉客户列表
     * @param customerService 微信客服信息
     * @return 判断结果
     */
    public Boolean checkThisPage(List<CustomerAcquisitionCustomerItemResponseBody> customerList, LandingPageWechatCustomerService customerService) {
        log.info("获客链接ID:[{}],加粉数量 = [{}]", customerService.getWechatCustomerAcquisitionLinkId(), customerList.size());
        long count = customerList.stream().filter(item -> StringUtils.isBlank(item.getState())).count();
        log.info("获客链接ID:[{}],state参数为空的加粉数量:[{}]", customerService.getWechatCustomerAcquisitionLinkId(), count);
        if (count > 0) {
            return true;
        } else {
            //判断state不为空的集合中，是否存在重复添加同一个获客链接的情况
            //按照外部联系人userId先去重
            List<CustomerAcquisitionCustomerItemResponseBody> userIdList = customerList.stream().filter(item -> StringUtils.isNotBlank(item.getState())).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(distinctByExternalUseridFunction()))), ArrayList::new));
            List<CustomerAcquisitionCustomerItemResponseBody> stateList = customerList.stream().filter(item -> StringUtils.isNotBlank(item.getState())).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(distinctByStateFunction()))), ArrayList::new));
            log.info("不重复的外部联系人数量 = [{}]， 不重复的获客链接state的数量 = [{}]", userIdList.size(), stateList.size());
            log.info("获客链接ID = [{}]监测结果:[{}]", customerService.getWechatCustomerAcquisitionLinkId(), userIdList.size() > stateList.size() ? "不合法" : "合法");
            return userIdList.size() > stateList.size();
        }
    }

    /**
     * 根据ExternalUserid去重的Function
     */
    public static Function<CustomerAcquisitionCustomerItemResponseBody, String> distinctByExternalUseridFunction() {
        return (CustomerAcquisitionCustomerItemResponseBody dish) -> dish.getExternalUserid();
    }

    /**
     * 根据State去重的Function
     */
    public static Function<CustomerAcquisitionCustomerItemResponseBody, String> distinctByStateFunction() {
        return (CustomerAcquisitionCustomerItemResponseBody dish) -> dish.getState();
    }


    /**
     * 检查state的合法性
     * 判断标准： 获客链接中的state为空/不符合一叶特征、残缺补全都算异常
     * 备注：state生成规则：CustomerCquisitionPrefixEnum.IS_USE.getPrefix() + RandomUtil.randomString(10);
     *
     * @return 检查结果
     */
    public Boolean checkState(CustomerAcquisitionDTO customerAcquisitionDTO) {
        Boolean result = false;
        if (Objects.nonNull(customerAcquisitionDTO)) {
            String state = customerAcquisitionDTO.getState();
            //检查前缀
            if (StringUtils.isNotBlank(state) && state.startsWith(CustomerCquisitionPrefixEnum.IS_USE.getPrefix())) {
                //检查后缀长度是否为10
                String[] res = state.split(CustomerCquisitionPrefixEnum.IS_USE.getPrefix());
                if (res.length > 1) {
                    result = (res[1].length() == 10);
                }
            } else {
                //增加判断微信客服机器人里面地获客链接判断
                result = this.checkRobotCustomerAcquisitionState(state);
            }
        }
        return result;
    }

    /**
     * 检查是否是不需要校验的state参数
     *
     * @param state 自归因参数
     * @return 如果合法，返回true，反之，返回false
     */
    public Boolean checkOtherState(String state) {
        //以下几种state参数不进行防刷检查
        if (StringUtils.contains(state, String.format(OfficialContactWayPrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))
            || StringUtils.contains(state, String.format(IdenticalOfficialContactWayPrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))
            || StringUtils.contains(state, String.format(ContactMeQRCodeStatePrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))
            || StringUtils.contains(state, String.format(RobotContactWayPrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))
            || StringUtils.contains(state, String.format(IdenticalOfficialMultContactWayPrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))
            || StringUtils.contains(state, String.format(OfficialMultContactWayPrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))) {
            return true;
        }
        return false;
    }

    /**
     * 判断微信机器人发送获客链接地state参数是否合法
     *
     * @param state 自归因参数
     * @return 判断结果
     */
    public Boolean checkRobotCustomerAcquisitionState(String state) {
        Boolean result = false;
        if (StringUtils.isNotBlank(state) && state.startsWith(RobotCustomerAcquisitionPrefixEnum.IS_USE_FOR_ROBOT.getPrefix())) {
            String[] res = state.split(RobotCustomerAcquisitionPrefixEnum.IS_USE_FOR_ROBOT.getPrefix());
            if (res.length > 1) {
                result = (res[1].length() == 10);
            }
        }
        return result;
    }

    public Boolean checkCustomerByLink(CustomerAcquisitionDTO customerAcquisitionDTO) {
        List<LandingPageWechatCustomerService> list = customerService.queryByLink(customerAcquisitionDTO);
        return list.size() > 0;
    }

    /**
     * 重新创建获客链接，并删除旧的获客链接
     */
    public void createNewCustomerAcquisitionLink(CustomerAcquisitionDTO customerAcquisitionDTO) {

        log.info("state = [{}], stateIsLegal = [{}]", customerAcquisitionDTO.getState(), customerAcquisitionDTO.getStateIsLegal());
        //对应的客服配置
        List<LandingPageWechatCustomerService> lpwcsList = customerAcquisitionDTO.getLpwcsList();

        if (!lpwcsList.isEmpty()) {
            //去重
            List<LandingPageWechatCustomerService> distinctLpwcsList = lpwcsList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(LandingPageWechatCustomerService::getWechatUserId))),
                ArrayList::new));

            log.info("distinctLpwcsList = {}", JSONObject.toJSONString(distinctLpwcsList));

            if (distinctLpwcsList.isEmpty()) {
                return;
            }

            String agentId = TenantContextHolder.get();
            //过滤出获客链接ID不为空的客服配置
            distinctLpwcsList.stream().filter(item -> Objects.nonNull(item.getWechatCustomerAcquisitionLinkId())).forEach(e -> {
                String key = RedisConstant.CUSTOMER_ACQUISITION_LINK_CHECK_PREFIX.concat(agentId).concat(":").concat(e.getWechatCustomerAcquisitionLinkId());
                //查询缓存中是否已经处理过该获客链接
                String value = stringRedisTemplate.opsForValue().get(key);
                if (StringUtils.isNotBlank(value) && Objects.equals(value, "alreadyResolved")) {
                    //24h内已经删除成功过，不再进行处理
                    log.info("获客链接ID:[{}],24h内已经删除成功过，不再进行处理", e.getWechatCustomerAcquisitionLinkId());
                    return;
                }
                EnterpriseWechat weChatConfig = this.getAccessToken(e.getCorpId());
                log.info("agentId = [{}],corpId = {},advertiserAccountGroupId = [{}],获取企业微信配置 weChatConfig = {}", agentId, e.getCorpId(), e.getAdvertiserAccountGroupId(), JSONObject.toJSONString(weChatConfig));
                if (Objects.isNull(weChatConfig)) {
                    log.info("agentId = [{}],advertiserAccountGroupId = [{}],获取企业微信配置为空", agentId, e.getAdvertiserAccountGroupId());
                    return;
                } else {
                    customerAcquisitionDTO.setEnterpriseWechat(weChatConfig);
                }
                CustomerAcquisitionRequestItemBody customerAcquisitionRequestItemBody = new CustomerAcquisitionRequestItemBody();
                customerAcquisitionRequestItemBody.setUserList(Collections.singletonList(e.getWechatUserId()));
                //注意：企业微信接口传false表示需要验证, true表示不需要验证
                Boolean verify = !Objects.equals(e.getWechatCustomerAcquisitionLinkVerify(), SwitchStatus.OPEN);
                log.info("linkId = [{}],是否需要验证verify = [{}]", e.getWechatCustomerAcquisitionLinkId(), verify);
                String name = StringUtils.isNotBlank(e.getWechatCustomerAcquisitionLinkName()) ? e.getWechatCustomerAcquisitionLinkName() : e.getWechatName();
                if (StringUtils.isNotBlank(name) && name.length() > 16) {
                    //超过16个字符，裁剪前面16个字符
                    name = name.substring(0, 16);
                }
                CustomerAcquisitionRequestBody customerAcquisitionRequestBody = new CustomerAcquisitionRequestBody().setLinkName(name).setRange(customerAcquisitionRequestItemBody).setSkipVerify(verify);
                //创建获客链接
                CustomerAcquisitionResponseBody customerAcquisition = workWechatCustomerAcquisitionClient.createCustomerAcquisition(weChatConfig.getAccessToken(), customerAcquisitionRequestBody);
                log.info("agentId=[{}],获客链接名称=[{}],获客链接防刷检测，创建获客链接返回的结果:[{}]", agentId, e.getWechatName(), customerAcquisition);
                //记录操作记录表
                customerAcquisitionLinkRecordService.addCreateOperationRecord(customerAcquisitionRequestBody, customerAcquisition, e, customerAcquisitionDTO);
                //更新获客链接
                if (Objects.nonNull(customerAcquisition) && Objects.nonNull(customerAcquisition.getLink())) {
                    CustomerAcquisitionResponseItemBody link = customerAcquisition.getLink();
                    String linkId = link.getLinkId();
                    String url = link.getUrl();
                    //增加缓存，记录这个获客链接是一叶生成的，后续防刷的时候需要判断，只对一叶生成的获客链接进行防刷校验
                    landingPageWechatCustomerServiceRedis.cacheYiyeLinkId(linkId, agentId, e.getCorpId(), e.getId());
                    //更新公库
                    log.info("重新创建获客链接，更新公库，获客链接ID = [{}]，获客链接 = [{}]", linkId, url);
                    this.updateCommonData(link, e, customerAcquisition);
                    //更新私库的获客链接信息
                    boolean res = landingPageWechatCustomerServiceService.update(new LambdaUpdateWrapper<LandingPageWechatCustomerService>()
                        .eq(LandingPageWechatCustomerService::getId, e.getId())
                        .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkId, linkId)
                        .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLink, url)
                        .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkReason, customerAcquisition.getErrmsg())
                        .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkStatus, WechatCustomerAcquisitionLinkStatus.NORMAL));
                    //更新成功后，删除旧的获客链接
                    if (res) {
                        CustomerAcquisitionRequestBody deleteCustomerAcquisitionRequestBody = new CustomerAcquisitionRequestBody().setLinkId(e.getWechatCustomerAcquisitionLinkId());
                        BaseWorkWechatResponseBody responseBody = workWechatCustomerAcquisitionClient.deleteCustomerAcquisition(weChatConfig.getAccessToken(), deleteCustomerAcquisitionRequestBody);
                        log.info("获客链接防刷，删除获客链接,linkId = [{}], 响应的参数responseBody = [{}]", e.getWechatCustomerAcquisitionLinkId(), responseBody);
                        if (responseBody.getErrcode() == 0) {
                            //此时异常链接ID已经被删除，这里加缓存的目的，是后续定时job轮询的时候，24小时内该获客链接不需要再处理，因为删除成功后，获客链接肯定失效
                            stringRedisTemplate.opsForValue().set(key, "alreadyResolved", 24, TimeUnit.HOURS);
                            //清除原有的落地页获客链接的缓存
                            log.info("删除获客链接后，清除缓存中旧的获客链接,旧的获客链接ID = [{}], 新获客链接ID = [{}]", e.getWechatCustomerAcquisitionLinkId(), linkId);
                            landingPageWechatCustomerServiceService.deleteCacheByServiceIds(Collections.singleton(e.getId()));
                        }
                        //记录操作记录表
                        customerAcquisitionLinkRecordService.addDeleteOperationRecord(deleteCustomerAcquisitionRequestBody, responseBody, e, customerAcquisition, customerAcquisitionDTO);
                    }
                }
            });
        }
    }


    /**
     * 获取access_token
     *
     * @param corpId 企业微信id
     * @return 企业微信配置
     */
    public EnterpriseWechat getAccessToken(String corpId) {
        if (StringUtils.isNotBlank(corpId)) {
            return enterpriseWechatService.getEnterpriseWechatCacheByCorpId(corpId);
        }
        return null;
    }

    /**
     * 更新公库
     */
    public void updateCommonData(CustomerAcquisitionResponseItemBody itemBody, LandingPageWechatCustomerService landingPageWechatCustomerService, CustomerAcquisitionResponseBody customerAcquisition) {
        if (Objects.nonNull(itemBody) && Objects.nonNull(landingPageWechatCustomerService) && Objects.nonNull(customerAcquisition)) {
            WorkWechatCustomerAcquisitionLinkDTO dto = new WorkWechatCustomerAcquisitionLinkDTO();
            dto.setCorpId(landingPageWechatCustomerService.getCorpId());
            dto.setWechatCustomerAcquisitionLink(itemBody.getUrl());
            dto.setWechatCustomerAcquisitionLinkId(itemBody.getLinkId());
            dto.setWechatCustomerAcquisitionLinkStatus(WechatCustomerAcquisitionLinkStatus.NORMAL);
            dto.setWechatCustomerAcquisitionLinkVerify(landingPageWechatCustomerService.getWechatCustomerAcquisitionLinkVerify());
            dto.setUserId(landingPageWechatCustomerService.getWechatUserId());
            dto.setWechatCustomerAcquisitionLinkReason(customerAcquisition.getErrmsg())
                .setWechatCustomerAcquisitionLinkName(landingPageWechatCustomerService.getWechatCustomerAcquisitionLinkName());
            log.info("发送消息到MQ，进行更新获客链接信息,,dto = {}", JSONObject.toJSONString(dto));
            landingPageSender.sendAcquisitionLink(dto);
        }
    }

    /**
     * 获取指定企微下的全部获客链接
     */
    public void getRemoteEnterpriseContact(String corpId, String accessToken) {
        if (StringUtils.isBlank(accessToken)) {
            accessToken = enterpriseWechatService.getAccessTokenByCorpId(corpId);
        }
        List<WorkWechatCustomerAcquisitionLink> list = workWechatCustomerAcquisitionLinkService.list(Wrappers.lambdaQuery(WorkWechatCustomerAcquisitionLink.class)
            .eq(WorkWechatCustomerAcquisitionLink::getCorpId, corpId)
            .isNotNull(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLinkId));
        if (StringUtils.isBlank(accessToken) || org.apache.commons.collections4.CollectionUtils.isEmpty(list)) return;
        CustomerAcquisitionListRequestBody customerAcquisitionListRequestBody = new CustomerAcquisitionListRequestBody();
        customerAcquisitionListRequestBody.setLimit(100);
        try {
            boolean flag = true;
            Set<String> result = new HashSet<>();
            while (flag) {
                CustomerAcquisitionListResponseBody customerAcquisitionListResponseBody = workWechatCustomerAcquisitionClient.customerAcquisitionList(accessToken, customerAcquisitionListRequestBody);
                List<String> linkIdList = customerAcquisitionListResponseBody.getLinkIdList();
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(linkIdList)) {
                    flag = false;
                } else {
                    result.addAll(linkIdList);
                }
                String nextCursor = customerAcquisitionListResponseBody.getNextCursor();
                if (StringUtils.isNotBlank(nextCursor)) {
                    customerAcquisitionListRequestBody.setCursor(nextCursor);
                }
            }
            log.info("获取完毕,size:{}", result.size());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(result)) {
                //数据库中存在的数据
                Set<String> set = list.stream().map(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLinkId).collect(Collectors.toSet());
                Set<String> userIds = list.stream().map(WorkWechatCustomerAcquisitionLink::getUserId).collect(Collectors.toSet());
                result.stream().filter(id -> !set.contains(id)).forEach(e -> {
                    log.info("发送校验删除:{}", e);
                    CleanRemoteAcquisitionDto cleanRemoteAcquisitionDto = new CleanRemoteAcquisitionDto();
                    cleanRemoteAcquisitionDto.setLinkId(e).setUserId(userIds).setCorpId(corpId);
                    enterpriseWechatCustomerAcquisitionSender.cleanRemoteAcquisition(cleanRemoteAcquisitionDto);
                });
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 校验并清除远端获客链接
     *
     * @param cleanRemoteAcquisitionDto
     */
    public void checkRemoteAcquisition(CleanRemoteAcquisitionDto cleanRemoteAcquisitionDto) {
        Set<String> userIds = cleanRemoteAcquisitionDto.getUserId();
        String linkId = cleanRemoteAcquisitionDto.getLinkId();
        String corpId = cleanRemoteAcquisitionDto.getCorpId();
        if (CollectionUtils.isEmpty(userIds) || StringUtils.isAnyBlank(linkId, corpId)) {
            return;
        }
        String accessToken = enterpriseWechatService.getAccessTokenByCorpId(corpId);
        //获取获客链接详情
        CustomerAcquisitionRequestBody customerAcquisitionRequestBody = new CustomerAcquisitionRequestBody();
        customerAcquisitionRequestBody.setLinkId(linkId);
        try {
            //查询当前获客链接详情
            CustomerAcquisitionResponseBody customerAcquisitionResponseBody = workWechatCustomerAcquisitionClient.customerAcquisitionDetail(accessToken, customerAcquisitionRequestBody);
            Integer errcode = customerAcquisitionResponseBody.getErrcode();
            if (Objects.equals(errcode, 0)) {
                CustomerAcquisitionRequestItemBody range = customerAcquisitionResponseBody.getRange();
                if (Objects.nonNull(range)) {
                    //当前获客链接使用的客服userId
                    List<String> userList = range.getUserList();
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userList)) {
                        long count = userList.stream().filter(userIds::contains).count();
                        if (count > 0) {
                            //如果当前的获客链接客服在我们系统中存在 则删除
                            workWechatCustomerAcquisitionClient.deleteCustomerAcquisition(accessToken, new CustomerAcquisitionRequestBody().setLinkId(linkId));
                            log.info("删除获客链接成功!linkId:{}", linkId);
                        }
                    }
                }
            }
        } catch (MarketingApiException e) {
            log.error("获客链接详情失败:linkId：{}", linkId, e);
            //是否为并发限制异常
            Integer errcode = e.getErrcode();
            EnterpriseWechatGlobalErrorCode enterpriseWechatGlobalErrorCode = EnumUtil.getByCode(errcode, EnterpriseWechatGlobalErrorCode.class);
            if (Objects.nonNull(enterpriseWechatGlobalErrorCode)) {
                switch (enterpriseWechatGlobalErrorCode) {
                    case API_CONCURRENCY_LIMITATION:
                        log.error("并发限制异常,放入延迟队列延迟处理:linkId:{}", linkId);
                        enterpriseWechatCustomerAcquisitionSender.cleanRemoteAcquisitionDelay(cleanRemoteAcquisitionDto);
                        break;
                }
            }
        }
    }

    /**
     * 删除获客助手链接
     *
     * @param dto 入参
     */
    public void deleteAcquisitionLink(EnterpriseWechatCustomerAcquisitionBatchDeleteDto dto) {
        log.info("删除获客助手链接, 入参dto = {}", JSONObject.toJSONString(dto));

        List<AcquisitionBatchDeleteDTO> list = dto.getAcquisitionBatchDeleteList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int size = list.size();
        boolean batchOperationFlag = Objects.nonNull(dto.getBatchOperationFlag()) && dto.getBatchOperationFlag();
        for (AcquisitionBatchDeleteDTO acquisitionBatchDeleteDTO : list) {

            String corpId = acquisitionBatchDeleteDTO.getCorpId();
            String userId = acquisitionBatchDeleteDTO.getUserId();
            String linkId = acquisitionBatchDeleteDTO.getWechatCustomerAcquisitionLinkId();
            String ip = dto.getIp();
            try {
                //先把状态改成删除中
                if (StringUtils.isNotBlank(linkId)) {
                    landingPageWechatCustomerServiceService.lambdaUpdate()
                        .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkStatus, WechatCustomerAcquisitionLinkStatus.DELETING)
                        .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkReason, size > 0 ? "批量删除获客链接" : "手动删除获客链接")
                        .eq(LandingPageWechatCustomerService::getWechatUserId, userId)
                        .eq(LandingPageWechatCustomerService::getCorpId, corpId)
                        .update();
                }

                if (StringUtils.isBlank(userId) || StringUtils.isBlank(corpId) || StringUtils.isBlank(linkId)) {
                    log.error("删除获客链接，参数不合法, acquisitionBatchDeleteDTO = {}", JSONObject.toJSONString(acquisitionBatchDeleteDTO));
                    throw new RuntimeException("删除获客链接，参数不合法");
                }


                String accessToken = enterpriseWechatService.getAccessTokenByCorpId(corpId);
                User user = dto.getUser();
                log.info("删除获客助手链接, user = {}", JSONObject.toJSONString(user));
                if (StringUtils.isNotBlank(accessToken)) {
                    CustomerAcquisitionRequestBody customerAcquisitionRequestBody = new CustomerAcquisitionRequestBody();
                    customerAcquisitionRequestBody.setLinkId(linkId);

                    BaseWorkWechatResponseBody responseBody = workWechatCustomerAcquisitionClient.deleteCustomerAcquisition(accessToken, customerAcquisitionRequestBody);
                    if (Objects.nonNull(responseBody)) {
                        if (responseBody.getErrcode() == 0) {
                            //企业微信侧的获客链接删除成功后，需要删除系统内的获客链接信息
                            landingPageWechatCustomerServiceService.lambdaUpdate()
                                .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkId, null)
                                .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLink, null)
                                .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkStatus, WechatCustomerAcquisitionLinkStatus.EMPTY)
                                .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkVerify, SwitchStatus.CLOSE)
                                .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkReason, "手动删除获客链接")
                                .eq(LandingPageWechatCustomerService::getWechatUserId, userId)
                                .eq(LandingPageWechatCustomerService::getCorpId, corpId)
                                .update();

                            workWechatCustomerAcquisitionLinkService.update(new LambdaUpdateWrapper<WorkWechatCustomerAcquisitionLink>()
                                .set(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLinkId, null)
                                .set(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLink, null)
                                .set(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLinkReason, null)
                                .set(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLinkStatus, WechatCustomerAcquisitionLinkStatus.EMPTY)
                                .set(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLinkVerify, SwitchStatus.CLOSE)
                                .eq(WorkWechatCustomerAcquisitionLink::getCorpId, corpId)
                                .eq(WorkWechatCustomerAcquisitionLink::getUserId, userId));


                            //保存删除获客链接的操作日志
                            String operDesc = "删除获客链接，linkId: " + linkId;
                            this.saveAcquisitionLinkDeletedLog(userId, corpId, operDesc, user, ip, null, linkId, batchOperationFlag);

                            //删除跨账户的获客链接ID
                            this.crossAccountDeleteLinkId(corpId, linkId, userId, ip, user);
                        } else {
                            log.info("删除获客链接失败，acquisitionBatchDeleteDTO:{},企业微信响应responseBody = {} ", JSONObject.toJSONString(acquisitionBatchDeleteDTO), JSONObject.toJSONString(responseBody));
                            landingPageWechatCustomerServiceService.lambdaUpdate()
                                .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkStatus, acquisitionBatchDeleteDTO.getWechatCustomerAcquisitionLinkStatus())
                                .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkReason, size > 0 ? "批量删除获客链接出现异常:" + responseBody.getErrmsg() : "手动删除获客链接出现异常" + responseBody.getErrmsg())
                                .eq(LandingPageWechatCustomerService::getWechatUserId, userId)
                                .eq(LandingPageWechatCustomerService::getCorpId, corpId)
                                .update();
                            String operDesc = "批量删除获客链接出现异常，linkId: " + linkId;
                            this.saveAcquisitionLinkDeletedLog(userId, corpId, operDesc, user, ip, null, linkId, batchOperationFlag);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("批量删除获客链接出现异常, acquisitionBatchDeleteDTO = {}", JSONObject.toJSONString(acquisitionBatchDeleteDTO), e);
                //删除失败，把状态还原回去
                if (StringUtils.isNotBlank(linkId)) {
                    landingPageWechatCustomerServiceService.lambdaUpdate()
                        .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkStatus, acquisitionBatchDeleteDTO.getWechatCustomerAcquisitionLinkStatus())
                        .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkReason, size > 0 ? "批量删除获客链接失败" : "手动删除获客链接失败")
                        .eq(LandingPageWechatCustomerService::getWechatUserId, userId)
                        .eq(LandingPageWechatCustomerService::getCorpId, corpId)
                        .update();
                }
            }
        }
    }

    /**
     * 跨账户删除获客链接
     *
     * @param corpId 企业微信id
     * @param linkId 获客链接id
     * @param userId 用户userId
     */
    public void crossAccountDeleteLinkId(String corpId, String linkId, String userId, String ip, User user) {
        DeleteLinkIDTO deleteLinkIDTO = new DeleteLinkIDTO();
        String agentId = TenantContextHolder.get();
        User u = new User();
        if (Objects.nonNull(user)) {
            u.setId(user.getId())
                .setUsername(user.getUsername())
                .setOperationRole(user.getOperationRole());
        }
        deleteLinkIDTO.setCorpId(corpId).setLinkId(linkId).setUserId(userId).setAgentId(agentId).setIp(ip).setUser(u);
        enterpriseWechatCustomerAcquisitionSender.crossAccountDeleteLinkId(deleteLinkIDTO);
    }


    /**
     * 保存获客链接被删除的操作日志
     *
     * @param wechatUserId 客服的userId
     * @param corpId       企业微信id
     * @param operDesc     操作描述
     * @param user         当前登陆人
     * @param ip           ip
     */
    public void saveAcquisitionLinkDeletedLog(String wechatUserId, String corpId, String operDesc, User user, String ip, List<LandingPageWechatCustomerService> list, String linkId, Boolean batchOperationFlag) {
        log.info("保存获客链接被删除的操作日志, wechatUserId:{}, corpId:{}, operDesc:{}, user:{}, ip:{}, list:{}, linkId:{}, batchOperationFlag:{}");
        AcquisitionLinkChangeLogDto changeLogDto = new AcquisitionLinkChangeLogDto();
        changeLogDto.setUserId(wechatUserId)
            .setCorpId(corpId)
            .setOperAction(Objects.nonNull(batchOperationFlag) && batchOperationFlag ? CompareOperActionEnum.BATCH_CUSTOMER_SERVICE_ACQUISITION_LINK_DELETE : CompareOperActionEnum.CUSTOMER_SERVICE_ACQUISITION_LINK_DELETE)
            .setOperDesc(operDesc)
            .setLandingPageWechatCustomerServiceList(list)
            .setLinkId(linkId)
            .setOperIp(ip);

        if (Objects.nonNull(user)) {
            changeLogDto
                .setOperUserId(user.getId())
                .setOperUserName(user.getUsername())
                .setOperationRole(user.getOperationRole());
        }
        userOperationLogDetailActionSender.sendAcquisitionLinkDeleteLog(changeLogDto);
    }

    public void receiveCrossAccountDeleteLinkId(DeleteLinkIDTO deleteLinkIDTO) {
        try {
            String agentId = deleteLinkIDTO.getAgentId();
            String userId = deleteLinkIDTO.getUserId();
            String corpId = deleteLinkIDTO.getCorpId();
            String linkId = deleteLinkIDTO.getLinkId();
            String ip = deleteLinkIDTO.getIp();
            User user = deleteLinkIDTO.getUser();
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(corpId) || StringUtils.isBlank(linkId)) {
                log.error("跨账户删除获客链接，参数不合法, deleteLinkIDTO = {}", JSONObject.toJSONString(deleteLinkIDTO));
                throw new RuntimeException("删除获客链接，参数不合法");
            }
            //待修改逻辑 用corpId查询对应的账户
            Set<String> agentIds = enterpriseWechatService.lambdaQuery().eq(EnterpriseWechat::getCorpid, corpId)
                .ne(EnterpriseWechat::getAgentId, agentId).list().stream().map(EnterpriseWechat::getAgentId).collect(Collectors.toSet());
            log.info("删除获客链接，根据corpid查询关联的账户, deleteLinkIDTO = {}, agentIds = {}", deleteLinkIDTO, agentIds);
            for (String item : agentIds) {
                TenantContextHolder.set(item);
                boolean res = landingPageWechatCustomerServiceService.lambdaUpdate()
                    .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkId, null)
                    .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLink, null)
                    .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkStatus, WechatCustomerAcquisitionLinkStatus.EMPTY)
                    .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkVerify, SwitchStatus.CLOSE)
                    .set(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkReason, "手动删除获客链接")
                    .eq(LandingPageWechatCustomerService::getWechatUserId, userId)
                    .eq(LandingPageWechatCustomerService::getCorpId, corpId)
                    .update();
                log.info("更新客服的获客链接状态, res = {}，linkId = {}", res, linkId);
                if (res) {
                    //保存删除获客链接的操作日志
                    String operDesc = "删除获客链接，linkId: " + linkId;

                    saveAcquisitionLinkDeletedLog(userId, corpId, operDesc, user, ip, null, linkId, false);
                }
            }
        } catch (Exception e) {
            log.error("跨账户删除获客链接,出现异常, deleteLinkIDTO = {}", deleteLinkIDTO, e);
        } finally {
            log.info("跨账户删除获客链接结束, 监听到的参数 = {}", deleteLinkIDTO);
        }
    }

    /**
     * 发送消息，异步删除获客链接
     *
     * @param dto 入参
     */
    public void sendForDeleteAcquisitionLink(EnterpriseWechatCustomerAcquisitionBatchDeleteDto dto) {
        enterpriseWechatCustomerAcquisitionSender.batchDeleteLinkId(dto);
    }
}
