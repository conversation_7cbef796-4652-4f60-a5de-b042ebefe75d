package ai.yiye.agent.landingpage.service;

import ai.yiye.agent.domain.LandingPage;
import ai.yiye.agent.domain.LandingPageSort;
import ai.yiye.agent.landingpage.mapper.LandingPageSortMapper;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@DS("postgresql")
public class LandingPageSortService extends ServiceImpl<LandingPageSortMapper, LandingPageSort> {

    public void setTop(LandingPage landingPage, Long userId) {
        if (Objects.isNull(userId)) {
            log.info("落地页置顶userId必填参数为空");
            return;
        }
        LandingPageSort lps = this.getOne(new LambdaQueryWrapper<LandingPageSort>()
            .eq(LandingPageSort::getUserId, userId)
            .eq(LandingPageSort::getLandingPageId, landingPage.getId())
            .eq(LandingPageSort::getAdvertiserAccountGroupId, landingPage.getAdvertiserAccountGroupId())
            .last(" limit 1")
        );
        //不存在，新增一条排序记录
        if (Objects.isNull(lps)) {
            lps = new LandingPageSort().setUserId(userId)
                .setAdvertiserAccountGroupId(landingPage.getAdvertiserAccountGroupId())
                .setLandingPageId(landingPage.getId())
                .setLandingPageTop(-1)
                .setLandingPageGroupTop(-1)
                .setCreatedAt(Instant.now())
                .setUpdatedAt(Instant.now());
            baseMapper.insert(lps);
        }
        final Long id = lps.getId();
        //置顶操作
        if (landingPage.getTop()) {
            //置顶
            if (Objects.isNull(landingPage.getLandingPageGroupId())) {
                //置顶-全部
                baseMapper.setAllTop(id);
            } else {
                //置顶-组内
                baseMapper.setGroupTop(id);
            }
        } else {
            //取消置顶
            if (Objects.isNull(landingPage.getLandingPageGroupId())) {
                //取消置顶-全部
                baseMapper.restAllTop(id);
            } else {
                //取消置顶-组内
                baseMapper.restGroupTop(id);
            }
        }
    }

    public void initData(Long userId, List<Long> pmpIds) {
        if (CollectionUtils.isEmpty(pmpIds)) {
            return;
        }
        baseMapper.initData(userId, pmpIds);
    }

    public void deleteAllData() {
        baseMapper.deleteAllData();
    }

}
