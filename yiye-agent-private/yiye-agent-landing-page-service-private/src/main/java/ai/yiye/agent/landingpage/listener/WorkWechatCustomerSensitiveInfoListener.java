package ai.yiye.agent.landingpage.listener;


import ai.yiye.agent.autoconfigure.rabbitmq.config.Constants;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.LandingPageWechatCustomerService;
import ai.yiye.agent.domain.WorkWechatCustomerSensitiveInfo;
import ai.yiye.agent.domain.dto.CustomerSensitiveInfoDeleteDto;
import ai.yiye.agent.domain.dto.WorkWechatCustomerSensitiveInfoDTO;
import ai.yiye.agent.domain.landingpage.EnterpriseWechat;
import ai.yiye.agent.landingpage.service.EnterpriseWechatService;
import ai.yiye.agent.landingpage.service.LandingPageWechatCustomerServiceService;
import ai.yiye.agent.landingpage.service.WorkWechatCustomerSensitiveInfoService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WorkWechatCustomerSensitiveInfoListener {

    @Resource
    private WorkWechatCustomerSensitiveInfoService workWechatCustomerSensitiveInfoService;

    @Resource
    protected EnterpriseWechatService enterpriseWechatService;

    @Resource
    private LandingPageWechatCustomerServiceService landingPageWechatCustomerServiceService;

    /**
     * 企微客服敏感信息
     *
     * @param dto
     */
    @RabbitListener(bindings = {@QueueBinding(
        key = Constants.WORK_WECHAT_CUSTOMER_SENSITIVE_INFO_QUEUE,
        value = @Queue(
            value = Constants.WORK_WECHAT_CUSTOMER_SENSITIVE_INFO_QUEUE, durable = "true",
            autoDelete = "false", exclusive = "false"
        ),
        exchange = @Exchange(
            name = Constants.WORK_WECHAT_CUSTOMER_SENSITIVE_INFO_EXCHANGE,
            type = ExchangeTypes.TOPIC
        )
    )})
    public void sensitiveInfo(WorkWechatCustomerSensitiveInfoDTO dto) {
        log.info("同步企微客服敏感信息 dto:[{}]", JSONObject.toJSONString(dto));
        WorkWechatCustomerSensitiveInfo sensitiveInfo = new WorkWechatCustomerSensitiveInfo();
        BeanUtils.copyProperties(dto, sensitiveInfo);
        workWechatCustomerSensitiveInfoService.batchSaveOrUpdate(Lists.newArrayList(sensitiveInfo));
        List<String> agentIds = enterpriseWechatService.getAgentIdByCorpId(sensitiveInfo.getCorpId());
        if (CollectionUtils.isEmpty(agentIds)) {
            return;
        }
        agentIds.parallelStream().forEach(agentId -> {
            try {
                TenantContextHolder.set(agentId);
                landingPageWechatCustomerServiceService.update(new LambdaUpdateWrapper<LandingPageWechatCustomerService>()
                    .set(LandingPageWechatCustomerService::getWechatMobile, sensitiveInfo.getWechatMobile())
                    .set(LandingPageWechatCustomerService::getWechatEmail, sensitiveInfo.getWechatEmail())
                    .eq(LandingPageWechatCustomerService::getCorpId, sensitiveInfo.getCorpId())
                    .eq(LandingPageWechatCustomerService::getWechatUserId, sensitiveInfo.getUserId()));
            } catch (Exception e) {
                log.error("同步企微客服敏感信息异常-agentId:{}-dto:{}-异常", agentId, JSONObject.toJSONString(dto), e);
            } finally {
                TenantContextHolder.clearContext();
            }
        });
    }

    /**
     * 删除企微客服敏感信息
     *
     * @param dto
     */
    @RabbitListener(bindings = {@QueueBinding(
        key = Constants.WORK_WECHAT_CUSTOMER_SENSITIVE_INFO_DELETE_QUEUE,
        value = @Queue(
            value = Constants.WORK_WECHAT_CUSTOMER_SENSITIVE_INFO_DELETE_QUEUE, durable = "true",
            autoDelete = "false", exclusive = "false"
        ),
        exchange = @Exchange(
            name = Constants.WORK_WECHAT_CUSTOMER_SENSITIVE_INFO_DELETE_EXCHANGE,
            type = ExchangeTypes.TOPIC
        )
    )})
    public void deleteSensitiveInfo(CustomerSensitiveInfoDeleteDto dto) {
        log.info("删除企微客服敏感信息 dto:[{}]", JSONObject.toJSONString(dto));
        List<LandingPageWechatCustomerService> changeUpdateWechatCustomerServices = dto.getChangeUpdateWechatCustomerServices();
        if (CollectionUtils.isEmpty(changeUpdateWechatCustomerServices)) {
            return;
        }
        List<String> userIds = changeUpdateWechatCustomerServices.stream().filter(e -> StringUtils.isNotBlank(e.getWechatUserId())).map(LandingPageWechatCustomerService::getWechatUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        EnterpriseWechat enterpriseWechat = dto.getEnterpriseWechat();
        if (enterpriseWechat == null || StringUtils.isBlank(enterpriseWechat.getCorpid())) {
            return;
        }
        workWechatCustomerSensitiveInfoService.remove(new LambdaQueryWrapper<WorkWechatCustomerSensitiveInfo>()
            .eq(WorkWechatCustomerSensitiveInfo::getCorpId, enterpriseWechat.getCorpid())
            .in(WorkWechatCustomerSensitiveInfo::getUserId, userIds));
    }

}
