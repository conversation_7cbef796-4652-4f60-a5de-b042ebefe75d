package ai.yiye.agent.landingpage.service;

import ai.yiye.agent.autoconfigure.constants.Constants;
import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.security.SecurityConstant;
import ai.yiye.agent.autoconfigure.security.TokenProvider;
import ai.yiye.agent.autoconfigure.security.jwt.JwtTokenGenerator;
import ai.yiye.agent.autoconfigure.web.argument.Direction;
import ai.yiye.agent.autoconfigure.web.exception.ErrorConstants;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.common.util.DateTimeUtil;
import ai.yiye.agent.common.util.TraceUtil;
import ai.yiye.agent.common.util.WecatAppleVersionUtil;
import ai.yiye.agent.domain.*;
import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.dto.*;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.form.PageViewForm;
import ai.yiye.agent.domain.landingpage.*;
import ai.yiye.agent.domain.landingpage.dto.WechatAppletWarnMessageDelayDto;
import ai.yiye.agent.domain.landingpage.dto.WechatAppletWarnMessageDto;
import ai.yiye.agent.domain.marketing.AdvertiserAccountGroupRequestBody;
import ai.yiye.agent.domain.pageview.PageViewInfo;
import ai.yiye.agent.domain.sessionarchive.EnterpriseWechatSessionArchiveRel;
import ai.yiye.agent.domain.utils.IPUtil;
import ai.yiye.agent.domain.utils.UrlUtils;
import ai.yiye.agent.domain.utils.WebUtils;
import ai.yiye.agent.domain.vo.TracePageVO;
import ai.yiye.agent.landingpage.config.AgentConf;
import ai.yiye.agent.landingpage.config.PageViewConf;
import ai.yiye.agent.landingpage.controller.vo.MiniWechatResponseBody;
import ai.yiye.agent.landingpage.dto.LandingPageWechatAppletConfigDto;
import ai.yiye.agent.landingpage.dto.LandingPageWechatAppletConfigVo;
import ai.yiye.agent.landingpage.dto.OpenAppIdDto;
import ai.yiye.agent.landingpage.enums.WechatAppletStatusToggleEnum;
import ai.yiye.agent.landingpage.enums.exception.WechatAppletResultCode;
import ai.yiye.agent.landingpage.mapper.LandingPageWechatAppletConfigMapper;
import ai.yiye.agent.landingpage.redis.LandingPageWechatAppletConfigRedis;
import ai.yiye.agent.landingpage.redis.WxOpenRedis;
import ai.yiye.agent.landingpage.remote.AdvertiserAccountGroupRemote;
import ai.yiye.agent.landingpage.remote.BossBackendRemote;
import ai.yiye.agent.landingpage.sender.*;
import ai.yiye.agent.weixin.client.OpenAppletCreateQrCodeApiClient;
import ai.yiye.agent.weixin.client.OpenWeixinApiClient;
import ai.yiye.agent.weixin.client.WechatAppletApiClient;
import ai.yiye.agent.weixin.domain.OpenCreateOpenResponseBody;
import ai.yiye.agent.weixin.domain.OpenGetByAppIdResponseBody;
import ai.yiye.agent.weixin.exception.MarketingWeOpenApiException;
import ai.yiye.agent.weixin.exception.WechatApiException;
import ai.yiye.agent.weixin.mapper.OpenWechatMappers;
import ai.yiye.agent.weixin.mapper.WechatPropertyMapper;
import brave.Tracer;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 微信小程序 - 业务逻辑层
 */
@Slf4j
@Service
@DS(DbConstants.POSTGRESQL_DEFAULT)  //设置成默认数据库
public class LandingPageWechatAppletConfigService extends ServiceImpl<LandingPageWechatAppletConfigMapper, LandingPageWechatAppletConfig> {

    @Autowired
    private WechatAppletApiClient wechatAppletApiClient;
    @Autowired
    private WechatAppletApiClient newWechatAppletApiClient;
    @Autowired
    private LandingPageWechatAppletConfigRedis landingPageWechatAppletConfigRedis;
    @Autowired
    private RedisTemplate<String, Object> defaultObjectRedisTemplate;
    //@Autowired
    //private WorkWechatClientService workWechatClientService;
    @Autowired
    private EnterpriseWechatService enterpriseWechatService;
    @Autowired
    private OpenWeixinApiClient openWeixinApiClient;
    @Autowired
    private OpenWeixinApiClient openWechatApiClient;
    @Autowired
    private LandingPageWechatAppletConfigMapper landingPageWechatAppletConfigMapper;
    @Autowired
    private LandingPageWechatTemplateService landingPageWechatTemplateService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private LandingPageService landingPageService;
    @Autowired
    private LandingPageChannelService landingPageChannelService;
    @Autowired
    private AgentConf agentConf;
    @Autowired
    private EnterpriseWechatsPmpRelService enterpriseWechatsPmpRelService;
    @Autowired
    private LandingPageWechatAppletPmpRelService landingPageWechatAppletPmpRelService;
    @Autowired
    private AdvertiserAccountGroupRemote advertiserAccountGroupRemote;
    @Autowired
    private LandingPageWechatAppletConfigService landingPageWechatAppletConfigService;
    @Autowired
    private MessageNoticeSender messageNoticeSender;
    @Autowired
    private JwtTokenGenerator jwtTokenGenerator;
    @Autowired
    private PageViewInfoPgService pageViewInfoPgService;
    @Autowired
    private PageViewinfoSender pageViewinfoSender;
    @Resource
    private LandingPageWechatOfficialAccountRelService landingPageWechatOfficialAccountRelService;
    @Autowired
    private PaymentWechatOfficialAccountPmpRelService paymentWechatOfficialAccountPmpRelService;
    @Resource
    private WxOpenRedis wxOpenRedis;
    @Autowired
    private SubmitDataService submitDataService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private LandingPageCommonParamsService landingPageCommonParamsService;
    @Autowired
    private LandingPageSender landingPageSender;
    @Autowired
    private RedisTemplate<String, Object> objectRedisTemplate;
    @Autowired
    private PageViewConf pageViewConf;
    @Autowired
    private StaticWechatAppletSender staticWechatAppletSender;
    @Autowired
    private EnterpriseWechatExternalUserAuthDataService enterpriseWechatExternalUserAuthDataService;
    @Autowired
    private WxOpenService wxOpenService;
    @Autowired
    private AgentConfService agentConfService;
    @Autowired
    private QiyetuiAppPmpRelService qiyetuiAppPmpRelService;
    @Autowired
    private AdvertiserAccountGroupService advertiserAccountGroupService;
    @Autowired
    private LandingPageDouyinAppletPmpRelService landingPageDouyinAppletPmpRelService;
    @Resource
    private EnterpriseWechatSessionArchiveService enterpriseWechatSessionArchiveService;
    @Resource
    private EnterpriseWechatSessionArchiveRelService enterpriseWechatSessionArchiveRelService;
    @Resource
    private BossBackendRemote bossBackendRemote;
    @Resource
    private QiyetuiAppService qiyetuiAppService;
    @Autowired
    public Tracer tracer;
    @Autowired
    private WechatAppletWarnMessageSender wechatAppletWarnMessageSender;


    public static boolean isNotShow(String platform) {
        return StringUtils.isNotEmpty(platform) && (StringUtils.equals("windows", platform) || StringUtils.equals("mac", platform));
    }

    /**
     * 获取当前客户 所能使用的企业微信类型
     *
     * @param agentId
     * @return
     */
    public EnterpriseWechatType getEnterpriseWechatType(final String agentId) {
        if (StringUtils.isBlank(agentId)) {
            //兼容应用发布上线时部分页面缺少参数导致没有agentid的场景
            return EnterpriseWechatType.SERVICE_PROVIDER;
        }
        return EnterpriseWechatType.GENERATION_DEVELOPMENT;
    }

    /**
     * 小程序备用非备用手动转换
     *
     * @param id     小程序ID
     * @param status 状态 0:切换为备用小程序 1:切换为非备用小程序
     */
    public void updateApplectStatus(Long id, Integer status) {
        String agentId = TenantContextHolder.get();
        LandingPageWechatAppletConfig landingPageWechatAppletConfig = this.getOne(new LambdaQueryWrapper<LandingPageWechatAppletConfig>()
            .eq(LandingPageWechatAppletConfig::getId, id)
            .eq(LandingPageWechatAppletConfig::getAgentId, agentId));

        if (ObjectUtils.isEmpty(landingPageWechatAppletConfig)) {
            throw new RestException("小程序不存在!");
        }
        //切换为备用
        if (WechatAppletStatusToggleEnum.SPARE.getCode().equals(status)) {
            this.updateSpareStatus(landingPageWechatAppletConfig);
        }
        //切换为非备用
        else if (WechatAppletStatusToggleEnum.NORMAL.getCode().equals(status)) {
            this.updateNormalStatus(landingPageWechatAppletConfig);
        }
    }

    //切换为备用小程序
    private void updateSpareStatus(LandingPageWechatAppletConfig wechatAppletConfig) {
        String wechatAppletName = wechatAppletConfig.getWechatAppletName();
        //已经是备用小程序
        if (wechatAppletConfig.getWechatAppletStatus() == 2 && wechatAppletConfig.getAppletAttribute().equals(AppletAttribute.BACKUP_APPLET)) {
            log.info("当前小程序【{}】 appId:{} 已经是备用小程序", wechatAppletName, wechatAppletConfig.getWechatAppletAppid());
            return;
        }
        //状态异常的不支持切换
        if (wechatAppletConfig.getWechatAppletStatus() == 0) {
            throw new RestException("状态异常，不支持切换为备用小程序");
        }
        //校验是否已被渠道使用生成小程序路径
        String appletOriginalId = wechatAppletConfig.getAppletOriginalId();
        List<LandingPageChannel> landingPageChannels = this.searchChannelsByAppletOriginalId(appletOriginalId);
        if (!CollectionUtils.isEmpty(landingPageChannels)) {
            throw new RestException("小程序已被落地页渠道引用生成小程序路径，不支持更改使用状态");
        }
        //开始切换
        wechatAppletConfig.setWechatAppletStatus(2).setAppletAttribute(AppletAttribute.BACKUP_APPLET).setAppletRemarks("备用小程序");
        boolean b = this.updateById(wechatAppletConfig);
        if (!b) {
            throw new RestException("小程序【" + wechatAppletName + "】状态更新失败!");
        }
        //清理缓存, 非备用小程序现在可共享，需要清理关联的pmp缓存
        String wechatAppletAppid = wechatAppletConfig.getWechatAppletAppid();
        this.removeAppletRedisCache(wechatAppletAppid);
    }

    //切换为非备用小程序
    private void updateNormalStatus(LandingPageWechatAppletConfig wechatAppletConfig) {
        String wechatAppletName = wechatAppletConfig.getWechatAppletName();
        //已经是非备用小程序
        if (wechatAppletConfig.getWechatAppletStatus() == 1 && wechatAppletConfig.getAppletAttribute().equals(AppletAttribute.NORMAL_APPLET)) {
            log.info("当前小程序【{}】 appId:{} 已经是非备用小程序", wechatAppletName, wechatAppletConfig.getWechatAppletAppid());
            return;
        }
        //开始切换
        wechatAppletConfig.setWechatAppletStatus(1).setAppletAttribute(AppletAttribute.NORMAL_APPLET).setAppletRemarks("正常");
        boolean b = this.updateById(wechatAppletConfig);
        if (!b) {
            throw new RestException("小程序【" + wechatAppletName + "】状态更新失败!");
        }
        //清理缓存, 备用小程序可共享，需要清理关联的pmp缓存
        String wechatAppletAppid = wechatAppletConfig.getWechatAppletAppid();
        this.removeAppletRedisCache(wechatAppletAppid);
    }

    //清除与小程序关联的项目缓存
    public void removeAppletRedisCache(String wechatAppletAppid) {
        //清理缓存, 备用小程序可共享，需要清理关联的pmp缓存
        //查询关联项目列表
        List<Long> pmpIds = this.selectPmpIdsByAppid(wechatAppletAppid);
        if (CollectionUtils.isEmpty(pmpIds)) {
            log.info("当前小程序 appId:{} 没有关联的项目!", wechatAppletAppid);
            return;
        }
        String agentId = TenantContextHolder.get();
        pmpIds.stream().forEach(e -> {
            EnterpriseWechatType enterpriseWechatType = landingPageWechatAppletConfigService.getEnterpriseWechatType(agentId);
            //清除缓存
            landingPageWechatAppletConfigRedis.deleteByPmpId(agentId, e, enterpriseWechatType);
        });
    }

    //根据小程序原始ID查询渠道关联
    private List<LandingPageChannel> searchChannelsByAppletOriginalId(String appletOriginalId) {
        LambdaQueryWrapper<LandingPageChannel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LandingPageChannel::getAppletOriginalId, appletOriginalId)
            .eq(LandingPageChannel::getDeleteStatus, DeleteStatus.NORMAL);
        List<LandingPageChannel> list = this.landingPageChannelService.list(lambdaQueryWrapper);
        return list;
    }

    public SchemeResultDto generateSchemeJson(final Long accountGroupId, final String agentId, final String path, final String query, final String yiyeQueryStr, final Boolean isExpire, Boolean openNewWechatApplet, Boolean checkWechatToken, Long generateSchemeCount, String userAgent) {
        String sinaWeiboScheme = "";
        WechatAppletOpenlinkDto waoDto = generateScheme(accountGroupId, agentId, path, query, yiyeQueryStr, isExpire, openNewWechatApplet, checkWechatToken, generateSchemeCount);
        try {
            sinaWeiboScheme = "sinaweibo://wbdiversion?username=" + waoDto.getLandingPageWechatAppletConfig().getAppletOriginalId() + "&path=" + URLEncoder.encode(("/" + path + "?" + query + "&yiyeQueryId=" + waoDto.getYiyeQueryId()), "UTF-8");
        } catch (Exception e) {
            log.error("组装新浪微博scheme异常", e);
        }
        return new SchemeResultDto().setWechatAppletScheme(!Objects.isNull(waoDto) ? waoDto.getWechatAppletScheme() : "").setSinaWeiboScheme(sinaWeiboScheme);
    }

    /**
     * 生成【微信小程序】Scheme码
     *
     * @param agentId             agentId
     * @param path                地址
     * @param query               条件
     * @param yiyeQueryStr        无法传递的query参数
     * @param isExpire            时效
     * @param openNewWechatApplet 是否停止执行
     */
    public WechatAppletOpenlinkDto generateScheme(final Long advertiseAccountGroupId, final String agentId, final String path, final String query, final String yiyeQueryStr, final Boolean isExpire, Boolean openNewWechatApplet, Boolean checkWechatToken, Long generateSchemeCount) {
        //通过 query 获取落地页信息
        //LandingPageType landingPageType = this.getLandingPageType(query);
        LandingPageType landingPageType = null;
        if (Constants.WECHAT_APPLET_DETAIL_PATH.equals(path)) {
            landingPageType = LandingPageType.WECHAT_APPLET_MINI;
        }
        if (Constants.WECHAT_APPLET_INDEX_PATH.equals(path)) {
            landingPageType = LandingPageType.H5;
        }
        LandingPageWechatAppletConfig lpwac = this.getLandingPageWechatAppletConfig(agentId, advertiseAccountGroupId, generateSchemeCount, landingPageType);
        EnterpriseWechatType enterpriseWechatType = lpwac.getEnterpriseWechatType();
        JSONObject jsonObject = new JSONObject();
        JSONObject jumpWxa = new JSONObject();
        LandingPageCommonParams landingPageCommonParams = new LandingPageCommonParams(CommonParamsType.YIYE_QUERY_STR_PARAM, yiyeQueryStr);
        final String yiyeQueryId = landingPageCommonParams.getUuidKey();
        jumpWxa.put("path", path);
        jumpWxa.put("query", StringUtils.isBlank(query) ? "yiyeQueryId=" + yiyeQueryId : ("yiyeQueryId=" + yiyeQueryId + "&" + query));
        jsonObject.put("jump_wxa", jumpWxa);
        jsonObject.put("is_expire", isExpire);
        if (isExpire) {
            jsonObject.put("expire_type", 1);
            jsonObject.put("expire_interval", RedisConstant.WECHAT_APPLET_EXPIRE_INTERVAL);
        }
        JSONObject resultParams = wechatAppletApiClient.generateScheme(lpwac.getAccessToken(), jsonObject);
        log.info("微信小程序获取scheme响应参数 resultParams={}", resultParams.toJSONString());
        if (resultParams.size() <= 0) {
            throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_WECHAT_APPLET_CONFIG_GENERATE_SCHEME);
        }
        Integer resultCode = resultParams.getInteger("errcode");
        if (0 == resultCode) {
            //每次生成schema的时候，进行解析，将query参数进行异步保存
            staticWechatAppletSender.sendStaticGenerateScheme(agentId, yiyeQueryId, yiyeQueryStr, lpwac, resultParams.getString("openlink"), advertiseAccountGroupId);
            landingPageWechatAppletConfigRedis.saveQueryParams(yiyeQueryId, yiyeQueryStr);
            return new WechatAppletOpenlinkDto().setWechatAppletScheme(resultParams.getString("openlink")).setYiyeQueryId(yiyeQueryId).setLandingPageWechatAppletConfig(lpwac);
        } else {
            log.warn("微信小程序获取scheme失败：resultParams ======>> {}", resultParams.toJSONString());
            if ("40001".equals(resultCode.toString()) || "42001".equals(resultCode.toString())) {
                log.info("微信小程序获取scheme失败：小程序access_token过期 ====== >>：{}", resultParams.toJSONString());
                if (EnterpriseWechatType.GENERATION_DEVELOPMENT.equals(enterpriseWechatType)) {
                    if (checkWechatToken) {
                        refreshAppletToken(landingPageWechatAppletConfigMapper.selectById(lpwac.getId()));
                        return generateScheme(advertiseAccountGroupId, agentId, path, query, yiyeQueryStr, isExpire, true, false, generateSchemeCount);
                    }
                }
            }
            if ("40002".equals(resultCode.toString())) {    //暂无生成权限
                log.info("微信小程序获取scheme失败：暂无生成权限 ====== >>：{}", resultParams.toJSONString());
                throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_WECHAT_APPLET_CONFIG_SCHEME_GENERATION_FREQUENCY_IS_TOO_FAST);
            }
            /**
             对于接口返回{"errcode":50002,"errmsg":"user limited rid: xxxxxx"}，则通常是该小程序或者公众号被冻结了、注销了或者迁移了。
             */
            if ("40013".equals(resultCode.toString()) || "50002".equals(resultCode.toString())) {    //生成权限被封禁
                log.info("微信小程序获取scheme失败：生成权限被封禁 ====== >> {}", resultParams.toJSONString());
                if (openNewWechatApplet) {  //启用备用小程序
                    openNewWechatApplet(agentId, advertiseAccountGroupId, enterpriseWechatType, lpwac, resultParams);
                    return landingPageWechatAppletConfigService.generateScheme(advertiseAccountGroupId, agentId, path, query, yiyeQueryStr, isExpire, false, true, generateSchemeCount);    //重新获取小程序scheme码（1次）
                }
                throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_WECHAT_APPLET_CONFIG_GENERATE_SCHEME);
            }
            if ("85079".equals(resultCode.toString())) {    //小程序未发布
                log.info("微信小程序获取scheme失败：小程序未发布 ====== >> ：appid={}；resultParams={}；", lpwac.getWechatAppletAppid(), resultParams.toJSONString());
                throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_WECHAT_APPLET_CONFIG_GENERATE_SCHEME);
            }
            if ("40165".equals(resultCode.toString())) {    //参数path填写错误
                log.info("微信小程序获取scheme失败：参数path填写错误 ====== >> ：path={}；resultParams={}；", path, resultParams.toJSONString());
                throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_WECHAT_APPLET_CONFIG_GENERATE_SCHEME);
            }
            if ("40212".equals(resultCode.toString())) {    //参数query填写错误
                log.info("微信小程序获取scheme失败：参数query填写错误 ====== >> ：query={}；resultParams={}；", query, resultParams.toJSONString());
                throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_WECHAT_APPLET_CONFIG_GENERATE_SCHEME);
            }
            if ("85401".equals(resultCode.toString())) {    //参数expire_time填写错误，时间间隔大于1分钟且小于1年
                log.info("微信小程序获取scheme失败：参数expire_time填写错误，时间间隔大于1分钟且小于1年 ====== >> {}", resultParams.toJSONString());
                throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_WECHAT_APPLET_CONFIG_GENERATE_SCHEME);
            }
            if ("85402".equals(resultCode.toString())) {    //参数env_version填写错误
                log.info("微信小程序获取scheme失败：参数env_version填写错误 ====== >> {}", resultParams.toJSONString());
                throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_WECHAT_APPLET_CONFIG_GENERATE_SCHEME);
            }
            if ("44990".equals(resultCode.toString())) {    //生成Scheme频率过快（超过100次/秒）
                log.info("微信小程序获取scheme失败：生成Scheme频率过快（超过100次/秒），小程序appid={}；resultParams={}；", lpwac.getWechatAppletAppid(), resultParams.toJSONString());
                throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_WECHAT_APPLET_CONFIG_SCHEME_GENERATION_FREQUENCY_IS_TOO_FAST);
            }
            if ("85400".equals(resultCode.toString())) {
                log.info("微信小程序获取scheme失败：【长期有效Scheme达到生成上限10万】，小程序appid={}；resultParams={}；", lpwac.getWechatAppletAppid(), resultParams.toJSONString());
                throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_WECHAT_APPLET_CONFIG_SCHEME_GENERATION_FREQUENCY_IS_TOO_FAST);
            }
            if ("45009".equals(resultCode.toString())) {
                log.info("微信小程序获取scheme失败：【单天生成Scheme数量超过上限50万 】，小程序appid={}；resultParams={}；", lpwac.getWechatAppletAppid(), resultParams.toJSONString());
                throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_WECHAT_APPLET_CONFIG_SCHEME_ACCESS_LIMIT_EXCEEDED);
            }
        }
        log.info("微信小程序获取scheme：未知错误 resultParams ======>>：{}", resultParams.toJSONString());
        throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_WECHAT_APPLET_CONFIG_GENERATE_SCHEME);
    }

    /**
     * 轮询获取PMP内可用的小程序
     */
    public LandingPageWechatAppletConfig getLandingPageWechatAppletConfig(String agentId, Long advertiseAccountGroupId, Long generateSchemeCount, LandingPageType landingPageType) {
        // ---------------------------------- 一秒钟超过100次访问，切换小程序；一天一个小程序生成50万次，切换小程序 ----------------------------------
        LandingPageWechatAppletConfigService landingPageWechatAppletConfigService = applicationContext.getBean(this.getClass());
        // 判断当前账户需要使用是小程序类型
        EnterpriseWechatType enterpriseWechatType = landingPageWechatAppletConfigService.getEnterpriseWechatType(agentId);
        log.info("==========当前账户小程序类型========：{}", enterpriseWechatType);
        //获取到的accessToken
        final LandingPageWechatAppletConfig landingPageWechatAppletConfig = landingPageWechatAppletConfigRedis.getLandingPageWechatAppletConfigByCount(agentId, advertiseAccountGroupId, enterpriseWechatType, landingPageType,
            //获取当天创建Scheme码次数
            () -> Objects.isNull(generateSchemeCount) ? landingPageWechatAppletConfigRedis.innerGenerateSchemeCount(agentId, advertiseAccountGroupId, enterpriseWechatType) : generateSchemeCount,
            //获取小程序配置信息
            () -> landingPageWechatAppletConfigService.getLandingPageWechatAppletConfigList(agentId, advertiseAccountGroupId, enterpriseWechatType, landingPageType, 1),
            //开启备用小程序
            () -> landingPageWechatAppletConfigService.openNewWechatApplet(agentId, advertiseAccountGroupId, enterpriseWechatType, null, null)
        );
        log.info("==============当前可用小程序==============：{}", landingPageWechatAppletConfig);
        if (Objects.isNull(landingPageWechatAppletConfig) || StringUtils.isBlank(landingPageWechatAppletConfig.getAccessToken())) {
            landingPageWechatAppletConfigService.sendWechatAppletFeiShuMessage(new LandingPageWechatAppletConfig().setAgentId(agentId), advertiseAccountGroupId, null, null, SendRobotMessageScene.THIS_PROJECT_NOT_WECHAT_APPLET);
            throw new RestException(ErrorConstants.ERROR_NOT_HAVE_CAN_USE_WECHAT_APPLET_CONFIG);
        }
        return landingPageWechatAppletConfig;
    }

    /**
     * 开启备用小程序
     */
    public LandingPageWechatAppletConfig openNewWechatApplet(final String agentId, final Long advertiseAccountGroupId, final EnterpriseWechatType ewType, final LandingPageWechatAppletConfig lpwac, final JSONObject resultParams) {
        log.info("微信小程序获取scheme失败，开启备用小程序，agentId={}；advertiseAccountGroupId={}；ewType={}；lpwac={}；resultParams={}；", agentId, advertiseAccountGroupId, ewType, lpwac, resultParams);
        LandingPageWechatAppletConfigService landingPageWechatAppletConfigService = applicationContext.getBean(this.getClass());
        if (!Objects.isNull(lpwac)) {
            landingPageWechatAppletConfigMapper.updateById(new LandingPageWechatAppletConfig()
                .setId(lpwac.getId())
                .setOpenAppId(lpwac.getOpenAppId())
                .setWechatAppletStatus(0)
                .setUpdatedAt(Instant.now())
                .setRemarks("生成权限被封禁，参数：" + resultParams.toJSONString())
                .setAppletRemarks("调用异常，无法通过该小程序访问页面")
            );
            landingPageWechatAppletConfigRedis.deleteById(agentId, advertiseAccountGroupId, ewType, lpwac.getWechatAppletAppid());
            //发送站内通知
            messageNoticeSender.sendForbidApplet(lpwac.setWechatAppletAbandonType(WechatAppletAbandonType.JOBABANDON));
        }
        TenantContextHolder.set(agentId);
        List<LandingPageWechatAppletConfig> lpwacList = landingPageWechatAppletConfigService.getLandingPageWechatAppletConfigByPmpId(agentId, advertiseAccountGroupId, ewType, null, 2, Direction.asc);
        LandingPageWechatAppletConfig willOpenWechatApplet = (!CollectionUtils.isEmpty(lpwacList)) ? lpwacList.get(0) : null;
        //启用备用小程序
        if (!Objects.isNull(willOpenWechatApplet)) {
            landingPageWechatAppletConfigService.updateById(new LandingPageWechatAppletConfig()
                .setId(willOpenWechatApplet.getId())
                .setOpenAppId(willOpenWechatApplet.getOpenAppId())
                .setWechatAppletStatus(1)
                .setUpdatedAt(Instant.now())
                .setRemarks("【" + agentId + "】 备用小程序被启用，时间：" + Instant.now())
                .setAppletRemarks("正常")
                .setAppletAttribute(AppletAttribute.NORMAL_APPLET)
            );
            if (!Objects.isNull(lpwac)) {
                landingPageWechatAppletConfigService.sendWechatAppletFeiShuMessage(lpwac, null, "微信小程序下线，启用小程序【" + willOpenWechatApplet.getWechatAppletName() + "】", "微信小程序获取scheme失败：启用备用小程序成功", SendRobotMessageScene.WECHAT_APPLET_OFFLINE_ENABLE_STANDBY_APPLET);
            }
        } else {
            if (!Objects.isNull(lpwac)) {
                landingPageWechatAppletConfigService.sendWechatAppletFeiShuMessage(lpwac, null, "微信小程序下线，当前项目下无可用小程序，请尽快配置", "微信小程序获取scheme码失败：启用备用小程序失败，无备用小程序可用，请尽快配置", SendRobotMessageScene.WECHAT_APPLET_OFFLINE_THIS_PROJECT_NOT_WECHAT_APPLET);
            }
        }
        return willOpenWechatApplet;
    }

    /**
     * 获取小程序缓存
     */
    public LandingPageWechatAppletConfig getAndCacheLandingPageWechatAppletConfig(final String appid) {
        String key = RedisConstant.LANDING_PAGE_WECHAT_APPLET_CONFIG(appid);
        LandingPageWechatAppletConfig landingPageWechatAppletConfig = (LandingPageWechatAppletConfig) defaultObjectRedisTemplate.opsForValue().get(key);
        if (Objects.isNull(landingPageWechatAppletConfig)) {
            landingPageWechatAppletConfig = baseMapper.selectOne(new LambdaQueryWrapper<LandingPageWechatAppletConfig>().eq(LandingPageWechatAppletConfig::getWechatAppletAppid, appid));
            if (!Objects.isNull(landingPageWechatAppletConfig)) {
                defaultObjectRedisTemplate.opsForValue().set(key, landingPageWechatAppletConfig, 10, TimeUnit.MINUTES);
            } else {
                log.warn("查询不到该小程序 appid: {} redis-key: {}", appid, key);
            }
        }
        return landingPageWechatAppletConfig;
    }

    public List<LandingPageWechatAppletConfig> getLandingPageWechatAppletConfigByPmpId(String agentId, Long pmpId, EnterpriseWechatType ewType, LandingPageType landingPageType, Integer wechatAppletStatus, Direction direction) {
        return baseMapper.getLandingPageWechatAppletConfigByPmpId(agentId, pmpId, ewType, landingPageType, wechatAppletStatus, (Objects.isNull(direction) ? null : direction.toString()));
    }

    /**
     * 获取小程序配置信息
     */
    public List<LandingPageWechatAppletConfig> getLandingPageWechatAppletConfigList(final String agentId, final Long pmpId, final EnterpriseWechatType ewType, LandingPageType landingPageType, Integer wechatAppletStatus) {
        log.info("微信小程序获取scheme失败，获取小程序配置信息，agentId={}；pmpId={}；ewType={}；", agentId, pmpId, ewType);
        List<LandingPageWechatAppletConfig> lpwacList = applicationContext.getBean(this.getClass()).getLandingPageWechatAppletConfigByPmpId(agentId, pmpId, ewType, landingPageType, wechatAppletStatus, null);
        if (CollectionUtils.isEmpty(lpwacList)) {
            log.info("微信小程序 - 获取accessToken - 未配置小程序参数，当前客户当前pmp没有可用小程序 agentId={}；pmpId={}；ewType={}；landingPageType={}；", agentId, pmpId, ewType, landingPageType);
            return null;
        }
        log.info("获取小程序配置信息，当前客户所有代开发小程序列表，agentId={}；pmpId={}；ewType={}；allLpwacList={}；landingPageType={}；", agentId, pmpId, ewType, lpwacList, landingPageType);
        return lpwacList;
    }

    /**
     * 小程序授权，获取openid、unionID
     */
    public JSONObject authCodeToSession(String agentId, final String jsCode, final String appId, final String yiyeQueryId, Boolean retry, String platform, HttpServletRequest request, String userAgent) {
        LandingPageWechatAppletConfigService bean = applicationContext.getBean(this.getClass());
        if (StringUtils.isBlank(agentId)) {
            log.info("agentId is blank Maybe from the home page");
        }
        LandingPageWechatAppletConfig landingPageWechatAppletConfig = bean.getAndCacheLandingPageWechatAppletConfig(appId);
        if (Objects.isNull(landingPageWechatAppletConfig)) {
            log.warn("未匹配到对应【appId】的小程序信息 jsCode ======>>{}； appId ====== >>{}；agentId ======>>{}；yiyeQueryId ======>> {}", jsCode, appId, agentId, yiyeQueryId);
            return null;
        }
        JSONObject result = new JSONObject();
        try {
            if (EnterpriseWechatType.GENERATION_DEVELOPMENT.equals(landingPageWechatAppletConfig.getEnterpriseWechatType())) {
                String componentAppId = wxOpenRedis.getComponentAppId(landingPageWechatAppletConfig.getComponentAppId());
                String accessToken = wxOpenRedis.getComponentAccessToken(componentAppId);
                result = newWechatAppletApiClient.componentAuthCodeToSession(landingPageWechatAppletConfig.getWechatAppletAppid(), componentAppId, accessToken, jsCode);
            } else {
                result = newWechatAppletApiClient.authCodeToSession(landingPageWechatAppletConfig.getWechatAppletAppid(), landingPageWechatAppletConfig.getWechatAppletSecret(), jsCode);
            }
            //关于小程序session_key泄露漏洞
            //https://developers.weixin.qq.com/community/minihome/doc/000806202400280b0edd5866156c01?blockType=99
//            result.remove("session_key");
//            String session_key = result.getString("session_key");
//            result.put("sessionKey", session_key);
            result.remove("session_key");
        } catch (WechatApiException e) {
            if (!Objects.isNull(e.getErrcode()) && e.getErrcode() == 40001) {
                //缓存过期，删除redis中某个指定appid小程序的缓存
                landingPageWechatAppletConfigRedis.deleteWechatByAgentId(agentId, landingPageWechatAppletConfig.getEnterpriseWechatType());
                landingPageWechatAppletConfigRedis.deleteWechatByAppid(appId);
                if (retry) {
                    return bean.authCodeToSession(agentId, jsCode, appId, yiyeQueryId, false, platform, request, userAgent);
                } else {
                    log.error("调取微信API接口获取小程序unionid异常 jsCode ======>>{}； appId ====== >>{}；agentId ======>>{}；yiyeQueryId ======>> {}", jsCode, appId, landingPageWechatAppletConfig.getAgentId(), yiyeQueryId, e);
                }
            } else if (!Objects.isNull(e.getErrcode()) && e.getErrcode() == 40029) {
                //code兑换失败，invalid code
                log.warn("调取微信API接口获取小程序unionid异常 jsCode ======>>{}； appId ====== >>{}；agentId ======>>{}；yiyeQueryId ======>> {}", jsCode, appId, landingPageWechatAppletConfig.getAgentId(), yiyeQueryId, e);
            } else {
                log.error("调取微信API接口获取小程序unionid异常 jsCode ======>>{}； appId ====== >>{}；agentId ======>>{}；yiyeQueryId ======>> {}", jsCode, appId, landingPageWechatAppletConfig.getAgentId(), yiyeQueryId, e);
            }
        } catch (Exception e) {
            log.error("调取微信API接口获取小程序unionid异常 jsCode ======>>{}； appId ====== >>{}；agentId ======>>{}；yiyeQueryId ======>> {}", jsCode, appId, landingPageWechatAppletConfig.getAgentId(), yiyeQueryId, e);
            return null;
        }

        try {
            if (!StringUtils.isBlank(yiyeQueryId)) {
                result.put("yiyeQueryId", yiyeQueryId);
                String queryParams = landingPageWechatAppletConfigRedis.getQueryParams(yiyeQueryId);
                if (StringUtils.isBlank(StringUtils.trim(queryParams))) {
                    queryParams = landingPageCommonParamsService.getParamsByUUidKey(yiyeQueryId);
                }
                result.put("yiyeQueryStr", StringUtils.isBlank(yiyeQueryId) ? null : queryParams);
                PageViewInfo pageViewInfo = updateOfficialPv(agentId, yiyeQueryId, queryParams, result.getString("openid"), result.getString("unionid"), request, userAgent);
                staticWechatAppletSender.sendStaticAuthToSuccess(yiyeQueryId, queryParams, landingPageWechatAppletConfig, agentId, pageViewInfo);
            }
        } catch (Exception e) {
            log.error("yiyeQueryId获取缓存失败 jsCode ======>>{}； appId ====== >>{}；agentId ======>>{}；yiyeQueryId ======>> {}", jsCode, appId, landingPageWechatAppletConfig.getAgentId(), yiyeQueryId, e);
            return null;
        }

        log.info("小程序授权返回信息 jsCode ======>>{}； appId ====== >>{}；agentId ======>>{}；yiyeQueryId ======>> {} result ====== >> {}", jsCode, appId, landingPageWechatAppletConfig.getAgentId(), yiyeQueryId, JSONObject.toJSONString(result));
        result.put(SecurityConstant.AUTHORIZATION_HEADER, jwtTokenGenerator.generateToken(agentId, UserTokenKeyType.LANDING_PAGE_ROLE));
        result.put(TokenProvider.AGENT_ID_KEY, landingPageWechatAppletConfig.getAgentId());
        return result;
    }

    /**
     * 小程序 - 获取不限制的小程序码（小程序页面预览，包含：编辑器预览、落地页预览、渠道列表预览）
     */
    public Object getwxacodeunlimit(Long advertiseAccountGroupId, String page, String scene, Boolean checkPath, String envVersion, Integer width) {
        //如果前端没有传渠道参数，则随机设置一个渠道
        //scene值为  ,iE2yVGFh,,0  第一个逗号前可能有值 也可能无值 需要进行判断
        //scene中的第一个值agentId是否为空
        boolean flag = false;
        if (StringUtils.isNotBlank(scene)) {
            String[] arr = scene.split(",");
            if (arr.length >= 1 && StringUtils.isBlank(arr[0])) {
                flag = true;
            }
            if (arr.length >= 3 && StringUtils.isBlank(arr[2])) {
                String token = arr[1];
                LandingPageChannel landingPageChannel = landingPageChannelService.getChannelByLandingPageToken(token);
                if (!Objects.isNull(landingPageChannel)) {
                    arr[2] = landingPageChannel.getChannelParam();
                    scene = String.join(",", Arrays.asList(arr));
                }
            }
        }
        LandingPageWechatAppletConfig landingPageWechatAppletConfig = this.getLandingPageWechatAppletConfig(TenantContextHolder.get(), advertiseAccountGroupId, null, LandingPageType.WECHAT_APPLET_MINI);
        LandingPageCommonParams landingPageCommonParams = new LandingPageCommonParams(CommonParamsType.GET_WECHAT_APPLET_PREVIEW_QR_CODE_PARAM, ((flag ? landingPageWechatAppletConfig.getAgentId() : "") + scene));
        final String uuidKye = landingPageCommonParams.getUuidKey();
        JSONObject params = new JSONObject();
        params.put("page", page);
        params.put("scene", uuidKye);
        params.put("check_path", checkPath);
        params.put("env_version", envVersion);
        params.put("width", width);
        Object result = OpenAppletCreateQrCodeApiClient.getQWechatAppletRCode(landingPageWechatAppletConfig.getAccessToken(), params);
        if (!Objects.isNull(result) && result instanceof String) {
            landingPageSender.sendSaveLandingPageCommonParams(landingPageCommonParams);
            return result;
        }
        log.error("小程序获取预览二维码错误 result={}；landingPageWechatAppletConfig={}；params={}；", result, JSONObject.toJSONString(landingPageWechatAppletConfig), params);
        throw new RestException(ErrorConstants.ERROR_GET_WECHAT_APPLET_PREVIEW_QR_CODE_IMAGE_ERROR);
    }

    /**
     * 小程序 - 授权获取手机号
     */
    public JSONObject businessGetuserphonenumber(String code, String wechatAppletAppid) {
        LandingPageWechatAppletConfigService bean = applicationContext.getBean(this.getClass());
        LandingPageWechatAppletConfig landingPageWechatAppletConfig = bean.getAndCacheLandingPageWechatAppletConfig(wechatAppletAppid);
        if (Objects.isNull(landingPageWechatAppletConfig)) {
            throw new RestException(ErrorConstants.ERROR_NOT_HAVE_CAN_USE_WECHAT_APPLET_CONFIG);
        }
        JSONObject params = new JSONObject();
        params.put("code", code);
        return wechatAppletApiClient.businessGetuserphonenumber(landingPageWechatAppletConfig.getAccessToken(), params);
    }

//    public static void main(String[] args) {
//        //普通页
//        //landing_page_wechat_applet_config:query_params:7ce297af2c984b16b9eb332dd7d4f87d
//        //String yiyeQueryStr = "customUrl=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzU3NzQxMjE3NA%3D%3D%26mid%3D2247483673%26idx%3D1%26sn%3Db58b2c84e1801959810c1823675536a6%26chksm%3Dfd044c38ca73c52eb6774d7ecd784d3e6bf9722f1dfff0cdea2baa14dc55225a589b144f8610%23rd&agentId=rele&yiye_pvParams=origin%3Dhttps%253A%252F%252Frelessl2.yiye.ai%26landingPageId%3D204%26uid%3Dba7afce9-37b0-z757-9ddf-913280b17ee3%26sid%3De57a0541-b833-z4c8-85ce-188c83c6d586%26pid%3Dbb058319-a394-z923-8ac2-30c5b6b983ce%26token%3DxzwWEiGb%26time%3D1656929729558%26url%3Dhttps%253A%252F%252Frelessl2.yiye.ai%252Frele%252FxzwWEiGb%253F_cl%253D46ef%2526openid%253DoTvo61fsi1PH_K3ztTmcTr2ze2YE%2526unionid%253Dosqg_57sbtt04dUTu1B6oMlVbcIc%26referrer%3D%26wechatOpenid%3DoTvo61fsi1PH_K3ztTmcTr2ze2YE%26agentId%3Drele%26advertiserAccountGroupId%3D7%26wechatUnionid%3Dosqg_57sbtt04dUTu1B6oMlVbcIc%26wechatAppletOpenid%3D%26wechatAppletUnionid%3D%26parentSubmitDataId%3D%26followOfficialAccountAppId%3Dwxb6461ca8b001ed28%26parentSubmitDataId%3Dundefined%26followOfficialAccountAppId%3Dwxb6461ca8b001ed28";
//        //landing_page_wechat_applet_config:query_params:0eee12e87d764130b67eeff3e243487b
//        String yiyeQueryStr = "customUrl=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzU3NzQxMjE3NA%3D%3D%26mid%3D2247483673%26idx%3D1%26sn%3Db58b2c84e1801959810c1823675536a6%26chksm%3Dfd044c38ca73c52eb6774d7ecd784d3e6bf9722f1dfff0cdea2baa14dc55225a589b144f8610%23rd&agentId=dbq&yiye_pvParams=origin%3Dhttp%253A%252F%252Faaa.dbq.yiye.ai%26landingPageId%3D933%26uid%3D64390453-d7d8-z943-8b73-f67768f7a19e%26sid%3D774ed1a7-55df-z2cb-b1ee-a734eafa72aa%26pid%3D1ee1aa82-953e-z092-be67-776529d9dd7a%26token%3DvjcCNCtq%26time%3D1656988628718%26url%3Dhttp%253A%252F%252Faaa.dbq.yiye.ai%252Fdbq%252FvjcCNCtq%253F_cl%253Df042%2526adid%253D1737478458607630%2526clickid%253DEI6Y0tKjh4sDGP2LsOGy9B0g_eyRtdWMnQYwDDjBuAJCIjIwMjIwNzA1MTAzNzAzMDEwMjEyMTcyMDQ3MURERTIxNjlIwbgCkAEA%2526creativeid%253D1737478458608654%2526creativetype%253D5%26referrer%3D%26wechatOpenid%3D%26agentId%3Ddbq%26advertiserAccountGroupId%3D113%26wechatUnionid%3D%26wechatAppletOpenid%3D%26wechatAppletUnionid%3D%26parentSubmitDataId%3D%26followOfficialAccountAppId%3Dwxb6461ca8b001ed28%26parentSubmitDataId%3Dundefined%26followOfficialAccountAppId%3Dwxb6461ca8b001ed28";
//        String currentUrl = UrlUtils.parseSingleParams("https://yiye.ai", yiyeQueryStr);
//        Map<String, String> yiyePvParams = UrlUtils.getYiyePvParams(currentUrl);
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.putAll(yiyePvParams);
//        PageViewForm pageViewForm = jsonObject.toJavaObject(PageViewForm.class);
//        pageViewForm.setWechatAppletOpenid("123")
//                .setWechatAppletUnionid("456")
//                .setOrigin(StringUtils.isNotBlank(pageViewForm.getOrigin()) ? URLDecoder.decode(pageViewForm.getOrigin()) : null)
//                .setUrl(StringUtils.isNotBlank(pageViewForm.getUrl()) ? URLDecoder.decode(pageViewForm.getUrl()) : null);
//        System.out.println(String.format("小程序内跳转公众号pageViewForm ：" + JSONObject.toJSONString(pageViewForm)));
//    }

    //    public static void main(String[] args) {
//        //原生页
//        //landing_page_wechat_applet_config:query_params:9c4651b6c7724bc8b357b9b71c6fd7fa
//        //String yiyeQueryStr = "agentId=rele&customUrl=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzU3NzQxMjE3NA%3D%3D%26mid%3D2247483673%26idx%3D1%26sn%3Db58b2c84e1801959810c1823675536a6%26chksm%3Dfd044c38ca73c52eb6774d7ecd784d3e6bf9722f1dfff0cdea2baa14dc55225a589b144f8610%23rd&followOfficialAccountAppId=wxb6461ca8b001ed28&aid=__AID__&cid=__CID__&clickid=__CLICKID__&pid=2b35b47517444ee0b333571e371fdfaf&yiye_pvParams=origin%3Dhttps%253A%252F%252Frele.asprelease.yiye-a.com%26landingPageId%3D204%26uid%3D2b35b47517444ee0b333571e371fdfaf%26sid%3D2b35b47517444ee0b333571e371fdfaf%26pid%3D2b35b47517444ee0b333571e371fdfaf%26time%3D%26token%3DxzwWEiGb%26url%3Dhttps%253A%252F%252Frele.asprelease.yiye-a.com%252Fsite%252Frele%252FxzwWEiGb%252Ff800%26referrer%3D%26wechatOpenid%3D%26agentId%3Drele%26advertiserAccountGroupId%3D7%26wechatUnionid%3D%26wechatAppletOpenid%3D%26wechatAppletUnionid%3D%26parentSubmitDataId%3D%26followOfficialAccountAppId%3Dwxb6461ca8b001ed28";
//        //landing_page_wechat_applet_config:query_params:b0dfe7ff713e4caeac9ad94c369c519e
//        String yiyeQueryStr = "agentId=dbq&customUrl=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzU3NzQxMjE3NA%3D%3D%26mid%3D2247483673%26idx%3D1%26sn%3Db58b2c84e1801959810c1823675536a6%26chksm%3Dfd044c38ca73c52eb6774d7ecd784d3e6bf9722f1dfff0cdea2baa14dc55225a589b144f8610%23rd&followOfficialAccountAppId=wxb6461ca8b001ed28&aid=****************&cid=****************&clickid=EI6IlYikh4sDGP2LsOGy9B0g_eyRtdWMnQYwDDjBuAJCIjIwMjIwNzA1MTAzODQzMDEwMjEyMDQ1MDgzMDU5REIxQkRIwbgCkAEA&pid=2104929c562c4aa6a14d753f96933368&yiye_pvParams=origin%3Dhttp%253A%252F%252Fbbb.dbq.yiye.ai%26landingPageId%3D933%26uid%3D2104929c562c4aa6a14d753f96933368%26sid%3D2104929c562c4aa6a14d753f96933368%26pid%3D2104929c562c4aa6a14d753f96933368%26time%3D%26token%3DvjcCNCtq%26url%3Dhttp%253A%252F%252Fbbb.dbq.yiye.ai%252Fsite%252Fdbq%252FvjcCNCtq%252F7bdb%253F_cl%253D7bdb%26referrer%3D%26wechatOpenid%3D%26agentId%3Ddbq%26advertiserAccountGroupId%3D113%26wechatUnionid%3D%26wechatAppletOpenid%3D%26wechatAppletUnionid%3D%26parentSubmitDataId%3D%26followOfficialAccountAppId%3Dwxb6461ca8b001ed28";
//        String currentUrl = UrlUtils.parseSingleParams("https://yiye.ai", yiyeQueryStr);
//        Map<String, String> yiyePvParams = UrlUtils.getYiyePvParams(currentUrl);
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.putAll(yiyePvParams);
//        PageViewForm pageViewForm = jsonObject.toJavaObject(PageViewForm.class);
//        pageViewForm.setWechatAppletOpenid("123")
//                .setWechatAppletUnionid("456")
//                .setOrigin(StringUtils.isNotBlank(pageViewForm.getOrigin()) ? URLDecoder.decode(pageViewForm.getOrigin()) : null)
//                .setUrl(StringUtils.isNotBlank(pageViewForm.getUrl()) ? URLDecoder.decode(pageViewForm.getUrl()) : null);
//        System.out.println(String.format("小程序内跳转公众号pageViewForm ：" + JSONObject.toJSONString(pageViewForm)));
//    }

    /**
     * //公众号处理
     */
    public PageViewInfo updateOfficialPv(String agentId, String yiyeQueryId, String yiyeQueryStr, String openid, String unionid, HttpServletRequest request, String userAgent) {
        try {
            if (StringUtils.contains(yiyeQueryStr, "yiye_pvParams")) {
                //构造 PageViewForm pageView
                String currentUrl = UrlUtils.parseSingleParams(UrlUtils.URL_DOMAIN, yiyeQueryStr);
                Map<String, String> yiyePvParams = UrlUtils.getYiyePvParams(currentUrl);
                if (ObjectUtils.isEmpty(yiyePvParams)) {
                    log.warn("公众号链接解析异常! currentUrl:{}", currentUrl);
                    return null;
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.putAll(yiyePvParams);
                PageViewForm pageViewForm = jsonObject.toJavaObject(PageViewForm.class);
                pageViewForm.setWechatAppletOpenid(openid)
                    .setWechatAppletUnionid(unionid)
                    .setOrigin(StringUtils.isNotBlank(pageViewForm.getOrigin()) ? URLDecoder.decode(pageViewForm.getOrigin()) : null)
                    .setUrl(StringUtils.isNotBlank(pageViewForm.getUrl()) ? URLDecoder.decode(pageViewForm.getUrl()) : null);
                log.info("小程序内跳转公众号pageViewForm {} ", JSONObject.toJSONString(pageViewForm));
                PageViewInfo pageViewInfoOld = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                    .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoQueryTime()))
                    .eq(PageViewInfo::getPid, pageViewForm.getPid()), false);
                //兼容多次调用覆盖数据的场景
                if (!Objects.isNull(pageViewInfoOld) && (YesOrNoEnum.YES.equals(pageViewInfoOld.getClickJumpToWoaStatus()) || FollowStatus.FOLLOW.equals(pageViewInfoOld.getFollowOfficialAccountStatus()))) {
                    return pageViewInfoOld;
                }
                pageViewInfoOld = Objects.isNull(pageViewInfoOld) ? new PageViewInfo() : pageViewInfoOld;
                PageViewInfo pageViewInfo = UrlUtils.setPageViewInfoCommonValues(pageViewForm, request, userAgent);
                pageViewInfo
                    //填单参数
                    .setParentSubmitDataId(pageViewInfoOld.getParentSubmitDataId())
                    //小程序加粉参数
                    .setIdentifyQrCodeStatus(pageViewInfoOld.getIdentifyQrCodeStatus())
                    .setWechatCustomerServiceId(pageViewInfoOld.getWechatCustomerServiceId())
                    .setWechatCustomerServiceGroupId(pageViewInfoOld.getWechatCustomerServiceGroupId())
                    .setAddEnterpriseWechatStatus(pageViewInfoOld.getAddEnterpriseWechatStatus())
                    //关注公众号参数
                    .setClickJumpToWoaStatus(YesOrNoEnum.YES)
                    .setFollowOfficialAccountStatus(FollowStatus.NOT_FOLLOW)
                    .setFollowOfficialAccountAppId(pageViewForm.getFollowOfficialAccountAppId())
                    //错误信息
                    .setWcsMatchingErrorStatus(pageViewInfoOld.getWcsMatchingErrorStatus())
                    .setIdentifyQrCodeWcsId(pageViewInfoOld.getIdentifyQrCodeWcsId())
                    .setIdentifyQrCodeWcsUserId(pageViewInfoOld.getIdentifyQrCodeWcsUserId())
                    .setIdentifyQrCodeWcsUserName(pageViewInfoOld.getIdentifyQrCodeWcsUserName())
                    .setCbUserIdMatchingWcsId(pageViewInfoOld.getCbUserIdMatchingWcsId())
                    .setCbUserIdMatchingWcsUserName(pageViewInfoOld.getCbUserIdMatchingWcsUserName())
                    .setWcsMatchingErrorMessage(pageViewInfoOld.getWcsMatchingErrorMessage())
                    // ua flowSource 设置为空 防止 小程序内跳转公众号历史文章页 获取了 新的ua 覆盖了一条页面的UA 信息
                    .setUa(null)
                    .setFlowSource(null)
                    .setWechatOfficialHistoryArticleStatus(WechatOfficialHistoryArticleStatus.OFFICIAL_HISTORY_ARTICLE_PAGE)
                    //批量更新clickhouse标识
                    .setUpdatedAt(Instant.now())
                    .setWechatAppletOpenid(openid)
                    .setWechatAppletUnionid(unionid);
                /**
                 * unionid openid 兑换 pending_id
                 */
                try {
                    //页面是否为小程序访问，并且 已经获取到小程序访问unionid 与 openid
                    //小程序原生页场景
                    //小程序h5场景
                    //填单后跳转小程序场景
                    Long advertiserAccountGroupId = pageViewInfo.getAdvertiserAccountGroupId();
                    //兑换后查看是否需要 pading_id
                    EnterpriseWechatExternalUserAuthData enterpriseWechatExternalUserAuthData = enterpriseWechatExternalUserAuthDataService.getEnterpriseWechatData(advertiserAccountGroupId, unionid, openid);
                    pageViewInfo.setPendingId(enterpriseWechatExternalUserAuthData.getPendingId())
                        .setWechatExternalUserid(enterpriseWechatExternalUserAuthData.getExternalUserid());
                } catch (Exception e) {
                    log.error("unionid_to_external_userid error.. [{}] {} {} {}", pageViewInfo.getAdvertiserAccountGroupId(), Objects.nonNull(pageViewInfo.getWechatAppletUnionid()) ? pageViewInfo.getWechatAppletUnionid() : pageViewInfo.getWechatUnionid(), Objects.nonNull(pageViewInfo.getWechatAppletOpenid()) ? pageViewInfo.getWechatAppletOpenid() : pageViewInfo.getWechatOpenid(), e);
                }
                pageViewinfoSender.pageViewInitFollowOfficialAccount(pageViewInfo);

                return pageViewInfo;
            }
        } catch (Exception e) {
            log.error("小程序跳转公众号关注解析异常! {} {} {}", agentId, yiyeQueryId, yiyeQueryStr, e);
        }
        return null;
    }


    /**
     * @param templateId
     * @param extJson
     * @param accessToken
     * @param version
     * @param desc
     * @return
     */
    public LandingPageWechatAppletConfig publishMimiTemplate(Long id, String templateId, String extJson, String accessToken, String version, String desc) {

        AppletTemplateType descTemplateType = AppletTemplateType.getTemplateTypeByDesc(desc);

        //上传代码的情况
        LandingPageWechatAppletConfig landingPageWechatAppletConfig = landingPageWechatAppletConfigService.getById(id);
        String expVersionStr = null;
        AppletTemplateType expTemplateType = null;
        String releaseVersionStr = null;
        AppletTemplateType releaseTemplateType = null;
        JSONObject jsonObject = openWeixinApiClient.versionInfo(accessToken, new HashMap<>());

        //这里需要讨论，如果这里的他没有提交过，这里应该是空的
        JSONObject expInfo = jsonObject.getJSONObject("exp_info");
        //获取到体验版的版本号
        if (Objects.nonNull(expInfo)) {
            expVersionStr = expInfo.getString("exp_version");
            String expDesc = expInfo.getString("exp_desc");
            expTemplateType = AppletTemplateType.getTemplateTypeByDesc(expDesc);

        }
        //现在的版本号
        JSONObject releaseInfo = jsonObject.getJSONObject("release_info");
        if (Objects.nonNull(releaseInfo)) {
            releaseVersionStr = releaseInfo.getString("release_version");
            String releaseDesc = releaseInfo.getString("release_desc");
            releaseTemplateType = AppletTemplateType.getTemplateTypeByDesc(releaseDesc);
        }
        landingPageWechatAppletConfig.setNextVersion(version);
        //现有版本与正式版本一致 直接审核通过 并可以正式使用
        JSONObject latestAuditStatus = openWechatApiClient.getLatestAuditStatus(accessToken);
        Integer auditStatus = null;
        String userVersion = null;
        AppletTemplateType userTemplateType = null;
        String auditid = null;
        if (Objects.nonNull(latestAuditStatus) && latestAuditStatus.getInteger("errcode") == 0) {
            auditStatus = latestAuditStatus.getInteger("status");
            userVersion = latestAuditStatus.getString("user_version");
            String userDesc = latestAuditStatus.getString("user_desc");
            userTemplateType = AppletTemplateType.getTemplateTypeByDesc(userDesc);
            auditid = latestAuditStatus.getString("auditid");
        }

        /**
         * 已发布的版本 与 要升级的版本一致
         */
        if (StringUtils.equals(releaseVersionStr, version) && releaseTemplateType.equals(descTemplateType)) {
            landingPageWechatAppletConfig
                .setAccess(true)
                .setAuditid(auditid)
                .setVersion(version)
                .setAuditStatus(WeAppAuditStatusEnum.WEAPP_AUDIT_SUCCESS);
            return landingPageWechatAppletConfig;
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("template_id", templateId);
        queryMap.put("ext_json", extJson);
        queryMap.put("user_version", version);
        queryMap.put("user_desc", desc);
        //预览版本与现有版本不一致
        /**
         * 预览版本与现有版本不一致 需要上传模板
         */
        if (!StringUtils.equals(expVersionStr, version) || !expTemplateType.equals(descTemplateType)) {
            //上传代码的模板
            openWeixinApiClient.publishTemplate(accessToken, queryMap);
        }

        /**
         * 审核中的版本与现有版本不一致 需要撤销审核
         */
        //审核中的版本与现有版本不一致
        if (Objects.nonNull(latestAuditStatus)
            && (Objects.equals(auditStatus, 2) || Objects.equals(auditStatus, 4))
            && (!StringUtils.equals(userVersion, version) || !userTemplateType.equals(descTemplateType))) {
            //撤销审核
            openWeixinApiClient.undocodeaudit(accessToken);
        }
        //设置小程序用户隐私保护指引
        openWeixinApiClient.setPrivacySetting(accessToken, JSONObject.parseObject(agentConf.getOpenWeixinPrivacySetting()));
        //当前版本已经审核通过直接发布
        if (StringUtils.equals(expVersionStr, userVersion) && !StringUtils.equals(expVersionStr, releaseVersionStr) && Objects.equals(auditStatus, 0)) {
            //发布
            wxOpenService.wxaRelease(landingPageWechatAppletConfig);
            landingPageWechatAppletConfig.setAccess(true)
                .setAuditStatus(WeAppAuditStatusEnum.WEAPP_AUDIT_SUCCESS)
                .setRejectReason(null).setVersion(expVersionStr)
                .setNextVersion(expVersionStr);
            return landingPageWechatAppletConfig;
        }
        //提交审核
        JSONObject submitAuditData = openWeixinApiClient.submitAudit(accessToken, new HashMap<>());
        log.info("审核信息====" + JSON.toJSONString(submitAuditData));
        if (Objects.nonNull(submitAuditData) && submitAuditData.getInteger("errcode") == 0) {
            landingPageWechatAppletConfig
                .setAuditid(submitAuditData.getString("auditid"))
                .setAuditStatus(!WeAppAuditStatusEnum.WEAPP_AUDIT_SUCCESS.equals(landingPageWechatAppletConfig.getAuditStatus()) ? WeAppAuditStatusEnum.WEAPP_AUDITING : null);
        }
        return landingPageWechatAppletConfig;
    }

    /*public static void main(String[] args) {
        System.out.println(JSONObject.toJSONString(JSONObject.parseObject("{\n" +
            "    \"owner_setting\" : {\n" +
            "       \"contact_weixin\" : \"wyw930123\",\n" +
            "       \"notice_method\" : \"公告\"\n" +
            "    },\n" +
            "    \"setting_list\" : [\n" +
            "       {\n" +
            "          \"privacy_key\" : \"PhoneNumber\",\n" +
            "          \"privacy_text\" : \"预约报名\"\n" +
            "       }\n" +
            "    ],\n" +
            "    \"sdk_privacy_info_list\":[\n" +
            "        {\n" +
            "            \"sdk_name\":\"yiye\",\n" +
            "            \"sdk_biz_name\":\"北京一数一叶有限公司\",\n" +
            "            \"sdk_list\":[\n" +
            "                {\n" +
            "                    \"privacy_key\":\"PhoneNumber\",\n" +
            "                    \"privacy_text\":\"预约报名\"\n" +
            "                }\n" +
            "                \n" +
            "            ]\n" +
            "        }\n" +
            "    ],\n" +
            "    \"privacy_ver\":2\n" +
            " }")));
    }*/

    /**
     * 获取下个一兼容版本
     *
     * @param version
     * @return
     */
    public static String getNextVersion(String version) {
        String[] split = StringUtils.split(version, ".");
        //
        if (!Objects.isNull(split) && split.length <= 3) {
            return version + ".0";
        } else {
            int index = split.length - 1;
            String c = split[index];
            c = (Integer.valueOf(c) + 1) + "";
            split[index] = c;
            List<String> strings = Arrays.asList(split);
            String collect = strings.stream().collect(Collectors.joining("."));
            return collect;
        }
    }

    /**
     * 校验下个版本是否已经推送过
     * false 没有发布过
     * true 已经发布过
     *
     * @return
     */
    public static boolean checkoutNextVersion(String currentVersion, String templateVersion) {
        if (StringUtils.isNotBlank(currentVersion) && StringUtils.isNotBlank(templateVersion)) {
            String[] split = StringUtils.split(currentVersion, ".");
            String[] split1 = StringUtils.split(templateVersion, ".");
            if (split.length == split1.length && StringUtils.equals(currentVersion, templateVersion)) {
                return true;
            } else if (split.length > split1.length) {
                List<String> strings = new ArrayList<>(Arrays.asList(split));
                strings.remove(strings.size() - 1);
                String collect = strings.stream().collect(Collectors.joining("."));
                if (StringUtils.equals(collect, templateVersion)) {
                    return true;
                }
            }
        }
        return false;
    }


    public LandingPageWechatAppletConfig refreshAppleteStatus(Long id) {
        LandingPageWechatAppletConfig appletConfig = landingPageWechatAppletConfigMapper.selectById(id);
        // 查询已经绑定的开放平台
        try {
            JSONObject openInfoByAppId = openWeixinApiClient.getOpenInfoByAppId(appletConfig.getAccessToken(), new HashMap<>());
            OpenGetByAppIdResponseBody openGetByAppIdResponseBody = new WechatPropertyMapper(openInfoByAppId).mapProperty(OpenWechatMappers.WECHAT_OPEN_GET).unwrap(OpenGetByAppIdResponseBody.class);
            //开放平台openAppid
            String openAppid = openGetByAppIdResponseBody.getOpenAppid();
        } catch (Exception e) {
            log.warn("LandingPageWechatAppletConfigService.refreshAppleteStatus刷新小程序状态调用接口异常", e);
        }
        return null;


    }

    /**
     * 获取pmp列表
     *
     * @param from
     * @param id                       id
     * @param user
     * @param advertiserAccountGroupId 项目ID不为空时，查询此项目ID当前生效的企微，查询与此企微相关联的pmp列表
     * @return
     */
    public Set<AdvertiserAccountGroup> getPmpGroup(String from, Long id, User user, Long advertiserAccountGroupId) {
        String agentId = TenantContextHolder.get();
        Set<AdvertiserAccountGroup> advertiserAccountGroups = advertiserAccountGroupRemote.listUserAccountGroup(user.getId()).stream()
            .sorted(Comparator.comparing(AdvertiserAccountGroup::getCreatedAt).reversed()).collect(Collectors.toCollection(LinkedHashSet::new));
        log.info("当前获取的pmp列表为：{}", advertiserAccountGroups);
        if (CollectionUtils.isEmpty(advertiserAccountGroups)) {
            log.info("当前客户环境不存在项目组，请先创建");
            return null;
        }
        switch (from) {
            case "enterpriseWechat-develop"://企业微信代开发应用
                EnterpriseWechat enterpriseWechat = enterpriseWechatService.getById(id);
                if (ObjectUtils.isEmpty(enterpriseWechat)) {
                    log.info("==>共享pmp,查询企微id:{}不存在", id);
                    return null;
                }
                //获取企业微信转增列表，需要过滤所有拥有企业微信的pmp，每个pmp只能由一个企业微信
                List<EnterpriseWechatsPmpRel> wechatsPmpRels = enterpriseWechatsPmpRelService.list(new LambdaQueryWrapper<EnterpriseWechatsPmpRel>().
                    select(EnterpriseWechatsPmpRel::getAdvertiserAccountGroupId).eq(EnterpriseWechatsPmpRel::getEnterpriseWechatsId, id).eq(EnterpriseWechatsPmpRel::getAgentId, agentId));
                if (CollectionUtils.isEmpty(wechatsPmpRels)) {
                    log.info("当前企业微信未绑定任何pmp，ID：{}", id);
                } else {
                    ai.yiye.agent.domain.AgentConf agent = agentConfService.getOne(Wrappers.lambdaQuery(ai.yiye.agent.domain.AgentConf.class)
                        .select(ai.yiye.agent.domain.AgentConf::getLicense).eq(ai.yiye.agent.domain.AgentConf::getAgentId, TenantContextHolder.get()));
                    ai.yiye.agent.domain.AgentConf.License license = agent.getLicense();
                    //白名单-项目下支持多企微授权
                    BaseStatusEnum multiEnterprise = license.getMultiEnterprise();
                    Set<Long> pmpRelGroupIds;
                    if (BaseStatusEnum.ENABLE.equals(multiEnterprise)) {
                        //开通白名单 PMP内无当前共享企业微信自建应用授权
                        pmpRelGroupIds = wechatsPmpRels.stream().map(EnterpriseWechatsPmpRel::getAdvertiserAccountGroupId).collect(Collectors.toSet());
                    } else {
                        //未开通白名单 PMP内无企业微信自建应用授权
                        pmpRelGroupIds = enterpriseWechatsPmpRelService.list(new LambdaQueryWrapper<EnterpriseWechatsPmpRel>().
                                select(EnterpriseWechatsPmpRel::getAdvertiserAccountGroupId)
                                .eq(EnterpriseWechatsPmpRel::getAgentId, agentId)
                                .eq(EnterpriseWechatsPmpRel::getEnterpriseWechatType, EnterpriseWechatType.GENERATION_DEVELOPMENT))
                            .stream().map(EnterpriseWechatsPmpRel::getAdvertiserAccountGroupId).collect(Collectors.toSet());
                    }
                    Set<Long> finalPmpRelGroupIds = pmpRelGroupIds;
                    advertiserAccountGroups.removeAll(advertiserAccountGroups.stream().filter(g -> finalPmpRelGroupIds.contains(g.getId())).collect(Collectors.toSet()));
                }
                //还需要移除掉绑定企业推的且corpid不一致的项目  取消授权或共享时企业微信一致的校验 29832
//                List<Long> pmpIds = qiyetuiAppPmpRelService.searchListByAgentAndNeCorpId(agentId, enterpriseWechat.getCorpid());
//                if (!org.springframework.util.CollectionUtils.isEmpty(pmpIds) && !org.springframework.util.CollectionUtils.isEmpty(advertiserAccountGroups)) {
//                    advertiserAccountGroups.removeAll(advertiserAccountGroups.stream().filter(g -> pmpIds.contains(g.getId())).collect(Collectors.toSet()));
//                }
                return advertiserAccountGroups;
            case "wechatApplet":
                //获取小程序转增列表，过滤所有有该小程序的pmp
                List<LandingPageWechatAppletPmpRel> wechatAppletPmpRels = landingPageWechatAppletPmpRelService.list(new LambdaQueryWrapper<LandingPageWechatAppletPmpRel>()
                    .select(LandingPageWechatAppletPmpRel::getAdvertiserAccountGroupId).eq(LandingPageWechatAppletPmpRel::getLandingPageWechatAppletId, id).eq(LandingPageWechatAppletPmpRel::getAgentId, TenantContextHolder.get()));
//                Set<LandingPageWechatAppletPmpRel> wechatAppletPmpRels = new HashSet<>(wechatAppletPmpRelLists);
                if (CollectionUtils.isEmpty(wechatAppletPmpRels)) {
                    log.info("当前小程序未绑定任何pmp，ID：{}", id);
                } else {
                    Set<Long> wechatAppletPmpRelGroupIds = wechatAppletPmpRels.stream().map(LandingPageWechatAppletPmpRel::getAdvertiserAccountGroupId).collect(Collectors.toSet());
                    log.info("拥有当前小程序的pmpId集合：{}", wechatAppletPmpRelGroupIds);
                    advertiserAccountGroups.removeAll(advertiserAccountGroups.stream().filter(g -> wechatAppletPmpRelGroupIds.contains(g.getId())).collect(Collectors.toSet()));
                }
                //pmpId不为空，查询此pmp企微相关联pmp列表
                if (!ObjectUtils.isEmpty(advertiserAccountGroupId)) {
                    Set<AdvertiserAccountGroup> filterPmpList = this.wechatAppletPmpFilter(advertiserAccountGroups, advertiserAccountGroupId);
                    return filterPmpList;
                }
                return advertiserAccountGroups;
            case "wechatOfficialAccount":
                //获取公众号转增列表，过滤所有有该公众号的pmp
                List<LandingPageWechatOfficialAccountRel> wechatOfficialAccountRels = landingPageWechatOfficialAccountRelService.list(new LambdaQueryWrapper<LandingPageWechatOfficialAccountRel>().eq(LandingPageWechatOfficialAccountRel::getWechatOfficialAccountId, id));
                if (CollectionUtils.isEmpty(wechatOfficialAccountRels)) {
                    log.info("当前公众号未绑定任何pmp，ID：{}", id);
                } else {
                    Set<Long> wechatOfficialAccountRelGroupIds = wechatOfficialAccountRels.stream().map(LandingPageWechatOfficialAccountRel::getAdvertiserAccountGroupId).collect(Collectors.toSet());
                    log.info("拥有当前公众号的pmpId集合：{}", wechatOfficialAccountRelGroupIds);
                    advertiserAccountGroups.removeAll(advertiserAccountGroups.stream().filter(g -> wechatOfficialAccountRelGroupIds.contains(g.getId())).collect(Collectors.toSet()));
                }
                return advertiserAccountGroups;
            case "paymentWechatOfficialAccount":
                //获取公众号转增列表，过滤所有有该公众号的pmp
                List<PaymentWechatOfficialAccountPmpRel> paymentWechatOfficialAccountPmpRels = paymentWechatOfficialAccountPmpRelService.list(new LambdaQueryWrapper<PaymentWechatOfficialAccountPmpRel>().eq(PaymentWechatOfficialAccountPmpRel::getWechatOfficialAccountId, id));
                if (CollectionUtils.isEmpty(paymentWechatOfficialAccountPmpRels)) {
                    log.info("微信支付当前公众号未绑定任何pmp，ID：{}", id);
                } else {
                    Set<Long> wechatOfficialAccountRelGroupIds = paymentWechatOfficialAccountPmpRels.stream().map(PaymentWechatOfficialAccountPmpRel::getAdvertiserAccountGroupId).collect(Collectors.toSet());
                    log.info("微信支付拥有当前公众号的pmpId集合：{}", wechatOfficialAccountRelGroupIds);
                    advertiserAccountGroups.removeAll(advertiserAccountGroups.stream().filter(g -> wechatOfficialAccountRelGroupIds.contains(g.getId())).collect(Collectors.toSet()));
                }
                return advertiserAccountGroups;
            case "qiyetui"://企业推应用
                //获取企业微信转增列表，需要过滤所有拥有企业微信的pmp，每个pmp只能由一个企业微信
                qiyetuiPmpFilter(advertiserAccountGroups, id);
                return advertiserAccountGroups;
            case "douyin-applet":
                //获取字节小程序转增列表，过滤所有有该字节小程序的pmp
                douyinAppletPmpFilter(id, advertiserAccountGroups);
                return advertiserAccountGroups;
            case "session-archive":
                //获取会话存档转增列表，过滤所有有该会话存档的pmp
                return sessionArchivePmpFilter(id, advertiserAccountGroups);
            default:
                return null;
        }
    }

    private void qiyetuiPmpFilter(Set<AdvertiserAccountGroup> advertiserAccountGroups, Long id) {
        String agentId = TenantContextHolder.get();
        List<QiyetuiAppPmpRel> pmpRels = qiyetuiAppPmpRelService.list(new LambdaQueryWrapper<QiyetuiAppPmpRel>().
            select(QiyetuiAppPmpRel::getAdvertiserAccountGroupId)
            .eq(QiyetuiAppPmpRel::getAgentId, agentId)
            .eq(QiyetuiAppPmpRel::getQiyeAppId, id));
        if (!org.springframework.util.CollectionUtils.isEmpty(pmpRels)) {
            Set<Long> pmpRelGroupIds = pmpRels.stream().map(QiyetuiAppPmpRel::getAdvertiserAccountGroupId).collect(Collectors.toSet());
            log.info("----拥有当前企业微信的pmpId集合：{}", pmpRelGroupIds);
            advertiserAccountGroups.removeAll(advertiserAccountGroups.stream().filter(g -> pmpRelGroupIds.contains(g.getId())).collect(Collectors.toSet()));
        }
        //过滤完有了的之后，过滤权限问题
        List<Long> pmpIdList = bossBackendRemote.getByWhiteType(agentId, WhiteType.WECHAT_QIYETUI.getValue());
        if (!org.springframework.util.CollectionUtils.isEmpty(pmpIdList)) {
            log.info("当前有企业推权限的集合：{}", pmpIdList);
            advertiserAccountGroups.removeAll(advertiserAccountGroups.stream().filter(g -> !pmpIdList.contains(g.getId())).collect(Collectors.toSet()));
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(advertiserAccountGroups)) {
            QiyetuiApp qiyetuiApp = qiyetuiAppService.getById(id);
            if (ObjectUtils.isEmpty(qiyetuiApp)) {
                return;
            }
            //过滤掉绑定了代开发但不是此企微corpid的项目列表  取消授权或共享时企业微信一致的校验  ones  29832
//            List<Long> pmpIds = enterpriseWechatService.searchListByAgentIdAndNeCorpid(agentId, qiyetuiApp.getCorpid(), EnterpriseWechatType.GENERATION_DEVELOPMENT);
//            advertiserAccountGroups.removeAll(advertiserAccountGroups.stream().filter(g -> pmpIds.contains(g.getId())).collect(Collectors.toSet()));
        }
    }

    private void douyinAppletPmpFilter(Long id, Set<AdvertiserAccountGroup> advertiserAccountGroups) {
        List<LandingPageDouyinAppletPmpRel> rels = landingPageDouyinAppletPmpRelService.list(new LambdaQueryWrapper<LandingPageDouyinAppletPmpRel>().eq(LandingPageDouyinAppletPmpRel::getLandingPageDouyinAppletId, id));
        if (CollectionUtils.isEmpty(rels)) {
            log.info("当前字节小程序未绑定任何pmp，ID：{}", id);
        } else {
            Set<Long> relGroupIds = rels.stream().map(LandingPageDouyinAppletPmpRel::getAdvertiserAccountGroupId).collect(Collectors.toSet());
            log.info("拥有当前字节小程序的pmpId集合：{}", relGroupIds);
            advertiserAccountGroups.removeAll(advertiserAccountGroups.stream().filter(g -> relGroupIds.contains(g.getId())).collect(Collectors.toSet()));
        }
        //过滤字节小程序的权限
        List<Long> pmpIdList = bossBackendRemote.getByWhiteType(TenantContextHolder.get(), WhiteType.DOUYIN_APPLET.getValue());
        log.info("拥有当前字节小程序的有权限的pmpId集合：{}", pmpIdList);
        if (!CollectionUtils.isEmpty(pmpIdList)) {
            advertiserAccountGroups.removeAll(advertiserAccountGroups.stream().filter(g -> !pmpIdList.contains(g.getId())).collect(Collectors.toSet()));
        }
    }

    private Set<AdvertiserAccountGroup> sessionArchivePmpFilter(Long id, Set<AdvertiserAccountGroup> advertiserAccountGroups) {
        String agentId = TenantContextHolder.get();
        List<EnterpriseWechatSessionArchiveRel> rels = enterpriseWechatSessionArchiveRelService.list(new LambdaQueryWrapper<EnterpriseWechatSessionArchiveRel>().eq(EnterpriseWechatSessionArchiveRel::getSessionArchiveId, id));
        //使用相同企业微信自建应用代开发的pmpId
        List<Long> pmpIds = enterpriseWechatSessionArchiveService.getEnterpriseWechatPmpId(id);
        //获取有会话存档权限的pmpId
        List<Long> pmpIdList = bossBackendRemote.getByWhiteType(agentId, WhiteType.WORK_WECHAT_SESSION_FILE_CONFIG.getValue());
        if (CollectionUtils.isEmpty(rels)) {
            log.info("当前会话存档未绑定任何pmp，ID：{}", id);
        } else {
            Set<Long> relGroupIds = rels.stream().map(EnterpriseWechatSessionArchiveRel::getAdvertiserAccountGroupId).collect(Collectors.toSet());
            log.info("拥有当前会话存档的pmpId集合：{}", relGroupIds);
            advertiserAccountGroups.removeAll(advertiserAccountGroups.stream().filter(g -> relGroupIds.contains(g.getId())).collect(Collectors.toSet()));
        }
        advertiserAccountGroups = advertiserAccountGroups.stream().filter(g -> pmpIds.contains(g.getId()) && pmpIdList.contains(g.getId())).collect(Collectors.toSet());
        return advertiserAccountGroups;
    }

    /**
     * 获取全部pmp列表
     *
     * @param user
     * @param advertiserAccountGroupId
     * @return
     */
    public Set<AdvertiserAccountGroup> getPmpGroupList(User user, Long advertiserAccountGroupId) {
        LinkedHashSet<AdvertiserAccountGroup> advertiserAccountGroups = this.advertiserAccountGroupRemote.listUserAccountGroup(user.getId()).stream()
            .sorted(Comparator.comparing(AdvertiserAccountGroup::getCreatedAt).reversed()).collect(Collectors.toCollection(LinkedHashSet::new));
        //pmpId不为空，查询此pmp企微相关联pmp列表
        if (!ObjectUtils.isEmpty(advertiserAccountGroupId)) {
            Set<AdvertiserAccountGroup> accountGroups = this.wechatAppletPmpFilter(advertiserAccountGroups, advertiserAccountGroupId);
            return accountGroups;
        }
        return advertiserAccountGroups;
    }

    /**
     * 过滤出相同企微下的pmp列表
     *
     * @param advertiserAccountGroups
     * @param advertiserAccountGroupId
     * @return
     */
    private Set<AdvertiserAccountGroup> wechatAppletPmpFilter(Set<AdvertiserAccountGroup> advertiserAccountGroups, Long advertiserAccountGroupId) {
        String agentId = TenantContextHolder.get();
        //查询当前pmp生效的企微
        //1.197.0 去除自建应用相关代码 只需查询代开发应用
        EnterpriseWechat enterpriseWechat = this.enterpriseWechatService.getEnterpriseWechatNew(agentId, advertiserAccountGroupId);
        if (ObjectUtils.isEmpty(enterpriseWechat)) {
            return null;
        }
        //查询相同企微下的pmp列表
        List<EnterpriseWechatsPmpRel> pmpRelByEnterpriseWechat = this.enterpriseWechatsPmpRelService.getPmpRelByEnterpriseWechat(enterpriseWechat, agentId);
        if (CollectionUtils.isEmpty(pmpRelByEnterpriseWechat)) {
            return null;
        }
        List<Long> advertiserAccountIds = pmpRelByEnterpriseWechat.stream().map(EnterpriseWechatsPmpRel::getAdvertiserAccountGroupId).collect(Collectors.toList());
        //advertiserAccountGroups已排除掉本身关联的pmp之后，再与查询到的关联相同企微的pmp 取交集
        Set<AdvertiserAccountGroup> collect = advertiserAccountGroups.stream().filter(e -> advertiserAccountIds.contains(e.getId())).collect(Collectors.toSet());
        return collect;
    }

    private void refreshAppletToken(LandingPageWechatAppletConfig landingPageWechatAppletConfig) {
        log.info("开始刷新指定小程序token，APPID：{}", landingPageWechatAppletConfig.getWechatAppletAppid());
        //this.refreshAppletAccessToken(landingPageWechatAppletConfig); // 已废弃
//        String componentAppId = wxOpenRedis.getComponentAppId(landingPageWechatAppletConfig.getComponentAppId());
//        Map<String, String> map = new HashMap<>();
//        map.put(componentAppId, wxOpenRedis.getComponentAccessToken(componentAppId));
//        wxOpenService.refreshAppletAccessTokenByEntity(landingPageWechatAppletConfig, null);
        wxOpenService.refreshAppletAccessTokenByEntity(landingPageWechatAppletConfig);
    }

    //private void refreshAppletAccessToken(LandingPageWechatAppletConfig landingPageWechatAppletConfig) {
    //    String componentAppId = wxOpenRedis.getComponentAppId(landingPageWechatAppletConfig.getComponentAppId());
    //    String accessToken = wxOpenRedis.getComponentAccessToken(componentAppId);
    //    Map<Object, Object> resultMap = new HashMap<>();
    //    resultMap.put("component_appid", componentAppId);
    //    resultMap.put("authorizer_appid", landingPageWechatAppletConfig.getWechatAppletAppid());
    //    resultMap.put("authorizer_refresh_token", landingPageWechatAppletConfig.getRefreshToken());
    //    try {
    //        JSONObject result = openWeixinApiClient.getApiAuthToken(accessToken, resultMap);
    //        LandingPageWechatAppletConfig marketingWechatMiniProgram = landingPageWechatAppletConfig;
    //        marketingWechatMiniProgram.setAccessToken(result.getString("authorizer_access_token"));
    //        marketingWechatMiniProgram.setRefreshToken(result.getString("authorizer_refresh_token"));
    //        //刷新token时，维护一个过期时间，
    //        Integer expiresIn = result.getInteger("expires_in");
    //        Instant expireTime = Instant.now().plusSeconds(expiresIn);
    //        marketingWechatMiniProgram.setExpireTime(expireTime);
    //        marketingWechatMiniProgram.setUpdatedAt(Instant.now());
    //        landingPageWechatAppletConfigService.saveOrUpdate(marketingWechatMiniProgram);
    //        log.info("当前小程序使用时，token过期，已经刷新当前小程序token，小程序 AppletOriginalId：{}, ID: {}"
    //            , landingPageWechatAppletConfig.getAppletOriginalId(), landingPageWechatAppletConfig.getId());
    //        defaultObjectRedisTemplate.boundValueOps(RedisConstant.LANDING_PAGE_WECHAT_APPLET_REFRESH_TIMES(landingPageWechatAppletConfig.getId()))
    //            .set(1, 5, TimeUnit.MINUTES);
    //    } catch (MarketingWeOpenApiException e) {
    //        defaultObjectRedisTemplate.boundValueOps(RedisConstant.LANDING_PAGE_WECHAT_APPLET_REFRESH_TIMES(landingPageWechatAppletConfig.getId()))
    //            .set(1, 5, TimeUnit.MINUTES);
    //        log.warn("小程序刷新token状态码异常 :{}", JSON.toJSONString(landingPageWechatAppletConfig), e);
    //    } catch (Exception e) {
    //        log.error("小程序-刷新令牌token报错-applet:{}", JSON.toJSONString(landingPageWechatAppletConfig), e);
    //    } finally {
    //        landingPageWechatAppletConfigRedis.deleteWechatByAgentId(landingPageWechatAppletConfig.getAgentId(), landingPageWechatAppletConfig.getEnterpriseWechatType());
    //        landingPageWechatAppletConfigRedis.deleteWechatByAppid(landingPageWechatAppletConfig.getWechatAppletAppid());
    //    }
    //}

    public List<LandingPageWechatAppletConfig> getByLandingPageIds(List<String> ids, String agentId) {
        return baseMapper.getByLandingPageIds(ids, agentId);
    }

    public List<LandingPageWechatAppletConfig> getByLandingPageChannelIds(List<String> ids, String agentId) {
        return baseMapper.getByLandingPageChannelIds(ids, agentId);
    }

    /**
     * 获取同主体的开放平台id和其剩余绑定数
     *
     * @param principalName
     * @param componentAppId
     * @return
     */
    public List<OpenAppIdDto> getOpenInfo(String principalName, String componentAppId, String agentId) {
        return baseMapper.getOpenInfo(principalName, componentAppId, agentId);
    }

    /**
     * 获取同主体的开放平台id
     *
     * @param principalName
     * @param agentId
     * @return
     */
    public List<String> getOpenAppId(String principalName, String componentAppId, String agentId) {
        return baseMapper.getOpenAppId(principalName, componentAppId, agentId);
    }


    //构建消息，进行发送
    private void noticeMessageSend(LandingPageWechatAppletConfig landingPageWechatAppletConfig, LandingPageWechatAppletPmpRel landingPageWechatAppletPmpRel, String content, MarketingNoticeSubType noAvaliableWechatApplet) {
        TenantContextHolder.set(landingPageWechatAppletPmpRel.getAgentId());
        MessageNotice messageNotice = new MessageNotice();
        messageNotice.setAdvertiserAccountGroupId(landingPageWechatAppletPmpRel.getAdvertiserAccountGroupId());
        messageNotice.setCreatedAt(Instant.now());
        messageNotice.setUpdatedAt(Instant.now());
        messageNotice.setNoticeSubType(noAvaliableWechatApplet);
        messageNotice.setNoticeType(MarketingNoticeType.APPLET_NOTICE);
        messageNotice.setNoticeTitle("小程序异常警告");
        messageNotice.setNoticeContent(content);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appletAppid", landingPageWechatAppletConfig.getWechatAppletAppid());
        jsonObject.put("noticeType", MarketingNoticeType.APPLET_NOTICE);
        messageNotice.setExt(jsonObject);
        messageNoticeSender.sendNoticeMessage(messageNotice);
    }


    public void forbidAppletProcess(LandingPageWechatAppletConfig landingPageWechatAppletConfig) {
        if (StringUtils.isBlank(landingPageWechatAppletConfig.getAgentId())) {
            log.info("数据源agentId为空终止站内信操作 landingPageWechatAppletConfig={}；", JSONObject.toJSONString(landingPageWechatAppletConfig));
        }
        //#31981 【线上问题】小程序异常时站内信再GMP范围内串数据了 https://ones.yiye.ai/project/#/team/WtsduTeT/task/XxxHuwKeU7bk73aq
        TenantContextHolder.set(landingPageWechatAppletConfig.getAgentId());
        //首先根据小程序查他所有的PMP，然后每个PMP进行查询
        Long id = landingPageWechatAppletConfig.getId();
        List<LandingPageWechatAppletPmpRel> list = landingPageWechatAppletPmpRelService.list(new LambdaQueryWrapper<LandingPageWechatAppletPmpRel>().eq(LandingPageWechatAppletPmpRel::getLandingPageWechatAppletId, id).eq(LandingPageWechatAppletPmpRel::getAgentId, landingPageWechatAppletConfig.getAgentId()));
        if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
            Set<Long> accountGroupIds = list.stream().map(LandingPageWechatAppletPmpRel::getAdvertiserAccountGroupId).collect(Collectors.toSet());
            List<AdvertiserAccountGroup> advertiserAccountGroups = advertiserAccountGroupService.listByIds(accountGroupIds);
            Map<Long, AdvertiserAccountGroup> accountGroupMap = advertiserAccountGroups.stream().collect(Collectors.toMap(AdvertiserAccountGroup::getId, t -> t, (v1, v2) -> v1));
            //然后每个PMP进行遍历
            for (int i = 0; i < list.size(); i++) {
                //每个PMP进行查询 看是否有小程序
                LandingPageWechatAppletPmpRel landingPageWechatAppletPmpRel = list.get(i);
                AdvertiserAccountGroup accountGroup = accountGroupMap.get(landingPageWechatAppletPmpRel.getAdvertiserAccountGroupId());
                if (landingPageWechatAppletConfig.getWechatAppletAbandonType() == WechatAppletAbandonType.JOBABANDON) {
                    //TODO 所有与小程序下线飞书通知相关内容，请在mq生产端进行发送，此处不再处理飞书提醒
                    //workWechatClientService.sendWarning(String.format("检测微信小程序是否被封禁 - 微信 - 小程序 - 获取scheme，失败：生成权限被封禁，即将下线该小程序，小程序appid：%s；pmpid=%s；", landingPageWechatAppletConfig.getWechatAppletAppid(), list.get(i).getAdvertiserAccountGroupId()), landingPageWechatAppletConfig.getWechatAppletAppid());
                }
                List<LandingPageWechatAppletPmpRel> list1 = landingPageWechatAppletPmpRelService.list(new LambdaQueryWrapper<LandingPageWechatAppletPmpRel>()
                    .eq(LandingPageWechatAppletPmpRel::getAdvertiserAccountGroupId, landingPageWechatAppletPmpRel.getAdvertiserAccountGroupId())
                    .eq(LandingPageWechatAppletPmpRel::getAgentId, landingPageWechatAppletPmpRel.getAgentId())
                    .notIn(LandingPageWechatAppletPmpRel::getLandingPageWechatAppletId, landingPageWechatAppletConfig.getId()));

                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list1)) {
                    //然后根据lists1的appid参数，获取到他现在PMP下的所有账户
                    List<Long> collect = list1.stream().map(e -> e.getLandingPageWechatAppletId()).collect(Collectors.toList());
//                可用小程序
                    List<LandingPageWechatAppletConfig> validWechatApplet = landingPageWechatAppletConfigService.list(new LambdaQueryWrapper<LandingPageWechatAppletConfig>().in(LandingPageWechatAppletConfig::getId, collect)
                        .eq(LandingPageWechatAppletConfig::getEnterpriseWechatType, EnterpriseWechatType.GENERATION_DEVELOPMENT)
                        .eq(LandingPageWechatAppletConfig::getAuthStatus, WechatMiniAuthStatusEnum.AUTHSUCCESS)
                        .eq(LandingPageWechatAppletConfig::getOpenStatus, Boolean.TRUE)
                        .in(LandingPageWechatAppletConfig::getWechatAppletStatus, Arrays.asList(1, 2))
                        .eq(LandingPageWechatAppletConfig::getValid, ValidType.VALID));
                    //2022-11-24修复线上问题#26404
                    if (org.springframework.util.CollectionUtils.isEmpty(validWechatApplet)) {
                        String content = buildContent(landingPageWechatAppletConfig.getWechatAppletName(), accountGroup.getName(), "获取scheme失败，当前项目下已无小程序可用，请配置");
                        noticeMessageSend(landingPageWechatAppletConfig, landingPageWechatAppletPmpRel, content, MarketingNoticeSubType.NO_AVALIABLE_WECHAT_APPLET);
                        continue;
                    }
//                首先判断是不是仍有可用小程序，且是正常小程序
                    List<LandingPageWechatAppletConfig> collect2 = validWechatApplet.stream().filter(e -> e.getAppletAttribute().equals(AppletAttribute.NORMAL_APPLET)).collect(Collectors.toList());
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(collect2)) {
                        String content = buildContent(landingPageWechatAppletConfig.getWechatAppletName(), accountGroup.getName(), "获取scheme失败，当前正在使用的小程序还有【" + collect2.stream().map(e -> e.getWechatAppletName()).collect(Collectors.joining("】,【")) + "】");
                        noticeMessageSend(landingPageWechatAppletConfig, landingPageWechatAppletPmpRel, content, MarketingNoticeSubType.SWITCH_OPEN_WECHAT_APPLET);
                    } else {
                        //这边只过滤出可用小程序
                        JSONObject queryParam = new JSONObject();
                        queryParam.put("result", "轮询发现小程序报错");
                        //正常情况下不开启备用小程序，只有正常的走小程序过期 才会进行
//                    LandingPageWechatAppletConfig landingPageWechatAppletConfig1 = landingPageWechatAppletConfigService.openNewWechatApplet(landingPageWechatAppletPmpRel.getAgentId(), landingPageWechatAppletPmpRel.getAdvertiserAccountGroupId(), EnterpriseWechatType.GENERATION_DEVELOPMENT, landingPageWechatAppletConfig, queryParam);
                        //这里只查询，对于小程序的启用不启用不做配置
                        String content = buildContent(landingPageWechatAppletConfig.getWechatAppletName(), accountGroup.getName(), "获取scheme失败,已启用备用小程序");
                        noticeMessageSend(landingPageWechatAppletConfig, landingPageWechatAppletPmpRel, content, MarketingNoticeSubType.START_SPARE_WECHAT_APPLET);
                    }
                } else {
                    String content = buildContent(landingPageWechatAppletConfig.getWechatAppletName(), accountGroup.getName(), "获取scheme失败，当前项目下已无小程序可用，请配置");
                    noticeMessageSend(landingPageWechatAppletConfig, landingPageWechatAppletPmpRel, content, MarketingNoticeSubType.NO_AVALIABLE_WECHAT_APPLET);
                }
            }

        }
    }

    private static String buildContent(String wechatAppletName, String accountGroupName, String ext) {
        StringBuilder contentBuilder = new StringBuilder();
        contentBuilder.append("【");
        contentBuilder.append(accountGroupName);
        contentBuilder.append("】");
        contentBuilder.append("小程序【");
        contentBuilder.append(wechatAppletName);
        contentBuilder.append("】");
        contentBuilder.append(ext);
        return contentBuilder.toString();
    }


    public Page<LandingPageWechatAppletConfig> listWechatAppletConfigByAccountGroupId(Page<LandingPageWechatAppletConfig> page, LandingPageWechatAppletConfig landingPageWechatAppletConfig, String agentId) {
        String status = landingPageWechatAppletConfig.getStatus();
        //小程序状态字符串分割
        if (StringUtils.isNotBlank(status)) {
            String[] split = status.split(",");
            List<String> list = Arrays.asList(split);
            landingPageWechatAppletConfig.setStatusList(list);
        }
        String codeAuditStatus = landingPageWechatAppletConfig.getCodeAuditStatus();
        if (StringUtils.isNotBlank(codeAuditStatus)) {
            String[] split = codeAuditStatus.split(",");
            List<String> list = Arrays.asList(split);
            List<WeAppAuditStatusEnum> weAppAuditStatusEnums = list.stream().map(e -> WeAppAuditStatusEnum.valueOf(e)).collect(Collectors.toList());
            landingPageWechatAppletConfig.setAuditStatusList(weAppAuditStatusEnums);
        }

        return baseMapper.listWechatAppletConfigByAccountGroupId(page, landingPageWechatAppletConfig, agentId);
    }

    //新增的小程序要取到模板信息配置模板
    public void wechatAppletVersion(LandingPageWechatAppletConfig appletConfig) {
        LandingPageWechatAppletConfigService landingPageWechatAppletConfigService = applicationContext.getBean(this.getClass());

        Long templateTypeId = appletConfig.getTemplateTypeId();
        //根据typeId，寻找到该项模板的最新版本
        String componentAppId = wxOpenRedis.getComponentAppId(appletConfig.getComponentAppId());
        //获取id最大的一条模板信息 -> 更新对应客户的小程序
        LandingPageWechatAppletTemplate one = landingPageWechatTemplateService.getOne(new LambdaQueryWrapper<LandingPageWechatAppletTemplate>().eq(LandingPageWechatAppletTemplate::getComponentAppId, componentAppId).eq(LandingPageWechatAppletTemplate::getTemplateTypeId, templateTypeId).orderByDesc(LandingPageWechatAppletTemplate::getId).last("LIMIT 1"));
        JSONObject extJson = one.getExtJson();
        MinitWechatExt.AppConfig appConfig = new MinitWechatExt.AppConfig();
        MiniWechatResponseBody miniWechatResponseBody = landingPageService.homePage(appletConfig);
        String homePage = miniWechatResponseBody.getHomePage();
        String webviewHost = landingPageService.getDomainNotHavePath();
        appConfig.setAccount(appletConfig.getWechatAppletName())
            .setApiHost(agentConf.getHost())
            .setTraceDomain(agentConf.getTraceHost())
            .setDefaultPage(homePage)
            .setWebviewHost(webviewHost)
            .setAppName(appletConfig.getWechatAppletName());
        log.info("小程序主页地址为{}", homePage);
        log.info("小程序homePage地址为{}", webviewHost);
        MinitWechatExt minitWechatExt = new MinitWechatExt();
        minitWechatExt.setAppConfig(appConfig);
        extJson.put("ext", minitWechatExt);
        extJson.put("extAppid", appletConfig.getWechatAppletAppid());
        String version = one.getUserVersion();
        try {
            /**
             * 上传代码并生成预览版 and 提交代码审核 and 设置小程序用户隐私保护指引
             */
            LandingPageWechatAppletConfig landingPageWechatAppletConfig = landingPageWechatAppletConfigService.publishMimiTemplate(appletConfig.getId()
                , one.getTemplateId(), JSONObject.toJSONString(extJson), appletConfig.getAccessToken(), one.getUserVersion(), one.getUserDesc());
            LambdaUpdateWrapper<LandingPageWechatAppletConfig> conf = new LambdaUpdateWrapper<LandingPageWechatAppletConfig>().eq(LandingPageWechatAppletConfig::getId, landingPageWechatAppletConfig.getId())
                .set(Objects.nonNull(landingPageWechatAppletConfig.getAuditStatus()), LandingPageWechatAppletConfig::getAuditStatus, landingPageWechatAppletConfig.getAuditStatus())
                .set(StringUtils.isNotBlank(landingPageWechatAppletConfig.getNextVersion()), LandingPageWechatAppletConfig::getNextVersion, landingPageWechatAppletConfig.getNextVersion())
                .set(StringUtils.isNotBlank(landingPageWechatAppletConfig.getAuditid()), LandingPageWechatAppletConfig::getAuditid, landingPageWechatAppletConfig.getAuditid())
                .set(Objects.nonNull(landingPageWechatAppletConfig.getTemplateTypeId()), LandingPageWechatAppletConfig::getTemplateTypeId, landingPageWechatAppletConfig.getTemplateTypeId());
            if (Objects.nonNull(landingPageWechatAppletConfig.getAccess()) && landingPageWechatAppletConfig.getAccess()) {
                conf.set(Objects.nonNull(landingPageWechatAppletConfig.getAccess()), LandingPageWechatAppletConfig::getAccess, landingPageWechatAppletConfig.getAccess());
            }
            if (StringUtils.isNotBlank(landingPageWechatAppletConfig.getVersion())) {
                conf.set(LandingPageWechatAppletConfig::getVersion, landingPageWechatAppletConfig.getVersion());
            }
            landingPageWechatAppletConfigService.update(conf);
        } catch (MarketingWeOpenApiException e) {
            LambdaUpdateWrapper<LandingPageWechatAppletConfig> objectLambdaUpdateWrapper = new LambdaUpdateWrapper<LandingPageWechatAppletConfig>()
                .eq(LandingPageWechatAppletConfig::getId, appletConfig.getId())
                .set(StringUtils.isNotBlank(e.getMessage()), LandingPageWechatAppletConfig::getRejectReason, e.getMessage())
                .set(StringUtils.isNotBlank(version), LandingPageWechatAppletConfig::getNextVersion, version);
            if (Objects.nonNull(e.getErrcode())) {
                switch (e.getErrcode()) {
                    case 85009:
                        objectLambdaUpdateWrapper
                            .set(!WeAppAuditStatusEnum.WEAPP_AUDIT_SUCCESS.equals(appletConfig.getAuditStatus()), LandingPageWechatAppletConfig::getAuditStatus, WeAppAuditStatusEnum.WEAPP_AUDITING);
                        break;
                    case 61039:
                        // 61039  表示隐私接口检查任务未完成，请稍等一分钟再重试
                        //上面已经设置了失败原因，这里不需要再设置和版本
                        break;
                    case 53104:
                    case 86369:
                        /**
                         *  {"errcode":86369,"errmsg":"小程序上架需先完成备案 rid: 64f6c2d1-068e9965-1071cb63"}
                         *  {"errcode":53104,"errmsg":"小程序发布需先完成认证 rid: 66399931-4028bf1b-7a064716"}
                         */
                        objectLambdaUpdateWrapper
                            .set(LandingPageWechatAppletConfig::getAuditStatus, WeAppAuditStatusEnum.WEAPP_RELEASE_FAIL);
                        break;
                    case 86020:
                        objectLambdaUpdateWrapper
                            .set(LandingPageWechatAppletConfig::getAuditStatus, WeAppAuditStatusEnum.WEAPP_NAME_ILLEGALITY);
                        break;
                    default:
                        log.warn("发布小程序发布出现marketingApi异常-----小程序信息为{},异常信息为{},", JSON.toJSONString(appletConfig), e.getMessage(), e);
                        //上面已经设置了失败原因，这里不需要再设置和版本
                        break;
                }
            } else {
                log.warn("发布小程序发布出现marketingApi异常-----小程序信息为{},异常信息为{},", JSON.toJSONString(appletConfig), e.getMessage(), e);
            }
            landingPageWechatAppletConfigService.update(objectLambdaUpdateWrapper);
        } catch (Exception e) {
            log.error("发布小程序发布异常-----小程序信息为{},异常信息为{},", JSON.toJSONString(appletConfig), e.getMessage(), e);
            landingPageWechatAppletConfigService.update(new LambdaUpdateWrapper<LandingPageWechatAppletConfig>()
                .eq(LandingPageWechatAppletConfig::getId, appletConfig.getId()).set(LandingPageWechatAppletConfig::getRejectReason, e.getMessage())
                .set(StringUtils.isNotBlank(version), LandingPageWechatAppletConfig::getNextVersion, version));
        } finally {
            //清理小程序缓存
            landingPageWechatAppletConfigRedis.deleteWechatByAgentId(appletConfig.getAgentId(), appletConfig.getEnterpriseWechatType());
            landingPageWechatAppletConfigRedis.deleteWechatByAppid(appletConfig.getWechatAppletAppid());
        }
    }

    public List<LandingPageWechatAppletConfig> selectSubmitAuditWechatAppletConfigs() {
        return baseMapper.selectSubmitAuditWechatAppletConfigs();
    }


    /**
     * 获取跳转页面的页面类型
     *
     * @param query
     * @return
     */
    public LandingPageType getLandingPageType(String query) {
        Map<String, String> params = UrlUtils.getParams(UrlUtils.URL_DOMAIN + "?" + query);
        String token = params.get("token");
        //通过落地页参数获取落地页类型
        if (StringUtils.isNotBlank(token)) {
            return landingPageWechatAppletConfigRedis.getAndSaveLandingPageTypeByKey(TenantContextHolder.get() + ":" + pageViewConf.getCacheKey() + pageViewConf.getCommonCacheKey() + "landing-page-type:" + token, () -> {
                LandingPage landingPage = landingPageService.getOne(new LambdaQueryWrapper<LandingPage>().eq(LandingPage::getToken, token));
                if (Objects.nonNull(landingPage)) {
                    return landingPage.getLandingPageType().getExtName();
                }
                return LandingPageType.H5.getExtName();
            }, 1L, TimeUnit.DAYS);
        }
        return LandingPageType.H5;
    }

    public String generateSchemeFromBaiDu(SchemeApiDto schemeApiDto, String userAgent) {
        log.info("开始：生成【微信小程序】Scheme码 （百度营销-营销通-全场景调起小程序） ======>> schemeApiDto={}", schemeApiDto);
        SchemeApiResultDto schemeApiResultDto = new SchemeApiResultDto().setCode(-1);
        //根据小程序原始id，校验是否存在该小程序，gh_f745dbca1b1c
        String originId = schemeApiDto.getOriginId();
        String systemOriginId = landingPageWechatAppletConfigRedis.getLandingPageWechatAppletConfigByOriginId(originId, () ->
            baseMapper.selectOne(new LambdaQueryWrapper<LandingPageWechatAppletConfig>().eq(LandingPageWechatAppletConfig::getAppletOriginalId, originId)
                .orderByDesc(LandingPageWechatAppletConfig::getUpdatedAt).orderByDesc(LandingPageWechatAppletConfig::getCreatedAt)
            ));
        if (StringUtils.isBlank(originId) || StringUtils.isBlank(systemOriginId) || !originId.equals(systemOriginId)) {
            schemeApiResultDto.setMsg("原始id对应的小程序不存在");
            return JSONObject.toJSONString(schemeApiResultDto);
        }
        String signStr = "currentTime=" + schemeApiDto.getCurrentTime() + "&originId=" + systemOriginId;
        String sign = DigestUtils.md5Hex(signStr.toLowerCase());
        if (sign.equals(schemeApiDto.getSign())) {
            //根据参数生成scheme
            try {
                Boolean isExpire = Objects.isNull(schemeApiDto.getIsExpire()) ? true : schemeApiDto.getIsExpire();
                String agentId = UrlUtils.getUrlParrams(schemeApiDto.getQuery(), "agentId");
                String token = UrlUtils.getUrlParrams(schemeApiDto.getQuery(), "token");
                String cl = UrlUtils.getUrlParrams(schemeApiDto.getQuery(), "_cl");
                String referrer = UrlUtils.getUrlParrams(schemeApiDto.getQuery(), "referrer");
                String advertiseAccountGroupIdStr = UrlUtils.getUrlParrams(schemeApiDto.getQuery(), "advertiseAccountGroupId");
                Long advertiseAccountGroupId = StringUtils.isNoneBlank(StringUtils.trim(advertiseAccountGroupIdStr)) ? Long.valueOf(advertiseAccountGroupIdStr) : null;
                String yiyeQueryStr = schemeApiDto.getQuery();
                String query = "_cl=" + cl + "&token=" + token + "&agentId=" + agentId + "&advertiseAccountGroupId=" + advertiseAccountGroupId + "&referrer=" + referrer;
                String scheme = generateScheme(advertiseAccountGroupId, agentId, schemeApiDto.getPath(), query, yiyeQueryStr, isExpire, true, true, null).getWechatAppletScheme();
                if (StringUtils.isBlank(scheme)) {
                    schemeApiResultDto.setMsg("获取scheme失败");
                } else {
                    schemeApiResultDto.setCode(0).setResult(new SchemeDto().setScheme(scheme));
                }
            } catch (RestException e) {
                log.warn("获取scheme失败： ======>> message={}；", e.getMessage(), e);
                schemeApiResultDto.setMsg(String.format("获取小程序scheme错误：%s", e.getMessage()));
                return JSONObject.toJSONString(schemeApiResultDto);
            } catch (Exception e) {
                log.error("获取scheme失败： ======>> message={}；", e.getMessage(), e);
                schemeApiResultDto.setMsg("获取小程序scheme失败");
                return JSONObject.toJSONString(schemeApiResultDto);
            }
        } else {
            schemeApiResultDto.setMsg("获取小程序scheme错误：sign校验失败");
        }
        log.info("结束：生成【微信小程序】Scheme码 （百度营销-营销通-全场景调起小程序） ======>> schemeApiDto={}；schemeApiResultDto={}", schemeApiDto, schemeApiResultDto);
        return JSONObject.toJSONString(schemeApiResultDto);
    }

    /**
     * 立即预约生成手机号
     */
    public void makeAnAppointmentByWechat(LandingPageWechatAuthData landingPageWechatAuthData) {
        /**
         * 客服页面的立即预约
         */
        if (StringUtils.isBlank(landingPageWechatAuthData.getPid())) {
            generateCustomerByNotPid(landingPageWechatAuthData);
            return;
        }
        /**
         * 落地页内的立即预约
         */
        generateCustomerByWechatPhone(landingPageWechatAuthData);
    }

    /**
     * 落地页内的立即预约
     *
     * @param landingPageWechatAuthData
     */
    @Deprecated
    public void generateCustomerByWechatPhone(LandingPageWechatAuthData landingPageWechatAuthData) {
        /**
         * 获取页面的停留时长访问深度
         */
        String monitorPidKey = TraceUtil.KEY_PREFIX_MONITOR + landingPageWechatAuthData.getPid();
        TracePageVO vo = (TracePageVO) objectRedisTemplate.opsForValue().get(monitorPidKey);
        BigDecimal lengthOfStay = Objects.isNull(vo) ? null : vo.getLengthOfStay();
        BigDecimal depth = Objects.isNull(vo) ? null : vo.getDepth();
        //通过appid 查询所有的项目
        SubmitData submitDataForm = new SubmitData();
        IPUtil.invokeByIp(submitDataForm);
        // 通过ua解析浏览器，操作系统，设备，网络类型等
        WebUtils.invokeUserAgent(submitDataForm);
        /**
         * 优先查看是否为h5填单跳转到的加粉页面
         */
        Long parentSubmitDataId = landingPageWechatAuthData.getParentSubmitDataId();
        //记录客资信息
        SubmitData submitData = (!Objects.isNull(parentSubmitDataId) && 0L != parentSubmitDataId) ? submitDataService.getOne(new LambdaQueryWrapper<SubmitData>()
            .ge(SubmitData::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerQueryTime()))
            .eq(SubmitData::getId, parentSubmitDataId)) : submitDataService.getOne(new LambdaQueryWrapper<SubmitData>()
            .eq(SubmitData::getPid, landingPageWechatAuthData.getPid()));
        if (Objects.isNull(submitData)) {
            submitData = new SubmitData();
            BeanUtils.copyProperties(submitDataForm, submitData);
            submitData.setId(null).setLandingPageId(-1L)
                /*修复bug：#15239 【YIYE_AGENT_V1.119.0】微信广告上报客资状态不正确，且媒体来源不正确*/
                .setPid(landingPageWechatAuthData.getPid())
                .setAdvertiserAccountGroupId(landingPageWechatAuthData.getAdvertiserAccountGroupId())
                .setWechatAppletOpenid(landingPageWechatAuthData.getWechatAppletOpenid())
                .setWechatAppletUnionid(landingPageWechatAuthData.getWechatAppletUnionid())
                .setLandingPageId(landingPageWechatAuthData.getLandingPageId())
                .setChannelId(landingPageWechatAuthData.getChannelId())
                .setUrl(landingPageWechatAuthData.getUrl())
                .setSubmitType(SubmitType.FORM)
                .setWechatAppletPhone(landingPageWechatAuthData.getPhone())
                .setAccessDepth(depth)
                .setLengthOfStay(lengthOfStay)
                .setPlatformSources(Platform.getPlatformSourcesByUrlOrRefer(landingPageWechatAuthData.getUrl(), null));

            log.info("立即预约【新增】填单数据 submitData={}", JSONObject.toJSONString(submitData));
            submitDataService.save(submitData);
        } else {
            submitData
                .setWechatAppletPhone(landingPageWechatAuthData.getPhone())
                .setAccessDepth(depth)
                .setLengthOfStay(lengthOfStay)
                .setUpdatedAt(Instant.now());
            log.info("立即预约【修改】填单数据 submitData={}", JSONObject.toJSONString(submitData));
            submitDataService.updateById(submitData);
        }


        //记录客资信息
        Customer customer = (!Objects.isNull(submitData) && !Objects.isNull(submitData.getId()) && 0L != submitData.getId()) ? customerService.getOne(new LambdaQueryWrapper<Customer>()
            .ge(Customer::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerQueryTime()))
            .eq(Customer::getSubmitDataId, submitData.getId()).last(" limit 1")) : null;
        if (Objects.isNull(customer)) {
            AdvertiserAccountGroup advertiserAccountGroup = advertiserAccountGroupRemote.fetchById(landingPageWechatAuthData.getAdvertiserAccountGroupId());
            customer = new Customer().setId(null).setLandingPageId(-1L)
                /*修复bug：#15239 【YIYE_AGENT_V1.119.0】微信广告上报客资状态不正确，且媒体来源不正确*/
                .setPid(landingPageWechatAuthData.getPid())
                .setSubmitDataId(submitData.getId())
                .setAdvertiserAccountGroupId(landingPageWechatAuthData.getAdvertiserAccountGroupId())
                .setWechatAppletOpenid(landingPageWechatAuthData.getWechatAppletOpenid())
                .setWechatAppletUnionid(landingPageWechatAuthData.getWechatAppletUnionid())
                .setLandingPageId(landingPageWechatAuthData.getLandingPageId())
                .setChannelId(landingPageWechatAuthData.getChannelId())
                .setCustomerType(SubmitType.FORM)
                .setPlatformSources(Platform.getPlatformSourcesByUrlOrRefer(landingPageWechatAuthData.getUrl(), null))
                .setWechatAppletPhone(landingPageWechatAuthData.getPhone())
                .setPhone(landingPageWechatAuthData.getPhone())
                .setUrl(landingPageWechatAuthData.getUrl())
                .setAccessDepth(depth)
                .setLengthOfStay(lengthOfStay)
                .setUnread(1)
                //ua ip 信息
                .setIp(submitData.getIp())
                .setUa(submitData.getUa())
                .setProvince(submitData.getProvince())
                .setCity(submitData.getCity())
                .setLocation(submitData.getLocation())
                //经纬度
                .setLongitude(submitData.getLongitude())
                //维度
                .setLatitude(submitData.getLatitude())
                .setBrowserType(submitData.getBrowserType())
                .setBrowser(submitData.getBrowserType())
                .setOsType(submitData.getOsType())
                .setDevice(submitData.getDevice())
                .setOs(submitData.getOsType())
                .setNetworkType(submitData.getNetworkType());
            if (ReplaceOperationType.OPERATION.equals(advertiserAccountGroup.getReplaceOperation())) {
                customer.setOperationType(OperationType.OPERATION);
            }
            log.info("立即预约【新增】客资数据 customer={}", JSONObject.toJSONString(customer));
            customerService.save(customer);
        } else {
            customer.setId(customer.getId())
                .setSubmitDataId(submitData.getId())
                .setPid(StringUtils.isNotBlank(customer.getPid()) ? customer.getPid() : landingPageWechatAuthData.getPid())
                .setNextPid(submitData.getPid())
                .setWechatAppletOpenid(landingPageWechatAuthData.getWechatAppletOpenid())
                .setWechatAppletUnionid(landingPageWechatAuthData.getWechatAppletUnionid())
                .setUpdatedAt(Instant.now())
                .setPhone(Objects.isNull(customer.getPhone()) ? landingPageWechatAuthData.getPhone() : customer.getPhone())
                .setWechatAppletPhone(landingPageWechatAuthData.getPhone())
                .setAccessDepth(depth)
                .setLengthOfStay(lengthOfStay)
                .setUnread(1);
            log.info("【保存】客资数据 customer={}", JSONObject.toJSONString(customer));
            customerService.updateById(customer);
        }
        /**
         * 更新填单状态
         */
        pageViewinfoSender.wechatApplePhone(customer.getPid());
    }

    /**
     * 没有pid
     * 客服页面点击立即预约生成客资
     *
     * @param landingPageWechatAuthData
     */
    public void generateCustomerByNotPid(LandingPageWechatAuthData landingPageWechatAuthData) {
        //通过appid 查询所有的项目
        SubmitData submitDataForm = new SubmitData();
        IPUtil.invokeByIp(submitDataForm);
        // 通过ua解析浏览器，操作系统，设备，网络类型等
        WebUtils.invokeUserAgent(submitDataForm);
        List<Long> advertiserAccountGroupIds = landingPageWechatAppletConfigService.selectPmpIdsByAppid(landingPageWechatAuthData.getWechatAppletAppid());
        advertiserAccountGroupIds.stream().forEach(advertiserAccountGroupId -> {
            try {
                SubmitData submitData = new SubmitData();
                BeanUtils.copyProperties(submitDataForm, submitData);
                submitData.setLandingPageId(-1L)
                    /*修复bug：#15239 【YIYE_AGENT_V1.119.0】微信广告上报客资状态不正确，且媒体来源不正确*/
                    .setSubmitType(SubmitType.FORM)
                    .setAdvertiserAccountGroupId(advertiserAccountGroupId)
                    .setIp(landingPageWechatAuthData.getIp())
                    .setUa(landingPageWechatAuthData.getUa())
                    .setWechatAppletPhone(landingPageWechatAuthData.getPhone())
                    .setWechatAppletOpenid(landingPageWechatAuthData.getWechatAppletOpenid())
                    .setWechatAppletUnionid(landingPageWechatAuthData.getWechatAppletUnionid());
                submitDataService.saveOrUpdate(submitData);
                AdvertiserAccountGroup advertiserAccountGroup = advertiserAccountGroupRemote.fetchById(advertiserAccountGroupId);

                Customer customer = new Customer();
                customer.setLandingPageId(-1L)
                    .setSubmitDataId(submitData.getId())
                    .setAdvertiserAccountGroupId(advertiserAccountGroupId)
                    .setCustomerType(SubmitType.FORM)
                    .setPhone(landingPageWechatAuthData.getPhone())
                    .setIp(submitData.getIp())
                    .setUa(submitData.getUa())
                    .setProvince(submitData.getProvince())
                    .setCity(submitData.getCity())
                    .setLocation(submitData.getLocation())
                    //经纬度
                    .setLongitude(submitData.getLongitude())
                    //维度
                    .setLatitude(submitData.getLatitude())
                    .setBrowserType(submitData.getBrowserType())
                    .setBrowser(submitData.getBrowserType())
                    .setOsType(submitData.getOsType())
                    .setDevice(submitData.getDevice())
                    .setOs(submitData.getOsType())
                    .setNetworkType(submitData.getNetworkType())
                    .setWechatAppletPhone(landingPageWechatAuthData.getPhone())
                    .setWechatAppletOpenid(landingPageWechatAuthData.getWechatAppletOpenid())
                    .setWechatAppletUnionid(landingPageWechatAuthData.getWechatAppletUnionid());
                if (ReplaceOperationType.OPERATION.equals(advertiserAccountGroup.getReplaceOperation())) {
                    customer.setOperationType(OperationType.OPERATION);
                }
                customerService.saveOrUpdate(customer);
            } catch (Exception e) {
                log.error("客服页面点击立即预约生成客资 error data:{}", JSONObject.toJSONString(landingPageWechatAuthData), e);
            }
        });
    }

    /**
     * 获取小程序绑定的pmpid
     *
     * @param appid
     * @return
     */
    public List<Long> selectPmpIdsByAppid(String appid) {
        return baseMapper.selectPmpIdsByAppid(appid);
    }


    /**
     * 异常小程序删除
     */
    public List<LandingPageWechatAppletConfigVo> removeAbnormal(List<Long> ids) {
        List<LandingPageWechatAppletConfig> landingPageWechatAppletConfigs = this.listByIds(ids);
        if (CollectionUtils.isEmpty(landingPageWechatAppletConfigs)) {
            throw new RestException("找不到对应小程序!");
        }
        //需求修改 不能删除的需返回，能删除的需要删除
        //检测是否存在非异常的小程序
        List<LandingPageWechatAppletConfig> appletConfigs = landingPageWechatAppletConfigs.stream().filter(e -> e.getWechatAppletStatus() != 0).collect(Collectors.toList());
        List<LandingPageWechatAppletConfigVo> error = new ArrayList<>();
        if (!CollectionUtils.isEmpty(appletConfigs)) {
            List<LandingPageWechatAppletConfigVo> collect = appletConfigs.stream().map(e -> {
                LandingPageWechatAppletConfigVo fail = new LandingPageWechatAppletConfigVo();
                String wechatAppletName = e.getWechatAppletName();
                fail.setId(e.getId());
                fail.setWechatAppletName(wechatAppletName);
                fail.setErrorMsg("【" + wechatAppletName + "】为正常的小程序，不可删除!");
                return fail;
            }).collect(Collectors.toList());
            error.addAll(collect);
            //移除正常的小程序
            landingPageWechatAppletConfigs.removeAll(appletConfigs);
        }

        //开始删除
        landingPageWechatAppletConfigs.stream().forEach(e -> {
            //无需开启事务 开放平台无论解绑成功失败都删除
            try {
                this.removeApplet(e);
                log.info("====删除小程序【" + e.getWechatAppletName() + "】记录及对应项目关联成功!=====");
                //解绑开放平台
                boolean unbindFlag = this.unbindAppletConfig(e);
                if (!unbindFlag) {
                    log.warn("小程序 【" + e.getWechatAppletName() + "】 解绑开放平台失败!");
                }
            } catch (Exception exception) {
                String message = exception.getMessage();
                LandingPageWechatAppletConfigVo landingPageWechatAppletConfigVo = new LandingPageWechatAppletConfigVo();
                landingPageWechatAppletConfigVo.setId(e.getId());
                landingPageWechatAppletConfigVo.setWechatAppletName(e.getWechatAppletName());
                landingPageWechatAppletConfigVo.setErrorMsg(message);
                error.add(landingPageWechatAppletConfigVo);
            }
        });
        return error;
    }

    /**
     * 删除小程序记录
     * 删除所有pmp关联
     * 解绑通过一叶系统绑定的微信开放平台
     * 清除小程序缓存
     *
     * @param landingPageWechatAppletConfig
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeApplet(LandingPageWechatAppletConfig landingPageWechatAppletConfig) {
        Long id = landingPageWechatAppletConfig.getId();
        String wechatAppletName = landingPageWechatAppletConfig.getWechatAppletName();
        boolean b = this.removeById(id);
        if (!b) {
            throw new RestException("删除小程序【" + wechatAppletName + "】 失败!");
        }
        //删除所有pmp关联
        LambdaQueryWrapper<LandingPageWechatAppletPmpRel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LandingPageWechatAppletPmpRel::getLandingPageWechatAppletId, id);
        boolean remove = this.landingPageWechatAppletPmpRelService.remove(lambdaQueryWrapper);
        if (!remove) {
            throw new RestException("移除小程序 【" + wechatAppletName + "】 项目关联失败!");
        }
        //清除当前小程序缓存
        wxOpenService.clearRedisCache(landingPageWechatAppletConfig);
    }

    /**
     * 与开放平台解绑
     * 解绑失败打印错误日志
     *
     * @param appletConfig
     * @return
     */
    public boolean unbindAppletConfig(LandingPageWechatAppletConfig appletConfig) {
        try {
            Map<String, Object> queryMap = new HashMap<>();
            String openAppId = appletConfig.getOpenAppId();
            if (StringUtils.isNotBlank(openAppId)) {
                queryMap.put("open_appid", openAppId);
                try {
                    JSONObject open = openWeixinApiClient.unbindOpen(appletConfig.getAccessToken(), queryMap);
                    OpenCreateOpenResponseBody unwrap = new WechatPropertyMapper(open).mapProperty(OpenWechatMappers.WECHAT_OPEN_ID).unwrap(OpenCreateOpenResponseBody.class);
                    log.info("小程序-解绑微信开放平台账号接口-解绑成功-unwrap:{}", JSON.toJSONString(unwrap));
                    return true;
                } catch (MarketingWeOpenApiException e) {
                    String errorInfo = Objects.isNull(e.getCause()) ? e.getMessage() : e.getCause().getMessage();
                    log.warn("小程序-解绑微信开放平台账号接口-wechatOfficialAccount:[{}],异常信息:[{}]", JSON.toJSONString(appletConfig), errorInfo, e);
                    WechatAppletResultCode resultCode = WechatAppletResultCode.getByCode(e.getErrcode());
                    if (resultCode != null) {
                        throw new RestException(resultCode.getMsg());
                    }
                    throw new RestException(String.format(WechatAppletResultCode.NOT_SUPPORT_UNBIND.getMsg(), appletConfig.getWechatAppletName()));
                }
            }
            return false;
        } catch (Exception e) {
            log.error("e=>", e);
            return false;
        }
    }

    /**
     * 小程序与pmp解绑
     *
     * @param landingPageWechatAppletConfigDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<LandingPageWechatAppletConfigVo> unbindPmp(LandingPageWechatAppletConfigDto landingPageWechatAppletConfigDto) {
        List<Long> applectIds = landingPageWechatAppletConfigDto.getApplectIds();
        Long advertiserAccountGroupId = landingPageWechatAppletConfigDto.getAdvertiserAccountGroupId();
        if (CollectionUtils.isEmpty(applectIds) || ObjectUtils.isEmpty(advertiserAccountGroupId)) {
            throw new RestException("参数错误!");
        }
        List<LandingPageWechatAppletConfig> landingPageWechatAppletConfigs = this.listByIds(applectIds);
        if (CollectionUtils.isEmpty(landingPageWechatAppletConfigs)) {
            throw new RestException("小程序不存在!");
        }
        Map<Long, LandingPageWechatAppletConfig> configMap = landingPageWechatAppletConfigs.stream().collect(Collectors.toMap(LandingPageWechatAppletConfig::getId, Function.identity()));
        List<LandingPageWechatAppletConfigVo> error = new ArrayList<>();
        applectIds.stream().forEach(e -> {
            LandingPageWechatAppletConfig landingPageWechatAppletConfig = configMap.get(e);
            this.unbind(landingPageWechatAppletConfig, advertiserAccountGroupId, error);
        });
        String agentId = TenantContextHolder.get();
        //清除当前项目小程序缓存
        EnterpriseWechatType enterpriseWechatType = landingPageWechatAppletConfigService.getEnterpriseWechatType(agentId);
        landingPageWechatAppletConfigRedis.deleteByPmpId(agentId, advertiserAccountGroupId, enterpriseWechatType);
        return error;
    }

    /**
     * 解绑操作
     *
     * @param landingPageWechatAppletConfig
     * @param advertiserAccountGroupId
     * @param error                         解绑失败的小程序
     */
    private void unbind(LandingPageWechatAppletConfig landingPageWechatAppletConfig, Long advertiserAccountGroupId, List<LandingPageWechatAppletConfigVo> error) {
        LandingPageWechatAppletConfigVo errorConfig = null;
        //小程序或项目不存在
        if (ObjectUtils.isEmpty(landingPageWechatAppletConfig) || ObjectUtils.isEmpty(advertiserAccountGroupId)) {
            return;
        }
        Long id = landingPageWechatAppletConfig.getId();
        String wechatAppletName = landingPageWechatAppletConfig.getWechatAppletName();
        Integer wechatAppletStatus = landingPageWechatAppletConfig.getWechatAppletStatus();
        if (wechatAppletStatus.equals(0)) {
            errorConfig = new LandingPageWechatAppletConfigVo();
            errorConfig.setId(id)
                .setWechatAppletName(landingPageWechatAppletConfig.getWechatAppletName())
                .setErrorMsg("小程序:【" + wechatAppletName + "】状态异常,不可解绑!");
            error.add(errorConfig);
            return;
        }
        //查询当前小程序与pmp关联数
        LambdaQueryWrapper<LandingPageWechatAppletPmpRel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LandingPageWechatAppletPmpRel::getLandingPageWechatAppletId, id);
        int count = this.landingPageWechatAppletPmpRelService.count(lambdaQueryWrapper);
        //小程序仅与一个pmp绑定
        if (count <= 1) {
            errorConfig = new LandingPageWechatAppletConfigVo();
            errorConfig.setId(id)
                .setWechatAppletName(landingPageWechatAppletConfig.getWechatAppletName())
                .setErrorMsg("小程序:【" + wechatAppletName + "】仅与一个项目存在绑定关系!");
            error.add(errorConfig);
            return;
        }
        //查询参数加上项目ID
        lambdaQueryWrapper.eq(LandingPageWechatAppletPmpRel::getAdvertiserAccountGroupId, advertiserAccountGroupId);
        boolean b = this.landingPageWechatAppletPmpRelService.remove(lambdaQueryWrapper);
        if (!b) {
            errorConfig = new LandingPageWechatAppletConfigVo();
            errorConfig.setId(id)
                .setWechatAppletName(landingPageWechatAppletConfig.getWechatAppletName())
                .setErrorMsg("小程序:【" + wechatAppletName + "】解绑失败!");
            error.add(errorConfig);
        }
    }


    /**
     * 判断项目内小程序满足的版本
     *
     * @param advertiserAccountGroupId
     * @return
     */
    public JSONObject getPmpWechatAppletMinVersionJson(ai.yiye.agent.domain.AgentConf agent, Long advertiserAccountGroupId) {
        List<LandingPageWechatAppletConfig> minNextVersionConfig = baseMapper.selectMinNextVersionByTemplateType(TenantContextHolder.get(), advertiserAccountGroupId);
        List<LandingPageWechatAppletConfig> minVersionConfig = baseMapper.selectMinVersionByTemplateType(TenantContextHolder.get(), advertiserAccountGroupId);
        AppletTemplateType nextTemplateType = null;
        List<LandingPageWechatAppletTemplate> templateTypes = landingPageWechatTemplateService.getMaxTemplateTypeList();
        AdvertiserAccountGroup advertiserAccountGroup = advertiserAccountGroupRemote.fetchById(advertiserAccountGroupId);
        JSONObject minVersion = getMinVersionJson(minVersionConfig, minNextVersionConfig, advertiserAccountGroup.getLandingPageAppletVersionJson(), agent.getLandingPageVersion(), templateTypes);
        advertiserAccountGroupRemote.updateById(new AdvertiserAccountGroupRequestBody().setLandingPageWechatAppletVersionJson(minVersion).setId(advertiserAccountGroupId));
        return minVersion;


//        //获取可访问小程序的最小版本
//        LandingPageWechatAppletConfig minNextVersionConfig = baseMapper.selectMinNextVersion(TenantContextHolder.get(), advertiserAccountGroupId);
//        LandingPageWechatAppletConfig minVersionConfig = baseMapper.selectMinVersion(TenantContextHolder.get(), advertiserAccountGroupId);
//        String nextVersion = null;
//        String version = null;
//        if (Objects.nonNull(minNextVersionConfig)) {
//            nextVersion = minNextVersionConfig.getNextVersion();
//        }
//        if (Objects.nonNull(minVersionConfig)) {
//            version = minVersionConfig.getVersion();
//        }
//        LandingPageWechatAppletTemplate one = landingPageWechatTemplateService.getOne(new LambdaQueryWrapper<LandingPageWechatAppletTemplate>().orderByDesc(LandingPageWechatAppletTemplate::getId).last("LIMIT 1"));
//        //最小版本可以满足的对应编辑器的版本list
//        ai.yiye.agent.domain.AgentConf agentConf = agentConfService.getOne(new LambdaQueryWrapper<ai.yiye.agent.domain.AgentConf>().eq(ai.yiye.agent.domain.AgentConf::getAgentId, TenantContextHolder.get()).last("limit 1"));
//        AdvertiserAccountGroup advertiserAccountGroup = advertiserAccountGroupRemote.fetchById(advertiserAccountGroupId);
//        String minVersion = getMinVersion(version, nextVersion, advertiserAccountGroup.getLandingPageAppletVersionJson(), agentConf.getLandingPageWechatAppletVersion(), one.getUserVersion());
//        advertiserAccountGroupRemote.updateById(new AdvertiserAccountGroupRequestBody().setLandingPageWechatAppletVersion(minVersion).setId(advertiserAccountGroupId));
//        //跟新项目可使用的版本
//        return minVersion;
    }


    /**
     * 判断项目内小程序满足的版本
     *
     * @param advertiserAccountGroupId
     * @return
     */
    public String getPmpWechatAppletMinVersion(Long advertiserAccountGroupId) {
        //获取可访问小程序的最小版本
        LandingPageWechatAppletConfig minNextVersionConfig = baseMapper.selectMinNextVersion(TenantContextHolder.get(), advertiserAccountGroupId);
        LandingPageWechatAppletConfig minVersionConfig = baseMapper.selectMinVersion(TenantContextHolder.get(), advertiserAccountGroupId);
        String nextVersion = null;
        String version = null;
        if (Objects.nonNull(minNextVersionConfig)) {
            nextVersion = minNextVersionConfig.getNextVersion();
        }
        if (Objects.nonNull(minVersionConfig)) {
            version = minVersionConfig.getVersion();
        }
        LandingPageWechatAppletTemplate one = landingPageWechatTemplateService.getOne(new LambdaQueryWrapper<LandingPageWechatAppletTemplate>().orderByDesc(LandingPageWechatAppletTemplate::getId).last("LIMIT 1"));
        //最小版本可以满足的对应编辑器的版本list
        ai.yiye.agent.domain.AgentConf agentConf = agentConfService.getOne(new LambdaQueryWrapper<ai.yiye.agent.domain.AgentConf>().eq(ai.yiye.agent.domain.AgentConf::getAgentId, TenantContextHolder.get()).last("limit 1"));
        AdvertiserAccountGroup advertiserAccountGroup = advertiserAccountGroupRemote.fetchById(advertiserAccountGroupId);
        String minVersion = getMinVersion(version, nextVersion, advertiserAccountGroup.getLandingPageWechatAppletVersion(), agentConf.getLandingPageWechatAppletVersion(), one.getUserVersion());
        advertiserAccountGroupRemote.updateById(new AdvertiserAccountGroupRequestBody().setLandingPageWechatAppletVersion(minVersion).setId(advertiserAccountGroupId));
        //跟新项目可使用的版本
        return minVersion;
    }

    /**
     * 比较判断当前可访问的小程序版本
     * 注意事项：如果小程序模板 与 已经设置过的当前版本比较不一致 需要使用当前最小的小程序版本
     * 如果模板上的版本 与 pmp 上版本一致
     * 当前项目可使用的小程序版本只增加 不降低
     *
     * @param vsersion    1.177.3
     * @param nextVersion 1.177.0
     * @param pmpVersion
     * @return
     */
    private static String getMinVersion(String vsersion, String nextVersion, String pmpVersion, String agentVersion, String userVersion) {
        String minVersion = WecatAppleVersionUtil.getMinVersion(vsersion, nextVersion);
        String maxVersion = WecatAppleVersionUtil.getMaxVersion(minVersion, pmpVersion);
        String endVersion = WecatAppleVersionUtil.getMaxVersion(maxVersion, agentVersion);
        if (StringUtils.isBlank(minVersion)) {
            return WecatAppleVersionUtil.getMinVersion(minVersion, userVersion);
        }
        return endVersion;
    }

    /**
     * * 注意事项：如果小程序模板 与 已经设置过的当前版本比较不一致 需要使用当前最小的小程序版本
     * * 如果模板上的版本 与 pmp 上版本一致
     * * 当前项目可使用的小程序版本只增加 不降低
     *
     * @return
     */
    private JSONObject getMinVersionJson(List<LandingPageWechatAppletConfig> minVersionConfig, List<LandingPageWechatAppletConfig> minNextVersionConfig, JSONObject groupAppletVersion, JSONObject agentAppletVersion, List<LandingPageWechatAppletTemplate> templateTypes) {
        Map<Long, LandingPageWechatAppletConfig> minTypeVersion = minVersionConfig.stream().collect(Collectors.toMap(LandingPageWechatAppletConfig::getTemplateTypeId, Function.identity(), (key1, key2) -> key2));
        Map<Long, LandingPageWechatAppletConfig> minNextTypeVersion = minNextVersionConfig.stream().collect(Collectors.toMap(LandingPageWechatAppletConfig::getTemplateTypeId, Function.identity(), (key1, key2) -> key2));

        JSONObject jsonObject = new JSONObject();
        for (int i = 0; i < templateTypes.size(); i++) {
            AppletTemplateType templateTypeByValue = AppletTemplateType.getTemplateTypeByValue(templateTypes.get(i).getTemplateTypeId());
            String type = templateTypeByValue.toString();
            String versionTemplate = templateTypes.get(i).getUserVersion();
            String minVersionTemplate = null;
            String minNextVersionTemplate = null;
            if (Objects.isNull(minTypeVersion.get(templateTypeByValue.getId().longValue()))) {
                minVersionTemplate = "";
            } else {
                minVersionTemplate = minTypeVersion.get(templateTypeByValue.getId().longValue()).getVersion();
            }

            if (Objects.isNull(minNextTypeVersion.get(templateTypeByValue.getId().longValue()))) {
                minNextVersionTemplate = "";
            } else {
                minNextVersionTemplate = minNextTypeVersion.get(templateTypeByValue.getId().longValue()).getNextVersion();
            }
            String minVersion = WecatAppleVersionUtil.getMinVersion(minVersionTemplate, minNextVersionTemplate);
            String maxVersion = null;
            if (Objects.isNull(groupAppletVersion)) {
                maxVersion = WecatAppleVersionUtil.getMaxVersion(minVersion, "");
            } else {
                maxVersion = WecatAppleVersionUtil.getMaxVersion(minVersion, groupAppletVersion.getString(type));
            }
            String endVersion = null;
            if (Objects.isNull(agentAppletVersion)) {
                endVersion = WecatAppleVersionUtil.getMaxVersion(maxVersion, "");
            } else {
                endVersion = WecatAppleVersionUtil.getMaxVersion(maxVersion, agentAppletVersion.getString(type));
            }
            if (StringUtils.isEmpty(minVersion)) {
                String minVersion1 = WecatAppleVersionUtil.getMinVersion(minVersion, versionTemplate);
                jsonObject.put(type, minVersion1);
            } else {
                jsonObject.put(type, endVersion);
            }
        }

        return jsonObject;

    }


    /**
     * 根据 channelId 查找 appId
     *
     * @param originalId 渠道ID
     * @return
     */
    public String findAppIdByOriginalId(String originalId) {
        return getBaseMapper().findAppIdByOriginalId(originalId);
    }


    public LandingPageWechatAppletConfig selectConfByAppid(String appid) {
        return baseMapper.selectOne(new LambdaQueryWrapper<LandingPageWechatAppletConfig>().eq(LandingPageWechatAppletConfig::getWechatAppletAppid, appid));
    }

    public void updateTemplateTypeAndVersionId(LandingPageWechatAppletConfig wechatAppletConfig) {

        baseMapper.updateTemplateTypeAndVersionId(wechatAppletConfig);

    }

    public Boolean updateRemark(LandingPageWechatAppletConfig landingPageWechatAppletConfig) {
        return baseMapper.updateRemark(landingPageWechatAppletConfig);
    }

    //public Boolean offlineNotificationMessageTest(String wechatAppletAppid) {
    //    LandingPageWechatAppletConfig lpwac = baseMapper.selectOne(new LambdaQueryWrapper<LandingPageWechatAppletConfig>()
    //        .eq(LandingPageWechatAppletConfig::getWechatAppletAppid, wechatAppletAppid)
    //        .eq(LandingPageWechatAppletConfig::getAgentId, TenantContextHolder.get())
    //    );
    //    if (Objects.isNull(lpwac)) {
    //        throw new RestException("小程序不存在");
    //    }
    //    baseMapper.updateById(new LandingPageWechatAppletConfig()
    //        .setId(lpwac.getId())
    //        .setOpenAppId(lpwac.getOpenAppId())
    //        .setWechatAppletStatus(0)
    //        .setUpdatedAt(Instant.now())
    //        .setRemarks("生成权限被封禁，参数：测试接口：用于测试指定小程序封禁发送右键通知 + 站内信")
    //    );
    //    wxOpenService.clearRedisCache(lpwac);
    //    //发送站内通知
    //    lpwac.setWechatAppletAbandonType(WechatAppletAbandonType.JOBABANDON);
    //    messageNoticeSender.sendForbidApplet(lpwac);
    //    workWechatClientService.sendWarning(String.format("微信小程序获取scheme失败：生成权限被封禁，下线成功，小程序appid：%s；", lpwac.getWechatAppletAppid()), lpwac.getWechatAppletAppid());
    //    return true;
    //}

    public List<LandingPageWechatAppletConfig> findCanUseWechatAppletNameByWechatAppletId(String agentId, Long landingPageWechatAppletId) {
        return baseMapper.findCanUseWechatAppletNameByWechatAppletId(agentId, landingPageWechatAppletId);
    }

    /**
     * 设置公共参数
     */
    public List<AdvertiserAccountGroup> setCommonParams(WechatAppletWarnMessageDto wawmDto, String agentId) {
        TenantContextHolder.set(agentId);
        //所属项目
        List<Long> pmpRelIds = new ArrayList<>();
        if (!Objects.isNull(wawmDto.getWechatAppletId()) && StringUtils.isNotBlank(agentId)) {
            List<LandingPageWechatAppletPmpRel> pmpRels = landingPageWechatAppletPmpRelService.list(new LambdaQueryWrapper<LandingPageWechatAppletPmpRel>()
                .select(LandingPageWechatAppletPmpRel::getAdvertiserAccountGroupId)
                .eq(LandingPageWechatAppletPmpRel::getAgentId, agentId)
                .eq(LandingPageWechatAppletPmpRel::getLandingPageWechatAppletId, wawmDto.getWechatAppletId())
            );
            if (!org.springframework.util.CollectionUtils.isEmpty(pmpRels)) {
                pmpRelIds.addAll(pmpRels.stream().map(LandingPageWechatAppletPmpRel::getAdvertiserAccountGroupId).collect(Collectors.toList()));
            }
        } else {
            pmpRelIds.addAll(wawmDto.getPmpIds());
        }
        List<AdvertiserAccountGroup> advertiserAccountGroupList = getPmpNames(pmpRelIds);
        wawmDto.setPmpNames(!CollectionUtils.isEmpty(advertiserAccountGroupList) ? StringUtils.join(advertiserAccountGroupList.stream().map(AdvertiserAccountGroup::getName).collect(Collectors.toList()), "；") : "");
        //异常事件详情
        wawmDto.setExceptionDetails((StringUtils.isNotBlank(wawmDto.getExceptionDetails()) ? wawmDto.getExceptionDetails().replaceAll("\"", "\\\\\"") : ""));
        //接口响应内容
        wawmDto.setApiResponseContent(StringUtils.isNotBlank(wawmDto.getApiResponseContent()) ? wawmDto.getApiResponseContent().replaceAll("\"", "\\\\\"") : "");
        return advertiserAccountGroupList;
    }

    /**
     * 根据项目id获取项目名称
     */
    public List<AdvertiserAccountGroup> getPmpNames(List<Long> pmpRelIds) {
        if (!CollectionUtils.isEmpty(pmpRelIds)) {
            List<AdvertiserAccountGroup> advertiserAccountGroupList = advertiserAccountGroupService.list(new LambdaQueryWrapper<AdvertiserAccountGroup>()
                .select(AdvertiserAccountGroup::getId, AdvertiserAccountGroup::getName).in(AdvertiserAccountGroup::getId, pmpRelIds)
            );
            if (CollectionUtils.isEmpty(advertiserAccountGroupList)) {
                return new ArrayList<>();
            }
            return advertiserAccountGroupList;
        }
        return new ArrayList<>();
    }

    /**
     * 发送小程序飞书告警通知
     */
    public void sendWechatAppletFeiShuMessage(LandingPageWechatAppletConfig lpwac, Long advertiserAccountGroupId, String errorMessageContent, String apiResponseContent, SendRobotMessageScene sendRobotMessageScene) {
        if (Objects.isNull(sendRobotMessageScene) || Objects.isNull(lpwac) || StringUtils.isBlank(lpwac.getAgentId())) {
            return;
        }
        try {
            TenantContextHolder.set(lpwac.getAgentId());
            Date nowDate = Date.from(Instant.now());
            //下线、下线后启用备用小程序、下线后当前项目下无可用小程序
            if (SendRobotMessageScene.WECHAT_APPLET_OFFLINE_LIST.contains(sendRobotMessageScene)) {
                //存储是否发送第一次通知消息
                String firstMessageExistRedisKey = RedisConstant.getWechatAppletMessageFirstMessageExist(lpwac.getAgentId(), lpwac.getWechatAppletAppid());
                Long incrementNum = defaultObjectRedisTemplate.opsForValue().increment(firstMessageExistRedisKey);
                //第一次发生异常，立即向mq中发送发书通知队列，在mq中等待10秒，并设置第一次发送过期时间
                if (!Objects.isNull(incrementNum) && incrementNum == 1L) {
                    defaultObjectRedisTemplate.expire(firstMessageExistRedisKey, agentConf.getFirstMessageExistRedisKeyExpireTime(), TimeUnit.DAYS);
                    wechatAppletWarnMessageSender.sendWechatAppletFeiShuMessage(new WechatAppletWarnMessageDto().setSendRobotMessageScene(sendRobotMessageScene)
                        .setWechatAppletId(lpwac.getId()).setWechatAppletName(lpwac.getWechatAppletName())
                        .setWechatAppletAppid(lpwac.getWechatAppletAppid()).setWechatAppletName(lpwac.getWechatAppletName())
                        .setDate(DateTimeUtil.yyyy_MM_dd_HH_mm_ss.format(nowDate)).setExceptionDetails(errorMessageContent)
                        .setApiResponseContent(apiResponseContent).setAgentId(lpwac.getAgentId())
                        .setThreadName(Thread.currentThread().getName())
                    );
                }
                return;
            }
            //无可用小程序 通知
            if (Objects.equals(SendRobotMessageScene.THIS_PROJECT_NOT_WECHAT_APPLET, sendRobotMessageScene)) {
                //存储是否发送第一次通知消息
                String projectNotWechatAppletRedisKey = RedisConstant.getWechatAppletMessageProjectNotWechatApplet(lpwac.getAgentId(), advertiserAccountGroupId);
                Long incrementNum = defaultObjectRedisTemplate.opsForValue().increment(projectNotWechatAppletRedisKey);
                if (!Objects.isNull(incrementNum) && incrementNum == 1L) {
                    //无小程序可用，第一次发送，发送MQ消息，设定redis-key值失效时间为5分钟+3分钟
                    defaultObjectRedisTemplate.expire(projectNotWechatAppletRedisKey, agentConf.getThisProjectNotWechatAppletKeyExpireTime() + 3, TimeUnit.MINUTES);
                    wechatAppletWarnMessageSender.sendThisProjectNotWechatAppletFeiShuMessage(new WechatAppletWarnMessageDto().setSendRobotMessageScene(sendRobotMessageScene)
                        .setDate(DateTimeUtil.yyyy_MM_dd_HH_mm_ss.format(nowDate)).setPmpIds(Collections.singletonList(advertiserAccountGroupId))
                        .setExceptionDetails("当前项目下无可用小程序，请尽快配置；").setAgentId(lpwac.getAgentId()).setThreadName(Thread.currentThread().getName())
                    );
                    //第一次触发消息，同时发送延时队列触发5分钟后提示
                    wechatAppletWarnMessageSender.sendThisProjectNotWechatAppletFeiShuMessageDelay(new WechatAppletWarnMessageDelayDto()
                        .setSendRobotMessageScene(sendRobotMessageScene).setAgentId(lpwac.getAgentId()).setAdvertiserAccountGroupId(advertiserAccountGroupId)
                        .setRedisKey(projectNotWechatAppletRedisKey).setThisProjectNotWechatAppletKeyExpireTime(agentConf.getThisProjectNotWechatAppletKeyExpireTime())
                        .setThreadName(Thread.currentThread().getName())
                    );
                }
            }
        } catch (Exception e) {
            log.error("发送微信小程序告警通知系统内部异常 lpwac={}；advertiserAccountGroupId={}；errorMessageContent={}；apiResponseContent={}；sendRobotMessageScene={}；errorMessage={}；", lpwac, advertiserAccountGroupId, errorMessageContent, apiResponseContent, sendRobotMessageScene, e.getMessage(), e);
        }
    }

    /**
     * 微信小程序-飞书告警通知：下线、下线后启用备用小程序、下线后当前项目下无可用小程序
     */
    public String gethatAppletFeiShuMessage(WechatAppletWarnMessageDto wawmDto) {
        //agentId
        String agentId = wawmDto.getAgentId();
        //Appid
        String wechatAppletid = wawmDto.getWechatAppletAppid();
        //昵称
        String wechatAppletName = StringUtils.isNotBlank(wawmDto.getWechatAppletName()) ? wawmDto.getWechatAppletName() : "";
        //标题
        String title = String.format(wawmDto.getSendRobotMessageScene().getTitle(), wechatAppletName);
        //设置公共参数
        List<AdvertiserAccountGroup> advertiserAccountGroupList = setCommonParams(wawmDto, agentId);
        if (Arrays.asList(SendRobotMessageScene.WECHAT_APPLET_OFFLINE_ENABLE_STANDBY_APPLET, SendRobotMessageScene.WECHAT_APPLET_OFFLINE).contains(wawmDto.getSendRobotMessageScene())) {
            List<LandingPageWechatAppletConfig> lpwacList = landingPageWechatAppletConfigService.findCanUseWechatAppletNameByWechatAppletId(agentId, wawmDto.getWechatAppletId());
            /*
             * 小程序关联了多个pmp项目：
             *
             * 1、部分pmp下存在可用小程序
             * 2、部分pmp下不存在可用小程序
             */
            if (!CollectionUtils.isEmpty(advertiserAccountGroupList) && advertiserAccountGroupList.size() > 1) {//可用小程序关联的pmp项目id
                List<Long> lpwacRelIdList = !CollectionUtils.isEmpty(lpwacList) ? lpwacList.stream().map(LandingPageWechatAppletConfig::getAdvertiserAccountGroupId).collect(Collectors.toList()) : null;
                if (!CollectionUtils.isEmpty(lpwacRelIdList)) {
                    //部分pmp下存在可用小程序、部分pmp下不存在可用小程序
                    StringBuilder sb = new StringBuilder().append("微信小程序【").append(wawmDto.getWechatAppletName()).append("】下线；");
                    List<AdvertiserAccountGroup> atLpwacList = advertiserAccountGroupList.stream().filter(e -> lpwacRelIdList.contains(e.getId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(atLpwacList)) {
                        atLpwacList.forEach(advertiserAccountGroup -> {
                            List<String> hasAppletNameList = lpwacList.stream().filter(e -> Objects.equals(e.getAdvertiserAccountGroupId(), advertiserAccountGroup.getId())).map(LandingPageWechatAppletConfig::getWechatAppletName).collect(Collectors.toList());
                            sb.append(advertiserAccountGroup.getName()).append(" 所属项目下当前小程序还有【").append(hasAppletNameList.get(0)).append(hasAppletNameList.size() > 1 ? ("，等" + hasAppletNameList.size() + "个...") : "").append("】；");
                        });
                    }
                    List<AdvertiserAccountGroup> notAtLpwacList = advertiserAccountGroupList.stream().filter(e -> !lpwacRelIdList.contains(e.getId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(notAtLpwacList)) {
                        sb.append(notAtLpwacList.stream().map(AdvertiserAccountGroup::getName).collect(Collectors.joining("、"))).append(" 所属项目下当前无可用小程序，请尽快配置；");
                    }
                    wawmDto.setExceptionDetails(sb.toString());
                } else {
                    //小程序关联的所有pmp都没有可用小程序
                    wawmDto.setExceptionDetails(advertiserAccountGroupList.stream().map(AdvertiserAccountGroup::getName).collect(Collectors.joining("、")) + " 微信小程序【" + wawmDto.getWechatAppletName() + "】下线，当前项目下无可用小程序，请尽快配置");
                }
            } else {
                /*
                 * 小程序只关联了单个pmp项目
                 *
                 * 1、微信小程序下线，有可用小程序
                 * 2、微信小程序下线，启用备用小程序
                 * 3、微信小程序下线，无任何可用小程序
                 */
                if (!CollectionUtils.isEmpty(lpwacList)) {
                    List<String> hasAppletNameList = lpwacList.stream().map(LandingPageWechatAppletConfig::getWechatAppletName).collect(Collectors.toList());
                    wawmDto.setExceptionDetails(wawmDto.getExceptionDetails() + String.format("，当前项目内还有【%s】", hasAppletNameList.size() > 1 ? (hasAppletNameList.get(0) + "，等" + lpwacList.size() + "个...") : hasAppletNameList.get(0)));
                } else {
                    wawmDto.setExceptionDetails(wawmDto.getExceptionDetails() + "，当前项目无可用小程序，请尽快配置");
                }
            }
        }
        //组装模板数据
        return String.format(FeiShuClientService.WECHAT_APPLET_OFFLINE_MESSAGE_MODEL_STR, title, wechatAppletid, wechatAppletName, wawmDto.getDate(), wawmDto.getPmpNames(), wawmDto.getExceptionDetails(), wawmDto.getApiResponseContent(), agentId, tracer.currentSpan().context().traceIdString(), wawmDto.getThreadName());
    }

    /**
     * 4、当前项目下无可用小程序
     * <p>
     * 微信小程序-飞书告警通知：当前项目下无可用小程序
     */
    public String getThisProjectNotWechatAppletFeiShuMessage(WechatAppletWarnMessageDto wawmDto) {
        //设置公共参数
        setCommonParams(wawmDto, wawmDto.getAgentId());
        return String.format(FeiShuClientService.THIS_PROJECT_NOT_WECHAT_APPLET_MODEL_STR, wawmDto.getSendRobotMessageScene().getTitle(), wawmDto.getDate(), wawmDto.getPmpNames(), wawmDto.getExceptionDetails(), wawmDto.getAgentId(), tracer.currentSpan().context().traceIdString(), wawmDto.getThreadName());
    }


    /**
     * 腾讯官方小程序审核失败，封装飞书通知消息内容
     */
    public String getUrlCallBackFailFeiShuMessage(AuditFailureDto auditFailureDto) {
        //消息模板（腾讯官方小程序页面【落地页名称-渠道名称】落地页id-渠道id 审核失败 【失败原因】,请及时更换页面）
        String content = "落地页名称【%s】渠道名称【%s】pageId:【%s】 %s,失败原因【%s】,请及时更换页面";
        content = String.format(content, auditFailureDto.getLandingPageName(), auditFailureDto.getChannelName(),
            auditFailureDto.getQiyetuiPageId(),
            LandingPageReviewStatus.PRELIMINARY_AUDIT.equals(auditFailureDto.getReview()) ? "巡检审核失败" : "审核失败", auditFailureDto.getFailReason());
        return String.format(FeiShuClientService.TENCENT_WECHAT_APPLET_AUDIT_FAIL_MESSAGE_MODEL_STR, auditFailureDto.getNoticeTitle(), auditFailureDto.getDate(), auditFailureDto.getPmpName(), content, auditFailureDto.getAgentId(), tracer.currentSpan().context().traceIdString(), auditFailureDto.getThreadName());
    }

    /**
     * 联系我二维码生成失败，封装飞书通知消息内容
     */
    public String getCustomerContactFailSendMessage(AuditFailureDto auditFailureDto) {
        String content = "腾讯官方小程序内二维码功能异常,所属企微【%s】,客服id【%s】,客服userid【%s】,异常原因【%s】,请及时处理异常!";
        content = String.format(content, auditFailureDto.getCorpName(), auditFailureDto.getLandingPageWechatCustomerServiceId(), auditFailureDto.getLandingPageWechatCustomerServiceUserId(), auditFailureDto.getFailReason());
        return String.format(FeiShuClientService.TENCENT_WECHAT_APPLET_AUDIT_FAIL_MESSAGE_MODEL_STR, auditFailureDto.getNoticeTitle(), auditFailureDto.getDate(), auditFailureDto.getPmpName(), content, auditFailureDto.getAgentId(), tracer.currentSpan().context().traceIdString(), auditFailureDto.getThreadName());
    }

    /**
     * 手动批量重置小程序状态为可用
     */
    public Integer recoverRefreshStatusByIds(List<Long> ids) {
        return baseMapper.recoverRefreshStatusByIds(ids);
    }

}
