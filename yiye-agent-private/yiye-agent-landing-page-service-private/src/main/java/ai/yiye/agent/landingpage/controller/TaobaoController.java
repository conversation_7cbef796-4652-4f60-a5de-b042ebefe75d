package ai.yiye.agent.landingpage.controller;

import ai.yiye.agent.autoconfigure.log.operation.OperationLog;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.common.util.DateTimeUtil;
import ai.yiye.agent.common.util.TaobaoSignMD5Util;
import ai.yiye.agent.domain.dto.TaobaoMovieLinkAddDTO;
import ai.yiye.agent.domain.dto.TaobaoMovieLinkDTO;
import ai.yiye.agent.domain.enumerations.TaobaoMovieLinkStatus;
import ai.yiye.agent.domain.landingpage.TaobaoMovieLink;
import ai.yiye.agent.domain.landingpage.TaobaoMovieLinkRecord;
import ai.yiye.agent.domain.result.Result;
import ai.yiye.agent.domain.result.ResultCodeEnum;
import ai.yiye.agent.landingpage.dto.TaobaoMovieCallbackRecordDto;
import ai.yiye.agent.landingpage.dto.TaobaoMovieLinkPageDto;
import ai.yiye.agent.landingpage.redis.TaobaoRedis;
import ai.yiye.agent.landingpage.remote.TaobaoOpenApiRemote;
import ai.yiye.agent.landingpage.sender.TaobaoSender;
import ai.yiye.agent.landingpage.service.TaobaoMovieLinkService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;

/**
 * 淘宝 - 控制层
 */
@Slf4j
@RestController
@RequestMapping(path = "/taobao")
public class TaobaoController {

    @Resource
    private TaobaoRedis taobaoRedis;

    @Resource
    private TaobaoSender taobaoSender;

    @Resource
    private TaobaoMovieLinkService taobaoMovieLinkService;

    @Resource
    private TaobaoOpenApiRemote taobaoOpenApiRemote;

    @PostMapping("/movie-link/add")
    @OperationLog("淘宝电影转链新增")
    public Result add(@RequestBody TaobaoMovieLinkAddDTO dto) {
        if (StringUtils.isAnyBlank(dto.getName(), dto.getSecret(), dto.getUserId()) || dto.getAdvertiserAccountGroupId() == null) {
            return Result.error(ResultCodeEnum.FAIL);
        }
        int num = taobaoMovieLinkService.count(new LambdaQueryWrapper<TaobaoMovieLink>()
            .eq(TaobaoMovieLink::getAgentId, TenantContextHolder.get())
            .eq(TaobaoMovieLink::getAdvertiserAccountGroupId, dto.getAdvertiserAccountGroupId())
            .eq(TaobaoMovieLink::getName, dto.getName()));
        if (num > 0) {
            return Result.error(ResultCodeEnum.FAIL, "名称不能重复");
        }
        TreeMap<String, String> paraMap = new TreeMap<>();
        paraMap.put("userId", dto.getUserId());
        paraMap.put("bizType", "tk");
        paraMap.put("timestamp", DateTimeUtil.localDateTimeFormatter(LocalDateTime.now(), "yyyyMMddHHmmss"));
        paraMap.put("sign", TaobaoSignMD5Util.getSignStr(paraMap, dto.getSecret()));
        JSONObject response = taobaoOpenApiRemote.cidLink(paraMap);
        log.info("淘宝CID转链接口-paraMap:{},response:{}", JSONObject.toJSONString(paraMap), JSONObject.toJSONString(response));
        if (response == null || !"success".equals(response.getString("code"))) {
            return Result.success(false);
        }
        JSONObject data = response.getJSONObject("data");
        String link = data.getString("deepLinkUrl");
        String cid = data.getString("cid");
        TaobaoMovieLink taobaoMovieLink = new TaobaoMovieLink();
        taobaoMovieLink.setAgentId(TenantContextHolder.get())
            .setAdvertiserAccountGroupId(dto.getAdvertiserAccountGroupId())
            .setName(dto.getName())
            .setUserId(dto.getUserId())
            .setLink(link)
            .setCid(cid)
            .setSecret(dto.getSecret());
        taobaoMovieLinkService.save(taobaoMovieLink);
        return Result.success(true);
    }

    /**
     * 分页
     *
     * @param page
     * @param dto
     * @return
     */
    @GetMapping("/movie-link/page")
    public Page<TaobaoMovieLink> page(Page<TaobaoMovieLink> page, TaobaoMovieLinkPageDto dto) {
        dto.setAgentId(TenantContextHolder.get());
        return taobaoMovieLinkService.getPage(page, dto);
    }

    /**
     * 淘宝CID订单推送
     *
     * @param dto
     * @return
     */
    @RequestMapping("/webhook")
    public Map<String, Object> webhook(TaobaoMovieCallbackRecordDto dto) {
        log.info("淘宝CID订单推送，dto:{}", JSON.toJSONString(dto));
        if (dto != null) {
            taobaoSender.taobaoMovieWebhookHandle(dto);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("code", "success");
        map.put("message", "SUCCESS");
        map.put("data", true);
        return map;
    }

    /**
     * 获取淘宝电影转链
     *
     * @param dto
     * @return
     */
    @PostMapping("/movie-link/get")
    public Result getLink(@RequestBody TaobaoMovieLinkDTO dto) {
        log.info("落地页-生成页-获取淘宝电影转链-开始-dto:{}", JSONObject.toJSONString(dto));
        if (StringUtils.isAnyBlank(dto.getPid(), dto.getAgentId()) || dto.getTaobaoMovieLinkId() == null) {
            return Result.error(ResultCodeEnum.FAIL);
        }
        //获取链接
        String link = taobaoRedis.getLink(dto.getTaobaoMovieLinkId());
        if (StringUtils.isBlank(link)) {
            return Result.error(ResultCodeEnum.FAIL);
        }
        //自定义参数
        String state = UUID.randomUUID().toString().trim().replaceAll("-", "");
        link = link + "-" + state;
        TaobaoMovieLinkRecord linkRecord = new TaobaoMovieLinkRecord();
        linkRecord.setAgentId(dto.getAgentId())
            .setPid(dto.getPid())
            .setTaobaoMovieLinkId(dto.getTaobaoMovieLinkId())
            .setLink(link)
            .setState(state)
            .setStatus(TaobaoMovieLinkStatus.GENERATE)
            .setCreatedAt(Instant.now());
        taobaoSender.saveTaobaoMovieLinkRecord(linkRecord);
        log.info("落地页-生成页-获取淘宝电影转链-结束-link:{},dto:{}", link, JSON.toJSONString(dto));
        taobaoSender.recordTaoBaoMovieAppletJumpNum(dto);
        return Result.success(link);
    }

}
