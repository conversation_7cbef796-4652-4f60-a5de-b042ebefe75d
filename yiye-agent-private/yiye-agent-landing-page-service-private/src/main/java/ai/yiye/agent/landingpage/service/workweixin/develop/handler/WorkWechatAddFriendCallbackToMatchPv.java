package ai.yiye.agent.landingpage.service.workweixin.develop.handler;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.common.util.DateTimeUtil;
import ai.yiye.agent.common.util.TraceUtil;
import ai.yiye.agent.domain.*;
import ai.yiye.agent.domain.dto.*;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.landingpage.EnterpriseWechat;
import ai.yiye.agent.domain.landingpage.OfficialAccountSendMessageRecord;
import ai.yiye.agent.domain.landingpage.WorkWechatUser;
import ai.yiye.agent.domain.pageview.PageViewInfo;
import ai.yiye.agent.domain.utils.CollectionUtil;
import ai.yiye.agent.domain.utils.CustomerAdParamUtil;
import ai.yiye.agent.domain.utils.UrlUtils;
import ai.yiye.agent.domain.vo.TracePageVO;
import ai.yiye.agent.landingpage.config.CustomerAcquisitionConfig;
import ai.yiye.agent.landingpage.config.LandingPageWechatCustomerContactConfig;
import ai.yiye.agent.landingpage.config.UnAdSceneConfig;
import ai.yiye.agent.landingpage.config.WorkWechatDevelopConf;
import ai.yiye.agent.landingpage.dto.AdSourceEnterpriseWechatTagDTO;
import ai.yiye.agent.landingpage.dto.AddWorkWechatAutoRuleDTO;
import ai.yiye.agent.landingpage.dto.MakeEnterpriseWechatTagDto;
import ai.yiye.agent.landingpage.enums.*;
import ai.yiye.agent.landingpage.redis.LandingPageWechatCustomerServiceRedis;
import ai.yiye.agent.landingpage.remote.AdvertiserAccountGroupRemote;
import ai.yiye.agent.landingpage.sender.*;
import ai.yiye.agent.landingpage.service.*;
import ai.yiye.agent.weixin.client.WorkWeixinApiClient;
import ai.yiye.agent.weixin.domain.CustomerAcquisitionDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.tp.message.WxCpTpMessageHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 企业微信添加好友回调 - 匹配pv曝光 - 生成客资
 */
@Slf4j
public abstract class WorkWechatAddFriendCallbackToMatchPv implements WxCpTpMessageHandler {

    public static final TimeUnit TIME_UNIT = TimeUnit.MINUTES;

    @Autowired
    private PageViewSender pageViewSender;
    @Autowired
    private PageViewInfoPgService pageViewInfoPgService;
    @Autowired
    protected WorkWechatDevelopConf workWechatDevelopConf;
    @Autowired
    protected WorkWeixinApiClient workWeixinApiClient;
    @Autowired
    protected RedisTemplate<String, Object> defaultObjectRedisTemplate;
    @Autowired
    protected EnterpriseWechatService enterpriseWechatService;
    @Autowired
    protected EnterpriseWechatsPmpRelService enterpriseWechatsPmpRelService;
    @Autowired
    protected CustomerService customerService;
    @Autowired
    private SubmitDataService submitDataService;
    @Autowired
    protected StringRedisTemplate stringRedisTemplate;
    @Autowired
    protected UploadSender uploadSender;
    @Autowired
    protected ApplicationContext applicationContext;
    @Autowired
    private LandingPageWechatCustomerServiceService landingPageWechatCustomerServiceService;
    @Autowired
    private SubmitDataSender submitDataSender;
    @Autowired
    protected LandingPageSender landingPageSender;
    @Autowired
    private RedisTemplate<String, Object> objectRedisTemplate;
    @Autowired
    private AdvertiserAccountGroupRemote advertiserAccountGroupRemote;
    @Autowired
    private CustomerSender customerSender;

    @Resource
    protected PageViewInfoService pageViewInfoService;

    @Resource
    protected ai.yiye.agent.landingpage.config.AgentConf agentConf;

    @Resource
    private LandingPageWechatCustomerContactConfig landingPageWechatCustomerContactConfig;

    @Resource
    private TrafficEngineCustomerServiceOperationSender trafficEngineCustomerServiceOperationSender;

    @Resource
    private EnterpriseWechatCustomerAcquisitionSender enterpriseWechatCustomerAcquisitionSender;

    @Resource
    private CustomerAcquisitionConfig customerAcquisitionConfig;

    @Resource
    private LandingPageWechatCustomerContactSender landingPageWechatCustomerContactSender;

    @Resource
    private OfficialAccountSendMessageRecordService officialAccountSendMessageRecordService;

    @Resource
    private EnterpriseWechatTagStrategyService enterpriseWechatTagStrategyService;

    @Resource
    private OfficialWechatCustomerContactService officialWechatCustomerContactService;

    @Resource
    private LandingPageWechatCustomerServiceGroupRelService landingPageWechatCustomerServiceGroupRelService;

    @Resource
    private OfficialWechatCustomerServiceGroupService officialWechatCustomerServiceGroupService;

    @Resource
    private RobotCustomerLiveCodeRecordService robotCustomerLiveCodeRecordService;

    @Resource
    private RobotCustomerMultipleLiveCodeRecordService robotCustomerMultipleLiveCodeRecordService;

    @Resource
    private RobotCustomerAcquisitionLinkRecordService robotCustomerAcquisitionLinkRecordService;

    @Resource
    private EnterpriseWechatTagStrategySender enterpriseWechatTagStrategySender;

    @Autowired
    private PageViewinfoSender pageViewinfoSender;

    @Autowired
    private EnterpriseWechatRobotCustomerService enterpriseWechatRobotCustomerService;

    @Resource
    private WechatOfficialAccountAssistantReplyService wechatOfficialAccountAssistantReplyService;

    @Resource
    private UnAdSceneConfig unAdSceneConfig;

    @Autowired
    private EnterpriseRobotCustomerSender enterpriseRobotCustomerSender;

    @Resource
    private LandingPageWechatCustomerServiceRedis landingPageWechatCustomerServiceRedis;

    @Resource
    private RobotDynamicCustomerContactGenerateRecordService robotDynamicCustomerContactGenerateRecordService;

    /**
     * 企业微信添加好友回调 - 匹配pv曝光 - 生成客资上报
     *
     * @param agentId
     * @param externalUnionId
     * @param externalUserId
     * @param name
     * @param corpId
     * @param userId
     * @param ewacr
     * @param externalUserSex
     */
    public void matching(final String agentId, final String externalUnionId, final String externalUserId,
                         final String name, final String corpId, final String corpName, final String userId,
                         final EnterpriseWechatAddContactRecord ewacr, EnterpriseWechat enterpriseWechat, String state,
                         final boolean linkCheckFlag, Sex externalUserSex) {
        final Long TIMES = workWechatDevelopConf.getMatchingDataTime();
        log.info("企业微信添加好友回调匹配pv曝光生成客资，开始匹配：agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；times={}", agentId, externalUnionId, externalUserId, name, corpId, userId, TIMES);
        ewacr.setWechatUserIdMatchingStatus(0).setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.NOT_ADDED);
        try {
            TenantContextHolder.set(agentId);
            //基于企微维度保存加粉代开发
            landingPageWechatCustomerServiceRedis.saveAddEnterpriseWechatStatus(corpId, externalUserId);
            //配置的客服
            List<LandingPageWechatCustomerService> lpwcsList = landingPageWechatCustomerServiceService.list(new LambdaQueryWrapper<LandingPageWechatCustomerService>()
                .eq(LandingPageWechatCustomerService::getCorpId, corpId)
                .eq(LandingPageWechatCustomerService::getWechatUserId, userId)
            );
            final Instant todayNow = Instant.now();
            final Instant beforeNow = todayNow.minusMillis(TIME_UNIT.toMillis(TIMES));
            WechatAppletAddWay wechatAppletAddWay = ewacr.getWechatAppletAddWay();
            log.info("明文代开发回调加粉添加方式:{}", wechatAppletAddWay);
            if (WechatAppletAddWay.ADD_THROUGH_CUSTOMER_ACQUISITION_LINK.equals(wechatAppletAddWay)) {
                //异步进行获客链接接口防刷检测
                log.info("明文代开发回调加粉添加方式:{},进行获客链接防刷检测", wechatAppletAddWay);
                //进行获客链接防刷
                //this.checkRepeatLink(linkCheckFlag, agentId, state, lpwcsList);
                //判断是否是非广告来源加粉客资，进行打标签
                if (StringUtils.isBlank(state)) {
                    log.info("通过获客链接加粉，没有state参数,不走自归因参数逻辑,判断是否是非广告来源加粉客资，进行打标签");
                    //加粉方式为获客助手加粉 但state为空的 说明加粉时的获客链接时没有带参数的 直接打上非广告标签
                    sendMessageMakeNonAdSourceEnterpriseWechatTag(null, new MakeEnterpriseWechatTagDto().setCorpId(corpId).setUserId(userId).setExternalUserUserid(externalUserId), null, MakeNonAdSourceCustomerTagEventType.ADD_THROUGH_CUSTOMER_ACQUISITION_LINK_WITHOUT_STATE, externalUserSex);
                } else {
                    //判断获客链接是否被篡改
                    sendMessageMakeNonAdSourceEnterpriseWechatTag(state, new MakeEnterpriseWechatTagDto().setCorpId(corpId).setUserId(userId).setExternalUserUserid(externalUserId).setWechatAppletAddWay(WechatAppletAddWay.ADD_THROUGH_CUSTOMER_ACQUISITION_LINK), null, MakeNonAdSourceCustomerTagEventType.ADD_THROUGH_CUSTOMER_ACQUISITION_LINK_WITHOUT_STATE, externalUserSex);
                }
            }

            try {
                //state参数级别最高，优先判断
                if (StringUtils.isNotBlank(state)) {
                    boolean b = matchStateResult(agentId, externalUnionId, externalUserId, name, corpId, corpName, userId, ewacr, enterpriseWechat, state, lpwcsList, externalUserSex);
                    if (b) {
                        return;
                    }
                }
            } catch (Exception e) {
                log.error("企业微信添加好友回调匹配pv曝光生成客资,state参数逻辑异常!state:{}", state, e);
            }


            /**
             * 匹配微信客服添加好友
             */
            PageViewInfo wechatCustomerPageView = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                //48小时内
                .gt(PageViewInfo::getCreatedAt, beforeNow)
                //unionid_B + userid_B 两个字段完全匹配
                .eq(PageViewInfo::getWechatExternalUserid, externalUserId)
                //已长按识别
                .eq(PageViewInfo::getIdentifyQrCodeStatus, IdentifyQrCodeStatus.IDENTIFIED)
                //未匹配过的
                .eq(PageViewInfo::getAddEnterpriseWechatStatus, AddEnterpriseWechatStatus.NOT_ADDED)
                //最近一次携带媒体下发的click-ID的访问
                .orderByDesc(PageViewInfo::getId).last(" limit 1"));


            if (Objects.nonNull(wechatCustomerPageView) && LinkType.WECHAT_CUSTOMER_ROBOT_SERVICE.equals(wechatCustomerPageView.getLinkType())) {
                enterpriseWechatRobotCustomerService.statisticWechatCustomerServiceRobotData(
                    wechatCustomerPageView.getOriginRobotId(),
                    EnterpriseWechatRobotCustomerDataStatisticsField.ADD_ENTERPRISE_WECHAT_COUNT,
                    null
                );
            }
            /**
             * 没有父级页面的
             * 只有 微信客服内 长按加粉 externalUserId 值才会存在
             */
            /**
             * 匹配微信客服匹配成功
             */
            if (Objects.nonNull(wechatCustomerPageView) && StringUtils.isNotBlank(wechatCustomerPageView.getPid()) && (LinkType.WECHAT_CUSTOMER_ROBOT_SERVICE.equals(wechatCustomerPageView.getLinkType()) && StringUtils.isNotBlank(wechatCustomerPageView.getParentPid()))) {
                log.info("微信客服添加好友匹配成功：agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；", agentId, externalUnionId, externalUserId, name, corpId, userId, JSONObject.toJSONString(wechatCustomerPageView));
                matchedPvSaveCustomerAndSaveCache(agentId, externalUserId, externalUnionId, name, wechatCustomerPageView, ewacr, AddSource.LANDING_PAGE_LINK, lpwcsList, enterpriseWechat, userId, todayNow, beforeNow, SubmitType.WECHAT_APPLET, corpId);
                //判断是否是非广告来源加粉客资，进行打标签
                log.info("不走自归因参数逻辑,判断是否是非广告来源加粉客资，进行打标签");
                sendMessageMakeNonAdSourceEnterpriseWechatTag(null, new MakeEnterpriseWechatTagDto().setCorpId(corpId).setUserId(userId).setExternalUserUserid(externalUserId), wechatCustomerPageView, MakeNonAdSourceCustomerTagEventType.WITHOUT_STATE, externalUserSex);
                return;
            }
            /**
             *  规则： unionid userid 一致
             *  clickid is not null
             *  没有匹配过的最近一条
             */

            PageViewInfo pvInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                //48小时内
                .gt(PageViewInfo::getCreatedAt, beforeNow)
                .eq(PageViewInfo::getWechatCustomerServiceUserId, userId)
                //已长按识别
                .eq(PageViewInfo::getIdentifyQrCodeStatus, IdentifyQrCodeStatus.IDENTIFIED)
                //unionid_B + userid_B 两个字段完全匹配
                .eq(PageViewInfo::getEnterpriseWechatCorpId, corpId)
                .and(wrapper -> wrapper.eq(PageViewInfo::getWechatAppletUnionid, externalUnionId)
                    .or(wr -> wr.eq(PageViewInfo::getWechatUnionid, externalUnionId)))
                //未匹配过的
                .eq(PageViewInfo::getAddEnterpriseWechatStatus, AddEnterpriseWechatStatus.NOT_ADDED)
                //最近一次携带媒体下发的click-ID的访问
                .isNotNull(PageViewInfo::getClickId).orderByDesc(PageViewInfo::getId).last(" limit 1")
            );
            if (!Objects.isNull(pvInfo) && !StringUtils.isBlank(pvInfo.getPid())) {
                log.info("企业微信添加好友回调匹配pv曝光生成客资，规则1.1.0匹配成功：agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；", agentId, externalUnionId, externalUserId, name, corpId, userId, pvInfo);
                matchedPvSaveCustomerAndSaveCache(agentId, externalUserId, externalUnionId, name, pvInfo, ewacr, AddSource.LANDING_PAGE_LINK, lpwcsList, enterpriseWechat, userId, todayNow, beforeNow, SubmitType.WECHAT_APPLET, corpId);
                //判断是否是非广告来源加粉客资，进行打标签
                log.info("不走自归因参数逻辑,判断是否是非广告来源加粉客资，进行打标签");
                sendMessageMakeNonAdSourceEnterpriseWechatTag(null, new MakeEnterpriseWechatTagDto().setCorpId(corpId).setUserId(userId).setExternalUserUserid(externalUserId), pvInfo, MakeNonAdSourceCustomerTagEventType.WITHOUT_STATE, externalUserSex);
                return;
            }

            /**
             * 规则： unionid 一致
             *  clickid is not null
             * 没有匹配过的最近一条
             */
            pvInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                //48小时内
                .gt(PageViewInfo::getCreatedAt, beforeNow)
                //已长按识别
                .eq(PageViewInfo::getIdentifyQrCodeStatus, IdentifyQrCodeStatus.IDENTIFIED)
                .eq(PageViewInfo::getAddEnterpriseWechatStatus, AddEnterpriseWechatStatus.NOT_ADDED)
                //使用unionid进行匹配
                .and(wrapper -> wrapper.eq(PageViewInfo::getWechatAppletUnionid, externalUnionId)
                    .or(wr -> wr.eq(PageViewInfo::getWechatUnionid, externalUnionId)))
                //最近一次携带媒体下发的click-ID的访问
                .isNotNull(PageViewInfo::getClickId).orderByDesc(PageViewInfo::getId).last(" limit 1")
            );
            if (!Objects.isNull(pvInfo) && !StringUtils.isBlank(pvInfo.getPid())) {
                log.info("企业微信添加好友回调匹配pv曝光生成客资，规则1.2匹配成功：agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；", agentId, externalUnionId, externalUserId, name, corpId, userId, pvInfo);
                matchedPvSaveCustomerAndSaveCache(agentId, externalUserId, externalUnionId, name, pvInfo, ewacr, AddSource.LANDING_PAGE_LINK, lpwcsList, enterpriseWechat, userId, todayNow, beforeNow, SubmitType.WECHAT_APPLET, corpId);
                //判断是否是非广告来源加粉客资，进行打标签
                log.info("不走自归因参数逻辑,判断是否是非广告来源加粉客资，进行打标签");
                sendMessageMakeNonAdSourceEnterpriseWechatTag(null, new MakeEnterpriseWechatTagDto().setCorpId(corpId).setUserId(userId).setExternalUserUserid(externalUserId), pvInfo, MakeNonAdSourceCustomerTagEventType.WITHOUT_STATE, externalUserSex);

                return;
            }

            /**
             * 规则： unionid userid 一致
             * clickid is null
             * 没有匹配过的最近一条
             */
            pvInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                //48小时内
                .gt(PageViewInfo::getCreatedAt, beforeNow)
                .eq(PageViewInfo::getWechatCustomerServiceUserId, userId)
                .eq(PageViewInfo::getEnterpriseWechatCorpId, corpId)
                //已长按识别
                .eq(PageViewInfo::getIdentifyQrCodeStatus, IdentifyQrCodeStatus.IDENTIFIED)
                //未匹配过的
                .eq(PageViewInfo::getAddEnterpriseWechatStatus, AddEnterpriseWechatStatus.NOT_ADDED)
                //unionid_B + userid_B 两个字段完全匹配
                .and(wrapper -> wrapper.eq(PageViewInfo::getWechatAppletUnionid, externalUnionId)
                    .or(wr -> wr.eq(PageViewInfo::getWechatUnionid, externalUnionId)))
                //最近一次携带媒体下发的click-ID的访问
                .isNull(PageViewInfo::getClickId).orderByDesc(PageViewInfo::getId).last(" limit 1")
            );
            if (!Objects.isNull(pvInfo) && !StringUtils.isBlank(pvInfo.getPid())) {
                log.info("企业微信添加好友回调匹配pv曝光生成客资，规则1.3匹配成功：agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；", agentId, externalUnionId, externalUserId, name, corpId, userId, pvInfo);
                matchedPvSaveCustomerAndSaveCache(agentId, externalUserId, externalUnionId, name, pvInfo, ewacr, AddSource.LANDING_PAGE_LINK, lpwcsList, enterpriseWechat, userId, todayNow, beforeNow, SubmitType.WECHAT_APPLET, corpId);
                //判断是否是非广告来源加粉客资，进行打标签
                log.info("不走自归因参数逻辑,判断是否是非广告来源加粉客资，进行打标签");
                sendMessageMakeNonAdSourceEnterpriseWechatTag(null, new MakeEnterpriseWechatTagDto().setCorpId(corpId).setUserId(userId).setExternalUserUserid(externalUserId), pvInfo, MakeNonAdSourceCustomerTagEventType.WITHOUT_STATE, externalUserSex);

                return;
            }

            /**
             * unionid 一致
             * clickid is null
             * 没有匹配过的最近一条
             */
            pvInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                //48小时内
                .gt(PageViewInfo::getCreatedAt, beforeNow)
                //已长按识别
                .eq(PageViewInfo::getIdentifyQrCodeStatus, IdentifyQrCodeStatus.IDENTIFIED)
                .eq(PageViewInfo::getAddEnterpriseWechatStatus, AddEnterpriseWechatStatus.NOT_ADDED)
                //使用unionid进行匹配
                .and(wrapper -> wrapper.eq(PageViewInfo::getWechatAppletUnionid, externalUnionId)
                    .or(wr -> wr.eq(PageViewInfo::getWechatUnionid, externalUnionId)))
                //最近一次携带媒体下发的click-ID的访问
                .isNull(PageViewInfo::getClickId).orderByDesc(PageViewInfo::getId).last(" limit 1")
            );
            if (!Objects.isNull(pvInfo) && !StringUtils.isBlank(pvInfo.getPid())) {
                log.info("企业微信添加好友回调匹配pv曝光生成客资，规则1.4匹配成功：agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；", agentId, externalUnionId, externalUserId, name, corpId, userId, pvInfo);
                matchedPvSaveCustomerAndSaveCache(agentId, externalUserId, externalUnionId, name, pvInfo, ewacr, AddSource.LANDING_PAGE_LINK, lpwcsList, enterpriseWechat, userId, todayNow, beforeNow, SubmitType.WECHAT_APPLET, corpId);
                //判断是否是非广告来源加粉客资，进行打标签
                log.info("不走自归因参数逻辑,判断是否是非广告来源加粉客资，进行打标签");
                sendMessageMakeNonAdSourceEnterpriseWechatTag(null, new MakeEnterpriseWechatTagDto().setCorpId(corpId).setUserId(userId).setExternalUserUserid(externalUserId), pvInfo, MakeNonAdSourceCustomerTagEventType.WITHOUT_STATE, externalUserSex);

                return;
            }


            /**
             * 已经匹配过的
             * unionid 相等
             */
            pvInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                //48小时内
                .gt(PageViewInfo::getCreatedAt, beforeNow)
                //已长按识别
                .eq(PageViewInfo::getIdentifyQrCodeStatus, IdentifyQrCodeStatus.IDENTIFIED)
                .eq(PageViewInfo::getAddEnterpriseWechatStatus, AddEnterpriseWechatStatus.ADDED)
                //使用unionid进行匹配
                .and(wrapper -> wrapper.eq(PageViewInfo::getWechatAppletUnionid, externalUnionId)
                    .or(wr -> wr.eq(PageViewInfo::getWechatUnionid, externalUnionId)))
                //匹配过的
                .orderByDesc(PageViewInfo::getId).last(" limit 1")
            );
            if (!Objects.isNull(pvInfo) && !StringUtils.isBlank(pvInfo.getPid())) {
                log.info("企业微信添加好友回调匹配pv曝光生成客资，规则1.5匹配成功：agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；", agentId, externalUnionId, externalUserId, name, corpId, userId, pvInfo);
                //matchedPvSaveCustomerAndSaveCache(agentId, externalUserId, externalUnionId, name, pvInfo, ewacr, AddSource.LANDING_PAGE_LINK_ONLY_UNIONID_MATCHED_SUCCESS);
                //判断是否是非广告来源加粉客资，进行打标签
                log.info("不走自归因参数逻辑,判断是否是非广告来源加粉客资，进行打标签");
                sendMessageMakeNonAdSourceEnterpriseWechatTag(null, new MakeEnterpriseWechatTagDto().setCorpId(corpId).setUserId(userId).setExternalUserUserid(externalUserId), pvInfo, MakeNonAdSourceCustomerTagEventType.WITHOUT_STATE, externalUserSex);

                return;
            }
            /**
             *  规则三：若使用“规则一”和“规则二”仍未匹配到回调的unionid存在  则判定为“外部来源”，
             *  微信客服列表，同一客服（userid）添加好友次数：相同unionid在48小时内仅记录一次
             */
            //根据回传的企业微信公司di + 微信客服userId，匹配微信客服信息，将客资信息插入到客资列表中


            if (!CollectionUtils.isEmpty(lpwcsList)) {
                log.info("企业微信添加好友回调匹配pv曝光生成客资，规则1.6匹配成功：agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；lpwcsList={}；", agentId, externalUnionId, externalUserId, name, corpId, userId, pvInfo, lpwcsList);
                for (LandingPageWechatCustomerService lpwcs : lpwcsList) {
                    saveOtherSubmitDataAndCustomer(agentId, externalUnionId, externalUserId, name, corpId, userId, ewacr, enterpriseWechat, lpwcsList, todayNow, beforeNow, pvInfo, lpwcs, SubmitType.WECHAT_APPLET);
                }
                log.info("明文代开发,最后的情况，不走自归因参数逻辑,判断是否是非广告来源加粉客资，进行打标签");
                sendMessageMakeNonAdSourceEnterpriseWechatTag(null, new MakeEnterpriseWechatTagDto().setCorpId(corpId).setUserId(userId).setExternalUserUserid(externalUserId), pvInfo, MakeNonAdSourceCustomerTagEventType.WITHOUT_STATE, externalUserSex);
                return;
            }
            log.info("企业微信添加好友回调匹配pv曝光生成客资，规则3.1匹配无结果，匹配结束，直接返回：agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；lpwcsList={}；", agentId, externalUnionId, externalUserId, name, corpId, userId, pvInfo, lpwcsList);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("企业微信添加好友回调匹配pv曝光生成客资，企业微信加粉回调匹配错误：agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；message={}；stackTrace={}；", agentId, externalUnionId, externalUserId, name, corpId, userId, e.getMessage(), e.getStackTrace());
        }
    }

    private void saveOtherSubmitDataAndCustomer(String agentId, String externalUnionId, String externalUserId,
                                                String name, String corpId, String userId, EnterpriseWechatAddContactRecord ewacr,
                                                EnterpriseWechat enterpriseWechat, List<LandingPageWechatCustomerService> lpwcsList,
                                                Instant todayNow, Instant beforeNow, PageViewInfo pvInfo,
                                                LandingPageWechatCustomerService lpwcs, SubmitType submitType) {
        //查询是否已经生成过客资
        Customer one = customerService.getOne(new LambdaQueryWrapper<Customer>()
            .gt(Customer::getCreatedAt, beforeNow)
            .and(wrapper -> wrapper.eq(Customer::getWechatAppletUnionid, externalUnionId)
                .or(wr -> wr.eq(Customer::getWechatUnionid, externalUnionId)))
            .eq(Customer::getWechatAppletUserid, externalUserId)
            .eq(Customer::getCbUserIdMatchingWcsId, lpwcs.getId())
            .orderByDesc(Customer::getId).last(" limit 1"));
        //没有就生成
        if (Objects.isNull(one)) {
            ewacr.setMatchLpwcsId(lpwcs.getId())
                .setWechatAppletGroupChatName(StringUtils.trim(lpwcs.getWechatUserName()))
                .setWechatCustomerServiceId(lpwcs.getId())
                .setAdvertiserAccountGroupId(lpwcs.getAdvertiserAccountGroupId());
            saveSubmitDataAndCustomer(agentId, pvInfo, ewacr, enterpriseWechat, submitType, corpId, userId, externalUserId);
            //微信客服列表增加非落地页链路增加好友的统计
            if (!Objects.equals(ewacr.getWechatUserIdMatchingStatus(), 1) && !Objects.equals(ewacr.getAddEnterpriseWechatStatus(), AddEnterpriseWechatStatus.ADDED)) {
                this.generateWechatCustomerIndicatorStatistics(pvInfo, lpwcs);
            }
        }
    }

    /**
     * 封装微信客服统计数据，发送MQ异步处理
     */
    public void generateWechatCustomerIndicatorStatistics(PageViewInfo pageViewInfo,
                                                          LandingPageWechatCustomerService lpwcs) {
        try {
            if (Objects.nonNull(lpwcs)) {
                log.info("明文代开发，非落地页链路增加企业微信客服，进行统计表统计,客服ID:[{}]", lpwcs.getId());
                WechatCustomerServiceEventDto dto = new WechatCustomerServiceEventDto();
                dto.setWechatCustomerServiceId(lpwcs.getId());
                dto.setWechatCustomerServiceUserId(lpwcs.getWechatUserId());
                dto.setWechatCustomerServiceCorpId(lpwcs.getCorpId());
                dto.setOtherWayAddEnterpriseWechatNum(1);
                dto.setAdvertiserAccountGroupId(lpwcs.getAdvertiserAccountGroupId());
                dto.setIndicatorStatisticEventEnum(IndicatorStatisticEventEnum.OTHER_WAY_ADD_ENTERPRISE_WECHAT_CUSTOMER_SERVICE);
                dto.setStatisticDate(LocalDateTime.now());
                if (Objects.nonNull(pageViewInfo)) {
                    log.info("非落地页链路增加企业微信客服，进行统计表统计,pageId:[{}],url:[{}]，reffer:[{}]", pageViewInfo.getId(), pageViewInfo.getUrl(), pageViewInfo.getReferrer());
                    if (Objects.nonNull(pageViewInfo.getCreatedAt())) {
                        dto.setPageViewTime(LocalDateTime.ofInstant(pageViewInfo.getCreatedAt(), ZoneId.systemDefault()));
                        dto.setConvertTime(LocalDateTime.ofInstant(pageViewInfo.getCreatedAt(), ZoneId.systemDefault()));
                        dto.setStatisticDate(LocalDateTime.ofInstant(pageViewInfo.getCreatedAt(), ZoneId.systemDefault()));
                    } else {
                        dto.setPageViewTime(LocalDateTime.now());
                        dto.setConvertTime(LocalDateTime.now());
                        dto.setStatisticDate(LocalDateTime.now());
                    }
                    //补充1.219.0所需的PV字段
                    dto.setPid(pageViewInfo.getPid())
                        .setSid(pageViewInfo.getSid())
                        .setUid(pageViewInfo.getUid())
                        .setIp(pageViewInfo.getIp())
                        .setUa(pageViewInfo.getUa())
                        .setParentPid(pageViewInfo.getParentPid())
                        .setFirstPagePid(pageViewInfo.getFirstPagePid())
                        .setPlatformId(Platform.getEnumById(pageViewInfo.getPlatformId()))
                        .setProvince(pageViewInfo.getProvince())
                        .setCity(pageViewInfo.getCity())
                        .setDevice(pageViewInfo.getDevice())
                        .setOs(pageViewInfo.getOs())
                        .setOsType(pageViewInfo.getOsType())
                        .setBrowser(pageViewInfo.getBrowser())
                        .setBrowserType(pageViewInfo.getBrowserType())
                        .setNetworkType(pageViewInfo.getNetworkType())
                        .setUrl(pageViewInfo.getUrl())
                        .setClickId(pageViewInfo.getClickId())
                        .setCreativeId(pageViewInfo.getCreativeId())
                        .setAdgroupId(pageViewInfo.getAdgroupId())
                        .setCampaignId(pageViewInfo.getCampaignId())
                        .setLandingPageId(pageViewInfo.getLandingPageId())
                        .setLengthOfStay(pageViewInfo.getLengthOfStay())
                        .setLandingPageChannelId(pageViewInfo.getChannelId())
                        .setFlowSource(pageViewInfo.getFlowSource());
                } else {
                    dto.setPageViewTime(LocalDateTime.now());
                    dto.setConvertTime(LocalDateTime.now());
                    dto.setStatisticDate(LocalDateTime.now());
                }
                dto.setUniqueKey(UUID.randomUUID().toString()).setLockKeyStatisticDate(dto.getStatisticDate());
                landingPageSender.sendWechatCustomerIndicatorStatistics(dto);
            }
        } catch (Exception e) {
            log.error("非落地页链路增加企业微信客服，进行统计表统计异常,", e);
        }
    }

    /**
     * 匹配到曝光 - 保存客资信息 - 记录匹配信息
     */
    public void matchedPvSaveCustomerAndSaveCache(final String agentId, final String externalUserId, final String externalUnionId, final String name,
                                                  final PageViewInfo pvInfo, final EnterpriseWechatAddContactRecord ewacr, final AddSource addSource,
                                                  final List<LandingPageWechatCustomerService> lpwcsList, EnterpriseWechat enterpriseWechat,
                                                  String userId, Instant todayNow, Instant beforeNow, SubmitType submitType, String corpId) {
        LandingPageWechatCustomerService matchingLpwcs = null;
        if (!CollectionUtils.isEmpty(lpwcsList)) {
            //pv中的pmpid与微信客服pmpid不匹配的客服均为外部来源（例如同一个agentId下查询出5条userid相同的微信客服，最终加粉只有一个pmp下的客资才有上报状态，加粉落地页等信息，其余4条均为外部客资）
            List<LandingPageWechatCustomerService> matchPmpLpwcsList = lpwcsList.stream()
                .filter(lpwcs -> Objects.equals(pvInfo.getAdvertiserAccountGroupId(), lpwcs.getAdvertiserAccountGroupId()))
                .collect(Collectors.toList());
            MatchingWorkWechatCustomerDto matchingWorkWechatCustomerDto = null;
            if (!CollectionUtils.isEmpty(matchPmpLpwcsList)) {
                //#20423 【线上问题】客服名称匹配错误，与后台实际添加不一致 https://ones.yiye.ai/project/#/team/WtsduTeT/task/EvybVgBAJWDg2Gpu
                matchingLpwcs = matchPmpLpwcsList.get(0);
                String wechatAppletGroupChatName = StringUtils.trim(matchingLpwcs.getWechatUserName());
                ewacr.setWechatCustomerServiceId(pvInfo.getWechatCustomerServiceId())
                    .setMatchLpwcsId(matchingLpwcs.getId()).setWechatAppletGroupChatName(wechatAppletGroupChatName)
                    .setWechatUserIdMatchingStatus(1).setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED);
                matchingWorkWechatCustomerDto = saveSubmitDataAndCustomer(agentId, pvInfo, ewacr, enterpriseWechat, submitType, corpId, userId, externalUserId);
            }
            //生成其他pmp客资
            List<LandingPageWechatCustomerService> unMatchPmpLpwcsList = lpwcsList.stream()
                .filter(lpwcs -> !Objects.equals(pvInfo.getAdvertiserAccountGroupId(), lpwcs.getAdvertiserAccountGroupId()))
                .collect(Collectors.toList());
            if (!org.springframework.util.CollectionUtils.isEmpty(unMatchPmpLpwcsList) && !(Objects.nonNull(matchingWorkWechatCustomerDto) && Boolean.TRUE.equals(matchingWorkWechatCustomerDto.getUpdateWorkWechatCustomer()))) {
//            if (!CollectionUtils.isEmpty(unMatchPmpLpwcsList) && (Objects.isNull(matchingWorkWechatCustomerDto) || Boolean.FALSE.equals(matchingWorkWechatCustomerDto.getUpdateWorkWechatCustomer()))) {
                unMatchPmpLpwcsList.forEach(lpwcs -> {
                    ewacr.setWechatUserIdMatchingStatus(0)
                        .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.NOT_ADDED);
                    saveOtherSubmitDataAndCustomer(agentId, externalUnionId, externalUserId, name, enterpriseWechat.getCorpid(), userId, ewacr, enterpriseWechat, lpwcsList, todayNow, beforeNow, null, lpwcs, submitType);
                });
            }
            //公众号关注后通过公众号蓝链加粉成功：公众号助手自动回复-携带上级关注页pid、公众号助手点击菜单链接跳转-仅携带蓝链标识
            if (LinkType.WECHAT_OFFICIAL_ACCOUNT_BLUE_URL.equals(pvInfo.getLinkType()) ||
                StringUtils.contains(pvInfo.getUrl(), (UrlUtils.LINK_TYPE_PARAM + "=" + LinkType.WECHAT_OFFICIAL_ACCOUNT_BLUE_URL.getId()))
            ) {
                customerSender.sendUpdateWechatOfficialCustomer(new LandingPageWechatOfficialAccountCustomer()
                    //归因参数
                    .setMatchingPid(pvInfo.getParentPid())
                    .setAddEnterpriseWechatPid(pvInfo.getPid())
                    .setOpenid(StringUtils.isNotBlank(pvInfo.getParentPid()) ? null : pvInfo.getWechatOpenid())
                    //公众号粉丝客资数据
                    .setUserName(ewacr.getWechatAppletName())
                    .setUserPhoto(ewacr.getAvatar())
                    .setExternalUseridAddAt(ewacr.getExternalUseridAddAt())
                    .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
                    .setDeleteWechatTime(null)
                    .setFollowedAddEnterpriseWechat(FollowedAddEnterpriseWechat.FOLLOWED_BLUE_LINK_ADD_ENTERPRISE_WECHAT)
                    .setWechatAppletExternalUserid(ewacr.getWechatAppletExternalUserid())
                    .setWechatAppletGroupChatName(ewacr.getWechatAppletGroupChatName())
                    .setWechatAppletUserid(ewacr.getWechatAppletUserid())
                    .setWechatAppletUserPmpId(pvInfo.getAdvertiserAccountGroupId())
                    .setCorpid(enterpriseWechat.getCorpid())
                    .setEnterpriseWechatName(enterpriseWechat.getCorpName())
                    .setExternalUserSex(ewacr.getExternalUserSex())
                    .setWechatAppletAddWay(ewacr.getWechatAppletAddWay())
                    .setFollowAddWechatPmpId(pvInfo.getAdvertiserAccountGroupId())
                    .setFollowAddWechatLandingPageId(pvInfo.getLandingPageId())
                    .setFollowAddWechatChannelId(pvInfo.getChannelId())
                    .setFollowAddWechatChannelUrl(pvInfo.getUrl())
                    .setSubmitDataId(Objects.isNull(matchingWorkWechatCustomerDto) ? null : matchingWorkWechatCustomerDto.getSubmitDataId())
                    .setCustomerId(Objects.isNull(matchingWorkWechatCustomerDto) ? null : matchingWorkWechatCustomerDto.getCustomerId())
                    .setUpdatedAt(Instant.now())
                );
            }
        }
        if (!SubmitType.OFFICIAL_ACCOUNT_FANS.equals(submitType)) {
            //上报添加企业微信成功
            uploadSender.sendAddWorkWechatUpload(pvInfo.getPid());
            // 查询pv 更新添加成员数
            pageViewSender.sendUpdateAddEnterpriseWechatStatusSuccess(new WorkWechatUser()
                .setExternalUseridAddAt(ewacr.getExternalUseridAddAt())
                .setPid(pvInfo.getPid())
                .setExternalUserId(externalUserId)
                .setUnionId(externalUnionId)
                .setExternalName(name)
                .setWechatUserIdMatchingStatus(1)
                .setIdentifyQrCodeWcsId(ewacr.getIdentifyQrCodeWcsId())
                .setIdentifyQrCodeWcsUserId(ewacr.getIdentifyQrCodeWcsUserId())
                .setIdentifyQrCodeWcsUserName(ewacr.getIdentifyQrCodeWcsUserName())
                .setCbUserIdMatchingWcsUserName(ewacr.getCbUserIdMatchingWcsUserName())
                .setCbUserIdMatchingWcsId(ewacr.getCbUserIdMatchingWcsId())
                .setWcsMatchingErrorStatus(ewacr.getWcsMatchingErrorStatus())
                .setWcsMatchingErrorMessage(ewacr.getWcsMatchingErrorMessage()));

            //添加企业微信客服成功，异步统计落地页指标
            if (Objects.nonNull(matchingLpwcs) && Objects.nonNull(matchingLpwcs.getWechatUserId())) {
                log.info("明文添加企业微信客服成功,异步统计落地页指标,WechatUserId = [{}]", matchingLpwcs.getWechatUserId());
                pageViewInfoService.sendMessageOfEnterpriseEvent(pvInfo, IndicatorStatisticEventEnum.ADD_ENTERPRISE_WECHAT_SUCCESS, null);
            }
        }
    }

    /**
     * 匹配到曝光 - 保存客资信息 - 记录匹配信息 - 跳转微信名片页链路
     */
    public void customerAcquisitionMatchedPvSaveCustomerAndSaveCache(final String agentId, final String externalUserId, final String unionId, final String name,
                                                                     final PageViewInfo pvInfo, final EnterpriseWechatAddContactRecord ewacr, final AddSource addSource,
                                                                     final List<LandingPageWechatCustomerService> lpwcsList, EnterpriseWechat enterpriseWechat,
                                                                     FrontComponentType componentType, String userId, Boolean flag, String corpId) {
        Long submitDataId = Objects.nonNull(pvInfo.getSubmitDataId()) ? pvInfo.getSubmitDataId() : pvInfo.getParentSubmitDataId();
        MatchingWorkWechatCustomerDto matchingWorkWechatCustomerDto = null;
        if (!CollectionUtils.isEmpty(lpwcsList)) {
            //pv中的pmpid与微信客服pmpid不匹配的客服均为外部来源（例如同一个agentId下查询出5条userid相同的微信客服，最终加粉只有一个pmp下的客资才有上报状态，加粉落地页等信息，其余4条均为外部客资）
            List<LandingPageWechatCustomerService> matchPmpLpwcsList = lpwcsList.stream()
                .filter(lpwcs -> Objects.equals(pvInfo.getAdvertiserAccountGroupId(), lpwcs.getAdvertiserAccountGroupId()))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(matchPmpLpwcsList)) {
                //#20423 【线上问题】客服名称匹配错误，与后台实际添加不一致 https://ones.yiye.ai/project/#/team/WtsduTeT/task/EvybVgBAJWDg2Gpu
                LandingPageWechatCustomerService lpwcs = matchPmpLpwcsList.get(0);
                String wechatAppletGroupChatName = StringUtils.trim(lpwcs.getWechatUserName());
                ewacr.setWechatCustomerServiceId(pvInfo.getWechatCustomerServiceId())
                    .setMatchLpwcsId(lpwcs.getId())
                    .setWechatAppletGroupChatName(wechatAppletGroupChatName)
                    .setWechatUserIdMatchingStatus(1)
                    .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
                    .setWechatCustomerAcquisitionLinkId(pvInfo.getClickCustomerAcquisitonLinkId())
                    .setWechatCustomerAcquisitionLink(pvInfo.getClickCustomerAcquisitonLink())
                    .setWechatAppletAddWay(WechatAppletAddWay.ADD_THROUGH_CUSTOMER_ACQUISITION_LINK);
                matchingWorkWechatCustomerDto = saveSubmitDataAndCustomer(agentId, pvInfo, ewacr, enterpriseWechat, submitDataId, SubmitType.WECHAT_APPLET, corpId, userId, externalUserId);
            }
            //生成其他pmp客资
            List<LandingPageWechatCustomerService> unMatchPmpLpwcsList = lpwcsList.stream()
                .filter(lpwcs -> !Objects.equals(pvInfo.getAdvertiserAccountGroupId(), lpwcs.getAdvertiserAccountGroupId()))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(unMatchPmpLpwcsList) && !(Objects.nonNull(matchingWorkWechatCustomerDto) && Boolean.TRUE.equals(matchingWorkWechatCustomerDto.getUpdateWorkWechatCustomer()))) {
//            if (!CollectionUtils.isEmpty(unMatchPmpLpwcsList) && (Objects.isNull(matchingWorkWechatCustomerDto) || Boolean.FALSE.equals(matchingWorkWechatCustomerDto.getUpdateWorkWechatCustomer()))) {
                unMatchPmpLpwcsList.forEach(lpwcs -> {
                    ewacr.setWechatUserIdMatchingStatus(0)
                        .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.NOT_ADDED)
                        .setMatchLpwcsId(lpwcs.getId())
                        .setWechatAppletGroupChatName(StringUtils.trim(lpwcs.getWechatUserName()))
                        .setWechatCustomerServiceId(lpwcs.getId())
                        .setAdvertiserAccountGroupId(lpwcs.getAdvertiserAccountGroupId())
                        .setWechatAppletAddWay(WechatAppletAddWay.ADD_THROUGH_CUSTOMER_ACQUISITION_LINK);
                    saveSubmitDataAndCustomer(agentId, null, ewacr, enterpriseWechat, SubmitType.WECHAT_APPLET, corpId, userId, externalUserId);
                    //非落地页链路添加企业微信客服，增加统计记录
                    if (!Objects.equals(ewacr.getWechatUserIdMatchingStatus(), 1) && !Objects.equals(ewacr.getAddEnterpriseWechatStatus(), AddEnterpriseWechatStatus.ADDED)) {
                        this.generateWechatCustomerIndicatorStatistics(pvInfo, lpwcs);
                    }
                });
            }
            //公众号关注后通过公众号蓝链加粉成功：公众号助手自动回复-携带上级关注页pid、公众号助手点击菜单链接跳转-仅携带蓝链标识
            if (LinkType.WECHAT_OFFICIAL_ACCOUNT_BLUE_URL.equals(pvInfo.getLinkType()) ||
                StringUtils.contains(pvInfo.getUrl(), (UrlUtils.LINK_TYPE_PARAM + "=" + LinkType.WECHAT_OFFICIAL_ACCOUNT_BLUE_URL.getId()))
            ) {
                customerSender.sendUpdateWechatOfficialCustomer(new LandingPageWechatOfficialAccountCustomer()
                    //归因参数
                    .setMatchingPid(pvInfo.getParentPid())
                    .setAddEnterpriseWechatPid(pvInfo.getPid())
                    .setOpenid(StringUtils.isNotBlank(pvInfo.getParentPid()) ? null : pvInfo.getWechatOpenid())
                    //公众号粉丝客资数据
                    .setUserName(ewacr.getWechatAppletName())
                    .setUserPhoto(ewacr.getAvatar())
                    .setExternalUseridAddAt(ewacr.getExternalUseridAddAt())
                    .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
                    .setDeleteWechatTime(null)
                    .setFollowedAddEnterpriseWechat(FollowedAddEnterpriseWechat.FOLLOWED_BLUE_LINK_ADD_ENTERPRISE_WECHAT)
                    .setWechatAppletExternalUserid(ewacr.getWechatAppletExternalUserid())
                    .setWechatAppletGroupChatName(ewacr.getWechatAppletGroupChatName())
                    .setWechatAppletUserid(ewacr.getWechatAppletUserid())
                    .setWechatAppletUserPmpId(pvInfo.getAdvertiserAccountGroupId())
                    .setCorpid(enterpriseWechat.getCorpid())
                    .setEnterpriseWechatName(enterpriseWechat.getCorpName())
                    .setExternalUserSex(ewacr.getExternalUserSex())
                    .setWechatAppletAddWay(ewacr.getWechatAppletAddWay())
                    .setFollowAddWechatPmpId(pvInfo.getAdvertiserAccountGroupId())
                    .setFollowAddWechatLandingPageId(pvInfo.getLandingPageId())
                    .setFollowAddWechatChannelId(pvInfo.getChannelId())
                    .setFollowAddWechatChannelUrl(pvInfo.getUrl())
                    .setSubmitDataId(Objects.isNull(matchingWorkWechatCustomerDto) ? null : matchingWorkWechatCustomerDto.getSubmitDataId())
                    .setCustomerId(Objects.isNull(matchingWorkWechatCustomerDto) ? null : matchingWorkWechatCustomerDto.getCustomerId())
                    .setUpdatedAt(Instant.now())
                );
            }
        }
        UploadEventType addWorkWechatSuccessEvent = null;
        if (Objects.nonNull(componentType)) {
            addWorkWechatSuccessEvent = componentType.getAddWorkWechatSuccessEvent();
        }
        if (Objects.isNull(addWorkWechatSuccessEvent) && flag) {
            addWorkWechatSuccessEvent = UploadEventType.ADD_ENTERPRISE_WECHAT_SUCCESS;
        }
        //落地页组件添加微信客服事件 成功 上报
        LandingPageUploadDto landingPageUploadDto = new LandingPageUploadDto()
            .setPid(pvInfo.getPid())
            .setSubmitDataId(ObjectUtils.isEmpty(matchingWorkWechatCustomerDto) ? null : matchingWorkWechatCustomerDto.getSubmitDataId())
            .setIsAddWechat(true)
            .setUploadEventType(addWorkWechatSuccessEvent);
        uploadSender.sendLandingPageWdigetWorkWechatAddSuccess(landingPageUploadDto);
        // 查询pv 更新添加成员数
        pageViewSender.sendUpdateAddEnterpriseWechatStatusSuccess(new WorkWechatUser()
            .setExternalUseridAddAt(ewacr.getExternalUseridAddAt())
            .setPid(pvInfo.getPid())
            .setExternalUserId(externalUserId)
            .setUnionId(unionId)
            .setExternalName(name)
            .setWechatUserIdMatchingStatus(1)
            .setIdentifyQrCodeWcsId(ewacr.getIdentifyQrCodeWcsId())
            .setIdentifyQrCodeWcsUserId(ewacr.getIdentifyQrCodeWcsUserId())
            .setIdentifyQrCodeWcsUserName(ewacr.getIdentifyQrCodeWcsUserName())
            .setCbUserIdMatchingWcsId(ewacr.getCbUserIdMatchingWcsId())
            .setCbUserIdMatchingWcsUserName(ewacr.getCbUserIdMatchingWcsUserName())
            .setWcsMatchingErrorStatus(ewacr.getWcsMatchingErrorStatus())
            .setWcsMatchingErrorMessage(ewacr.getWcsMatchingErrorMessage()));
    }


    /**
     * 保存客资-非名片加粉页有中间页，因此需要传parentSubmitId
     *
     * @param agentId
     * @param pvInfo
     * @param ewacr
     * @param enterpriseWechat
     * @return
     */
    public MatchingWorkWechatCustomerDto saveSubmitDataAndCustomer(final String agentId, PageViewInfo pvInfo, EnterpriseWechatAddContactRecord ewacr,
                                                                   EnterpriseWechat enterpriseWechat, SubmitType submitType, String corpId, String userId, String externalUserId) {
        return this.saveSubmitDataAndCustomer(agentId, pvInfo, ewacr, enterpriseWechat, Objects.nonNull(pvInfo) ? pvInfo.getParentSubmitDataId() : null, submitType, corpId, userId, externalUserId);
    }

    /**
     * 保存【填单】【客资】信息 - 获客链接名片页 - 需传递当前页submitId
     */
    public MatchingWorkWechatCustomerDto saveSubmitDataAndCustomer(final String agentId, PageViewInfo pvInfo,
                                                                   EnterpriseWechatAddContactRecord ewacr, EnterpriseWechat enterpriseWechat,
                                                                   Long submitDataId, SubmitType submitType, String corpId, String userId, String externalUserId) {
        MatchingWorkWechatCustomerDto matchingWorkWechatCustomerDto = new MatchingWorkWechatCustomerDto();
        log.info("开始保存【填单】【客资】信息 agentId={}；pvInfo={}；ewacr={}；", agentId, pvInfo, ewacr);
        if (Objects.isNull(pvInfo)) {
            log.info("开始保存【填单】【客资】信息，pvInfo不存在，pvInfo={}", pvInfo);
            pvInfo = new PageViewInfo();
        }
        final String pid = pvInfo.getPid();
//        final Long parentSubmitDataId = pvInfo.getParentSubmitDataId();
        final String wechatAppletUserid = ewacr.getWechatAppletUserid();
        final Long advertiserAccountGroupId = Objects.isNull(pvInfo.getAdvertiserAccountGroupId()) ? ewacr.getAdvertiserAccountGroupId() : pvInfo.getAdvertiserAccountGroupId();
        // 停留时长和访问深度5分钟更新一次，在生成客资的时候如果还没有更新数据，则去redis拿
        if (Objects.isNull(pvInfo.getAccessDepth()) || Objects.isNull(pvInfo.getLengthOfStay())) {
            obtainDurationAndDepth(pvInfo);
        }
        CustomerToMqType customerToMqType = CustomerToMqType.ADD_WECHAT_CUSTOMER;
        //记录客资信息
        SubmitData submitData = (!Objects.isNull(submitDataId) && 0L != submitDataId) ? submitDataService.getOne(new LambdaQueryWrapper<SubmitData>()
            .ge(SubmitData::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerQueryTime()))
            .eq(SubmitData::getId, submitDataId)) : null;
        if (Objects.isNull(submitData)) {
            submitData = new SubmitData().setId(null).setLandingPageId(-1L)
                /*修复bug：#15239 【YIYE_AGENT_V1.119.0】微信广告上报客资状态不正确，且媒体来源不正确*/
                .setPid(pvInfo.getPid()).setAdvertiserAccountGroupId(advertiserAccountGroupId)
                .setWechatAppletName(ewacr.getWechatAppletName())
                .setWechatAppletExternalUserid(ewacr.getWechatAppletExternalUserid())
                .setWechatAppletOpenid(pvInfo.getWechatAppletOpenid())
                .setWechatAppletUnionid(ewacr.getWechatAppletUnionid())
                .setWechatAppletUserid(ewacr.getWechatAppletUserid())
                .setMatchLpwcsId(ewacr.getMatchLpwcsId())
                .setWechatAppletGroupChatName(ewacr.getWechatAppletGroupChatName())
                .setWechatAppletAddWay(ewacr.getWechatAppletAddWay())
                .setWechatCustomerServiceId(ewacr.getWechatCustomerServiceId())
                .setReferrer(pvInfo.getReferrer())
                .setSubmitType(submitType)
                .setPlatformSources(Platform.getPlatformSourcesByUrlOrRefer(pvInfo.getUrl(), pvInfo.getReferrer()))
                .setIsAddWechatAppleCustomer(true)
                .setIp(pvInfo.getIp()).setProvince(pvInfo.getProvince()).setCity(pvInfo.getCity())
                .setAccessDepth(pvInfo.getAccessDepth()).setLengthOfStay(pvInfo.getLengthOfStay())
                .setWcsMatchingErrorStatus(ewacr.getWcsMatchingErrorStatus())
                .setIdentifyQrCodeWcsId(ewacr.getIdentifyQrCodeWcsId())
                .setIdentifyQrCodeWcsUserId(ewacr.getIdentifyQrCodeWcsUserId())
                .setIdentifyQrCodeWcsUserName(ewacr.getIdentifyQrCodeWcsUserName())
                .setCbUserIdMatchingWcsId(ewacr.getCbUserIdMatchingWcsId())
                .setCbUserIdMatchingWcsUserName(ewacr.getCbUserIdMatchingWcsUserName())
                .setWcsMatchingErrorMessage(ewacr.getWcsMatchingErrorMessage())
                .setCreatedAt(Instant.now()).setUpdatedAt(Instant.now())
                .setWechatUserIdMatchingStatus(ewacr.getWechatUserIdMatchingStatus())
                .setAddEnterpriseWechatStatus(ewacr.getAddEnterpriseWechatStatus())
                .setFlowSource(pvInfo.getFlowSource())
                //字节系列相关
                .setDouyinAppletOpenid(pvInfo.getDouyinAppletOpenid())
                .setDouyinAppletUnionid(pvInfo.getDouyinAppletUnionid())
                .setDouyinWebAppUserOpenid(pvInfo.getDouyinWebAppUserOpenid())
                .setDouyinWebAppUserUnionid(pvInfo.getDouyinWebAppUserUnionid())
                //会话存档相关
                .setExternalUserSex(ewacr.getExternalUserSex())
                .setExternalUseridAddAt(ewacr.getExternalUseridAddAt())
                //获客助手
                .setWechatCustomerAcquisitionLinkId(ewacr.getWechatCustomerAcquisitionLinkId())
                .setWechatCustomerAcquisitionLink(ewacr.getWechatCustomerAcquisitionLink())
                //企业微信相关
                .setCorpid(enterpriseWechat.getCorpid())
                .setWorkWechatName(enterpriseWechat.getCorpName())
            ;
            log.info("【新增】填单数据 submitData={}", submitData);
            if (SubmitType.OFFICIAL_ACCOUNT_FANS.equals(submitType)) {
                submitData.setWechatOfficialAccountLandingPageId(pvInfo.getLandingPageId())
                    .setWechatOfficialAccountLandingPageChannelId(pvInfo.getChannelId())
                    .setWechatOfficialAccountLandingPageViewUrl(pvInfo.getUrl())
                    .setWechatOfficialAccountAppid(pvInfo.getThisPageWechatOfficialAccountAppId())
                    .setWechatOfficialAccountName(pvInfo.getThisPageWechatOfficialAccountName());
            } else {
                submitData.setWechatAppletLandingPageId(pvInfo.getLandingPageId())
                    .setWechatAppletLandingPageChannelId(pvInfo.getChannelId())
                    .setWechatAppletLandingPageViewUrl(pvInfo.getUrl());
            }
            submitDataService.save(submitData);
        } else {
            submitData.setId(submitData.getId())
                /*修复bug：#15239 【YIYE_AGENT_V1.119.0】微信广告上报客资状态不正确，且媒体来源不正确*/
                .setPid(StringUtils.isNotBlank(submitData.getPid()) ? submitData.getPid() : pvInfo.getPid())
                .setNextPid(pvInfo.getPid())
                .setWechatAppletName(ewacr.getWechatAppletName())
                .setWechatAppletExternalUserid(ewacr.getWechatAppletExternalUserid())
                .setWechatAppletOpenid(pvInfo.getWechatAppletOpenid())
                .setWechatAppletUnionid(ewacr.getWechatAppletUnionid())
                .setWechatAppletUserid(ewacr.getWechatAppletUserid())
                .setMatchLpwcsId(ewacr.getMatchLpwcsId())
                .setWechatAppletGroupChatName(ewacr.getWechatAppletGroupChatName())
                .setWechatAppletAddWay(ewacr.getWechatAppletAddWay())
                .setWechatCustomerServiceId(ewacr.getWechatCustomerServiceId())
                .setIsAddWechatAppleCustomer(true)
                .setWcsMatchingErrorStatus(WechatCustomerServiceMatchingErrorStatus.DATA_NORMAL)
                .setIdentifyQrCodeWcsId(ewacr.getIdentifyQrCodeWcsId())
                .setIdentifyQrCodeWcsUserId(ewacr.getIdentifyQrCodeWcsUserId())
                .setIdentifyQrCodeWcsUserName(ewacr.getIdentifyQrCodeWcsUserName())
                .setCbUserIdMatchingWcsId(ewacr.getCbUserIdMatchingWcsId())
                .setCbUserIdMatchingWcsUserName(ewacr.getCbUserIdMatchingWcsUserName())
                .setWcsMatchingErrorMessage(ewacr.getWcsMatchingErrorMessage())
                .setCreatedAt(Instant.now()).setUpdatedAt(Instant.now())
                .setWechatUserIdMatchingStatus(ewacr.getWechatUserIdMatchingStatus())
                .setAddEnterpriseWechatStatus(ewacr.getAddEnterpriseWechatStatus())
                .setFlowSource(pvInfo.getFlowSource())
                //字节系列相关
                .setDouyinAppletOpenid(pvInfo.getDouyinAppletOpenid())
                .setDouyinAppletUnionid(pvInfo.getDouyinAppletUnionid())
                .setDouyinWebAppUserOpenid(pvInfo.getDouyinWebAppUserOpenid())
                .setDouyinWebAppUserUnionid(pvInfo.getDouyinWebAppUserUnionid())
                //会话存档相关
                .setExternalUserSex(ewacr.getExternalUserSex())
                .setExternalUseridAddAt(ewacr.getExternalUseridAddAt())
                //获客助手
                .setWechatCustomerAcquisitionLinkId(ewacr.getWechatCustomerAcquisitionLinkId())
                .setWechatCustomerAcquisitionLink(ewacr.getWechatCustomerAcquisitionLink())
                //企业微信相关
                .setCorpid(enterpriseWechat.getCorpid())
                .setWorkWechatName(enterpriseWechat.getCorpName());
            if (SubmitType.OFFICIAL_ACCOUNT_FANS.equals(submitType)) {
                submitData.setWechatOfficialAccountLandingPageId(pvInfo.getLandingPageId())
                    .setWechatOfficialAccountLandingPageChannelId(pvInfo.getChannelId())
                    .setWechatOfficialAccountLandingPageViewUrl(pvInfo.getUrl())
                    .setWechatOfficialAccountAppid(pvInfo.getThisPageWechatOfficialAccountAppId())
                    .setWechatOfficialAccountName(pvInfo.getThisPageWechatOfficialAccountName());
            } else {
                submitData.setWechatAppletLandingPageId(pvInfo.getLandingPageId())
                    .setWechatAppletLandingPageChannelId(pvInfo.getChannelId())
                    .setWechatAppletLandingPageViewUrl(pvInfo.getUrl());
            }
            log.info("【修改】填单数据 submitData={}", submitData);
            submitDataService.updateById(submitData);
            customerToMqType = CustomerToMqType.CHANGE_FORM_STRAND_ADD_WECHAT_CUSTOMER;
        }
        //记录客资信息
        Customer customer = (!Objects.isNull(submitData) && !Objects.isNull(submitData.getId()) && 0L != submitData.getId()) ? customerService.getOne(new LambdaQueryWrapper<Customer>()
            .ge(Customer::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerQueryTime()))
            .eq(Customer::getSubmitDataId, submitData.getId())
            .last(" limit 1")) : null;
        if (Objects.isNull(customer)) {
            AdvertiserAccountGroup advertiserAccountGroup = advertiserAccountGroupRemote.fetchById(advertiserAccountGroupId);
            customer = new Customer().setId(null).setLandingPageId(-1L)
                /*修复bug：#15239 【YIYE_AGENT_V1.119.0】微信广告上报客资状态不正确，且媒体来源不正确*/
                .setPid(pvInfo.getPid()).setSubmitDataId(submitData.getId())
                .setAdvertiserAccountGroupId(advertiserAccountGroupId)
                .setWechatAppletName(ewacr.getWechatAppletName())
                .setWechatAppletExternalUserid(ewacr.getWechatAppletExternalUserid())
                .setWechatAppletOpenid(pvInfo.getWechatAppletOpenid())
                .setWechatAppletUnionid(ewacr.getWechatAppletUnionid())
                .setWechatAppletUserid(ewacr.getWechatAppletUserid())
                .setMatchLpwcsId(ewacr.getMatchLpwcsId())
                .setWechatAppletGroupChatName(ewacr.getWechatAppletGroupChatName())
                .setWechatAppletAddWay(ewacr.getWechatAppletAddWay())
                .setWechatCustomerServiceId(ewacr.getWechatCustomerServiceId())
                .setReferrer(pvInfo.getReferrer())
                .setCustomerType(submitType)
                .setPlatformSources(Platform.getPlatformSourcesByUrlOrRefer(pvInfo.getUrl(), pvInfo.getReferrer()))
                .setIsAddWechatAppleCustomer(true)
                .setIp(pvInfo.getIp()).setProvince(pvInfo.getProvince()).setCity(pvInfo.getCity())
                .setAccessDepth(pvInfo.getAccessDepth()).setLengthOfStay(pvInfo.getLengthOfStay())
                .setWcsMatchingErrorStatus(WechatCustomerServiceMatchingErrorStatus.DATA_NORMAL)
                .setIdentifyQrCodeWcsId(ewacr.getIdentifyQrCodeWcsId())
                .setIdentifyQrCodeWcsUserId(ewacr.getIdentifyQrCodeWcsUserId())
                .setIdentifyQrCodeWcsUserName(ewacr.getIdentifyQrCodeWcsUserName())
                .setCbUserIdMatchingWcsId(ewacr.getCbUserIdMatchingWcsId())
                .setCbUserIdMatchingWcsUserName(ewacr.getCbUserIdMatchingWcsUserName())
                .setWcsMatchingErrorMessage(ewacr.getWcsMatchingErrorMessage())
                .setCreatedAt(Instant.now()).setUpdatedAt(Instant.now())
                .setWechatUserIdMatchingStatus(ewacr.getWechatUserIdMatchingStatus())
                .setAddEnterpriseWechatStatus(ewacr.getAddEnterpriseWechatStatus())
                .setUnread(1)
                .setFlowSource(pvInfo.getFlowSource())
                //字节系列相关
                .setDouyinAppletOpenid(pvInfo.getDouyinAppletOpenid())
                .setDouyinAppletUnionid(pvInfo.getDouyinAppletUnionid())
                .setDouyinWebAppUserOpenid(pvInfo.getDouyinWebAppUserOpenid())
                .setDouyinWebAppUserUnionid(pvInfo.getDouyinWebAppUserUnionid())
                //会话存档相关
                .setExternalUserSex(ewacr.getExternalUserSex())
                .setExternalUseridAddAt(ewacr.getExternalUseridAddAt())
                //获客助手
                .setWechatCustomerAcquisitionLinkId(ewacr.getWechatCustomerAcquisitionLinkId())
                .setWechatCustomerAcquisitionLink(ewacr.getWechatCustomerAcquisitionLink())
                //企业推
                .setQiyePendingId(pvInfo.getQiyePendingId())
                .setQiyePersonnelId(pvInfo.getQiyePersonnelId())
                //企业推自归因参数
                .setWechatCustomerContactState(pvInfo.getLandingPageWechatCustomerContactState())
                //企业微信相关
                .setCorpid(enterpriseWechat.getCorpid())
                .setWorkWechatName(enterpriseWechat.getCorpName())
                //头像
                .setExternalUserAvatar(ewacr.getAvatar());
            if (SubmitType.OFFICIAL_ACCOUNT_FANS.equals(submitType)) {
                customer.setWechatOfficialAccountLandingPageId(pvInfo.getLandingPageId())
                    .setWechatOfficialAccountLandingPageChannelId(pvInfo.getChannelId())
                    .setWechatOfficialAccountLandingPageViewUrl(pvInfo.getUrl())
                    .setWechatOfficialAccountAppid(pvInfo.getThisPageWechatOfficialAccountAppId())
                    .setWechatOfficialAccountName(pvInfo.getThisPageWechatOfficialAccountName());
                //异步记录单人活码加粉记录（新版本查询）
                this.synRecordOfficialAccountFans(customer, pvInfo, ewacr);
            } else if (SubmitType.OFFICIAL_ACCOUNT_CUSTOMER_GROUP_CODE_FANS.equals(submitType)) {
                //通过多人活码生成的客资，多记录一下客服对应的分组ID和pv中记录的公众号appid
                if (Objects.nonNull(pvInfo)) {
                    log.info("生成客资,通过多人活码生成的客资，客服对应的分组ID和pv中记录的公众号appid,pid= {},appid = {}, 分组ID = {},客服ID = {}, pmpid ={}", pvInfo.getPid(), pvInfo.getFollowOfficialAccountAppId(),
                        pvInfo.getWechatCustomerServiceGroupId(), customer.getWechatCustomerServiceId(), customer.getAdvertiserAccountGroupId());
                    customer.setWechatOfficialAccountAppid(pvInfo.getFollowOfficialAccountAppId());
                    customer.setWechatCustomerServiceGroupId(pvInfo.getWechatCustomerServiceGroupId());
                }
            } else {
                customer.setWechatAppletLandingPageId(pvInfo.getLandingPageId())
                    .setWechatAppletLandingPageChannelId(pvInfo.getChannelId())
                    .setWechatAppletLandingPageViewUrl(pvInfo.getUrl());
            }
            if (ReplaceOperationType.OPERATION.equals(advertiserAccountGroup.getReplaceOperation())) {
                customer.setOperationType(OperationType.OPERATION);
            }
            customerService.setCustomerAdParam(customer, true);
            CustomerAdParamUtil.setAdParam(customer, pvInfo.getUrl(), pvInfo.getReferrer());
            log.info("【新增】客资数据 customer={}", customer);
            customerService.save(customer);
        } else {
            //#33966 【YIYE_ANGENT_V1.223.0】客资，表单/订单设置为限填一次后，同一微信通过表单/订单再次添加微信客服，未产生对应的新客资（现状只是更新了首次产生的客资） https://ones.yiye.ai/project/#/team/WtsduTeT/task/U698caAzdTwgsXrm
            matchingWorkWechatCustomerDto.setUpdateWorkWechatCustomer(
                (!Objects.isNull(customer.getIsAddWechatAppleCustomer()) && customer.getIsAddWechatAppleCustomer()) || (!Objects.isNull(customer.getIsAddWechatGroupChatCustomer()) && customer.getIsAddWechatGroupChatCustomer())
            );
            AdvertiserAccountGroup advertiserAccountGroup = advertiserAccountGroupRemote.fetchById(advertiserAccountGroupId);
            customer.setId(customer.getId()).setSubmitDataId(submitData.getId())
                /*修复bug：#15239 【YIYE_AGENT_V1.119.0】微信广告上报客资状态不正确，且媒体来源不正确*/
                .setPid(StringUtils.isNotBlank(customer.getPid()) ? customer.getPid() : pvInfo.getPid())
                .setNextPid(pvInfo.getPid())
                .setWechatAppletName(ewacr.getWechatAppletName())
                .setWechatAppletExternalUserid(ewacr.getWechatAppletExternalUserid())
                .setWechatAppletOpenid(pvInfo.getWechatAppletOpenid())
                .setWechatAppletUnionid(ewacr.getWechatAppletUnionid())
                .setWechatAppletUserid(ewacr.getWechatAppletUserid())
                .setMatchLpwcsId(ewacr.getMatchLpwcsId())
                .setWechatAppletGroupChatName(ewacr.getWechatAppletGroupChatName())
                .setWechatAppletAddWay(ewacr.getWechatAppletAddWay())
                .setWechatCustomerServiceId(ewacr.getWechatCustomerServiceId())
                .setIsAddWechatAppleCustomer(true)
                .setWcsMatchingErrorStatus(ewacr.getWcsMatchingErrorStatus())
                .setIdentifyQrCodeWcsId(ewacr.getIdentifyQrCodeWcsId())
                .setIdentifyQrCodeWcsUserId(ewacr.getIdentifyQrCodeWcsUserId())
                .setIdentifyQrCodeWcsUserName(ewacr.getIdentifyQrCodeWcsUserName())
                .setCbUserIdMatchingWcsId(ewacr.getCbUserIdMatchingWcsId())
                .setCbUserIdMatchingWcsUserName(ewacr.getCbUserIdMatchingWcsUserName())
                .setWcsMatchingErrorMessage(ewacr.getWcsMatchingErrorMessage())
                .setCreatedAt(Instant.now()).setUpdatedAt(Instant.now())
                .setWechatUserIdMatchingStatus(ewacr.getWechatUserIdMatchingStatus())
                .setAddEnterpriseWechatStatus(ewacr.getAddEnterpriseWechatStatus())
                .setUnread(1)
                .setFlowSource(pvInfo.getFlowSource())
                //字节系列相关
                .setDouyinAppletOpenid(pvInfo.getDouyinAppletOpenid())
                .setDouyinAppletUnionid(pvInfo.getDouyinAppletUnionid())
                .setDouyinWebAppUserOpenid(pvInfo.getDouyinWebAppUserOpenid())
                .setDouyinWebAppUserUnionid(pvInfo.getDouyinWebAppUserUnionid())
                //会话存档相关
                .setExternalUserSex(ewacr.getExternalUserSex())
                .setExternalUseridAddAt(ewacr.getExternalUseridAddAt())
                //获客助手
                .setWechatCustomerAcquisitionLinkId(ewacr.getWechatCustomerAcquisitionLinkId())
                .setWechatCustomerAcquisitionLink(ewacr.getWechatCustomerAcquisitionLink())
                //企业推
                .setQiyePendingId(pvInfo.getQiyePendingId())
                .setQiyePersonnelId(pvInfo.getQiyePersonnelId())
                //企业推自归因参数
                .setWechatCustomerContactState(pvInfo.getLandingPageWechatCustomerContactState())
                //企业微信相关
                .setCorpid(enterpriseWechat.getCorpid())
                .setWorkWechatName(enterpriseWechat.getCorpName())
                //头像
                .setExternalUserAvatar(ewacr.getAvatar());
            if (SubmitType.OFFICIAL_ACCOUNT_FANS.equals(submitType)) {
                customer.setWechatOfficialAccountLandingPageId(pvInfo.getLandingPageId())
                    .setWechatOfficialAccountLandingPageChannelId(pvInfo.getChannelId())
                    .setWechatOfficialAccountLandingPageViewUrl(pvInfo.getUrl())
                    .setWechatOfficialAccountAppid(pvInfo.getThisPageWechatOfficialAccountAppId())
                    .setWechatOfficialAccountName(pvInfo.getThisPageWechatOfficialAccountName());
                //异步记录单人活码加粉记录（新版本查询）
                this.synRecordOfficialAccountFans(customer, pvInfo, ewacr);
            } else if (SubmitType.OFFICIAL_ACCOUNT_CUSTOMER_GROUP_CODE_FANS.equals(submitType)) {
                //通过多人活码生成的客资，多记录一下客服对应的分组ID和pv中记录的公众号appid
                if (Objects.nonNull(pvInfo)) {
                    log.info("更新客资,通过多人活码生成的客资，客服对应的分组ID和pv中记录的公众号appid,pid = {}, appid = {}, 分组ID = {},客服ID = {}, pmpid ={}", pvInfo.getPid(), pvInfo.getFollowOfficialAccountAppId(),
                        pvInfo.getWechatCustomerServiceGroupId(), customer.getWechatCustomerServiceId(), customer.getAdvertiserAccountGroupId());
                    customer.setWechatOfficialAccountAppid(pvInfo.getFollowOfficialAccountAppId());
                    customer.setWechatCustomerServiceGroupId(pvInfo.getWechatCustomerServiceGroupId());
                }
            } else {
                customer.setWechatAppletLandingPageId(pvInfo.getLandingPageId())
                    .setWechatAppletLandingPageChannelId(pvInfo.getChannelId())
                    .setWechatAppletLandingPageViewUrl(pvInfo.getUrl());
            }
            if (ReplaceOperationType.OPERATION.equals(advertiserAccountGroup.getReplaceOperation())) {
                customer.setOperationType(OperationType.OPERATION);
            }
            if (Arrays.asList(SubmitType.FORM, SubmitType.ORDER, SubmitType.TAO_BAO_ORDER).contains(customer.getCustomerType()) && StringUtils.isNotBlank(customer.getPhone())) {
                customerSender.sendFillWecomRemarkMobiles(corpId, userId, externalUserId, customer.getLandingPageId(), customer.getPhone());
            }
            CustomerAdParamUtil.setAdParam(customer, pvInfo.getUrl(), pvInfo.getReferrer());
            log.info("【保存】客资数据 customer={}", customer);
            customerService.updateById(customer);
            customerToMqType = CustomerToMqType.CHANGE_FORM_STRAND_ADD_WECHAT_CUSTOMER;
        }
        if (!ObjectUtils.isEmpty(pvInfo.getTelephoneFromQA())) {
            customerSender.sendFillWecomRemarkMobiles(corpId, userId, externalUserId, pvInfo.getLandingPageId(), pvInfo.getTelephoneFromQA()[0]);
        }
        customerSender.sendFillWecomDescription(corpId, userId, externalUserId, pid);
        customerSender.sendFillWecomRemark(corpId, userId, externalUserId, pid, ewacr.getWechatAppletName());
        if (EnterpriseWechatType.GENERATION_DEVELOPMENT.equals(enterpriseWechat.getEnterpriseWechatType())) {
            //提送队列查询匹配代企业推客资 以及 执行会话存档策略
            customerSender.sendRetryDevelopmentCustomer(new QiyetuiCustomerDto()
                .setAgentId(agentId)
                .setId(customer.getId())
                .setCorpId(enterpriseWechat.getCorpid())
                .setWechatAppletGroupChatName(customer.getWechatAppletGroupChatName())
//                    .setReferrer(customer.getReferrer())
                .setWechatAppletName(customer.getWechatAppletName())
                .setExternalUseridAddAt(customer.getExternalUseridAddAt())
                .setWechatAppletUserid(customer.getWechatAppletUserid())
                .setWechatAppletExternalUserid(customer.getWechatAppletExternalUserid())
                .setExternalUserSex(customer.getExternalUserSex())
                .setSubmitDataId(submitData.getId()));
        }
        try {
            if (StringUtils.isNotBlank(wechatAppletUserid)) {
                landingPageSender.sendAddWorkWechatAutoRule(new AddWorkWechatAutoRuleDTO().setAdvertiserAccountGroupId(advertiserAccountGroupId)
                    .setWechatUserId(wechatAppletUserid));
            }
        } catch (Exception e) {
            log.error("成功添加企业微信数自动下线策略出现异常", e);
        }
        try {
            //wehook推送消息填单消息
            submitDataSender.sendSubmitDataIdToWebhook(new CustomerSendMqDto().setSubmitDataId(submitData.getId())
                .setCustomerToMqType(customerToMqType));
        } catch (Exception e) {
            log.error("wehook推送消息填单消息出现异常", e);
        }
        return matchingWorkWechatCustomerDto.setPid(pid).setSubmitDataId(submitData.getId())
            .setCustomerId(customer.getId());
    }

    /**
     * 查询客服分组关联信息
     *
     * @param wechatCustomerServiceId  客服记录ID
     * @param advertiserAccountGroupId 项目ID
     * @param appid                    微信公众号appid
     * @return 客服分组关联信息
     */
    public Long queryGroupId(Long wechatCustomerServiceId, Long advertiserAccountGroupId, String appid) {
        try {
            log.info("查询客服分组,wechatCustomerServiceId={},advertiserAccountGroupId={}, appid ={} ", wechatCustomerServiceId, advertiserAccountGroupId, appid);
            List<Long> wechatCustomerServiceGroupIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIdAndPmpId(wechatCustomerServiceId, advertiserAccountGroupId);
            log.info("查询客服分组集合大小 = {}", wechatCustomerServiceGroupIds);
            if (!wechatCustomerServiceGroupIds.isEmpty()) {
                for (Long groupId : wechatCustomerServiceGroupIds) {
                    //查询关联表的记录,有appid的记录只会有一条
                    OfficialWechatCustomerServiceGroup serviceGroup = officialWechatCustomerServiceGroupService.lambdaQuery()
                        .eq(OfficialWechatCustomerServiceGroup::getAdvertiserAccountGroupId, advertiserAccountGroupId)
                        .eq(OfficialWechatCustomerServiceGroup::getWechatCustomerServiceGroupId, groupId)
                        .eq(StringUtils.isNotBlank(appid), OfficialWechatCustomerServiceGroup::getAppId, appid)
                        .eq(OfficialWechatCustomerServiceGroup::getDeleteStatus, DeleteStatus.NORMAL)
                        .last(" limit 1").one();
                    if (Objects.nonNull(serviceGroup)) {
                        return groupId;
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询客服分组出现异常,wechatCustomerServiceId={},advertiserAccountGroupId={}", wechatCustomerServiceId, advertiserAccountGroupId);
        }
        return null;
    }

    private void obtainDurationAndDepth(PageViewInfo pageViewInfo) {
        if (StringUtils.isBlank(pageViewInfo.getPid())) {
            return;
        }
        String monitorPidKey = TraceUtil.KEY_PREFIX_MONITOR + pageViewInfo.getPid();
        TracePageVO vo = (TracePageVO) objectRedisTemplate.opsForValue().get(monitorPidKey);
        if (vo == null) {
            return;
        }
        pageViewInfo.setLengthOfStay(vo.getLengthOfStay()).setAccessDepth(vo.getDepth()).setUpdatedAt(Instant.now());
    }

    /**
     * 加粉成功之后，删除机器人动态渠道二维码
     * @param state 自归因参数
     * @param userId 客服的userId
     */
    public void deleteRobotLIveQrCode(String state, String userId, String corpId, EnterpriseWechat enterpriseWechat){
        robotDynamicCustomerContactGenerateRecordService.deleteRobotLIveQrCode(state, userId, corpId, enterpriseWechat);
    }


    //匹配state归因
    private boolean matchStateResult(final String agentId, final String externalUnionId, final String externalUserId,
                                     final String name, final String corpId, final String corpName, final String userId,
                                     final EnterpriseWechatAddContactRecord ewacr, EnterpriseWechat enterpriseWechat,
                                     String state, List<LandingPageWechatCustomerService> lpwcsList,
                                     Sex externalUserSex) {

        //获客助手逻辑
        if (state.contains(CustomerCquisitionPrefixEnum.IS_USE.getPrefix())) {
            String key = RedisConstant.LANDING_PAGE_WECHAT_CUSTOMER_ACQUISITION_STATE_CONFIG + state;
            //pid
            Object o = objectRedisTemplate.opsForValue().get(key);
            if (Objects.nonNull(o)) {
                CustomerAcquisitionStateDto customerAcquisitionStateDto = (CustomerAcquisitionStateDto) o;
                String pid = customerAcquisitionStateDto.getPid();
                FrontComponentType componentType = customerAcquisitionStateDto.getComponentType();
                PageViewInfo pageViewInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                    .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoQueryTime()))
                    .eq(PageViewInfo::getPid, pid));

                //判断是否是非广告来源加粉客资，进行打标签
                sendMessageMakeNonAdSourceEnterpriseWechatTag(state, new MakeEnterpriseWechatTagDto().setCorpId(corpId).setUserId(userId).setExternalUserUserid(externalUserId), pageViewInfo, MakeNonAdSourceCustomerTagEventType.CUSTOMER_ACQUISITION_LINK, externalUserSex);

                if (Objects.nonNull(pageViewInfo)) {
                    if (AddEnterpriseWechatStatus.ADDED.equals(pageViewInfo.getAddEnterpriseWechatStatus())) {
                        log.info("===>微信名片页跳转加粉匹配失败,对应自归因参数pv为已加粉,为单删再次添加客资,state:{},externalUserId:{}", state, externalUserId);
                        return false;
                    }
                    log.info("====>微信名片页跳转pv匹配成功 agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；",
                        agentId, externalUnionId, externalUserId, name, corpId, userId, JSONObject.toJSONString(pageViewInfo));
                    customerAcquisitionMatchedPvSaveCustomerAndSaveCache(agentId, externalUserId, externalUnionId, name, pageViewInfo,
                        ewacr, AddSource.LANDING_PAGE_LINK, lpwcsList, enterpriseWechat,
                        componentType, userId, true, corpId);
                    //客服分组自动打标签sender
                    Optional.ofNullable(pageViewInfo.getLandingPageId()).ifPresent(e -> {
                        MakeEnterpriseWechatTagDto makeEnterpriseWechatTagDto = new MakeEnterpriseWechatTagDto();
                        makeEnterpriseWechatTagDto.setLangingPageId(e)
                            .setUserId(userId)
                            .setExternalUserUserid(externalUserId)
                            .setCorpId(corpId);
                        enterpriseWechatTagStrategySender.sendMakeEnterpriseWechatTag(makeEnterpriseWechatTagDto);
                    });
                    //校验是否有问答或弹窗模板自动打标签配置
                    Optional.ofNullable(pageViewInfo.getClickWidgetTemplatesId()).ifPresent(item -> {
                        MakeEnterpriseWechatTagDto makeEnterpriseWechatTagDto = new MakeEnterpriseWechatTagDto();
                        makeEnterpriseWechatTagDto.setClickWidgetTemplatesId(item)
                            .setUserId(userId)
                            .setExternalUserUserid(externalUserId)
                            .setCorpId(corpId);
                        enterpriseWechatTagStrategySender.sendWidgetTemplateMakeEnterpriseWechatTag(makeEnterpriseWechatTagDto);
                    });
                    if (LinkType.WECHAT_CUSTOMER_ROBOT_SERVICE.equals(pageViewInfo.getLinkType())) {
                        enterpriseWechatRobotCustomerService.statisticWechatCustomerServiceRobotData(
                            pageViewInfo.getOriginRobotId(),
                            EnterpriseWechatRobotCustomerDataStatisticsField.ADD_ENTERPRISE_WECHAT_COUNT,
                            null
                        );
                    }
                    return true;
                }
            }
        }

        //微信客服机器人获客助手逻辑
        if (state.contains(RobotCustomerAcquisitionPrefixEnum.IS_USE_FOR_ROBOT.getPrefix())) {
            //修改机器人发送时 访客信息缓存记录的加粉状态
            enterpriseRobotCustomerSender.sendRobotMsgAddOnce(state);
            RobotCustomerAcquisitionLinkRecord robotCustomerAcquisitionLinkRecord = robotCustomerAcquisitionLinkRecordService.queryByExternalUserIdAndCustomerUserId(externalUserId, userId, state, corpId);
            String pid = Objects.nonNull(robotCustomerAcquisitionLinkRecord) ? robotCustomerAcquisitionLinkRecord.getPid() : null;
            log.info("明文代开发微信客服机器人获客助手逻辑,查询消息记录表,pid = {}", pid);
            if (StringUtils.isNotBlank(pid)) {
                PageViewInfo pageViewInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                    .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoQueryTime()))
                    .eq(PageViewInfo::getPid, pid));

                //判断是否是非广告来源加粉客资，进行打标签
                sendMessageMakeNonAdSourceEnterpriseWechatTag(state, new MakeEnterpriseWechatTagDto().setCorpId(corpId).setUserId(userId).setExternalUserUserid(externalUserId), pageViewInfo, MakeNonAdSourceCustomerTagEventType.CUSTOMER_ACQUISITION_LINK, externalUserSex);

                if (Objects.nonNull(pageViewInfo)) {
                    if (AddEnterpriseWechatStatus.ADDED.equals(pageViewInfo.getAddEnterpriseWechatStatus())) {
                        log.info("===>微信客服机器人获客助手匹配失败,对应自归因参数pv为已加粉,为单删再次添加客资,state:{},externalUserId:{}", state, externalUserId);
                        return false;
                    }
                    log.info("====>明文代开发微信客服机器人获客助手逻辑pv匹配成功 agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；",
                        agentId, externalUnionId, externalUserId, name, corpId, userId, JSONObject.toJSONString(pageViewInfo));
                    customerAcquisitionMatchedPvSaveCustomerAndSaveCache(agentId, externalUserId, externalUnionId, name, pageViewInfo,
                        ewacr, AddSource.LANDING_PAGE_LINK, lpwcsList, enterpriseWechat,
                        null, userId, true, corpId);
                    //校验机器人获客助手是否配置了自动打标签
                    Optional.ofNullable(robotCustomerAcquisitionLinkRecord.getMsgChildrenTemplateId()).ifPresent(e -> {
                        MakeEnterpriseWechatTagDto makeEnterpriseWechatTagDto = new MakeEnterpriseWechatTagDto();
                        makeEnterpriseWechatTagDto.setMsgChildrenTemplateId(e)
                            .setUserId(userId)
                            .setExternalUserUserid(externalUserId)
                            .setCorpId(corpId);
                        enterpriseWechatTagStrategySender.sendRobotContactMakeEnterpriseWechatTag(makeEnterpriseWechatTagDto);
                    });
                    enterpriseWechatRobotCustomerService.statisticWechatCustomerServiceRobotData(
                        robotCustomerAcquisitionLinkRecord.getWechatCustomerServiceId(),
                        EnterpriseWechatRobotCustomerDataStatisticsField.ADD_ENTERPRISE_WECHAT_COUNT,
                        null
                    );
                    return true;
                }
            } else {
                //判断是否是非广告来源加粉客资，进行打标签
                log.info("微信客服机器人获客助手逻辑,进行检查是否是非广告来源，pid为空,corpId={},userId={},externalUserId={}", corpId, userId, externalUserId);
                sendMessageMakeNonAdSourceEnterpriseWechatTag(state, new MakeEnterpriseWechatTagDto().setCorpId(corpId).setUserId(userId).setExternalUserUserid(externalUserId), null, MakeNonAdSourceCustomerTagEventType.CUSTOMER_ACQUISITION_LINK, externalUserSex);
            }
        }

        //机器人活码
        if (state.contains(String.format(RobotContactWayPrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))) {
            final Long TIMES = workWechatDevelopConf.getMatchingDataTime();
            final Instant todayNow = Instant.now();
            final Instant beforeNow = todayNow.minusMillis(TIME_UNIT.toMillis(TIMES));
            RobotCustomerLiveCodeRecord robotCustomerLiveCodeRecord = robotCustomerLiveCodeRecordService.getPid(externalUserId, userId, beforeNow);
            String pid = Objects.nonNull(robotCustomerLiveCodeRecord) ? robotCustomerLiveCodeRecord.getPid() : null;
            log.info("机器人活码归因匹配,查询消息记录表,pid = {}", pid);
            if (StringUtils.isNotBlank(pid)) {
                PageViewInfo pageViewInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                    .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoQueryTime()))
                    .eq(PageViewInfo::getPid, pid).last(" limit 1"));
                if (Objects.nonNull(pageViewInfo)) {
                    if (AddEnterpriseWechatStatus.ADDED.equals(pageViewInfo.getAddEnterpriseWechatStatus())) {
                        log.info("===>机器人活码归因匹配失败,对应自归因参数pv为已加粉,为单删再次添加客资,state:{},externalUserId:{}", state, externalUserId);
                        return false;
                    }
                    log.info("====>机器人活码归因匹配pv成功 agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；",
                        agentId, externalUnionId, externalUserId, name, corpId, userId, JSONObject.toJSONString(pageViewInfo));
                    matchedPvSaveCustomerAndSaveCache(agentId, externalUserId, externalUnionId, name, pageViewInfo, ewacr, AddSource.LANDING_PAGE_LINK, lpwcsList, enterpriseWechat, userId, todayNow, beforeNow, SubmitType.WECHAT_APPLET, corpId);
                    //校验机器人是否配置了自动打标签
                    Optional.ofNullable(robotCustomerLiveCodeRecord.getMsgChildrenTemplateId()).ifPresent(e -> {
                        MakeEnterpriseWechatTagDto makeEnterpriseWechatTagDto = new MakeEnterpriseWechatTagDto();
                        makeEnterpriseWechatTagDto.setMsgChildrenTemplateId(e)
                            .setUserId(userId)
                            .setExternalUserUserid(externalUserId)
                            .setCorpId(corpId);
                        enterpriseWechatTagStrategySender.sendRobotContactMakeEnterpriseWechatTag(makeEnterpriseWechatTagDto);
                    });
                    enterpriseWechatRobotCustomerService.statisticWechatCustomerServiceRobotData(
                        robotCustomerLiveCodeRecord.getWechatCustomerServiceId(),
                        EnterpriseWechatRobotCustomerDataStatisticsField.ADD_ENTERPRISE_WECHAT_COUNT,
                        null
                    );
                    return true;
                }
            }
            if (!CollectionUtils.isEmpty(lpwcsList)) {
                for (LandingPageWechatCustomerService lpwcs : lpwcsList) {
                    saveOtherSubmitDataAndCustomer(agentId, externalUnionId, externalUserId, name, corpId, userId, ewacr, enterpriseWechat, lpwcsList, todayNow, beforeNow, null, lpwcs, SubmitType.WECHAT_APPLET);
                }
                return true;
            }
        }

        //机器人异主体活码
        if (state.contains(String.format(RobotContactWayPrefixEnum.DYNAMIC_QR_CODE_IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))) {
            final Long TIMES = workWechatDevelopConf.getMatchingDataTime();
            final Instant todayNow = Instant.now();
            final Instant beforeNow = todayNow.minusMillis(TIME_UNIT.toMillis(TIMES));
            RobotCustomerMultipleLiveCodeRecord robotCustomerLiveCodeRecord = robotCustomerMultipleLiveCodeRecordService.getRecord(externalUserId, userId, state, corpId);
            String pid = Objects.nonNull(robotCustomerLiveCodeRecord) ? robotCustomerLiveCodeRecord.getPid() : null;
            log.info("机器人异主体活码归因匹配,查询消息记录表,pid = {}", pid);
            if (StringUtils.isNotBlank(pid)) {
                PageViewInfo pageViewInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                    .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoQueryTime()))
                    .eq(PageViewInfo::getPid, pid).last(" limit 1"));
                if (Objects.nonNull(pageViewInfo)) {
                    if (AddEnterpriseWechatStatus.ADDED.equals(pageViewInfo.getAddEnterpriseWechatStatus())) {
                        log.info("===>机器人异主体活码归因匹配失败,对应自归因参数pv为已加粉,为单删再次添加客资,state:{},externalUserId:{}", state, externalUserId);
                        return false;
                    }
                    log.info("====>机器人异主体活码归因匹配pv成功 agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；",
                        agentId, externalUnionId, externalUserId, name, corpId, userId, JSONObject.toJSONString(pageViewInfo));
                    matchedPvSaveCustomerAndSaveCache(agentId, externalUserId, externalUnionId, name, pageViewInfo, ewacr, AddSource.LANDING_PAGE_LINK, lpwcsList, enterpriseWechat, userId, todayNow, beforeNow, SubmitType.WECHAT_APPLET, corpId);
                    //校验机器人异主体活码是否配置了自动打标签
                    Optional.ofNullable(robotCustomerLiveCodeRecord.getMsgChildrenTemplateId()).ifPresent(e -> {
                        MakeEnterpriseWechatTagDto makeEnterpriseWechatTagDto = new MakeEnterpriseWechatTagDto();
                        makeEnterpriseWechatTagDto.setMsgChildrenTemplateId(e)
                            .setUserId(userId)
                            .setExternalUserUserid(externalUserId)
                            .setCorpId(corpId);
                        enterpriseWechatTagStrategySender.sendRobotContactMakeEnterpriseWechatTag(makeEnterpriseWechatTagDto);
                    });
                    enterpriseWechatRobotCustomerService.statisticWechatCustomerServiceRobotData(
                        robotCustomerLiveCodeRecord.getWechatCustomerServiceId(),
                        EnterpriseWechatRobotCustomerDataStatisticsField.ADD_ENTERPRISE_WECHAT_COUNT,
                        null
                    );
                    //加粉成功之后，删除活码
                    deleteRobotLIveQrCode(state, userId, corpId, enterpriseWechat);
                    //判断是否是非广告来源加粉客资，进行打标签
                    sendMessageMakeNonAdSourceEnterpriseWechatTag(state, new MakeEnterpriseWechatTagDto().setUserId(userId).setCorpId(corpId).setExternalUserUserid(externalUserId).setExternalUnionId(externalUnionId), pageViewInfo, MakeNonAdSourceCustomerTagEventType.LANDING_PAGE_ADD, externalUserSex);
                    return true;
                }
            }
            if (!CollectionUtils.isEmpty(lpwcsList)) {
                for (LandingPageWechatCustomerService lpwcs : lpwcsList) {
                    saveOtherSubmitDataAndCustomer(agentId, externalUnionId, externalUserId, name, corpId, userId, ewacr, enterpriseWechat, lpwcsList, todayNow, beforeNow, null, lpwcs, SubmitType.WECHAT_APPLET);
                }
                //加粉成功之后，删除活码
                deleteRobotLIveQrCode(state, userId, corpId, enterpriseWechat);
                return true;
            }
        }

        //页面内加粉自归因参数逻辑
        String isUse = String.format(ContactWayPrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv());
        if (state.contains(isUse)) {
            //渠道二维码异步检测延迟删除
            sendDeleteAddCallbackCustomerContact(enterpriseWechat, state);
            final Long TIMES = workWechatDevelopConf.getSelfAttributionMatchingDateTime();
            final Instant todayNow = Instant.now();
            final Instant beforeNow = todayNow.minusMillis(TIME_UNIT.toMillis(TIMES));
            PageViewInfo pageViewInfo = getPvByQiyeState(state, beforeNow);

            //判断是否是非广告来源加粉客资，进行打标签
            sendMessageMakeNonAdSourceEnterpriseWechatTag(state, new MakeEnterpriseWechatTagDto().setUserId(userId).setCorpId(corpId).setExternalUserUserid(externalUserId), pageViewInfo, MakeNonAdSourceCustomerTagEventType.LANDING_PAGE_ADD, externalUserSex);

            if (Objects.nonNull(pageViewInfo)) {
                if (AddEnterpriseWechatStatus.ADDED.equals(pageViewInfo.getAddEnterpriseWechatStatus())) {
                    log.info("===>页面内加粉归因匹配失败,对应自归因参数pv为已加粉,为单删再次添加客资,state:{},externalUserId:{}", state, externalUserId);
                    return false;
                }
                String parentPid = pageViewInfo.getParentPid();
                if (StringUtils.isNotBlank(parentPid)) {
                    PageViewInfo parentPv = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                        .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoQueryTime()))
                        .eq(PageViewInfo::getPid, parentPid));
                    if (Objects.nonNull(parentPv)) {
                        //需要兼容填单，可能存在填单跳中间页，或者填单直跳企业推
                        //如果企业推回调的pid对应的是表单页面，那么，企业推当前pv的parentSubmitDataId即为表单/订单页面的submitDataId
                        if (Arrays.asList(SubmitType.FORM, SubmitType.ORDER)
                            .contains(parentPv.getWidgetTemplateType())) {
                            pageViewInfo.setParentSubmitDataId(parentPv.getSubmitDataId());
                            parentPv.setParentSubmitDataId(parentPv.getSubmitDataId());
                        }
                        /**
                         * 同步修改page_view_info.parent_submit_data_id字段，用于合并上报记录
                         * one      为企业推加分页面上一个页面的pv信息
                         * itemPv   为企业推加粉页面pv信息
                         * one.getParentSubmitDataId() 为表单/订单串联企业推加粉常规链路：投放页面 → 表单/订单 → 中间页 → 企业推加分页面
                         */
                        Long parentSubmitDataId = !Objects.isNull(parentPv.getParentSubmitDataId()) ? parentPv.getParentSubmitDataId() : pageViewInfo.getSubmitDataId();
                        if (!Objects.isNull(parentSubmitDataId) && 0L != parentSubmitDataId) {
                            //当前实体插入用于下面合并客资，顺带修改数据库
                            pageViewInfo.setParentSubmitDataId(parentSubmitDataId);
                            pageViewInfoPgService.updateParentSubmitDataIdByPid(pageViewInfo.getPid(), parentSubmitDataId);
                        }
                    }
                }
                log.info("====>企业推自归因pv匹配成功 agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；",
                    agentId, externalUnionId, externalUserId, name, corpId, userId, JSONObject.toJSONString(pageViewInfo));
                matchedPvSaveCustomerAndSaveCache(agentId, externalUserId, externalUnionId, name, pageViewInfo, ewacr, AddSource.LANDING_PAGE_LINK, lpwcsList, enterpriseWechat, userId, todayNow, beforeNow, SubmitType.WECHAT_APPLET, corpId);
                //state匹配成功
                //自动打标签sender
                Optional.ofNullable(pageViewInfo.getLandingPageId()).ifPresent(e -> {
                    MakeEnterpriseWechatTagDto makeEnterpriseWechatTagDto = new MakeEnterpriseWechatTagDto();
                    makeEnterpriseWechatTagDto.setLangingPageId(e)
                        .setUserId(userId)
                        .setExternalUserUserid(externalUserId)
                        .setCorpId(corpId);
                    enterpriseWechatTagStrategySender.sendMakeEnterpriseWechatTag(makeEnterpriseWechatTagDto);
                });
                if (LinkType.WECHAT_CUSTOMER_ROBOT_SERVICE.equals(pageViewInfo.getLinkType())) {
                    enterpriseWechatRobotCustomerService.statisticWechatCustomerServiceRobotData(
                        pageViewInfo.getOriginRobotId(),
                        EnterpriseWechatRobotCustomerDataStatisticsField.ADD_ENTERPRISE_WECHAT_COUNT,
                        null
                    );
                }
                return true;
            }
        }
        //微信公众号回复自归因参数逻辑
        if (StringUtils.contains(state, String.format(OfficialContactWayPrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))
            || StringUtils.contains(state, String.format(IdenticalOfficialContactWayPrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))) {
            final Long TIMES = workWechatDevelopConf.getSelfAttributionMatchingDateTime();
            final Instant todayNow = Instant.now();
            final Instant beforeNow = todayNow.minusMillis(TIME_UNIT.toMillis(TIMES));
            LambdaQueryWrapper<OfficialAccountSendMessageRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OfficialAccountSendMessageRecord::getState, state);
            wrapper.gt(OfficialAccountSendMessageRecord::getCreatedAt, beforeNow);
            wrapper.orderByDesc(OfficialAccountSendMessageRecord::getCreatedAt);
            wrapper.last(" limit 1");
            boolean isSameSubject = StringUtils.contains(state, String.format(IdenticalOfficialContactWayPrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()));
            if (isSameSubject) {
                wrapper.eq(OfficialAccountSendMessageRecord::getUnionId, externalUnionId);
            }
            OfficialAccountSendMessageRecord officialAccountSendMessageRecord = officialAccountSendMessageRecordService.getOne(wrapper);
            PageViewInfo pageViewInfo = new PageViewInfo();
            if (officialAccountSendMessageRecord != null) {
                pageViewInfo = pageViewInfoPgService.getByPid(officialAccountSendMessageRecord.getPid());
                List<OfficialAccountSendMessageRecord> officialAccountSendMessageRecords = officialAccountSendMessageRecordService.list(
                    Wrappers.lambdaQuery(OfficialAccountSendMessageRecord.class)
                        .select(OfficialAccountSendMessageRecord::getId, OfficialAccountSendMessageRecord::getMediaId)
                        .eq(OfficialAccountSendMessageRecord::getOpenId, officialAccountSendMessageRecord.getOpenId())
                        .eq(OfficialAccountSendMessageRecord::getAddEnterpriseWechatStatus, AddEnterpriseWechatStatus.NOT_ADDED)
                );
                if (CollectionUtils.isNotEmpty(officialAccountSendMessageRecords)) {
                    officialAccountSendMessageRecordService.update(
                        Wrappers.lambdaUpdate(OfficialAccountSendMessageRecord.class)
                            .set(OfficialAccountSendMessageRecord::getAddEnterpriseWechatStatus, AddEnterpriseWechatStatus.ADDED)
                            .in(OfficialAccountSendMessageRecord::getId, CollectionUtil.mapToSet(officialAccountSendMessageRecords, OfficialAccountSendMessageRecord::getId))
                    );
                    if (!isSameSubject) {
                        officialAccountSendMessageRecords.forEach(t -> {
                            officialWechatCustomerContactService.deleteCustomerContactByMaterialId(t.getMediaId());
                        });
                    }
                }
                //检测公众助手规则自动打标签
                Optional.ofNullable(officialAccountSendMessageRecord.getOfficialAssistantReplyMessageId()).ifPresent(e -> {
                    MakeEnterpriseWechatTagDto makeEnterpriseWechatTagDto = new MakeEnterpriseWechatTagDto();
                    makeEnterpriseWechatTagDto.setOfficialAssistantReplyMessageId(e)
                        .setUserId(userId)
                        .setExternalUserUserid(externalUserId)
                        .setCorpId(corpId);
                    enterpriseWechatTagStrategySender.sendOfficialMakeEnterpriseWechatTag(makeEnterpriseWechatTagDto);
                });
                if (LinkType.WECHAT_CUSTOMER_ROBOT_SERVICE.equals(pageViewInfo.getLinkType())) {
                    enterpriseWechatRobotCustomerService.statisticWechatCustomerServiceRobotData(
                        pageViewInfo.getOriginRobotId(),
                        EnterpriseWechatRobotCustomerDataStatisticsField.ADD_ENTERPRISE_WECHAT_COUNT,
                        null
                    );
                }

                //查询公众号助手配置的规则
                log.info("判断公众号助手配置的规则是否是非广告来源,发送消息");
                sendMessageMakeNonAdSourceEnterpriseWechatTag(state, new MakeEnterpriseWechatTagDto().setUserId(userId).setCorpId(corpId).setExternalUserUserid(externalUserId).setOfficialAccountNonAdScene(true), pageViewInfo, MakeNonAdSourceCustomerTagEventType.OFFICIAL_ACCOUNT_ASSISTANT, externalUserSex);

            }
            log.info("====>公众号企微联系我二维码自归因pv匹配成功 agentId={}；unionId={}；externalUserId={}；name={}；corpid={}；userId={}；pvInfo={}；",
                agentId, externalUnionId, externalUserId, name, corpId, userId, JSONObject.toJSONString(pageViewInfo));
            if (!org.springframework.util.CollectionUtils.isEmpty(lpwcsList)) {
                if (Objects.isNull(pageViewInfo)) {
                    pageViewInfo = new PageViewInfo();
                }
                Long advertiserAccountGroupId = Objects.isNull(officialAccountSendMessageRecord) ? null : officialAccountSendMessageRecord.getAdvertiserAccountGroupId();
                PageViewInfo pvInfo = pageViewInfo;
                lpwcsList.stream().forEach(e -> {
                    if (Objects.nonNull(advertiserAccountGroupId) && Objects.nonNull(e.getAdvertiserAccountGroupId())
                        && advertiserAccountGroupId.equals(e.getAdvertiserAccountGroupId())) {
                        log.info("==>企微代开发加粉回调-公众号单人活码加粉，客服id:{},识别到为公众号加粉客服,归为公众号内加粉数内", e.getId());
                        //加粉归为公众号发送二维码加粉数
                        ewacr.setWechatUserIdMatchingStatus(1)
                            .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED);
                        pvInfo.setAdvertiserAccountGroupId(advertiserAccountGroupId);
                        ewacr.setWechatAppletGroupChatName(StringUtils.trim(e.getWechatUserName()))
                            .setWechatCustomerServiceId(e.getId());
                        MatchingWorkWechatCustomerDto matchingWorkWechatCustomerDto = saveSubmitDataAndCustomer(agentId, pvInfo, ewacr, enterpriseWechat, SubmitType.OFFICIAL_ACCOUNT_FANS, corpId, userId, externalUserId);
                        if (StringUtils.isNotBlank(pvInfo.getPid())) {
                            //关注公众号后发码添加企业微信
                            uploadSender.sendFollowOfficialAddCustomer(new UploadDto()
                                .setUploadEventType(UploadEventType.FOLLOW_OFFICIAL_ADD_CUSTOMER)
                                .setPid(pvInfo.getPid())
                                .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED));
                            //公众号关注后通过公众号蓝链加粉成功：公众号助手自动回复-携带上级关注页pid、公众号助手点击菜单链接跳转-仅携带蓝链标识
                                customerSender.sendUpdateWechatOfficialCustomer(new LandingPageWechatOfficialAccountCustomer()
                                    //归因参数
                                    .setMatchingPid(pvInfo.getPid())
                                    .setAddEnterpriseWechatPid(pvInfo.getPid())
                                    .setOpenid(StringUtils.isNotBlank(pvInfo.getPid()) ? null : pvInfo.getWechatOpenid())
                                    //公众号粉丝客资数据
                                    .setUserName(ewacr.getWechatAppletName())
                                    .setUserPhoto(ewacr.getAvatar())
                                    .setExternalUseridAddAt(ewacr.getExternalUseridAddAt())
                                    .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
                                    .setDeleteWechatTime(null)
                                    .setFollowedAddEnterpriseWechat(FollowedAddEnterpriseWechat.QR_CODE_ADD_ENTERPRISE_WECHAT)
                                    .setWechatAppletExternalUserid(ewacr.getWechatAppletExternalUserid())
                                    .setWechatAppletGroupChatName(ewacr.getWechatAppletGroupChatName())
                                    .setWechatAppletUserid(ewacr.getWechatAppletUserid())
                                    .setWechatAppletUserPmpId(pvInfo.getAdvertiserAccountGroupId())
                                    .setCorpid(enterpriseWechat.getCorpid())
                                    .setEnterpriseWechatName(enterpriseWechat.getCorpName())
                                    .setExternalUserSex(ewacr.getExternalUserSex())
                                    .setWechatAppletAddWay(ewacr.getWechatAppletAddWay())
                                    //.setFollowAddWechatPmpId(pvInfo.getAdvertiserAccountGroupId())
                                    //.setFollowAddWechatLandingPageId(pvInfo.getLandingPageId())
                                    //.setFollowAddWechatChannelId(pvInfo.getChannelId())
                                    //.setFollowAddWechatChannelUrl(pvInfo.getUrl())
                                    .setSubmitDataId(Objects.isNull(matchingWorkWechatCustomerDto) ? null : matchingWorkWechatCustomerDto.getSubmitDataId())
                                    .setCustomerId(Objects.isNull(matchingWorkWechatCustomerDto) ? null : matchingWorkWechatCustomerDto.getCustomerId())
                                    .setUpdatedAt(Instant.now())
                                );

                            pageViewInfoService.sendMessageOfEnterpriseEvent(pvInfo, IndicatorStatisticEventEnum.FOLLOW_OFFICIAL_ACCOUNT_ADD_FANS, null);
                        }
                    } else {
                        ewacr.setWechatUserIdMatchingStatus(0)
                            .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.NOT_ADDED);
                        saveOtherSubmitDataAndCustomer(agentId, externalUnionId, externalUserId, name, enterpriseWechat.getCorpid(), userId, ewacr, enterpriseWechat, lpwcsList, todayNow, beforeNow, null, e, SubmitType.OFFICIAL_ACCOUNT_FANS);
                    }
                });
            }
            //不需要通过此方法进行客资生成
//            matchedPvSaveCustomerAndSaveCache(agentId, externalUserId, externalUnionId, name, pageViewInfo, ewacr, AddSource.LANDING_PAGE_LINK, lpwcsList, enterpriseWechat, null, SubmitType.OFFICIAL_ACCOUNT_FANS);
            //同主体公众号企微联系我二维码
            if (isSameSubject) {
                log.info("判断是否非关联同主体公众号关注用户 agentId={}；userId={}；externalUserId={}；name={}；corpid={}；unionId={}；pvInfo={}；",
                    agentId, userId, externalUserId, name, corpId, externalUnionId, JSONObject.toJSONString(pageViewInfo));
                if (isUnrelatedOfficialUser(externalUnionId, userId, state)) {
                    //对非关联同主体公众号关注用户打标签
                    log.info("对非关联同主体公众号关注用户打标签 agentId={}；userId={}；externalUserId={}；name={}；corpid={}；unionId={}；pvInfo={}；",
                        agentId, userId, externalUserId, name, corpId, externalUnionId, JSONObject.toJSONString(pageViewInfo));
                    enterpriseWechatTagStrategyService.tagUnrelatedOfficialUser(externalUserId, userId, agentId, corpId, externalUserSex, (Objects.isNull(pageViewInfo) ? null : pageViewInfo.getUrl()), (Objects.isNull(pageViewInfo) ? null : pageViewInfo.getReferrer()));
                }
            }
            return true;
        }


        //多人分组活码只生成客资
        if (StringUtils.contains(state, String.format(OfficialMultContactWayPrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))
            || StringUtils.contains(state, String.format(IdenticalOfficialMultContactWayPrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))) {
            log.info("===>企微代开发加粉回调-识别为公众号企微多人联系我二维码自归因参数,state:{}", state);
            final Long TIMES = workWechatDevelopConf.getMatchingDataTime();
            final Instant todayNow = Instant.now();
            final Instant beforeNow = todayNow.minusMillis(TIME_UNIT.toMillis(TIMES));
            //查询公众号二维码发送记录，此表上记录的项目ID为发送的客服分组的项目ID
            LambdaQueryWrapper<OfficialAccountSendMessageRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(OfficialAccountSendMessageRecord::getAppId,
                OfficialAccountSendMessageRecord::getWechatCustomerServiceGroupId,
                OfficialAccountSendMessageRecord::getAdvertiserAccountGroupId);
            wrapper.eq(OfficialAccountSendMessageRecord::getState, state);
            wrapper.gt(OfficialAccountSendMessageRecord::getCreatedAt, beforeNow);
            wrapper.orderByDesc(OfficialAccountSendMessageRecord::getCreatedAt);
            wrapper.last(" limit 1");

            OfficialAccountSendMessageRecord one = officialAccountSendMessageRecordService.getOne(wrapper);
            PageViewInfo pageViewInfo = new PageViewInfo();
            if (Objects.nonNull(one)) {
                pageViewInfo.setFollowOfficialAccountAppId(one.getAppId())
                    .setWechatCustomerServiceGroupId(one.getWechatCustomerServiceGroupId());
            }
            log.info("===>企微代开发加粉回调-识别为公众号企微多人联系我二维码自归因参数,state:{}, pageViewInfo = {}", state, JSONObject.toJSONString(pageViewInfo));
            if (!org.springframework.util.CollectionUtils.isEmpty(lpwcsList)) {
                Long advertiserAccountGroupId = Objects.isNull(one) ? null : one.getAdvertiserAccountGroupId();
                lpwcsList.stream().forEach(e -> {
                    if (Objects.nonNull(advertiserAccountGroupId) && Objects.nonNull(e.getAdvertiserAccountGroupId())
                        && advertiserAccountGroupId.equals(e.getAdvertiserAccountGroupId())) {
                        log.info("==>企微代开发加粉回调-公众号多人活码加粉，客服id:{},识别到为公众号加粉客服,归为公众号内加粉数内", e.getId());
                        //加粉归为公众号发送二维码加粉数
                        ewacr.setWechatUserIdMatchingStatus(1)
                            .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED);
                    } else {
                        ewacr.setWechatUserIdMatchingStatus(0)
                            .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.NOT_ADDED);
                    }
                    saveOtherSubmitDataAndCustomer(agentId, externalUnionId, externalUserId, name, enterpriseWechat.getCorpid(), userId, ewacr, enterpriseWechat, lpwcsList, todayNow, beforeNow, pageViewInfo, e, SubmitType.OFFICIAL_ACCOUNT_CUSTOMER_GROUP_CODE_FANS);
                });
            }
            return true;
        }

        //图片二维码自归因参数 - 不做自归因匹配
        if (StringUtils.contains(state, String.format(ContactMeQRCodeStatePrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv()))) {
            log.info("state:{} 图片二维码自归因参数，不走自归因逻辑", state);
            return false;
        }

        //互动广告逻辑
        String format = String.format(TrafficEnginePrefixEnum.IN_USE.getPrefix(), landingPageWechatCustomerContactConfig.getEnv());
        if (state.contains(format)) {
            log.info("state:{},互动广告逻辑", state);
            for (LandingPageWechatCustomerService lpwcs : lpwcsList) {
                //找到对应账户项目生成客资，传递客资信息至互动广告侧
                TrafficEngineCustomerMessageDto trafficEngineCustomerMessageDto = new TrafficEngineCustomerMessageDto();
                trafficEngineCustomerMessageDto.setState(state)
                    .setAgentId(agentId)
                    .setEnterpriseWechatAddContactRecord(ewacr)
                    .setLandingPageWechatCustomerService(lpwcs)
                    .setCorpId(corpId)
                    .setCorpName(corpName)
                    .setExternalUserId(externalUserId);
                trafficEngineCustomerServiceOperationSender.sendAddTrafficEngineCustomer(trafficEngineCustomerMessageDto);
            }
            return true;
        }
        return false;
    }


    /**
     * 发送消息判断客资是否属于非广告来源，进行打标签
     *
     * @param state                      页面内加粉自归因参数
     * @param makeEnterpriseWechatTagDto 客资信息
     * @param pageViewInfo               PV信息
     */
    public void sendMessageMakeNonAdSourceEnterpriseWechatTag(String state, MakeEnterpriseWechatTagDto makeEnterpriseWechatTagDto, PageViewInfo pageViewInfo, MakeNonAdSourceCustomerTagEventType eventType, Sex externalUserSex) {
        try {
            log.info("明文代开发，发送消息判断客资是否属于非广告来源，进行打标签, state = {},eventType = {}", state, eventType);
            if (Objects.nonNull(makeEnterpriseWechatTagDto)) {

                AdSourceEnterpriseWechatTagDTO dto = new AdSourceEnterpriseWechatTagDTO();
                dto.setUserId(makeEnterpriseWechatTagDto.getUserId())
                    .setExternalUserUserid(makeEnterpriseWechatTagDto.getExternalUserUserid())
                    .setState(state)
                    .setCorpId(makeEnterpriseWechatTagDto.getCorpId())
                    .setExternalUserSex(externalUserSex)
                    .setEventType(eventType)
                    .setUrl(pageViewInfo.getUrl())
                    .setReferrer(pageViewInfo.getReferrer());
                if (Objects.nonNull(pageViewInfo)) {
                    dto.setClickId(pageViewInfo.getClickId())
                        .setAdvertiserAccountGroupId(pageViewInfo.getAdvertiserAccountGroupId())
                        .setWechatOpenid(pageViewInfo.getWechatOpenid())
                        .setWechatUnionid(pageViewInfo.getWechatUnionid())
                        .setWechatAppletOpenid(pageViewInfo.getWechatAppletOpenid())
                        .setWechatAppletUnionid(pageViewInfo.getWechatAppletUnionid())
                        .setLandingPageId(pageViewInfo.getLandingPageId())
                        .setUrl(pageViewInfo.getUrl())
                        .setUid(pageViewInfo.getUid())
                        .setPid(pageViewInfo.getPid())
                        .setReferrer(pageViewInfo.getReferrer())
                        .setLinkType(pageViewInfo.getLinkType())
                    ;
                }
                //判断一下获客链接是否不为空并且被篡改
                if (StringUtils.isNotBlank(state) && WechatAppletAddWay.ADD_THROUGH_CUSTOMER_ACQUISITION_LINK.equals(makeEnterpriseWechatTagDto.getWechatAppletAddWay())) {
                    String key = RedisConstant.LANDING_PAGE_WECHAT_CUSTOMER_ACQUISITION_STATE_CONFIG + state;
                    log.info("明文代开发，判断一下获客链接是否不为空并且被篡改, key = {}", key);
                    Object redisValue = objectRedisTemplate.opsForValue().get(key);
                    //有值说明是合法的，没有值的话，判断是否是微信客服机器人发送的获客助手链接的情况
                    if (Objects.isNull(redisValue)) {
                        //继续判断是否是机器人发送的获客链接
                        String robotKey = RedisConstant.ROBOT_WECHAT_CUSTOMER_ACQUISITION_STATE_CONFIG + state;
                        Object robotRedisValue = objectRedisTemplate.opsForValue().get(robotKey);
                        log.info("明文代开发，判断一下获客链接是否不为空并且被篡改,判断是否是机器人蓝链发送的, robotKey = {}", robotKey);
                        if (Objects.isNull(robotRedisValue)) {
                            //说明获客链接被篡改
                            log.info("说明获客链接state参数被篡改, state = {}", state);
                            dto.setEventType(MakeNonAdSourceCustomerTagEventType.ADD_THROUGH_CUSTOMER_ACQUISITION_LINK_STATE_ILLEGAL);
                        }
                    }
                }

                log.info("明文代开发,发送消息判断客资是否属于非广告来源，进行打标签,开关标识 OpenFlag = {}", Objects.nonNull(unAdSceneConfig) ? unAdSceneConfig.getOpenFlag() : null);
                if (Objects.nonNull(unAdSceneConfig) && Objects.nonNull(unAdSceneConfig.getOpenFlag()) && unAdSceneConfig.getOpenFlag()) {
                    enterpriseWechatTagStrategySender.sendMakeNonAdSourceEnterpriseWechatTag(dto);
                }

                //更新一下缓存的加粉状态为已经加粉，用于微信客服机器人判断重复访客记录
                pageViewInfoService.updateAddStatus(dto);
            }

        } catch (Exception e) {
            log.error("明文代开发，发送消息判断客资是否属于非广告来源，进行打标签出现异常,state={}, eventType = {}", state, eventType);
        }
    }

    /**
     * 是否非关联同主体公众号关注用户
     *
     * @param externalUnionId
     * @param userId
     * @param state
     * @return
     */
    private boolean isUnrelatedOfficialUser(String externalUnionId, String userId, String state) {
        if (StringUtils.isBlank(externalUnionId) || StringUtils.isBlank(userId) || StringUtils.isBlank(state)) {
            return true;
        }
        OfficialAccountSendMessageRecord record = officialAccountSendMessageRecordService.getOne(
            new LambdaQueryWrapper<OfficialAccountSendMessageRecord>()
                .eq(OfficialAccountSendMessageRecord::getUnionId, externalUnionId)
                .eq(OfficialAccountSendMessageRecord::getWechatCustomerServiceUserId, userId)
                .eq(OfficialAccountSendMessageRecord::getState, state)
                .eq(OfficialAccountSendMessageRecord::getSubjectType, OfficialWechatCustomerSubjectType.IDENTICAL)
                .last("limit 1"));
        if (record == null) {
            return true;
        }
        return false;
    }

    /**
     * 企业推自归因查询pv
     *
     * @param state
     * @return
     */
    private PageViewInfo getPvByQiyeState(String state, Instant beforeNow) {
        //查询最近2小时内state相匹配的记录，查询不到则查询客资列表相同的state,若是存在，则获取qiyePendingId查询最近24小时pv
        PageViewInfo pageViewInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
            .eq(PageViewInfo::getLandingPageWechatCustomerContactState, state)
            .gt(PageViewInfo::getCreatedAt, beforeNow)
            .orderByDesc(PageViewInfo::getCreatedAt)
            .last(" limit 1"));
        if (Objects.nonNull(pageViewInfo)) {
            AddEnterpriseWechatStatus addEnterpriseWechatStatus = pageViewInfo.getAddEnterpriseWechatStatus();
            //匹配到的pv是未修改过加粉状态的
            if (Objects.nonNull(addEnterpriseWechatStatus) && AddEnterpriseWechatStatus.NOT_ADDED.equals(addEnterpriseWechatStatus)) {
                if (!IdentifyQrCodeStatus.IDENTIFIED.equals(pageViewInfo.getIdentifyQrCodeStatus())) {
                    // 没有长按的补偿长按
                    //补偿二维码全屏展示pv的长按统计
                    pageViewinfoSender.addWechatIdentifyReport(pageViewInfo);
                }
                return pageViewInfo;
            }
        } else {
            // 1.240.0 二维码全屏展示 无法获取长按事件，所以需要在此处需要匹配二维码 state参数 与  pid 的关系
            if (StringUtils.isNotBlank(StringUtils.trim(state))) {
                StringBuilder keyBuilder = new StringBuilder();
                keyBuilder.append(RedisConstant.IDENTIFY_QRCODE_DATA_NEW_KEY);
                keyBuilder.append(":state:").append(state);
                String key = keyBuilder.toString();
                Object o = objectRedisTemplate.opsForValue().get(key);
                log.info("获取存储二维码 state参数 与  pid 的关系 key:[{}]", key);
                if (Objects.nonNull(o)) {
                    QrCodeShowImageDto qrCodeShowImageDto = JSON.parseObject(o.toString(), QrCodeShowImageDto.class);
                    log.info("获取存储二维码 state参数 与  pid 的关系 o:[{}]", JSON.toJSONString(qrCodeShowImageDto));
                    pageViewInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                        .eq(PageViewInfo::getPid, qrCodeShowImageDto.getPid())
                        .gt(PageViewInfo::getCreatedAt, beforeNow)
                        .orderByDesc(PageViewInfo::getCreatedAt)
                        .last(" limit 1"));
                    if (Objects.nonNull(pageViewInfo)) {
                        pageViewInfo.setLandingPageWechatCustomerContactState(state)
                            .setWechatCustomerServiceGroupId(qrCodeShowImageDto.getWechatCustomerServiceGroupId())
                            .setWechatCustomerServiceId(qrCodeShowImageDto.getWechatCustomerServiceId())
                            .setEnterpriseWechatCorpId(qrCodeShowImageDto.getEnterpriseWechatCorpId())
                            .setPid(qrCodeShowImageDto.getPid());
                        //补偿二维码全屏展示pv的长按统计
                        pageViewinfoSender.addWechatIdentifyReport(pageViewInfo);
                    }
                }
            }
        }
        return pageViewInfo;
        //上面查询不符合结果 则继续走下面逻辑
        //查询客资列表,取pendingId，查询24小时内该pendingId的记录
//        Customer customer = customerService.getOne(new LambdaQueryWrapper<Customer>()
//            .select(Customer::getId, Customer::getQiyePendingId, Customer::getWechatCustomerContactState, Customer::getUid, Customer::getNextPid)
//            .eq(Customer::getWechatCustomerContactState, state)
//            .orderByDesc(Customer::getCreatedAt)
//            .last(" limit 1"));
//        if (Objects.nonNull(customer)) {
//            String qiyePendingId = customer.getQiyePendingId();
//            if (StringUtils.isNotBlank(qiyePendingId)) {
//                pageViewInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
//                    .eq(PageViewInfo::getQiyePendingId, qiyePendingId)
//                    .gt(PageViewInfo::getCreatedAt, beforeNow)
//                    .orderByDesc(PageViewInfo::getCreatedAt)
//                    .last(" limit 1"));
//            }
//            //查询不到 则最终使用uid查询
//            //使用uid的弊端：若是表单串加粉，uid会是表单页的uid,使用uid查询也不会是加粉页的pv,需要特殊处理，查询客资是否包含next_pid,有next_pid的
//            //通过next_pid查询pv,再使用该pv的uid查询
//            AtomicReference<String> uid = new AtomicReference<>(customer.getUid());
//            //企业推链路查询，查询不到会将pageViewInfo置空，或者本身第一步操作就没查询到pageViewInfo
//            if ((Objects.isNull(pageViewInfo) || AddEnterpriseWechatStatus.ADDED.equals(pageViewInfo.getAddEnterpriseWechatStatus())) && StringUtils.isNotBlank(uid.get())) {
//                log.info("企微代开发v2自归因匹配,降级使用uid查询,state:{},nextPid:{}", state, customer.getNextPid());
//                Optional.ofNullable(customer.getNextPid()).ifPresent(e -> {
//                    log.info("企微代开发v2自归因匹配,降级使用uid查询,state:{},客资记录包含nextPid:{},使用nextPid查询pv记录", state, e);
//                    //使用nextPid查询pv，存在则使用该pv的uid
//                    PageViewInfo pgServiceOne = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
//                        .eq(PageViewInfo::getPid, e)
//                        .orderByDesc(PageViewInfo::getCreatedAt)
//                        .last(" limit 1"));
//                    if (Objects.nonNull(pgServiceOne)) {
//                        uid.set(pgServiceOne.getUid());
//                    }
//                });
//                //uid查询
//                pageViewInfo = pageViewInfoPgService.getOne(new LambdaQueryWrapper<PageViewInfo>()
//                    .eq(PageViewInfo::getUid, uid.get())
//                    .gt(PageViewInfo::getCreatedAt, beforeNow)
//                    .orderByDesc(PageViewInfo::getCreatedAt)
//                    .last(" limit 1"));
//            }
//        }
//        return pageViewInfo;
    }

    /**
     * 检查同一个获客助手链接是否存在被重复加粉的情况
     *
     * @param linkCheckFlag 一次加粉回调只推一次，不然会导致企业微信后台创建多条获客链接，造成资金浪费
     * @param agentId       当前线程对应的agentId
     * @param state         一叶生成的标识
     * @param lpwcsList     配置的微信客服
     */
    public void checkRepeatLink(boolean linkCheckFlag, String agentId, String state,
                                List<LandingPageWechatCustomerService> lpwcsList) {
        try {
            log.info("获客链接防刷检测开关, 开关标识=[{}]，agentId = [{}],linkCheckFlag = [{}]", customerAcquisitionConfig.getOpenFlag(), agentId, linkCheckFlag);
            if (Objects.nonNull(customerAcquisitionConfig) && customerAcquisitionConfig.getOpenFlag() && linkCheckFlag) {
                if (!lpwcsList.isEmpty()) {
                    //查询当前代开发企微
                    CustomerAcquisitionDTO dto = new CustomerAcquisitionDTO();
                    dto.setState(state);
                    dto.setLpwcsList(lpwcsList);
                    enterpriseWechatCustomerAcquisitionSender.checkCustomerAcquisition(dto);
                }
            }
        } catch (Exception e) {
            log.error("检查同一个获客助手链接是否存在被重复加粉,发送消息异常,state=[{}]", state, e);
        }
    }


    /**
     * 渠道二维码 异步检测延迟删除
     *
     * @param enterpriseWechat
     * @param state
     */
    private void sendDeleteAddCallbackCustomerContact(EnterpriseWechat enterpriseWechat, String state) {
        try {
            EnterpriseWechatCodeAddDeleteFlag qrCodeAddDeleteFlag = enterpriseWechat.getQrCodeAddDeleteFlag();
            //空值和未开启不执行
            if (Objects.isNull(qrCodeAddDeleteFlag) || EnterpriseWechatCodeAddDeleteFlag.CLOSE.equals(qrCodeAddDeleteFlag)) {
                log.info("未开启渠道二维码延迟删除功能，不执行加粉回调延迟删除!");
                return;
            }
            CustomerContactDeleteDto customerContactDeleteDto = new CustomerContactDeleteDto().setEnterpriseWechat(enterpriseWechat)
                .setState(state);
            landingPageWechatCustomerContactSender.sendDeleteAddCallbackCustomerContact(customerContactDeleteDto);
        } catch (Exception e) {
            log.warn("添加好友回调-检测清除渠道二维码失败!,state:{}", state, e);
        }
    }

    /**
     * 异步记录单人活码加粉记录（新版本查询）
     *
     * @param customer 客资信息
     */
    public void synRecordOfficialAccountFans(Customer customer, PageViewInfo pageViewInfo,
                                             EnterpriseWechatAddContactRecord ewacr) {
        try {
            log.info("明文代开发,异步记录单人活码加粉记录（新版本查询）,pid = {}", Objects.nonNull(pageViewInfo) ? pageViewInfo.getPid() : null);
            if (Objects.nonNull(pageViewInfo) && Objects.nonNull(customer) && Objects.nonNull(ewacr)) {
                WechatCustomerServiceEventDto dto = new WechatCustomerServiceEventDto();
                dto.setWechatCustomerServiceId(customer.getWechatCustomerServiceId());
                dto.setWechatCustomerServiceUserId(pageViewInfo.getWechatCustomerServiceUserId());
                dto.setWechatCustomerServiceCorpId(pageViewInfo.getEnterpriseWechatCorpId());
                dto.setOfficialAccountQrCodeAddWorkWechatNum(1);
                dto.setAdvertiserAccountGroupId(pageViewInfo.getAdvertiserAccountGroupId());
                dto.setIndicatorStatisticEventEnum(IndicatorStatisticEventEnum.OFFICIAL_ACCOUNT_QR_CODE_ADD_ENTERPRISE_WECHAT_SUCCESS);
                if (Objects.nonNull(pageViewInfo.getCreatedAt())) {
                    dto.setPageViewTime(LocalDateTime.ofInstant(pageViewInfo.getCreatedAt(), ZoneId.systemDefault()));
                    dto.setConvertTime(LocalDateTime.ofInstant(pageViewInfo.getCreatedAt(), ZoneId.systemDefault()));
                    dto.setStatisticDate(LocalDateTime.ofInstant(pageViewInfo.getCreatedAt(), ZoneId.systemDefault()));
                } else {
                    dto.setPageViewTime(LocalDateTime.now());
                    dto.setConvertTime(LocalDateTime.now());
                    dto.setStatisticDate(LocalDateTime.now());
                }
                //补充1.219.0所需的PV字段
                dto.setPid(pageViewInfo.getPid())
                    .setUniqueKey(customer.getWechatAppletExternalUserid())
                    .setSid(pageViewInfo.getSid())
                    .setUid(pageViewInfo.getUid())
                    .setIp(pageViewInfo.getIp())
                    .setUa(pageViewInfo.getUa())
                    .setParentPid(pageViewInfo.getParentPid())
                    .setFirstPagePid(pageViewInfo.getFirstPagePid())
                    .setPlatformId(Platform.getEnumById(pageViewInfo.getPlatformId()))
                    .setProvince(pageViewInfo.getProvince())
                    .setCity(pageViewInfo.getCity())
                    .setDevice(pageViewInfo.getDevice())
                    .setOs(pageViewInfo.getOs())
                    .setOsType(pageViewInfo.getOsType())
                    .setBrowser(pageViewInfo.getBrowser())
                    .setBrowserType(pageViewInfo.getBrowserType())
                    .setNetworkType(pageViewInfo.getNetworkType())
                    .setUrl(pageViewInfo.getUrl())
                    .setClickId(pageViewInfo.getClickId())
                    .setCreativeId(pageViewInfo.getCreativeId())
                    .setAdgroupId(pageViewInfo.getAdgroupId())
                    .setCampaignId(pageViewInfo.getCampaignId())
                    .setLandingPageId(pageViewInfo.getLandingPageId())
                    .setLandingPageChannelId(pageViewInfo.getChannelId())
                    .setFlowSource(pageViewInfo.getFlowSource())
                    .setLengthOfStay(pageViewInfo.getLengthOfStay());
                log.info("异步记录单人活码加粉记录（新版本查询）,发生的消息内容dto：{}", JSONObject.toJSONString(dto));
                landingPageSender.sendWechatCustomerIndicatorStatistics(dto);
            }
        } catch (Exception e) {
            log.error("异步记录单人活码加粉记录(新版本查询)异常", e);
        }
    }

}

