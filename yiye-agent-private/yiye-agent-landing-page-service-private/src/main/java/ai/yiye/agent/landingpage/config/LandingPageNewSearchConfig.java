package ai.yiye.agent.landingpage.config;

import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author：lilidong
 * @name：LandingPageNewSearchConfig
 * @Date：2023/3/21 9:46
 */

@Component
@RefreshScope
@ConfigurationProperties(prefix = "yiye.agent.landing-page.newsearch")
public class LandingPageNewSearchConfig {

    /**
     * 落地页查询切换到关联统计表查询开关
     */
    private Boolean landingPageNewSearch ;

    /**
     * 微信客服列表切换到关联统计表查询开关
     */
    private Boolean wechatCustomerNewSearch;

    private List<String> agentIds = new ArrayList<>();

    public List<String> getAgentIds() {
        return agentIds;
    }

    public void setAgentIds(List<String> agentIds) {
        this.agentIds = agentIds;
    }

    public Boolean getLandingPageNewSearch() {
        return landingPageNewSearch && (agentIds.contains(TenantContextHolder.get()) || CollectionUtils.isEmpty(agentIds));
    }

    public void setLandingPageNewSearch(Boolean landingPageNewSearch) {
        this.landingPageNewSearch = landingPageNewSearch;
    }

    public Boolean getWechatCustomerNewSearch() {
        return wechatCustomerNewSearch && (agentIds.contains(TenantContextHolder.get()) || CollectionUtils.isEmpty(agentIds));
    }

    public void setWechatCustomerNewSearch(Boolean wechatCustomerNewSearch) {
        this.wechatCustomerNewSearch = wechatCustomerNewSearch;
    }

    /**
     * 微信客服列表指标统计MQ批量消费开关 true:开启; false: 关闭
     */
    private Boolean openWechatCustomerIndicatorStatistic;

    public Boolean getOpenWechatCustomerIndicatorStatistic() {
        return openWechatCustomerIndicatorStatistic;
    }

    public void setOpenWechatCustomerIndicatorStatistic(Boolean openWechatCustomerIndicatorStatistic) {
        this.openWechatCustomerIndicatorStatistic = openWechatCustomerIndicatorStatistic;
    }

    /**
     * 落地页指标统计批量消费开关 true:开启; false: 关闭
     */
    private Boolean openLandingPageIndicatorStatistic;

    public Boolean getOpenLandingPageIndicatorStatistic() {
        return openLandingPageIndicatorStatistic;
    }

    public void setOpenLandingPageIndicatorStatistic(Boolean openLandingPageIndicatorStatistic) {
        this.openLandingPageIndicatorStatistic = openLandingPageIndicatorStatistic;
    }

}
