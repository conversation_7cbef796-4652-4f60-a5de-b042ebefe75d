package ai.yiye.agent.landingpage.controller;

import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.dto.JumpWhatsappSuccessDto;
import ai.yiye.agent.domain.dto.WhatsappBusinessCustomerServiceRedisDto;
import ai.yiye.agent.domain.enumerations.OnlineStatusType;
import ai.yiye.agent.domain.landingpage.WhatsappCustomerService;
import ai.yiye.agent.domain.landingpage.WhatsappCustomerServiceGroup;
import ai.yiye.agent.domain.result.Result;
import ai.yiye.agent.domain.utils.UrlUtils;
import ai.yiye.agent.landingpage.controller.vo.WhatsappCustomerServiceVo;
import ai.yiye.agent.landingpage.redis.WhatsappCustomerServiceRedis;
import ai.yiye.agent.landingpage.sender.PageViewinfoSender;
import ai.yiye.agent.landingpage.sender.SubmitDataSender;
import ai.yiye.agent.landingpage.service.WhatsappCustomerServiceGroupService;
import ai.yiye.agent.landingpage.service.WhatsappCustomerServiceService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * 落地页 - 微信客服管理 - 分组 - 控制层
 */
@Slf4j
@RestController
@RequestMapping(path = "/whatsapp-customer-service")
public class WhatsappCustomerServiceController {

    @Autowired
    private SubmitDataSender submitDataSender;
    @Autowired
    private PageViewinfoSender pageViewinfoSender;
    @Autowired
    private WhatsappCustomerServiceRedis whatsappCustomerServiceRedis;
    @Autowired
    private WhatsappCustomerServiceService whatsappCustomerServiceService;

    @Autowired
    private WhatsappCustomerServiceGroupService whatsappCustomerServiceGroupService;

    /**
     * 获取客服分组列表
     */
    @GetMapping("/group/list")
    public Result<?> getGroupList(Long advertiserAccountGroupId) {
        return Result.success(whatsappCustomerServiceGroupService.getGroupList(advertiserAccountGroupId));
    }

    /**
     * 添加客服分组
     */
    @PostMapping("/group")
    public Result<?> addGroup(@RequestBody WhatsappCustomerServiceGroup whatsappCustomerServiceGroup) {
        whatsappCustomerServiceGroupService.addGroup(whatsappCustomerServiceGroup);
        return Result.success();
    }

    /**
     * 修改客服分组
     */
    @PutMapping("/group")
    public Result<?> editGroup(@RequestBody WhatsappCustomerServiceGroup whatsappCustomerServiceGroup) {
        whatsappCustomerServiceGroupService.editGroup(whatsappCustomerServiceGroup);
        return Result.success();
    }

    /**
     * 删除客服分组
     */
    @DeleteMapping("/group/{groupId}")
    public Result<?> deleteGroupById(@PathVariable Long groupId) {
        whatsappCustomerServiceGroupService.deleteGroupById(groupId);
        return Result.success();
    }

    /**
     * 分页获取项目下的客服
     */
    @GetMapping("/page/by-pmp-id")
    public Result<?> getCustomerServicePageByPmpId(WhatsappCustomerServiceVo vo) {
        return Result.success(whatsappCustomerServiceService.getCustomerServicePageByPmpId(vo));
    }

    /**
     * 获取客服详情
     */
    @GetMapping("/{customerServiceId}")
    public Result<?> getCustomerServiceById(@PathVariable Long customerServiceId) {
        return Result.success(whatsappCustomerServiceService.getCustomerServiceById(customerServiceId));
    }

    /**
     * 修改客服
     */
    @PutMapping
    public Result<?> editCustomerService(@RequestBody WhatsappCustomerService whatsappCustomerService) {
        whatsappCustomerServiceService.editCustomerService(whatsappCustomerService);
        return Result.success();
    }

    /**
     * 添加客服
     */
    @PostMapping
    public Result<?> addCustomerService(@RequestBody WhatsappCustomerService whatsappCustomerService) {
        whatsappCustomerServiceService.addCustomerService(whatsappCustomerService);
        return Result.success();
    }

    /**
     * 删除客服
     */
    @DeleteMapping("/{customerServiceId}")
    public Result<?> deleteCustomerServiceById(@PathVariable Long customerServiceId) {
        whatsappCustomerServiceService.deleteCustomerServiceById(customerServiceId);
        return Result.success();
    }

    /**
     * 修改客服在线状态
     */
    @PostMapping("/modify-online/{onlineStatus}/{customerServiceId}")
    public Result<?> modifyOnlineCustomerService(@PathVariable Long customerServiceId, @PathVariable OnlineStatusType onlineStatus) {
        whatsappCustomerServiceService.modifyOnlineCustomerService(customerServiceId, onlineStatus);
        return Result.success();
    }

    /**
     * 获取客服语言代码
     */
    @GetMapping("/language-code/list")
    public Result<?> getLanguageCodeList() {
        return Result.success(whatsappCustomerServiceService.getLanguageCodeList());
    }

    /**
     * 根据WhatsApp客服 分组id，获取跳转链接
     */
    @GetMapping("/get-jump-link")
    public String getShowImages(@RequestParam("pid") String pid, @RequestParam("groupId") Long groupId) throws UnsupportedEncodingException {
        if (StringUtils.isBlank(pid) || Objects.isNull(groupId)) {
            return null;
        }
        log.info("根据WhatsApp客服分组id获取跳转链接===========>>>>入参 pid={}；groupId={}；", pid, groupId);
        List<WhatsappBusinessCustomerServiceRedisDto> wbcsList = whatsappCustomerServiceRedis.getWhatsappBusinessServiceDataByGroupId(groupId,
            () -> whatsappCustomerServiceService.getByGroupId(groupId)
        );
        if (CollectionUtils.isEmpty(wbcsList)) {
            log.info("根据WhatsApp客服分组id获取跳转链接，组内无客服 pid={}；groupId={}；", pid, groupId);
            return null;
        }
        Long showGroupNum = whatsappCustomerServiceRedis.getShowGroupNum(groupId);
        int shouIndex = Long.valueOf(showGroupNum % wbcsList.size()).intValue();
        WhatsappBusinessCustomerServiceRedisDto wbcs = wbcsList.get(shouIndex);
        if (Objects.isNull(wbcs) || StringUtils.isBlank(wbcs.getTelephone())) {
            log.info("根据WhatsApp客服分组id获取跳转链接，无效的客服信息 pid={}；groupId={}；wbcs={}；", pid, groupId, JSONObject.toJSONString(wbcs));
            return null;
        }
        //发送点击whatsapp链接跳转成功上报队列
        submitDataSender.sendJumpWhatsappSuccessUpload(pid);
        //发送点击whatsapp链接跳转成功pv状态更新队列
        pageViewinfoSender.sendJumpWhatsappSuccess(new JumpWhatsappSuccessDto()
            .setAgentId(TenantContextHolder.get())
            .setPid(pid)
            .setWhatsappCustomerServiceGroupId(groupId)
            .setWhatsappCustomerServiceId(wbcs.getId())
        );
        String randomStr = whatsappCustomerServiceRedis.saveRandomStrMatchPid(pid, 10);
        String text = StringUtils.isBlank(wbcs.getReplyContent()) ? "" : URLEncoder.encode(wbcs.getReplyContent().replace("{{YIYE_ID}}", randomStr), StandardCharsets.UTF_8.toString());
        String resultUrl = UrlUtils.WHATSAPP_JUMP_INDEX_URL + wbcs.getTelephone() + UrlUtils.URL_LINKER + "?text=" + text;
        //记录pid与跳转匹配关系，持续24小时
        whatsappCustomerServiceRedis.saveShowGroupNum(groupId);
        return resultUrl;
    }

}
