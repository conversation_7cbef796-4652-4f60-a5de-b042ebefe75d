package ai.yiye.agent.landingpage.service;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.util.CommonUtil;
import ai.yiye.agent.common.util.DateTimeUtil;
import ai.yiye.agent.common.util.WorkWechatSignatureGetter;
import ai.yiye.agent.domain.WorkWechatApplicationCustomizedAppOrder;
import ai.yiye.agent.domain.WorkWechatApplicationOrder;
import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.landingpage.EnterpriseWechat;
import ai.yiye.agent.domain.util.EnumUtil;
import ai.yiye.agent.landingpage.config.AgentConf;
import ai.yiye.agent.landingpage.mapper.WorkWechatApplicationCustomizedAppOrderMapper;
import ai.yiye.agent.weixin.client.WorkWeixinApiClient;
import ai.yiye.agent.weixin.enums.EnterpriseWechatGlobalErrorCode;
import ai.yiye.agent.weixin.exception.MarketingApiException;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@DS(DbConstants.POSTGRESQL_DEFAULT)
public class WorkWechatApplicationCustomizedAppOrderService extends ServiceImpl<WorkWechatApplicationCustomizedAppOrderMapper, WorkWechatApplicationCustomizedAppOrder> {


    @Autowired
    private AgentConf agentConf;
    @Autowired
    private WorkWeixinApiClient workWeixinApiClient;
    @Autowired
    private EnterpriseWechatService enterpriseWechatService;
    @Autowired
    private EnterpriseWechatDevelopOauthService enterpriseWechatDevelopOauthService;
    @Resource
    private RedisTemplate<String, Object> defaultObjectRedisTemplate;

    /**
     * 获取当前时间内有效期 【新购/续期】订单
     */
    public WorkWechatApplicationCustomizedAppOrder getNearNowWorkWechatApplicationOrder(String corpId, String suiteId, Instant nowTime) {
        return baseMapper.getNearNowWorkWechatApplicationOrder(corpId, suiteId, nowTime);
    }

    /**
     * 创建应用订单
     */
    public void paytoolOpenOrder(JSONObject requestParams) {
        try {
            final String secret = agentConf.getWecomCheckoutPageKey();
            if (StringUtils.isBlank(secret)) {
                throw new RestException("系统未配置：调用收银台相关接口需要通过此密钥计算签名");
            }
            String providerAccessToken = enterpriseWechatDevelopOauthService.getProviderAccessToken();
            log.info("同步收银台收款订单, 开始检查接口状态, providerAccessToken = {}", providerAccessToken);
            if (StringUtils.isBlank(providerAccessToken)) {
                throw new RestException("企业微信代开发-ProviderAccessToken获取异常");
            }
            requestParams.put("sig", new WorkWechatSignatureGetter().hmacSha256(secret, requestParams));
            JSONObject responseBody = workWeixinApiClient.paytoolOpenOrder(providerAccessToken, requestParams);
            if (Objects.isNull(responseBody) || !Objects.equals(responseBody.getInteger("errcode"), 0)) {
                throw new RestException("同步收银台收款订单异常：" + responseBody.getString("errmsg"));
            }
            syncWorkWechatApplicationOrder();
        } catch (MarketingApiException ex) {
            EnterpriseWechatGlobalErrorCode enterpriseWechatGlobalErrorCode = EnumUtil.getByCode(ex.getErrcode(), EnterpriseWechatGlobalErrorCode.class);
            if (Objects.nonNull(enterpriseWechatGlobalErrorCode)) {
                throw new RestException(enterpriseWechatGlobalErrorCode.getMessage());
            }
            throw new RestException("企业微信系统内部错误：" + ex.getMessage());
        }
    }

    /**
     * 创建应用订单 - 请求参数
     */
    public JSONObject getCreateApplicationParams(EnterpriseWechat enterpriseWechat, Integer userRangeNum) {
        JSONObject requestParams = new JSONObject();
        setCommonRequestParams(requestParams);
        //--------- 下单具体参数 ---------
        JSONObject product_list = new JSONObject();
        JSONObject customized_app = new JSONObject();
        customized_app.put("notify_custom_corp", 0);
        customized_app.put("order_type", 0);
        JSONArray buy_info_list = new JSONArray();
        JSONObject buy_info_list_1 = new JSONObject();
        buy_info_list_1.put("suiteid", enterpriseWechat.getSuiteId());
        buy_info_list_1.put("user_count", (!Objects.isNull(userRangeNum) ? userRangeNum : 0) + 10);
        buy_info_list_1.put("total_price", 20000);
        buy_info_list_1.put("duration_days", 30);
        //buy_info_list_1.put("take_effect_date", DateTimeUtil.getYyyyMMdd(LocalDateTime.now()));
        buy_info_list.add(buy_info_list_1);
        customized_app.put("notify_custom_corp", 1);
        customized_app.put("buy_info_list", buy_info_list);
        product_list.put("customized_app", customized_app);
        //--------- 下单具体参数 ---------
        requestParams.put("pay_type", 2);
        requestParams.put("product_list", product_list);
        requestParams.put("custom_corpid", enterpriseWechat.getOpenCorpid());
        return requestParams;
    }

    /**
     * 扩容应用订单 - 请求参数
     */
    public JSONObject getExpansionApplicationParams(EnterpriseWechat enterpriseWechat, Integer userRangeNum) {
        JSONObject requestParams = new JSONObject();
        setCommonRequestParams(requestParams);
        //--------- 下单具体参数 ---------
        JSONObject product_list = new JSONObject();
        JSONObject customized_app = new JSONObject();
        customized_app.put("notify_custom_corp", 0);
        customized_app.put("order_type", 1);
        JSONArray buy_info_list = new JSONArray();
        JSONObject buy_info_list_1 = new JSONObject();
        buy_info_list_1.put("suiteid", enterpriseWechat.getSuiteId());
        buy_info_list_1.put("user_count", (!Objects.isNull(userRangeNum) ? userRangeNum : 0) + 10);
        buy_info_list_1.put("total_price", 20000);
        buy_info_list_1.put("duration_days", 30);
        //buy_info_list_1.put("take_effect_date", DateTimeUtil.getYyyyMMdd(LocalDateTime.now()));
        buy_info_list.add(buy_info_list_1);
        customized_app.put("notify_custom_corp", 1);
        customized_app.put("buy_info_list", buy_info_list);
        product_list.put("customized_app", customized_app);
        //--------- 下单具体参数 ---------
        requestParams.put("pay_type", 2);
        requestParams.put("product_list", product_list);
        requestParams.put("custom_corpid", enterpriseWechat.getOpenCorpid());
        return requestParams;
    }

    /**
     * 续期应用订单 - 请求参数
     */
    public JSONObject getRenewalApplicationParams(EnterpriseWechat enterpriseWechat) {
        JSONObject requestParams = new JSONObject();
        setCommonRequestParams(requestParams);
        //--------- 下单具体参数 ---------
        JSONObject product_list = new JSONObject();
        JSONObject customized_app = new JSONObject();
        customized_app.put("notify_custom_corp", 0);
        customized_app.put("order_type", 2);
        JSONArray buy_info_list = new JSONArray();
        JSONObject buy_info_list_1 = new JSONObject();
        buy_info_list_1.put("suiteid", enterpriseWechat.getSuiteId());
        buy_info_list_1.put("total_price", 20000);
        buy_info_list_1.put("duration_days", 30);
        buy_info_list_1.put("take_effect_date", DateTimeUtil.getYyyyMMdd(LocalDateTime.now()));
        buy_info_list.add(buy_info_list_1);
        customized_app.put("notify_custom_corp", 1);
        customized_app.put("buy_info_list", buy_info_list);
        product_list.put("customized_app", customized_app);
        //--------- 下单具体参数 ---------
        requestParams.put("pay_type", 2);
        requestParams.put("product_list", product_list);
        requestParams.put("custom_corpid", enterpriseWechat.getOpenCorpid());
        return requestParams;
    }

    /**
     * 同步企微应用订单
     */
    public void syncWorkWechatApplicationOrder() {
        String redisKey = RedisConstant.SYNC_WORK_WECHAT_APPLICATION_ORDER;
        Long num = defaultObjectRedisTemplate.opsForValue().increment(redisKey);
        defaultObjectRedisTemplate.expire(redisKey, 6, TimeUnit.SECONDS);
        boolean interrupt = !Objects.isNull(num) && num > 1;
        if (interrupt) {
            throw new RestException("正在同步【应用到期时间】中，请稍等...");
        }
        String secret = agentConf.getWecomCheckoutPageKey();
        if (StringUtils.isBlank(secret)) {
            throw new RestException("系统未配置：调用收银台相关接口需要通过此密钥计算签名");
        }
        JSONObject requestParams = new JSONObject();
        setCommonRequestParams(requestParams);
        //首次无最大时间同步全部订单，查服务商下全部应用订单
        Instant maxCreateTime = baseMapper.getMaxCreateTime();
        if (!Objects.isNull(maxCreateTime)) {
            //不加1秒，会重复拉取订单
            requestParams.put("start_time", maxCreateTime.getEpochSecond() + 1);
            requestParams.put("end_time", Instant.now().getEpochSecond());
        }
        getAndSaveWorkWechatApplicationOrderBatch(requestParams);
        reloadOtherData();
    }

    /**
     * 批量同步并保存应用订单
     */
    public void getAndSaveWorkWechatApplicationOrderBatch(JSONObject requestParams) {
        final String secret = agentConf.getWecomCheckoutPageKey();
        if (StringUtils.isBlank(secret)) {
            throw new RestException("系统未配置：调用收银台相关接口需要通过此密钥计算签名");
        }
        String providerAccessToken = enterpriseWechatDevelopOauthService.getProviderAccessToken();
        log.info("同步收银台收款订单, 开始检查接口状态, providerAccessToken = {}", providerAccessToken);
        if (StringUtils.isBlank(providerAccessToken)) {
            throw new RestException("企业微信代开发-ProviderAccessToken获取异常");
        }
        requestParams.put("limit", 100);
        requestParams.put("sig", new WorkWechatSignatureGetter().hmacSha256(secret, requestParams));
        JSONObject responseBody = workWeixinApiClient.getPayToolOrderList(providerAccessToken, requestParams);
        if (Objects.isNull(responseBody) || !Objects.equals(responseBody.getInteger("errcode"), 0)) {
            throw new RestException("同步收银台收款订单异常：" + responseBody.getString("errmsg"));
        }
        JSONArray payOrderList = responseBody.getJSONArray("pay_order_list");
        if (CollectionUtils.isEmpty(payOrderList)) {
            return;
        }
        //根据响应的【客户企业的corpid（加密企业微信id）】获取企微corpid集合
        Set<String> enterpriseWechatCustomCorpids = new HashSet<>();
        payOrderList.forEach(e -> {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(e));
            enterpriseWechatCustomCorpids.add(jsonObject.getString("custom_corpid"));
        });
        List<EnterpriseWechat> enterpriseWechats = enterpriseWechatService.list(new LambdaQueryWrapper<EnterpriseWechat>()
            .select(EnterpriseWechat::getOpenCorpid, EnterpriseWechat::getCorpid)
            .in(EnterpriseWechat::getOpenCorpid, enterpriseWechatCustomCorpids)
        );
        List<WorkWechatApplicationCustomizedAppOrder> workWechatApplicationCustomizedAppOrderSaveList = new ArrayList<>();
        payOrderList.forEach(payOrder -> {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(payOrder));
            if (CollectionUtils.isEmpty(jsonObject)) {
                return;
            }
            String corpId = enterpriseWechats.stream().filter(e -> StringUtils.equals(jsonObject.getString("custom_corpid"), e.getOpenCorpid())).findFirst().orElse(new EnterpriseWechat()).getCorpid();
            WorkWechatApplicationOrder wwao = new WorkWechatApplicationOrder()
                .setId(null)
                .setCorpId(Objects.isNull(corpId) ? null : corpId)
                .setBusinessType(requestParams.getInteger("business_type"))
                .setOrderId(jsonObject.getString("order_id"))
                .setCreateTime(DateTimeUtil.long2Instant(jsonObject.getLong("create_time") * 1000))
                .setCustomCorpid(jsonObject.getString("custom_corpid"))
                .setBuyContent(jsonObject.getString("buy_content"))
                .setOriginPrice(jsonObject.getLong("origin_price"))
                .setPaidPrice(jsonObject.getLong("paid_price"))
                .setOrderStatus(jsonObject.getInteger("order_status"))
                .setOrderFrom(jsonObject.getInteger("order_from"))
                .setCreator(jsonObject.getString("creator"))
                .setPayType(jsonObject.getInteger("pay_type"))
                .setCreatedAt(Instant.now())
                .setUpdatedAt(Instant.now());
            JSONObject orderDetailRequestParams = new JSONObject();
            orderDetailRequestParams.put("ts", Instant.now().getEpochSecond());
            orderDetailRequestParams.put("nonce_str", CommonUtil.getUuidReplaceAll());
            orderDetailRequestParams.put("order_id", wwao.getOrderId());
            orderDetailRequestParams.put("sig", new WorkWechatSignatureGetter().hmacSha256(secret, orderDetailRequestParams));
            JSONObject orderDetailResponseBody = workWeixinApiClient.getPayToolOrderDetail(providerAccessToken, orderDetailRequestParams);
            if (Objects.isNull(orderDetailResponseBody)) {
                log.info("同步收银台收款订单详情异常-暂停同步：无法获取相应参数！");
                throw new RestException("同步收银台收款订单详情异常-暂停同步：无法获取相应参数！");
            }
            Integer errcode = orderDetailResponseBody.getInteger("errcode");
            if (!Objects.equals(errcode, 0)) {
                log.info("同步收银台收款订单详情异常-异常错误提示：{}；", orderDetailResponseBody.getString("errmsg"));
                throw new RestException("同步收银台收款订单详情异常-异常错误提示：{}；", orderDetailResponseBody.getString("errmsg"));
            }
            final JSONObject payOrderDetail = orderDetailResponseBody.getJSONObject("pay_order");
            if (CollectionUtils.isEmpty(payOrderDetail)) {
                return;
            }
            final String customCorpName = payOrderDetail.getString("custom_corp_name");
            final JSONObject productList = payOrderDetail.getJSONObject("product_list");
            if (CollectionUtils.isEmpty(productList)) {
                return;
            }
            final JSONObject customizedApp = productList.getJSONObject("customized_app");
            if (CollectionUtils.isEmpty(customizedApp)) {
                return;
            }
            final JSONArray buyInfoList = customizedApp.getJSONArray("buy_info_list");
            if (CollectionUtils.isEmpty(buyInfoList)) {
                return;
            }
            buyInfoList.forEach(e -> {
                JSONObject buyInfo = JSONObject.parseObject(JSONObject.toJSONString(e));
                WorkWechatApplicationCustomizedAppOrder wwacao = new WorkWechatApplicationCustomizedAppOrder()
                    .setId(null)
                    .setCorpId(wwao.getCorpId())
                    .setCustomCorpName(customCorpName)
                    .setBusinessType(wwao.getBusinessType())
                    .setOrderId(wwao.getOrderId())
                    .setCreateTime(wwao.getCreateTime())
                    .setCustomCorpid(wwao.getCustomCorpid())
                    .setBuyContent(wwao.getBuyContent())
                    .setOrderStatus(wwao.getOrderStatus())
                    .setOrderFrom(wwao.getOrderFrom())
                    .setCreator(wwao.getCreator())
                    .setPayType(wwao.getPayType())
                    .setOrderType(customizedApp.getInteger("order_type"))
                    .setSuiteid(buyInfo.getString("suiteid"))
                    .setUserCount(buyInfo.getInteger("user_count"))
                    .setDurationDays(buyInfo.getInteger("duration_days"))
                    .setOriginPrice(buyInfo.getLong("origin_price"))
                    .setOriginPriceAll(wwao.getOriginPrice())
                    .setPaidPrice(buyInfo.getLong("paid_price"))
                    .setPaidPriceAll(wwao.getPaidPrice())
                    .setTakeEffectDate(buyInfo.getString("take_effect_date"))
                    .setCreatedAt(Instant.now())
                    .setUpdatedAt(Instant.now())
                    ;
                if (Objects.equals(1, wwacao.getOrderType()) && Objects.isNull(wwacao.getTakeEffectDate())) {
                }
                if (StringUtils.isNotBlank(wwacao.getTakeEffectDate())) {
                    Instant actualTakeEffectDate = DateTimeUtil.convertTakeEffectDateFormat(wwacao.getTakeEffectDate());
                    wwacao.setTakeEffectDateTime(actualTakeEffectDate);
                    if (!Objects.isNull(actualTakeEffectDate)) {
                        wwacao.setActualTakeEffectDate(DateTimeUtil.getAfterDayInstantByDays(actualTakeEffectDate, wwacao.getDurationDays()));
                    }
                }
                workWechatApplicationCustomizedAppOrderSaveList.add(wwacao);
            });
        });
        if (!CollectionUtils.isEmpty(workWechatApplicationCustomizedAppOrderSaveList)) {
            Lists.partition(workWechatApplicationCustomizedAppOrderSaveList, 100).forEach(super::saveOrUpdateBatch);
        }
        //有下一页继续执行订单列表获取
        Integer hasMore = responseBody.getInteger("has_more");
        if (!Objects.isNull(hasMore) && hasMore >= 1) {
            setCommonRequestParams(requestParams);
            requestParams.put("cursor", responseBody.getString("next_cursor"));
            getAndSaveWorkWechatApplicationOrderBatch(requestParams);
        }
    }

    public void reloadOtherData() {
        List<WorkWechatApplicationCustomizedAppOrder> workWechatApplicationCustomizedAppOrders = baseMapper.selectList(new LambdaQueryWrapper<WorkWechatApplicationCustomizedAppOrder>()
            .eq(WorkWechatApplicationCustomizedAppOrder::getOrderType, 1)
            .isNull(WorkWechatApplicationCustomizedAppOrder::getTakeEffectDate)
        );
        List<WorkWechatApplicationCustomizedAppOrder> reloadDataList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(workWechatApplicationCustomizedAppOrders)) {
            workWechatApplicationCustomizedAppOrders.forEach(workWechatApplicationCustomizedAppOrder -> {
                WorkWechatApplicationCustomizedAppOrder wwacao = this.getNearNowWorkWechatApplicationOrder(workWechatApplicationCustomizedAppOrder.getCorpId(), workWechatApplicationCustomizedAppOrder.getSuiteid(), Instant.now());
                if (Objects.isNull(wwacao)) {
                    return;
                }
                reloadDataList.add(workWechatApplicationCustomizedAppOrder
                    .setDurationDays(wwacao.getDurationDays())
                    .setTakeEffectDate(wwacao.getTakeEffectDate())
                    .setTakeEffectDateTime(wwacao.getTakeEffectDateTime())
                    .setActualTakeEffectDate(wwacao.getActualTakeEffectDate())
                );
            });
        }
        if (!CollectionUtils.isEmpty(reloadDataList)) {
            Lists.partition(reloadDataList, 100).forEach(super::saveOrUpdateBatch);
        }
    }

    private static void setCommonRequestParams(JSONObject requestParams) {
        requestParams.put("business_type", 2);
        requestParams.put("ts", Instant.now().getEpochSecond());
        requestParams.put("nonce_str", CommonUtil.getUuidReplaceAll());
    }

    public static void main(String[] args) {
//        String secret = "";
//        JSONObject requestParams = new JSONObject();
//        setCommonRequestParams(requestParams);
//        requestParams.put("start_time", DateTimeUtil.stringToInstant("2025-05-21 00:00:00").getEpochSecond());
//        requestParams.put("end_time", DateTimeUtil.stringToInstant("2025-05-22 00:00:00").getEpochSecond());
//        requestParams.put("limit", 100);
//        requestParams.put("sig", new WorkWechatSignatureGetter().hmacSha256(secret, requestParams));
//        System.out.println(requestParams);

//        String orderId = "N00000D98D766682DCF0B0270FA3B";
//        JSONObject orderDetailRequestParams = new JSONObject();
//        orderDetailRequestParams.put("ts", Instant.now().getEpochSecond());
//        orderDetailRequestParams.put("nonce_str", CommonUtil.getUuidReplaceAll());
//        orderDetailRequestParams.put("order_id", orderId);
//        orderDetailRequestParams.put("sig", new WorkWechatSignatureGetter().hmacSha256(secret, orderDetailRequestParams));
        Long create_time = 1747832587L;
        System.out.println(DateTimeUtil.instantPaserToyyyyMMddHHmmss(DateTimeUtil.long2Instant(create_time * 1000)));

        Integer duration_days = 31;
        String take_effect_date = "20250521";
        System.out.println("take_effect_date：" + take_effect_date);
        Instant take_effect_date_time = DateTimeUtil.convertTakeEffectDateFormat(take_effect_date);
        System.out.println("take_effect_date_time：" + DateTimeUtil.instantPaserToyyyyMMddHHmmss(take_effect_date_time));
        if (!Objects.isNull(take_effect_date_time)) {
            String actual_take_effect_date = DateTimeUtil.instantPaserToyyyyMMddHHmmss(DateTimeUtil.getAfterDayInstantByDays(take_effect_date_time, duration_days));
            System.out.println("actual_take_effect_date：" + actual_take_effect_date);
        }

    }

    /**
     * 获取有效期内可用人数
     */
    public Integer getApplicationUserCount(String corpId, String suiteId, Instant nowTime) {
        return baseMapper.getApplicationUserCount(corpId, suiteId, nowTime);
    }

    /**
     * 应用到期时间
     */
    public WorkWechatApplicationCustomizedAppOrder getMaxActualTakeEffectDate(String corpId, String suiteId) {
        return baseMapper.getMaxActualTakeEffectDate(corpId, suiteId, Instant.now());
    }


    public static void main(String[] args) {
        String secret = "oHvZo-bupTV71mXlox_Ir2CNSCwtNvLBFx-nA-PTjdc";
        String providerAccessToken = "SgHhTDvxXVK5PZR9rncGl0JcO5lKOinL4tb8b8vpMvRpYE5ZGEdyHF2xBaRCp15GHKWp6IZYmRvj8yp8ZKnNawj68Gf0UmF4ERfZmfrgZ2VW-RTTfdbKU19iuZzsV4uZ";
        JSONObject requestParams = new JSONObject();
        setCommonRequestParams(requestParams);
        requestParams.put("start_time", DateTimeUtil.stringToInstant("2025-05-21 00:00:00").getEpochSecond());
        requestParams.put("end_time", DateTimeUtil.stringToInstant("2025-05-22 00:00:00").getEpochSecond());
        requestParams.put("limit", 100);
        requestParams.put("sig", new WorkWechatSignatureGetter().hmacSha256(secret, requestParams));

        System.out.println(requestParams);


        String orderId = "N00000D98D766682DCF0B0270FA3B";
        JSONObject orderDetailRequestParams = new JSONObject();
        orderDetailRequestParams.put("ts", Instant.now().getEpochSecond());
        orderDetailRequestParams.put("nonce_str", CommonUtil.getUuidReplaceAll());
        orderDetailRequestParams.put("order_id", orderId);
        orderDetailRequestParams.put("sig", new WorkWechatSignatureGetter().hmacSha256(secret, orderDetailRequestParams));
        System.out.println(orderDetailRequestParams);

    }

}
