package ai.yiye.agent.landingpage.service;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.upload.FileUpload;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.LandingPageWechatCustomerService;
import ai.yiye.agent.domain.RobotCustomerDynamicContact;
import ai.yiye.agent.domain.User;
import ai.yiye.agent.domain.UserOperationLogDetail;
import ai.yiye.agent.domain.bo.SyntheticBackgroundImgBO;
import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.dto.*;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.landingpage.*;
import ai.yiye.agent.domain.landingpage.dto.DynamicBatchDeleteDTO;
import ai.yiye.agent.domain.landingpage.dto.RobotCustomerContactDto;
import ai.yiye.agent.domain.landingpage.dto.RobotCustomerDynamicContactDTO;
import ai.yiye.agent.domain.util.EnumUtil;
import ai.yiye.agent.landingpage.config.IdentifyQrcodeCacheConfig;
import ai.yiye.agent.landingpage.config.LandingPageWechatCustomerContactConfig;
import ai.yiye.agent.landingpage.dto.LandingPageWechatCustomerServiceRedisDto;
import ai.yiye.agent.landingpage.dto.OfficialCustomerContactNoticeDto;
import ai.yiye.agent.landingpage.dto.RobotDynamicQrCodeCheckDTO;
import ai.yiye.agent.landingpage.enums.RobotContactWayPrefixEnum;
import ai.yiye.agent.landingpage.mapper.RobotDynamicCustomerContactGenerateRecordMapper;
import ai.yiye.agent.landingpage.redis.LandingPageWechatCustomerServiceRedis;
import ai.yiye.agent.landingpage.sender.RobotCustomerContactSender;
import ai.yiye.agent.landingpage.sender.RobotDynamicCustomerContactGenerateSender;
import ai.yiye.agent.landingpage.utils.FileUtil;
import ai.yiye.agent.landingpage.vo.ContactRemoteClearVO;
import ai.yiye.agent.weixin.client.WorkWeixinApiClient;
import ai.yiye.agent.weixin.domain.CustomerContactAddWayRequestBody;
import ai.yiye.agent.weixin.domain.CustomerContactAddWayResponseBody;
import ai.yiye.agent.weixin.domain.CustomerContactDelWayRequestBody;
import ai.yiye.agent.weixin.domain.TempMaterialResponseBody;
import ai.yiye.agent.weixin.enums.EnterpriseWechatGlobalErrorCode;
import ai.yiye.agent.weixin.exception.MarketingApiException;
import ai.yiye.agent.weixin.util.WxErrorMessageUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@Slf4j
@Service
@DS(DbConstants.POSTGRESQL_DEFAULT)
public class RobotDynamicCustomerContactGenerateRecordService extends ServiceImpl<RobotDynamicCustomerContactGenerateRecordMapper, RobotDynamicCustomerContactGenerateRecord> {

    @Resource
    private LandingPageWechatCustomerServiceService landingPageWechatCustomerServiceService;

    @Resource
    private EnterpriseWechatService enterpriseWechatService;

    @Resource
    private EnterpriseWechatCustomerContactService enterpriseWechatCustomerContactService;

    @Resource
    private RobotCustomerDynamicContactService robotCustomerDynamicContactService;

    @Resource
    private LandingPageWechatCustomerContactConfig landingPageWechatCustomerContactConfig;

    @Resource
    private RobotCustomerContactService robotCustomerContactService;

    @Resource
    private OfficialWechatCustomerContactService officialWechatCustomerContactService;

    @Resource
    private RobotCustomerContactSender robotCustomerContactSender;

    @Resource
    private WorkWechatUserVisibleRangeRobotCustomerContactService workWechatUserVisibleRangeRobotCustomerContactService;

    @Resource
    private EnterpriseWechatRobotCustomerService enterpriseWechatRobotCustomerService;

    @Resource
    private LandingPageWechatCustomerServiceRedis landingPageWechatCustomerServiceRedis;

    @Resource
    private RobotDynamicCustomerContactGenerateSender robotDynamicCustomerContactGenerateSender;

    @Resource
    private UserOperationLogDetailService userOperationLogDetailService;

    @Autowired
    private RedisTemplate<String, Object> defaultObjectRedisTemplate;

    @Resource
    private EnterpriseTempMaterialService enterpriseTempMaterialService;


    @Resource
    private FileUpload fileUpload;

    @Resource
    private RedisTemplate<String, Object> objectRedisTemplate;

    private static final Integer ONE_DAY = 1;

    @Resource
    private IdentifyQrcodeCacheConfig identifyQrcodeCacheConfig;

    @Resource
    private RedissonClient redissonClient;

    @Resource(name = "BeanCleanUsedOfficialCustomerContactQiniuThreadPool")
    private Executor beanCleanUsedOfficialCustomerContactQiniuThreadPool;


    @Resource
    private RobotDynamicCustomerContactDeleteFailRecordService robotDynamicCustomerContactDeleteFailRecordService;

    @Resource
    private EnterpriseWechatsPmpRelService enterpriseWechatsPmpRelService;


    @Autowired
    private WorkWeixinApiClient workWeixinApiClient;



    private static final List<LandingPageWechatCustomerContactStatus> CUSTOMER_CONTACT_CLEAN_OLD_DATA = new ArrayList<LandingPageWechatCustomerContactStatus>() {{
        add(LandingPageWechatCustomerContactStatus.GENERATED);
        add(LandingPageWechatCustomerContactStatus.CREATION_FAILURE);
    }};

    /**
     * 实现动态渠道二维码（微信客服机器人内发活码）生成
     * @param dto
     */
    public void batchAddDynamic(RobotCustomerContactDto dto) {
        log.info("实现动态渠道二维码（微信客服机器人内发活码）生成，入参dto = {} ", JSONObject.toJSONString(dto));
        List<Long> landingPageWechatCustomerServiceIds = dto.getIds();
        Long advertiserAccountGroupId = dto.getAdvertiserAccountGroupId();
        boolean batchOperationFlag  = Objects.nonNull(dto.getBatchOperationFlag()) && dto.getBatchOperationFlag();
        if (CollectionUtils.isEmpty(landingPageWechatCustomerServiceIds)) {
            throw new RestException("客服不可为空!");
        }

        //1.295.0放开批量生成的限制
//        if (landingPageWechatCustomerServiceIds.size() > 1){
//            //1.272.0迭代暂时只需要单个生成动态渠道二维码
//            throw new RestException("暂时不支持批量生成动态渠道二维码!");
//        }

        List<LandingPageWechatCustomerService> list = landingPageWechatCustomerServiceService.listByIds(landingPageWechatCustomerServiceIds);
        Optional.ofNullable(list).filter(e -> CollectionUtils.isNotEmpty(e)).orElseThrow(() -> new RestException("客服不存在!"));


        Set<String> corpIds = list.stream().map(LandingPageWechatCustomerService::getCorpId)
                .filter(e -> StringUtils.isNotBlank(e))
                .collect(Collectors.toSet());

        //批量操作的时候，会指定选择的企业微信
        log.info("前端传的企业微信corpId的集合:{}", dto.getCorpIds());
        if(!CollectionUtils.isEmpty(dto.getCorpIds())){
            corpIds.addAll(dto.getCorpIds());
        }

        Optional.ofNullable(corpIds).filter(e -> CollectionUtils.isNotEmpty(e)).orElseThrow(() -> new RestException("请设置企微成员!"));

        String robotCorpId = dto.getRobotCorpId();
        if (StringUtils.isNotBlank(robotCorpId)){
            log.info("微信客服机器人所属的企业微信robotCorpId = {}", robotCorpId);
            corpIds.add(robotCorpId);
        }

        //查询该项目的微信客服机器人绑定的企业微信列表
        boolean robotConfigSaveFlag = dto.getRobotConfigSaveFlag();
        log.info("是否是编辑微信客服机器人的标识, robotConfigSaveFlag = {}, advertiserAccountGroupId = {}", robotConfigSaveFlag, advertiserAccountGroupId);
        //编辑微信客服机器人的时候，只需要生成这个企微的素材就行
        if (Objects.nonNull(robotConfigSaveFlag) && !robotConfigSaveFlag && Objects.nonNull(advertiserAccountGroupId) && !batchOperationFlag) {
            Set<String> robotRelateCorpIds = enterpriseWechatRobotCustomerService.getCorpIdByPmpId(advertiserAccountGroupId);
            if (!robotRelateCorpIds.isEmpty()) {
                log.info("项目{}下，微信客服机器人已经关联的企业微信集合 = {}", advertiserAccountGroupId, robotRelateCorpIds);
                corpIds.addAll(robotRelateCorpIds);
            }
        }

        //查询当前项目对应代开发应用
        List<EnterpriseWechat> enterpriseWechatList = corpIds.stream().map(e -> {
            EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatCacheByCorpId(e);
            if (Objects.nonNull(enterpriseWechat)) {
                WechatCustomerContactStateInitFlag wechatCustomerContactStateInitFlag = enterpriseWechat.getWechatCustomerContactStateInitFlag();
                if (Objects.nonNull(wechatCustomerContactStateInitFlag) && !WechatCustomerContactStateInitFlag.NORMAL.equals(wechatCustomerContactStateInitFlag)) {
                    log.error("企业微信{}未初始化，请前往「落地页 - 页面配置 - 企业微信自建应用代开发授权」进行初始化操作", e);
                    return null;
                }
                //标记此企微已经使用过联系我二维码
                enterpriseWechatCustomerContactService.saveOrUpdateByCorpId(enterpriseWechat.getCorpid());
                return enterpriseWechat;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (enterpriseWechatList.isEmpty()) {
            throw new RestException("企微代开发应用不存在!");
        }
        //处理生成的逻辑
        String agentId = TenantContextHolder.get();
        list.forEach(e -> {
            try {
                TenantContextHolder.set(agentId);
                for (EnterpriseWechat enterpriseWechat : enterpriseWechatList) {
                    //执行生成二维码逻辑
                    log.info("客服{}，正在生成企业微信corpId = {}的活码素材", e.getId(), enterpriseWechat.getCorpid() );

                    //企业微信名称
                    String wechatName = e.getWechatName();
                    String enterpriseWechatCorpId = enterpriseWechat.getCorpid();
                    String corpId = e.getCorpId();
                    log.info("客服{}进行活码素材初始化，客服绑定的corpId = {}, enterpriseWechatCorpId = {}", e.getId(), corpId, enterpriseWechatCorpId);
                    //判断这个用户是否绑定这个企业微信
                    if (!Objects.equals(corpId, enterpriseWechatCorpId)) {
                        resolveGenerateDynamicQrCode(e, dto, enterpriseWechat, false, batchOperationFlag);
                        log.info("客服{}, id = {}绑定的企业微信是其他主体的，直接执行生成活码素材, corpId = {}, enterpriseWechatCorpId = {}, dtoNew = {}", wechatName, e.getId(), corpId, enterpriseWechatCorpId, JSONObject.toJSONString(dto));
                    }else {
                        log.info("客服{}, id = {}绑定的企业微信是列表上面显示的，直接执行生成活码素材, corpId = {}, enterpriseWechatCorpId = {}, dto = {}", wechatName, e.getId(), corpId, enterpriseWechatCorpId, JSONObject.toJSONString(dto));
                        resolveGenerateDynamicQrCode(e, dto, enterpriseWechat, true, batchOperationFlag);
                    }
                }
            } catch (Exception exception) {
                log.info("==>批量生成机器人活码错误!客服:{}", JSON.toJSONString(e), exception);
            } finally {
                TenantContextHolder.clearContext();
            }
        });

    }

    /**
     * 处理具体客服对应的动态渠道二维码（微信机器人内加粉）
     * @param landingPageWechatCustomerService 微信客服
     * @param dto 前端传参
     * @param enterpriseWechat 企业微信信息
     * @param changeFlag 是否需要修改微信客服信息配置
     */
    public void resolveGenerateDynamicQrCode(LandingPageWechatCustomerService landingPageWechatCustomerService, RobotCustomerContactDto dto, EnterpriseWechat enterpriseWechat,boolean changeFlag, boolean batchOperationFlag){


        log.info("处理具体客服对应的动态渠道二维码（微信机器人内加粉）,入参dto = {} ", JSONObject.toJSONString(dto));

        if (Objects.isNull(landingPageWechatCustomerService)){
            throw new RestException("微信客服信息不能为空对象!");
        }

        String wechatUserId = landingPageWechatCustomerService.getWechatUserId();
        LandingPageWechatCustomerContactStatus landingPageWechatCustomerContactStatus = landingPageWechatCustomerService.getRobotCustomerDynamicContactStatus();
        if (LandingPageWechatCustomerContactStatus.IN_FORMATION.equals(landingPageWechatCustomerContactStatus)) {
            log.info("动态渠道二维码（微信机器人内加粉）-客服userid:{}正在生成中，请勿多次提交", wechatUserId);
            return;
        }
        LandingPageWechatCustomerContactVerifyStatus robotCustomerDynamicContactVerify = dto.getRobotCustomerDynamicContactVerify();

        Long advertiserAccountGroupId = dto.getAdvertiserAccountGroupId();
        String corpId = enterpriseWechat.getCorpid();

        //是否生成批量二维码
        boolean generateFlag = true;
        boolean robotConfigSaveFlag = Objects.nonNull(dto.getRobotConfigSaveFlag()) &&  dto.getRobotConfigSaveFlag();

        if (changeFlag) {
            //生成微信客服列表预览的动态渠道二维码，因为同一个客服需要上传素材到不同的企业微信，所以根据changeFlag判断需要生成微信客服列表的预览图片，其他企业微信的只生成素材，不能生成预览的图片
            if (Objects.isNull(robotConfigSaveFlag) || Objects.equals(Boolean.FALSE, robotConfigSaveFlag)){
                //保存机器人配置的时候，不要去动任何微信客服列表的东西，他只是初始化素材，对应的素材个数校验会在后面进行检查
                log.info("保存微信客服的时候，生成预览图, robotConfigSaveFlag = {}, changeFlag = {}", robotConfigSaveFlag, changeFlag);
                generateFlag = this.generatePreViewRobotQrCode(landingPageWechatCustomerService, dto, enterpriseWechat, changeFlag);
            }
        }
        log.info("处理具体客服对应的动态渠道二维码（微信机器人内加粉）,判断是否需要批量生成, generateFlag = {}, changeFlag = {}, robotConfigSaveFlag = {}", generateFlag, changeFlag, robotConfigSaveFlag);

        if (generateFlag){

            String ip = dto.getIp();
            User user = dto.getUser();
            log.info("处理具体客服对应的动态渠道二维码（微信机器人内加粉）,user = {}", JSONObject.toJSONString(user));
            //异步生成当前客服多个二维码
            RobotCustomerDynamicContactDTO robotCustomerDynamicContactDTO = new RobotCustomerDynamicContactDTO();
            robotCustomerDynamicContactDTO.setEnterpriseWechat(enterpriseWechat)
                    .setLandingPageWechatCustomerService(landingPageWechatCustomerService)
                    .setAdvertiserAccountGroupId(advertiserAccountGroupId)
                    .setWechatCustomerContactVerify(robotCustomerDynamicContactVerify)
                    .setRobotCustomerContactDto(dto)
                    .setChangeFlag(changeFlag)
                    .setRobotConfigSaveFlag(dto.getRobotConfigSaveFlag())
                    .setBatchOperationFlag(batchOperationFlag)
                    .setUser(user)
                    .setIp(ip);

            robotDynamicCustomerContactGenerateSender.sendGenerateAndCleanQrCodeForRobotCustomerService(robotCustomerDynamicContactDTO);
        }
    }

    /**
     * 生成客户列表中展示的联系我二维码
     * changeFlag: 编辑保存机器人配置的标识
     */
    public boolean generatePreViewRobotQrCode(LandingPageWechatCustomerService landingPageWechatCustomerService, RobotCustomerContactDto dto, EnterpriseWechat enterpriseWechat,boolean changeFlag){


        boolean generateFlag = true;
        boolean jobInitFlag = dto.getJobInitFlag();
        CustomerContactAddWayResponseBody customerContactAddWayResponseBody;
        LandingPageWechatCustomerContactVerifyStatus verifyStatus = dto.getRobotCustomerDynamicContactVerify();
        Long id = landingPageWechatCustomerService.getId();
        String wechatUserName = landingPageWechatCustomerService.getWechatUserName();
        String corpid = enterpriseWechat.getCorpid();
        String wechatUserId = landingPageWechatCustomerService.getWechatUserId();
        LandingPageWechatCustomerService param = new LandingPageWechatCustomerService();
        param.setId(id).setRobotCustomerDynamicContactVerify(verifyStatus);
        boolean robotConfigSaveFlag = dto.getRobotConfigSaveFlag();
        log.info("是否是定时任务初始化的标识, jobInitFlag = {}, verifyStatus = {},robotConfigSaveFlag = {}, changeFlag = {}", jobInitFlag, verifyStatus, robotConfigSaveFlag, changeFlag);
        try {

            if (!robotConfigSaveFlag && changeFlag) {
                boolean res = landingPageWechatCustomerServiceService.updateRobotCustomerDynamicContactStatusById(id);
                log.info("微信客服配置生成动态渠道二维码，生成状态置为生成中，id = {}, res = {}", id, res);
            }

            String accessToken =  enterpriseWechatService.getAccessTokenByCorpId(corpid);
            //生成客户列表中展示的联系我二维码
            String state = RobotContactWayPrefixEnum.DYNAMIC_QR_CODE_PREVIEW.getPrefix() + RandomUtil.randomString(6) + DateUtil.format(new DateTime(), "mmss");
            state = String.format(state, landingPageWechatCustomerContactConfig.getEnv());
            log.info("生成客户列表中展示的联系我二维码(微信客服机器人动态活码),自归因参数state = {}", state);
            //生成动态渠道二维码
            customerContactAddWayResponseBody = robotCustomerContactService.executeGenerateQrCode(wechatUserId, accessToken, state, verifyStatus);
            String qrCode = customerContactAddWayResponseBody.getQrCode();
            String path = landingPageWechatCustomerContactConfig.getRobotCustomerDynamicContactPath() + state + ".png";
            //合并二维码并上传
            EnterpriseWechatTempMaterial material = robotCustomerContactService.drawImageInit(dto, qrCode, path, enterpriseWechat);
            if (Objects.isNull(material)){
                log.error("==>初始化微信客服列表背景图异常,landingPageWechatCustomerService = {}",landingPageWechatCustomerService);
                return false;
            }
            String url = material.getMaterialPath();
            Long mediaId = material.getId();
            log.info("生成客户列表中展示的联系我二维码(微信客服机器人动态活码),url = {}, material = {}", url, JSONObject.toJSONString(material));
            param.setRobotCustomerDynamicContactStatus(LandingPageWechatCustomerContactStatus.GENERATED)
                .setRobotCustomerDynamicContactQrCode(qrCode)
                .setRobotCustomerDynamicContactBackgroundUrl(url)
                .setRobotCustomerDynamicContactQiniuPath(path)
                .setRobotCustomerDynamicContactConfigId(customerContactAddWayResponseBody.getConfigId())
                .setRobotCustomerDynamicContactState(state)
                .setRobotCustomerDynamicContactVerify(verifyStatus)
                .setRobotCustomerDynamicContactMaterialId(mediaId);
            landingPageWechatCustomerService.setRobotCustomerDynamicContactQiniuPath(path).setRobotCustomerDynamicContactBackgroundUrl(url);
            //修改公库记录且通知到各账户下
            OfficialCustomerContactNoticeDto officialCustomerContactNoticeDto = new OfficialCustomerContactNoticeDto();
            officialCustomerContactNoticeDto.setCorpid(corpid).setState(state).setQrCode(qrCode)
                .setResultUrl(url).setMediaId(String.valueOf(mediaId)).setConfigId(customerContactAddWayResponseBody.getConfigId())
                .setErrorMessage(null).setLandingPageWechatCustomerContactStatus(LandingPageWechatCustomerContactStatus.GENERATED)
                .setLandingPageWechatCustomerContactVerify(dto.getRobotCustomerContactVerify());

            //修改公库记录且通知到各账户下
            if (!robotConfigSaveFlag && changeFlag) {
                this.synUpdatePublicVisiable(landingPageWechatCustomerService, officialCustomerContactNoticeDto);
            }
        } catch (MarketingApiException e) {
            generateFlag = false;
            log.warn("==>生成机器人活码(动态渠道二维码)失败,marketing异常!客服名称:{},userid:{}", wechatUserName, wechatUserId, e);
            String message = e.getMessage();
            String errorMessage = WxErrorMessageUtil.decorationErrorMessage(true, message);
            param.setRobotCustomerDynamicContactFailureReason(errorMessage);
            param.setRobotCustomerDynamicContactStatus(LandingPageWechatCustomerContactStatus.CREATION_FAILURE);
            Integer errcode = e.getErrcode();
            EnterpriseWechatGlobalErrorCode enterpriseWechatGlobalErrorCode = EnumUtil.getByCode(errcode, EnterpriseWechatGlobalErrorCode.class);
            if (EnterpriseWechatGlobalErrorCode.CUSTOMER_CONTACT_ADDED_ITEMS_REACHES_THE_UPPER_LIMIT.equals(enterpriseWechatGlobalErrorCode)) {
                enterpriseWechatService.update(new LambdaUpdateWrapper<EnterpriseWechat>()
                        .set(EnterpriseWechat::getWechatCustomerContactStateInitFlag, WechatCustomerContactStateInitFlag.ANOMALY)
                        .set(EnterpriseWechat::getWechatCustomerContactStateInitFailureReason, enterpriseWechatGlobalErrorCode.getMessage())
                        .eq(EnterpriseWechat::getId, enterpriseWechat.getId()));
                //清除企微代开发记录缓存
                enterpriseWechatService.clearEnterpriseWechatCache(enterpriseWechat.getCorpid());
            }
            officialWechatCustomerContactService.sendFeishuMessage(enterpriseWechat.getCorpName(), landingPageWechatCustomerService.getId(), landingPageWechatCustomerService.getWechatUserId(),
                    landingPageWechatCustomerService.getAdvertiserAccountGroupId(), errcode, errorMessage);
        } catch (Exception e) {
            log.error("==>微信客服机器人内生成动态渠道二维码失败,exception异常!客服名称:{},userid:{}", wechatUserName, wechatUserId, e);
            String errorInfo = Objects.isNull(e.getCause()) ? e.getMessage() : e.getCause().getMessage();
            param.setRobotCustomerDynamicContactFailureReason(errorInfo);
            param.setRobotCustomerDynamicContactStatus(LandingPageWechatCustomerContactStatus.CREATION_FAILURE);
        }
        //更新微信客服的动态渠道二维码（微信机器人内）的相关配置
        log.info("更新微信客服的动态渠道二维码（微信机器人内）的相关配置, robotConfigSaveFlag = {},是否需要修改基础信息标识,changeFlag = {},  dto = {}", robotConfigSaveFlag, changeFlag,  dto);
        if (!robotConfigSaveFlag && changeFlag) {
            //robotConfigSaveFlag为true表示这个是保存微信客服配置的时候触发的，不需要更新微信客服表的信息
            log.info("更新微信客服的动态渠道二维码（微信机器人内）的相关配置, 更新客服信息, param = {}", param);
            landingPageWechatCustomerServiceService.updateById(param);
        }
        if (Objects.equals(LandingPageWechatCustomerContactStatus.CREATION_FAILURE, param.getRobotCustomerDynamicContactStatus())){
            log.info("微信客服机器人内生成动态渠道二维码失败，将 generateFlag 设置为false");
            generateFlag = false;
        }
        return generateFlag;
    }


    /**
     * 修改公库记录且通知到各账户下
     */
    public void synUpdatePublicVisiable(LandingPageWechatCustomerService landingPageWechatCustomerService, OfficialCustomerContactNoticeDto officialCustomerContactNoticeDto){
        String agentId = TenantContextHolder.get();
        String wechatUserId = landingPageWechatCustomerService.getWechatUserId();
        String corpid = officialCustomerContactNoticeDto.getCorpid();
        String state = officialCustomerContactNoticeDto.getState();
        String qrCode = officialCustomerContactNoticeDto.getQrCode();
        String resultUrl = officialCustomerContactNoticeDto.getResultUrl();
        String mediaId = officialCustomerContactNoticeDto.getMediaId();
        String configId = officialCustomerContactNoticeDto.getConfigId();
        String errorMessage = officialCustomerContactNoticeDto.getErrorMessage();
        String robotCustomerDynamicContactQiniuPath = landingPageWechatCustomerService.getRobotCustomerDynamicContactQiniuPath();
        LandingPageWechatCustomerContactStatus landingPageWechatCustomerContactStatus = officialCustomerContactNoticeDto.getLandingPageWechatCustomerContactStatus();
        LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerify = officialCustomerContactNoticeDto.getLandingPageWechatCustomerContactVerify();
        //公库记录生成或修改
        WorkWechatUserVisibleRangeRobotCustomerContact  serviceOne = new WorkWechatUserVisibleRangeRobotCustomerContact()
            .setCorpId(corpid).setUserId(wechatUserId)
            .setGenerateStatus(landingPageWechatCustomerContactStatus)
            .setQrCode(qrCode)
            .setConfigId(configId)
            .setFailureReason(errorMessage)
            .setContactVerify(landingPageWechatCustomerContactVerify)
            .setCustomerContactBackgroundUrl(resultUrl)
            .setMaterialId(mediaId)
            .setCustomerContactState(state)
            .setQiniuPath(robotCustomerDynamicContactQiniuPath)
            .setAgentId(agentId);
        workWechatUserVisibleRangeRobotCustomerContactService.insertOrUpdateByCorpidAndUserId(serviceOne);
        //通知到各账户同一客服下
        RobotWechatCustomerDynamicContactMessage message = new RobotWechatCustomerDynamicContactMessage();
        message
            .setRobotWechatCustomerContactStatus(landingPageWechatCustomerContactStatus)
            .setRobotWechatCustomerContactState(state)
            .setRobotWechatCustomerContactQrCode(qrCode)
            .setRobotResultUrl(resultUrl)
            .setRobotWechatCustomerContactMaterialId(mediaId)
            .setRobotWechatCustomerContactConfigId(configId)
            .setCorpId(corpid)
            .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerService.getId())
            .setLandingPageWechatCustomerServiceWechatUserId(wechatUserId)
            .setWechatUserName(landingPageWechatCustomerService.getWechatUserName())
            .setRobotWechatCustomerContactFailureReason(errorMessage)
            .setRobotWechatCustomerContactVerify(landingPageWechatCustomerContactVerify)
            .setAdvertiserAccountGroupId(landingPageWechatCustomerService.getAdvertiserAccountGroupId())
            .setRobotWechatCustomerContactQiniuPath(robotCustomerDynamicContactQiniuPath);
        robotDynamicCustomerContactGenerateSender.sendCustomerContactGenerateStatusChange(message);
    }

    /**
     * 保存动态渠道二维码相关的设置(微信客服机器人内发送活码)
     * @param dto 配置传参
     */
    public void saveRobotCustomerDynamicContactConfig(RobotCustomerContactDto dto){
        for (Long landingPageWechatCustomerServiceId : dto.getIds()) {
            RobotCustomerDynamicContact customerContact = new RobotCustomerDynamicContact();
            customerContact.setLandingPageWechatCustomerServiceId(landingPageWechatCustomerServiceId)
                .setRobotCustomerDynamicContactVerify(dto.getRobotCustomerDynamicContactVerify())
                .setBackgroundUrl(dto.getBackgroundUrl())
                .setBackgroundWidth(dto.getBackgroundWidth())
                .setBackgroundHeight(dto.getBackgroundHeight())
                .setQrCodeWidth(dto.getQrCodeWidth())
                .setQrCodeHeight(dto.getQrCodeHeight())
                .setQrCodeIndexLeft(dto.getQrCodeIndexLeft())
                .setQrCodeIndexTop(dto.getQrCodeIndexTop());
            landingPageWechatCustomerServiceRedis.deleteRobotCustomerDynamicContactCache(landingPageWechatCustomerServiceId);
            //保存动态渠道二维码相关的设置
            robotCustomerDynamicContactService.saveOrUpdateByServiceId(customerContact);
            //设置缓存，后续生成多个活码的时候直接取最新的缓存
            RobotCustomerContactDto robotCustomerDynamicContactDTO = new RobotCustomerContactDto();
            BeanUtils.copyProperties(customerContact, robotCustomerDynamicContactDTO);
            landingPageWechatCustomerServiceRedis.setRobotCustomerDynamicContactBasicConfigCache(robotCustomerDynamicContactDTO, landingPageWechatCustomerServiceId);
        }
    }



    /**
     * 初始化创建联系我二维码生成,清除旧数据处理
     */
    public void batchGenerateQrcode(RobotCustomerDynamicContactDTO robotCustomerDynamicContactDTO) {

        LandingPageWechatCustomerService landingPageWechatCustomerService = robotCustomerDynamicContactDTO.getLandingPageWechatCustomerService();
        Long landingPageWechatCustomerServiceId = landingPageWechatCustomerService.getId();
        String wechatUserId = landingPageWechatCustomerService.getWechatUserId();
        String wechatUserName = landingPageWechatCustomerService.getWechatUserName();
        String wechatUserCorpId = landingPageWechatCustomerService.getCorpId();
        User user = robotCustomerDynamicContactDTO.getUser();
        Boolean batchOperationFlag = robotCustomerDynamicContactDTO.getBatchOperationFlag();
        Long advertiserAccountGroupId = robotCustomerDynamicContactDTO.getAdvertiserAccountGroupId();
        Boolean robotConfigSaveFlag = Objects.nonNull(robotCustomerDynamicContactDTO.getRobotConfigSaveFlag()) && Objects.equals(robotCustomerDynamicContactDTO.getRobotConfigSaveFlag(), true) ;

        EnterpriseWechat enterpriseWechat = robotCustomerDynamicContactDTO.getEnterpriseWechat();

        LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerify = robotCustomerDynamicContactDTO.getWechatCustomerContactVerify();

        String ip = robotCustomerDynamicContactDTO.getIp();

        //不是保存微信客服机器人配置页面的时候，查询清除旧二维码
        if (!robotConfigSaveFlag) {
            this.searchOldContactAndClean(landingPageWechatCustomerService, landingPageWechatCustomerContactVerify, enterpriseWechat);
        }

        //客服旧状态
        LandingPageWechatCustomerContactStatus oldLandingPageRobotCustomerDynamicContactStatus = landingPageWechatCustomerService.getRobotCustomerDynamicContactStatus();
        LandingPageWechatCustomerContactVerifyStatus oldLandingPageRobotCustomerDynamicContactVerify = landingPageWechatCustomerService.getRobotCustomerDynamicContactVerify();
        String corpid = enterpriseWechat.getCorpid();
        String accessToken = enterpriseWechat.getAccessToken();
        Long enterpriseWechatId = enterpriseWechat.getId();
        boolean hasKey = landingPageWechatCustomerServiceRedis.hasRobotCustomerDynamicContactCache(corpid, wechatUserId);
        log.info("客服所属的企业微信：{}，要上传素材的企业微信：{}, hasKey = {}", wechatUserCorpId, corpid, hasKey);
        int generateNum = landingPageWechatCustomerContactConfig.getRobotCustomerDynamicGenerateNum();
        boolean jobInitFlag = false;

        Long customerContactCacheSize = 0L;
        if (hasKey) {

            customerContactCacheSize = landingPageWechatCustomerServiceRedis.getRobotCustomerDynamicContactCacheSize(corpid, wechatUserId);
            log.info("缓存中已存在此客服机器人内渠道二维码的缓存,查询此客服已经生成多少二维码,wechatUserId:{}, corpid = {},customerContactCacheSize = {}, generateNum = {}", wechatUserId, corpid, customerContactCacheSize, generateNum);
            if (customerContactCacheSize >= generateNum) {
                log.info("缓存中已存在此客服机器人内渠道二维码的缓存，数量为:{} 大于等于设置的最大值:{},无需新增多个联系我二维码,直接修改为生成完成，且清理客服分组缓存!wechatUserId:{}", customerContactCacheSize, generateNum, wechatUserId);
                //修改客服记录生成状态
                landingPageWechatCustomerServiceService.update(new LambdaUpdateWrapper<LandingPageWechatCustomerService>()
                    .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactStatus, LandingPageWechatCustomerContactStatus.GENERATED)
                    .eq(LandingPageWechatCustomerService::getId, landingPageWechatCustomerServiceId));
                //清除与当前客服相关联的客服分组缓存
                landingPageWechatCustomerServiceService.deleteCacheByServiceIds(Arrays.asList(landingPageWechatCustomerServiceId));
                //修改公库记录且通知到各账户下
                OfficialCustomerContactNoticeDto officialCustomerContactNoticeDto = new OfficialCustomerContactNoticeDto();
                officialCustomerContactNoticeDto.setCorpid(corpid).setState(null).setQrCode(null)
                    .setResultUrl(null).setMediaId(null).setConfigId(null)
                    .setErrorMessage(null).setLandingPageWechatCustomerContactStatus(LandingPageWechatCustomerContactStatus.GENERATED)
                    .setLandingPageWechatCustomerContactVerify(landingPageWechatCustomerContactVerify);
                boolean changeFlag = robotCustomerDynamicContactDTO.getChangeFlag();
                if (changeFlag) {
                    this.synUpdatePublicVisiable(landingPageWechatCustomerService, officialCustomerContactNoticeDto);
                }

                //判断是否是定时任务触发的初始化操作，是的话，继续生成20个
                RobotCustomerContactDto contactDto = robotCustomerDynamicContactDTO.getRobotCustomerContactDto();
                if (Objects.nonNull(contactDto)) {
                    jobInitFlag = contactDto.getJobInitFlag();
                    log.info("是否是定时任务初始化触发的标识, jobInitFlag = {}", jobInitFlag);
                    if (!jobInitFlag) {
                        log.info("不是定时任务初始化触发的标识, 缓存中现有的活码已经大于配置的数量，直接结束流程，不用继续生成活码标识。jobInitFlag = {}", jobInitFlag);
                        String content = "现有的活码数量已经大于配置的数量,不需要继续生成";
                        if (!robotConfigSaveFlag) {
                            this.sendMessageToRecordOperationLog(landingPageWechatCustomerService, user, ip, batchOperationFlag, content, landingPageWechatCustomerContactVerify);
                        }
                        return;
                    }
                }
            }
        }

        //微信客服机器人保存的时候，之前缓存里面可能只生成了A，B企业微信的活码，此时操作的机器人是C企业微信的，所以这里需要再查询一下数量
        if (robotConfigSaveFlag){
            customerContactCacheSize = landingPageWechatCustomerServiceRedis.getRobotCustomerDynamicContactCacheSize(corpid, wechatUserId);
            log.info("微信客服机器人保存的时候，之前缓存里面可能只生成了A，B企业微信的活码，此时操作的机器人是C企业微信的，所以这里需要再查询一下数量, customerContactCacheSize = {}", customerContactCacheSize);
        }
        //如果相减数量大于0 数值代表还需生成的数量
        int param = generateNum - Convert.toInt(customerContactCacheSize);
        log.info("===>客服userid:{},批量生成微信客服机器人内联系我二维码, param = {}, generateNum = {}, customerContactCacheSize = {}", wechatUserId, param, generateNum, customerContactCacheSize);
        if (param > 0 && !jobInitFlag) {
            generateNum = param;
        }
        //缓存中数量大于配置的数量，不需要再生成
        if(param <= 0 ){
            generateNum = 0;
        }
        log.info("===>客服userid:{}, corpid : {}, 批量生成微信客服机器人动态活码, 是否是定时任务初始化触发的标识, jobInitFlag = {}, generateNum = {}, param = {}", wechatUserId, corpid, jobInitFlag, generateNum, param);

        String content = "";

        if (!robotConfigSaveFlag){
            this.sendMessageToRecordOperationLog(landingPageWechatCustomerService, user, ip, batchOperationFlag, content, landingPageWechatCustomerContactVerify);
        }
        RobotCustomerDynamicContactDTO message = new RobotCustomerDynamicContactDTO()
                .setLandingPageWechatCustomerServiceWechatUserId(wechatUserId)
                .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerServiceId)
                .setWechatUserName(wechatUserName)
                .setAccessToken(accessToken)
                .setEnterpriseWechatId(enterpriseWechatId)
                .setCorpId(wechatUserCorpId)
                .setUploadMaterialCorpId(corpid)
                .setDetectionCustomerContactCompleteFlag(true)
                .setFailModifyStatus(true)
                .setWechatCustomerContactVerify(landingPageWechatCustomerContactVerify)
                .setAdvertiserAccountGroupId(advertiserAccountGroupId)
                .setIp(ip)
                .setOldWechatCustomerContactStatus(oldLandingPageRobotCustomerDynamicContactStatus)
                .setOldWechatCustomerContactVerify(oldLandingPageRobotCustomerDynamicContactVerify)
                .setRobotCustomerContactDto(robotCustomerDynamicContactDTO.getRobotCustomerContactDto())
                .setEnterpriseWechat(enterpriseWechat)
                .setRealTimeConsumerFlag(false)
                .setRobotConfigSaveFlag(robotCustomerDynamicContactDTO.getRobotConfigSaveFlag())
                .setDelayFlag(true);

        for (int i = 0; i < generateNum; i++) {
            robotDynamicCustomerContactGenerateSender.sendGenerateQrCodeForCustomerService(message);
        }
    }


    /**
     * 发送消息进行日志记录（生成动态渠道二维码图片）
     */
    public void sendMessageToRecordOperationLog(LandingPageWechatCustomerService landingPageWechatCustomerService, User user, String ip, Boolean batchOperationFlag, String content, LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerify){
        RobotCustomerDynamicContactLogDto robotCustomerDynamicContactLogDto = new RobotCustomerDynamicContactLogDto();
        try {
            robotCustomerDynamicContactLogDto.setLandingPageWechatCustomerService(landingPageWechatCustomerService);
            robotCustomerDynamicContactLogDto.setUser(user);
            robotCustomerDynamicContactLogDto.setIp(ip);
            robotCustomerDynamicContactLogDto.setBatchOperationFlag(batchOperationFlag);
            robotCustomerDynamicContactLogDto.setContent(content);
            robotCustomerDynamicContactLogDto.setLandingPageWechatCustomerContactVerify(landingPageWechatCustomerContactVerify);
            robotDynamicCustomerContactGenerateSender.sendMessageToRecordOperationLog(robotCustomerDynamicContactLogDto);
        }catch (Exception e){
            log.error("发送生成微信客服机器人动态渠道二维码图片操作日志的消息出现异常, robotCustomerDynamicContactLogDto = {}", JSONObject.toJSONString(robotCustomerDynamicContactLogDto), e);
        }
    }

    /**
     * 保存批量删除的操作日志
     */
    public void saveBatchDeleteLog(LandingPageWechatCustomerService landingPageWechatCustomerService, User user, String ip, CompareOperActionEnum compareOperActionEnum, String operDesc){
        RLock fairLock = null;
        UserOperationLogDetail userOperationLogDetail = new UserOperationLogDetail();
        try {
            String agentId = TenantContextHolder.get();
            String key = RedisConstant.BATCH_DELETE_ROBOT_DYNAMIC_CUSTOMER_SERVICE_QR_CODE_KEY + agentId + ":" + landingPageWechatCustomerService.getWechatUserId();
            Object obj = defaultObjectRedisTemplate.opsForValue().get(key);
            log.info("查询缓存是否已经记录过批量删除动态渠道二维码的日志, key = {}, obj = {}", key, obj);
            if (Objects.nonNull(obj)) {
                log.info("3分钟内缓存已经记录过批量删除动态渠道二维码的日志, 不再重复保存操作日志, key = {}, obj = {}", key, obj);
                return;
            }

            fairLock = redissonClient.getFairLock(key);
            //尝试加锁，最多等待10秒
            boolean res = fairLock.tryLock(0,10, TimeUnit.SECONDS);
            if (!res) {
                log.info("保存生成动态渠道二维码的操作日志获取锁失败,key = [{}]", key);
                return ;
            }

            String username = user.getUsername();
            userOperationLogDetail.setOperId(landingPageWechatCustomerService.getId());
            userOperationLogDetail.setOperName(landingPageWechatCustomerService.getWechatUserName());
            userOperationLogDetail.setOperUserId(user.getId());
            userOperationLogDetail.setOperUserName(username);
            userOperationLogDetail.setOperIp(ip);
            userOperationLogDetail.setOperDesc(operDesc);
            userOperationLogDetail.setOperAction(compareOperActionEnum);
            userOperationLogDetail.setOperationRole(user.getOperationRole());
            userOperationLogDetail.setAdvertiserAccountGroupId(landingPageWechatCustomerService.getAdvertiserAccountGroupId());
            userOperationLogDetail.setOperLevel(UserOperationLogDetailActionLevel.CUSTOMER_SERVICE);
            userOperationLogDetail.setOperOpreands(UserOperationLogDetailOperands.WECHAT_CUSTOMER_SERVICE);
            userOperationLogDetail.setOperTime(Instant.now());
            JSONObject operDescJsonBySn = LandingPageWechatCustomerService.getOperDescJsonBySn("", "", "");
            userOperationLogDetail.setOperDescJson(operDescJsonBySn);
            userOperationLogDetailService.save(userOperationLogDetail);
            defaultObjectRedisTemplate.opsForValue().set(key, "1", 3, TimeUnit.MINUTES);
        }catch (Exception e){
            log.error("保存批量删除微信客服动态渠道二维码的操作日志,出现异常, userOperationLogDetail = {}", JSONObject.toJSONString(userOperationLogDetail), e);
        }
    }

    /**
     * 保存生成动态渠道二维码的操作日志
     */
    public void saveOperationLog(LandingPageWechatCustomerService landingPageWechatCustomerService, User user, String ip, Boolean batchOperationFlag, String content, LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerify){

        RLock fairLock = null;
        try {

            if (Objects.isNull(user)){
                log.error("保存生成动态渠道二维码的操作日志失败，user为空");
                return;
            }
            String agentId = TenantContextHolder.get();
            String key = RedisConstant.BATCH_GENERATE_ROBOT_DYNAMIC_CUSTOMER_SERVICE_QR_CODE_KEY + agentId + ":" + landingPageWechatCustomerService.getWechatUserId() + ":" + landingPageWechatCustomerContactVerify;
            String objectKey = key + ":object";
            Object obj = defaultObjectRedisTemplate.opsForValue().get(objectKey);
            log.info("查询缓存是否已经记录过生成动态渠道二维码的日志, key = {}, obj = {}", objectKey, obj);
            if (Objects.nonNull(obj)) {
                log.info("10s内缓存已经记录过生成动态渠道二维码的日志, 不再重复保存操作日志, key = {}, obj = {}", key, obj);
                return;
            }

            fairLock = redissonClient.getFairLock(key);
            //尝试加锁，最多等待10秒
            boolean res = fairLock.tryLock(0,10, TimeUnit.SECONDS);
            if (!res) {
                log.info("保存生成动态渠道二维码的操作日志获取锁失败,key = [{}]", key);
                return ;
            }

            String username = user.getUsername();
            Long id =  user.getId();
            log.info("保存生成动态渠道二维码的操作日志,batchOperationFlag = {},操作人username = {}, userId = {}", batchOperationFlag, username, id);
            UserOperationLogDetail userOperationLogDetail = new UserOperationLogDetail();
            userOperationLogDetail.setOperId(landingPageWechatCustomerService.getId());
            userOperationLogDetail.setOperName(landingPageWechatCustomerService.getWechatUserName());

            userOperationLogDetail.setOperUserId(user.getId());
            userOperationLogDetail.setOperUserName(username);
            userOperationLogDetail.setOperIp(ip);

            LandingPageWechatCustomerContactStatus robotCustomerDynamicContactStatus = landingPageWechatCustomerService.getRobotCustomerDynamicContactStatus();

            String operDesc = batchOperationFlag ? CompareOperActionEnum.BATCH_ROBOT_DYNAMIC_CUSTOMER_SERVICE_CONTACT_SUCCESS.getName() : CompareOperActionEnum.SINGLE_ROBOT_DYNAMIC_CUSTOMER_SERVICE_CONTACT_SUCCESS.getName();
            if (Objects.equals(robotCustomerDynamicContactStatus, LandingPageWechatCustomerContactStatus.GENERATED) && batchOperationFlag){
                operDesc = CompareOperActionEnum.BATCH_ROBOT_DYNAMIC_CUSTOMER_SERVICE_CONTACT_CHANGE.getName();
            }
            CompareOperActionEnum compareOperActionEnum = batchOperationFlag ? CompareOperActionEnum.BATCH_ROBOT_DYNAMIC_CUSTOMER_SERVICE_CONTACT_SUCCESS : CompareOperActionEnum.SINGLE_ROBOT_DYNAMIC_CUSTOMER_SERVICE_CONTACT_SUCCESS;

            String verify = LandingPageWechatCustomerContactVerifyStatus.ENABLE.equals(landingPageWechatCustomerService.getRobotCustomerDynamicContactVerify()) ? "需验证通过" : "免验证通过";
            if (!Objects.equals(landingPageWechatCustomerService.getRobotCustomerDynamicContactVerify(), landingPageWechatCustomerContactVerify)){
                String newVerify = LandingPageWechatCustomerContactVerifyStatus.ENABLE.equals(landingPageWechatCustomerContactVerify) ? "需验证通过" : "免验证通过";
                verify = "添加方式:" + verify + "->" + newVerify;
                operDesc = operDesc + "<br>" + verify;
            }
            userOperationLogDetail.setOperDesc(operDesc);
            userOperationLogDetail.setOperAction(compareOperActionEnum);
            userOperationLogDetail.setOperationRole(user.getOperationRole());
            userOperationLogDetail.setAdvertiserAccountGroupId(landingPageWechatCustomerService.getAdvertiserAccountGroupId());
            userOperationLogDetail.setOperLevel(UserOperationLogDetailActionLevel.CUSTOMER_SERVICE);
            userOperationLogDetail.setOperOpreands(UserOperationLogDetailOperands.WECHAT_CUSTOMER_SERVICE);
            userOperationLogDetail.setOperTime(Instant.now());
            JSONObject operDescJsonBySn = LandingPageWechatCustomerService.getOperDescJsonBySn("", "", "");
            userOperationLogDetail.setOperDescJson(operDescJsonBySn);
            userOperationLogDetailService.save(userOperationLogDetail);
            defaultObjectRedisTemplate.opsForValue().set(objectKey, "1", 10, TimeUnit.SECONDS);
        }catch (Exception e){
            log.error("保存生成动态渠道二维码的操作日志",e);
        }
    }

    /**
     * 查询旧二维码并清理
     *
     * @param landingPageWechatCustomerService
     * @param landingPageWechatCustomerContactVerify
     */
    private void searchOldContactAndClean(LandingPageWechatCustomerService landingPageWechatCustomerService,
                                          LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerify,
                                          EnterpriseWechat enterpriseWechat) {
        String wechatUserId = landingPageWechatCustomerService.getWechatUserId();
        String corpId = enterpriseWechat.getCorpid();
        String robotWechatCustomerContactConfigId = landingPageWechatCustomerService.getRobotCustomerDynamicContactConfigId();
        String robotWechatCustomerContactQiniuPath = landingPageWechatCustomerService.getRobotCustomerDynamicContactQiniuPath();
        //若是生成完成 或失败 并且验证方式不一致
        //需要查询失败的情况 查询失败情况下是否存在余量
        if (CUSTOMER_CONTACT_CLEAN_OLD_DATA.contains(landingPageWechatCustomerService.getRobotCustomerDynamicContactStatus())
                && !landingPageWechatCustomerService.getRobotCustomerDynamicContactVerify().equals(landingPageWechatCustomerContactVerify)) {
            log.info("微信客服机器人内客服userid:{}生成过联系我二维码，但添加方式不一致，需要清除旧数据!最新的verify = {},原来的最新的verify = {},原来的robotWechatCustomerContactConfigId = {}", wechatUserId, landingPageWechatCustomerContactVerify,
                landingPageWechatCustomerService.getRobotCustomerDynamicContactVerify(), robotWechatCustomerContactConfigId);

            //清除数据
            List<RobotCustomerDynamicContactCacheDTO> contactAndClean = landingPageWechatCustomerServiceRedis.getAllRobotCustomerContactClean(corpId, wechatUserId);
            if (!CollectionUtils.isEmpty(contactAndClean)) {
                //查询数据库中存在的
                List<Long> collect = contactAndClean.stream().map(RobotCustomerDynamicContactCacheDTO::getId).collect(Collectors.toList());
                List<RobotDynamicCustomerContactGenerateRecord> pageWechatCustomerContacts = listByIds(collect);
                //发送删除队列清理
                pageWechatCustomerContacts.stream().forEach(e -> {
                    RobotCustomerDynamicContactDeleteDto robotDynamicCustomerContactDeleteDto = new RobotCustomerDynamicContactDeleteDto()
                            .setEnterpriseWechat(enterpriseWechat)
                            .setRobotDynamicCustomerContactGenerateRecord(e)
                            .setFlag(true);
                    robotDynamicCustomerContactGenerateSender.sendDeleteSingleQrCode(robotDynamicCustomerContactDeleteDto);
                });
            }
        }
    }



    /**
     * 异步生成二维码 - 用于客服二维码初始化
     *
     * @param message
     */
    public boolean asynchGenerateQrCodeForCustomerService(RobotCustomerDynamicContactDTO message , boolean tokenFailureRetry,boolean limitFlag){
        Boolean detectionCustomerContactCompleteFlag = message.getDetectionCustomerContactCompleteFlag();
        String accessToken = null;
        String wechatUserId = message.getLandingPageWechatCustomerServiceWechatUserId();
        Long landingPageWechatCustomerServiceId = message.getLandingPageWechatCustomerServiceId();
        String corpId = message.getCorpId();
        String uploadMaterialCorpId = message.getUploadMaterialCorpId();
        boolean  realTimeConsumerFlag = Objects.nonNull(message.getRealTimeConsumerFlag()) && message.getRealTimeConsumerFlag();
        log.info("异步生成二维码,corpId = {}, uploadMaterialCorpId = {},是否是实时消耗的标识:realTimeConsumerFlag = {} ", corpId, uploadMaterialCorpId, realTimeConsumerFlag);
        Long advertiserAccountGroupId = message.getAdvertiserAccountGroupId();
        String wechatUserName = message.getWechatUserName();
        User user = message.getUser();
        String ip = message.getIp();

        EnterpriseWechat enterpriseWechat = message.getEnterpriseWechat();
        Long enterpriseWechatId = message.getEnterpriseWechatId();
        LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerify = message.getWechatCustomerContactVerify();
        LandingPageWechatCustomerContactStatus oldLandingPageWechatCustomerContactStatus = message.getOldWechatCustomerContactStatus();
        LandingPageWechatCustomerContactVerifyStatus oldLandingPageWechatCustomerContactVerify = message.getOldWechatCustomerContactVerify();
        RobotCustomerContactDto robotCustomerContactDto = message.getRobotCustomerContactDto();

        RobotWechatCustomerDynamicContactMessage robotWechatCustomerDynamicContactMessage = this.generateRobotWechatCustomerDynamicContactMessage(message);

        if (StringUtils.isBlank(wechatUserId)){
            log.info("微信客服机器人内,生成联系我二维码,wechatUserId为空 ，直接结束流程");
            return false;
        }

        //如果accessToken为空，需要查询代开发应用
        if (StringUtils.isBlank(accessToken)) {
            EnterpriseWechat enterpriseWechatNew = enterpriseWechatService.getByCorpId(corpId);
            if (Objects.isNull(enterpriseWechatNew)) {
                return false;
            }
            enterpriseWechatId = enterpriseWechatNew.getId();
            accessToken = enterpriseWechatNew.getAccessToken();
            corpId = enterpriseWechatNew.getCorpid();
        }
        CustomerContactAddWayRequestBody customerContactAddWayRequestBody = new CustomerContactAddWayRequestBody();
        customerContactAddWayRequestBody.setUser(Arrays.asList(wechatUserId));
        log.info("微信客服机器人内,生成联系我二维码, accessToken = {}",accessToken);
        try {
            //生成并且保存缓存
            if(Objects.nonNull(message.getRobotConfigSaveFlag()) && message.getRobotConfigSaveFlag()){
                //微信客服机器人保存的时候，走不限流
                limitFlag = false;
            }
            generateAndSaveCache( wechatUserId, accessToken, corpId, limitFlag, landingPageWechatCustomerContactVerify, enterpriseWechat, robotCustomerContactDto, landingPageWechatCustomerServiceId, uploadMaterialCorpId, realTimeConsumerFlag);

            //检测联系我二维码是否全部生成完毕
            if (Objects.nonNull(detectionCustomerContactCompleteFlag) && detectionCustomerContactCompleteFlag) {
                detectionRobotCustomerContactComplete(corpId, wechatUserId, landingPageWechatCustomerServiceId, wechatUserName, user, ip, landingPageWechatCustomerContactVerify, oldLandingPageWechatCustomerContactStatus, oldLandingPageWechatCustomerContactVerify);
            }

            return true;
        } catch (MarketingApiException e) {
            log.warn("==>微信客服机器人内,生成联系我二维码失败,marketing异常!,userid:{}", wechatUserId, e);
            Integer errcode = e.getErrcode();
            EnterpriseWechatGlobalErrorCode enterpriseWechatGlobalErrorCode = EnumUtil.getByCode(errcode, EnterpriseWechatGlobalErrorCode.class);
            if (Objects.nonNull(enterpriseWechatGlobalErrorCode)) {
                switch (enterpriseWechatGlobalErrorCode) {
                    case CUSTOMER_CONTACT_ADDED_ITEMS_REACHES_THE_UPPER_LIMIT:
                        //二维码数量超出上限
                        enterpriseWechatService.update(new LambdaUpdateWrapper<EnterpriseWechat>()
                            .set(EnterpriseWechat::getWechatCustomerContactStateInitFlag, WechatCustomerContactStateInitFlag.ANOMALY)
                            .set(EnterpriseWechat::getWechatCustomerContactStateInitFailureReason, enterpriseWechatGlobalErrorCode.getMessage())
                            .eq(EnterpriseWechat::getCorpid, corpId));
                        //清除企微代开发记录缓存
                        enterpriseWechatService.clearEnterpriseWechatCache(corpId);
                        robotWechatCustomerDynamicContactMessage.setRobotWechatCustomerContactStatus(LandingPageWechatCustomerContactStatus.CREATION_FAILURE);
                        robotWechatCustomerDynamicContactMessage.setRobotWechatCustomerContactFailureReason(enterpriseWechatGlobalErrorCode.getMessage());
                        robotDynamicCustomerContactGenerateSender.sendCustomerContactGenerateStatusChange(robotWechatCustomerDynamicContactMessage);
                        break;
                    case INVALID_ACCESS_TOKEN:
                        //accessToken失效 重新获取
                        if (tokenFailureRetry) {
                            log.info("微信客服机器人,生成企微侧企微联系我二维码,token失效，重新获取重新执行生成!wechatUserId:{}", wechatUserId);
                            EnterpriseWechat wechat = enterpriseWechatService.getByCorpId(corpId);
                            if (Objects.nonNull(wechat)) {
                                message
                                    .setLandingPageWechatCustomerServiceWechatUserId(wechatUserId)
                                    .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerServiceId)
                                    .setCorpId(corpId)
                                    .setAdvertiserAccountGroupId(advertiserAccountGroupId)
                                    .setEnterpriseWechatId(enterpriseWechatId)
                                    .setAccessToken(wechat.getAccessToken());
                            }
                            //重新执行，执行成功直接返回
                            boolean b = asynchGenerateQrCodeForCustomerService(message, false,limitFlag);
                            return b;
                        }
                        break;
                    case API_CONCURRENCY_LIMITATION:
                        Boolean delayFlag = message.getDelayFlag();
                        if (Objects.nonNull(delayFlag) && delayFlag) {
                            //并发限制 延迟2分钟再处理,再处理只处理一次
                            message
                                .setLandingPageWechatCustomerServiceWechatUserId(wechatUserId)
                                .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerServiceId)
                                .setCorpId(corpId)
                                .setAdvertiserAccountGroupId(advertiserAccountGroupId)
                                .setEnterpriseWechatId(enterpriseWechatId)
                                .setAccessToken(accessToken)
                                .setDelayFlag(false);
                            robotDynamicCustomerContactGenerateSender.sendDelayGenerateQrCodeForCustomerService(message);
                        } else {
                            //企微接口并发调用超过限制,初始化客服的将状态修改为异常,原因修改为并发限制
                            //修改公库客服对应生成状态
                            workWechatUserVisibleRangeRobotCustomerContactService.update(new LambdaUpdateWrapper<WorkWechatUserVisibleRangeRobotCustomerContact>()
                                .set(WorkWechatUserVisibleRangeRobotCustomerContact::getGenerateStatus, LandingPageWechatCustomerContactStatus.CREATION_FAILURE)
                                .set(WorkWechatUserVisibleRangeRobotCustomerContact::getFailureReason, enterpriseWechatGlobalErrorCode.getMessage())
                                .eq(WorkWechatUserVisibleRangeRobotCustomerContact::getCorpId, corpId)
                                .eq(WorkWechatUserVisibleRangeRobotCustomerContact::getUserId, wechatUserId));

                            robotWechatCustomerDynamicContactMessage.setRobotWechatCustomerContactStatus(LandingPageWechatCustomerContactStatus.CREATION_FAILURE)
                            .setRobotWechatCustomerContactFailureReason(enterpriseWechatGlobalErrorCode.getMessage())
                                .setRobotQrCreateFailFlag(true);
                            robotDynamicCustomerContactGenerateSender.sendCustomerContactGenerateStatusChange(robotWechatCustomerDynamicContactMessage);
                        }
                        break;
                    case SYSTEM_ERROR:
                        //系统异常，稍后重试
                        boolean b = asynchGenerateQrCodeForCustomerService(message, false,limitFlag);
                        break;
                    case TOO_MANY_REQUESTS:
                        //并发限制 延迟2分钟再处理,再处理只处理一次
                        message
                            .setLandingPageWechatCustomerServiceWechatUserId(wechatUserId)
                            .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerServiceId)
                            .setCorpId(corpId)
                            .setAdvertiserAccountGroupId(advertiserAccountGroupId)
                            .setEnterpriseWechatId(enterpriseWechatId)
                            .setAccessToken(accessToken)
                            .setDelayFlag(false);
                        robotDynamicCustomerContactGenerateSender.sendDelayGenerateQrCodeForCustomerService(message);
                        break;

                }
            } else {
                //不为自定义枚举中记录的企微错误码-可能是没加上去
                //修改公库客服对应生成状态
                workWechatUserVisibleRangeRobotCustomerContactService.update(new LambdaUpdateWrapper<WorkWechatUserVisibleRangeRobotCustomerContact>()
                    .set(WorkWechatUserVisibleRangeRobotCustomerContact::getGenerateStatus, LandingPageWechatCustomerContactStatus.CREATION_FAILURE)
                    .set(WorkWechatUserVisibleRangeRobotCustomerContact::getFailureReason, e.getMessage())
                    .eq(WorkWechatUserVisibleRangeRobotCustomerContact::getCorpId, corpId)
                    .eq(WorkWechatUserVisibleRangeRobotCustomerContact::getUserId, wechatUserId));

                robotWechatCustomerDynamicContactMessage.setRobotWechatCustomerContactStatus(LandingPageWechatCustomerContactStatus.CREATION_FAILURE)
                    .setRobotWechatCustomerContactFailureReason(enterpriseWechatGlobalErrorCode.getMessage());
                robotDynamicCustomerContactGenerateSender.sendCustomerContactGenerateStatusChange(robotWechatCustomerDynamicContactMessage);

            }


        } catch (Exception e) {
            log.error("==>队列生成联系我二维码失败!userid:{}", wechatUserId, e);
        }
        return false;
    }




    /**
     * 实体转化
     */
    private RobotWechatCustomerDynamicContactMessage generateRobotWechatCustomerDynamicContactMessage(RobotCustomerDynamicContactDTO message) {
        RobotWechatCustomerDynamicContactMessage contactMessage = new RobotWechatCustomerDynamicContactMessage();
        contactMessage.setCorpId(message.getCorpId());
        contactMessage.setLandingPageWechatCustomerServiceWechatUserId(message.getLandingPageWechatCustomerServiceWechatUserId());
        contactMessage.setRobotWechatCustomerContactQrCode(message.getCustomerContactQrCode());
        contactMessage.setRobotWechatCustomerContactConfigId(message.getWechatCustomerContactConfigId());
        contactMessage.setRobotResultUrl(message.getResultUrl());
        contactMessage.setRobotWechatCustomerContactMaterialId(message.getWechatCustomerContactMaterialId());
        contactMessage.setRobotWechatCustomerContactQiniuPath(message.getOfficialWechatCustomerContactQiniuPath());
        contactMessage.setRobotWechatCustomerContactState(message.getState());
        contactMessage.setRobotWechatCustomerContactVerify(message.getWechatCustomerContactVerify());
        return contactMessage;
    }


    /**
     * 检测联系我二维码是否全部生成完毕
     *
     * @param landingPageWechatCustomerServiceId
     */
    private void detectionRobotCustomerContactComplete(String corpId, String wechatUserId, Long landingPageWechatCustomerServiceId, String wechatUserName,
                                                  User user, String ip, LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerify,
                                                  LandingPageWechatCustomerContactStatus oldLandingPageWechatCustomerContactStatus,
                                                  LandingPageWechatCustomerContactVerifyStatus oldLandingPageWechatCustomerContactVerify) {
        Long num = landingPageWechatCustomerServiceRedis.getRobotCustomerDynamicContactCacheSize(corpId, wechatUserId);
        int generateNum = landingPageWechatCustomerContactConfig.getRobotCustomerDynamicGenerateNum();
        //判断是否全部生成
        if (Objects.nonNull(num) && num >= generateNum) {
            //修改客服记录生成状态
            landingPageWechatCustomerServiceService.update(new LambdaUpdateWrapper<LandingPageWechatCustomerService>()
                .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactStatus, LandingPageWechatCustomerContactStatus.GENERATED)
                .eq(LandingPageWechatCustomerService::getId, landingPageWechatCustomerServiceId));
            //清除与当前客服相关联的客服分组缓存
            landingPageWechatCustomerServiceService.deleteCacheByServiceIds(Arrays.asList(landingPageWechatCustomerServiceId));
            //修改公库客服对应生成状态
            workWechatUserVisibleRangeRobotCustomerContactService.update(new LambdaUpdateWrapper<WorkWechatUserVisibleRangeRobotCustomerContact>()
                .set(WorkWechatUserVisibleRangeRobotCustomerContact::getGenerateStatus, LandingPageWechatCustomerContactStatus.GENERATED)
                .eq(WorkWechatUserVisibleRangeRobotCustomerContact::getCorpId, corpId)
                .eq(WorkWechatUserVisibleRangeRobotCustomerContact::getUserId, wechatUserId));

            RobotWechatCustomerDynamicContactMessage message = new RobotWechatCustomerDynamicContactMessage();
            message
                .setRobotWechatCustomerContactStatus(LandingPageWechatCustomerContactStatus.GENERATED)
                .setCorpId(corpId)
                .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerServiceId)
                .setLandingPageWechatCustomerServiceWechatUserId(wechatUserId)
                .setWechatUserName(wechatUserName)
                .setRobotWechatCustomerContactVerify(landingPageWechatCustomerContactVerify)
                .setUser(user)
                .setIp(ip)
                .setVerifyChanges(false);
            if (LandingPageWechatCustomerContactStatus.GENERATED.equals(oldLandingPageWechatCustomerContactStatus) &&  !Objects.equals(landingPageWechatCustomerContactVerify,oldLandingPageWechatCustomerContactVerify)) {
                message.setVerifyChanges(true)
                    .setOldRobotWechatCustomerContactVerify(oldLandingPageWechatCustomerContactVerify);
            }
            robotDynamicCustomerContactGenerateSender.sendCustomerContactGenerateStatusChange(message);
        }
    }


    /**
     * 生成并保存缓存
     * corpId：客服所属的企业微信
     * otherCorpId : 需要上传素材的企业微信
     * 同主体的时候：corpId == otherCorpId；异主体的时候：corpId ！= otherCorpId
     */
    private void generateAndSaveCache( String wechatUserId, String accessToken, String corpId, boolean limitFlag, LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerify,
                                       EnterpriseWechat enterpriseWechat, RobotCustomerContactDto robotCustomerContactDto, Long landingPageWechatCustomerServiceId, String uploadMaterialCorpId, boolean realTimeConsumerFlag) {
        log.info("机器人生成动态渠道二维码，入参 客服所属企业微信 corpId = {}, wechatUserId = {}, 需要上传素材的企业微信uploadMaterialCorpId = {}, realTimeConsumerFlag = {}", corpId, wechatUserId, uploadMaterialCorpId, realTimeConsumerFlag );
        String state = RobotContactWayPrefixEnum.DYNAMIC_QR_CODE_IN_USE.getPrefix() + RandomUtil.randomString(6) + DateUtil.format(new DateTime(), "mmss");
        state = String.format(state, landingPageWechatCustomerContactConfig.getEnv());
        CustomerContactAddWayResponseBody customerContactAddWayResponseBody = null;
        if (!limitFlag) {
            //不限流
            customerContactAddWayResponseBody = officialWechatCustomerContactService.executeGenerateQrCode(wechatUserId, accessToken, state, landingPageWechatCustomerContactVerify);
        } else {
            //限流
            customerContactAddWayResponseBody = officialWechatCustomerContactService.executeGenerateQrCodeForCustomerService(corpId, wechatUserId, accessToken, state, landingPageWechatCustomerContactVerify);
        }

        if (Objects.isNull(customerContactAddWayResponseBody)){
            log.info("微信客服机器人内生成动态渠道二维码失败");
            return;
        }
        //创建临时素材
        String path = landingPageWechatCustomerContactConfig.getRobotCustomerDynamicContactPath() + state + ".png";

        RobotDynamicCustomerContactGenerateRecord rangeRobotCustomerContact = new RobotDynamicCustomerContactGenerateRecord();
        try {

            String qrCode = customerContactAddWayResponseBody.getQrCode();
            String configId = customerContactAddWayResponseBody.getConfigId();
           // EnterpriseWechatTempMaterial material = robotCustomerContactService.drawImage(robotCustomerContactDto, qrCode, path, enterpriseWechat);
           // Instant expireAt = material.getExpireAt();
            rangeRobotCustomerContact
                //.setMaterialId(String.valueOf(material.getId()))
                .setContactImageQiniuPath(path)
                .setConfigId(configId)
                //.setContactImageUrl(material.getMaterialPath())
                .setQrCodeUrl(qrCode);
                //.setExpireAt(expireAt);
        } catch (Exception e) {
            log.info("素材上传失败，需要删除渠道链接及七牛云资源 wechatUserId:{},path:{}", wechatUserId, path, e);
            if (e instanceof MarketingApiException) {
                //上面生成临时素材的步骤，先合成图片，上传七牛云，再上传公众号临时素材，只需要捕获临时素材上传抛的异常，清除七牛云
                //清掉七牛云
                if (StringUtils.isNotBlank(path) && !"/".equals(path)) {
                    fileUpload.delete(path);
                }
            }
            //清掉渠道二维码
            CustomerContactDelWayRequestBody customerContactDelWayRequestBody = new CustomerContactDelWayRequestBody();
            customerContactDelWayRequestBody.setConfigId(customerContactAddWayResponseBody.getConfigId());
            officialWechatCustomerContactService.executeDelRobotDynamicContactWay(accessToken, customerContactDelWayRequestBody);
        }
        String agentId = TenantContextHolder.get();
        rangeRobotCustomerContact
            .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerServiceId)
            .setLandingPageWechatCustomerServiceWechatUserId(wechatUserId)
            .setDynamicRobotWechatCustomerContactQrCode(customerContactAddWayResponseBody.getQrCode())
            .setConfigId(customerContactAddWayResponseBody.getConfigId())
            .setRobotWechatCustomerContactState(state)
            .setDynamicRobotCustomerContactStatus(LandingPageWechatCustomerContactStatus.GENERATED)
            .setAgentId(agentId)
            .setCorpId(corpId);

        //公库存储
        save(rangeRobotCustomerContact);

        RobotCustomerDynamicContactCacheDTO robotCustomerDynamicContactCacheDTO = new RobotCustomerDynamicContactCacheDTO();
        robotCustomerDynamicContactCacheDTO.setQrCode(rangeRobotCustomerContact.getDynamicRobotWechatCustomerContactQrCode())
            .setState(state)
            .setId(rangeRobotCustomerContact.getId())
            .setLandingPageWechatCustomerServiceWechatUserId(rangeRobotCustomerContact.getLandingPageWechatCustomerServiceWechatUserId())
            .setLandingPageWechatCustomerServiceId(rangeRobotCustomerContact.getLandingPageWechatCustomerServiceId())
            .setCorpId(corpId)
            .setConfigId(rangeRobotCustomerContact.getConfigId())
            .setResultUrl(rangeRobotCustomerContact.getContactImageUrl())
            .setMaterialId(rangeRobotCustomerContact.getMaterialId())
            .setQiniuPath(rangeRobotCustomerContact.getContactImageQiniuPath())
            .setUploadMaterialCorpId(uploadMaterialCorpId)
            .setRobotDynamicWechatCustomerContactVerifyStatus(landingPageWechatCustomerContactVerify)
            .setExpireAt(rangeRobotCustomerContact.getExpireAt());
        //添加缓存
        landingPageWechatCustomerServiceRedis.setRobotCustomerDynamicContactCache(uploadMaterialCorpId, wechatUserId, robotCustomerDynamicContactCacheDTO);
        //合成背景图片的单独再处理
        if(Objects.nonNull(robotCustomerContactDto)) {
            String backgroundUrl = robotCustomerContactDto.getBackgroundUrl();
            log.info("机器人生成动态渠道二维码, 异步进行背景图片的合成, backgroundUrl = {}, robotCustomerContactDto = {}", backgroundUrl, JSONObject.toJSONString(robotCustomerContactDto));
            if (StringUtils.isNotBlank(backgroundUrl)) {
                this.postSyntheticBackgroundImg(rangeRobotCustomerContact.getId(), robotCustomerContactDto, customerContactAddWayResponseBody.getQrCode(), path, corpId, wechatUserId, state, uploadMaterialCorpId, realTimeConsumerFlag);
            }
        }
    }


    /**
     * 发送消息进行微信客服机器人活码合成二维码背景图
     * @param rangeRobotCustomerContactId 二维码存储记录ID
     * @param robotCustomerContactDto 活码基础配置信息
     * @param qrCode 企业微信返回的二维码url
     * @param path 七牛云地址
     * @param corpId 企业微信corpId
     */
    public void postSyntheticBackgroundImg(Long rangeRobotCustomerContactId,RobotCustomerContactDto robotCustomerContactDto, String qrCode, String path, String corpId, String wechatUserId, String state, String uploadMaterialCorpId, Boolean realTimeConsumerFlag){
        log.info("发送消息进行微信客服机器人活码合成二维码背景图，入参 rangeRobotCustomerContactId = {}, qrCode = {}, path = {}," +
                " corpId = {}, wechatUserId = {}, state = {}, 需要上传素材的企业微信uploadMaterialCorpId = {}, realTimeConsumerFlag = {}", rangeRobotCustomerContactId, qrCode, path, corpId, wechatUserId, state, uploadMaterialCorpId, realTimeConsumerFlag);
        SyntheticBackgroundImgBO bo = new SyntheticBackgroundImgBO();
        bo.setRangeRobotCustomerContactId(rangeRobotCustomerContactId).setRobotCustomerContactDto(robotCustomerContactDto).setRealTimeConsumerFlag(realTimeConsumerFlag)
                .setPath(path).setCorpId(corpId).setQrCode(qrCode).setWechatUserId(wechatUserId).setState(state).setUploadMaterialCorpId(uploadMaterialCorpId);
        //投递消息，进行图片合成
        robotDynamicCustomerContactGenerateSender.postSyntheticBackgroundImg(bo);
    }

    /**
     * 处理客服机器人活码二维码合成背景图
     * @param bo 参数
     */
    public void resolveSyntheticBackgroundImg(SyntheticBackgroundImgBO bo){
        try {
            log.info("开始处理二维码合成背景图，参数 bo = {}", bo);
            if (Objects.isNull(bo)) {
                return;
            }
            RobotCustomerContactDto robotCustomerContactDto = bo.getRobotCustomerContactDto();
            String qrCode = bo.getQrCode();
            String path = bo.getPath();
            String corpId = bo.getCorpId();
            //如果不是实时消耗的话，就走微信客服列表页面预生成码的队列，目的是因为合成服务压力有点大，所以分开调用
            Boolean realTimeConsumerFlag = Objects.nonNull(bo.getRealTimeConsumerFlag()) && bo.getRealTimeConsumerFlag();
            String uploadMaterialCorpId = bo.getUploadMaterialCorpId();
            Long id = bo.getRangeRobotCustomerContactId();
            robotCustomerContactDto.setRangeRobotCustomerContactId(id);
            robotCustomerContactDto.setUploadMaterialCorpId(bo.getUploadMaterialCorpId());
            robotCustomerContactDto.setState(bo.getState());
            String wechatUserId = bo.getWechatUserId();
            robotCustomerContactDto.setWechatUserId(wechatUserId);

            String state = bo.getState();
            if (StringUtils.isBlank(qrCode) || StringUtils.isBlank(path) || StringUtils.isBlank(corpId)) {
                log.error("微信客服机器人活码，合成背景图片参数不合法，直接结束流程");
                return;
            }
            log.info("处理客服机器人活码二维码合成背景图，上传素材, realTimeConsumerFlag = {}, uploadMaterialCorpId = {}, robotCustomerContactDto = {}", realTimeConsumerFlag, uploadMaterialCorpId, robotCustomerContactDto);

            robotCustomerContactService.preDrawImage(robotCustomerContactDto, qrCode, path, new EnterpriseWechat().setCorpid(uploadMaterialCorpId));

//            EnterpriseWechatTempMaterial material = realTimeConsumerFlag ? robotCustomerContactService.drawImage(robotCustomerContactDto, qrCode, path, new EnterpriseWechat().setCorpid(uploadMaterialCorpId)):
//                                                                           robotCustomerContactService.preDrawImage(robotCustomerContactDto, qrCode, path, new EnterpriseWechat().setCorpid(uploadMaterialCorpId));
//            if (Objects.nonNull(material) && Objects.nonNull(id)){
//                Instant expireAt = material.getExpireAt();
//                Long materialId = material.getId();
//                this.lambdaUpdate()
//                    .set(RobotDynamicCustomerContactGenerateRecord::getMaterialId, material.getId())
//                    .set(RobotDynamicCustomerContactGenerateRecord::getContactImageUrl, material.getMaterialPath())
//                    .set(RobotDynamicCustomerContactGenerateRecord::getDynamicMaterialId, material.getMaterialId())
//                    .set(RobotDynamicCustomerContactGenerateRecord::getExpireAt, expireAt).eq(RobotDynamicCustomerContactGenerateRecord::getId, id).update();
//                //缓存素材ID信息
//                landingPageWechatCustomerServiceRedis.setRobotDynamicQrCodeMaterialId(uploadMaterialCorpId, wechatUserId, materialId, state, material.getMaterialPath(), material.getMaterialId(), expireAt);
//            }
        }catch (Exception e){
            log.error("微信客服机器人活码，合成背景图片异常, bo = {}", JSONObject.toJSONString(bo), e);
        }
    }


    /**
     * 保存生成的动态渠道二维码背景图并缓存
     * @param compositeImageDto 合成后的信息
     * @param renewalFlag 是否是续期的标识
     */
    public void saveRobotDynamicBackgroundImg(CompositeQrCodeBackgroundImageDto compositeImageDto, boolean renewalFlag){
        try{
            if (Objects.nonNull(compositeImageDto)) {
                Instant instant = Instant.now();
                EnterpriseWechatTempMaterial material = new EnterpriseWechatTempMaterial();
                material.setCorpId(compositeImageDto.getEnterpriseWechatCorpId())
                    .setMaterialPath(compositeImageDto.getQrCodeWithBackgroundUrl())
                    .setMaterialId(compositeImageDto.getMediaId())
                    .setCreatedAt(instant)
                    .setExpireAt(instant.plus(3, ChronoUnit.DAYS))
                    .setMaterialType(EnterpriseTempMaterialType.IMAGE);
                enterpriseTempMaterialService.saveOrUpdate(material);
                Long id = compositeImageDto.getLandingPageWechatCustomerContactId();
                String uploadMaterialCorpId = compositeImageDto.getEnterpriseWechatCorpId();
                String wechatUserId = compositeImageDto.getWechatUserId();
                String state = compositeImageDto.getState();
                Long materialId = material.getId();
                log.info("保存生成的动态渠道二维码背景图并缓存, 是否是续期的标识:{}, id = {}, uploadMaterialCorpId = {}, wechatUserId = {}, materialId = {}, state = {}",renewalFlag, id,  uploadMaterialCorpId, wechatUserId, materialId, state);
                if (Objects.nonNull(material) && Objects.nonNull(id) && StringUtils.isNotBlank(wechatUserId) && StringUtils.isNotBlank(state)) {
                    Instant expireAt = material.getExpireAt();
                    this.lambdaUpdate()
                        .set(RobotDynamicCustomerContactGenerateRecord::getMaterialId, material.getId())
                        .set(RobotDynamicCustomerContactGenerateRecord::getContactImageUrl, material.getMaterialPath())
                        .set(RobotDynamicCustomerContactGenerateRecord::getDynamicMaterialId, material.getMaterialId())
                        .set(RobotDynamicCustomerContactGenerateRecord::getUploadMaterialCorpId, uploadMaterialCorpId)
                        .set(RobotDynamicCustomerContactGenerateRecord::getExpireAt, expireAt)
                        .eq(RobotDynamicCustomerContactGenerateRecord::getId, id)
                        .update();
                    //缓存素材ID信息
                    log.info("合成图片后，进行素材id的缓存, 上传素材对应的corpid = {}, wechatUserId = {}, materialId = {}, 设置缓存的时间 : {}", uploadMaterialCorpId, wechatUserId,  materialId, Instant.now());
                    landingPageWechatCustomerServiceRedis.setRobotDynamicQrCodeMaterialId(uploadMaterialCorpId, wechatUserId, materialId, state, material.getMaterialPath(), material.getMaterialId(), expireAt);
                }
            }
        }catch (Exception e){
            log.error("保存生成的动态渠道二维码背景图并缓存异常，是否是续期的标识:{}, compositeImageDto = {}", renewalFlag, JSONObject.toJSONString(compositeImageDto), e);
        }
    }

    /**
     * 微信客服机器人内，生成动态渠道二维码,修改corpid绑定的多个账户下的同一userid的联系我二维码生成状态及预览链接
     */
    public void changeCustomerContactGenerateStatus(RobotWechatCustomerDynamicContactMessage message) {
        log.info("微信客服机器人内，生成动态渠道二维码,修改corpid绑定的多个账户下的同一userid的联系我二维码生成状态及预览链接, message = {}", JSONObject.toJSONString(message));
        String agentId = TenantContextHolder.get();
        String corpId = message.getCorpId();
        String wechatUserId = message.getLandingPageWechatCustomerServiceWechatUserId();
        String landingPageWechatCustomerContactQrCode = message.getRobotWechatCustomerContactQrCode();
        String robotResultUrl = message.getRobotResultUrl();
        String landingPageWechatCustomerContactConfigId = message.getRobotWechatCustomerContactConfigId();
        String landingPageWechatCustomerContactFailureReason = message.getRobotWechatCustomerContactFailureReason();
        LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerify = message.getRobotWechatCustomerContactVerify();
        LandingPageWechatCustomerContactStatus landingPageWechatCustomerContactStatus = message.getRobotWechatCustomerContactStatus();
        String robotWechatCustomerContactMaterialId = message.getRobotWechatCustomerContactMaterialId();
        String robotWechatCustomerContactState = message.getRobotWechatCustomerContactState();
        String path = message.getRobotWechatCustomerContactQiniuPath();

        //如果是初始化渠道二维码时 出现的并发异常 且生成失败 那么就只执行一次 查询缓存
        Boolean officialQrCreateFailFlag = message.getRobotQrCreateFailFlag();
        if (Objects.nonNull(officialQrCreateFailFlag) && officialQrCreateFailFlag &&
            LandingPageWechatCustomerContactStatus.CREATION_FAILURE.equals(landingPageWechatCustomerContactStatus)) {
            String key = RedisConstant.ROBOT_WECHAT_DYNAMIC_QR_CODE_CREATE_FAIL_KEY + corpId + ":" + wechatUserId;
            Object o = defaultObjectRedisTemplate.opsForValue().get(key);
            if (Objects.nonNull(o)) {
                log.info("微信客服机器人动态渠道二维码初始化通知客服状态变更为异常,此次变更与上次一致，无需重复变更!,corpId:{},wechatUserId:{}", corpId, wechatUserId);
                return;
            }
            //设置缓存
            defaultObjectRedisTemplate.opsForValue().set(key, "1", 1, TimeUnit.MINUTES);
        }

        LambdaUpdateWrapper<LandingPageWechatCustomerService> updateWrapper = new LambdaUpdateWrapper<LandingPageWechatCustomerService>()
            .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactStatus, landingPageWechatCustomerContactStatus)
            .set(StringUtils.isNotBlank(landingPageWechatCustomerContactQrCode), LandingPageWechatCustomerService::getRobotCustomerDynamicContactQrCode, landingPageWechatCustomerContactQrCode)
            .set(StringUtils.isNotBlank(robotResultUrl), LandingPageWechatCustomerService::getRobotCustomerDynamicContactBackgroundUrl, robotResultUrl)
            .set(StringUtils.isNotBlank(landingPageWechatCustomerContactConfigId), LandingPageWechatCustomerService::getRobotCustomerDynamicContactConfigId, landingPageWechatCustomerContactConfigId)
            .set(StringUtils.isNotBlank(landingPageWechatCustomerContactFailureReason), LandingPageWechatCustomerService::getRobotCustomerDynamicContactFailureReason, landingPageWechatCustomerContactFailureReason)
            .set(Objects.nonNull(landingPageWechatCustomerContactVerify), LandingPageWechatCustomerService::getRobotCustomerDynamicContactVerify, landingPageWechatCustomerContactVerify)
            .set(StringUtils.isNotBlank(robotWechatCustomerContactMaterialId), LandingPageWechatCustomerService::getRobotCustomerDynamicContactMaterialId, robotWechatCustomerContactMaterialId)
            .set(StringUtils.isNotBlank(robotWechatCustomerContactState), LandingPageWechatCustomerService::getRobotCustomerDynamicContactState, robotWechatCustomerContactState)
            .set(StringUtils.isNotBlank(path), LandingPageWechatCustomerService::getRobotCustomerDynamicContactQiniuPath, path)
            .eq(LandingPageWechatCustomerService::getCorpId, corpId)
            .eq(LandingPageWechatCustomerService::getWechatUserId, wechatUserId);

        List<String> agentIds = new ArrayList<>();
        if (StringUtils.isNotBlank(agentId)) {
            agentIds.add(agentId);
        }
        if (CollectionUtils.isEmpty(agentIds)) {
            return;
        }

        List<LandingPageWechatCustomerContactStatus> contactStatuses = Arrays.asList(LandingPageWechatCustomerContactStatus.GENERATED, LandingPageWechatCustomerContactStatus.CREATION_FAILURE);
        agentIds.stream().forEach(e -> {
            try {
                TenantContextHolder.set(e);
                log.info("开始修改账户:{}微信客服机器人内联系我二维码状态及预览链接", e);
                landingPageWechatCustomerServiceService.update(updateWrapper);
                if (!contactStatuses.contains(landingPageWechatCustomerContactStatus)) {
                    return;
                }

                //查询此账户是否绑定了此userid
                List<LandingPageWechatCustomerService> wechatCustomerServices = landingPageWechatCustomerServiceService.list(new LambdaQueryWrapper<LandingPageWechatCustomerService>()
                    .select(LandingPageWechatCustomerService::getId, LandingPageWechatCustomerService::getWechatUserName, LandingPageWechatCustomerService::getAdvertiserAccountGroupId)
                    .eq(LandingPageWechatCustomerService::getWechatUserId, wechatUserId)
                    .eq(LandingPageWechatCustomerService::getCorpId, corpId));
                Optional.ofNullable(wechatCustomerServices).ifPresent(m -> {
                    m.stream().forEach(ws -> {
                        if (LandingPageWechatCustomerContactStatus.GENERATED.equals(landingPageWechatCustomerContactStatus)) {
                            //清除与当前客服相关联的客服分组缓存
                            landingPageWechatCustomerServiceService.deleteCacheByServiceIds(Arrays.asList(ws.getId()));
                        }
                    });
                });
            } catch (Exception exception) {
                log.error("账户:{},公众号联系我二维码状态及预览链接变更失败", e, exception);
            } finally {
                TenantContextHolder.clearContext();
            }
        });
    }


    public void deleteExpireQrCode(RobotCustomerDynamicContactCacheDTO robotCustomerContactDeleteDto){
        try {
            String agentId = TenantContextHolder.get();
            if (Objects.nonNull(robotCustomerContactDeleteDto)) {
                String configId = robotCustomerContactDeleteDto.getConfigId();
                String state = robotCustomerContactDeleteDto.getState();
                if (StringUtils.isNotBlank(configId) && StringUtils.isNotBlank(state)) {
                    RobotDynamicCustomerContactGenerateRecord generateRecord = this.lambdaQuery().eq(RobotDynamicCustomerContactGenerateRecord::getRobotWechatCustomerContactState, state)
                        .eq(RobotDynamicCustomerContactGenerateRecord::getConfigId, configId)
                        .eq(StringUtils.isNotBlank(agentId), RobotDynamicCustomerContactGenerateRecord::getAgentId, agentId).last(" limit 1").one();
                    if (Objects.nonNull(generateRecord)) {
                        String corpId = generateRecord.getCorpId();
                        EnterpriseWechat enterpriseWechat = enterpriseWechatService.getByCorpId(corpId);
                        if (Objects.isNull(enterpriseWechat)) {
                            log.info("====>延迟删除微信客服机器人企微侧企微联系我二维码,没有查询到企业微信的信息, corpId = {}", corpId);
                            return;
                        }
                        String accessToken = enterpriseWechat.getAccessToken();
                        if (StringUtils.isBlank(configId)) {
                            log.info("====>延迟删除微信客服机器人企微侧企微联系我二维码,configId为空，无法删除");
                            return;
                        }
                        log.info("====>删除微信客服机器人企微侧企微联系我二维码,configId:{}", configId);
                        CustomerContactDelWayRequestBody customerContactDelWayRequestBody = new CustomerContactDelWayRequestBody();
                        customerContactDelWayRequestBody.setConfigId(configId);

                        officialWechatCustomerContactService.executeDelContactWay(accessToken, customerContactDelWayRequestBody);
                        log.info("====>删除微信客服机器人企微联系我二维码-企微侧删除成功,configId:{}", configId);
                        removeById(generateRecord.getId());
                        String qiniuPath = generateRecord.getContactImageQiniuPath();
                        log.info("====>删除微信客服机器人企微联系我二维码-删除七牛云链接,qiniuPath:{}", qiniuPath);
                        if (StringUtils.isNotBlank(qiniuPath) && !"/".equals(qiniuPath)) {
                            log.info("====>开始进行删除微信客服机器人企微联系我二维码-删除七牛云链接,qiniuPath:{}", qiniuPath);
                            //删除临时素材七牛云链接或永久素材七牛云链接
                            fileUpload.delete(qiniuPath);
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("====>删除微信客服机器人企微联系我二维码-删除失败,robotCustomerContactDeleteDto:{}", JSONObject.toJSONString(robotCustomerContactDeleteDto), e);
        }
    }

    /**
     * 删除企业微信侧的动态渠道二维码
     */
    public void deleteSingleRobotDynamicQrCode(RobotCustomerDynamicContactDeleteDto robotCustomerContactDeleteDto,  boolean tokenFailureRetry) {
        String agentId = TenantContextHolder.get();
        log.info("删除企业微信侧的动态渠道二维码, 入参, agentId = {}, robotCustomerContactDeleteDto = {}", agentId, JSONObject.toJSONString(robotCustomerContactDeleteDto));
        RobotDynamicCustomerContactGenerateRecord generateRecord = robotCustomerContactDeleteDto.getRobotDynamicCustomerContactGenerateRecord();
        EnterpriseWechat enterpriseWechatTemp = robotCustomerContactDeleteDto.getEnterpriseWechat();
        String materialId = robotCustomerContactDeleteDto.getMaterialId();
        String state = robotCustomerContactDeleteDto.getState();
        String corpId = Objects.nonNull(robotCustomerContactDeleteDto.getCorpIdForDelete()) ? robotCustomerContactDeleteDto.getCorpIdForDelete() : (Objects.nonNull(generateRecord) ? generateRecord.getCorpId() : null);
        String configId = robotCustomerContactDeleteDto.getConfigId();
        log.info("删除企业微信侧的动态渠道二维码, agentId = {}, corpId = {}, configId = {}, state = {}", agentId, corpId, configId, state);

        if (Objects.isNull(generateRecord) && StringUtils.isNotBlank(state)) {
            //基于素材查询联系我二维码记录
            generateRecord = this.lambdaQuery().eq(RobotDynamicCustomerContactGenerateRecord::getRobotWechatCustomerContactState, state)
                .eq(StringUtils.isNotBlank(agentId), RobotDynamicCustomerContactGenerateRecord::getAgentId, agentId).last(" limit 1").one();

            corpId = StringUtils.isNotBlank(corpId) ? corpId : (Objects.nonNull(generateRecord) ? generateRecord.getCorpId() : null);
        }

        if (Objects.isNull(generateRecord)) {
            log.info("====>删除微信客服机器人在企微侧企微的联系我二维码失败，未传递渠道二维码参数或此为永久素材ID不在临时素材渠道二维码表中,materialId:{}, state = {},configId = {}", materialId, state, configId);
            return;
        }

        if (StringUtils.isBlank(corpId)) {
            log.error("====>删除微信客服机器人在企微侧企微的联系我二维码失败，corpId不能为空, robotCustomerContactDeleteDto = {}", JSONObject.toJSONString(robotCustomerContactDeleteDto));
            return;
        }
        configId = StringUtils.isNotBlank(configId) ? configId : generateRecord.getConfigId();

        //查询企微信息（考虑队列可能阻塞导致的token失效，这里查一下企业微信信息）
        EnterpriseWechat enterpriseWechat  = enterpriseWechatService.getByCorpId(corpId);
        if (Objects.isNull(enterpriseWechat)) {
            log.info("====>延迟删除微信客服机器人企微侧企微联系我二维码,没有查询到企业微信的信息, corpId = {}", corpId);
            return;
        }
        String accessToken = enterpriseWechat.getAccessToken();
        String userId = generateRecord.getLandingPageWechatCustomerServiceWechatUserId();
        if (StringUtils.isBlank(configId)) {
            log.info("====>延迟删除微信客服机器人企微侧企微联系我二维码,configId为空，无法删除");
            return;
        }
        log.info("====>删除微信客服机器人企微侧企微联系我二维码,configId:{}", configId);
        CustomerContactDelWayRequestBody customerContactDelWayRequestBody = new CustomerContactDelWayRequestBody();
        customerContactDelWayRequestBody.setConfigId(configId);
        try {
            officialWechatCustomerContactService.executeDelContactWay(accessToken, customerContactDelWayRequestBody);
            log.info("====>删除微信客服机器人企微联系我二维码-企微侧删除成功,configId:{}", configId);
            if (robotCustomerContactDeleteDto.isFlag()) {
                log.info("删除微信客服机器人企微侧企微联系我二维码，删除数据库记录, id = {}, state = {},configId = {}", generateRecord.getId(), state, configId);
                removeById(generateRecord.getId());
                //删除一下合成图片缓存的素材ID和url信息
                landingPageWechatCustomerServiceRedis.deleteRobotDynamicQrCodeMaterialId(corpId, userId, state);
            }
            String qiniuPath = generateRecord.getContactImageQiniuPath();
            log.info("====>删除微信客服机器人企微联系我二维码-删除七牛云链接,qiniuPath:{}", qiniuPath);
            if (StringUtils.isNotBlank(qiniuPath) && !"/".equals(qiniuPath)) {
                log.info("====>开始进行删除微信客服机器人企微联系我二维码-删除七牛云链接,qiniuPath:{}", qiniuPath);
                //删除临时素材七牛云链接或永久素材七牛云链接
                fileUpload.delete(qiniuPath);
            }
            //移除“删除失败记录表中的记录”
            this.removeFailRecord(configId, userId,corpId);

        } catch (MarketingApiException e) {
            log.error("==>删除微信客服机器人企微联系我二维码失败,marketing异常!configId:{}", configId, e);
            Integer errcode = e.getErrcode();
            EnterpriseWechatGlobalErrorCode enterpriseWechatGlobalErrorCode = EnumUtil.getByCode(errcode, EnterpriseWechatGlobalErrorCode.class);
            if (Objects.nonNull(enterpriseWechatGlobalErrorCode)) {
                switch (enterpriseWechatGlobalErrorCode) {
                    case INVALID_OF_CUSTOMER_CONTANT_CONFIG_ID:
                        //如果是无效的configID证明已经被删除过了 无需再删除
                        if (robotCustomerContactDeleteDto.isFlag()) {
                            //根据ID删除记录需要区分公私库
                            log.info("删除微信客服机器人企微联系我二维码失败,configId无效,删除记录表中的记录,configId:{}", configId);
                            removeById(generateRecord.getId());
                        }
                        break;
                    case INVALID_ACCESS_TOKEN:
                        //accessToken失效 重新获取
                        if (tokenFailureRetry) {
                            log.info("删除微信客服机器人企微侧企微联系我二维码,token失效，重新获取重新执行删除!configId:{}", configId);
                            LambdaQueryWrapper<EnterpriseWechat> queryWrapper = new LambdaQueryWrapper<EnterpriseWechat>()
                                .select(EnterpriseWechat::getId, EnterpriseWechat::getAccessToken)
                                .eq(EnterpriseWechat::getId, enterpriseWechat.getId());
                            EnterpriseWechat wechat = enterpriseWechatService.getOne(queryWrapper);
                            if (Objects.nonNull(wechat)) {
                                enterpriseWechat.setAccessToken(wechat.getAccessToken());
                            }
                            robotCustomerContactDeleteDto.setEnterpriseWechat(enterpriseWechat);
                            deleteSingleRobotDynamicQrCode(robotCustomerContactDeleteDto, false);
                        }

                        log.info("删除微信客服机器人企微联系我二维码失败,token失效, configId = {}", configId);
                        //保存到删除失败记录表
                        this.saveDelFailRecord(robotCustomerContactDeleteDto, EnterpriseWechatGlobalErrorCode.INVALID_ACCESS_TOKEN);
                        break;
                    case API_FREQ_OUT_OF_LIMIT:
                        log.info("删除微信客服机器人企微联系我二维码失败,接口超频, configId = {}", configId);
                        //保存到删除失败记录表
                        this.saveDelFailRecord(robotCustomerContactDeleteDto, EnterpriseWechatGlobalErrorCode.API_FREQ_OUT_OF_LIMIT);
                        break;
                }
            }
        } catch (Exception e) {
            log.error("删除微信客服机器人企微联系我二维码失败,configId:{}", configId);
        }
    }

    /**
     * 删除远端的企业微信链接后，把数据库的异常记录移除
     * @param configId 企业微信素材ID
     * @param userId 客服userId
     * @param corpId 企业微信corpId
     */
    private void removeFailRecord(String configId, String userId, String corpId) {
        try {
            robotDynamicCustomerContactDeleteFailRecordService.remove(new LambdaQueryWrapper<RobotDynamicCustomerContactDeleteFailRecord>()
                .eq(RobotDynamicCustomerContactDeleteFailRecord::getConfigId, configId)
                .eq(RobotDynamicCustomerContactDeleteFailRecord::getLandingPageWechatCustomerServiceWechatUserId, userId)
                .eq(RobotDynamicCustomerContactDeleteFailRecord::getCorpId, corpId));
        }catch (Exception e){
            log.error("删除远端的企业微信链接后，把数据库的异常记录移除出现异常,configId:{}", configId, e);
        }
    }


    /**
     * 保存删除企业微信远端动态渠道二维码失败的记录
     */
    public void saveDelFailRecord(RobotCustomerDynamicContactDeleteDto robotCustomerDynamicContactDeleteDto, EnterpriseWechatGlobalErrorCode errorCode) {
        try {
            RobotDynamicCustomerContactGenerateRecord one = robotCustomerDynamicContactDeleteDto.getRobotDynamicCustomerContactGenerateRecord();
            if (Objects.nonNull(one)){
                RobotDynamicCustomerContactDeleteFailRecord failRecord = new RobotDynamicCustomerContactDeleteFailRecord();
                BeanUtils.copyProperties(one, failRecord);
                failRecord.setState(one.getRobotWechatCustomerContactState());
                failRecord.setFailureReason(Objects.nonNull(errorCode) ? errorCode.getMessage() : "");
                robotDynamicCustomerContactDeleteFailRecordService.save(failRecord);
            }
        }catch (Exception e){
            log.error("保存删除企业微信远端动态渠道二维码失败的记录出现异常,errorCode:{}, robotCustomerDynamicContactDeleteDto = {}", errorCode, robotCustomerDynamicContactDeleteDto, e);
        }
    }

    public void contactBatchDelete(List<Long> ids, DynamicBatchDeleteDTO bo) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<LandingPageWechatCustomerService> list = landingPageWechatCustomerServiceService.listByIds(ids);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        batchDelete(ids, list, null, bo);
    }

    private void batchDelete(List<Long> ids, List<LandingPageWechatCustomerService> list, String configId, DynamicBatchDeleteDTO bo) {

        log.info("批量删除机器人活码, ids = {}, configId = {}", ids, configId);
        if (ids.isEmpty() || list.isEmpty()){
            log.info("批量删除机器人活码参数不合法,不处理，直接结束流程");
            return;
        }

        Set<String> robotRelateCorpIds = new HashSet<>(16);
        //查询关联的企业微信
        if (!list.isEmpty()){
            LandingPageWechatCustomerService one = list.get(0);
            if (Objects.nonNull(one)){
                Long advertiserAccountGroupId = one.getAdvertiserAccountGroupId();
                robotRelateCorpIds = enterpriseWechatRobotCustomerService.getCorpIdByPmpId(advertiserAccountGroupId);
                //再查询一下项目关联的企业微信
                Set<String> relateCorpId = enterpriseWechatsPmpRelService.getCorpIdByPmpId(advertiserAccountGroupId);
                robotRelateCorpIds.addAll(relateCorpId);
            }
        }

        //置空客服记录上对应机器人活码相关数据
        landingPageWechatCustomerServiceService.update(new LambdaUpdateWrapper<LandingPageWechatCustomerService>()
            .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactStatus, LandingPageWechatCustomerContactStatus.UNGENERATED)
            .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactQrCode, null)
            .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactBackgroundUrl, null)
            .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactQiniuPath, null)
            .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactConfigId, null)
            .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactFailureReason, null)
            .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactVerify, LandingPageWechatCustomerContactVerifyStatus.DISABLE)
            .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactState, null)
            .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactMaterialId, null)
            .in(LandingPageWechatCustomerService::getId, ids));

        //清除与当前客服相关联的客服分组缓存
        landingPageWechatCustomerServiceService.deleteCacheByServiceIds(ids);
        //删除企业微信侧的联系我二维码(系统内的)
        for (LandingPageWechatCustomerService landingPageWechatCustomerService : list) {
            RobotCustomerContactDeleteDto deleteParam = new RobotCustomerContactDeleteDto();
            String robotCustomerDynamicContactConfigId = landingPageWechatCustomerService.getRobotCustomerDynamicContactConfigId();
            String robotCustomerDynamicContactQiniuPath = landingPageWechatCustomerService.getRobotCustomerDynamicContactQiniuPath();
            if (StringUtils.isNotBlank(robotCustomerDynamicContactConfigId) && StringUtils.isNotBlank(robotCustomerDynamicContactQiniuPath)) {
                deleteParam.setCorpId(landingPageWechatCustomerService.getCorpId())
                    .setRobotCustomerContactConfigId(robotCustomerDynamicContactConfigId)
                    .setRobotCustomerContactQiniuPath(robotCustomerDynamicContactQiniuPath);
                log.info("操作删除机器人动态活码,操作的入参是: configId = {}, deleteParam = {}", configId, JSONObject.toJSONString(deleteParam));
                robotCustomerContactSender.sendDeleteQrCode(deleteParam);
                if (Objects.nonNull(bo)){
                    User user = bo.getUser();
                    String ip = bo.getIp();
                    CompareOperActionEnum compareOperActionEnum = bo.getCompareOperActionEnum();
                    String operDesc  = bo.getOperDesc();
                    saveBatchDeleteLog(landingPageWechatCustomerService, user, ip, compareOperActionEnum, operDesc);
                }
            }
        }

        //基础配置表也要清除，不然留存没有意义的数据
        log.info("即将清除微信客服机器人数据库种的背景图设置等信息, ids = {}", ids);
        LambdaQueryWrapper<RobotCustomerDynamicContact> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(RobotCustomerDynamicContact::getLandingPageWechatCustomerServiceId, ids);
        robotCustomerDynamicContactService.remove(queryWrapper);

        Set<String> wechatUserIds = list.stream().filter(item -> Objects.nonNull(item.getWechatUserId())).map(LandingPageWechatCustomerService::getWechatUserId).collect(Collectors.toSet());
        if (!wechatUserIds.isEmpty()) {
            log.info("操作删除机器人动态活码，进行清除数据库生成状态记录表的数据, wechatUserIds = {}", wechatUserIds);
            //删除生成状态记录表
            LambdaQueryWrapper<WorkWechatUserVisibleRangeRobotCustomerContact> visibleQueryWrapper = new LambdaQueryWrapper<>();
            visibleQueryWrapper.in(WorkWechatUserVisibleRangeRobotCustomerContact::getUserId, wechatUserIds);
            workWechatUserVisibleRangeRobotCustomerContactService.remove(visibleQueryWrapper);
        }

        //删除缓存的基础配置
        for (LandingPageWechatCustomerService landingPageWechatCustomerService : list) {
            landingPageWechatCustomerServiceRedis.deleteRobotCustomerDynamicContactCache(landingPageWechatCustomerService.getId());

            //删除实时存储表的记录
            if (StringUtils.isBlank(configId)) {
                List<RobotDynamicCustomerContactGenerateRecord> deleteConfigList = this.lambdaQuery()
                    .select(RobotDynamicCustomerContactGenerateRecord::getConfigId,
                        RobotDynamicCustomerContactGenerateRecord::getId,
                        RobotDynamicCustomerContactGenerateRecord::getContactImageQiniuPath)
                    .eq(RobotDynamicCustomerContactGenerateRecord::getLandingPageWechatCustomerServiceWechatUserId, landingPageWechatCustomerService.getWechatUserId()).list();
                for (RobotDynamicCustomerContactGenerateRecord one : deleteConfigList) {
                    if (StringUtils.isNotBlank(one.getConfigId())) {
                        RobotCustomerContactDeleteDto deleteParam = new RobotCustomerContactDeleteDto();
                        deleteParam.setCorpId(landingPageWechatCustomerService.getCorpId())
                            .setRobotCustomerContactConfigId(one.getConfigId())
                            .setRobotDynamicCustomerContactGenerateRecordId(one.getId())
                            .setRobotCustomerContactQiniuPath(one.getContactImageQiniuPath());
                        log.info("操作删除机器人动态活码实时存储表的记录,调用企业微信接口,删除远端的二维码, 操作的入参是: {}", JSONObject.toJSONString(deleteParam));
                        robotCustomerContactSender.sendDeleteDynamicQrCode(deleteParam);
                        //删除实时表的记录，已经挪到后面，远程删除成功之后再删除记录表的记录
                        //this.removeById(one.getId());
                    }
                }
            }else{
                log.info("configId不为空，操作删除机器人动态活码,调用企业微信接口,删除远端的二维码, configId = {}", configId);
                //有值说明是定时任务触发的，不需要查询数据库记录，直接根据传来的configId进行删除
                RobotCustomerContactDeleteDto deleteParam = new RobotCustomerContactDeleteDto();
                deleteParam.setCorpId(landingPageWechatCustomerService.getCorpId())
                    .setRobotCustomerContactConfigId(configId)
                    .setRobotCustomerContactQiniuPath(null);
                log.info("操作删除机器人动态活码实时存储表的记录,调用企业微信接口,删除远端的二维码, 操作的入参是: {}", JSONObject.toJSONString(deleteParam));
                robotCustomerContactSender.sendDeleteDynamicQrCode(deleteParam);
            }
            //删除缓存
            String corpId = landingPageWechatCustomerService.getCorpId();
            robotRelateCorpIds.add(corpId);
            String wechatUserId = landingPageWechatCustomerService.getWechatUserId();
            for (String deleteCorpId : robotRelateCorpIds) {
                if (StringUtils.isNotBlank(wechatUserId) && StringUtils.isNotBlank(corpId)) {
                    log.info("删除微信客服机器人缓存中动态渠道二维码记录, deleteCorpId: {}, wechatUserId: {}", deleteCorpId, wechatUserId);
                    landingPageWechatCustomerServiceRedis.clearRobotCustomerDynamicContactPublicCache(deleteCorpId, wechatUserId);
                }
            }
        }
    }


    /**
     * 获取微信客服机器人动态渠道二维码
     * @param corpId 客服机器人所属的企业微信ID
     * @param wechatCustomerServiceGroupId 客服分组ID
     * @param externalUserId 客户的userId
     * @param openKfid 客服ID
     * @param flag true表示取到的客服和机器人属于同一个企业微信；false表示异主体企业微信
     * @return 微信客服机器人动态渠道二维码
     */
    public RobotCustomerLiveCodeDTO getRobotDynamicQrCode(String corpId, Long wechatCustomerServiceGroupId, String externalUserId, String openKfid, List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList
                             ,Long wechatCustomerServiceRobotId, boolean flag) {

        log.info("获取微信客服机器人动态渠道二维码,corpId = {}, wechatCustomerServiceGroupId = {}, externalUserId = {}, wechatCustomerServiceRobotId = {}", corpId, wechatCustomerServiceGroupId, externalUserId, wechatCustomerServiceRobotId);


        LandingPageWechatCustomerServiceRedisDto serviceRedisDto = getSameCustomer(wechatCustomerServiceGroupId, externalUserId, openKfid, landingPageWechatCustomerServiceList, wechatCustomerServiceRobotId);

        int timeLimit = landingPageWechatCustomerContactConfig.getTimeLimit();
        if (serviceRedisDto != null) {
            //校验是否发同一个自归因参数的码，避免同一个用户频繁点击，导致活码无畏消耗
            boolean isNeedSendLiveCode =  checkIsNeedSendLiveCodeInTime(externalUserId,openKfid, serviceRedisDto);
            log.info("校验设置时间内是否需要发码的结果 isNeedSendLiveCode = {}, timeLimit = {}", isNeedSendLiveCode, timeLimit);
            //同一访客
            if (!isNeedSendLiveCode){
                log.info("设定的时间范围内频繁发送消息，不进行发送新的活码，取缓存中已发送的码, externalUserId = {}, openKfid = {}, wechatCustomerServiceGroupId = {}", externalUserId, openKfid, wechatCustomerServiceGroupId );
                RobotCustomerLiveCodeDTO  dto = getTheSameImgInTime(externalUserId,  openKfid,  wechatCustomerServiceGroupId);
                if (Objects.nonNull(dto)){
                    String state = dto.getRobotCustomerDynamicContactState();
                    log.info("同一个访客，短时间内频繁发送消息，回复相同归因参数的活码, state = {}", state);
                    return dto;
                }
            }

            log.info("获取微信客服机器人动态渠道二维码,同一访客");
            LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerifyStatus = LandingPageWechatCustomerContactVerifyStatus.DISABLE;
            if (Objects.nonNull( serviceRedisDto.getRobotCustomerDynamicContactVerify()) &&  serviceRedisDto.getRobotCustomerDynamicContactVerify() == 1){
                landingPageWechatCustomerContactVerifyStatus = LandingPageWechatCustomerContactVerifyStatus.ENABLE;
            }

            log.info("获取微信客服机器人动态渠道二维码,同一访客，corpId = {}, flag = {}", corpId, flag);
            //使用预生成的公众号单人活码
            RobotCustomerDynamicContactCacheDTO dto = useRobotPregeneration(serviceRedisDto.getId(), serviceRedisDto.getLandingPageWechatCustomerServiceWechatUserId(),corpId, serviceRedisDto.getAdvertiserAccountGroupId(),
                landingPageWechatCustomerContactVerifyStatus,serviceRedisDto);
            if (Objects.nonNull(dto)){
                RobotDynamicQrCodeDTO o = landingPageWechatCustomerServiceRedis.getRobotDynamicQrCodeMaterialId(corpId, dto.getLandingPageWechatCustomerServiceWechatUserId(), dto.getState());
                log.info("同一访客，获取微信客服机器人动态渠道二维码对应的素材ID,o = {}", o);
                String materialPath = null;
                String dynamicMaterialId = null;
                if (StringUtils.isNotBlank(dto.getResultUrl())){
                    log.info("获取微信客服机器人动态渠道二维码,同一访客,取得是旧版本已经生成的动态渠道二维码, materialPath = {}", materialPath);
                    materialPath = dto.getResultUrl();
                    dynamicMaterialId = dto.getDynamicMaterialId();
                }else {
                    log.info("获取微信客服机器人动态渠道二维码,同一访客,取得是新版本已经生成的动态渠道二维码, materialPath = {}", materialPath);
                    materialPath = Objects.nonNull(o) ? o.getMaterialPath() : null;
                    dynamicMaterialId = Objects.nonNull(o) ? o.getDynamicMaterialId() : null;
                }

                setSendNum(externalUserId,  openKfid, serviceRedisDto);
                RobotCustomerLiveCodeDTO robotCustomerLiveCodeDTO = new RobotCustomerLiveCodeDTO().setWechatCustomerServiceId(dto.getLandingPageWechatCustomerServiceId())
                    .setLandingPageWechatCustomerServiceWechatUserId(dto.getLandingPageWechatCustomerServiceWechatUserId())
                    .setRobotCustomerContactBackgroundUrl(materialPath)
                    .setRobotCustomerDynamicContactMaterialId(Objects.nonNull(o) ? o.getMaterialId() : null)
                    .setRobotCustomerDynamicContactState(dto.getState())
                    .setQrCode(dto.getQrCode())
                    .setDynamicContactMaterialId(dynamicMaterialId)
                    .setCorpIdForDelete(dto.getCorpId())
                    .setUploadMaterialCorpId(dto.getUploadMaterialCorpId())
                    .setConfigId(dto.getConfigId())
                    .setExpireAt(dto.getExpireAt())
                    .setFlag(flag);
                //设置缓存
                if (Objects.nonNull(dto.getSetTheSameVisitor()) && dto.getSetTheSameVisitor()) {
                    setTheSameImgInTime(externalUserId, openKfid, wechatCustomerServiceGroupId, robotCustomerLiveCodeDTO, timeLimit);
                }
                return robotCustomerLiveCodeDTO;
            }
        } else {
            log.info("获取微信客服机器人动态渠道二维码,不是同一访客");
            //根据权重算法获得客服数据
            LandingPageWechatCustomerServiceRedisDto customerServiceRedisDto = landingPageWechatCustomerServiceService.getWechatCustomerServiceByWeight(wechatCustomerServiceGroupId, landingPageWechatCustomerServiceList);
            if (customerServiceRedisDto != null) {
                //存入缓存
                saveSameCustomer(customerServiceRedisDto, wechatCustomerServiceGroupId, externalUserId, openKfid, wechatCustomerServiceRobotId);

                LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerifyStatus = LandingPageWechatCustomerContactVerifyStatus.DISABLE;
                if (Objects.nonNull( customerServiceRedisDto.getRobotCustomerDynamicContactVerify()) &&  customerServiceRedisDto.getRobotCustomerDynamicContactVerify() == 1){
                    landingPageWechatCustomerContactVerifyStatus = LandingPageWechatCustomerContactVerifyStatus.ENABLE;
                }

                log.info("获取微信客服机器人动态渠道二维码,不是同一访客，先取机器人所属的企业微信是否有活码记录, corpId = {}, flag = {}", corpId, flag);
                RobotCustomerDynamicContactCacheDTO dto = useRobotPregeneration(customerServiceRedisDto.getId(), customerServiceRedisDto.getLandingPageWechatCustomerServiceWechatUserId(),
                    corpId, customerServiceRedisDto.getAdvertiserAccountGroupId(), landingPageWechatCustomerContactVerifyStatus,customerServiceRedisDto);

                if (Objects.isNull(dto)){
                    corpId = customerServiceRedisDto.getCorpId();
                    log.info("获取微信客服机器人动态渠道二维码,不是同一访客,取机器人所在的企业微信，没有找到素材，再去看这个客服所在的企业微信是否有素材, corpId = {}", corpId);
                    //这个情况是取机器人所在的企业微信，没有找到素材的时候，再去看这个客服所在的企业微信是否有素材
                    dto = useRobotPregeneration(customerServiceRedisDto.getId(), customerServiceRedisDto.getLandingPageWechatCustomerServiceWechatUserId(),
                            corpId, customerServiceRedisDto.getAdvertiserAccountGroupId(), landingPageWechatCustomerContactVerifyStatus,customerServiceRedisDto);
                    log.info("获取微信客服机器人动态渠道二维码,不是同一访客,取机器人所在的企业微信，没有找到素材再去取这个客服所在的企业微信是否有素材, corpId = {}, dto = {}", corpId, dto);
                }

                if (Objects.nonNull(dto)) {
                    RobotDynamicQrCodeDTO o = landingPageWechatCustomerServiceRedis.getRobotDynamicQrCodeMaterialId(corpId, dto.getLandingPageWechatCustomerServiceWechatUserId(), dto.getState());

                    log.info("不是同一访客, 获取微信客服机器人动态渠道二维码对应的素材ID,o = {}", o);
                    String materialPath = null;
                    String dynamicMaterialId = null;
                    if (StringUtils.isNotBlank(dto.getResultUrl())) {
                        materialPath = dto.getResultUrl();
                        dynamicMaterialId = dto.getDynamicMaterialId();
                        log.info("获取微信客服机器人动态渠道二维码,不是同一访客,取得是旧版本已经生成的动态渠道二维码, materialPath = {}, dynamicMaterialId = {}", materialPath, dynamicMaterialId);
                    } else {
                        materialPath = Objects.nonNull(o) ? o.getMaterialPath() : null;
                        dynamicMaterialId = Objects.nonNull(o) ? o.getDynamicMaterialId() : null;
                        log.info("获取微信客服机器人动态渠道二维码,不是同一访客,取得是新版本已经生成的动态渠道二维码, materialPath = {}, dynamicMaterialId = {}", materialPath, dynamicMaterialId);
                    }
                    setSendNum(externalUserId, openKfid, customerServiceRedisDto);
                    RobotCustomerLiveCodeDTO robotCustomerLiveCodeDTO = new RobotCustomerLiveCodeDTO().setWechatCustomerServiceId(dto.getLandingPageWechatCustomerServiceId())
                        .setLandingPageWechatCustomerServiceWechatUserId(dto.getLandingPageWechatCustomerServiceWechatUserId())
                        .setRobotCustomerDynamicContactState(dto.getState())
                        .setRobotCustomerContactBackgroundUrl(materialPath)
                        .setRobotCustomerDynamicContactMaterialId(Objects.nonNull(o) ? o.getMaterialId() : null)
                        .setQrCode(dto.getQrCode())
                        .setDynamicContactMaterialId(dynamicMaterialId)
                        .setCorpIdForDelete(dto.getCorpId())
                        .setUploadMaterialCorpId(dto.getUploadMaterialCorpId())
                        .setExpireAt(dto.getExpireAt())
                        .setConfigId(dto.getConfigId())
                        .setFlag(flag);
                    //设置缓存
                    if (Objects.nonNull(dto.getSetTheSameVisitor()) && dto.getSetTheSameVisitor()){
                        setTheSameImgInTime(externalUserId, openKfid, wechatCustomerServiceGroupId, robotCustomerLiveCodeDTO, timeLimit);
                    }
                    return robotCustomerLiveCodeDTO;
                }
            }
        }
        return null;
    }


    /**
     * 同一个访客5分钟之内取同一个活码素材（即state相同）
     */
    public RobotCustomerLiveCodeDTO getTheSameImgInTime(String externalUserId, String openKfid, Long wechatCustomerServiceGroupId){
        return landingPageWechatCustomerServiceRedis.getTheSameLiveCodeInTime(externalUserId, openKfid, wechatCustomerServiceGroupId);
    }

    /**
     * 同一个访客5分钟之内,设置活码素材（即state相同）
     */
    public void setTheSameImgInTime(String externalUserId,  String openKfid,  Long wechatCustomerServiceGroupId, RobotCustomerLiveCodeDTO robotCustomerLiveCodeDTO, Integer timeLimit){
        landingPageWechatCustomerServiceRedis.setTheSameImgInTime(externalUserId, openKfid, wechatCustomerServiceGroupId, robotCustomerLiveCodeDTO, timeLimit);
    }

    /**
     * 校验同一个机器人内，同一个用户是否在短时间内频繁发送消息
     * @param externalUserId 外部联系人的userId
     * @param openKfid 微信客服机器人ID
     * @return
     */
    public boolean checkIsNeedSendLiveCodeInTime(String externalUserId, String openKfid, LandingPageWechatCustomerServiceRedisDto serviceRedisDto){
        String landingPageWechatCustomerServiceWechatUserId = serviceRedisDto.getLandingPageWechatCustomerServiceWechatUserId();
        String key = RedisConstant.ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_QR_CODE_NEED_SEND_QR_CODE_KEY + "externalUserId:" + externalUserId + "openKfid:" + openKfid + "userid:" + landingPageWechatCustomerServiceWechatUserId;
        try {
            Object obj = defaultObjectRedisTemplate.opsForValue().get(key);
            log.info("微信客服机器人发送活码之前，检查已经发送的活码数量, key = {}, obj = {}", key, obj);
            if (Objects.nonNull(obj) && Objects.nonNull(serviceRedisDto)) {
                int timeLimit = landingPageWechatCustomerContactConfig.getTimeLimit();
                log.info("设置{}分钟内发送同一个二维码", timeLimit);
                return false;
            }
        }catch (Exception e){
            log.error("微信客服机器人发送活码之前，检查已经发送的活码数量异常, key = {}", e, key);
        }
        return true;
    }

    public void setSendNum(String externalUserId, String openKfid, LandingPageWechatCustomerServiceRedisDto customerServiceRedisDto){
        String landingPageWechatCustomerServiceWechatUserId = customerServiceRedisDto.getLandingPageWechatCustomerServiceWechatUserId();
        String key =  RedisConstant.ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_QR_CODE_NEED_SEND_QR_CODE_KEY+ "externalUserId:" + externalUserId + "openKfid:" + openKfid + "userid:" + landingPageWechatCustomerServiceWechatUserId;
        Long num = defaultObjectRedisTemplate.opsForValue().increment(key);
        int timeLimit = landingPageWechatCustomerContactConfig.getTimeLimit();
        log.info("微信客服机器人发送活码之后，进行计数, key = {}, timeLimit = {}, num = {}", key, timeLimit, num);
        defaultObjectRedisTemplate.expire(key, timeLimit, TimeUnit.MINUTES);
    }



    /**
     * 使用预生成的机器人动态渠道单人活码
     *
     * @param landingPageWechatCustomerServiceId 客服ID
     * @param landingPageWechatCustomerServiceWechatUserId 客服userId
     * @param corpId 企业微信ID
     * @param advertiserAccountGroupId 项目ID
     * @param landingPageWechatCustomerContactVerifyStatus 添加好友是否需要验证
     * @return
     */
    private RobotCustomerDynamicContactCacheDTO useRobotPregeneration(long landingPageWechatCustomerServiceId,
                                                                      String landingPageWechatCustomerServiceWechatUserId,
                                                                      String corpId,
                                                                      Long advertiserAccountGroupId,
                                                                      LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerifyStatus,
                                                                      LandingPageWechatCustomerServiceRedisDto customerServiceRedisDto) {
        int generateNum = landingPageWechatCustomerContactConfig.getRobotCustomerDynamicGenerateNum();
        //公库key
        String publicKey = RedisConstant.ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_QR_CODE_KEY + corpId + ":" + landingPageWechatCustomerServiceWechatUserId;
        String generateKey = RedisConstant.ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_QR_CODE_GENERATE_KEY + corpId + ":" + landingPageWechatCustomerServiceWechatUserId;

        log.info("自动回复，使用预生成的机器人动态渠道单人活码 publicKey = {},generateKey  = {}", publicKey,  generateKey);
        //取公库队列值
        Object entries = defaultObjectRedisTemplate.opsForList().leftPop(publicKey);
        RobotCustomerDynamicContactCacheDTO robotCustomerDynamicContactCacheDTO = null;
        //当前客服缓存二维码数量 - 公库
        Long customerContactCacheSize = landingPageWechatCustomerServiceRedis.getRobotCustomerDynamicContactCacheSize(corpId, landingPageWechatCustomerServiceWechatUserId);
        EnterpriseWechat enterpriseWechat = enterpriseWechatService.getByCorpId(corpId);
        //缓存为空,或者公库二维码少于20个，就需要补偿
        if (Objects.isNull(entries) || (customerContactCacheSize <= generateNum)) {
            log.warn("====>微信客服机器人链路,根据客服获取动态渠道二维码，缓存中值已被全部消耗或者剩余不到{}个,wechatUserId:{}", landingPageWechatCustomerContactConfig.getRobotCustomerDynamicGenerateNum(),
                landingPageWechatCustomerServiceWechatUserId);
            //是否正在生成中
            Object o = defaultObjectRedisTemplate.opsForValue().get(generateKey);
            generateNum = Objects.isNull(entries) ? generateNum : NumberUtil.sub(Convert.toLong(generateNum), customerContactCacheSize).intValue();
            log.info("自动回复，进行实时补充二维码, 需要补充生成的活码数量 generateNum = {}", generateNum);
            if (Objects.isNull(o)) {
                //标识此客服二维码在生成中
                defaultObjectRedisTemplate.opsForValue().set(generateKey, true, 8, TimeUnit.SECONDS);
                //没有正在生成中的队列，需要重新补偿生成generateNum个
                IntStream.range(0, generateNum).forEach(i -> {
                    //生成新的二维码
                    generateRobotDynamicContact(landingPageWechatCustomerServiceId, landingPageWechatCustomerServiceWechatUserId, corpId, advertiserAccountGroupId, landingPageWechatCustomerContactVerifyStatus, enterpriseWechat, customerServiceRedisDto);
                });
            }
            //队列缓存为空，直接返回空
            if (Objects.isNull(entries)) {
                log.info("自动回复，队列缓存为空，直接返回空");
                return null;
            }
        }
        log.info("微信客服机器人链路,根据客服获取动态渠道二维码,能走到这证明队列缓存有值, entries = {}", JSONObject.toJSONString(entries));
        //能走到这证明队列缓存有值
        robotCustomerDynamicContactCacheDTO = (RobotCustomerDynamicContactCacheDTO) entries;

        //这里是针对1.302.0迭代版本之前的旧数据进行判断,能取到值，说明这个机器人所属的企业微信下是有素材的，1.302.0迭代开始，新生成的码缓存中这个字段都有值
        if (Objects.isNull(robotCustomerDynamicContactCacheDTO.getUploadMaterialCorpId())){
            robotCustomerDynamicContactCacheDTO.setUploadMaterialCorpId(corpId);
        }

        //生成新的二维码(取一个马上补充一个)
        generateRobotDynamicContact(landingPageWechatCustomerServiceId, landingPageWechatCustomerServiceWechatUserId, corpId, advertiserAccountGroupId, landingPageWechatCustomerContactVerifyStatus, enterpriseWechat, customerServiceRedisDto);
        //修改获取到的二维码的使用状态
        robotDynamicCustomerContactGenerateSender.changeQrCodeUseStatus(robotCustomerDynamicContactCacheDTO);
        //去获取具体的信息
        String objectKey = RedisConstant.ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_QR_CODE_OBJECT_KEY + corpId + ":" + robotCustomerDynamicContactCacheDTO.getState();
        Object one = defaultObjectRedisTemplate.opsForValue().get(objectKey);


        log.info("微信客服机器人链路,根据客服获取动态渠道二维码, objectKey = {}, one = {}", objectKey, one);
        //未过期的，正常使用
        if (Objects.nonNull(one)) {
           // defaultObjectRedisTemplate.delete(objectKey);
            RobotCustomerDynamicContactCacheDTO result = (RobotCustomerDynamicContactCacheDTO) one;
            if (Objects.isNull(result.getUploadMaterialCorpId())){
                result.setUploadMaterialCorpId(corpId);
            }
            log.info("微信客服机器人链路,根据客服获取动态渠道二维码，缓存中的素材正常，直接返回, result = {}", JSONObject.toJSONString(result));
            return result;
        }
        log.info("微信客服机器人链路,根据客服获取动态渠道二维码，缓存中的素材对象不存在，删除这个客服的活码队列, corpId = {}, robotCustomerDynamicContactCacheDTO = {}", corpId, JSONObject.toJSONString(robotCustomerDynamicContactCacheDTO));
        if (StringUtils.isNotBlank(robotCustomerDynamicContactCacheDTO.getConfigId()) && StringUtils.isNotBlank(robotCustomerDynamicContactCacheDTO.getState()) && StringUtils.isNotBlank(robotCustomerDynamicContactCacheDTO.getCorpId())) {
            RobotDynamicCustomerContactGenerateRecord record = this.lambdaQuery().eq(RobotDynamicCustomerContactGenerateRecord::getConfigId, robotCustomerDynamicContactCacheDTO.getConfigId())
                .eq(RobotDynamicCustomerContactGenerateRecord::getRobotWechatCustomerContactState, robotCustomerDynamicContactCacheDTO.getState())
                .eq(RobotDynamicCustomerContactGenerateRecord::getCorpId, robotCustomerDynamicContactCacheDTO.getCorpId()).last(" limit 1").one();

            if (Objects.isNull(record)) {
                landingPageWechatCustomerServiceRedis.clearRobotCustomerDynamicContactCacheByHand(corpId, landingPageWechatCustomerServiceWechatUserId);
                generateRobotDynamicContact(landingPageWechatCustomerServiceId, landingPageWechatCustomerServiceWechatUserId, corpId, advertiserAccountGroupId,
                    landingPageWechatCustomerContactVerifyStatus, enterpriseWechat, customerServiceRedisDto);
                robotCustomerDynamicContactCacheDTO.setSetTheSameVisitor(false);
                //兼容 带背景图的缓存过期后 从数据库中获取到有背景图的二维码 并插入到 对象中
            } else {
                if(StringUtils.isNotBlank(record.getContactImageUrl())){
                    log.info("素材缓存已过期，获取到数据库里合成好的背景图 record：{}",JSONObject.toJSONString(record));
                    robotCustomerDynamicContactCacheDTO.setResultUrl(record.getContactImageUrl());
                }
            }

        }
        //已经过期,需要重新生成,兜底
        this.postReUploadExpireMaterial(robotCustomerDynamicContactCacheDTO, landingPageWechatCustomerServiceId,corpId,robotCustomerDynamicContactCacheDTO.getState());

        return robotCustomerDynamicContactCacheDTO;
    }


    /**
     * 针对已经过期的素材，需要异步重新上传
     */
    public void postReUploadExpireMaterial(RobotCustomerDynamicContactCacheDTO robotCustomerDynamicContactCacheDTO, Long landingPageWechatCustomerServiceId, String corpId, String state){

        try {
            SyntheticBackgroundImgBO bo = new SyntheticBackgroundImgBO();
            bo.setQrCode(robotCustomerDynamicContactCacheDTO.getQrCode())
                .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerServiceId)
                .setCorpId(corpId)
                .setRobotCustomerDynamicContactCacheDTO(robotCustomerDynamicContactCacheDTO)
                .setState(state);
            robotDynamicCustomerContactGenerateSender.postReUploadExpireMaterial(bo);
        }catch (Exception e){
            log.error("微信客服机器人获取活码, 针对已经过期的素材，发送消息进行重新上传素材异常", e);
        }
    }

    /**
     * 微信客服机器人获取活码, 重新上传素材
     * @param bo
     */
    public void reUploadExpireMaterial(SyntheticBackgroundImgBO bo){
        //已经过期,需要重新生成,兜底
        String path = landingPageWechatCustomerContactConfig.getRobotCustomerDynamicContactPath() + bo.getState() + ".png";
        Long landingPageWechatCustomerServiceId = bo.getLandingPageWechatCustomerServiceId();
        String qrCode = bo.getQrCode();
        RobotCustomerDynamicContactCacheDTO dto = bo.getRobotCustomerDynamicContactCacheDTO();
        //取微信客服机器人，动态渠道二维码的基础配置信息
        RobotCustomerDynamicContact robotCustomerDynamicContact = robotCustomerDynamicContactService.getByLandingPageServiceId(landingPageWechatCustomerServiceId);
        if (Objects.nonNull(robotCustomerDynamicContact)) {
            EnterpriseWechatTempMaterial enterpriseWechatTempMaterial = reUploadMaterial(qrCode, robotCustomerDynamicContact, path, new EnterpriseWechat().setCorpid(bo.getCorpId()));
            if (Objects.nonNull(enterpriseWechatTempMaterial)) {
                dto.setMaterialId(String.valueOf(enterpriseWechatTempMaterial.getId())).setExpireAt(enterpriseWechatTempMaterial.getExpireAt()).setDynamicMaterialId(enterpriseWechatTempMaterial.getMaterialId());
                //异步修改此state已经改变的素材id
                robotDynamicCustomerContactGenerateSender.changeMaterialIdByState(dto);
            }
        }
    }


    /**
     * 重新上传素材
     * @param qrCode 动态渠道二维码
     * @param robotCustomerDynamicContact
     * @param path 七牛云存储路径
     * @param enterpriseWechat 企业微信信息
     * @return
     */
    public EnterpriseWechatTempMaterial reUploadMaterial(String qrCode,RobotCustomerDynamicContact robotCustomerDynamicContact, String path, EnterpriseWechat enterpriseWechat){
        try {
            if (Objects.isNull(robotCustomerDynamicContact)){
                log.info("微信客服机器人重新重新上传素材, 背景设置信息不能为空");
                return null;
            }
            RobotCustomerContactDto robotCustomerContactDto = new RobotCustomerContactDto();
            robotCustomerContactDto.setBackgroundUrl(robotCustomerDynamicContact.getBackgroundUrl());
            robotCustomerContactDto.setBackgroundHeight(robotCustomerDynamicContact.getBackgroundHeight());
            robotCustomerContactDto.setQrCodeWidth(robotCustomerDynamicContact.getQrCodeWidth());
            robotCustomerContactDto.setQrCodeIndexLeft(robotCustomerDynamicContact.getQrCodeIndexLeft());
            robotCustomerContactDto.setQrCodeIndexTop(robotCustomerDynamicContact.getQrCodeIndexTop());
            log.info("重新上传微信客服机器人动态渠道二维码素材, robotCustomerContactDto = {}", JSONObject.toJSONString(robotCustomerContactDto));
            EnterpriseWechatTempMaterial material = robotCustomerContactService.drawImage(robotCustomerContactDto, qrCode, path, enterpriseWechat);
            return material;
        }catch (Exception e){
            log.error("重新上传微信客服机器人动态渠道二维码素材出现异常", e);
        }
        return null;
    }


    /**
     * corpId:机器人所属的企业微信ID
     * 生成新的动态渠道二维码,补充二维码的消耗
     */
    private void generateRobotDynamicContact(long landingPageWechatCustomerServiceId, String landingPageWechatCustomerServiceWechatUserId, String corpId, Long advertiserAccountGroupId,
                                         LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerifyStatus, EnterpriseWechat enterpriseWechat,
                                             LandingPageWechatCustomerServiceRedisDto customerServiceRedisDto ) {
        //当前客服缓存二维码数量
        Long customerContactCacheSize = landingPageWechatCustomerServiceRedis.getRobotCustomerDynamicContactCacheSize(corpId, landingPageWechatCustomerServiceWechatUserId);
        log.info("生成新的动态渠道二维码,补充二维码的消耗, 当前客服缓存剩余的二维码数量 = {}", customerContactCacheSize);
        //规定的单个客服缓存中生成二维码的总量
        int generateNum = landingPageWechatCustomerContactConfig.getRobotCustomerDynamicGenerateNum();
        if (customerContactCacheSize >= generateNum) {
            log.info("实时生成微信客服机器人内的动态渠道二维码,缓存中已存在此客服的缓存，数量为:{} 大于等于设置的最大值:{},无需新增多个联系我二维码!wechatUserId:{}", customerContactCacheSize, generateNum, landingPageWechatCustomerServiceWechatUserId);
            return;
        }
       //查询基础的配置信息（背景图这些）
        RobotCustomerContactDto robotCustomerDynamicContactDTO = landingPageWechatCustomerServiceRedis.getRobotCustomerDynamicContactCache(landingPageWechatCustomerServiceId);

        if (Objects.isNull(robotCustomerDynamicContactDTO)) {
            log.info("缓存中，没有查到微信客服机器人活码背景图基础设置，进行查询数据库, agentid = {}", TenantContextHolder.get());
            RobotCustomerDynamicContact robotCustomerDynamicContact =  robotCustomerDynamicContactService.getRobotCustomerDynamicContactBasicConfigCache(landingPageWechatCustomerServiceId);
            if (Objects.nonNull(robotCustomerDynamicContact)){
                robotCustomerDynamicContactDTO = new RobotCustomerContactDto();
                BeanUtils.copyProperties(robotCustomerDynamicContact, robotCustomerDynamicContactDTO);
                landingPageWechatCustomerServiceRedis.setRobotCustomerDynamicContactBasicConfigCache(robotCustomerDynamicContactDTO, landingPageWechatCustomerServiceId);
            }
        }

        if (Objects.nonNull(enterpriseWechat) && Objects.nonNull(customerServiceRedisDto)) {
            LandingPageWechatCustomerContactVerifyStatus verifyStatus = (Objects.nonNull(customerServiceRedisDto.getRobotCustomerDynamicContactVerify()) && customerServiceRedisDto.getRobotCustomerDynamicContactVerify() == 1)
                ? LandingPageWechatCustomerContactVerifyStatus.ENABLE : LandingPageWechatCustomerContactVerifyStatus.DISABLE;
            RobotCustomerDynamicContactDTO message = new RobotCustomerDynamicContactDTO()
                .setLandingPageWechatCustomerServiceWechatUserId(landingPageWechatCustomerServiceWechatUserId)
                .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerServiceId)
                .setAccessToken(enterpriseWechat.getAccessToken())
                .setEnterpriseWechatId(enterpriseWechat.getId())
                .setCorpId(customerServiceRedisDto.getCorpId())
                .setUploadMaterialCorpId(corpId)
                .setDetectionCustomerContactCompleteFlag(true)
                .setFailModifyStatus(true)
                .setWechatCustomerContactVerify(landingPageWechatCustomerContactVerifyStatus)
                .setAdvertiserAccountGroupId(advertiserAccountGroupId)
                .setOldWechatCustomerContactStatus(checkContactStatus(customerServiceRedisDto.getRobotCustomerDynamicContactStatus()) )
                //添加好友是否需要验证
                .setOldWechatCustomerContactVerify(verifyStatus)
                .setRobotCustomerContactDto(robotCustomerDynamicContactDTO)
                .setEnterpriseWechat(enterpriseWechat)
                .setRealTimeConsumerFlag(true)
                .setDelayFlag(true);
            robotDynamicCustomerContactGenerateSender.sendRealTimeGenerateQrCodeForCustomerService(message);
        }
    }


    public LandingPageWechatCustomerContactStatus checkContactStatus(Integer status){

        LandingPageWechatCustomerContactStatus landingPageWechatCustomerContactStatus = LandingPageWechatCustomerContactStatus.UNGENERATED;
        switch (status){
            case 1:
                landingPageWechatCustomerContactStatus = LandingPageWechatCustomerContactStatus.IN_FORMATION;
                break;
            case 2:
                landingPageWechatCustomerContactStatus = LandingPageWechatCustomerContactStatus.GENERATED;
                break;
            case 3:
                landingPageWechatCustomerContactStatus = LandingPageWechatCustomerContactStatus.CREATION_FAILURE;
                break;
            default:
                break;
        }
        return landingPageWechatCustomerContactStatus;
    }


    /**
     * 查询同一访客缓存信息
     *
     */
    private LandingPageWechatCustomerServiceRedisDto getSameCustomer(Long wechatCustomerServiceGroupId, String externalUserId, String openKfid, List<LandingPageWechatCustomerServiceRedisDto> landingPageWechatCustomerServiceList
          ,Long wechatCustomerServiceRobotId) {
        log.info("获取微信客服机器人动态渠道二维码,查询同一访客缓存信息 , wechatCustomerServiceGroupId = {}, externalUserId = {}, wechatCustomerServiceRobotId = {}", wechatCustomerServiceGroupId, externalUserId, wechatCustomerServiceRobotId);
        if (wechatCustomerServiceGroupId == null || StringUtils.isBlank(externalUserId) || StringUtils.isBlank(openKfid)) {
            return null;
        }
        String robotId = Objects.nonNull(wechatCustomerServiceRobotId) ? String.valueOf(wechatCustomerServiceRobotId) : openKfid;
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(RedisConstant.ROBOT_DYNAMIC_QR_CODE_DATA_KEY).append(robotId)
            .append(":externalUserId:").append(externalUserId)
            .append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
        String key = keyBuilder.toString();
        log.info("获取微信客服机器人动态渠道二维码,查询同一访客缓存信息 , key = {}", key);
        Object redisValue = objectRedisTemplate.opsForValue().get(key);
        if (Objects.isNull(redisValue)) {
            return null;
        }
        Long wechatCustomerServiceId = JSON.parseObject(redisValue.toString(), LandingPageWechatCustomerServiceRedisDto.class).getId();
        LandingPageWechatCustomerServiceRedisDto serviceRedisDto = null;
        if (Objects.nonNull(wechatCustomerServiceId)) {
            serviceRedisDto = landingPageWechatCustomerServiceList.stream().filter(service -> wechatCustomerServiceId.equals(service.getId())).findFirst().orElse(null);
        }
        return serviceRedisDto;
    }

    /**
     * 保存同一访客缓存信息
     *
     * @param customerServiceRedisDto
     * @param wechatCustomerServiceGroupId
     * @param externalUserId
     * @param openKfid
     */
    private void saveSameCustomer(LandingPageWechatCustomerServiceRedisDto customerServiceRedisDto, Long wechatCustomerServiceGroupId, String externalUserId, String openKfid, Long wechatCustomerServiceRobotId) {
        log.info("获取微信客服机器人动态渠道二维码,设置同一访客缓存信息 , wechatCustomerServiceGroupId = {}, externalUserId = {}, wechatCustomerServiceRobotId = {}", wechatCustomerServiceGroupId, externalUserId, wechatCustomerServiceRobotId);
        if (wechatCustomerServiceGroupId == null || StringUtils.isBlank(externalUserId) || StringUtils.isBlank(openKfid)) {
            return;
        }
        String robotId = Objects.nonNull(wechatCustomerServiceRobotId) ? String.valueOf(wechatCustomerServiceRobotId) : openKfid;
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(RedisConstant.ROBOT_DYNAMIC_QR_CODE_DATA_KEY).append(robotId)
            .append(":externalUserId:").append(externalUserId)
            .append(":wechatCustomerServiceGroupId:").append(wechatCustomerServiceGroupId);
        String key = keyBuilder.toString();
        objectRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(customerServiceRedisDto), (ONE_DAY * identifyQrcodeCacheConfig.getDay()), TimeUnit.DAYS);
    }


    /**
     * 修改联系我二维码使用状态
     *
     * @param robotCustomerDynamicContactDTO
     */
    public void changeQrCodeUseStatus(RobotCustomerDynamicContactDTO robotCustomerDynamicContactDTO) {
        //基于自归因参数 更新
        LambdaUpdateWrapper<RobotDynamicCustomerContactGenerateRecord> updateWrapper = new LambdaUpdateWrapper<RobotDynamicCustomerContactGenerateRecord>()
            .set(RobotDynamicCustomerContactGenerateRecord::getUsedStatus, LandingPageWechatCustomerContactUsedStatus.USED)
            .set(RobotDynamicCustomerContactGenerateRecord::getUsedAt, Instant.now())
            .eq(RobotDynamicCustomerContactGenerateRecord::getRobotWechatCustomerContactState, robotCustomerDynamicContactDTO.getState());
        //更新公库
        update(updateWrapper);
    }

    /**
     * 修改动态渠道二维码素材ID
     *
     * @param robotCustomerDynamicContactDTO
     */
    public void changeMaterialIdByState(RobotCustomerDynamicContactCacheDTO robotCustomerDynamicContactDTO) {
        log.info("素材过期，重新更新修改动态渠道二维码素材ID, robotCustomerDynamicContactDTO = {}", robotCustomerDynamicContactDTO);
        String state = robotCustomerDynamicContactDTO.getState();
        String materialId = robotCustomerDynamicContactDTO.getMaterialId();
        Instant expireAt = robotCustomerDynamicContactDTO.getExpireAt();
        if (StringUtils.isNotBlank(state) && StringUtils.isNotBlank(materialId)) {
            update(new LambdaUpdateWrapper<RobotDynamicCustomerContactGenerateRecord>()
                .set(RobotDynamicCustomerContactGenerateRecord::getMaterialId, materialId)
                .set(Objects.nonNull(expireAt) , RobotDynamicCustomerContactGenerateRecord::getExpireAt, expireAt)
                .eq(RobotDynamicCustomerContactGenerateRecord::getRobotWechatCustomerContactState, state));
        }
    }


    /**
     * 清除机器人动态渠道二维码相关的记录
     * @param landingPageWechatCustomerService 微信客服信息
     *  @param flag 是否设置客服表信息
     */
    public void checkRobotCustomerDynamicContactClean(LandingPageWechatCustomerService landingPageWechatCustomerService, boolean flag) {

        String agentId = TenantContextHolder.get();
        String wechatUserId = landingPageWechatCustomerService.getWechatUserId();
        if (StringUtils.isBlank(wechatUserId) || StringUtils.isBlank(agentId)){
            log.info("清除机器人动态渠道二维码相关的记录,参数为空,不做处理。 agentId = {}, wechatUserId = {}, flag = {}",agentId, wechatUserId, flag);
            return;
        }
        String corpId = landingPageWechatCustomerService.getCorpId();

        if (StringUtils.isBlank(wechatUserId)) {
            return;
        }
        EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatCacheByCorpId(corpId);
        if (Objects.isNull(enterpriseWechat)) return;
        //查询当前账户是否还存在绑定了此userid的客服
        int count = landingPageWechatCustomerServiceService.count(Wrappers.lambdaQuery(LandingPageWechatCustomerService.class)
            .eq(LandingPageWechatCustomerService::getWechatUserId, wechatUserId));
        if (count > 0) {
            log.info("当前账户还存在绑定了此userid的客服，agentId:{},userId:{},数量：{}", agentId, wechatUserId, count);
            return;
        }

        //查询公库二维码客服实时信息
        WorkWechatUserVisibleRangeRobotCustomerContact  contactServiceOne = workWechatUserVisibleRangeRobotCustomerContactService.getOne(Wrappers.lambdaQuery(WorkWechatUserVisibleRangeRobotCustomerContact.class)
            .eq(WorkWechatUserVisibleRangeRobotCustomerContact::getUserId, wechatUserId)
            .eq(WorkWechatUserVisibleRangeRobotCustomerContact::getAgentId, agentId)
            .last("limit 1"));
        if (Objects.nonNull(contactServiceOne)){
            //删除背景图配置
            LambdaQueryWrapper<RobotCustomerDynamicContact> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RobotCustomerDynamicContact::getLandingPageWechatCustomerServiceId, contactServiceOne.getId());
            robotCustomerDynamicContactService.remove(queryWrapper);
            //删除生成状态记录表
            LambdaQueryWrapper<WorkWechatUserVisibleRangeRobotCustomerContact> visibleQueryWrapper = new LambdaQueryWrapper<>();
            visibleQueryWrapper.eq(WorkWechatUserVisibleRangeRobotCustomerContact::getUserId, contactServiceOne.getUserId());
            workWechatUserVisibleRangeRobotCustomerContactService.remove(visibleQueryWrapper);
            //清除缓存中的动态渠道基础信息
            landingPageWechatCustomerServiceRedis.deleteRobotCustomerDynamicContactCache(landingPageWechatCustomerService.getId());
        }
        log.info("取消可见范围,微信客服机器人动态渠道二维码,是否重置客服表信息，flag = {}", flag);
        Long id = landingPageWechatCustomerService.getId();
        if (flag) {

            log.info("取消可见范围,微信客服机器人动态渠道二维码，清空微信客服机器人动态渠道二维码配置, id = {}", id);
            //置空客服记录上对应机器人活码相关数据
            landingPageWechatCustomerServiceService.update(new LambdaUpdateWrapper<LandingPageWechatCustomerService>()
                .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactMaterialId, null)
                .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactQrCode, null)
                .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactState, null)
                .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactQiniuPath, null)
                .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactBackgroundUrl, null)
                .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactStatus, LandingPageWechatCustomerContactStatus.UNGENERATED)
                .set(LandingPageWechatCustomerService::getRobotCustomerDynamicContactVerify, LandingPageWechatCustomerContactVerifyStatus.DISABLE)
                .eq(LandingPageWechatCustomerService::getId, landingPageWechatCustomerService.getId()));
        }
//        List<LandingPageWechatCustomerService> list = new ArrayList<>();
//        list.add(landingPageWechatCustomerService);
//        log.info("清除机器人动态渠道二维码相关的记录, flag = {}, id = {}", flag, id);
//        batchDelete(Arrays.asList(id), list, null);
    }


    /**
     * 取消授权-清除联系我二维码缓存及数据库数据
     *
     * @param corpId
     */
    public void cancelAuthDeleteQrCode(String corpId) {
        //查询联系我二维码中此企微关联的userid - 公库
        List<String> list = this.baseMapper.getWechatUserIdsByCorpId(corpId);
        String agentId = TenantContextHolder.get();
        log.info("取消授权-清除联系我二维码缓存及数据库数据, agentId = {}, corpId = {}", agentId, corpId);
        list.stream().forEach(e -> {
            //清除当前客服联系我二维码缓存 - 公库
            landingPageWechatCustomerServiceRedis.clearRobotCustomerDynamicContactPublicCache(corpId, e);

        });

        //将客服表微信客服机器人相关的信息置空
        landingPageWechatCustomerServiceService.clearRobotCustomerDynamicContactQrCode(corpId, agentId);

        //删除临时素材对应的七牛云资源
        CompletableFuture.supplyAsync(() -> execSearchAndCleanQiniu(corpId), beanCleanUsedOfficialCustomerContactQiniuThreadPool).exceptionally(e -> {
            log.error("取消授权-,清除机器人客服临时素材二维码对应的七牛云资源! corpId:{}", corpId, e);
            return null;
        });
    }


    /**
     * 查询并清除
     *
     * @param corpId
     */
    private boolean execSearchAndCleanQiniu(String corpId) {
        Page<RobotDynamicCustomerContactGenerateRecord> page = new Page<>(1, 300);
        LambdaQueryWrapper<RobotDynamicCustomerContactGenerateRecord> queryWrapper = new LambdaQueryWrapper<RobotDynamicCustomerContactGenerateRecord>()
            .eq(RobotDynamicCustomerContactGenerateRecord::getCorpId, corpId);
        Page<RobotDynamicCustomerContactGenerateRecord> contactPage = page(page, queryWrapper);
        long pages = contactPage.getPages();
        if (pages <= 0) {
            return true;
        }
        //还未删除记录,循环页数即可
        for (int i = 1; i <= pages; i++) {
            page = new Page<>(i, 300);
            execDeleteQiniu(page, queryWrapper);
        }
        //清完七牛云记录 全部清除
        boolean remove = remove(new LambdaQueryWrapper<RobotDynamicCustomerContactGenerateRecord>()
            .eq(RobotDynamicCustomerContactGenerateRecord::getCorpId, corpId));
        log.info("===>取消授权-清除联系我二维码数据库数据结果:{}", remove);
        //清除此corpId在公库企微公众号信息上永久素材的七牛云链接
        List<WorkWechatUserVisibleRangeRobotCustomerContact> list = workWechatUserVisibleRangeRobotCustomerContactService.list(new LambdaQueryWrapper<WorkWechatUserVisibleRangeRobotCustomerContact>()
            .eq(WorkWechatUserVisibleRangeRobotCustomerContact::getCorpId, corpId));
        if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
            list.stream().filter(e -> (StringUtils.isNotBlank(e.getQiniuPath()) && !"/".equals(e.getQiniuPath())))
                .map(WorkWechatUserVisibleRangeRobotCustomerContact::getQiniuPath).forEach(e -> {
                    try {
                        fileUpload.delete(e);
                    } catch (Exception ex) {
                        log.info("取消授权-删除客服永久素材七牛云资源失败,qiniuPath:{}", e, ex);
                    }
                });
            //清除此企微公库记录
            workWechatUserVisibleRangeRobotCustomerContactService.remove(new LambdaQueryWrapper<WorkWechatUserVisibleRangeRobotCustomerContact>()
                .eq(WorkWechatUserVisibleRangeRobotCustomerContact::getCorpId, corpId));
        }
        return true;
    }


    private void execDeleteQiniu(Page<RobotDynamicCustomerContactGenerateRecord> page, LambdaQueryWrapper<RobotDynamicCustomerContactGenerateRecord> queryWrapper) {
        Page<RobotDynamicCustomerContactGenerateRecord> contactPage = page(page, queryWrapper);
        List<RobotDynamicCustomerContactGenerateRecord> records = contactPage.getRecords();
        if (!org.springframework.util.CollectionUtils.isEmpty(records)) {
            records.stream().forEach(e -> {
                String qiniuPath = e.getDynamicRobotWechatCustomerContactQrCode();
                if (StringUtils.isNotBlank(qiniuPath) && !"/".equals(qiniuPath)) {
                    fileUpload.delete(qiniuPath);
                }
            });
        }
    }

    /**
     * 加粉成功之后，删除机器人动态渠道二维码
     * @param state 自归因参数
     * @param userId 客服的userId
     */
    public void deleteRobotLIveQrCode(String state, String userId, String corpId, EnterpriseWechat enterpriseWechat) {
        try {
            log.info("加粉成功之后, 删除机器人动态渠道二维码, 入参 state = {}, userId = {}, corpId = {} ", state, userId, corpId);
            if (StringUtils.isBlank(state) || StringUtils.isBlank(userId) || StringUtils.isBlank(corpId)) {
                log.info("加粉成功之后, 删除机器人动态渠道二维码,参数不能为空");
                return;
            }
            RobotDynamicCustomerContactGenerateRecord one = this.lambdaQuery().eq(RobotDynamicCustomerContactGenerateRecord::getRobotWechatCustomerContactState, state)
                .eq(RobotDynamicCustomerContactGenerateRecord::getLandingPageWechatCustomerServiceWechatUserId, userId)
                .eq(RobotDynamicCustomerContactGenerateRecord::getCorpId, corpId).last("limit 1").one();
            if (Objects.nonNull(one)) {
                log.info("加粉成功之后，删除机器人动态渠道二维码,查询到的活码记录ID = {}，素材ID = {}", one.getId(), one.getMaterialId());
                RobotCustomerDynamicContactDeleteDto robotDynamicCustomerContactDeleteDto = new RobotCustomerDynamicContactDeleteDto()
                    .setEnterpriseWechat(enterpriseWechat)
                    .setRobotDynamicCustomerContactGenerateRecord(one)
                    .setCorpId(corpId)
                    .setConfigId(one.getConfigId())
                    .setWechatUserId(one.getLandingPageWechatCustomerServiceWechatUserId())
                    .setFlag(true);
                log.info("加粉成功之后，发送消息进行删除机器人动态渠道二维码, robotDynamicCustomerContactDeleteDto = {}", JSONObject.toJSONString(robotDynamicCustomerContactDeleteDto));
                robotDynamicCustomerContactGenerateSender.sendDeleteSingleQrCode(robotDynamicCustomerContactDeleteDto);
            } else {
                log.info("加粉成功之后，删除机器人动态渠道二维码,没有查到活码记录");
            }
        }catch (Exception e){
            log.error("加粉成功之后，删除机器人动态渠道二维码出现异常, state = {}, userId = {}, corpId = {} ", state, userId, corpId, e);
        }
    }


    /**
     * 清除生成过多的动态渠道二维码
     * @param corpId 企业微信ID
     * @param userId 客服userId
     */
    public void queryAndDeleteRecordByCorpIdAndUserId(String corpId, String userId) {
      if (StringUtils.isBlank(corpId)){
          log.error("查询机器人动态渠道二维码记录，企业ID不能为空");
          return;
      }
      List<RobotDynamicCustomerContactGenerateRecord> list =   this.lambdaQuery()
            .eq(RobotDynamicCustomerContactGenerateRecord::getCorpId, corpId)
            .eq(RobotDynamicCustomerContactGenerateRecord::getLandingPageWechatCustomerServiceWechatUserId, userId)
            .list();

      EnterpriseWechat enterpriseWechat = enterpriseWechatService.getByCorpId(corpId);
      if (!CollectionUtils.isEmpty(list)){
          //调用企业微信接口，进行删除远程的二维码
          if (list.size() > 20){
              int index = 1;
              for (int i = 20; i < list.size(); i++) {
                  RobotDynamicCustomerContactGenerateRecord one = list.get(i);
                  RobotCustomerDynamicContactDeleteDto robotDynamicCustomerContactDeleteDto = new RobotCustomerDynamicContactDeleteDto()
                      .setEnterpriseWechat(enterpriseWechat)
                      .setRobotDynamicCustomerContactGenerateRecord(one)
                      .setCorpId(corpId)
                      .setFlag(true);
                  log.info("手动触发，发送消息进行删除机器人动态渠道二维码,发送第{}个消息, corpId = {}, wechatUserId = {}, configId = {},  state = {}", index, one.getCorpId(), one.getConfigId(), one.getLandingPageWechatCustomerServiceWechatUserId(), one.getRobotWechatCustomerContactState());
                  robotDynamicCustomerContactGenerateSender.sendDeleteSingleQrCode(robotDynamicCustomerContactDeleteDto);
                  index++;
              }
          }
      }
    }

    /**
     * 清除企业微信端微信客服机器人活码
     * @param bo
     */
    public void clearRemoteDynamicQrCode(ContactRemoteClearVO bo) {
        try{
            String agentId = bo.getAgentId();
            String userId = bo.getUserId();
            String corpId = bo.getCorpId();
            String configId = bo.getConfigId();
            log.info("清除企业微信端微信客服机器人活码, agentId = {}, userId = {}, corpId = {}, configId = {}", agentId, userId, corpId, configId);
            TenantContextHolder.set(agentId);
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(corpId)) {
                log.info("清除企业微信端微信客服机器人活码失败，userId或corpId为空");
                return;
            }
            List<LandingPageWechatCustomerService> list = landingPageWechatCustomerServiceService.queryCustomerServiceList(userId, corpId,  agentId);
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            List<Long> ids = list.stream().map(LandingPageWechatCustomerService::getId).collect(Collectors.toList());
            batchDelete(ids, list, configId, null);
        }catch (Exception e){
            log.error("清除企业微信端微信客服机器人活码失败,bo = {}", JSON.toJSONString(bo), e);
        }
    }

    /**
     * 异步删除微信客服机器人动态渠道二维码
     */
    public void synContactBatchDelete(DynamicBatchDeleteDTO dto) {
        robotDynamicCustomerContactGenerateSender.synContactBatchDelete(dto);
    }

    /**
     * 微信客服机器人自动回复，取不到码的时候，检查是否需要重新初始化活码或者续期
     * @param dto
     */
    public void checkRobotDynamicQrCode(RobotDynamicQrCodeCheckDTO dto) {
        try{
           log.info("微信客服机器人自动回复，取不到码的时候，检查是否需要重新初始化活码或者续期,bo = {}", JSON.toJSONString(dto));
            String corpId = dto.getCorpId();
            Long wechatCustomerServiceGroupId = dto.getWechatCustomerServiceGroupId();
            String lockKey = "checkRobotDynamicQrCode:"+corpId+":"+wechatCustomerServiceGroupId;
            RLock fairLock = null;
            fairLock = redissonClient.getFairLock(lockKey);
            //尝试加锁，最多等待10MINS
            boolean res = fairLock.tryLock(0,10, TimeUnit.MINUTES);
            if (!res) {
                log.info("微信客服机器人自动回复，取不到码的时候，检查是否需要重新初始化活码或者续期获取锁失败,lockKey = [{}]", lockKey);
                return ;
            }

            //查询该分组下设置了动态渠道二维码的客服
            List<LandingPageWechatCustomerService> list = landingPageWechatCustomerServiceService.queryUserIdForInitRobotDynamicQrCode(wechatCustomerServiceGroupId);
            if (Objects.nonNull(list) && !list.isEmpty()){
                for (LandingPageWechatCustomerService landingPageWechatCustomerService : list) {
                    String wechatUserId = landingPageWechatCustomerService.getWechatUserId();
                    boolean hasKey = landingPageWechatCustomerServiceRedis.hasRobotCustomerDynamicContactCache(corpId, wechatUserId);
                    log.info("微信客服机器人自动回复，取不到同主体的码,判断缓存中是否有客服{}在{}企业微信下的素材，hasKey = {}", wechatUserId, corpId, hasKey);
                    if (!hasKey){
                        //缓存中不存在的话，需要补充生成这个企业微信下的活码并上传素材到对应的企业微信
                        log.info("微信客服机器人自动回复，取不到同主体的码,缓存中不存在客户{}的活码, corpId = {}", wechatUserId, corpId);
                        generateRobotDynamicQrCodeForCustomerService(landingPageWechatCustomerService, corpId);
                    }else {
                        //缓存中存在的话，判断缓存中的素材id是否存在，不存在的或者素材已经过期，则进行续期，因为合成图片的服务比较慢，存在发消息的时候，还没有合并完图片，导致缓存中没有素材ID
                        //便历数组，拿到state参数，然后检查是否存在合成后的素材对象，没有的话，进行续期（robot_wechat_customer_dynamic_contact_qr_code_material_id:里面的对象）
                        String accessToken = enterpriseWechatService.getAccessTokenByCorpId(corpId);
                        if (StringUtils.isBlank(accessToken)) {
                            return;
                        }
                        List<RobotCustomerDynamicContactCacheDTO> robotCustomerDynamicContactCacheList = landingPageWechatCustomerServiceRedis.getAllRobotCustomerContactClean(corpId, wechatUserId);
                        if (Objects.nonNull(robotCustomerDynamicContactCacheList)) {
                            for (RobotCustomerDynamicContactCacheDTO robotCustomerDynamicContactCacheDTO : robotCustomerDynamicContactCacheList) {
                                String state = robotCustomerDynamicContactCacheDTO.getState();
                                if (StringUtils.isBlank(state)) {
                                    return;
                                }
                                RobotDynamicQrCodeDTO dynamicQrCodeDTO = landingPageWechatCustomerServiceRedis.getRobotDynamicQrCodeMaterialId(corpId, wechatUserId, state);
                                if (Objects.nonNull(dynamicQrCodeDTO)) {
                                    Instant expireAt = dynamicQrCodeDTO.getExpireAt();
                                    Instant now = Instant.now();
                                    log.info("素材过期，重新进行续期, corpId = {}, wechatUserId = {}, state = {}, 素材过期时间：{},当前系统时间:{}", corpId, wechatUserId, state, expireAt, now);
                                    log.info("遍历缓存的数组，判断是否需要进行素材续期, corpId = {}, wechatUserId = {}, state = {}, 素材过期时间：{},当前系统时间:{}", corpId, wechatUserId, state, expireAt, now);
                                    if (Objects.nonNull(expireAt) && expireAt.isBefore(now)) {
                                        //重新上传素材
                                        String resultUrl = dynamicQrCodeDTO.getMaterialPath();
                                        if (StringUtils.isNotBlank(resultUrl)) {
                                            InputStream inputStream = FileUtil.getInputStream(resultUrl);
                                            MultipartFile multipartFile = FileUtil.getMultipartFile(inputStream, UUID.randomUUID() + ".png");
                                            TempMaterialResponseBody tempMaterialResponseBody = workWeixinApiClient.uploadTempFile(accessToken, EnterpriseTempMaterialType.IMAGE.getName(), multipartFile);
                                            String mediaId = tempMaterialResponseBody.getMediaId();
                                            //把素材ID设置到缓存里面
                                            CompositeQrCodeBackgroundImageDto compositeImageDto = new CompositeQrCodeBackgroundImageDto();
                                            compositeImageDto.setEnterpriseWechatCorpId(corpId);
                                            compositeImageDto.setMediaId(mediaId);
                                            compositeImageDto.setLandingPageWechatCustomerContactId(robotCustomerDynamicContactCacheDTO.getId());
                                            compositeImageDto.setWechatUserId(landingPageWechatCustomerService.getWechatUserId());
                                            compositeImageDto.setState(state);
                                            this.saveRobotDynamicBackgroundImg(compositeImageDto, true);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }catch (Exception e){
            log.error("微信客服机器人自动回复，取不到码的时候，检查是否需要重新初始化活码或者续期出现异常,bo = {}", JSON.toJSONString(dto), e);
        }
    }

    public void generateRobotDynamicQrCodeForCustomerService(LandingPageWechatCustomerService landingPageWechatCustomerService, String corpId){

        int generateNum = landingPageWechatCustomerContactConfig.getRobotCustomerDynamicGenerateNum();
        EnterpriseWechat enterpriseWechat =  enterpriseWechatService.getEnterpriseWechatCacheByCorpId(corpId);

        if (Objects.isNull(enterpriseWechat)){
            return;
        }
        String accessToken = enterpriseWechat.getAccessToken();
        //查询基础的配置信息（背景图这些）
        RobotCustomerContactDto dto = landingPageWechatCustomerServiceRedis.getRobotCustomerDynamicContactCache(landingPageWechatCustomerService.getId());
        log.info("自动回复消息，检测到活码是异主体，进行检测和生成, 背景图设置dto:{}", JSONObject.toJSONString(dto));
        if (Objects.nonNull(dto)) {
            RobotCustomerDynamicContactDTO message = new RobotCustomerDynamicContactDTO()
                .setLandingPageWechatCustomerServiceWechatUserId(landingPageWechatCustomerService.getWechatUserId())
                .setLandingPageWechatCustomerServiceId(landingPageWechatCustomerService.getId())
                .setWechatUserName(landingPageWechatCustomerService.getWechatUserName())
                .setAccessToken(accessToken)
                .setEnterpriseWechatId(enterpriseWechat.getId())
                .setCorpId(landingPageWechatCustomerService.getCorpId())
                .setUploadMaterialCorpId(corpId)
                .setDetectionCustomerContactCompleteFlag(true)
                .setFailModifyStatus(true)
                //这里要核对一下验证方式是否正确
                .setWechatCustomerContactVerify(landingPageWechatCustomerService.getRobotCustomerDynamicContactVerify())
                .setAdvertiserAccountGroupId(landingPageWechatCustomerService.getAdvertiserAccountGroupId())
                .setOldWechatCustomerContactStatus(landingPageWechatCustomerService.getRobotCustomerDynamicContactStatus())
                .setOldWechatCustomerContactVerify(landingPageWechatCustomerService.getRobotCustomerDynamicContactVerify())
                .setRobotCustomerContactDto(dto)
                .setEnterpriseWechat(enterpriseWechat)
                .setRealTimeConsumerFlag(false)
                .setRobotConfigSaveFlag(true)
                .setDelayFlag(true);

            for (int i = 0; i < generateNum; i++) {
                robotDynamicCustomerContactGenerateSender.sendGenerateQrCodeForCustomerService(message);
            }
        }
    }
}
