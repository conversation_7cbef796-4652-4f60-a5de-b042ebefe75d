package ai.yiye.agent.landingpage.service;

import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.EnterpriseWechatsPmpRel;
import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.landingpage.EnterpriseWechat;
import ai.yiye.agent.landingpage.mapper.EnterpriseWechatsPmpRelMapper;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/4 14:12
 */

@Service
@DS(DbConstants.POSTGRESQL_DEFAULT)
public class EnterpriseWechatsPmpRelService extends ServiceImpl<EnterpriseWechatsPmpRelMapper, EnterpriseWechatsPmpRel> {

    /**
     * 根据企业微信查询绑定PMP的关系
     */
    public List<EnterpriseWechatsPmpRel> getPmpRelByEnterpriseWechat(Long enterpriseWechatsId) {
        return baseMapper.selectList(new LambdaQueryWrapper<EnterpriseWechatsPmpRel>().eq(EnterpriseWechatsPmpRel::getEnterpriseWechatsId, enterpriseWechatsId));
    }

    /**
     * 根据企业微信查询绑定PMP的关系
     */
    public List<EnterpriseWechatsPmpRel> getPmpRelByEnterpriseWechat(EnterpriseWechat ew, String agentId) {
        return baseMapper.selectList(new LambdaQueryWrapper<EnterpriseWechatsPmpRel>().eq(EnterpriseWechatsPmpRel::getAgentId, agentId).eq(EnterpriseWechatsPmpRel::getEnterpriseWechatsId, ew.getId()));
    }

    /**
     * 初始化企业微信pmp关系表corpid字段（仅首次上线执行一次，可重复执行，回退重复需要重新执行）
     */
    public void initEnterpriseWechatsPmpRelCorpidData() {
        baseMapper.initEnterpriseWechatsPmpRelCorpidData();
    }


    public Set<String> getCorpIdByPmpId(Long pmpId) {
        return baseMapper.getByPmpId(pmpId);
    }

    /**
     * 校验当前账号是否存在该企业微信
     */
    public void verifyPermissionsByAgentId(String corpId) {
        int count = baseMapper.selectCount(new LambdaQueryWrapper<EnterpriseWechatsPmpRel>()
            .eq(EnterpriseWechatsPmpRel::getCorpid, corpId)
            .eq(EnterpriseWechatsPmpRel::getAgentId, TenantContextHolder.get())
        );
        if (count <= 0) {
            throw new RestException("当前账号无此企业微信操作权限，请检查后操作！");
        }
    }

    //基于企微corpId集合查询agentId集合
    public Set<String> getAgentIdByCorpIds(Set<String> corpIds) {
        List<EnterpriseWechatsPmpRel> list = this.lambdaQuery().select(EnterpriseWechatsPmpRel::getAgentId)
            .in(EnterpriseWechatsPmpRel::getCorpid, corpIds).list();
        return list.stream().map(EnterpriseWechatsPmpRel::getAgentId).collect(Collectors.toSet());
    }
}
