package ai.yiye.agent.landingpage.service.workweixin.developUpgrade.handler;

import ai.yiye.agent.domain.WorkWechatCustomerAcquisitionLink;
import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.dto.WorkWechatCustomerAcquisitionLinkDTO;
import ai.yiye.agent.domain.enumerations.WechatCustomerAcquisitionLinkStatus;
import ai.yiye.agent.domain.landingpage.EnterpriseWechat;
import ai.yiye.agent.landingpage.dto.AcquisitionLinkAbnormalLogDto;
import ai.yiye.agent.landingpage.sender.LandingPageSender;
import ai.yiye.agent.landingpage.sender.UserOperationLogDetailActionSender;
import ai.yiye.agent.landingpage.service.EnterpriseWechatService;
import ai.yiye.agent.landingpage.service.WorkWechatCustomerAcquisitionLinkService;
import ai.yiye.agent.weixin.domain.xml.WxCpTpXmlModelMessage;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import me.chanjar.weixin.cp.tp.message.WxCpTpMessageHandler;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 企微获客助手-删除获客链接事件
 */

@DS(DbConstants.POSTGRESQL_DEFAULT)
@Slf4j
@Service
public class WorkWeixinDevelopUpgradeCustomerAcquisitionLinkDeleteMessageHandler implements WxCpTpMessageHandler {

    @Resource
    protected EnterpriseWechatService enterpriseWechatService;

    @Resource
    private WorkWechatCustomerAcquisitionLinkService workWechatCustomerAcquisitionLinkService;

    @Resource
    private LandingPageSender landingPageSender;

    @Resource
    private UserOperationLogDetailActionSender userOperationLogDetailActionSender;

    @Override
    public WxCpXmlOutMessage handle(WxCpTpXmlMessage wxCpTpXmlMessage, Map<String, Object> map, WxCpTpService wxCpTpService, WxSessionManager wxSessionManager) throws WxErrorException {
        if (Objects.isNull(wxCpTpXmlMessage)) {
            log.error("==>企业微信代开发V2-删除获客链接事件，终止处理：wxCpTpXmlMessage={}；context={}；wxCpService={}；sessionManager={}；", JSONObject.toJSONString(wxCpTpXmlMessage), map, wxCpTpService, wxSessionManager);
            return null;
        }
        log.info("==>企业微信代开发V2-删除获客链接事件======>> wxCpTpXmlModelMessage={}；context={}；wxCpService={}；sessionManager={}；", JSONObject.toJSONString(wxCpTpXmlMessage), map, wxCpTpService, wxSessionManager);
        WxCpTpXmlModelMessage wxCpTpXmlModelMessage = (WxCpTpXmlModelMessage) wxCpTpXmlMessage;
        //失效链接ID
        String linkId = wxCpTpXmlModelMessage.getLinkId();
        if (StringUtils.isBlank(linkId)) {
            log.info("企业微信代开发V2-删除获客链接事件,linkId为空!");
            return null;
        }
        String corpId = wxCpTpXmlModelMessage.getPlaintextCorpId();
        WorkWechatCustomerAcquisitionLink link = workWechatCustomerAcquisitionLinkService.getOne(new LambdaQueryWrapper<WorkWechatCustomerAcquisitionLink>()
            .eq(WorkWechatCustomerAcquisitionLink::getWechatCustomerAcquisitionLinkId, linkId)
            .last("limit 1"));
        if (link == null) {
            log.info("==>企业微信代开发V2-删除获客链接事件-查询不到当前客服信息:linkId:{},corpId:{}", linkId, corpId);
            return null;
        }
        WorkWechatCustomerAcquisitionLinkDTO dto = new WorkWechatCustomerAcquisitionLinkDTO();
        dto.setCorpId(link.getCorpId())
            .setUserId(link.getUserId())
            .setWechatCustomerAcquisitionLinkId(linkId)
            .setWechatCustomerAcquisitionLinkName(link.getWechatCustomerAcquisitionLinkName())
            .setAcquisitionChooseEnum(link.getAcquisitionChooseEnum())
            .setWechatCustomerAcquisitionLink(link.getWechatCustomerAcquisitionLink())
            .setWechatCustomerAcquisitionLinkStatus(WechatCustomerAcquisitionLinkStatus.ANOMALY)
            .setWechatCustomerAcquisitionLinkReason("链接在后台被删除")
            .setWechatCustomerAcquisitionLinkVerify(link.getWechatCustomerAcquisitionLinkVerify());
        landingPageSender.sendAcquisitionLink(dto);
        EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatCacheByCorpId(corpId);
        AcquisitionLinkAbnormalLogDto logDto = new AcquisitionLinkAbnormalLogDto();
        Long createTime = wxCpTpXmlModelMessage.getCreateTime();
        String time = DateUtil.formatDateTime(new Date(createTime * 1000));
        logDto.setCorpId(link.getCorpId())
            .setCorpName(Objects.nonNull(enterpriseWechat) ? enterpriseWechat.getCorpName() : "")
            .setUserId(link.getUserId())
            .setTime(time)
            .setOperDesc("客服获客助手链接异常<br>异常原因：链接在后台被删除");
        userOperationLogDetailActionSender.sendAcquisitionLinkAbnormalLog(logDto);
        return null;
    }
}
