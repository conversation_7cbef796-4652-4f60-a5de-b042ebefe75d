package ai.yiye.agent.landingpage.redis;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.domain.EnterpriseWechatCorpid;
import ai.yiye.agent.landingpage.service.EnterpriseWechatCorpidService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 企业微信缓存
 */
@Slf4j
@Component
public class EnterpriseWechatCorpidRedis extends RedisConstant {

    @Resource
    private StringRedisTemplate defaultStringRedisTemplate;

    @Resource
    private EnterpriseWechatCorpidService enterpriseWechatCorpidService;

    /**
     * 清除缓存
     *
     * @param key
     * @return
     */
    public Boolean deleteCache(String key) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        return defaultStringRedisTemplate.delete(key);
    }

    /**
     * openCorpIdToCorpId
     */
    public String openCorpIdToCorpId(String openCorpId) {
        if (StringUtils.isBlank(openCorpId)) {
            return null;
        }
        String key = ENTERPRISE_WECHAT_OPENCORPID_TO_CORPID_KEY + openCorpId;
        String corpId = defaultStringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(corpId)) {
            return corpId;
        }
        EnterpriseWechatCorpid enterpriseWechatCorpid = enterpriseWechatCorpidService.getOne(
            Wrappers.lambdaQuery(EnterpriseWechatCorpid.class)
                .eq(EnterpriseWechatCorpid::getOpenCorpid, openCorpId)
                .last("limit 1")
        );
        if (enterpriseWechatCorpid == null) {
            return null;
        }
        corpId = enterpriseWechatCorpid.getCorpid();
        defaultStringRedisTemplate.opsForValue().set(key, corpId, 2, TimeUnit.HOURS);
        return corpId;
    }

    /**
     * corpIdToOpenCorpId
     */
    public String corpIdToOpenCorpId(String corpId) {
        if (StringUtils.isBlank(corpId)) {
            return null;
        }
        String key = ENTERPRISE_WECHAT_CORPID_TO_OPENCORPID_KEY + corpId;
        String openCorpId = defaultStringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(openCorpId)) {
            return openCorpId;
        }
        EnterpriseWechatCorpid enterpriseWechatCorpid = enterpriseWechatCorpidService.getOne(
            Wrappers.lambdaQuery(EnterpriseWechatCorpid.class)
                .eq(EnterpriseWechatCorpid::getCorpid, corpId)
                .last("limit 1")
        );
        if (enterpriseWechatCorpid == null) {
            return null;
        }
        openCorpId = enterpriseWechatCorpid.getOpenCorpid();
        defaultStringRedisTemplate.opsForValue().set(key, openCorpId, 2, TimeUnit.HOURS);
        return openCorpId;
    }

}
