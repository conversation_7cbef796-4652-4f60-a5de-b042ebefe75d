package ai.yiye.agent.landingpage.config;

import ai.yiye.agent.autoconfigure.sentinel.fegin.SentinelFeign;
import ai.yiye.agent.landingpage.remote.WechatAppletDataQiyeApiClient;
import ai.yiye.agent.weixin.ObjectMapperHolder;
import ai.yiye.agent.weixin.exception.MarketingApiException;
import ai.yiye.agent.weixin.logger.MyLogger;
import com.alibaba.fastjson.JSONObject;
import feign.*;
import feign.codec.Decoder;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2022/7/12 12:13
 */
@Configuration
@Component
@Slf4j
public class QiyeTuiDataApiConfigration {
    @Autowired
    private QiYeTuiWechatAppletSecret qiYeTuiWechatAppletSecret;

    @Bean
    public WechatAppletDataQiyeApiClient wechatAppletDataQiyeApiClient(Request.Options weixinRequestoptions,Retryer weixinRequestretryer) {
        return SentinelFeign.builder()
            .options(weixinRequestoptions)
            .retryer(weixinRequestretryer)
            .logLevel(Logger.Level.FULL)
            .logger(new MyLogger())
            .encoder(new JacksonEncoder())
            .requestInterceptor(encodeSignInterceptor())
            .decoder(new WorkWechatQiyeDataResultDecoder(new JacksonDecoder(ObjectMapperHolder.getObjectMapper())))
            .target(WechatAppletDataQiyeApiClient.class, qiYeTuiWechatAppletSecret.getUrl() + "/");
    }

    public static RequestInterceptor encodeSignInterceptor() {
        return req -> {
            String method = req.method();
            Map<String, Collection<String>> headers = req.headers();
            String date = generateData();
            Collection<String> authorization = headers.get("Authorization");
            //第一次发起请求，生成对应Authorization,之后失败重试则不再重新获取
            if (CollectionUtils.isEmpty(authorization)){
                String secretId = (String) headers.get("secretId").toArray()[0];
                String secretKey = (String) headers.get("secretKey").toArray()[0];
                //source 任意值
                String authen = null;
                try {
                    authen = generateAuth(secretId, secretKey, date, "GenerateScheme");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                req.header("Authorization", authen);
                req.header("Date", date);
                req.header("Source", "GenerateScheme");
                req.removeHeader("secretId");
                req.removeHeader("secretKey");
            }
        };
    }

    public static String generateData() {
        Calendar cd = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, d MMM yyyy HH:mm:ss 'GMT'", Locale.US);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        return sdf.format(cd.getTime());
    }

    public static String generateAuth(String secretId, String secretKey, String date, String source) throws Exception {

        String date1 = "date: " + date + "\n" + "source: " + source;
        String signature = HmacSHA1Base64Encrypt(date1, secretKey);

        String authorization = "hmac id=" + '"' + secretId + '"'
            + ", algorithm=" + '"' + "hmac-sha1" + '"' + ",headers=" + '"' + "date source" + '"' + ", signature=" + '"' + signature + '"';

        return authorization;
    }

    public static String HmacSHA1Base64Encrypt(String encryptText, String encryptKey) throws Exception {
        byte[] data = encryptKey.getBytes("UTF-8");
        // 根据给定的字节数组构造一个密钥,第二参数指定一个密钥算法的名称
        SecretKey secretKey = new SecretKeySpec(data, "HMAC-SHA1");
        // 生成一个指定 Mac 算法 的 Mac 对象
        Mac mac = Mac.getInstance("HmacSHA1");
        // 用给定密钥初始化 Mac 对象
        mac.init(secretKey);
        byte[] text = encryptText.getBytes("UTF-8");
        // 完成 Mac 操作
        byte[] bytes = mac.doFinal(text);
        //进行base64编码
        String signature = new String(Base64.getEncoder().encode(bytes), "UTF-8");
        return signature;
    }

    public final static class WorkWechatQiyeDataResultDecoder implements Decoder {

        final Decoder delegate;

        public WorkWechatQiyeDataResultDecoder(Decoder delegate) {
            Objects.requireNonNull(delegate, "Decoder must not be null. ");
            this.delegate = delegate;
        }

        @Override
        public Object decode(Response response, Type type) throws IOException, FeignException {
            String resultStr = IOUtils.toString(response.body().asInputStream(), StandardCharsets.UTF_8);
            JSONObject resultObj = JSONObject.parseObject(resultStr);
            JSONObject res = resultObj.getJSONObject("Response");
            JSONObject error = res.getJSONObject("Error");
            String code = error.getString("Code");
//            Integer errcode = resultObj.getInteger("errcode");
            if (ObjectUtils.isNotEmpty(code) && !code.equals(0)) {
                String errmsg = error.getString("Message");
                if ("110011".equals(code)) {
                    log.warn("==>企业推上报事件错误:{},response:{}", errmsg, resultObj.toJSONString());
                }else if("BusinessError".equals(code)){
                    log.warn("==>企业推应用状态异常:{},response:{}", errmsg, resultObj.toJSONString());
                }else if("CorpNoAuthError".equals(code)){
                    log.warn("==>该机构未完成商家认证，不支持调用生成URLScheme接口:{},response:{}", errmsg, resultObj.toJSONString());
                } else if("InvalidError".equals(code)){
                    log.warn("==>失效异常:{},response:{}", errmsg, resultObj.toJSONString());
                } else {
                    log.error("work wechat qiyetui  api error ! code{} errmsg{}, response {}",code, errmsg, resultObj.toJSONString());
                    throw new MarketingApiException(errmsg, 500);
                }
            }
            return delegate.decode(response.toBuilder().body(resultObj.toJSONString(), StandardCharsets.UTF_8).build(), type);
        }

    }

    public static void main(String[] args) {
        String date = generateData();

        String secretId = "AKIDdph2xZVek3tcf3aq6zNFxdn8muqfawlh4kz9";
        String secretKey = "I78ZWrYv988qRBwkRHyta0ozvpdaxqgneN3ZOUVv";
        String authen = null;
        try {
            authen = generateAuth(secretId, secretKey, date, "GenerateScheme");
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("{}", authen);
        log.info("{}", date);
//        req.header("Authorization", authen);
//        req.header("Date", date);
//        req.header("Source", "GenerateScheme");
//        req.removeHeader("secretId");
//        req.removeHeader("secretKey");

    }

}
