package ai.yiye.agent.landingpage.utils;

import ai.yiye.agent.domain.LandingPageDomainBlackIp;
import ai.yiye.agent.domain.User;
import ai.yiye.agent.domain.enumerations.IpType;
import ai.yiye.agent.landingpage.dto.EnterpriseWechatImportDTO;
import ai.yiye.agent.landingpage.dto.IpImportDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellType;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class CsvUtil {

    public static List<EnterpriseWechatImportDTO> readXlsx(InputStream inStream) throws IOException {
        InputStream is = inStream;
        HSSFWorkbook xssfWorkbook = new HSSFWorkbook(is);
        List<EnterpriseWechatImportDTO> medicalWhiteListVOs = new ArrayList<EnterpriseWechatImportDTO>();
        EnterpriseWechatImportDTO medicalvo = null;
        HSSFSheet sheetAt = xssfWorkbook.getSheetAt(0);
        if (sheetAt == null) {
            return medicalWhiteListVOs;
        }
        for (int rowNum = 1; rowNum <= sheetAt.getLastRowNum(); rowNum++) {
            HSSFRow xssfRow = sheetAt.getRow(rowNum);
            if (xssfRow != null) {
                medicalvo = new EnterpriseWechatImportDTO();
                HSSFCell name = xssfRow.getCell(0);
                HSSFCell weight = xssfRow.getCell(1);
                medicalvo.setWcName(getStrValue(name));
                medicalvo.setQrCodeWeight(getIntValue(weight));
                medicalWhiteListVOs.add(medicalvo);
            }
        }
        return medicalWhiteListVOs;
    }

    public static List<LandingPageDomainBlackIp> readIpXlsx(InputStream inStream, final String domainName, final Long landingPageDomainId, final String userName, final Long userId) throws IOException {
        HSSFWorkbook xssfWorkbook = new HSSFWorkbook(inStream);
        List<LandingPageDomainBlackIp> ipList = new ArrayList<>();
        HSSFSheet sheetAt = xssfWorkbook.getSheetAt(0);
        if (Objects.isNull(sheetAt)) {
            return ipList;
        }
        String ip = null;
        for (int rowNum = 1; rowNum <= sheetAt.getLastRowNum(); rowNum++) {
            HSSFRow xssfRow = sheetAt.getRow(rowNum);
            if (xssfRow != null) {
                ip = getStrValue(xssfRow.getCell(0));
                if (StringUtils.isBlank(ip)) {
                    continue;
                }
                ipList.add(new LandingPageDomainBlackIp()
                    .setIp(ip)
                    .setLandingPageDomainId(landingPageDomainId)
                    .setIpType(IpType.BLACK_IP)
                    .setDomainName(domainName)
                    .setCreatedAt(Instant.now())
                    .setUserId(userId)
                    .setOperatorAccountName(userName)
                );
            }
        }
        return ipList;
    }

    private static String getIntValue(HSSFCell xssfRow) {
        if (xssfRow == null) {
            return "";
        }
        if ( CellType.NUMERIC.getCode()==xssfRow.getCellType().getCode() ) {
            if (new BigDecimal(String.valueOf(xssfRow.getNumericCellValue())).compareTo(new BigDecimal(Long.valueOf((long)xssfRow.getNumericCellValue()).toString())) == 0) {
                return String.valueOf(new BigDecimal(String.valueOf(xssfRow.getNumericCellValue())).longValue());
            }
            return String.valueOf(xssfRow.getNumericCellValue());
        } else {
            return xssfRow.getStringCellValue() == null ? "" : xssfRow.getStringCellValue();
        }
    }

    private static String getStrValue(HSSFCell xssfRow) {
        if (xssfRow == null) {
            return "";
        }
        if (CellType.NUMERIC.getCode()==xssfRow.getCellType().getCode()) {
            // 判断是否为科学计数法（包含E、e、+等符号）
            Object number = xssfRow.getNumericCellValue();
            String numberStr = String.valueOf(number);
            numberStr = StringUtils.isBlank(StringUtils.trim(numberStr)) ? "" : numberStr;
            // 判断是否为科学计数法（包含E、e、+等符号）
            if (("" + numberStr).contains("E") || ("" + numberStr).contains("e") || ("" + numberStr).contains("+")) {
                BigDecimal bd = new BigDecimal("" + numberStr);
                return new BigDecimal(Double.parseDouble(bd.toString())).toString();
            }
            return String.valueOf((int) xssfRow.getNumericCellValue());
        } else {
            return xssfRow.getStringCellValue() == null ? "" : xssfRow.getStringCellValue();
        }
    }

}
