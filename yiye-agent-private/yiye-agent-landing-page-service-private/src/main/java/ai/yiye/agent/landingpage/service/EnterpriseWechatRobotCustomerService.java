package ai.yiye.agent.landingpage.service;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.web.argument.Direction;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.*;
import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.landingpage.*;
import ai.yiye.agent.domain.landingpage.dto.RobotCustomerContactDto;
import ai.yiye.agent.domain.utils.CollectionUtil;
import ai.yiye.agent.domain.utils.UrlUtils;
import ai.yiye.agent.landingpage.config.EnterpriseWechatThirdPartyConfig;
import ai.yiye.agent.landingpage.controller.vo.EnterpriseWechatRobotCustomerInfoVo;
import ai.yiye.agent.landingpage.dto.*;
import ai.yiye.agent.landingpage.enums.RobotGroupCodeUsePattern;
import ai.yiye.agent.landingpage.enums.exception.WorkWeixinResultCode;
import ai.yiye.agent.landingpage.mapper.DestroyCustomerServiceRobotRecordMapper;
import ai.yiye.agent.landingpage.mapper.EnterpriseWechatRobotCustomerMapper;
import ai.yiye.agent.landingpage.redis.EnterpriseWechatRobotCustomerRedis;
import ai.yiye.agent.landingpage.redis.LandingPageWechatCustomerServiceRedis;
import ai.yiye.agent.landingpage.sender.*;
import ai.yiye.agent.landingpage.service.readonly.EnterpriseWechatRobotCustomerReadOnlyService;
import ai.yiye.agent.landingpage.utils.FileUtil;
import ai.yiye.agent.marketing.exception.MarketingApiException;
import ai.yiye.agent.weixin.client.WorkWeixinApiClient;
import ai.yiye.agent.weixin.domain.*;
import ai.yiye.agent.weixin.exception.MarketingWeOpenApiException;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.io.InputStream;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EnterpriseWechatRobotCustomerService extends ServiceImpl<EnterpriseWechatRobotCustomerMapper, EnterpriseWechatRobotCustomer> {

    /**
     * 企业微信微信客服机器人SCENE
     */
    private static final String ENTERPRISE_WECHAT_CUSTOMER_SERVICE_ROBOT_SCENE = "yiye";

    @Autowired
    private EnterpriseWechatRobotCustomerReadOnlyService enterpriseWechatRobotCustomerReadOnlyService;
    @Autowired
    private WorkWeixinApiClient workWeixinApiClient;
    @Autowired
    private EnterpriseWechatService enterpriseWechatService;
    @Autowired
    private EnterpriseWechatRobotCustomerMapper enterpriseWechatRobotCustomerMapper;
    @Autowired
    private StringRedisTemplate defaultStringRedisTemplate;

    @Autowired
    private RedisTemplate<String, Object> defaultObjectRedisTemplate;

    @Autowired
    private RedisTemplate<String, Object> objectRedisTemplate;

    @Autowired
    private EnterpriseWechatRobotCustomerPmpRelService enterpriseWechatRobotCustomerPmpRelService;

    @Autowired
    private EnterpriseWechatCostomerMsgTemplateService enterpriseWechatCostomerMsgTemplateService;

    @Autowired
    private EnterpriseWechatCustomerMsgChildrenTemplateService enterpriseWechatCustomerMsgChildrenTemplateService;

    @Autowired
    private EnterpriseWechatCustomerAutoAnswerRuleService enterpriseWechatCustomerAutoAnswerRuleService;

    @Autowired
    private EnterpriseWechatCustomerTemplateAutoAnswerRuleRelService enterpriseWechatCustomerTemplateAutoAnswerRuleRelService;

    @Autowired
    private EnterpriseWechatCustomerTemplateRelService enterpriseWechatCustomerTemplateRelService;

    @Autowired
    private EnterpriseTempMaterialService enterpriseTempMaterialService;

    @Autowired
    private EnterpriseWechatThirdPartyConfig enterpriseWechatThirdPartyConfig;

    @Autowired
    @Qualifier("enterpriseWechatThirdPartyApiClient")
    private WorkWeixinApiClient enterpriseWechatThirdPartyApiClient;

    @Autowired
    private EnterpriseWechatThirdPartyApplicationAuthService enterpriseWechatThirdPartyApplicationAuthService;

    @Autowired
    private AdvertiserAccountGroupService advertiserAccountGroupService;

    @Autowired
    private EnterpriseWechatRobotCustomerRedis enterpriseWechatRobotCustomerRedis;

    @Autowired
    private EnterpriseWechatRobotCustomerGroupRelService enterpriseWechatRobotCustomerGroupRelService;

    @Resource
    private UserOperationLogDetailActionSender userOperationLogDetailActionSender;

    @Resource
    private EnterpriseRobotCustomerSender enterpriseRobotCustomerSender;

    @Autowired
    private EnterpriseWechatCustomerMessageService enterpriseWechatCustomerMessageService;

    @Autowired
    private MessageNoticeSender messageNoticeSender;

    @Autowired
    private DestroyCustomerServiceRobotRecordMapper destroyCustomerServiceRobotRecordMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private LandingPagePmpParamsConfigService landingPagePmpParamsConfigService;

    @Autowired
    private OutSiteMsgSender outSiteMsgSender;

    @Autowired
    private BossAdvertiserAccountGroupService bossAdvertiserAccountGroupService;

    @Resource
    private LandingPageWechatCustomerServiceService landingPageWechatCustomerServiceService;

    @Resource
    private LandingPageWechatCustomerServiceRedis landingPageWechatCustomerServiceRedis;

    @Resource
    private RobotCustomerDynamicContactService robotCustomerDynamicContactService;

    @Resource
    private RobotCustomerContactSender robotCustomerContactSender;

    @Resource
    private EnterpriseWechatCustomerMsgTemplateCityRelService enterpriseWechatCustomerMsgTemplateCityRelService;

    @Resource
    private EnterpriseWechatCustomerAutoAnswerRuleCityRelService enterpriseWechatCustomerAutoAnswerRuleCityRelService;

    @Resource
    private EnterpriseWechatCustomerKeywordReplyRuleCityRelService enterpriseWechatCustomerKeywordReplyRuleCityRelService;


    private void uploadTempFile(List<EnterpriseWechatCustomerMsgChildrenTemplate> list, EnterpriseWechat enterpriseWechat) {
        if (CollectionUtils.isNotEmpty(list)) {
            for (EnterpriseWechatCustomerMsgChildrenTemplate autoAnswerDto : list) {
                String linkPicUrl = autoAnswerDto.getLinkPicUrl();
                //小程序封面
                String appletThumbUrl = autoAnswerDto.getAppletThumbUrl();
                if (StringUtils.isNotBlank(linkPicUrl)) {
                    uploadAppletTempFile(linkPicUrl, autoAnswerDto, enterpriseWechat, true);
                }
                if (StringUtils.isNotBlank(appletThumbUrl)) {
                    uploadAppletTempFile(appletThumbUrl, autoAnswerDto, enterpriseWechat, false);
                }
            }
        }
    }

    private void uploadAppletTempFile(String path, EnterpriseWechatCustomerMsgChildrenTemplate autoAnswerDto, EnterpriseWechat enterpriseWechat, boolean flag) {
        if (StringUtils.isBlank(path)) {
            return;
        }
        //查询素材库
        EnterpriseWechatTempMaterial temp = enterpriseTempMaterialService.getOne(new LambdaQueryWrapper<EnterpriseWechatTempMaterial>().eq(EnterpriseWechatTempMaterial::getCorpId, enterpriseWechat.getCorpid()).eq(EnterpriseWechatTempMaterial::getMaterialPath, path).last("limit 1"));
        Long id = null;
        if (Objects.isNull(temp)) {
            InputStream inputStream = FileUtil.getInputStream(path);
            MultipartFile tempFile = FileUtil.getMultipartFile(inputStream, "media");
            TempMaterialResponseBody tempMaterialResponseBody = workWeixinApiClient.uploadTempFile(enterpriseWechat.getAccessToken(), EnterpriseTempMaterialType.IMAGE.getName(), tempFile);
            Instant instant = Instant.ofEpochSecond(Long.parseLong(tempMaterialResponseBody.getCreatedAt()));
            EnterpriseWechatTempMaterial enterpriseWechatTempMaterial = new EnterpriseWechatTempMaterial();
            enterpriseWechatTempMaterial.setCorpId(enterpriseWechat.getCorpid())
                .setMaterialPath(path)
                .setMaterialId(tempMaterialResponseBody.getMediaId())
                .setCreatedAt(instant)
                .setExpireAt(instant.plus(3, ChronoUnit.DAYS))
                .setMaterialType(EnterpriseTempMaterialType.IMAGE);
            enterpriseTempMaterialService.saveOrUpdate(enterpriseWechatTempMaterial);
            id = enterpriseWechatTempMaterial.getId();
        } else {
            id = temp.getId();
        }
        if (!flag) {
            //特殊情况 小程序封面素材ID
            autoAnswerDto.setAppletThumbMediaId(id);
        } else {
            autoAnswerDto.setLinkPicMaterialId(id);
        }
    }

    /**
     * 微信客服机器人配置变更，清除缓存
     *
     * @param robotCustomer 微信客服机器人配置信息
     */
    public void deleteEnterpriseWechatRobotCustomerCache(EnterpriseWechatRobotCustomer robotCustomer) {
        defaultObjectRedisTemplate.delete(RedisConstant.ENTERPRISE_WECHAT_ROBOT_CUSTOMER_PREFIX + robotCustomer.getCorpId() + ":" + robotCustomer.getOpenKfId());
        objectRedisTemplate.delete(RedisConstant.ENTERPRISE_WECHAT_ROBOT_CUSTOMER_PRIVATE_PREFIX + robotCustomer.getCorpId() + ":" + robotCustomer.getOpenKfId());
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean addCustomerRobot(EnterpriseWechatRobotCustomerSaveDto dto, EnterpriseWechat enterpriseWechat) {
        EnterpriseWechatRobotCustomer enterpriseWechatRobotCustomer = new EnterpriseWechatRobotCustomer();
        BeanUtils.copyProperties(dto, enterpriseWechatRobotCustomer);
        RobotCustomerCreateRequestBody robotCustomerCreateRequestBody = new RobotCustomerCreateRequestBody();
        robotCustomerCreateRequestBody.setName(dto.getName());
        robotCustomerCreateRequestBody.setMediaId(dto.getMediaId());
        RobotCustomerResponseBody robotCustomerResponseBody;
        try {
            //接口创建客服机器人
            robotCustomerResponseBody = workWeixinApiClient.addRobotCustomer(enterpriseWechat.getAccessToken(), robotCustomerCreateRequestBody);
        } catch (MarketingWeOpenApiException e) {
            String errorInfo = Objects.isNull(e.getCause()) ? e.getMessage() : e.getCause().getMessage();
            log.warn("新增企业微信客服接口-dto:[{}],异常信息:[{}]", JSON.toJSONString(dto), errorInfo, e);
            WorkWeixinResultCode resultCode = WorkWeixinResultCode.getByCode(e.getErrcode());
            if (resultCode != null) {
                throw new MarketingApiException(resultCode.getMsg());
            }
            throw new MarketingApiException(errorInfo);
        }
        String openKfUrl = this.obtainCustomerServiceRobotLink(enterpriseWechat.getAccessToken(), robotCustomerResponseBody.getOpenKfid());
        enterpriseWechatRobotCustomer.setOpenKfUrl(openKfUrl);
        enterpriseWechatRobotCustomer.setOpenKfId(robotCustomerResponseBody.getOpenKfid());
        enterpriseWechatRobotCustomer.setManagePrivilege(true);
        enterpriseWechatRobotCustomer.setType(EnterpriseWechatRobotCustomerType.AGENT_DEVELOP_APPLICATION_API_ADD);
        // 新增到企业微信客服机器人
        enterpriseWechatRobotCustomerMapper.insert(enterpriseWechatRobotCustomer);
        enterpriseWechatRobotCustomerMapper.insertToPublic(enterpriseWechatRobotCustomer.setAgentId(TenantContextHolder.get()));
        //添加成功后，新增到关系表中
        EnterpriseWechatRobotCustomerPmpRel enterpriseWechatRobotCustomerPmpRel = new EnterpriseWechatRobotCustomerPmpRel();
        enterpriseWechatRobotCustomerPmpRel.setAdvertiserAccountGroupId(dto.getAdvertiserAccountGroupId());
        enterpriseWechatRobotCustomerPmpRel.setWechatRobotCustomerId(enterpriseWechatRobotCustomer.getId());
        enterpriseWechatRobotCustomerPmpRel.setAgentId(TenantContextHolder.get());
        enterpriseWechatRobotCustomerPmpRelService.saveOrUpdate(enterpriseWechatRobotCustomerPmpRel);
        enterpriseWechatRobotCustomerPmpRel.setId(null);
        enterpriseWechatRobotCustomerPmpRelService.saveOrUpdateByPublic(enterpriseWechatRobotCustomerPmpRel);
        // 维护一份数据到公共库 用于后续的消息回调时区分哪个用户下的客服

        EnterpriseWechatRobotCustomerIntelligenceType intelligenceType = enterpriseWechatRobotCustomer.getIntelligenceType();
        if (EnterpriseWechatRobotCustomerIntelligenceType.INTELLIGENCE_CITY.equals(intelligenceType)) {
            //智能城市机器人
            List<EnterpriseWechatRobotCustomerCitySaveDto> cityRobots = dto.getCityRobots();
            if (CollectionUtils.isEmpty(cityRobots)) throw new RestException("策略不能为空!");
            cityRobots.forEach(e -> {
                String strategyId = e.getStrategyId();
                Long[] ipRegionalSourceCity = e.getIpRegionalSourceCity();
                String ipRegionalSourceCityName = e.getIpRegionalSourceCityName();
                Long[] ipRegionalSourceProvince = e.getIpRegionalSourceProvince();
                String ipRegionalSourceProvinceName = e.getIpRegionalSourceProvinceName();
                CommonYesOrNoStatus reveal = e.getReveal();
                SwitchStatus autoAnswer = e.getAutoAnswer();
                SwitchStatus keywordReply = e.getKeywordReply();
                SwitchStatus keywordReplyNotTriggeredWhenAdded = e.getKeywordReplyNotTriggeredWhenAdded();

                //欢迎语 和 自动回复
                List<EnterpriseWechatRobotCustomerWelcomeMessageDto> welcomeMessage = e.getWelcomeMessage();
                List<EnterpriseWechatCustomerMsgTemplate> autoAnswerList = e.getAutoAnswerList();
                EnterpriseWechatCustomerMessageStrategyType city = EnterpriseWechatCustomerMessageStrategyType.CITY;
                Long templateId = this.saveWelcomeMessage(dto.getName(), enterpriseWechatRobotCustomer.getId(), dto.getAdvertiserAccountGroupId(), welcomeMessage, city);
                Long autoAnswerId = this.saveAnswerMessage(dto.getName(), enterpriseWechatRobotCustomer.getId(), dto.getAdvertiserAccountGroupId(), autoAnswerList, StrategyType.AUTO_ANSWER, city);
                //创建城市关联关系
                EnterpriseWechatCustomerMsgTemplateCityRel enterpriseWechatCustomerMsgTemplateCityRel = new EnterpriseWechatCustomerMsgTemplateCityRel();
                enterpriseWechatCustomerMsgTemplateCityRel
                    .setEnterpriseWechatCustomerId(enterpriseWechatRobotCustomer.getId())
                    .setEnterpriseWechatCustomerTemplateId(templateId)
                    .setAdvertiserAccountGroupId(dto.getAdvertiserAccountGroupId())
                    .setReveal(reveal)
                    .setStrategyId(strategyId)
                    .setIpRegionalSourceCity(ipRegionalSourceCity).setIpRegionalSourceCityName(ipRegionalSourceCityName)
                    .setIpRegionalSourceProvince(ipRegionalSourceProvince).setIpRegionalSourceProvinceName(ipRegionalSourceProvinceName)
                    .setAutoAnswer(autoAnswer)
                    .setKeywordReply(keywordReply)
                    .setKeywordReplyNotTriggeredWhenAdded(keywordReplyNotTriggeredWhenAdded);
                enterpriseWechatCustomerMsgTemplateCityRelService.save(enterpriseWechatCustomerMsgTemplateCityRel);
                //是否开启自动回复
                if (SwitchStatus.OPEN.equals(autoAnswer)) {
                    EnterpriseWechatCustomerAutoAnswerRuleCityRel enterpriseWechatCustomerAutoAnswerRuleCityRel = new EnterpriseWechatCustomerAutoAnswerRuleCityRel();
                    enterpriseWechatCustomerAutoAnswerRuleCityRel.setEnterpriseWechatCustomerId(enterpriseWechatRobotCustomer.getId())
                        .setEnterpriseWechatCustomerAutoAnswerRuleId(autoAnswerId)
                        .setAdvertiserAccountGroupId(dto.getAdvertiserAccountGroupId())
                        .setReveal(reveal)
                        .setStrategyId(strategyId)
                        .setIpRegionalSourceCity(ipRegionalSourceCity).setIpRegionalSourceCityName(ipRegionalSourceCityName)
                        .setIpRegionalSourceProvince(ipRegionalSourceProvince).setIpRegionalSourceProvinceName(ipRegionalSourceProvinceName);
                    enterpriseWechatCustomerAutoAnswerRuleCityRelService.save(enterpriseWechatCustomerAutoAnswerRuleCityRel);
                }
                // 保存机器人和关键词回复规则的关联关系 到 enterprise_wechat_customer_keyword_reply_rule_city_rel 表
                if (SwitchStatus.OPEN.equals(keywordReply) && CollectionUtils.isNotEmpty(e.getKeywordReplyRuleIds())) {
                    List<EnterpriseWechatCustomerKeywordReplyRuleCityRel> rels = e.getKeywordReplyRuleIds().stream().map(ruleId -> new EnterpriseWechatCustomerKeywordReplyRuleCityRel()
                            .setEnterpriseWechatCustomerId(enterpriseWechatRobotCustomer.getId())
                            .setAdvertiserAccountGroupId(dto.getAdvertiserAccountGroupId())
                            .setEnterpriseWechatCustomerAutoAnswerRuleId(ruleId)
                            .setStrategyId(strategyId)
                            .setIpRegionalSourceCity(ipRegionalSourceCity)
                            .setIpRegionalSourceCityName(ipRegionalSourceCityName)
                            .setIpRegionalSourceProvince(ipRegionalSourceProvince)
                            .setIpRegionalSourceProvinceName(ipRegionalSourceProvinceName)
                            .setReveal(reveal)
                            .setKeywordReply(keywordReply)
                            .setKeywordReplyNotTriggeredWhenAdded(keywordReplyNotTriggeredWhenAdded))
                        .collect(Collectors.toList());
                    enterpriseWechatCustomerKeywordReplyRuleCityRelService.saveBatch(rels);
                }

            });
        } else {
            //其他类型的全部走这
            EnterpriseWechatCustomerMessageStrategyType messageStrategyType = EnterpriseWechatRobotCustomerIntelligenceType.GENERAL.equals(intelligenceType) ? EnterpriseWechatCustomerMessageStrategyType.ORIGINAL : EnterpriseWechatCustomerMessageStrategyType.BLUE_LINK_ADD_FANS;
            this.saveWelcomeMessage(dto.getName(), enterpriseWechatRobotCustomer.getId(), dto.getAdvertiserAccountGroupId(), dto.getWelcomeMessage(), messageStrategyType);
            this.saveWelcomeMessage(dto.getName(), enterpriseWechatRobotCustomer.getId(), dto.getAdvertiserAccountGroupId(), dto.getWelcomeMessageCa(), EnterpriseWechatCustomerMessageStrategyType.ACQUISITION_ASSISTANT);
            this.saveAnswerMessage(dto.getName(), enterpriseWechatRobotCustomer.getId(), dto.getAdvertiserAccountGroupId(), dto.getAutoAnswerList(), StrategyType.AUTO_ANSWER, messageStrategyType);
            this.saveAnswerMessage(dto.getName(), enterpriseWechatRobotCustomer.getId(), dto.getAdvertiserAccountGroupId(), dto.getAutoAnswerListCa(), StrategyType.AUTO_ANSWER, EnterpriseWechatCustomerMessageStrategyType.ACQUISITION_ASSISTANT);
            this.saveAnswerMessage(dto.getName(), enterpriseWechatRobotCustomer.getId(), dto.getAdvertiserAccountGroupId(), dto.getRetainMessageList(), StrategyType.RETAIN_MESSAGE, messageStrategyType);
            this.saveAnswerMessage(dto.getName(), enterpriseWechatRobotCustomer.getId(), dto.getAdvertiserAccountGroupId(), dto.getRetainMessageListCa(), StrategyType.RETAIN_MESSAGE, EnterpriseWechatCustomerMessageStrategyType.ACQUISITION_ASSISTANT);
            // 保存机器人和关键词回复规则的关联关系 到 enterprise_wechat_customer_template_auto_answer_rule_rel 表
            if (EnterpriseWechatRobotCustomerIntelligenceType.GENERAL.equals(intelligenceType) && SwitchStatus.OPEN.equals(dto.getKeywordReply()) && CollectionUtils.isNotEmpty(dto.getKeywordReplyRuleIds())) {
                List<EnterpriseWechatCustomerTemplateAutoAnswerRuleRel> rels = dto.getKeywordReplyRuleIds().stream().map(ruleId -> {
                    EnterpriseWechatCustomerTemplateAutoAnswerRuleRel rel = new EnterpriseWechatCustomerTemplateAutoAnswerRuleRel();
                    rel.setEnterpriseWechatCustomerId(enterpriseWechatRobotCustomer.getId());
                    rel.setEnterpriseWechatCustomerAutoAnswerRuleId(ruleId);
                    return rel;
                }).collect(Collectors.toList());
                enterpriseWechatCustomerTemplateAutoAnswerRuleRelService.saveBatch(rels);
            }
        }

        // 更新关联关系
        enterpriseWechatRobotCustomerGroupRelService.fixRelation(dto.getWechatRobotCustomerGroupIds(), enterpriseWechatRobotCustomer.getId(), dto.getAdvertiserAccountGroupId());
        this.cleanWechatServiceRobotCustomersCache(enterpriseWechatRobotCustomer.getId());
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCustomerRobot(EnterpriseWechatRobotCustomerSaveDto dto, EnterpriseWechatRobotCustomer robotCustomer, EnterpriseWechat enterpriseWechat) {
        Long id = dto.getId();
        String openKfId = robotCustomer.getOpenKfId();
        //名称 与 头像 变更
        if (!robotCustomer.getName().equals(dto.getName()) || !robotCustomer.getFilePath().equals(dto.getFilePath())) {
            //名称或头像有更新的话，调用企业微信接口进行更新
            RobotCustomerUpdateRequestBody robotCustomerUpdateRequestBody = new RobotCustomerUpdateRequestBody();
            robotCustomerUpdateRequestBody.setOpenKfid(robotCustomer.getOpenKfId());
            robotCustomerUpdateRequestBody.setName(dto.getName());
            if (!robotCustomer.getFilePath().equals(dto.getFilePath()) && StringUtils.isNotBlank(dto.getMediaId())) {
                robotCustomerUpdateRequestBody.setMediaId(dto.getMediaId());
            }
            try {
                workWeixinApiClient.updateCustomerRobot(enterpriseWechat.getAccessToken(), robotCustomerUpdateRequestBody);
            } catch (MarketingWeOpenApiException e) {
                String errorInfo = Objects.isNull(e.getCause()) ? e.getMessage() : e.getCause().getMessage();
                log.warn("修改企业微信客服接口-dto:[{}],异常信息:[{}]", JSON.toJSONString(dto), errorInfo, e);
                WorkWeixinResultCode resultCode = WorkWeixinResultCode.getByCode(e.getErrcode());
                if (resultCode != null) {
                    throw new MarketingApiException(resultCode.getMsg());
                }
                throw new MarketingApiException(errorInfo);
            }
        }
        if (!Objects.equals(robotCustomer.getIntelligenceType(), dto.getIntelligenceType())
            || !Objects.equals(robotCustomer.getAcquisitionAssistantMsgSendRatio(), dto.getAcquisitionAssistantMsgSendRatio())
            || !Objects.equals(robotCustomer.getBlueLinkAddFansMsgSendRatio(), dto.getBlueLinkAddFansMsgSendRatio())) {
            objectRedisTemplate.delete(RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_ROBOT_STRATEGY_NO + robotCustomer.getId());
        }
        robotCustomer.setName(dto.getName())
            .setFilePath(dto.getFilePath())
            .setCustomRemarks(dto.getCustomRemarks())
            .setAutoAnswer(dto.getAutoAnswer())
            .setKeywordReply(dto.getKeywordReply())
            .setKeywordReplyNotTriggeredWhenAdded(dto.getKeywordReplyNotTriggeredWhenAdded())
            .setRetainMessage(dto.getRetainMessage())
            .setRetainTimeInterval(dto.getRetainTimeInterval())
            .setDetainTimeInterval(dto.getDetainTimeInterval())
            .setNonLandingPageProhibitSend(dto.getNonLandingPageProhibitSend())
            .setNonLandingPageSendContent(dto.getNonLandingPageSendContent())
            .setAutoAnswerCa(dto.getAutoAnswerCa())
            .setRetainMessageCa(dto.getRetainMessageCa())
            .setDetainTimeIntervalCa(dto.getDetainTimeIntervalCa())
            .setIntelligenceType(dto.getIntelligenceType())
            .setAcquisitionAssistantMsgSendRatio(dto.getAcquisitionAssistantMsgSendRatio())
            .setBlueLinkAddFansMsgSendRatio(dto.getBlueLinkAddFansMsgSendRatio())
            .setRecoveryConstructDelay(dto.getRecoveryConstructDelay())
            .setRecoveryDestroyDelay(dto.getRecoveryDestroyDelay())
            .setAccountAbnormalityRecoveryStatus(dto.getAccountAbnormalityRecoveryStatus())
            .setRecoveryStatus(dto.getRecoveryStatus())
            .setRecoveryStrategy(dto.getRecoveryStrategy())
            .setRecoveryEntryRatio(dto.getRecoveryEntryRatio())
            .setOpenKfId(null)
            .setOpenKfUrl(null)
            .setUpdatedAt(Instant.now());

        if (Objects.equals(dto.getKeywordReply(), SwitchStatus.OPEN) && CollectionUtils.isNotEmpty(dto.getKeywordReplyRuleIds())) {
            robotCustomer.setKeywordReplyRuleIds(dto.getKeywordReplyRuleIds().toArray(new Long[0]));
        }

        //更新机器人信息
        this.updateById(robotCustomer);
        //清除缓存
        enterpriseWechatCustomerAutoAnswerRuleService.clearAutoAnswerRuleCache(robotCustomer);
        /**
         * 更新企业微信客服的公共库数据
         */
        enterpriseWechatRobotCustomerMapper.updateToPublic(robotCustomer.setAgentId(TenantContextHolder.get()));
        //模板与客服关联记录
        List<EnterpriseWechatCustomerTemplateRel> customerTemplateRels = enterpriseWechatCustomerTemplateRelService.list(
            Wrappers.lambdaQuery(EnterpriseWechatCustomerTemplateRel.class)
                .eq(EnterpriseWechatCustomerTemplateRel::getEnterpriseWechatCustomerId, id)
        );

        //清理欢迎语模版与城市客服关联记录
        enterpriseWechatCustomerMsgTemplateCityRelService.remove(Wrappers.lambdaQuery(EnterpriseWechatCustomerMsgTemplateCityRel.class)
            .eq(EnterpriseWechatCustomerMsgTemplateCityRel::getEnterpriseWechatCustomerId, id));
        //清理自动回复与城市记录关联记录
        enterpriseWechatCustomerAutoAnswerRuleCityRelService.remove(Wrappers.lambdaQuery(EnterpriseWechatCustomerAutoAnswerRuleCityRel.class)
            .eq(EnterpriseWechatCustomerAutoAnswerRuleCityRel::getEnterpriseWechatCustomerId, id));
        //清理关键词回复与城市记录关联记录
        enterpriseWechatCustomerKeywordReplyRuleCityRelService.remove(Wrappers.lambdaQuery(EnterpriseWechatCustomerKeywordReplyRuleCityRel.class)
            .eq(EnterpriseWechatCustomerKeywordReplyRuleCityRel::getEnterpriseWechatCustomerId, id));
        // 删除非城市客服欢迎语
        if (CollectionUtils.isNotEmpty(customerTemplateRels)) {
            Set<Long> customerTemplateRelIds = customerTemplateRels.stream().map(EnterpriseWechatCustomerTemplateRel::getId).collect(Collectors.toSet());
            Set<Long> customerTemplateRelTemplateIds = customerTemplateRels.stream().map(EnterpriseWechatCustomerTemplateRel::getEnterpriseWechatCustomerTemplateId).collect(Collectors.toSet());
            // 清理非城市客服欢迎语关系
            enterpriseWechatCustomerTemplateRelService.removeByIds(customerTemplateRelIds);
            // 清理欢迎语的消息模版
            enterpriseWechatCostomerMsgTemplateService.removeByIds(customerTemplateRelTemplateIds);
            // 清理欢迎语的消息子模版
            enterpriseWechatCustomerMsgChildrenTemplateService.remove(
                Wrappers.lambdaQuery(EnterpriseWechatCustomerMsgChildrenTemplate.class)
                    .in(EnterpriseWechatCustomerMsgChildrenTemplate::getMsgTemplateId, customerTemplateRelTemplateIds)
            );
            enterpriseWechatCustomerMsgChildrenTemplateService.deleteEnterpriseWechatCustomerMsgChildrenTemplateCache(customerTemplateRelTemplateIds);
        }
        // 删除非城市客服自动回复&挽留消息&关键词回复
        List<EnterpriseWechatRobotCustomerStrategyDto> strategys = baseMapper.getStrategy(id);
        if (CollectionUtils.isNotEmpty(strategys)) {
            Set<Long> enterpriseWechatCustomerAutoAnswerRuleIds = strategys.stream().map(EnterpriseWechatRobotCustomerStrategyDto::getEnterpriseWechatCustomerAutoAnswerRuleId).collect(Collectors.toSet());
            Set<Long> msgTemplateIds = strategys.stream().map(EnterpriseWechatRobotCustomerStrategyDto::getMsgTemplateIds).filter(ObjectUtils::isNotEmpty).flatMap(Arrays::stream).collect(Collectors.toSet());
            //清理自动回复关联表
            enterpriseWechatCustomerTemplateAutoAnswerRuleRelService.remove(new LambdaQueryWrapper<EnterpriseWechatCustomerTemplateAutoAnswerRuleRel>()
                .eq(EnterpriseWechatCustomerTemplateAutoAnswerRuleRel::getEnterpriseWechatCustomerId, id));
            //清理自动回复记录表 - 不包含关键词回复策略（关键词回复策略用于关键词列表）
            enterpriseWechatCustomerAutoAnswerRuleService.remove(new LambdaQueryWrapper<EnterpriseWechatCustomerAutoAnswerRule>()
                .ne(EnterpriseWechatCustomerAutoAnswerRule::getType, StrategyType.KEYWORD)
                .in(EnterpriseWechatCustomerAutoAnswerRule::getId, enterpriseWechatCustomerAutoAnswerRuleIds));
            //清理消息模板表
            enterpriseWechatCostomerMsgTemplateService.remove(new LambdaQueryWrapper<EnterpriseWechatCustomerMsgTemplate>()
                .in(EnterpriseWechatCustomerMsgTemplate::getId, msgTemplateIds));
            //清理消息模板子表
            enterpriseWechatCustomerMsgChildrenTemplateService.remove(new LambdaQueryWrapper<EnterpriseWechatCustomerMsgChildrenTemplate>()
                .in(EnterpriseWechatCustomerMsgChildrenTemplate::getMsgTemplateId, msgTemplateIds));
            //清理子消息模板缓存
            enterpriseWechatCustomerMsgChildrenTemplateService.deleteEnterpriseWechatCustomerMsgChildrenTemplateCache(msgTemplateIds);
        }
        //清理模板缓存
        enterpriseWechatCostomerMsgTemplateService.clearCache(id);

        //重建关联关系
        EnterpriseWechatRobotCustomerIntelligenceType intelligenceType = robotCustomer.getIntelligenceType();
        if (EnterpriseWechatRobotCustomerIntelligenceType.INTELLIGENCE_CITY.equals(intelligenceType)) {
            //智能城市机器人
            List<EnterpriseWechatRobotCustomerCitySaveDto> cityRobots = dto.getCityRobots();
            if (CollectionUtils.isEmpty(cityRobots)) throw new RestException("策略不能为空!");
            cityRobots.forEach(e -> {
                String strategyId = e.getStrategyId();
                CommonYesOrNoStatus reveal = e.getReveal();
                Long[] ipRegionalSourceCity = e.getIpRegionalSourceCity();
                String ipRegionalSourceCityName = e.getIpRegionalSourceCityName();
                Long[] ipRegionalSourceProvince = e.getIpRegionalSourceProvince();
                String ipRegionalSourceProvinceName = e.getIpRegionalSourceProvinceName();
                SwitchStatus autoAnswer = e.getAutoAnswer();
                SwitchStatus keywordReply = e.getKeywordReply();
                SwitchStatus keywordReplyNotTriggeredWhenAdded = e.getKeywordReplyNotTriggeredWhenAdded();

                //欢迎语 和 自动回复
                List<EnterpriseWechatRobotCustomerWelcomeMessageDto> welcomeMessage = e.getWelcomeMessage();
                List<EnterpriseWechatCustomerMsgTemplate> autoAnswerList = e.getAutoAnswerList();
                EnterpriseWechatCustomerMessageStrategyType city = EnterpriseWechatCustomerMessageStrategyType.CITY;
                Long templateId = this.saveWelcomeMessage(dto.getName(), robotCustomer.getId(), dto.getAdvertiserAccountGroupId(), welcomeMessage, city);
                Long autoAnswerId = this.saveAnswerMessage(dto.getName(), robotCustomer.getId(), dto.getAdvertiserAccountGroupId(), autoAnswerList, StrategyType.AUTO_ANSWER, city);
                //创建关联关系
                EnterpriseWechatCustomerMsgTemplateCityRel enterpriseWechatCustomerMsgTemplateCityRel = new EnterpriseWechatCustomerMsgTemplateCityRel();
                enterpriseWechatCustomerMsgTemplateCityRel.setEnterpriseWechatCustomerId(robotCustomer.getId())
                    .setEnterpriseWechatCustomerTemplateId(templateId)
                    .setAdvertiserAccountGroupId(dto.getAdvertiserAccountGroupId())
                    .setReveal(reveal)
                    .setStrategyId(strategyId)
                    .setIpRegionalSourceCity(ipRegionalSourceCity).setIpRegionalSourceCityName(ipRegionalSourceCityName)
                    .setIpRegionalSourceProvince(ipRegionalSourceProvince).setIpRegionalSourceProvinceName(ipRegionalSourceProvinceName)
                    .setAutoAnswer(autoAnswer)
                    .setKeywordReply(keywordReply)
                    .setKeywordReplyNotTriggeredWhenAdded(keywordReplyNotTriggeredWhenAdded)
                ;
                enterpriseWechatCustomerMsgTemplateCityRelService.save(enterpriseWechatCustomerMsgTemplateCityRel);
                //是否开启自动回复
                if (SwitchStatus.OPEN.equals(autoAnswer)) {
                    EnterpriseWechatCustomerAutoAnswerRuleCityRel enterpriseWechatCustomerAutoAnswerRuleCityRel = new EnterpriseWechatCustomerAutoAnswerRuleCityRel();
                    enterpriseWechatCustomerAutoAnswerRuleCityRel.setEnterpriseWechatCustomerId(robotCustomer.getId())
                        .setEnterpriseWechatCustomerAutoAnswerRuleId(autoAnswerId)
                        .setAdvertiserAccountGroupId(dto.getAdvertiserAccountGroupId())
                        .setReveal(reveal)
                        .setStrategyId(strategyId)
                        .setIpRegionalSourceCity(ipRegionalSourceCity).setIpRegionalSourceCityName(ipRegionalSourceCityName)
                        .setIpRegionalSourceProvince(ipRegionalSourceProvince)
                        .setIpRegionalSourceProvinceName(ipRegionalSourceProvinceName);

                    enterpriseWechatCustomerAutoAnswerRuleCityRelService.save(enterpriseWechatCustomerAutoAnswerRuleCityRel);
                }
                // 保存机器人和关键词回复规则的关联关系 到 enterprise_wechat_customer_keyword_reply_rule_city_rel 表
                if (SwitchStatus.OPEN.equals(keywordReply) && CollectionUtils.isNotEmpty(e.getKeywordReplyRuleIds())) {
                    List<EnterpriseWechatCustomerKeywordReplyRuleCityRel> rels = e.getKeywordReplyRuleIds()
                        .stream()
                        .map(ruleId -> new EnterpriseWechatCustomerKeywordReplyRuleCityRel()
                        .setEnterpriseWechatCustomerId(robotCustomer.getId())
                        .setAdvertiserAccountGroupId(dto.getAdvertiserAccountGroupId())
                        .setEnterpriseWechatCustomerAutoAnswerRuleId(ruleId)
                        .setStrategyId(strategyId)
                        .setIpRegionalSourceCity(ipRegionalSourceCity).setIpRegionalSourceCityName(ipRegionalSourceCityName)
                        .setIpRegionalSourceProvince(ipRegionalSourceProvince).setIpRegionalSourceProvinceName(ipRegionalSourceProvinceName)
                        .setReveal(reveal)
                        .setKeywordReply(keywordReply)
                        .setKeywordReplyNotTriggeredWhenAdded(keywordReplyNotTriggeredWhenAdded))
                        .collect(Collectors.toList());
                    enterpriseWechatCustomerKeywordReplyRuleCityRelService.saveBatch(rels);
                }
            });
        } else {
            //其他类型走这里
            EnterpriseWechatCustomerMessageStrategyType messageStrategyType = EnterpriseWechatRobotCustomerIntelligenceType.GENERAL.equals(robotCustomer.getIntelligenceType()) ? EnterpriseWechatCustomerMessageStrategyType.ORIGINAL : EnterpriseWechatCustomerMessageStrategyType.BLUE_LINK_ADD_FANS;
            this.saveWelcomeMessage(dto.getName(), id, dto.getAdvertiserAccountGroupId(), dto.getWelcomeMessage(), messageStrategyType);
            this.saveWelcomeMessage(dto.getName(), id, dto.getAdvertiserAccountGroupId(), dto.getWelcomeMessageCa(), EnterpriseWechatCustomerMessageStrategyType.ACQUISITION_ASSISTANT);
            this.saveAnswerMessage(dto.getName(), id, dto.getAdvertiserAccountGroupId(), dto.getAutoAnswerList(), StrategyType.AUTO_ANSWER, messageStrategyType);
            this.saveAnswerMessage(dto.getName(), id, dto.getAdvertiserAccountGroupId(), dto.getAutoAnswerListCa(), StrategyType.AUTO_ANSWER, EnterpriseWechatCustomerMessageStrategyType.ACQUISITION_ASSISTANT);
            this.saveAnswerMessage(dto.getName(), id, dto.getAdvertiserAccountGroupId(), dto.getRetainMessageList(), StrategyType.RETAIN_MESSAGE, messageStrategyType);
            this.saveAnswerMessage(dto.getName(), id, dto.getAdvertiserAccountGroupId(), dto.getRetainMessageListCa(), StrategyType.RETAIN_MESSAGE, EnterpriseWechatCustomerMessageStrategyType.ACQUISITION_ASSISTANT);
            // 保存机器人和关键词回复规则的关联关系 到 enterprise_wechat_customer_template_auto_answer_rule_rel 表
            if (EnterpriseWechatRobotCustomerIntelligenceType.GENERAL.equals(intelligenceType) && SwitchStatus.OPEN.equals(dto.getKeywordReply()) && CollectionUtils.isNotEmpty(dto.getKeywordReplyRuleIds())) {
                List<EnterpriseWechatCustomerTemplateAutoAnswerRuleRel> rels = dto.getKeywordReplyRuleIds().stream().map(ruleId -> {
                    EnterpriseWechatCustomerTemplateAutoAnswerRuleRel rel = new EnterpriseWechatCustomerTemplateAutoAnswerRuleRel();
                    rel.setEnterpriseWechatCustomerId(robotCustomer.getId());
                    rel.setEnterpriseWechatCustomerAutoAnswerRuleId(ruleId);
                    return rel;
                }).collect(Collectors.toList());
                enterpriseWechatCustomerTemplateAutoAnswerRuleRelService.saveBatch(rels);
            }
        }
        //清理模板缓存
        enterpriseWechatCostomerMsgTemplateService.clearCache(id);
        //清理缓存
        robotCustomer.setOpenKfId(openKfId);
        deleteEnterpriseWechatRobotCustomerCache(robotCustomer);
        String cityKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_MSG_TEMPLATE_CITY_PREFIX + EnterpriseWechatCustomerMessageStrategyType.CITY.name() + ":" + robotCustomer.getId() + ":" + openKfId;
        String cityAutoKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_AUTO_ANSWER_RULE_CITY_PREFIX + EnterpriseWechatCustomerMessageStrategyType.CITY.name() + ":" + robotCustomer.getId() + ":" + StrategyType.AUTO_ANSWER.getId();
        objectRedisTemplate.delete(cityKey);
        objectRedisTemplate.delete(cityAutoKey);
        //发送消息，检查是否需要生成这个企业微信的机器人活码素材
        enterpriseRobotCustomerSender.sendToGenerateMultipleLiveCode(dto);
        return true;
    }

    /**
     * 清理缓存
     *
     * @param robotCustomer
     */
    public void cleanMsgTemplateCache(EnterpriseWechatRobotCustomer robotCustomer) {
        Long id = robotCustomer.getId();
        String openKfId = robotCustomer.getOpenKfId();
        //自动回复缓存
        enterpriseWechatCustomerAutoAnswerRuleService.clearAutoAnswerRuleCache(robotCustomer);
        //清理模板缓存
        enterpriseWechatCostomerMsgTemplateService.clearCache(id);
        //清理缓存
        deleteEnterpriseWechatRobotCustomerCache(robotCustomer);
        String cityKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_MSG_TEMPLATE_CITY_PREFIX + EnterpriseWechatCustomerMessageStrategyType.CITY.name() + ":" + robotCustomer.getId() + ":" + openKfId;
        String cityAutoKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_AUTO_ANSWER_RULE_CITY_PREFIX + EnterpriseWechatCustomerMessageStrategyType.CITY.name() + ":" + robotCustomer.getId() + ":" + StrategyType.AUTO_ANSWER.getId();

        //关键词回复的策略清理
        String intelligenceCityKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_KEYWORD_ANSWER_RULE_PREFIX + robotCustomer.getId() + ":" + EnterpriseWechatRobotCustomerIntelligenceType.INTELLIGENCE_CITY;
        String generalKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_KEYWORD_ANSWER_RULE_PREFIX + robotCustomer.getId() + ":" + EnterpriseWechatRobotCustomerIntelligenceType.GENERAL;
        List<String> list = Arrays.asList(cityKey, cityAutoKey, intelligenceCityKey, generalKey);


        objectRedisTemplate.delete(list);
    }


    /**
     * 保存欢迎语
     */
    private Long saveWelcomeMessage(String name, Long enterpriseWechatCustomerId, Long advertiserAccountGroupId,
                                    List<EnterpriseWechatRobotCustomerWelcomeMessageDto> welcomeMessageList,
                                    EnterpriseWechatCustomerMessageStrategyType messageStrategyType) {
        if (CollectionUtils.isEmpty(welcomeMessageList)) {
            return null;
        }
        EnterpriseWechatCustomerMsgTemplate welcomeMessageTemplate = new EnterpriseWechatCustomerMsgTemplate();
        welcomeMessageTemplate.setName(name);
        welcomeMessageTemplate.setMsgType(RobotCustomerMsgType.MSGMENU);
        welcomeMessageTemplate.setMessageStrategyType(messageStrategyType);
        enterpriseWechatCostomerMsgTemplateService.save(welcomeMessageTemplate);
        EnterpriseWechatCustomerTemplateRel customerTemplateRel = new EnterpriseWechatCustomerTemplateRel();
        customerTemplateRel.setEnterpriseWechatCustomerId(enterpriseWechatCustomerId)
            .setEnterpriseWechatCustomerTemplateId(welcomeMessageTemplate.getId());
        enterpriseWechatCustomerTemplateRelService.save(customerTemplateRel);
        List<EnterpriseWechatCustomerMsgChildrenTemplate> childrenTemplates = new ArrayList<>();
        for (int i = 0; i < welcomeMessageList.size(); i++) {
            EnterpriseWechatRobotCustomerWelcomeMessageDto temp = welcomeMessageList.get(i);
            EnterpriseWechatCustomerMsgChildrenTemplate enterpriseWechatCustomerMsgChildrenTemplate = new EnterpriseWechatCustomerMsgChildrenTemplate();
            BeanUtils.copyProperties(temp, enterpriseWechatCustomerMsgChildrenTemplate);
            enterpriseWechatCustomerMsgChildrenTemplate.setMsgTemplateId(welcomeMessageTemplate.getId())
                .setSort(i).setAdvertiserAccountGroupId(advertiserAccountGroupId)
                .setJumpType(temp.getJumpType())
                .setAcquisitionLinkServiceGroupId(temp.getAcquisitionLinkServiceGroupId());
            childrenTemplates.add(enterpriseWechatCustomerMsgChildrenTemplate);
        }
        enterpriseWechatCustomerMsgChildrenTemplateService.saveBatch(childrenTemplates);
        return welcomeMessageTemplate.getId();
    }

    /**
     * 保存自动回复、挽留消息
     */
    private Long saveAnswerMessage(String name, Long enterpriseWechatCustomerId, Long advertiserAccountGroupId,
                                   List<EnterpriseWechatCustomerMsgTemplate> msgTemplates, StrategyType strategyType,
                                   EnterpriseWechatCustomerMessageStrategyType messageStrategyType) {
        if (CollectionUtils.isNotEmpty(msgTemplates)) {
            List<EnterpriseWechatCustomerMsgChildrenTemplate> childrenTemplates = new ArrayList<>();
            List<EnterpriseWechatCustomerTemplateAutoAnswerRuleRel> ruleRelList = Lists.newArrayList();
            for (int i = 0; i < msgTemplates.size(); i++) {
                EnterpriseWechatCustomerMsgTemplate enterpriseWechatCustomerMsgTemplate = msgTemplates.get(i);
                enterpriseWechatCustomerMsgTemplate.setName(name + "-" + (StrategyType.AUTO_ANSWER.equals(strategyType) ? "自动回复模板" : "挽留消息模板") + i);
                enterpriseWechatCustomerMsgTemplate.setMessageStrategyType(messageStrategyType);
                enterpriseWechatCostomerMsgTemplateService.save(enterpriseWechatCustomerMsgTemplate);
                List<EnterpriseWechatCustomerMsgChildrenTemplate> childrenTemplateList = enterpriseWechatCustomerMsgTemplate.getTemplates();
                for (int j = 0; j < childrenTemplateList.size(); j++) {
                    EnterpriseWechatCustomerMsgChildrenTemplate enterpriseWechatCustomerMsgChildrenTemplate = childrenTemplateList.get(j);
                    enterpriseWechatCustomerMsgChildrenTemplate.setMsgTemplateId(enterpriseWechatCustomerMsgTemplate.getId()).setSort(j).setAdvertiserAccountGroupId(advertiserAccountGroupId);
                    childrenTemplates.add(enterpriseWechatCustomerMsgChildrenTemplate);
                }
            }
            EnterpriseWechatCustomerAutoAnswerRule autoAnswerRule = new EnterpriseWechatCustomerAutoAnswerRule();
            autoAnswerRule.setMsgTemplateIds(CollectionUtil.mapToArray(msgTemplates, EnterpriseWechatCustomerMsgTemplate::getId, Long[]::new))
                .setRuleType(AutoAnswerRuleType.ANY_CONTENT)
                .setMsgType(RobotCustomerMsgType.LINK)
                .setAdvertiserAccountGroupId(advertiserAccountGroupId)
                .setName(name + "-" + (StrategyType.AUTO_ANSWER.equals(strategyType) ? "自动回复模板" : "挽留消息模板"))
                .setType(strategyType)
                .setMessageStrategyType(messageStrategyType);
            enterpriseWechatCustomerAutoAnswerRuleService.save(autoAnswerRule);
            EnterpriseWechatCustomerTemplateAutoAnswerRuleRel autoAnswerRuleRel = new EnterpriseWechatCustomerTemplateAutoAnswerRuleRel();
            autoAnswerRuleRel.setEnterpriseWechatCustomerId(enterpriseWechatCustomerId)
                .setEnterpriseWechatCustomerAutoAnswerRuleId(autoAnswerRule.getId());
            ruleRelList.add(autoAnswerRuleRel);
            enterpriseWechatCustomerMsgChildrenTemplateService.saveBatch(childrenTemplates);
            enterpriseWechatCustomerTemplateAutoAnswerRuleRelService.saveBatch(ruleRelList);
            Set<Long> msgTemplateIdCollect = childrenTemplates.stream().map(EnterpriseWechatCustomerMsgChildrenTemplate::getMsgTemplateId).collect(Collectors.toSet());
            enterpriseWechatCustomerMsgChildrenTemplateService.deleteEnterpriseWechatCustomerMsgChildrenTemplateCache(msgTemplateIdCollect);
            return autoAnswerRule.getId();
        }
        return null;
    }

    public Boolean uploadUpdateTempFile(EnterpriseWechatRobotCustomerSaveDto dto) {
        Long id = dto.getId();
        if (id == null) {
            return false;
        }
        EnterpriseWechatRobotCustomer robotCustomer = this.getById(id);
        if (robotCustomer == null) {
            return false;
        }
        EnterpriseWechat enterpriseWechat = enterpriseWechatService.getOne(new LambdaQueryWrapper<EnterpriseWechat>().eq(EnterpriseWechat::getCorpid, dto.getCorpId()).eq(EnterpriseWechat::getEnterpriseWechatType, dto.getEnterpriseWechatType()).last("limit 1"));
        if (enterpriseWechat == null) {
            return false;
        }
        if (!robotCustomer.getFilePath().equals(dto.getFilePath())) {
            InputStream inputStream = FileUtil.getInputStream(dto.getFilePath());
            MultipartFile tempFile = FileUtil.getMultipartFile(inputStream, "media");
            TempMaterialResponseBody tempMaterialResponseBody = workWeixinApiClient.uploadTempFile(enterpriseWechat.getAccessToken(), EnterpriseTempMaterialType.IMAGE.getName(), tempFile);
            dto.setMediaId(tempMaterialResponseBody.getMediaId());
        }
        if (CollectionUtils.isNotEmpty(dto.getAutoAnswerList())) {
            List<EnterpriseWechatCustomerMsgChildrenTemplate> autoAnswerList = dto.getAutoAnswerList().stream()
                .map(EnterpriseWechatCustomerMsgTemplate::getTemplates).flatMap(Collection::stream).collect(Collectors.toList());
            uploadTempFile(autoAnswerList, enterpriseWechat);
        }
        if (CollectionUtils.isNotEmpty(dto.getRetainMessageList())) {
            List<EnterpriseWechatCustomerMsgChildrenTemplate> retainMessageList = dto.getRetainMessageList().stream()
                .map(EnterpriseWechatCustomerMsgTemplate::getTemplates).flatMap(Collection::stream).collect(Collectors.toList());
            uploadTempFile(retainMessageList, enterpriseWechat);
        }
        if (CollectionUtils.isNotEmpty(dto.getAutoAnswerListCa())) {
            List<EnterpriseWechatCustomerMsgChildrenTemplate> autoAnswerList = dto.getAutoAnswerListCa().stream()
                .map(EnterpriseWechatCustomerMsgTemplate::getTemplates).flatMap(Collection::stream).collect(Collectors.toList());
            uploadTempFile(autoAnswerList, enterpriseWechat);
        }
        if (CollectionUtils.isNotEmpty(dto.getRetainMessageListCa())) {
            List<EnterpriseWechatCustomerMsgChildrenTemplate> retainMessageList = dto.getRetainMessageListCa().stream()
                .map(EnterpriseWechatCustomerMsgTemplate::getTemplates).flatMap(Collection::stream).collect(Collectors.toList());
            uploadTempFile(retainMessageList, enterpriseWechat);
        }
        return true;
    }

    public EnterpriseWechatRobotCustomerInfoVo getCustomerRobot(Long id) {
        EnterpriseWechatRobotCustomer robotCustomer = this.getById(id);
        if (robotCustomer == null) {
            return null;
        }
        EnterpriseWechatRobotCustomerInfoVo vo = new EnterpriseWechatRobotCustomerInfoVo();
        BeanUtils.copyProperties(robotCustomer, vo);
        //查询分组关联
        List<EnterpriseWechatRobotCustomerGroupRel> list = enterpriseWechatRobotCustomerGroupRelService.list(Wrappers.lambdaQuery(EnterpriseWechatRobotCustomerGroupRel.class)
            .eq(EnterpriseWechatRobotCustomerGroupRel::getEnterpriseWechatRobotCustomerId, id)
            .ne(EnterpriseWechatRobotCustomerGroupRel::getEnterpriseWechatRobotCustomerGroupId, -1));
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Long> collect = list.stream().map(EnterpriseWechatRobotCustomerGroupRel::getEnterpriseWechatRobotCustomerGroupId).collect(Collectors.toSet());
            vo.setWechatRobotCustomerGroupIds(collect);
        }

        EnterpriseWechatRobotCustomerIntelligenceType intelligenceType = robotCustomer.getIntelligenceType();
        if (EnterpriseWechatRobotCustomerIntelligenceType.INTELLIGENCE_CITY.equals(intelligenceType)) {
            //查询全部城市策略的欢迎语模板
            List<EnterpriseWechatRobotCustomerWelcomeMessageDto> welcomeMessage = baseMapper.getWelcomeMessageByCity(id, EnterpriseWechatCustomerMessageStrategyType.CITY);
            Optional.ofNullable(welcomeMessage).filter(CollectionUtils::isNotEmpty).ifPresent(e -> {
                //区分城市策略,为空值且类型为兜底 则为兜底策略
                Map<String, List<EnterpriseWechatRobotCustomerWelcomeMessageDto>> collect = welcomeMessage.stream().collect(Collectors.groupingBy(EnterpriseWechatRobotCustomerWelcomeMessageDto::getStrategyId));
                //必须有欢迎语 才可能会有自动回复消息
                //查询全部城市与自动回复消息关联记录
                List<EnterpriseWechatCustomerAutoAnswerRuleCityDto> enterpriseWechatCustomerAutoAnswerRuleCityRels = enterpriseWechatCustomerAutoAnswerRuleCityRelService.searchRule(id);
                //策略列表构建
                List<EnterpriseWechatRobotCustomerCitySaveDto> enterpriseWechatRobotCustomerCitySaveDtos = new ArrayList<>();
                collect.forEach((strategyId, value) -> {
                    EnterpriseWechatRobotCustomerCitySaveDto enterpriseWechatRobotCustomerCitySaveDto = cityRobots(strategyId, value, enterpriseWechatCustomerAutoAnswerRuleCityRels);
                    Optional.ofNullable(enterpriseWechatRobotCustomerCitySaveDto).ifPresent(enterpriseWechatRobotCustomerCitySaveDtos::add);
                });
                //enterpriseWechatRobotCustomerCitySaveDtos基于EnterpriseWechatRobotCustomerCitySaveDto的msgTemplateId 进行升序
                enterpriseWechatRobotCustomerCitySaveDtos.sort(Comparator.comparing(EnterpriseWechatRobotCustomerCitySaveDto::getMsgTemplateId));

                //获取关键词回复规则id
                List<EnterpriseWechatCustomerKeywordReplyRuleCityRel> enterpriseWechatCustomerKeywordReplyRuleCityRels = enterpriseWechatCustomerKeywordReplyRuleCityRelService.list(new LambdaQueryWrapper<EnterpriseWechatCustomerKeywordReplyRuleCityRel>()
                    .eq(EnterpriseWechatCustomerKeywordReplyRuleCityRel::getEnterpriseWechatCustomerId, id));
                Map<String, List<EnterpriseWechatCustomerKeywordReplyRuleCityRel>> collect1 = enterpriseWechatCustomerKeywordReplyRuleCityRels.stream().collect(Collectors.groupingBy(EnterpriseWechatCustomerKeywordReplyRuleCityRel::getStrategyId));
                //将关键词回复规则id放入到对应的城市策略中
                enterpriseWechatRobotCustomerCitySaveDtos.forEach(cityData -> {
                    String strategyId = cityData.getStrategyId();
                    List<EnterpriseWechatCustomerKeywordReplyRuleCityRel> enterpriseWechatCustomerKeywordReplyRuleCityRelList = collect1.get(strategyId);
                    if (CollectionUtils.isNotEmpty(enterpriseWechatCustomerKeywordReplyRuleCityRelList)) {
                        Set<Long> keywordReplyRuleIds = enterpriseWechatCustomerKeywordReplyRuleCityRelList.stream()
                            .map(EnterpriseWechatCustomerKeywordReplyRuleCityRel::getEnterpriseWechatCustomerAutoAnswerRuleId)
                            .collect(Collectors.toSet());
                        cityData.setKeywordReplyRuleIds(keywordReplyRuleIds);
                    } else {
                        cityData.setKeywordReplyRuleIds(Collections.emptySet());
                    }
                });
                vo.setCityRobots(enterpriseWechatRobotCustomerCitySaveDtos);
            });
        }


        //欢迎语
        List<EnterpriseWechatRobotCustomerWelcomeMessageDto> welcomeMessage = baseMapper.getWelcomeMessage(id, EnterpriseWechatRobotCustomerIntelligenceType.GENERAL.equals(robotCustomer.getIntelligenceType()) ? EnterpriseWechatCustomerMessageStrategyType.ORIGINAL : EnterpriseWechatCustomerMessageStrategyType.BLUE_LINK_ADD_FANS);
        vo.setWelcomeMessage(welcomeMessage);
        //欢迎语
        List<EnterpriseWechatRobotCustomerWelcomeMessageDto> welcomeMessageCa = baseMapper.getWelcomeMessage(id, EnterpriseWechatCustomerMessageStrategyType.ACQUISITION_ASSISTANT);
        vo.setWelcomeMessageCa(welcomeMessageCa);
        //获取所有策略关联关系
        List<EnterpriseWechatCustomerTemplateAutoAnswerRuleRel> enterpriseWechatCustomerTemplateAutoAnswerRuleRels = enterpriseWechatCustomerTemplateAutoAnswerRuleRelService.list(
            Wrappers.lambdaQuery(EnterpriseWechatCustomerTemplateAutoAnswerRuleRel.class).eq(EnterpriseWechatCustomerTemplateAutoAnswerRuleRel::getEnterpriseWechatCustomerId, id)
        );
        if (CollectionUtils.isEmpty(enterpriseWechatCustomerTemplateAutoAnswerRuleRels)) {
            return vo;
        }
        //获取所有策略下的规则
        List<EnterpriseWechatCustomerAutoAnswerRule> enterpriseWechatCustomerAutoAnswerRules = enterpriseWechatCustomerAutoAnswerRuleService.listByIds(
            CollectionUtil.mapToSet(enterpriseWechatCustomerTemplateAutoAnswerRuleRels, EnterpriseWechatCustomerTemplateAutoAnswerRuleRel::getEnterpriseWechatCustomerAutoAnswerRuleId)
        );
        //获取排除掉关键词回复的其它回复规则 - 自动回复
        List<EnterpriseWechatCustomerAutoAnswerRule> answerRules = enterpriseWechatCustomerAutoAnswerRules.stream()
            //排除关键词回复规则
            .filter(rule -> !StrategyType.KEYWORD.equals(rule.getType()))
            .filter(rule -> ObjectUtils.isNotEmpty(rule.getMsgTemplateIds()))
            .collect(Collectors.toList());
        //获取关键词回复规则id
        Set<Long> keywordRuleIds = enterpriseWechatCustomerAutoAnswerRules.stream()
            .filter(rule -> StrategyType.KEYWORD.equals(rule.getType()))
            .map(EnterpriseWechatCustomerAutoAnswerRule::getId)
            .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(keywordRuleIds)) {
            vo.setKeywordReplyRuleIds(keywordRuleIds);
        }
        if (CollectionUtils.isEmpty(answerRules)) {
            return vo;
        }
        List<EnterpriseWechatCustomerMsgTemplate> enterpriseWechatCustomerMsgTemplates = enterpriseWechatCostomerMsgTemplateService.listByIds(
            answerRules.stream().map(EnterpriseWechatCustomerAutoAnswerRule::getMsgTemplateIds).filter(ObjectUtils::isNotEmpty).flatMap(Arrays::stream).collect(Collectors.toSet())
        );
        if (CollectionUtils.isEmpty(enterpriseWechatCustomerMsgTemplates)) {
            return vo;
        }
        List<EnterpriseWechatCustomerMsgChildrenTemplate> enterpriseWechatCustomerMsgChildrenTemplates = enterpriseWechatCustomerMsgChildrenTemplateService.list(
            Wrappers.lambdaQuery(EnterpriseWechatCustomerMsgChildrenTemplate.class)
                .in(EnterpriseWechatCustomerMsgChildrenTemplate::getMsgTemplateId, CollectionUtil.mapToList(enterpriseWechatCustomerMsgTemplates, EnterpriseWechatCustomerMsgTemplate::getId))
        );
        if (CollectionUtils.isEmpty(enterpriseWechatCustomerMsgChildrenTemplates)) {
            return vo;
        }
        Map<Long, List<EnterpriseWechatCustomerMsgChildrenTemplate>> msgTemplateIdToChildrenTemplate = CollectionUtil.groupingBy(
            enterpriseWechatCustomerMsgChildrenTemplates, EnterpriseWechatCustomerMsgChildrenTemplate::getMsgTemplateId
        );
        enterpriseWechatCustomerMsgTemplates.forEach(t -> {
            t.setTemplates(msgTemplateIdToChildrenTemplate.get(t.getId()));
        });
        for (EnterpriseWechatCustomerAutoAnswerRule enterpriseWechatCustomerAutoAnswerRule : answerRules) {
            if (ObjectUtils.isEmpty(enterpriseWechatCustomerAutoAnswerRule.getMsgTemplateIds())) {
                enterpriseWechatCustomerAutoAnswerRule.setMsgTemplateIds(new Long[]{});
            }
            List<EnterpriseWechatCustomerMsgTemplate> msgTemplates = enterpriseWechatCustomerMsgTemplates.stream().filter(
                t -> Arrays.asList(enterpriseWechatCustomerAutoAnswerRule.getMsgTemplateIds()).contains(t.getId())
            ).collect(Collectors.toList());
            if (Arrays.asList(EnterpriseWechatCustomerMessageStrategyType.ORIGINAL, EnterpriseWechatCustomerMessageStrategyType.BLUE_LINK_ADD_FANS)
                .contains(enterpriseWechatCustomerAutoAnswerRule.getMessageStrategyType())) {
                if (StrategyType.AUTO_ANSWER.equals(enterpriseWechatCustomerAutoAnswerRule.getType())) {
                    vo.setAutoAnswerList(splitEnterpriseWechatCustomerMsgTemplate(msgTemplates));
                } else if (StrategyType.RETAIN_MESSAGE.equals(enterpriseWechatCustomerAutoAnswerRule.getType())) {
                    vo.setRetainMessageList(splitEnterpriseWechatCustomerMsgTemplate(msgTemplates));
                }
            } else if (EnterpriseWechatCustomerMessageStrategyType.ACQUISITION_ASSISTANT.equals(enterpriseWechatCustomerAutoAnswerRule.getMessageStrategyType())) {
                if (StrategyType.AUTO_ANSWER.equals(enterpriseWechatCustomerAutoAnswerRule.getType())) {
                    vo.setAutoAnswerListCa(splitEnterpriseWechatCustomerMsgTemplate(msgTemplates));
                } else if (StrategyType.RETAIN_MESSAGE.equals(enterpriseWechatCustomerAutoAnswerRule.getType())) {
                    vo.setRetainMessageListCa(splitEnterpriseWechatCustomerMsgTemplate(msgTemplates));
                }
            }
        }
        return vo;
    }

    private EnterpriseWechatRobotCustomerCitySaveDto cityRobots(String strategyId, List<EnterpriseWechatRobotCustomerWelcomeMessageDto> value, List<EnterpriseWechatCustomerAutoAnswerRuleCityDto> enterpriseWechatCustomerAutoAnswerRuleCityRels) {

        if (CollectionUtils.isEmpty(value)) return null;
        EnterpriseWechatRobotCustomerCitySaveDto enterpriseWechatRobotCustomerCitySaveDto = new EnterpriseWechatRobotCustomerCitySaveDto();
        enterpriseWechatRobotCustomerCitySaveDto.setStrategyId(strategyId);
        EnterpriseWechatRobotCustomerWelcomeMessageDto enterpriseWechatRobotCustomerWelcomeMessageDto = value.get(0);
        enterpriseWechatRobotCustomerCitySaveDto
            .setMsgTemplateId(value.get(0).getMsgTemplateId())
            .setReveal(enterpriseWechatRobotCustomerWelcomeMessageDto.getReveal())
            .setIpRegionalSourceCity(enterpriseWechatRobotCustomerWelcomeMessageDto.getIpRegionalSourceCity())
            .setIpRegionalSourceCityName(enterpriseWechatRobotCustomerWelcomeMessageDto.getIpRegionalSourceCityName())
            .setIpRegionalSourceProvince(enterpriseWechatRobotCustomerWelcomeMessageDto.getIpRegionalSourceProvince())
            .setIpRegionalSourceProvinceName(enterpriseWechatRobotCustomerWelcomeMessageDto.getIpRegionalSourceProvinceName())
            .setAutoAnswer(enterpriseWechatRobotCustomerWelcomeMessageDto.getAutoAnswer())
            .setKeywordReply(enterpriseWechatRobotCustomerWelcomeMessageDto.getKeywordReply())
            .setKeywordReplyNotTriggeredWhenAdded(enterpriseWechatRobotCustomerWelcomeMessageDto.getKeywordReplyNotTriggeredWhenAdded())
            .setWelcomeMessage(value);
        //构建策略自动回复列表
        if (CollectionUtils.isNotEmpty(enterpriseWechatCustomerAutoAnswerRuleCityRels)) {
            List<EnterpriseWechatCustomerAutoAnswerRuleCityDto> answerCity = enterpriseWechatCustomerAutoAnswerRuleCityRels.stream()
                .filter(c -> Objects.equals(strategyId, c.getStrategyId()) && ObjectUtils.isNotEmpty(c.getMsgTemplateIds()))
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(answerCity)) {
                List<EnterpriseWechatCustomerMsgTemplate> enterpriseWechatCustomerMsgTemplates = enterpriseWechatCostomerMsgTemplateService.listByIds(
                    answerCity.stream().map(EnterpriseWechatCustomerAutoAnswerRule::getMsgTemplateIds).filter(ObjectUtils::isNotEmpty).flatMap(Arrays::stream).collect(Collectors.toSet())
                );
                if (CollectionUtils.isNotEmpty(enterpriseWechatCustomerMsgTemplates)) {
                    List<EnterpriseWechatCustomerMsgChildrenTemplate> enterpriseWechatCustomerMsgChildrenTemplates = enterpriseWechatCustomerMsgChildrenTemplateService.list(
                        Wrappers.lambdaQuery(EnterpriseWechatCustomerMsgChildrenTemplate.class)
                            .in(EnterpriseWechatCustomerMsgChildrenTemplate::getMsgTemplateId, CollectionUtil.mapToList(enterpriseWechatCustomerMsgTemplates, EnterpriseWechatCustomerMsgTemplate::getId))
                    );
                    if (CollectionUtils.isNotEmpty(enterpriseWechatCustomerMsgChildrenTemplates)) {
                        Map<Long, List<EnterpriseWechatCustomerMsgChildrenTemplate>> msgTemplateIdToChildrenTemplate = CollectionUtil.groupingBy(
                            enterpriseWechatCustomerMsgChildrenTemplates, EnterpriseWechatCustomerMsgChildrenTemplate::getMsgTemplateId
                        );
                        enterpriseWechatCustomerMsgTemplates.forEach(t -> {
                            t.setTemplates(msgTemplateIdToChildrenTemplate.get(t.getId()));
                        });
                        for (EnterpriseWechatCustomerAutoAnswerRule enterpriseWechatCustomerAutoAnswerRule : answerCity) {
                            if (ObjectUtils.isEmpty(enterpriseWechatCustomerAutoAnswerRule.getMsgTemplateIds())) {
                                enterpriseWechatCustomerAutoAnswerRule.setMsgTemplateIds(new Long[]{});
                            }
                            List<EnterpriseWechatCustomerMsgTemplate> msgTemplates = enterpriseWechatCustomerMsgTemplates.stream().filter(
                                t -> Arrays.asList(enterpriseWechatCustomerAutoAnswerRule.getMsgTemplateIds()).contains(t.getId())
                            ).collect(Collectors.toList());
                            if (EnterpriseWechatCustomerMessageStrategyType.CITY.equals(enterpriseWechatCustomerAutoAnswerRule.getMessageStrategyType())) {
                                enterpriseWechatRobotCustomerCitySaveDto.setAutoAnswerList(splitEnterpriseWechatCustomerMsgTemplate(msgTemplates));
                            }
                        }
                    }
                }
            }
        }
        return enterpriseWechatRobotCustomerCitySaveDto;
    }


    private List<EnterpriseWechatCustomerMsgTemplate> splitEnterpriseWechatCustomerMsgTemplate(List<EnterpriseWechatCustomerMsgTemplate> msgTemplates) {
        if (CollectionUtils.isNotEmpty(msgTemplates)) {
            List<EnterpriseWechatCustomerMsgTemplate> msgTemplateList = msgTemplates.stream().filter(
                t -> RobotCustomerMsgType.LINK.equals(t.getMsgType()) && t.getTemplates() != null && t.getTemplates().size() > 1
            ).collect(Collectors.toList());
            msgTemplates.removeAll(msgTemplateList);
            msgTemplateList = msgTemplateList.stream().map(EnterpriseWechatCustomerMsgTemplate::getTemplates).flatMap(Collection::stream).map(t -> {
                EnterpriseWechatCustomerMsgTemplate msgTemplate = new EnterpriseWechatCustomerMsgTemplate();
                msgTemplate.setMsgType(RobotCustomerMsgType.LINK);
                msgTemplate.setAdvertiserAccountGroupId(t.getAdvertiserAccountGroupId());
                msgTemplate.setTemplates(new ArrayList<EnterpriseWechatCustomerMsgChildrenTemplate>() {{
                    add(t);
                }});
                return msgTemplate;
            }).collect(Collectors.toList());
            msgTemplates.addAll(0, msgTemplateList);
        }
        return msgTemplates;
    }

    @DS(DbConstants.POSTGRESQL_DEFAULT)
    public Boolean deleteFromPublic(@NotBlank String openKfId, @NotBlank String corpId, @NotBlank String agentId) {
        return remove(new LambdaQueryWrapper<EnterpriseWechatRobotCustomer>()
            .eq(EnterpriseWechatRobotCustomer::getOpenKfId, openKfId)
            .eq(EnterpriseWechatRobotCustomer::getAgentId, agentId)
            .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId));
    }

    @DS(DbConstants.POSTGRESQL_DEFAULT)
    public EnterpriseWechatRobotCustomer findByOpenKfId(String openKfId, String corpId) {
        String newOpenKfId = defaultStringRedisTemplate.opsForValue().get(RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_ROBOT_OLD_TO_NEW_OPEN_KF_ID + corpId + ":" + openKfId);
        openKfId = StringUtils.isNotBlank(newOpenKfId) ? newOpenKfId : openKfId;
        String key = RedisConstant.ENTERPRISE_WECHAT_ROBOT_CUSTOMER_PREFIX + corpId + ":" + openKfId;
        log.info("查询公共库客服信息确认客服机器人所属账户,key={}", key);
        Object result = defaultObjectRedisTemplate.opsForValue().get(key);
        if (Objects.nonNull(result)) {
            log.info("查询公共库客服信息确认客服机器人所属账户,key={},走缓存", key);
            EnterpriseWechatRobotCustomer enterpriseWechatRobotCustomer = (EnterpriseWechatRobotCustomer) result;
            return enterpriseWechatRobotCustomer;
        }
        EnterpriseWechatRobotCustomer ewrc = getOne(new LambdaQueryWrapper<EnterpriseWechatRobotCustomer>()
            .eq(EnterpriseWechatRobotCustomer::getOpenKfId, openKfId)
            .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId)
            .last("limit 1"));

        if (Objects.nonNull(ewrc)) {
            defaultObjectRedisTemplate.opsForValue().set(key, ewrc, 2, TimeUnit.HOURS);
        }
        return ewrc;
    }

    //删除已有的客服帐号 表中删除，然后关系删除，然后远程接口删除
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteCustomerRobot(Long id, EnterpriseWechatRobotCustomer robotCustomer, EnterpriseWechat enterpriseWechat) {
        Set<Long> enterpriseWechatCustomerAutoAnswerRuleIds = Sets.newHashSet();
        Set<Long> msgTemplateIds = Sets.newHashSet();
        EnterpriseWechatCustomerTemplateRel customerTemplateRel = enterpriseWechatCustomerTemplateRelService.getOne(new LambdaQueryWrapper<EnterpriseWechatCustomerTemplateRel>()
            .eq(EnterpriseWechatCustomerTemplateRel::getEnterpriseWechatCustomerId, id)
            .last("limit 1"));
        if (customerTemplateRel != null) {
            Long enterpriseWechatCustomerTemplateId = customerTemplateRel.getEnterpriseWechatCustomerTemplateId();
            msgTemplateIds.add(enterpriseWechatCustomerTemplateId);
        }
        List<EnterpriseWechatRobotCustomerStrategyDto> strategys = baseMapper.getStrategy(id);
        if (CollectionUtils.isNotEmpty(strategys)) {
            Set<Long> tempAutoAnswerRuleIds = strategys.stream().map(EnterpriseWechatRobotCustomerStrategyDto::getEnterpriseWechatCustomerAutoAnswerRuleId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(tempAutoAnswerRuleIds)) {
                enterpriseWechatCustomerAutoAnswerRuleIds.addAll(tempAutoAnswerRuleIds);
            }
            Set<Long> tempMsgTemplateIds = strategys.stream().map(EnterpriseWechatRobotCustomerStrategyDto::getMsgTemplateIds).flatMap(Arrays::stream).filter(ObjectUtils::isNotEmpty).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(tempMsgTemplateIds)) {
                msgTemplateIds.addAll(tempMsgTemplateIds);
            }
        }

        //清理欢迎语模版与城市客服关联记录
        enterpriseWechatCustomerMsgTemplateCityRelService.remove(Wrappers.lambdaQuery(EnterpriseWechatCustomerMsgTemplateCityRel.class)
            .eq(EnterpriseWechatCustomerMsgTemplateCityRel::getEnterpriseWechatCustomerId, id));
        //清理自动回复与城市记录关联记录
        enterpriseWechatCustomerAutoAnswerRuleCityRelService.remove(Wrappers.lambdaQuery(EnterpriseWechatCustomerAutoAnswerRuleCityRel.class)
            .eq(EnterpriseWechatCustomerAutoAnswerRuleCityRel::getEnterpriseWechatCustomerId, id));
        //清理关键词回复与城市记录关联记录
        enterpriseWechatCustomerKeywordReplyRuleCityRelService.remove(Wrappers.lambdaQuery(EnterpriseWechatCustomerKeywordReplyRuleCityRel.class)
            .eq(EnterpriseWechatCustomerKeywordReplyRuleCityRel::getEnterpriseWechatCustomerId, id));
        //清理城市客服緩存
        String cityKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_MSG_TEMPLATE_CITY_PREFIX + EnterpriseWechatCustomerMessageStrategyType.CITY.name() + ":" + robotCustomer.getId() + ":" + robotCustomer.getOpenKfId();
        String cityAutoKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_AUTO_ANSWER_RULE_CITY_PREFIX + EnterpriseWechatCustomerMessageStrategyType.CITY.name() + ":" + robotCustomer.getId() + ":" + StrategyType.AUTO_ANSWER.getId();
        objectRedisTemplate.delete(cityKey);
        objectRedisTemplate.delete(cityAutoKey);

        //清除客服机器人记录
        enterpriseWechatRobotCustomerMapper.deleteById(id);
        enterpriseWechatRobotCustomerMapper.deleteToPublic(robotCustomer.getOpenKfId(), robotCustomer.getCorpId(), TenantContextHolder.get());
        enterpriseWechatRobotCustomerPmpRelService.deleteByRobotCustomerId(id);
        enterpriseWechatCustomerTemplateRelService.remove(new LambdaQueryWrapper<EnterpriseWechatCustomerTemplateRel>()
            .eq(EnterpriseWechatCustomerTemplateRel::getEnterpriseWechatCustomerId, id));
        enterpriseWechatCustomerTemplateAutoAnswerRuleRelService.remove(new LambdaQueryWrapper<EnterpriseWechatCustomerTemplateAutoAnswerRuleRel>()
            .eq(EnterpriseWechatCustomerTemplateAutoAnswerRuleRel::getEnterpriseWechatCustomerId, id));
        if (CollectionUtils.isNotEmpty(enterpriseWechatCustomerAutoAnswerRuleIds)) {
            enterpriseWechatCustomerAutoAnswerRuleService.remove(new LambdaQueryWrapper<EnterpriseWechatCustomerAutoAnswerRule>()
                .ne(EnterpriseWechatCustomerAutoAnswerRule::getType, StrategyType.KEYWORD)
                .in(EnterpriseWechatCustomerAutoAnswerRule::getId, enterpriseWechatCustomerAutoAnswerRuleIds));
        }
        if (CollectionUtils.isNotEmpty(msgTemplateIds)) {
            enterpriseWechatCostomerMsgTemplateService.remove(new LambdaQueryWrapper<EnterpriseWechatCustomerMsgTemplate>()
                .in(EnterpriseWechatCustomerMsgTemplate::getId, msgTemplateIds));
        }
        if (CollectionUtils.isNotEmpty(msgTemplateIds)) {
            enterpriseWechatCustomerMsgChildrenTemplateService.remove(new LambdaQueryWrapper<EnterpriseWechatCustomerMsgChildrenTemplate>()
                .in(EnterpriseWechatCustomerMsgChildrenTemplate::getMsgTemplateId, msgTemplateIds));
        }
        RobotCustomerUpdateRequestBody robotCustomerUpdateRequestBody = new RobotCustomerUpdateRequestBody();
        robotCustomerUpdateRequestBody.setOpenKfid(robotCustomer.getOpenKfId());
        try {
            if (robotCustomer.getManagePrivilege()) {
                workWeixinApiClient.deleteCustomerRobot(enterpriseWechat.getAccessToken(), robotCustomerUpdateRequestBody);
            }
        } catch (Exception e) {
            log.warn("删除企业微信客服-agentId:{},robotCustomer:{}-异常", TenantContextHolder.get(), JSON.toJSONString(robotCustomer), e);
        }
        return true;
    }

    public IPage<EnterpriseWechatRobotCustomer> getPageList(IPage<EnterpriseWechatRobotCustomer> page, EnterpriseWechatRobotCustomerDto ewrcDto) {
        if (StringUtils.isBlank(ewrcDto.getStartTime()) || StringUtils.isBlank(ewrcDto.getEndTime())) {
            return null;
        }
        if (!Objects.isNull(ewrcDto.getOrder()) && !Objects.isNull(ewrcDto.getSortField())) {
            String sortFieldStr = "";
            switch (ewrcDto.getSortField()) {
                case RECEPTION_CUSTOMER_NUM:
                    sortFieldStr = "reception_customer_num";
                    break;
                case ADD_WORK_WECHAT_NUM:
                    sortFieldStr = "add_work_wechat_num";
                    break;
                case ADD_WORK_WECHAT_RATE:
                    sortFieldStr = "add_work_wechat_rate";
                    break;
                case FOLLOW_OFFICIAL_ACCOUNT_NUM:
                    sortFieldStr = "follow_official_account_num";
                    break;
                case FOLLOW_OFFICIAL_ACCOUNT_RATE:
                    sortFieldStr = "follow_official_account_rate";
                    break;
                case CREATED_AT:
                    sortFieldStr = "created_at";
                    break;
                case UPDATED_AT:
                    sortFieldStr = "updated_at";
                    break;
            }
            ewrcDto.setSortFieldStr(sortFieldStr).setOrderStr(!Objects.isNull(ewrcDto.getOrder()) ? ewrcDto.getOrder().toString() : Direction.desc.toString());
        }
        IPage<EnterpriseWechatRobotCustomer> pageList = enterpriseWechatRobotCustomerReadOnlyService.getPageNewList(page, ewrcDto, TenantContextHolder.get());
        if (!Objects.isNull(pageList) && !CollectionUtils.isEmpty(pageList.getRecords())) {
            List<EnterpriseWechat> enterpriseWechatList = enterpriseWechatService.list(new LambdaQueryWrapper<EnterpriseWechat>()
                .in(EnterpriseWechat::getCorpid, pageList.getRecords().stream().map(EnterpriseWechatRobotCustomer::getCorpId).collect(Collectors.toList()))
            );
            Map<String, EnterpriseWechat> enterpriseWechatMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(enterpriseWechatList)) {
                enterpriseWechatList.forEach(enterpriseWechat -> enterpriseWechatMap.put(enterpriseWechat.getCorpid(), enterpriseWechat));
            }
            List<Long> robotIds = pageList.getRecords().stream().map(EnterpriseWechatRobotCustomer::getId).collect(Collectors.toList());
            //查询客服关联分组
            List<EnterpriseWechatRobotCustomer> groupsByRobotId = enterpriseWechatRobotCustomerGroupRelService.getGroupsByRobotId(robotIds);
            Map<Long, String> groupMap = groupsByRobotId.stream().collect(Collectors.toMap(EnterpriseWechatRobotCustomer::getId, EnterpriseWechatRobotCustomer::getGroupIds));
            pageList.getRecords().forEach(ewrc -> {
                EnterpriseWechat enterpriseWechat = enterpriseWechatMap.get(ewrc.getCorpId());
                if (Objects.nonNull(enterpriseWechat)) {
                    ewrc.setWorkWechatName(enterpriseWechat.getCorpName())/*.setEnterpriseWechatType(enterpriseWechat.getEnterpriseWechatType())*/;
                }
                Optional.of(groupMap).ifPresent(m -> {
                    String groupIds = m.get(ewrc.getId());
                    if (StringUtils.isNotBlank(groupIds)) {
                        String[] split = groupIds.split(",");
                        List<Long> collect = Arrays.stream(split).map(e -> {
                            Long aLong = Convert.toLong(e, null);
                            return aLong;
                        }).collect(Collectors.toList());
                        ewrc.setWechatRobotCustomerGroupIds(collect);
                    }
                });


            });
        }
        return pageList;
    }

    /**
     * 刷新微信客服机器人
     */
    public void refreshCustomerServiceRobot(Long advertiserAccountGroupId) {
        String agentId = TenantContextHolder.get();
        List<EnterpriseWechat> enterpriseWechatList = enterpriseWechatService.getPmpEnterpriseWechatList(agentId, advertiserAccountGroupId);
        if (CollectionUtils.isEmpty(enterpriseWechatList)) {
            return;
        }
        for (EnterpriseWechat enterpriseWechat : enterpriseWechatList) {
            try {
                List<KfAccountList> kfAccountList = this.getKfAccountList(enterpriseWechat.getAccessToken());
                this.deleteCustomerServiceRobotAbsent(kfAccountList, enterpriseWechat.getCorpid());
                this.updateCustomerServiceRobotNameAndAvatar(kfAccountList, enterpriseWechat.getCorpid());
            } catch (Exception e) {
                log.error("刷新微信客服机器人失败", e);
            }
        }
        TenantContextHolder.set(agentId);
    }

    /**
     * 更新微信客服机器人的名称和头像
     */
    private void updateCustomerServiceRobotNameAndAvatar(List<KfAccountList> kfAccountList, String corpId) {
        TenantContextHolder.clearContext();
        if (CollectionUtils.isEmpty(kfAccountList)) {
            return;
        }
        Map<String, KfAccountList> openKfIdToKfAccount = CollectionUtil.toMap(kfAccountList, KfAccountList::getOpenKfid);
        List<EnterpriseWechatRobotCustomer> robotCustomers = this.list(
            Wrappers.lambdaQuery(EnterpriseWechatRobotCustomer.class)
                .select(
                    EnterpriseWechatRobotCustomer::getId,
                    EnterpriseWechatRobotCustomer::getAgentId,
                    EnterpriseWechatRobotCustomer::getOpenKfId,
                    EnterpriseWechatRobotCustomer::getCorpId,
                    EnterpriseWechatRobotCustomer::getName,
                    EnterpriseWechatRobotCustomer::getFilePath
                )
                .in(
                    EnterpriseWechatRobotCustomer::getOpenKfId,
                    CollectionUtil.mapToList(kfAccountList, KfAccountList::getOpenKfid)
                )
                .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId)
        );
        robotCustomers = robotCustomers.stream().filter(t -> {
            KfAccountList kfAccount = openKfIdToKfAccount.get(t.getOpenKfId());
            return !StringUtils.equals(t.getName(), kfAccount.getName()) || !StringUtils.equals(t.getFilePath(), kfAccount.getAvatar());
        }).peek(item -> {
            KfAccountList kfAccount = openKfIdToKfAccount.get(item.getOpenKfId());
            item.setName(kfAccount.getName());
            item.setFilePath(kfAccount.getAvatar());
            //清理缓存
            deleteEnterpriseWechatRobotCustomerCache(item);
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(robotCustomers)) {
            return;
        }
        Map<String, List<String>> agentId2OpenKfIds = robotCustomers.stream().collect(
            Collectors.groupingBy(EnterpriseWechatRobotCustomer::getAgentId, Collectors.mapping(EnterpriseWechatRobotCustomer::getOpenKfId, Collectors.toList()))
        );
        for (EnterpriseWechatRobotCustomer robotCustomer : robotCustomers) {
            robotCustomer.setOpenKfId(null);
            robotCustomer.setCorpId(null);
            robotCustomer.setAgentId(null);
        }
        this.updateBatchById(robotCustomers);
        for (String agentId : agentId2OpenKfIds.keySet()) {
            TenantContextHolder.set(agentId);
            List<String> openKfIds = agentId2OpenKfIds.get(agentId);
            List<EnterpriseWechatRobotCustomer> privateRobotCustomers = this.list(
                Wrappers.lambdaQuery(EnterpriseWechatRobotCustomer.class)
                    .select(
                        EnterpriseWechatRobotCustomer::getId,
                        EnterpriseWechatRobotCustomer::getCorpId,
                        EnterpriseWechatRobotCustomer::getOpenKfId
                    )
                    .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId)
                    .in(EnterpriseWechatRobotCustomer::getOpenKfId, openKfIds)
            );
            privateRobotCustomers.forEach(item -> {
                KfAccountList kfAccount = openKfIdToKfAccount.get(item.getOpenKfId());
                item.setName(kfAccount.getName());
                item.setFilePath(kfAccount.getAvatar());
                //清理缓存
                deleteEnterpriseWechatRobotCustomerCache(item);
                item.setOpenKfId(null);
                item.setCorpId(null);
            });
            this.updateBatchById(privateRobotCustomers);
            TenantContextHolder.clearContext();
        }
    }

    /**
     * 删除不存在的微信客服机器人
     */
    private void deleteCustomerServiceRobotAbsent(List<KfAccountList> kfAccountList, String corpId) {
        TenantContextHolder.clearContext();
        List<EnterpriseWechatRobotCustomer> robotCustomers = this.list(
            Wrappers.lambdaQuery(EnterpriseWechatRobotCustomer.class)
                .select(
                    EnterpriseWechatRobotCustomer::getId,
                    EnterpriseWechatRobotCustomer::getAgentId,
                    EnterpriseWechatRobotCustomer::getOpenKfId
                )
                .notIn(
                    CollectionUtils.isNotEmpty(kfAccountList),
                    EnterpriseWechatRobotCustomer::getOpenKfId,
                    CollectionUtil.mapToList(kfAccountList, KfAccountList::getOpenKfid)
                )
                .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId)
        );
        this.deleteCustomerServiceRobotBatch(robotCustomers, corpId);
    }

    /**
     * 公共获取 - 跳转微信客服机器人（获取客服帐号链接）
     */
    public String getJumpToWechatLinkUrl(Long id, String pid, String agentId) {
        TenantContextHolder.set(agentId);
        log.info("跳转微信客服机器人-pid:{},agentId:{},id:{}", pid, agentId, id);
        if (Objects.isNull(id) || StringUtils.isEmpty(StringUtils.trim(pid))) {
            return null;
        }
        EnterpriseWechatRobotCustomer eqrc = (EnterpriseWechatRobotCustomer) objectRedisTemplate.opsForValue().get(RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_SERVICE_ROBOT_CACHE + id);
        if (eqrc == null && (eqrc = this.getById(id)) != null) {
            objectRedisTemplate.opsForValue().set(RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_SERVICE_ROBOT_CACHE + id, eqrc, 2, TimeUnit.DAYS);
        }
        //1.244.0 加上客服机器人使用状态判断 只有上线了才可使用
        if (Objects.isNull(eqrc) || StringUtils.isEmpty(StringUtils.trim(eqrc.getOpenKfId())) || !BaseStatusEnum.ENABLE.equals(eqrc.getUsageStatus())) {
            return null;
        }
        String sceneParam = pid.replace("-", "");
        String redisKey = RedisConstant.WORK_WEIXIN_KF_SCENE_PID_PREFIX + eqrc.getOpenKfId() + ":" + sceneParam;
        if (defaultStringRedisTemplate.opsForValue().get(redisKey) == null) {
            defaultStringRedisTemplate.opsForValue().set(redisKey, pid, 1, TimeUnit.DAYS);
            enterpriseRobotCustomerSender.recoveryCustomerServiceRobot(id, 1, false, false, null, sceneParam);
        }
        if (StringUtils.isNotBlank(eqrc.getOpenKfUrl())) {
            return eqrc.getOpenKfUrl() + "&scene_param=" + sceneParam;
        }
        String accessToken = enterpriseWechatService.getAccessTokenByCorpId(eqrc.getCorpId());
        RobotCustomerLinkRequestBody robotCustomerLinkRequestBody = new RobotCustomerLinkRequestBody();
        robotCustomerLinkRequestBody.setOpenKfid(eqrc.getOpenKfId());
        robotCustomerLinkRequestBody.setScene(ENTERPRISE_WECHAT_CUSTOMER_SERVICE_ROBOT_SCENE);
        RobotCustomerLinkResponseBody robotCustomerLinkResponseBody = workWeixinApiClient.cgiBinKfAddContactWay(accessToken, robotCustomerLinkRequestBody);
        return robotCustomerLinkResponseBody.getUrl() + "&scene_param=" + sceneParam;
    }

    /**
     * 尝试恢复企业微信客服机器人
     */
    @SneakyThrows
    public void tryRecoveryCustomerServiceRobot(RecoveryCustomerServiceRobotDto recoveryCustomerServiceRobotDto) {
        EnterpriseWechatRobotCustomer enterpriseWechatRobotCustomer = this.getById(recoveryCustomerServiceRobotDto.getWechatCustomerServiceRobotId());
        //强制修复
        if (recoveryCustomerServiceRobotDto.getForceRecovery()) {
            this.recoveryCustomerServiceRobot(recoveryCustomerServiceRobotDto);
            return;
        }
        if (recoveryCustomerServiceRobotDto.getAbnormalRecovery()) {
            //异常修复
            this.abnormalRecoveryCustomerServiceRobot(recoveryCustomerServiceRobotDto, enterpriseWechatRobotCustomer);
        } else {
            //智能修复
            this.intelligentRecoveryCustomerServiceRobot(recoveryCustomerServiceRobotDto, enterpriseWechatRobotCustomer);
        }
    }

    /**
     * 智能恢复企业微信客服机器人
     */
    public void intelligentRecoveryCustomerServiceRobot(RecoveryCustomerServiceRobotDto recoveryCustomerServiceRobotDto, EnterpriseWechatRobotCustomer enterpriseWechatRobotCustomer) {
        if (BaseStatusEnum.ENABLE.equals(enterpriseWechatRobotCustomer.getRecoveryStatus())) {
            if (enterpriseWechatRobotCustomer.getRecoveryStrategy() == null || EnterpriseWechatRobotCustomerRecoveryStrategy.TIME_POINT.equals(enterpriseWechatRobotCustomer.getRecoveryStrategy())) {
                if (Instant.now().minus(enterpriseWechatRobotCustomer.getRecoveryConstructDelay(), ChronoUnit.MINUTES).isAfter(enterpriseWechatRobotCustomer.getRecoveryAt())) {
                    this.recoveryCustomerServiceRobot(recoveryCustomerServiceRobotDto);
                    return;
                }
            }
            if (EnterpriseWechatRobotCustomerRecoveryStrategy.ENTRY_RATIO.equals(enterpriseWechatRobotCustomer.getRecoveryStrategy())) {
                if (this.robotEntryRatio(enterpriseWechatRobotCustomer.getOpenKfId(), recoveryCustomerServiceRobotDto.getScene()) <= enterpriseWechatRobotCustomer.getRecoveryEntryRatio()) {
                    this.recoveryCustomerServiceRobot(recoveryCustomerServiceRobotDto);
                    return;
                }
            }
        }
    }

    /**
     * 企业微信客服机器人进线率
     */
    private int robotEntryRatio(String openKfId, String scene) {
        String clickListKey = RedisConstant.WORK_WEIXIN_KF_CLICK_LIST + openKfId;
        objectRedisTemplate.opsForList().leftPush(clickListKey, scene);
        objectRedisTemplate.expire(clickListKey, 1, TimeUnit.DAYS);
        List<Object> clickList = objectRedisTemplate.opsForList().range(clickListKey, 0, -1);
        if (clickList.size() < 10) {
            return 100;
        }
        for (int i = 0; i < clickList.size() - 10; i++) {
            objectRedisTemplate.opsForList().rightPop(clickListKey);
        }
        clickList = clickList.subList(0, 10);
        List<String> entryListKeys = clickList.stream().map(t -> RedisConstant.WORK_WEIXIN_KF_ENTRY_LIST + openKfId + ":" + t.toString()).collect(Collectors.toList());
        return (int) (objectRedisTemplate.opsForValue().multiGet(entryListKeys).stream().filter(Objects::nonNull).count() * 10);
    }

    /**
     * 异常恢复企业微信客服机器人
     */
    public void abnormalRecoveryCustomerServiceRobot(RecoveryCustomerServiceRobotDto recoveryCustomerServiceRobotDto, EnterpriseWechatRobotCustomer enterpriseWechatRobotCustomer) {
        Long wechatCustomerServiceRobotId = recoveryCustomerServiceRobotDto.getWechatCustomerServiceRobotId();
        if (BaseStatusEnum.ENABLE.equals(getWechatCustomerServiceRobotAbnormalOfflineStatus(wechatCustomerServiceRobotId))) {
            modifyWechatCustomerServiceRobotStatus(wechatCustomerServiceRobotId, EnterpriseWechatRobotCustomerStatus.ACCOUNT_ABNORMALITY);
            modifyWechatCustomerServiceRobotUsageStatus(wechatCustomerServiceRobotId, BaseStatusEnum.DISABLE);
            enterpriseRobotCustomerSender.sendCustomerServiceRobotAbnormalNotification(wechatCustomerServiceRobotId, false, recoveryCustomerServiceRobotDto.getAbnormalNoticeType());
        } else {
            if (BaseStatusEnum.ENABLE.equals(enterpriseWechatRobotCustomer.getAccountAbnormalityRecoveryStatus())) {
                this.recoveryCustomerServiceRobot(recoveryCustomerServiceRobotDto);
            } else {
                modifyWechatCustomerServiceRobotStatus(wechatCustomerServiceRobotId, EnterpriseWechatRobotCustomerStatus.ACCOUNT_ABNORMALITY);
                enterpriseRobotCustomerSender.sendCustomerServiceRobotAbnormalNotification(wechatCustomerServiceRobotId, false, recoveryCustomerServiceRobotDto.getAbnormalNoticeType());
            }
        }
    }

    /**
     * 恢复企业微信客服机器人
     */
    public void recoveryCustomerServiceRobot(RecoveryCustomerServiceRobotDto recoveryCustomerServiceRobotDto) {
        Long wechatCustomerServiceRobotId = recoveryCustomerServiceRobotDto.getWechatCustomerServiceRobotId();
        EnterpriseWechatRobotCustomer enterpriseWechatRobotCustomer = this.getById(wechatCustomerServiceRobotId);
        String corpId = enterpriseWechatRobotCustomer.getCorpId();
        String openKfId = enterpriseWechatRobotCustomer.getOpenKfId();
        String agentId = TenantContextHolder.get();
        String recoverLockKey = agentId + ":" + RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_ROBOT_RECOVER_LOCK + wechatCustomerServiceRobotId;
        EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatCacheByCorpId(corpId);
        RLock recoverLock = redissonClient.getFairLock(recoverLockKey);
        try {
            if (!recoverLock.tryLock(0, 180, TimeUnit.SECONDS)) {
                return;
            }
            String robotName = enterpriseWechatRobotCustomer.getName();
            String mediaId = enterpriseWechatCustomerMessageService.getThumbMediaIdByMediaId(null, enterpriseWechatRobotCustomer.getFilePath(), enterpriseWechatRobotCustomer.getCorpId());
            String accessToken = enterpriseWechat.getAccessToken();
            String newOpenKfId = this.createCustomerServiceRobot(accessToken, robotName, mediaId);
            String newOpenKfUrl = this.obtainCustomerServiceRobotLink(accessToken, newOpenKfId);
            baseMapper.recoveryCustomerServiceRobot(agentId, robotName, newOpenKfId, newOpenKfUrl, enterpriseWechatRobotCustomer.getCorpId(), enterpriseWechatRobotCustomer.getOpenKfId());
            TenantContextHolder.clearContext();
            log.info("尝试恢复企业微信客服机器人, corpId = {}, openKfId = {}, newOpenKfId = {}", corpId, openKfId, newOpenKfId);
            defaultStringRedisTemplate.opsForValue().set(RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_ROBOT_OLD_TO_NEW_OPEN_KF_ID + corpId + ":" + openKfId, newOpenKfId, 30, TimeUnit.MINUTES);
            TenantContextHolder.set(agentId);
            this.cleanWechatServiceRobotCustomersCache(wechatCustomerServiceRobotId);
            enterpriseRobotCustomerSender.destroyCustomerServiceRobot(
                wechatCustomerServiceRobotId, corpId, openKfId,
                enterpriseWechatRobotCustomer.getOpenKfUrl(), newOpenKfId, newOpenKfUrl,
                enterpriseWechatRobotCustomer.getRecoveryDestroyDelay(),
                recoveryCustomerServiceRobotDto.getAbnormalRecovery()
            );
            if (recoveryCustomerServiceRobotDto.getAbnormalRecovery()) {
                enterpriseRobotCustomerSender.sendCustomerServiceRobotAbnormalNotification(wechatCustomerServiceRobotId, true, recoveryCustomerServiceRobotDto.getAbnormalNoticeType());
            }
        } catch (ai.yiye.agent.weixin.exception.MarketingApiException e) {
            log.error("创建微信客服机器人失败，errcode:{}，errmsg:{}", e.getErrcode(), e.getMessage(), e);
            if (Objects.equals(e.getErrcode(), WorkWeixinResultCode.KF_ACCOUNT_LIMIT_FOR_USAGE_RATE_TOO_LOW.getCode())) {
                enterpriseRobotCustomerSender.sendCustomerServiceRobotAbnormalNotification(wechatCustomerServiceRobotId, false, EnterpriseWechatRobotCustomerAbnormalNoticeType.CREATE_LIMIT);
            } else {
                if (recoveryCustomerServiceRobotDto.getRetryCount() <= 3) {
                    enterpriseRobotCustomerSender.recoveryCustomerServiceRobot(
                        recoveryCustomerServiceRobotDto.getWechatCustomerServiceRobotId(),
                        recoveryCustomerServiceRobotDto.getRetryCount() + 1,
                        recoveryCustomerServiceRobotDto.getAbnormalRecovery(),
                        recoveryCustomerServiceRobotDto.getForceRecovery(),
                        recoveryCustomerServiceRobotDto.getAbnormalNoticeType(),
                        null);
                } else {
                    if (recoveryCustomerServiceRobotDto.getAbnormalRecovery()) {
                        modifyWechatCustomerServiceRobotStatus(wechatCustomerServiceRobotId, EnterpriseWechatRobotCustomerStatus.ACCOUNT_ABNORMALITY);
                    }
                    enterpriseRobotCustomerSender.sendCustomerServiceRobotAbnormalNotification(wechatCustomerServiceRobotId, false, EnterpriseWechatRobotCustomerAbnormalNoticeType.RECOVERY_FAIL);
                }
            }
        } catch (Exception e) {
            log.error("恢复企业微信客服机器人失败", e);
        } finally {
            recoverLock.unlock();
        }
    }

    private String robotNameGenerate(String oldName) {
        String suffixes = "⁰¹²³⁴⁵⁶⁷⁸⁹";
        int index = suffixes.indexOf(oldName.charAt(oldName.length() - 1));
        if (index == -1) {
            return oldName + "⁰";
        } else if (index == 9) {
            return oldName.substring(0, oldName.length() - 1) + "⁰";
        } else {
            return oldName.substring(0, oldName.length() - 1) + suffixes.charAt(index + 1);
        }
    }

    /**
     * 获取企业微信客服机器人链接
     */
    private String obtainCustomerServiceRobotLink(String accessToken, String openKfId) {
        RobotCustomerLinkRequestBody robotCustomerLinkRequestBody = new RobotCustomerLinkRequestBody();
        robotCustomerLinkRequestBody.setOpenKfid(openKfId);
        robotCustomerLinkRequestBody.setScene(ENTERPRISE_WECHAT_CUSTOMER_SERVICE_ROBOT_SCENE);
        RobotCustomerLinkResponseBody robotCustomerLinkResponseBody = workWeixinApiClient.cgiBinKfAddContactWay(accessToken, robotCustomerLinkRequestBody);
        return robotCustomerLinkResponseBody.getUrl();
    }

    /**
     * 创建企业微信客服机器人
     */
    private String createCustomerServiceRobot(String accessToken, String name, String mediaId) {
        RobotCustomerCreateRequestBody robotCustomerCreateRequestBody = new RobotCustomerCreateRequestBody();
        robotCustomerCreateRequestBody.setName(name);
        robotCustomerCreateRequestBody.setMediaId(mediaId);
        RobotCustomerResponseBody robotCustomerResponseBody = workWeixinApiClient.addRobotCustomer(accessToken, robotCustomerCreateRequestBody);
        return robotCustomerResponseBody.getOpenKfid();
    }

    /**
     * 查询当前项目客服机器人列表
     */
    public List<EnterpriseWechatRobotCustomer> robots(Long advertiserAccountGroupId) {
        return this.baseMapper.robots(advertiserAccountGroupId, TenantContextHolder.get());
    }

    /**
     * 暂存微信客服组件授权任务
     */
    public void temporaryStoreCustomerServiceComponentAuthTask(CustomerServiceComponentAuthTaskInfoDto taskInfo) {
        defaultObjectRedisTemplate.opsForValue().set(RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_SERVICE_COMPONENT_AUTH_TASK_INFO_KEY + taskInfo.getState(), taskInfo, 2, TimeUnit.HOURS);
    }

    /**
     * 获取微信客服组件授权任务
     */
    public CustomerServiceComponentAuthTaskInfoDto getTemporaryCustomerServiceComponentAuthTask(String state) {
        return (CustomerServiceComponentAuthTaskInfoDto) defaultObjectRedisTemplate.opsForValue().get(RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_SERVICE_COMPONENT_AUTH_TASK_INFO_KEY + state);
    }

    /**
     * 创建微信客服组件授权任务
     */
    public CustomerServiceComponentAuthTaskInfoDto createCustomerServiceComponentAuthTask(Long advertiserAccountGroupId) {
        String state = RandomUtil.randomString(6) + DateUtil.format(new DateTime(), "mmss");
        CustomerServiceComponentAuthTaskInfoDto taskInfo = new CustomerServiceComponentAuthTaskInfoDto();
        taskInfo.setState(state);
        taskInfo.setAgentId(TenantContextHolder.get());
        taskInfo.setAdvertiserAccountGroupId(advertiserAccountGroupId);
        taskInfo.setSuccess(false);
        taskInfo.setErrorMessage("微信客服组件未授权");
        taskInfo.setAuthUrl(enterpriseWechatThirdPartyConfig.getCustomerServiceComponentAuthUrl() + "?state=" + state);
        this.temporaryStoreCustomerServiceComponentAuthTask(taskInfo);
        return taskInfo;
    }

    /**
     * 获取微信客服组件授权任务信息
     */
    public CustomerServiceComponentAuthTaskInfoDto getCustomerServiceComponentAuthTaskInfo(String state) {
        CustomerServiceComponentAuthTaskInfoDto taskInfo = getTemporaryCustomerServiceComponentAuthTask(state);
        defaultObjectRedisTemplate.delete(RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_SERVICE_COMPONENT_AUTH_TASK_INFO_KEY + state);
        return taskInfo;
    }

    /**
     * 获取企业微信微信客服信息
     */
    public List<KfAccountList> getKfAccountList(String corpId, String state, String accessToken, List<String> openKfIds, Map<String, String> idConvertOpenKfIdMap) {
        List<KfAccountList> kfAccounts = new ArrayList<>();
        //获取客服列表
        List<KfAccountList> kfAccountList = getKfAccountList(accessToken);
        Set<String> developOpenKfIds = kfAccountList.stream().map(KfAccountList::getOpenKfid).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(developOpenKfIds)) {
            return kfAccounts;
        }
        //不需要转换客服id
        if (developOpenKfIds.containsAll(openKfIds)) {
            log.info("微信客服组件授权不需要转换客服id, state: {} corpid:{} openKfIds:[{}] ", state, corpId, JSON.toJSONString(openKfIds));
            kfAccounts = kfAccountList.stream()
                //过滤掉不在服务商授权的微信客服
                .filter(t -> openKfIds.contains(t.getOpenKfid())).collect(Collectors.toList());
            kfAccounts.forEach(t -> idConvertOpenKfIdMap.put(t.getOpenKfid(), t.getOpenKfid()));
        } else {
            log.info("微信客服组件授权需要转换客服id, state: {} corpid:{} openKfIds:[{}] ", state, corpId, JSON.toJSONString(openKfIds));
            try {
                //微信客服ID转换
                IdConvertOpenKfIdResponseBody idConvertOpenKfIdResponseBody = workWeixinApiClient.idConvertOpenKfId(accessToken, new IdConvertOpenKfIdRequestBody(new ArrayList<>(developOpenKfIds)));
                if (idConvertOpenKfIdResponseBody.getErrcode() == 0) {
                    List<IdConvertOpenKfIdResponseBody.Item> items = idConvertOpenKfIdResponseBody.getItems();
                    if (!CollectionUtils.isEmpty(items)) {
                        Map<String, String> map = CollectionUtil.toMap(items, IdConvertOpenKfIdResponseBody.Item::getNewOpenKfId, IdConvertOpenKfIdResponseBody.Item::getOpenKfId);
                        developOpenKfIds = openKfIds.stream()
                            //过滤掉不在服务商授权的微信客服
                            .filter(openKfId -> StringUtils.isNotBlank(map.get(openKfId)))
                            //通过转换后的id获取客服信息
                            .map(openKfId -> {
                                String developOpenKfId = map.get(openKfId);
                                idConvertOpenKfIdMap.put(developOpenKfId, openKfId);
                                return developOpenKfId;
                            })
                            .collect(Collectors.toSet());
                        //过滤掉不在服务商授权的微信客服
                        if (developOpenKfIds.size() != openKfIds.size()) {
                            return kfAccounts;
                        }
                        if (CollectionUtils.isNotEmpty(kfAccountList)) {
                            kfAccounts = kfAccountList.stream()
                                //过滤掉不在服务商授权的微信客服
                                .filter(t -> StringUtils.isNotBlank(idConvertOpenKfIdMap.get(t.getOpenKfid()))).collect(Collectors.toList());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("代开发应用查询微信客服信息失败", e);
                return kfAccounts;
            }
        }
        return kfAccounts;
    }

    /**
     * 获取企业微信微信客服链接
     */
    public String getKfAccountUrl(String openKfId, String accessToken) {
        RobotCustomerLinkRequestBody robotCustomerLinkRequestBody = new RobotCustomerLinkRequestBody();
        robotCustomerLinkRequestBody.setOpenKfid(openKfId);
        robotCustomerLinkRequestBody.setScene(ENTERPRISE_WECHAT_CUSTOMER_SERVICE_ROBOT_SCENE);
        try {
            RobotCustomerLinkResponseBody robotCustomerLinkResponseBody = enterpriseWechatThirdPartyApiClient.cgiBinKfAddContactWay(accessToken, robotCustomerLinkRequestBody);
            if (robotCustomerLinkResponseBody.getErrcode() == 0) {
                return robotCustomerLinkResponseBody.getUrl();
            } else {
                log.error("获取微信客服链接失败, accessToken：{}, 请求体：{}", accessToken, JSON.toJSONString(robotCustomerLinkRequestBody));
            }
        } catch (Exception e) {
            log.error("获取微信客服链接失败, accessToken：{}, 请求体：{}", accessToken, JSON.toJSONString(robotCustomerLinkRequestBody), e);
        }
        return null;
    }

//    /**
//     * 授权微信客服机器人
//     */
//    public void authCustomerServiceRobot(List<String> openKfIds, String corpId, String state) {
//        TenantContextHolder.clearContext();
//        CustomerServiceComponentAuthTaskInfoDto taskInfo = this.getTemporaryCustomerServiceComponentAuthTask(state);
//        if (taskInfo == null) {
//            log.error("微信客服组件授权state参数无效, state:{}", state);
//            return;
//        }
//        if (taskInfo.getSuccess()) {
//            log.error("微信客服组件授权任务已完成请勿重复授权, state: {}", state);
//            return;
//        }
//        String agentId = taskInfo.getAgentId();
//        Long advertiserAccountGroupId = taskInfo.getAdvertiserAccountGroupId();
//        List<EnterpriseWechat> enterpriseWechatList = enterpriseWechatService.getPmpEnterpriseWechatList(agentId, advertiserAccountGroupId);
//        EnterpriseWechat enterpriseWechat = enterpriseWechatList.stream().filter(t -> StringUtils.equals(t.getCorpid(), corpId)).findFirst().orElse(null);
//        if (enterpriseWechat == null) {
//            taskInfo.setErrorMessage("微信客服主体与企业微信代开发授权不一致");
//            this.temporaryStoreCustomerServiceComponentAuthTask(taskInfo);
//            return;
//        }
//        Map<String, String> idConvertOpenKfIdMap = new HashMap<>();
//        List<KfAccountList> kfAccounts = getKfAccountList(corpId, state, enterpriseWechat.getAccessToken(), openKfIds, idConvertOpenKfIdMap);
//        if (kfAccounts.size() != openKfIds.size()) {
//            taskInfo.setErrorMessage("代开发应用无法查询到授权的微信客服信息");
//            this.temporaryStoreCustomerServiceComponentAuthTask(taskInfo);
//            return;
//        }
//        openKfIds = CollectionUtil.mapToList(kfAccounts, KfAccountList::getOpenKfid);
//        String customerServiceComponentAccessToken = enterpriseWechatThirdPartyApplicationAuthService.getCustomerServiceComponentAccessToken(corpId);
//        //已授权过的微信客服
//        List<EnterpriseWechatRobotCustomer> authorizedRobotCustomers = this.list(
//            Wrappers.lambdaQuery(EnterpriseWechatRobotCustomer.class)
//                .select(
//                    EnterpriseWechatRobotCustomer::getId,
//                    EnterpriseWechatRobotCustomer::getAgentId,
//                    EnterpriseWechatRobotCustomer::getAdvertiserAccountGroupId,
//                    EnterpriseWechatRobotCustomer::getOpenKfId,
//                    EnterpriseWechatRobotCustomer::getNewOpenKfUrl
//                )
//                .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId)
//                .in(EnterpriseWechatRobotCustomer::getOpenKfId, openKfIds)
//        );
//        if (CollectionUtils.isNotEmpty(authorizedRobotCustomers)) {
//            List<String> authorizedRobotCustomerNames = authorizedRobotCustomers.stream().filter(
//                t -> !StringUtils.equals(t.getAgentId(), agentId) || !Objects.equals(t.getAdvertiserAccountGroupId(), advertiserAccountGroupId)
//            ).map(
//                t -> kfAccounts.stream().filter(item -> StringUtils.equals(item.getOpenKfid(), t.getOpenKfId())).map(KfAccountList::getName).findFirst().orElse("")
//            ).collect(Collectors.toList());
//            //已授权过的微信客服不在本次授权的项目下
//            if (CollectionUtils.isNotEmpty(authorizedRobotCustomerNames)) {
//                taskInfo.setErrorMessage("微信客服" + String.join("、", authorizedRobotCustomerNames) + "已在其他项目授权使用");
//                this.temporaryStoreCustomerServiceComponentAuthTask(taskInfo);
//                return;
//            }
//            //更新公库已存在的微信客服的更新时间、微信客服机器人类型、微信客服链接
//            for (EnterpriseWechatRobotCustomer authorizedRobotCustomer : authorizedRobotCustomers) {
//                authorizedRobotCustomer.setType(EnterpriseWechatRobotCustomerType.WECHAT_CUSTOMER_SERVICE_COMPONENT_AUTH);
//                authorizedRobotCustomer.setUpdatedAt(Instant.now());
//                authorizedRobotCustomer.setNewOpenKfId(idConvertOpenKfIdMap.get(authorizedRobotCustomer.getOpenKfId()));
//                if (StringUtils.isNotBlank(authorizedRobotCustomer.getNewOpenKfUrl())) {
//                    continue;
//                }
//                String openKfUrl = getKfAccountUrl(authorizedRobotCustomer.getNewOpenKfId(), customerServiceComponentAccessToken);
//                if (StringUtils.isBlank(openKfUrl)) {
//                    taskInfo.setErrorMessage("获取微信客服链接失败");
//                    this.temporaryStoreCustomerServiceComponentAuthTask(taskInfo);
//                    return;
//                }
//                authorizedRobotCustomer.setNewOpenKfUrl(openKfUrl);
//            }
//            this.updateBatchById(authorizedRobotCustomers);
//            List<String> authorizedOpenKfIds = CollectionUtil.mapToList(authorizedRobotCustomers, EnterpriseWechatRobotCustomer::getOpenKfId);
//            kfAccounts.removeIf(t -> authorizedOpenKfIds.contains(t.getOpenKfid()));
//        }
//        //本次授权的微信客服
//        List<EnterpriseWechatRobotCustomer> authRobotCustomers = kfAccounts.stream().map(t -> {
//            Boolean managePrivilege = t.getManagePrivilege();
//            EnterpriseWechatRobotCustomer robotCustomer = new EnterpriseWechatRobotCustomer();
//            robotCustomer.setName(t.getName());
//            robotCustomer.setCorpId(corpId);
//            robotCustomer.setOpenKfId(t.getOpenKfid());
//            robotCustomer.setNewOpenKfId(idConvertOpenKfIdMap.get(t.getOpenKfid()));
//            robotCustomer.setFilePath(t.getAvatar());
//            robotCustomer.setAgentId(agentId);
//            robotCustomer.setAdvertiserAccountGroupId(advertiserAccountGroupId);
//            robotCustomer.setManagePrivilege(managePrivilege);
//            robotCustomer.setType(EnterpriseWechatRobotCustomerType.WECHAT_CUSTOMER_SERVICE_COMPONENT_AUTH)
//                .setUsageStatus(Objects.nonNull(managePrivilege) && managePrivilege ? BaseStatusEnum.ENABLE : BaseStatusEnum.DISABLE);
//            return robotCustomer;
//        }).collect(Collectors.toList());
//        for (EnterpriseWechatRobotCustomer authRobotCustomer : authRobotCustomers) {
//            String openKfUrl = getKfAccountUrl(authRobotCustomer.getNewOpenKfId(), customerServiceComponentAccessToken);
//            if (StringUtils.isBlank(openKfUrl)) {
//                taskInfo.setErrorMessage("获取微信客服链接失败");
//                this.temporaryStoreCustomerServiceComponentAuthTask(taskInfo);
//                return;
//            }
//            authRobotCustomer.setNewOpenKfUrl(openKfUrl);
//        }
//        //公库保存本次授权的微信客服
//        if (CollectionUtils.isNotEmpty(authRobotCustomers)) {
//            this.saveBatch(authRobotCustomers);
//        }
//        TenantContextHolder.set(agentId);
//        //更新私库已授权过的微信客服的更新时间
//        if (CollectionUtils.isNotEmpty(authorizedRobotCustomers)) {
//            Map<String, EnterpriseWechatRobotCustomer> openKfIdToRobot = CollectionUtil.toMap(authorizedRobotCustomers, EnterpriseWechatRobotCustomer::getOpenKfId);
//            authorizedRobotCustomers = this.list(
//                Wrappers.lambdaQuery(EnterpriseWechatRobotCustomer.class)
//                    .select(EnterpriseWechatRobotCustomer::getId,
//                        EnterpriseWechatRobotCustomer::getOpenKfId
//                    )
//                    .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId)
//                    .in(EnterpriseWechatRobotCustomer::getOpenKfId, openKfIds)
//            );
//            authorizedRobotCustomers = authorizedRobotCustomers.stream()
//                .filter(t -> openKfIdToRobot.containsKey(t.getOpenKfId())).peek(t -> {
//                    EnterpriseWechatRobotCustomer authorizedRobotCustomer = openKfIdToRobot.get(t.getOpenKfId());
//                    t.setType(EnterpriseWechatRobotCustomerType.WECHAT_CUSTOMER_SERVICE_COMPONENT_AUTH);
//                    t.setUpdatedAt(authorizedRobotCustomer.getUpdatedAt());
//                    t.setNewOpenKfId(authorizedRobotCustomer.getNewOpenKfId());
//                    t.setNewOpenKfUrl(authorizedRobotCustomer.getNewOpenKfUrl());
//                }).collect(Collectors.toList());
//            this.updateBatchById(authorizedRobotCustomers);
//        }
//        //私库保存本次授权的微信客服
//        if (CollectionUtils.isNotEmpty(authRobotCustomers)) {
//            authRobotCustomers.forEach(t -> t.setId(null));
//            this.saveBatch(authRobotCustomers);
//            List<EnterpriseWechatRobotCustomerPmpRel> enterpriseWechatRobotCustomerPmpRels = authRobotCustomers.stream().map(t -> {
//                EnterpriseWechatRobotCustomerPmpRel enterpriseWechatRobotCustomerPmpRel = new EnterpriseWechatRobotCustomerPmpRel();
//                enterpriseWechatRobotCustomerPmpRel.setWechatRobotCustomerId(t.getId());
//                enterpriseWechatRobotCustomerPmpRel.setAgentId(t.getAgentId());
//                enterpriseWechatRobotCustomerPmpRel.setAdvertiserAccountGroupId(advertiserAccountGroupId);
//                return enterpriseWechatRobotCustomerPmpRel;
//            }).collect(Collectors.toList());
//            enterpriseWechatRobotCustomerPmpRelService.saveBatch(enterpriseWechatRobotCustomerPmpRels);
//            TenantContextHolder.clearContext();
//            enterpriseWechatRobotCustomerPmpRels.forEach(t -> t.setId(null));
//            enterpriseWechatRobotCustomerPmpRelService.saveBatch(enterpriseWechatRobotCustomerPmpRels);
//            TenantContextHolder.set(agentId);
//            //初始化到未分组中
//            List<EnterpriseWechatRobotCustomerGroupRel> groupRels = authRobotCustomers.stream().map(e -> {
//                EnterpriseWechatRobotCustomerGroupRel rel = new EnterpriseWechatRobotCustomerGroupRel();
//                rel.setEnterpriseWechatRobotCustomerGroupId(-1L)
//                    .setOrderNum(0)
//                    .setEnterpriseWechatRobotCustomerId(e.getId())
//                    .setAdvertiserAccountGroupId(advertiserAccountGroupId);
//                return rel;
//            }).collect(Collectors.toList());
//            enterpriseWechatRobotCustomerGroupRelService.saveBatch(groupRels);
//        }
//        taskInfo.setSuccess(true);
//        taskInfo.setErrorMessage("微信客服组件授权成功");
//        this.temporaryStoreCustomerServiceComponentAuthTask(taskInfo);
//        //清理缓存
//        for (EnterpriseWechatRobotCustomer authRobotCustomer : authRobotCustomers) {
//            deleteEnterpriseWechatRobotCustomerCache(authRobotCustomer);
//        }
//
//    }

    /**
     * 取消授权微信客服机器人
     */
    public void cancelAuthCustomerServiceRobot(String corpId) {
        if (StringUtils.isBlank(corpId)) {
            return;
        }
        //清除微信客服机器人数据
        TenantContextHolder.clearContext();
        List<EnterpriseWechatRobotCustomer> robotCustomers = this.list(
            Wrappers.lambdaQuery(EnterpriseWechatRobotCustomer.class)
                .select(
                    EnterpriseWechatRobotCustomer::getId,
                    EnterpriseWechatRobotCustomer::getAgentId,
                    EnterpriseWechatRobotCustomer::getOpenKfId
                )
                .eq(EnterpriseWechatRobotCustomer::getType, EnterpriseWechatRobotCustomerType.WECHAT_CUSTOMER_SERVICE_COMPONENT_AUTH)
                .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId)
        );
        deleteCustomerServiceRobotBatch(robotCustomers, corpId);
    }

    /**
     * 批量删除微信客服机器人
     */
    private void deleteCustomerServiceRobotBatch(List<EnterpriseWechatRobotCustomer> robotCustomers, String corpId) {
        if (CollectionUtils.isEmpty(robotCustomers)) {
            return;
        }
        //清除公库中的微信客服机器人数据
        TenantContextHolder.clearContext();
        this.removeByIds(CollectionUtil.mapToList(robotCustomers, EnterpriseWechatRobotCustomer::getId));
        //清除私库中的微信客服机器人数据
        Map<String, List<String>> agentId2OpenKfIds = robotCustomers.stream().collect(
            Collectors.groupingBy(EnterpriseWechatRobotCustomer::getAgentId, Collectors.mapping(EnterpriseWechatRobotCustomer::getOpenKfId, Collectors.toList()))
        );
        for (String agentId : agentId2OpenKfIds.keySet()) {
            TenantContextHolder.set(agentId);
            List<String> openKfIds = agentId2OpenKfIds.get(agentId);
            robotCustomers = this.list(
                Wrappers.lambdaQuery(EnterpriseWechatRobotCustomer.class)
                    .select(EnterpriseWechatRobotCustomer::getId)
                    .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId)
                    .in(EnterpriseWechatRobotCustomer::getOpenKfId, openKfIds)
            );
            if (CollectionUtils.isEmpty(robotCustomers)) {
                continue;
            }
            List<Long> robotCustomerIds = CollectionUtil.mapToList(robotCustomers, EnterpriseWechatRobotCustomer::getId);
            this.removeByIds(robotCustomerIds);
            enterpriseWechatRobotCustomerPmpRelService.remove(
                Wrappers.lambdaQuery(EnterpriseWechatRobotCustomerPmpRel.class)
                    .in(EnterpriseWechatRobotCustomerPmpRel::getWechatRobotCustomerId, robotCustomerIds)
            );
            enterpriseWechatRobotCustomerPmpRelService.removePublic(agentId, robotCustomerIds);
            List<EnterpriseWechatCustomerTemplateRel> robotCustomerTemplateRels = enterpriseWechatCustomerTemplateRelService.list(
                Wrappers.lambdaQuery(EnterpriseWechatCustomerTemplateRel.class)
                    .in(EnterpriseWechatCustomerTemplateRel::getEnterpriseWechatCustomerId, robotCustomerIds)
            );
            if (CollectionUtils.isNotEmpty(robotCustomerTemplateRels)) {
                enterpriseWechatCustomerTemplateRelService.removeByIds(CollectionUtil.mapToList(robotCustomerTemplateRels, EnterpriseWechatCustomerTemplateRel::getId));
                List<Long> robotCustomerTemplateIds = CollectionUtil.mapToList(robotCustomerTemplateRels, EnterpriseWechatCustomerTemplateRel::getEnterpriseWechatCustomerTemplateId);
                if (CollectionUtils.isNotEmpty(robotCustomerTemplateIds)) {
                    enterpriseWechatCostomerMsgTemplateService.remove(
                        Wrappers.lambdaQuery(EnterpriseWechatCustomerMsgTemplate.class)
                            .select(EnterpriseWechatCustomerMsgTemplate::getId)
                            .in(EnterpriseWechatCustomerMsgTemplate::getId, robotCustomerTemplateIds)
                    );
                    enterpriseWechatCustomerMsgChildrenTemplateService.remove(
                        Wrappers.lambdaQuery(EnterpriseWechatCustomerMsgChildrenTemplate.class)
                            .in(EnterpriseWechatCustomerMsgChildrenTemplate::getMsgTemplateId, robotCustomerTemplateIds)
                    );
                }
            }
            List<EnterpriseWechatCustomerTemplateAutoAnswerRuleRel> robotCustomerTemplateAutoAnswerRuleRels = enterpriseWechatCustomerTemplateAutoAnswerRuleRelService.list(
                Wrappers.lambdaQuery(EnterpriseWechatCustomerTemplateAutoAnswerRuleRel.class)
                    .select(
                        EnterpriseWechatCustomerTemplateAutoAnswerRuleRel::getId,
                        EnterpriseWechatCustomerTemplateAutoAnswerRuleRel::getEnterpriseWechatCustomerAutoAnswerRuleId
                    )
                    .in(EnterpriseWechatCustomerTemplateAutoAnswerRuleRel::getEnterpriseWechatCustomerId, robotCustomerIds)
            );
            if (CollectionUtils.isNotEmpty(robotCustomerTemplateAutoAnswerRuleRels)) {
                enterpriseWechatCustomerTemplateAutoAnswerRuleRelService.removeByIds(
                    CollectionUtil.mapToList(robotCustomerTemplateAutoAnswerRuleRels, EnterpriseWechatCustomerTemplateAutoAnswerRuleRel::getId)
                );
                List<Long> robotCustomerTemplateAutoAnswerRuleIds = CollectionUtil.mapToList(
                    robotCustomerTemplateAutoAnswerRuleRels, EnterpriseWechatCustomerTemplateAutoAnswerRuleRel::getEnterpriseWechatCustomerAutoAnswerRuleId
                );
                if (CollectionUtils.isNotEmpty(robotCustomerTemplateAutoAnswerRuleIds)) {
                    List<EnterpriseWechatCustomerAutoAnswerRule> robotCustomerAutoAnswerRules = enterpriseWechatCustomerAutoAnswerRuleService.list(
                        Wrappers.lambdaQuery(EnterpriseWechatCustomerAutoAnswerRule.class)
                            .select(EnterpriseWechatCustomerAutoAnswerRule::getId, EnterpriseWechatCustomerAutoAnswerRule::getMsgTemplateIds)
                            .ne(EnterpriseWechatCustomerAutoAnswerRule::getType, StrategyType.KEYWORD)
                            .in(EnterpriseWechatCustomerAutoAnswerRule::getId, robotCustomerTemplateAutoAnswerRuleIds)
                    );
                    enterpriseWechatCustomerAutoAnswerRuleService.removeByIds(CollectionUtil.mapToList(robotCustomerAutoAnswerRules, EnterpriseWechatCustomerAutoAnswerRule::getId));
                    List<Long> robotCustomerTemplateIds = robotCustomerAutoAnswerRules.stream().map(EnterpriseWechatCustomerAutoAnswerRule::getMsgTemplateIds)
                        .filter(ObjectUtils::isNotEmpty).flatMap(Arrays::stream).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(robotCustomerTemplateIds)) {
                        enterpriseWechatCostomerMsgTemplateService.remove(
                            Wrappers.lambdaQuery(EnterpriseWechatCustomerMsgTemplate.class)
                                .select(EnterpriseWechatCustomerMsgTemplate::getId)
                                .in(EnterpriseWechatCustomerMsgTemplate::getId, robotCustomerTemplateIds)
                        );
                        enterpriseWechatCustomerMsgChildrenTemplateService.remove(
                            Wrappers.lambdaQuery(EnterpriseWechatCustomerMsgChildrenTemplate.class)
                                .in(EnterpriseWechatCustomerMsgChildrenTemplate::getMsgTemplateId, robotCustomerTemplateIds)
                        );
                    }
                }
            }
            TenantContextHolder.clearContext();
        }
    }

    /**
     * 微信客服机器人代开发授权变更
     */
    public void customerServiceRobotAgentDevelopAuthChange(String corpId) {
        //更新公库中的微信客服机器人代开发管理权限
        TenantContextHolder.clearContext();
        if (StringUtils.isBlank(corpId)) {
            return;
        }
        String accessToken = enterpriseWechatService.getAccessTokenByCorpId(corpId);
        if (StringUtils.isBlank(accessToken)) {
            return;
        }
        /**
         * 获取所有的客服列表
         */
        List<KfAccountList> kfAccountList = getKfAccountList(accessToken);
        if (CollectionUtils.isEmpty(kfAccountList)) return;

        Map<String, Boolean> openKfId2ManagePrivilege = CollectionUtil.toMap(kfAccountList, KfAccountList::getOpenKfid, KfAccountList::getManagePrivilege);
        List<String> openKfIds = CollectionUtil.mapToList(kfAccountList, KfAccountList::getOpenKfid);
        List<EnterpriseWechatRobotCustomer> robotCustomers = this.list(
            Wrappers.lambdaQuery(EnterpriseWechatRobotCustomer.class)
                .select(
                    EnterpriseWechatRobotCustomer::getId,
                    EnterpriseWechatRobotCustomer::getAgentId,
                    EnterpriseWechatRobotCustomer::getOpenKfId,
                    EnterpriseWechatRobotCustomer::getManagePrivilege
                )
                .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId)
                .in(EnterpriseWechatRobotCustomer::getOpenKfId, openKfIds)
        );
        robotCustomers = robotCustomers.stream().filter(
            t -> !Objects.equals(t.getManagePrivilege(), openKfId2ManagePrivilege.get(t.getOpenKfId()))
        ).map(t -> t.setManagePrivilege(openKfId2ManagePrivilege.get(t.getOpenKfId()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(robotCustomers)) {
            return;
        }
        Map<String, List<String>> agentId2OpenKfIds = robotCustomers.stream().collect(
            Collectors.groupingBy(EnterpriseWechatRobotCustomer::getAgentId, Collectors.mapping(EnterpriseWechatRobotCustomer::getOpenKfId, Collectors.toList()))
        );
        for (EnterpriseWechatRobotCustomer robotCustomer : robotCustomers) {
            robotCustomer.setAgentId(null);
            robotCustomer.setOpenKfId(null);
        }
        this.updateBatchById(robotCustomers);
        //更新私库中的微信客服机器人代开发管理权限
        for (String agentId : agentId2OpenKfIds.keySet()) {
            TenantContextHolder.set(agentId);
            openKfIds = agentId2OpenKfIds.get(agentId);
            List<EnterpriseWechatRobotCustomer> privateRobotCustomers = this.list(
                Wrappers.lambdaQuery(EnterpriseWechatRobotCustomer.class)
                    .select(
                        EnterpriseWechatRobotCustomer::getId,
                        EnterpriseWechatRobotCustomer::getOpenKfId,
                        EnterpriseWechatRobotCustomer::getUsageStatus
                    )
                    .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId)
                    .in(EnterpriseWechatRobotCustomer::getOpenKfId, openKfIds)
            );
            if (CollectionUtils.isEmpty(privateRobotCustomers)) {
                continue;
            }
            List<Long> robotIds = Lists.newArrayList();
            privateRobotCustomers.forEach(t -> {
                BaseStatusEnum usageStatusOld = t.getUsageStatus();
                Boolean b = openKfId2ManagePrivilege.get(t.getOpenKfId());
                t.setManagePrivilege(b);
                //如果取消了代开发权限 则直接下线
                if (Objects.nonNull(b) && !b) {
                    t.setUsageStatus(BaseStatusEnum.DISABLE);
                    if (!BaseStatusEnum.DISABLE.equals(usageStatusOld)) {
                        robotIds.add(t.getId());
                    }
                }
                t.setOpenKfId(null);
            });
            this.updateBatchById(privateRobotCustomers);
            if (CollectionUtils.isNotEmpty(robotIds)) {
                OperationLogRobotUsageStatusDto robotUsageStatusDto = new OperationLogRobotUsageStatusDto();
                robotUsageStatusDto.setIds(robotIds)
                    .setOperUserName("授权变更")
                    .setUsageStatus(BaseStatusEnum.DISABLE);
                userOperationLogDetailActionSender.sendRobotUsageStatusLog(robotUsageStatusDto);
                //发送清除机器人分组缓存
                enterpriseRobotCustomerSender.sendCleanRobotGroupCache(robotIds);
            }
            TenantContextHolder.clearContext();
        }
    }

    public List<KfAccountList> getKfAccountList(String accessToken) {
        HashMap<String, Object> params = Maps.newHashMap();
        int offset = 0;
        params.put("offset", offset);
        params.put("limit", 100);
        RobotCustomerListResponseBody robotCustomerListResponseBody = workWeixinApiClient.kfAccountList(accessToken, params);
        List<KfAccountList> accountList = robotCustomerListResponseBody.getAccountList();
        if (CollectionUtils.isEmpty(accountList)) {
            return new ArrayList<>();
        }
        //按客服id去重，防止分页查询时数据重复
        Map<String, KfAccountList> kfAccountMap = CollectionUtil.toMap(accountList, KfAccountList::getOpenKfid, Function.identity());
        //查询下一页
        while (accountList.size() == 100) {
            offset += 100;
            params.put("offset", offset);
            robotCustomerListResponseBody = workWeixinApiClient.kfAccountList(accessToken, params);
            accountList = robotCustomerListResponseBody.getAccountList();
            if (CollectionUtils.isEmpty(accountList)) {
                break;
            }
            Map<String, KfAccountList> nextPageMap = CollectionUtil.toMap(accountList, KfAccountList::getOpenKfid, Function.identity());
            kfAccountMap.putAll(nextPageMap);
        }
        return new ArrayList<>(kfAccountMap.values());
    }


    /**
     * 查询微信客服机器人信息(这里查私库就行)
     *
     * @param corpId   企业微信corpId
     * @param openKfid 企业微信机器人客服id
     * @return 微信客服机器人信息
     */

    public EnterpriseWechatRobotCustomer getRobotByCorpIdAndOpenKfid(String corpId, String openKfid) {
        String newOpenKfId = defaultStringRedisTemplate.opsForValue().get(RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_ROBOT_OLD_TO_NEW_OPEN_KF_ID + corpId + ":" + openKfid);
        log.info("查询微信客服机器人信息,corpId={}, openKfid = {}, newOpenKfId = {}", corpId, openKfid, newOpenKfId);
        openKfid = StringUtils.isNotBlank(newOpenKfId) ? newOpenKfId : openKfid;
        String key = RedisConstant.ENTERPRISE_WECHAT_ROBOT_CUSTOMER_PRIVATE_PREFIX + corpId + ":" + openKfid;
        log.info("查询微信客服机器人信息,key={}", key);
        Object result = objectRedisTemplate.opsForValue().get(key);
        if (Objects.nonNull(result)) {
            log.info("查询微信客服机器人信息,key={},走缓存", key);
            EnterpriseWechatRobotCustomer enterpriseWechatRobotCustomer = (EnterpriseWechatRobotCustomer) result;
            //如果走的私库，私库中部分记录没有存agentId
            if (StringUtils.isBlank(enterpriseWechatRobotCustomer.getAgentId())) {
                enterpriseWechatRobotCustomer.setAgentId(TenantContextHolder.get());
            }
            return enterpriseWechatRobotCustomer;
        }
        EnterpriseWechatRobotCustomer enterpriseWechatRobotCustomer = getOne(new LambdaQueryWrapper<EnterpriseWechatRobotCustomer>()
            .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId).eq(EnterpriseWechatRobotCustomer::getOpenKfId, openKfid));
        log.info("缓存没查到微信客服机器人信息,key={}, openKfid = {}, 查询数据库的结果 enterpriseWechatRobotCustomer = {}", key, openKfid, JSONObject.toJSONString(enterpriseWechatRobotCustomer));
        if (Objects.nonNull(enterpriseWechatRobotCustomer)) {
            //如果走的私库，私库中部分记录没有存agentId
            if (StringUtils.isBlank(enterpriseWechatRobotCustomer.getAgentId())) {
                enterpriseWechatRobotCustomer.setAgentId(TenantContextHolder.get());
            }
            objectRedisTemplate.opsForValue().set(key, enterpriseWechatRobotCustomer, 2, TimeUnit.HOURS);
        }
        return enterpriseWechatRobotCustomer;
    }

    /**
     * 修改微信客服机器人使用状态
     */
    public void modifyWechatCustomerServiceRobotUsageStatus(Long wechatCustomerServiceRobotId, BaseStatusEnum wechatCustomerServiceRobotUsageStatus) {
        EnterpriseWechatRobotCustomer enterpriseWechatRobotCustomer = this.getById(wechatCustomerServiceRobotId);
        String openKfId = enterpriseWechatRobotCustomer.getOpenKfId();
        String corpId = enterpriseWechatRobotCustomer.getCorpId();
        if (!Objects.equals(enterpriseWechatRobotCustomer.getUsageStatus(), wechatCustomerServiceRobotUsageStatus)) {
            this.lambdaUpdate()
                .eq(EnterpriseWechatRobotCustomer::getId, wechatCustomerServiceRobotId)
                .set(EnterpriseWechatRobotCustomer::getUsageStatus, wechatCustomerServiceRobotUsageStatus)
                .set(EnterpriseWechatRobotCustomer::getUpdatedAt, Instant.now())
                .update();
            objectRedisTemplate.delete(RedisConstant.ENTERPRISE_WECHAT_ROBOT_CUSTOMER_PRIVATE_PREFIX + corpId + ":" + openKfId);
            enterpriseWechatRobotCustomerRedis.cleanWechatServiceRobotCustomersRedis(new HashSet<>(enterpriseWechatRobotCustomer.getWechatRobotCustomerGroupIds()));
            enterpriseWechatRobotCustomerRedis.cleanRobotCache(enterpriseWechatRobotCustomer.getId());
            String agentId = TenantContextHolder.get();
            TenantContextHolder.clearContext();
            this.lambdaUpdate()
                .eq(EnterpriseWechatRobotCustomer::getOpenKfId, openKfId)
                .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId)
                .set(EnterpriseWechatRobotCustomer::getUsageStatus, wechatCustomerServiceRobotUsageStatus)
                .set(EnterpriseWechatRobotCustomer::getUpdatedAt, Instant.now())
                .update();
            defaultObjectRedisTemplate.delete(RedisConstant.ENTERPRISE_WECHAT_ROBOT_CUSTOMER_PREFIX + corpId + ":" + openKfId);
            TenantContextHolder.set(agentId);
            robotCustomerAbnormalOfflineOperationLog(wechatCustomerServiceRobotId);
        }
    }

    /**
     * 恢复微信客服机器人状态
     */
    public void recoveryWechatCustomerServiceRobotStatus(Long wechatCustomerServiceRobotId) {
        modifyWechatCustomerServiceRobotStatus(wechatCustomerServiceRobotId, EnterpriseWechatRobotCustomerStatus.NORMAL);
    }

    /**
     * 修改微信客服机器人状态
     */
    public void modifyWechatCustomerServiceRobotStatus(Long wechatCustomerServiceRobotId, EnterpriseWechatRobotCustomerStatus enterpriseWechatRobotCustomerStatus) {
        EnterpriseWechatRobotCustomer enterpriseWechatRobotCustomer = this.getById(wechatCustomerServiceRobotId);
        String corpId = enterpriseWechatRobotCustomer.getCorpId();
        String openKfId = enterpriseWechatRobotCustomer.getOpenKfId();
        if (!Objects.equals(enterpriseWechatRobotCustomer.getStatus(), enterpriseWechatRobotCustomerStatus)) {
            this.lambdaUpdate()
                .eq(EnterpriseWechatRobotCustomer::getId, wechatCustomerServiceRobotId)
                .set(EnterpriseWechatRobotCustomer::getStatus, enterpriseWechatRobotCustomerStatus)
                .set(EnterpriseWechatRobotCustomer::getUpdatedAt, Instant.now())
                .update();
            String agentId = TenantContextHolder.get();
            TenantContextHolder.clearContext();
            this.lambdaUpdate()
                .eq(EnterpriseWechatRobotCustomer::getOpenKfId, openKfId)
                .eq(EnterpriseWechatRobotCustomer::getCorpId, corpId)
                .set(EnterpriseWechatRobotCustomer::getStatus, enterpriseWechatRobotCustomerStatus)
                .set(EnterpriseWechatRobotCustomer::getUpdatedAt, Instant.now())
                .update();
            TenantContextHolder.set(agentId);
            defaultObjectRedisTemplate.delete(RedisConstant.ENTERPRISE_WECHAT_ROBOT_CUSTOMER_PREFIX + corpId + ":" + openKfId);
            objectRedisTemplate.delete(RedisConstant.ENTERPRISE_WECHAT_ROBOT_CUSTOMER_PRIVATE_PREFIX + corpId + ":" + openKfId);
        }
        if (EnterpriseWechatRobotCustomerStatus.NORMAL.equals(enterpriseWechatRobotCustomerStatus)) {
            String firstAbnormalTimeKey = RedisConstant.WECHAT_CUSTOMER_SERVICE_ROBOT_FIRST_ABNORMAL_TIME + wechatCustomerServiceRobotId;
            String abnormalCountKey = RedisConstant.WECHAT_CUSTOMER_SERVICE_ROBOT_ABNORMAL_COUNT + wechatCustomerServiceRobotId;
            String continuousAbnormalCountKey = RedisConstant.WECHAT_CUSTOMER_SERVICE_ROBOT_CONTINUOUS_ABNORMAL_COUNT + wechatCustomerServiceRobotId;
            objectRedisTemplate.delete(firstAbnormalTimeKey);
            objectRedisTemplate.delete(abnormalCountKey);
            objectRedisTemplate.delete(continuousAbnormalCountKey);
        }
    }

    /**
     * 检查账户是否异常
     */
    public void checkAccountAbnormal(EnterpriseWechatRobotCustomerCheckAccountAbnormalDto enterpriseWechatRobotCustomerCheckAccountAbnormalDto) {
        Long wechatCustomerServiceRobotId = enterpriseWechatRobotCustomerCheckAccountAbnormalDto.getWechatCustomerServiceRobotId();
        String firstAbnormalTimeKey = RedisConstant.WECHAT_CUSTOMER_SERVICE_ROBOT_FIRST_ABNORMAL_TIME + wechatCustomerServiceRobotId;
        Instant firstAbnormalTime;
        if (Boolean.TRUE.equals(objectRedisTemplate.hasKey(firstAbnormalTimeKey))) {
            firstAbnormalTime = Instant.ofEpochSecond((Integer) objectRedisTemplate.opsForValue().get(firstAbnormalTimeKey));
        } else {
            firstAbnormalTime = Instant.now();
            objectRedisTemplate.opsForValue().set(firstAbnormalTimeKey, firstAbnormalTime.getEpochSecond(), 2, TimeUnit.MINUTES);
        }
        String abnormalCountKey = RedisConstant.WECHAT_CUSTOMER_SERVICE_ROBOT_ABNORMAL_COUNT + wechatCustomerServiceRobotId;
        long abnormalCount = objectRedisTemplate.opsForValue().increment(abnormalCountKey);
        objectRedisTemplate.expire(abnormalCountKey, 2, TimeUnit.MINUTES);
        String continuousAbnormalCountKey = RedisConstant.WECHAT_CUSTOMER_SERVICE_ROBOT_CONTINUOUS_ABNORMAL_COUNT + wechatCustomerServiceRobotId;
        long continuousAbnormalCount = objectRedisTemplate.opsForValue().increment(continuousAbnormalCountKey);
        objectRedisTemplate.expire(continuousAbnormalCountKey, 2, TimeUnit.HOURS);
        if ((abnormalCount > 10 && Instant.now().minusSeconds(60).isAfter(firstAbnormalTime)) || continuousAbnormalCount >= 30) {
            enterpriseRobotCustomerSender.recoveryCustomerServiceRobot(wechatCustomerServiceRobotId, 1, true, false, EnterpriseWechatRobotCustomerAbnormalNoticeType.MSG_SEND_MANY_FAIL, null);
        }
    }

    /**
     * 账户异常
     */
    public void accountAbnormal(Long wechatCustomerServiceRobotId) {
        modifyWechatCustomerServiceRobotStatus(wechatCustomerServiceRobotId, EnterpriseWechatRobotCustomerStatus.ACCOUNT_ABNORMALITY);
        if (BaseStatusEnum.ENABLE.equals(getWechatCustomerServiceRobotAbnormalOfflineStatus(wechatCustomerServiceRobotId))) {
            modifyWechatCustomerServiceRobotUsageStatus(wechatCustomerServiceRobotId, BaseStatusEnum.DISABLE);
        }
    }

    /**
     * 微信客服机器人异常下线操作日志
     */
    private void robotCustomerAbnormalOfflineOperationLog(Long wechatCustomerServiceRobotId) {
        OperationLogRobotUsageStatusDto robotUsageStatusDto = new OperationLogRobotUsageStatusDto();
        robotUsageStatusDto.setIds(Lists.newArrayList(wechatCustomerServiceRobotId))
            .setOperUserName("异常监测")
            .setUsageStatus(BaseStatusEnum.DISABLE);
        userOperationLogDetailActionSender.sendRobotUsageStatusLog(robotUsageStatusDto);
    }

    /**
     * 获取是否开启异常时下线微信客服机器人操作状态
     */
    private BaseStatusEnum getWechatCustomerServiceRobotAbnormalOfflineStatus(Long wechatCustomerServiceRobotId) {
        EnterpriseWechatRobotCustomer enterpriseWechatRobotCustomer = this.getById(wechatCustomerServiceRobotId);
        return advertiserAccountGroupService.getWechatCustomerServiceRobotAbnormalOfflineStatus(enterpriseWechatRobotCustomer.getAdvertiserAccountGroupId());
    }

    /**
     * 尝试恢复微信客服机器人使用状态
     */
    public void tryRecoveryWechatCustomerServiceRobotStatus(Long wechatCustomerServiceRobotId) {
        modifyWechatCustomerServiceRobotStatus(wechatCustomerServiceRobotId, EnterpriseWechatRobotCustomerStatus.NORMAL);
    }

    /**
     * 根据机器人分组ID获取跳转微信客服链接
     *
     * @return
     */
    public String getGroupJumpToWechatLinkUrl(RobotCustomerGroupCodeDto customerGroupCodeDto) {
        Long customerServiceRobotGroupId = customerGroupCodeDto.getCustomerServiceRobotGroupId();
        String agentId = customerGroupCodeDto.getAgentId();
        RobotGroupCodeUsePattern robotGroupCodeUsePattern = customerGroupCodeDto.getUsePattern();
        String pid = customerGroupCodeDto.getPid();
        //获取机器人分组中分组列表
        List<EnterpriseWechatRobotCustomerRedisDto> redisDtos = enterpriseWechatRobotCustomerRedis.getWechatServiceDataByGroupId(customerServiceRobotGroupId,
            () -> enterpriseWechatRobotCustomerGroupRelService.searchUseCustomerRobotList(customerServiceRobotGroupId));
        if (CollectionUtils.isEmpty(redisDtos)) {
            log.info("通过机器人分组ID获取机器人列表无数据,agentId:{},robotGroupId:{}", agentId, customerServiceRobotGroupId);
            return null;
        }
        //基于排序进行倒序
        redisDtos.stream().sorted(Comparator.comparing(EnterpriseWechatRobotCustomerRedisDto::getOrderNum).reversed());
        //使用方式  以下的方式都是基于分组维度，只有同一访客时才会基于客服ID+落地页ID+clickId去获取同同一客服（需求要求）
        //重复访客校验
        String clickId = getClickId(customerGroupCodeDto.getUrl());
        EnterpriseWechatRobotCustomerRedisDto takeTurnsRobot = getSameVisitorRobot(clickId, customerGroupCodeDto, redisDtos);
        if (Objects.isNull(takeTurnsRobot)) {
            //非重复访客
            switch (robotGroupCodeUsePattern) {
                case TAKE_TURNS:
                    //轮流方式 还是记录客服分组ID展示客服数
                    takeTurnsRobot = getTakeTurnsRobot(customerServiceRobotGroupId, redisDtos);
                    break;
                case SEQUENCE:
                    //永远取第一个
                    takeTurnsRobot = redisDtos.get(0);
                    break;
            }
            //记录访客信息
            enterpriseWechatRobotCustomerRedis.saveSameVisitorRobot(customerServiceRobotGroupId, customerGroupCodeDto.getLandingPageId(),
                clickId, takeTurnsRobot);
        }
        //获取机器人链接
        String url = Objects.nonNull(takeTurnsRobot) ? getJumpToWechatLinkUrl(takeTurnsRobot.getId(), pid, agentId) : null;
        return url;
    }

    /**
     * 同一访客获取机器人
     *
     * @param customerGroupCodeDto
     * @param redisDtos
     * @return
     */
    private EnterpriseWechatRobotCustomerRedisDto getSameVisitorRobot(String clickId, RobotCustomerGroupCodeDto customerGroupCodeDto, List<EnterpriseWechatRobotCustomerRedisDto> redisDtos) {
        Long landingPageId = customerGroupCodeDto.getLandingPageId();
        String pid = customerGroupCodeDto.getPid();
        Long customerServiceRobotGroupId = customerGroupCodeDto.getCustomerServiceRobotGroupId();
        if (StringUtils.isBlank(clickId) || Objects.isNull(landingPageId)) {
            log.info("同一访客获取机器人链接校验,clickId或者落地页ID为空,不查询,landingPageId:{}, groupId:{},pid:{}", landingPageId,
                customerServiceRobotGroupId, pid);
            return null;
        }
        //根据客服分组ID + 落地页ID + clickId查询
        EnterpriseWechatRobotCustomerRedisDto sameVisitorRobot = enterpriseWechatRobotCustomerRedis.getSameVisitorRobot(customerServiceRobotGroupId, landingPageId, clickId, redisDtos);
        return sameVisitorRobot;
    }

    /**
     * 获取clickId
     *
     * @param url
     * @return
     */
    private String getClickId(String url) {
        String clickId = UrlUtils.getClickIdByPlatform(url, null);
        if (StringUtils.isBlank(clickId)) {
            log.info("同一访客获取机器人链接校验获取clickId为空,url:{}", url);
        }
        return clickId;
    }

    /**
     * 轮流获取客服
     *
     * @param customerServiceRobotGroupId
     * @param redisDtos
     * @return
     */
    private EnterpriseWechatRobotCustomerRedisDto getTakeTurnsRobot(Long customerServiceRobotGroupId, List<EnterpriseWechatRobotCustomerRedisDto> redisDtos) {
        //机器人分组展示数
        Long groupShowNum = enterpriseWechatRobotCustomerRedis.getGroupShowNum(customerServiceRobotGroupId);
        groupShowNum = Objects.isNull(groupShowNum) ? 0L : groupShowNum - 1;
        //展示数除以客服数取余
        int index = Convert.toInt(groupShowNum % redisDtos.size(), 0);
        EnterpriseWechatRobotCustomerRedisDto enterpriseWechatRobotCustomerRedisDto = redisDtos.get(index);
        return enterpriseWechatRobotCustomerRedisDto;
    }

    /**
     * 统计微信客服机器人数据
     */
    public void statisticWechatCustomerServiceRobotData(Long wechatCustomerServiceRobotId,
                                                        EnterpriseWechatRobotCustomerDataStatisticsField enterpriseWechatRobotCustomerDataStatisticsField,
                                                        String externalUserid) {
        String key = RedisConstant.WECHAT_CUSTOMER_SERVICE_ROBOT_DATA_STATISTICS + wechatCustomerServiceRobotId + ":" + LocalDate.now();
        if (enterpriseWechatRobotCustomerDataStatisticsField.isDeduplicate()) {
            String duplicateSetKey = enterpriseWechatRobotCustomerDataStatisticsField.getDuplicateSetKey() + wechatCustomerServiceRobotId + ":" + LocalDate.now();
            boolean flag = objectRedisTemplate.opsForSet().add(duplicateSetKey, externalUserid) <= 0;
            objectRedisTemplate.expire(duplicateSetKey, 2, TimeUnit.DAYS);
            if (flag) {
                return;
            }
        }
        objectRedisTemplate.opsForHash().increment(key, enterpriseWechatRobotCustomerDataStatisticsField.getField(), 1L);
        objectRedisTemplate.expire(key, 2, TimeUnit.DAYS);
    }

    /**
     * 客服机器人上下线
     *
     * @param enterpriseWechatRobotCustomerDto
     */
    public void online(EnterpriseWechatRobotCustomerDto enterpriseWechatRobotCustomerDto) {
        EnterpriseWechatRobotCustomer wechatRobotCustomer = getById(enterpriseWechatRobotCustomerDto.getId());
        if (Objects.isNull(wechatRobotCustomer)) {
            throw new RestException("机器人不存在");
        }
        Boolean managePrivilege = wechatRobotCustomer.getManagePrivilege();
        BaseStatusEnum usageStatus = enterpriseWechatRobotCustomerDto.getUsageStatus();
        if ((Objects.isNull(managePrivilege) || !managePrivilege) && BaseStatusEnum.ENABLE.equals(usageStatus)) {
            throw new RestException("请完善授权再进行上线开启操作");
        }
        AdvertiserAccountGroup advertiserAccountGroup = advertiserAccountGroupService.getOne(Wrappers.lambdaQuery(AdvertiserAccountGroup.class)
            .eq(AdvertiserAccountGroup::getId, wechatRobotCustomer.getAdvertiserAccountGroupId())
            .last("limit 1"));
        //异常下线状态开启 且客服是异常的情况下 不可上线
        Optional.of(advertiserAccountGroup).ifPresent(e -> {
            BaseStatusEnum wechatCustomerServiceRobotAbnormalOfflineStatus = e.getWechatCustomerServiceRobotAbnormalOfflineStatus();
            //开启时并且要操作上线 判断客服当前状态为异常时 不允许上线
            if (BaseStatusEnum.ENABLE.equals(wechatCustomerServiceRobotAbnormalOfflineStatus)
                && BaseStatusEnum.ENABLE.equals(usageStatus) && EnterpriseWechatRobotCustomerStatus.ACCOUNT_ABNORMALITY.equals(wechatRobotCustomer.getStatus())) {
                throw new RestException("客服状态异常，请先处理异常再进行上线开启操作");
            }
        });
        update(Wrappers.lambdaUpdate(EnterpriseWechatRobotCustomer.class)
            .set(EnterpriseWechatRobotCustomer::getUsageStatus, enterpriseWechatRobotCustomerDto.getUsageStatus())
            .eq(EnterpriseWechatRobotCustomer::getId, enterpriseWechatRobotCustomerDto.getId()));
        this.cleanWechatServiceRobotCustomersCache(enterpriseWechatRobotCustomerDto.getId());
    }

    /**
     * 清除微信客服机器人缓存
     */
    public void cleanWechatServiceRobotCustomersCache(Long wechatCustomerServiceRobotId) {
        List<EnterpriseWechatRobotCustomerGroupRel> rels = enterpriseWechatRobotCustomerGroupRelService.list(
            Wrappers.lambdaQuery(EnterpriseWechatRobotCustomerGroupRel.class)
                .eq(EnterpriseWechatRobotCustomerGroupRel::getEnterpriseWechatRobotCustomerId, wechatCustomerServiceRobotId)
                .ne(EnterpriseWechatRobotCustomerGroupRel::getEnterpriseWechatRobotCustomerGroupId, -1)
        );
        Set<Long> collect = rels.stream().map(EnterpriseWechatRobotCustomerGroupRel::getEnterpriseWechatRobotCustomerGroupId).collect(Collectors.toSet());
        //清除缓存
        enterpriseWechatRobotCustomerRedis.cleanWechatServiceRobotCustomersRedis(collect);
        enterpriseWechatRobotCustomerRedis.cleanGroupShowNum(collect);
        enterpriseWechatRobotCustomerRedis.cleanRobotCache(wechatCustomerServiceRobotId);
    }


    /**
     * 上传林志素材
     *
     * @param dto
     * @return
     */
    public Boolean uploadAddTempFile(EnterpriseWechatRobotCustomerSaveDto dto) {
        EnterpriseWechat enterpriseWechat = enterpriseWechatService.getOne(new LambdaQueryWrapper<EnterpriseWechat>().eq(EnterpriseWechat::getCorpid, dto.getCorpId()).eq(EnterpriseWechat::getEnterpriseWechatType, dto.getEnterpriseWechatType()).last("limit 1"));
        if (enterpriseWechat == null) {
            return false;
        }
        InputStream inputStream = FileUtil.getInputStream(dto.getFilePath());
        MultipartFile tempFile = FileUtil.getMultipartFile(inputStream, "media");
        TempMaterialResponseBody tempMaterialResponseBody = workWeixinApiClient.uploadTempFile(enterpriseWechat.getAccessToken(), EnterpriseTempMaterialType.IMAGE.getName(), tempFile);
        dto.setMediaId(tempMaterialResponseBody.getMediaId());
        if (CollectionUtils.isNotEmpty(dto.getAutoAnswerList())) {
            List<EnterpriseWechatCustomerMsgChildrenTemplate> autoAnswerList = dto.getAutoAnswerList().stream()
                .map(EnterpriseWechatCustomerMsgTemplate::getTemplates).flatMap(Collection::stream).collect(Collectors.toList());
            uploadTempFile(autoAnswerList, enterpriseWechat);
        }
        if (CollectionUtils.isNotEmpty(dto.getRetainMessageList())) {
            List<EnterpriseWechatCustomerMsgChildrenTemplate> retainMessageList = dto.getRetainMessageList().stream()
                .map(EnterpriseWechatCustomerMsgTemplate::getTemplates).flatMap(Collection::stream).collect(Collectors.toList());
            uploadTempFile(retainMessageList, enterpriseWechat);
        }
        if (CollectionUtils.isNotEmpty(dto.getAutoAnswerListCa())) {
            List<EnterpriseWechatCustomerMsgChildrenTemplate> autoAnswerList = dto.getAutoAnswerListCa().stream()
                .map(EnterpriseWechatCustomerMsgTemplate::getTemplates).flatMap(Collection::stream).collect(Collectors.toList());
            uploadTempFile(autoAnswerList, enterpriseWechat);
        }
        if (CollectionUtils.isNotEmpty(dto.getRetainMessageListCa())) {
            List<EnterpriseWechatCustomerMsgChildrenTemplate> retainMessageList = dto.getRetainMessageListCa().stream()
                .map(EnterpriseWechatCustomerMsgTemplate::getTemplates).flatMap(Collection::stream).collect(Collectors.toList());
            uploadTempFile(retainMessageList, enterpriseWechat);
        }
        return true;
    }

    /**
     * 销毁企业微信客服机器人
     */
    public void destroyCustomerServiceRobot(DestroyCustomerServiceRobotDto destroyCustomerServiceRobotDto) {
        String accessToken = enterpriseWechatService.getAccessTokenByCorpId(destroyCustomerServiceRobotDto.getCorpId());
        RobotCustomerUpdateRequestBody robotCustomerUpdateRequestBody = new RobotCustomerUpdateRequestBody();
        robotCustomerUpdateRequestBody.setOpenKfid(destroyCustomerServiceRobotDto.getOldOpenKfId());
        workWeixinApiClient.deleteCustomerRobot(accessToken, robotCustomerUpdateRequestBody);
        EnterpriseWechatRobotCustomerDestroyRecord enterpriseWechatRobotCustomerDestroyRecord = new EnterpriseWechatRobotCustomerDestroyRecord();
        enterpriseWechatRobotCustomerDestroyRecord.setWechatCustomerServiceRobotId(destroyCustomerServiceRobotDto.getWechatCustomerServiceRobotId());
        enterpriseWechatRobotCustomerDestroyRecord.setCorpId(destroyCustomerServiceRobotDto.getCorpId());
        enterpriseWechatRobotCustomerDestroyRecord.setOldOpenKfId(destroyCustomerServiceRobotDto.getOldOpenKfId());
        enterpriseWechatRobotCustomerDestroyRecord.setOldOpenKfUrl(destroyCustomerServiceRobotDto.getOldOpenKfUrl());
        enterpriseWechatRobotCustomerDestroyRecord.setNewOpenKfId(destroyCustomerServiceRobotDto.getNewOpenKfId());
        enterpriseWechatRobotCustomerDestroyRecord.setNewOpenKfUrl(destroyCustomerServiceRobotDto.getNewOpenKfUrl());
        destroyCustomerServiceRobotRecordMapper.insert(enterpriseWechatRobotCustomerDestroyRecord);
    }

    /**
     * 微信客服机器人异常通知
     */
    @SneakyThrows
    public void customerServiceRobotAbnormalNotification(CustomerServiceRobotAbnormalNotificationDto customerServiceRobotAbnormalNotificationDto) {
        EnterpriseWechatRobotCustomer robotCustomer = this.getById(customerServiceRobotAbnormalNotificationDto.getWechatCustomerServiceRobotId());
        EnterpriseWechatRobotCustomerAbnormalNoticeType abnormalNoticeType = customerServiceRobotAbnormalNotificationDto.getAbnormalNoticeType();
        LandingPagePmpParamsConfig pmpConfig = landingPagePmpParamsConfigService.getConfig(robotCustomer.getAdvertiserAccountGroupId(), LandingPagePmpConfigType.WECHAT_CUSTOMER_SERVICE_ROBOT);
        EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatCacheByCorpId(robotCustomer.getCorpId());
        String noticeLockKey = null;
        if (EnterpriseWechatRobotCustomerAbnormalNoticeType.MSG_SEND_RESTRICT.equals(abnormalNoticeType)) {
            noticeLockKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_ROBOT_MSG_SEND_RESTRICT_NOTICE_LOCK + TenantContextHolder.get() + ":" + robotCustomer.getId();
        }
        if (EnterpriseWechatRobotCustomerAbnormalNoticeType.CREATE_LIMIT.equals(abnormalNoticeType)) {
            noticeLockKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_ROBOT_CREATE_LIMIT_NOTICE_LOCK + enterpriseWechat.getCorpid();
        }
        if (EnterpriseWechatRobotCustomerAbnormalNoticeType.RECOVERY_FAIL.equals(abnormalNoticeType)) {
            noticeLockKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_ROBOT_CREATE_FAIL_NOTICE_LOCK + TenantContextHolder.get() + ":" + robotCustomer.getId();
        }
        if (EnterpriseWechatRobotCustomerAbnormalNoticeType.SUBJECT_UNAUTHORIZED.equals(abnormalNoticeType)) {
            noticeLockKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_ROBOT_SUBJECT_UNAUTHORIZED_NOTICE_LOCK + enterpriseWechat.getCorpid();
        }
        if (EnterpriseWechatRobotCustomerAbnormalNoticeType.MEMBER_UN_LOG_ON.equals(abnormalNoticeType)) {
            noticeLockKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_ROBOT_MEMBER_UN_LOG_ON_NOTICE_LOCK + enterpriseWechat.getCorpid();
        }
        if (EnterpriseWechatRobotCustomerAbnormalNoticeType.SECURITY_RESTRICT.equals(abnormalNoticeType)) {
            noticeLockKey = RedisConstant.ENTERPRISE_WECHAT_CUSTOMER_ROBOT_SECURITY_RESTRICT_NOTICE_LOCK + enterpriseWechat.getCorpid();
        }
        if (noticeLockKey != null) {
            RLock noticeLocker = redissonClient.getLock(noticeLockKey);
            if (!noticeLocker.tryLock(0, 30, TimeUnit.MINUTES) || noticeLocker.getHoldCount() != 1) {
                return;
            }
        }
        String title = "微信客服机器人异常";
        String content = String.format(
            "时间：%s\n所属项目：%s\n所属项目ID：%s\n所属企业微信：%s\n所属企业微信ID：%s\n异常微信客服机器人：%s\n异常微信客服机器人ID：%s\n异常事件：%s\n执行动作：%s",
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now()),
            bossAdvertiserAccountGroupService.getAdvertiserAccountGroupNameById(robotCustomer.getAdvertiserAccountGroupId()),
            robotCustomer.getAdvertiserAccountGroupId(),
            enterpriseWechat.getCorpName(),
            enterpriseWechat.getCorpid(),
            EnterpriseWechatRobotCustomerAbnormalNoticeType.CREATE_LIMIT.equals(abnormalNoticeType) ? "" : robotCustomer.getName(),
            EnterpriseWechatRobotCustomerAbnormalNoticeType.CREATE_LIMIT.equals(abnormalNoticeType) ? "" : robotCustomer.getId(),
            EnterpriseWechatRobotCustomerAbnormalNoticeType.CREATE_LIMIT.equals(abnormalNoticeType) ? String.format(abnormalNoticeType.getDescription(), enterpriseWechat.getCorpName(), enterpriseWechat.getCorpid()) : abnormalNoticeType.getDescription(),
            customerServiceRobotAbnormalNotificationDto.getRecover() ? "创建新微信客服机器人替换成功" : "-"
        );
        outSiteMsgSender.sendOutSiteMsg(OutSiteMsgType.FEISHU, pmpConfig.getFeishuRobotKey(), title, content);
        outSiteMsgSender.sendOutSiteMsg(OutSiteMsgType.WECOM, pmpConfig.getWecomRobotKey(), title, content);
        outSiteMsgSender.sendOutSiteMsg(OutSiteMsgType.DINGTALK, pmpConfig.getDingtalkRobotKey(), title, content);
        if (EnterpriseWechatRobotCustomerAbnormalNoticeType.CREATE_LIMIT.equals(abnormalNoticeType)) {
            Set<Long> pmpIds = enterpriseWechatService.getRelatedPmpByCorpIdAndAgentId(enterpriseWechat.getCorpid(), TenantContextHolder.get());
            MessageNotice messageNotice = new MessageNotice()
                .setNoticeTitle("微信客服机器人创建达至上限，无法新建")
                .setNoticeContent(String.format("%s（%s）微信客服机器人创建达至上限，无法新建", enterpriseWechat.getCorpName(), enterpriseWechat.getCorpid()))
                .setNoticeType(MarketingNoticeType.WECHAT_CUSTOMER_SERVICE_ROBOT_CREATE_LIMIT);
            for (Long pmpId : pmpIds) {
                messageNotice.setAdvertiserAccountGroupId(pmpId);
                messageNoticeSender.sendNoticeMessage(messageNotice);
            }
        }
        if (EnterpriseWechatRobotCustomerAbnormalNoticeType.RECOVERY_FAIL.equals(abnormalNoticeType)) {
            MessageNotice messageNotice = new MessageNotice()
                .setNoticeTitle("微信客服机器人被投诉风险提示智能恢复失败")
                .setNoticeContent(String.format("%s（%s）被投诉风险提示智能恢复失败", robotCustomer.getName(), robotCustomer.getId()))
                .setNoticeType(MarketingNoticeType.WECHAT_CUSTOMER_SERVICE_ROBOT_RECOVERY_FAIL)
                .setAdvertiserAccountGroupId(robotCustomer.getAdvertiserAccountGroupId());
            messageNoticeSender.sendNoticeMessage(messageNotice);
        }
    }

    /**
     * 根据项目ID查询绑定的企业微信corpid
     *
     * @param advertiserAccountGroupId 项目ID
     * @return
     */
    public Set<String> getCorpIdByPmpId(Long advertiserAccountGroupId) {
        if (Objects.nonNull(advertiserAccountGroupId)) {
            return this.baseMapper.getCorpIdByPmpId(advertiserAccountGroupId);
        }
        return new HashSet<>();
    }


    /**
     * 检查是否需要生成新的企业微信的活码素材
     *
     * @param dto
     */
    public void checkWetherToGenerateMultipleLiveCode(EnterpriseWechatRobotCustomerSaveDto dto) {
        if (Objects.nonNull(dto)) {
            List<EnterpriseWechatCustomerMsgTemplate> autoAnswerList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(dto.getAutoAnswerList())) {
                autoAnswerList.addAll(dto.getAutoAnswerList());
            }
            SwitchStatus keywordReply = dto.getKeywordReply();
            //这里是保存机器人配置的时候，判断关键词回复里面是否配置了机器人活码，根据规则ID查询子模板记录，  封装需要的参数即可
            if (Objects.equals(keywordReply, SwitchStatus.OPEN)){
                Set<Long> keywordReplyRuleIds = dto.getKeywordReplyRuleIds();
                //封装autoAnswerList
                if (CollectionUtils.isEmpty(keywordReplyRuleIds)){
                    return;
                }
                log.info("保存机器人配置的时候，判断关键词回复里面是否配置了机器人活码，根据规则ID查询子模板记录, keywordReplyRuleIds = {}", keywordReplyRuleIds);
                List<EnterpriseWechatCustomerMsgChildrenTemplate> childrenTemplates = enterpriseWechatCustomerMsgChildrenTemplateService.queryChildrenTemplates(keywordReplyRuleIds);
                if (CollectionUtils.isNotEmpty(childrenTemplates)){
                    EnterpriseWechatCustomerMsgTemplate enterpriseWechatCustomerMsgTemplate = new EnterpriseWechatCustomerMsgTemplate();
                    enterpriseWechatCustomerMsgTemplate.setTemplates(childrenTemplates);
                    enterpriseWechatCustomerMsgTemplate.setMsgType(RobotCustomerMsgType.LINK);
                    autoAnswerList.add(enterpriseWechatCustomerMsgTemplate);
                    dto.setKeywordReplyFlag(true);
                }
            }
            if (CollectionUtils.isNotEmpty(autoAnswerList)) {
                //判断是否设置了活码，这里循环的目的是因为，不同的自动回复里面都选了活码回复，但是客服分组不是同一个
                for (EnterpriseWechatCustomerMsgTemplate enterpriseWechatCustomerMsgTemplate : autoAnswerList) {
                    try {
                        RobotCustomerMsgType msgType = enterpriseWechatCustomerMsgTemplate.getMsgType();
                        if (Objects.equals(msgType, RobotCustomerMsgType.LINK)) {
                            List<EnterpriseWechatCustomerMsgChildrenTemplate> templates = enterpriseWechatCustomerMsgTemplate.getTemplates();
                            if (!templates.isEmpty()) {
                                for (EnterpriseWechatCustomerMsgChildrenTemplate template : templates) {
                                    ImageType imageType = template.getImageType();
                                    if (Objects.equals(imageType, ImageType.MULTIPLE_LIVE_CODE)) {
                                        boolean keywordReplyFlag = dto.getKeywordReplyFlag();
                                        log.info("检查是否需要生成新的企业微信的活码素材, keywordReplyFlag = {}", keywordReplyFlag);
                                        //所选的客服分组ID
                                        Long liveCodeServiceGroupId = Objects.equals(keywordReplyFlag,true) ? template.getLiveCodeServiceGroupIdDynamic() : template.getLiveCodeServiceGroupId();
                                        String corpId = dto.getCorpId();
                                        Long advertiserAccountGroupId = dto.getAdvertiserAccountGroupId();
                                        log.info("检查是否需要生成新的企业微信的活码素材, liveCodeServiceGroupId = {}, corpId = {}, imageType = {}, advertiserAccountGroupId = {}", liveCodeServiceGroupId, corpId, imageType, advertiserAccountGroupId);
                                        if (Objects.nonNull(liveCodeServiceGroupId) && StringUtils.isNotBlank(corpId)) {
                                            //校验这个分组是否已经有活码生成
                                            //查询这个分组下面设置了活码的客服
                                            List<LandingPageWechatCustomerService> list = landingPageWechatCustomerServiceService.getRobotLiveCodeCustomerService(liveCodeServiceGroupId, advertiserAccountGroupId);

                                            for (LandingPageWechatCustomerService landingPageWechatCustomerService : list) {
                                                String wechatUserId = landingPageWechatCustomerService.getWechatUserId();
                                                if (StringUtils.isBlank(wechatUserId) || StringUtils.isBlank(corpId)) {
                                                    log.error("保存机器人活码配置，生成活码素材参数不合法，直接跳过这个客服, wechatUserId = {}, corpId = {}", wechatUserId, corpId);
                                                    continue;
                                                }

                                                String key = RedisConstant.ENTERPRISE_WECHAT_ROBOT_CUSTOMER_SAVE_KEY + corpId + ":" + wechatUserId;
                                                log.info("保存机器人活码配置，生成活码素材，加锁，key = {}", key);
                                                RLock fairLock = null;
                                                fairLock = redissonClient.getFairLock(key);
                                                //尝试加锁，最多等待60秒
                                                boolean res = fairLock.tryLock(0, 60, TimeUnit.SECONDS);
                                                if (!res) {
                                                    log.info("保存机器人活码配置,生成动态渠道二维码获取锁失败,key = [{}]", key);
                                                    return;
                                                }

                                                //获取背景图信息的基础配置
                                                Long landingPageWechatCustomerServiceId = landingPageWechatCustomerService.getId();
                                                RobotCustomerDynamicContact robotCustomerDynamicContact = robotCustomerDynamicContactService.getByLandingPageServiceId(landingPageWechatCustomerServiceId);
                                                if (Objects.nonNull(robotCustomerDynamicContact)) {
                                                    RobotCustomerContactDto robotCustomerContactDto = new RobotCustomerContactDto();
                                                    robotCustomerContactDto.setIds(Arrays.asList(landingPageWechatCustomerService.getId()))
                                                        .setRobotCustomerDynamicContactVerify(robotCustomerDynamicContact.getRobotCustomerDynamicContactVerify())
                                                        .setRobotCustomerContactVerify(robotCustomerDynamicContact.getRobotCustomerDynamicContactVerify())
                                                        .setBackgroundUrl(robotCustomerDynamicContact.getBackgroundUrl())
                                                        .setQrCodeWidth(robotCustomerDynamicContact.getQrCodeWidth())
                                                        .setQrCodeHeight(robotCustomerDynamicContact.getQrCodeHeight())
                                                        .setQrCodeIndexLeft(robotCustomerDynamicContact.getQrCodeIndexLeft())
                                                        .setQrCodeIndexTop(robotCustomerDynamicContact.getQrCodeIndexTop())
                                                        .setBackgroundWidth(robotCustomerDynamicContact.getBackgroundWidth())
                                                        .setBackgroundHeight(robotCustomerDynamicContact.getBackgroundHeight())
                                                        .setAdvertiserAccountGroupId(dto.getAdvertiserAccountGroupId())
                                                        .setRobotConfigSaveFlag(true)
                                                        .setRobotCorpId(corpId)
                                                        .setJobInitFlag(false)
                                                        .setWechatUserId(wechatUserId)
                                                        .setLiveCodeServiceGroupId(liveCodeServiceGroupId)
                                                    ;
                                                    log.info("保存微信客服机器人配置,进行活码生成, robotCustomerContactDto = {}", JSONObject.toJSONString(robotCustomerContactDto));
                                                    robotCustomerContactSender.sendBatchCreateDynamicQrCode(robotCustomerContactDto);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("保存微信客服机器人配置,进行动态渠道二维码生成,出现异常, robotCustomerContactDto = {}", JSONObject.toJSONString(dto), e);
                    }
                }
            }
        }
    }

    /**
     * 微信客服机器人进线记录
     */
    public void entryRecord(EnterpriseWechatRobotCustomer robot, String scene) {
        String entryListKey = RedisConstant.WORK_WEIXIN_KF_ENTRY_LIST + robot.getOpenKfId() + ":" + scene;
        objectRedisTemplate.opsForValue().set(entryListKey, scene, 1, TimeUnit.DAYS);
    }


    /**
     * 查询关键词回复记录是否被机器人绑定
     */
    public String queryKeywordReplyRule(Long id) {
        return this.baseMapper.queryKeywordReplyRule(id);
    }
}
