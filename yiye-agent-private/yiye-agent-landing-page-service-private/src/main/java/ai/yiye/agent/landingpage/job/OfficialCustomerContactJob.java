package ai.yiye.agent.landingpage.job;

import ai.yiye.agent.autoconfigure.mybatis.multidatasource.service.AgentConfService;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.domain.OfficialWechatCustomerServiceGroup;
import ai.yiye.agent.domain.OfficialWechatCustomerServiceUser;
import ai.yiye.agent.domain.landingpage.WorkWechatUserVisibleRangeOfficialCustomerContact;
import ai.yiye.agent.landingpage.config.LandingPageWechatCustomerContactConfig;
import ai.yiye.agent.landingpage.dto.CustomerContactCheckUsedDto;
import ai.yiye.agent.landingpage.sender.MultiplayerCodeSender;
import ai.yiye.agent.landingpage.sender.OfficialCustomerContactSender;
import ai.yiye.agent.landingpage.service.*;
import ai.yiye.agent.landingpage.service.action.CancelAuthService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OfficialCustomerContactJob {


    @Autowired
    private AgentConfService agentConfService;

    @Autowired
    private OfficialCustomerContactSender officialCustomerContactSender;
    @Autowired
    private LandingPageWechatOfficialMaterialFileService landingPageWechatOfficialMaterialFileService;
    @Autowired
    private OfficialWechatCustomerServiceUserService officialWechatCustomerServiceUserService;
    @Autowired
    private OfficialWechatCustomerGroupContactService officialWechatCustomerGroupContactService;
    @Resource(name = "BeanCleanUsedOfficialCustomerContactThreadPool")
    private Executor beanCleanUsedOfficialCustomerContactThreadPool;

    @Resource
    private MultiplayerCodeSender multiplayerCodeSender;
    @Qualifier("CancelAuthService")
    @Resource
    private CancelAuthService cancelAuthService;
    @Resource
    private OfficialWechatCustomerServiceGroupService officialWechatCustomerServiceGroupService;
    @Resource
    private LandingPageWechatCustomerContactConfig landingPageWechatCustomerContactConfig;

    @Resource
    private WorkWechatUserVisibleRangeOfficialCustomerContactService workWechatUserVisibleRangeOfficialCustomerContactService;

    /**
     * 检测已使用的公众号单人联系我二维码是否超时，清除远端联系我二维码
     *
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob(value = "OfficialCustomerContactCheckUsed")
    public ReturnT<String> customerContactCheckUsed(String param) throws Exception {
        log.info("开始执行检测清除已使用的超时联系我二维码企微侧记录任务=======================>>>>>>>>>");
        StopWatch stopWatchTemp = new StopWatch();
        stopWatchTemp.start();
        TenantContextHolder.clearContext();
        LambdaQueryWrapper<AgentConf> lambdaQueryWrapper = new LambdaQueryWrapper<AgentConf>()
            .in(AgentConf::getStatus, Arrays.asList(0, 1));
        List<AgentConf> list = agentConfService.list(lambdaQueryWrapper);
        list.forEach(agentConf -> {
            CustomerContactCheckUsedDto customerContactCheckUsedDto = new CustomerContactCheckUsedDto();
            customerContactCheckUsedDto.setAgentId(agentConf.getAgentId()).setStartTime(param);
            officialCustomerContactSender.sendCleanAgentUseContact(customerContactCheckUsedDto);
        });
        stopWatchTemp.stop();
        log.info("==============【检测清除已使用的超时联系我二维码企微侧记录结束,耗时:{}S】==============", stopWatchTemp.getTotalTimeSeconds());
        return ReturnT.SUCCESS;
    }


    /**
     * 检测已使用的公众号多人联系我二维码是否超时，清除远端联系我二维码
     *
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob(value = "OfficialCustomerContactGroupCheckUsed")
    public ReturnT<String> customerContactGroupCheckUsed(String param) throws Exception {
        log.info("开始执行检测清除已使用的超时多人联系我二维码企微侧记录任务=======================>>>>>>>>>");
        TenantContextHolder.clearContext();
        LambdaQueryWrapper<AgentConf> lambdaQueryWrapper = new LambdaQueryWrapper<AgentConf>()
            .in(AgentConf::getStatus, Arrays.asList(0, 1));
        List<AgentConf> list = agentConfService.list(lambdaQueryWrapper);
        list.forEach(agentConf -> {
            CustomerContactCheckUsedDto customerContactCheckUsedDto = new CustomerContactCheckUsedDto();
            customerContactCheckUsedDto.setAgentId(agentConf.getAgentId()).setStartTime(param);
            multiplayerCodeSender.sendCleanAgentUseContact(customerContactCheckUsedDto);
        });
        return ReturnT.SUCCESS;
    }


    /**
     * 客服 单人活码 公众号临时素材续期 临时素材有效期为3天
     *
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob(value = "officialTemporaryMaterialsExpire")
    public ReturnT<String> officialTemporaryMaterialsExpire(String param) throws Exception {
        log.info("===============[客服单人活码]公众号临时素材续期检测开始=======================>>>>>>>>>");
        StopWatch stopWatchTemp = new StopWatch();
        stopWatchTemp.start();
        TenantContextHolder.clearContext();

        // 获取所有已经绑定企业微信的可用公众号
        List<String> officialAccountAppids = officialWechatCustomerServiceUserService.getOfficialAccountAppids();
        officialAccountAppids.stream().map(appid -> {
            return CompletableFuture.supplyAsync(() -> {
                    landingPageWechatOfficialMaterialFileService.materialExpire(appid);
                    return Void.class;
                }, beanCleanUsedOfficialCustomerContactThreadPool
            ).exceptionally(e -> {
                log.error("公众号appid:{} 续期异常，errorMessage:{}", appid, e.getMessage(), e);
                return Void.class;
            });
        }).collect(Collectors.toList()).stream().map(CompletableFuture::join);
        stopWatchTemp.stop();
        log.info("==============[客服单人活码]公众号临时素材续期检测结束,耗时:{}S ==============", stopWatchTemp.getTotalTimeSeconds());
        return ReturnT.SUCCESS;
    }

    /**
     * 客服分组 多人活码 公众号临时素材续期 临时素材有效期为3天
     *
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob(value = "officialCustomerGroupTemporaryMaterialsExpire")
    public ReturnT<String> officialCustomerGroupTemporaryMaterialsExpire(String param) throws Exception {
        TenantContextHolder.clearContext();
        log.info("===============[客服分组多人活码]公众号临时素材续期检测开始=======================>>>>>>>>>");
        StopWatch stopWatchTemp = new StopWatch();
        stopWatchTemp.start();
        LambdaQueryWrapper<AgentConf> lambdaQueryWrapper = new LambdaQueryWrapper<AgentConf>()
            .in(AgentConf::getStatus, Arrays.asList(0, 1));
        List<AgentConf> list = agentConfService.list(lambdaQueryWrapper);
        list.forEach(agentConf -> {
            TenantContextHolder.set(agentConf.getAgentId());
            try {

                officialWechatCustomerGroupContactService.doRenewal();
            } catch (Exception e) {
                log.error("[客服分组多人活码]公众号临时素材续期检测异常 agnetId:{}", TenantContextHolder.get(), e);
            }
        });
        //

        stopWatchTemp.stop();
        log.info("===============[客服分组多人活码]公众号临时素材续期检测结束,耗时:{}s =======================>>>>>>>>>", stopWatchTemp.getTotalTimeSeconds());
        return ReturnT.SUCCESS;
    }


    /**
     * 客服分组 多人活码 分组内长时间没有可用客服，自动删除多人活码
     *
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob(value = "noCustomerServiceJob")
    public ReturnT<String> noCustomerService(String param) throws Exception {
        TenantContextHolder.clearContext();
        log.info("===============[客服分组多人活码]清理长时间没有可用客服的分组开始=======================>>>>>>>>>");
        StopWatch stopWatchTemp = new StopWatch();
        stopWatchTemp.start();
        LambdaQueryWrapper<AgentConf> lambdaQueryWrapper = new LambdaQueryWrapper<AgentConf>()
            .in(AgentConf::getStatus, Arrays.asList(0, 1));
        List<AgentConf> list = agentConfService.list(lambdaQueryWrapper);

        list.forEach(agentConf -> {
            TenantContextHolder.set(agentConf.getAgentId());
            //
            try {
                List<OfficialWechatCustomerServiceGroup> customerServiceGroups = officialWechatCustomerServiceGroupService.list(new LambdaQueryWrapper<OfficialWechatCustomerServiceGroup>()
                    .le(OfficialWechatCustomerServiceGroup::getNoCustomerServiceStartAt, Instant.now().minus(landingPageWechatCustomerContactConfig.getNoCustomerServiceDuration(), ChronoUnit.MINUTES)));
                customerServiceGroups.forEach(officialWechatCustomerServiceGroup -> {
                    cancelAuthService.clearWechatCustomerServiceGroup(officialWechatCustomerServiceGroup, null);
                    cancelAuthService.clearWechatCustomerGroupDb(officialWechatCustomerServiceGroup);
                });
            } catch (Exception e) {
                log.error("[客服分组多人活码]清理长时间没有可用客服异常", e);
            }

        });

        stopWatchTemp.stop();
        log.info("===============[客服分组多人活码]清理长时间没有可用客服的分组结束,耗时:{}s =======================>>>>>>>>>", stopWatchTemp.getTotalTimeSeconds());
        return ReturnT.SUCCESS;
    }


    /**
     * 一次性脚本 将所有agentId为空的数据，查询修改为对应的agentId
     *
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob(value = "FixOfficialVisibleAgentIdJob")
    public ReturnT<String> fixVisibleAgentId(String param) throws Exception {
        TenantContextHolder.clearContext();
        log.info("===============[公众号内客服渠道二维码实时表初始化agentId]开始=======================>>>>>>>>>");
        StopWatch stopWatchTemp = new StopWatch();
        stopWatchTemp.start();
        //公众号渠道二维码实时表
        List<WorkWechatUserVisibleRangeOfficialCustomerContact> customerContacts = workWechatUserVisibleRangeOfficialCustomerContactService.searchVisibleAgentId();
        if (CollectionUtils.isNotEmpty(customerContacts)) {
            customerContacts.forEach(e -> {
                String corpId = e.getCorpId();
                String agentId = e.getAgentId();
                if (StringUtils.isNoneBlank(corpId, agentId)) {
                    log.info("更新公众号内客服渠道二维码实时表agentId,corpId:{},agentId:{}", corpId, agentId);
                    workWechatUserVisibleRangeOfficialCustomerContactService.update(Wrappers.lambdaUpdate(WorkWechatUserVisibleRangeOfficialCustomerContact.class)
                        .set(WorkWechatUserVisibleRangeOfficialCustomerContact::getAgentId, agentId)
                        .eq(WorkWechatUserVisibleRangeOfficialCustomerContact::getCorpId, corpId));
                }
            });
        }
        //公众号客服绑定关系表
        List<OfficialWechatCustomerServiceUser> officialWechatCustomerServiceUsers = officialWechatCustomerServiceUserService.searchVisibleAgentId();
        if (CollectionUtils.isNotEmpty(officialWechatCustomerServiceUsers)) {
            officialWechatCustomerServiceUsers.forEach(e -> {
                String corpId = e.getCorpId();
                String agentId = e.getAgentId();
                if (StringUtils.isNoneBlank(corpId, agentId)) {
                    log.info("更新公众号客服绑定关系表agentId,corpId:{},agentId:{}", corpId, agentId);
                    officialWechatCustomerServiceUserService.update(Wrappers.lambdaUpdate(OfficialWechatCustomerServiceUser.class)
                        .set(OfficialWechatCustomerServiceUser::getAgentId, agentId)
                        .eq(OfficialWechatCustomerServiceUser::getCorpId, corpId));
                }
            });
        }
        stopWatchTemp.stop();
        log.info("===============[公众号内客服渠道二维码实时表初始化agentId]结束,耗时:{}s =======================>>>>>>>>>", stopWatchTemp.getTotalTimeSeconds());
        return ReturnT.SUCCESS;
    }

}
