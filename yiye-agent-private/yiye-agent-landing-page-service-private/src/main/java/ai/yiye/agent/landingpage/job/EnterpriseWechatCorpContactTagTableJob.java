package ai.yiye.agent.landingpage.job;

import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.domain.dto.CreateTableParams;
import ai.yiye.agent.landingpage.config.WorkWechatDevelopConf;
import ai.yiye.agent.landingpage.sender.EnterpriseWechatCorpContactTagSender;
import ai.yiye.agent.landingpage.service.AgentConfService;
import ai.yiye.agent.landingpage.service.EnterpriseWechatCorpContactTagService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/1/20 16:19
 * @Desc 客资表分区
 * @Version 1.0
 */
@Slf4j
@Component
public class EnterpriseWechatCorpContactTagTableJob extends IJobHandler {

    @Resource
    private AgentConfService agentConfService;

    @Resource
    private EnterpriseWechatCorpContactTagService enterpriseWechatCorpContactTagService;

    @Resource
    private EnterpriseWechatCorpContactTagSender enterpriseWechatCorpContactTagSender;


    @Resource
    private WorkWechatDevelopConf workWechatDevelopConf;


    @Override
    @XxlJob(value = "EnterpriseWechatCorpContactTagTableJob")
    public ReturnT<String> execute(String param) throws Exception {
        log.info("==============【enterprise_wechat_corp_contact_tag表按月分分区创建下个月的分区表-任务调度开始】==============");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<AgentConf> list = agentConfService.list(new LambdaQueryWrapper<AgentConf>().in(AgentConf::getStatus, Arrays.asList(new Integer[]{0, 1})));
        Optional.ofNullable(list).filter(CollectionUtils::isNotEmpty).ifPresent(e -> {
            LocalDateTime startOfMonth = null;
            LocalDateTime startOfNextMonth = null;
            //指定日期
            if (StringUtils.isNotBlank(param)) {
                startOfMonth = LocalDateTime.parse(param, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                startOfMonth = startOfMonth.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                startOfNextMonth = startOfMonth.plusMonths(1);
            } else {
                // 获取下月开始时间
                startOfMonth = LocalDateTime.now()
                    .plusMonths(1) //增加一个月
                    .withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                // 获取下下个月的开始时间
                startOfNextMonth = LocalDateTime.now()
                    .plusMonths(2) // 增加两个月
                    .withDayOfMonth(1) // 设置为下个月的第一天
                    .withHour(0) // 设置小时为 0
                    .withMinute(0) // 设置分钟为 0
                    .withSecond(0) // 设置秒为 0
                    .withNano(0); // 设置纳秒为 0
            }


            int year = startOfMonth.getYear();
            int month = startOfMonth.getMonth().getValue();
            String originalTableName = workWechatDevelopConf.getEnterpriseWechatCorpContactTagPartitionTableName();
            String tableName = originalTableName + "_" + year + "_" + month;
            // 定义日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String currentMonthDate = startOfMonth.format(formatter);
            String nextMonthDate = startOfNextMonth.format(formatter);
            CreateTableParams createTableParams = new CreateTableParams(originalTableName, tableName, String.valueOf(month), currentMonthDate, nextMonthDate);
            e.forEach(agentConf -> {
                String agentId = agentConf.getAgentId();
                log.info("==============【enterpriseWechatCorpContactTag表按月分分区】agentId:{}", agentId);
                try {
                    TenantContextHolder.set(agentId);
                    //客资表按月分分区
                    enterpriseWechatCorpContactTagService.createTableSubmit(createTableParams);
                } catch (Exception ex) {
                    log.error("enterpriseWechatCorpContactTag表按月分分区-任务调度异常", ex);
                } finally {
                    TenantContextHolder.clearContext();
                }
            });
        });
        log.info("==============【EnterpriseWechatCorpContactTag表按月分分区-任务调度结束】==============");
        return ReturnT.SUCCESS;
    }


    /**
     * 检测下个月EnterpriseWechatCorpContactTag分区是否创建
     */
    @XxlJob("checkEnterpriseWechatCorpContactTagTableExist")
    public ReturnT<String> checkEnterpriseWechatCorpContactTagTableExist(String param) {
        // 获取下月开始时间
        LocalDateTime startOfMonth = LocalDateTime.now()
            .plusMonths(1) //增加一个月
            .withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        int year = startOfMonth.getYear();
        int month = startOfMonth.getMonth().getValue();
        String originalTableName = workWechatDevelopConf.getEnterpriseWechatCorpContactTagPartitionTableName();
        String tableName = originalTableName + "_" + year + "_" + month;
        log.info("下个月EnterpriseWechatCorpContactTag分区表名称：" + tableName);
        List<AgentConf> agentDBConfigs = agentConfService.list(new LambdaQueryWrapper<AgentConf>().in(AgentConf::getStatus, Arrays.asList(new Integer[]{0, 1})));
        //查询information_schema.tables关于enterprise_wechat_corp_contact_tag_new_xx的全部记录
        List<String> tableNums = enterpriseWechatCorpContactTagService.tableNum(tableName);
        agentDBConfigs.forEach(agentConf -> {
            String agentId = agentConf.getAgentId();
            String objectName = "asp_saas_" + agentId;
            try {
                TenantContextHolder.set(agentConf.getAgentId());
                if (CollectionUtils.isEmpty(tableNums)) {
                    enterpriseWechatCorpContactTagSender.sendWarning(String.format("系统检测出下个月的【EnterpriseWechatCorpContactTag新表分区未创建】，请及时创建：tableName=%s；agentId=%s；", tableName, agentConf.getAgentId()));
                } else if (!tableNums.contains(objectName)) {
                    //当前库中不存在此数据表
                    enterpriseWechatCorpContactTagSender.sendWarning(String.format("系统检测出下个月的【EnterpriseWechatCorpContactTag新表分区未创建】，请及时创建：tableName=%s；agentId=%s；", tableName, agentConf.getAgentId()));
                }
            } catch (Exception e) {
                enterpriseWechatCorpContactTagSender.sendWarning(String.format("检测【EnterpriseWechatCorpContactTag客资分区表】是否存在，发生异常：tableName=%s；agentId=%s；message=%s；", tableName, agentConf.getAgentId(), e.getMessage()));
            } finally {
                TenantContextHolder.clearContext();
            }
        });
        return ReturnT.SUCCESS;
    }
}
