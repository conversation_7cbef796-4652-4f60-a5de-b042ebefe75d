package ai.yiye.agent.landingpage.controller;

import ai.yiye.agent.domain.UploadConfigChangeRecord;
import ai.yiye.agent.domain.User;
import ai.yiye.agent.domain.result.Result;
import ai.yiye.agent.landingpage.service.readonly.UploadConfigChangeRecordReadOnlyService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/uconfig-change-record")
public class UploadConfigChangeRecordController {

    @Autowired
    private UploadConfigChangeRecordReadOnlyService uploadConfigChangeRecordReadOnlyService;

    @PostMapping("/collect/filtering")
    public Result<IPage<UploadConfigChangeRecord>> filtering(@RequestBody UploadConfigChangeRecord uccr, IPage<UploadConfigChangeRecord> page) {
        return Result.success(uploadConfigChangeRecordReadOnlyService.filtering(uccr, page));
    }

    @PostMapping("overall/collect/filtering")
    public Result<IPage<UploadConfigChangeRecord>> overallfiltering(@RequestBody UploadConfigChangeRecord uccr,
                                                                    IPage<UploadConfigChangeRecord> page,@AuthenticationPrincipal User user) {
        return Result.success(uploadConfigChangeRecordReadOnlyService.overallfiltering(uccr, page,user));
    }

    /**
     * 上报配置变更日志导出
     */
    @PostMapping("/export/async")
    public Result<Boolean> export(@RequestBody UploadConfigChangeRecord uccr,@AuthenticationPrincipal User user) {
        return uploadConfigChangeRecordReadOnlyService.getBytesAsync(uccr,user);
    }

}
