package ai.yiye.agent.landingpage.service;


import ai.yiye.agent.autoconfigure.web.exception.ErrorConstants;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.LandingPage;
import ai.yiye.agent.domain.LandingPageCustomCode;
import ai.yiye.agent.domain.LandingPageStrategy;
import ai.yiye.agent.domain.LandingPageWechatCustomerServiceGroup;
import ai.yiye.agent.domain.boss.BossCustomerDomain;
import ai.yiye.agent.domain.boss.BossLandingPageDomain;
import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.landingpage.*;
import ai.yiye.agent.domain.query.LandingPageQuery;
import ai.yiye.agent.landingpage.config.LandingPageNewSearchConfig;
import ai.yiye.agent.landingpage.contants.LandingPageConstant;
import ai.yiye.agent.landingpage.dto.ChannelStatusDto;
import ai.yiye.agent.landingpage.mapper.LandingPageCustomCodeMapper;
import ai.yiye.agent.landingpage.mapper.LandingPageMapper;
import ai.yiye.agent.landingpage.remote.BossBackendRemote;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.CaseFormat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PathVariable;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static ai.yiye.agent.landingpage.utils.Constants.EMP_TYPE;
import static ai.yiye.agent.landingpage.utils.Constants.PMP_TYPE;

/**
 * @program: yiye-agent-backend
 * @description: 落地页Service
 * @author: xuhainan
 * @create: 2020-06-12 10:29
 **/
@Slf4j
@Service
@DS(DbConstants.READONLY_PG_POOL_NAME)
public class LandingPageReadOnlyService extends ServiceImpl<LandingPageMapper, LandingPage> {

    private static final String httpStatus = "http://";
    @Autowired
    private LandingPageMapper landingPageMapper;
    @Autowired
    private LandingPageCustomCodeMapper landingPageCustomCodeMapper;

    @Autowired
    private LandingPageStrategyService strategyService;

    @Autowired
    private LandingPageDomainBindingService landingPageDomainBindingService;
    @Autowired
    private BossBackendRemote bossBackendRemote;

    @Autowired
    private MarketingLandingPageService marketingLandingPageService;

    @Autowired
    private LandingPageWechatCustomerServiceGroupService landingPageWechatCustomerServiceGroupService;
    @Resource
    private LandingPageWechatOfficialAccountService landingPageWechatOfficialAccountService;
    @Autowired
    private SystemFunctionService systemFunctionService;
    @Autowired
    private LandingPageWechatCustomerCityCodeService landingPageWechatCustomerCityCodeService;
    @Autowired
    private LandingPageWechatGroupChatGroupService landingPageWechatGroupChatGroupService;

    @Autowired
    private LandingPageNewSearchConfig landingPageNewSearchConfig;

    @Resource
    private PageViewEventInfoService pageViewEventInfoService;

    @Resource
    private LandingPageDayStatisticsService landingPageDayStatisticsService;

    public static void main(String[] args) {
        List<LandingPageListVo> persons = Arrays.asList(
            new LandingPageListVo().setId(1L).setDynamicWechatOfficialAccountIds(new Long[]{10L, 20L}),
            new LandingPageListVo().setId(2L).setDynamicWechatOfficialAccountIds(new Long[]{}),
            new LandingPageListVo().setId(3L).setDynamicWechatOfficialAccountIds(null),
            new LandingPageListVo().setId(4L).setDynamicWechatOfficialAccountIds(new Long[]{30L, 40L}),
            new LandingPageListVo().setId(5L).setDynamicWechatOfficialAccountIds(new Long[]{30L, 50L})
        );
        Set<Long> combinedList = persons.stream()
            .filter(person -> person.getDynamicWechatOfficialAccountIds() != null && person.getDynamicWechatOfficialAccountIds().length > 0)
            .flatMap(person -> Arrays.stream(person.getDynamicWechatOfficialAccountIds()))
            .collect(Collectors.toSet());
        combinedList.forEach(System.out::println);
    }

    /**
     * 落地页列表
     *
     * @param page 分页信息
     */
    public IPage<LandingPageListVo> pageListNew(IPage<LandingPage> page, LandingPageQuery landingPageQuery) {
        long startTime = System.currentTimeMillis();
        landingPageQuery.setSortField(CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, landingPageQuery.getSortField()));
        //数据固化增加新版本，根据配置切换新旧版本查询
//        IPage<LandingPageListVo> vo = landingPageNewSearchConfig.getLandingPageNewSearch() ? landingPageMapper.pageListFromIndicatorStatistics(page, landingPageQuery) : landingPageMapper.pageListNew(page, landingPageQuery);
        IPage<LandingPageListVo> vo;
        boolean flag = this.checkFlag(landingPageQuery.getNewSearchFlag());
        log.info("落地页列表,新查询标识 flag = {} ", flag);
        if (flag) {
            vo = pagelist(page, landingPageQuery);
        } else {
            landingPageQuery.initTime();
            vo = landingPageMapper.pageListNew(page, landingPageQuery);
        }

        long endTime = System.currentTimeMillis();
        log.info("查询pg执行耗时：" + (endTime - startTime));

        // 再从clickhouse中查询重复访客数，访客数，重复访客率
        // TODO 这里移除重复访客数据 查询效率比较慢
//        List<Long> landingPageIds = vo.getRecords().stream().map(LandingPageListVo::getId).collect(Collectors.toList());


        //查询微信客服
        List<LandingPageListVo> records = vo.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            //获取查询的客服分组id集合
            Set<Long> wechatCustomerServiceGroupIds = records.stream()
                .filter(e -> !ObjectUtils.isEmpty(e.getWechatCustomerServiceGroupId()))
                .map(LandingPageListVo::getWechatCustomerServiceGroupId).collect(Collectors.toSet());
            Set<Long> noAdSceneWechatCustomerServiceGroupIds = records.stream()
                .filter(e -> !ObjectUtils.isEmpty(e.getNoAdSceneWechatCustomerServiceGroupId()))
                .map(LandingPageListVo::getNoAdSceneWechatCustomerServiceGroupId).collect(Collectors.toSet());
            wechatCustomerServiceGroupIds.addAll(noAdSceneWechatCustomerServiceGroupIds);
            Map<Long, List<LandingPageWechatCustomerServiceGroup>> listMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(wechatCustomerServiceGroupIds)) {
                List<LandingPageWechatCustomerServiceGroup> landingPageWechatCustomerServiceGroups = landingPageWechatCustomerServiceGroupService.listByIds(wechatCustomerServiceGroupIds);
                listMap = CollectionUtils.isEmpty(landingPageWechatCustomerServiceGroups) ? new HashMap<>() : landingPageWechatCustomerServiceGroups.stream()
                    .collect(Collectors.groupingBy(LandingPageWechatCustomerServiceGroup::getId));
            }
            //获取查询的公众号id集合
            Set<Long> wechatOfficialAccountIds = records.stream()
                .filter(e -> !ObjectUtils.isEmpty(e.getWechatOfficialAccountId()))
                .map(LandingPageListVo::getWechatOfficialAccountId).collect(Collectors.toSet());
            Set<Long> dynamicWechatOfficialAccountIds = records.stream()
                .filter(e -> e.getDynamicWechatOfficialAccountIds() != null && e.getDynamicWechatOfficialAccountIds().length > 0)
                .flatMap(e -> Arrays.stream(e.getDynamicWechatOfficialAccountIds()))
                .collect(Collectors.toSet());
            wechatOfficialAccountIds.addAll(dynamicWechatOfficialAccountIds);
            Map<Long, LandingPageWechatOfficialAccount> officialAccountMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(wechatOfficialAccountIds)) {
                officialAccountMap = landingPageWechatOfficialAccountService.listByIds(wechatOfficialAccountIds)
                    .stream()
                    .collect(Collectors.toMap(LandingPageWechatOfficialAccount::getId, Function.identity(), (key1, key2) -> key2));
            }
            Set<Long> landingPageIds = records.stream().filter(e -> !ObjectUtils.isEmpty(e.getId())).map(LandingPageListVo::getId).collect(Collectors.toSet());
            //根据落地页id，查询他下边的所有渠道列表
            //原先此方法只查询了抖音原生页对应渠道，此次改为查询对应落地页全部渠道,在下面再进行筛选抖音原生页渠道
            List<ChannelStatusDto> channels = marketingLandingPageService.getChannelStatusByLandingPage(landingPageIds);
            Map<Long, List<ChannelStatusDto>> longListMap = channels.stream().collect(Collectors.groupingBy(ChannelStatusDto::getLandingPageId, Collectors.toList()));
            //抖音原生页渠道
            List<ChannelStatusDto> douyinChannels = channels.stream().filter(e -> LandingPageChannelType.ORANGE_STATION_PATH.equals(e.getLandingPageChannelType())).collect(Collectors.toList());
            Map<Long, List<ChannelStatusDto>> channelStatusDtos = douyinChannels.stream()
                .collect(Collectors.groupingBy(ChannelStatusDto::getLandingPageId, Collectors.toList()));
            //1.194.0 根据城市区域码id查询城市区域码集合
            List<Long> landingPageWechatCustomerCityCodeIds = records.stream()
                .filter(a -> Objects.nonNull(a.getLandingPageWechatCustomerCityCodeId()))
                .map(a -> a.getLandingPageWechatCustomerCityCodeId()).collect(Collectors.toList());
            Map<Long, LandingPageWechatCustomerCityCode> landingPageWechatCustomerCityCodeMap = Maps.newHashMap();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(landingPageWechatCustomerCityCodeIds)) {
                landingPageWechatCustomerCityCodeMap = landingPageWechatCustomerCityCodeService.listByIds(landingPageWechatCustomerCityCodeIds)
                    .stream()
                    .collect(Collectors.toMap(LandingPageWechatCustomerCityCode::getId, Function.identity()));
            }

            // 1.197.0 企业微信群分组
            List<Long> chatGroupIds = records.stream()
                .filter(a -> Objects.nonNull(a.getWechatGroupChatGroupId()))
                .map(e -> e.getWechatGroupChatGroupId()).collect(Collectors.toList());
            Map<Long, LandingPageWechatGroupChatGroup> landingPageWechatGroupChatGroupMap = Maps.newHashMap();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(chatGroupIds)) {
                landingPageWechatGroupChatGroupMap = landingPageWechatGroupChatGroupService.listByIds(chatGroupIds)
                    .stream()
                    .collect(Collectors.toMap(LandingPageWechatGroupChatGroup::getId, Function.identity()));
            }
            // 1.197.0 城市区域码下的企业微信群
            List<Long> groupChatCityCodeIds = records.stream()
                .filter(a -> Objects.nonNull(a.getLandingPageWechatGroupChatCityCodeId()))
                .map(e -> e.getLandingPageWechatGroupChatCityCodeId()).collect(Collectors.toList());
            Map<Long, LandingPageWechatCustomerCityCode> landingPageWechatGroupChatCityCodeMap = Maps.newHashMap();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(groupChatCityCodeIds)) {
                landingPageWechatGroupChatCityCodeMap = landingPageWechatCustomerCityCodeService.listByIds(groupChatCityCodeIds)
                    .stream()
                    .collect(Collectors.toMap(LandingPageWechatCustomerCityCode::getId, Function.identity()));
            }
            //获客助手城市区域码
            List<Long> customerAcquisitionCityCodeIds = records.stream()
                .filter(a -> Objects.nonNull(a.getWechatCustomerAcquisitionCityCodeId()))
                .map(e -> e.getWechatCustomerAcquisitionCityCodeId()).collect(Collectors.toList());
            Map<Long, LandingPageWechatCustomerCityCode> customerAcquisitionCityCodeMap = Maps.newHashMap();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(customerAcquisitionCityCodeIds)) {
                customerAcquisitionCityCodeMap = landingPageWechatCustomerCityCodeService.listByIds(customerAcquisitionCityCodeIds)
                    .stream()
                    .collect(Collectors.toMap(LandingPageWechatCustomerCityCode::getId, Function.identity()));
            }
            ArrayList<LandingPageReviewStatus> landingPageChannelError = Lists.newArrayList(LandingPageReviewStatus.PRELIMINARY_AUDIT, LandingPageReviewStatus.AUDIT_FAILURE);
            for (LandingPageListVo re : records) {
                DynamicVo dynamicVo = new DynamicVo();
                dynamicVo.setQrCodeUrl(re.getQrCodeUrl());
                Long wechatCustomerServiceGroupId = re.getWechatCustomerServiceGroupId();
                if (!ObjectUtils.isEmpty(wechatCustomerServiceGroupId)) {
                    dynamicVo.setWechatCustomerServiceGroupId(wechatCustomerServiceGroupId);
                    List<LandingPageWechatCustomerServiceGroup> groups = listMap.get(wechatCustomerServiceGroupId);
                    dynamicVo.setWechatCustomerServiceGroupName(CollectionUtils.isEmpty(groups) ? null : groups.get(0)
                        .getName());
                    //客服分组页面内加粉动态渠道二维码 加上落地页自动打标签配置
                    dynamicVo.setUseWechatCustomerContact(Convert.toBool(re.getUseWechatCustomerContact()));
                    dynamicVo.setAutoMarkEnterpriseWechatTag(re.getAutoMarkEnterpriseWechatTag())
                        .setEnterpriseWechatTags(re.getEnterpriseWechatTags());
                    dynamicVo.setShowFullScreenCustomerContact(re.getShowFullScreenCustomerContact());
                    dynamicVo.setClickFullScreenCustomerContact(re.getClickFullScreenCustomerContact());
                    dynamicVo.setShowCustomerServiceQrCode(re.getShowCustomerServiceQrCode())
                        .setFixedContactCodeFlag(re.getFixedContactCodeFlag());
                }
                Long wechatOfficialAccountId = re.getWechatOfficialAccountId();
                if (!ObjectUtils.isEmpty(wechatOfficialAccountId)) {
                    dynamicVo.setWechatOfficialAccountId(wechatOfficialAccountId);
                    LandingPageWechatOfficialAccount officialAccount = officialAccountMap.get(wechatOfficialAccountId);
                    dynamicVo.setWechatOfficialAccountName(officialAccount == null ? null : officialAccount.getNickName());
                    // 1.222.0 企业推公众号二维码过期时间
                    if (LandingPageType.QIYETUI_APPLET.equals(re.getLandingPageType())) {
                        String qrCodeExpireStr = re.getQrCodeExpireStr();
                        dynamicVo.setQrCodeExpireStr(qrCodeExpireStr);
                    }
                }
                List<DynamicWechatOfficialAccountVo> dynamicWechatOfficialAccounts = Lists.newArrayList();
                Long[] dynamicWechatOfficialAccountIdArray = re.getDynamicWechatOfficialAccountIds();
                if (dynamicWechatOfficialAccountIdArray != null && dynamicWechatOfficialAccountIdArray.length > 0) {
                    for (Long dynamicWechatOfficialAccountId : dynamicWechatOfficialAccountIdArray) {
                        DynamicWechatOfficialAccountVo officialAccountVo = new DynamicWechatOfficialAccountVo();
                        officialAccountVo.setDynamicWechatOfficialAccountId(dynamicWechatOfficialAccountId);
                        LandingPageWechatOfficialAccount officialAccount = officialAccountMap.get(dynamicWechatOfficialAccountId);
                        officialAccountVo.setDynamicWechatOfficialAccountName(officialAccount == null ? null : officialAccount.getNickName());
                        dynamicWechatOfficialAccounts.add(officialAccountVo);
                    }
                    String qrCodeExpireStr = re.getQrCodeExpireStr();
                    dynamicVo.setQrCodeExpireStr(qrCodeExpireStr);
                    dynamicVo.setQrCodeExpireShowFixed(re.getQrCodeExpireShowFixed());
                    dynamicVo.setDisableWechatUserRepeatFollow(re.getDisableWechatUserRepeatFollow());
                    dynamicVo.setJudgmentDimensionType(re.getJudgmentDimensionType());
                    dynamicVo.setDays(re.getDays());
                    dynamicVo.setDataFilteringType(re.getDataFilteringType());
                    dynamicVo.setDataFilteringPmpType(re.getDataFilteringPmpType());
                    dynamicVo.setDataFilteringPmpIds(re.getDataFilteringPmpIds());
                }
                dynamicVo.setDynamicWechatOfficialAccounts(dynamicWechatOfficialAccounts);
                dynamicVo.setNonAdSceneSettings(re.getNonAdSceneSettings())
                    .setPurpose(re.getPurpose())
                    .setSceneSettings(re.getSceneSettings())
                    .setNoAdSceneAction(re.getNoAdSceneAction());
                Long noAdSceneWechatCustomerServiceGroupId = re.getNoAdSceneWechatCustomerServiceGroupId();
                if (!ObjectUtils.isEmpty(noAdSceneWechatCustomerServiceGroupId)) {
                    dynamicVo.setNoAdSceneWechatCustomerServiceGroupId(noAdSceneWechatCustomerServiceGroupId);
                    List<LandingPageWechatCustomerServiceGroup> groups = listMap.get(noAdSceneWechatCustomerServiceGroupId);
                    dynamicVo.setNoAdSceneWechatCustomerServiceGroupName(CollectionUtils.isEmpty(groups) ? null : groups.get(0)
                        .getName());
                }
                re.setDynamicVo(dynamicVo);
                //城市区域码数据
                Long landingPageWechatCustomerCityCodeId = re.getLandingPageWechatCustomerCityCodeId();
                if (!ObjectUtils.isEmpty(landingPageWechatCustomerCityCodeId)) {
                    LandingPageWechatCustomerCityCode landingPageWechatCustomerCityCode = landingPageWechatCustomerCityCodeMap.get(landingPageWechatCustomerCityCodeId);
                    Boolean bool = Convert.toBool(re.getUseCityWechatCustomerContact());
                    dynamicVo.setAutoMarkEnterpriseWechatTag(re.getAutoMarkEnterpriseWechatTag())
                        .setEnterpriseWechatTags(re.getEnterpriseWechatTags());
                    dynamicVo.setLandingPageWechatCustomerCityCodeId(landingPageWechatCustomerCityCodeId)
                        .setLandingPageWechatCustomerCityCodeName(landingPageWechatCustomerCityCode.getName())
                        .setUseCityWechatCustomerContact(bool);
                }
                //1.197.0 对应上边的微信群分组名称
                Long wechatGroupChatGroupId = re.getWechatGroupChatGroupId();
                if (!ObjectUtils.isEmpty(wechatGroupChatGroupId)) {
                    LandingPageWechatGroupChatGroup enterpriseWechatGroupChat = landingPageWechatGroupChatGroupMap.get(wechatGroupChatGroupId);
                    dynamicVo.setWechatGroupChatGroupId(wechatGroupChatGroupId)
                        .setWechatGroupChatGroupName(enterpriseWechatGroupChat.getName());
                }
//                城市区域码下的企业微信群
                Long groupChatCityCodeId = re.getLandingPageWechatGroupChatCityCodeId();
                if (!ObjectUtils.isEmpty(groupChatCityCodeId)) {
                    LandingPageWechatCustomerCityCode landingPageWechatCustomerCityCode = landingPageWechatGroupChatCityCodeMap.get(groupChatCityCodeId);
                    dynamicVo.setLandingPageWechatGroupChatCityCodeId(groupChatCityCodeId)
                        .setLandingPageWechatGroupChatCityCodeName(landingPageWechatCustomerCityCode.getName());
                }
                Long wechatCustomerAcquisitionCityCodeId = re.getWechatCustomerAcquisitionCityCodeId();
                if (Objects.nonNull(wechatCustomerAcquisitionCityCodeId)) {
                    LandingPageWechatCustomerCityCode landingPageWechatCustomerCityCode = customerAcquisitionCityCodeMap.get(wechatCustomerAcquisitionCityCodeId);
                    dynamicVo.setWechatCustomerAcquisitionCityCodeId(wechatCustomerAcquisitionCityCodeId)
                        .setWechatCustomerAcquisitionCityCodeName(landingPageWechatCustomerCityCode.getName())
                        .setAutoMarkEnterpriseWechatTag(re.getAutoMarkEnterpriseWechatTag())
                        .setEnterpriseWechatTags(re.getEnterpriseWechatTags());
                }
                //这里设置落地页是否可以编辑
                if (LandingPageType.AIP.equals(re.getLandingPageType())) {
                    //这里有两个判断，一个是审核状态，一个是校验状态
                    List<ChannelStatusDto> channelStatusByLandingPage = channelStatusDtos.get(re.getId());
                    if (CollectionUtils.isEmpty(channelStatusByLandingPage)) {
                        re.setReview(LandingPageReviewStatus.NOT_AUDIT);
                    } else {
                        for (int i = 0; i < channelStatusByLandingPage.size(); i++) {
                            ChannelStatusDto channelStatusDto = channelStatusByLandingPage.get(i);
                            if (channelStatusDto.getValidateStatus()
                                .equals(MarketingLandingPageValidateStatus.UN_VALIDATE) && StringUtils.isNotBlank(channelStatusDto.getAccountId())) {
                                re.setReview(LandingPageReviewStatus.IN_THE_REVIEW);
                                break;
                            }
                            //判断完校验状态，再判断审核状态
                            //审核通过或审核中的状态，标识为不可编辑
                            if (channelStatusDto.getAuditStatus()
                                .equals(MarketingLandingPageAuditStatus.AUDITING) || channelStatusDto.getAuditStatus()
                                .equals(MarketingLandingPageAuditStatus.AUDIT_ACCEPTED)) {
                                re.setReview(LandingPageReviewStatus.IN_THE_REVIEW);
                                break;
                            }
                            re.setReview(LandingPageReviewStatus.NOT_AUDIT);
                        }
                    }
                } else if (LandingPageType.QIYETUI_APPLET.equals(re.getLandingPageType())) {
                    if (Objects.nonNull(longListMap)) {
                        List<ChannelStatusDto> statusDtos = longListMap.get(re.getId());
                        if (!CollectionUtils.isEmpty(statusDtos)) {
                            List<ChannelStatusDto> collect = statusDtos.stream()
                                .filter(e -> landingPageChannelError.contains(e.getReview())).collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(collect)) {
                                re.setQiyetuiChannelError(true);
                            }
                        }
                    }
                }
            }
        }
        return vo;
    }


    /**
     * 落地页统计clickHouse查询
     *
     * @param page
     * @param landingPageQuery
     * @return
     */
    public IPage<LandingPageListVo> pagelist(IPage<LandingPage> page, LandingPageQuery landingPageQuery) {

        long total = pageViewEventInfoService.pageTotal(landingPageQuery);
        long current = page.getCurrent();
        long size = page.getSize();
        //自行查询落地页总数
        Page<LandingPage> landingPagePage = new Page<>(current, size, false);
        //查询落地页总数
        long totalPage = (total + size - 1) / size;
        landingPagePage.setPages(totalPage);
        landingPagePage.setTotal(total);
        //查询日期范围是否包含昨天今天，不包含则直接查询天聚合统计表
        Date startTime = Date.from(landingPageQuery.getStartTime());
        Date endTime = Date.from(landingPageQuery.getEndTime());
        DateTime date = DateUtil.date();
        DateTime yesterday = DateUtil.yesterday();
        DateTime beginYesterDay = DateUtil.beginOfDay(yesterday);
        DateTime nowEnd = DateUtil.endOfDay(date);
        IPage<LandingPageListVo> pagelist = null;
        //把昨天到今天的时间当成一个整体a，如果时间范围包含a那么就统计表查开始时间到前天23:59:59，实时数据表就聚合昨天凌晨到当前的数据进行联合查询
        //如果时间范围在昨天之前，那么只查询天聚合表
        //如果时间范围在a范围内，那么就只查询实时表
        //结束时间比较昨日凌晨00:00:00
        if (DateUtil.compare(endTime, beginYesterDay) <= 0) {
            //时间范围在昨天之前，那么只查询天聚合表
            pagelist = landingPageDayStatisticsService.pagelist(landingPagePage, landingPageQuery);
        }
        //开始时间小于等于昨天凌晨  结束时间大于等于今天23:59：59
        else if (DateUtil.compare(beginYesterDay, startTime) <= 0 && DateUtil.compare(nowEnd, endTime) >= 0) {
            //只查询实时表的数据 参数开始时间 - 今天23:59:59
            pagelist = pageViewEventInfoService.pagelist(landingPagePage, landingPageQuery);
        } else {
            //跨天包含昨天今天 进行联合查询,天统计表只查询到前天晚上23:59:59之前,实时数据查询昨天到参数结束时间
            pagelist = pageViewEventInfoService.pagelistTranssky(landingPagePage, landingPageQuery);
        }
        pagelist.setTotal(total);

        //查询pgsql落地页主表
        if (!CollectionUtils.isEmpty(pagelist.getRecords())) {
            List<LandingPageListVo> records = pagelist.getRecords();
            List<Long> ids = records.stream().map(LandingPageListVo::getId).collect(Collectors.toList());
            List<LandingPage> landingPages = listByIds(ids);
            Map<Long, LandingPage> collect = landingPages.stream()
                .collect(Collectors.toMap(LandingPage::getId, Function.identity()));
            CopyOptions copyOptions = new CopyOptions().setIgnoreNullValue(true);
            records.forEach(e -> {
                LandingPage landingPage = collect.get(e.getId());
                BeanUtil.copyProperties(landingPage, e, copyOptions);
                if (StringUtils.equals("false", e.getUseWechatCustomerContact())) {
                    e.setUseWechatCustomerContact("f");
                } else if (StringUtils.equals("true", e.getUseWechatCustomerContact())) {
                    e.setUseWechatCustomerContact("t");
                }
            });
        }
        return pagelist;
    }

    /**
     * 获取落地页列表总计数据
     *
     * @return 查询结果
     */
    public LandingPageListVo totalData(LandingPageQuery landingPageQuery) {
        //数据固化增加新版本，根据配置切换新旧版本查询
//        LandingPageListVo landingPageListVo = landingPageNewSearchConfig.getLandingPageNewSearch() ? landingPageMapper.totalDataNew(landingPageQuery) : landingPageMapper.totalData(landingPageQuery);
        LandingPageListVo landingPageListVo;
        boolean openFlag = this.checkFlag(landingPageQuery.getNewSearchFlag());
        log.info("获取落地页列表总计数据,新旧查询标识 newSearchFlag={},校验结果:{}", landingPageQuery.getNewSearchFlag(), openFlag);
        if (openFlag) {
            //clickhouse 时间精度问题，需要设置的是秒，最后一秒钟数据都归属到 59秒了。不需要重新格式化时间
            landingPageListVo = totalDataNew(landingPageQuery);
        } else {
            landingPageQuery.initTime();
            landingPageListVo = landingPageMapper.totalData(landingPageQuery);
        }

        List<LandingPage> list = landingPageMapper.listLandingPageByCondition(landingPageQuery);
        if (CollectionUtils.isEmpty(list) || landingPageListVo == null) {
            return new LandingPageListVo();
        }
        // 再从clickhouse中查询重复访客数，访客数，重复访客率
        // TODO 重复访客数
        LandingPageListVo visit = null; //pageViewInfoService.getTotalVisitData(landingPageQuery, list);
        if (visit != null) {
            landingPageListVo.setVisitorNum(visit.getVisitorNum());
            landingPageListVo.setRepeatVisitorNum(visit.getRepeatVisitorNum());
            landingPageListVo.setRepeatVisitorRate(visit.getRepeatVisitorRate());
        }
        return landingPageListVo;
    }

    /**
     * 判断新旧查询开关的标识
     *
     * @param newSearchFlag 入参
     * @return true标识新查询，false走旧查询
     */
    public Boolean checkFlag(String newSearchFlag) {
        if (StringUtils.isBlank(newSearchFlag)) {
            return landingPageNewSearchConfig.getLandingPageNewSearch();
        } else {
            return Objects.equals(LandingPageConstant.newSearchFlag, newSearchFlag);
        }
    }

    //1.219.0 总计查询
    public LandingPageListVo totalDataNew(LandingPageQuery landingPageQuery) {
        //查询日期范围是否包含昨天今天，不包含则直接查询天聚合统计表
        Date startTime = Date.from(landingPageQuery.getStartTime());
        Date endTime = Date.from(landingPageQuery.getEndTime());
        DateTime date = DateUtil.date();
        DateTime yesterday = DateUtil.yesterday();
        DateTime beginYesterDay = DateUtil.beginOfDay(yesterday);
        DateTime nowEnd = DateUtil.endOfDay(date);
        LandingPageListVo landingPagePage = null;
        //把昨天到今天的时间当成一个整体a，如果时间范围包含a那么就统计表查开始时间到前天23:59:59，实时数据表就聚合昨天凌晨到当前的数据进行联合查询
        //如果时间范围在昨天之前，那么只查询天聚合表
        //如果时间范围在a范围内，那么就只查询实时表
        //结束时间比较昨日凌晨00:00:00
        if (DateUtil.compare(endTime, beginYesterDay) <= 0) {
            //查询范围为昨天之前,使用天统计表
            landingPagePage = landingPageDayStatisticsService.pagelistTotal(landingPageQuery);
        } else if (DateUtil.compare(beginYesterDay, startTime) <= 0 && DateUtil.compare(nowEnd, endTime) >= 0) {
            //参数开始时间小于等于昨天凌晨  结束时间大于等于今天23:59：59
            //只查询实时表的数据 参数开始时间 - 今天23:59:59
            landingPagePage = pageViewEventInfoService.pagelistTotal(landingPageQuery);
        } else {
            //跨天包含今天 进行联合查询
            landingPagePage = pageViewEventInfoService.pagelistTransskyTotal(landingPageQuery);
        }
        return landingPagePage;
    }

    /**
     * 落地页列表
     *
     * @param page         分页信息
     * @param queryWrapper 条件信息
     * @param isGroupTop   是否分组置顶
     */
    public IPage<LandingPage> pageList(IPage<LandingPage> page, QueryWrapper<LandingPage> queryWrapper,
                                       Boolean isGroupTop) {
        return landingPageMapper.pageList(page, queryWrapper, isGroupTop);
    }

    /**
     * 返回默认域名的连接
     **/
    public LandingPageUrlVo getDefaultLink(Long advertiserAccountId, String type, Long advertiserAccountGroupId) {
        LandingPageUrlVo defaultUrl = new LandingPageUrlVo();
        if (Objects.equals(type, EMP_TYPE)) {
            defaultUrl.setAdvertiserAccountId(advertiserAccountId);
        } else {
            defaultUrl.setAdvertiserAccountGroupId(advertiserAccountId);
        }
        String agentId = TenantContextHolder.get();
        String prefix;
        BossCustomerDomain bossCustomer = bossBackendRemote.getBossCustomerDomain(agentId);
        List<LandingPageDomainBinding> list = landingPageDomainBindingService.listEnableDomain(advertiserAccountGroupId);
        String httpsStatus = bossCustomer.getRequestType().getName();
        if (!CollectionUtils.isEmpty(list)) {
            // 如果有自定义域名，则返回第一条
            prefix = list.get(0).getProtocolType().getName() + list.get(0).getLandingPageDomain() + "/" + agentId;
        } else if (bossCustomer.getLandingPageDomain() != null) {
            prefix = httpsStatus + (bossCustomer.isSaasEnv() ? agentId + "." : "") + bossCustomer.getLandingPageDomain() + "/" + agentId;
        } else {
            BossLandingPageDomain bossLandingPageDomain = bossBackendRemote.getDefaultSystemDomain();
            prefix = httpsStatus + agentId + "." + bossLandingPageDomain.getDomain() + "/" + agentId;
        }
        defaultUrl.setName(LandingPageChannelType.CURRENCY.getName());
        defaultUrl.setLandingPageChannelType(LandingPageChannelType.CURRENCY);
        defaultUrl.setUrl(prefix);
        return defaultUrl;
    }

    /**
     * 拼接落地页链接的uri
     *
     * @param landingPage       落地页信息
     * @param landingPageUrlVos 落地页url信息
     */
    public void concatToken(LandingPage landingPage, List<LandingPageUrlVo> landingPageUrlVos) {
        String token = "/" + landingPage.getToken();
        landingPageUrlVos.forEach(l -> {
            String domain = l.getUrl();
            if (!domain.startsWith(ProtocolTypeEnum.HTTPS.getName()) && !domain.startsWith(httpStatus)) {
                domain = httpStatus + domain;
            }
            l.setUrl(domain + token);
        });
    }

    /**
     * 校验落地页是否重复
     *
     * @param name 投放账户名称
     */
    public void verifyRepeated(String name, Long advertiserAccountGroupId) {
        if (null == advertiserAccountGroupId || 0 == advertiserAccountGroupId) {
            throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_PARAM_ADVERTISERACCOUNTGROUPID_IS_NOT_NULL);
        }
        //校验是否重复
        int count = super.count(new LambdaQueryWrapper<LandingPage>().eq(LandingPage::getName, name)
            .eq(LandingPage::getStatus, 1).eq(LandingPage::getAdvertiserAccountGroupId, advertiserAccountGroupId));
        if (count <= 0) {
            return;
        }
        throw new RestException(ErrorConstants.ERROR_LANDING_NAME_REPEATED);
    }

    public LandingPage getOne(@PathVariable Long id) {
        LandingPage landingPage = landingPageMapper.selectById(id);
        List<LandingPageCustomCode> customCodeList = landingPageCustomCodeMapper.selectList(new LambdaQueryWrapper<LandingPageCustomCode>().eq(LandingPageCustomCode::getLandingPageId, id));
        landingPage.setCustomCode(customCodeList);
        List<LandingPageStrategy> strategyList = strategyService.list(new LambdaQueryWrapper<LandingPageStrategy>().eq(LandingPageStrategy::getLandingPageId, id)
            .orderByAsc(LandingPageStrategy::getCreatedAt));
        landingPage.setLandingPageStrategys(strategyList);
        // 查看域名
        List<LandingPageUrlVo> landingPageHosts = new ArrayList<LandingPageUrlVo>();
        // TODO: 前端去拼通用地址
        landingPageHosts.add(this.getDefaultLink(Objects.nonNull(landingPage.getAdvertiserAccountGroupId()) ? null : landingPage.getAdvertiserAccountId(), Objects.nonNull(landingPage.getAdvertiserAccountGroupId()) ? EMP_TYPE : PMP_TYPE, landingPage.getAdvertiserAccountGroupId()));
        this.concatToken(this.getById(id), landingPageHosts);
        landingPage.setLandingPageHosts(landingPageHosts);
        return landingPage;
    }

    public String offline(String contentStr) {

        if (StringUtils.isBlank(contentStr)) {
            return contentStr;
        }
        JSONArray content = JSON.parseArray(contentStr);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(content)) {
            return contentStr;
        }
        JSONArray contentEnd = new JSONArray();
        content.forEach(item -> {
            JSONObject itemContent = JSONObject.parseObject(item.toString());
            String name = itemContent.getString("name");
            FunctionStatus functionStatus = FunctionStatus.ONLINE;
            switch (name) {
                // 动态二维码组件
                case "DynamicQRcode":
                    JSONObject dynamicQRcodeModel = itemContent.getJSONObject("model");
                    String imageSource = dynamicQRcodeModel.getString("imageSource");
                    if (DynamicQRcodeType.LOCAL_UPLOAD.getCode().equals(imageSource)) {
                        //1.195.0 校验是否已经下线 -- 已经下线的功能不在渲染
                        if (systemFunctionService.offline(imageSource)) {
                            functionStatus = FunctionStatus.OFFLINE;
                        }
                    }
                    break;
            }
            if (FunctionStatus.ONLINE.equals(functionStatus)) {
                contentEnd.add(itemContent);
            }
        });
        if (contentEnd.size() == 0) {
            return "";
        }
        return contentEnd.toJSONString();
    }

}
