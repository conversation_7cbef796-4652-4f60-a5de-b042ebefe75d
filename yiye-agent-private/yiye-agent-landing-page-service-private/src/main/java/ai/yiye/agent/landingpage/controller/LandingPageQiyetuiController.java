package ai.yiye.agent.landingpage.controller;

import ai.yiye.agent.domain.vo.PageViewInfoVO;
import ai.yiye.agent.landingpage.config.LandingPageSynConfig;
import ai.yiye.agent.landingpage.service.LandingPageQiyetuiService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author：lilidong
 * @Date：2023/5/29 14:49
 */

@RestController
@RequestMapping("/qiyetui-link-type")
public class LandingPageQiyetuiController {

    @Resource
    private LandingPageQiyetuiService landingPageQiyetuiService;

    @Resource
    private LandingPageSynConfig landingPageSynConfig;


    /**
     * 判断落地页是否属于企业推链路
     * @param landingPageId 落地页页面ID
     * @return 判断结果
     */
    @GetMapping("/check-landing-page-link-type")
    public Boolean checkLandingPageLinkType(Long landingPageId,String agentId){
        if (landingPageSynConfig.getSynOpenFlag()) {
            return (Objects.nonNull(landingPageId) && StringUtils.isNotBlank(agentId)) ? landingPageQiyetuiService.checkLandingPageIsQiyetui(landingPageId, agentId) : false;
        }else {
            return false;
        }
    }

    @GetMapping("/getUrlByPid")
    PageViewInfoVO getUrlByPid(@RequestParam("pid") String pid, @RequestParam("agentId") String agentId){
        return landingPageSynConfig.getSynOpenFlag() ? landingPageQiyetuiService.getUrlByPid(pid,agentId) : null;
    }
}
