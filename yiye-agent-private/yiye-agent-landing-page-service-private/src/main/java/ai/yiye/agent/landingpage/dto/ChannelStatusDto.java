package ai.yiye.agent.landingpage.dto;

import ai.yiye.agent.domain.enumerations.LandingPageChannelType;
import ai.yiye.agent.domain.enumerations.LandingPageReviewStatus;
import ai.yiye.agent.domain.enumerations.MarketingLandingPageAuditStatus;
import ai.yiye.agent.domain.enumerations.MarketingLandingPageValidateStatus;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/2 16:17
 */
@Data
public class ChannelStatusDto implements Serializable {
    //落地页审核状态
    private MarketingLandingPageAuditStatus auditStatus;
    //落地页校验状态
    private MarketingLandingPageValidateStatus validateStatus;
    private String accountId;

    private Long landingPageId;

    /**
     * 落地页【渠道类型】
     */
    private LandingPageChannelType landingPageChannelType;

    /**
     * 企业推渠道状态
     */
    private LandingPageReviewStatus review;
}
