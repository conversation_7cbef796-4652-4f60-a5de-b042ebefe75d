package ai.yiye.agent.landingpage.service;


import ai.yiye.agent.autoconfigure.mybatis.multidatasource.service.BaseAdvertiserAccountGroupService;
import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.redis.annotation.RedisDS;
import ai.yiye.agent.autoconfigure.web.exception.ErrorConstants;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.*;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.landingpage.dto.LandingPageGenerateDTO;
import ai.yiye.agent.domain.landingpage.dto.LandingPageShowDto;
import ai.yiye.agent.landingpage.config.PageViewConf;
import ai.yiye.agent.landingpage.dto.LandingPageWidgetTemplateDTO;
import ai.yiye.agent.landingpage.mapper.PingApplicationMapper;
import ai.yiye.agent.landingpage.mapper.WechatMerchantAccountMapper;
import ai.yiye.agent.landingpage.redis.LandingPageRedis;
import ai.yiye.agent.landingpage.remote.LandingPageGenerateRemote;
import ai.yiye.agent.landingpage.remote.PaymentPingRemote;
import ai.yiye.agent.landingpage.remote.WechatMerchantAccountRemote;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Service
@DS("postgresql")
public class LandingPageGenerateService {

    public static final String UPDATE_LANIND_PAGE_URL = "/generate/landing-pages";
    public static final String UPDATE_LANIND_PAGE_PREVIEW_URL = "/generate/landing-pages/preview";

    @Resource
    private LandingPageService landingPageService;

    @Autowired
    private RedisTemplate<String, Object> defaultObjectRedisTemplate;
    @Autowired
    private RedisTemplate<String, byte[]> defaultBytesRedisTemplate;
    @Autowired
    @Qualifier("defaultShowRedisTemplate")
    private RedisTemplate<String, Object> defaultShowRedisTemplate;

    @Resource
    private PageViewConf pageViewConf;
    @Resource
    private LandingPageWidgetTemplateService widgetTemplateService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RedisTemplate<String, Object> fileRedisTemplate;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private PaymentPingRemote paymentPingRemote;
    @Resource
    private PingApplicationMapper pingApplicationMapper;
    @Resource
    private WechatMerchantAccountRemote wechatMerchantAccountRemote;
    @Resource
    private WechatMerchantAccountMapper wechatMerchantAccountMapper;
    @Autowired
    private LandingPageChannelService landingPageChannelService;
    @Autowired
    private LandingPageWidgetTemplateRelService landingPageWidgetTemplateRelService;
    @Autowired
    private UploadConfigurationService uploadConfigurationService;
    @Autowired
    private LandingPageCustomCodeService landingPageCustomCodeService;
    @Autowired
    private LandingPageStrategyService strategyService;
    @Autowired
    private LandingPageGenerateRemote landingPageGenerateRemote;
    @Autowired
    private MarketingLandingPageService marketingLandingPageService;
    @Autowired
    private LandingPageWidgetTemplateService landingPageWidgetTemplateService;
    @Autowired
    private LandingPagePopupWidgetTemplateRelService landingPagePopupWidgetTemplateRelService;
    @Autowired
    private LandingPagePreviewService landingPagePreviewService;
    @Autowired
    private LandingPageGenerateService landingPageGenerateService;
    @Autowired
    private LandingPageStrategyPreviewService landingPageStrategyPreviewService;
    @Autowired
    private LandingPageWidgetTemplatePreviewRelService landingPageWidgetTemplatePreviewRelService;
    @Autowired
    private AgentConfService agentConfService;
    @Autowired
    private WechatOfficialAccountService wechatOfficialAccountService;
    @Autowired
    private SystemFunctionService systemFunctionService;
    @Autowired
    private BaseAdvertiserAccountGroupService baseAdvertiserAccountGroupService;
    @Autowired
    private LandingPageRedis landingPageRedis;



    public static boolean wechatPayCheck(String content) {
        boolean check = false;
        JSONArray jsonArray = JSONArray.parseArray(content);

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = (JSONObject) jsonArray.get(i);
            if (StringUtils.equals("OrderPaymentMethod", (String) jsonObject.get("name"))) {
                JSONObject model = (JSONObject) jsonObject.get("model");
                JSONArray modes = (JSONArray) model.get("mode");
                for (int j = 0; j < modes.size(); j++) {
                    JSONObject mode = (JSONObject) modes.get(j);
                    if (StringUtils.equals("微信支付", mode.getString("name"))) {
                        check = mode.getBoolean("checked");
                    }
                }
            }
        }
        return check;
    }

    public boolean generateLandingPageContent(LandingPageGenerateDTO generateDTO) {
        //设置落地页相关参数
        wechatOfficialAccountService.optionsLandingPageRelatedParam(generateDTO);
        //添加订单相关的公众号等信息
        JSONArray sufContnet = new JSONArray();
        if (null == generateDTO.getContent() || "".equals(generateDTO.getContent().trim())) {
            generateDTO.setContent(sufContnet.toJSONString());
            return true;
        }
        JSONArray content = JSON.parseArray(generateDTO.getContent());
        if (null == content || content.isEmpty()) {
            generateDTO.setContent(sufContnet.toJSONString());
            return true;
        }
        content.forEach(item -> {
            JSONObject itemContent = JSONObject.parseObject(item.toString());
            String name = itemContent.getString("name");
            FunctionStatus functionStatus = FunctionStatus.ONLINE;
            switch (name) {
                case "Form":
                    LandingPageWidgetTemplateDTO formRelDto = new LandingPageWidgetTemplateDTO();
                    LandingPageWidgetTemplate form = landingPageWidgetTemplateService.getInfoById((long) itemContent.getJSONObject("model").getInteger("form"));
                    LandingPageWidgetTemplateRel formRel = landingPageWidgetTemplateRelService.getOne(new QueryWrapper<LandingPageWidgetTemplateRel>().eq("landing_page_id", generateDTO.getId()).eq("landing_page_widget_template_id", form.getId()).eq("uuid", itemContent.getString("uuid")));
                    BeanUtils.copyProperties(formRel, formRelDto);
                    BeanUtils.copyProperties(form, formRelDto);
                    JSONObject formModel = itemContent.getJSONObject("model");
                    formModel.put("content", formRelDto.toJSON());
                    itemContent.put("model", formModel);
                    break;
                case "OrderWidget":
                    LandingPageWidgetTemplateDTO orderFromRelDto = new LandingPageWidgetTemplateDTO();
                    LandingPageWidgetTemplate orderForm = landingPageWidgetTemplateService.getInfoById((long) itemContent.getJSONObject("model").getInteger("orderTemplate"));
                    LandingPageWidgetTemplateRel orderRel = landingPageWidgetTemplateRelService.getOne(new QueryWrapper<LandingPageWidgetTemplateRel>().eq("landing_page_id", generateDTO.getId()).eq("landing_page_widget_template_id", orderForm.getId()).eq("uuid", itemContent.getString("uuid")));
                    JSONObject orderModel = itemContent.getJSONObject("model");
                    if (wechatPayCheck(orderForm.getContent()) && checkMerchant(orderForm.getExt().getInteger("merchantId"))) {
                        orderModel.put("canWechatOrder", true);
                    }
                    BeanUtils.copyProperties(orderRel, orderFromRelDto);
                    BeanUtils.copyProperties(orderForm, orderFromRelDto);
                    JSONObject ext = orderForm.getExt();
                    if (ext.get("merchantId") != null) {
                        Long merchantId = Long.parseLong(ext.get("merchantId").toString());
                        WechatMerchantAccount wechatMerchantAccount = wechatMerchantAccountRemote.fetchMerchant(merchantId);
                        orderFromRelDto.setWechatMerchantAccount(wechatMerchantAccount);
                    }
                    if (Objects.nonNull(orderFromRelDto.getPingAppId())) {
                        orderFromRelDto.setPingApplication(paymentPingRemote.fetchPaymentByApplicationId(orderFromRelDto.getPingAppId()));
                    }
                    orderModel.put("content", orderFromRelDto.toJSON());
                    itemContent.put("model", orderModel);
                    break;
                case "CommentWidget":
                    LandingPageWidgetTemplateDTO commentRelDto = new LandingPageWidgetTemplateDTO();
                    LandingPageWidgetTemplate comment = landingPageWidgetTemplateService.getInfoById((long) itemContent.getJSONObject("model").getInteger("commentTemplate"));
                    LandingPageWidgetTemplateRel commentRel = landingPageWidgetTemplateRelService.getOne(new QueryWrapper<LandingPageWidgetTemplateRel>().eq("landing_page_id", generateDTO.getId()).eq("landing_page_widget_template_id", comment.getId()).eq("uuid", itemContent.getString("uuid")));
                    BeanUtils.copyProperties(commentRel, commentRelDto);
                    BeanUtils.copyProperties(comment, commentRelDto);
                    JSONObject commontModel = itemContent.getJSONObject("model");
                    commontModel.put("content", commentRelDto.toJSON());
                    itemContent.put("model", commontModel);
                    break;
                case "Splice":
                    itemContent = assemblyDataItemContent(itemContent, generateDTO);
                    break;
                case "LogicalAtlas":
                    LandingPageWidgetTemplateDTO logicalRelDto = new LandingPageWidgetTemplateDTO();
                    log.info("----------------------------dataSource{}===============logicalAtlas{}", TenantContextHolder.get(), itemContent.getJSONObject("model").getInteger("logicalAtlas"));
                    if (itemContent.getJSONObject("model").getInteger("logicalAtlas") == null) {
                        break;
                    }
                    LandingPageWidgetTemplate logical = landingPageWidgetTemplateService.getInfoById((long) itemContent.getJSONObject("model").getInteger("logicalAtlas"));
                    log.info("----------------------------logical{}", JSON.toJSONString(logical));
                    if (logical != null) {
                        //图片组件实现的逻辑问答，没有rel表
                        LandingPageWidgetTemplateRel logicalRel = landingPageWidgetTemplateRelService.getOne(new QueryWrapper<LandingPageWidgetTemplateRel>().eq("landing_page_id", generateDTO.getId()).eq("landing_page_widget_template_id", logical.getId()).eq("uuid", itemContent.getString("uuid")));
                        if (logicalRel != null) {
                            BeanUtils.copyProperties(logicalRel, logicalRelDto);
                        }
                        BeanUtils.copyProperties(logical, logicalRelDto);
                    }
                    JSONObject logicalModel = itemContent.getJSONObject("model");
                    logicalModel.put("content", logicalRelDto.toJSON());
                    itemContent.put("model", logicalModel);
                    break;
                    // 动态二维码组件
                case "DynamicQRcode":
                    JSONObject dynamicQRcodeModel = itemContent.getJSONObject("model");
                    String imageSource = dynamicQRcodeModel.getString("imageSource");
                    if (DynamicQRcodeType.LOCAL_UPLOAD.getCode().equals(imageSource)) {
                        //1.195.0 校验是否已经下线 -- 已经下线的功能不在渲染
                        if (systemFunctionService.offline(imageSource)) {
                            functionStatus = FunctionStatus.OFFLINE;
                        }
                    }
                    break;
            }
            if(FunctionStatus.ONLINE.equals(functionStatus)){
                sufContnet.add(itemContent);
            }
        });
        generateDTO.setContent(sufContnet.toJSONString());
        log.info("landing-page:{}", JSON.toJSONString(generateDTO));
        return true;
    }

    private JSONObject assemblyDataItemContent(JSONObject itemContent, LandingPageGenerateDTO generateDTO) {
        log.info("提取拼接组件内容与表单订单的关系 item ====== >>{}", itemContent);
        JSONObject model = itemContent.getJSONObject("model");
        List<LandingPageWidgetTemplateDTO> landingPageWidgetTemplateDTOList = new ArrayList<>();
        List<LandingPageWidgetTemplateRel> landingPageWidgetTemplateRelList = new ArrayList<>();
        JSONArray childrenNew = new JSONArray();
        if (null != model && model.size() > 0) {
            JSONArray children = model.getJSONArray("children");
            for (Object childrenRow : children) {
                JSONObject rowJsonChildren = null == childrenRow ? null : (JSONObject) childrenRow;
                if (null != childrenRow && rowJsonChildren.size() > 0) {
                    JSONObject modelChildren = rowJsonChildren.getJSONObject("model");
                    String rowJsonChildrenName = rowJsonChildren.getString("name");
                    String uuid = rowJsonChildren.getString("id");
                    String parentId = rowJsonChildren.getString("parentId");
                    landingPageWidgetTemplateRelList.addAll(landingPageWidgetTemplateRelService.list(new LambdaQueryWrapper<LandingPageWidgetTemplateRel>().eq(LandingPageWidgetTemplateRel::getLandingPageId, generateDTO.getId()).eq(LandingPageWidgetTemplateRel::getUuid, uuid).eq(LandingPageWidgetTemplateRel::getParentUuid, parentId)));
                    //落地页 - 模板 - 组件 - 主键id
                    Long templateId = "Form".equals(rowJsonChildrenName) ? modelChildren.getLong("form") : ("OrderWidget".equals(rowJsonChildrenName) ? modelChildren.getLong("orderTemplate") : null);
                    if (null != templateId && 0 != templateId) {
                        LandingPageWidgetTemplate landingPageWidgetTemplateOrder = landingPageWidgetTemplateService.getInfoById(templateId);
                        LandingPageWidgetTemplateDTO orderFromRelDtoSplice = new LandingPageWidgetTemplateDTO();
                        BeanUtils.copyProperties(landingPageWidgetTemplateOrder, orderFromRelDtoSplice);
                        if (null != landingPageWidgetTemplateOrder.getApplicationId() && landingPageWidgetTemplateOrder.getApplicationId() != 0) {
                            if (PaymentType.WECHAT_PAY.toString().equals(orderFromRelDtoSplice.getPaymentType())) {
                                orderFromRelDtoSplice.setWechatMerchantAccount(wechatMerchantAccountMapper.selectById(landingPageWidgetTemplateOrder.getApplicationId()));
                                orderFromRelDtoSplice.setCanPay(true);
                            }
                            if (PaymentType.PING_PAY.toString().equals(orderFromRelDtoSplice.getPaymentType())) {
                                orderFromRelDtoSplice.setPingApplication(pingApplicationMapper.selectById(landingPageWidgetTemplateOrder.getApplicationId()));
                                orderFromRelDtoSplice.setCanPay(true);
                            }
                        }
                        landingPageWidgetTemplateDTOList.add(orderFromRelDtoSplice);
                        modelChildren.put("content", orderFromRelDtoSplice.toJSON());
                        rowJsonChildren.put("model", modelChildren);
                    }
                    log.info("model.children[0].model={}", modelChildren);
                }
                //重新赋值
                childrenNew.add(rowJsonChildren);
            }
            //重新赋值
            model.put("children", childrenNew);
        }
        //拼接组件内所有表单、订单详情数据
        itemContent.put("landingPageWidgetTemplateList", landingPageWidgetTemplateDTOList);
        //拼接组件与表单订单的关联关系
        itemContent.put("landingPageWidgetTemplateRelList", landingPageWidgetTemplateRelList);
        //重新赋值
        itemContent.put("model", model);
        return itemContent;
    }


    //落地页预览生成pageContent，preview
    public boolean previewLandingPageContent(LandingPageGenerateDTO generateDTO, String previewToken) {
        String agentId = TenantContextHolder.get();
        log.info("进入previewLandingPageContent方法了，{}", agentId);
        //设置落地页相关参数
        wechatOfficialAccountService.optionsLandingPageRelatedParam(generateDTO);
        //添加订单相关的公众号等信息
        JSONArray content = JSON.parseArray(generateDTO.getContent());
        JSONArray sufContnet = new JSONArray();
        content.forEach(item -> {
            JSONObject itemContent = JSONObject.parseObject(item.toString());
            String name = itemContent.getString("name");
            switch (name) {
                case "Form":
                    LandingPageWidgetTemplateDTO formRelDto = new LandingPageWidgetTemplateDTO();
                    LandingPageWidgetTemplate form = widgetTemplateService.getInfoById((long) itemContent.getJSONObject("model").getInteger("form"));
                    LandingPageWidgetTemplatePreviewRel formRel = landingPageWidgetTemplatePreviewRelService.getOne(new QueryWrapper<LandingPageWidgetTemplatePreviewRel>().eq("landing_page_id", generateDTO.getId()).eq("landing_page_widget_template_id", form.getId()).eq("uuid", itemContent.getString("uuid")).eq("preview_token", previewToken));
                    BeanUtils.copyProperties(formRel, formRelDto);
                    BeanUtils.copyProperties(form, formRelDto);
                    JSONObject formModel = itemContent.getJSONObject("model");
                    formModel.put("content", formRelDto.toJSON());
                    itemContent.put("model", formModel);
                    break;
                case "OrderWidget":
                    LandingPageWidgetTemplateDTO orderFromRelDto = new LandingPageWidgetTemplateDTO();
                    LandingPageWidgetTemplate orderForm = widgetTemplateService.getInfoById((long) itemContent.getJSONObject("model").getInteger("orderTemplate"));
                    LandingPageWidgetTemplatePreviewRel orderRel = landingPageWidgetTemplatePreviewRelService.getOne(new QueryWrapper<LandingPageWidgetTemplatePreviewRel>().eq("landing_page_id", generateDTO.getId()).eq("landing_page_widget_template_id", orderForm.getId()).eq("uuid", itemContent.getString("uuid")).eq("preview_token", previewToken));
                    JSONObject orderModel = itemContent.getJSONObject("model");
                    if (wechatPayCheck(orderForm.getContent()) && checkMerchant(orderForm.getExt().getInteger("merchantId"))) {
                        orderModel.put("canWechatOrder", true);
                    }
                    BeanUtils.copyProperties(orderRel, orderFromRelDto);
                    BeanUtils.copyProperties(orderForm, orderFromRelDto);
                    JSONObject ext = orderForm.getExt();
                    if (ext.get("merchantId") != null) {
                        Long merchantId = Long.parseLong(ext.get("merchantId").toString());
                        WechatMerchantAccount wechatMerchantAccount = wechatMerchantAccountRemote.fetchMerchant(merchantId);
                        orderFromRelDto.setWechatMerchantAccount(wechatMerchantAccount);
                    }
                    if (Objects.nonNull(orderFromRelDto.getPingAppId())) {
                        orderFromRelDto.setPingApplication(paymentPingRemote.fetchPaymentByApplicationId(orderFromRelDto.getPingAppId()));
                    }
                    orderModel.put("content", orderFromRelDto.toJSON());
                    itemContent.put("model", orderModel);
                    break;
                case "CommentWidget":
                    LandingPageWidgetTemplateDTO commentRelDto = new LandingPageWidgetTemplateDTO();
                    LandingPageWidgetTemplate comment = widgetTemplateService.getInfoById((long) itemContent.getJSONObject("model").getInteger("commentTemplate"));
                    LandingPageWidgetTemplatePreviewRel commentRel = landingPageWidgetTemplatePreviewRelService.getOne(new QueryWrapper<LandingPageWidgetTemplatePreviewRel>()
                            .eq("landing_page_id", generateDTO.getId())
                            .eq("landing_page_widget_template_id", comment.getId())
                            .eq("uuid", itemContent.getString("uuid"))
                            .eq("preview_token", previewToken));
                    BeanUtils.copyProperties(commentRel, commentRelDto);
                    BeanUtils.copyProperties(comment, commentRelDto);
                    JSONObject commontModel = itemContent.getJSONObject("model");
                    commontModel.put("content", commentRelDto.toJSON());
                    itemContent.put("model", commontModel);
                    break;
                case "Splice":
                    itemContent = assemblyDataItemContentPreview(itemContent, generateDTO, previewToken);
                    break;
                case "LogicalAtlas":
                    TenantContextHolder.set(agentId);
                    LandingPageWidgetTemplateDTO logicalRelDto = new LandingPageWidgetTemplateDTO();
                    if (itemContent.getJSONObject("model").getInteger("logicalAtlas") == null) {
                        break;
                    }
                    LandingPageWidgetTemplate logical = widgetTemplateService.getInfoById((long) itemContent.getJSONObject("model").getInteger("logicalAtlas"));
                    if (logical != null) {
                        LandingPageWidgetTemplatePreviewRel logicalRel = landingPageWidgetTemplatePreviewRelService.getOne(new QueryWrapper<LandingPageWidgetTemplatePreviewRel>().eq("landing_page_id", generateDTO.getId()).eq("landing_page_widget_template_id", logical.getId()).eq("preview_token", previewToken));
                        BeanUtils.copyProperties(logicalRel, logicalRelDto);
                        BeanUtils.copyProperties(logical, logicalRelDto);
                    }
                    JSONObject logicalModel = itemContent.getJSONObject("model");
                    logicalModel.put("content", logicalRelDto.toJSON());
                    itemContent.put("model", logicalModel);
            }
            sufContnet.add(itemContent);
        });
        generateDTO.setContent(sufContnet.toString());
        log.info("landing-page:{}", JSON.toJSONString(generateDTO));
        return true;
    }


    /**
     * 解析拼接组件 & 组装拼接组件所需数据
     */
    private JSONObject assemblyDataItemContentPreview(JSONObject itemContent, LandingPageGenerateDTO generateDTO, String previewToken) {
        log.info("提取拼接组件内容与表单订单的关系 item ====== >>{}", itemContent);
        JSONObject model = itemContent.getJSONObject("model");
        List<LandingPageWidgetTemplateDTO> landingPageWidgetTemplateDTOList = new ArrayList<>();
        List<LandingPageWidgetTemplatePreviewRel> landingPageWidgetTemplatePreviewRelList = new ArrayList<>();
        JSONArray childrenNew = new JSONArray();
        if (null != model && model.size() > 0) {
            JSONArray children = model.getJSONArray("children");
            for (Object childrenRow : children) {
                JSONObject rowJsonChildren = null == childrenRow ? null : (JSONObject) childrenRow;
                if (null != childrenRow && rowJsonChildren.size() > 0) {
                    JSONObject modelChildren = rowJsonChildren.getJSONObject("model");
                    String rowJsonChildrenName = rowJsonChildren.getString("name");
                    landingPageWidgetTemplatePreviewRelList.addAll(landingPageWidgetTemplatePreviewRelService.list(new LambdaQueryWrapper<LandingPageWidgetTemplatePreviewRel>().eq(LandingPageWidgetTemplatePreviewRel::getPreviewToken, previewToken)));
                    //落地页 - 模板 - 组件 - 主键id
                    Long templateId = "Form".equals(rowJsonChildrenName) ? modelChildren.getLong("form") : ("OrderWidget".equals(rowJsonChildrenName) ? modelChildren.getLong("orderTemplate") : null);
                    if (null != templateId && 0 != templateId) {
                        LandingPageWidgetTemplate landingPageWidgetTemplateOrder = landingPageWidgetTemplateService.getInfoById(templateId);
                        LandingPageWidgetTemplateDTO orderFromRelDtoSplice = new LandingPageWidgetTemplateDTO();
                        BeanUtils.copyProperties(landingPageWidgetTemplateOrder, orderFromRelDtoSplice);
                        if (null != landingPageWidgetTemplateOrder.getApplicationId() && landingPageWidgetTemplateOrder.getApplicationId() != 0) {
                            if (PaymentType.WECHAT_PAY.toString().equals(orderFromRelDtoSplice.getPaymentType())) {
                                orderFromRelDtoSplice.setWechatMerchantAccount(wechatMerchantAccountMapper.selectById(landingPageWidgetTemplateOrder.getApplicationId()));
                                orderFromRelDtoSplice.setCanPay(true);
                            }
                            if (PaymentType.PING_PAY.toString().equals(orderFromRelDtoSplice.getPaymentType())) {
                                orderFromRelDtoSplice.setPingApplication(pingApplicationMapper.selectById(landingPageWidgetTemplateOrder.getApplicationId()));
                                orderFromRelDtoSplice.setCanPay(true);
                            }
                        }
                        landingPageWidgetTemplateDTOList.add(orderFromRelDtoSplice);
                        modelChildren.put("content", orderFromRelDtoSplice.toJSON());
                        rowJsonChildren.put("model", modelChildren);
                    }
                    log.info("model.children[0].model={}", modelChildren);
                }
                //重新赋值
                childrenNew.add(rowJsonChildren);
            }
            //重新赋值
            model.put("children", childrenNew);
        }
        //拼接组件内所有表单、订单详情数据
        itemContent.put("landingPageWidgetTemplateList", landingPageWidgetTemplateDTOList);
        //拼接组件与表单订单的关联关系
        itemContent.put("landingPageWidgetTemplateRelList", landingPageWidgetTemplatePreviewRelList);
        //重新赋值
        itemContent.put("model", model);
        return itemContent;
    }

    private boolean checkMerchant(Integer merchantId) {
        if (Objects.isNull(merchantId)) {
            return false;
        }
        WechatMerchantAccount wechatMerchantAccount = wechatMerchantAccountMapper.selectById(merchantId);
        return wechatMerchantAccount != null && WechatMerchantAccountStatus.SUCCESS.equals(wechatMerchantAccount.getValidate());
    }

    public void updateCommonPreviewPage(LandingPageGenerateDTO data) {
        log.debug(JSONObject.toJSONString(data));
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity requestEntity = new HttpEntity(data, header);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(data.getGenerateConf().getHost() + UPDATE_LANIND_PAGE_PREVIEW_URL, requestEntity, String.class);
        HttpStatus httpStatus = responseEntity.getStatusCode();
        Object body = responseEntity.getBody();
        if (httpStatus.OK.equals(httpStatus) && StringUtils.isNotBlank((String) body)) {
            stringRedisTemplate.opsForValue().set(pageViewConf.getCommonPreviewCacheKey() + ":" + data.getToken(), (String) body);
        } else {
            log.warn("generate landing-page preview error! status: {} message: {}", httpStatus, body);
        }
    }

    public void updateCommonPage(LandingPageGenerateDTO data) {
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity requestEntity = new HttpEntity(data, header);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(data.getGenerateConf().getHost() + UPDATE_LANIND_PAGE_URL, requestEntity, String.class);
        HttpStatus httpStatus = responseEntity.getStatusCode();
        Object body = responseEntity.getBody();
        if (httpStatus.OK.equals(httpStatus) && StringUtils.isNotBlank((String) body)) {
            stringRedisTemplate.opsForValue().set(pageViewConf.getCommonCacheKey() + ":" + data.getToken(), (String) body);
        } else {
            log.warn("generate landing-page error! status: {} message: {}", httpStatus, body);
        }
    }

    public void updateAllLandingPage(Long advertiserAccountId) {
        landingPageService.
                list(new QueryWrapper<LandingPage>()
                        .select("id").eq("advertsier_account_id", advertiserAccountId))
                .forEach(item -> {
                    List<LandingPageChannel> channels = landingPageChannelService.list(new LambdaQueryWrapper<LandingPageChannel>().eq(LandingPageChannel::getLandingPageId, item.getId()));
                    this.deleteLandingPageCache(item.getToken(), channels);
                });
    }


    @RedisDS
    public String commonPage(String token) {
        String pageView = stringRedisTemplate.opsForValue().get(pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getCommonCacheKey() + token);
        return pageView;
    }

    @Deprecated
    @RedisDS
    public String commonIlynxPage(String token) {
        String pageView = stringRedisTemplate.opsForValue().get(pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getIlynxCacheKey() + token);
        return pageView;
    }

    @Deprecated
    @RedisDS
    public void saveCommonIlynxPage(String token, String body) {
        stringRedisTemplate.opsForValue().set(pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getIlynxCacheKey() + token, body, 1, TimeUnit.DAYS);
    }

    public String commonPreviewPage(String token) {
        String pageView = stringRedisTemplate.opsForValue().get(pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getCommonPreviewCacheKey() + ":" + token);
        return pageView;
    }

    /**
     * 清理缓存 - 传递的channels是需要带上落地页ID
     */
    public void deleteLandingPageCache(String token, List<LandingPageChannel> channels) {
        if (CollectionUtils.isEmpty(channels)) {
            return;
        }
        Set<String> lpCachekeys = new HashSet<>();
        Set<String> channelCacheKeys = new HashSet<>();
        Set<String> landingPageDataCacheKeys = new HashSet<>();
        for (LandingPageChannel channel : channels) {
            String lpCachekey = pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getCommonCacheKey() + token;
            lpCachekeys.add(lpCachekey);
            String channelCacheKey = pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getCommonCacheKey() + token + ":" + channel.getChannelParam();
            channelCacheKeys.add(channelCacheKey);
            String landingPageDataCacheKey = pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getCommonDataCacheKey() + token + ":" + channel.getChannelParam();
            landingPageDataCacheKeys.add(landingPageDataCacheKey);
        }
        //h5 落地页缓存、外网落地页
        defaultObjectRedisTemplate.delete(lpCachekeys);
        defaultShowRedisTemplate.delete(lpCachekeys);
        //内网渠道、外网渠道 渠道是否能访问
        defaultObjectRedisTemplate.delete(channelCacheKeys);
        defaultShowRedisTemplate.delete(channelCacheKeys);
        //小程序内页面缓存 落地页-生成页-数据
        defaultObjectRedisTemplate.delete(landingPageDataCacheKeys);
        defaultShowRedisTemplate.delete(landingPageDataCacheKeys);
        //1.213.0迭代，删除落地信息缓存：落地页-生成页-java对象信息：内网落地页缓存、外网落地页缓存
        String landingPageInfoKey = RedisConstant.getLandingPageInfoKey(TenantContextHolder.get(), token);
        defaultBytesRedisTemplate.delete(landingPageInfoKey);
        defaultShowRedisTemplate.delete(landingPageInfoKey);
        //1.227.0迭代，删除落地信息缓存：落地页-生成页-java对象信息：内网落地页缓存、外网落地页缓存
        landingPageRedis.deleteLandingPageCacheByToken(token);
        //1.239.0 落地页自动标签配置缓存删除 - 基于落地页ID删除
        channels.stream().map(LandingPageChannel::getLandingPageId).filter(Objects::nonNull).distinct().forEach(landingPageId -> {
            landingPageRedis.deleteLandingPageAutoTagCache(landingPageId);
            landingPageRedis.deleteLandingPageTaobaoDspCache(landingPageId);
        });

    }

    /**
     * 清理原生页面缓存
     * 页面删除的时候才需要清理
     *
     * @param token
     * @param channels
     */
    public void clearAipCache(String token, List<LandingPageChannel> channels) {
        Set<String> ilynxCacheKeys = new HashSet<>();
        Set<String> channelCacheKeys = new HashSet<>();
        List<LandingPageChannel> collect = channels.stream().filter(c -> LandingPageChannelType.ORANGE_STATION_PATH.equals(c.getLandingPageChannelType())).collect(Collectors.toList());
        for (LandingPageChannel channel : collect) {
            String ilynxCacheKey = pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getIlynxCacheKey() + token + ":" + channel.getChannelParam();
            ilynxCacheKeys.add(ilynxCacheKey);

            String channelCacheKey = pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getCommonCacheKey() + token + ":" + channel.getChannelParam();
            channelCacheKeys.add(channelCacheKey);
        }
        //清理AIP 原生页、外网原生页
        defaultObjectRedisTemplate.delete(ilynxCacheKeys);
        defaultShowRedisTemplate.delete(ilynxCacheKeys);

        //内网渠道、外网渠道 渠道是否能访问
        defaultObjectRedisTemplate.delete(channelCacheKeys);
        defaultShowRedisTemplate.delete(channelCacheKeys);

        //1.213.0迭代，删除落地信息缓存：落地页-生成页-java对象信息：内网落地页缓存、外网落地页缓存
        String landingPageInfoKey = RedisConstant.getLandingPageInfoKey(TenantContextHolder.get(), token);
        defaultBytesRedisTemplate.delete(landingPageInfoKey);
        defaultShowRedisTemplate.delete(landingPageInfoKey);
        //1.227.0迭代，删除落地信息缓存：落地页-生成页-java对象信息：内网落地页缓存、外网落地页缓存
        landingPageRedis.deleteLandingPageCacheByToken(token);
        //1.239.0 落地页自动标签配置缓存删除 - 基于落地页ID删除
        channels.stream().map(LandingPageChannel::getLandingPageId).filter(Objects::nonNull).distinct().forEach(landingPageId -> {
            landingPageRedis.deleteLandingPageAutoTagCache(landingPageId);
            landingPageRedis.deleteLandingPageTaobaoDspCache(landingPageId);
        });
    }

    /**
     * 单一渠道清理
     * @param token
     * @param channelParam
     */
    public void clearAipCacheByChannel(String token, String channelParam) {
        String ilynxCacheKey = pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getIlynxCacheKey() + token + ":" + channelParam;
        String channelCacheKey = pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getCommonCacheKey() + token + ":" + channelParam;
        //清理AIP 原生页、外网原生页
        defaultObjectRedisTemplate.delete(ilynxCacheKey);
        defaultShowRedisTemplate.delete(ilynxCacheKey);

        //内网渠道、外网渠道 渠道是否能访问
        defaultObjectRedisTemplate.delete(channelCacheKey);
        defaultShowRedisTemplate.delete(channelCacheKey);
    }

    /**
     * 生成页缓存数据
     *
     * @param token        落地页token
     * @param channelParam 落地页渠道（_cl）参数
     */
    public LandingPageShowDto generateCache(String token, String channelParam) {
        String landingPageDataCacheKey = pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getCommonDataCacheKey() + token + ":" + channelParam;
        Object redisObj = defaultObjectRedisTemplate.opsForValue().get(landingPageDataCacheKey);
        if (!Objects.isNull(redisObj) && (redisObj instanceof String)) {
            String jsonStr = StringUtils.trim((String) redisObj);
            if (StringUtils.isNotBlank(jsonStr)) {
                return JSONObject.toJavaObject(JSONObject.parseObject(jsonStr), LandingPageShowDto.class);
            }
        }
        LandingPageShowDto landingPageShowDto = landingPageGenerateService.generate(token, channelParam);
        if (!Objects.isNull(landingPageShowDto)) {
            defaultObjectRedisTemplate.opsForValue().set(landingPageDataCacheKey, JSONObject.toJSONString(landingPageShowDto), 1L, TimeUnit.DAYS);
        }
        return landingPageShowDto;
    }

    public LandingPageShowDto generate(String token, String channelParam) {
        LandingPage landingPage = this.landingPageService.getOne(new LambdaQueryWrapper<LandingPage>().eq(LandingPage::getToken, token)
                //业务修改对应迭代版本号：YIYE_AGENT_V1.133.0 落地页模块列表优化 & 余量到期提醒 & 客服模块userID 必填
                .in(LandingPage::getDeleteStatus, Arrays.asList(DeleteStatus.NORMAL, DeleteStatus.IS_DELETE)));
        if (Objects.isNull(landingPage)) {
            throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_NOT_EXISTS);
        }
        final Long landingPageId = landingPage.getId();
        LandingPageGenerateDTO landingPageGenerateDTO = new LandingPageGenerateDTO();
        BeanUtils.copyProperties(landingPage, landingPageGenerateDTO);
        this.generateLandingPageContent(landingPageGenerateDTO);
        LandingPageShowDto landingPageShowDto = new LandingPageShowDto();
        landingPageShowDto.setLandingPageGenerateDTO(landingPageGenerateDTO);
        landingPageShowDto.setLandingPageCustomCodeList(landingPageCustomCodeService.list(new LambdaQueryWrapper<LandingPageCustomCode>().eq(LandingPageCustomCode::getLandingPageId, landingPageId)));
        if (StringUtils.isNotBlank(channelParam)) {
            LandingPageChannel landingPageChannel = landingPageChannelService.getOne(new LambdaQueryWrapper<LandingPageChannel>().eq(LandingPageChannel::getLandingPageId, landingPage.getId()).eq(LandingPageChannel::getChannelParam, channelParam)
                    //业务修改对应迭代版本号：YIYE_AGENT_V1.133.0 落地页模块列表优化 & 余量到期提醒 & 客服模块userID 必填
                    .eq(LandingPageChannel::getDeleteStatus, DeleteStatus.NORMAL));
            if (Objects.isNull(landingPageChannel)) {
                throw new RestException(ErrorConstants.ERROR_LANDING_PAGE_CHANNEL_NOT_EXISTS);
            }
            landingPageShowDto.setLandingPageChannel(landingPageChannel);
            landingPageShowDto.setUploadConfiguration(uploadConfigurationService.getOne(new LambdaQueryWrapper<UploadConfiguration>().eq(UploadConfiguration::getLandingPageId, landingPageId).eq(UploadConfiguration::getChannelId, landingPageChannel.getId())));
            //落地页访问url
            landingPageShowDto.setUrl(landingPageService.getLandingPageChannelUrl(landingPageChannel.getLandingPageChannelType(), landingPage.getToken(), channelParam, landingPage.getAdvertiserAccountGroupId()));
        }
        landingPageShowDto.setTraceDomain(landingPageChannelService.getLandingPageTraceDomainUrl());
        landingPageShowDto.setDefaultDomain(landingPageChannelService.getLandingPageDomainUrl(landingPage.getAdvertiserAccountGroupId()));
        landingPageShowDto.setLandingPageStrategies(strategyService.list(new LambdaQueryWrapper<LandingPageStrategy>().eq(LandingPageStrategy::getLandingPageId, landingPageId)));

        //查询字节小程序页面配置的策略
        if (Objects.equals(landingPage.getLandingPageType(), LandingPageType.DOUYIN_APPLET_PAGE )) {
            JSONArray douYinLandingPageStrategyContent = landingPageService.queryDouYinLandingPageStrategys(landingPageId);
            landingPageShowDto.setBytePageStrategyContent(douYinLandingPageStrategyContent);
        }

        List<LandingPageWidgetTemplate> landingPageWidgetTemplates = landingPageWidgetTemplateRelService.selectLandingPageTemplates(landingPageId);
        //落地页-与之关联的模板
        landingPageShowDto.setLandingPageWidgetTemplates(landingPageWidgetTemplates);

        List<LandingPageWidgetTemplate> collect = landingPageWidgetTemplates.stream().filter(a -> WidgetTemplateType.POPUP_TYPE.equals(a.getWtType())).collect(Collectors.toList());
        //落地页-模板-弹层
        landingPageShowDto.setLandingPageWidgetPopupTemplates(landingPageWidgetTemplates.stream().filter(a -> WidgetTemplateType.POPUP_TYPE.equals(a.getWtType())).collect(Collectors.toList()));
        //落地页-模板关系
        List<LandingPageWidgetTemplateRel> list = landingPageWidgetTemplateRelService.list(new LambdaQueryWrapper<LandingPageWidgetTemplateRel>().eq(LandingPageWidgetTemplateRel::getLandingPageId, landingPageId));
        landingPageShowDto.setLandingPageWidgetTemplatesRel(list);
        //落地页-模板关系-弹层
        List<LandingPageWidgetTemplateRel> landingPageWidgetTemplateRelList = list.stream().filter(a -> WidgetTemplateType.POPUP_TYPE.equals(a.getWtType())).collect(Collectors.toList());

        //落地页与弹层间接关联关系
        List<LandingPageWidgetTemplate> popupWidget = landingPageWidgetTemplateService.getPopupByLandingPageId(landingPage.getId());
        // 弹层与表单订单的关系
        List<LandingPageWidgetTemplateRel> collect1 = popupWidget.stream().map(a -> {
            LandingPageWidgetTemplateRel landingPageWidgetTemplateRel = new LandingPageWidgetTemplateRel();
            landingPageWidgetTemplateRel.setLandingPageId(landingPage.getId()).setLandingPageWidgetTemplateId(a.getId());
            landingPageWidgetTemplateRel.setUuid(a.getUuid());
            return landingPageWidgetTemplateRel;
        }).collect(Collectors.toList());
        //将落地页与弹层  与落地页与弹层的关系 都放在这一个list里面
        landingPageWidgetTemplateRelList.addAll(collect1);
        //遍历list，获取弹窗里面的详情
        landingPageWidgetTemplateRelList.forEach(landingPageWidgetTemplateRel -> {
            //弹窗组件模板信息
            List<LandingPagePopupWidgetTemplateRel> landingPagePopupWidgetTemplateRelList = landingPagePopupWidgetTemplateRelService.list(new LambdaQueryWrapper<LandingPagePopupWidgetTemplateRel>().eq(LandingPagePopupWidgetTemplateRel::getLandingPagePopupWidgetTemplateId, landingPageWidgetTemplateRel.getLandingPageWidgetTemplateId()));
            landingPagePopupWidgetTemplateRelList.forEach(landingPagePopupWidgetTemplateRel -> {
                //弹窗模板关系对应的组件详情
                LandingPageWidgetTemplate landingPageWidgetTemplate = landingPageWidgetTemplateService.getById(landingPagePopupWidgetTemplateRel.getLandingPageWidgetTemplateId());
                landingPagePopupWidgetTemplateRel.setLandingPageWidgetTemplate(landingPageWidgetTemplate);
            });
            landingPageWidgetTemplateRel.setLandingPagePopupWidgetTemplateRelList(landingPagePopupWidgetTemplateRelList);
        });

        collect.addAll(popupWidget);
        landingPageShowDto.setLandingPageWidgetPopupTemplates(collect);

        landingPageShowDto.setLandingPageWidgetPopupTemplatesRel(landingPageWidgetTemplateRelList);
        //白名单
        AgentConf agent = agentConfService.findByAgentId(TenantContextHolder.get());
        landingPageShowDto.setLicense(agent.getLicense());
        //获取是否展示技术支持
//        BossAdvertiserAccountGroup bossAdvertiserAccountGroup = bossBackendRemote.getwhiteType(TenantContextHolder.get(), landingPage.getAdvertiserAccountGroupId());
        AdvertiserAccountGroup advertiserAccountGroup = baseAdvertiserAccountGroupService.fetchAdvertiserAccountGroup(TenantContextHolder.get(), landingPage.getAdvertiserAccountGroupId());

        if (!Objects.isNull(advertiserAccountGroup)) {
            landingPageShowDto.setShowTechnicalSupport(WhiteType.NOT_SHOW_TECHNICAL_SUPPORT.contains(advertiserAccountGroup.getWhiteTypes()));
            landingPageShowDto.setOpenSaveParamsCollect(WhiteType.OPEN_SAVE_PARAMS_COLLECT.contains(advertiserAccountGroup.getWhiteTypes()));
            landingPageShowDto.setCrossLandingPageSelect(WhiteType.CROSS_PMP_LANDING_SELECT.contains(advertiserAccountGroup.getWhiteTypes()));
        }

        /**
         * 设置微信JS-SDK相关内容
         */
        String corpid = pageViewConf.getJsSdkMap().get(TenantContextHolder.get() +"-" +landingPageId);
        if(StringUtils.isNotBlank(corpid)){
            landingPageShowDto.setWorkWeixinJsSdkCorpid(corpid);
        }
        log.info("landingPageShowDto ====== >> {}", JSONObject.toJSONString(landingPageShowDto));
        return landingPageShowDto;
    }

    /**
     * 落地页 - 预览页
     *
     * @param previewToken
     * @return
     */
    public LandingPageShowDto generatePreviewToken(String previewToken) {
        LandingPagePreview landingPagePreview = landingPagePreviewService.getOne(new LambdaQueryWrapper<LandingPagePreview>().eq(LandingPagePreview::getPreviewToken, previewToken));
        if (Objects.isNull(landingPagePreview)) {
            throw new RestException("落地页查询不到");
        }
        LandingPageGenerateDTO landingPageGenerateDTO = new LandingPageGenerateDTO();
        BeanUtils.copyProperties(landingPagePreview, landingPageGenerateDTO);
        landingPageGenerateService.previewLandingPageContent(landingPageGenerateDTO, previewToken);
        LandingPageShowDto landingPageShowDto = new LandingPageShowDto();
        landingPageShowDto.setLandingPageGenerateDTO(landingPageGenerateDTO);
        landingPageShowDto.setLandingPageCustomCodeList(landingPageCustomCodeService.list(new LambdaQueryWrapper<LandingPageCustomCode>().eq(LandingPageCustomCode::getLandingPageId, landingPageGenerateDTO.getId())));
        landingPageShowDto.setLandingPageStrategyPreviews(landingPageStrategyPreviewService.list(new LambdaQueryWrapper<LandingPageStrategyPreview>().eq(LandingPageStrategyPreview::getPreviewToken, previewToken)));
        //landingPageShowDto.setLandingPageWidgetTemplatePreviewRels(landingPageWidgetTemplatePreviewRelService.list(new LambdaQueryWrapper<LandingPageWidgetTemplatePreviewRel>().eq(LandingPageWidgetTemplatePreviewRel::getPreviewToken, previewToken)));
//        List<LandingPageWidgetTemplate> landingPageWidgetTemplates = landingPageWidgetTemplateRelService.selectLandingPageTemplates(landingPageGenerateDTO.getId());

        List<LandingPageWidgetTemplate> landingPageWidgetTemplates = Lists.newArrayList();
        if (null != landingPagePreview.getTemplateIds() && landingPagePreview.getTemplateIds().size() > 0) {
            JSONArray popUpTemplateIdsArr = landingPagePreview.getTemplateIds().getJSONArray("popUpTemplateIds");
            List<Long> popUpTemplateIds = new ArrayList<>();
            for (Object popUpTemplateIdObj : popUpTemplateIdsArr) {
                JSONObject popUpTemplateIdJson = JSONObject.parseObject(JSONObject.toJSONString(popUpTemplateIdObj));
                Object popUpTemplateId = popUpTemplateIdJson.get("id");
                if (null == popUpTemplateId) continue;
                popUpTemplateIds.add(Long.valueOf(popUpTemplateId.toString()));
            }
            if (popUpTemplateIds.size() > 0) {
                List<LandingPageWidgetTemplate> landingPageWidgetTemplateList = landingPageWidgetTemplateService.list(new LambdaQueryWrapper<LandingPageWidgetTemplate>().in(LandingPageWidgetTemplate::getId, popUpTemplateIds));
                landingPageWidgetTemplates.addAll(landingPageWidgetTemplateList);
            }
        }

        //获取当前落地页所有的模板
        List<LandingPageWidgetTemplate> landingPageWidgetTemplates2 = landingPageWidgetTemplatePreviewRelService.selectLandingPageTemplates(previewToken);
        //落地页-与之关联的模板
        landingPageShowDto.setLandingPageWidgetTemplates(landingPageWidgetTemplates2);
        //落地页-模板关系
        List<LandingPageWidgetTemplatePreviewRel> listPreview = landingPageWidgetTemplatePreviewRelService.list(new LambdaQueryWrapper<LandingPageWidgetTemplatePreviewRel>().eq(LandingPageWidgetTemplatePreviewRel::getPreviewToken, previewToken));
        landingPageShowDto.setLandingPageWidgetTemplatePreviewRels(listPreview);

        //落地页-模板关系-弹层
        List<LandingPageWidgetTemplatePreviewRel> landingPageWidgetTemplatePreviewRelList = listPreview.stream().filter(a -> WidgetTemplateType.POPUP_TYPE.equals(a.getWtType())).collect(Collectors.toList());

        //落地页与弹层间接关联关系
        List<LandingPageWidgetTemplate> popupWidget = landingPagePreviewService.getPopupByLandingPageByPreviewToken(previewToken);

        // 弹层与表单订单的关系
        List<LandingPageWidgetTemplatePreviewRel> collect1 = popupWidget.stream().map(a -> {
            LandingPageWidgetTemplatePreviewRel landingPageWidgetTemplateRel = new LandingPageWidgetTemplatePreviewRel();
            landingPageWidgetTemplateRel.setLandingPageWidgetTemplateId(a.getId());
            return landingPageWidgetTemplateRel;
        }).collect(Collectors.toList());

        landingPageWidgetTemplatePreviewRelList.addAll(collect1);
        landingPageWidgetTemplatePreviewRelList.forEach(landingPageWidgetTemplatePreviewRel -> {
            //弹窗组件模板信息
            List<LandingPagePopupWidgetTemplateRel> landingPagePopupWidgetTemplateRelList = landingPagePopupWidgetTemplateRelService.list(new LambdaQueryWrapper<LandingPagePopupWidgetTemplateRel>().eq(LandingPagePopupWidgetTemplateRel::getLandingPagePopupWidgetTemplateId, landingPageWidgetTemplatePreviewRel.getLandingPageWidgetTemplateId()));
            landingPagePopupWidgetTemplateRelList.forEach(landingPagePopupWidgetTemplateRel -> {
                //弹窗模板关系对应的组件详情
                LandingPageWidgetTemplate landingPageWidgetTemplate = landingPageWidgetTemplateService.getById(landingPagePopupWidgetTemplateRel.getLandingPageWidgetTemplateId());
                landingPagePopupWidgetTemplateRel.setLandingPageWidgetTemplate(landingPageWidgetTemplate);
            });
            landingPageWidgetTemplatePreviewRel.setLandingPagePopupWidgetTemplateRelList(landingPagePopupWidgetTemplateRelList);
        });
        List<LandingPageWidgetTemplate> collect = landingPageWidgetTemplates.stream().filter(a -> WidgetTemplateType.POPUP_TYPE.equals(a.getWtType())).collect(Collectors.toList());

        collect.addAll(popupWidget);
        landingPageShowDto.setLandingPageWidgetPopupTemplates(collect);
        //落地页-模板-弹层
        landingPageShowDto.setLandingPageWidgetPopupTemplatePreviewRels(landingPageWidgetTemplatePreviewRelList);

        AdvertiserAccountGroup advertiserAccountGroup = baseAdvertiserAccountGroupService.fetchAdvertiserAccountGroup(TenantContextHolder.get(), landingPagePreview.getAdvertiserAccountGroupId());
        //获取是否展示技术支持
//        AgentConf agent = agentConfService.findByAgentId(TenantContextHolder.get());
        //白名单
        AgentConf agent = agentConfService.findByAgentId(TenantContextHolder.get());
        landingPageShowDto.setLicense(agent.getLicense());
        if (!Objects.isNull(advertiserAccountGroup)) {
            landingPageShowDto.setShowTechnicalSupport(WhiteType.NOT_SHOW_TECHNICAL_SUPPORT.contains(advertiserAccountGroup.getWhiteTypes()));
            landingPageShowDto.setCrossLandingPageSelect(WhiteType.CROSS_PMP_LANDING_SELECT.contains(advertiserAccountGroup.getWhiteTypes()));
        }
        log.info("landingPageShowDto ====== >> {}", JSONObject.toJSONString(landingPageShowDto));


        return landingPageShowDto;
    }

    /**
     * 渲染页面
     * @param token
     * @param channelParam
     * @param intergrated
     * @return
     */
    public String getIlynxHtml(String token, String channelParam, String intergrated) throws IOException {
        LandingPageShowDto generateBody = this.generate(token, channelParam);
        if (StringUtils.isNotBlank(intergrated)) {
            LandingPageGenerateDTO landingPageGenerateDTO = generateBody.getLandingPageGenerateDTO();
            landingPageGenerateDTO.setIntergrated(intergrated);
            generateBody.setLandingPageGenerateDTO(landingPageGenerateDTO);
        }
        log.debug(JSONObject.toJSONString(generateBody));
        String htmlByLandingPage = landingPageGenerateRemote.getHtmlByLandingPage(generateBody);
        return htmlByLandingPage;
    }

    @Deprecated
    public byte[] getIlynxTemplate(String agentId, String token, String channelParam) {
        Object template = fileRedisTemplate.opsForValue().get(pageViewConf.getIlynxTemplateCacheKey() + ":" + token + ":" + channelParam);
        if (Objects.isNull(template)) {
            try {
                return marketingLandingPageService.buildIlynx(agentId, token, channelParam);
            } catch (IOException ioException) {
                log.error("ilynx build error agentId:{} token:{} channelParam:{}", agentId, token, channelParam);
            }

        }
        return Objects.nonNull(template) ? (byte[]) template : null;
    }
}
