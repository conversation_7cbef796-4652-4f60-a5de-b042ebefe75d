package ai.yiye.agent.landingpage.config;

import ai.yiye.agent.domain.*;
import ai.yiye.agent.domain.enumerations.CompareLogMethodValueType;
import ai.yiye.agent.domain.landingpage.EnterpriseWechatRobotCustomerGroup;
import ai.yiye.agent.domain.landingpage.EnterpriseWechatRobotCustomerGroupRel;
import ai.yiye.agent.domain.landingpage.dto.EnterpriseWechatRobotCustomerGroupDto;
import ai.yiye.agent.domain.result.Result;
import ai.yiye.agent.domain.utils.CollectionUtil;
import ai.yiye.agent.domain.utils.WebUtils;
import ai.yiye.agent.landingpage.dto.CompareLogObject;
import ai.yiye.agent.landingpage.dto.WechatBatchOperationDto;
import ai.yiye.agent.landingpage.enums.WechatOperationType;
import ai.yiye.agent.landingpage.sender.UserOperationLogDetailActionSender;
import ai.yiye.agent.landingpage.service.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/6 16:13
 */
@Aspect
@Slf4j
@Component
public class CompareLogAspect {

    @Autowired
    private LandingPageWechatCustomerServiceService landingPageWechatCustomerServiceService;
    @Autowired
    private LandingPageWechatCustomerServiceGroupRelService landingPageWechatCustomerServiceGroupRelService;
    @Autowired
    private LandingPageWechatCustomerServiceGroupService landingPageWechatCustomerServiceGroupService;
    @Autowired
    private LandingPageWechatCustomerServiceAutoRuleService landingPageWechatCustomerServiceAutoRuleService;
    @Autowired
    private UserOperationLogDetailActionSender userOperationLogDetailActionSender;
    @Resource
    private LandingPageWechatCustomerServiceAbnormalMonitorService landingPageWechatCustomerServiceAbnormalMonitorService;
    @Resource
    private EnterpriseWechatRobotCustomerService enterpriseWechatRobotCustomerService;
    @Resource
    private EnterpriseWechatRobotCustomerGroupService enterpriseWechatRobotCustomerGroupService;
    @Resource
    private EnterpriseWechatRobotCustomerGroupRelService enterpriseWechatRobotCustomerGroupRelService;

    @Pointcut("@annotation(ai.yiye.agent.landingpage.config.CompareLog)")
    public void logPoint() {
    }

    @Around("logPoint()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        //批量操作
        List<CompareLogMethodValueType> batchList = Collections.singletonList(CompareLogMethodValueType.BATCH_UPDATE_ALL);
        List<CompareLogMethodValueType> batchUpload = Collections.singletonList(CompareLogMethodValueType.BATCH_UPLOAD_CUSTOMER);
        MethodSignature ms = (MethodSignature) joinPoint.getSignature();
        Method method = ms.getMethod();
        CompareLog annotation = method.getAnnotation(CompareLog.class);
        Object proceed = null;
        if (!Objects.isNull(annotation)) {
            CompareLogObject compareLogObject = new CompareLogObject();
            //通过annation后面的参数，来获取每个的操作路径。然后将请求参数和路径塞到内容中。

            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) requestAttributes;
            assert servletRequestAttributes != null;
            HttpServletRequest request = servletRequestAttributes.getRequest();
            String ipAddress = WebUtils.getIpAddr(request);
            String path = request.getServletPath();
            String requestMethod = request.getMethod();
            String methArgs = readAsChars(request);
            JSONObject oldDataList = new JSONObject();

            if (batchList.contains(annotation.value())) {
                //批量修改要拆开
                try {
                    oldDataList = getOldBatchData(requestMethod, path, methArgs, annotation.value());
                } catch (Exception e) {
                    log.error("获取前链路数据出现问题{}", e.getMessage(), e);
                }
                proceed = joinPoint.proceed();
                try {
                    JSONObject newDataList = getOldBatchData(requestMethod, path, methArgs, annotation.value());
                    //进行一个operation的转换
                    for (Map.Entry<String, Object> entry : oldDataList.entrySet()) {
                        JSONObject value = (JSONObject) entry.getValue();
                        JSONObject newData = (JSONObject) newDataList.get(entry.getKey());
                        compareLogObject = new CompareLogObject();
                        compareLogObject.setOperAtionType(getOperationTypeByOperat(path));
                        compareLogObject.setOperationLogDetailActionLevel(annotation.operLevelStr());
                        compareLogObject.setOperationLogDetailOperands(annotation.operOpreands());
                        compareLogObject.setIp(ipAddress);
                        compareLogObject.setOldData(value);
                        compareLogObject.setNewData(newData);
                        if (SecurityContextHolder.getContext().getAuthentication() != null) {
                            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                            User user = (User) authentication.getPrincipal();
                            compareLogObject.setOperUserId(user.getId());
                            compareLogObject.setOperUserName(user.getUsername());
                            compareLogObject.setOperationRole(user.getOperationRole());
                        }
                        userOperationLogDetailActionSender.sendOperationLogCompareDetail(compareLogObject);
                    }
                } catch (Exception e) {
                    log.error("获取保存后链路数据出现问题{}", e.getMessage(), e);
                }
            } else if (batchUpload.contains(annotation.value())) {
                //批量导入  需要获取旧的所有idlist然后进行对比 看哪些新增了
                proceed = joinPoint.proceed();
                try {
                    Result<?> result = (Result<?>) proceed;
                    JSONObject oldIds = new JSONObject();
                    oldIds.put("newIds", result.getResult());
                    compareLogObject = new CompareLogObject();
                    compareLogObject.setOperAtionType(annotation.value());
                    compareLogObject.setOperationLogDetailActionLevel(annotation.operLevelStr());
                    compareLogObject.setOperationLogDetailOperands(annotation.operOpreands());
                    compareLogObject.setIp(ipAddress);
                    compareLogObject.setNewData(oldIds);
                    if (SecurityContextHolder.getContext().getAuthentication() != null) {
                        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                        User user = (User) authentication.getPrincipal();
                        compareLogObject.setOperUserId(user.getId());
                        compareLogObject.setOperUserName(user.getUsername());
                        compareLogObject.setOperationRole(user.getOperationRole());
                    }
                    userOperationLogDetailActionSender.sendOperationLogCompareDetail(compareLogObject);
                } catch (Exception e) {
                    log.error("获取保存后链路数据出现问题{}", e.getMessage(), e);
                }
            } else {
                JSONObject oldData=new JSONObject();
                try {
                     oldData = getOldData(requestMethod, path, methArgs, annotation.value());
                } catch (Exception e) {
                    log.error("获取前链路数据出现问题{}", e.getMessage(), e);
                }

                proceed = joinPoint.proceed();
                try {
                JSONObject newData = getOldData(requestMethod, path, methArgs, annotation.value());
                compareLogObject.setOperAtionType(annotation.value());
                compareLogObject.setOperationLogDetailActionLevel(annotation.operLevelStr());
                compareLogObject.setOperationLogDetailOperands(annotation.operOpreands());

                compareLogObject.setIp(ipAddress);
                compareLogObject.setOldData(oldData);
                compareLogObject.setNewData(newData);
                if (SecurityContextHolder.getContext().getAuthentication() != null) {
                    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                    User user = (User) authentication.getPrincipal();
                    compareLogObject.setOperUserId(user.getId());
                    compareLogObject.setOperUserName(user.getUsername());
                    compareLogObject.setOperationRole(user.getOperationRole());
                }
                userOperationLogDetailActionSender.sendOperationLogCompareDetail(compareLogObject);
                } catch (Exception e) {
                    log.error("获取保存后链路数据出现问题{}", e.getMessage(), e);
                }
            }

        } else {
            proceed = joinPoint.proceed();

        }
        return proceed;
    }

    private static CompareLogMethodValueType getOperationTypeByOperat(String path) {

        String opertion = path.substring(path.lastIndexOf("/") + 1);
        //获取到了批量操作的动作
        WechatOperationType of = WechatOperationType.of(opertion);
        if (of.equals(WechatOperationType.ONLINE) || of.equals(WechatOperationType.OFFLINE)) {
            return CompareLogMethodValueType.UPDATE_ONLINE_STATUS;
        } else if (of.equals(WechatOperationType.DELETE)) {
            return CompareLogMethodValueType.BATCH_DELETE;
        } else {
            //有单个编辑逻辑的，都走这里
            return CompareLogMethodValueType.EDIT_CUSTOMER_SERVICE;
        }

    }

    private static WechatOperationType getWechatOperationType(String path) {
        String operation = path.substring(path.lastIndexOf("/") + 1);
        return WechatOperationType.of(operation);
    }

    /**
     * 批量修改
     *
     * @param requestMethod
     * @param path
     * @param methArgs
     * @param
     * @return
     */
    private JSONObject getOldBatchData(String requestMethod, String path, String methArgs, CompareLogMethodValueType methodValueType) {
        WechatOperationType wechatOperationType = getWechatOperationType(path);
        JSONObject jsonObject = new JSONObject();
        //首先获取operation
        JSONObject tempObject = JSON.parseObject(methArgs);
        WechatBatchOperationDto wechatBatchOperationDto = JSONObject.toJavaObject(tempObject, WechatBatchOperationDto.class);
        List<Long> wechatCustomerIds = wechatBatchOperationDto.getWechatCustomerIds();
        List<LandingPageWechatCustomerService> landingPageWechatCustomerServiceList = landingPageWechatCustomerServiceService.listByIds(wechatCustomerIds);
        List<LandingPageWechatCustomerServiceGroupRel> landingPageWechatCustomerServiceGroupRelList = WechatOperationType.EDITGROUP.equals(wechatOperationType) || WechatOperationType.REMOVEGROUP.equals(wechatOperationType) ?
            landingPageWechatCustomerServiceGroupRelService.list(
                Wrappers.lambdaQuery(LandingPageWechatCustomerServiceGroupRel.class)
                    .in(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceId, wechatCustomerIds)
            ) : new ArrayList<>();
        List<LandingPageWechatCustomerServiceGroup> landingPageWechatCustomerServiceGroupList = (WechatOperationType.EDITGROUP.equals(wechatOperationType) || WechatOperationType.REMOVEGROUP.equals(wechatOperationType)) && CollectionUtils.isNotEmpty(landingPageWechatCustomerServiceGroupRelList) ?
            landingPageWechatCustomerServiceGroupService.list(
                Wrappers.lambdaQuery(LandingPageWechatCustomerServiceGroup.class)
                    .in(LandingPageWechatCustomerServiceGroup::getId, CollectionUtil.mapToSet(landingPageWechatCustomerServiceGroupRelList, LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceGroupId))
            ) : new ArrayList<>();
        List<LandingPageWechatCustomerServiceAutoRule> landingPageWechatCustomerServiceAutoRuleList = WechatOperationType.AUTORULE.equals(wechatOperationType) || WechatOperationType.CLOSEAUTORULE.equals(wechatOperationType) ?
            landingPageWechatCustomerServiceAutoRuleService.list(
                Wrappers.lambdaQuery(LandingPageWechatCustomerServiceAutoRule.class)
                    .in(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, wechatCustomerIds)
            ) : new ArrayList<>();
        List<LandingPageWechatCustomerServiceAbnormalMonitor> landingPageWechatCustomerServiceAbnormalMonitorList = WechatOperationType.ABNORMALMONITOR.equals(wechatOperationType) || WechatOperationType.CLOSEABNORMALMONITOR.equals(wechatOperationType) ?
            landingPageWechatCustomerServiceAbnormalMonitorService.list(
                Wrappers.lambdaQuery(LandingPageWechatCustomerServiceAbnormalMonitor.class)
                    .in(LandingPageWechatCustomerServiceAbnormalMonitor::getLandingPageWechatCustomerServiceId, wechatCustomerIds)
            ) : new ArrayList<>();
        for (LandingPageWechatCustomerService landingPageWechatCustomerService : landingPageWechatCustomerServiceList) {
            JSONObject oneData = new JSONObject();
            Set<Long> landingPageWechatCustomerServiceGroupIds = landingPageWechatCustomerServiceGroupRelList.stream().filter(t -> t.getLandingPageWechatCustomerServiceId().equals(landingPageWechatCustomerService.getId())).map(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceGroupId).collect(Collectors.toSet());
            List<LandingPageWechatCustomerServiceGroup> list1 = landingPageWechatCustomerServiceGroupList.stream().filter(t -> landingPageWechatCustomerServiceGroupIds.contains(t.getId())).collect(Collectors.toList());
            List<LandingPageWechatCustomerServiceAutoRule> list2 = landingPageWechatCustomerServiceAutoRuleList.stream().filter(t -> t.getLandingPageWechatCustomerServiceId().equals(landingPageWechatCustomerService.getId())).collect(Collectors.toList());
            List<LandingPageWechatCustomerServiceAbnormalMonitor> list3 = landingPageWechatCustomerServiceAbnormalMonitorList.stream().filter(t -> t.getLandingPageWechatCustomerServiceId().equals(landingPageWechatCustomerService.getId())).collect(Collectors.toList());
            oneData.put("domain", landingPageWechatCustomerService);
            oneData.put("groupList", list1);
            oneData.put("autoRule", list2);
            oneData.put("abnormalMonitor", list3);
            jsonObject.put(landingPageWechatCustomerService.getId() + "", oneData);
        }
        return jsonObject;
    }

    /**
     * 根据operation的类型来判断是什么操作
     *
     * @param
     * @return
     */
    private JSONObject getOldData(String requestMethod, String requestPath, String requestParam, CompareLogMethodValueType annotationValue) {
        JSONObject jsonObject = new JSONObject();

        switch (annotationValue.name()) {
            case "UPDATE_ONLINE_STATUS":
                String substring = requestPath.substring(requestPath.lastIndexOf("/") + 1);
                LandingPageWechatCustomerService byId = landingPageWechatCustomerServiceService.getById(Long.parseLong(substring));
                jsonObject = (JSONObject) JSONObject.toJSON(byId);
                break;
            case "SAVE_CUSTOMER_SERVICE":
                //当保存的时候，这边仅仅查最新的一条
                byId = landingPageWechatCustomerServiceService.getLastCustomerService();
                jsonObject = (JSONObject) JSONObject.toJSON(byId);
                break;
            case "EDIT_CUSTOMER_SERVICE":
                JSONObject jo = JSON.parseObject(requestParam);
                Long id = (Long) jo.getLong("id");
                byId = landingPageWechatCustomerServiceService.getById(id);
                List<LandingPageWechatCustomerServiceGroupRel> list = landingPageWechatCustomerServiceGroupRelService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceGroupRel>().eq(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceId, byId.getId()));
                List<LandingPageWechatCustomerServiceGroup> list1 = landingPageWechatCustomerServiceGroupService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceGroup>().in(LandingPageWechatCustomerServiceGroup::getId, list.stream().map(e -> e.getLandingPageWechatCustomerServiceGroupId()).collect(Collectors.toList())));
                List<LandingPageWechatCustomerServiceAutoRule> list2 = landingPageWechatCustomerServiceAutoRuleService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>().eq(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, id));
                List<LandingPageWechatCustomerServiceAbnormalMonitor> list3 = landingPageWechatCustomerServiceAbnormalMonitorService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAbnormalMonitor>().eq(LandingPageWechatCustomerServiceAbnormalMonitor::getLandingPageWechatCustomerServiceId, id));
                jsonObject.put("domain", byId);
                jsonObject.put("groupList", list1);
                jsonObject.put("autoRule", list2);
                jsonObject.put("abnormalMonitor", list3);
                break;
            case "BATCH_UPDATE_BASE":
                JSONArray objects = JSON.parseArray(requestParam);
                List<LandingPageWechatCustomerService> objects1 = new ArrayList<>();
                for (Object object : objects) {
                    JSONObject tempData = (JSONObject) object;
                    id = (Long) tempData.getLong("id");
                    byId = landingPageWechatCustomerServiceService.getById(id);
                    objects1.add(byId);
                }
                jsonObject.put("list", objects1);
                break;
            case "DELETE_CUSTOMER_SERVICE":
                substring = requestPath.substring(requestPath.lastIndexOf("/") + 1);
                byId = landingPageWechatCustomerServiceService.getById(Long.parseLong(substring));
                jsonObject = (JSONObject) JSONObject.toJSON(byId);
                break;

            case "BATCH_SAVE_CUSTOMER_SERVICE":
                objects = JSON.parseArray(requestParam);
                objects1 = landingPageWechatCustomerServiceService.list(new LambdaQueryWrapper<LandingPageWechatCustomerService>().orderByDesc(LandingPageWechatCustomerService::getCreatedAt).last("limit" + objects.size()));
                jsonObject.put("domainList", objects1);
                break;
            case "CUSTOMER_SERVICE_GROUP_ADD":
                LandingPageWechatCustomerServiceGroup landingPageWechatCustomerServiceGroup = landingPageWechatCustomerServiceGroupService.getLastCustomerServiceGroup();
                jsonObject = (JSONObject) JSONObject.toJSON(landingPageWechatCustomerServiceGroup);
                break;
            case "CUSTOMER_SERVICE_GROUP_DELETE":
                substring = requestPath.substring(requestPath.lastIndexOf("/") + 1);
                landingPageWechatCustomerServiceGroup = landingPageWechatCustomerServiceGroupService.getById(Long.parseLong(substring));
                jsonObject = (JSONObject) JSONObject.toJSON(landingPageWechatCustomerServiceGroup);
                break;
            case "CUSTOMER_SERVICE_GROUP_NAME_CHANGE":
                JSONObject tempObject = JSON.parseObject(requestParam);
                LandingPageWechatCustomerServiceGroup wechatBatchOperationDto = JSONObject.toJavaObject(tempObject, LandingPageWechatCustomerServiceGroup.class);
                landingPageWechatCustomerServiceGroup = landingPageWechatCustomerServiceGroupService.getById(wechatBatchOperationDto.getId());
                jsonObject = (JSONObject) JSONObject.toJSON(landingPageWechatCustomerServiceGroup);
                break;
            case "REMOVE_AT_GROUP":
                //移除分组
                jo = JSON.parseObject(requestParam);
                id = (Long) jo.getLong("landingPageWechatCustomerServiceId");
                byId = landingPageWechatCustomerServiceService.getById(id);
                list = landingPageWechatCustomerServiceGroupRelService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceGroupRel>().eq(LandingPageWechatCustomerServiceGroupRel::getLandingPageWechatCustomerServiceId, byId.getId()));
                list1 = landingPageWechatCustomerServiceGroupService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceGroup>().in(LandingPageWechatCustomerServiceGroup::getId, list.stream().map(e -> e.getLandingPageWechatCustomerServiceGroupId()).collect(Collectors.toList())));
                list2 = landingPageWechatCustomerServiceAutoRuleService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAutoRule>().eq(LandingPageWechatCustomerServiceAutoRule::getLandingPageWechatCustomerServiceId, id));
                list3 = landingPageWechatCustomerServiceAbnormalMonitorService.list(new LambdaQueryWrapper<LandingPageWechatCustomerServiceAbnormalMonitor>().eq(LandingPageWechatCustomerServiceAbnormalMonitor::getLandingPageWechatCustomerServiceId, id));
                jsonObject.put("domain", byId);
                jsonObject.put("groupList", list1);
                jsonObject.put("autoRule", list2);
                jsonObject.put("abnormalMonitor", list3);
            case "ROBOT_CUSTOMER_DELETE":
                substring = requestPath.substring(requestPath.lastIndexOf("/") + 1);
                EnterpriseWechatRobotCustomer robotCustomer = enterpriseWechatRobotCustomerService.getById(Long.parseLong(substring));
                jsonObject = (JSONObject) JSONObject.toJSON(robotCustomer);
                break;
            case "ROBOT_CUSTOMER_EDIT":
                jo = JSON.parseObject(requestParam);
                id = jo.getLong("id");
                robotCustomer = enterpriseWechatRobotCustomerService.getById(id);
                List<EnterpriseWechatRobotCustomerGroupRel> groupRelList = enterpriseWechatRobotCustomerGroupRelService.list(new LambdaQueryWrapper<EnterpriseWechatRobotCustomerGroupRel>().eq(EnterpriseWechatRobotCustomerGroupRel::getEnterpriseWechatRobotCustomerId, robotCustomer.getId()));
                List<EnterpriseWechatRobotCustomerGroup> groupList = enterpriseWechatRobotCustomerGroupService.list(new LambdaQueryWrapper<EnterpriseWechatRobotCustomerGroup>().in(EnterpriseWechatRobotCustomerGroup::getId, groupRelList.stream().map(EnterpriseWechatRobotCustomerGroupRel::getEnterpriseWechatRobotCustomerGroupId).collect(Collectors.toList())));
                jsonObject.put("domain", robotCustomer);
                jsonObject.put("groupList", groupList);
                break;
            case "ROBOT_CUSTOMER_GROUP_ADD":
                EnterpriseWechatRobotCustomerGroup robotCustomerGroup = enterpriseWechatRobotCustomerGroupService.getOne(new LambdaQueryWrapper<EnterpriseWechatRobotCustomerGroup>()
                    .orderByDesc(EnterpriseWechatRobotCustomerGroup::getCreatedAt)
                    .last(" limit 1")
                );
                jsonObject = (JSONObject) JSONObject.toJSON(robotCustomerGroup);
                break;
            case "ROBOT_CUSTOMER_GROUP_DELETE":
                substring = requestPath.substring(requestPath.lastIndexOf("/") + 1);
                robotCustomerGroup = enterpriseWechatRobotCustomerGroupService.getById(Long.parseLong(substring));
                jsonObject = (JSONObject) JSONObject.toJSON(robotCustomerGroup);
                break;
            case "ROBOT_CUSTOMER_GROUP_NAME_CHANGE":
                tempObject = JSON.parseObject(requestParam);
                EnterpriseWechatRobotCustomerGroupDto groupDto = JSONObject.toJavaObject(tempObject, EnterpriseWechatRobotCustomerGroupDto.class);
                robotCustomerGroup = enterpriseWechatRobotCustomerGroupService.getById(groupDto.getId());
                jsonObject = (JSONObject) JSONObject.toJSON(robotCustomerGroup);
                break;
            case "ROBOT_CUSTOMER_UPDATE_USAGE_STATUS":
                jo = JSON.parseObject(requestParam);
                id = jo.getLong("id");
                robotCustomer = enterpriseWechatRobotCustomerService.getById(id);
                jsonObject = (JSONObject) JSONObject.toJSON(robotCustomer);
                break;
            case "ROBOT_CUSTOMER_ABNORMAL":
                jsonObject = JSON.parseObject(requestParam);
                break;
        }
        return jsonObject;
    }


    private String readAsChars(HttpServletRequest request) {
        BufferedReader br = null;
        StringBuilder sb = new StringBuilder("");
        try {
            br = request.getReader();
            String str;
            while ((str = br.readLine()) != null) {
                sb.append(str);
            }
            br.close();
        } catch (Exception e) {
            log.info("io异常，插入数据失败" + e.getMessage());
        } finally {
            if (null != br) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return sb.toString();
    }


}
