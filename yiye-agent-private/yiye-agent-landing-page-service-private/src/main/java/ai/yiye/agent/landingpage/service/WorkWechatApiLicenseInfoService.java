package ai.yiye.agent.landingpage.service;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.*;
import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.dto.WorkWechatCustomerLicenseInfoDTO;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.landingpage.EnterpriseWechat;
import ai.yiye.agent.domain.landingpage.WorkWechatApiLicenseInfo;
import ai.yiye.agent.domain.landingpage.WorkWechatApiLicenseInfoFlow;
import ai.yiye.agent.domain.util.EnumUtil;
import ai.yiye.agent.landingpage.config.WorkWechatDevelopConf;
import ai.yiye.agent.landingpage.dto.*;
import ai.yiye.agent.landingpage.mapper.WorkWechatApiLicenseInfoMapper;
import ai.yiye.agent.landingpage.sender.EnterpriseWechatMessageAsyncSender;
import ai.yiye.agent.landingpage.sender.LandingPageSender;
import ai.yiye.agent.landingpage.sender.OutSiteMsgSender;
import ai.yiye.agent.weixin.client.WorkWeixinApiClient;
import ai.yiye.agent.weixin.enums.EnterpriseWechatGlobalErrorCode;
import ai.yiye.agent.weixin.exception.MarketingApiException;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @Date 2025/4/17 11:14
 * @Version 1.0
 */
@Slf4j
@Service
@DS(DbConstants.POSTGRESQL_DEFAULT)
public class WorkWechatApiLicenseInfoService extends ServiceImpl<WorkWechatApiLicenseInfoMapper, WorkWechatApiLicenseInfo> {

    @Resource
    private WorkWeixinApiClient workWeixinApiClient;

    @Resource
    protected EnterpriseWechatService enterpriseWechatService;

    @Resource
    private WorkWechatDevelopConf workWechatDevelopConf;

    @Resource
    private WorkWechatApiLicenseInfoFlowService workWechatApiLicenseInfoFlowService;

    @Resource
    private WorkWechatCustomerLicenseInfoService workWechatCustomerLicenseInfoService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private WorkWechatApiLicenseUserVisibleRangeService workWechatApiLicenseUserVisibleRangeService;

    @Resource
    private EnterpriseWechatDevelopOauthService enterpriseWechatDevelopOauthService;

    @Resource
    private WorkWechatApiLicenseOrderService workWechatApiLicenseOrderService;

    @Resource
    private WorkWechatApiLicenseActionRecordService workWechatApiLicenseActionRecordService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private LandingPageSender landingPageSender;

    @Resource
    private LandingPageWechatCustomerServiceService landingPageWechatCustomerServiceService;

    @Resource
    private EnterpriseWechatMessageAsyncSender enterpriseWechatMessageAsyncSender;

    @Resource
    private OutSiteMsgSender outSiteMsgSender;

    /**
     * 刷新企微微信许可证信息
     */
    public void syncLicenseInfo(String corpId) {
        if (StringUtils.isBlank(corpId)) {
            return;
        }
        EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatCacheByCorpId(corpId);
        if (Objects.isNull(enterpriseWechat)) {
            log.info("获取企微剩余许可证列表-企业微信不存在 corpId:[{}]", corpId);
            return;
        }
        //获取服务商接口调用凭证
        String providerAccessToken;
        try {
            providerAccessToken = enterpriseWechatDevelopOauthService.getProviderAccessToken();
        } catch (Exception e) {
            log.error("获取企微剩余许可证列表-ProviderAccessToken获取失败 corpId:[{}]", corpId);
            return;
        }
        LocalDateTime localDateTime = LocalDateTime.now().minusYears(1).minusMonths(1);
        LocalDateTime firstDay = localDateTime.withDayOfMonth(1);
        ZoneId zoneId = ZoneId.systemDefault();
        List<WorkWechatApiLicenseInfo> list = new ArrayList<>();
        for (int i = 0; i <= 12; i++) {
            LocalDateTime startTime = firstDay.plusMonths(1);
            LocalDateTime endTime = startTime.plusMonths(1).minusDays(1);
            exec(startTime.atZone(zoneId).toInstant().getEpochSecond(), endTime.atZone(zoneId).toInstant().getEpochSecond(), corpId, providerAccessToken, list);
            firstDay = firstDay.plusMonths(1);
        }
        //移除旧信息
        this.lambdaUpdate().eq(WorkWechatApiLicenseInfo::getCorpId, corpId).remove();
        //插入新信息
        if (CollectionUtils.isNotEmpty(list)) {
            //查询订单信息 得到订单购买激活码的时长
            Set<String> orderIds = list.stream().map(WorkWechatApiLicenseInfo::getOrderId).collect(Collectors.toSet());
            List<WorkWechatApiLicenseOrder> workWechatLicenseOrders = this.getOrderInfo(providerAccessToken, corpId, orderIds);
            if (CollectionUtils.isNotEmpty(workWechatLicenseOrders)) {
                workWechatApiLicenseOrderService.saveOrUpdateBatch(workWechatLicenseOrders);
                Map<String, List<WorkWechatApiLicenseOrder>> listMap = workWechatLicenseOrders.stream().collect(Collectors.groupingBy(WorkWechatApiLicenseOrder::getOrderId));
                list.forEach(m -> {
                    List<WorkWechatApiLicenseOrder> workWechatLicenseOrderList = listMap.get(m.getOrderId());
                    if (CollectionUtils.isNotEmpty(workWechatLicenseOrderList)) {
                        WorkWechatApiLicenseOrder workWechatLicenseOrder = workWechatLicenseOrderList.get(0);
                        JSONObject accountDuration = workWechatLicenseOrder.getAccountDuration();
                        Optional.ofNullable(accountDuration).ifPresent(n -> {
                            m.setMonths(n.getInteger("months")).setDays(n.getInteger("days"));
                        });
                    }
                });
            }
            this.saveBatch(list);
        }
    }

    /**
     * 查询订单信息 组装记录
     *
     * @param providerAccessToken
     * @param corpId
     * @param orderIds
     * @return
     */
    public List<WorkWechatApiLicenseOrder> getOrderInfo(String providerAccessToken, String corpId, Set<String> orderIds) {
        List<WorkWechatApiLicenseOrder> workWechatLicenseOrders = Lists.newArrayList();
        orderIds.forEach(m -> {
            JSONObject jsonObject = workWeixinApiClient.getOrder(providerAccessToken, MapUtil.builder(new HashMap<String, Object>()).put("order_id", m).map());
            if (!Objects.equals(jsonObject.getInteger("errcode"), 0)) {
                log.error("查询订单信息失败：{}", jsonObject);
                return;
            }
            JSONObject orderInfo = jsonObject.getJSONObject("order");
            if (Objects.isNull(orderInfo)) {
                log.error("查询订单信息为空：{}", jsonObject);
                return;
            }
            JSONObject accountCount = orderInfo.getJSONObject("account_count");
            JSONObject accountDuration = orderInfo.getJSONObject("account_duration");
            String agentId = TenantContextHolder.get();
            WorkWechatApiLicenseOrder workWechatLicenseOrder = WorkWechatApiLicenseOrder.builder().corpId(corpId).orderId(m)
                .orderType(WorkWechatApiLicenseOrderType.getEnum(orderInfo.getInteger("order_type")))
                .orderStatus(WorkWechatApiLicenseOrderStatus.fromValue(orderInfo.getInteger("order_status")))
                .price(orderInfo.getLong("price"))
                .accountCount(accountCount)
                .accountDuration(accountDuration)
                .createTime(orderInfo.getLong("create_time"))
                .agentId(agentId)
                .payTime(orderInfo.getLong("pay_time")).build();
            workWechatLicenseOrders.add(workWechatLicenseOrder);
        });
        return workWechatLicenseOrders;
    }

    //查询订单列表 -》 查询订单中的激活码账户列表 -》 查询激活码详情 -》 插入或更新数据库
    public void exec(Long startTime, Long endTime, String corpId, String providerAccessToken, List<WorkWechatApiLicenseInfo> workWechatApiLicenseInfos) {
        //查询订单列表
        Map<String, Object> orderMap = Maps.newHashMap();
        orderMap.put("corpid", corpId);
        orderMap.put("start_time", startTime);
        orderMap.put("end_time", endTime);
        Integer has_more = 1;
        List<WorkWechatApiLicenseInfo> result = Lists.newArrayList();
        while (Objects.equals(has_more, 1)) {
            //查询订单列表
            JSONObject jsonObject = workWeixinApiClient.listOrder(providerAccessToken, orderMap);
            if (!Objects.equals(jsonObject.getInteger("errcode"), 0)) {
                has_more = 0;
                log.error("查询订单列表失败：{}", jsonObject);
                throw new RestException("查询失败");
            }
            has_more = jsonObject.getInteger("has_more");
            String nextCursor = jsonObject.getString("next_cursor");
            if (Objects.equals(has_more, 1) && StringUtils.isNotBlank(nextCursor)) {
                orderMap.put("cursor", nextCursor);
            } else {
                orderMap.remove("cursor");
            }
            JSONArray orderList = jsonObject.getJSONArray("order_list");
            Optional.ofNullable(orderList).filter(CollectionUtils::isNotEmpty).ifPresent(e -> {
                Set<String> orderIds = e.stream().map(m -> (LinkedHashMap<String, Object>) m).filter(m -> !Objects.equals(m.get("order_type"), 2)).map(m -> String.valueOf(m.get("order_id"))).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(orderIds)) {
                    List<WorkWechatApiLicenseInfo> orderAccount = getOrderAccount(providerAccessToken, orderIds);
                    if (CollectionUtils.isNotEmpty(orderAccount)) {
                        result.addAll(orderAccount);
                    }
                }
            });
        }
        //查询激活码详情
        if (CollectionUtils.isNotEmpty(result)) {

            List<List<WorkWechatApiLicenseInfo>> partition = Lists.partition(result, 300);
            Map<String, Object> accountMap = Maps.newHashMap();
            accountMap.put("corpid", corpId);
            partition.forEach(m -> {
                Set<String> codes = m.stream().map(WorkWechatApiLicenseInfo::getActiveCode).collect(Collectors.toSet());
                Map<String, String> orderAndCode = m.stream().collect(Collectors.toMap(WorkWechatApiLicenseInfo::getActiveCode, WorkWechatApiLicenseInfo::getOrderId));
                accountMap.put("active_code_list", codes);
                //批量查询激活码详情
                JSONObject jsonObject = workWeixinApiClient.batchGetActiveInfoByCode(providerAccessToken, accountMap);
                if (!Objects.equals(jsonObject.getInteger("errcode"), 0)) {
                    log.error("查询激活码详情失败：{}", jsonObject);
                    throw new RestException("刷新剩余激活码失败!");
                }
                JSONArray activeInfoList = jsonObject.getJSONArray("active_info_list");
                Optional.ofNullable(activeInfoList).filter(CollectionUtils::isNotEmpty).ifPresent(e -> {
                    //构造实体
                    List<WorkWechatApiLicenseInfoFlow> collect = this.constructionLicense(corpId, orderAndCode, e);
                    //插入或更新许可证流水表
                    workWechatApiLicenseInfoFlowService.saveOrUpdateBatch(collect);
                    List<WorkWechatApiLicenseInfoFlow> wechatApiLicenseInfoFlows = collect.stream().filter(license -> WorkWechatApiLicenseStatus.UNBOUND.equals(license.getStatus())).collect(Collectors.toList());
                    Optional.ofNullable(wechatApiLicenseInfoFlows).filter(CollectionUtils::isNotEmpty).ifPresent(license -> {
                        List<WorkWechatApiLicenseInfo> licenseInfos = license.stream().map(l -> BeanUtil.copyProperties(l, WorkWechatApiLicenseInfo.class)).collect(Collectors.toList());
                        workWechatApiLicenseInfos.addAll(licenseInfos);
                    });
                });
            });

        }
    }

    /**
     * 构造许可证实体
     *
     * @param activeInfoList
     */
    public List<WorkWechatApiLicenseInfoFlow> constructionLicense(String corpId, Map<String, String> orderAndCode, JSONArray activeInfoList) {
        return activeInfoList.stream().map(o -> (LinkedHashMap<String, Object>) o).map(o -> {
            String activeCode = Objects.nonNull(o.get("active_code")) ? String.valueOf(o.get("active_code")) : null;
            Integer type = Convert.toInt(o.get("type"), null);
            Integer status = Convert.toInt(o.get("status"), null);
            String userid = Objects.nonNull(o.get("userid")) ? String.valueOf(o.get("userid")) : null;
            Long createTime = Convert.toLong(o.get("create_time"), null);
            Long activeTime = Convert.toLong(o.get("active_time"), null);
            Long expireTime = Convert.toLong(o.get("expire_time"), null);
            WorkWechatApiLicenseInfoFlow workWechatApiLicenseInfoFlow = new WorkWechatApiLicenseInfoFlow();
            Object object = o.get("merge_info");
            Optional.ofNullable(object).ifPresent(info -> {
                LinkedHashMap<String, Object> mergeInfo = (LinkedHashMap<String, Object>) info;
                String toActiveCode = Objects.nonNull(mergeInfo.get("to_active_code")) ? String.valueOf(mergeInfo.get("to_active_code")) : null;
                String fromActiveCode = Objects.nonNull(mergeInfo.get("from_active_code")) ? String.valueOf(mergeInfo.get("from_active_code")) : null;
                workWechatApiLicenseInfoFlow.setToActiveCode(toActiveCode).setFromActiveCode(fromActiveCode);
            });
            workWechatApiLicenseInfoFlow.setCorpId(corpId)
                .setActiveCode(activeCode)
                .setOrderId(Objects.nonNull(orderAndCode) ? orderAndCode.get(activeCode) : null)
                .setApiAccountType(ApiAccountType.getEnumById(type))
                .setStatus(WorkWechatApiLicenseStatus.getByValue(status))
                .setUserid(userid)
                .setActiveTime(activeTime)
                .setExpireTime(expireTime)
                .setCreateTime(createTime);
            return workWechatApiLicenseInfoFlow;
        }).collect(Collectors.toList());
    }

    /**
     * 基于订单ID获取订单下的账户列表（激活码）
     *
     * @param providerAccessToken
     * @param orderIds
     * @return
     */
    public List<WorkWechatApiLicenseInfo> getOrderAccount(String providerAccessToken, Set<String> orderIds) {
        Map<String, Object> accountMap = Maps.newHashMap();
        accountMap.put("limit", 1000);
        List<WorkWechatApiLicenseInfo> allActiveCodes = Lists.newArrayList();
        orderIds.forEach(m -> {
            Integer has_more = 1;
            while (Objects.equals(has_more, 1)) {
                accountMap.put("order_id", m);
                //查询订单中的账户列表
                JSONObject jsonObject = null;
                try {
                    jsonObject = workWeixinApiClient.listOrderAccount(providerAccessToken, accountMap);
                } catch (MarketingApiException e) {
                    log.error("查询订单中的账号列表失败：orderId:{}", m, e);
                    Integer errcode = e.getErrcode();
                    if (Objects.equals(errcode, 701083)) {
                        log.error("orderId:{},订单未支付！", m);
                    }
                    return;
                }
                if (!Objects.equals(jsonObject.getInteger("errcode"), 0)) {
                    has_more = 0;
                    log.error("获取订单中的账号列表失败：{}", jsonObject);
                    throw new RestException("查询失败");
                }
                has_more = jsonObject.getInteger("has_more");
                String nextCursor = jsonObject.getString("next_cursor");
                if (Objects.equals(has_more, 1) && StringUtils.isNotBlank(nextCursor)) {
                    accountMap.put("cursor", nextCursor);
                } else {
                    accountMap.remove("cursor");
                }
                JSONArray accountList = jsonObject.getJSONArray("account_list");
                Optional.ofNullable(accountList).filter(CollectionUtils::isNotEmpty).ifPresent(e -> {
                    List<WorkWechatApiLicenseInfo> licenseInfos = e.stream().map(o -> (LinkedHashMap<String, Object>) o).filter(o -> Objects.equals(o.get("type"), 2) && Objects.nonNull(o.get("active_code")))
                        .map(o -> {
                            String activeCode = String.valueOf(o.get("active_code"));
                            return new WorkWechatApiLicenseInfo().setActiveCode(activeCode).setOrderId(m);
                        }).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(licenseInfos)) {
                        allActiveCodes.addAll(licenseInfos);
                    }
                });
            }
        });
        return allActiveCodes;
    }


    /**
     * 企微成员许可证相关操作
     *
     * @param enterpriseWechatLicenseDto
     */
    public void operate(EnterpriseWechatLicenseDto enterpriseWechatLicenseDto) {
        EnterpriseWechatsUserOperationEvent event = enterpriseWechatLicenseDto.getEvent();
        String corpId = enterpriseWechatLicenseDto.getCorpId();
        if (Objects.isNull(event) || StringUtils.isBlank(corpId)) {
            return;
        }
        RLock lock = redissonClient.getFairLock(RedisConstant.WECHAT_LICENSE_OPERATE_LOCK_KEY + corpId);
        try {
            if (!lock.tryLock(0, 10, TimeUnit.SECONDS)) {
                throw new RestException("操作过于频繁，请稍后再试");
            }
            try {
                //获取企业微信
                EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatCacheByCorpId(corpId);
                if (Objects.isNull(enterpriseWechat)) {
                    log.info("企业微信不存在 dto:[{}]", corpId);
                    throw new RestException("企微信息不存在!");
                }
                switch (event) {
                    case RENEW:
                        this.renew(enterpriseWechatLicenseDto);
                        break;
                    case ACTIVATE:
                        this.activateBySystemCode(enterpriseWechatLicenseDto);
                        break;
                    case PURCHASE_ACTIVATE:
                        this.purchaseActivate(enterpriseWechatLicenseDto);
                        break;
                    case MIGRATE:
                        this.migrate(enterpriseWechatLicenseDto);
                        break;
                    case ORDER_PURCHASE:
                        this.orderPurchase(enterpriseWechatLicenseDto);
                        break;
                }
            } finally {
                lock.unlock();
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

    }

    //转移
    private void migrate(EnterpriseWechatLicenseDto enterpriseWechatLicenseDto) {
        String corpId = enterpriseWechatLicenseDto.getCorpId();
        //转移出去成员的userid
        String handoverUserid = enterpriseWechatLicenseDto.getHandoverUserid();
        //接收成员的userid
        String takeoverUserid = enterpriseWechatLicenseDto.getTakeoverUserid();
        if (StringUtils.isBlank(handoverUserid) || StringUtils.isBlank(takeoverUserid)) {
            throw new RestException("转移成员信息不能为空！");
        }
        //查询企业微信成员信息
        WorkWechatApiLicenseUserVisibleRange workWechatCustomerLicenseInfo = workWechatApiLicenseUserVisibleRangeService.lambdaQuery()
            .eq(WorkWechatApiLicenseUserVisibleRange::getCorpId, corpId)
            .eq(WorkWechatApiLicenseUserVisibleRange::getUserId, handoverUserid)
            .last(" limit 1")
            .one();
        if (Objects.isNull(workWechatCustomerLicenseInfo)) {
            log.info("转移成员信息不存在，corpId:[{}],handoverUserid:[{}]", corpId, handoverUserid);
            throw new RestException("转移成员信息不存在！");
        }
        //开始转移
        String providerAccessToken;
        try {
            providerAccessToken = enterpriseWechatDevelopOauthService.getProviderAccessToken();
        } catch (Exception e) {
            log.error("获取服务商ProviderAccessToken获取失败 corpId:[{}]", corpId);
            throw new RestException("操作失败!");
        }
        Map<String, Object> map = MapUtil.builder(new HashMap<String, Object>()).put("handover_userid", handoverUserid)
            .put("takeover_userid", takeoverUserid).map();
        ArrayList<Map<String, Object>> objects = Lists.newArrayList();
        objects.add(map);
        Map<String, Object> objectMap = MapUtil.builder(new HashMap<String, Object>()).put("corpid", corpId)
            .put("transfer_list", objects)
            .map();
        Long organizationId = organizationService.getOrganizationId();
        JSONObject jsonObject = null;
        WorkWechatApiLicenseActionRecord workWechatApiLicenseActionRecord = WorkWechatApiLicenseActionRecord.builder()
            .corpId(corpId)
            .status(WorkWechatApiLicenseActionStatus.SUCCESS)
            .takeoverUserId(takeoverUserid)
            .handoverUserId(handoverUserid)
            .operatorId(enterpriseWechatLicenseDto.getOperatorId())
            .operatorName(enterpriseWechatLicenseDto.getOperatorName())
            .operatorIp(enterpriseWechatLicenseDto.getOperatorIp())
            .actionType(EnterpriseWechatsUserOperationEvent.MIGRATE)
            .agentId(TenantContextHolder.get())
            .organizationId(organizationId)
            .build();
        try {
            jsonObject = workWeixinApiClient.batchTransferLicense(providerAccessToken, objectMap);
        } catch (MarketingApiException e) {
            log.error("转移成员失败", e);
            workWechatApiLicenseActionRecord.setStatus(WorkWechatApiLicenseActionStatus.FAILURE)
                .setFailReason(e.getMessage());
            JSONObject eJsonObject = e.getJsonObject();
            if (Objects.nonNull(eJsonObject)) {
                JSONArray transferResult = eJsonObject.getJSONArray("transfer_result");
                if (CollectionUtils.isNotEmpty(transferResult) && Objects.nonNull(transferResult.getJSONObject(0).getInteger("errcode"))) {
                    JSONObject transferResultObject = transferResult.getJSONObject(0);
                    Integer errcode = transferResultObject.getInteger("errcode");
                    EnterpriseWechatGlobalErrorCode errorCode = EnumUtil.getByCode(errcode, EnterpriseWechatGlobalErrorCode.class);
                    if (Objects.nonNull(errorCode)) {
                        workWechatApiLicenseActionRecord.setFailReason(errorCode.getMessage());
                    }
                }
            }
        }
        if (Objects.nonNull(jsonObject) && !Objects.equals(jsonObject.getInteger("errcode"), 0)) {
            workWechatApiLicenseActionRecord.setStatus(WorkWechatApiLicenseActionStatus.FAILURE)
                .setFailReason(StringUtils.isNotBlank(workWechatApiLicenseActionRecord.getFailReason()) ? workWechatApiLicenseActionRecord.getFailReason() : jsonObject.toJSONString());
            log.error("转移成员失败：{}", jsonObject);
        }
        //转移动作记录
        workWechatApiLicenseActionRecordService.saveOrUpdate(workWechatApiLicenseActionRecord);
        if (WorkWechatApiLicenseActionStatus.FAILURE.equals(workWechatApiLicenseActionRecord.getStatus())) {
            throw new RestException(workWechatApiLicenseActionRecord.getFailReason());
        } else if (WorkWechatApiLicenseActionStatus.SUCCESS.equals(workWechatApiLicenseActionRecord.getStatus())) {
            List<String> userIds = Arrays.asList(takeoverUserid, handoverUserid);
            //转移成功，更新数据库
            List<WorkWechatApiLicenseUserVisibleRange> list = workWechatApiLicenseUserVisibleRangeService.lambdaQuery()
                .in(WorkWechatApiLicenseUserVisibleRange::getUserId, userIds)
                .list();
            if (CollectionUtils.isNotEmpty(list) && list.size() == 2) {
                //交换两者状态
                WorkWechatApiLicenseUserVisibleRange licenseUserVisibleRangOne = list.get(0);
                WorkWechatApiLicenseUserVisibleRange licenseUserVisibleRangeTwo = list.get(1);
                // 创建临时对象暂存状态
                WorkWechatApiLicenseUserVisibleRange temp = new WorkWechatApiLicenseUserVisibleRange()
                    .setLicenseStatus(licenseUserVisibleRangOne.getLicenseStatus())
                    .setLicenseActiveTime(licenseUserVisibleRangOne.getLicenseActiveTime())
                    .setLicenseExpireTime(licenseUserVisibleRangOne.getLicenseExpireTime())
                    .setApiAccountType(licenseUserVisibleRangOne.getApiAccountType());

                licenseUserVisibleRangOne
                    .setLicenseStatus(licenseUserVisibleRangeTwo.getLicenseStatus())
                    .setLicenseActiveTime(licenseUserVisibleRangeTwo.getLicenseActiveTime())
                    .setLicenseExpireTime(licenseUserVisibleRangeTwo.getLicenseExpireTime())
                    .setApiAccountType(licenseUserVisibleRangeTwo.getApiAccountType());

                licenseUserVisibleRangeTwo
                    .setLicenseStatus(temp.getLicenseStatus())
                    .setLicenseActiveTime(temp.getLicenseActiveTime())
                    .setLicenseExpireTime(temp.getLicenseExpireTime())
                    .setApiAccountType(temp.getApiAccountType());
                //暂时更新数据库
                workWechatApiLicenseUserVisibleRangeService.updateBatchById(Arrays.asList(licenseUserVisibleRangOne, licenseUserVisibleRangeTwo));
                //异步查询远端更新
                enterpriseWechatMessageAsyncSender.senderAsyncWechatUserStatus(WxLicenseDto.builder().userIds(userIds).corpId(corpId).build());
            }
        }

    }


    //下单购买激活码
    private void orderPurchase(EnterpriseWechatLicenseDto enterpriseWechatLicenseDto) {
        EnterpriseWechatsUserOperationEvent event = enterpriseWechatLicenseDto.getEvent();
        String corpId = enterpriseWechatLicenseDto.getCorpId();
        Long operatorId = enterpriseWechatLicenseDto.getOperatorId();
        String operatorIp = enterpriseWechatLicenseDto.getOperatorIp();
        String operatorName = enterpriseWechatLicenseDto.getOperatorName();
        //续费时长 - 月份
        Integer duration = enterpriseWechatLicenseDto.getDuration();
        //购买数
        int size = enterpriseWechatLicenseDto.getLicenseNum();
        //所需费用
        Long totalPrice = priceCalculation(size, duration);
        //查询组织余额
        Long organizationServiceBalance = organizationService.getBalance();
        if (totalPrice > organizationServiceBalance) {
            String agentId = TenantContextHolder.get();
            log.error("组织余额不足，无法购买许可证,agentId:[{}],totalPrice:[{}],organizationServiceBalance:[{}]", agentId, totalPrice, organizationServiceBalance);
            throw new RestException("组织余额不足,购买许可证失败");
        }
        //校验应用订单
        workWechatApiLicenseUserVisibleRangeService.checkApplicationOrder(corpId);
        String providerAccessToken;
        try {
            providerAccessToken = enterpriseWechatDevelopOauthService.getProviderAccessToken();
        } catch (Exception e) {
            log.error("获取服务商ProviderAccessToken获取失败 corpId:[{}]", corpId);
            throw new RestException("操作失败!");
        }
        //查询服务商账户余额 单位：分
        JSONObject accountBalance = workWeixinApiClient.getAccountBalance(providerAccessToken);
        if (Objects.isNull(accountBalance) || Objects.equals(accountBalance.getInteger("errcode"), 0L)) {
            log.error("获取企业微信账户余额失败,corpId:[{}]", corpId);
            throw new RestException("获取企业微信账户余额失败,购买许可证失败!");
        }
        Long balance = accountBalance.getLong("balance");
        if (balance < totalPrice) {
            log.error("服务商账户余额不足,corpId:[{}],totalPrice:[{}],balance:[{}]", corpId, totalPrice, balance);
            float totalPriceFloat = Objects.nonNull(totalPrice) ? totalPrice.floatValue() / 100 : 0f;
            float balanceFloat = Objects.nonNull(balance) ? balance.floatValue() / 100 : 0f;
            outSiteMsgSender.sendOutSiteMsg(OutSiteMsgType.FEISHU, workWechatDevelopConf.getServiceBalanceFeiShuKey(), "购买并激活,服务商余额不足", "企微接口许可购买余额不足，请联系管理员充值!\n服务商余额：" + balanceFloat + "元\n订单金额：" + totalPriceFloat + "元");
            throw new RestException("服务商账户余额不足,购买许可证失败!");
        }
        //执行购买操作
        WxLicenseDto wxLicenseDto = this.purchase(event, providerAccessToken, corpId, size, null, duration, totalPrice, operatorId, operatorIp, operatorName);
        List<WorkWechatApiLicenseInfo> purchase = wxLicenseDto.getWorkWechatApiLicenseInfos();
        if (CollectionUtils.isEmpty(purchase)) {
            WorkWechatApiLicenseOrder workWechatApiLicenseOrder = wxLicenseDto.getWorkWechatApiLicenseOrder();
            log.info("购买许可证失败或订单还在支付中,查询激活码结果为空,corpId:[{}],size:[{}],orders:[{}]", corpId, size, Objects.nonNull(wxLicenseDto.getWorkWechatApiLicenseOrder()) ? JSONObject.toJSONString(wxLicenseDto.getWorkWechatApiLicenseOrder()) : null);
            //订单为已支付 但确没有激活码 需要把订单状态改为修改中 由支付回调去激活
            if (Objects.nonNull(workWechatApiLicenseOrder) && WorkWechatApiLicenseOrderStatus.PAID.equals(workWechatApiLicenseOrder.getOrderStatus())) {
                workWechatApiLicenseOrderService.lambdaUpdate().set(WorkWechatApiLicenseOrder::getOrderStatus, WorkWechatApiLicenseOrderStatus.PENDING_PAYMENT)
                    .eq(WorkWechatApiLicenseOrder::getOrderId, workWechatApiLicenseOrder.getOrderId()).update();
            }
        }
    }

    //购买并激活
    private void purchaseActivate(EnterpriseWechatLicenseDto enterpriseWechatLicenseDto) {
        EnterpriseWechatsUserOperationEvent event = enterpriseWechatLicenseDto.getEvent();
        String corpId = enterpriseWechatLicenseDto.getCorpId();
        Long operatorId = enterpriseWechatLicenseDto.getOperatorId();
        String operatorIp = enterpriseWechatLicenseDto.getOperatorIp();
        String operatorName = enterpriseWechatLicenseDto.getOperatorName();
        //要下单购买的成员
        List<String> userIds = enterpriseWechatLicenseDto.getUserId();
        if (CollectionUtils.isEmpty(userIds)) {
            throw new RestException("企微成员不能为空！");
        }
        List<String> newUserIds = Lists.newArrayList(userIds);
        //续费时长 - 月份
        Integer duration = enterpriseWechatLicenseDto.getDuration();
        //续费人数
        int size = userIds.size();
        //所需费用
        Long totalPrice = priceCalculation(size, duration);
        //查询组织余额
        Long organizationServiceBalance = organizationService.getBalance();
        if (totalPrice > organizationServiceBalance) {
            String agentId = TenantContextHolder.get();
            log.error("组织余额不足，无法购买许可证,agentId:[{}],totalPrice:[{}],organizationServiceBalance:[{}]", agentId, totalPrice, organizationServiceBalance);
            throw new RestException("组织余额不足,购买许可证失败");
        }
        //查询当前企微成员状态
        List<WorkWechatApiLicenseUserVisibleRange> list = workWechatApiLicenseUserVisibleRangeService.lambdaQuery()
            .eq(WorkWechatApiLicenseUserVisibleRange::getCorpId, corpId)
            .in(WorkWechatApiLicenseUserVisibleRange::getUserId, userIds)
            .list();
        if (CollectionUtils.isEmpty(list)) {
            log.info("购买许可证失败，企微成员不存在,corpId:[{}],userIds:[{}]", corpId, userIds);
            throw new RestException("购买许可证失败,企微成员不存在");
        }
        //校验应用订单
        workWechatApiLicenseUserVisibleRangeService.checkApplicationOrder(corpId);
        Set<String> collect = list.stream().filter(e -> LicenseStatus.ACTIVE.equals(e.getLicenseStatus()))
            .map(WorkWechatApiLicenseUserVisibleRange::getUserId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(collect)) {
            log.info("购买许可证失败，企微成员已激活,corpId:[{}],userIds:[{}]", corpId, collect);
            String join = String.join(",", collect);
            throw new RestException("购买许可证失败," + join + " 此前已激活，不可重复激活!");
        }
        String providerAccessToken;
        try {
            providerAccessToken = enterpriseWechatDevelopOauthService.getProviderAccessToken();
        } catch (Exception e) {
            log.error("获取服务商ProviderAccessToken获取失败 corpId:[{}]", corpId);
            throw new RestException("操作失败!");
        }
        //查询服务商账户余额 单位：分
        JSONObject accountBalance = workWeixinApiClient.getAccountBalance(providerAccessToken);
        if (Objects.isNull(accountBalance) || Objects.equals(accountBalance.getInteger("errcode"), 0L)) {
            log.error("获取企业微信账户余额失败,corpId:[{}]", corpId);
            throw new RestException("获取企业微信账户余额失败,购买许可证失败!");
        }
        Long balance = accountBalance.getLong("balance");
        if (balance < totalPrice) {
            log.error("服务商账户余额不足,corpId:[{}],totalPrice:[{}],balance:[{}]", corpId, totalPrice, balance);
            float totalPriceFloat = Objects.nonNull(totalPrice) ? totalPrice.floatValue() / 100 : 0f;
            float balanceFloat = Objects.nonNull(balance) ? balance.floatValue() / 100 : 0f;
            outSiteMsgSender.sendOutSiteMsg(OutSiteMsgType.FEISHU, workWechatDevelopConf.getServiceBalanceFeiShuKey(), "购买并激活,服务商余额不足", "企微接口许可购买余额不足，请联系管理员充值!\n服务商余额：" + balanceFloat + "元\n订单金额：" + totalPriceFloat + "元");
            throw new RestException("服务商账户余额不足,购买许可证失败!");
        }
        //执行购买操作
        WxLicenseDto wxLicenseDto = this.purchase(event, providerAccessToken, corpId, userIds.size(), userIds, duration, totalPrice, operatorId, operatorIp, operatorName);
        List<WorkWechatApiLicenseInfo> purchase = wxLicenseDto.getWorkWechatApiLicenseInfos();
        if (CollectionUtils.isEmpty(purchase)) {
            WorkWechatApiLicenseOrder workWechatApiLicenseOrder = wxLicenseDto.getWorkWechatApiLicenseOrder();
            log.info("购买许可证失败或订单还在支付中,查询激活码结果为空,corpId:[{}],userIds:[{}],orders:[{}]", corpId, userIds, Objects.nonNull(wxLicenseDto.getWorkWechatApiLicenseOrder()) ? JSONObject.toJSONString(wxLicenseDto.getWorkWechatApiLicenseOrder()) : null);
            //订单为已支付 但确没有激活码 需要把订单状态改为修改中 由支付回调去激活
            if (Objects.nonNull(workWechatApiLicenseOrder) && WorkWechatApiLicenseOrderStatus.PAID.equals(workWechatApiLicenseOrder.getOrderStatus())) {
                workWechatApiLicenseOrderService.lambdaUpdate().set(WorkWechatApiLicenseOrder::getOrderStatus, WorkWechatApiLicenseOrderStatus.PENDING_PAYMENT)
                    .eq(WorkWechatApiLicenseOrder::getOrderId, workWechatApiLicenseOrder.getOrderId()).update();
            }
            return;
        }
        WorkWechatApiLicenseOrder workWechatApiLicenseOrder = wxLicenseDto.getWorkWechatApiLicenseOrder();
        //激活 若是存在失败的则存入剩余激活码数量中
        this.licenseInfoActive(workWechatApiLicenseOrder, purchase, providerAccessToken, corpId, userIds, duration, operatorId, operatorIp, operatorName);
        enterpriseWechatMessageAsyncSender.senderAsyncWechatUserStatus(WxLicenseDto.builder()
            .userIds(newUserIds)
            .corpId(corpId).build());
    }

    //购买并激活 -激活
    public void licenseInfoActive(WorkWechatApiLicenseOrder workWechatApiLicenseOrder, List<WorkWechatApiLicenseInfo> purchase, String providerAccessToken,
                                  String corpId, List<String> userIds, Integer duration, Long operatorId, String operatorIp, String operatorName) {
        ArrayList<String> successUserIds = Lists.newArrayList();
        ArrayList<String> errorUserIds = Lists.newArrayList();
        //剩余激活码
        Set<String> remainderCodes = Sets.newHashSet();
        Set<String> purchaseCodes = purchase.stream().map(WorkWechatApiLicenseInfo::getActiveCode).collect(Collectors.toSet());
        //购买并激活只会有一个订单
        WorkWechatApiLicenseInfo workWechatApiLicenseInfo = purchase.get(0);
        String orderId = workWechatApiLicenseInfo.getOrderId();
        List<WorkWechatUserActiveDto> invalidCodes = Lists.newArrayList();
        //激活
        this.activeByPurchase(EnterpriseWechatsUserOperationEvent.PURCHASE_ACTIVATE, providerAccessToken, corpId, userIds, successUserIds, errorUserIds, purchaseCodes, remainderCodes, invalidCodes, null, operatorId, operatorIp, operatorName);
        //此次激活有成员激活失败 剩余激活码 存储到数据库中
        if (CollectionUtils.isNotEmpty(remainderCodes)) {
            Set<String> orderIds = purchase.stream().map(WorkWechatApiLicenseInfo::getOrderId).collect(Collectors.toSet());
            List<WorkWechatApiLicenseInfo> licenseInfos = purchase.stream().filter(e -> remainderCodes.contains(e.getActiveCode())).collect(Collectors.toList());
            Map<String, List<WorkWechatApiLicenseOrder>> listMap = null;
            if (CollectionUtils.isNotEmpty(orderIds)) {
                List<WorkWechatApiLicenseOrder> workWechatLicenseOrders = this.getOrderInfo(providerAccessToken, corpId, orderIds);
                if (CollectionUtils.isNotEmpty(workWechatLicenseOrders)) {
                    workWechatApiLicenseOrderService.saveOrUpdateBatch(workWechatLicenseOrders);
                    listMap = workWechatLicenseOrders.stream().collect(Collectors.groupingBy(WorkWechatApiLicenseOrder::getOrderId));
                    Map<String, List<WorkWechatApiLicenseOrder>> finalListMap = listMap;
                    licenseInfos.forEach(m -> {
                        List<WorkWechatApiLicenseOrder> workWechatLicenseOrderList = finalListMap.get(m.getOrderId());
                        if (CollectionUtils.isNotEmpty(workWechatLicenseOrderList)) {
                            WorkWechatApiLicenseOrder workWechatLicenseOrder = workWechatLicenseOrderList.get(0);
                            JSONObject accountDuration = workWechatLicenseOrder.getAccountDuration();
                            Optional.ofNullable(accountDuration).ifPresent(n -> {
                                m.setMonths(n.getInteger("months")).setDays(n.getInteger("days"));
                            });
                        }
                    });
                }
            }
            this.saveBatch(licenseInfos);
        }
        //动作记录
        WorkWechatApiLicenseActionRecord workWechatApiLicenseActionRecord = WorkWechatApiLicenseActionRecord.builder()
            .corpId(corpId).orderId(orderId)
            .status(WorkWechatApiLicenseActionStatus.SUCCESS)
            .actionType(EnterpriseWechatsUserOperationEvent.PURCHASE_ACTIVATE)
            .apiLicenseNum(userIds.size()).duration(duration)
            .operatorId(operatorId).operatorIp(operatorIp).build();
        List<WorkWechatApiLicenseActionRecord.User> users = new ArrayList<>();
        Optional.ofNullable(successUserIds).filter(CollectionUtils::isNotEmpty).ifPresent(s -> {
            s.stream().map(m -> {
                WorkWechatApiLicenseActionRecord.User user = new WorkWechatApiLicenseActionRecord.User();
                user.setUserId(m).setStatus(WorkWechatApiLicenseActionStatus.SUCCESS);
                return user;
            }).forEach(users::add);
        });
        Optional.ofNullable(errorUserIds).filter(CollectionUtils::isNotEmpty).ifPresent(s -> {
            s.stream().map(m -> {
                WorkWechatApiLicenseActionRecord.User user = new WorkWechatApiLicenseActionRecord.User();
                user.setUserId(m).setStatus(WorkWechatApiLicenseActionStatus.FAILURE).setFailReason("成员存在问题，操作失败!");
                return user;
            }).forEach(users::add);
        });
        //失效的激活码
        Optional.ofNullable(invalidCodes).filter(CollectionUtils::isNotEmpty).ifPresent(s -> {
            Set<String> collect = s.stream().map(WorkWechatUserActiveDto::getActiveCode).collect(Collectors.toSet());
            Map<String, Object> accountMap = Maps.newHashMap();
            accountMap.put("corpid", corpId);
            accountMap.put("active_code_list", collect);
            Map<String, WorkWechatApiLicenseInfoFlow> infoFlowMap = null;
            try {
                JSONObject jsonObject = workWeixinApiClient.batchGetActiveInfoByCode(providerAccessToken, accountMap);
                if (!Objects.equals(jsonObject.getInteger("errcode"), 0)) {
                    log.info("查询激活码详情失败：{}", jsonObject);
                } else {
                    JSONArray activeInfoList = jsonObject.getJSONArray("active_info_list");
                    if (CollectionUtils.isNotEmpty(activeInfoList)) {
                        List<WorkWechatApiLicenseInfoFlow> workWechatApiLicenseInfoFlows = this.constructionLicense(corpId, null, activeInfoList);
                        infoFlowMap = workWechatApiLicenseInfoFlows.stream().collect(Collectors.toMap(WorkWechatApiLicenseInfoFlow::getActiveCode, Function.identity()));
                    }
                }
            } catch (Exception e) {
                log.info("购买并激活->获取激活码信息失败,corpId:[{}],activeCodeList:[{}]", corpId, collect, e);
            }
            Map<String, WorkWechatApiLicenseInfoFlow> finalInfoFlowMap = infoFlowMap;
            s.stream().map(m -> {
                String activeCode = m.getActiveCode();
                WorkWechatApiLicenseActionRecord.User user = new WorkWechatApiLicenseActionRecord.User();
                user.setUserId(m.getUserId()).setStatus(WorkWechatApiLicenseActionStatus.FAILURE).setFailReason("激活码被其他成员使用，操作失败!");
                if (StringUtils.isNotBlank(activeCode) && Objects.nonNull(finalInfoFlowMap)) {
                    WorkWechatApiLicenseInfoFlow workWechatApiLicenseInfoFlow = finalInfoFlowMap.get(activeCode);
                    if (Objects.nonNull(workWechatApiLicenseInfoFlow)) {
                        String userid = workWechatApiLicenseInfoFlow.getUserid();
                        user.setFailReason("激活码被其他成员使用，操作失败! 成员userId:" + userid);
                    }
                }
                return user;
            }).forEach(users::add);
        });
        if (Objects.nonNull(workWechatApiLicenseOrder)) {
            JSONObject accountDuration = workWechatApiLicenseOrder.getAccountDuration();
            if (Objects.nonNull(accountDuration)) {
                workWechatApiLicenseActionRecord.setDuration(accountDuration.getInteger("months"));
            }
            workWechatApiLicenseActionRecord.setAmount(workWechatApiLicenseOrder.getPrice())
                .setOperatorId(workWechatApiLicenseOrder.getOperatorId())
                .setOperatorIp(workWechatApiLicenseOrder.getOperatorIp());
        }
        workWechatApiLicenseActionRecord.setUsers(CollectionUtils.isNotEmpty(users) ? JSONArray.parseArray(JSONArray.toJSONString(users)) : null);
        workWechatApiLicenseActionRecordService.saveOrUpdate(workWechatApiLicenseActionRecord);
    }


    /**
     * @param successUserIds 成功的用户和激活码
     * @param userIds        需要激活的用户ID
     * @param errorUserIds   失败的用户 - 无法激活的用户
     * @param purchaseCodes  购买的激活码
     * @param remainderCodes 剩余的激活码
     * @param invalidCodes   无效的激活码
     * @param count          计数器 - 使用数据库中的激活码数量时使用
     */
    //通过激活码激活成员
    private void activeByPurchase(EnterpriseWechatsUserOperationEvent event, String providerAccessToken, String corpId,
                                  List<String> userIds,
                                  List<String> successUserIds,
                                  List<String> errorUserIds,
                                  Set<String> purchaseCodes,
                                  Set<String> remainderCodes,
                                  List<WorkWechatUserActiveDto> invalidCodes,
                                  AtomicInteger count, Long operatorId, String operatorIp, String operatorName) {
        ArrayList<String> purchaseList = Lists.newArrayList(purchaseCodes);
        //执行激活操作
        Map<String, Object> paramMap = MapUtil.builder(new HashMap<String, Object>()).put("corpid", corpId).map();
        List<Map<String, Object>> activeInfoList = new ArrayList<>();
        IntStream.range(0, userIds.size()).forEach(index -> {
            String userId = userIds.get(index);
            String activeCode = purchaseList.get(index);
            Map<String, Object> activeInfo = MapUtil.builder(new HashMap<String, Object>())
                .put("active_code", activeCode)
                .put("userid", userId).map();
            activeInfoList.add(activeInfo);
        });
        paramMap.put("active_list", activeInfoList);
        //批量激活账户
        JSONObject jsonObject = null;
        try {
            jsonObject = workWeixinApiClient.batchActiveAccount(providerAccessToken, paramMap);
        } catch (MarketingApiException e) {
            log.info("激活账号失败,paramMap:{}", JSONObject.toJSONString(paramMap), e);
            JSONObject eJsonObject = e.getJsonObject();
            if (Objects.nonNull(eJsonObject)) {
                JSONArray activeResult = eJsonObject.getJSONArray("active_result");
                if (CollectionUtils.isNotEmpty(activeResult)) {
                    JSONObject one = activeResult.getJSONObject(0);
                    Integer errcode = one.getInteger("errcode");
                    String userid = one.getString("userid");
                    if (Objects.nonNull(errcode) && !Objects.equals(errcode, 0)) {
                        EnterpriseWechatGlobalErrorCode globalErrorCode = EnumUtil.getByCode(errcode, EnterpriseWechatGlobalErrorCode.class);
                        if (Objects.nonNull(globalErrorCode)) {
                            throw new RestException("userid:" + userid + "," + globalErrorCode.getMessage());
                        }
                    }
                }
            }
        }
        if (!Objects.equals(jsonObject.getInteger("errcode"), 0)) {
            log.error("购买并激活账号失败：{}", jsonObject);
            throw new RestException("激活失败!");
        }
        JSONArray activeResult = jsonObject.getJSONArray("active_result");
        Optional.ofNullable(activeResult).filter(CollectionUtils::isNotEmpty).ifPresent(e -> {
            List<WorkWechatUserActiveDto> collect = e.stream().map(o -> (LinkedHashMap<String, Object>) o).map(o -> {
                String activeCode = Objects.nonNull(o.get("active_code")) ? String.valueOf(o.get("active_code")) : null;
                Integer errcode = Convert.toInt(o.get("errcode"), null);
                String userid = Objects.nonNull(o.get("userid")) ? String.valueOf(o.get("userid")) : null;
                return new WorkWechatUserActiveDto()
                    .setActiveCode(activeCode).setUserId(userid).setErrorCode(errcode);
            }).collect(Collectors.toList());
            //激活码已被使用过
            List<WorkWechatUserActiveDto> errorCollected = collect.stream().filter(m -> Objects.nonNull(m.getErrorCode()) && Objects.equals(m.getErrorCode(), EnterpriseWechatGlobalErrorCode.LICENSE_ALREADY_BOUND.getCode())).collect(Collectors.toList());
            //无法激活的userIds
            List<WorkWechatUserActiveDto> invalidCollected = collect.stream().filter(m -> Objects.nonNull(m.getErrorCode()) &&
                !Objects.equals(m.getErrorCode(), 0) && !Objects.equals(m.getErrorCode(), EnterpriseWechatGlobalErrorCode.LICENSE_ALREADY_BOUND.getCode())).collect(Collectors.toList());
            //激活成功
            List<WorkWechatUserActiveDto> collected = collect.stream().filter(m -> Objects.nonNull(m.getErrorCode()) && Objects.equals(m.getErrorCode(), 0)).collect(Collectors.toList());
            //将成功激活的用户加入到成功集合中 并从userIds中移除
            Optional.ofNullable(collected).filter(CollectionUtils::isNotEmpty).ifPresent(item -> {
                Set<String> successUserId = item.stream().map(WorkWechatUserActiveDto::getUserId).collect(Collectors.toSet());
                successUserIds.addAll(successUserId);
                Set<String> successActiveCodes = item.stream().map(WorkWechatUserActiveDto::getActiveCode).collect(Collectors.toSet());
                //计数器为空 说明不是通过数据库中的激活码激活的
                if (Objects.nonNull(count)) {
                    //清除可用许可证表记录
                    this.lambdaUpdate().eq(WorkWechatApiLicenseInfo::getCorpId, corpId)
                        .in(WorkWechatApiLicenseInfo::getActiveCode, successActiveCodes).remove();
                }
                userIds.removeAll(successUserIds);
            });
            //将无效激活码删除
            Optional.ofNullable(errorCollected).filter(CollectionUtils::isNotEmpty).ifPresent(item -> {
                Set<String> invalidActiveCodes = item.stream().map(WorkWechatUserActiveDto::getActiveCode).collect(Collectors.toSet());
                invalidCodes.addAll(item);
                //计数器为空 说明不是通过数据库中的激活码激活的
                if (Objects.nonNull(count)) {
                    //清除可用许可证表记录
                    this.lambdaUpdate().eq(WorkWechatApiLicenseInfo::getCorpId, corpId)
                        .in(WorkWechatApiLicenseInfo::getActiveCode, invalidActiveCodes).remove();
                }
            });
            //无法激活的userIds
            Optional.ofNullable(invalidCollected).filter(CollectionUtils::isNotEmpty).ifPresent(item -> {
                Set<String> invalidUserIds = item.stream().map(WorkWechatUserActiveDto::getUserId).collect(Collectors.toSet());
                //此次剩余的激活码
                if (Objects.isNull(remainderCodes)) {
                    item.stream().map(WorkWechatUserActiveDto::getActiveCode).forEach(remainderCodes::add);
                }
                errorUserIds.addAll(invalidUserIds);
                userIds.removeAll(invalidUserIds);
            });
            //还存在未激活的用户且不为购买并激活操作 - 递归调用
            if (CollectionUtils.isNotEmpty(userIds) && Objects.nonNull(count)) {
                count.addAndGet(1);
                execActive(event, corpId, providerAccessToken, successUserIds, userIds, errorUserIds, count, operatorId, operatorIp, operatorName);
            }
        });
    }


    //购买许可证
    private WxLicenseDto purchase(EnterpriseWechatsUserOperationEvent event, String providerAccessToken, String corpId, Integer licenseNum, List<String> userIds, Integer duration, Long totalPrice, Long operatorId, String operatorIp, String operatorName) {
        WxLicenseDto wxLicenseDto = new WxLicenseDto();
        Map<String, Object> accountCount = MapUtil.builder(new HashMap<String, Object>()).put("base_count", 0).put("external_contact_count", licenseNum).map();
        Map<String, Object> accountDuration = MapUtil.builder(new HashMap<String, Object>()).put("months", duration)
            .map();
        Map<String, Object> map = MapUtil.builder(new HashMap<String, Object>()).put("corpid", corpId)
            .put("buyer_userid", workWechatDevelopConf.getBuyerUserid())
            .put("account_count", accountCount)
            .put("account_duration", accountDuration).map();
        JSONObject newOrder = null;
        try {
            newOrder = workWeixinApiClient.createNewOrder(providerAccessToken, map);
        } catch (MarketingApiException e) {
            Integer errcode = e.getErrcode();
            EnterpriseWechatGlobalErrorCode errorCode = EnumUtil.getByCode(errcode, EnterpriseWechatGlobalErrorCode.class);
            if (Objects.nonNull(errorCode)) {
                log.info("购买许可证失败,corpId:{},error:{}", corpId, errorCode.getMessage());
                throw new RestException(errorCode.getMessage());
            } else {
                log.info("购买许可证失败,corpId:{}", corpId, e);
                throw new RestException("购买许可证失败!");
            }
        }
        if (!Objects.equals(newOrder.getInteger("errcode"), 0)) {
            log.info("购买许可证失败,corpId:{},error:{}", corpId, newOrder);
            return wxLicenseDto;
        }
        //订单ID
        String orderId = newOrder.getString("order_id");
        List<WorkWechatApiLicenseOrder> orderInfos = getOrderInfo(providerAccessToken, corpId, Sets.newHashSet(orderId));
        if (CollectionUtils.isEmpty(orderInfos)) {
            log.info("购买许可证失败,订单信息结果为空,corpId:[{}],orderId:[{}]", corpId, orderId);
            return wxLicenseDto;
        }
        orderInfos.forEach(orderInfo -> {
            orderInfo.setOperatorId(operatorId).setOperatorIp(operatorIp);
        });
        workWechatApiLicenseOrderService.saveOrUpdateBatch(orderInfos);
        //冻结余额
        OrganizationTransDTO transDTO = OrganizationTransDTO.builder()
            .bizType(OrganizationFlowBizType.API_LICENSE)
            .bizId(orderId)
            .amount(totalPrice)
            .build();
        organizationService.freezeBalance(transDTO);
        //执行购买操作
        WorkWechatApiLicenseOrder workWechatApiLicenseOrder = this.payOrder(providerAccessToken, corpId, orderId);
        if (Objects.isNull(workWechatApiLicenseOrder)) {
            log.info("购买许可证失败,订单支付失败,corpId:[{}],orderId:[{}]", corpId, orderId);
            return wxLicenseDto;
        }
        WorkWechatApiLicenseOrderStatus orderStatus = workWechatApiLicenseOrder.getOrderStatus();
        log.info("许可证购买后订单状态,orderId:{},status:{}", orderId, orderStatus);
        List<WorkWechatApiLicenseInfo> workWechatApiLicenseInfos = null;
        List<WorkWechatApiLicenseActionRecord.User> users = null;
        if (CollectionUtils.isNotEmpty(userIds)) {
            users = userIds.stream().map(userId -> {
                WorkWechatApiLicenseActionRecord.User user = new WorkWechatApiLicenseActionRecord.User();
                user.setUserId(userId).setStatus(WorkWechatApiLicenseActionStatus.PROCESSING);
                return user;
            }).collect(Collectors.toList());
        }
        Long organizationId = organizationService.getOrganizationId();
        WorkWechatApiLicenseActionRecord workWechatApiLicenseActionRecord = WorkWechatApiLicenseActionRecord.builder()
            .actionType(event)
            .corpId(corpId)
            .orderId(orderId)
            .realOrderId(orderId)
            .operatorIp(operatorIp)
            .operatorId(operatorId)
            .operatorName(operatorName)
            .status(WorkWechatApiLicenseActionStatus.PROCESSING)
            .users(CollectionUtils.isNotEmpty(users) ? JSONArray.parseArray(JSONArray.toJSONString(users)) : null)
            .agentId(TenantContextHolder.get())
            .organizationId(organizationId)
            .duration(duration)
            .apiLicenseNum(licenseNum)
            .build();
        if (WorkWechatApiLicenseOrderStatus.PAID.equals(orderStatus)) {
            //查询订单中的激活码账户列表
            workWechatApiLicenseInfos = this.getOrderAccount(providerAccessToken, Sets.newHashSet(orderId));
            log.info("购买许可证成功!orderId:{},actives:{}", orderId, JSONObject.toJSONString(workWechatApiLicenseInfos));
            workWechatApiLicenseActionRecord.setStatus(WorkWechatApiLicenseActionStatus.SUCCESS)
                .setAmount(workWechatApiLicenseOrder.getPrice());
        }
        workWechatApiLicenseActionRecordService.saveOrUpdate(workWechatApiLicenseActionRecord);
        wxLicenseDto.setWorkWechatApiLicenseInfos(workWechatApiLicenseInfos).setWorkWechatApiLicenseOrder(orderInfos.get(0));
        return wxLicenseDto;
    }

    //激活成员 - 基于系统中存在的剩余激活码
    private void activateBySystemCode(EnterpriseWechatLicenseDto enterpriseWechatLicenseDto) {
        List<String> userIds = enterpriseWechatLicenseDto.getUserId();
        String corpId = enterpriseWechatLicenseDto.getCorpId();
        Long operatorId = enterpriseWechatLicenseDto.getOperatorId();
        String operatorIp = enterpriseWechatLicenseDto.getOperatorIp();
        String operatorName = enterpriseWechatLicenseDto.getOperatorName();
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatCacheByCorpId(corpId);
        String providerAccessToken;
        try {
            providerAccessToken = enterpriseWechatDevelopOauthService.getProviderAccessToken();
        } catch (Exception e) {
            log.error("获取服务商ProviderAccessToken获取失败 corpId:[{}]", corpId);
            throw new RestException("操作失败!");
        }
        //查询校验成员状态信息
        List<WorkWechatApiLicenseUserVisibleRange> list = workWechatApiLicenseUserVisibleRangeService.lambdaQuery()
            .eq(WorkWechatApiLicenseUserVisibleRange::getCorpId, corpId)
            .in(WorkWechatApiLicenseUserVisibleRange::getUserId, userIds)
            .in(WorkWechatApiLicenseUserVisibleRange::getLicenseStatus, LicenseStatus.NOT_ACTIVE, LicenseStatus.INITIAL)
            .orderByDesc(WorkWechatApiLicenseUserVisibleRange::getId)
            .last(" limit " + userIds.size())
            .list();
        if (CollectionUtils.isEmpty(list) || !Objects.equals(list.size(), userIds.size())) {
            log.info("成员不存在未激活的记录,corpId:[{}],userId:[{}]", corpId, userIds);
            throw new RestException("激活失败!");
        }
        ArrayList<String> successUserIds = Lists.newArrayList();
        ArrayList<String> errorUserIds = Lists.newArrayList();
        //执行激活操作 单纯的激活 没有订单ID
        this.execActive(EnterpriseWechatsUserOperationEvent.ACTIVATE, corpId, providerAccessToken, successUserIds, userIds, errorUserIds, new AtomicInteger(1), operatorId, operatorIp, operatorName);
        //刷新成员
        try {
            if (CollectionUtils.isNotEmpty(successUserIds)) {
                successUserIds.forEach(e -> {
                    this.syncWechatCustomer(enterpriseWechat, e);
                });
            }
        } catch (Exception e) {
            log.error("刷新成员状态失败!corpId:{},userId:{}", corpId, successUserIds.get(0), e);
        }
    }

    /**
     * 执行批量激活
     *
     * @param successUserIds 成功的用户和激活码
     * @param userIds        需要激活的用户ID
     * @param errorUserIds   失败的用户 - 无法激活的用户
     */
    private void execActive(EnterpriseWechatsUserOperationEvent event, String corpId, String providerAccessToken,
                            List<String> successUserIds,
                            List<String> userIds, List<String> errorUserIds,
                            AtomicInteger count, Long operatorId, String operatorIp, String operatorName) {
        String result = CollectionUtils.isNotEmpty(successUserIds) ? String.join(",", successUserIds) : null;
        log.info("当前递归次数:{}", count.get());
        if (count.get() >= 5) {
            log.info("激活失败,已重试5次!,corpId:[{}]", corpId);
            log.info("corpId:{},剩余还需要激活的用户userIds:{},已激活的用户userIds:{},无效的用户userIds:{}", corpId, userIds, successUserIds, errorUserIds);
            throw new RestException("激活失败!" + (StringUtils.isNotBlank(result) ? "已激活的用户userIds:[" + result + "]" : ""));
        }
        //查询激活码列表
        List<WorkWechatApiLicenseInfo> workWechatApiLicenseInfos = this.lambdaQuery()
            .eq(WorkWechatApiLicenseInfo::getCorpId, corpId)
            .eq(WorkWechatApiLicenseInfo::getApiAccountType, ApiAccountType.INTERWORKING_ACCOUNT)
            .last(" limit " + userIds.size())
            .list();
        if (CollectionUtils.isEmpty(workWechatApiLicenseInfos) || workWechatApiLicenseInfos.size() < userIds.size()) {
            log.info("该企微下剩余激活码不足!,corpId:[{}]", corpId);
            log.info("已激活的用户userIds:{},无效的用户userIds:{}", successUserIds, errorUserIds);
            throw new RestException("剩余激活码不足,激活失败!" + (StringUtils.isNotBlank(result) ? "已激活的用户userIds:[" + result + "]" : ""));
        }
        Set<String> activeCodes = workWechatApiLicenseInfos.stream().map(WorkWechatApiLicenseInfo::getActiveCode).collect(Collectors.toSet());
        List<WorkWechatUserActiveDto> invalidCodes = Lists.newArrayList();
        //激活
        this.activeByPurchase(event, providerAccessToken, corpId, userIds, successUserIds, errorUserIds, activeCodes, null, invalidCodes, count, operatorId, operatorIp, operatorName);
        List<WorkWechatApiLicenseActionRecord> collect = new ArrayList<>();
        //这里只会有单纯的激活才会走
        Long organizationId = organizationService.getOrganizationId();
        Optional.ofNullable(successUserIds).filter(CollectionUtils::isNotEmpty).ifPresent(item -> {
            List<WorkWechatApiLicenseActionRecord> actionRecords = item.stream().map(e -> {
                WorkWechatApiLicenseActionRecord.User user = new WorkWechatApiLicenseActionRecord.User();
                user.setUserId(e).setStatus(WorkWechatApiLicenseActionStatus.SUCCESS);
                JSONArray objects = new JSONArray();
                objects.add(user);
                user.setUserId(e).setStatus(WorkWechatApiLicenseActionStatus.SUCCESS);
                return WorkWechatApiLicenseActionRecord.builder()
                    .corpId(corpId).operatorId(operatorId).operatorIp(operatorIp).actionType(event)
                    .status(WorkWechatApiLicenseActionStatus.SUCCESS)
                    .users(objects)
                    .agentId(TenantContextHolder.get())
                    .organizationId(organizationId)
                    .build();
            }).collect(Collectors.toList());
            collect.addAll(actionRecords);
        });
        Optional.ofNullable(errorUserIds).filter(CollectionUtils::isNotEmpty).ifPresent(item -> {
            List<WorkWechatApiLicenseActionRecord> actionRecords = item.stream().map(e -> {
                WorkWechatApiLicenseActionRecord.User user = new WorkWechatApiLicenseActionRecord.User();
                user.setUserId(e).setStatus(WorkWechatApiLicenseActionStatus.FAILURE).setFailReason("成员存在问题，操作失败!");
                JSONArray objects = new JSONArray();
                objects.add(user);
                return WorkWechatApiLicenseActionRecord.builder()
                    .corpId(corpId).operatorId(operatorId).operatorIp(operatorIp).actionType(event)
                    .status(WorkWechatApiLicenseActionStatus.FAILURE)
                    .users(objects)
                    .agentId(TenantContextHolder.get())
                    .organizationId(organizationId)
                    .build();
            }).collect(Collectors.toList());
            collect.addAll(actionRecords);
        });

        if (CollectionUtils.isNotEmpty(collect)) {
            workWechatApiLicenseActionRecordService.saveOrUpdateBatch(collect);
        }
    }


    //续费
    private void renew(EnterpriseWechatLicenseDto enterpriseWechatLicenseDto) {
        List<String> userId = enterpriseWechatLicenseDto.getUserId();
        String corpId = enterpriseWechatLicenseDto.getCorpId();
        if (CollectionUtils.isEmpty(userId)) {
            return;
        }
        //续费时长 - 月份
        Integer duration = enterpriseWechatLicenseDto.getDuration();
        //续费人数
        int size = userId.size();
        //所需费用
        Long totalPrice = priceCalculation(size, duration);
        //查询组织余额
        Long organizationServiceBalance = organizationService.getBalance();
        if (totalPrice > organizationServiceBalance) {
            String agentId = TenantContextHolder.get();
            log.error("组织余额不足，无法续费,agentId:[{}],totalPrice:[{}],organizationServiceBalance:[{}]", agentId, totalPrice, organizationServiceBalance);
            throw new RestException("余额不足,续费失败");
        }
        String providerAccessToken;
        try {
            providerAccessToken = enterpriseWechatDevelopOauthService.getProviderAccessToken();
        } catch (Exception e) {
            log.error("获取服务商ProviderAccessToken获取失败 corpId:[{}]", corpId);
            throw new RestException("续费失败!");
        }

        List<WorkWechatApiLicenseUserVisibleRange> list = workWechatApiLicenseUserVisibleRangeService.lambdaQuery()
            .eq(WorkWechatApiLicenseUserVisibleRange::getCorpId, corpId)
            .in(WorkWechatApiLicenseUserVisibleRange::getUserId, userId)
            .list();
        if (CollectionUtils.isEmpty(list)) {
            log.error("该企微下的成员许可证信息不存在,corpId:[{}]", corpId);
            throw new RestException("不存在成员信息，请先点击接口状态查询!");
        }
        Set<String> currentUserIds = list.stream().map(WorkWechatApiLicenseUserVisibleRange::getUserId).collect(Collectors.toSet());
        Set<String> notExist = userId.stream().filter(e -> !currentUserIds.contains(e)).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(notExist)) {
            log.error("该企微下的成员不存在,corpId:[{}],userId:[{}]", corpId, notExist);
            String join = String.join(",", notExist);
            throw new RestException("以下成员当前不存在许可证信息，不可续费！[{}]", join);
        }
        //判断是否满足续费条件   仅账号类型为互动账号并激活状态为已激活的成员（接口到期剩余时长小于等于20天），操作项 - 续费 按钮置亮，支持续费
        String errorUserIds = list.stream().filter(e -> userId.contains(e.getUserId())).filter(e -> {
            Instant now = Instant.now();
            Instant licenseExpireTime = e.getLicenseExpireTime();
            boolean flag = false;
            if (Objects.nonNull(licenseExpireTime) && licenseExpireTime.isAfter(now) && ChronoUnit.DAYS.between(now, licenseExpireTime) <= 20) {
                flag = true;
            }
            //不在激活期的 或者过期时间不在小于等于20天范围的
            return !LicenseStatus.ACTIVE.equals(e.getLicenseStatus()) || !flag;
        }).map(WorkWechatApiLicenseUserVisibleRange::getUserId).collect(Collectors.joining());
        if (StringUtils.isNotBlank(errorUserIds)) {
            log.error("该企微下的成员不满足续费条件,corpId:[{}],userId:[{}]", corpId, errorUserIds);
            throw new RestException("以下成员不满足续费条件，不可续费！[{" + errorUserIds + "}]");
        }
        //查询服务商账户余额 单位：分
        JSONObject accountBalance = workWeixinApiClient.getAccountBalance(providerAccessToken);
        if (Objects.isNull(accountBalance) || Objects.equals(accountBalance.getInteger("errcode"), 0L)) {
            log.error("获取企业微信账户余额失败,corpId:[{}]", corpId);
            throw new RestException("续费失败!");
        }
        Long balance = accountBalance.getLong("balance");
        if (balance < totalPrice) {
            log.error("服务商账户余额不足,corpId:[{}],totalPrice:[{}],balance:[{}]", corpId, totalPrice, balance);
            float totalPriceFloat = Objects.nonNull(totalPrice) ? totalPrice.floatValue() / 100 : 0f;
            float balanceFloat = Objects.nonNull(balance) ? balance.floatValue() / 100 : 0f;
            outSiteMsgSender.sendOutSiteMsg(OutSiteMsgType.FEISHU, workWechatDevelopConf.getServiceBalanceFeiShuKey(), "许可证续费,服务商余额不足", "企微接口许可购买余额不足，请联系管理员充值!\n服务商余额：" + balanceFloat + "元\n订单金额：" + totalPriceFloat + "元");
            throw new RestException("续费失败!");
        }
        //校验应用订单
        workWechatApiLicenseUserVisibleRangeService.checkApplicationOrder(corpId);
        //执行续费操作
        final String[] jobId = {null};
        //每次只能提交1000个 使用同一jobId进行串联
        Lists.partition(userId, 1000).forEach(m -> {
            List<Map<Object, Object>> collect = m.stream().map(e -> MapUtil.builder().put("userid", e).put("type", 2).map()).collect(Collectors.toList());
            Map<String, Object> map = MapUtil.builder(new HashMap<String, Object>())
                .put("corpid", corpId)
                .put("account_list", collect).map();
            if (StringUtils.isNotBlank(jobId[0])) {
                map.put("jobid", jobId[0]);
            }
            JSONObject jsonObject = workWeixinApiClient.createRenewOrderJob(providerAccessToken, map);
            if (!Objects.equals(jsonObject.getInteger("errcode"), 0)) {
                log.error("创建续期任务失败,corpId:[{}],jsonObject:{}", corpId, jsonObject.toJSONString());
                throw new RestException("创建续期任务失败!");
            }
            String string = jsonObject.getString("jobid");
            if (StringUtils.isBlank(jobId[0]) && StringUtils.isNotBlank(string)) {
                jobId[0] = string;
            }
        });
        Map<String, Object> months = MapUtil.builder(new HashMap<String, Object>()).put("months", duration).map();
        //提交续期订单
        Map<String, Object> map = MapUtil.builder(new HashMap<String, Object>())
            .put("jobid", jobId[0])
            .put("buyer_userid", workWechatDevelopConf.getBuyerUserid())
            .put("account_duration", months).map();
        JSONObject jsonObject = null;
        try {
            jsonObject = workWeixinApiClient.submitOrderJob(providerAccessToken, map);
        } catch (MarketingApiException e) {
            Integer errcode = e.getErrcode();
            EnterpriseWechatGlobalErrorCode errorCode = EnumUtil.getByCode(errcode, EnterpriseWechatGlobalErrorCode.class);
            if (Objects.nonNull(errorCode)) {
                log.error("提交续期订单失败,corpId:[{}],errorCode:[{}],errorMsg:[{}]", corpId, errorCode.getCode(), errorCode.getMessage());
            } else {
                log.error("提交续期订单失败,corpId:[{}]", corpId, e);
            }
            throw new RestException("提交续期订单失败!");
        }
        if (!Objects.equals(jsonObject.getInteger("errcode"), 0)) {
            log.error("提交续期任务失败,corpId:[{}],jsonObject:{}", corpId, jsonObject.toJSONString());
            throw new RestException("提交续期任务失败!");
        }
        //订单号
        String orderId = jsonObject.getString("order_id");
        //查询订单详情 如果是订单为支付成功 可能为服务商测试企业
        List<WorkWechatApiLicenseOrder> orderInfo = getOrderInfo(providerAccessToken, corpId, Sets.newHashSet(orderId));
        if (CollectionUtils.isEmpty(orderInfo)) {
            log.error("查询订单详情为空,corpId:[{}],orderId:[{}]", corpId, orderId);
            throw new RestException("查询订单详情失败!");
        }
        //先保存订单记录
        workWechatApiLicenseOrderService.saveOrUpdateBatch(orderInfo);
        WorkWechatApiLicenseOrder workWechatApiLicenseOrder = orderInfo.get(0);
        WorkWechatApiLicenseOrderStatus orderStatus = workWechatApiLicenseOrder.getOrderStatus();
        List<WorkWechatApiLicenseActionRecord.User> users = userId.stream().map(e -> {
            WorkWechatApiLicenseActionRecord.User user = new WorkWechatApiLicenseActionRecord.User();
            user.setUserId(e).setStatus(WorkWechatApiLicenseOrderStatus.PAID.equals(orderStatus) ? WorkWechatApiLicenseActionStatus.SUCCESS : WorkWechatApiLicenseActionStatus.PROCESSING);
            return user;
        }).collect(Collectors.toList());
        JSONArray objects = JSONArray.parseArray(JSONArray.toJSONString(users));
        String agentId = TenantContextHolder.get();
        Long organizationId = organizationService.getOrganizationId();
        //保存动作记录
        WorkWechatApiLicenseActionRecord workWechatApiLicenseActionRecord = WorkWechatApiLicenseActionRecord.builder()
            .corpId(corpId)
            .orderId(orderId)
            .realOrderId(orderId)
            .amount(workWechatApiLicenseOrder.getPrice())
            .users(objects)
            .operatorId(enterpriseWechatLicenseDto.getOperatorId())
            .operatorIp(enterpriseWechatLicenseDto.getOperatorIp())
            .operatorName(enterpriseWechatLicenseDto.getOperatorName())
            .actionType(EnterpriseWechatsUserOperationEvent.RENEW)
            .duration(duration)
            .status(WorkWechatApiLicenseOrderStatus.PAID.equals(orderStatus) ? WorkWechatApiLicenseActionStatus.SUCCESS : WorkWechatApiLicenseActionStatus.PROCESSING)
            .agentId(agentId)
            .organizationId(organizationId)
            .build();
        workWechatApiLicenseActionRecordService.saveOrUpdate(workWechatApiLicenseActionRecord);
        if (WorkWechatApiLicenseOrderStatus.PAID.equals(orderStatus)) {
            log.info("提交续期订单后查询订单状态：已支付,orderId:[{}]", orderId);
            return;
        }
        //冻结所需组织金额
        OrganizationTransDTO transDTO = OrganizationTransDTO.builder()
            .bizType(OrganizationFlowBizType.API_LICENSE)
            .bizId(orderId)
            .amount(totalPrice)
            .build();
        organizationService.freezeBalance(transDTO);
        //支付
        this.payOrder(providerAccessToken, corpId, orderId);

    }

    //使用余额支付且支付成功后检验修改订单状态
    @Transactional(rollbackFor = Exception.class)
    public WorkWechatApiLicenseOrder payOrder(String providerAccessToken, String corpId, String orderId) {
        OrganizationTransDTO transDTO = OrganizationTransDTO.builder().bizType(OrganizationFlowBizType.API_LICENSE)
            .bizId(orderId)
            .build();
        //使用余额支付
        JSONObject submitPayJobJson = null;
        try {
            submitPayJobJson = workWeixinApiClient.submitPayJob(providerAccessToken, MapUtil.builder(new HashMap<String, Object>())
                .put("order_id", orderId)
                .put("payer_userid", workWechatDevelopConf.getBuyerUserid()).map());
        } catch (MarketingApiException e) {
            log.error("订单支付失败,corpId:[{}],orderId:{}", corpId, orderId, e);
            //解冻余额
            organizationService.unfreezeBalance(transDTO);
            Integer errcode = e.getErrcode();
            EnterpriseWechatGlobalErrorCode errorCode = EnumUtil.getByCode(errcode, EnterpriseWechatGlobalErrorCode.class);
            if (Objects.nonNull(errorCode)) {
                log.error("订单支付失败,corpId:[{}],errorCode:[{}],errorMsg:[{}]", corpId, errorCode.getCode(), errorCode.getMessage());
                if (EnterpriseWechatGlobalErrorCode.TEST_ENTERPRISES_NOT_NEED_PAY.equals(errorCode)) {
                    List<WorkWechatApiLicenseOrder> orderInfos = getOrderInfo(providerAccessToken, corpId, Sets.newHashSet(orderId));
                    if (CollectionUtils.isNotEmpty(orderInfos)) {
                        return orderInfos.get(0);
                    }
                }
            }
            return null;
        }
        if (!Objects.equals(submitPayJobJson.getInteger("errcode"), 0)) {
            log.error("支付失败,corpId:[{}],jsonObject:{}", corpId, submitPayJobJson.toJSONString());
            //解冻余额
            organizationService.unfreezeBalance(transDTO);
            return null;
        }
        //支付任务ID
        String payJobId = submitPayJobJson.getString("jobid");
        log.info("orderId:{},支付任务ID:{}", orderId, payJobId);
        //查询订单详情
        List<WorkWechatApiLicenseOrder> orderInfos = getOrderInfo(providerAccessToken, corpId, Sets.newHashSet(orderId));
        if (CollectionUtils.isEmpty(orderInfos)) {
            log.error("查询订单详情失败,corpId:[{}],orderId:{}", corpId, orderId);
            return null;
        }
        WorkWechatApiLicenseOrder workWechatApiLicenseOrder = orderInfos.get(0);
        WorkWechatApiLicenseOrderStatus orderStatus = workWechatApiLicenseOrder.getOrderStatus();

        //修改订单状态
        workWechatApiLicenseOrderService.saveOrUpdateBatch(orderInfos);
        if (WorkWechatApiLicenseOrderStatus.PAID.equals(orderStatus)) {
            log.info("支付任务查询成功,订单状态为已支付,orderId:[{}]", orderId);
            Long price = workWechatApiLicenseOrder.getPrice();
            transDTO.setAmount(price);
            //扣减余额
            organizationService.deductBalance(transDTO);
        }
        return workWechatApiLicenseOrder;
    }


    //按照月份为单位计算所需价格
    public Long priceCalculation(Integer userNum, Integer duration) {
        List<LicensePriceDto> licensePrices = workWechatDevelopConf.getLicensePrices();
        if (CollectionUtils.isEmpty(licensePrices)) {
            log.error("获取企业微信价格配置为空");
            throw new RestException("获取企业微信价格配置异常");
        }
        List<Integer> numbers = IntStream.rangeClosed(1, userNum)
            .boxed()
            .collect(Collectors.toList());

        int fullYears = duration / 12;  // 完整年数
        int leftMonths = duration % 12;  // 剩余月份
        long totalYearsPrice = 0L;
        long totalMonthsPrice = 0L;
        if (fullYears > 0) {
            // 计算完整年数的价格
            totalYearsPrice = numbers.stream().map(e -> {
                Optional<LicensePriceDto> optionalLicensePriceDto = licensePrices.stream().filter(licensePrice -> {
                    Integer startNum = licensePrice.getStartNum();
                    Integer endNum = licensePrice.getEndNum();
                    String dateUnit = licensePrice.getDateUnit();
                    return (Objects.nonNull(startNum) && Objects.nonNull(endNum) && startNum < e && e <= endNum) && StringUtils.equals(dateUnit, "YEAR");
                }).findFirst();
                if (!optionalLicensePriceDto.isPresent()) {
                    log.error("按照年份为单位计算所需价格失败! userNum:{}", userNum);
                    throw new RestException("年份价格配置获取失败!");
                }
                LicensePriceDto licensePriceDto = optionalLicensePriceDto.get();
                return licensePriceDto.getPrice() * fullYears;
            }).reduce(0L, Long::sum);
        }

        if (leftMonths > 0) {
            // 计算剩余月份的价格
            totalMonthsPrice = numbers.stream().map(e -> {
                Optional<LicensePriceDto> optionalLicensePriceDto = licensePrices.stream().filter(licensePrice -> {
                    Integer startNum = licensePrice.getStartNum();
                    Integer endNum = licensePrice.getEndNum();
                    String dateUnit = licensePrice.getDateUnit();
                    return (Objects.nonNull(startNum) && Objects.nonNull(endNum) && startNum < e && e <= endNum) && StringUtils.equals(dateUnit, "MONTH");
                }).findFirst();
                if (!optionalLicensePriceDto.isPresent()) {
                    log.error("按照月份为单位计算所需价格失败! userNum:{}", userNum);
                    throw new RestException("价格配置获取失败!");
                }
                LicensePriceDto licensePriceDto = optionalLicensePriceDto.get();
                return licensePriceDto.getPrice() * leftMonths;
            }).reduce(0L, Long::sum);
        }

        Long totalPrice = totalYearsPrice + totalMonthsPrice;
        log.info("计算所需价格，userNum:{},totalPrice:{}", userNum, totalPrice);
        return totalPrice;
    }


    /**
     * 更新客服维度接口许可信息
     *
     * @param enterpriseWechat
     * @param userId
     * @return
     */
    public WorkWechatCustomerLicenseInfoDTO syncWechatCustomer(EnterpriseWechat enterpriseWechat, String userId) {
        String corpId = enterpriseWechat.getCorpid();
        String providerAccessToken;
        try {
            providerAccessToken = enterpriseWechatDevelopOauthService.getProviderAccessToken();
        } catch (Exception e) {
            throw new RestException("ProviderAccessToken获取失败");
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("corpid", corpId);
        map.put("suite_id", enterpriseWechat.getSuiteId());
        JSONObject appLicenseInfo = workWeixinApiClient.getAppLicenseInfo(providerAccessToken, map);
        if (appLicenseInfo == null || appLicenseInfo.getIntValue("errcode") != 0
            || appLicenseInfo.getLong("license_check_time") == null) {
            throw new RestException("获取应用的接口许可状态失败");
        }
        Instant licenseCheckTime = Instant.ofEpochSecond(appLicenseInfo.getLong("license_check_time"));
        Instant now = Instant.now();
        WorkWechatCustomerLicenseInfoDTO licenseInfo = workWechatCustomerLicenseInfoService.getActiveInfoByUser(userId, corpId, providerAccessToken, licenseCheckTime, now);
        if (licenseInfo == null) {
            throw new RestException("获取成员的激活详情失败");
        }
        landingPageSender.sendLicenseInfo(licenseInfo);
        landingPageWechatCustomerServiceService.update(new LambdaUpdateWrapper<LandingPageWechatCustomerService>()
            .set(LandingPageWechatCustomerService::getLicenseStatus, licenseInfo.getLicenseStatus())
            .set(LandingPageWechatCustomerService::getLicenseExpireTime, licenseInfo.getLicenseExpireTime())
            .eq(LandingPageWechatCustomerService::getCorpId, licenseInfo.getCorpId())
            .eq(LandingPageWechatCustomerService::getWechatUserId, licenseInfo.getUserId()));
        return licenseInfo;
    }


    /**
     * 刷新许可证自动激活状态
     *
     * @param corpId
     */
    public void autoActivateStatus(String corpId) {
        String providerAccessToken;
        try {
            providerAccessToken = enterpriseWechatDevelopOauthService.getProviderAccessToken();
            JSONObject jsonObject = workWeixinApiClient.getAutoActiveStatus(providerAccessToken, MapUtil.builder(new HashMap<String, Object>()).put("corpid", corpId).map());
            if (Objects.equals(jsonObject.getInteger("errcode"), 0)) {
                Integer autoActiveStatus = jsonObject.getInteger("auto_active_status");
                enterpriseWechatService.lambdaUpdate().set(EnterpriseWechat::getAutoActiveStatus, EnterpriseAutoActiveStatus.of(autoActiveStatus))
                    .eq(EnterpriseWechat::getCorpid, corpId).update();
                enterpriseWechatService.clearEnterpriseWechatCache(corpId);
            }
        } catch (Exception e) {
            log.error("刷新许可证状态失败!corpId:[{}]", corpId, e);
            throw new RestException("操作失败!");
        }

    }
}

