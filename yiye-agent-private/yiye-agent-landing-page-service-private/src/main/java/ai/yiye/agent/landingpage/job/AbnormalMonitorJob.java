package ai.yiye.agent.landingpage.job;


import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.domain.dto.platform.WechatCustomerServiceGroupDto;
import ai.yiye.agent.landingpage.controller.vo.AbnormalMonitorTakeJobVO;
import ai.yiye.agent.landingpage.dto.CustomerServiceChangeDto;
import ai.yiye.agent.landingpage.dto.CustomerServiceOfflineDTO;
import ai.yiye.agent.landingpage.dto.CustomerServiceOperationMessage;
import ai.yiye.agent.landingpage.enums.CustomerServiceChangeEnum;
import ai.yiye.agent.landingpage.enums.CustomerServiceOperation;
import ai.yiye.agent.landingpage.redis.AbnormalMonitorRedis;
import ai.yiye.agent.landingpage.sender.LandingPageCustomerServiceSender;
import ai.yiye.agent.landingpage.sender.LandingPageSender;
import ai.yiye.agent.landingpage.sender.MultiplayerCodeSender;
import ai.yiye.agent.landingpage.sender.TrafficEngineCustomerServiceOperationSender;
import ai.yiye.agent.landingpage.service.AgentConfService;
import ai.yiye.agent.landingpage.service.LandingPageWechatCustomerServiceAbnormalMonitorService;
import ai.yiye.agent.landingpage.service.LandingPageWechatCustomerServiceGroupRelService;
import ai.yiye.agent.landingpage.service.LandingPageWechatCustomerServiceService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;


/**
 * 客服异常监测
 **/
@Slf4j
@Component
public class AbnormalMonitorJob extends IJobHandler {

    @Resource
    private TrafficEngineCustomerServiceOperationSender trafficEngineCustomerServiceOperationSender;

    @Resource
    private LandingPageWechatCustomerServiceService landingPageWechatCustomerServiceService;

    @Resource
    private AgentConfService agentConfService;

    @Resource
    private AbnormalMonitorRedis abnormalMonitorRedis;

    @Resource
    private LandingPageWechatCustomerServiceAbnormalMonitorService abnormalMonitorService;

    @Resource
    private LandingPageWechatCustomerServiceGroupRelService landingPageWechatCustomerServiceGroupRelService;

    @Resource
    private MultiplayerCodeSender multiplayerCodeSender;

    @Resource
    private LandingPageCustomerServiceSender landingPageCustomerServiceSender;

    @Resource
    private LandingPageSender landingPageSender;

    @Override
    @XxlJob(value = "abnormalMonitorTakeEffectTimeJob")
    public ReturnT<String> execute(String param) throws Exception {
        log.info("==============【异常监测-设置生效或客服上线时间策略-任务调度开始】==============");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<AgentConf> list = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getStatus, 1));
        list.parallelStream().forEach(agentConf -> {
            log.info("==============【异常监测-设置生效或客服上线时间策略-任务调度-agentId:{}-开始】==============", agentConf.getAgentId());
            StopWatch stopWatchTemp = new StopWatch();
            stopWatchTemp.start();
            try {
                TenantContextHolder.set(agentConf.getAgentId());
                List<Long> offlineIds = Lists.newArrayList();
                //获取需要做统计的客服
                List<AbnormalMonitorTakeJobVO> services = abnormalMonitorService.getByAbnormalMonitorTakeEffectTime();
                if (CollectionUtils.isEmpty(services)) return;
                for (AbnormalMonitorTakeJobVO vo : services) {
                    LocalDateTime takeEffectTime = abnormalMonitorRedis.getTime(vo.getWechatUserId(), vo.getAdvertiserAccountGroupId(), RedisConstant.ABNORMAL_MONITOR_TAKE_EFFECT_TIME);
                    if (takeEffectTime == null) {
                        continue;
                    }
                    Integer inspectionCycle = vo.getInspectionCycle();
                    long minutes = Duration.between(takeEffectTime, LocalDateTime.now()).toMinutes();
                    if (inspectionCycle == null || inspectionCycle > minutes) {
                        continue;
                    }
                    LocalDateTime addWechatTime = abnormalMonitorRedis.getTime(vo.getWechatUserId(), vo.getAdvertiserAccountGroupId(), RedisConstant.ABNORMAL_MONITOR_ADD_WECHAT_TIME);
                    if (addWechatTime == null || addWechatTime.isBefore(takeEffectTime) || addWechatTime.isEqual(takeEffectTime)) {
                        offlineIds.add(vo.getId());
                    }
                }
                //异常监测下线
                landingPageWechatCustomerServiceService.abnormalMonitorOffline(offlineIds);
                //发送客服分组客服变更通知
                landingPageCustomerServiceSender.sendCallBackMessage(new WechatCustomerServiceGroupDto().setWechatCustomerServiceIds(Sets.newHashSet(offlineIds)));
                //客服异常监测下线发送互动广告
                setTrafficEngine(agentConf.getAgentId(), CustomerServiceOperation.OFFLINE, offlineIds);
                //发送变更项目客服分组多人活码队列
                sendCustomerServiceChange(agentConf.getAgentId(), offlineIds);
                //客服触发自动化规则或异常监测下线消息推送
                landingPageSender.customerServiceOffline(new CustomerServiceOfflineDTO().setIds(offlineIds));
            } catch (Exception e) {
                log.error("异常监测-设置生效或客服上线时间策略-任务调度-agentId:{}-异常", agentConf.getAgentId(), e);
            } finally {
                TenantContextHolder.clearContext();
                stopWatchTemp.stop();
                log.info("==============【异常监测-设置生效或客服上线时间策略-任务调度-agentId:{}-结束,耗时:{}S】==============", agentConf.getAgentId(), stopWatchTemp.getTotalTimeSeconds());
            }
        });
        stopWatch.stop();
        log.info("==============【异常监测-设置生效或客服上线时间策略-任务调度结束,耗时:{}S】==============", stopWatch.getTotalTimeSeconds());
        return SUCCESS;
    }

    /**
     * 客服异常监测上下线通知互动广告
     *
     * @param agentId
     * @param customerServiceOperation
     * @param ids
     */
    private void setTrafficEngine(String agentId, CustomerServiceOperation customerServiceOperation, List<Long> ids) {
        Optional.ofNullable(ids).ifPresent(e -> {
            if (ids.size() > 0) {
                ids.stream().forEach(m -> {
                    CustomerServiceOperationMessage offlineMessage = new CustomerServiceOperationMessage();
                    offlineMessage.setCustomerServiceOperation(customerServiceOperation)
                        .setLandingPageWechatCustomerServiceId(m)
                        .setAgentId(agentId);
                    trafficEngineCustomerServiceOperationSender.sendSearchCustomerServiceDetailAndSendTraffic(offlineMessage);
                });
            }
        });
    }

    @XxlJob(value = "abnormalMonitorIdentifyTimeJob")
    public ReturnT<String> abnormalMonitorIdentifyTimeJob(String param) throws Exception {
        log.info("==============【异常监测-客服二维码首次展示并被长按时策略-任务调度开始】==============");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<AgentConf> list = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getStatus, 1));
        list.parallelStream().forEach(agentConf -> {
            log.info("==============【异常监测-客服二维码首次展示并被长按时策略-任务调度-agentId:{}-开始】==============", agentConf.getAgentId());
            StopWatch stopWatchTemp = new StopWatch();
            stopWatchTemp.start();
            try {
                TenantContextHolder.set(agentConf.getAgentId());
                List<Long> offlineIds = Lists.newArrayList();
                //获取需要做统计的客服
                List<AbnormalMonitorTakeJobVO> services = abnormalMonitorService.getByAbnormalMonitorIdentifyTime();
                if (CollectionUtils.isEmpty(services)) return;
                for (AbnormalMonitorTakeJobVO vo : services) {
                    LocalDateTime identifyTime = abnormalMonitorRedis.getTime(vo.getWechatUserId(), vo.getAdvertiserAccountGroupId(), RedisConstant.ABNORMAL_MONITOR_FIRST_IDENTIFY_TIME);
                    if (identifyTime == null) {
                        continue;
                    }
                    Integer inspectionCycle = vo.getInspectionCycle();
                    long minutes = Duration.between(identifyTime, LocalDateTime.now()).toMinutes();
                    if (inspectionCycle == null || inspectionCycle > minutes) {
                        continue;
                    }
                    LocalDateTime addWechatTime = abnormalMonitorRedis.getTime(vo.getWechatUserId(), vo.getAdvertiserAccountGroupId(), RedisConstant.ABNORMAL_MONITOR_ADD_WECHAT_TIME);
                    if (addWechatTime == null || addWechatTime.isBefore(identifyTime) || addWechatTime.isEqual(identifyTime)) {
                        offlineIds.add(vo.getId());
                    }
                }
                //异常监测下线
                landingPageWechatCustomerServiceService.abnormalMonitorOffline(offlineIds);
                //发送客服分组客服变更通知
                landingPageCustomerServiceSender.sendCallBackMessage(new WechatCustomerServiceGroupDto().setWechatCustomerServiceIds(Sets.newHashSet(offlineIds)));
                //客服异常监测下线发送互动广告
                setTrafficEngine(agentConf.getAgentId(), CustomerServiceOperation.OFFLINE, offlineIds);
                //发送变更项目客服分组多人活码队列
                sendCustomerServiceChange(agentConf.getAgentId(), offlineIds);
                //客服触发自动化规则或异常监测下线消息推送
                landingPageSender.customerServiceOffline(new CustomerServiceOfflineDTO().setIds(offlineIds));
            } catch (Exception e) {
                log.error("异常监测-客服二维码首次展示并被长按时策略-任务调度-agentId:{}-异常", agentConf.getAgentId(), e);
            } finally {
                TenantContextHolder.clearContext();
                stopWatchTemp.stop();
                log.info("==============【异常监测-客服二维码首次展示并被长按时策略-任务调度-agentId:{}-结束,耗时:{}S】==============", agentConf.getAgentId(), stopWatchTemp.getTotalTimeSeconds());
            }
        });
        stopWatch.stop();
        log.info("==============【异常监测-客服二维码首次展示并被长按时策略-任务调度结束,耗时:{}S】==============", stopWatch.getTotalTimeSeconds());
        return SUCCESS;
    }

    @XxlJob(value = "abnormalMonitorIdentifyCountJob")
    public ReturnT<String> abnormalMonitorIdentifyCountJob(String param) throws Exception {
        log.info("==============【异常监测-按长按多次但未进粉控制策略-任务调度开始】==============");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<AgentConf> list = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getStatus, 1));
        list.parallelStream().forEach(agentConf -> {
            log.info("==============【异常监测-按长按多次但未进粉控制策略-任务调度-agentId:{}-开始】==============", agentConf.getAgentId());
            StopWatch stopWatchTemp = new StopWatch();
            stopWatchTemp.start();
            try {
                TenantContextHolder.set(agentConf.getAgentId());
                List<Long> offlineIds = Lists.newArrayList();
                //获取需要做统计的客服
                List<AbnormalMonitorTakeJobVO> services = abnormalMonitorService.getByAbnormalMonitorIdentifyCount();
                if (CollectionUtils.isEmpty(services)) return;
                for (AbnormalMonitorTakeJobVO vo : services) {
                    LocalDateTime lastIdentifyTime = abnormalMonitorRedis.getTime(vo.getWechatUserId(), vo.getAdvertiserAccountGroupId(), RedisConstant.ABNORMAL_MONITOR_LAST_IDENTIFY_TIME);
                    if (lastIdentifyTime == null) {
                        continue;
                    }
                    Integer inspectionCycle = vo.getInspectionCycle();
                    long minutes = Duration.between(lastIdentifyTime, LocalDateTime.now()).toMinutes();
                    if (inspectionCycle == null || inspectionCycle > minutes) {
                        continue;
                    }
                    LocalDateTime addWechatTime = abnormalMonitorRedis.getTime(vo.getWechatUserId(), vo.getAdvertiserAccountGroupId(), RedisConstant.ABNORMAL_MONITOR_ADD_WECHAT_TIME);
                    if (addWechatTime == null || addWechatTime.isBefore(lastIdentifyTime) || addWechatTime.isEqual(lastIdentifyTime)) {
                        offlineIds.add(vo.getId());
                    }
                }
                //异常监测下线
                landingPageWechatCustomerServiceService.abnormalMonitorOffline(offlineIds);
                //发送客服分组客服变更通知
                landingPageCustomerServiceSender.sendCallBackMessage(new WechatCustomerServiceGroupDto().setWechatCustomerServiceIds(Sets.newHashSet(offlineIds)));
                //客服异常监测下线发送互动广告
                setTrafficEngine(agentConf.getAgentId(), CustomerServiceOperation.OFFLINE, offlineIds);
                //发送变更项目客服分组多人活码队列
                sendCustomerServiceChange(agentConf.getAgentId(), offlineIds);
                //客服触发自动化规则或异常监测下线消息推送
                landingPageSender.customerServiceOffline(new CustomerServiceOfflineDTO().setIds(offlineIds));
            } catch (Exception e) {
                log.error("异常监测-按长按多次但未进粉控制策略-任务调度-agentId:{}-异常", agentConf.getAgentId(), e);
            } finally {
                TenantContextHolder.clearContext();
                stopWatchTemp.stop();
                log.info("==============【异常监测-按长按多次但未进粉控制策略-任务调度-agentId:{}-结束,耗时:{}S】==============", agentConf.getAgentId(), stopWatchTemp.getTotalTimeSeconds());
            }
        });
        stopWatch.stop();
        log.info("==============【异常监测-按长按多次但未进粉控制策略-任务调度结束,耗时:{}S】==============", stopWatch.getTotalTimeSeconds());
        return SUCCESS;
    }

    @XxlJob(value = "abnormalMonitorOfficialSendCodeCountJob")
    public ReturnT<String> abnormalMonitorOfficialSendCodeCountJob(String param) throws Exception {
        log.info("==============【异常监测-按公众号内发码多次未进粉控制策略-任务调度开始】==============");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<AgentConf> list = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getStatus, 1));
        list.parallelStream().forEach(agentConf -> {
            log.info("==============【异常监测-按公众号内发码多次未进粉控制策略-任务调度-agentId:{}-开始】==============", agentConf.getAgentId());
            StopWatch stopWatchTemp = new StopWatch();
            stopWatchTemp.start();
            try {
                TenantContextHolder.set(agentConf.getAgentId());
                List<Long> offlineIds = Lists.newArrayList();
                //获取需要做统计的客服
                List<AbnormalMonitorTakeJobVO> services = abnormalMonitorService.getByAbnormalMonitorOfficialSendCodeCount();
                if (CollectionUtils.isEmpty(services)) return;
                for (AbnormalMonitorTakeJobVO vo : services) {
                    LocalDateTime lastTime = abnormalMonitorRedis.getTime(vo.getWechatUserId(), vo.getAdvertiserAccountGroupId(), RedisConstant.ABNORMAL_MONITOR_LAST_OFFICIAL_SEND_CODE_TIME);
                    if (lastTime == null) {
                        continue;
                    }
                    Integer inspectionCycle = vo.getInspectionCycle();
                    long minutes = Duration.between(lastTime, LocalDateTime.now()).toMinutes();
                    if (inspectionCycle == null || inspectionCycle > minutes) {
                        continue;
                    }
                    LocalDateTime addWechatTime = abnormalMonitorRedis.getTime(vo.getWechatUserId(), vo.getAdvertiserAccountGroupId(), RedisConstant.ABNORMAL_MONITOR_ADD_WECHAT_TIME);
                    if (addWechatTime == null || addWechatTime.isBefore(lastTime) || addWechatTime.isEqual(lastTime)) {
                        offlineIds.add(vo.getId());
                    }
                }
                //异常监测下线
                landingPageWechatCustomerServiceService.abnormalMonitorOffline(offlineIds);
                //发送客服分组客服变更通知
                landingPageCustomerServiceSender.sendCallBackMessage(new WechatCustomerServiceGroupDto().setWechatCustomerServiceIds(Sets.newHashSet(offlineIds)));
                //客服异常监测下线发送互动广告
                setTrafficEngine(agentConf.getAgentId(), CustomerServiceOperation.OFFLINE, offlineIds);
                //发送变更项目客服分组多人活码队列
                sendCustomerServiceChange(agentConf.getAgentId(), offlineIds);
                //客服触发自动化规则或异常监测下线消息推送
                landingPageSender.customerServiceOffline(new CustomerServiceOfflineDTO().setIds(offlineIds));
            } catch (Exception e) {
                log.error("异常监测-按公众号内发码多次未进粉控制策略-任务调度-agentId:{}-异常", agentConf.getAgentId(), e);
            } finally {
                TenantContextHolder.clearContext();
                stopWatchTemp.stop();
                log.info("==============【异常监测-按公众号内发码多次未进粉控制策略-任务调度-agentId:{}-结束,耗时:{}S】==============", agentConf.getAgentId(), stopWatchTemp.getTotalTimeSeconds());
            }
        });
        stopWatch.stop();
        log.info("==============【异常监测-按公众号内发码多次未进粉控制策略-任务调度结束,耗时:{}S】==============", stopWatch.getTotalTimeSeconds());
        return SUCCESS;
    }

    @XxlJob(value = "abnormalMonitorRobotSendCodeCountJob")
    public ReturnT<String> abnormalMonitorRobotSendCodeCountJob(String param) throws Exception {
        log.info("==============【异常监测-按微信客服机器人内发码多次但未进粉控制策略-任务调度开始】==============");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<AgentConf> list = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getStatus, 1));
        list.parallelStream().forEach(agentConf -> {
            log.info("==============【异常监测-按微信客服机器人内发码多次但未进粉控制策略-任务调度-agentId:{}-开始】==============", agentConf.getAgentId());
            StopWatch stopWatchTemp = new StopWatch();
            stopWatchTemp.start();
            try {
                TenantContextHolder.set(agentConf.getAgentId());
                List<Long> offlineIds = Lists.newArrayList();
                //获取需要做统计的客服
                List<AbnormalMonitorTakeJobVO> services = abnormalMonitorService.getByAbnormalMonitorRobotSendCodeCount();
                if (CollectionUtils.isEmpty(services)) return;
                for (AbnormalMonitorTakeJobVO vo : services) {
                    LocalDateTime lastTime = abnormalMonitorRedis.getTime(vo.getWechatUserId(), vo.getAdvertiserAccountGroupId(), RedisConstant.ABNORMAL_MONITOR_LAST_ROBOT_SEND_CODE_TIME);
                    if (lastTime == null) {
                        continue;
                    }
                    Integer inspectionCycle = vo.getInspectionCycle();
                    long minutes = Duration.between(lastTime, LocalDateTime.now()).toMinutes();
                    if (inspectionCycle == null || inspectionCycle > minutes) {
                        continue;
                    }
                    LocalDateTime addWechatTime = abnormalMonitorRedis.getTime(vo.getWechatUserId(), vo.getAdvertiserAccountGroupId(), RedisConstant.ABNORMAL_MONITOR_ADD_WECHAT_TIME);
                    if (addWechatTime == null || addWechatTime.isBefore(lastTime) || addWechatTime.isEqual(lastTime)) {
                        offlineIds.add(vo.getId());
                    }
                }
                //异常监测下线
                landingPageWechatCustomerServiceService.abnormalMonitorOffline(offlineIds);
                //发送客服分组客服变更通知
                landingPageCustomerServiceSender.sendCallBackMessage(new WechatCustomerServiceGroupDto().setWechatCustomerServiceIds(Sets.newHashSet(offlineIds)));
                //客服异常监测下线发送互动广告
                setTrafficEngine(agentConf.getAgentId(), CustomerServiceOperation.OFFLINE, offlineIds);
                //发送变更项目客服分组多人活码队列
                sendCustomerServiceChange(agentConf.getAgentId(), offlineIds);
                //客服触发自动化规则或异常监测下线消息推送
                landingPageSender.customerServiceOffline(new CustomerServiceOfflineDTO().setIds(offlineIds));
            } catch (Exception e) {
                log.error("异常监测-按微信客服机器人内发码多次但未进粉控制策略-任务调度-agentId:{}-异常", agentConf.getAgentId(), e);
            } finally {
                TenantContextHolder.clearContext();
                stopWatchTemp.stop();
                log.info("==============【异常监测-按微信客服机器人内发码多次但未进粉控制策略-任务调度-agentId:{}-结束,耗时:{}S】==============", agentConf.getAgentId(), stopWatchTemp.getTotalTimeSeconds());
            }
        });
        stopWatch.stop();
        log.info("==============【异常监测-按微信客服机器人内发码多次但未进粉控制策略-任务调度结束,耗时:{}S】==============", stopWatch.getTotalTimeSeconds());
        return SUCCESS;
    }

    /**
     * 发送变更项目客服分组多人活码队列
     *
     * @param agentId
     * @param ids
     */
    private void sendCustomerServiceChange(String agentId, Collection<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            //获取分组id
            List<Long> groupIds = landingPageWechatCustomerServiceGroupRelService.getGroupIdsByServiceIds(ids);
            if (CollectionUtils.isNotEmpty(groupIds)) {
                CustomerServiceChangeDto dto = new CustomerServiceChangeDto();
                dto.setAgentId(agentId)
                    .setType(CustomerServiceChangeEnum.TOP_LINE_DOWNLINE)
                    .setLandingPageCustomerServiceGroupIds(new HashSet<>(groupIds));
                multiplayerCodeSender.sendCustomerServiceChange(dto);
            }
        }
    }

}
