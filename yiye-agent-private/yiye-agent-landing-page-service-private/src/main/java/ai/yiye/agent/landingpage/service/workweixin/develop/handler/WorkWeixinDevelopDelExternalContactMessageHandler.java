package ai.yiye.agent.landingpage.service.workweixin.develop.handler;


import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.LandingPageWechatOfficialAccountCustomer;
import ai.yiye.agent.domain.bo.CustomerBO;
import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.dto.EnterpriseWechatSessionDelDto;
import ai.yiye.agent.domain.dto.WechatCustomerTagDto;
import ai.yiye.agent.domain.enumerations.CustomerEventType;
import ai.yiye.agent.domain.enumerations.YiyeCustomRecordType;
import ai.yiye.agent.domain.landingpage.EnterpriseWechat;
import ai.yiye.agent.landingpage.sender.CustomerSender;
import ai.yiye.agent.landingpage.sender.EnterpriseSessionSender;
import ai.yiye.agent.landingpage.service.EnterpriseWechatService;
import ai.yiye.agent.landingpage.service.EnterpriseWechatTagService;
import ai.yiye.agent.weixin.domain.xml.WxCpTpXmlModelMessage;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import me.chanjar.weixin.cp.tp.message.WxCpTpMessageHandler;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 删除企业客户事件
 * 授权企业中配置了客户联系功能的成员删除外部联系人时，企业微信服务器会向应用的“指令回调URL”推送该事件
 */

@DS(DbConstants.POSTGRESQL_DEFAULT)
@Slf4j
@Service
public class WorkWeixinDevelopDelExternalContactMessageHandler implements WxCpTpMessageHandler {

    @Resource
    protected EnterpriseWechatService enterpriseWechatService;

    @Resource
    private EnterpriseSessionSender enterpriseSessionSender;
    @Resource
    private EnterpriseWechatTagService enterpriseWechatTagService;
    @Autowired
    private CustomerSender customerSender;

    @Override
    public WxCpXmlOutMessage handle(WxCpTpXmlMessage wxCpTpXmlMessage, Map<String, Object> context, WxCpTpService wxCpService, WxSessionManager sessionManager) throws WxErrorException {
        final Instant nowTime = Instant.now();
        if (Objects.isNull(wxCpTpXmlMessage)) {
            log.error("==>企业微信代开发V1-成员删除外部联系人事件，终止处理：wxCpTpXmlMessage={}；context={}；wxCpService={}；sessionManager={}；", JSONObject.toJSONString(wxCpTpXmlMessage), context, wxCpService, sessionManager);
            return null;
        }
        WxCpTpXmlModelMessage wxCpTpXmlModelMessage = (WxCpTpXmlModelMessage) wxCpTpXmlMessage;
        log.info("==>企业微信代开发V1-成员删除外部联系人事件 ======>> wxCpTpXmlModelMessage={}；context={}；wxCpService={}；sessionManager={}；", JSONObject.toJSONString(wxCpTpXmlModelMessage), context, wxCpService, sessionManager);
        String corpId = wxCpTpXmlModelMessage.getToUserName();
        EnterpriseWechat enterpriseWechat = enterpriseWechatService.getEnterpriseWechatCacheByCorpId(corpId);
        if (Objects.isNull(enterpriseWechat)) {
            log.info("==>企业微信代开发V1-查询不到当前企业信息，请联系客户重新授权 corpId:{}", corpId);
            return null;
        }
        Set<String> agentIds = enterpriseWechatService.getRelatedAgentByCorpId(corpId);
        if (CollectionUtils.isEmpty(agentIds)) {
            log.info("==>企业微信代开发V1-查询不到当前开启的企业或企业不存在，请联系客户重新授权 corpId:{}", corpId);
            return null;
        }

        final String userID = wxCpTpXmlMessage.getUserID();                                                                 //企微：同意添加好友的企业微信客服
        final String externalUserID = wxCpTpXmlMessage.getExternalUserID();
        log.info("==>企业微信代开发V1-成员删除外部联系人事件==>corpid:{},客服userId:{},外部联系人userId:{}",corpId,userID,externalUserID);
        if (StringUtils.isNotBlank(userID) && StringUtils.isNotBlank(externalUserID)){
            //修改会话存档对应记录
            EnterpriseWechatSessionDelDto enterpriseWechatSessionDelDto = new EnterpriseWechatSessionDelDto()
                .setCorpId(corpId)
                .setWorkUserID(userID)
                .setExternalUserID(externalUserID)
                .setWechatDeleteType(YiyeCustomRecordType.EMPLOYEE_DELETE_CUS);
            enterpriseSessionSender.sendDelCustomerInitCustomer(enterpriseWechatSessionDelDto);
            //清理会话存档的标签
            WechatCustomerTagDto wechatCustomerTagDto = new WechatCustomerTagDto();
            wechatCustomerTagDto.setCorpid(corpId)
                .setUserid(userID)
                .setExternalUserid(externalUserID);
            enterpriseSessionSender.sendDelCustomerTagChange(wechatCustomerTagDto);


            for (String agentId : agentIds) {
                TenantContextHolder.set(agentId);
                //客资标签记录清理标签
                enterpriseWechatTagService.deleteTagHistroy(externalUserID,agentId,corpId,userID);
                //公众号助手【公众号粉丝列表-更新删除好友时间】
                customerSender.sendUpdateDeleteWechatTime(new LandingPageWechatOfficialAccountCustomer()
                    .setAgentId(agentId)
                    .setWechatAppletExternalUserid(externalUserID)
                    .setWechatAppletUserid(userID)
                    .setDeleteWechatTime(nowTime)
                );

                log.info("企业微信代开发V1-成员删除外部联系人事件,发送消息队列进行上报 ");
                customerSender.sendForUpdateCustomerDeleteFriendInfo(new CustomerBO().setCustomerEventType(CustomerEventType.CUSTOMER_SERVICE_DELETE_EXTERNAL_CONTACT)
                        .setAgentId(agentId)
                        .setWechatAppletExternalUserid(externalUserID)
                        .setWechatAppletUserid(userID)
                        .setEventTime(nowTime)
                        .setCorpId(corpId));
                TenantContextHolder.clearContext();
            }
        }
        return null;
    }
}
