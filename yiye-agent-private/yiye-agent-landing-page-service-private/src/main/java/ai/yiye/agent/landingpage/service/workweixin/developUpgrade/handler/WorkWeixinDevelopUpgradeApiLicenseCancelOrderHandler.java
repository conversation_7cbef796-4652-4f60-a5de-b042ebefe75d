package ai.yiye.agent.landingpage.service.workweixin.developUpgrade.handler;

import ai.yiye.agent.domain.WorkWechatApplicationCustomizedAppOrder;
import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.landingpage.service.WorkWechatApplicationCustomizedAppOrderService;
import ai.yiye.agent.weixin.domain.xml.WxCpTpXmlModelMessage;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 服务商-收银台-下单成功通知、取消订单、改单通知、支付成功通知、退款通知、取消订单通知
 */
@DS(DbConstants.POSTGRESQL_DEFAULT)
@Slf4j
@Service
public class WorkWeixinDevelopUpgradeApiLicenseCancelOrderHandler extends WorkWechatUpgradeAddFriendCallbackToMatchPv {

    @Autowired
    private WorkWechatApplicationCustomizedAppOrderService workWechatApplicationCustomizedAppOrderService;

    @Override
    public WxCpXmlOutMessage handle(WxCpTpXmlMessage wxCpTpXmlMessage, Map<String, Object> context, WxCpTpService wxCpService, WxSessionManager sessionManager) {
        log.info("企业微信代开发V2-服务商-收银台-下单成功通知、取消订单、改单通知、支付成功通知、退款通知、取消订单通知-wxCpTpXmlMessage={}", JSONObject.toJSONString(wxCpTpXmlMessage));
        // 通过 getAllFieldsMap 获取 ChatId
        WxCpTpXmlModelMessage wxCpTpXmlModelMessage = (WxCpTpXmlModelMessage) wxCpTpXmlMessage;
        final String orderId = wxCpTpXmlModelMessage.getOrderId();
        if (StringUtils.isNotBlank(orderId)) {
            workWechatApplicationCustomizedAppOrderService.update(new LambdaUpdateWrapper<WorkWechatApplicationCustomizedAppOrder>()
                .set(WorkWechatApplicationCustomizedAppOrder::getOrderStatus, 3)
                .eq(WorkWechatApplicationCustomizedAppOrder::getOrderId, orderId)
            );
            workWechatApplicationCustomizedAppOrderService.reloadOtherData();
        }
        return null;
    }

}
