spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: snapshot
    version: #project.version#
  profiles:
    active: #spring.profiles.active#
  cloud:
    nacos:
      username: nacos
      password: nacosnotcoronavirus
      server-addr: 192.168.1.10:8848
      discovery:
        namespace: d7201ebd-6a18-42ec-918d-7a1282eef999
        metadata:
          version: ${spring.application.version}
      config:
        namespace: d7201ebd-6a18-42ec-918d-7a1282eef999
        file-extension: yml
        shared-configs:
          - data-id: yiye-agent-private-common.yml
            refresh: true
