package ai.yiye.agent.snapshot.sender;

import ai.yiye.agent.autoconfigure.rabbitmq.config.Constants;
import ai.yiye.agent.domain.dto.CompositeQrCodeBackgroundImageDto;
import ai.yiye.agent.domain.landingpage.SnapshotDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SnapshotSender {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送快照流处理回调
     * @param snapshotDto
     */
    public void sendSnapshotCallback(SnapshotDto snapshotDto){
        rabbitTemplate.convertAndSend(Constants.DEAL_WITH_CALLBACK_SNAPSHOT_EXCHANGE, Constants.DEAL_WITH_CALLBACK_SNAPSHOT_KEY, snapshotDto);
    }

    /**
     * 发送公众号历史文章页快照流处理回调
     * @param snapshotDto
     */
    public void sendSnapshotOfficialHistoryCallback(SnapshotDto snapshotDto){
        rabbitTemplate.convertAndSend(Constants.DEAL_WITH_CALLBACK_WECHAT_OFFICIAL_HISTORY_SNAPSHOT_EXCHANGE, Constants.DEAL_WITH_CALLBACK_WECHAT_OFFICIAL_HISTORY_SNAPSHOT_KEY, snapshotDto);
    }

    /**
     * 发送空白页页快照流处理回调
     * @param snapshotDto
     */
    public void sendSnapshotOfficialMidCallback(SnapshotDto snapshotDto){
        rabbitTemplate.convertAndSend(Constants.DEAL_WITH_CALLBACK_WECHAT_OFFICIAL_MID_SNAPSHOT_EXCHANGE, Constants.DEAL_WITH_CALLBACK_WECHAT_OFFICIAL_MID_SNAPSHOT_KEY, snapshotDto);
    }

    /**
     * 异步保存微信客服联系我二维码背景图
     */
    public void sendAsyncSaveContactMeQrCodeBackgroundImage(CompositeQrCodeBackgroundImageDto compositeImageDto) {
        rabbitTemplate.convertAndSend(Constants.LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_SAVE_CONTACT_ME_QR_CODE_BACKGROUND_IMAGE_EXCHANGE, Constants.LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_SAVE_CONTACT_ME_QR_CODE_BACKGROUND_IMAGE_QUEUE, compositeImageDto);
    }

    /**
     * 异步保存带二维码的图片并上传企业微信
     */
    public void sendAsyncSaveImageWithQrCodeToEnterpriseWechat(CompositeQrCodeBackgroundImageDto compositeImageDto) {
        rabbitTemplate.convertAndSend(Constants.SNAPSHOT_ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_BACKGROUND_IMG_GENERATE_RESULT_EXCHANGE, Constants.SNAPSHOT_ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_BACKGROUND_IMG_GENERATE_RESULT_QUERY, compositeImageDto);
    }

}
