package ai.yiye.agent.webhook.sender;


import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PushDataSender {

    private static final String PUSH_DATA_QUEUE = "yiye-agent-private.push-data.queue";

    private static final String DIRECT_EXCHANGE = "yiye.agent.private.direct";


    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void sendDataToQueue(JSONObject jsonObject){
        rabbitTemplate.convertAndSend(DIRECT_EXCHANGE, PUSH_DATA_QUEUE, jsonObject);
    }


}
