package ai.yiye.agent.adtrace.controller;

import ai.yiye.agent.adtrace.config.AdTraceConfig;
import ai.yiye.agent.adtrace.response.AdTraceResponse;
import ai.yiye.agent.adtrace.sender.AdTraceSender;
import ai.yiye.agent.adtrace.service.AdTraceConfService;
import ai.yiye.agent.adtrace.service.AdvertisingTraceService;
import ai.yiye.agent.domain.adtrace.CustomerMatchDTO;
import ai.yiye.agent.domain.adtrace.MatchStatus;
import ai.yiye.agent.domain.adtrace.PvInfoMatchDTO;
import ai.yiye.agent.domain.dto.AgentConfDto;
import ai.yiye.agent.domain.enumerations.AdTraceType;
import ai.yiye.agent.domain.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 广告监测模块
 *
 * @Author：lilidong
 * @Date：2023/6/15 15:31
 */
@Slf4j
@RestController
@RequestMapping("/advertise-trace")
public class AdvertisingTraceController {

    @Resource
    private AdTraceConfig adTraceConfig;

    @Resource
    private AdTraceConfService adTraceConfService;

    private static final Map<String, AdTraceType> AD_TRACE_TYPE_REF = new HashMap<>(4);

    static {
        AD_TRACE_TYPE_REF.put("click", AdTraceType.CLICK);
        AD_TRACE_TYPE_REF.put("impression", AdTraceType.IMPRESSION);
        AD_TRACE_TYPE_REF.put("effective-play", AdTraceType.EFFECTIVE_PLAY);
        AD_TRACE_TYPE_REF.put("play-over", AdTraceType.PLAY_OVER);
        AD_TRACE_TYPE_REF.put("play", AdTraceType.PLAY);
        AD_TRACE_TYPE_REF.put("begin-play", AdTraceType.BEGIN_PLAY);
        AD_TRACE_TYPE_REF.put("enterprise-wechat", AdTraceType.ENTERPRISE_WECHAT);
        AD_TRACE_TYPE_REF.put("pm-attention", AdTraceType.PM_ATTENTION);
        AD_TRACE_TYPE_REF.put("android-click", AdTraceType.ANDROID_CLICK);
        AD_TRACE_TYPE_REF.put("ios-click", AdTraceType.IOS_CLICK);
        AD_TRACE_TYPE_REF.put("web-click", AdTraceType.WEB_CLICK);
        AD_TRACE_TYPE_REF.put("mp-small-game", AdTraceType.MP_SMALL_GAME);
        AD_TRACE_TYPE_REF.put("mp-small-program", AdTraceType.MP_SMALL_PROGRAM);
        AD_TRACE_TYPE_REF.put("mp-native-page", AdTraceType.MP_NATIVE_PAGE);
    }


    @Resource
    private AdTraceSender adTraceSender;

    @Resource
    private AdvertisingTraceService advertisingTraceService;




    /**
     * 处理巨量广告监测回传的数据
     *
     * @param traceType 监测类型
     * @param params    回传的参数
     */
    @RequestMapping("/resolve-ad-trace-ocean/{traceType}")
    public AdTraceResponse resolveAdTraceFromOcean(@PathVariable String traceType, @RequestParam Map<String, Object> params) {
        try {
            log.info("巨量广告监测回传,监测类型=[{}],params=[{}],开关标识=[{}]", traceType, params, adTraceConfig.getOpenFlag());
            AdTraceType adTraceType = AD_TRACE_TYPE_REF.get(traceType);
            if (adTraceConfig.getOpenFlag() && Objects.nonNull(adTraceType)) {
                advertisingTraceService.sendTraceInfo(adTraceType, params);
            }
            return AdTraceResponse.OK_RESPONSE;
        }catch (Exception e) {
            log.warn("巨量广告监测回传, 参数 = {}，异常", params, e);
            return AdTraceResponse.BAD_RESPONSE;
        }
    }


    /**
     * 缓存pv的信息
     *
     * @param pvInfoMatchDTO pv信息
     */
    @PostMapping("/cachePvInfo")
    public void cachePvInfo(@RequestBody PvInfoMatchDTO pvInfoMatchDTO) {
        log.info("缓存pv的信息,开关标识=[{}]",adTraceConfig.getOpenFlag());
        if (adTraceConfig.getOpenFlag()) {
            adTraceSender.cachePvInMessage(pvInfoMatchDTO);
        }
    }

    /**
     * 处理快手广告监测回传的数据
     * @param pvInfoMatchDTO 回传的参数
     */
    @RequestMapping("/resolve-ks-ad-trace")
    public void resolveAdTraceFromKuaiShou(@RequestBody PvInfoMatchDTO pvInfoMatchDTO) {
        log.info("快手广告监测回传,url=[{}],开关标识=[{}]", pvInfoMatchDTO.getUrl(),adTraceConfig.getOpenFlag());
        if(adTraceConfig.getOpenFlag() && StringUtils.isNotBlank(pvInfoMatchDTO.getUrl())) {
            advertisingTraceService.sendKsAdTraceInfo(pvInfoMatchDTO);
        }
    }

    /**
     * 接收处理同步过来的广告监测的白名单信息
     */
    @PostMapping("/synCustomerAgentLicense")
    public void resolveCustomerAgentLicense(@RequestBody AgentConfDto agentConfDto){
        adTraceConfService.resolveCustomerAgentLicense(agentConfDto);
    }


    /**
     * 通过requestId ip 匹配是否存在点击监测数据
     * @return
     */
    @PostMapping("/matchPvInfo")
    public Result<MatchStatus> matchPvInfo(@RequestBody CustomerMatchDTO customerMatchDTO) {
        if (adTraceConfig.getOpenFlag()) {
            MatchStatus matchStatus = advertisingTraceService.matchPvInfo(customerMatchDTO);
            return Result.success(matchStatus);
        }
        return Result.success(MatchStatus.NOT_OPEN_MATCHEND);
    }
}
