package ai.yiye.agent.adtrace.job;

import ai.yiye.agent.adtrace.config.AdTraceConfig;
import ai.yiye.agent.adtrace.service.AdvertisingTraceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAccessor;
import java.util.Calendar;
import java.util.Date;

/**
 * 广告监测服务创建子表定时任务
 */

@Slf4j
@Component
public class AdTraceCreateTableJob extends IJobHandler {

    @Resource
    private AdvertisingTraceService advertisingTraceService;

    private static final String DATE_FORMATTER = "yyyyMMdd";

    private static final String AD_TRACE_SUB_TABLE = "advertising_trace";

    @Resource
    private AdTraceConfig adTraceConfig;


    @Override
    @XxlJob(value = "adTraceCreateTableJob")
    public ReturnT<String> execute(String param) {
        log.info("==============【广告监测服务创建子表任务-开始】,param=[{}]==============",param);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        String time = DateTimeFormatter.ofPattern(DATE_FORMATTER).format(LocalDateTime.now());
        log.info("当前日期:[{}]", time);
        //创建指定日期后面七天的日表
        if (StringUtils.isNotBlank(param)){
            //日期格式 yyyyMMdd
            if (dateStrIsValid(param)) {
                //创建监测事件为view的表
                String pvSubTableName = AD_TRACE_SUB_TABLE.concat("_view_").concat(param);
                advertisingTraceService.createSubTable(pvSubTableName,"view_".concat(param));
                //创建其他监测事件的表
                String subTableName = AD_TRACE_SUB_TABLE.concat("_").concat(param);
                advertisingTraceService.createSubTable(subTableName,param);
            }
        }else {
            for (int i = 0; i < 7; i++) {
                //一次创建一周的表
                String nextTime = getFutureDate(i);
                if (StringUtils.isNotBlank(nextTime)){
                    //创建监测事件为view的表
                    String pvSubTableName = AD_TRACE_SUB_TABLE.concat("_view_").concat(nextTime);
                    advertisingTraceService.createSubTable(pvSubTableName,"view_".concat(nextTime));
                    //创建其他监测事件的表
                    String subTableName = AD_TRACE_SUB_TABLE.concat("_").concat(nextTime);
                    advertisingTraceService.createSubTable(subTableName,nextTime);
                }
            }
        }
        stopWatch.stop();
        log.info("==============【广告监测服务创建子表任务-结束，耗时:{}S】==============", stopWatch.getTotalTimeSeconds());
        return SUCCESS;
    }

    @XxlJob(value = "dropAdTraceSubTableJob")
    public ReturnT<String> dropAdTraceSubTable(String param) {
        log.info("==============【广告监测服务删除子表任务-开始】==============");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        Integer expireDay = adTraceConfig.getExpireDay();
        String time = DateTimeFormatter.ofPattern(DATE_FORMATTER).format(LocalDateTime.now().minusDays(expireDay));
        log.info("需要删除清理的日期:[{}]", time);
        //删除监测事件表
        String pvSubTableName = AD_TRACE_SUB_TABLE.concat("_view_").concat(time);
        advertisingTraceService.deleteSubTableByDate(pvSubTableName,time);

        log.info("==============【广告监测服务删除子表任务-结束，耗时:{}S】==============", stopWatch.getTotalTimeSeconds());
        return ReturnT.SUCCESS;
    }

    /**
     * 获取当前日期后第N天的日期
     *
     * @param next 数字
     * @return 第next天的日期
     */
    public static String getFutureDate(int next) {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + next);
            Date today = calendar.getTime();
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
            return format.format(today);
        }catch (Exception e) {
            log.error("获取当前日期后第[{}]天的日期异常",next,e);
        }
        return null;
    }


    /**
     * 验证字符串是否为指定日期格式
     *
     * @param dateString 待验证字符串
     * @return 有效性结果, true 为正确, false 为错误
     */
    public static boolean dateStrIsValid(String dateString) {

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        TemporalAccessor date = null;
        try {
            date = dateTimeFormatter.parse(dateString);
            return dateString.equals(dateTimeFormatter.format(date));
        } catch (DateTimeParseException e) {
            log.error("创建广告监测数据表，传参格式不正确，传入的参数是:[{}]",dateString,e);
            return false;
        }
    }

}
