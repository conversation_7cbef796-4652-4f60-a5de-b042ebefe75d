package ai.yiye.agent.adtrace.service;

import ai.yiye.agent.adtrace.mapper.AdTraceConfMapper;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AdTraceConf;
import ai.yiye.agent.domain.dto.AgentConfDto;
import ai.yiye.agent.domain.enumerations.AdTraceWhiteListType;
import ai.yiye.agent.domain.enumerations.DeleteStatus;
import ai.yiye.agent.domain.enumerations.OceanEngineConstructTrackUrlLicenseStatus;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Author：lilidong
 * @Date：2023/6/30 11:43
 */
@Slf4j
@Service
public class AdTraceConfService extends ServiceImpl<AdTraceConfMapper, AdTraceConf> {


    public void resolveCustomerAgentLicense(AgentConfDto agentConfDto) {
        try {
            TenantContextHolder.clearContext();
            AdTraceConf adTraceConf = new AdTraceConf();
            adTraceConf.setAgentId(agentConfDto.getAgentId());
            adTraceConf.setStatus(agentConfDto.getStatus());
            adTraceConf.setPlatformId(agentConfDto.getPlatformId());
            //非关闭状态
            if (Objects.nonNull(agentConfDto.getLicense())) {
                if (!Objects.equals(agentConfDto.getLicense().getOceanEngineConstructTrackUrlLicenseStatus(), OceanEngineConstructTrackUrlLicenseStatus.CLOSE)) {
                    adTraceConf.setAdTraceWhiteListType(AdTraceWhiteListType.OPEN);
                } else {
                    adTraceConf.setAdTraceWhiteListType(AdTraceWhiteListType.CLOSE);
                }
            }
            //先查表中是否有记录，因为状态会更新
            AdTraceConf one = this.lambdaQuery().eq(AdTraceConf::getAgentId, agentConfDto.getAgentId()).eq(AdTraceConf::getPlatformId, agentConfDto.getPlatformId()).orderByDesc(AdTraceConf::getCreatedAt).last(" limit 1").one();
            if (Objects.nonNull(one)) {
                adTraceConf.setId(one.getId());
            }
            this.saveOrUpdate(adTraceConf);
        } catch (Exception e) {
            log.error("同步用户广告监测链接白名单信息异常", e);
        }
    }

    /**
     * 查询用户是否开启白名单
     *
     * @param agentId    用户标识
     * @param platformId 平台ID
     */
    public boolean queryWhiteListOpen(String agentId, Integer platformId) {
        try {
            if (StringUtils.isNotBlank(agentId) && Objects.nonNull(platformId)) {
                AdTraceConf one = this.lambdaQuery().eq(AdTraceConf::getAgentId, agentId)
                    .eq(AdTraceConf::getPlatformId, platformId)
                    .eq(AdTraceConf::getDeleteStatus, DeleteStatus.NORMAL)
                    .orderByDesc(AdTraceConf::getCreatedAt)
                    .last(" limit 1").one();
                log.info("用户白名单详情,one = {}", JSONObject.toJSONString(one));
                return Objects.nonNull(one) && Objects.equals(one.getAdTraceWhiteListType(), AdTraceWhiteListType.OPEN);
            }
        }catch (Exception e) {
            log.error("查询用户白名单配置异常,agentId = [{}]", agentId, e);
        }
        return false;
    }

}
