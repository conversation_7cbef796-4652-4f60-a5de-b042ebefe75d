package ai.yiye.agent.adtrace.listener;

import ai.yiye.agent.adtrace.service.AdvertisingTraceService;
import ai.yiye.agent.autoconfigure.rabbitmq.config.Constants;
import ai.yiye.agent.domain.adtrace.AdTraceDTO;
import ai.yiye.agent.domain.adtrace.PvInfoMatchDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author：lilidong
 * @Date：2023/6/15 15:40
 */

@Slf4j
@Component
public class KuaiShouAdTraceListener {

    @Resource
    private AdvertisingTraceService advertisingTraceService;


    @RabbitListener(bindings = {@QueueBinding(
        key = Constants.LANDING_PAGE_KUAI_SHOU_AD_TRACE_MESSAGE_QUEUE, value = @Queue(value = Constants.LANDING_PAGE_KUAI_SHOU_AD_TRACE_MESSAGE_QUEUE, durable = "true", autoDelete = "false", exclusive = "false"),
        exchange = @Exchange(name = Constants.LANDING_PAGE_KUAI_SHOU_AD_TRACE_MESSAGE_EXCHANGE, type = ExchangeTypes.TOPIC))})
    @RabbitHandler
    public void resolveAdTrace(final PvInfoMatchDTO pvInfoMatchDTO) {
        try {
            log.info("监听到快手广告数据回传,pid={},url=[{}]", pvInfoMatchDTO.getPid(), pvInfoMatchDTO.getUrl());
            advertisingTraceService.resolveKuaiShouAdTrace(pvInfoMatchDTO);
        } catch (Exception e) {
            log.error("消费者监听快手广告数据回传异常,pvInfoMatchDTO = [{}]",pvInfoMatchDTO, e);
        }
    }

}
