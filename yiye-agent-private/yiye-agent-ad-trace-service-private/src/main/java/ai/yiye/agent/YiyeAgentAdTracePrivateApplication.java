package ai.yiye.agent;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * @Author：lilidong
 * @Date：2023/6/14 9:51
 */
@EnableDiscoveryClient
@SpringBootApplication
@MapperScan("ai.yiye.agent.adtrace.mapper")
public class YiyeAgentAdTracePrivateApplication {

    public static void main(String[] args) {
        SpringApplication.run(YiyeAgentAdTracePrivateApplication.class);
        System.out.println("广告监控服务启动成功");
    }
}
