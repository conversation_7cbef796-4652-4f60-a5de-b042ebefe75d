<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.yiye.agent.adtrace.mapper.AdvertisingTraceMapper">
    <resultMap id="BaseResultMap" type="ai.yiye.agent.domain.AdvertisingTrace">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="agent_id" jdbcType="VARCHAR" property="agentId"/>
        <result column="trace_type" jdbcType="INTEGER" property="traceType"/>
        <result column="pv_pid" jdbcType="VARCHAR" property="pvPid"/>
        <result column="pv_uid" jdbcType="VARCHAR" property="pvUid"/>
        <result column="link_id" jdbcType="VARCHAR" property="linkId"/>
        <result column="ext" jdbcType="VARCHAR" property="ext"/>
        <result column="mid1" jdbcType="VARCHAR" property="mid1"/>
        <result column="mid2" jdbcType="VARCHAR" property="mid2"/>
        <result column="mid3" jdbcType="VARCHAR" property="mid3"/>
        <result column="mid4" jdbcType="VARCHAR" property="mid4"/>
        <result column="mid5" jdbcType="VARCHAR" property="mid5"/>
        <result column="mid6" jdbcType="VARCHAR" property="mid6"/>
        <result column="event_asset_id" jdbcType="INTEGER" property="eventAssetId"/>
        <result column="platform_id" jdbcType="INTEGER" property="platformId"/>
        <result column="account_id" jdbcType="VARCHAR" property="adgroupId"/>
        <result column="adgroup_id" jdbcType="VARCHAR" property="adgroupId"/>
        <result column="adgroup_name" jdbcType="VARCHAR" property="adgroupName"/>
        <result column="click_id" jdbcType="VARCHAR" property="clickId"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="ipv4" jdbcType="VARCHAR" property="ipv4"/>
        <result column="ipv6" jdbcType="VARCHAR" property="ipv6"/>
        <result column="ad_id" jdbcType="VARCHAR" property="adId"/>
        <result column="ad_name" jdbcType="VARCHAR" property="adName"/>
        <result column="promotion_id" jdbcType="VARCHAR" property="promotionId"/>
        <result column="promotion_name" jdbcType="VARCHAR" property="promotionName"/>
        <result column="project_id" jdbcType="VARCHAR" property="projectId"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="creative_id" jdbcType="VARCHAR" property="creativeId"/>
        <result column="creative_name" jdbcType="VARCHAR" property="creativeName"/>
        <result column="c_type" jdbcType="VARCHAR" property="cType"/>
        <result column="advertiser_id" jdbcType="VARCHAR" property="advertiserId"/>
        <result column="c_site" jdbcType="VARCHAR" property="cSite"/>
        <result column="c_type" jdbcType="VARCHAR" property="cType"/>
        <result column="convert_id" jdbcType="VARCHAR" property="convertId"/>
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="track_id" jdbcType="VARCHAR" property="trackId"/>
        <result column="sl" jdbcType="VARCHAR" property="sl"/>
        <result column="imei" jdbcType="VARCHAR" property="imei"/>
        <result column="imei2" jdbcType="VARCHAR" property="imei2"/>
        <result column="imei3" jdbcType="VARCHAR" property="imei3"/>
        <result column="imei4" jdbcType="VARCHAR" property="imei4"/>
        <result column="idfa" jdbcType="VARCHAR" property="idfa"/>
        <result column="idfa2" jdbcType="VARCHAR" property="idfa2"/>
        <result column="idfa3" jdbcType="VARCHAR" property="idfa3"/>
        <result column="idfa_md5" jdbcType="VARCHAR" property="idfaMd5"/>
        <result column="kenyid_caa" jdbcType="VARCHAR" property="kenyidCaa"/>
        <result column="android_id" jdbcType="VARCHAR" property="androidId"/>
        <result column="android_id2" jdbcType="VARCHAR" property="androidId2"/>
        <result column="android_id3" jdbcType="VARCHAR" property="androidId3"/>
        <result column="oaid" jdbcType="VARCHAR" property="oaid"/>
        <result column="oaid_md5" jdbcType="VARCHAR" property="oaidMd5"/>
        <result column="os" jdbcType="VARCHAR" property="os"/>
        <result column="mac" jdbcType="VARCHAR" property="mac"/>
        <result column="mac2" jdbcType="VARCHAR" property="mac2"/>
        <result column="mac3" jdbcType="VARCHAR" property="mac3"/>
        <result column="ua" jdbcType="VARCHAR" property="ua"/>
        <result column="geo" jdbcType="VARCHAR" property="geo"/>
        <result column="ts" jdbcType="VARCHAR" property="ts"/>
        <result column="callback_param" jdbcType="VARCHAR" property="callbackParam"/>
        <result column="callback_url" jdbcType="VARCHAR" property="callbackUrl"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="union_site" jdbcType="VARCHAR" property="unionSite"/>
        <result column="caid" jdbcType="VARCHAR" property="caid"/>
        <result column="caid1" jdbcType="VARCHAR" property="caid1"/>
        <result column="caid2" jdbcType="VARCHAR" property="caid2"/>
        <result column="caid1_md5" jdbcType="VARCHAR" property="caid1Md5"/>
        <result column="caid2_md5" jdbcType="VARCHAR" property="caid2Md5"/>
        <result column="photo_id" jdbcType="VARCHAR" property="photoId"/>
        <result column="ac_creative" jdbcType="VARCHAR" property="acCreative"/>
        <result column="winfo_id" jdbcType="VARCHAR" property="winfoId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="ad_created_at" jdbcType="TIMESTAMP" property="adCreatedAt"/>
        <result column="pv_created_at" jdbcType="TIMESTAMP" property="pvCreatedAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="delete_status" jdbcType="INTEGER" property="deleteStatus"/>
    </resultMap>


    <sql id="Base_Column_List">
       id,agent_id,trace_type,pv_pid,pv_uid,link_id,ext,mid1,mid2,mid3,mid4,mid5,mid6,event_asset_id,platform_id,account_id,adgroup_id,adgroup_name,click_id,ip,ipv4,ipv6,ad_id,ad_name,promotion_id,
       promotion_name,project_id,project_name,creative_id,creative_name,c_type,advertiser_id,c_site,convert_id,request_id,track_id,sl,imei,imei2,imei3,imei4,idfa,idfa2,idfa3,idfa_md5,kenyid_caa,android_id,
       android_id2,android_id3,oaid,oaid_md5,os,mac,mac1,mac2,mac3,ua,geo,ts,callback_param,callback_url,model,union_site,caid,caid1,caid2,caid1_md5,caid2_md5,photo_id,ac_creative,winfo_id,created_at,updated_at,delete_status
    </sql>


    <insert id="createAdTraceSubTable">
        create table if not exists "advertising_trace_${time}"(like "advertising_trace" including all) inherits(advertising_trace);
    </insert>

    <delete id="dropAdTraceSubTable">
        drop table if exists advertising_trace_${time};
        drop table if exists advertising_trace_view_${time};
    </delete>

</mapper>

