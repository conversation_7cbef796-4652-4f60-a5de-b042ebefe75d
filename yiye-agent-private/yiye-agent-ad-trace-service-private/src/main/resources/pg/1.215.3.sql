CREATE TABLE IF NOT EXISTS "ad_trace_conf" (
    "id" bigserial NOT NULL PRIMARY KEY,
    "agent_id" VARCHAR,
    "platform_id" int4 NOT NULL DEFAULT 0,
    "status" int4 NOT NULL,
    "created_at" TIMESTAMP (6) NOT NULL DEFAULT now(),
    "updated_at" TIMESTAMP (6) NOT NULL DEFAULT now(),
    "ad_trace_white_list_type" int4 DEFAULT 0,
    "delete_status" int4 DEFAULT 0
    );

COMMENT ON COLUMN "ad_trace_conf"."agent_id" IS '客户标识';

COMMENT ON COLUMN "ad_trace_conf"."platform_id" IS '平台标识';

COMMENT ON COLUMN "ad_trace_conf"."created_at" IS '创建时间';

COMMENT ON COLUMN "ad_trace_conf"."updated_at" IS '更新时间';

COMMENT ON COLUMN "ad_trace_conf"."delete_status" IS '逻辑删除标记 0-正常；1-删除';

CREATE INDEX if not exists  "agent_id_trace_index" ON "ad_trace_conf" USING btree (
    "agent_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
