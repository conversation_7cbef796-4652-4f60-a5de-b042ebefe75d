DROP TABLE IF EXISTS advertising_trace;

CREATE TABLE IF NOT EXISTS "advertising_trace" (
                                                   "id" bigserial NOT NULL PRIMARY KEY,
                                                   "agent_id" VARCHAR,
                                                   "trace_type" int4,
                                                   "pv_pid"  VARCHAR,
                                                   "pv_uid" VARCHAR,
                                                   "link_id" VARCHAR,
                                                   "ext" jsonb,
                                                   "mid1"  VARCHAR,
                                                   "mid2"  VARCHAR,
                                                   "mid3"  VARCHAR,
                                                   "mid4"  VARCHAR,
                                                   "mid5"  VARCHAR,
                                                   "mid6"  VARCHAR,
                                                   "oaid2" VARCHAR,
                                                   "event_asset_id" VARCHAR,
                                                   "platform_id" int4,
                                                   "account_id" VARCHAR NOT NULL,
                                                   "adgroup_id" VARCHAR,
                                                   "adgroup_name" VARCHAR,
                                                   "click_id" VARCHAR,
                                                   "ip" VARCHAR NOT NULL,
                                                   "ipv4" VARCHAR,
                                                   "ipv6" VARCHAR,
                                                   "ad_id" VARCHAR,
                                                   "ad_name" <PERSON>RC<PERSON><PERSON>,
                                                   "promotion_id" VARCHAR,
                                                   "promotion_name" VARCHAR,
                                                   "project_id" VARCHAR,
                                                   "project_name" VARCHAR,
                                                   "creative_id" VA<PERSON>HAR,
                                                   "creative_name" <PERSON><PERSON>HA<PERSON>,
                                                   "c_type" VA<PERSON>HA<PERSON>,
                                                   "advertiser_id" VARC<PERSON><PERSON>,
                                                   "c_site" VARCHAR,
                                                   "convert_id" VARCHAR,
                                                   "request_id" VARCHAR,
                                                   "track_id" VARCHAR,
                                                   "sl" VARCHAR,
                                                   "imei" VARCHAR,
                                                   "imei2" VARCHAR,
                                                   "imei3" VARCHAR,
                                                   "imei4" VARCHAR,
                                                   "idfa" VARCHAR,
                                                   "idfa2" VARCHAR,
                                                   "idfa3" VARCHAR,
                                                   "idfa_md5" VARCHAR,
                                                   "kenyid_caa" VARCHAR,
                                                   "android_id" VARCHAR,
                                                   "android_id2" VARCHAR,
                                                   "android_id3" VARCHAR,
                                                   "oaid" VARCHAR,
                                                   "oaid_md5" VARCHAR,
                                                   "os" VARCHAR,
                                                   "mac" VARCHAR,
                                                   "mac1" VARCHAR,
                                                   "mac2" VARCHAR,
                                                   "mac3" VARCHAR,
                                                   "ua" VARCHAR,
                                                   "geo" VARCHAR,
                                                   "ts" int8 DEFAULT 0,
                                                   "callback_param" VARCHAR,
                                                   "callback_url" VARCHAR,
                                                   "model" VARCHAR,
                                                   "union_site" VARCHAR,
                                                   "caid" VARCHAR,
                                                   "caid1" VARCHAR,
                                                   "caid2" VARCHAR,
                                                   "caid1_md5" VARCHAR,
                                                   "caid2_md5" VARCHAR,
                                                   "photo_id" VARCHAR,
                                                   "ac_creative" int4 DEFAULT 0,
                                                   "add_work_wechat_success" int4 DEFAULT 0,
                                                   "follow_wechat_account_success" int4 DEFAULT 0,
                                                   "corp_id"  VARCHAR,
                                                   "official_account_app_id"  VARCHAR,
                                                   "winfo_id" VARCHAR,
                                                   "created_at" TIMESTAMP (6) NOT NULL DEFAULT now(),
    "updated_at" TIMESTAMP (6) NOT NULL DEFAULT now(),
    "delete_status" int4 DEFAULT 0
    );

COMMENT ON COLUMN "advertising_trace"."agent_id" IS '客户标识';

COMMENT ON COLUMN "advertising_trace"."trace_type" IS '监测类型: 0=点击,1=视频播放,2=播放完,3=曝光';

COMMENT ON COLUMN "advertising_trace"."event_asset_id" IS '事件资产ID';

COMMENT ON COLUMN "advertising_trace"."platform_id" IS '平台类型';

COMMENT ON COLUMN "advertising_trace"."add_work_wechat_success" IS '是否成功添加企业微信，1-是；0否';

COMMENT ON COLUMN "advertising_trace"."follow_wechat_account_success" IS '是否成功关注公众号，1-是；0否';

COMMENT ON COLUMN "advertising_trace"."corp_id" IS '企业微信ID';

COMMENT ON COLUMN "advertising_trace"."official_account_app_id" IS '微信公众号 - appid';



COMMENT ON COLUMN "advertising_trace"."promotion_id" IS '巨量广告体验版中特有的宏参，代表巨量广告体验版的广告ID';


COMMENT ON COLUMN "advertising_trace"."promotion_name" IS '巨量广告体验版中的广告名称';


COMMENT ON COLUMN "advertising_trace"."project_name" IS '巨量广告体验版中特有的宏参，代表巨量广告体验版的项目名称';


COMMENT ON COLUMN "advertising_trace"."project_id" IS '巨量广告体验版中特有的宏参，代表巨量广告体验版的项目ID';

COMMENT ON COLUMN "advertising_trace"."account_id" IS '投放账户id';

COMMENT ON COLUMN "advertising_trace"."adgroup_id" IS '广告组id';

COMMENT ON COLUMN "advertising_trace"."pv_pid" IS '曝光id，对应page_view_info.pid';

COMMENT ON COLUMN "advertising_trace"."pv_uid" IS '曝光uid，来自表：page_view_info.uid';

COMMENT ON COLUMN "advertising_trace"."ext" IS '扩展信息';

COMMENT ON COLUMN "advertising_trace"."adgroup_name" IS '广告组名称';

COMMENT ON COLUMN "advertising_trace"."click_id" IS 'clickId，用于串联广告';

COMMENT ON COLUMN "advertising_trace"."ad_id" IS '广告计划ID';

COMMENT ON COLUMN "advertising_trace"."ad_name" IS '广告计划名称';

COMMENT ON COLUMN "advertising_trace"."creative_id" IS '广告创意ID';

COMMENT ON COLUMN "advertising_trace"."creative_name" IS '广告创意名称';

COMMENT ON COLUMN "advertising_trace"."c_type" IS '创意样式';

COMMENT ON COLUMN "advertising_trace"."advertiser_id" IS '广告主ID';

COMMENT ON COLUMN "advertising_trace"."c_site" IS '广告投放位置';

COMMENT ON COLUMN "advertising_trace"."convert_id" IS '转化Id';

COMMENT ON COLUMN "advertising_trace"."request_id" IS '请求下发的ID';


COMMENT ON COLUMN "advertising_trace"."mid1" IS '针对巨量广告体验版，图片素材宏参数（下发原始素材id）';


COMMENT ON COLUMN "advertising_trace"."mid2" IS '针对巨量广告体验版，标题素材宏参数（下发原始素材id）';


COMMENT ON COLUMN "advertising_trace"."mid3" IS '针对巨量广告体验版，视频素材宏参数（下发原始素材id）';


COMMENT ON COLUMN "advertising_trace"."mid4" IS '针对巨量广告体验版，搭配试玩素材宏参数（下发原始素材id）';


COMMENT ON COLUMN "advertising_trace"."mid5" IS '针对巨量广告体验版，落地页素材宏参数（下发原始素材id）';


COMMENT ON COLUMN "advertising_trace"."mid6" IS '针对巨量广告体验版，安卓下载详情页素材宏参数（下发原始素材id）';

COMMENT ON COLUMN "advertising_trace"."track_id" IS '请求下发的id&创意id的md5,16位';

COMMENT ON COLUMN "advertising_trace"."sl" IS '这次请求的语言,如 zh';

COMMENT ON COLUMN "advertising_trace"."imei" IS '安卓的设备 ID的md5 摘要，32位';

COMMENT ON COLUMN "advertising_trace"."imei2" IS '安卓系统，对15位数字的 IMEI （比如860576038225452）进行 MD5（备注：安卓广告唯一标示，imei双卡手机可能有两个，取默认的一个）';

COMMENT ON COLUMN "advertising_trace"."imei3" IS '安卓系统，Android下的IMEI，原文计算SHA1';

COMMENT ON COLUMN "advertising_trace"."imei4" IS 'IMEI进行 MD5，适配三方监测方案，imei2 imei4取一个即可，都会替换';

COMMENT ON COLUMN "advertising_trace"."idfa" IS 'IOS 6+的设备id字段，32位';

COMMENT ON COLUMN "advertising_trace"."idfa2" IS'iOS下的idfa计算MD5，规则为32位十六进制数字+4位连接符“-”的原文（比如：32ED3EE5-9968-4F25-A015-DE3CFF569568），再计算MD5，再转大写。';

COMMENT ON COLUMN "advertising_trace"."idfa3" IS 'iOS下的idfa计算SHA1，规则是原文带“-”，计算SHA1';

COMMENT ON COLUMN "advertising_trace"."idfa_md5" IS 'IOS 6+的设备id的md5摘要，32位';

COMMENT ON COLUMN "advertising_trace"."kenyid_caa" IS 'URL Encode后的JSON数组；其中kenyId为中广协ID（即CAID），kenyId_MD5为CAID原值MD5加密后的结果（32位小写）, version为信通院算法包版本号，支持两个版本同时下发（即最新版和上一版）';

COMMENT ON COLUMN "advertising_trace"."android_id" IS '安卓id原值的md5，32位';

COMMENT ON COLUMN "advertising_trace"."android_id2" IS '对ANDROIDID（举例:8f6581815307be28）进行MD5';

COMMENT ON COLUMN "advertising_trace"."android_id3" IS 'Android下的AndroidID，原文计算SHA1';

COMMENT ON COLUMN "advertising_trace"."oaid" IS 'Android Q及更高版本的设备号，32位';

COMMENT ON COLUMN "advertising_trace"."oaid_md5" IS 'Android Q及更高版本的设备号的md5摘要，32位';

COMMENT ON COLUMN "advertising_trace"."os" IS '操作系统平台';

COMMENT ON COLUMN "advertising_trace"."mac" IS '移动设备mac地址,转换成大写字母,去掉“:”，并且取md5摘要后的结果';

COMMENT ON COLUMN "advertising_trace"."mac1" IS '移动设备 mac 地址,转换成大写字母,并且取md5摘要后的结果，32位';

COMMENT ON COLUMN "advertising_trace"."mac2" IS '对 MAC 进行 MD5';

COMMENT ON COLUMN "advertising_trace"."mac3" IS '对 MAC 去除分隔符之后进行 MD5';

COMMENT ON COLUMN "advertising_trace"."ua" IS '用户代理(User Agent)，一个特殊字符串头，使得服务器能够识别客户使用的操作系统及版本、CPU类型、浏览器及版本、浏览器渲染引擎、浏览器语言、浏览器插件等。';

COMMENT ON COLUMN "advertising_trace"."geo" IS '位置信息，包含三部分:latitude（纬度），longitude（经度）以及precise（确切信息,精度）举例说明：十进制保留1位小数，西经南纬保留负数，用字母 x 分 割纬度与精度 (先纬后经，最后精度），如：35.7x122.4x100.0';

COMMENT ON COLUMN "advertising_trace"."ts" IS '客户端发生广告点击事件的时间，以毫秒为单位时间戳';

COMMENT ON COLUMN "advertising_trace"."callback_param" IS '客户端发生广告点击事件的时间，以毫秒为单位时间戳';

COMMENT ON COLUMN "advertising_trace"."callback_url" IS '直接把调用事件回传接口的url生成出来，广告主可以直接使用';

COMMENT ON COLUMN "advertising_trace"."model" IS '手机型号';

COMMENT ON COLUMN "advertising_trace"."union_site" IS '对外广告位编码';

COMMENT ON COLUMN "advertising_trace"."caid" IS ' 中国广告协会互联网广告标识，包含最新两个版本的CAID和版本号，url encode之后的json字符串(【CAID】和【CAID1、CAID2】的信息一致，使用一种即可；建议使用【CAID】，参数中包含多个信息，后续维护成本低）';

COMMENT ON COLUMN "advertising_trace"."photo_id" IS '素材ID';

COMMENT ON COLUMN "advertising_trace"."ac_creative" IS '是否为高级创意，1-是';

COMMENT ON COLUMN "advertising_trace"."winfo_id" IS '快手字段，仅支持搜索流量，winfoid可通过marketing api 中关键词接口获得，对应word_info_id，如果为非搜索流量或智能扩词流量，则winfoid不替换';


COMMENT ON COLUMN "advertising_trace"."created_at" IS '创建时间';


COMMENT ON COLUMN "advertising_trace"."created_at" IS '修改时间';


COMMENT ON COLUMN "advertising_trace"."delete_status" IS '逻辑删除标记 0-正常；1-删除';


CREATE INDEX if not exists  "created_at_updated_at_trace_index" ON "advertising_trace" USING btree (
    "created_at" "pg_catalog"."timestamp_ops" ASC NULLS LAST,
    "updated_at" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );

CREATE INDEX if not exists  "pid_trace_index" ON "advertising_trace" USING btree (
    "pv_pid" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);

CREATE INDEX if not exists  "clickId_trace_index" ON "advertising_trace" USING btree (
    "click_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);

CREATE INDEX if not exists  "uid_trace_index" ON "advertising_trace" USING btree (
    "pv_uid" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);

CREATE INDEX if not exists  "agent_id_trace_index" ON "advertising_trace" USING btree (
    "agent_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);

CREATE INDEX if not exists  "ip_index" ON "advertising_trace" USING btree (
    "ip" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);

CREATE INDEX if not exists  "link_id_trace_index" ON "advertising_trace" USING btree (
    "link_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
