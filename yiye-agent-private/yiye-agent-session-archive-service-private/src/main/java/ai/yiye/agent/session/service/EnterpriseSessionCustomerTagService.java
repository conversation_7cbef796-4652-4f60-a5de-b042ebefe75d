package ai.yiye.agent.session.service;

import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.enumerations.TagBuildType;
import ai.yiye.agent.domain.sessionarchive.EnterpriseSessionCustomerTag;
import ai.yiye.agent.domain.sessionarchive.EnterpriseSessionEmployeeCustomers;
import ai.yiye.agent.domain.sessionarchive.dto.EnterpriseSessionCustomerTagDto;
import ai.yiye.agent.session.mapper.EnterpriseSessionCustomerTagMapper;
import ai.yiye.agent.weixin.domain.xml.WorkWechatCustomerDetailsResponse;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 企业微信-会话存档-微信客户-客户标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-29
 */
@Slf4j
@Service
@DS(DbConstants.POSTGRESQL_DEFAULT)  //设置成默认数据库
public class EnterpriseSessionCustomerTagService extends ServiceImpl<EnterpriseSessionCustomerTagMapper, EnterpriseSessionCustomerTag> {
    @Autowired
    private EnterpriseSessionEmployeeCustomersService enterpriseSessionEmployeeCustomersService;

    /**
     * 查询客户标签
     *
     * @param enterpriseSessionCustomerTagDto
     * @return
     */
    public List<EnterpriseSessionCustomerTagDto> searchByUserAndEmployee(EnterpriseSessionCustomerTagDto enterpriseSessionCustomerTagDto) {
        List<EnterpriseSessionCustomerTagDto> list = getBaseMapper().searchByUserAndEmployee(enterpriseSessionCustomerTagDto);
        return list;
    }

    public void changeTags(WorkWechatCustomerDetailsResponse workWechatCustomerDetail){
        List<WorkWechatCustomerDetailsResponse.FollowUser> followedUsers = workWechatCustomerDetail.getFollowUser();
        String externalUserid = workWechatCustomerDetail.getExternalContact().getExternalUserid();
        String corpid = workWechatCustomerDetail.getCorpid();
        Long enterpriseWechatId = workWechatCustomerDetail.getEnterpriseWechatId();
        //查询所有的标签,
        //校验用户是否开通会话存档
        List<EnterpriseSessionEmployeeCustomers> list = enterpriseSessionEmployeeCustomersService.list(new LambdaQueryWrapper<EnterpriseSessionEmployeeCustomers>()
                .eq(EnterpriseSessionEmployeeCustomers::getOpenUserId, externalUserid)
                .eq(EnterpriseSessionEmployeeCustomers::getCorpid, workWechatCustomerDetail.getCorpid())
                .eq(EnterpriseSessionEmployeeCustomers::getEnterpriseWechatId, workWechatCustomerDetail.getEnterpriseWechatId()));
        List<String> userIds = list.stream().map(a -> a.getEmployeeId()).collect(Collectors.toList());
        Map<String, EnterpriseSessionEmployeeCustomers> enterpriseSessionEmployeeCustomerMap = list.stream().collect(Collectors.toMap(EnterpriseSessionEmployeeCustomers::getEmployeeId, Function.identity()));
        //查询用户信息
        //校验是否存在会话存档
        followedUsers.stream()
                .filter(followUser -> userIds.contains(followUser.getUserid()))
                .forEach(followUser -> {
                    //tags //排除个人标签，个人标签标签id为null
                    List<WorkWechatCustomerDetailsResponse.FollowUser.FollowUserTag> tags = followUser.getTags().stream()
                            .filter(Objects::nonNull)
                            .filter(followUserTag -> followUserTag.getType() == 1 && StringUtils.isNotBlank(followUserTag.getTagId()))
                            .collect(Collectors.toList());

                    List<EnterpriseSessionCustomerTag> userTgs = tags.stream().map(tag -> {
                        EnterpriseSessionCustomerTag enterpriseSessionCustomerTag = new EnterpriseSessionCustomerTag();
                        enterpriseSessionCustomerTag.setTagId(tag.getTagId())
                                .setTagName(tag.getTagName())
                                .setOpenUserId(externalUserid)
                                .setEmployeeId(followUser.getUserid())
                                .setBuildType(TagBuildType.DEFAULT)
                                .setCorpid(corpid)
                                .setEnterpriseWechatId(enterpriseWechatId);
                        return enterpriseSessionCustomerTag;
                    }).collect(Collectors.toList());
                    //tagIds
                    List<String> tagIds = userTgs.stream().map(a -> a.getTagId()).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(tagIds)){
                        //删除已经删除的标签
                        this.deleteByUserIdAndTagIds(corpid,followUser.getUserid(),externalUserid,tagIds);
                        //其它的批量插入或更新
                        this.batchSaveOrUpdate(userTgs);
                    }else {
                        //没有标签全部删除
                        this.deleteByUserIdAndTagIds(corpid,followUser.getUserid(),externalUserid,tagIds);
                    }
                });
    }

    /**
     * 删除当前用户对用的标签id
     * @param corpid
     * @param userid
     * @param externalUserid
     * @param tagIds
     */
    public void deleteByUserIdAndTagIds(String corpid,String userid,String externalUserid,List<String> tagIds){
        this.baseMapper.deleteByUserIdAndTagIds(corpid,userid,externalUserid,tagIds);
    }

    public void batchSaveOrUpdate(List<EnterpriseSessionCustomerTag> enterpriseSessionCustomerTags){
        this.baseMapper.batchSaveOrUpdate(enterpriseSessionCustomerTags);
    }

}
