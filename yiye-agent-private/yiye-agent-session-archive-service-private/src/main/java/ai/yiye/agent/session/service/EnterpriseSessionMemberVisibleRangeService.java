package ai.yiye.agent.session.service;

import ai.yiye.agent.domain.constants.DbConstants;
import ai.yiye.agent.domain.sessionarchive.EnterpriseSessionMemberVisibleRange;
import ai.yiye.agent.session.mapper.EnterpriseSessionMemberVisibleRangeMapper;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 企业微信-会话存档-成员可见范围
 */
@Slf4j
@Service
@DS(DbConstants.POSTGRESQL_DEFAULT)  //设置成默认数据库
public class EnterpriseSessionMemberVisibleRangeService extends ServiceImpl<EnterpriseSessionMemberVisibleRangeMapper, EnterpriseSessionMemberVisibleRange> {

}
