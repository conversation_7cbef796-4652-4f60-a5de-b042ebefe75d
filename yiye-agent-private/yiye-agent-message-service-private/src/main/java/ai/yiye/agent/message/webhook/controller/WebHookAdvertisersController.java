package ai.yiye.agent.message.webhook.controller;

import ai.yiye.agent.domain.message.AdvertiserAccountVo;
import ai.yiye.agent.message.webhook.service.WebHookAdvertiserAccountRelService;
import ai.yiye.agent.message.webhook.service.WebHookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @program: yiye-agent-backend
 * @description:
 * @author: x<PERSON><PERSON><PERSON>
 * @create: 2020-05-18 16:24
 **/
@RestController
@RequestMapping("/webhook-advertiser-accounts")
public class WebHookAdvertisersController {

    @Autowired
    private WebHookService webHookService;
    @Autowired
    private WebHookAdvertiserAccountRelService webHookAdvertiserAccountRelService;

    /**
     * 根据webhook地址id查询与投放账户相关关系
     *
     * @param webhoodId webhook地址id
     * @return
     */
    @GetMapping
    public List<AdvertiserAccountVo> getByWebHoodId(@RequestParam("webhookId") Long webhoodId) {
        return webHookAdvertiserAccountRelService.getByWebHoodId(webhoodId);
    }

    /**
     * 用用投放账户
     *
     * @param id                   webhook地址id
     * @param advertiserAccountVos 投放账户id集合
     */
    @PostMapping("/{id}/relate/advertiser-accout-relate")
    @ResponseStatus(HttpStatus.CREATED)
    public void actionBatch(@PathVariable("id") Long id,
                            @RequestBody List<AdvertiserAccountVo> advertiserAccountVos) {
       this.webHookService.actionBatch(id, advertiserAccountVos);
    }

}
