package ai.yiye.agent.message.sms.listener;


import ai.yiye.agent.autoconfigure.rabbitmq.config.Constants;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.common.util.Md5Util;
import ai.yiye.agent.domain.dto.ResultDto;
import ai.yiye.agent.domain.dto.SmsSendReq;
import ai.yiye.agent.domain.message.SmsSendLog;
import ai.yiye.agent.domain.message.SmsSignature;
import ai.yiye.agent.message.sms.remote.SmsRemoteService;
import ai.yiye.agent.message.sms.sender.dto.SendQueueDto;
import ai.yiye.agent.message.sms.service.SmsSendLogService;
import ai.yiye.agent.message.wechat.mapper.UserMapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @program: yiye-agent-backend
 * @description: 短信发送，调用公域发送接口
 * @author: xuhainan
 * @create: 2020-05-21 00:04
 **/
@Component
@Slf4j
public class SmsSendListener {

	@Value("${sms.request-url}")
	private String requestUrl;
	@Value("${sms.salt}")
	private String salt;

	@Autowired
	private UserMapper userMapper;
	@Autowired
	private SmsRemoteService smsRemoteService;
	@Autowired
	private SmsSendLogService smsSendLogService;

	@RabbitListener(bindings = {
			@QueueBinding(key = Constants.QUEUE_SMS_SEND, value = @Queue(value = Constants.QUEUE_SMS_SEND,
					durable = "true", autoDelete = "false", exclusive = "false"), exchange = @Exchange(name = Constants.QUEUE_SMS_SEND_EXCHANGE))
	})
	@RabbitHandler
	public void sendSms(String message) {
		SendQueueDto sendQueueDto = JSON.parseObject(message, SendQueueDto.class);
		this.sendSms(sendQueueDto);
		TenantContextHolder.clearContext();

	}


	/**
	 * 短信发送
	 */
	private void sendSms(SendQueueDto sendQueueDto) {
		final String md5 = Md5Util.getMd5((sendQueueDto.getFlagId() + salt).getBytes());
		SmsSignature smsSignature = sendQueueDto.getSmsSignature();
		SmsSendReq smsSendReq = new SmsSendReq()
				.setCode(sendQueueDto.getCode())
				.setContent(sendQueueDto.getMessage())
				.setSign(smsSignature.getName())
				.setMobile(sendQueueDto.getMobile())
				.setFlagId(sendQueueDto.getFlagId())
				.setAgentIdTableId(TenantContextHolder.get());
		//用http的方式,发送信息
		String description = null;
		ResultDto<SmsSendLog> resultDto = null;
		SmsSendLog smsSendLog = new SmsSendLog();
		try {
			//如果是sms，这里要
			resultDto = smsRemoteService.sendSms(smsSendReq, md5);
			//接口请求成功，未报异常
			if (resultDto.getCode() == 100) {
				smsSendLog = resultDto.getData();
				description = resultDto.getMessage();
			} else {
				description = "短信发送失败：" + resultDto.getMessage();
				//异常
				log.error("短信发送失败：{}", sendQueueDto);
			}
		} catch (Exception e) {
			description = "短信发送错误：" + e.getMessage();
			log.error(e.getMessage(), e);
		} finally {
			smsSendLog
					.setContent(null)
					.setSendData(StringUtils.isNotBlank(smsSendLog.getSendData()) ? smsSendLog.getSendData() : (Objects.isNull(smsSendReq) ? null : JSONObject.toJSONString(smsSendReq)))
					.setResultData(StringUtils.isNotBlank(smsSendLog.getResultData()) ? smsSendLog.getResultData() : (Objects.isNull(resultDto) ? null : JSONObject.toJSONString(resultDto)))
					.setDescription(description);
			//每次发送的时候都放到需要回执的消息中，然后那边response对消息进行获取回执
			smsSendLogService.updateById(smsSendLog.setId(sendQueueDto.getMessageSmsSendLogId()));
		}
	}

}
