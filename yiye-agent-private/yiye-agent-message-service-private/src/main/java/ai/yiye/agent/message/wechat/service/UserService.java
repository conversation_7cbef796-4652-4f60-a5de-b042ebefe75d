package ai.yiye.agent.message.wechat.service;


import ai.yiye.agent.autoconfigure.security.SecurityConstant;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.autoconfigure.security.UnAuthorizedException;
import ai.yiye.agent.autoconfigure.security.jwt.JwtTokenGenerator;
import ai.yiye.agent.autoconfigure.security.remote.UCenterRemote;
import ai.yiye.agent.autoconfigure.web.exception.Asserts;
import ai.yiye.agent.domain.Permission;
import ai.yiye.agent.domain.User;
import ai.yiye.agent.domain.dto.AdvertiserManagerDto;
import ai.yiye.agent.domain.enumerations.PermissionLevel;
import ai.yiye.agent.domain.enumerations.PermissionType;
import ai.yiye.agent.domain.enumerations.UserRole;
import ai.yiye.agent.domain.enumerations.UserTokenKeyType;
import ai.yiye.agent.message.wechat.mapper.PermissionMapper;
import ai.yiye.agent.message.wechat.mapper.UserMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.CaseFormat;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserService extends ServiceImpl<UserMapper, User> {

    @Resource
    private PermissionMapper permissionMapper;

    @Resource
    private JwtTokenGenerator jwtTokenGenerator;

//    @Value("yiye.agent.id")
//    private String agentId;

    @Resource
    private UCenterRemote uCenterRemote;

    private static final Long customer_level = 50000L;
    private static final Multimap<String, String> contrastMap = HashMultimap.create();

    static {
        //地址权限
        contrastMap.put("showAddress", "address");

        contrastMap.put("showAddress", "receive_province");

        contrastMap.put("showAddress", "select_province");

        contrastMap.put("showAddress", "receive_city");

        contrastMap.put("showAddress", "select_city");

        contrastMap.put("showAddress", "receive_area");

        contrastMap.put("showAddress", "select_area");

        contrastMap.put("showAddress", "areaSelect");
        //下单备注 memo
        contrastMap.put("receiveRemark", "memo");
        //规格1
        contrastMap.put("standard1", "content1");
        //规格2
        contrastMap.put("standard2", "content2");
        //规格3
        contrastMap.put("standard3", "content3");
    }

    public Set<Long> needNoticeUserIds(Long advertiserId) {
        Set<Long> userIds = this.baseMapper.needNoticeUserIds(advertiserId);
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptySet();
        }
        return userIds;
    }

    public User getUserWithPermissions(Long userId) {
        User user = getById(userId);
        Asserts.assertNotNull(user, UnAuthorizedException::new);
        if (user.getRole() == UserRole.ROLE_ADMIN) {
            user.setPermissions(permissionMapper.selectAll());
        } else if (user.getPermissionGroupId() != null) {
            user.setPermissions(permissionMapper.findByGroupId(user.getPermissionGroupId()));
        }
        return user;
    }

    /**
     * 获取当前用户没有的字段权限
     *
     * @param user 用户
     */
    public List<String> getMenuBarDataPermission(User user) {
        List<String> selectModules = this.getSelectMenuBarDataPermission(user);
        List<String> allModules = permissionMapper.selectList(new LambdaQueryWrapper<Permission>()
            .eq(Permission::getLevel, PermissionLevel.PERMISSION_LEVEL_MENU_BAR_DATA)
            .eq(Permission::getParentId, customer_level))
            .stream().map(Permission::getModule).collect(Collectors.toList());
        allModules.removeAll(selectModules);
        allModules = allModules.stream()
            .map(s -> CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, s))
            .collect(Collectors.toList());
        List<String> finalAllModules = allModules;
        contrastMap.keySet().forEach(contrast -> {
            if (!selectModules.contains(contrast)) {
                finalAllModules.addAll(contrastMap.get(contrast));
            }
        });
        return finalAllModules;
    }

    /**
     * 获取当前用户没有的字段权限
     *
     * @param user 用户
     */
    public List<String> getSelectMenuBarDataPermission(User user) {
        user = this.getUserWithPermissions(user.getId());
        List<Permission> permissions = user.getPermissions();
        return permissions.stream()
            .filter(permission -> PermissionLevel.PERMISSION_LEVEL_MENU_BAR_DATA.equals(permission.getLevel())
                && permission.getParentId().equals(customer_level))
            .map(Permission::getModule)
            .collect(Collectors.toList());
    }

    public Set<Permission> getDataPermission(User user) {
        user = this.getUserWithPermissions(user.getId());
        List<Permission> permissions = user.getPermissions();
        return permissions.stream()
            //过滤工作台的数据权限
            .filter(permission -> permission.getTType().getValue() > PermissionType.PERMISSION_OPERATION.getValue() && permission.getParentId().equals(Permission.WORKBENCH_ROOT_PERMISSION_ID))
            .collect(Collectors.toSet());
    }

    public List<Long> getSubUser(User user) {
        String token = jwtTokenGenerator.generateToken(TenantContextHolder.get(), user.getId(), user.getUsername(), UserTokenKeyType.COMMON_ROLE);
        Authentication authentication = new UsernamePasswordAuthenticationToken(null, token.substring(SecurityConstant.BEARER_PREFIX.length()), Collections.emptyList());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        AdvertiserManagerDto advertiserManagerDto = uCenterRemote.listByManager();
        SecurityContextHolder.clearContext();
        return advertiserManagerDto.getUserManager();
    }
}
