package ai.yiye.agent.statistics.controller;

import ai.yiye.agent.statistics.service.*;
import ai.yiye.agent.statistics.web.request.*;
import ai.yiye.agent.statistics.web.response.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/agent/report")
public class ReportController {


    @Autowired
    private DataAudienceService marketingDataAudienceService;

    @Autowired
    private DeliveryAnalyseService deliveryAnalyseService;

    @Autowired
    private CreativeAnalyseService creativeAnalyseService;

    @Autowired
    private LandingPageService landingPageService;


    @PostMapping("/countAdByStatus")
    public AjaxResult countAdByStatus(@RequestBody AdStatusQuery adStatusQuery){
        return AjaxResult.success(deliveryAnalyseService.adInfoBoard(adStatusQuery));
    }

    @GetMapping("/listAdIdByStatus")
    public AjaxResult listAdIdByStatus(AdStatusQuery adStatusQuery){
        return AjaxResult.success(null);
    }

    @PostMapping("/generalView")
    public AjaxResult generalView(@RequestBody GeneralViewQuery generalViewQuery){
        return AjaxResult.success(deliveryAnalyseService.dataOverview(generalViewQuery));
    }

    @PostMapping("/generalSingleView")
    public AjaxResult generalSingleView(@RequestBody GeneralViewQuery generalViewQuery){
        return AjaxResult.success(deliveryAnalyseService.dataOverview(generalViewQuery));
    }


    @PostMapping("/generalViewRollRate")
    public AjaxResult generalViewRollRate(@RequestBody GeneralViewQuery generalViewQuery){

        return AjaxResult.success(deliveryAnalyseService.generalViewRollRate(generalViewQuery));
    }

    @PostMapping("/countNewAds")
    public AjaxResult countNewAds(@RequestBody NewAdsQuery newAdsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","广告数");
        result.put("data",deliveryAnalyseService.newAdTrends(newAdsQuery));
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/doubleMetricData")
    public AjaxResult doubleMetricData(@RequestBody MetricsQuery metricsQuery){
        return AjaxResult.success(deliveryAnalyseService.indicatorsCompareAnalysis(metricsQuery));
    }

    @PostMapping("/singleMetricData")
    public AjaxResult doubleMetricData(@RequestBody SingleMetricsQuery singleMetricsQuery){
        return AjaxResult.success(deliveryAnalyseService.mutliIndicatorsCompareAnalysis(singleMetricsQuery));
    }

    @PostMapping("/upAndDownOnHour")
    public AjaxResult upAndDownOnHour(@RequestBody SingleMetricsQuery singleMetricsQuery){
        List data = deliveryAnalyseService.lastHourIncrease(singleMetricsQuery);
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","小时环比涨跌");
        result.put("data",data);
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/adNumOverflow")
    public AjaxResult adNumOverflow(@RequestBody BaseRequestParam requestParam){
        List data = Arrays.asList(deliveryAnalyseService.adNumRing(requestParam));
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","广告数溢出占比");
        result.put("data",data);
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/adRateOverflow")
    public AjaxResult adRateOverflow(@RequestBody BaseRequestParam requestParam){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","广告数溢出率占比");
        result.put("data",Arrays.asList(deliveryAnalyseService.adOverflowRing(requestParam)));
        results.add(result);
        return AjaxResult.success(results);
    }


    @PostMapping("/adBiddingAndCostTrends")
    public AjaxResult adBiddingAndCostTrends(@RequestBody BaseRequestParam BaseRequestParam){
        List data = deliveryAnalyseService.adBidTrendAnalyse(BaseRequestParam);
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","出价与成本趋势");
        result.put("data",data);
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/adBurnout")
    public AjaxResult adBurnout(@RequestBody SingleMetricsQuery singleMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","广告燃尽分析");
        result.put("data", deliveryAnalyseService.adBurnoutAnalysis(singleMetricsQuery));
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/adEffect")
    public AjaxResult adEffect(@RequestBody BaseRequestParam requestParam){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","广告效果分析");
        result.put("data", deliveryAnalyseService.adEffectAnalysis(requestParam));
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/distributionOfLaunchPeriod")
    public AjaxResult distributionOfLaunchPeriod(@RequestBody SingleMetricsQuery singleMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","广告投放时段分布");
        result.put("data",deliveryAnalyseService.deliveryTimeDistribution(singleMetricsQuery));
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/creativeType")
    public AjaxResult creativeType(@RequestBody BaseRequestParam BaseRequestParam){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","创意类型占比");
        result.put("data",creativeAnalyseService.creativeTypeProportion(BaseRequestParam));
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/creativeImgBeautyScore")
    public AjaxResult creativeImgBeautyScore(@RequestBody BaseRequestParam BaseRequestParam){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","创意图片评价-美观度");
        result.put("data",creativeAnalyseService.imageAestheticsAnalysis(BaseRequestParam));
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/creativeImgHDScore")
    public AjaxResult creativeImgHDScore(@RequestBody BaseRequestParam BaseRequestParam){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","创意图片评价-清晰度");
        result.put("data",creativeAnalyseService.imageClarityAnalysis(BaseRequestParam));
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/creativeMetric")//视频播放数
    public AjaxResult creativeMetric(@RequestBody CreativeMetricQuery creativeMetricQuery){
        Map result = creativeAnalyseService.creativeMetric(creativeMetricQuery);
        return AjaxResult.success(result==null?0:result.get("data"));
    }

    @PostMapping("/creativeVideoPercent")
    public AjaxResult creativeVideoPercent(@RequestBody BaseRequestParam BaseRequestParam){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","创意分析-视频播放进度分析");
        result.put("data",creativeAnalyseService.videoProcessAnalysis(BaseRequestParam));
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/creativeVideoNetwork")
    public AjaxResult creativeVideoNetwork(@RequestBody BaseRequestParam BaseRequestParam){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","创意分析-视频播放网络");
        result.put("data",creativeAnalyseService.videoNetEnvironmentAnalysis(BaseRequestParam));
        results.add(result);
        return AjaxResult.success(results);
    }


    @PostMapping("/creativeOcrTags")
    public AjaxResult creativeOcrTags(@RequestBody SingleMetricsQuery singleMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","创意分析-图片OCR标签");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/creativeImgMultiTags")
    public AjaxResult creativeImgMultiTags(@RequestBody SingleMetricsQuery singleMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","创意分析-图片多标签");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/creativeCopyTags")
    public AjaxResult creativeCopyTags(@RequestBody SingleMetricsQuery singleMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","创意分析-文案关键词");
        result.put("data",null);
//        result.put("data",creativeAnalyseService.getCreativeCopyWritings(singleMetricsQuery.setParticipleType(ParticipleType.COPYWRITER.getValue())));
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/creativeInterestTags")
    public AjaxResult creativeInterestTags(@RequestBody  SingleMetricsQuery singleMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","创意分析-创意兴趣标签");
//        result.put("data",creativeAnalyseService.getCreativeCopyWritings(singleMetricsQuery.setParticipleType(ParticipleType.LABEL.getValue())));
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/adConsume")
    public AjaxResult adConsume(@RequestBody SingleMetricsQuery singleMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","广告位消耗占比");
        result.put("data",deliveryAnalyseService.creativeFilter(singleMetricsQuery));
        results.add(result);
        return AjaxResult.success(results);
    }





    /**
     *@Description: 人群画像-地域分布
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:30 下午
     */
    @PostMapping("/adPersonasArea")
    public AjaxResult adPersonasArea(@RequestBody AudienceMetricsQuery audienceMetricsQuery) {
        return AjaxResult.buildSuccess("人群画像-地域分布", marketingDataAudienceService.adPersonasArea(audienceMetricsQuery));
    }
    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:41 下午
     */
    @PostMapping("/adPersonasTag")
    public AjaxResult adPersonasTag(@RequestBody AudienceMetricsQuery audienceMetricsQuery) {
        audienceMetricsQuery.setType("tag");
        return AjaxResult.buildSuccess("人群画像-兴趣分布",marketingDataAudienceService.adPersonasArea(audienceMetricsQuery));
    }
    /**
     *@Description: 人群画像-地域分布
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:30 下午
     */
    @PostMapping("/adPersonasAge")
    public AjaxResult adPersonasAge(@RequestBody AudienceMetricsQuery audienceMetricsQuery) {
        audienceMetricsQuery.setType("age");
        return AjaxResult.buildSuccess("人群画像-年龄分布", marketingDataAudienceService.adPersonasArea(audienceMetricsQuery));
    }
    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:40 下午
     */
    @PostMapping("/adPersonasGender")
    public AjaxResult adPersonasGender(@RequestBody AudienceMetricsQuery audienceMetricsQuery) {
        audienceMetricsQuery.setType("gender");
        return AjaxResult.buildSuccess("人群画像-性别分布", marketingDataAudienceService.adPersonasArea(audienceMetricsQuery));
    }
    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:40 下午
     */
    @PostMapping("/adPersonasPlatform")
    public AjaxResult adPersonasPlatform(@RequestBody AudienceMetricsQuery audienceMetricsQuery) {
        audienceMetricsQuery.setType("platform");
        return AjaxResult.buildSuccess("人群画像-平台分布",marketingDataAudienceService.adPersonasArea(audienceMetricsQuery));
    }
    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:41 下午
     */
    @PostMapping("/adPersonasAc")
    public AjaxResult adPersonasAc(@RequestBody AudienceMetricsQuery audienceMetricsQuery) {
        audienceMetricsQuery.setType("ac");
        return AjaxResult.buildSuccess("人群画像-网络分布",marketingDataAudienceService.adPersonasArea(audienceMetricsQuery));
    }
    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:41 下午
     */
    @PostMapping("/adPersonasAreaYiye")
    public AjaxResult adPersonasAreaYiye(@RequestBody PageViewMetricsQuery pageViewMetricsQuery) {
        return AjaxResult.buildSuccess("人群画像-地域分布一叶",marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery));
    }
    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:41 下午
     */
    @PostMapping("/adPersonasPlatformYiye")
    public AjaxResult adPersonasPlatformYiye(@RequestBody PageViewMetricsQuery pageViewMetricsQuery) {
        pageViewMetricsQuery.setType("os");
        return AjaxResult.buildSuccess("人群画像-平台分布一叶",marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery));
    }

    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:41 下午
     */
    @PostMapping("/adPersonasAcYiye")
    public AjaxResult adPersonasAcYiye(@RequestBody PageViewMetricsQuery pageViewMetricsQuery) {
        pageViewMetricsQuery.setType("network_type");
        return AjaxResult.buildSuccess("人群画像-网络分布一叶",marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery));
    }
    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:41 下午
     */
    @PostMapping("/adPersonasScreenSizeYiye")
    public AjaxResult adPersonasScreenSizeYiye(@RequestBody PageViewMetricsQuery pageViewMetricsQuery) {
        pageViewMetricsQuery.setType("screen_size");
        List aa=marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery);
        return AjaxResult.buildSuccess("人群画像-屏幕尺寸一叶",marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery));
    }
    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:41 下午
     */
    @PostMapping("/adPersonasScreenDpiYiye")
    public AjaxResult adPersonasScreenDpiYiye(@RequestBody PageViewMetricsQuery pageViewMetricsQuery) {
        pageViewMetricsQuery.setType("screen_dpi");
        return AjaxResult.buildSuccess("人群画像-屏幕分辨率一叶",marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery));
    }

    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:41 下午
     */
    @PostMapping("/adPersonasDistributionOfOperators")
    public AjaxResult adPersonasDistributionOfOperators(@RequestBody PageViewMetricsQuery pageViewMetricsQuery) {
        pageViewMetricsQuery.setType("operator");
        return AjaxResult.buildSuccess("人群画像-运营商分布",marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery));
    }
     /**
      *@Description:
      *@Param:
      *@return:
      *@Author:王建魁
      *@Date: 2020/9/17-6:41 下午
      */
    @PostMapping("/adPersonasDistributionOfBrowser")
    public AjaxResult adPersonasDistributionOfBrowser(@RequestBody PageViewMetricsQuery pageViewMetricsQuery) {
        pageViewMetricsQuery.setType("browser");
        return AjaxResult.buildSuccess("人群画像-浏览器分布",marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery));
    }
    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:41 下午
     */
    @PostMapping("/adPersonasDistributionOfDevicePrice")
    public AjaxResult adPersonasDistributionOfDevicePrice(@RequestBody PageViewMetricsQuery pageViewMetricsQuery) {
        pageViewMetricsQuery.setType("price_range");
        List ls =marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery);
        return AjaxResult.buildSuccess("人群画像-设备价格分布",marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery));
    }
     /**
      *@Description:
      *@Param:
      *@return:
      *@Author:王建魁
      *@Date: 2020/9/17-6:41 下午
      */
    @PostMapping("/adPersonasStayProportion")
    public AjaxResult adPersonasStayProportion(@RequestBody PageViewMetricsQuery pageViewMetricsQuery) {
        pageViewMetricsQuery.setType("length_of_stay");
        List aa = marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery);
        return AjaxResult.buildSuccess("人群画像-停留时长占比",marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery));
    }
    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:41 下午
     */
    @PostMapping("/adPersonasSourceProportion")
    public AjaxResult adPersonasSourceProportion(@RequestBody PageViewMetricsQuery pageViewMetricsQuery) {
        pageViewMetricsQuery.setType("referrer");
        return AjaxResult.buildSuccess("人群画像-来源分布",marketingDataAudienceService.adPersonasAreaYiye(pageViewMetricsQuery));
    }





    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:41 下午
     */
    @PostMapping("/adPersonasRepeatProportion")
    public AjaxResult adPersonasRepeatProportion(@RequestBody PageViewMetricsQuery pageViewMetricsQuery) {
        List bb = marketingDataAudienceService.adPersonasPageRepeatProportion(pageViewMetricsQuery);
        return AjaxResult.buildSuccess("人群画像-重复访客分布",marketingDataAudienceService.adPersonasPageRepeatProportion(pageViewMetricsQuery));
    }
    /**
     *@Description:
     *@Param:
     *@return:
     *@Author:王建魁
     *@Date: 2020/9/17-6:41 下午
     */
    @PostMapping("/adPersonasAvgStayProportion")
    public AjaxResult adPersonasAvgStayProportion(@RequestBody PageViewMetricsQuery pageViewMetricsQuery) {
        return AjaxResult.buildSuccess("人群画像-平均停留时长趋势",marketingDataAudienceService.adPersonasAvgStayProportion(pageViewMetricsQuery));
    }

    @GetMapping("/adPageViewAdGroup")
    public AjaxResult adPageViewAdGroup(PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-广告筛选");
        result.put("data",landingPageService.getLandingPageAdgroups(pageViewMetricsQuery));
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @GetMapping("/getLandingPageIdByAdIds")
    public AjaxResult geLandingPageIdByAdIds(PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","根据广告id获取落地页id");
        result.put("data",landingPageService.getLandingPageIdByAdIds(pageViewMetricsQuery));
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @GetMapping("/adPageViewAdGroupByLandPageId")
    public AjaxResult adPageViewAdGroupByLandPageId(PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-落地页ID筛选广告");
        result.put("data",landingPageService.adPageViewAdGroupByLandPageId(pageViewMetricsQuery));
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @GetMapping("/adPageViewGroupList")
    public AjaxResult adPageViewGroupList(PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-分组列表");
        result.put("data",landingPageService.getPageViewGroups(pageViewMetricsQuery));
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @GetMapping("/adPageViewLandingPage")
    public AjaxResult adPageViewLandingPage(PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-分组列表");
        result.put("data", landingPageService.getPageViewLandingPages(pageViewMetricsQuery));
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewTotalView")
    public AjaxResult adPageViewTotalView(@RequestBody PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-浏览数");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewFill")
    public AjaxResult adPageViewFill(@RequestBody PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-填单数");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewUnFill")
    public AjaxResult adPageViewUnFill(@RequestBody PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-未填单数");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewTotalUid")
    public AjaxResult adPageViewTotalUid(@RequestBody PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-访客数");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewRepeatUid")
    public AjaxResult adPageViewRepeatUid(@RequestBody PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-重复访客数");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewAvgStay")
    public AjaxResult adPageViewAvgStay(@RequestBody PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-平均停留时长");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewAvgLength")
    public AjaxResult adPageViewAvgLength(@RequestBody  PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-平均访问深度");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewTotalViewRollRate")
    public AjaxResult adPageViewTotalViewRollRate(@RequestBody PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-浏览数环比");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewFillRollRate")
    public AjaxResult adPageViewFillRollRate(@RequestBody PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-填单数环比");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewUnFillRollRate")
    public AjaxResult adPageViewUnFillRollRate(@RequestBody  PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-未填单数环比");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewTotalUidRollRate")
    public AjaxResult adPageViewTotalUidRollRate(@RequestBody  PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-访客数环比");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewRepeatUidRollRate")
    public AjaxResult adPageViewRepeatUidRollRate(@RequestBody  PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-重复访客数环比");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewAvgStayRollRate")
    public AjaxResult adPageViewAvgStayRollRate(@RequestBody  PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-平均停留时长环比");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewAvgLengthRollRate")
    public AjaxResult adPageViewAvgLengthRollRate(@RequestBody  PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-平均访问深度环比");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(result.get("data"));
    }

    @PostMapping("/adPageViewAccessDepthRollRate")
    public AjaxResult adPageViewAccessDepthRollRate(@RequestBody PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-访问深度环比");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/adPageViewStayLengthRollRate")
    public AjaxResult adPageViewStayLengthRollRate(@RequestBody  PageViewMetricsQuery pageViewMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","落地页-停留时长环比");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(results);
    }

    @GetMapping("/adDownIds")
    public AjaxResult adDownIds(BaseRequestParam BaseRequestParam){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","获取未溢出数下钻adgroup_id");
        result.put("data",deliveryAnalyseService.getAdDownIds(BaseRequestParam));
        results.add(result);
        return AjaxResult.success(results);
    }

    @GetMapping("/adOverIds")
    public AjaxResult adOverIds(BaseRequestParam BaseRequestParam){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","获取已溢出数下钻adgroup_id");
        result.put("data",deliveryAnalyseService.getAdOverIds(BaseRequestParam));
        results.add(result);
        return AjaxResult.success(results);
    }

    @GetMapping("/adPercentIds")
    public AjaxResult adPercentIds(PercentOverQuery percentOverQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","获取已溢出率下钻adgroup_id");
        result.put("data",deliveryAnalyseService.getAdPercentIds(percentOverQuery));
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/adColorBubble")
    public AjaxResult adColorBubble(@RequestBody  SingleMetricsQuery singleMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","创意图片色彩分析");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/adOperatorReport")
    public AjaxResult adOperatorReport(@RequestBody SingleMetricsQuery singleMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","操作对数据的影响");
        result.put("data",null);
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/adTargetPacketPie")
    public AjaxResult adTargetPacketPie(@RequestBody  SingleMetricsQuery singleMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","定向包数据分析饼图");
        result.put("data",deliveryAnalyseService.targetingDataAnalyse(singleMetricsQuery));
        results.add(result);
        return AjaxResult.success(results);
    }

    @PostMapping("/adTargetPacketBar")
    public AjaxResult adTargetPacketBar(@RequestBody SingleMetricsQuery singleMetricsQuery){
        List results = new ArrayList();
        Map result = new HashMap();
        result.put("name","定向包数据分析柱状图");
        result.put("data",deliveryAnalyseService.targetingDataAnalyse(singleMetricsQuery));
        results.add(result);
        return AjaxResult.success(results);
    }
}
