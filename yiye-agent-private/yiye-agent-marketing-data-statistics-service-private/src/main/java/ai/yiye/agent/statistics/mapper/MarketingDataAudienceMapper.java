package ai.yiye.agent.statistics.mapper;


import ai.yiye.agent.statistics.web.request.AudienceMetricsQuery;
import ai.yiye.agent.statistics.web.request.PageViewMetricsQuery;
import ai.yiye.agent.statistics.web.response.GeneralViewVo;

import java.util.List;

/**
 * 受众数据Mapper接口
 *
 * <AUTHOR>
 * @date 2020-06-25
 */
public interface MarketingDataAudienceMapper
{
    List<GeneralViewVo> adPersonasArea(AudienceMetricsQuery audienceMetricsQuery);
    List adPersonasPageRepeatProportion(PageViewMetricsQuery pageViewMetricsQuery);
    List<GeneralViewVo> adPersonasAreaYiye(PageViewMetricsQuery pageViewMetricsQuery);
    List<GeneralViewVo> adPersonasAvgStayProportion(PageViewMetricsQuery pageViewMetricsQuery);
    List<Long> queryLandingPageIds(PageViewMetricsQuery pageViewMetricsQuery);
}
