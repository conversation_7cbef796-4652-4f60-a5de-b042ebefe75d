package ai.yiye.agent.statistics.utils;


import java.util.List;
import java.util.Optional;

public class BaseQuery {
//    @ApiModelProperty("开始时间 yyyy-MM-dd HH:mm:ss")
    private String startTime;
//    @ApiModelProperty("结束时间 yyyy-MM-dd HH:mm:ss")
    private String endTime;
//    @ApiModelProperty("广告组ID")
    private List<Long> subAdTypeId;
//    @ApiModelProperty("创意ID")
    private List<String> creativeId;
//    @ApiModelProperty("计费方式")
    private List<String> calType;
//    @ApiModelProperty("小于多少花费")
    private Double lessThanCost;
//    @ApiModelProperty("大于多少花费")
    private Double greaterThanCost;
//    @ApiModelProperty("小于多少转化量")
    private Integer lessThanConvertNum;
//    @ApiModelProperty("大于多少转化量")
    private Integer greaterThanConvertNum;
//    @ApiModelProperty("小于多少转化成本")
    private Double lessThanConvertCost;
//    @ApiModelProperty("大于多少转化成本")
    private Double greaterThanConvertCost;
//    @ApiModelProperty("小于多少展示数")
    private Integer lessThanShowNum;
//    @ApiModelProperty("大于多少展示数")
    private Integer greaterThanShowNum;
//    @ApiModelProperty("分时 or 分日")
    private String calDateType;
//    @ApiModelProperty("广告主ID")
    private Long accountId;

//    @ApiModelProperty("平台ID")
    private Integer platformId;

//    @ApiModelProperty("落地页ID")
    private Integer landingPageId;

//    @ApiModelProperty("转化目标")
    private List<String> optimizationGoal;
    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public List<Long> getSubAdTypeId() {
        return subAdTypeId;
    }

    public void setSubAdTypeId(List<Long> subAdTypeId) {
        this.subAdTypeId = subAdTypeId;
    }

    public List<String> getCreativeId() {
        return creativeId;
    }

    public void setCreativeId(List<String> creativeId) {
        this.creativeId = creativeId;
    }

    public List<String> getCalType() {
        return calType;
    }

    public void setCalType(List<String> calType) {
        this.calType = calType;
    }

    public Double getLessThanCost() {
        return Optional.ofNullable(lessThanCost).map(cost -> cost * 100).orElse(null);
    }

    public void setLessThanCost(Double lessThanCost) {
        this.lessThanCost = lessThanCost;
    }

    public Double getGreaterThanCost() {
        return Optional.ofNullable(greaterThanCost).map(cost -> cost * 100).orElse(null);
    }

    public void setGreaterThanCost(Double greaterThanCost) {
        this.greaterThanCost = greaterThanCost;
    }

    public Integer getLessThanConvertNum() {
        return lessThanConvertNum;
    }

    public void setLessThanConvertNum(Integer lessThanConvertNum) {
        this.lessThanConvertNum = lessThanConvertNum;
    }

    public Integer getGreaterThanConvertNum() {
        return greaterThanConvertNum;
    }

    public void setGreaterThanConvertNum(Integer greaterThanConvertNum) {
        this.greaterThanConvertNum = greaterThanConvertNum;
    }

    public Double getLessThanConvertCost() {
        return Optional.ofNullable(lessThanConvertCost).map(cost -> cost * 100).orElse(null);
    }

    public void setLessThanConvertCost(Double lessThanConvertCost) {
        this.lessThanConvertCost = lessThanConvertCost;
    }

    public Double getGreaterThanConvertCost() {
        return Optional.ofNullable(greaterThanConvertCost).map(cost -> cost * 100).orElse(null);
    }

    public void setGreaterThanConvertCost(Double greaterThanConvertCost) {
        this.greaterThanConvertCost = greaterThanConvertCost;
    }

    public Integer getLessThanShowNum() {
        return lessThanShowNum;
    }

    public void setLessThanShowNum(Integer lessThanShowNum) {
        this.lessThanShowNum = lessThanShowNum;
    }

    public Integer getGreaterThanShowNum() {
        return greaterThanShowNum;
    }

    public void setGreaterThanShowNum(Integer greaterThanShowNum) {
        this.greaterThanShowNum = greaterThanShowNum;
    }

    public String getCalDateType() {
        return calDateType;
    }

    public void setCalDateType(String calDateType) {
        this.calDateType = calDateType;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Integer getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Integer platformId) {
        this.platformId = platformId;
    }

    public Integer getLandingPageId() {
        return landingPageId;
    }

    public void setLandingPageId(Integer landingPageId) {
        this.landingPageId = landingPageId;
    }

    public List<String> getOptimizationGoal() {
        return optimizationGoal;
    }

    public void setOptimizationGoal(List<String> optimizationGoal) {
        this.optimizationGoal = optimizationGoal;
    }
}
