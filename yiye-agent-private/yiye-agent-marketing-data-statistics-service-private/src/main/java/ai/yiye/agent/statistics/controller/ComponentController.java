package ai.yiye.agent.statistics.controller;

import ai.yiye.agent.domain.User;
import ai.yiye.agent.statistics.domain.ReportComponent;
import ai.yiye.agent.statistics.service.ReportComponentService;
import ai.yiye.agent.statistics.web.response.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/agent/component")
public class ComponentController {

    @Autowired
    private ReportComponentService reportComponentService;

    @GetMapping("/listByType")
    public AjaxResult listByType(ReportComponent query, @AuthenticationPrincipal User user) {
        query.setUserId(user.getId());
        return AjaxResult.success(reportComponentService.selectReportComponentList(query));
    }

    @PostMapping
    public AjaxResult addOrUpdate(@RequestBody ReportComponent reportComponent, @AuthenticationPrincipal User user){
        reportComponent.setUserId(user.getId());
        return AjaxResult.success(reportComponentService.insertOrUpdateReportComponent(reportComponent));
    }

    @GetMapping("/resetDefault")
    public AjaxResult resetDefault(ReportComponent reportComponent, @AuthenticationPrincipal User user){
        reportComponent.setUserId(user.getId());
        return AjaxResult.success(reportComponentService.resetDefault(reportComponent));
    }

}
