package ai.yiye.agent.statistics.service;

import ai.yiye.agent.domain.marketing.data.AdvertiserGroup;
import ai.yiye.agent.statistics.domain.MarketingDataAdgroup;
import ai.yiye.agent.statistics.mapper.AdvertiserGroupMapper;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AdvertiserGroupService extends ServiceImpl<AdvertiserGroupMapper, AdvertiserGroup> {

    @DS("postgresql")
    public List<MarketingDataAdgroup> selectMarketingDataAdgroupList(MarketingDataAdgroup marketingDataAdgroup) {
        Optional.ofNullable(marketingDataAdgroup.getCampaignIds()).ifPresent(campaignIds -> {
            marketingDataAdgroup.setSubAdTypeId(
                Arrays.asList(campaignIds.split(","))
                      .stream().map(Long::valueOf).collect(Collectors.toList())
            );
        });
        return baseMapper.selectMarketingDataAdgroupList(marketingDataAdgroup);
    }
}
