package ai.yiye.agent.statistics.mapper;


import ai.yiye.agent.statistics.domain.ReportComponent;

import java.util.List;

/**
 * 自定义组件Mapper接口
 *
 */
public interface ReportComponentMapper
{
    /**
     * 查询自定义组件
     *
     * @param id 自定义组件ID
     * @return 自定义组件
     */
    ReportComponent selectReportComponentById(Long id);

    /**
     * 查询自定义组件列表
     *
     * @param reportComponent 自定义组件
     * @return 自定义组件集合
     */
    List<ReportComponent> selectReportComponentList(ReportComponent reportComponent);

    /**
     * 新增自定义组件
     *
     * @param reportComponent 自定义组件
     * @return 结果
     */
    int insertReportComponent(ReportComponent reportComponent);

    /**
     * 修改自定义组件
     *
     * @param reportComponent 自定义组件
     * @return 结果
     */
    int updateReportComponent(ReportComponent reportComponent);

    /**
     * 删除自定义组件
     *
     * @param id 自定义组件ID
     * @return 结果
     */
    int deleteReportComponentById(Long id);

    /**
     * 批量删除自定义组件
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteReportComponentByIds(String[] ids);

    /**
     * 重置布局
     * @param reportComponent
     * @return
     */
    int resetDefault(ReportComponent reportComponent);
}
