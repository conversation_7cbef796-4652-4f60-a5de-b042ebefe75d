package ai.yiye.agent.statistics.domain;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 广告组信息对象 marketing_data_adgroup
 *
 * <AUTHOR>
 */
@Data
public class MarketingDataAdgroup extends BaseEntity {
    /** ID */
    private Long id;

    /** 广告组ID */
    private Long adgroupId;

    /** 投放账号ID */
    private String accountId;

    /** 平台ID */
    private Long platformId;

    /** null */
    private Long campaignId;

    /** 广告组名称 */
    private String name;

    /** 出价 */
    private Long bid;

    /** 出价方式 */
    private String billingEvent;

    private String biddingEvent;

    private String optimizationGoal;

    /** 广告状态 */
    private String adStatus;

    /** 学习期 */
    private String learningStatus;

    /** 定向包id */
    private Long targetingId;

    /** 定向内容-没有使用定向包的广告使用（与targeting_id 二选一使用） */
    private String targeting;

    /** 投放时间 */
    private String timeSeries;

    /** 投放开始时间 */
    private Date beginAt;

    /** 投放结束时间 */
    private Date endAt;

    /** 创建时间 */
    private Date createdAt;

    /** 更新时间 */
    private Date updatedAt;

    /** null */
    private String status;

    /** null */
    private Integer isDeleted;

    /** null */
    private String ext;

    private String campaignIds;

    private List<Long> subAdTypeId;

}
