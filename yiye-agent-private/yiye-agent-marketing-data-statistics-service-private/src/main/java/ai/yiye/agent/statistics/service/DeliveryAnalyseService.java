package ai.yiye.agent.statistics.service;

import ai.yiye.agent.statistics.configuration.SelfRefBean;
import ai.yiye.agent.statistics.mapper.DeliveryAnalyseMapper;
import ai.yiye.agent.statistics.utils.FieldMappers;
import ai.yiye.agent.statistics.web.request.*;
import ai.yiye.agent.statistics.web.response.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeliveryAnalyseService implements SelfRefBean<DeliveryAnalyseService> {

    @Autowired
    private DeliveryAnalyseMapper deliveryAnalyseMapper;

    /**
     * 投放分析 - 数据概览
     * @return
     */
    @DS("postgresql")
    public GeneralViewVo dataOverview(GeneralViewQuery query) {
        GeneralViewVo viewVo = deliveryAnalyseMapper.dataOverview(query);
        if(null == viewVo) {
            viewVo = new GeneralViewVo();
        }
        // 加载落地页相关数据
        List<Long> landingPageIds = deliveryAnalyseMapper.getLandingPageIds(query);
        // 去clickhouse进行查询相关统计数据
        if(!CollectionUtils.isEmpty(landingPageIds)) {
            query.setLandingPageIds(landingPageIds);
            GeneralViewVo pageVo = self().landingDataOverview(query);
            // 数据合并处理
            if(null != pageVo) {
                viewVo.setLandingViews(pageVo.getLandingViews())
                      .setPageViewNums(pageVo.getLandingViews())
                      .setLandingForms(pageVo.getLandingForms())
                      .setLandingVisitors(pageVo.getLandingVisitors())
                      .setLandingUnForms(pageVo.getLandingUnForms())
                      .setLandingRepeatVisitors(pageVo.getLandingRepeatVisitors())
                      .setLandingRepeatRate(pageVo.getLandingRepeatRate())
                      .setLandingAvgStay(pageVo.getLandingAvgStay())
                      .setLandingAvgLength(pageVo.getLandingAvgLength());
            }
        }
        FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, viewVo, query.getType(), "data");

        return viewVo;
    }

    /**
     * 生成昨天与今天的数据环比
     * @param query
     * @return
     */
    public Object generalViewRollRate(GeneralViewQuery query) {
        // 设置前置时间
        String nowStartTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(0, 0, 0)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String nowEndTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 59, 59)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        String preStartTime = LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.of(0, 0, 0)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String preEndTime = LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.of(23, 59, 59)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        BigDecimal curData = new BigDecimal(0);
        BigDecimal preData = new BigDecimal(0);

        if(FieldMappers.inLanding(query.getType())) { // 包含落地页相关属性
            List<Long> landingPageIds = deliveryAnalyseMapper.getLandingPageIds(query);
            if(!CollectionUtils.isEmpty(landingPageIds)) {
                query.setLandingPageIds(landingPageIds).setStartTime(nowStartTime).setEndTime(nowEndTime);
                GeneralViewVo curPageVo = self().landingDataOverview(query);
                FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, curPageVo, query.getType(), "data");
                if(null != curPageVo && StringUtils.isNotEmpty(curPageVo.getData())) {
                    curData = new BigDecimal(curPageVo.getData());
                }

                query.setStartTime(preStartTime).setEndTime(preEndTime);
                GeneralViewVo prePageVo = self().landingDataOverview(query);
                FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, prePageVo, query.getType(), "data");
                if(null != prePageVo && StringUtils.isNotEmpty(prePageVo.getData())) {
                    preData = new BigDecimal(prePageVo.getData());
                }

            }
        } else {
            query.setStartTime(nowStartTime).setEndTime(nowEndTime);
            GeneralViewVo todayVo = deliveryAnalyseMapper.dataOverview(query);
            FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, todayVo, query.getType(), "data");
            if(null != todayVo && StringUtils.isNotEmpty(todayVo.getData())) {
                curData = new BigDecimal(todayVo.getData());
            }
            query.setStartTime(preStartTime).setEndTime(preEndTime);
            GeneralViewVo predayVo = deliveryAnalyseMapper.dataOverview(query);
            FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, predayVo, query.getType(), "data");
            if(null != predayVo && StringUtils.isNotEmpty(predayVo.getData())) {
                preData = new BigDecimal(predayVo.getData());
            }
        }

        if(preData.setScale(4).equals(BigDecimal.ZERO.setScale(4))) {
            return "-";
        } else {
            return curData.subtract(preData)
                          .divide(preData,4, RoundingMode.HALF_UP)
                          .multiply(new BigDecimal(100))
                          .setScale(2, BigDecimal.ROUND_HALF_UP) + "%";
        }
    }

    /**
     * 投放分析 - 广告信息看板
     * @param query
     * @return
     */
    public Integer adInfoBoard(AdStatusQuery query) {
        return deliveryAnalyseMapper.adInfoBoard(query);
    }

    /**
     * 投放分析 - 投放时段分布
     * @param query
     * @return
     */
    public List<JSONObject> deliveryTimeDistribution(SingleMetricsQuery query) {
        List<GeneralViewVo> datas = deliveryAnalyseMapper.deliveryTimeDistribution(query);
        if(!CollectionUtils.isEmpty(datas)) {
            return datas.stream().map(data -> {
                FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, data, query.getMetric(), "data");
                JSONObject vo = JSONObject.parseObject(JSONObject.toJSONString(data));
                vo.put("data", Optional.ofNullable(data.getData()).map(Double::valueOf).orElse(Double.valueOf(0)));
                return vo;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 广告数量环图
     * @param requestParam
     * @return
     */
    public JSONObject adNumRing(BaseRequestParam requestParam) {
        List<AdNumRingVo> datas = deliveryAnalyseMapper.adNumRing(requestParam);
        JSONObject result = JSONObject.parseObject("{'overnum':0,'downnum':0}");
        if(!CollectionUtils.isEmpty(datas)) {
            datas.stream().forEach(data -> {
                result.put(data.getIsOut() ? "overnum" : "downnum", data.getData());
            });
        }
        return result;
    }

    /**
     * 投放分析 - 广告溢出率环图
     * @param requestParam
     * @return
     */
    public JSONObject adOverflowRing(BaseRequestParam requestParam) {
        List<AdOverflowRingVo> datas = deliveryAnalyseMapper.adOverflowRing(requestParam);
        JSONObject result = JSONObject.parseObject("{'lessp10':0, 'lessp20':0, 'lessp31':0}");
        datas.forEach(data -> {
            Optional.of(data.getOutRate())
                .ifPresent(rate -> {
                    if(rate <= 0.1) {
                        result.put("lessp10", result.getIntValue("lessp10") + 1);
                    } else if(rate <= 0.2) {
                        result.put("lessp20", result.getIntValue("lessp20") + 1);
                    } else {
                        result.put("lessp31", result.getIntValue("lessp31") + 1);
                    }
                });
        });
        return result;
    }

    /**
     * 指标对比分析
     * @param query
     * @return
     */
    public List<JSONObject> indicatorsCompareAnalysis(MetricsQuery query) {
        List<GeneralViewVo> datas = deliveryAnalyseMapper.indicatorsCompareAnalysis(query);
        List<JSONObject> results = new ArrayList<>();

        JSONObject result1 = new JSONObject();
        result1.put("name", query.getMetric1());
        result1.put("data", new JSONArray());

        JSONObject result2 = new JSONObject();
        result2.put("name", query.getMetric2());
        result2.put("data", new JSONArray());

        if(!CollectionUtils.isEmpty(datas)) {
            // 翻译第一个指标数据
            List<JSONObject> mertics1 = datas.stream().map(data -> {
                JSONObject vo = new JSONObject();
                FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, data, query.getMetric1(), "data");
                vo.put("xaxis", data.getXaxis());
                vo.put("data", Optional.ofNullable(data.getData()).map(Double::valueOf).orElse(Double.valueOf(0)));
                return vo;
            }).collect(Collectors.toList());
            result1.put("data", mertics1);
            // 翻译第二个指标数据
            List<JSONObject> mertics2 = datas.stream().map(data -> {
                JSONObject vo = new JSONObject();
                FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, data, query.getMetric2(), "data");
                vo.put("xaxis", data.getXaxis());
                vo.put("data", Optional.ofNullable(data.getData()).map(Double::valueOf).orElse(Double.valueOf(0)));
                return vo;
            }).collect(Collectors.toList());
            result2.put("data", mertics2);
        }
        results.add(result1);
        results.add(result2);
        return results;
    }

    /**
     * 投放分析 - 多轴联动指标对比分析
     * @param query
     * @return
     */
    public List<JSONObject> mutliIndicatorsCompareAnalysis(SingleMetricsQuery query) {
        List<GeneralViewVo> datas = deliveryAnalyseMapper.mutliIndicatorsCompareAnalysis(query);
        List<JSONObject> results = new ArrayList<>();
        if(!CollectionUtils.isEmpty(datas)) {
            JSONObject result = new JSONObject();
            result.put("name", query.getMetric());
            result.put("data", datas.stream().map(data -> {
                FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, data, query.getMetric(), "data");
                JSONObject vo = new JSONObject();
                vo.put("xaxis", data.getXaxis());
                vo.put("data", Optional.ofNullable(data.getData()).map(Double::valueOf).orElse(Double.valueOf(0)));
                return vo;
            }).collect(Collectors.toList()));
            results.add(result);
        }
        return results;
    }

    /**
     * 投放分析 - 上小时同比涨幅
     * @return
     */
    public List<JSONObject> lastHourIncrease(SingleMetricsQuery query) {
        List<GeneralViewVo> datas = deliveryAnalyseMapper.lastHourIncrease(query);
        List<JSONObject> results = new ArrayList<>();
        if(!CollectionUtils.isEmpty(datas)) {
            return datas.stream().map(data -> {
                FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, data, query.getMetric(), "data");
                JSONObject vo = new JSONObject();
                vo.put("xaxis", data.getXaxis());
                vo.put("data", JSONObject.parseObject(String.format("{'data': %s}", Optional.ofNullable(data.getData()).map(Double::valueOf).orElse(Double.valueOf(0)))));
                return vo;
            }).collect(Collectors.toList());
        }
        return results;
    }

    /**
     * 广告筛选器
     * @param query
     * @return
     */
    public List<CreativeFilterVo> creativeFilter(SingleMetricsQuery query) {
        List<CreativeFilterVo> datas = deliveryAnalyseMapper.creativeFilter(query);
        if(!CollectionUtils.isEmpty(datas)) {
            datas = datas.stream().map(data -> {
                FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, data, query.getMetric(), "data");
                return data;
            }).sorted((o1, o2) -> {
                Double d1 = Optional.ofNullable(o1.getData()).map(Double::valueOf).orElse(Double.valueOf(0));
                Double d2 = Optional.ofNullable(o2.getData()).map(Double::valueOf).orElse(Double.valueOf(0));
                // 针对快排增加以下取反操作
                return ~Double.compare(d1, d2);
            }).limit(5).peek(data -> {
                if(query.getMetric().contains("率")) {
                    String value = new BigDecimal(data.getData().toString())
                                        .multiply(new BigDecimal(100))
                                        .setScale(2, BigDecimal.ROUND_HALF_UP) + "%";
                    data.setData(value);
                }
            }).collect(Collectors.toList());
        }
        return datas;
    }

    /**
     * 投放分析 - 新建广告趋势
     * @param query
     * @return
     */
    public List<JSONObject> newAdTrends(NewAdsQuery query) {
        List<GeneralViewVo> datas = deliveryAnalyseMapper.newAdTrends(query);
        if(!CollectionUtils.isEmpty(datas)) {
            return datas.stream().map(data -> {

                return JSONObject.parseObject(
                    String.format("{'data':%s,'xaxis':'%s'}",
                        Optional.ofNullable(data.getData()).map(Double::valueOf).orElse(Double.valueOf(0)), data.getXaxis())
                );
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 定向包数据查询
     * @param query
     * @return
     */
    public List<JSONObject> targetingDataAnalyse(SingleMetricsQuery query) {
        // 首先进行加载PG内的广告组以及广告创意数据
        TargetingFilterVo filter = deliveryAnalyseMapper.targetingFilter(query);
        if(null != filter) {
            query.setAdgroupIds(filter.getAdgroupIds())
                 .setCreativeIds(filter.getCreativeIds());
        }
        List<TargetingViewVo> datas = self().getTargetingAnalyseDatas(query);
        if(!CollectionUtils.isEmpty(datas)) {
            return datas.stream().map(data -> {
                FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, data, query.getMetric(), "data");

                return JSONObject.parseObject(
                    String.format("{'data':%s,'name':'%s'}",
                        Optional.ofNullable(data.getData()).map(Double::valueOf).orElse(Double.valueOf(0)), data.getName())
                );
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public List<JSONObject> adBurnoutAnalysis(SingleMetricsQuery query) {
        List<AdBurnoutVo> datas = deliveryAnalyseMapper.adBurnoutAnalysis(query);
        // 计算点击率
        List<JSONObject> clickRates = datas.stream().map(data -> {
            FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, data, query.getMetric(), "data");
            FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, data, "点击率", "xaxis");

            return JSONObject.parseObject(String.format("{'status':'%s', 'adgroupid': '%s', 'xaxis': %s, 'data': %s}",
                data.getStatus(),
                data.getAdgroupId(),
                Optional.ofNullable(data.getXaxis()).map(Double::valueOf).orElse(Double.valueOf(0)),
                Optional.ofNullable(data.getData()).map(Double::valueOf).orElse(Double.valueOf(0))
            ));
        }).collect(Collectors.toList());
        // 计算转换率
        List<JSONObject> convertRates = datas.stream().map(data -> {
            FieldMappers.objectMapped(FieldMappers.DELIVERY_ANALYSE_MAPPERS, data, "目标转化率", "xaxis");

            return JSONObject.parseObject(String.format("{'status':'%s', 'adgroupid': '%s', 'xaxis': %s, 'data': %s}",
                data.getStatus(),
                data.getAdgroupId(),
                Optional.ofNullable(data.getXaxis()).map(Double::valueOf).orElse(Double.valueOf(0)),
                Optional.ofNullable(data.getData()).map(Double::valueOf).orElse(Double.valueOf(0))
            ));
        }).collect(Collectors.toList());

        List<JSONObject> results = new ArrayList<>();
        JSONObject clickRateJson = new JSONObject();
        clickRateJson.put("xaxis", "点击率");
        clickRateJson.put("data", clickRates);
        results.add(clickRateJson);

        JSONObject convertRateJson = new JSONObject();
        convertRateJson.put("xaxis", "转化率");
        convertRateJson.put("data", convertRates);
        results.add(convertRateJson);
        return results;
    }

    public List<JSONObject> adEffectAnalysis(BaseRequestParam query) {
        List<AdBurnoutVo> datas = deliveryAnalyseMapper.adBurnoutAnalysis(query);

        if(!CollectionUtils.isEmpty(datas)) {
            return datas.stream().map(data -> {
                return JSONObject.parseObject(String.format("{'status':'%s', 'adgroupid': '%s', 'convertrate': %s, 'clickrate': %s}",
                    data.getStatus(),
                    data.getAdgroupId(),
                    Optional.ofNullable(data.getConvertRate()).map(Double::valueOf).orElse(Double.valueOf(0)),
                    Optional.ofNullable(data.getClickRate()).map(Double::valueOf).orElse(Double.valueOf(0))
                ));
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 广告成本溢出分析
     * @param query
     * @return
     */
    public List<JSONObject> adBidTrendAnalyse(BaseRequestParam query) {
        List<JSONObject> results = new ArrayList<>();
        // 首先进行加载PG内的广告组以及广告创意数据
        TargetingFilterVo filter = deliveryAnalyseMapper.targetingFilter(query);
        if(null != filter) {
            query.setAdgroupIds(filter.getAdgroupIds());
        }
        JSONObject averageBidJson = new JSONObject();
        // 首先进行获取平均出价
        List<AdAverageVo> averageBids = self().getAverageBid(query);
        averageBidJson.put("data", averageBids);
        averageBidJson.put("xaxis", "平均出价");
        // 计算平均转化成本
        JSONObject averageConvertJson = new JSONObject();
        List<AdAverageVo> averageConvertCost = deliveryAnalyseMapper.adAverageConvertCost(query);
        Map<String, Double> dataMap = new HashMap<>();
        query.getDateRanges().forEach(date -> {
            dataMap.put(date, 0D);
        });
        averageConvertCost.forEach(convert -> {
            if(dataMap.containsKey(convert.getXaxis())) {
                dataMap.put(convert.getXaxis(), convert.getData());
            }
        });

        averageConvertJson.put("data", dataMap.keySet().stream().map(key -> {
            return new AdAverageVo().setData(dataMap.get(key)).setXaxis(key);
        }).collect(Collectors.toList()));
        averageConvertJson.put("xaxis", "平均目标转化成本");

        results.add(averageBidJson);
        results.add(averageConvertJson);
        return results;
    }

    /**
     * 获取未溢出的广告组信息
     * @param query
     * @return
     */
    public List<Long> getAdDownIds(BaseRequestParam query) {
        // 获取所有的广告数量
        List<AdNumOverFlowVo> datas = deliveryAnalyseMapper.getAdNumOverFlows(query);
        return datas.stream().filter(data -> !data.getIsOut()).map(AdNumOverFlowVo::getAdgroupId).collect(Collectors.toList());
    }
    /**
     * 获取溢出的广告组信息
     * @param query
     * @return
     */
    public List<Long> getAdOverIds(BaseRequestParam query) {
        // 获取所有的广告数量
        List<AdNumOverFlowVo> datas = deliveryAnalyseMapper.getAdNumOverFlows(query);
        return datas.stream().filter(data -> data.getIsOut()).map(AdNumOverFlowVo::getAdgroupId).collect(Collectors.toList());
    }

    /**
     * 获取广告溢出占比的广告组信息
     * @param query
     * @return
     */
    public List<Long> getAdPercentIds(PercentOverQuery query) {
        List<AdNumOverFlowVo> datas = deliveryAnalyseMapper.getAdNumOverFlows(query);
        return datas.stream().filter(data -> {
            Double outRate = Optional.of(data.getOutRate()).map(rate -> {
                if(rate < 0) {
                    return 0D;
                }
                if(rate > 0.2) { // 排除大于20%的相关数据
                    return 0.2D;
                }
                return rate;
            }).orElse(0D);
            return outRate >= query.getStartP() && outRate <= query.getEndP();
        }).map(AdNumOverFlowVo::getAdgroupId).collect(Collectors.toList());
    }

    @DS("clickhouse")
    public List<AdAverageVo> getAverageBid(BaseRequestParam query) {
        List<String> dateRanges = query.getDateRanges();
        if(!CollectionUtils.isEmpty(dateRanges)) {
            return Lists.partition(dateRanges, 3).stream().map(dateRange -> {
                BaseRequestParam newQuery = new BaseRequestParam();
                BeanUtils.copyProperties(dateRange,newQuery );
                newQuery.setDateRanges(dateRange);
                return deliveryAnalyseMapper.adAverageBid(newQuery);
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @DS("clickhouse")
    public List<TargetingViewVo> getTargetingAnalyseDatas(SingleMetricsQuery query) {
        return deliveryAnalyseMapper.targetingDataAnalyse(query);
    }

    /**
     * 投放分析 - 数据概览(落地页数据统计)
     * @return
     */
    @DS("clickhouse")
    public GeneralViewVo landingDataOverview(GeneralViewQuery query) {
        return deliveryAnalyseMapper.landingDataOverview(query);
    }


}
