package ai.yiye.agent.statistics.domain;

import lombok.Data;

import java.util.Date;

/**
 * 一叶系统落地页曝光对象 page_view_info
 *
 * <AUTHOR>
 */
@Data
public class PageViewInfo extends BaseEntity {

    /** ID */
    private Long id;

    /** 公司id */
    private String accountId;

    /** 落地页id */
    private Long landingPageId;

    /** 组件id */
    private Long widgetTemplateId;

    /** 组件类型 */
    private Integer widgetTemplateType;

    /** 落地页访问用户ip */
    private String ip;

    /** 通过ip解析出的 省 */
    private String province;

    /** 城市 */
    private String city;

    /** 运营商 */
    private String operator;

    /** user-agent */
    private String ua;

    /** yiye.ai 域名下 同一个浏览器上的唯一标识 */
    private String uid;

    /** 同一次访问的sessin_id */
    private String sid;

    /** 落地页每次加载一次生成一个,--曝光id */
    private String pid;

    /** 访问来源,--访问页面从哪个页面跳转过来的 */
    private String referrer;

    /** 访问的页面 */
    private String origin;

    /** 访问url */
    private String url;

    /** 设备品牌 */
    private String brand;

    /** 设备价格 */
    private String devicePrice;

    /** 设备信息 */
    private String device;

    /** 操作系统 */
    private String os;

    /** 操作系统类型 */
    private String osType;

    /** 浏览器 */
    private String browser;

    /** 浏览器型号 */
    private String browserType;

    /** 网络类型 */
    private String networkType;

    /** 屏幕尺寸 */
    private String screenSize;

    /** 分辨率 */
    private String screenDpi;

    /** 停留时长 */
    private Double lengthOfStay;

    /** 访问深度 */
    private Double accessDepth;

    /** null */
    private String viewContent;

    /** 填单id */
    private Long submitDataId;

    /** 是否填单 */
    private Integer fill;

    /** 该次曝光-对应的填单内容 */
    private String fillContent;

    /** 填单时间 */
    private Date fillAt;

    /** null */
    private Date createdAt;

    /** null */
    private Date updatedAt;

    /** null */
    private String clickId;

    /**
     * 扩展字段
     */
    private String ext;

    private String wechatOpenid;

    /**
     * 广告组id
     */
    private Long adgroupId;

    /**
     * 平台id
     */
    private Long platformId;
    /***
     * 微信客服需要用的场景值，可以跟pid进行查询
     */
    private String scene;
}
