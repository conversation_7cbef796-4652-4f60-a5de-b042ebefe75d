<!DOCTYPE html>
<html lang="en">

<head>
    <title>遇到点问题丨一叶智能</title>
    <link rel="icon" href="https://saas-static.yiye.ai/favicon.ico" type="image/x-icon">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <style>
        * {
            padding: 0;
            margin: 0;
            font-family: Arial, Hiragino Sans GB, Helvetica, PingFang SC, Microsoft YaHei, sans-serif;
        }

        @media only screen and (max-width: 992px) {
            .header {
                display: none;
            }

            .info-container {
                margin-top: 110px;
                overflow: hidden;
            }

            .img {
                width: 336px;
                height: 180px;
            }

            .info-code {
                height: 40px;
            }

            .info-code-span {
                border-bottom: 1px solid #333;
                padding-bottom: 3px;
                line-height: 30px;;
                font-size: 30px;
                font-weight: bolder;
            }

            .info-text {
                font-size: 12px;
            }
        }

        @media only screen and (min-width: 993px) {
            .header {
                display: block;
            }

            .info-container {
                margin-top: 100px;
            }

            .img {
                width: 560px;
                height: 300px;
            }

            .info-code {
                height: 90px;
            }

            .info-code-span {
                border-bottom: 1px solid #333;
                padding-bottom: 10px;
                font-size: 55px;
                font-weight: bold;
            }

            .info-text {
                font-size: 20px;
            }
        }

        .header {
            width: 100%;
            height: 60px;
            background: rgb(39, 44, 66);
            background-image: url(/images/logo.png);
            background-repeat: no-repeat;
            background-position: 20px center;
        }

        .img-container, .info-code, .info-text {
            text-align: center;
        }

    </style>
</head>
<body>
<div class="header">

</div>
<div class="info-container">
    <div class="img-container">
        <img class="img" src="https://saas-static.yiye.ai/500.jpg" alt="">
    </div>
    <div class="info-code">
        <span class="info-code-span">500</span>
    </div>
    <div class="info-text">服务器错误</div>
</div>
</body>
</html>
