package ai.yiye.agent.marketing.collect.mapper;

import ai.yiye.agent.domain.dto.MaterialAdRelDto;
import ai.yiye.agent.domain.marketing.data.MaterialDailyReport;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface MaterialDailyReportMapper extends BaseMapper<MaterialDailyReport> {

    List<MaterialAdRelDto> selectMaterialByAdId(@Param(Constants.WRAPPER) Wrapper wrapper);

    /**
     * 删除素材报表的视频截图数据
     *
     * @param accountId
     */
    void deleteVideoScreenshot(@Param("accountId") String accountId);

    /**
     * 将报表重复素材的更新到material_group
     *
     * @param accountId
     */
    void updateMaterialGroup(@Param("accountId") String accountId, @Param("startAt") LocalDate startAt, @Param("endAt") LocalDate endAt);

    /**
     * 删除重复的素材报表，只保留按照width+height分组后的第一个
     *
     * @param accountId
     */
    void deleteRepetitionReport(@Param("accountId") String accountId);

}
