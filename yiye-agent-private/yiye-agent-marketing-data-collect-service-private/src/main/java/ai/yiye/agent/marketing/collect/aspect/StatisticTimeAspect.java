package ai.yiye.agent.marketing.collect.aspect;

import ai.yiye.agent.domain.AdvertiserAccount;
import ai.yiye.agent.marketing.aspect.StatisticTime;
import ai.yiye.agent.marketing.oceanengine.OceanApiModule;
import ai.yiye.agent.marketing.tencent.TencentApiModule;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;

@Slf4j
@Aspect
@Component
public class StatisticTimeAspect {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Pointcut("@annotation(ai.yiye.agent.marketing.aspect.StatisticTime)")
    public void statisticTimePoint() {
    }


    @Around(value = "statisticTimePoint()")
    public Object statisticTimeEnhance(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        StatisticTime statisticTime = signature.getMethod().getAnnotation(StatisticTime.class);

        Object[] args = point.getArgs();
        AdvertiserAccount advertiserAccount = new AdvertiserAccount();
        String key = "";
        for (Object a : args) {
            if (a instanceof AdvertiserAccount) {
                advertiserAccount = ((AdvertiserAccount) a);
            } else if (a instanceof TencentApiModule) {
                TencentApiModule tencentApiModule = (TencentApiModule) a;
                key = tencentApiModule.name();
            } else if (a instanceof OceanApiModule) {
                OceanApiModule oceanApiModule = (OceanApiModule) a;
                key = oceanApiModule.name();
            }
        }
        if (StringUtils.isBlank(key) && statisticTime.isDataStore()) {
            key = signature.getDeclaringTypeName() + "." + signature.getName();
        }
        String requestId = advertiserAccount.getRequestId();
        if (StringUtils.isBlank(requestId)) {
            return point.proceed(args);
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Object obj = point.proceed(args);
        stopWatch.stop();
        redisTemplate.boundHashOps(requestId).expire(Duration.ofMinutes(30));
        redisTemplate.boundHashOps(requestId).increment(key, stopWatch.getTotalTimeMillis());
        return obj;
    }
}

@Data
class StopWatch {

    private Instant startTime;
    private Instant endTime;

    public void start() {
        startTime = Instant.now();
    }

    public void stop() {
        endTime = Instant.now();
    }

    public Long getTotalTimeMillis() {
        return Duration.between(startTime, endTime).toMillis();
    }
}
