package ai.yiye.agent.marketing.collect.service.vivo;

import ai.yiye.agent.domain.AdvertiserAccount;
import ai.yiye.agent.domain.enumerations.Platform;
import ai.yiye.agent.domain.marketing.data.Advertise;
import ai.yiye.agent.domain.marketing.data.AdvertiserCreative;
import ai.yiye.agent.domain.marketing.data.AdvertiserCreativeMaterialRelation;
import ai.yiye.agent.domain.marketing.data.AdvertiserMaterial;
import ai.yiye.agent.marketing.collect.mapper.AdvertiseBaseMapper;
import ai.yiye.agent.marketing.collect.mapper.AdvertiserCreativeMapper;
import ai.yiye.agent.marketing.collect.service.AbstractAcquireBeanService;
import ai.yiye.agent.marketing.collect.service.AdvertiserCollectService;
import ai.yiye.agent.marketing.mapper.MappedPropertyConfig;
import ai.yiye.agent.marketing.oceanengine.OceanApiModule;
import ai.yiye.agent.marketing.vivo.VivoApiModule;
import ai.yiye.agent.marketing.vivo.client.VivoAdApiClient;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class VivoAdvertiserCreativeService extends AbstractAcquireBeanService<AdvertiserCreativeMapper, AdvertiserCreative> implements AdvertiserCollectService<AdvertiserCreative> {

    private static final String CODE_NAMES[] = {"imgsCode", "avatarCode", "videoCode"};
    @Autowired
    private VivoAdApiClient vivoAdApiClient;
    @Autowired
    private AdvertiseBaseMapper advertiseBaseMapper;
    @Autowired
    private VivoAdvertiserAdService adService;

    public List<AdvertiserCreative> getAll(AdvertiserAccount account, List<Advertise> advertises) {
        // 将所有广告id放入参数中
        Map<String, Object> params = this.buildParam(advertises);
        //获取所有广告id的创意
        return Optional.ofNullable(account)
            .map(ac -> {
                List<Object> responseBody = this.vivoAdApiClient.getAll(VivoApiModule.creative, ac, params);
                return Optional.ofNullable(getDatas(account, AdvertiserCreative.class, responseBody))
                    .map(list -> {
                        return this.saveAdsAndCreatives(list, advertises, account);
                    }).orElse(null);
            }).orElse(null);
    }

    /**
     * 将广告和创意组装起来
     * 一个广告会有多个创意，故marketing_data_advertise表中一个广告需要关联创意id之后保存多条
     *
     * @param creatives
     * @param advertises
     * @param account
     * @return
     */
    private List<AdvertiserCreative> saveAdsAndCreatives(List<AdvertiserCreative> creatives, List<Advertise> advertises, AdvertiserAccount account) {
        Map<Long, List<AdvertiserCreative>> adCreativeMap = creatives.stream()
            .map(e -> {
                e.setAdvertiserAccountStatus(account.getSystemStatus());
                return e;
            })
            .collect(Collectors.groupingBy(AdvertiserCreative::getAdvertisementId));
        List<Advertise> actualAdvertises = advertises.stream().map(ad -> {
            List<AdvertiserCreative> advertiserCreatives = adCreativeMap.get(ad.getAdId());
            List<Advertise> adList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(advertiserCreatives)) {
                advertiserCreatives.forEach(creative -> {
                    Advertise newAd = new Advertise();
                    BeanUtils.copyProperties(ad, newAd);
                    newAd.setAdcreativeId(creative.getCreativeId());
                    newAd.setAdcreative(creative.getExt());
                    adList.add(newAd);
                });

            }
            return adList;
        }).flatMap(List::stream).collect(Collectors.toList());
        // 保存广告数据
        this.adService.pageInsert(account, actualAdvertises);
        // 保存创意数据
        this.pageInsert(account, creatives);
        // 保存创意和图片之间的关联
        this.saveCreativeMaterialRel(creatives);
        return creatives;
    }

    /**
     * 保存创意和图片素材之间的关联
     *
     * @param creativeList
     */
    private void saveCreativeMaterialRel(List<AdvertiserCreative> creativeList) {
        List<AdvertiserCreativeMaterialRelation> relations = new ArrayList<>();
        Optional.ofNullable(creativeList)
            .ifPresent(creatives ->
                creatives.forEach(c -> {
                        if (c.getExt() != null) {
                            // 从创意数据中分别获取图片、头像、视频的code
                            for (String codeName : CODE_NAMES) {
                                String imgCode = c.getExt().getString(codeName);
                                if(!StringUtils.isEmpty(imgCode)){
                                    AdvertiserCreativeMaterialRelation relation = new AdvertiserCreativeMaterialRelation();
                                    relation.setCreativeId(c.getCreativeId());
                                    relation.setImageId(imgCode);
                                    relation.setMaterialId(imgCode);
                                    relation.setPlatformId(Platform.VIVO.getId());
                                    relations.add(relation);
                                }
                            }
                        }
                    }
                )
            );
        Optional.of(relations).filter(list->list.size()>0)
            .ifPresent(rels ->
                advertiseBaseMapper.saveOrUpdateBatch(AdvertiserCreativeMaterialRelation.class,
                    rels,
                    AdvertiserCreativeMaterialRelation.CONFLICTS));
    }

    /**
     * 构建参数
     * @param advertises
     * @return
     */
    private Map<String, Object> buildParam(List<Advertise> advertises) {
        Map<String, Object> params = new HashMap<>();
        Set<Long> advertisementIds = new HashSet<>();
        Optional.ofNullable(advertises).ifPresent(list -> {
            list.forEach(ad -> {
                Optional.ofNullable(ad.getAdId()).ifPresent(id -> {
                    advertisementIds.add(id);
                });
            });
        });
        params.put("advertisementIds", advertisementIds);
        return params;
    }

    @Override
    public List<AdvertiserCreative> getAll(AdvertiserAccount advertiserAccount) {
        return null;
    }

    @Override
    public List<AdvertiserCreative> pageInsert(AdvertiserAccount account, List<AdvertiserCreative> beans) {
        Optional.ofNullable(beans)
            .filter(creatives -> creatives.size() > 0)
            .ifPresent(creative -> advertiseBaseMapper.saveOrUpdateBatch(AdvertiserCreative.class, creative, AdvertiserCreative.CONFLICTS));
        return beans;
    }

    @Override
    public List<MappedPropertyConfig> getMappedPropertyConfigs() {
        return VivoApiModule.creative.getMappedPropertyConfigs();
    }
}
