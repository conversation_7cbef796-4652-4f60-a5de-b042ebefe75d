package ai.yiye.agent.marketing.collect.stream;

import ai.yiye.agent.domain.marketing.data.AdvertiserMaterial;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class MaterialSender {

    private static final String materialTopic = "yiye-agent-private.material-data-analyse.exchange";

    @Autowired
    private StreamBridge streamBridge;


    public void sendMaterialAnalyse(AdvertiserMaterial material) {
        Optional.ofNullable(material)
            .ifPresent(ms -> streamBridge.send(materialTopic, material));
    }
}
