package ai.yiye.agent.marketing.collect.mapper;

import ai.yiye.agent.domain.boss.BossAdvertiserAccountFundExtractRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/7/18 19:41
 */
@Repository
public interface BossAdvertiserAccountFundExtractRecordMapper extends BaseMapper<BossAdvertiserAccountFundExtractRecord> {
    void saveBatchByDayTimeAndAccount(@Param("record") BossAdvertiserAccountFundExtractRecord record);


}
