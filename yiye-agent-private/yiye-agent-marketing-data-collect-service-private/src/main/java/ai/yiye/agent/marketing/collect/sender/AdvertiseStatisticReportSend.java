package ai.yiye.agent.marketing.collect.sender;

import ai.yiye.agent.autoconfigure.rabbitmq.config.Constants;
import ai.yiye.agent.domain.dto.AdvertiseStatisticReportJobDto;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AdvertiseStatisticReportSend {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void sendAdvertiseStatisticReport(AdvertiseStatisticReportJobDto advertiseStatisticReportJobDto){
        Message message = new Message(JSON.toJSONString(advertiseStatisticReportJobDto).getBytes());
        rabbitTemplate.convertAndSend(Constants.MARKETING_ADVERTISE_STATISTIC_REPORT_EXCHANGE, Constants.MARKETING_ADVERTISE_STATISTIC_REPORT_QUEUE, message);
    }

    public void sendAdvertiseStatisticReportNew(AdvertiseStatisticReportJobDto advertiseStatisticReportJobDto) {
        Message message = new Message(JSON.toJSONString(advertiseStatisticReportJobDto).getBytes());
        rabbitTemplate.convertAndSend(Constants.MARKETING_ADVERTISE_STATISTIC_REPORT_EXCHANGE_NEW, Constants.MARKETING_ADVERTISE_STATISTIC_REPORT_QUEUE_NEW, message);
    }

}
