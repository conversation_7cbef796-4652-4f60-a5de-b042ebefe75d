package ai.yiye.agent.marketing.collect.job;

import ai.yiye.agent.autoconfigure.mybatis.multidatasource.service.AgentConfService;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AdvertiserAccount;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.domain.dto.AccountMessageDto;
import ai.yiye.agent.domain.enumerations.Platform;
import ai.yiye.agent.marketing.collect.remote.AdvertiserAccountRemote;
import ai.yiye.agent.marketing.collect.stream.AccountMessageService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class VivoDataCollectJob {

    @Autowired
    private AdvertiserAccountRemote accountRemote;
    @Autowired
    private AccountMessageService accountMessageService;

    @Autowired
    private AgentConfService agentConfService;

    /**
     * 拉取广告基础数据
     *
     * @param param
     * @return
     */
    @XxlJob(value = "vivoDataCollectExecutor")
    public ReturnT<String> collectAdData(String param) {
        //saas
        List<AgentConf> agentDBConfigs = agentConfService.list(new LambdaQueryWrapper<>());
        agentDBConfigs.parallelStream().forEach(e -> {
            TenantContextHolder.set(e.getAgentId());
            List<AdvertiserAccount> accounts = accountRemote.getAdvertiserAccountByPlatFormId(Lists.newArrayList(Platform.VIVO.getId()));
            accounts.stream().forEach(advertiserAccount -> {
                // 判断投放账号是否进行抽取数据
                if (null != advertiserAccount.getExtractFlag() && advertiserAccount.getExtractFlag()) {
                    accountMessageService.sendAccounts(toDto(advertiserAccount, false));
                }
            });
            TenantContextHolder.clearContext();
        });

        return ReturnT.SUCCESS;
    }

    /**
     * 拉取素材
     *
     * @param param
     * @return
     */
    @XxlJob(value = "vivoMaterialCollectExecutor")
    public ReturnT<String> collectMaterialData(String param) {
        List<AdvertiserAccount> accounts = accountRemote.getAdvertiserAccountByPlatFormId(Lists.newArrayList(Platform.VIVO.getId()));
        accounts.parallelStream().forEach(advertiserAccount -> {
            // 判断投放账号是否进行抽取数据
            if (null != advertiserAccount.getExtractFlag() && advertiserAccount.getExtractFlag()) {
                accountMessageService.sendAccounts(toDto(advertiserAccount, true));
            }
        });
        return ReturnT.SUCCESS;
    }

    private AccountMessageDto toDto(AdvertiserAccount account, Boolean extractMaterial) {
        AccountMessageDto dto = new AccountMessageDto();
        dto.setAdvertiserAccount(account);
        dto.setAccountId(account.getAccountId());
        dto.setExtractVivoMaterial(extractMaterial);
        return dto;
    }
}
