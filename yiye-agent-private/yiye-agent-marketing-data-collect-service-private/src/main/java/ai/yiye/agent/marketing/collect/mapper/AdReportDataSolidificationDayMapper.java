package ai.yiye.agent.marketing.collect.mapper;

import ai.yiye.agent.domain.DataSolidificationDay;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AdReportDataSolidificationDayMapper extends BaseMapper<DataSolidificationDay> {

    void deleteSolidDataByDayList(@Param("advertiserAccountId") Long advertiserAccountId, @Param("dayTime") String dayTime);

    void updateSolidData(@Param("advertiserAccountId") Long advertiserAccountId, @Param("dayTime") String dayTime);
}
