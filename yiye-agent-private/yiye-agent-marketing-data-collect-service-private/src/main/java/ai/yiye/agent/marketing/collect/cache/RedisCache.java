package ai.yiye.agent.marketing.collect.cache;

import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Component
public class RedisCache<K, V> implements Cache<K, V> {


    private final RedisTemplate<K, V> redisTemplate;

    public RedisCache(RedisTemplate<K, V> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }


    @Override
    public void putCache(K key, V value) {
        redisTemplate.opsForValue().set(key, value);
    }

    @Override
    public void putCache(K key, Supplier<V> value) {
        redisTemplate.opsForValue().getAndSet(key, value.get());
    }

    @Override
    public void putCache(K key, V value, Long time, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, time, timeUnit);
    }

    @Override
    public void putCache(K key, Supplier<V> value, Long time, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value.get(), time, timeUnit);
    }

    /**
     * 批量插入数据
     * @param datas
     */
    @Override
    public void putBatchCache(Map<K, V> datas, Long time, TimeUnit timeUnit) {
        redisTemplate.execute(new RedisCallback<V>() {
            RedisSerializer serializer = redisTemplate.getDefaultSerializer();
            @Override
            public V doInRedis(RedisConnection connection) throws DataAccessException {
                try {
                    connection.openPipeline();
                    datas.entrySet().stream().forEach(data -> {
                        connection.set(serializer.serialize(data.getKey()),
                            serializer.serialize(data.getValue()),
                            Expiration.from(time, timeUnit),
                            RedisStringCommands.SetOption.UPSERT
                        );
                    });
                } finally {
                    connection.closePipeline();
                }
                return null;
            }
        });
    }


    @Override
    public V getAndSetCache(K k, Supplier<V> value) {
        return Optional.ofNullable(redisTemplate.opsForValue().get(k))
            .orElseGet(() -> {
                V v = value.get();
                redisTemplate.opsForValue().set(k, v);
                return v;
            });
    }

    @Override
    public V get(K k) {
        return redisTemplate.opsForValue().get(k);
    }

    @Override
    public V getAndSetCache(K k, Supplier<V> value, Long time, TimeUnit timeUnit) {
        return Optional.ofNullable(redisTemplate.opsForValue().get(k))
            .orElseGet(() -> {
                V v = value.get();
                redisTemplate.opsForValue().set(k, v, time, timeUnit);
                return v;
            });
    }

    @Override
    public void deleteCache(K k) {
        redisTemplate.delete(k);
    }
}
