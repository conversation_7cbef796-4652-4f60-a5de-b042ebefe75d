package ai.yiye.agent.marketing.collect.aspect.retry;

import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.enumerations.Platform;
import ai.yiye.agent.marketing.collect.config.RabbitMqConfig;
import ai.yiye.agent.marketing.oceanengine.OceanApiModule;
import ai.yiye.agent.marketing.oceanengine.OceanEngineApiResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static ai.yiye.agent.marketing.collect.config.RabbitMqConfig.DELAY_MILLS;
import static ai.yiye.agent.marketing.oceanengine.OceanEngineConstants.ERROR_STATUS_CODES;

/**
 * 暂时只对创意、创意小时报表、广告小时报的接口拉取响应切入，判断是否重试(暂时只判断超时和请求频繁的响应)
 */
@Slf4j
@Component
@Aspect
public class DataCollectRetryAspect {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Pointcut("@annotation(ai.yiye.agent.marketing.aspect.DataCollectTaskRetry)")
    public void retryPoint() {
    }

    @Around("retryPoint()")
    public Object retryEnhance(ProceedingJoinPoint point) throws Throwable {
//        MethodSignature signature = (MethodSignature) point.getSignature();
        Object[] args = point.getArgs();
        Object obj = point.proceed(args);
        try {
            OceanEngineApiResponse response = (OceanEngineApiResponse)obj;
            // 判断返回的响应码在需要采集的集合中
            if(response != null && ERROR_STATUS_CODES.containsKey(String.valueOf(response.getCode()))) {
                OceanApiModule module = (OceanApiModule) args[0];
                if (module == OceanApiModule.hourlyReport
                    || module == OceanApiModule.creative
                    || module == OceanApiModule.hourlyReportAd) {
                    DataCollectRetryAccount advertiserAccount = new DataCollectRetryAccount();
                    advertiserAccount.setAccessToken(args[4].toString());
                    advertiserAccount.setAccountId(args[3].toString());
                    advertiserAccount.setPlatformId(Platform.OCEAN_ENGINE.getId());
                    // 构造重试mq消息体
                    DataCollectRetryDTO retryDto = new DataCollectRetryDTO();
                    retryDto.setAccount(advertiserAccount);
                    retryDto.setPage((Integer) args[1]);
                    retryDto.setParams((Map) args[5]);
                    retryDto.setModule(module);
                    retryDto.setTriedTimes(0);
                    retryDto.setAgentId(TenantContextHolder.get());

                    MessageProperties messageProperties = new MessageProperties();
                    // 固定30秒钟延迟时间
                    messageProperties.setExpiration(DELAY_MILLS + "");
                    Message message = new Message(JSON.toJSONString(retryDto).getBytes(), messageProperties);
                    log.info("ocean module:{} 请求响应码:{}, 重试消息：{}", module, response.getCode(), retryDto);
                    this.rabbitTemplate.convertAndSend(RabbitMqConfig.DELAY_EXCHANGE_NAME, RabbitMqConfig.ROUTING_KEY, message);
                }
            }
        }catch (Throwable e){
            log.error("DataCollectRetryAspect : 重试切面获取参数出错:{}", e);
            throw e;
        }
        return obj;
    }

    public static void main(String[] args) {
        DataCollectRetryDTO retryDto = new DataCollectRetryDTO();
        DataCollectRetryAccount advertiserAccount = new DataCollectRetryAccount();
        advertiserAccount.setAccessToken("9143cee7c88b163c766f5d51d2a144e412f7906e");
        advertiserAccount.setAccountId("****************");
        advertiserAccount.setPlatformId(Platform.OCEAN_ENGINE.getId());

        retryDto.setAccount(advertiserAccount);
        retryDto.setPage(1);
        Map<String, Object> map = new HashMap<>();
        map.put("time_granularity", "STAT_TIME_GRANULARITY_HOURLY");
        map.put("group_by", "[\"STAT_GROUP_BY_FIELD_ID\",\"STAT_GROUP_BY_FIELD_STAT_TIME\"]");
        map.put("filtering", "{\"ad_ids\":[****************]}");
        map.put("start_date", "2021-06-17");
        map.put("end_date", "2021-06-17");
        retryDto.setParams(map);
        retryDto.setModule(OceanApiModule.hourlyReportAd);
        retryDto.setTriedTimes(0);

        System.out.println(JSON.toJSONString(retryDto));

    }
}
