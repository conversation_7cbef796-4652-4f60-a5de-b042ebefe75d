package ai.yiye.agent.marketing.collect.mapper;

import ai.yiye.agent.domain.clickhousetypehandlers.JSONStringTypeHandler;
import ai.yiye.agent.domain.marketing.data.UpdateIgnore;
import ai.yiye.agent.domain.subtablerules.SubTableUtils;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public final class AdvertiserMapperHelper implements ApplicationContextAware {

    private static DataBaseHandlerMapper handlerMapper;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        handlerMapper = applicationContext.getBean(DataBaseHandlerMapper.class);
    }

    /**
     * 获取对象的表名
     *
     * @param clazz
     * @return
     */
    public static String getTableName(Class<?> clazz, Object data) {
        return Optional.ofNullable(SubTableUtils.reflectGetAndCreateSubTable(clazz, handlerMapper, data))
            .orElse(SqlHelper.table(clazz).getTableName());
    }

    public static String getSqlSelect(Class<?> clazz) {
        return SqlHelper.table(clazz).getFieldList()
            .stream()
            .map(field -> field.getColumn())
            .collect(Collectors.joining(","));
    }

    /**
     * 获取表字段
     *
     * @param clazz
     * @return
     */
    public static String getTableFields(Class<?> clazz) {
        return SqlHelper.table(clazz).getFieldList()
            .stream()
            .map(field -> String.format("#{%s}", field.getEl()))
            .collect(Collectors.joining(","));
    }

    /**
     * 获取列表下表字段
     *
     * @param clazz
     * @return
     */
    public static String getTableFields(Class<?> clazz, String item) {
        return SqlHelper.table(clazz).getFieldList()
            .stream()
            .map(field -> {
                if (null != field.getTypeHandler()
                    && field.getTypeHandler().getName().equals(JSONStringTypeHandler.class.getName())) {
                    return String.format("#{%s.%s, typeHandler=%s}", item, field.getEl(), JSONTypeHandler.class.getCanonicalName());
                } else {
                    return String.format("#{%s.%s}", item, field.getEl());
                }
            })
            .collect(Collectors.joining(","));
    }

    /**
     * 排除typeHandler进行存储数据
     * @param clazz
     * @param item
     * @return
     */
    public static String getTableFieldsWithoutTypeHandler(Class<?> clazz, String item) {
        return SqlHelper.table(clazz).getFieldList()
            .stream()
            .map(field -> {
                return String.format("#{%s.%s}", item, field.getEl());
            })
            .collect(Collectors.joining(","));
    }

    /**
     * 获取更新的相关字段
     *
     * @param clazz
     * @return
     */
    public static String getUpdateSql(Class<?> clazz) {
        return SqlHelper.table(clazz).getFieldList()
            .stream()
            .filter(field -> Optional.ofNullable(field.getField().getAnnotation(UpdateIgnore.class)).map(ignore -> ignore.value()).orElse(true))
            .map(field -> String.format("%s = EXCLUDED.%s", field.getColumn(), field.getColumn()))
            .collect(Collectors.joining(","));
    }

    /**
     * 连接冲突的字段
     *
     * @param conflicts
     * @return
     */
    public static String concatConflictFiled(String[] conflicts) {
        return StringUtils.join(conflicts, ",");
    }
}
