package ai.yiye.agent.marketing.collect.job;

import ai.yiye.agent.autoconfigure.mybatis.multidatasource.service.AgentConfService;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.domain.dto.AdvertiseStatisticReportJobDto;
import ai.yiye.agent.domain.utils.CollectionUtil;
import ai.yiye.agent.marketing.collect.sender.AdvertiseStatisticReportSend;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AdvertiseStatisticReportJob {

    @Autowired
    private AgentConfService agentConfService;

    @Autowired
    private AdvertiseStatisticReportSend advertiseStatisticReportSend;

    /**
     * 投放广告数据统计报表统计
     */
    @XxlJob(value = "AdvertiseStatisticReport")
    public ReturnT<String> advertiseStatisticReport(String param) {
        AdvertiseStatisticReportJobDto advertiseStatisticReportJobDto = generateAdvertiseStatisticReportJobDto(param);
        for (String agentId : advertiseStatisticReportJobDto.getAgentIds()) {
            TenantContextHolder.set(agentId);
            advertiseStatisticReportSend.sendAdvertiseStatisticReport(advertiseStatisticReportJobDto);
            TenantContextHolder.clearContext();
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 投放广告数据统计报表统计(新)
     */
    @XxlJob(value = "AdvertiseStatisticReportNew")
    public ReturnT<String> advertiseStatisticReportNew(String param) {
        AdvertiseStatisticReportJobDto advertiseStatisticReportJobDto = generateAdvertiseStatisticReportJobDto(param);
        for (String agentId : advertiseStatisticReportJobDto.getAgentIds()) {
            TenantContextHolder.set(agentId);
            advertiseStatisticReportSend.sendAdvertiseStatisticReportNew(advertiseStatisticReportJobDto);
            TenantContextHolder.clearContext();
        }
        return ReturnT.SUCCESS;
    }

    private AdvertiseStatisticReportJobDto generateAdvertiseStatisticReportJobDto(String param) {
        AdvertiseStatisticReportJobDto advertiseStatisticReportJobDto = StringUtils.isEmpty(param) ? new AdvertiseStatisticReportJobDto() : JSON.parseObject(param, AdvertiseStatisticReportJobDto.class);
        List<AgentConf> agents = agentConfService.list(Wrappers.lambdaQuery(AgentConf.class).eq(AgentConf::getStatus, 1));
        if (CollectionUtils.isNotEmpty(advertiseStatisticReportJobDto.getAgentIds())) {
            agents = agents.stream().filter(t -> advertiseStatisticReportJobDto.getAgentIds().contains(t.getAgentId())).collect(Collectors.toList());
        }
        final LocalDate statisticStateTime = advertiseStatisticReportJobDto.getStartTime() == null ? LocalDate.now().minusDays(1) : advertiseStatisticReportJobDto.getStartTime();
        final LocalDate statisticEndTime = advertiseStatisticReportJobDto.getEndTime() == null ? LocalDate.now() : advertiseStatisticReportJobDto.getEndTime();
        advertiseStatisticReportJobDto.setAgentIds(CollectionUtil.mapToSet(agents, AgentConf::getAgentId));
        advertiseStatisticReportJobDto.setStartTime(statisticStateTime);
        advertiseStatisticReportJobDto.setEndTime(statisticEndTime);
        return advertiseStatisticReportJobDto;
    }

}
