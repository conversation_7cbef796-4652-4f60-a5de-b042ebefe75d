package ai.yiye.agent.marketing.collect.service.tencent;


import ai.yiye.agent.domain.AdvertiserAccount;
import ai.yiye.agent.domain.AreaDict;
import ai.yiye.agent.domain.TimeUtils;
import ai.yiye.agent.domain.cache.CachePrefix;
import ai.yiye.agent.domain.enumerations.CityLevel;
import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import ai.yiye.agent.domain.marketing.data.AdvertiserAudience;
import ai.yiye.agent.marketing.aspect.StatisticTime;
import ai.yiye.agent.marketing.collect.aspect.EnableClickHouse;
import ai.yiye.agent.marketing.collect.cache.RedisCache;
import ai.yiye.agent.marketing.collect.mapper.AdvertiserAudienceMapper;
import ai.yiye.agent.marketing.collect.service.AbstractAcquireBeanService;
import ai.yiye.agent.marketing.collect.service.AdvertiserCollectService;
import ai.yiye.agent.marketing.collect.service.AreaDictService;
import ai.yiye.agent.marketing.mapper.MappedPropertyConfig;
import ai.yiye.agent.marketing.tencent.client.TencentAdApiClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static ai.yiye.agent.marketing.tencent.TencentApiModule.targeting_tag_reports;

@Slf4j
@Service
public class WechatAdvertiserAudienceService extends AbstractAcquireBeanService<AdvertiserAudienceMapper, AdvertiserAudience> implements AdvertiserCollectService<AdvertiserAudience> {

    private static final Map<String, String> TARGET_TYPE = new HashMap<String, String>() {
        {
            put("AGE_GENDER", "age_gender");
            put("PROVINCE", "region_id");
            put("CITY", "city_id");
        }
    };
    private static final Map<String, String> TARGET_TYPE_NAME = new HashMap<String, String>() {
        {
            put("AGE_GENDER", "age_gender");
            put("PROVINCE", "province");
            put("CITY", "city");
        }
    };
    private static final Map<String, String> GENDER_TYPE = new HashMap<String, String>() {
        {
            put("FEMALE", "女");
            put("MALE", "男");
            put("不限", "其他");
        }
    };


    @Autowired
    private TencentAdvertiserGroupService advertiserGroupService;
    @Autowired
    private TencentAdApiClient tencentAdApiClient;
    @Autowired
    private AdvertiserAudienceMapper advertiserAudienceMapper;
    @Autowired
    private RedisCache<String, String> redisCache;
    @Autowired
    private AreaDictService areaDictService;


    @Override
    public List<AdvertiserAudience> getAll(AdvertiserAccount account) {
        Optional.ofNullable(account)
            .ifPresent(a -> {
                Optional.ofNullable(advertiserGroupService.getAdGroupByAccountId(account.getAccountId()))
                    .ifPresent(advertiserGroups -> {
                        advertiserGroups
                            .forEach(advertiserGroup -> {
                                TARGET_TYPE.keySet().forEach(type -> {
                                    List<Object> lists = tencentAdApiClient.getAll(
                                        targeting_tag_reports,
                                        account,
                                        this.buildParam(type, advertiserGroup.getAdgroupId()));
                                    this.targetSave(
                                        a,
                                        getDatas(account, AdvertiserAudience.class, lists)
                                            .stream()
                                            .peek(data -> {
                                                data.setType(TARGET_TYPE_NAME.get(type));
                                                data.setFirstPayUserNum(data.getWechatFirstPayUserNum());
                                                data.setOrderNum(data.getWechatOrderNum());
                                                data.setEffectiveLeadsUserNum(data.getWechatEffectiveLeadsUserNum());
                                                data.setOfficialAccountFollowNum(data.getWechatOfficialAccountFollowNum());
                                                data.setAdgroupId(advertiserGroup.getAdgroupId());
                                                data.setCampaignId(advertiserGroup.getCampaignId());
                                                data.setAccountId(account.getAccountId());
                                                data.setOptimizerId(account.getOptimizerId());
                                                data.setAdvertiserAccountStatus(account.getSystemStatus());
                                                data.setCreatedAt(TimeUtils.getYesterdayDayInstant());
                                            }).collect(Collectors.toList())
                                    );
                                });
                            });
                    });
            });
        return null;
    }

    private Map<String, Object> buildParam(String type, Long adgroupId) {
        Map<String, Object> params = new HashMap<>();
        params.put("level", "ADGROUP");
        params.put("type", type);
        JSONObject dateRange = new JSONObject();
        dateRange.put("start_date", TimeUtils.getYesterdayDayString());
        dateRange.put("end_date", TimeUtils.getYesterdayDayString());
        params.put("date_range", dateRange.toJSONString());
        params.put("pos_type", "POSITION_TYPE_WECHAT_MOMENTS");
        JSONArray filtering = new JSONArray();
        JSONObject filter = new JSONObject();
        filter.put("field", "adgroup_id");
        filter.put("operator", "EQUALS");
        filter.put("values", new Long[]{adgroupId});
        filtering.add(filter);
        params.put("filtering", filtering.toJSONString());
        return params;
    }

    @Override
    @StatisticTime(isDataStore = true)
    @EnableClickHouse(invoke = AdvertiserAudience.class)
    public List<AdvertiserAudience> pageInsert(AdvertiserAccount account, List<AdvertiserAudience> beans) {
        Optional<AdvertiserAudience> first = beans.stream().findFirst();
        if (first.isPresent() && "age_gender".equals(first.get().getType())) {
            List<AdvertiserAudience> ageAndGenderList = this.composeAgeAndGender(beans).stream().filter(Objects::nonNull).collect(Collectors.toList());
            advertiserAudienceMapper.saveOrUpdateBatch(AdvertiserAudience.class, ageAndGenderList, AdvertiserAudience.CONFLICTS);
            return ageAndGenderList;
        } else {
            beans.forEach(data -> {
                switch (data.getType()) {
                    case "province":
                        this.composeRegion(data, CityLevel.CITY_LEVEL_PROVINCE);
                        break;
                    case "city":
                        this.composeOtherRegion(data);
                        this.composeRegion(data, CityLevel.CITY_LEVEL_CITY);
                        break;
                }
            });
            List<AdvertiserAudience> nonBeans = beans.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(nonBeans)) {
                advertiserAudienceMapper.saveOrUpdateBatch(AdvertiserAudience.class,
                    nonBeans,
                    AdvertiserAudience.CONFLICTS);
            }
            return nonBeans;
        }
    }

    private List<AdvertiserAudience> composeAgeAndGender(List<AdvertiserAudience> beans) {
        List<AdvertiserAudience> ageGenderList = Lists.newArrayList();
        // 处理年龄数据
        Map<String, List<AdvertiserAudience>> ageCollect = beans.stream().filter(a -> "age_gender".equals(a.getType())).collect(Collectors.groupingBy(AdvertiserAudience::getAge));
        Optional.ofNullable(ageCollect).ifPresent(age -> age.forEach((key, value) -> {
            AdvertiserAudience advertiserAudience = this.reduceAudience(value.stream());
            Optional.ofNullable(advertiserAudience)
                .ifPresent(audience -> {
                    AdvertiserAudience copyAudience = new AdvertiserAudience();
                    BeanUtils.copyProperties(audience, copyAudience);
                    copyAudience.setType("age");
                    copyAudience.setTypeName(key);
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put(key, value.stream().map(AbstractMarketingData::getExt).collect(Collectors.toList()));
                    copyAudience.setExt(jsonObject);
                    ageGenderList.add(copyAudience);
                });
        }));
        // 处理性别
        Map<String, List<AdvertiserAudience>> genderCollect = beans.stream().filter(a -> "age_gender".equals(a.getType())).collect(Collectors.groupingBy(AdvertiserAudience::getGenderName));
        Optional.ofNullable(genderCollect).ifPresent(gender -> gender.forEach((key, value) -> {
            AdvertiserAudience advertiserAudience = this.reduceAudience(value.stream());
            Optional.ofNullable(advertiserAudience)
                .ifPresent(audience -> {
                    AdvertiserAudience copyAudience = new AdvertiserAudience();
                    BeanUtils.copyProperties(audience, copyAudience);
                    copyAudience.setType("gender");
                    copyAudience.setTypeName(GENDER_TYPE.get(key));
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put(GENDER_TYPE.get(key), value.stream().map(AbstractMarketingData::getExt).collect(Collectors.toList()));
                    copyAudience.setExt(jsonObject);
                    ageGenderList.add(copyAudience);
                });
        }));
        return ageGenderList;
    }

    private AdvertiserAudience reduceAudience(Stream<AdvertiserAudience> advertiserAudienceStream) {
        Optional<AdvertiserAudience> advertiserAudience = advertiserAudienceStream.reduce((x, y) -> {
            AdvertiserAudience audience = new AdvertiserAudience();
            BeanUtils.copyProperties(x, audience);
            audience.setButtonNum(this.addNum(x.getButtonNum(), y.getButtonNum()));
            audience.setClickNum(this.addNum(x.getClickNum(), y.getClickNum()));
            audience.setCommentNum(this.addNum(x.getCommentNum(), y.getCommentNum()));
            audience.setConvertNum(this.addNum(x.getConvertNum(), y.getConvertNum()));
            audience.setCostNum(this.addNum(x.getCostNum(), y.getCostNum()));
            audience.setDownloadFinish(this.addNum(x.getDownloadFinish(), y.getDownloadFinish()));
            audience.setEffectiveLeadsNum(this.addNum(x.getEffectiveLeadsNum(), y.getEffectiveLeadsNum()));
            audience.setEffectiveLeadsUserNum(this.addNum(x.getEffectiveLeadsUserNum(), y.getEffectiveLeadsUserNum()));
            audience.setFirstPayUserNum(this.addNum(x.getFirstPayUserNum(), y.getFirstPayUserNum()));
            audience.setFormAppointmentNum(this.addNum(x.getFormAppointmentNum(), y.getFormAppointmentNum()));
            audience.setFormAppointmentUserNum(this.addNum(x.getFormAppointmentUserNum(), y.getFormAppointmentUserNum()));
            audience.setOfficialAccountFollowNum(this.addNum(x.getOfficialAccountFollowNum(), y.getOfficialAccountFollowNum()));
            audience.setOrderAmount(this.addNum(x.getOrderAmount(), y.getOrderAmount()));
            audience.setOrderNum(this.addNum(x.getOrderNum(), y.getOrderAmount()));
            audience.setPayAmount(this.addNum(x.getPayAmount(), y.getPayAmount()));
            audience.setPayBehaviorNum(this.addNum(x.getPayBehaviorNum(), y.getPayBehaviorNum()));
            audience.setPraiseNum(this.addNum(x.getPraiseNum(), y.getPraiseNum()));
            audience.setRegisterNum(this.addNum(x.getRegisterNum(), y.getRegisterNum()));
            audience.setSalesLeadsNum(this.addNum(x.getSalesLeadsNum(), y.getSalesLeadsNum()));
            audience.setShareNum(this.addNum(x.getShareNum(), y.getShareNum()));
            audience.setShowNum(this.addNum(x.getShowNum(), y.getShowNum()));
            return audience;
        });
        return advertiserAudience.orElse(null);
    }

    private Long addNum(Long a, Long b) {
        if (Objects.nonNull(a) || Objects.nonNull(b)) {
            return a + b;
        }
        return null;
    }

    /**
     * 处理城市
     *
     * @param data 受众信息
     */
    private void composeRegion(AdvertiserAudience data, CityLevel level) {
        String key = CityLevel.CITY_LEVEL_PROVINCE.equals(level) ? data.getProvince() : data.getCity();
        AreaDict areaDict = JSON.parseObject(redisCache.get(CachePrefix.AREA_DICT + key), AreaDict.class);
        if (Objects.isNull(areaDict)) {
            areaDict = this.getAreaDict(key, level, data);
        }
        if (CityLevel.CITY_LEVEL_PROVINCE.equals(level)) {
            Optional.ofNullable(areaDict).ifPresent(p -> data.setTypeName(p.getName()));
        } else {
            Optional.ofNullable(areaDict).ifPresent(c -> data.setTypeName((StringUtils.isEmpty(c.getParentName()) ? "" : (c.getParentName() + ":")) + c.getName()));
        }
    }

    private AreaDict getAreaDict(String name, CityLevel level, AdvertiserAudience audiences) {
        AreaDict areaDict = areaDictService.selectOne(
            new LambdaQueryWrapper<AreaDict>()
                .likeRight(AreaDict::getName, name)
                .eq(AreaDict::getCityLevel, level.getValue())
                .last("limit 1"));
        if (Objects.isNull(areaDict) &&
            (Objects.isNull(areaDict = areaDictService.selectOne(new LambdaQueryWrapper<AreaDict>().eq(AreaDict::getName, name).last("limit 1"))))) {
            log.debug("地域信息有误，受众id为：{}，错误地域为：{}", audiences.getAdgroupId(), audiences.getProvince() + ":" + audiences.getCity());
            return null;
        } else {
            redisCache.putCache(CachePrefix.AREA_DICT + name, JSON.toJSONString(areaDict));
            return areaDict;
        }
    }

    /**
     * 处理异常地域
     *
     * @param data 受众
     */
    private void composeOtherRegion(AdvertiserAudience data) {
        if (data.getCity().endsWith("其他")) {
            String city = data.getCity();
            String replace = city.replace("其他", "未知");
            data.setCity(replace);
        }
    }

    @Override
    public List<MappedPropertyConfig> getMappedPropertyConfigs() {
        return targeting_tag_reports.getMappedPropertyConfigs();
    }
}
