package ai.yiye.agent.marketing.collect.service;

import ai.yiye.agent.domain.AdReportDataSolidification;
import ai.yiye.agent.domain.AdTrace;
import ai.yiye.agent.domain.AdvertiserAccount;
import ai.yiye.agent.domain.DataSolidification;
import ai.yiye.agent.marketing.collect.mapper.AdTraceMapper;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/10 19:26
 */
@DS("clickhouse")
@Service
public class AdTraceService extends ServiceImpl<AdTraceMapper, AdTrace> {
    @Autowired
    private DataSolidificationService dataSolidificationService;

    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    //构建clickhouse的sql
    public List<Map> selectSolidData(List<String> fields, String adGroupIdCondition, AdvertiserAccount account, LocalDateTime dayTime, Integer type) {
        String adgroupIds = String.format("adgroup_id in (%s)", adGroupIdCondition);
        //接下来构建sql
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("any(adgroup_id) as adgroup_id_count,");
        for (int i = 0; i < fields.size(); i++) {
            stringBuffer.append("ifNull(sum(case  when ");
            stringBuffer.append(fields.get(i));
            stringBuffer.append("='1' then 1 *sign end),0) as ");
            stringBuffer.append(fields.get(i) + "_count,");
        }
        String substring = stringBuffer.substring(0, stringBuffer.length() - 1);
        stringBuffer = new StringBuffer(substring);

        stringBuffer.append(" from marketing_ad_trace ");
        stringBuffer.append("where ");
        stringBuffer.append(adgroupIds);
        stringBuffer.append(" and account_id ='" + account.getAccountId() + "' ");
        stringBuffer.append(" and toDateTime(created_at)>='" + df.format(dayTime) + "'");
        if (type == 1) {
            stringBuffer.append(" and toDateTime(created_at) <='" + df.format(dayTime.plusHours(1L)) + "'");
        } else if (type == 2) {
            stringBuffer.append(" and toDateTime(created_at) <='" + df.format(dayTime.plusDays(1L)) + "'");
        }
        List<Map<String, Object>> dataSolidifications = baseMapper.selectListMap(stringBuffer.toString());
        if (CollectionUtils.isEmpty(dataSolidifications) || dataSolidifications.get(0) == null
            || dataSolidifications.get(0).get("adgroup_id_count") == null) {
            return new ArrayList<>();
        }
        List<Map> resultList = new ArrayList<>();
        for (int i = 0; i < dataSolidifications.size(); i++) {
            Map<String, Object> stringObjectMap = dataSolidifications.get(i);
            HashMap<String, Object> tempMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : stringObjectMap.entrySet()) {
                if (entry.getKey().lastIndexOf("_count") > 0) {
                    tempMap.put(entry.getKey().substring(0, entry.getKey().lastIndexOf("_count")), entry.getValue());
                }
            }
            resultList.add(tempMap);
        }
        //落地页pv与uv
        StringBuffer stringBuffer1 = new StringBuffer();
        stringBuffer1.append("pv_pid,adgroup_id ");
        stringBuffer1.append(" from marketing_ad_trace ");
        stringBuffer1.append("where ");
        stringBuffer1.append(adgroupIds);
        stringBuffer.append(" and toDateTime(created_at)>='"+df.format(dayTime)+"'");
        stringBuffer.append(" and toDateTime(created_at) <='"+df.format(dayTime.plusHours(1L))+"'");
        stringBuffer1.append(" group by ");
        stringBuffer1.append("adgroup_id,pv_pid");
        List<Map<String, Object>> adgroupIdAndPid = baseMapper.selectListMap(stringBuffer1.toString());
        //再通过adgroupId分组进行查询
        Map<String, List<Map<String, Object>>> adgroup_id1 = adgroupIdAndPid.stream().collect(Collectors.groupingBy(e -> e.get("adgroup_id").toString()));
        for (Map.Entry<String, List<Map<String, Object>>> entry : adgroup_id1.entrySet()) {
            List<String> pidList = new ArrayList<>();
            List<Map<String, Object>> value = entry.getValue();
            for (int i = 0; i < value.size(); i++) {
                if (value.get(i).get("pv_pid") != null) {
                    pidList.add(value.get(i).get("pv_pid").toString());
                }
            }
            if (pidList.size() > 0) {
                String pids = String.format(" pid in (%s)", "'" + pidList.stream().map(Object::toString).collect(Collectors.joining("','")) + "'");
                //将所有的pid总结成一个list，方便查. 这里pv_pid实际上是一个pv的list。查uid去重作为唯一自然人 就是uv
                String uv = "select count(distinct(uid)) from page_view_info where";
                uv += pids;
                uv += " and toDateTime(created_at)>='" + df.format(dayTime) + "'";
                if (type == 1) {
                    uv += " and toDateTime(created_at) <='" + df.format(dayTime.plusHours(1L)) + "'";
                } else if (type == 2) {
                    uv += " and toDateTime(created_at) <='" + df.format(dayTime.plusDays(1L)) + "'";
                }
                Long uvCount = baseMapper.selectLong(uv);
                dataSolidificationService.updateByAdgroupId(entry.getKey(), uvCount, pids.length());
            }
        }
        // 根据pidList 获取他的pv与uv
        //按广告组来select和update
        // //然后进行平均停留时长的查询
        //dayTime 仅仅是一个开始时间
        LocalDateTime endTime = dayTime.plusDays(1);
        List<Map<String, Object>> stayAvgTime = baseMapper.selectAvgStayTime(account.getAccountId(), dayTime, endTime);
        Map<String, Object> objectObjectHashMap = new HashMap<>();
        for (int i = 0; i < stayAvgTime.size(); i++) {
            Map<String, Object> stringObjectMap = stayAvgTime.get(i);
            objectObjectHashMap.put(stringObjectMap.get("adgroup_id").toString(), stringObjectMap.get("pv_stat_time"));
        }
        for (int i = 0; i < resultList.size(); i++) {
            Map<String, Object> stringObjectMap = resultList.get(i);
            String adgroup_id = stringObjectMap.get("adgroup_id").toString();
            Object o = objectObjectHashMap.get(adgroup_id);
            if (o != null) {
                stringObjectMap.put("pv_stat_time", o);
            }
        }
        resultList = qrcodeAndAddWord(resultList, account, dayTime, adgroupIds, type);

        return resultList;
    }

    //qr识别数和企微建联数
    private List<Map> qrcodeAndAddWord(List<Map> resultList, AdvertiserAccount account, LocalDateTime dayTime, String adgroupIds, Integer type) {
        //根据广告id和时间来获取他的qrcode识别数
        StringBuffer stringBuffer2 = new StringBuffer();
        stringBuffer2.append("adgroup_id, sum(identify_qr_code * sign) as identify_qr_code,sum(add_work_wechat * sign) as add_enterprise_wechat" +
            " from marketing_ad_trace where account_id=");

        stringBuffer2.append("'" + account.getAccountId() + "'");
        stringBuffer2.append("and " + adgroupIds);
        stringBuffer2.append(" and toDateTime(created_at)>='" + df.format(dayTime) + "'");
        if (type == 1) {
            stringBuffer2.append(" and toDateTime(created_at) <='" + df.format(dayTime.plusHours(1L)) + "'");
        } else if (type == 2) {
            stringBuffer2.append(" and toDateTime(created_at) <='" + df.format(dayTime.plusDays(1L)) + "'");
        }
        stringBuffer2.append(" group by ");
        stringBuffer2.append("adgroup_id");
        List<Map<String, Object>> qrCodeAndWorkChat = baseMapper.selectListMap(stringBuffer2.toString());
        Map<String, Object> qrcodeMap = new HashMap<>();
        Map<String, Object> workWeChat = new HashMap<>();

        for (int i = 0; i < qrCodeAndWorkChat.size(); i++) {
            Map<String, Object> stringObjectMap = qrCodeAndWorkChat.get(i);
            qrcodeMap.put(stringObjectMap.get("adgroup_id").toString(), stringObjectMap.get("identify_qr_code"));
            workWeChat.put(stringObjectMap.get("adgroup_id").toString(), stringObjectMap.get("add_enterprise_wechat"));
        }

        for (int i = 0; i < resultList.size(); i++) {
            Map<String, Object> stringObjectMap = resultList.get(i);
            String adgroup_id = stringObjectMap.get("adgroup_id").toString();
            Object o = qrcodeMap.get(adgroup_id);
            if (o != null) {
                stringObjectMap.put("identify_qr_code_num", o);
            }
            Object wechat = workWeChat.get(adgroup_id);
            if (wechat != null) {
                stringObjectMap.put("add_work_wechat_num", wechat);
            }
        }
        resultList = rundeCustomeField(resultList, account, dayTime, adgroupIds, type);


        return resultList;
    }

    //润德自定义字段
    private List<Map> rundeCustomeField(List<Map> resultList, AdvertiserAccount account, LocalDateTime dayTime, String adgroupIds, Integer type) {
        //润德字段：clueFill：线索填单   clueConnect：线索建联  clueEffective：线索有效  signClass：报班
        String[] strings = {"clue_fill", "clue_connect", "clue_effective", "sign_class"};
        List<String> fields = Arrays.asList(strings);

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("any(adgroup_id) as adgroup_id_count,");
        for (int i = 0; i < fields.size(); i++) {
            stringBuffer.append("ifNull(sum(case  when ");
            stringBuffer.append(fields.get(i));
            stringBuffer.append("='1' then 1 *sign end),0) as ");
            stringBuffer.append(fields.get(i) + ",");
        }
        String substring = stringBuffer.substring(0, stringBuffer.length() - 1);
        stringBuffer = new StringBuffer(substring);

        stringBuffer.append(" from marketing_ad_trace ");
        stringBuffer.append("where ");
        stringBuffer.append(adgroupIds);
        stringBuffer.append(" and account_id ='" + account.getAccountId() + "' ");
        stringBuffer.append(" and toDateTime(created_at)>='" + df.format(dayTime) + "'");
        if (type == 1) {
            stringBuffer.append(" and toDateTime(created_at) <='" + df.format(dayTime.plusHours(1L)) + "'");
        } else if (type == 2) {
            stringBuffer.append(" and toDateTime(created_at) <='" + df.format(dayTime.plusDays(1L)) + "'");
        }
        stringBuffer.append("group by adgroup_id");
        List<Map<String, Object>> dataSolidifications = baseMapper.selectListMap(stringBuffer.toString());
        if (CollectionUtils.isEmpty(dataSolidifications) || dataSolidifications.get(0) == null
            || dataSolidifications.get(0).get("adgroup_id_count") == null) {
            return new ArrayList<>();
        }
        //这个update要update到marketing_solidation表里
        Map<String, Object> clueFillMap = new HashMap<>();
        Map<String, Object> clueConnectMap = new HashMap<>();
        Map<String, Object> clueEffectiveMap = new HashMap<>();
        Map<String, Object> signClassMap = new HashMap<>();

        for (int i = 0; i < dataSolidifications.size(); i++) {
            Map<String, Object> stringObjectMap = dataSolidifications.get(i);
            clueFillMap.put(stringObjectMap.get("adgroup_id_count").toString(), stringObjectMap.get("clue_fill"));
            clueConnectMap.put(stringObjectMap.get("adgroup_id_count").toString(), stringObjectMap.get("clue_connect"));
            clueEffectiveMap.put(stringObjectMap.get("adgroup_id_count").toString(), stringObjectMap.get("clue_effective"));
            signClassMap.put(stringObjectMap.get("adgroup_id_count").toString(), stringObjectMap.get("sign_class"));
        }

        for (int i = 0; i < resultList.size(); i++) {
            Map<String, Object> stringObjectMap = resultList.get(i);
            String adgroup_id = stringObjectMap.get("adgroup_id").toString();
            Object o = clueFillMap.get(adgroup_id);
            if (o != null) {
                stringObjectMap.put("clue_fill_num", o);
            }
            Object wechat = clueConnectMap.get(adgroup_id);
            if (wechat != null) {
                stringObjectMap.put("clue_connect_num", wechat);
            }
            Object clueeffective = clueEffectiveMap.get(adgroup_id);
            if (wechat != null) {
                stringObjectMap.put("clue_effective_num", clueeffective);
            }
            Object signClass = signClassMap.get(adgroup_id);
            if (wechat != null) {
                signClassMap.put("sign_class_num", signClass);
            }
        }
        //这里已经查到了，然后进行update
        return resultList;
    }

}
