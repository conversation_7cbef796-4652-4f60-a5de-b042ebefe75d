package ai.yiye.agent.marketing.collect.mapper;

import ai.yiye.agent.domain.subtablerules.HandlerMapper;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;

import java.util.Optional;

public interface DataBaseHandlerMapper extends HandlerMapper {

    @Override
    default boolean isHandler() {
        return Optional.ofNullable(DynamicDataSourceContextHolder.peek())
            .map(dataSource -> !dataSource.contains("clickhouse")).orElse(true);
    }
}
