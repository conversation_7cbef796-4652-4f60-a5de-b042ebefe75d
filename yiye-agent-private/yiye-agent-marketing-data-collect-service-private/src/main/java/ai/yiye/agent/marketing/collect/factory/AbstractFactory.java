package ai.yiye.agent.marketing.collect.factory;


import ai.yiye.agent.marketing.collect.handler.AdvertiseExtractHandler;

public abstract class AbstractFactory {

    /**
     * 获取抽取的客户端
     *
     * @param clazz
     * @param <T>
     * @return
     */
    abstract <T extends AdvertiseExtractHandler> T getExtractHandler(Class<T> clazz);

    /**
     * 获取抽取客户端
     *
     * @param paltformId
     * @return
     */
    abstract AdvertiseExtractHandler getExtractHandler(Integer paltformId);
}
