package ai.yiye.agent.marketing.collect.mapper;

import ai.yiye.agent.domain.dto.MarketingDataReportDto;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public interface AdvertiseBaseMapper {

    Logger log = LoggerFactory.getLogger(AdvertiseBaseMapper.class);

    /**
     * 批量进行新增或者保存数据
     *
     * @param clazz
     * @param list
     * @param conflicts
     * @return
     */
    int saveOrUpdateBatchSql(@Param("entityClass") Class<?> clazz, @Param("list") List<?> list, @Param("conflicts") String[] conflicts);


    default int saveOrUpdateBatch(Class<?> clazz, List<?> list, String[] conflicts) {
        int size = 0;
        Long startTime = System.currentTimeMillis();
        try {
            size = saveOrUpdateBatchSql(clazz, list, conflicts);
        } catch (Exception e) {
            log.warn("saveOrUpdateBatch存储数据方法异常，数据类型 = {}, 数据数量 = {}", clazz.getSimpleName(), list.size(), e);
            e.printStackTrace();
        }
        log.info("saveOrUpdateBatch存储数据，数据类型 = {}, 数据数量 = {}, 时间 = {}", clazz.getSimpleName(), list.size(), (System.currentTimeMillis() - startTime) + "ms");
        return size;
    }

    /**
     * 1. 批量进行新增或者保存数据
     * 2. 同步公域
     */
    int saveOrUpdateBatchWithSyncSql(@Param("entityClass") Class<?> clazz, @Param("list") List<?> list, @Param("conflicts") String[] conflicts);

    default int saveOrUpdateBatchWithSync(Class<?> clazz, List<?> list, String[] conflicts) {
        int size = 0;
        Long startTime = System.currentTimeMillis();
        try {
            size = saveOrUpdateBatchWithSyncSql(clazz, list, conflicts);
        } catch (Exception e) {
            log.warn("saveOrUpdateBatchWithSync存储数据方法异常，数据类型 = {}, 数据数量 = {}", clazz.getSimpleName(), list.size(), e);
            e.printStackTrace();
        }
        log.info("saveOrUpdateBatchWithSync存储数据，数据类型 = {}, 数据数量 = {}, 时间 = {}", clazz.getSimpleName(), list.size(), (System.currentTimeMillis() - startTime) + "ms");
        return size;
    }

    /**
     * 批量保存数据
     *
     * @param clazz
     * @param list
     * @return
     */
    int saveBatchSql(@Param("entityClass") Class<?> clazz, @Param("list") List<?> list);

    default int saveBatch(Class<?> clazz, List<?> list) {
        int size = 0;
        Long startTime = System.currentTimeMillis();
        try {
            size = saveBatchSql(clazz, list);
        } catch (Exception e) {
            log.warn("saveBatch存储数据方法异常，数据类型 = {}, 数据数量 = {}", clazz.getSimpleName(), list.size(), e);
            e.printStackTrace();
        }
        log.info("saveBatch存储数据，数据类型 = {}, 数据数量 = {}, 时间 = {}", clazz.getSimpleName(), list.size(), (System.currentTimeMillis() - startTime) + "ms");
        return size;
    }

    /**
     * 更新头条creative的campaign字段
     *
     * @return
     */
    int updateOceanCampaignId();

    int deleteByAccountId(@Param("entityClass") Class<?> clazz, @Param("accountId") String accountId, @Param("platformId") Integer platformId);

    void deleteByAdvertiserAndDay(MarketingDataReportDto marketingDataReportDto);

    /**
     * 统计投放账户的拉取信息
     * @return
     */
    List<Map> statisticsAccountInfo();
}
