package ai.yiye.agent.marketing.collect.stream;

import ai.yiye.agent.autoconfigure.rabbitmq.config.Constants;
import ai.yiye.agent.domain.dto.*;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * Created by  lxr.
 * User: luxurong
 * Date: 2020/6/24
 */


@Service
public class AccountMessageService {
    private static final String audienceTopicName = "yiye-agent-private.marketing-audience-collect.exchange";

    private static final String customTopicName = "yiye-agent-private.marketing-custom-collect.exchange";

    private static final String topicName = "yiye-agent-private.marketing-data-collect.exchange";

    private static final String reportTopicName = "yiye-agent-private.marketing-report-collect.exchange";

    private static final String campaignAdReportTopicName = "yiye-agent-private.marketing-campaign-ad-report-collect.exchange";

    private static final String materialDailyReportTopicName = "yiye-agent-private.marketing-material-daily-report-collect.exchange";

    private static final String tencentOperateLogTopicName = "yiye-agent-private.marketing-tencent-operate-log-collect.exchange";

    private static final String INIT_DATA_BASE_EXCHANGE = "yiye-agent-private.marketing-data-collect-base-data-init.exchange";
    private static final String INIT_DATA_REPORT_EXCHANGE = "yiye-agent-private.marketing-data-collect-report-data-init.exchange";


    @Resource
    private AmqpTemplate amqpTemplate;

    @Autowired
    private StreamBridge streamBridge;

    public void sendAccounts(AccountMessageDto message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(topicName, message));

    }

    public void sendInitDataAccounts(AccountMessageDto message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(INIT_DATA_BASE_EXCHANGE, message));

    }

    public void sendAudienceAccounts(AccountMessageDto message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(audienceTopicName, message));

    }

    /**
     * 发送报表数据
     *
     * @param message
     */
    public void sendReportAccounts(AccountMessageDto message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(reportTopicName, message));
    }

    /**
     * 发送计划和广告组报表数据
     *
     * @param message
     */
    public void sendCampaignAdReportAccounts(AccountMessageDto message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(campaignAdReportTopicName, message));
    }

    /**
     * 初始化数据的时候，发送消息到初始化报表数据队列
     *
     * @param message
     */
    public void sendCampaignAdReportDataInitAccounts(AccountMessageDto message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(INIT_DATA_REPORT_EXCHANGE, message));
    }


    public void sendCustomCollect(AccountMessageDto accountMessageDto) {

        Optional.ofNullable(accountMessageDto)
            .ifPresent(ms ->
                streamBridge.send(Constants.AUDIENCES_CUSTOM_PRIVATE_EXCHANGE, accountMessageDto)
            );

    }

    public void sendMaterialDailyReport(AccountMessageDto accountMessageDto) {
        Optional.ofNullable(accountMessageDto).ifPresent(account -> {
            streamBridge.send(materialDailyReportTopicName, account);
        });
    }

    public void handlerFlowPackage(AccountMessageDto accountMessageDto) {
        Optional.ofNullable(accountMessageDto)
            .ifPresent(ms ->
                streamBridge.send(Constants.FLOW_PACKAGE_PRIVATE_EXCHANGE, accountMessageDto)
            );
    }

    public void sendTencentLog(AccountMessageDto toDto) {
        Optional.ofNullable(toDto)
            .ifPresent(ms ->
                streamBridge.send(tencentOperateLogTopicName, toDto)
            );
    }

    public void handlerCategory(AccountMessageDto message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(Constants.TARGETING_CATEGORY_PUBLIC_EXCHANGE, message));
    }

    /**
     * 广告数据补偿,推送消息，提醒项目列表数据重新固化
     *
     * @param message 消息
     */
    public void sendMessageForAdDataCompensation(AccountAdDataCompensationDTO message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(Constants.AD_DATA_REPORT_COMPENSATION_EXCHANGE, message));
    }

    /**
     * 更新广告基础数据
     */
    public void sendMarketingBaseData(MarketingBaseDataDTO message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(Constants.MARKETING_BASE_DATA_EXCHANGE, message));
    }

    /**
     * 补偿广告报表数据
     */
    public void sendMarketingAdReportCompensate(MarketingAdReportCompensateDTO message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(Constants.MARKETING_AD_REPORT_COMPENSATE_EXCHANGE, message));
    }

    /**
     * 修正广告报表数据
     */
    public void sendMarketingAdReport(MarketingAdReportDTO message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(Constants.MARKETING_AD_REPORT_EXCHANGE, message));
    }

    /**
     * 拉取账户报表数据
     */
    public void sendMarketingAccountReport(MarketingAccountReportDTO message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(Constants.MARKETING_ACCOUNT_REPORT_EXCHANGE, message));
    }

    /**
     * 修正账户报表数据
     *
     * @param message
     */
    public void sendMarketingAccountReportCorrect(MarketingAccountReportCorrectDTO message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(Constants.MARKETING_ACCOUNT_REPORT_CORRECT_EXCHANGE, message));
    }

    /**
     * 补偿账户报表数据
     *
     * @param message
     */
    public void sendMarketingAccountReportCompensate(MarketingAccountReportCompensateDTO message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(Constants.MARKETING_ACCOUNT_REPORT_COMPENSATE_EXCHANGE, message));
    }

    /**
     * 账户报表数据推送
     *
     * @param message
     */
    public void marketingAccountReportDataPush(MarketingAccountReportDataPushDTO message) {
        Optional.ofNullable(message)
            .ifPresent(ms -> streamBridge.send(Constants.MARKETING_ACCOUNT_REPORT_DATA_PUSH_EXCHANGE, message));
    }

}
