package ai.yiye.agent.marketing.collect.config;

import com.google.common.collect.Maps;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
public class RabbitMqConfig {

    /**
     * 绑定了死信交换机的无消费者的延迟交换机
     */
    public static final String DELAY_EXCHANGE_NAME = "yiye-agent-private.data-collect-retry-delay.exchange";
    public static final String ROUTING_KEY = "#";
    public static final String DELAY_EXCHANGE_TTL_QUEUE = "yiye-agent-private.data-collect-retry-delay.queue";

    public static final String PROCESS_EXCHANGE_NAME = "yiye-agent-private.data-collect-retry-process.exchange";
    public static final String PROCESS_QUEUE_NAME = "yiye-agent-private.data-collect-retry-process.queue";
    public static final int DELAY_MILLS = 30_000;
    public static final int MAX_RETRY_TIMES = 3;

    @Bean
    public RabbitTemplate rabbitTemplate(CachingConnectionFactory rabbitConnectionFactory){
        RabbitTemplate template = new RabbitTemplate(rabbitConnectionFactory);
        template.setMessageConverter(new Jackson2JsonMessageConverter());
        return template;
    }

    @Bean(value = "containerFactory")
    public RabbitListenerContainerFactory<?> containerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        // 设置确认模式手工确认
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        factory.setConcurrentConsumers(2);
        factory.setPrefetchCount(1);
        return factory;
    }

    /**
     * 创建延迟交换机
     * @return
     */
    @Bean
    public TopicExchange delayProducerExchange(){
        return new TopicExchange(DELAY_EXCHANGE_NAME,true,false);
    }

    /**
     * 创建延迟队列，并配置死信交换机及其路由键
     * @return
     */
    @Bean
    public Queue delayQueue(){
        Map<String, Object> argsMap= Maps.newHashMap();
        argsMap.put("x-dead-letter-exchange",PROCESS_EXCHANGE_NAME);
        argsMap.put("x-dead-letter-routing-key",ROUTING_KEY);
        // 此队列无消费者,并配置一个死信交换机
        // 当消息过期之后会发送至死信交换机
        return new Queue(DELAY_EXCHANGE_TTL_QUEUE,true,false,false,argsMap);
    }
    /**
     * 绑定  延迟交换机  ----路由键----> 延迟队列
     * @return
     */
    @Bean
    public Binding delayExchangeQueueBinding(){
        return BindingBuilder.bind(delayQueue()).to(delayProducerExchange()).with(ROUTING_KEY);
    }

    /**
     * 创建死信交换机
     * @return
     */
    @Bean
    public TopicExchange deadLetterExchange(){
        return new TopicExchange(PROCESS_EXCHANGE_NAME,true,false);
    }

    /**
     * 创建真正处理消息的队列
     * @return
     */
    @Bean
    public Queue processQueue(){
        return new Queue(PROCESS_QUEUE_NAME,true);
    }

    /**
     * 绑定   死信交换机 ----死信路由----->  真正处理消息的队列
     * @return
     */
    @Bean
    public Binding successKillDeadBinding(){
        return BindingBuilder.bind(processQueue()).to(deadLetterExchange()).with(ROUTING_KEY);
    }


}
