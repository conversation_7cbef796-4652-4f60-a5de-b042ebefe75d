package ai.yiye.agent.marketing.collect.mapper;

import ai.yiye.agent.domain.marketing.data.AdvertiserGroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdvertiserGroupMapper extends BaseMapper<AdvertiserGroup> {

    /**
     * 根据广告组ID集合查询广告列表
     * @param adgroupIds
     * @param platformId
     * @return
     */
    List<AdvertiserGroup> selectListByAdGroups(@Param("adgroupIds") String adgroupIds, @Param("platformId") Integer platformId);

    List<Long> selectAllAdGroupIds(@Param("accountId") String accountId,
                                   @Param("platformId") Integer platformId);

    List<AdvertiserGroup> selectAdgroups(@Param("adgroupId") Long adgroupId, @Param("platformId") int platformId);

    List<AdvertiserGroup> selectAdgroupsByAccountPlatformAdgroupId(@Param("accountId") String accountId,@Param("adgroupId") Long adgroupId, @Param("platformId") int platformId);


}
