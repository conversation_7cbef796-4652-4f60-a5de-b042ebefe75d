package ai.yiye.agent.marketing.collect.remote;

import ai.yiye.agent.autoconfigure.openfeign.OpenFeignAutoConfigurationBoss;
import ai.yiye.agent.domain.boss.BossAdvertiserAccountGroupDayReport;
import ai.yiye.agent.domain.dto.AgentDateFund;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/18 14:48
 */
@FeignClient(value = "boss-backend",  configuration = OpenFeignAutoConfigurationBoss.class)
public interface BossBackendRemote {
    /**
     * 获取所有pv计费方式的agentId
     *
     * @return
     */
    @GetMapping("boss-backend/customer/all/pv-combo/agent")
    List<String> getAllPvComboAgent();

    @PostMapping("/boss-backend/customer/fund-cost/{agentId}/{advertiserAccountGroupId}")
    void updatePmpDayReport(@PathVariable("agentId") String agentId,@PathVariable("advertiserAccountGroupId") Long advertiserAccountGroupId, @RequestBody BossAdvertiserAccountGroupDayReport accountGroupDayReport);

    @PostMapping("boss-backend/customer/fund-cost-agent/{agentId}")
    void agentFundRecord(@PathVariable ("agentId")String agentId,@RequestBody AgentDateFund agentDateFund);

}
