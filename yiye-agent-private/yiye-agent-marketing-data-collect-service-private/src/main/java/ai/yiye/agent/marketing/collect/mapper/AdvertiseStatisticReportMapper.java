package ai.yiye.agent.marketing.collect.mapper;

import ai.yiye.agent.domain.AdvertiseStatisticReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface AdvertiseStatisticReportMapper extends BaseMapper<AdvertiseStatisticReport> {

    List<AdvertiseStatisticReport> generateStatisticReport(@Param("stateTime") LocalDate stateTime, @Param("endTime") LocalDate endTime, @Param("accountId") String accountId);

}
