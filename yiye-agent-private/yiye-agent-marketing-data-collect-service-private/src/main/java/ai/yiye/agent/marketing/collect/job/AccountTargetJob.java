package ai.yiye.agent.marketing.collect.job;

import ai.yiye.agent.autoconfigure.mybatis.multidatasource.service.AgentConfService;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.marketing.collect.service.HumanEffiectiveAnalysisService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
@Slf4j
public class AccountTargetJob extends PageAggregation {

    @Autowired
    private HumanEffiectiveAnalysisService humanEffiectiveAnalysisService;
    @Autowired
    private AgentConfService agentConfService;

    /**
     * 跟新历史的账户目标
     *
     * @param param
     */
    @XxlJob(value = "updateAccountTargetExecutor")
    public ReturnT<String> updateHistoyAccountTarget(String param) {
        //saas
        List<AgentConf> agentDBConfigs = agentConfService.getEffectiveAgent();
            agentDBConfigs.stream().forEach(e -> {
                try {
                    TenantContextHolder.set(e.getAgentId());
                    // 创建昨天的历史数据
                    humanEffiectiveAnalysisService.updatePastAccountTarget(false, LocalDateTime.now().minusDays(1), null);
                    // 更新一天前的数据
                    humanEffiectiveAnalysisService.updatePastAccountTarget(true, LocalDateTime.now().minusDays(2), null);
                    // 更新两天前的数据
                    humanEffiectiveAnalysisService.updatePastAccountTarget(true, LocalDateTime.now().minusDays(3), null);
                    // 创建昨天的投放账户报表数据
                    humanEffiectiveAnalysisService.updateDailyAccount(null, LocalDateTime.now().minusDays(1), null);
                    // 更新一天前的数据
                    humanEffiectiveAnalysisService.updateDailyAccount(null, LocalDateTime.now().minusDays(2), null);
                    // 更新两天前的数据
                    humanEffiectiveAnalysisService.updateDailyAccount(null, LocalDateTime.now().minusDays(3), null);
                } catch (Exception exception) {
                    exception.printStackTrace();
                    log.error("执行updateAccountTargetExecutor任务出错，agentId为{},message:{}", e.getAgentId(), e.toString());
                } finally {
                    TenantContextHolder.clearContext();
                }
            });

        return ReturnT.SUCCESS;
    }
}
