package ai.yiye.agent.marketing.collect.mapper;

import ai.yiye.agent.domain.marketing.data.AdvertiserOceanAdReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OceanAdReportMapper extends BaseMapper<AdvertiserOceanAdReport> {
    void batchSaveOrUpdate(@Param("advertiserOceanAdReports") List<AdvertiserOceanAdReport> advertiserOceanAdReports, @Param("tableName") String tableName);

    void createTable(@Param("tableName") String tableName);

    void createSubTable(@Param("time") String time);
}
