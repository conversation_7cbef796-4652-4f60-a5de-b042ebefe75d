package ai.yiye.agent.marketing.collect.mapper;

import ai.yiye.agent.domain.dto.MarketingDataReportDto;
import ai.yiye.agent.domain.marketing.data.AdvertiserHourlyReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

public interface AdvertiserHourlyReportMapper extends BaseMapper<AdvertiserHourlyReport> {

    /**
     * 更新小时报表的创意ID
     *
     * @return
     */
    int updateHourlyReportCreativeIds();

    void deleteByAdvertiserAndDay(MarketingDataReportDto marketingDataReportDto);
}
