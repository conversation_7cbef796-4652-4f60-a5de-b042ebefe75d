package ai.yiye.agent.marketing.collect.job;

import ai.yiye.agent.autoconfigure.mybatis.multidatasource.service.AgentConfService;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.domain.marketing.data.AdvertiserMaterial;
import ai.yiye.agent.marketing.collect.service.MaterialService;
import ai.yiye.agent.marketing.collect.stream.MaterialSender;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class MaterialJob {

    private static final int DEFAULT_PAGE_SIZE = 50;

    @Autowired
    private MaterialService materialService;

    @Autowired
    private MaterialSender materialSender;
    @Autowired
    private AgentConfService agentConfService;


    @XxlJob("handlerOldMaterial")
    public ReturnT<String> handlerOldMaterial(String param) throws Exception {
//saas
        List<AgentConf> agentDBConfigs = agentConfService.list(new LambdaQueryWrapper<>());
            agentDBConfigs.stream().forEach(e1 -> {
                TenantContextHolder.set(e1.getAgentId());
                List<AdvertiserMaterial> materials;
                int offset = 0;
                while (true) {
                    materials = materialService.listNoDataOffset(offset);
                    if (Objects.isNull(materials) || CollectionUtils.isEmpty(materials)) {
                        break;
                    }
                    offset++;
                    materials.forEach(material -> {
                        material.setAgentId(e1.getAgentId());
                        materialSender.sendMaterialAnalyse(material);
                    });
                }
                TenantContextHolder.clearContext();
            });

        //asp
        log.error("===> 开始进行更新素材数据完成!");
        return ReturnT.SUCCESS;
    }
}
