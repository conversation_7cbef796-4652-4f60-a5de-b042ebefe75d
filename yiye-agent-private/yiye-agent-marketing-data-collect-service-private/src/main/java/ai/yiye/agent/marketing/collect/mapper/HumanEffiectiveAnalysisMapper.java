package ai.yiye.agent.marketing.collect.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * 人效分析数据接口
 */
@Repository
public interface HumanEffiectiveAnalysisMapper {

    /**
     * 更新今天的投放账户目标
     *
     * @param accountId
     * @return
     */
    int updateTodayAccountTarget(@Param("history") Boolean history, @Param("dayTime") LocalDateTime localDate, @Param("accountId") String accountId);

    /**
     * 更新历史的投放账户的目标数据
     *
     * @param history   是否从历史数据处进行更新
     * @param localDate 更新时间
     * @return
     */
    int updatePastAccountTarget(@Param("history") Boolean history, @Param("dayTime") LocalDateTime localDate, @Param("accountId") String accountId);

    /**
     * 更新投放账户日报表数据
     *
     * @param localDate
     * @return
     */
    int updateDailyAccount(@Param("history") Boolean history, @Param("dayTime") LocalDateTime localDate, @Param("accountId") String accountId);
}
