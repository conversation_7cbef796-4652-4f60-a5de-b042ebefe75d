package ai.yiye.agent.marketing.collect.mapper;

import ai.yiye.agent.domain.AdvertiserAccount;
import ai.yiye.agent.domain.AdvertiserAccountDayReport;
import ai.yiye.agent.domain.dto.AdvertiserAccountDayReportDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 报表数据聚合(按广告主id与天进行统计)
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/10/23 9:30
 */
@Repository
public interface AdvertiserAccountDayReportMapper  extends BaseMapper<AdvertiserAccountDayReport> {

    /**
     * 条件查询报表后按广告主id与天进行聚合
     * @param advertiserAccountDayReportDto 条件
     * @return 聚合后的列表
     */
    List<AdvertiserAccountDayReport> getAdvertiserDayReport(AdvertiserAccountDayReportDto advertiserAccountDayReportDto);

    /**
     * 批量入库（有则更新，无则新增）
     * @param list 数据
     */
    void saveOrUpdateBatch(@Param("list") List<AdvertiserAccountDayReport> list);

    /**
     * 条件查询报表后按广告主id与天进行聚合
     * @param advertiserAccountDayReportDto
     */
    void saveOrUpdateAdvertiserDayReport(AdvertiserAccountDayReportDto advertiserAccountDayReportDto);

    void deleteAccountReportData(@Param("advertiserAccount") AdvertiserAccount advertiserAccount, @Param("dataList") List<String> dataList);

    void deleteAccountReportDataByDay(@Param("advertiserAccount")AdvertiserAccount advertiserAccount, @Param("dayTime") LocalDateTime dayTime);
}
