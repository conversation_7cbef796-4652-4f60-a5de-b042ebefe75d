<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ai.yiye.agent.marketing.collect.mapper.LandingPagePmpParamsConfigMapper">

    <select id="getDataPushConfig" resultType="ai.yiye.agent.domain.LandingPagePmpParamsConfig">
        SELECT A.*, b.NAME advertiser_account_group_name
        FROM landing_page_pmp_params_config A
	    LEFT JOIN marketing_advertiser_account_group b ON A.advertiser_account_group_id = b.ID
        WHERE
	    A.TYPE = 4
    </select>

</mapper>
