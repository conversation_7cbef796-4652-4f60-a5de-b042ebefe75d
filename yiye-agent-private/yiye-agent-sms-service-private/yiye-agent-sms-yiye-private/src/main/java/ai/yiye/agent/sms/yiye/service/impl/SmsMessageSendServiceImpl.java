package ai.yiye.agent.sms.yiye.service.impl;

import ai.yiye.agent.common.sms.Result;
import ai.yiye.agent.common.sms.ResultVO;
import ai.yiye.agent.common.util.HttpClientUtil;
import ai.yiye.agent.common.util.Md5Util;
import ai.yiye.agent.domain.dto.SmsSendReq;
import ai.yiye.agent.domain.message.SmsSendLog;
import ai.yiye.agent.sms.yiye.dto.StatusDtoBody;
import ai.yiye.agent.sms.yiye.dto.WlwxReportDto;
import ai.yiye.agent.sms.yiye.service.SmsMessageSendService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR> Wang
 * @version 1.0
 * @date 2020/12/4 9:51
 */
@Service
@Slf4j
public class SmsMessageSendServiceImpl implements SmsMessageSendService {
    @Value("${sms.send-url}")
    private String url;
    @Value("${sms.account}")
    private String account;
    @Value("${sms.password}")
    private String password ;
    @Value("${sms.token-url}")
    private String tokenUrl ;
    @Value("${sms.response-url}")
    private String responseUrl;


    @Override
    public SmsSendLog send(SmsSendReq message) throws NoSuchAlgorithmException, IOException, KeyManagementException {
        String phone = message.getMobile();
        String content = message.getContent();
        final String agentIdTableId = message.getAgentIdTableId();
        //拼装无线互联所需要的接口参数
        String result = this.httpSend(url, HttpMethod.POST.toString(), this.composeSmsBody(content, phone, agentIdTableId));
        //返回结果，然后对短信发回的内容进行优化
        ResultVO resultVO = JSONObject.toJavaObject(JSON.parseObject(result), ResultVO.class);
        //{"respCode":"0","respMsg":"提交成功！","result":[{"chargeNum":1,"code":"0","mobile":"***********","msg":"提交成功.","msgid":"59105112081639762365"}],"status":"success","totalChargeNum":1}
        SmsSendLog smsSendLog = new SmsSendLog();
        smsSendLog.setFlagId(message.getFlagId());
        smsSendLog.setMobile(phone);
        smsSendLog.setContent(content);
        if (resultVO != null && resultVO.getResult() != null && resultVO.getResult().size() > 0) {
            Result rt = resultVO.getResult().get(0);
            if (0==rt.getCode()) {
                smsSendLog.setMsgId(rt.getMsgid());
                smsSendLog.setSendStatus("success");
                smsSendLog.setCode(0);
                log.info("短信发送成功,号码:{},短信发送返回结果：{}", message.getMobile(), resultVO.getResult());
            }else {
                smsSendLog.setMsg(rt.getMsg());
                smsSendLog.setSendStatus("FAIL");
                smsSendLog.setCode(1);
                log.info("短信发送失败,号码:{},返回结果{}", message.getMobile(), rt.getMsg());
            }
        } else {
            smsSendLog.setMsg(resultVO.getRespMsg());
            smsSendLog.setSendStatus("FAIL");
            smsSendLog.setCode(1);
            log.info("短信发送失败,号码:{},请稍后再试{}",  message.getMobile(), resultVO.getRespMsg());
        }
        return smsSendLog;
    }

    @Override
    public StatusDtoBody getReport(String mobile, String messageId) throws Exception {
        String token = this.getToken();
        JSONObject object = JSONObject.parseObject(token);
        String sign = object.get("token") + password;
        sign = Md5Util.getMd5(sign.getBytes(StandardCharsets.UTF_8));

        Map<String, Object> jsonBody = Maps.newConcurrentMap();
        jsonBody.put("cust_code", account);
        jsonBody.put("token_id", object.get("token_id"));
        jsonBody.put("sign", sign);
        String resultBuffer = this.getResponseInfos(responseUrl, JSON.toJSONString(jsonBody));
        log.info("反馈信息为--->resultBuffer:{}", resultBuffer);
        StatusDtoBody statusDtoBody = getReportByMobile(resultBuffer, messageId, mobile);


        return statusDtoBody;
    }

    private StatusDtoBody getReportByMobile(String resultBuffer, String messageId, String mobile) {
        JSONArray array = JSONArray.parseArray(resultBuffer);

        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject o = array.getJSONObject(i);
                //根据手机号找到对应的消息
                if (o.getString("msgid").equals(messageId)) {
                    StatusDtoBody statusDtoBody = new StatusDtoBody();
                    statusDtoBody.setMobile(o.getString("mobile"));
                    statusDtoBody.setMsgid(o.getString("msgid"));
                    statusDtoBody.setRecvTime(o.getString("recv_time"));
                    statusDtoBody.setMessageStatus(o.getString("report_status"));
                    statusDtoBody.setReport(o.getString("report"));
                    statusDtoBody.setUid(o.getString("uid"));
                    return statusDtoBody;
                }
            }
        }
        return null;
    }

    @Override
    public List<StatusDtoBody> getReports(String mobile, String messageId) throws Exception {
        String token = this.getToken();
        JSONObject object = JSONObject.parseObject(token);
        String sign = object.get("token") + password;
        sign = Md5Util.getMd5(sign.getBytes(StandardCharsets.UTF_8));

        Map<String, Object> jsonBody = Maps.newConcurrentMap();
        jsonBody.put("cust_code", account);
        jsonBody.put("token_id", object.get("token_id"));
        jsonBody.put("sign", sign);
        String resultBuffer = this.getResponseInfos(responseUrl, JSON.toJSONString(jsonBody));
        log.info("反馈信息为--->resultBuffer:{}", resultBuffer);
        List<StatusDtoBody> statusDtoBodys = getStatusDtoBodys(resultBuffer);
        return statusDtoBodys;
    }

    private List<StatusDtoBody> getStatusDtoBodys(String resultBuffer) {
        JSONArray array = JSONArray.parseArray(resultBuffer);
        ArrayList<StatusDtoBody> list  = new ArrayList<>();
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject o = array.getJSONObject(i);
                //根据手机号找到对应的消息
                StatusDtoBody statusDtoBody = new StatusDtoBody();
                statusDtoBody.setMobile(o.getString("mobile"));
                statusDtoBody.setMsgid(o.getString("msgid"));
                statusDtoBody.setRecvTime(o.getString("recv_time"));
                statusDtoBody.setMessageStatus(o.getString("report_status"));
                statusDtoBody.setReport(o.getString("report"));
                statusDtoBody.setUid(o.getString("uid"));
                list.add(statusDtoBody);
            }
        }
        return list;
    }

    /**
     *
     * @param content
     * @param phone
     * @param agentIdTableId
     * @return
     */
    private String composeSmsBody(String content, String phone, final String agentIdTableId) {
        content = content.replaceAll("\r|\n|\t", "");
        // 数字签名，签名内容根据 “短信内容+客户密码”进行MD5编码后获得
        String sign = content + password;
        sign = Md5Util.getMd5(sign.getBytes(StandardCharsets.UTF_8));
        //长号码，选填
        String sp_code = "";
        // 是否需要状态报告
        String need_report = "yes";
        // 业务标识，选填，由客户自行填写不超过20位的数字
        // 此处传递agentId在数据库表中agent_db_conf的主键id, 因为agentId可能存在字母
        // TenantDataSourceNameUtil#getIdByAgentId 可以将主键id转换为 agentId
//        String uid = "";
        Map<String, String> smsBody = Maps.newConcurrentMap();
        smsBody.put("cust_code", account);
        smsBody.put("sp_code", sp_code);
        smsBody.put("content", content);
        smsBody.put("destMobiles", phone);
        smsBody.put("uid", agentIdTableId);
        smsBody.put("need_report", need_report);
        smsBody.put("sign", sign);
        return JSON.toJSONString(smsBody);
    }

    /**
     * 短信发送
     *
     * @param url     地址
     * @param method  请求类型
     * @param content 内容
     * @return
     * @throws IOException
     * @throws KeyManagementException
     * @throws NoSuchAlgorithmException
     */
    public String httpSend(String url, String method, String content) throws IOException, KeyManagementException, NoSuchAlgorithmException {
        return HttpClientUtil.httpSend(url, method, content);
    }

    /**
     * 请求短信回馈信息接口
     *
     * @param _url
     * @param content
     * @return
     * @throws Exception
     */
    private String getResponseInfos(String _url, String content) throws Exception {
        return HttpClientUtil.httpSend(_url, HttpMethod.GET.toString(), content);
    }

    /**
     * 获取token
     *
     * @return
     * @throws Exception
     */
    private String getToken() throws Exception {
        log.info("获取TOKEN");
        // 短信内容，必填
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cust_code", account);
        return this.getResponseInfos(tokenUrl, jsonObject.toJSONString());
    }

}
