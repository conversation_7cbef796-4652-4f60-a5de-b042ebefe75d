package ai.yiye.agent.sms.yiye.controller;

import ai.yiye.agent.domain.dto.ResultDto;
import ai.yiye.agent.domain.dto.SmsSendReq;
import ai.yiye.agent.domain.enumerations.ResultCode;
import ai.yiye.agent.domain.message.SmsSendLog;
import ai.yiye.agent.sms.yiye.service.SmsMessageSendService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/3 14:08
 */
@Slf4j
@RestController
@RequestMapping(value = "/sms")
public class YiYeSmsSendController {
    @Autowired
    private SmsMessageSendService volceSmsMessageSendServiceImpl;

    /**
     * @return
     * @param普通短信发送
     * <AUTHOR>
     * @date 2020/12/3 14:26
     */
    @PostMapping()
    public ResultDto<SmsSendLog> send(@RequestBody SmsSendReq message) {
        try {
            //短信发送
            SmsSendLog smsSendLog = volceSmsMessageSendServiceImpl.send(message);
            log.info("短信验证码发送成功，返回参数 smsSendLog ====== >>：{}", JSONObject.toJSONString(smsSendLog));
            smsSendLog.setContent("【一叶智能】短信验证码已发送至您手机, 15分钟内输入有效, 请妥善保管。");
            return ResultDto.result(ResultCode.SUCCESS.getCode(), smsSendLog,ResultCode.SUCCESS.getMsg());
        }catch (Exception e){
            log.error(e.getMessage(),e);
            return ResultDto.fail(ResultCode.SYSTEM_BUSY.getCode(),ResultCode.SYSTEM_BUSY.getMsg());
        }
    }


}
