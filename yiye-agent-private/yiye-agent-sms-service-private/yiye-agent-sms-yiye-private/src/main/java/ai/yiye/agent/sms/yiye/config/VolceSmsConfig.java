package ai.yiye.agent.sms.yiye.config;

import com.volcengine.service.sms.SmsService;
import com.volcengine.service.sms.SmsServiceInfoConfig;
import com.volcengine.service.sms.impl.SmsServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2024/12/28 10:18
 */
@Configuration
public class VolceSmsConfig {
	@Autowired
	private VolceSmsConfiguration volceSmsConfiguration;
	@Bean
	public SmsService smsService() {
		return SmsServiceImpl.getInstance(new SmsServiceInfoConfig(volceSmsConfiguration.getAccessKeyID(), volceSmsConfiguration.getSecretAccessKey()));
	}
}
