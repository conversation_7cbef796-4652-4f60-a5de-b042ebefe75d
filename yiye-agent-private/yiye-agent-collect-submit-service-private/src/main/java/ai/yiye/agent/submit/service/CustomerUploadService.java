package ai.yiye.agent.submit.service;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.common.util.CommonUtil;
import ai.yiye.agent.common.util.DateTimeUtil;
import ai.yiye.agent.common.util.HumpUtils;
import ai.yiye.agent.domain.*;
import ai.yiye.agent.domain.bo.CallBackInvalidMakeTagDTO;
import ai.yiye.agent.domain.bo.CustomerBO;
import ai.yiye.agent.domain.dto.*;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.ext.SubmitDataFormExt;
import ai.yiye.agent.domain.ext.SubmitDataOrderExt;
import ai.yiye.agent.domain.ext.SubmitDataOtherExt;
import ai.yiye.agent.domain.pageview.PageViewInfo;
import ai.yiye.agent.domain.util.PercentageExecuteUtils;
import ai.yiye.agent.domain.utils.IPUtil;
import ai.yiye.agent.domain.utils.UrlUtils;
import ai.yiye.agent.submit.dto.CustomerUploadAssociationSaveDto;
import ai.yiye.agent.submit.dto.UploadDataDTO;
import ai.yiye.agent.submit.redis.*;
import ai.yiye.agent.submit.sender.PageViewInfoSender;
import ai.yiye.agent.submit.sender.SubmitDataSender;
import ai.yiye.agent.submit.vo.UploadResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomerUploadService implements ApplicationContextAware {

    @Autowired
    private ai.yiye.agent.submit.config.AgentConf agentConf;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private SubmitDataService submitDataService;
    @Autowired
    private CustomerUploadRecordService customerUploadRecordService;
    @Autowired
    private UploadConfigurationTypesService uploadConfigurationTypesService;
    @Autowired
    private AdvertiserAccountService advertiserAccountService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private PageViewInfoService pageViewInfoService;
    @Resource
    private UploadConfigurationRedis uploadConfigurationRedis;
    @Resource
    private UploadConfigurationTypesRedis uploadConfigurationTypesRedis;
    @Resource
    private BossRedis bossRedis;
    @Resource
    private AdvertiserCreativeRedis advertiserCreativeRedis;
    @Autowired
    private SubmitDataSender submitDataSender;
    @Autowired
    private PageViewInfoSender pageViewInfoSender;
    @Resource
    private RedisTemplate<String, Object> objectRedisTemplate;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private AdvertiseRedis advertiseRedis;
    @Resource
    private PageViewInfoRedis pageViewInfoRedis;
    @Autowired
    private TaobaoDspRedis taobaoDspRedis;


    private final static List<UploadEventType> TAOBAO_DSP_EVENTS = Arrays.asList(
        UploadEventType.TAOBAO_PAGE_VIEW,
        UploadEventType.TAOBAO_ORDER_PAYMENT,
        UploadEventType.TAOBAO_PRODUCT_CLICK,
        UploadEventType.TAOBAO_FIRST_VISIT_VENUE,
        UploadEventType.TAOBAO_RED_ENVELOPE_RECEIVE,
        UploadEventType.TAOBAO_CANCEL_ORDER_PAYMENT,
        UploadEventType.TAOBAO_HIGH_COMMISSION_ORDER_PAYMENT
    );
    //巨量广告创意id
    private static final String OCEAN_CREATIVEID = "creativeid";

    public static final TimeUnit TIME_UNIT = TimeUnit.MINUTES;


    public UploadDataDTO getUploadDataDTO(String pid, Long submitDataId, UploadDto uploadDto) {
        final UploadEventType uploadEventType = uploadDto.getUploadEventType();
        final Long channelId = uploadDto.getChannelId();
        final Long landingPageId = uploadDto.getLandingPageId();
        /*
         * 场景一：无填单信息，只有pv曝光
         */
        SubmitData submitData = null;
        UploadDataDTO uploadDataDTO = new UploadDataDTO();
        // 通过pid 获取上报信息
        if (StringUtils.isNotBlank(pid)) {
            //防止爆量未配置pv上报
            if (UploadEventType.PAGE_VIEW.equals(uploadEventType)) {
                UploadConfiguration uploadConfiguration = uploadConfigurationRedis.getInfo(landingPageId, channelId);
                if (Objects.isNull(uploadConfiguration)) {
                    return null;
                }
                List<UploadConfigurationTypes> ucTypeList = this.getUploadConfigurationTypes(uploadConfiguration, uploadEventType, uploadDto);
                if (CollectionUtils.isEmpty(ucTypeList)) {
                    return null;
                }
                long typeNum = ucTypeList.stream().filter(e -> Objects.equals(uploadEventType, e.getUploadEventType())).count();
                if (typeNum <= 0) {
                    return null;
                }
            }

            // 检验taobaoDSP事件是否配置上报条件，没有配置上报则不需要走后面的上报逻辑
            if (Objects.nonNull(uploadEventType) && TAOBAO_DSP_EVENTS.contains(uploadEventType)) {
                UploadConfiguration uploadConfiguration = uploadConfigurationRedis.getInfo(landingPageId, channelId);
                if (Objects.isNull(uploadConfiguration)) {
                    return null;
                }
                List<UploadConfigurationTypes> ucTypeList = this.getUploadConfigurationTypes(uploadConfiguration, uploadEventType, uploadDto);
                if (CollectionUtils.isEmpty(ucTypeList)) {
                    return null;
                }
                //事件相等，并且事件下 配置的 DSPid 符合配置
                long typeNum = ucTypeList.stream().filter(e -> Objects.equals(uploadEventType, e.getUploadEventType())).count();
                if (typeNum <= 0) {
                    return null;
                }
            }

            submitData = submitDataService.getOne(new LambdaQueryWrapper<SubmitData>()
                .ge(SubmitData::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerQueryTime()))
                .eq(SubmitData::getPid, pid).orderByDesc(SubmitData::getCreatedAt).last(" limit 1"));
            //#31661 【线上问题】性别上报多选，导致上报内容错误 https://ones.yiye.ai/project/#/team/WtsduTeT/task/EFZs7UMKFS9am5nH
            SubmitData nextPidSubmitData = null;
            if (Objects.isNull(submitData)) {
                nextPidSubmitData = submitDataService.getOne(new LambdaQueryWrapper<SubmitData>()
                    .ge(SubmitData::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerQueryTime()))
                    .eq(SubmitData::getNextPid, pid).orderByDesc(SubmitData::getCreatedAt).last(" limit 1"));
            }
            if (Objects.isNull(submitData)) {
                PageViewInfo pageViewInfo = pageViewInfoService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                    .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoQueryTime()))
                    .eq(PageViewInfo::getPid, pid)
                    .last(" limit 1"));
                if (Objects.isNull(pageViewInfo)) {
                    return null;
                }
                uploadDataDTO.setPid(pid);
                uploadDataDTO.setLandingPageId(pageViewInfo.getLandingPageId());
                uploadDataDTO.setChannelId(pageViewInfo.getChannelId());
                uploadDataDTO.setUrl(pageViewInfo.getUrl());
                uploadDataDTO.setReferrer(pageViewInfo.getReferrer());
                //公众号openid、unionid
                uploadDataDTO.setOpenId(pageViewInfo.getWechatOpenid());
                uploadDataDTO.setUnionId(pageViewInfo.getWechatUnionid());
                uploadDataDTO.setAppId(pageViewInfo.getFollowOfficialAccountAppId());
                //小程序openid、unionid
                uploadDataDTO.setWechatAppletOpenid(pageViewInfo.getWechatAppletOpenid());
                uploadDataDTO.setWechatAppletUnionid(pageViewInfo.getWechatAppletUnionid());
                //流量来源
                uploadDataDTO.setFlowSource(pageViewInfo.getFlowSource());
                //企业微信-外部联系人-性别
                uploadDataDTO.setExternalUserSex(Objects.nonNull(nextPidSubmitData) ? nextPidSubmitData.getExternalUserSex() : null);
                //填单串加粉填单id
                uploadDataDTO.setFormMatchAddWechatSubmitDataId(Objects.nonNull(nextPidSubmitData) ? nextPidSubmitData.getId() : null);
                //1.247.0 字节小程序：openid、客资来源
                uploadDataDTO.setDouyinAppletOpenid(pageViewInfo.getDouyinAppletOpenid());
                uploadDataDTO.setDouyinCustomerSource(pageViewInfo.getDouyinCustomerSource());
                uploadDataDTO.setOppoUploadIp(pageViewInfo.getIp());
                uploadDataDTO.setIp(pageViewInfo.getIp());
                return uploadDataDTO;
            }
        }
        /*
         * 场景二：有填单信息，有pv曝光
         */
        if (Objects.isNull(submitData)) {
            if (Objects.isNull(submitDataId)) {
                return null;
            }
            submitData = submitDataService.getOne(new LambdaQueryWrapper<SubmitData>()
                .ge(SubmitData::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerQueryTime()))
                .eq(SubmitData::getId, submitDataId)
                .last(" limit 1")
            );
            if (Objects.isNull(submitData)) {
                return null;
            }
        }
        uploadDataDTO.setPid(submitData.getPid());
        uploadDataDTO.setSubmitDataId(Objects.isNull(submitDataId) ? submitData.getId() : submitDataId);
        //#31661 【线上问题】性别上报多选，导致上报内容错误 https://ones.yiye.ai/project/#/team/WtsduTeT/task/EFZs7UMKFS9am5nH
        //表单/订单链路
        uploadDataDTO.setLandingPageId(submitData.getLandingPageId()).setChannelId(submitData.getChannelId()).setUrl(submitData.getUrl());
        //whatsapp链路（建联、二次开口）
        if (!Objects.isNull(submitData.getWhatsappLandingPageId())) {
            uploadDataDTO.setLandingPageId(submitData.getWhatsappLandingPageId()).setChannelId(submitData.getWhatsappLandingPageChannelId()).setUrl(submitData.getWhatsappUrl());
        }
        //字节小程序 - 表单链路
        if (SubmitType.DOUYIN_APPLET.equals(submitData.getSubmitType())) {
            uploadDataDTO.setUrl(submitData.getDouyinAppletVisitUrl());
        }
        //字节小程序 - 订单链路
        if (SubmitType.DOUYIN_APPLET_ORDER.equals(submitData.getSubmitType())) {
            uploadDataDTO.setUrl(submitData.getDouyinAppletVisitUrl());
        }
        //加企微客服链路（加粉）
        if (!Objects.isNull(submitData.getWechatAppletLandingPageId())) {
            uploadDataDTO.setLandingPageId(submitData.getWechatAppletLandingPageId()).setChannelId(submitData.getWechatAppletLandingPageChannelId()).setUrl(submitData.getWechatAppletLandingPageViewUrl());
        }
        //加企微群链路（加群）
        if (!Objects.isNull(submitData.getGroupChatLandingPageId())) {
            uploadDataDTO.setLandingPageId(submitData.getGroupChatLandingPageId()).setChannelId(submitData.getGroupChatChannelId()).setUrl(submitData.getGroupChatUrl());
        }
        //公众号内发送活码加粉链路
        if (Objects.nonNull(submitData.getWechatOfficialAccountLandingPageId()) && UploadEventType.FOLLOW_OFFICIAL_ADD_CUSTOMER.equals(uploadEventType)) {
            uploadDataDTO.setLandingPageId(submitData.getWechatOfficialAccountLandingPageId()).setChannelId(submitData.getWechatOfficialAccountLandingPageChannelId())
                .setUrl(submitData.getWechatOfficialAccountLandingPageViewUrl());
        }
        //关注公众号后发码加粉并入群
        if (Objects.nonNull(submitData.getWechatOfficialAccountLandingPageId()) && UploadEventType.FOLLOW_OFFICIAL_JOIN_GROUP.equals(uploadEventType)) {
            uploadDataDTO.setLandingPageId(submitData.getWechatOfficialAccountLandingPageId()).setChannelId(submitData.getWechatOfficialAccountLandingPageChannelId())
                .setUrl(submitData.getWechatOfficialAccountLandingPageViewUrl());
        }
        if (Objects.nonNull(submitData.getWechatOfficialAccountLandingPageId()) && UploadEventType.DELETE_ENTERPRISE_WECHAT_FRIEND.equals(uploadEventType) &&
            StringUtils.isNotBlank(submitData.getWechatOfficialAccountLandingPageViewUrl())
        ) {
            uploadDataDTO.setLandingPageId(submitData.getWechatOfficialAccountLandingPageId()).setChannelId(submitData.getWechatOfficialAccountLandingPageChannelId())
                .setUrl(submitData.getWechatOfficialAccountLandingPageViewUrl());
        }
        uploadDataDTO.setReferrer(submitData.getReferrer());
        uploadDataDTO.setPhone(this.getPhone(submitData));
        uploadDataDTO.setName(this.getName(submitData));
        uploadDataDTO.setWhatsappUserName(submitData.getWhatsappUserName());
        uploadDataDTO.setWhatsappUserPhone(submitData.getWhatsappUserPhone());
        uploadDataDTO.setEmail(this.getEmail(submitData));
        uploadDataDTO.setLandingPageWidgetTemplateId(submitData.getLandingPageWidgetTemplateId());
        //小程序openid、unionid
        uploadDataDTO.setWechatAppletOpenid(submitData.getWechatAppletOpenid());
        uploadDataDTO.setWechatAppletUnionid(submitData.getWechatAppletUnionid());
        //流量来源
        uploadDataDTO.setFlowSource(submitData.getFlowSource());
        SubmitDataOtherExt submitDataOtherExt = JSONObject.toJavaObject(submitData.getOtherExt(), SubmitDataOtherExt.class);
        //无微信信息
        if (!Objects.isNull(submitDataOtherExt)) {
            SubmitDataOtherExt.PageViewExt pageViewExt = submitDataOtherExt.getPageViewExt();
            //pageViewExt,不需要支付的订单与表单
            if (!Objects.isNull(pageViewExt)) {
                //公众号openid、unionid
                uploadDataDTO.setOpenId(pageViewExt.getOpenId());
                uploadDataDTO.setUnionId(pageViewExt.getUnionId());
                uploadDataDTO.setAppId(pageViewExt.getAppId());
            }
        }
        Customer customer = customerService.selectLimitOne((!Objects.isNull(submitDataId) ? submitDataId : submitData.getId()));
        if (!Objects.isNull(customer)) {
            //公众号openid、unionid
            uploadDataDTO.setOpenId(!Objects.isNull(uploadDataDTO.getOpenId()) ? uploadDataDTO.getOpenId() : customer.getWechatOpenid());
            uploadDataDTO.setUnionId(!Objects.isNull(uploadDataDTO.getUnionId()) ? uploadDataDTO.getUnionId() : customer.getWechatUnionid());
            uploadDataDTO.setAppId(!Objects.isNull(uploadDataDTO.getAppId()) ? uploadDataDTO.getAppId() : customer.getAppId());
            //小程序openid、unionid
            uploadDataDTO.setWechatAppletOpenid(customer.getWechatAppletOpenid());
            uploadDataDTO.setWechatAppletUnionid(customer.getWechatAppletUnionid());
            //流量来源
            uploadDataDTO.setFlowSource(customer.getFlowSource());
            //获取落地页微信信息
            uploadDataDTO.setCustomer(customer);
            //1.247.0 字节小程序：openid、客资来源
            uploadDataDTO.setDouyinAppletOpenid(customer.getDouyinAppletOpenid());
            uploadDataDTO.setDouyinCustomerSource(customer.getDouyinCustomerSource());
            uploadDataDTO.setOppoUploadIp(customer.getIp());
            uploadDataDTO.setIp(customer.getIp());
        }
        uploadDataDTO.setSubmitData(submitData);
        //如果是企业微信标签变更，将取加粉页面的上报配置
        if (UploadEventType.WORK_WEIXIN_CUSTOMER_TAG.equals(uploadEventType)) {
            if (!Objects.isNull(submitData.getWechatAppletLandingPageId()) && 0 != submitData.getWechatAppletLandingPageId()) {
                uploadDataDTO.setLandingPageId(submitData.getWechatAppletLandingPageId());
            }
            if (!Objects.isNull(submitData.getWechatAppletLandingPageChannelId()) && 0 != submitData.getWechatAppletLandingPageChannelId()) {
                uploadDataDTO.setChannelId(submitData.getWechatAppletLandingPageChannelId()).setUrl(submitData.getWechatAppletLandingPageViewUrl());
            }
        }
        //企业微信-外部联系人-性别
        uploadDataDTO.setExternalUserSex(Objects.nonNull(submitData) ? submitData.getExternalUserSex() : null);
        return uploadDataDTO;
    }

    /**
     * 客资统一上报
     * uploadEventType 必填字段
     */
    public ResultBean<Object> upload(UploadDto uploadDto) {
        log.info("进入客资统一上报, uploadDto ======>> {}", JSONObject.toJSONString(uploadDto));
        try {
            final String agentId = TenantContextHolder.get();
            final Date date = new Date(Instant.now().toEpochMilli());
            final UploadEventType uploadEventType = uploadDto.getUploadEventType();
            if (Objects.isNull(uploadEventType)) {
                log.error("事件类型为空！请检查代码 uploadDto:{}", JSONObject.toJSONString(uploadDto));
                return new ResultBean<>(ResponseCode.UPLOAD_EVENT_TYPE_ERROR);
            }
            log.info("触发事件1 uploadEventType:{}  uploadDto:{}", uploadEventType, JSONObject.toJSONString(uploadDto));
            final String pid = uploadDto.getPid();

            List<String> blackPid = agentConf.getBlackPid();
            if (!CollectionUtils.isEmpty(blackPid) && StringUtils.isNotBlank(pid) && blackPid.contains(pid)) {
                log.info("该上报pid被禁用，禁止上报！pid:{}", pid);
                return new ResultBean<>(ResponseCode.SUCCESS);
            }

            final Long submitDataId = uploadDto.getSubmitDataId();
            /*
             * 适配上报场景参数
             */
            UploadDataDTO uploadDataDTO1 = this.getUploadDataDTO(pid, submitDataId, uploadDto);
            if (Objects.isNull(uploadDataDTO1)) {
                return new ResultBean<>(ResponseCode.UN_UPLOAD);
            }
            String url = uploadDataDTO1.getUrl() + "";
            String referrer = uploadDataDTO1.getReferrer();
            FlowSource fs = uploadDataDTO1.getFlowSource();
            String clickId = UrlUtils.getClickIdByPlatform(url, referrer);
            final Long channelId = uploadDataDTO1.getChannelId();
            final Long landingPageId = uploadDataDTO1.getLandingPageId();
            final Customer customer = uploadDataDTO1.getCustomer();
            final Sex externalUserSex = uploadDataDTO1.getExternalUserSex();
            Platform platform = UrlUtils.getPlatformSourcesByUrlOrRefer(url, referrer);
            //触发上报条件时间与成功添加企业微信时间超过设定时间不上报
            final Long addWechatSuccessTime = uploadDto.getAddWechatSuccessTime();
            final AddEnterpriseWechatStatus addEnterpriseWechatStatus = uploadDto.getAddEnterpriseWechatStatus();
            //记录上报记录
            CustomerUploadRecord uploadRecord = new CustomerUploadRecord();
            BeanUtils.copyProperties(uploadDataDTO1, uploadRecord);
            uploadRecord.setSubmitType(uploadEventType).setClickId(clickId);

            if (Objects.isNull(channelId)) {
                log.error("触发事件2: {} pid: {} uploadDto.getSubmitDataId: {}  {} url：{}", uploadEventType, pid, submitDataId, ResponseCode.CHANNEL_ID_UN_KNOW.getMsg(), url);
                return new ResultBean<>(ResponseCode.CHANNEL_ID_UN_KNOW);
            }

            // 落地页校验：是否存在
            if (Objects.isNull(landingPageId)) {
                log.error("触发事件3: {} pid: {} uploadDto.getSubmitDataId: {}  {} url：{}", uploadEventType, pid, submitDataId, ResponseCode.LANDING_PAGE_NOT_EXIST.getMsg(), url);
                return new ResultBean<>(ResponseCode.LANDING_PAGE_NOT_EXIST);
            }

            // 落地页上报信息校验：是否存在
            UploadConfiguration uploadConfiguration = uploadConfigurationRedis.getInfo(landingPageId, channelId);
            if (Objects.isNull(uploadConfiguration)) {
                if (!Objects.isNull(platform) && !Arrays.asList(Platform.OTHER, Platform.UN_KNOW).contains(platform) && !UploadEventType.PAGE_VIEW.equals(uploadEventType)) {
                    log.info("触发事件4: {} pid: {} uploadDto.getSubmitDataId: {}  {} url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.HAS_CLICKID_NOT_CONFIG.getMsg(), url);
                    return this.saveRecordAndLog(customer, uploadRecord, ResponseCode.HAS_CLICKID_NOT_CONFIG);
                }
                log.info("触发事件5: {} pid: {} uploadDto.getSubmitDataId: {}  {} ur：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.UPLOAD_CONFIG_NOT_EXIST.getMsg(), url);
                return new ResultBean<>(ResponseCode.UPLOAD_CONFIG_NOT_EXIST);
            }
            uploadRecord.setAdvertiserAccountId(uploadConfiguration.getAdvertiserAccountId()).setPlatformId(uploadConfiguration.getPlatformId());

            //媒体是快手，并且上报类型是js上报
            if (Platform.KUAISHOU.equals(uploadConfiguration.getPlatformId()) && MediaUploadType.KUAISHOU_JS_UPLOAD.equals(uploadConfiguration.getMediaUploadType())) {
                return this.updateJsUploadState(submitDataId);
            }

            //上报媒体为【微信广告】，推广类型为【微信公众号】，如果不是来源于【微信广告】，不进行上报
            if (Platform.MP_AD.equals(uploadConfiguration.getPlatformId()) && PromotionType.MP_OFFICIAL.equals(uploadConfiguration.getPromotionType()) && !SubscribeSceneType.ADD_SCENE_WECHAT_ADVERTISEMENT.equals(uploadDto.getSubscribeScene())) {
                log.info("触发事件11.1.1.1: {} pid: {} uploadDto.getSubmitDataId: {}  {} url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.NON_WECHAT_AD_SOURCE.getMsg(), url);
                return this.saveRecordAndLog(customer, uploadRecord, ResponseCode.NON_WECHAT_AD_SOURCE);
            }

            // 校验是否需要上报：主要是校验url中是否携带平台的一些参数，比如百度广告连接中是否包含【bd_vid】等
            boolean clickIdIsBlank = UrlUtils.clickIdIsBlank(url, referrer, uploadConfiguration.getPlatformId());

            //公众号蓝链不上报事件类型（兼容下方【11.1.1.2】校验逻辑）
            if (clickIdIsBlank) {
                //访问链接未携带click_id媒体点击参数时，上报条件-浏览页面，不上报，也不记录
                if (UploadEventType.PAGE_VIEW.toString().equals(uploadDto.getUploadEventType().toString())/* || Objects.isNull(customer)*/) {
                    log.info("触发事件12: {} pid: {} uploadDto.getSubmitDataId: {}  {} url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.PAGE_VIEW_NOT_CLICK_ID.getMsg(), url);
                    return new ResultBean<>(ResponseCode.PAGE_VIEW_NOT_CLICK_ID);
                }

                //长按识别公众号二维码
                if (UploadEventType.OFFICIAL_QR_CODE.toString().equals(uploadDto.getUploadEventType().toString())){
                    log.info("触发事件13: {} pid: {} uploadDto.getSubmitDataId: {}  {} url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.UPLOAD_EVENT_TYPE_IS_FOLLOW_OFFICIAL_QRCODE_NO_CLICKID.getMsg(), url);
                    this.saveRecordAndLog(customer, uploadRecord, ResponseCode.UPLOAD_EVENT_TYPE_IS_FOLLOW_OFFICIAL_QRCODE_NO_CLICKID);
                    return new ResultBean<>(ResponseCode.UPLOAD_EVENT_TYPE_IS_FOLLOW_OFFICIAL_QRCODE_NO_CLICKID);
                }

                if (UploadEventType.FOLLOW_OFFICIAL_ACCOUNT.toString().equals(uploadDto.getUploadEventType().toString())/* || Objects.isNull(customer)*/) {
                    log.info("触发事件14: {} pid: {} uploadDto.getSubmitDataId: {}  {}  url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.UPLOAD_EVENT_TYPE_IS_FOLLOW_OFFICIAL_ACCOUNT_NO_CLICKID.getMsg(), url);
                    this.saveRecordAndLog(customer, uploadRecord, ResponseCode.UPLOAD_EVENT_TYPE_IS_FOLLOW_OFFICIAL_ACCOUNT_NO_CLICKID);
                    return new ResultBean<>(ResponseCode.UPLOAD_EVENT_TYPE_IS_FOLLOW_OFFICIAL_ACCOUNT_NO_CLICKID);
                }
                //公众号关注后活码加粉 clickId为空的情况
                if (UploadEventType.FOLLOW_OFFICIAL_ADD_CUSTOMER.equals(uploadDto.getUploadEventType())){
                    log.info("触发事件14.0.1: {} pid: {} uploadDto.getSubmitDataId: {}  {}  url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.CLICKID_IS_NULL_NOT_UPDATE.getMsg(), url);
                    this.saveRecordAndLog(customer, uploadRecord, ResponseCode.CLICKID_IS_NULL_NOT_UPDATE);
                    return new ResultBean<>(ResponseCode.CLICKID_IS_NULL_NOT_UPDATE);
                }
            }

            //1.247.0字节小程序匹配最近一次clickid未使用访问来源中url进行上报规则
            if (!Objects.isNull(uploadDataDTO1.getDouyinCustomerSource()) && Objects.equals(DouyinCustomerSource.PRIVATE_SPHERE, uploadDataDTO1.getDouyinCustomerSource()) &&
                StringUtils.isNotBlank(uploadDataDTO1.getDouyinAppletOpenid())
            ) {
                url = pageViewInfoRedis.getDouyinPidCacheUrl(uploadDataDTO1.getDouyinAppletOpenid());
                uploadDataDTO1.setUrl(StringUtils.isNotBlank(url) ? url : uploadDataDTO1.getUrl());
                //#43540 【YIYE_AGENT_V1.247.0】同一clickid，同一上报类型，客资重复上报 https://ones.yiye.ai/project/#/team/WtsduTeT/task/EFZs7UMKpMaajgXk
                clickId = UrlUtils.getClickIdByPlatform(url, referrer);
                uploadRecord.setClickId(clickId).setMatchOtherPvUrl(url);
                clickIdIsBlank = false;
            }

            // 公众号蓝链匹配规则
            if (clickIdIsBlank) {
                //1.125.0版本：获取填单信息，根据openid、unionid、上报配置-平台id，匹配pv曝光信息
                String thisPid = null;
                String wechatUnionid = null;
                final Instant todayNow = Instant.now();
                final Instant beforeNow = todayNow.minusMillis(TimeUnit.MINUTES.toMillis(agentConf.getBlueLinkMatchingFollowOfficialAccountTime()));
                if (!Objects.isNull(customer)) {
                    thisPid = customer.getPid();
                    //#20070 【紧急需求】1.159.0 没有广告来源的客资上报状态应该为空,现在还有上报 https://ones.yiye.ai/project/#/team/WtsduTeT/task/EvybVgBAPG5xt2LQ
                    wechatUnionid = StringUtils.isBlank(StringUtils.trim(customer.getWechatUnionid())) ? StringUtils.trim(customer.getWechatAppletUnionid()) : StringUtils.trim(customer.getWechatUnionid());    //如果没有公众号unionId，取小程序unionId
                }
                if (StringUtils.isBlank(StringUtils.trim(wechatUnionid))) {
                    //兼容没有客资的场景
                    PageViewInfo uploadPvInfo = pageViewInfoService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                        .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoQueryTime()))
                        .eq(PageViewInfo::getPid, pid)
                        .orderByDesc(PageViewInfo::getCreatedAt)
                        .orderByDesc(PageViewInfo::getUpdatedAt)
                        .last(" limit 1")
                    );
                    if (!Objects.isNull(uploadPvInfo)) {
                        thisPid = uploadPvInfo.getPid();
                        //#20070 【紧急需求】1.159.0 没有广告来源的客资上报状态应该为空,现在还有上报 https://ones.yiye.ai/project/#/team/WtsduTeT/task/EvybVgBAPG5xt2LQ
                        wechatUnionid = StringUtils.isBlank(StringUtils.trim(uploadPvInfo.getWechatUnionid())) ? StringUtils.trim(uploadPvInfo.getWechatAppletUnionid()) : StringUtils.trim(uploadPvInfo.getWechatUnionid());   //如果没有公众号unionId，取小程序unionId
                        if (StringUtils.isBlank(StringUtils.trim(wechatUnionid))) {
                            log.info("触发事件15 - 没有获取到unionid信息，操作终止: {} pid: {} uploadDto.getSubmitDataId: {}  {} url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.CLICKID_IS_NULL_AND_UNIONID_IS_NULL.getMsg(), url);
                            this.saveRecordAndLog(customer, uploadRecord, ResponseCode.CLICKID_IS_NULL_NOT_UPDATE);
                            return new ResultBean<>(ResponseCode.CLICKID_IS_NULL_AND_UNIONID_IS_NULL);
                        }
                    }
                }
                //关注公众号链路：匹配访问来源：根据微信公众号unionid/微信小程序unionid，匹配上报配置对应媒体24小时内最近的一次访来源
                final String finalWechatUnionid = wechatUnionid;
                PageViewInfo pvInfo = pageViewInfoService.getOne(new LambdaQueryWrapper<PageViewInfo>()
                    .ge(PageViewInfo::getCreatedAt, beforeNow).le(PageViewInfo::getCreatedAt, todayNow)
                    //#20070 【紧急需求】1.159.0 没有广告来源的客资上报状态应该为空,现在还有上报 https://ones.yiye.ai/project/#/team/WtsduTeT/task/EvybVgBAPG5xt2LQ
                    .eq(PageViewInfo::getClickJumpToWoaStatus, YesOrNoEnum.YES)
                    .eq(PageViewInfo::getFollowOfficialAccountStatus, FollowStatus.FOLLOW)
                    .eq(PageViewInfo::getPlatformId, uploadConfiguration.getPlatformId())
                    .and(wrapper -> wrapper.eq(PageViewInfo::getWechatUnionid, finalWechatUnionid).or(wr -> wr.eq(PageViewInfo::getWechatAppletUnionid, finalWechatUnionid)))
                    .ne(PageViewInfo::getPid, thisPid).orderByDesc(PageViewInfo::getCreatedAt).orderByDesc(PageViewInfo::getUpdatedAt).last(" limit 1")
                );
                //未匹配成功，不上报
                if (Objects.isNull(pvInfo)) {
                    log.info("触发事件16: {} pid: {} uploadDto.getSubmitDataId: {}  {} url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.CLICKID_IS_NULL_AND_MATCHING_PV_IS_NULL.getMsg(), url);
                    this.saveRecordAndLog(customer, uploadRecord, ResponseCode.CLICKID_IS_NULL_NOT_UPDATE);
                    return new ResultBean<>(ResponseCode.CLICKID_IS_NULL_AND_MATCHING_PV_IS_NULL);
                }
                url = pvInfo.getUrl();
                referrer = pvInfo.getReferrer();
                fs = pvInfo.getFlowSource();
                //匹配到的访问来源，也不是来自于广告平台访问，不上报
                if (UrlUtils.clickIdIsBlank(url, referrer, uploadConfiguration.getPlatformId())) {
                    log.info("触发事件17: {} pid: {} uploadDto.getSubmitDataId: {}  {}  url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.CLICKID_IS_NULL.getMsg(), url);
                    this.saveRecordAndLog(customer, uploadRecord, ResponseCode.CLICKID_IS_NULL_NOT_UPDATE);
                    return new ResultBean<>(ResponseCode.CLICKID_IS_NULL);
                }
                //非广告平台访问，匹配成功的访问来源url与上报配置中对应媒体不符，不上报
                platform = UrlUtils.getPlatformSourcesByUrlOrRefer(url, referrer);
                if (!uploadConfiguration.getPlatformId().equals(platform)) {
                    log.info("触发事件18: {} pid: {} uploadDto.getSubmitDataId: {}  {}  访问来源url对应媒体：{} 上报配置对应媒体：{} url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.PV_URL_PLATFORM_NOT_MATCHING_UPLOAD_PARAM_PLATFORM.getMsg(), platform.getName(), uploadConfiguration.getPlatformId().getName(), url);
                    this.saveRecordAndLog(customer, uploadRecord, ResponseCode.CLICKID_IS_NULL_NOT_UPDATE);
                    return new ResultBean<>(ResponseCode.PV_URL_PLATFORM_NOT_MATCHING_UPLOAD_PARAM_PLATFORM);
                }
                uploadRecord.setUrl(url).setReferrer(referrer).setFlowSource(fs).setIsMatchingPageViewInfoUpload(true);
                uploadDataDTO1.setUrl(url).setReferrer(referrer).setFlowSource(fs).setIsMatchingPageViewInfoUpload(true);
                UpdateFlowSourceDto updateFlowSourceDto = new UpdateFlowSourceDto().setThisPid(thisPid).setMatchingPvInfo(pvInfo).setAgentId(agentId);
                //发送MQ根据pid修改对应【填单、客资】的【流量来源】
                submitDataSender.sendMatchingPvUpdateSubmitDataInfo(updateFlowSourceDto);
                //发送MQ根据pid修改对应【pv曝光】的【流量来源】
                pageViewInfoSender.sendMatchingPvUpdatePageViewInfo(updateFlowSourceDto);
            }
            uploadRecord.setAdvertiserAccountId(uploadConfiguration.getAdvertiserAccountId()).setPlatformId(uploadConfiguration.getPlatformId()).setClickId(UrlUtils.getClickIdByPlatform(url, referrer));

            //获取上报目标转化类型列表详情
            List<UploadConfigurationTypes> ucTypeList = this.getUploadConfigurationTypes(uploadConfiguration, uploadEventType, uploadDto);
            if (CollectionUtils.isEmpty(ucTypeList)) {
                log.info("触发事件19.0: {} pid: {} uploadDto.getSubmitDataId: {}  {}  url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.ACTION_TYPE_ERROR.getMsg(), url);
                return new ResultBean<>(ResponseCode.ACTION_TYPE_ERROR);
            }

            //会话存档相关校验，取值与队列发送前匹配到的目标转化类型保持一致，如果在校验期间有对上报配置进行修改/删除导致无法上报，则不上报，具体提示编号为：19.2.0.1
            if (UploadEventType.getWorkWechatSessionTypes().contains(uploadDto.getUploadEventType())) {
                ucTypeList = ucTypeList.stream().filter(e -> Objects.equals(e.getId(), uploadDto.getUploadConfigurationTypesId())).collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(ucTypeList)) {
                log.info("触发事件19.2.0.1: {} pid: {} uploadDto.getSubmitDataId: {}  {}  url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.ACTION_TYPE_ERROR.getMsg(), url);
                return new ResultBean<>(ResponseCode.ACTION_TYPE_ERROR);
            }

            log.info("触发事件27.0.0.3: uploadEventType={}；pid={}；submitDataId={}；url={}；", uploadEventType, pid, uploadDto.getSubmitDataId(), url);

            List<ResultBean<Object>> resultCollect = new ArrayList<>();
            for (UploadConfigurationTypes ucType : ucTypeList) {
                log.info("触发事件27.0.0.1: uploadEventType={}；pid={}；submitDataId={}；url={}；ucType={}；", uploadEventType, pid, uploadDto.getSubmitDataId(), url, ucType);
                uploadRecord.setSubmitType(uploadDto.getUploadEventType());
                uploadRecord.setUploadTimeArea(this.getUploadTimeAreaStr(uploadRecord, ucType))
                    .setActionType(Objects.isNull(ucType.getUploadEventTypeValue()) ? null : ucType.getUploadEventTypeValue()[0])
                    //会话存档相关
                    .setCompareType(ucType.getCompareType())
                    .setWorkWechatOpenNum(ucType.getWorkWechatOpenNum())
                    .setSex(ucType.getSex())
                    .setFuzzyMatchingField(ucType.getFuzzyMatchingField())
                    .setMatchingMode(ucType.getFuzzyMatchingField())
                    .setKeyWord(ucType.getKeyWord())
                    //触发上报条件时间与成功添加企业微信时间超过设定时间不上报
                    .setCheckUploadTimeAddWechatSuccess(ucType.getCheckUploadTimeAddWechatSuccess())
                    .setCheckUploadTimeAddWechatSuccessTime(ucType.getCheckUploadTimeAddWechatSuccessTime())
                ;

                // 当媒体下发的clickid参数值不为空，且【落地页-上报配置-上报媒体】与当前访问页面【媒体来源】不一致时终止上报，并记录为：上报配置中的上报媒体与媒体来源不一致，未上报
                // #42113 【YIYE_AGENT_V1.245.0】流量来源是快手，客资显示的是抖音 https://ones.yiye.ai/project/#/team/WtsduTeT/task/EvybVgBAmGIIjFYZ
                if (StringUtils.isNotBlank(clickId) && !Objects.isNull(platform) &&
                    !Arrays.asList(Platform.OTHER, Platform.UN_KNOW).contains(platform) &&
                    (!Objects.equals(platform, uploadConfiguration.getPlatformId())) &&
                    //#47449 【线上问题】百度信息流上报失败 https://ones.yiye.ai/project/#/team/WtsduTeT/task/EFZs7UMKEmLZpkw8
                    !(Platform.BAIDU.equals(uploadConfiguration.getPlatformId()) && Arrays.asList(Platform.BAIDU, Platform.BAIDU_SEARCH, Platform.BAIDU_MSG).contains(platform))
                ) {
                    log.info("触发事件11.1.1.2: {} pid: {} uploadDto.getSubmitDataId: {}  {} url：{} uConfig: {}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.UPLOAD_CONFIG_PLATFORM_NOT_EQUAL_PLATFORM.getMsg(), url, JSONObject.toJSONString(uploadConfiguration));
                    resultCollect.add(this.saveRecordAndLog(customer, uploadRecord, ResponseCode.UPLOAD_CONFIG_PLATFORM_NOT_EQUAL_PLATFORM));
                    continue;
                }

                //校验：触发上报条件时间与成功添加企业微信时间超过设定时间不上报
                if (!Objects.isNull(ucType.getCheckUploadTimeAddWechatSuccess()) && ucType.getCheckUploadTimeAddWechatSuccess()) {
                    //获取30天前当前时间段时间戳
                    Long beforeTime = DateTimeUtil.getBeforeDayMillisecondByTimeUnit(agentConf.getCheckUploadTimeAddWechatSuccessTimeTimeUnit(), ucType.getCheckUploadTimeAddWechatSuccessTime());
                    log.info("企业微信客户标签上报-触发上报条件时间与成功添加企业微信时间超过设定时间不上报2.0.0.1 addEnterpriseWechatStatus={}；addWechatSuccessTime={}；ucTypeId={}；ucTypeCheck={}；ucTypeCheckTime={}；checkUploadTimeAddWechatSuccessTimeTimeUnit={}；", addEnterpriseWechatStatus, addWechatSuccessTime, ucType.getId(), ucType.getCheckUploadTimeAddWechatSuccess(), ucType.getCheckUploadTimeAddWechatSuccessTime(), agentConf.getCheckUploadTimeAddWechatSuccessTimeTimeUnit());

                    //校验填单创建时间是否包含在配置的时间范围内,加粉时间在设置的时间范围内，正常上报，否则再判断
                    boolean timeCheckResult = checkTimeDiff(agentConf.getCheckUploadTimeAddWechatSuccessTimeTimeUnit(),  ucType.getCheckUploadTimeAddWechatSuccessTime(),  addWechatSuccessTime);

                    log.info("校验填单创建时间是否包含在配置的时间范围内,结果 timeCheckResult = {}", timeCheckResult);
                    //原来判断时间差的逻辑是：addWechatSuccessTime < beforeTime
                    if (!Objects.isNull(addEnterpriseWechatStatus) && AddEnterpriseWechatStatus.ADDED.equals(addEnterpriseWechatStatus) && !Objects.isNull(addWechatSuccessTime) && !timeCheckResult) {
                        log.info("企业微信客户标签上报-触发上报条件时间与成功添加企业微信时间超过设定时间不上报2.0.0.2 addEnterpriseWechatStatus={}；addWechatSuccessTime={}；ucTypeId={}；ucTypeCheck={}；ucTypeCheckTime={}；", addEnterpriseWechatStatus, addWechatSuccessTime, ucType.getId(), ucType.getCheckUploadTimeAddWechatSuccess(), ucType.getCheckUploadTimeAddWechatSuccessTime());
                        resultCollect.add(this.saveRecordAndLog(customer, uploadRecord, ResponseCode.EXCEED_CHECK_UPLOAD_TIME_ADD_WECHAT_SUCCESS_SET_TIME.getCode(), String.format(ResponseCode.EXCEED_CHECK_UPLOAD_TIME_ADD_WECHAT_SUCCESS_SET_TIME.getMsg(), (ucType.getCheckUploadTimeAddWechatSuccessTime() + "天"))));
                        continue;
                    }
                }
                //判断所属ip是否在配置的区域来源中
                if ((!Objects.isNull(ucType.getIpRegionalSourceProvince()) && ucType.getIpRegionalSourceProvince().length > 0)
                    || (!Objects.isNull(ucType.getIpRegionalSourceCity()) && ucType.getIpRegionalSourceCity().length > 0)){
                    Integer[] ipRegionalSourceProvince = ucType.getIpRegionalSourceProvince();
                    Integer[] ipRegionalSourceCity = ucType.getIpRegionalSourceCity();

                    String ip =  uploadDataDTO1.getIp();
                    log.info("判断所属ip是否在配置的区域来源中, ip:{}, ipRegionalSourceProvince: {}, ipRegionalSourceCity: {}", ip,  Arrays.toString(ipRegionalSourceProvince), Arrays.toString(ipRegionalSourceCity));
                    boolean res = checkIpRegionalSource(ipRegionalSourceProvince, ipRegionalSourceCity, uploadDataDTO1, pid, uploadDto);
                    log.info("判断所属ip是否在配置的区域来源中, res = {}", res);
                    if (!res){
                        log.info("判断所属ip是否在配置的区域来源中, ip不在配置的区域来源中, ip = {}, res = {}", ip, res);
                        resultCollect.add(this.saveRecordAndLog(customer, uploadRecord, ResponseCode.IP_REGIONAL_SOURCE_NOT_EQUAL.getCode(), ResponseCode.IP_REGIONAL_SOURCE_NOT_EQUAL.getMsg()));
                        continue;
                    }
                }

                //是否巨量引擎联调
                boolean isOceanEngineIntergratedTest = false;
                String intergrated = UrlUtils.getParameter(url, referrer, Collections.singleton(UrlUtils.OCEAN_ENGINE_TEST_URL_PARAM));
                if (StringUtils.isNotBlank(intergrated)) {
                    Object redisValue = objectRedisTemplate.opsForValue().get(RedisConstant.OCEAN_ENGINE_INTERGRATED + intergrated);
                    if (redisValue != null) {
                        isOceanEngineIntergratedTest = true;
                    }
                }
                BossComboDto bossCombo = bossRedis.getCombo(agentId);
                //是否生效时间内
                boolean isTakeEffect = false;
                if (bossCombo != null && BillingMode.ACCOUNT_CONSUMPTION.equals(bossCombo.getBillingMode())) {
                    LocalDateTime takeEffectTime = bossRedis.getTakeEffectTime(agentId);
                    LocalDateTime now = LocalDateTime.now();
                    if (takeEffectTime != null && !now.isBefore(takeEffectTime)) {
                        isTakeEffect = true;
                    }
                }
                //最新套餐的计费模式是按投放账户消耗计费
                if (agentConf.getOpenTaoCanJiFeiUpload() && (bossCombo != null && BillingMode.ACCOUNT_CONSUMPTION.equals(bossCombo.getBillingMode()) && !isOceanEngineIntergratedTest && isTakeEffect)) {
                    if (!Platform.OCEAN_ENGINE.equals(uploadConfiguration.getPlatformId())) {
                        log.info("触发事件6: {} pid: {} uploadDto.getSubmitDataId: {}  {} url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.NOT_OCEAN_ENGINE_UN_UPLOAD.getMsg(), url);
                        resultCollect.add(this.saveRecordAndLog(customer, uploadRecord, ResponseCode.NOT_OCEAN_ENGINE_UN_UPLOAD));
                        continue;
                    }
                    //获取广告创意id
                    String creativeId = UrlUtils.getParameter(url, referrer, Collections.singleton(OCEAN_CREATIVEID));
                    if (StringUtils.isBlank(creativeId)) {
                        log.info("触发事件7: {} pid: {} uploadDto.getSubmitDataId: {}  {} 无巨量创意id url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.NOT_OCEAN_ENGINE_UN_UPLOAD.getMsg(), url);
                        resultCollect.add(this.saveRecordAndLog(customer, uploadRecord, ResponseCode.NOT_OCEAN_ENGINE_UN_UPLOAD));
                        continue;
                    }
                    String accountId = advertiserCreativeRedis.getAccountId(creativeId);
                    if (StringUtils.isBlank(accountId)) {
                        log.info("触发事件8: {} pid: {} uploadDto.getSubmitDataId: {}  {} 无accountId url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.NOT_AUTHORIZED_ACCOUNT_UN_UPLOAD.getMsg(), url);
                        resultCollect.add(this.saveRecordAndLog(customer, uploadRecord, ResponseCode.NOT_AUTHORIZED_ACCOUNT_UN_UPLOAD));
                        continue;
                    }
                    AdvertiserAccount advertiserAccount = advertiserAccountService.getOne(new LambdaQueryWrapper<AdvertiserAccount>()
                        .eq(AdvertiserAccount::getAccountId, accountId)
                        .last("limit 1"));
                    if (advertiserAccount == null) {
                        log.info("触发事件9: {} pid: {} uploadDto.getSubmitDataId: {}  {} 无advertiserAccount url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.NOT_AUTHORIZED_ACCOUNT_UN_UPLOAD.getMsg(), url);
                        resultCollect.add(this.saveRecordAndLog(customer, uploadRecord, ResponseCode.NOT_AUTHORIZED_ACCOUNT_UN_UPLOAD));
                        continue;
                    }
                    if (!BillingModeStatus.START_BILLING.equals(advertiserAccount.getBillingModeStatus())) {
                        log.info("触发事件10: {} pid: {} uploadDto.getSubmitDataId: {}  {} 停止计费 url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.NOT_AUTHORIZED_ACCOUNT_UN_UPLOAD.getMsg(), url);
                        resultCollect.add(this.saveRecordAndLog(customer, uploadRecord, ResponseCode.NOT_AUTHORIZED_ACCOUNT_UN_UPLOAD));
                        continue;
                    }
                }

                //只有巨量引擎等少数广告媒体才校验【流量来源】
                if (Arrays.asList(Platform.OCEAN_ENGINE, Platform.KUAISHOU).contains(uploadConfiguration.getPlatformId())) {
                    final List<Integer> flowSources = Arrays.asList(ucType.getFlowSources());
                    if (!CollectionUtils.isEmpty(flowSources) && !flowSources.contains(Objects.isNull(fs) ? null : fs.getId())) {
                        ResponseCode code = ResponseCode.IS_KOU_LIANG_UPLOAD_UN_IN_LIU_LIANG_LAI_YUAN;
                        this.saveRecordAndLog(customer, uploadRecord, code);
                        resultCollect.add(new ResultBean<>(code));
                        continue;
                    }
                }

                //成功添加企业微信好友等需要校验性别的上报配置，校验性别
                log.info("客资上报，成功添加企业微信性别校验，uploadDto={},ucTypes={},submitData={},isIncludeGender={}", JSONObject.toJSONString(uploadDto), JSONObject.toJSONString(ucType), externalUserSex, uploadDto.getUploadEventType().isIncludeGender());
                if (
                    //#31683 多条上报条件的设置项完全相同时视为同一上报条件 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PiB4dM6VaXu9oJoG
                    (
                        //强校验性别，兼容 代开发/企业推 无性别，不上报
                        Objects.isNull(externalUserSex) &&
                        !(Objects.isNull(ucType.getSex()) || Objects.equals(Sex.ALL, ucType.getSex()))
                    )  || (
                        //
                        uploadDto.getUploadEventType().isIncludeGender() &&
                        !Objects.isNull(ucType.getSex()) &&
                        !Sex.ALL.equals(ucType.getSex()) &&
                        !Objects.isNull(externalUserSex) &&
                        !Objects.equals(ucType.getSex(), externalUserSex)
                    )
                ) {
                    log.info("触发事件19.1.0: {} pid: {} uploadDto.getSubmitDataId: {}  {}  访问来源url对应媒体：{} 上报配置对应媒体：{} url：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.ADD_ENTERPRISE_WECHAT_SUCCESS_SEX_UN_MATCHING.getMsg(), platform.getName(), uploadConfiguration.getPlatformId().getName(), url);
                    resultCollect.add(this.saveRecordAndLog(customer, uploadRecord, ResponseCode.ADD_ENTERPRISE_WECHAT_SUCCESS_SEX_UN_MATCHING));
                    continue;
                }

                final List<String> actionTypes = Arrays.asList(ucType.getUploadEventTypeValue());

                // 上报类型为【EMPTY】：不上报
                if (actionTypes.contains(UploadType.EMPTY.getName())) {
                    customerUploadRecordService.save(uploadRecord.setUuidKey(CommonUtil.getUuidReplaceAll()).setUuidKey(CommonUtil.getUuidReplaceAll()).setDescription(ResponseCode.UN_UPLOAD.getMsg()).setRecordCode(ResponseCode.UN_UPLOAD.getCode()).setUploadState(UploadStateType.NOT_REPORT).setPlatformId(uploadConfiguration.getPlatformId()));
                    saveUpload(customer, uploadRecord);
                    log.info("触发事件19.1.1:don't upload ! {} ", uploadDto);
                    resultCollect.add(new ResultBean<>(ResponseCode.SUCCESS));
                    continue;
                }
                //同一访客X天内通过X触发过公众号关注上报行为不上报
                if (UploadEventType.FOLLOW_OFFICIAL_ACCOUNT.equals(uploadDto.getUploadEventType())
                    && Boolean.TRUE.equals(ucType.getWhetherDeduplicateVisitor())
                    && StringUtils.isNotBlank(pid)
                ) {
                    PageViewInfo pageViewInfo = pageViewInfoService.getOne(
                        Wrappers.lambdaQuery(PageViewInfo.class)
                            .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoQueryTime()))
                            .eq(PageViewInfo::getPid, pid)
                            .orderByDesc(PageViewInfo::getCreatedAt)
                            .last("limit 1")
                    );
                    Long advertiserAccountGroupIdRange = null;
                    Long landingPageIdRange = null;
                    if (UploadDeduplicateVisitorRange.LANDING_PAGES_WITHIN_PROJECT.equals(ucType.getDeduplicateVisitorRange())) {
                        advertiserAccountGroupIdRange = pageViewInfo.getAdvertiserAccountGroupId();
                    } else if (UploadDeduplicateVisitorRange.CURRENT_LANDING_PAGE.equals(ucType.getDeduplicateVisitorRange())) {
                        landingPageIdRange = pageViewInfo.getLandingPageId();
                    }
                    if (
                        customerUploadRecordService.existOfficialAccountFollowUploadRecord(
                            pageViewInfo.getWechatOpenid(), ucType.getDeduplicateVisitorInterval().getInterval(), landingPageIdRange, advertiserAccountGroupIdRange
                        )
                    ) {
                        ResponseCode code = ResponseCode.OFFICIAL_ACCOUNT_FOLLOW_UN_REPEAT_UPLOAD;
                        for (String actionType : actionTypes) {
                            this.saveRecordAndLog(
                                customer, uploadRecord.setActionType(actionType), code.getCode(),
                                String.format(code.getMsg(), ucType.getDeduplicateVisitorInterval().getName(), ucType.getDeduplicateVisitorRange().getName())
                            );
                        }
                        resultCollect.add(new ResultBean<>(code));
                        continue;
                    }
                }
                //客服删除客户和客户删除客服，只上报一次
                if (UploadEventType.DELETE_ENTERPRISE_WECHAT_FRIEND.equals(uploadDto.getUploadEventType())) {
                    int count = customerUploadRecordService.count(new LambdaQueryWrapper<CustomerUploadRecord>()
                        .eq(CustomerUploadRecord::getClickId, clickId)
                        .eq(CustomerUploadRecord::getSubmitType, UploadEventType.DELETE_ENTERPRISE_WECHAT_FRIEND)
                        .eq(CustomerUploadRecord::getUploadState, UploadStateType.SUCCESS_REPORT)
                    );
                    if (count > 0) {
                        resultCollect.add(new ResultBean<>(ResponseCode.EQUAL_TYPE_UN_UPLOAD));
                        continue;
                    }
                }

                //扣量上报、上报策略
                ResponseCode code = this.doDeductionReport(date, uploadRecord, ucType, uploadConfiguration, uploadDataDTO1, platform);
                if (!Objects.isNull(code)) {
                    for (String actionType : actionTypes) {
                        this.saveRecordAndLog(customer, uploadRecord.setActionType(actionType), code);
                    }
                    resultCollect.add(new ResultBean<>(code));
                    continue;
                }

                //执行上报
                List<ResultBean<Object>> collect = actionTypes.stream().map(actionType -> {
                    try {
                        log.info("触发事件27.0.0.2: uploadEventType={}；pid={}；submitDataId={}；msg={}；ucType={}；actionType={}；", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.ACTION_TYPE_ERROR.getMsg(), ucType, actionType);
                        CustomerUploadRecord customerUploadRecord = new CustomerUploadRecord();
                        BeanUtils.copyProperties(uploadRecord, customerUploadRecord);
                        UploadDataDTO uploadDataDTO = new UploadDataDTO();
                        BeanUtils.copyProperties(uploadDataDTO1, uploadDataDTO);
                        uploadDataDTO.setUploadConfiguration(uploadConfiguration).setActionType(actionType).setPromotionType(uploadConfiguration.getPromotionType()).setUploadPlatformId(uploadConfiguration.getPlatformId());
                        return applicationContext.getBean(CustomerUploadService.class).doUpload(customerUploadRecord, actionType, uploadEventType, uploadConfiguration, uploadDataDTO, customer);
                    } catch (Exception e) {
                        log.error("触发事件20.1:upload error ! uploadEventType：{} pid: {} uploadDto.getSubmitDataId: {}  {} actionType：{}", uploadEventType, pid, uploadDto.getSubmitDataId(), ResponseCode.ACTION_TYPE_ERROR.getMsg(), actionType, e);
                        return new ResultBean<>(ResponseCode.UPLOAD_ERROR);
                    }
                }).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    resultCollect.addAll(collect);
                }
            }
            return resultCollect.stream().filter(a -> a.getCode() != ResponseCode.SUCCESS.getCode()).findFirst().orElse(new ResultBean<>(ResponseCode.SUCCESS));
        } catch (Exception e) {
            log.error("触发事件20.2:upload error ! {} ", e.getMessage(), e);
            return new ResultBean<>(ResponseCode.UPLOAD_ERROR);
        }
    }

    /**
     * 校验pv的ip是否在配置的IP区域来源范围内
     * @param ipRegionalSourceProvince 配置的省级code
     * @param ipRegionalSourceCity 配置的市级code
     * @return 校验结果
     */
    public Boolean checkIpRegionalSource(Integer[] ipRegionalSourceProvince, Integer[] ipRegionalSourceCity, UploadDataDTO uploadDataDTO, String pid, UploadDto uploadDto){
        try {
            if (Objects.isNull(ipRegionalSourceProvince) && Objects.isNull(ipRegionalSourceCity)) {
                return true;
            }
            String ip = null;
            if (StringUtils.isBlank(pid) || StringUtils.isBlank(uploadDataDTO.getIp())) {
                long submitId = uploadDto.getSubmitDataId();
                if (Objects.nonNull(submitId)){
                    SubmitData submitData = submitDataService.getOne(new LambdaQueryWrapper<SubmitData>()
                        .ge(SubmitData::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerQueryTime()))
                        .eq(SubmitData::getId, submitId)
                        .last(" limit 1")
                    );
                    if (Objects.nonNull(submitData)){
                        pid = submitData.getPid();
                        ip = submitData.getIp();
                        log.info("校验pv的ip是否在配置的IP区域来源范围内 submitId = {}, pid = {}, ip = {}", submitId, pid, ip);
                    }
                }
            }
            // 将Integer数组转换为String列表
            List<String> provinceList = Objects.nonNull(ipRegionalSourceProvince) ? Arrays.stream(ipRegionalSourceProvince).map(Object::toString).collect(Collectors.toList()) : new ArrayList<>();
            List<String> cityList = Objects.nonNull(ipRegionalSourceCity) ? Arrays.stream(ipRegionalSourceCity).map(Object::toString).collect(Collectors.toList()): new ArrayList<>();
            log.info("判断所属ip是否在配置的区域来源中, pid = {}, ipRegionalSourceProvince: {}, ipRegionalSourceCity: {}", pid, Arrays.toString(ipRegionalSourceProvince), Arrays.toString(ipRegionalSourceCity));
            if (Objects.nonNull(uploadDataDTO) && (provinceList.size() > 0 || cityList.size() > 0)) {
                 ip = uploadDataDTO.getIp();
                if (StringUtils.isBlank(ip)){
                    log.info("ip为空，根据pid查询对应的ip，pid = {}", pid);
                    if (StringUtils.isNotBlank(pid)) {
                        PageViewInfo pageViewInfo = pageViewInfoService.queryByPid(pid);
                        if (Objects.nonNull(pageViewInfo)) {
                            ip = pageViewInfo.getIp();
                            log.info("ip为空，根据pid查询对应的ip，pid = {}, ip = {}", pid, ip);
                        }
                    }
                }
                log.info("判断所属ip是否在配置的区域来源中,pv的ip = {}", ip);
                if (StringUtils.isNotBlank(ip)) {
                    String[] ipCode = IPUtil.caaFindIp(ip);
                    log.info("判断所属ip是否在配置的区域来源中, ipCode = {}", ipCode);
                    if (Objects.nonNull(ipCode) && ipCode.length > 0) {
                        boolean res = Arrays.stream(ipCode).anyMatch(code -> provinceList.contains(code) || cityList.contains(code));
                        log.info("判断所属ip是否在配置的区域来源中, res = {}", res);
                        return res;
                    }
                }
            }
        }catch (Exception e){
            log.error("判断所属ip是否在配置的区域来源中, 异常", e);
        }
        //默认是合法的
        return true;
    }


    /**
     * 作用： 检查上报条件时间与成功添加企业微信时间是否超过设定时间的时间范围，是的话，不进行标签上报
     * @param checkUploadTimeAddWechatSuccessTimeTimeUnit 配置的时间单位
     * @param checkUploadTimeAddWechatSuccessTime 配置的时间数值
     * @param addWechatSuccessTime 成功加粉的时间
     * @return 判断的结果
     */
    public boolean checkTimeDiff(String checkUploadTimeAddWechatSuccessTimeTimeUnit, Integer checkUploadTimeAddWechatSuccessTime, Long addWechatSuccessTime){
        if (!Objects.isNull(checkUploadTimeAddWechatSuccessTime) && Objects.equals(0, checkUploadTimeAddWechatSuccessTime)) {
            if (addWechatSuccessTime < DateTimeUtil.getMidnightTimestampOfToday()) {
                return false;
            }
            return true;
        }
        try {
            boolean res = false;
            // 计算相差的间隔
            double between = 0;
            log.info("检查上报条件时间与成功添加企业微信时间是否超过设定时间的时间范围,checkUploadTimeAddWechatSuccessTimeTimeUnit={}, checkUploadTimeAddWechatSuccessTime ={}, addWechatSuccessTime = {}",
                checkUploadTimeAddWechatSuccessTimeTimeUnit, checkUploadTimeAddWechatSuccessTime, addWechatSuccessTime);
            //需要对单位进行判断
            if (StringUtils.isNotBlank(checkUploadTimeAddWechatSuccessTimeTimeUnit) && Objects.nonNull(checkUploadTimeAddWechatSuccessTime) && Objects.nonNull(addWechatSuccessTime)) {
                //当前的时间
                Instant nowTime = Instant.now();
                //加粉的时间
                Instant addWechatSuccessTimeInstant = Instant.ofEpochMilli(addWechatSuccessTime);
                //加粉后，最晚允许打标签的时间
                Instant limitTime = DateTimeUtil.getAfterDayMillisecondByTimeUnit(addWechatSuccessTimeInstant,checkUploadTimeAddWechatSuccessTimeTimeUnit,checkUploadTimeAddWechatSuccessTime);
                log.info("检查上报条件时间与成功添加企业微信时间是否超过设定时间的时间范围,nowTime={}, addWechatSuccessTimeInstant={}, limitTime = {}",nowTime, addWechatSuccessTimeInstant, limitTime);
                if (Objects.nonNull(limitTime) && limitTime.isAfter(nowTime)){
                    //当前时间在限制的时间范围前，允许上报
                    return true;
                }else{
                    between = timeBetween(limitTime, nowTime, checkUploadTimeAddWechatSuccessTimeTimeUnit, checkUploadTimeAddWechatSuccessTime);
                    res = between >= 0 && between < 1;
                    log.info("检查上报条件时间与成功添加企业微信时间是否超过设定时间的时间范围结果,between={}, res = {}", between, res);
                }
            }
            return res;
        }catch (Exception e){
            log.error("检查上报条件时间与成功添加企业微信时间是否超过设定时间的时间范围，出现异常,checkUploadTimeAddWechatSuccessTimeTimeUnit={}, checkUploadTimeAddWechatSuccessTime ={}, addWechatSuccessTime = {}", checkUploadTimeAddWechatSuccessTimeTimeUnit, checkUploadTimeAddWechatSuccessTime, addWechatSuccessTime, e);
        }
        return false;
    }


    public ResultBean<Object> doUpload(CustomerUploadRecord uploadRecord, final String actionType, final UploadEventType uploadEventType, UploadConfiguration uploadConfiguration, UploadDataDTO uploadDataDTO, Customer customer) {
        log.info("触发事件25.0.1 获取锁加锁是为了避免新数据被旧数据覆盖保证幂等性 customer={}；actionType={}；uploadEventType={}；uploadConfiguration={}；uploadDataDTO={}；uploadRecord={}；", !Objects.isNull(customer) ? JSONObject.toJSONString(customer) : customer, actionType, uploadEventType, !Objects.isNull(uploadConfiguration) ? JSONObject.toJSONString(uploadConfiguration) : uploadConfiguration, !Objects.isNull(uploadDataDTO) ? JSONObject.toJSONString(uploadDataDTO) : uploadDataDTO, !Objects.isNull(uploadRecord) ? JSONObject.toJSONString(uploadRecord) : uploadRecord);
        uploadRecord.setActionType(actionType);
        final Long submitDataId = !Objects.isNull(uploadDataDTO.getSubmitDataId()) ? uploadDataDTO.getSubmitDataId() : uploadDataDTO.getFormMatchAddWechatSubmitDataId();
        final Platform platformId = uploadConfiguration.getPlatformId();
        //所有的上报都遵循如下代码逻辑（1.141.0版本添加）：1、相同ClickID点击参数 + 不同上报目标类型，可重复上报；2、相同ClickID点击参数上报目标类型相同仅上报一次；
        final String clickId = UrlUtils.getClickIdByPlatform(uploadDataDTO.getUrl(), uploadDataDTO.getReferrer());
        if (StringUtils.isBlank(clickId)) {
            return new ResultBean<>(ResponseCode.FAIL);
        }
        RLock fairLock = null;
        try {
            //获取锁 加锁是为了避免新数据被旧数据覆盖，保证幂等性
            fairLock = redissonClient.getFairLock(RedisConstant.SUBMIT_DATA_UPLOAD_PLATFORM_LOCK_TIME + (platformId + actionType + clickId));
            //尝试加锁，最多等待10秒
            boolean res = fairLock.tryLock(agentConf.getUploadPlatformLockTime(), agentConf.getUploadPlatformLockTime() + 5L, TimeUnit.SECONDS);
            if (!res) {
                log.error("客资上报获取redis锁失败2600 uploadRecord={}；actionType={}；uploadEventType={}；uploadConfiguration={}；customer={}", JSONObject.toJSONString(uploadRecord), actionType, uploadEventType, JSONObject.toJSONString(uploadConfiguration), (Objects.isNull(customer) ? null : JSONObject.toJSONString(customer)));
                throw new RestException("客资上报获取redis锁失败");
            }
            log.info("触发事件25.0.2 获取到并发锁开始执行上报流程 customer={}；actionType={}；uploadEventType={}；clickId={}；", !Objects.isNull(customer) ? JSONObject.toJSONString(customer) : customer, actionType, uploadEventType, clickId);
            ResponseCode responseCode = ResponseCode.EQUAL_TYPE_UN_UPLOAD;
            int count = customerUploadRecordService.count(new LambdaQueryWrapper<CustomerUploadRecord>()
                .ge(CustomerUploadRecord::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerUploadRecordQueryTime()))
                .eq(CustomerUploadRecord::getClickId, clickId)
                .eq(CustomerUploadRecord::getActionType, actionType)
                .eq(CustomerUploadRecord::getUploadState, UploadStateType.SUCCESS_REPORT)
            );
            if (count > 0) {
                //处理两次深转问题：非广告平台访问，unionid匹配访问来源成功，已在其他页面进行过深度转化，不上报
                if (!Objects.isNull(uploadDataDTO.getIsMatchingPageViewInfoUpload()) && uploadDataDTO.getIsMatchingPageViewInfoUpload()) {
                    responseCode = ResponseCode.NOT_MARKETING_AD_AND_IS_DEEP_CONVERSION;
                    log.info("触发事件22.0: {} pid: {} uploadDto.getSubmitDataId: {}  {} url:{}", uploadEventType, uploadDataDTO.getPid(), submitDataId, responseCode.getMsg(), uploadDataDTO.getUrl());
                }
                //已记录过重复上报记录，将不再记录
                int uploadedCount = customerUploadRecordService.count(new LambdaQueryWrapper<CustomerUploadRecord>()
                    .ge(CustomerUploadRecord::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerUploadRecordQueryTime()))
                    .eq(CustomerUploadRecord::getClickId, clickId)
                    .eq(CustomerUploadRecord::getActionType, actionType)
                    .eq(CustomerUploadRecord::getRecordCode, responseCode.getCode())
                );
                if (uploadedCount > 0) {
                    log.info("触发事件22.1: {} pid: {} uploadDto.getSubmitDataId: {}  {} url:{}", uploadEventType, uploadDataDTO.getPid(), submitDataId, responseCode.getMsg(), uploadDataDTO.getUrl());
                    return new ResultBean<>(ResponseCode.SUCCESS);
                }
                log.info("触发事件20.0: {} pid: {} uploadDto.getSubmitDataId: {}  {} url：{}", uploadEventType, uploadDataDTO.getPid(), submitDataId, responseCode.getMsg(), uploadDataDTO.getUrl());
                return this.saveRecordAndLog(customer, uploadRecord, responseCode);
            } else {
                // 上报记录校验：如已上报，则不上报
                count = customerUploadRecordService.count(new LambdaQueryWrapper<CustomerUploadRecord>()
                    .ge(CustomerUploadRecord::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerUploadRecordQueryTime()))
                    .eq(CustomerUploadRecord::getPid, uploadDataDTO.getPid())
                    .eq(CustomerUploadRecord::getSubmitType, uploadEventType)
                    .eq(CustomerUploadRecord::getActionType, actionType)
                    .eq(CustomerUploadRecord::getUploadState, UploadStateType.SUCCESS_REPORT)
                );
                if (count > 0) {
                    int uploadedCount = customerUploadRecordService.count(new LambdaQueryWrapper<CustomerUploadRecord>()
                        .ge(CustomerUploadRecord::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerUploadRecordQueryTime()))
                        .eq(CustomerUploadRecord::getPid, uploadDataDTO.getPid())
                        .eq(CustomerUploadRecord::getSubmitType, uploadEventType)
                        .eq(CustomerUploadRecord::getActionType, actionType)
                        .eq(CustomerUploadRecord::getRecordCode, ResponseCode.REPEAT_UPLOAD_ERROR.getCode())
                    );
                    if (uploadedCount > 0) {
                        log.info("触发事件21.1: {} pid: {} uploadDto.getSubmitDataId: {}  {}", uploadEventType, uploadDataDTO.getPid(), submitDataId, ResponseCode.REPEAT_UPLOAD_ERROR.getMsg());
                        return new ResultBean<>(ResponseCode.SUCCESS);
                    }
                    log.info("触发事件21.0: {} pid: {} uploadDto.getSubmitDataId: {}  {}", uploadEventType, uploadDataDTO.getPid(), submitDataId, ResponseCode.REPEAT_UPLOAD_ERROR.getMsg());
                    return this.saveRecordAndLog(customer, uploadRecord, ResponseCode.REPEAT_UPLOAD_ERROR);
                }
            }
            UploadService uploadService = PLATFORM_UPLOAD.get(platformId.getId());
            log.info("触发事件23.1.0:开始根据不同媒体执行上报  uploadDataDTO={}；uploadRecord={}；actionType={}；uploadEventType={}；uploadConfiguration={}；customer={}", JSONObject.toJSONString(uploadDataDTO), JSONObject.toJSONString(uploadRecord), actionType, uploadEventType, JSONObject.toJSONString(uploadConfiguration), (Objects.isNull(customer) ? null : JSONObject.toJSONString(customer)));
            UploadResponse uploadResponse = uploadService.uploadData(uploadDataDTO);
            uploadRecord.setSendData(uploadResponse.getSendData()).setResultData(uploadResponse.getResultData()).setDescription(uploadResponse.getDescription());
            log.info("触发事件23.1.1:开始根据不同媒体执行上报，uploadDataDTO={}；uploadRecord={}；uploadResponse={}", uploadDataDTO, uploadRecord, uploadResponse);
            if (!uploadResponse.getUplodState()) {
                if (!Objects.isNull(uploadResponse.getResponseCode())) {
                    uploadRecord.setDescription(uploadResponse.getResponseCode().getMsg());
                    return this.saveRecordAndLog(customer, uploadRecord, uploadResponse.getResponseCode().getCode(), uploadResponse.getResponseCode().getMsg());
                }
                //进行巨量callback无效上报
                callBackInvalidMakeTag(uploadRecord, customer, uploadResponse);
                return this.saveRecordAndLog(customer, uploadRecord, ResponseCode.PLATFORM_UPLOAD_ERROR.getCode(), StringUtils.isNotBlank(StringUtils.trim(uploadResponse.getDescription())) ? StringUtils.trim(uploadResponse.getDescription()) : ResponseCode.PLATFORM_UPLOAD_ERROR.getMsg());
            }
            // 保存上报成功记录
            customerUploadRecordService.save(uploadRecord.setUuidKey(CommonUtil.getUuidReplaceAll()).setRecordCode(ResponseCode.SUCCESS.getCode()).setUploadState(UploadStateType.SUCCESS_REPORT));
            //发送媒体上报数 - 统计
            pageViewInfoSender.sendUpdateAdUploadStatus(uploadDataDTO.getPid());

            try {
                //保存上报关联信息表
                saveUpload(customer, uploadRecord);
            } catch (Exception e) {
                log.error("触发事件24:add or update customer_upload_association error! uploadRecord:{}；message：{}", uploadRecord, e.getMessage(), e);
            }
            // 更新客资上报状态
            if (!Objects.isNull(submitDataId)) {
                LambdaUpdateWrapper<Customer> lambdaUpdateWrapper = new LambdaUpdateWrapper<Customer>()
                    .ge(Customer::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerUpdateTime()))
                    .eq(Customer::getSubmitDataId, submitDataId)
                    .set(Customer::getUploadState, UploadStateType.SUCCESS_REPORT).set(Customer::getActionType, actionType)
                    .set(Customer::getUploadPlatformId, uploadDataDTO.getUploadPlatformId()).set(Customer::getPromotionType, uploadDataDTO.getPromotionType());
                if (!Objects.isNull(uploadDataDTO.getIsMatchingPageViewInfoUpload()) && uploadDataDTO.getIsMatchingPageViewInfoUpload()) {
                    //#20372 【YIYE_AGENT_V1.159.0】巨量联调通过公众号蓝链添加好友后客资来源显示"未知" https://ones.yiye.ai/project/#/team/WtsduTeT/task/EvybVgBAk7JZKGVT
                    lambdaUpdateWrapper.set(Customer::getPlatformSources, uploadConfiguration.getPlatformId());
                    Customer cus = customerService.getOne(new LambdaQueryWrapper<Customer>()
                        .ge(Customer::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerQueryTime()))
                        .eq(Customer::getSubmitDataId, submitDataId).last(" limit 1"));
                    log.info("巨量联调通过公众号蓝链添加好友后客资来源显示1.2.0 cus={}；customer={}；uploadRecord={}；code={}；msg={}；", cus, customer, uploadRecord, UploadStateType.SUCCESS_REPORT.getId(), UploadStateType.SUCCESS_REPORT.getName());
                    if (!Objects.isNull(cus)) {
                        LambdaUpdateWrapper<SubmitData> submitDataLambdaUpdateWrapper = new LambdaUpdateWrapper<SubmitData>()
                            .set(SubmitData::getPlatformSources, uploadRecord.getPlatformId())
                            .ge(SubmitData::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerUpdateTime()))
                            .eq(SubmitData::getId, submitDataId)
                            ;
                        log.info("巨量联调通过公众号蓝链添加好友后客资来源显示1.2.1 cus={}；customer={}；uploadRecord={}；code={}；msg={}；", cus, customer, uploadRecord, UploadStateType.SUCCESS_REPORT.getId(), UploadStateType.SUCCESS_REPORT.getName());
                        lambdaUpdateWrapper.set(Customer::getReferrer, UrlUtils.appendMatchingUrlParamsToBeforeUrl(cus.getReferrer(), uploadRecord.getReferrer(), UrlUtils.WECHAT_OFFICIAL_REPLACE_URL_IGNORE_OR_SAVE_LIST));
                        submitDataLambdaUpdateWrapper.set(SubmitData::getReferrer, UrlUtils.appendMatchingUrlParamsToBeforeUrl(cus.getReferrer(), uploadRecord.getReferrer(), UrlUtils.WECHAT_OFFICIAL_REPLACE_URL_IGNORE_OR_SAVE_LIST));
                        if (StringUtils.isNotBlank(StringUtils.trim(cus.getUrl()))) {
                            String url = UrlUtils.appendMatchingUrlParamsToBeforeUrl(cus.getUrl(), uploadRecord.getUrl(), UrlUtils.WECHAT_OFFICIAL_REPLACE_URL_IGNORE_OR_SAVE_LIST);
                            lambdaUpdateWrapper.set(Customer::getUrl, url).set(Customer::getMatchingBeforeUrl, cus.getUrl()).set(Customer::getMatchingBeforeReferrer, cus.getReferrer());
                            submitDataLambdaUpdateWrapper.set(SubmitData::getUrl, url).set(SubmitData::getMatchingBeforeUrl, cus.getUrl()).set(SubmitData::getMatchingBeforeReferrer, cus.getReferrer());
                        }
                        if (StringUtils.isNotBlank(StringUtils.trim(cus.getWechatAppletLandingPageViewUrl()))) {
                            String wechatAppletUrl = UrlUtils.appendMatchingUrlParamsToBeforeUrl(cus.getWechatAppletLandingPageViewUrl(), uploadRecord.getUrl(), UrlUtils.WECHAT_OFFICIAL_REPLACE_URL_IGNORE_OR_SAVE_LIST);
                            lambdaUpdateWrapper.set(Customer::getWechatAppletLandingPageViewUrl, wechatAppletUrl)
                                .set(Customer::getMatchingBeforeUrl, cus.getWechatAppletLandingPageViewUrl()).set(Customer::getMatchingBeforeReferrer, cus.getReferrer());
                            submitDataLambdaUpdateWrapper.set(SubmitData::getWechatAppletLandingPageViewUrl, wechatAppletUrl)
                                .set(SubmitData::getMatchingBeforeUrl, cus.getWechatAppletLandingPageViewUrl()).set(SubmitData::getMatchingBeforeReferrer, cus.getReferrer());
                        }
                        log.info("巨量联调通过公众号蓝链添加好友后客资来源显示1.2.2 cus={}；customer={}；uploadRecord={}；code={}；msg={}；", cus, customer, uploadRecord, UploadStateType.SUCCESS_REPORT.getId(), UploadStateType.SUCCESS_REPORT.getName());
                        submitDataService.update(submitDataLambdaUpdateWrapper);
                    }
                }
                customerService.update(lambdaUpdateWrapper);
            }
        } catch (Exception e) {
            log.error("客资上报释放redis锁异常2601 uploadRecord={}；actionType={}；uploadEventType={}；uploadConfiguration={}；customer={}", JSONObject.toJSONString(uploadRecord), actionType, uploadEventType, JSONObject.toJSONString(uploadConfiguration), (Objects.isNull(customer) ? null : JSONObject.toJSONString(customer)), e);
            return new ResultBean<>(ResponseCode.FAIL);
        } finally {
            try {
                if (fairLock != null) {
                    fairLock.unlock();
                }
            } catch (Exception e) {
                log.error("客资上报释放redis锁异常2602 uploadRecord={}；actionType={}；uploadEventType={}；uploadConfiguration={}；customer={}", JSONObject.toJSONString(uploadRecord), actionType, uploadEventType, JSONObject.toJSONString(uploadConfiguration), (Objects.isNull(customer) ? null : JSONObject.toJSONString(customer)), e);
            }
        }
        return new ResultBean<>(ResponseCode.SUCCESS);
    }


    /**
     * 巨量上报，检查是否返回callback无效，是的话，进行打标签
     * @param customerUploadRecord
     */
    public void callBackInvalidMakeTag(CustomerUploadRecord customerUploadRecord, Customer customer, UploadResponse uploadResponse){
       try{
           if (Objects.isNull(customerUploadRecord) || Objects.isNull(customer)){
               log.info("巨量上报，检查是否返回callback无效, 参数不合法，不进行打标签");
               return;
           }
           Platform platform = customerUploadRecord.getPlatformId();
           Integer recordCode = customerUploadRecord.getRecordCode();
           boolean uploadState = uploadResponse.getUplodState();
           String description  =uploadResponse.getDescription();
           log.info("巨量上报，检查是否返回callback无效， platform = {}, recordCode = {}, uploadState = {}, description = {}", platform, recordCode, uploadState, description);
           if (!uploadState && StringUtils.isNotBlank(description)) {
               if (Objects.equals(platform, Platform.OCEAN_ENGINE)) {
                   if (description.contains("callback无效")) {
                       log.info("巨量上报，检查是否返回callback无效, customer = {}", JSONObject.toJSONString(customer));
                       String externalUserId = customer.getWechatAppletExternalUserid();
                       String agentId = TenantContextHolder.get();
                       String corpId = customer.getCorpid();
                       Sex sex = customerUploadRecord.getSex();
                       String wechatAppletUserid = customer.getWechatAppletUserid();
                       CallBackInvalidMakeTagDTO dto = new CallBackInvalidMakeTagDTO();
                       //封装信息，进行打标签
                       dto.setSex(sex).setExternalUserId(externalUserId).setAgentId(agentId).setCorpId(corpId).setWechatAppletUserid(wechatAppletUserid);
                       log.info("巨量上报，检查是否返回callback无效， platform = {}, 封装信息，进行打标签, dto = {}", platform, JSONObject.toJSONString(dto));
                       submitDataSender.uploadSuccessMakeCallbackInvalidTag(dto);
                   }
               }
           }
       }catch (Exception e){
           log.error("巨量上报，检查是否返回callback无出现异常, customerUploadRecord = {}", e, JSONObject.toJSONString(customerUploadRecord));
       }
    }

    /**
     * 将对应的上报记录保存至上报记录关联表中
     */
    private void saveUpload(Customer customer, CustomerUploadRecord uploadRecord) {
        CustomerUploadAssociationSaveDto customerUploadAssociationSaveDto = new CustomerUploadAssociationSaveDto();
        customerUploadAssociationSaveDto.setCustomer(customer);
        customerUploadAssociationSaveDto.setUploadRecord(uploadRecord);
        pageViewInfoSender.updateUploadSubmitNext(customerUploadAssociationSaveDto);
    }

    /**
     * 驼峰转下划线
     *
     * @param jsonObject   每个上报实体
     * @param uploadRecord 上报记录
     */
    private JSONObject humpCase(JSONObject jsonObject, CustomerUploadRecord uploadRecord) {
        if (ObjectUtils.isEmpty(jsonObject)){
            return null;
        }
        JSONObject result = new JSONObject();
        //特殊转换
        UploadEventType submitType = uploadRecord.getSubmitType();
        Integer submitTypeInteger = ObjectUtils.isEmpty(submitType) ? null : submitType.getValue();
        Platform platformId = uploadRecord.getPlatformId();
        Integer platformIdInteger = ObjectUtils.isArray(platformId) ? null : platformId.getId();
        UploadStateType uploadState = uploadRecord.getUploadState();
        Integer uploadStateInteger = ObjectUtils.isEmpty(uploadState) ? null : uploadState.getId();
        Instant createdAt = uploadRecord.getCreatedAt();
        String createdAtTime = ObjectUtils.isEmpty(createdAt) ? null : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date.from(createdAt));
        jsonObject.put("submitType", submitTypeInteger);
        jsonObject.put("platformId", platformIdInteger);
        jsonObject.put("uploadState", uploadStateInteger);
        jsonObject.put("createdAt", createdAtTime);
        jsonObject.entrySet().forEach(e->{
            String key = e.getKey();
            String s = HumpUtils.xX2x_x(key);
            result.put(s,e.getValue());
        });
        return result;
    }

    /**
     * 更新JS客资上报状态（快手）
     */
    private ResultBean<Object> updateJsUploadState(Long submitDataId) {
        if (Objects.nonNull(submitDataId)) {
            customerService.update(new LambdaUpdateWrapper<Customer>()
                .ge(Customer::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerUpdateTime()))
                .eq(Customer::getSubmitDataId, submitDataId)
                .set(Customer::getUploadState, UploadStateType.JS_SUCCESS_REPORT));
        }
        log.info("触发事件25:js don't upload ! {} ", ResponseCode.JS_SUCCESS_REPORT.getMsg());
        return new ResultBean<>(ResponseCode.JS_SUCCESS_REPORT);
    }

    /**
     * 获取表单、订单电话号码
     */
    private String getPhone(SubmitData submitData) {
        String phone = null;
        if (SubmitType.FORM.equals(submitData.getSubmitType())) {
            SubmitDataFormExt submitDataFormExt = JSONObject.toJavaObject(submitData.getFormExt(), SubmitDataFormExt.class);
            phone = Objects.isNull(submitDataFormExt) ? null : submitDataFormExt.getPhone();
        } else if (SubmitType.ORDER.equals(submitData.getSubmitType())) {
            SubmitDataOrderExt submitDataorderExt = JSONObject.toJavaObject(submitData.getOrderExt(), SubmitDataOrderExt.class);
            phone = Objects.isNull(submitDataorderExt) ? null : submitDataorderExt.getPhone();
        }
        return phone;
    }

    private String getName(SubmitData submitData) {
        String name = null;
        if (SubmitType.FORM.equals(submitData.getSubmitType())) {
            SubmitDataFormExt submitDataFormExt = JSONObject.toJavaObject(submitData.getFormExt(), SubmitDataFormExt.class);
            name = Objects.isNull(submitDataFormExt) ? null : submitDataFormExt.getName();
        } else if (SubmitType.ORDER.equals(submitData.getSubmitType())) {
            SubmitDataOrderExt submitDataorderExt = JSONObject.toJavaObject(submitData.getOrderExt(), SubmitDataOrderExt.class);
            name = Objects.isNull(submitDataorderExt) ? null : submitDataorderExt.getName();
        }
        return name;
    }

    private String getEmail(SubmitData submitData) {
        String email = null;
        if (SubmitType.FORM.equals(submitData.getSubmitType())) {
            SubmitDataFormExt submitDataFormExt = JSONObject.toJavaObject(submitData.getFormExt(), SubmitDataFormExt.class);
            email = Objects.isNull(submitDataFormExt) ? null : submitDataFormExt.getEmail();
        } else if (SubmitType.ORDER.equals(submitData.getSubmitType())) {
            SubmitDataOrderExt submitDataorderExt = JSONObject.toJavaObject(submitData.getOrderExt(), SubmitDataOrderExt.class);
            email = Objects.isNull(submitDataorderExt) ? null : submitDataorderExt.getEmail();
        }
        return email;
    }

    /**
     * 保存上报记录(上报失败)及异常日志输出
     */
    public ResultBean<Object> saveRecordAndLog(Customer customer,CustomerUploadRecord uploadRecord, ResponseCode responseCode) {
        return this.saveRecordAndLog(customer, uploadRecord, responseCode.getCode(), responseCode.getMsg());
    }

    @Autowired
    private CustomerUploadRecordServiceRedis customerUploadRecordServiceRedis;

    public ResultBean<Object> saveRecordAndLog(Customer customer, final CustomerUploadRecord uploadRecord, final Integer code, final String msg) {
        customerUploadRecordService.save(uploadRecord.setUuidKey(CommonUtil.getUuidReplaceAll()).setRecordCode(code).setDescription(msg));
        if (StringUtils.isNotBlank(uploadRecord.getClickId())) {
            //上报失败，设置缓存（判断是否广告来源）
            advertiseRedis.setUploadFail(uploadRecord.getClickId(), code);
            customerUploadRecordServiceRedis.saveWoaTheSameClickidError(uploadRecord.getClickId(), code);
        }
        final Long submitDataId = !Objects.isNull(uploadRecord.getSubmitDataId()) ? uploadRecord.getSubmitDataId() : uploadRecord.getFormMatchAddWechatSubmitDataId();
        if (Objects.nonNull(submitDataId)) {
            LambdaUpdateWrapper<Customer> lambdaUpdateWrapper = new LambdaUpdateWrapper<Customer>()
                .ge(Customer::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerUpdateTime()))
                .eq(Customer::getSubmitDataId, submitDataId)
                .set(Customer::getFailDescription, msg)
                .set(Customer::getActionType, uploadRecord.getActionType())
                ;
            //客资上报记录推送wehook：扣量上报，巨量引擎未符合媒体流量来源，标签及会话存档超过设定时间未上报的"uploadState", "上报状态统一为未上报；需求地址：#35762 WEBHOOK支持按上报事件状态进行推送 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PHVqcQyFPcdvQxDF
            if (ResponseCode.SEND_WEB_HOOK_RELOAD_UPLOAD_LIST.contains(uploadRecord.getRecordCode()) || ResponseCode.SEND_WEB_HOOK_RELOAD_UPLOAD_LIST.contains(code)) {
                lambdaUpdateWrapper.set(Customer::getUploadState, UploadStateType.NOT_REPORT);
            } else {
                lambdaUpdateWrapper.set(Customer::getUploadState, UploadStateType.FAIL_REPORT);
            }
            if (!Objects.isNull(uploadRecord.getIsMatchingPageViewInfoUpload()) && uploadRecord.getIsMatchingPageViewInfoUpload()) {
                LambdaUpdateWrapper<SubmitData> submitDataLambdaUpdateWrapper = new LambdaUpdateWrapper<SubmitData>()
                    .ge(SubmitData::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerUpdateTime()))
                    .eq(SubmitData::getId, submitDataId);
                //#20372 【YIYE_AGENT_V1.159.0】巨量联调通过公众号蓝链添加好友后客资来源显示"未知" https://ones.yiye.ai/project/#/team/WtsduTeT/task/EvybVgBAk7JZKGVT
                lambdaUpdateWrapper.set(Customer::getPlatformSources, uploadRecord.getPlatformId());
                submitDataLambdaUpdateWrapper.set(SubmitData::getPlatformSources, uploadRecord.getPlatformId());
                Customer cus = customerService.getOne(new LambdaQueryWrapper<Customer>()
                    .ge(Customer::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerQueryTime()))
                    .eq(Customer::getSubmitDataId, submitDataId).last(" limit 1"));
                log.info("巨量联调通过公众号蓝链添加好友后客资来源显示1.2.0 cus={}；customer={}；uploadRecord={}；code={}；msg={}；", cus, customer, uploadRecord, code, msg);
                if (!Objects.isNull(cus)) {
                    log.info("巨量联调通过公众号蓝链添加好友后客资来源显示1.2.1 cus={}；customer={}；uploadRecord={}；code={}；msg={}；", cus, customer, uploadRecord, UploadStateType.SUCCESS_REPORT.getId(), UploadStateType.SUCCESS_REPORT.getName());
                    lambdaUpdateWrapper.set(Customer::getReferrer, UrlUtils.appendMatchingUrlParamsToBeforeUrl(cus.getReferrer(), uploadRecord.getReferrer(), UrlUtils.WECHAT_OFFICIAL_REPLACE_URL_IGNORE_OR_SAVE_LIST));
                    submitDataLambdaUpdateWrapper.set(SubmitData::getReferrer, UrlUtils.appendMatchingUrlParamsToBeforeUrl(cus.getReferrer(), uploadRecord.getReferrer(), UrlUtils.WECHAT_OFFICIAL_REPLACE_URL_IGNORE_OR_SAVE_LIST));
                    if (StringUtils.isNotBlank(StringUtils.trim(cus.getUrl()))) {
                        String url = UrlUtils.appendMatchingUrlParamsToBeforeUrl(cus.getUrl(), uploadRecord.getUrl(), UrlUtils.WECHAT_OFFICIAL_REPLACE_URL_IGNORE_OR_SAVE_LIST);
                        lambdaUpdateWrapper.set(Customer::getUrl, url).set(Customer::getMatchingBeforeUrl, cus.getUrl()).set(Customer::getMatchingBeforeReferrer, cus.getReferrer());
                        submitDataLambdaUpdateWrapper.set(SubmitData::getUrl, url).set(SubmitData::getMatchingBeforeUrl, cus.getUrl()).set(SubmitData::getMatchingBeforeReferrer, cus.getReferrer());
                    }
                    if (StringUtils.isNotBlank(StringUtils.trim(cus.getWechatAppletLandingPageViewUrl()))) {
                        String wechatAppletUrl = UrlUtils.appendMatchingUrlParamsToBeforeUrl(cus.getWechatAppletLandingPageViewUrl(), uploadRecord.getUrl(), UrlUtils.WECHAT_OFFICIAL_REPLACE_URL_IGNORE_OR_SAVE_LIST);
                        lambdaUpdateWrapper.set(Customer::getWechatAppletLandingPageViewUrl, wechatAppletUrl)
                            .set(Customer::getMatchingBeforeUrl, cus.getWechatAppletLandingPageViewUrl()).set(Customer::getMatchingBeforeReferrer, cus.getReferrer());
                        submitDataLambdaUpdateWrapper.set(SubmitData::getWechatAppletLandingPageViewUrl, wechatAppletUrl)
                            .set(SubmitData::getMatchingBeforeUrl, cus.getWechatAppletLandingPageViewUrl()).set(SubmitData::getMatchingBeforeReferrer, cus.getReferrer());
                    }
                    log.info("巨量联调通过公众号蓝链添加好友后客资来源显示1.2.2 cus={}；customer={}；uploadRecord={}；code={}；msg={}；", cus, customer, uploadRecord, UploadStateType.SUCCESS_REPORT.getId(), UploadStateType.SUCCESS_REPORT.getName());
                }
                submitDataService.update(submitDataLambdaUpdateWrapper);
            }
            customerService.update(lambdaUpdateWrapper);
        }
        log.warn("触发事件26:upload error ! {} ", code);
        try {
            //保存上报关联信息表
            saveUpload(customer, uploadRecord);
        } catch (Exception e) {
            log.error("触发事件27:add or update customer_upload_association error! uploadRecord:{} message:{}", uploadRecord, e.getMessage(), e);
        }
        ResponseCode responseCode = ResponseCode.getEnumById(code);
        return new ResultBean<>(Objects.isNull(responseCode) ? ResponseCode.SUCCESS : responseCode);
    }

    /**
     * 获取上报事件类型
     */
    private UploadEventType getUploadEventType(SubmitData submitData, UploadEventType uploadEventType) {
        SubmitDataOrderExt submitDataOrderExt = submitData.orderExt();
        boolean isZrezoBuy = submitDataService.isZeroBuy(submitDataOrderExt.getPaymentType(), submitDataOrderExt.getDiscountPrice(), submitDataOrderExt.getPrice(), submitDataOrderExt.isUseCoupon());
        if (UploadEventType.ORDER_ORDER_SUBMIT.equals(uploadEventType) && isZrezoBuy) {
            return UploadEventType.ORDER_PAY_SUCCESS;
        }
        return uploadEventType;
    }

    private static final Map<Integer, UploadService> PLATFORM_UPLOAD = new HashMap<>();

    /**
     * 项目启动 - 初始化 - 上报方法对象
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        PLATFORM_UPLOAD.put(Platform.OCEAN_ENGINE.getId(), applicationContext.getBean(OceanUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.TENCENT_AD.getId(), applicationContext.getBean(TencentUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.MP_AD.getId(), applicationContext.getBean(TencentUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.ZHIHU.getId(), applicationContext.getBean(ZhiHuUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.BAIDU.getId(), applicationContext.getBean(BaiDuUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.VIVO.getId(), applicationContext.getBean(VivoUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.OPPO.getId(), applicationContext.getBean(OppoUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.KUAISHOU.getId(), applicationContext.getBean(KuaiShouUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.BILIBILI.getId(), applicationContext.getBean(BilibiliUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.SINA_WEIBO_SUPPER_FANS.getId(), applicationContext.getBean(SinaWeiboSupperFansUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.TUIA.getId(), applicationContext.getBean(TuiaUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.XIAOHONGSHU.getId(), applicationContext.getBean(XiaoHongShuUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.IQIYI.getId(), applicationContext.getBean(IQiYiUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.SOUL.getId(), applicationContext.getBean(SoulUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.ALI_PAY.getId(), applicationContext.getBean(AliPayUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.TIKTOK.getId(), applicationContext.getBean(TiktokUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.FACEBOOK.getId(), applicationContext.getBean(FacebookUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.YOUTH_KANDIAN.getId(), applicationContext.getBean(YouthKandianUploadServiceImpl.class));
        PLATFORM_UPLOAD.put(Platform.XIMALAYA.getId(), applicationContext.getBean(XimalayaUploadServiceImpl.class));
    }

    /**
     * 匹配上报行为类型
     */
    private List<UploadConfigurationTypes> getUploadConfigurationTypes(UploadConfiguration uploadConfiguration, UploadEventType uploadEventType, final UploadDto uploadDto) {
        log.info("开始匹配上报行为类型：uploadConfiguration ====== >> {}；uploadEventType ====== >> {}；uploadDto ====== >> {}", uploadConfiguration, uploadEventType, uploadDto);
        List<UploadConfigurationTypes> ucTypesList = new ArrayList<>();
        // 接口上报：传什么动作类型，报什么
        if (UploadEventType.INTERFACE_UPLOAD.equals(uploadEventType)) {
            ucTypesList.add(new UploadConfigurationTypes().setUploadEventTypeValue(uploadDto.getActionType()));
            return ucTypesList;
        }
        if (UploadEventType.BACKEND_CUSTOMER_UPLOAD.toString().equals(uploadDto.getUploadEventType().toString())) {
            //后链路上报判断
            LambdaQueryWrapper<UploadConfigurationTypes> lambdaQueryWrapper = new LambdaQueryWrapper<UploadConfigurationTypes>()
                .eq(UploadConfigurationTypes::getUploadConfigId, uploadConfiguration.getId())
                .eq(UploadConfigurationTypes::getLandingPageId, uploadConfiguration.getLandingPageId())
                .eq(UploadConfigurationTypes::getChannelId, uploadConfiguration.getChannelId())
                .eq(UploadConfigurationTypes::getCustomerStatusField, uploadDto.getColumnName())
                .eq(UploadConfigurationTypes::getCustomerStatusValue, Integer.valueOf(uploadDto.getColumnValue()))
                .eq(UploadConfigurationTypes::getUploadEventType, uploadEventType)
                .last(" limit 1");
            UploadConfigurationTypes ucTypes = uploadConfigurationTypesService.getOne(lambdaQueryWrapper);
            if (!Objects.isNull(ucTypes)) {
                ucTypesList.add(ucTypes);
            }
        } else if (UploadEventType.WORK_WEIXIN_CUSTOMER_TAG.toString().equals(uploadDto.getUploadEventType().toString())) {
            //企业标签上报
            ucTypesList = Collections.singletonList(uploadConfigurationTypesService.getById(uploadDto.getUploadConfigurationTypesId()));
        } else if(uploadEventType.isWidgetAddEnterpriseWechat()){
            //组件跳转加粉，需要兼容 企业微信添加成功事件
            List<UploadConfigurationTypes> info = uploadConfigurationTypesRedis.getInfos(uploadConfiguration,Arrays.asList(new UploadEventType[]{uploadEventType}) );
            if(!CollectionUtils.isEmpty(info)){
                ucTypesList.addAll(info);
            }else{
                info = uploadConfigurationTypesRedis.getInfos(uploadConfiguration, Arrays.asList(new UploadEventType[]{UploadEventType.ADD_ENTERPRISE_WECHAT_SUCCESS}));
                if(!CollectionUtils.isEmpty(info)){
                    ucTypesList.addAll(info);
                    uploadEventType = UploadEventType.ADD_ENTERPRISE_WECHAT_SUCCESS;
                    uploadDto.setUploadEventType(uploadEventType);
                }
            }
            //taobao dsp 事件上报
        } else if (TAOBAO_DSP_EVENTS.contains(uploadEventType)) {
            //过滤事件类型
            if(Objects.isNull(uploadDto.getTaobaoDspConfigId()) && !UploadEventType.REPORT_MANUALLY.equals(uploadDto.getTriggerUploadType())){
                return new ArrayList<>();
            }
            ucTypesList = uploadConfigurationTypesRedis.getInfos(uploadConfiguration, Arrays.asList(new UploadEventType[]{uploadEventType}));
            // 过滤事件类型下 dsp 账户是否满足 上报账户 && 手动上报场景下不校验数据源
            if (!CollectionUtils.isEmpty(ucTypesList) && !UploadEventType.REPORT_MANUALLY.equals(uploadDto.getTriggerUploadType())) {
                Long advertiserAccountGroupId = uploadConfiguration.getAdvertiserAccountGroupId();
                ucTypesList = ucTypesList.stream().filter(type -> {
                    TaobaoDspType taobaoDspType = type.getTaobaoDspType();
                    Long[] taobaoDspConfigIds = type.getTaobaoDspConfigIds();
                    List<Long> configIds = Objects.nonNull(taobaoDspConfigIds) ? Arrays.asList(taobaoDspConfigIds) : new ArrayList<>();
                    if (TaobaoDspType.ALL.equals(taobaoDspType)) {
                        if(Objects.nonNull(advertiserAccountGroupId)){
                            configIds = taobaoDspRedis.getPmpTaobaoDspConfigListCache(advertiserAccountGroupId)
                                .stream().map(TaobaoDspConfig::getId).collect(Collectors.toList());
                        }
                    }
                    return  CollectionUtils.containsAll(configIds, Arrays.asList(uploadDto.getTaobaoDspConfigId()));
                }).collect(Collectors.toList());
            }
        } else {
            ucTypesList = uploadConfigurationTypesRedis.getInfos(uploadConfiguration, Arrays.asList(new UploadEventType[]{uploadEventType}));
        }
        //为空 / 小于等于1条目标转化类型，直接返回结果
        if (CollectionUtils.isEmpty(ucTypesList) || ucTypesList.size() <= 1) {
            return ucTypesList;
        }
        //如果多条上报条件相同，并且【流量来源】也匹配成功，则取【上报配置-上报目标转化类型】排序中的第一条进行上报，相关原型文档地址：http://axure.yiyetech.com/%E5%B7%A8%E9%87%8F%E5%BC%95%E6%93%8E%E7%AB%AF%E5%8F%A3%E6%9D%A5%E6%BA%90%E4%B8%8A%E6%8A%A5/#id=dy7ibt&p=%E6%94%AF%E6%8C%81%E5%B7%A8%E9%87%8F%E5%BC%95%E6%93%8E%E6%8C%89%E5%90%84%E7%AB%AF%E5%8F%A3%E6%9D%A5%E6%BA%90%E4%B8%8A%E6%8A%A5&g=1
        ucTypesList.forEach(e -> e.setCreatedAtTime(Objects.isNull(e.getCreatedAt()) ? 0L : e.getCreatedAt().toEpochMilli()));
        ucTypesList = ucTypesList.stream().sorted(Comparator.comparing(UploadConfigurationTypes::getCreatedAtTime)).collect(Collectors.toList());
        //#31683 多条上报条件的设置项完全相同时视为同一上报条件 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PiB4dM6VaXu9oJoG
        ucTypesList = new CopyOnWriteArrayList<>(ucTypesList);
        for (UploadConfigurationTypes ucTypes : ucTypesList) {
            List<UploadConfigurationTypes> list = ucTypesList.parallelStream().filter(uuu ->
                //不与当前记录进行对比
                !Objects.equals(ucTypes.getId(), uuu.getId()) &&
                //上报事件类型
                Objects.equals(ucTypes.getUploadEventType(), uuu.getUploadEventType()) &&
                //流量来源
                CommonUtil.compareList(ucTypes.getFlowSources(), uuu.getFlowSources()) &&
                //性别
                Objects.equals(ucTypes.getSex(), uuu.getSex()) &&
                //上报比例
                Objects.equals(ucTypes.getUploadRatio(), uuu.getUploadRatio()) &&
                //执行时间
                CommonUtil.compareList(ucTypes.getUplaodTimeAreas(), uuu.getUplaodTimeAreas()) &&
                //上报转化目标类型
                CommonUtil.compareStringList(ucTypes.getUploadEventTypeValue(), uuu.getUploadEventTypeValue()) &&
                //企业微信客户开口次数
                Objects.equals(ucTypes.getWorkWechatOpenNum(), uuu.getWorkWechatOpenNum()) &&
                //大于等于 - 次数
                Objects.equals(ucTypes.getCompareType(), uuu.getCompareType()) &&
                //是否模糊匹配
                Objects.equals(ucTypes.getFuzzyMatchingField(), uuu.getFuzzyMatchingField()) &&
                //匹配关键词（模糊/全匹配）
                CommonUtil.compareStringList(ucTypes.getKeyWord(), uuu.getKeyWord()) &&
                //企业微信tagid
                CommonUtil.compareStringList(ucTypes.getEnterpriseWechatCorpTagId(), uuu.getEnterpriseWechatCorpTagId())
            ).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(list) && list.size() > 0) {
                log.info("触发事件28.0.1 过滤重复上报目标转化类型数据发现重复目标转化类型即将进行删除 uploadDto={}；ucTypes={}；ucTypesList={}；", JSONObject.toJSONString(uploadDto), JSONObject.toJSONString(ucTypes), JSONObject.toJSONString(ucTypesList));
                ucTypesList.remove(ucTypes);
            }
        }
        return ucTypesList;
    }



    /**
     * 计算两个时间之间间隔的分钟数
     * @param startInstant 开始时间
     * @param endInstant 结束时间
     * @return 相差的分钟数
     */
    public static double timeBetween(Instant startInstant, Instant endInstant, String timeUnit,Integer checkUploadTimeAddWechatSuccessTime) {

        log.info("计算两个时间之间间隔的分钟数,startInstant = {}, endInstant = {},timeUnit={},checkUploadTimeAddWechatSuccessTime={} ", startInstant,endInstant, timeUnit,checkUploadTimeAddWechatSuccessTime );
        if (StringUtils.isNotBlank(timeUnit)) {
            // 计算两个Instant之间的Duration
            Duration duration = Duration.between(endInstant, startInstant);

            // 获取Duration的总秒数
            long seconds = duration.getSeconds();

            double mins = 0;

            switch (timeUnit) {
                case "DAYS":
                    mins = (double) Math.abs(seconds) / (3600 * 24);
                    break;
                case "HOURS":
                    mins = (double) Math.abs(seconds) / 3600;
                    break;
                case "MINUTES":
                    mins = (double) Math.abs(seconds) / 60;
                    break;
                default:
                    break;
            }
            log.info("时间间隔 mins = {}, 设置的时间间隔是checkUploadTimeAddWechatSuccessTime={}", mins, checkUploadTimeAddWechatSuccessTime);
            // 返回格式化后的小时数，保留一位小数
           return mins;
        }
        return -1;
    }
//    public static void main(String[] args) {
//        //final Instant todayNow = Instant.now();
//        //final Instant thirtyDaysAgo = todayNow.minus(30, ChronoUnit.DAYS);
//        //log.info("thirtyDaysAgo = {}", thirtyDaysAgo);
//        System.out.println(UrlUtils.getClickIdByPlatform("https://zbyy.sygeyuan.cn/bsld/IZVSxF2k?IDEA_ID=&PLAN_ID=&UNIT_ID=&USER_ID=&bd_vid=PjTvrjfzrjbdnWbsPHcLnHT4rNtdPjNxnNt1nj0sg1PxrjTvn1fzPHRLnjf4&nid=&room_id=9531824392&_cl=87wn&parentPid=02af4634-fd7a-z08b-be7b-011a6c6fd638", "https://zbyy.sygeyuan.cn/bsld/eZDMTfK0?IDEA_ID=%7B%7BIDEA_ID%7D%7D&PLAN_ID=%7B%7BPLAN_ID%7D%7D&UNIT_ID=%7B%7BUNIT_ID%7D%7D&USER_ID=%7B%7BUSER_ID%7D%7D&_cl=ym09&bd_vid=PjTvrjfzrjbdnWbsPHcLnHT4rNtdPjNxnNt1nj0sg1PxrjTvn1fzPHRLnjf4&nid=&room_id=9531824392"));
//    }

    /**
     * 落地页 - 渠道 - 上报配置 - 转化目标类型 - 扣量上报
     */
    public ResponseCode doDeductionReport(final Date date, CustomerUploadRecord uploadRecord, UploadConfigurationTypes uploadConfigurationTypes, UploadConfiguration uploadConfiguration, UploadDataDTO uploadDataDTO1, Platform platform) {
        final TimeAreas timeAreas = TimeAreas.getIndexByDate(date);
        final String dateStr = new SimpleDateFormat("yyyy-MM-dd").format(date);
        final String openAdCampaignUploadKey = uploadConfiguration.getOpenAdCampaignUploadKey();
        final int uploadRatio = (Objects.isNull(uploadConfigurationTypes.getUploadRatio()) || uploadConfigurationTypes.getUploadRatio() == 0) ? 100 : uploadConfigurationTypes.getUploadRatio();
        final Integer[] uploadTimeAreas = (Objects.isNull(uploadConfigurationTypes.getUplaodTimeAreas()) || uploadConfigurationTypes.getUplaodTimeAreas().length <= 0) ? TimeAreas.getIdsArr() : uploadConfigurationTypes.getUplaodTimeAreas();
        final List<String> numberAreaList = CommonUtil.mergeContinuityNumberToSection(uploadTimeAreas);
        final String numAreaStr = TimeAreas.compareNumberContainsSection(numberAreaList, timeAreas.getId());
        uploadRecord.setLpuctId(uploadConfigurationTypes.getId()).setUploadRatio(uploadRatio);
        long countNum;
        if (!Objects.isNull(uploadConfiguration.getOpenAdCampaignUpload()) && uploadConfiguration.getOpenAdCampaignUpload()) {
            final Platform platform1 = Platform.getBaiDuUploadPlatform(uploadConfiguration, platform);
            //广告计划精准归因上报：#52963 上报配置支持按广告维度上报 https://ones.yiye.ai/project/#/team/WtsduTeT/task/PHVqcQyFT3xM9idS
            String campaignId = UrlUtils.getCampaignId(platform1, uploadDataDTO1.getUrl(), uploadDataDTO1.getReferrer());
            if (StringUtils.isBlank(StringUtils.trim(campaignId))) {
                return ResponseCode.IS_KOU_LIANG_UPLOAD_NOT_CAMPAIGN_ID;
            }
            countNum = uploadConfigurationTypesRedis.getOpenAdCampaignUploadNum(uploadConfiguration.getLandingPageId(), uploadConfigurationTypes.getId(), dateStr, numAreaStr, openAdCampaignUploadKey, campaignId);
        } else {
            //普通落地页扣量上报
            countNum = uploadConfigurationTypesRedis.getDeductionQuantityUploadCountNum(uploadConfiguration.getLandingPageId(), uploadConfigurationTypes.getId(), dateStr, numAreaStr);
        }
        final boolean withinUploadRatio = PercentageExecuteUtils.getFlag(uploadRatio, (int) countNum);
        final boolean isInUploadTimeArea = StringUtils.isNotBlank(StringUtils.trim(numAreaStr));
        if (isInUploadTimeArea && !withinUploadRatio) {
            //不在上报比例，在上报时间段内
            return ResponseCode.IS_KOU_LIANG_UPLOAD_UN_IN_RATIO;
        }
        if (!isInUploadTimeArea) {
            if (withinUploadRatio) {
                //在上报比例中，不在上报时间段内
                return ResponseCode.IS_KOU_LIANG_UPLOAD_UN_IN_TIME;
            } else {
                //不在比例中，且不在上报时间段内
                return ResponseCode.IS_KOU_LIANG_UPLOAD_UN_IN_TIME_AND_NOT_IN_RATIO;
            }
        }
        return null;
    }

    public String getUploadTimeAreaStr(CustomerUploadRecord uploadRecord, UploadConfigurationTypes uploadConfigurationTypes) {
        final Integer[] uploadTimeAreas = (Objects.isNull(uploadConfigurationTypes.getUplaodTimeAreas()) || uploadConfigurationTypes.getUplaodTimeAreas().length <= 0) ? TimeAreas.getIdsArr() : uploadConfigurationTypes.getUplaodTimeAreas();
        final List<String> numberAreaList = CommonUtil.mergeContinuityNumberToSection(uploadTimeAreas);
        String timeAreaStr = "";
        for (String nalStr : numberAreaList) {
            String[] spl = nalStr.split("-");
            if (spl.length >= 1) {
                TimeAreas startTime = TimeAreas.getEnumById(Integer.valueOf(spl[0]));
                TimeAreas endTime = TimeAreas.getEnumById(spl.length == 2 ? Integer.valueOf(spl[1]) : Integer.valueOf(spl[0]));
                timeAreaStr += (Objects.isNull(startTime) ? TimeAreas.AREA_TIME_01.getMinTime() : startTime.getMinTime()) + "-" + (Objects.isNull(endTime) ? TimeAreas.AREA_TIME_48.getMaxTime() : endTime.getMaxTime()) + "，";
            }
        }
        return StringUtils.isEmpty(StringUtils.trim(timeAreaStr)) ? timeAreaStr : timeAreaStr.substring(0, timeAreaStr.length() - 1);
    }



    /**
     * 监听到删除企业微信好友的消息,进行上报
     */
    public void resolveCustomerDeleteFriendEvent(CustomerBO customerBO) {

        try {
            if (Objects.isNull(customerBO)) {
                log.error("监听到删除企业微信好友的消息,customerBO为空, 直接结束流程, customerBO = {}", JSONObject.toJSONString(customerBO));
                return;
            }

            final Instant todayNow = Instant.now();
            final Instant beforeNow = todayNow.minus(30, ChronoUnit.DAYS);
            //查找最近一条记录的客资(30天内的)
            Customer customer = customerService.lambdaQuery()
                .eq(Customer::getWechatAppletExternalUserid, customerBO.getWechatAppletExternalUserid())
                .eq(Customer::getWechatAppletUserid, customerBO.getWechatAppletUserid())
                .gt(Customer::getCreatedAt, beforeNow)
                .isNotNull(Customer::getPid)
                .orderByDesc(Customer::getCreatedAt)
                .last(" limit 1").one();
            if (Objects.isNull(customer)) {
                log.info("监听到删除企业微信好友的消息,没有查询到客资信息, customerBO = {}", JSONObject.toJSONString(customerBO));
                return;
            }
            log.info("删除企业微信好友，查询到的客资信息 customer = {}", JSONObject.toJSONString(customer));

            //上报
            this.upload(UploadDto.builder()
                .pid(customer.getPid())
                .addWechatSuccessTime(Objects.nonNull(customer.getCreatedAt()) ? customer.getCreatedAt().toEpochMilli() : Instant.now().toEpochMilli())
                .addEnterpriseWechatStatus(customer.getAddEnterpriseWechatStatus())
                .uploadEventType(UploadEventType.DELETE_ENTERPRISE_WECHAT_FRIEND)
                .uploadType(UploadType.REAL_TIME)
                .build());
            //更新客资信息
            if (Objects.equals(customerBO.getCustomerEventType(), CustomerEventType.EXTERNAL_CONTACT_DELETE_CUSTOMER_SERVICE)) {
                log.info("外部联系人删除客服, 更新客资信息, id = {}", customer.getId());
                customerService.lambdaUpdate().set(Customer::getEventTime, customerBO.getEventTime())
                    .set(Customer::getEventType, CustomerEventType.EXTERNAL_CONTACT_DELETE_CUSTOMER_SERVICE)
                    .set(Customer::getCustomerDeleteServiceTime, customerBO.getEventTime())
                    .ge(Customer::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerUpdateTime()))
                    .eq(Customer::getId, customer.getId())
                    .update();
            } else if (Objects.equals(customerBO.getCustomerEventType(), CustomerEventType.CUSTOMER_SERVICE_DELETE_EXTERNAL_CONTACT)) {
                log.info("客服删除外部联系人, 更新客资信息, id = {}", customer.getId());
                customerService.lambdaUpdate().set(Customer::getEventTime, customerBO.getEventTime())
                    .set(Customer::getEventType, CustomerEventType.CUSTOMER_SERVICE_DELETE_EXTERNAL_CONTACT)
                    .set(Customer::getServiceDeleteCustomerTime, customerBO.getEventTime())
                    .ge(Customer::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerUpdateTime()))
                    .eq(Customer::getId, customer.getId())
                    .update();
            }
        }catch (Exception e){
            log.error("监听到删除企业微信好友的消息,进行上报出现异常, customerBO = {}", JSONObject.toJSONString(customerBO), e);
        }
    }

    public static void main(String[] args) {
        String[] ipCode = {"中国", "广东", "156005000000", "440000"};
        Integer[] ipRegionalSourceProvince = null;
        Integer[] ipRegionalSourceCity = null;

        // 将Integer数组转换为String列表
        List<String> provinceList = Objects.nonNull(ipRegionalSourceProvince) ? Arrays.stream(ipRegionalSourceProvince).map(Object::toString).collect(Collectors.toList()) : new ArrayList<>();
        List<String> cityList = Objects.nonNull(ipRegionalSourceCity) ? Arrays.stream(ipRegionalSourceCity).map(Object::toString).collect(Collectors.toList()): new ArrayList<>();

        boolean res = Arrays.stream(ipCode).anyMatch(code -> provinceList.contains(code) || cityList.contains(code));

        System.out.println("res = " + res);
    }


    //    /**
    //     * 手动补偿上报
    //     * @param customerId
    //     * @param uploadEventType
    //     * @param uploadConfId
    //     * @param uploadConfTypeId
    //     * @return
    //     */
    //    public boolean handUploadExtracted(Long customerId, UploadEventType uploadEventType, Long uploadConfId, Long uploadConfTypeId) {
    //        Customer customer = getCustomer(customerId, uploadEventType, uploadConfId, uploadConfTypeId);
    //        final String pid = customer.getPid();
    //        final String corpid = customer.getCorpid();
    //        final Long submitDataId = customer.getSubmitDataId();
    //        final Long addWechatSuccessTime = customer.getCreatedAt().toEpochMilli();
    //        PageViewInfo pageViewInfo;
    //        switch (uploadEventType) {
    //            //不上报
    //            case UN_UPLOAD:
    //                break;
    //            //表单填单、订单填单相关
    //            case FORM_FORM_SUBMIT:
    //            case POPUP_RECEIVE_COUPON_ORDER_SUBMIT:
    //            case ORDER_ORDER_SUBMIT:
    //                uploadSender.uploadSubmitDataLisenter(new CustomerSendMqDto()
    //                    .setSubmitDataId(submitDataId)
    //                    .setCustomerToMqType(CustomerToMqType.ADD_FORM_OR_RODER_CUSTOMER)
    //                );
    //                break;
    //            //企微标签相关
    //            case WORK_WEIXIN_CUSTOMER_TAG:
    //                uploadSender.uploadTagSubmitDataLisenter(new UploadDto()
    //                    .setSubmitDataId(submitDataId)
    //                    .setUploadConfigurationTypesId(uploadConfTypeId)
    //                    .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
    //                    .setAddWechatSuccessTime(addWechatSuccessTime));
    //                break;
    //            //pv上报相关
    //            case PAGE_VIEW:
    //                pageViewInfo = pageViewInfoService.queryByPid(pid);
    //                if (Objects.isNull(pageViewInfo)) {
    //                    break;
    //                }
    //                uploadSender.browseUploadLisenter(new UploadDto()
    //                    .setPid(pid)
    //                    .setLandingPageId(pageViewInfo.getLandingPageId())
    //                    .setChannelId(pageViewInfo.getChannelId()));
    //                break;
    //            //支付相关
    //            case ORDER_PAY_SUCCESS:
    //            case POPUP_RECEIVE_COUPON_PAY_SUCCESS:
    //            case ORDER_ZERO_MONEY_BUY:
    //                uploadSender.payCustomerUploadListener(new CustomerSendMqDto()
    //                    .setSubmitDataId(submitDataId)
    //                    .setCustomerToMqType(CustomerToMqType.UPDATE_CUSTOMER_PAYMENT_STATUS)
    //                );
    //                break;
    //            //后链路上报相关
    //            case BACKEND_CUSTOMER_UPLOAD:
    //                uploadSender.backendCustomerStatusUploadListener(null);
    //                break;
    //            //公众号相关
    //            case IDENTIFY_QR_CODE:
    //                uploadSender.identifyQrCodeStatusUploadListener(pid);
    //                break;
    //            //成功添加企业微信
    //            case ADD_ENTERPRISE_WECHAT_SUCCESS:
    //                uploadSender.addEnterpriseWechatStatusUploadListener(pid);
    //                break;
    //            //公众号关注
    //            case FOLLOW_OFFICIAL_ACCOUNT:
    //                //TODO
    //                //TODO uploadSender.followWechatOfficialAccount(new UploadDto().setPid(pid).setSubscribeScene(subscribeSceneType));
    //                break;
    //            case SUCCESSFUL_PURCHASE_OF_ONLINE_SHOP_GOODS:
    //                uploadSender.successfulPurchaseOfOnlineShopGoods(pid);
    //                break;
    //            case CLICK_URL_JUMP_GO_TO_ON_LINE_SHOP:
    //                uploadSender.clickUrlJumpGoToOnLineShop(pid);
    //                break;
    //            case OFFICIAL_QR_CODE:
    //                uploadSender.identifyOfficialQrCodeUploadListener(pid);
    //                break;
    //            case WORK_WECHAT_OPEN_NUM:
    //                uploadSender.workWeixinOpenNumUpload(new UploadDto()
    //                    .setSubmitDataId(submitDataId)
    //                    .setUploadConfigurationTypesId(uploadConfTypeId)
    //                    .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
    //                    .setAddWechatSuccessTime(addWechatSuccessTime));
    //                break;
    //            case WECHAT_USER_RECOVER_KEYWORD:
    //                uploadSender.workWeixinWechatUserRecoverKeywordUpload(new UploadDto()
    //                    .setSubmitDataId(submitDataId)
    //                    .setUploadConfigurationTypesId(uploadConfTypeId)
    //                    .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
    //                    .setAddWechatSuccessTime(addWechatSuccessTime));
    //                break;
    //            case WECHAT_CUSTOMER_SERVICE_RECOVER_KEYWORD:
    //                uploadSender.workWeixinCustomerServiceRecoverKeywordUpload(new UploadDto()
    //                    .setSubmitDataId(submitDataId)
    //                    .setUploadConfigurationTypesId(uploadConfTypeId)
    //                    .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
    //                    .setAddWechatSuccessTime(addWechatSuccessTime));
    //                break;
    //            case IDENTIFY_ENTERPRISE_WECHAT_GROUP_CHAT_QR_CODE:
    //                uploadSender.identifyEnterpriseWechatChatQrCode(pid);
    //                break;
    //            case ADD_ENTERPRISE_WECHAT_GROUP_CHAT_SUCCESS:
    //                uploadSender.addEnterpriseWechatChatSuccess(pid);
    //                break;
    //            case JUMP_TO_CLAIM_618_SUPER_RED_ENVELOPE:
    //                uploadSender.linkJumpUploadListener(pid);
    //                break;
    //            //-------------------------------------------------------- 未知上报 --------------------------------------------------------
    //            //TODO case ORDER_PAY_SUCCESS_ADD_ENTERPRISE_WECHAT_SUCCESS:
    //            //TODO case ORDER_PAY_SUCCESS_ADD_ENTERPRISE_WECHAT_SUCCESS_OPEN_FIRST:
    //            //TODO     break;
    //            //落地业内组件相关
    //            case LANDING_PAGE_SUSPENSION_RIGHT_ADD_ENTERPRISE_WECHAT_SUCCESS:
    //            case LANDING_PAGE_SUSPENSION_RIGHT_ADD_ENTERPRISE_WECHAT_SUCCESS_OPEN_FIRST:
    //            case FORM_SUBMIT_ADD_ENTERPRISE_WECHAT_SUCCESS:
    //            case FORM_SUBMIT_ADD_ENTERPRISE_WECHAT_SUCCESS_OPEN_FIRST:
    //            case LANDING_PAGE_LOGICAL_ATLAS_ADD_ENTERPRISE_WECHAT_SUCCESS:
    //            case LANDING_PAGE_LOGICAL_ATLAS_ADD_ENTERPRISE_WECHAT_SUCCESS_OPEN_FIRST:
    //            case LANDING_PAGE_NAVIGATION_ADD_ENTERPRISE_WECHAT_SUCCESS:
    //            case LANDING_PAGE_NAVIGATION_ADD_ENTERPRISE_WECHAT_SUCCESS_OPEN_FIRST:
    //            case LANDING_PAGE_AUTO_JUMP_ADD_ENTERPRISE_WECHAT_SUCCESS:
    //            case LANDING_PAGE_AUTO_JUMP_ADD_ENTERPRISE_WECHAT_SUCCESS_OPEN_FIRST:
    //            case LANDING_PAGE_SERVICE_BUSINESS_CARD_ADD_ENTERPRISE_WECHAT_SUCCESS:
    //            case LANDING_PAGE_SERVICE_BUSINESS_CARD_ADD_ENTERPRISE_WECHAT_SUCCESS_OPEN_FIRST:
    //            case LANDING_PAGE_COPY_INCREASE_FOLLOWERS_ADD_ENTERPRISE_WECHAT_SUCCESS:
    //            case LANDING_PAGE_COPY_INCREASE_FOLLOWERS_ADD_ENTERPRISE_WECHAT_SUCCESS_OPEN_FIRST:
    //                //落地页组件唤起名片添加成功事件
    //                String key = RedisConstant.LANDING_PAGE_WECHAT_CUSTOMER_ACQUISITION_STATE_CONFIG + customer.getWechatCustomerContactState();
    //                Object o = objectRedisTemplate.opsForValue().get(key);
    //                if (Objects.isNull(o)) {
    //                    break;
    //                }
    //                CustomerAcquisitionStateDto customerAcquisitionStateDto = (CustomerAcquisitionStateDto) o;
    //                FrontComponentType componentType = customerAcquisitionStateDto.getComponentType();
    //                UploadEventType addWorkWechatSuccessEvent = null;
    //                if (Objects.nonNull(componentType)) {
    //                    addWorkWechatSuccessEvent = componentType.getAddWorkWechatSuccessEvent();
    //                }
    //                if (Objects.isNull(addWorkWechatSuccessEvent)) {
    //                    addWorkWechatSuccessEvent = UploadEventType.ADD_ENTERPRISE_WECHAT_SUCCESS;
    //                }
    //                //落地页组件添加微信客服事件 成功 上报
    //                uploadSender.landingPageAddWechatEventUpload(new LandingPageUploadDto()
    //                    .setPid(pid)
    //                    .setSubmitDataId(submitDataId)
    //                    .setIsAddWechat(true)
    //                    .setUploadEventType(addWorkWechatSuccessEvent));
    //                break;
    //            //获客助手落地页成员发送消息次数
    //            case ADD_ENTERPRISE_WECHAT_SUCCESS_3_OPEN:
    //            case ADD_ENTERPRISE_WECHAT_SUCCESS_5_OPEN:
    //            case ADD_ENTERPRISE_WECHAT_SUCCESS_10_OPEN:
    //            case ADD_ENTERPRISE_WECHAT_SUCCESS_1_OPEN_CA:
    //            case ADD_ENTERPRISE_WECHAT_SUCCESS_3_OPEN_CA:
    //            case ADD_ENTERPRISE_WECHAT_SUCCESS_5_OPEN_CA:
    //            case ADD_ENTERPRISE_WECHAT_SUCCESS_10_OPEN_CA:
    //                uploadSender.acquisitionOpenNumUpload(new UploadDto()
    //                    .setPid(pid)
    //                    .setUploadEventType(uploadEventType)
    //                    .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
    //                    .setAddWechatSuccessTime(addWechatSuccessTime));
    //                break;
    //            //字节小程序相关
    //            case MESSAGE_AUTHORIZATION_SUCCESS:
    //                uploadSender.messageAuthorizationSuccess(pid);
    //                break;
    //            case AUTH_PHONE_SUCCESS:
    //                uploadSender.authPhoneSuccess(pid);
    //                break;
    //            case COPY_CONTENT_SUCCESS:
    //                uploadSender.copyContentSuccess(pid);
    //                break;
    //            //WhatsApp相关
    //            case WHATSAPP_JUMP_SUCCESS:
    //                uploadSender.jumpWhatsappSuccessUploadKey(pid);
    //                break;
    //            case WHATSAPP_ADD_FRIEND_SUCCESS:
    //                uploadSender.whatsappAddFriendSuccess(pid);
    //                break;
    //            case WHATSAPP_USER_OPEN_MOUTH:
    //                uploadSender.whatsappOpenMouth(pid);
    //                break;
    //            case FOLLOW_OFFICIAL_ADD_CUSTOMER:
    //                uploadSender.officialFollowAddCustomer(new UploadDto()
    //                    .setUploadEventType(UploadEventType.FOLLOW_OFFICIAL_ADD_CUSTOMER)
    //                    .setPid(pid)
    //                    .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED));
    //                break;
    //            case CLICK_TAO_BAO_MOVIE_APPLET_JUMP_SUCCESS:
    //                uploadSender.taoBaoMovieEventSuccessUpload(UploadDto.builder()
    //                    .pid(pid)
    //                    .uploadEventType(UploadEventType.CLICK_TAO_BAO_MOVIE_APPLET_JUMP_SUCCESS)
    //                    .uploadType(UploadType.REAL_TIME)
    //                    .build()
    //                );
    //                break;
    //            case TAO_BAO_MOVIE_APPLET_ORDER_SUCCESS:
    //                uploadSender.taoBaoMovieEventSuccessUpload(UploadDto.builder()
    //                    .pid(pid)
    //                    .uploadEventType(UploadEventType.TAO_BAO_MOVIE_APPLET_ORDER_SUCCESS)
    //                    .uploadType(UploadType.REAL_TIME)
    //                    .build()
    //                );
    //                break;
    //            case ELM_APPLET_IDENTIFY_QR_CODE:
    //                uploadSender.elmEventUpload(new UploadDto().setPid(pid).setUploadEventType(UploadEventType.ELM_APPLET_IDENTIFY_QR_CODE));
    //                break;
    //            case ELM_ADD_ENTERPRISE_WECHAT_SUCCESS:
    //                uploadSender.elmEventUpload(new UploadDto().setPid(pid).setUploadEventType(UploadEventType.ELM_ADD_ENTERPRISE_WECHAT_SUCCESS));
    //                break;
    //            case DELETE_ENTERPRISE_WECHAT_FRIEND:
    //                uploadSender.submitDataDirectlyUploadByPid(UploadDto.builder()
    //                    .pid(pid)
    //                    .addWechatSuccessTime(addWechatSuccessTime)
    //                    .addEnterpriseWechatStatus(customer.getAddEnterpriseWechatStatus())
    //                    .uploadEventType(UploadEventType.DELETE_ENTERPRISE_WECHAT_FRIEND)
    //                    .uploadType(UploadType.REAL_TIME)
    //                    .build());
    //                break;
    //            case ADD_ENTERPRISE_WECHAT_JOIN_GROUP:
    //                //成功添加企业微信后入群上报
    //                pageViewInfo = pageViewInfoService.queryByPid(pid);
    //                if (Objects.isNull(pageViewInfo)) {
    //                    throw new RestException("成功添加企业微信后入群参数缺失，上报终止！");
    //                }
    //                uploadSender.addEnterpriseWechatJoinGroupUploadDelay(new UploadDto()
    //                    .setPid(pid)
    //                    .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
    //                    .setCorpid(corpid)
    //                    .setChatId(customer.getGroupChatId())
    //                    .setExternalUserId(customer.getWechatAppletExternalUserid())
    //                    .setSubmitType(UploadEventType.ADD_ENTERPRISE_WECHAT_JOIN_GROUP)
    //                    .setGroupChatJoinTime(customer.getGroupChatJoinTime())
    //                    .setCustomerId(customer.getId())
    //                    .setAddWechatSuccessTime(customer.getExternalUseridAddAt() != null ? customer.getExternalUseridAddAt().toEpochMilli() : null)
    //                    .setLandingPageId(pageViewInfo.getLandingPageId()).setChannelId(pageViewInfo.getChannelId())
    //                    .setCheckUploadNotDropoutWechatGroupSuccessTime(this.getUploadConfigurationType(pageViewInfo.getLandingPageId(), pageViewInfo.getChannelId()))
    //                );
    //                break;
    //            case FOLLOW_OFFICIAL_JOIN_GROUP:
    //                //关注公众号后发码加粉并入群上报
    //                pageViewInfo = pageViewInfoService.queryByPid(pid);
    //                if (Objects.isNull(pageViewInfo)) {
    //                    throw new RestException("关注公众号后发码加粉并入群参数确实，上报终止！");
    //                }
    //                uploadSender.followOfficialJoinGroupUploadDelay(new UploadDto()
    //                    .setPid(pid)
    //                    .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
    //                    .setCorpid(corpid)
    //                    .setChatId(customer.getGroupChatId())
    //                    .setExternalUserId(customer.getWechatAppletExternalUserid())
    //                    .setSubmitType(UploadEventType.FOLLOW_OFFICIAL_JOIN_GROUP)
    //                    .setCustomerId(customer.getId())
    //                    .setGroupChatJoinTime(customer.getGroupChatJoinTime())
    //                    .setAddWechatSuccessTime(customer.getExternalUseridAddAt() != null ? customer.getExternalUseridAddAt().toEpochMilli() : null)
    //                    .setLandingPageId(pageViewInfo.getLandingPageId()).setChannelId(pageViewInfo.getChannelId())
    //                    .setCheckUploadNotDropoutWechatGroupSuccessTime(getUploadConfigurationType(pageViewInfo.getLandingPageId(), pageViewInfo.getChannelId()))
    //                );
    //                break;
    //            case SEND_QR_CODE_ROBOT:
    //                //成功发送客服二维码（微信客服机器人）
    //                uploadSender.submitDataDirectlyUploadByPid(UploadDto.builder()
    //                    .pid(pid)
    //                    .uploadEventType(UploadEventType.SEND_QR_CODE_ROBOT)
    //                    .uploadType(UploadType.REAL_TIME)
    //                    .build()
    //                );
    //                break;
    //            case ROBOT_SEND_PICTURE:
    //                //成功发送客服二维码（微信客服机器人）
    //                uploadSender.submitDataDirectlyUploadByPid(UploadDto.builder()
    //                    .pid(pid)
    //                    .uploadEventType(UploadEventType.ROBOT_SEND_PICTURE)
    //                    .uploadType(UploadType.REAL_TIME)
    //                    .build()
    //                );
    //                break;
    //            case AUTO_REPLY_ROBOT:
    //                //成功发送客服二维码（微信客服机器人）
    //                uploadSender.submitDataDirectlyUploadByPid(UploadDto.builder()
    //                    .pid(pid)
    //                    .uploadEventType(UploadEventType.AUTO_REPLY_ROBOT)
    //                    .uploadType(UploadType.REAL_TIME)
    //                    .build()
    //                );
    //                break;
    //            default:
    //                break;
    //        }
    //        return true;
    //    }

    public Customer getCustomer(Long customerId, UploadEventType uploadEventType, Long uploadConfId, Long uploadConfTypeId) {
        if (Objects.isNull(customerId) || Objects.isNull(uploadEventType) || Objects.isNull(uploadConfId) || Objects.isNull(uploadConfTypeId)) {
            throw new RestException("参数不合法!");
        }
        Customer customer = customerService.getOne(new LambdaQueryWrapper<Customer>()
            .ge(Customer::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerQueryTime()))
            .eq(Customer::getId, customerId)
            .last(" limit 1")
        );
        if (Objects.isNull(customer)) {
            throw new RestException("客资不存在！");
        }
        return customer;
    }

    public Integer getUploadConfigurationType(Long landingPageId, Long channelId){
        if (Objects.nonNull(landingPageId) && Objects.nonNull(channelId)){
            String key = RedisConstant.DROP_OUT_WECHAT_GROUP_UPLOAD_CONFIG + "landingPageId:" + landingPageId + "channelId:" + channelId;
            Object o = objectRedisTemplate.opsForValue().get(key);
            if (Objects.nonNull(o) && o instanceof UploadConfigurationTypesDTO){
                UploadConfigurationTypesDTO dto = (UploadConfigurationTypesDTO) o;
                int time =  dto.getCheckUploadNotDropoutWechatGroupSuccessTime();
                log.info("加群回调, 获取缓存中设置进群后X秒（时间）后未退群再上报的时间,time = {}s ", time);
                return time;
            }
        }
        log.info("加群回调, 获取缓存中设置进群后X秒（时间）后未退群再上报的时间,没查到结果，直接返回默认值,time = {}s ", 60);
        return 60;
    }

    /**
     * 更新客资的开口次数
     */
    public void updateCustomerAcquisitionOpenNum(UploadDto uploadDto) {
        try{
            log.info("更新客资表的开口次数, 入参 dto = {}", JSONObject.toJSONString(uploadDto));
            if (Objects.isNull(uploadDto)){
                return;
            }
            String pid = uploadDto.getPid();
            Long submitDataId = uploadDto.getSubmitDataId();
            Integer openChatNum  = uploadDto.getOpenChatNum();
            if (Objects.nonNull(openChatNum)) {
                UploadDataDTO uploadDataDTO = this.getUploadDataDTO(pid, submitDataId, uploadDto);
                if (Objects.nonNull(uploadDataDTO)) {
                    Customer customer = uploadDataDTO.getCustomer();
                    if (Objects.nonNull(customer)) {
                        customerService.lambdaUpdate().set(Customer::getOpenChatNum, openChatNum)
                            .eq(Customer::getId, customer.getId())
                            .ge(Customer::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerQueryTime()))
                            .update();
                    }
                }
            }
        }catch (Exception e){
            log.error("更新客资的开口次数失败，dto = {}", JSONObject.toJSONString(uploadDto), e);
        }
    }
}
