package ai.yiye.agent.submit.service;

import ai.yiye.agent.common.util.DateTimeUtil;
import ai.yiye.agent.domain.utils.UrlUtils;
import ai.yiye.agent.domain.*;
import ai.yiye.agent.domain.enumerations.BackendCustomerUploadType;
import ai.yiye.agent.domain.enumerations.Platform;
import ai.yiye.agent.domain.enumerations.UploadStateType;
import ai.yiye.agent.marketing.oceanengine.client.OceanBackendCustomerTypeUploadClient;
import ai.yiye.agent.marketing.oceanengine.response.BackendCustomerTypeUploadRequestBody;
import ai.yiye.agent.marketing.oceanengine.response.BackendCustomerTypeUploadResposeBody;
import ai.yiye.agent.submit.mapper.CustomerBackendDataUploadRecordMapper;
import ai.yiye.agent.submit.remote.BackendTraceDataRemote;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/7/22 9:39 下午
 */
@Slf4j
@Service
public class CustomerBackendDataUploadRecordService extends ServiceImpl<CustomerBackendDataUploadRecordMapper, CustomerBackendDataUploadRecord> {
    public final static Map<String, BackendCustomerUploadType> platformEventType = new HashMap<>();
    public final static List<String> CLUE_CONFIRM_LIST = Arrays.asList("clue_status", "wechat_relation", "phone_build_relation");
    public final static List<String> CLUE_HIGH_INTENTION_LIST = Arrays.asList("reservation_status");
    public final static List<String> ALL_STATUS_LIST = new ArrayList<>();
    private final static Integer SUCCESS_CODE = 0;

    static {
        ALL_STATUS_LIST.addAll(CLUE_CONFIRM_LIST);
        ALL_STATUS_LIST.addAll(CLUE_HIGH_INTENTION_LIST);
        platformEventType.put("clue_status", BackendCustomerUploadType.CLUE_CONFIRM);
        platformEventType.put("wechat_relation", BackendCustomerUploadType.CLUE_CONFIRM);
        platformEventType.put("phone_build_relation", BackendCustomerUploadType.CLUE_CONFIRM);

        platformEventType.put("reservation_status", BackendCustomerUploadType.CLUE_HIGH_INTENTION);
    }

    @Autowired
    private SubmitDataService submitDataService;
    @Autowired
    private UploadConfigurationService uploadConfigurationService;
    @Autowired
    private BackendTraceDataRemote backendTraceDataRemote;
    @Autowired
    private OceanBackendCustomerTypeUploadClient oceanBackendCustomerTypeUploadClient;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private ai.yiye.agent.submit.config.AgentConf agentConf;

    /**
     * 封装需要上报的信息
     */
    public static BackendTraceData getNeedUploadCustomer(Map<String, BackendTraceData> collect, List<String> clue) {
        return clue.stream()
            .filter(a -> Objects.nonNull(collect.get(a)))
            .map(a -> collect.get(a)).
                findFirst().orElse(null);
    }

    public static boolean checkClueStatus(Map<String, BackendTraceData> collect, List<String> keys) {
        long count = keys.stream().filter(key -> collect.containsKey(key)).count();
        return count > 0;
    }

    @Deprecated
    public void upload(Long submitId) {
        SubmitData submitData = submitDataService.getByIdAndCreatedAt(submitId);
        if (Objects.isNull(submitData)) {
            log.info("submit data 不存在 {}", submitId);
            return;
        }

        Long channelId = submitData.getChannelId();

        boolean haveClickId = UrlUtils.haveClickId(submitData.getUrl(), submitData.getReferrer(), UrlUtils.COMMON_CLICK_ID_PARAM);
        String clickId = UrlUtils.getParameter(submitData.getUrl(), submitData.getReferrer(), UrlUtils.CLICK_ID_PARAM);

        if (!(haveClickId && StringUtils.isNotBlank(clickId))) {
            log.info("不存在 ocean clickid 不需要上报 landingPageId:{} channelId:{} submitId:{}", submitData.getLandingPageId(), channelId, submitId);
            return;
        }

        //验证是否配置抖音上报
        UploadConfiguration uploadConfiguration = uploadConfigurationService.getOne(new LambdaQueryWrapper<UploadConfiguration>().eq(UploadConfiguration::getChannelId, channelId));
        if (Objects.isNull(uploadConfiguration) || !Platform.OCEAN_ENGINE.equals(uploadConfiguration.getPlatformId())) {
            log.info("没有配置头条上报 landingPageId:{} channelId:{} submitId:{}", submitData.getLandingPageId(), channelId, submitId);
            return;
        }

        //获取后链路客资状态信息
        Customer customer = customerService.selectLimitOne(submitId);
        if (Objects.isNull(customer)) {
            log.info("查不到对应的客资 landingPageId:{} channelId:{} submitId:{}", submitData.getLandingPageId(), channelId, submitId);
            return;
        }
        List<BackendTraceData> backendTraceDataList = backendTraceDataRemote.getListByCustomerId(customer.getId(), 1);
        if (CollectionUtils.isEmpty(backendTraceDataList)) {
            log.error("没有对应的后链路客资状态数据! landingPageId:{} channelId:{} submitId:{}", submitData.getLandingPageId(), channelId, submitId);
            return;
        }

        // 对比是否需要上报 以及需要上报的类型

        //获取已上报信息
        List<CustomerBackendDataUploadRecord> customerBackendDataUploadRecords = this.list(new LambdaQueryWrapper<CustomerBackendDataUploadRecord>()
            .eq(CustomerBackendDataUploadRecord::getSubmitDataId, submitId));
        Map<String, BackendTraceData> collect = backendTraceDataList.stream().sorted(Comparator.comparing(BackendTraceData::getId).reversed())
            .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(BackendTraceData::getDuplicateId))), ArrayList::new))
            .stream().collect(Collectors.toMap(BackendTraceData::getFiled, Function.identity()));
        Map<BackendCustomerUploadType, CustomerBackendDataUploadRecord> uploadRecords = customerBackendDataUploadRecords.stream().collect(Collectors.toMap(CustomerBackendDataUploadRecord::getBackendCustomerUploadType, Function.identity()));

        List<CustomerBackendDataUploadRecord> recosds = new ArrayList<>();
        //是否有 有效线索
        boolean isClueConfirm = checkClueStatus(collect, CLUE_CONFIRM_LIST);
        //是否有 回访-高潜成交
        boolean isCluehighIntention = checkClueStatus(collect, CLUE_HIGH_INTENTION_LIST);
        //有效客资 有效线索
        if (isClueConfirm || isCluehighIntention) {
            //有 后链路数据 未上报过 -> 进行上报
            if (!uploadRecords.containsKey(BackendCustomerUploadType.CLUE_CONFIRM)) {
                // 获取后链路数据
                BackendTraceData needUploadCustomer = getNeedUploadCustomer(collect, ALL_STATUS_LIST);
                CustomerBackendDataUploadRecord customerBackendDataUploadRecord = new CustomerBackendDataUploadRecord();
                customerBackendDataUploadRecord.setBackendCustomerUploadType(BackendCustomerUploadType.CLUE_CONFIRM)
                    .setSubmitDataId(submitId)
                    .setLandingPageWidgetTemplateId(submitData.getLandingPageWidgetTemplateId())
                    .setCreatedAt(needUploadCustomer.getCreatedAt())
                    .setFiled(needUploadCustomer.getFiled())
                    .setFiledName(needUploadCustomer.getFiledName())
                    .setFiledType(needUploadCustomer.getFiledType())
                    .setFiledTypeName(needUploadCustomer.getFiledTypeName())
                    .setLandingPageId(submitData.getLandingPageId())
                    .setUrl(submitData.getUrl())
                    .setPlatformId(Platform.OCEAN_ENGINE);
                recosds.add(customerBackendDataUploadRecord);
            }
        }

        //回访-高潜成交
        if (isCluehighIntention) {
            if (!uploadRecords.containsKey(BackendCustomerUploadType.CLUE_HIGH_INTENTION)) {
                BackendTraceData needUploadCustomer = getNeedUploadCustomer(collect, CLUE_HIGH_INTENTION_LIST);
                CustomerBackendDataUploadRecord customerBackendDataUploadRecord = new CustomerBackendDataUploadRecord();
                customerBackendDataUploadRecord.setBackendCustomerUploadType(BackendCustomerUploadType.CLUE_HIGH_INTENTION)
                    .setSubmitDataId(submitId)
                    .setLandingPageWidgetTemplateId(submitData.getLandingPageWidgetTemplateId())
                    .setCreatedAt(needUploadCustomer.getCreatedAt())
                    .setFiled(needUploadCustomer.getFiled())
                    .setFiledName(needUploadCustomer.getFiledName())
                    .setFiledType(needUploadCustomer.getFiledType())
                    .setFiledTypeName(needUploadCustomer.getFiledTypeName())
                    .setLandingPageId(submitData.getLandingPageId())
                    .setUrl(submitData.getUrl())
                    .setPlatformId(Platform.OCEAN_ENGINE);

                recosds.add(customerBackendDataUploadRecord);
            }
        }

        /**
         * 执行上报
         */
        this.doUpload(recosds, clickId);
    }

    public void doUpload(List<CustomerBackendDataUploadRecord> recosds, String clickId) {
        List<CustomerBackendDataUploadRecord> collect = recosds.stream().peek(a -> {
            BackendCustomerTypeUploadRequestBody build = BackendCustomerTypeUploadRequestBody.builder()
                .event_type(a.getBackendCustomerUploadType().getColumn())
                .context(new BackendCustomerTypeUploadRequestBody.Context(new BackendCustomerTypeUploadRequestBody.Ad(clickId)))
                .timestamp(a.getTimestamp()).build();
            try {
                BackendCustomerTypeUploadResposeBody conversion = oceanBackendCustomerTypeUploadClient.conversion(build);
                if (SUCCESS_CODE.equals(conversion.getCode())) {
                    a.setUploadState(UploadStateType.SUCCESS_REPORT);
                } else {
                    a.setUploadState(UploadStateType.FAIL_REPORT);
                }
                a.setRecordCode(conversion.getCode());
                a.setSendData(JSONObject.toJSONString(build))
                    .setResultData(JSONObject.toJSONString(conversion));
            } catch (Exception e) {
                log.error("上报失败 CustomerBackendDataUploadRecord : {} clickId: {}", JSONObject.toJSONString(a), clickId, e);
            }
        }).collect(Collectors.toList());
        this.saveBatch(collect);
    }

}
