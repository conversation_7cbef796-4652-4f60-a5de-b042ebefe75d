package ai.yiye.agent.submit.controller;


import ai.yiye.agent.autoconfigure.mybatis.multidatasource.service.AgentConfService;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.common.util.DateTimeUtil;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.domain.CustomerUploadRecord;
import ai.yiye.agent.domain.UploadConfiguration;
import ai.yiye.agent.domain.dto.ResultBean;
import ai.yiye.agent.domain.dto.UploadDto;
import ai.yiye.agent.domain.enumerations.UploadType;
import ai.yiye.agent.submit.service.CustomerUploadRecordService;
import ai.yiye.agent.submit.service.CustomerUploadService;
import ai.yiye.agent.submit.service.UploadConfigurationService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/upload")
public class UploadController {

    @Autowired
    private CustomerUploadService uploadService;
    @Autowired
    private AgentConfService agentConfService;
    @Autowired
    private CustomerUploadRecordService customerUploadService;
    @Autowired
    private UploadConfigurationService uploadConfigurationService;

    @PostMapping
    public ResultBean<Object> upload(@RequestBody UploadDto uploadDto) {
        return uploadService.upload(uploadDto.setUploadType(UploadType.INTERFACE));
    }

    public static final Instant stringToInstant(String string) {
        return DateTimeUtil.stringToInstant(string);
    }

    //更新一下数据源的问题,临时接口
    @GetMapping("populate-data")
    public String updateLatestData() {
        TenantContextHolder.clearContext();
        List<AgentConf> list = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getStatus, 1));
        for (int i = 0; i < list.size(); i++) {
            AgentConf agentConf = list.get(i);
            TenantContextHolder.set(agentConf.getAgentId());
            //去除所有的uploadConfiguration
            List<UploadConfiguration> list1 = uploadConfigurationService.list(new LambdaQueryWrapper<UploadConfiguration>().le(UploadConfiguration::getCreatedAt, stringToInstant("2021-12-16 00:00:00")).eq(UploadConfiguration::getPlatformId, 2).or().eq(UploadConfiguration::getPlatformId, 3).or().eq(UploadConfiguration::getPlatformId, 8));
            for (int i1 = 0; i1 < list1.size(); i1++) {
                UploadConfiguration uploadConfiguration = list1.get(i1);
                if (uploadConfiguration.getActionSetId() == null) {
                    CustomerUploadRecord one = customerUploadService.getOne(new LambdaQueryWrapper<CustomerUploadRecord>()
                        .eq(CustomerUploadRecord::getLandingPageId, uploadConfiguration.getLandingPageId())
                        .eq(CustomerUploadRecord::getAdvertiserAccountId, uploadConfiguration.getAdvertiserAccountId())
                        .orderByDesc(CustomerUploadRecord::getCreatedAt)
                        .isNull(CustomerUploadRecord::getDescription)
                        .last(" limit 1"));
                    if (one != null) {
                        String sendData = one.getSendData();
                        JSONObject object = JSONObject.parseObject(sendData);
                        String userActionSetId = object.getString("userActionSetId");
                        this.uploadConfigurationService.update(new LambdaUpdateWrapper<UploadConfiguration>().eq(UploadConfiguration::getId, uploadConfiguration.getId()).set(UploadConfiguration::getActionSetId, userActionSetId));
                    }
                }
            }
        }
        return "success";
    }

    @Resource
    private RedisTemplate<String, Object> objectRedisTemplate;

    //    /**
    //     * 1.197.0版本：手动补偿上报
    //     */
    //    @PostMapping("manual-compensation-upload")
    //    @OperationLog("1.197.0版本：手动补偿上报")
    //    public JSONArray manualCompensationUpload(@RequestBody List<String> agentIds) {
    //        if (CollectionUtils.isEmpty(agentIds)) {
    //            List<AgentConf> agentConfList = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getStatus, 1));
    //            if (CollectionUtils.isEmpty(agentConfList)) {
    //                return null;
    //            }
    //            agentIds = agentConfList.stream().map(AgentConf::getAgentId).collect(Collectors.toList());
    //        }
    //        JSONArray result = new JSONArray();
    //        agentIds.forEach(agentId -> {
    //            try {
    //                TenantContextHolder.set(agentId);
    //                List<CustomerUploadRecord> customerUploadRecordList = customerUploadService.manualCompensationUploadList();
    //                log.info("开始手动补偿上报 agentId={}；count={}；", agentId, CollectionUtils.isEmpty(customerUploadRecordList) ? 0 : customerUploadRecordList.size());
    //                if (!CollectionUtils.isEmpty(customerUploadRecordList)) {
    //                    customerUploadRecordList.forEach(customerUploadRecord -> {
    //                        String pid = customerUploadRecord.getPid();
    //                        Long submitDataId = customerUploadRecord.getSubmitDataId();
    //                        //根据事件类型发送上报队列
    //                        switch (customerUploadRecord.getSubmitType()) {
    //                            case UN_UPLOAD: //不上报
    //                                break;
    //                            case FORM_FORM_SUBMIT:  //表单提交
    //                            case POPUP_RECEIVE_COUPON_ORDER_SUBMIT: //领取优惠券-订单提交
    //                            case ORDER_ORDER_SUBMIT:    //订单提交
    //                                uploadSender.sendSubmitDataId(submitDataId);
    //                                break;
    //                            case ORDER_PAY_SUCCESS: //订单支付成功
    //                            case POPUP_RECEIVE_COUPON_PAY_SUCCESS:  //领取优惠券-支付成功
    //                            case ORDER_ZERO_MONEY_BUY:  //订单-0元购
    //                                uploadSender.sendPayment(submitDataId);
    //                                break;
    //                            //无需补偿，从接口上报入口即可补偿
    //                            //case INTERFACE_UPLOAD:
    //                            //    //接口上报
    //                            //    uploadSender.sendSubmitDataId(submitDataId);
    //                            //    break;
    //                            case IDENTIFY_QR_CODE:
    //                                //长按识别二维码
    //                                uploadSender.sendIdentifyQrCodeUpload(pid);
    //                                break;
    //                            case ADD_ENTERPRISE_WECHAT_SUCCESS:
    //                                //成功添加企业微信
    //                                uploadSender.sendAddWorkWechatUpload(pid);
    //                                break;
    //                            //无上报参数，无法补偿上报
    //                            //case BACKEND_CUSTOMER_UPLOAD:
    //                            //    //客资转化状态
    //                            //    uploadSender.sendBackendCustomer(new UploadDto());
    //                            //    break;
    //                            //链路所需参数丢失，无法补偿上报
    //                            //case WORK_WEIXIN_CUSTOMER_TAG:
    //                            //    //企业微信客户标签
    //                            //    uploadSender.sendWorkWeixinTagExchange(submitDataId);
    //                            //    break;
    //                            case FOLLOW_OFFICIAL_ACCOUNT:
    //                                //公众号关注
    //                                uploadSender.sendFollowOfficialAccount(new UploadDto().setPid(pid));
    //                                break;
    //                            case PAGE_VIEW:
    //                                //浏览页面
    //                                uploadSender.browseUpload(pid);
    //                                break;
    //                            case SUCCESSFUL_PURCHASE_OF_ONLINE_SHOP_GOODS:
    //                                //电商商品购买成功
    //                                uploadSender.successfulPurchaseOfOnlineShopGoods(pid);
    //                                break;
    //                            case CLICK_URL_JUMP_GO_TO_ON_LINE_SHOP:
    //                                //电商商品浏览
    //                                uploadSender.sendClickUrlJumpGoToOnLineShopByPid(pid);
    //                                break;
    //                            case OFFICIAL_QR_CODE:
    //                                //长按识别公众号二维码
    //                                uploadSender.sendIdentifyOfficialQrCodeUpload(pid);
    //                                break;
    //                            //链路所需参数丢失，无法补偿上报
    //                            //case WORK_WECHAT_OPEN_NUM:
    //                            //    //企业微信客户开口次数
    //                            //    uploadSender.sendWorkWeixinOpenNumUpload(pid);
    //                            //    break;
    //                            //链路所需参数丢失，无法补偿上报
    //                            //case WECHAT_USER_RECOVER_KEYWORD:
    //                            //    //企业微信客户回复关键词
    //                            //    uploadSender.sendSubmitDataId(submitDataId);
    //                            //    break;
    //                            //链路所需参数丢失，无法补偿上报
    //                            //case WECHAT_CUSTOMER_SERVICE_RECOVER_KEYWORD:
    //                            //    //企业微信销售回复关键词
    //                            //    uploadSender.sendSubmitDataId(submitDataId);
    //                            //    break;
    //                            case IDENTIFY_ENTERPRISE_WECHAT_GROUP_CHAT_QR_CODE:
    //                                //长按识别二维码（企业微信群）
    //                                uploadSender.workWeixinIdentifyEnterpriseWechatGroupChatQrCodeUpload(pid);
    //                                break;
    //                            case ADD_ENTERPRISE_WECHAT_GROUP_CHAT_SUCCESS:
    //                                //成功添加企业微信
    //                                uploadSender.sendWorkWechatAddGroupChatSuccess(pid);
    //                                break;
    //                        }
    //                    });
    //                    JSONObject row = new JSONObject();
    //                    row.put(agentId, CollectionUtils.isEmpty(customerUploadRecordList) ? 0 : customerUploadRecordList.size());
    //                    result.add(row);
    //                }
    //            } catch (Exception e) {
    //                log.error("1.197.0版本：手动补偿上报异常 agentId={}；", agentId);
    //            }
    //        });
    //        log.info("手动补偿结束 result={}；", JSONObject.toJSONString(result));
    //        return result;
    //    }

}
