package ai.yiye.agent.marketing.store.mapper;

import ai.yiye.agent.domain.clickhousetypehandlers.JSONStringTypeHandler;
import ai.yiye.agent.domain.marketing.data.InsertIdApply;
import ai.yiye.agent.domain.marketing.data.UpdateIgnore;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public final class AdvertiserMapperHelper {

    /**
     * 获取对象的表名
     *
     * @param clazz
     * @return
     */
    public static String getTableName(Class<?> clazz) {
        return SqlHelper.table(clazz).getTableName();
    }

    public static String getSqlSelect(Class<?> clazz) {
        return Stream.concat(SqlHelper.table(clazz).getFieldList()
            .stream()
            .map(field -> field.getColumn()), idApply(clazz)? Stream.of(SqlHelper.table(clazz).getKeyColumn()): Stream.empty())
            .collect(Collectors.joining(","));
    }

    private static boolean idApply(Class<?> clazz) {
        return Optional.ofNullable(clazz.getAnnotation(InsertIdApply.class))
            .map(apply -> {
                return apply.value();
            }).orElse(false);
    }

    /**
     * 获取表字段
     *
     * @param clazz
     * @return
     */
    public static String getTableFields(Class<?> clazz) {
        return Stream.concat(SqlHelper.table(clazz).getFieldList()
            .stream()
            .map(field -> String.format("#{%s}", field.getEl())),
                idApply(clazz)? Stream.of(String.format("#{%s}", SqlHelper.table(clazz).getKeyColumn())): Stream.empty())
            .collect(Collectors.joining(","));
    }

    /**
     * 获取列表下表字段
     *
     * @param clazz
     * @return
     */
    public static String getTableFields(Class<?> clazz, String item) {
        return Stream.concat(SqlHelper.table(clazz).getFieldList()
            .stream()
            .map(field -> {
                if (null != field.getTypeHandler()
                    && field.getTypeHandler().getName().equals(JSONStringTypeHandler.class.getName())) {
                    return String.format("#{%s.%s, typeHandler=%s}", item, field.getEl(), JSONTypeHandler.class.getCanonicalName());
                } else {
                    return String.format("#{%s.%s}", item, field.getEl());
                }
            }), idApply(clazz)? Stream.of(String.format("#{%s.%s}", item, SqlHelper.table(clazz).getKeyColumn())): Stream.empty())
            .collect(Collectors.joining(","));
    }

    /**
     * 获取更新的相关字段
     *
     * @param clazz
     * @return
     */
    public static String getUpdateSql(Class<?> clazz) {
        return SqlHelper.table(clazz).getFieldList()
            .stream()
            .filter(field -> Optional.ofNullable(field.getField().getAnnotation(UpdateIgnore.class)).map(ignore -> ignore.value()).orElse(true))
            .map(field -> String.format("%s = EXCLUDED.%s", field.getColumn(), field.getColumn()))
            .collect(Collectors.joining(","));
    }

    /**
     * 连接冲突的字段
     *
     * @param conflicts
     * @return
     */
    public static String concatConflictFiled(String[] conflicts) {
        return StringUtils.join(conflicts, ",");
    }
}
