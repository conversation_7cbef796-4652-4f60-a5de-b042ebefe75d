package ai.yiye.agent.collect.pageview.sevice;

import ai.yiye.agent.collect.pageview.dto.PageViewInfoDto;
import ai.yiye.agent.collect.pageview.mapper.CustomerUploadRecordMapper;
import ai.yiye.agent.common.util.HumpUtils;
import ai.yiye.agent.domain.ActionTypeConfig;
import ai.yiye.agent.domain.CustomerUploadRecord;
import ai.yiye.agent.domain.dto.UploadRecordReturnMessageDto;
import ai.yiye.agent.domain.enumerations.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@DS("postgresql")
public class CustomerUploadRecordService extends ServiceImpl<CustomerUploadRecordMapper, CustomerUploadRecord> {

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_FORMAT).withZone(ZoneId.systemDefault());

    public void reloadData(List<String> permissionList, PageViewInfoDto pageViewInfoDto, List<ActionTypeConfig> actionTypeConfigList, OperationRole role) {
        pageViewInfoDto.setWorkWechatNameAndCorpid((StringUtils.isNotBlank(pageViewInfoDto.getWorkWechatName()) ? pageViewInfoDto.getWorkWechatName() : "") + (StringUtils.isNotBlank(pageViewInfoDto.getCorpid()) ? "（" + pageViewInfoDto.getCorpid() + "）" : ""));
        pageViewInfoDto.setGroupChatWorkWechatNameAndCorpid((StringUtils.isNotBlank(pageViewInfoDto.getGroupChatWorkWechatName()) ? pageViewInfoDto.getGroupChatWorkWechatName() : "") + (StringUtils.isNotBlank(pageViewInfoDto.getGroupChatCorpid()) ? "（" + pageViewInfoDto.getGroupChatCorpid() + "）" : ""));
        final String pid = pageViewInfoDto.getPid();
        //1.173.0使用合并上报记录 不单只取其中之一
        String customerUploadRecordList = pageViewInfoDto.getCustomerUploadRecordList();
        if (StringUtils.isBlank(pid) || StringUtils.isBlank(customerUploadRecordList)) {
            if (OperationType.OPERATION.equals(pageViewInfoDto.getOperationType()) && OperationRole.COMMON.equals(role)) {
                //即使他为空，也要有状态
                List<UploadRecordReturnMessageDto> uploadRecordReturnMessageDtos = new ArrayList<>();
                UploadRecordReturnMessageDto uploadRecordReturnMessageDto = new UploadRecordReturnMessageDto().setIsSuccess(true).setUploadReturnMessage("运营托管").setClickId("").setPhotoColor(2);
                uploadRecordReturnMessageDtos.add(uploadRecordReturnMessageDto);
                pageViewInfoDto.setUploadPlatformId(pageViewInfoDto.getUploadPlatformId());
                pageViewInfoDto.setUploadRecordReturnMessageDtos(uploadRecordReturnMessageDtos);
            }
            return;
        }
        JSONArray jsonArray = JSONArray.parseArray(customerUploadRecordList);
        JSONArray result = new JSONArray();
        jsonArray.stream().forEach(e -> {
            JSONObject customerUploadRecordJSON = (JSONObject) e;
            JSONObject jsonObject = new JSONObject();
            customerUploadRecordJSON.entrySet().stream().forEach(m -> {
                String key = m.getKey();
                String s = HumpUtils.x_x2xX(key);
                jsonObject.put(s, m.getValue());
            });
            Integer platformId = jsonObject.getInteger("platformId");
            if (ObjectUtils.isNotEmpty(platformId)) {
                Platform enumById = Platform.getEnumById(platformId);
                if (ObjectUtils.isNotEmpty(enumById)) {
                    jsonObject.put("platformId", enumById);
                }
            }
            jsonObject.put("createdAt", null);
            jsonObject.put("updatedAt", null);
            result.add(jsonObject);
        });
        List<CustomerUploadRecord> customerUploadRecords = result.toJavaList(CustomerUploadRecord.class);
        if (CollectionUtils.isEmpty(customerUploadRecords)) {
            return;
        }
        List<UploadRecordReturnMessageDto> uploadRecordReturnMessageDtos = customerUploadRecords.stream().map(e -> this.getUploadRecordReturnMessageDto(permissionList, e, actionTypeConfigList, pageViewInfoDto)).collect(Collectors.toList());

        if (OperationType.OPERATION.equals(pageViewInfoDto.getOperationType())&& OperationRole.COMMON.equals(role)) {
            uploadRecordReturnMessageDtos.clear();
            UploadRecordReturnMessageDto uploadRecordReturnMessageDto = new UploadRecordReturnMessageDto().setIsSuccess(true).setUploadReturnMessage("运营托管").setClickId("").setPhotoColor(2);
            uploadRecordReturnMessageDtos.add(uploadRecordReturnMessageDto);
            if (CollectionUtils.isEmpty(uploadRecordReturnMessageDtos) ||Objects.isNull(uploadRecordReturnMessageDtos.get(0))) {
                pageViewInfoDto.setUploadPlatformId(pageViewInfoDto.getUploadPlatformId());
            } else {
                pageViewInfoDto.setUploadPlatformId(!Objects.isNull(customerUploadRecords.get(0).getPlatformId()) ? customerUploadRecords.get(0).getPlatformId() : pageViewInfoDto.getUploadPlatformId());
            }
            pageViewInfoDto.setUploadRecordReturnMessageDtos(uploadRecordReturnMessageDtos);
            return;
        }

        pageViewInfoDto.setUploadPlatformId(!Objects.isNull(customerUploadRecords.get(0).getPlatformId()) ? customerUploadRecords.get(0).getPlatformId() : pageViewInfoDto.getUploadPlatformId());

        pageViewInfoDto.setUploadRecordReturnMessageDtos(uploadRecordReturnMessageDtos);

        pageViewInfoDto.setFormName(Arrays.asList(SubmitType.FORM, SubmitType.DOUYIN_APPLET).contains(pageViewInfoDto.getWidgetTemplateType()) ? pageViewInfoDto.getWidgetTemplateName() : "");
        pageViewInfoDto.setOrderFormName(Objects.equals(SubmitType.ORDER, pageViewInfoDto.getWidgetTemplateType()) ? pageViewInfoDto.getWidgetTemplateName() : "");
    }

    /**
     * 循环多条上报记录
     *
     * @param customerUploadRecord
     * @param actionTypeConfigList
     * @return
     */
    private UploadRecordReturnMessageDto getUploadRecordReturnMessageDto(List<String> permissionList, CustomerUploadRecord customerUploadRecord, List<ActionTypeConfig> actionTypeConfigList,PageViewInfoDto pageViewInfoDto) {
                //客资上报状态暂不展示上报中青看点数据上报状态
                if (Platform.YOUTH_KANDIAN.equals(customerUploadRecord.getPlatformId())) {
                    return null;
                }
                String actionName = "";
                customerUploadRecord.setSubmitType(UploadEventType.getEnumById(customerUploadRecord.getSubmitType().ordinal()));
                if (CollectionUtils.isNotEmpty(actionTypeConfigList) && StringUtils.isNotBlank(customerUploadRecord.getActionType()) && !Objects.isNull(customerUploadRecord.getPlatformId())) {
                    for (ActionTypeConfig actionTypeConfig : actionTypeConfigList) {
                        if ((Objects.isNull(customerUploadRecord.getActionType()) || Objects.isNull(actionTypeConfig.getCode())) || (Objects.isNull(customerUploadRecord.getPlatformId()) || Objects.isNull(actionTypeConfig.getPlatformId()))) {
                            continue;
                        }
                        if (customerUploadRecord.getActionType().equals(actionTypeConfig.getCode()) && customerUploadRecord.getPlatformId().equals(actionTypeConfig.getPlatformId())) {
                            actionName = actionTypeConfig.getValue();
                            break;
                        }
                    }
                }

                //导出会话存档配置信息相关
                String openNumStr = "", keyWordStr = "", sexStr = "";
                if (permissionList.contains(WhiteType.WORK_WECHAT_SESSION_FILE_CONFIG.getValue())) {
                    String keyWord = Objects.isNull(customerUploadRecord.getKeyWord()) ? ""  : Arrays.toString(customerUploadRecord.getKeyWord());
                    int workWechatOpenNum = Objects.isNull(customerUploadRecord.getWorkWechatOpenNum()) ? 1 : customerUploadRecord.getWorkWechatOpenNum();
                    CompareType compareType = Objects.isNull(customerUploadRecord.getCompareType()) ? CompareType.GREATER_OR_EQUAL : customerUploadRecord.getCompareType();
                    FuzzyMatchingField fuzzyMatchingField = Objects.isNull(customerUploadRecord.getFuzzyMatchingField()) ? FuzzyMatchingField.LIKE_MATCHING : customerUploadRecord.getFuzzyMatchingField();
                    //客户开口次数  添加次数设置
                    if (UploadEventType.WORK_WECHAT_OPEN_NUM.equals(customerUploadRecord.getSubmitType())) {
                        openNumStr = compareType.getName() + " " + workWechatOpenNum + " 次；";
                    }
                    //关键词回复1.判断是客户回复还是销售回复     2.关键词是模糊匹配还是完全匹配（默认为模糊匹配）
                    if (Arrays.asList(UploadEventType.WECHAT_USER_RECOVER_KEYWORD, UploadEventType.WECHAT_CUSTOMER_SERVICE_RECOVER_KEYWORD).contains(customerUploadRecord.getSubmitType())) {
                        //false-完全匹配  true-模糊匹配
                        keyWordStr = (FuzzyMatchingField.LIKE_MATCHING.equals(fuzzyMatchingField) ? "模糊匹配" : "完全匹配") + "；关键词：" + (keyWord.length() > 0 ? keyWord : "") + "；";
                    }
                }
                if (permissionList.contains(WhiteType.WORK_WECHAT_GENDER_UPLOAD.getValue())) {
                    Sex sex = Objects.isNull(customerUploadRecord.getSex()) ? Sex.ALL : customerUploadRecord.getSex();
                    //上报条件-【成功添加企业微信/企业微信客户开口次数/企业微信客服回复关键词/企业微信销售回复关键词】，新增上报附加条件性别  性别-全部  默认 性别不限
                    if (UploadEventType.getWorkWechatSessionTypesUpload().contains(customerUploadRecord.getSubmitType())) {
                        sexStr = "性别 - " + (Objects.isNull(sex) ? "" : sex.getName()) + "；";
                    }
                }

                if (UploadEventType.WORK_WEIXIN_CUSTOMER_TAG.equals(customerUploadRecord.getSubmitType())) {
                    FuzzyMatchingField fuzzyMatchingField = Objects.isNull(customerUploadRecord.getMatchingMode()) ? FuzzyMatchingField.ALL_MATCHING : customerUploadRecord.getMatchingMode();
                    keyWordStr = (FuzzyMatchingField.LIKE_MATCHING.equals(fuzzyMatchingField) ? "模糊匹配" : "完全匹配") + "；";
                }

        /*int uploadRatio = Objects.isNull(customerUploadRecord.getUploadRatio()) || customerUploadRecord.getUploadRatio() <= 0 ? 100 : customerUploadRecord.getUploadRatio();
        String uploadTime = StringUtils.isEmpty(StringUtils.trim(customerUploadRecord.getUploadTimeArea())) ? TimeAreas.AREA_TIME_01.getMinTime() + "-" + TimeAreas.AREA_TIME_48.getMaxTime() : customerUploadRecord.getUploadTimeArea();*/
                String submitTypeName = Objects.isNull(customerUploadRecord.getSubmitType()) ? "" : customerUploadRecord.getSubmitType().getName();
                String uploadReturnMessage = "上报条件：" + submitTypeName + /*"；上报比例：" + uploadRatio + "%；执行时间：" + uploadTime +*/ "；" + openNumStr + keyWordStr + sexStr + "上报类型：" + actionName;

                //删除企业微信好友
                if (UploadEventType.DELETE_ENTERPRISE_WECHAT_FRIEND.equals(customerUploadRecord.getSubmitType()) && Objects.nonNull(pageViewInfoDto.getEventType())) {
                    log.info("访客细察导出，封装删除企业微信好友的信息");
                    String content = this.getDeleteFriendInfo(pageViewInfoDto);
                    if (StringUtils.isNotBlank(content)) {
                        uploadReturnMessage = uploadReturnMessage + " ;" + content;
                    }
                }

        UploadRecordReturnMessageDto uploadRecordReturnMessageDto = new UploadRecordReturnMessageDto().setIsSuccess(true).setUploadReturnMessage(uploadReturnMessage).setClickId(customerUploadRecord.getClickId()).setPhotoColor(2);

//        //配置了“公众号助手发码加粉后入群”或者"成功添加企业微信后入群"
        if (Objects.nonNull(pageViewInfoDto.getCheckUploadNotDropoutWechatGroupSuccess()) && pageViewInfoDto.getCheckUploadNotDropoutWechatGroupSuccess() &&
              Objects.nonNull(pageViewInfoDto.getGroupChatQuitScene()) && Objects.nonNull(pageViewInfoDto.getGroupChatQuitTime())) {
            String quitContent = this.getDropOutChatGroupInfo(pageViewInfoDto);
            uploadReturnMessage = uploadReturnMessage + " ;" + quitContent;
            uploadRecordReturnMessageDto.setUploadReturnMessage(uploadReturnMessage);
        }

        if (Objects.isNull(customerUploadRecord.getRecordCode()) || !Integer.valueOf(ResponseCode.SUCCESS.getCode()).equals(customerUploadRecord.getRecordCode())) {
            ResponseCode responseCode = ResponseCode.getEnumById(customerUploadRecord.getRecordCode());
            uploadRecordReturnMessageDto.setIsSuccess(false).setUploadReturnMessage(uploadReturnMessage).setUploadFailReason(customerUploadRecord.getDescription()).setUploadFailReasonDesc(customerUploadRecord.getDescription()).setPhotoColor(1);
            if (ResponseCode.matchingTsCode(customerUploadRecord.getRecordCode()) && !Objects.isNull(responseCode)) {
                uploadRecordReturnMessageDto.setUploadReturnMessage(responseCode.getMsg());
            }
            if (ResponseCode.matchingZhiHuiCode(customerUploadRecord.getRecordCode())) {
                uploadRecordReturnMessageDto.setPhotoColor(0);
            }
            if (ResponseCode.matchingKouLiangUpload(customerUploadRecord.getRecordCode())) {
                uploadRecordReturnMessageDto.setUploadFailReason(ResponseCode.IS_KOU_LIANG_UPLOAD_UN_IN_WHERE.getMsg()).setPhotoColor(0);
            }
            if (StringUtils.isNotBlank(uploadRecordReturnMessageDto.getUploadFailReason()) && uploadRecordReturnMessageDto.getUploadFailReason().contains("参数：callback无效")) {
                uploadRecordReturnMessageDto.setUploadFailReason("callback无效");
            }
            if (!Objects.isNull(uploadRecordReturnMessageDto.getPhotoColor()) && uploadRecordReturnMessageDto.getPhotoColor().equals(1)) {
                if (
                    (Objects.equals(ResponseCode.EXCEED_CHECK_UPLOAD_TIME_ADD_WECHAT_SUCCESS_SET_TIME.getCode(), customerUploadRecord.getRecordCode()) && Boolean.TRUE.equals(customerUploadRecord.getCheckUploadTimeAddWechatSuccess()))
                    || Objects.equals(ResponseCode.OFFICIAL_ACCOUNT_FOLLOW_UN_REPEAT_UPLOAD.getCode(), customerUploadRecord.getRecordCode())
                ) {
                    uploadRecordReturnMessageDto.setUploadFailReason("未上报：" + uploadRecordReturnMessageDto.getUploadFailReason());
                } else {
                    uploadRecordReturnMessageDto.setUploadFailReason("上报失败：" + uploadRecordReturnMessageDto.getUploadFailReason());
                }
            }
            if (Objects.equals(ResponseCode.EXCEED_CHECK_UPLOAD_TIME_ADD_WECHAT_SUCCESS_SET_TIME.getCode(), customerUploadRecord.getRecordCode()) && Boolean.TRUE.equals(customerUploadRecord.getCheckUploadTimeAddWechatSuccess())) {
                uploadRecordReturnMessageDto.setUploadReturnMessage(uploadRecordReturnMessageDto.getUploadReturnMessage() + String.format("；符合上报条件时间与添加企业微信成功时间超过%s天不上报", customerUploadRecord.getCheckUploadTimeAddWechatSuccessTime()));
            }
        }
        if (StringUtils.isNotBlank(customerUploadRecord.getMatchOtherPvUrl())) {
            uploadRecordReturnMessageDto.setMatchOtherPvUrl(customerUploadRecord.getMatchOtherPvUrl());
        }
        return uploadRecordReturnMessageDto;
    }


    /**
     * 封装退群的时间和退群方式文案
     */
    private String getDropOutChatGroupInfo(PageViewInfoDto pageViewInfoDto) {
        if (Objects.isNull(pageViewInfoDto)){
            log.info("访客细察导出, 封装退群的详情为空");
            return null;
        }
        String content = "\n";
        try {
            if (Objects.nonNull(pageViewInfoDto.getGroupChatQuitScene()) && Objects.nonNull(pageViewInfoDto.getGroupChatQuitTime())) {
                content = String.format("进群后%s秒内退群不上报,退群时间: %s; 退群方式: %s", pageViewInfoDto.getCheckUploadNotDropoutWechatGroupSuccessTime(),dateTimeFormatter.format(pageViewInfoDto.getGroupChatQuitTime()), pageViewInfoDto.getGroupChatQuitScene().getName());
                log.info("访客细察导出, 封装退群文案, content = {}", content);
                return content;
            }
        }catch (Exception e){
            log.info("访客细察导出，封装退群的详情异常, customerDto = {}", JSONObject.toJSONString(pageViewInfoDto), e );
        }
        return "";
    }

    /**
     * 封装删除企业微信好友的详情
     * @param pageViewInfoDto 客资信息
     * @return 返回封装的内容
     */
    public String getDeleteFriendInfo(PageViewInfoDto pageViewInfoDto){

        if (Objects.isNull(pageViewInfoDto)){
            log.info("访客细察导出, 封装删除企业微信好友的详情为空");
            return null;
        }
        String content = "\n";
        try {
            if (Objects.nonNull(pageViewInfoDto.getEventType())) {
                switch (pageViewInfoDto.getEventType()) {
                    case EXTERNAL_CONTACT_DELETE_CUSTOMER_SERVICE:
                        content = String.format(" \n客户删除客服: \n 时间:%s; \n%s(%s) 删除 %s(%s)", dateTimeFormatter.format(pageViewInfoDto.getEventTime()), pageViewInfoDto.getWechatAppletName(), pageViewInfoDto.getWechatAppletExternalUserid(), pageViewInfoDto.getWechatAppletGroupChatName(), pageViewInfoDto.getWechatAppletUserid());
                        break;
                    case CUSTOMER_SERVICE_DELETE_EXTERNAL_CONTACT:
                        content = String.format(" \n客服删除客户: \n时间:%s; \n %s(%s) 删除 %s(%s)", dateTimeFormatter.format(pageViewInfoDto.getEventTime()), pageViewInfoDto.getWechatAppletGroupChatName(), pageViewInfoDto.getWechatAppletUserid(), pageViewInfoDto.getWechatAppletName(), pageViewInfoDto.getWechatAppletExternalUserid());
                        break;
                    default:
                        break;
                }
            }
        }catch (Exception e){
            log.info("访客细察导出，封装删除企业微信好友的详情异常, customerDto = {}", JSONObject.toJSONString(pageViewInfoDto), e );
        }
        return content;
    }

}
