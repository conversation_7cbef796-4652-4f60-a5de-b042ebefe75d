package ai.yiye.agent.collect.pageview.mapper;


import ai.yiye.agent.collect.pageview.dto.PageViewEmptyDto;
import ai.yiye.agent.domain.dto.TableNumDTO;
import ai.yiye.agent.domain.pageview.PageViewInfo;
import ai.yiye.agent.domain.vo.TracePageVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.Instant;
import java.util.List;

public interface PageViewInfoMapper extends BaseMapper<PageViewInfo> {
    int deleteByPrimaryKey(Long id);

    int insert(PageViewInfo record);

    int insertSelective(PageViewInfo record);

    PageViewInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PageViewInfo record);

    int updateByPrimaryKey(PageViewInfo record);

    PageViewInfo selectByPid(@Param("pid") String pid);

    void createPVSubTable(@Param("time") String time, @Param("nextMonth") String nextMonth, @Param("nextMonthLast") String nextMonthLast);

    int insertOrUpatePrivate(@Param("pageViewInfo") PageViewInfo pageViewInfo, @Param("tableSuffix") String tableSuffix);

    void batchUpdateByPid(@Param("list")List<TracePageVO> list,@Param("lastTime") Instant lastTime);

    Integer countTableNum(@Param("tableSchema") String tableSchema, @Param("tableName") String tableName);

    List<TableNumDTO> tableNum(@Param("tableSchema") String tableSchema, @Param("tableNames") List<String> tableNames);

    /**
     * 通过 firstPagePid 获取首次曝光的原始链接
     * @param firstPagePid
     * @return
     */
    String getOriginalUrlByPid(@Param("firstPagePid") String firstPagePid);

    /**
     * 根据pid 查找firstPagePid
     * @param pid
     * @return
     */
    PageViewInfo getFirstPagePid(@Param("pid") String pid);

    /**
     * 获取所有表为空的pageview表
     * @return
     */
    List<PageViewEmptyDto> getEmptyTable();

    /**
     * 获取表是否存在记录
     * @param schemaname
     * @param tableName
     * @return
     */
    Long getExistRecord(@Param("schemaname") String schemaname, @Param("tableName") String tableName);

    /**
     * 删除表
     * @param schemaname
     * @param tableName
     */
    void dropTable(@Param("schemaname") String schemaname, @Param("tableName") String tableName);
}
