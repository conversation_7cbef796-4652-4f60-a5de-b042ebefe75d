package ai.yiye.agent.collect.pageview.sevice;

import ai.yiye.agent.autoconfigure.upload.FileUpload;
import ai.yiye.agent.collect.pageview.config.AgentConf;
import ai.yiye.agent.collect.pageview.dto.PageViewInfoDto;
import ai.yiye.agent.collect.pageview.mapper.LandingPageMapper;
import ai.yiye.agent.collect.pageview.remote.BossBackendRemote;
import ai.yiye.agent.collect.pageview.remote.CustomerCreateExportTaskRemote;
import ai.yiye.agent.collect.pageview.sender.PageViewinfoSender;
import ai.yiye.agent.collect.pageview.sevice.readonly.PageViewExportPgReadonlyService;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.common.redis.RedisConstant;
import ai.yiye.agent.common.util.DateTimeUtil;
import ai.yiye.agent.common.util.FileUtil;
import ai.yiye.agent.common.util.ZipTools;
import ai.yiye.agent.domain.ActionTypeConfig;
import ai.yiye.agent.domain.CustomerExportTask;
import ai.yiye.agent.domain.CustomerExportTaskDto;
import ai.yiye.agent.domain.LandingPage;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.pageview.PageViewExportRequestBody;
import ai.yiye.agent.domain.result.Result;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@DS("postgresql")
public class LandingPageService extends ServiceImpl<LandingPageMapper, LandingPage> {

    private static final String DATE_FORMATTER = "yyyyMMddHHmmss";
    private static final String PREFIX_CERTIFICATE = "collect-pageview/pageviews/";
    private static final String EXPORT_EXCEL_FILE_PATH = System.getProperty("user.dir") + File.separator + "page_view_export" + File.separator;

    @Autowired
    private CustomerCreateExportTaskRemote customerCreateExportTaskRemote;
    @Autowired
    private FileUpload fileUpload;
    @Autowired
    private AgentConf agentConf;
    @Resource
    private ActionTypeConfigService actionTypeConfigService;
    @Autowired
    private CustomerUploadRecordService customerUploadRecordService;
    @Autowired
    private PageViewExportService pageViewExportService;
    @Autowired
    private PageViewExportPgReadonlyService pageViewExportPgReadonlyService;
    @Autowired
    private BossBackendRemote bossBackendRemote;

    @Resource
    private RedisTemplate<String, Object> defaultObjectRedisTemplate;

    public List<PageViewInfoDto> findPageViewInfos(List<String> permissionList, PageViewExportRequestBody pageViewExportRequestBody, Integer pageSize, Integer pageNum, OperationRole role) {
        List<PageViewInfoDto> pageViewInfoDtoList = pageViewExportPgReadonlyService.selectExportPageInfo(null, pageViewExportRequestBody.getLandingPageId(), pageViewExportRequestBody.getChannelId(), pageViewExportRequestBody.getStartTime(), pageViewExportRequestBody.getEndTime(), pageSize, pageNum);
        if (CollectionUtils.isNotEmpty(pageViewInfoDtoList)) {
            List<ActionTypeConfig> actionTypeConfigList = actionTypeConfigService.list();
            pageViewInfoDtoList.forEach(e -> customerUploadRecordService.reloadData(permissionList, e, actionTypeConfigList, role));
        }
        return pageViewInfoDtoList;
    }

    public Long selectExportPageInfoCount(PageViewExportRequestBody pageViewExportRequestBody) {
        return pageViewExportPgReadonlyService.selectExportPageInfoCount(null, pageViewExportRequestBody.getLandingPageId(), pageViewExportRequestBody.getChannelId(), pageViewExportRequestBody.getStartTime(), pageViewExportRequestBody.getEndTime());
    }

    @Autowired
    private PageViewinfoSender pageViewinfoSender;

    /**
     * 通过当前用户权限获取访客细查（异步）
     */
    public Result getBytesAsync(PageViewExportRequestBody pageViewExportRequestBody, OperationRole role) {
        if (StringUtils.isBlank(pageViewExportRequestBody.getStartTime())) {
            return Result.error(CustomerCodeEnum.START_TIME_NOT_NULL);
        }
        if (StringUtils.isBlank(pageViewExportRequestBody.getEndTime())) {
            return Result.error(CustomerCodeEnum.END_TIME_NOT_NULL);
        }
        //文件名称前缀
        LocalDateTime localDateTime = LocalDateTime.now();
        final String fileNameIndex = "访客细察_" + DateTimeFormatter.ofPattern(DateTimeUtil.EXPORT_CSV_FILE_INDEX).format(localDateTime);
        //如果是今天，重置结束时间，防止一边进数据，一边查询，导致数据重复
        String endTime = DateTimeFormatter.ofPattern("yyyy-MM-dd 23:59:59").format(localDateTime);
        if (endTime.equals(pageViewExportRequestBody.getEndTime())) {
            pageViewExportRequestBody.setEndTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(localDateTime));
        }else{
            pageViewExportRequestBody.setEndTime(DateTimeUtil.nextDaySubHour(pageViewExportRequestBody.getEndTime()));
        }
        //新增任务
        CustomerExportTaskDto taskDto = customerCreateExportTaskRemote.addTask(pageViewExportRequestBody.getAdvertiserAccountGroupId(), fileNameIndex + ".zip",
            Objects.isNull(pageViewExportRequestBody.getChannelId()) ? ExportTaskType.EXPORT_LANDING_PAGE_PV_INFO : ExportTaskType.EXPORT_LANDING_PAGE_CHANNEL_PV_INFO
            , role);
        if (ObjectUtils.isEmpty(taskDto)) {
            return Result.error(CustomerCodeEnum.NOT_EXPORTABLE);
        }
        taskDto.setOperationRole(role);
        pageViewinfoSender.sendUploadPageViewInfoFileToOss(taskDto.setFileNameIndex(fileNameIndex).setPageViewExportRequestBody(pageViewExportRequestBody));
        return Result.success(true);
    }

    /**
     * 异步处理文件上传OSS
     */
    public void uploadFileToOss(CustomerExportTaskDto taskDto) {
        log.info("异步导出访客细察开始 taskDto={}；", JSONObject.toJSONString(taskDto));
        List<File> srcFiles = new ArrayList<>();
        final String zipFileName = EXPORT_EXCEL_FILE_PATH + taskDto.getReportName();
        Long dataCount = 0L;
        try {
            String fileNameIndex = taskDto.getFileNameIndex();
            PageViewExportRequestBody pageViewExportRequestBody = taskDto.getPageViewExportRequestBody();
            dataCount = this.selectExportPageInfoCount(pageViewExportRequestBody);
            Long exportCsvEveryFileMaxRowNum = agentConf.getExportCsvEveryFileMaxRowNum();
            long startNum = 0, endNum = exportCsvEveryFileMaxRowNum;
            List<String> permissionList = bossBackendRemote.getPermissionList(pageViewExportRequestBody.getAdvertiserAccountGroupId(), TenantContextHolder.get());
            ai.yiye.agent.domain.AgentConf.License agentLicense = bossBackendRemote.getCustomerAgentLicense(TenantContextHolder.get());
            if (dataCount > 0) {
                int pageNum = 0;
                int pageSize = agentConf.getExportCsvQueryDbPageSize();
                List<PageViewInfoDto> pageViewInfoDtos;
                long outFileNum = dataCount <= exportCsvEveryFileMaxRowNum ? 1 : (((dataCount / exportCsvEveryFileMaxRowNum) <= 0 ? 1 : (dataCount / exportCsvEveryFileMaxRowNum)) + ((dataCount % exportCsvEveryFileMaxRowNum) > 0 ? 1 : 0));
                long totalPageNum = dataCount <= pageSize ? 1 : (((dataCount / pageSize) <= 0 ? 1 : (dataCount / pageSize)) + ((dataCount % pageSize) > 0 ? 1 : 0));
                log.info("访客细查导出 任务名称:{} 总行数:{} 每个文件最大行数:{} 每页查询行数:{} 总文件数:{} 总查询页数:{}", taskDto.getReportName(), dataCount, exportCsvEveryFileMaxRowNum, pageSize, outFileNum, totalPageNum);
                //更新任务进度
                CustomerExportTask customerExportTask = new CustomerExportTask().setId(taskDto.getId())
                    .setTotalNum(dataCount)
                    .setSchedule(0);
                this.customerCreateExportTaskRemote.updateById(customerExportTask);
                for (int i = 0; i < outFileNum; i++) {
                    //每页查询次数
                    long forNum = exportCsvEveryFileMaxRowNum / pageSize;
                    // 最后一页查询次数
                    if (i == (outFileNum - 1)) {
                        //最后一页
                        //余量
                        long margin = dataCount - (exportCsvEveryFileMaxRowNum * i);
                        forNum = margin / pageSize <= 0 ? 1 : (margin / pageSize) + (margin % pageSize > 0 ? 1 : 0);
                    }
                    log.info("访客细查导出 任务名称:{} 总文件数:{}  第{}个文件 当前文件查询总次数:{} 总查询页数:{} 当前页数:{}", taskDto.getReportName(), outFileNum, i + 1, forNum, totalPageNum, pageNum + 1);
                    File file;
                    FileOutputStream fop = null;
                    //生成文件名称
                    String childrenFileName = fileNameIndex + "（" + startNum + "-" + endNum + "）.csv";
                    try {
                        File foder = new File(EXPORT_EXCEL_FILE_PATH);
                        file = new File(EXPORT_EXCEL_FILE_PATH + childrenFileName);
                        srcFiles.add(file);
                        for (int ii = 0; ii < forNum; ii++) {
                            log.info("访客细查导出 任务名称:{} 总文件数:{} 第{}个文件,当前文件查询总次数:{}, 第{}页 总查询页数:{} 当前页数:{}", taskDto.getReportName(), outFileNum, i + 1, forNum, ii + 1, totalPageNum, pageNum + 1);
                            pageViewInfoDtos = this.findPageViewInfos(permissionList, pageViewExportRequestBody, pageSize, pageNum, taskDto.getOperationRole());
                            if (CollectionUtils.isEmpty(pageViewInfoDtos)) {
                                break;
                            }
                            String csvData = pageViewExportService.formatPageViewInfoList(agentLicense, permissionList, pageViewInfoDtos, (ii == 0), pageViewExportRequestBody);
                            if (StringUtils.isNotBlank(csvData)) {
                                try {
                                    if (!foder.isDirectory()) {
                                        foder.mkdirs();
                                    }
                                    if (!file.exists()) {
                                        file.createNewFile();
                                    }
                                    //追加
                                    if (Objects.isNull(fop)) {
                                        fop = new FileOutputStream(file);
                                    }
                                    byte[] contentInBytes = csvData.getBytes();
                                    fop.write(contentInBytes);
                                    fop.flush();
                                    //最后一页，关闭流
                                    if (ii == forNum - 1 && fop != null) {
                                        //关闭流
                                        fop.close();
                                    }
                                } catch (IOException e) {
                                    log.error("异步导出访客细察 error={}", e.getMessage(), e);
                                }

                            }
                            pageNum++;
                            //更新任务进度
                            BigDecimal divide = new BigDecimal(pageNum).multiply(new BigDecimal(100)).divide(new BigDecimal(totalPageNum), 0, BigDecimal.ROUND_HALF_UP);
                            customerExportTask = new CustomerExportTask()
                                .setId(taskDto.getId())
                                .setTotalNum(dataCount)
                                .setSchedule(divide.intValue());
                            customerCreateExportTaskRemote.updateById(customerExportTask);
                        }
                        startNum = endNum;
                        endNum = startNum + exportCsvEveryFileMaxRowNum;
                        log.info("异步导出访客细察输出文件结束, taskDto={}；filePath={}；", JSONObject.toJSONString(taskDto), childrenFileName);
                    } finally {
                        try {
                            //关闭流
                            if (fop != null) {
                                fop.close();
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            } else {
                // 没有数据 打包空文件
                String csvData = pageViewExportService.formatPageViewInfoList(agentLicense, permissionList, null, true, pageViewExportRequestBody);
                String filePath = this.createdCvsGetFilePath(csvData, fileNameIndex, startNum, endNum);
                if (StringUtils.isNotBlank(filePath)) {
                    srcFiles.add(new File(filePath));
                }
            }
            log.info("异步导出访客细察输出文件地址 taskDto={}；srcFiles={}", JSONObject.toJSONString(taskDto), srcFiles);
            if (CollectionUtils.isNotEmpty(srcFiles)) {
                ZipTools.toZip(zipFileName, srcFiles);
                File file = new File(zipFileName);
                if (file.exists()) {
                    String url = fileUpload.multipartUpload(zipFileName, PREFIX_CERTIFICATE + taskDto.getReportName());
                    if (StringUtils.isNotBlank(url)) {
                        taskDto.setUrl(url).setReportStatus(CustomerExportTaskStatusEnum.COMPLETED.getCode());
                    }
                }
            }
        } catch (Exception e) {
            taskDto.setReportStatus(CustomerExportTaskStatusEnum.FAILED.getCode());
            log.error("异步导出访客细察异常 taskDto={}", JSONObject.toJSONString(taskDto), e);
        } finally {
            CustomerExportTask customerExportTask = new CustomerExportTask()
                .setId(taskDto.getId())
                .setUrl(taskDto.getUrl())
                .setReportStatus(taskDto.getReportStatus())
                .setTotalNum(dataCount).setSchedule(100);
            Boolean updateed = customerCreateExportTaskRemote.updateById(customerExportTask);
            log.info("异步导出访客细察更新导出记录状态完毕 updateed={}；taskDto={}", updateed, JSONObject.toJSONString(taskDto));
            srcFiles.add(new File(zipFileName));
            srcFiles.stream().filter(File::exists).forEach(File::delete);
        }
    }

    private String increaseBomHeader(String csvData) {
        byte[] bom = new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
        return new String(bom, StandardCharsets.UTF_8) + csvData;
    }

    /**
     * 创建csv文件获取文件路径
     */
    public String createdCvsGetFilePath(String csvData, String fileNameIndex, long startNum, long endNum) {
        String childrenFileName = fileNameIndex + "（" + startNum + "-" + endNum + "）.csv";
        return FileUtil.writeFile(csvData, EXPORT_EXCEL_FILE_PATH, childrenFileName);
    }

    /**
     * 取消下载任务
     */
    public String cancelCustomerExportTask(Long taskId) {
        return customerCreateExportTaskRemote.cancelExportTask(taskId);
    }

    public static void main(String[] args) {
        System.out.println(new BigDecimal(1).multiply(new BigDecimal(100)).divide(new BigDecimal(3), 0, BigDecimal.ROUND_HALF_UP));
        System.out.println(3 / 2);
    }

    public LandingPage queryFlowSourceJumpPageStatus(Long landingPageId) {
        //先查询是否开启白名单
        String agentId = TenantContextHolder.get();
        //设置一下缓存
        String key = RedisConstant.WHITE_LIST_REPEAT_VISITOR_OPEN_FLAG + agentId;
        Object obj = defaultObjectRedisTemplate.opsForValue().get(key);
        Boolean b = Optional.ofNullable(obj).map(e -> {
            ai.yiye.agent.domain.AgentConf.License license = (ai.yiye.agent.domain.AgentConf.License) e;
            BaseStatusEnum flowSourceJumpPageStatisticsStatus = license.getFlowSourceJumpPageStatisticsStatus();
            return BaseStatusEnum.ENABLE.equals(flowSourceJumpPageStatisticsStatus);
        }).orElse(false);
        if (b) {
            return this.lambdaQuery().select(LandingPage::getAutoJumpType).eq(LandingPage::getId, landingPageId).last("limit 1").one();
        }
        return null;
    }
}
