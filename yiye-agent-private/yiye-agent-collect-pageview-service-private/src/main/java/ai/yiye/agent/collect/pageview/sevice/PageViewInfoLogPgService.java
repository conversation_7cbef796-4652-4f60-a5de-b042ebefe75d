package ai.yiye.agent.collect.pageview.sevice;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.collect.pageview.mapper.PageViewInfoLogMapper;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.domain.dto.CheckWcsAddLimitOnceDto;
import ai.yiye.agent.domain.dto.LandingPageAddLimitUserInfo;
import ai.yiye.agent.domain.dto.RepeatVisitorRecordDTO;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.pageview.PageViewInfoLog;
import ai.yiye.agent.domain.utils.UrlUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class PageViewInfoLogPgService extends ServiceImpl<PageViewInfoLogMapper, PageViewInfoLog> {

    @Resource
    private RedisTemplate<String, Object> objectRedisTemplate;


    @Resource
    private RedisTemplate<String, Object> defaultObjectRedisTemplate;


    /**
     * 统计微信“限加一次”
     */
    @Deprecated
    public int pgCheckWcsAddLimitOnce(CheckWcsAddLimitOnceDto cwaloDto) {

        String agentId = TenantContextHolder.get();
        //判断是否开启白名单
        String key = RedisConstant.WHITE_LIST_REPEAT_VISITOR_OPEN_FLAG + agentId;
        Object obj = defaultObjectRedisTemplate.opsForValue().get(key);
        log.info("页面内加粉，校验禁止访客，走旧版本逻辑,查询缓存白名单开关信息, key = {}, obj = {}", key, obj);
        if (Objects.isNull(obj)){
            return 0;
        }
        int num = 0;

        if (obj instanceof AgentConf.License) {
            AgentConf.License agentLicense = (AgentConf.License) obj;
            RepeatVisitorCheckEnum repeatVisitorCheckEnum = agentLicense.getRepeatVisitorCheckEnum();
            log.info("进行重复访客的校验, repeatVisitorCheckEnum = {}", repeatVisitorCheckEnum);
            if (Objects.isNull(repeatVisitorCheckEnum) || Objects.equals(repeatVisitorCheckEnum, RepeatVisitorCheckEnum.CLOSE)) {
                log.info("未开启白名单，不进行重复访客的校验，直接返回未加粉");
                return 0;
            } else {
                DataFilteringType dataFilteringType = cwaloDto.getDataFilteringType();
                String pid = cwaloDto.getPid();
                String uid = cwaloDto.getUid();
                String wechatAppletUnionid = cwaloDto.getWechatAppletUnionid();
                String wechatOpenid = cwaloDto.getWechatOpenid();
                String qiyePersonnelId = cwaloDto.getQiyePersonnelId();
                Long pmpId = cwaloDto.getPmpId();
                List<Long> dataFilteringPmpIds = new ArrayList<>();
                //Long landingPageId = cwaloDto.getLandingPageId();
                //1.276.0迭代，取消落地页维度判断
                Long landingPageId = null;
                Integer days = cwaloDto.getDays();
                DataFilteringPmpType dataFilteringPmpType = cwaloDto.getDataFilteringPmpType();
                String wechatUnionid = cwaloDto.getWechatUnionid();
                String wechatExternalUserid = cwaloDto.getWechatExternalUserid();
                //不传，默认7天
                if (Objects.isNull(days)) {
                    days = 7;
                }
                //传0，永久 修改为过去90天（1.273.0迭代，产品说选择永久也修改成7天）
                if (days <= 0) {
                    days = 7;
                }
                final LocalDateTime now = LocalDateTime.now();
                String endTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String startTime = now.minusDays(days).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

                if (StringUtils.isNotBlank(wechatUnionid) && StringUtils.isBlank(wechatAppletUnionid)) {
                    wechatAppletUnionid = wechatUnionid;
                }
                if (StringUtils.isBlank(wechatAppletUnionid)) {
                    wechatAppletUnionid = null;
                }
                if (StringUtils.isBlank(wechatOpenid)) {
                    wechatOpenid = null;
                }
                if (StringUtils.isBlank(qiyePersonnelId)) {
                    qiyePersonnelId = null;
                }
                //当前落地页
                if (Objects.isNull(dataFilteringType) || DataFilteringType.THIS_LANDING_PAGE.equals(dataFilteringType)) {
                    pmpId = null;
                }
                //项目内所有落地页
                if (DataFilteringType.THIS_PMP_LANDING_PAGE.equals(dataFilteringType)) {
                    landingPageId = null;
                }
                //账户内所有的落地页
                if (DataFilteringType.ALL_PMP_LANDING_PAGE.equals(dataFilteringType)) {
                    pmpId = null;
                    landingPageId = null;
                    if (Objects.equals(dataFilteringPmpType, DataFilteringPmpType.CUSTOM_PMP)) {
                        if (Objects.nonNull(cwaloDto.getDataFilteringPmpIds()) && cwaloDto.getDataFilteringPmpIds().length > 0) {
                            dataFilteringPmpIds.addAll(Arrays.asList(cwaloDto.getDataFilteringPmpIds()));
                            if (Objects.nonNull(cwaloDto.getPmpId())) {
                                dataFilteringPmpIds.add(cwaloDto.getPmpId());
                            }
                            log.info("落地页校验禁止重复访客添加, dataFilteringPmpIds  ={}", dataFilteringPmpIds);
                        }
                    }
                }

                //项目维度
                if(Objects.equals(repeatVisitorCheckEnum, RepeatVisitorCheckEnum.PMP)){
                    pmpId = cwaloDto.getPmpId();
                    landingPageId = null;
                }else {
                   //账户维度
                    pmpId = null;
                    landingPageId = null;
                }

                log.info("页面内加粉，校验禁止访客，走旧版本逻辑,最终的参数值, repeatVisitorCheckEnum = {},pmpId = {},landingPageId = {},wechatAppletUnionid = {}, wechatOpenid = {}, qiyePersonnelId = {}, wechatExternalUserid = {}, " +
                        "dataFilteringPmpIds = {},pid = {}, uid = {}", repeatVisitorCheckEnum, pmpId, landingPageId, wechatAppletUnionid, wechatOpenid, qiyePersonnelId, wechatExternalUserid, dataFilteringPmpIds,pid, uid);
                /**
                 * 如果没有用户信息 直接返回0 没有添加过
                 * //如果全都是空的 就进入兜底方法
                 */
                if (StringUtils.isAllBlank(wechatAppletUnionid, wechatOpenid, qiyePersonnelId, wechatExternalUserid)) {
                    //多加一层判断，原本是直接 return 0
                    return this.checkRepeatVisitor(pid, uid, pmpId, landingPageId, startTime, endTime, dataFilteringPmpIds);
                }

                //如果存在wechatOpenId或者wechatExternalUserid 先走缓存查询下
                boolean b = checkAddLimitUserInfoCache(landingPageId, pmpId, wechatExternalUserid, wechatOpenid, repeatVisitorCheckEnum);
                if (b) {
                    log.info("外部联系人或openid 重复访客禁止添加，pid =[{}], wechatOpenid =[{}], pmpId =[{}], landingPageId =[{}]", pid, wechatOpenid, pmpId, landingPageId);
                    return 1;
                }
                log.info("检查重复访客禁止添加，pid =[{}], uid ==[{}], wechatAppletUnionid =[{}], wechatOpenid =[{}], qiyePersonnelId =[{}], pmpId =[{}], landingPageId =[{}], startTime =[{}], endTime =[{}]", pid, uid, wechatAppletUnionid, wechatOpenid, qiyePersonnelId, pmpId, landingPageId, startTime, endTime);
                 num = baseMapper.pgCheckWcsAddLimitOnce(pid, uid, wechatAppletUnionid, wechatOpenid, qiyePersonnelId, wechatExternalUserid, pmpId, landingPageId, startTime, endTime, dataFilteringPmpIds);
                log.info("检查重复访客禁止添加,旧查询方式,num = [{}]", num);
            }
        }
       return num;
    }

    /**
     * 页面内加粉，进行校验重复访客限加一次
     * @param cwaloDto 入参
     * @return 校验结果
     */
    public int checkWcsAddLimitOnce(CheckWcsAddLimitOnceDto cwaloDto){

        log.info("页面内加粉,检查重复访客禁止添加，入参cwaloDto = {} ", JSONObject.toJSONString(cwaloDto));

        String agentId = TenantContextHolder.get();
        //判断是否开启白名单
        String key = RedisConstant.WHITE_LIST_REPEAT_VISITOR_OPEN_FLAG + agentId;
        Object obj = defaultObjectRedisTemplate.opsForValue().get(key);
        log.info("重复访客校验，查询缓存白名单开关信息, key = {}, obj = {}", key, obj);
        if (Objects.isNull(obj)){
            return 0;
        }

        if (obj instanceof AgentConf.License) {
            AgentConf.License agentLicense = (AgentConf.License) obj;
            RepeatVisitorCheckEnum repeatVisitorCheckEnum = agentLicense.getRepeatVisitorCheckEnum();
            log.info("进行重复访客的校验, repeatVisitorCheckEnum = {}", repeatVisitorCheckEnum);
            if (Objects.isNull(repeatVisitorCheckEnum) || Objects.equals(repeatVisitorCheckEnum, RepeatVisitorCheckEnum.CLOSE)) {
                log.info("未开启白名单，不进行重复访客的校验，直接返回未加粉");
                return 0;
            } else {

                DataFilteringType dataFilteringType = cwaloDto.getDataFilteringType();
                String wechatOpenid = cwaloDto.getWechatOpenid();


                //微信公众号蓝链
                if (StringUtils.isNotBlank(wechatOpenid)) {
                    boolean res = checkOfficialAccountAddLimitUserInfoCache(cwaloDto, dataFilteringType, repeatVisitorCheckEnum);
                    if (res) {
                        log.info("校验公众号链路是否已经加过粉，校验结果 res = {}", res);
                        return 1;
                    }
                }
                //判断微信机器人蓝链是否加过粉
                if (StringUtils.isNotBlank(cwaloDto.getWechatExternalUserid())) {
                    boolean res = checkRobotAddLimitUserInfoCache(cwaloDto, dataFilteringType, repeatVisitorCheckEnum);
                    if (res) {
                        log.info("校验微信客服机器人链路是否已经加过粉，校验结果 res = {}", res);
                        return 1;
                    }
                }
                //判断是否是小程序链路
                if (StringUtils.isNotBlank(cwaloDto.getWechatAppletUnionid())) {
                    boolean res = checkWechatAppletAddLimitUserInfoCache(cwaloDto, dataFilteringType, repeatVisitorCheckEnum);
                    if (res) {
                        log.info("校验微信小程序链路是否已经加过粉，校验结果 res = {}", res);
                        return 1;
                    }
                }
                //校验uid
                if (StringUtils.isNotBlank(cwaloDto.getUid())) {
                    boolean res = checkAddLimitUserInfoCacheByUid(cwaloDto, dataFilteringType, repeatVisitorCheckEnum);
                    if (res) {
                        log.info("根据uid校验是否已经加过粉，校验结果 res = {}", res);
                        return 1;
                    }
                }
                /**
                 * 通过设备校验
                 * 1. 从url中获取imei和idfa
                 */
                if (StringUtils.isNotBlank(cwaloDto.getUrl())) {
                    boolean res = checkAddLimitUserInfoCacheByMacroparameter(cwaloDto, repeatVisitorCheckEnum);
                    if (res) {
                        log.info("根据宏参数校验是否已经加过粉，校验结果 res = {}", res);
                        return 1;
                    }
                }

            }
        }
        return 0;
    }

    private boolean checkAddLimitUserInfoCacheByMacroparameter(CheckWcsAddLimitOnceDto cwaloDto, RepeatVisitorCheckEnum repeatVisitorCheckEnum) {
        Long advertiserAccountGroupId = cwaloDto.getPmpId();
        int days = cwaloDto.getDays();
        String url = cwaloDto.getUrl();
        if (StringUtils.isBlank(url)){
            log.error("根据宏参数校验是否重复加粉, url不能为空");
            return false;
        }
        String key = null;
        String device = UrlUtils.getDevice(url, cwaloDto.getReferrer());
        //开启了项目维度，只检查项目维度，不检查落地页维度
        if (StringUtils.isNotBlank(device)) {
            if (Objects.equals(repeatVisitorCheckEnum, RepeatVisitorCheckEnum.PMP)) {
                key = RedisConstant.LANDING_PAGE_DIFFERENT_MACROPARAMETER_ADD_FRIEND + advertiserAccountGroupId + ":" + device;
            } else {
                key = RedisConstant.LANDING_PAGE_DIFFERENT_MACROPARAMETER_ADD_FRIEND + device;
            }
        }
        if (StringUtils.isEmpty(key)) {
            return false;
        }
        Object obj = objectRedisTemplate.opsForValue().get(key);
        if (Objects.nonNull(obj) && obj instanceof RepeatVisitorRecordDTO){
            RepeatVisitorRecordDTO repeatVisitorRecordDTO = (RepeatVisitorRecordDTO) obj;
            Instant addTime = repeatVisitorRecordDTO.getAddTime();
            return checkTime(days, addTime);
        }
        return false;
    }


    /**
     * 根据uid查看，是否已经加过粉
     */
    public boolean checkAddLimitUserInfoCacheByUid(CheckWcsAddLimitOnceDto cwaloDto, DataFilteringType dataFilteringType, RepeatVisitorCheckEnum repeatVisitorCheckEnum) {
        Long advertiserAccountGroupId = cwaloDto.getPmpId();
        List<Long> dataFilteringPmpIds = new ArrayList<>();
        Long landingPageId = cwaloDto.getLandingPageId();
        String wechatOpenid = cwaloDto.getWechatOpenid();
        String key = null;
        int days = cwaloDto.getDays();
        String uid = cwaloDto.getUid();
        if (StringUtils.isBlank(uid)){
            log.error("根据uid校验是否重复加粉, uid不能为空");
            return false;
        }
        //当前落地页
//        if (Objects.isNull(dataFilteringType) || DataFilteringType.THIS_LANDING_PAGE.equals(dataFilteringType)){
//            key = RedisConstant.LANDING_PAGE_DIFFERENT_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + advertiserAccountGroupId + ":" + landingPageId + ":" + uid;
//        } else if (Objects.equals(DataFilteringType.THIS_PMP_LANDING_PAGE, dataFilteringType)) {
//            //当前项目的所有落地页，查询项目级别
//            key = RedisConstant.LANDING_PAGE_DIFFERENT_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + advertiserAccountGroupId  + ":" + uid;
//        }else if (Objects.equals(DataFilteringType.ALL_PMP_LANDING_PAGE, dataFilteringType)) {
//            //所有项目的落地页，查询全局级别
//            key = RedisConstant.LANDING_PAGE_DIFFERENT_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + uid;
//            dataFilteringPmpIds.addAll(Arrays.asList(cwaloDto.getDataFilteringPmpIds()));
//        }

        //开启了项目维度，只检查项目维度，不检查落地页维度
        if(Objects.equals(repeatVisitorCheckEnum, RepeatVisitorCheckEnum.PMP)){
            log.info("根据uid查看，是否已经加过粉,用户开启了项目维度，只检查当前项目的加粉情况");
            //当前项目的所有落地页，查询项目级别
            key = RedisConstant.LANDING_PAGE_DIFFERENT_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + advertiserAccountGroupId  + ":" + uid;
        }else{
            //开启了账户维度维度
            key = RedisConstant.LANDING_PAGE_DIFFERENT_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + uid;
            dataFilteringPmpIds.addAll(Arrays.asList(cwaloDto.getDataFilteringPmpIds()));
        }

        Object obj = objectRedisTemplate.opsForValue().get(key);
        log.info("根据uid查看，是否已经加过粉,禁止访客重复添加, key = {}, obj = {} ", key, obj);
        if (Objects.isNull(obj) && !dataFilteringPmpIds.isEmpty()){
            for (Long pmpid : dataFilteringPmpIds) {
                boolean res = checkRelatePmpAddLimitUserInfo(RedisConstant.LANDING_PAGE_DIFFERENT_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND, pmpid, uid, days);
                if (res){
                    log.info("根据uid查看，是否已经加过粉,禁止访客重复添加,查到加粉记录， pmpid = {}, uid = {}", pmpid, uid);
                    return true;
                }
            }
        }
        if (Objects.nonNull(obj) && obj instanceof RepeatVisitorRecordDTO){
            RepeatVisitorRecordDTO repeatVisitorRecordDTO = (RepeatVisitorRecordDTO) obj;
            Instant addTime = repeatVisitorRecordDTO.getAddTime();
            boolean res =  checkTime(days, addTime);
            log.info("根据uid查看,校验重复访客禁止添加,校验历史上加粉时间与当前系统相差的天数, res = {}", res);
            return res;
        }
        return false;
    }

    /**
     * 查询关联项目是否有加粉记录
     */
    public boolean checkRelatePmpAddLimitUserInfo(String key, Long advertiserAccountGroupId, String match, int days){
        String cacheKey = key + advertiserAccountGroupId + ":" + match;
        Object obj = objectRedisTemplate.opsForValue().get(key);
        log.info("禁止访客重复添加， 查询关联项目是否有加粉记录, key = {}, obj = {} ", cacheKey, obj);
        if (Objects.nonNull(obj) && obj instanceof RepeatVisitorRecordDTO){
            RepeatVisitorRecordDTO repeatVisitorRecordDTO = (RepeatVisitorRecordDTO) obj;
            Instant addTime = repeatVisitorRecordDTO.getAddTime();
            return checkTime(days, addTime);
        }
        return false;
    }


    /**
     * 校验公众号链路是否已经加过粉
     */
    public boolean checkOfficialAccountAddLimitUserInfoCache(CheckWcsAddLimitOnceDto cwaloDto, DataFilteringType dataFilteringType, RepeatVisitorCheckEnum repeatVisitorCheckEnum) {
        Long advertiserAccountGroupId = cwaloDto.getPmpId();
        List<Long> dataFilteringPmpIds = new ArrayList<>();
        Long landingPageId = cwaloDto.getLandingPageId();
        String wechatOpenid = cwaloDto.getWechatOpenid();
        int days = cwaloDto.getDays();
        String key = null;
        if (StringUtils.isBlank(wechatOpenid)){
            log.error("公众号链路校验是否重复加粉, wechatOpenid不能为空");
            return false;
        }
        //当前落地页
//        if (Objects.isNull(dataFilteringType) || DataFilteringType.THIS_LANDING_PAGE.equals(dataFilteringType)){
//            key = RedisConstant.LANDING_PAGE_OFFICIAL_BLUE_CHAIN_ADD_FRIEND + advertiserAccountGroupId + ":" + landingPageId + ":" + wechatOpenid;
//        } else if (Objects.equals(DataFilteringType.THIS_PMP_LANDING_PAGE, dataFilteringType)) {
//            //当前项目的所有落地页，查询项目级别
//            key = RedisConstant.LANDING_PAGE_OFFICIAL_BLUE_CHAIN_ADD_FRIEND + advertiserAccountGroupId  + ":" + wechatOpenid;
//        }else if (Objects.equals(DataFilteringType.ALL_PMP_LANDING_PAGE, dataFilteringType)) {
//            //所有项目的落地页，查询全局级别
//            key = RedisConstant.LANDING_PAGE_OFFICIAL_BLUE_CHAIN_ADD_FRIEND + wechatOpenid;
//            dataFilteringPmpIds.addAll(Arrays.asList(cwaloDto.getDataFilteringPmpIds()));
//        }

        //开启了项目维度，只检查项目维度，不检查落地页维度
        if(Objects.equals(repeatVisitorCheckEnum, RepeatVisitorCheckEnum.PMP)){
            log.info("用户开启了项目维度，只检查当前项目的加粉情况");
            //当前项目的所有落地页，查询项目级别
            key = RedisConstant.LANDING_PAGE_OFFICIAL_BLUE_CHAIN_ADD_FRIEND + advertiserAccountGroupId  + ":" + wechatOpenid;
        }else{
            //开启了账户维度维度
            key = RedisConstant.LANDING_PAGE_OFFICIAL_BLUE_CHAIN_ADD_FRIEND + wechatOpenid;
            dataFilteringPmpIds.addAll(Arrays.asList(cwaloDto.getDataFilteringPmpIds()));
        }

        Object obj = objectRedisTemplate.opsForValue().get(key);
        log.info("公众号链路校验重复访客禁止添加, key = {}, obj = {}, dataFilteringPmpIds = {} ", key, obj, dataFilteringPmpIds);
        if (Objects.isNull(obj) && !dataFilteringPmpIds.isEmpty()){
            for (Long pmpid : dataFilteringPmpIds) {
                boolean res = checkRelatePmpAddLimitUserInfo(RedisConstant.LANDING_PAGE_OFFICIAL_BLUE_CHAIN_ADD_FRIEND, pmpid, wechatOpenid, days);
                if (res){
                    log.info("公众号链路校验重复访客禁止添加,查询关联的项目,查到加粉记录， pmpid = {}, wechatOpenid = {}", pmpid, wechatOpenid);
                    return true;
                }
            }
        }
        if (Objects.nonNull(obj) && obj instanceof RepeatVisitorRecordDTO){
            RepeatVisitorRecordDTO repeatVisitorRecordDTO = (RepeatVisitorRecordDTO) obj;
            Instant addTime = repeatVisitorRecordDTO.getAddTime();
            boolean res =  checkTime(days, addTime);
            log.info("公众号链路校验重复访客禁止添加,校验历史上加粉时间与当前系统相差的天数, res = {}", res);
            return res;
        }
        return false;
    }

    /**
     * 校验是否在时间范围内
     * @param days 页面配置的天数
     * @param addTime 历史上成功加粉的时间
     * @return 校验结果
     */
    public boolean checkTime(int days, Instant addTime){
        if (Objects.nonNull(days)  && Objects.nonNull(addTime)){
            Instant now = Instant.now();
            // 计算两个Instant对象之间的天数差
            long result = ChronoUnit.DAYS.between(addTime, now);
            log.info("禁止重复访客，校验加粉的时间差, now = {}, addTime = {}, days = {}, result = {}", now, addTime, days, result);
            if (days <= 0){
                return result <= 30;
            }
            return result <= days;
        }
        return false;
    }

    public static void main(String[] args) {
        Instant now = Instant.now();
        // 计算两个Instant对象之间的天数差
        long result = ChronoUnit.DAYS.between(Instant.parse("2024-08-29T16:00:00Z"), now);
        log.info("result = {}", result);
    }


    /**
     * 校验微信客服机器人链路是否已经加过粉
     */
    public boolean checkRobotAddLimitUserInfoCache(CheckWcsAddLimitOnceDto cwaloDto, DataFilteringType dataFilteringType, RepeatVisitorCheckEnum repeatVisitorCheckEnum) {
        Long advertiserAccountGroupId = cwaloDto.getPmpId();
        List<Long> dataFilteringPmpIds = new ArrayList<>();
        Long landingPageId = cwaloDto.getLandingPageId();
        String wechatExternalUserid = cwaloDto.getWechatExternalUserid();
        String key = null;
        int days = cwaloDto.getDays();
        if (StringUtils.isBlank(wechatExternalUserid)){
            log.error("校验微信客服机器人链路是否已经加过粉, wechatExternalUserid不能为空");
            return false;
        }
        //当前落地页
//        if (Objects.isNull(dataFilteringType) || DataFilteringType.THIS_LANDING_PAGE.equals(dataFilteringType)){
//            key = RedisConstant.LANDING_PAGE_ROBOT_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + advertiserAccountGroupId + ":" + landingPageId + ":" + wechatExternalUserid;
//        } else if (Objects.equals(DataFilteringType.THIS_PMP_LANDING_PAGE, dataFilteringType)) {
//            //当前项目的所有落地页，查询项目级别
//            key = RedisConstant.LANDING_PAGE_ROBOT_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + advertiserAccountGroupId  + ":" + wechatExternalUserid;
//        }else if (Objects.equals(DataFilteringType.ALL_PMP_LANDING_PAGE, dataFilteringType)) {
//            //所有项目的落地页，查询全局级别
//            key = RedisConstant.LANDING_PAGE_ROBOT_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + wechatExternalUserid;
//            dataFilteringPmpIds.addAll(Arrays.asList(cwaloDto.getDataFilteringPmpIds()));
//        }

        //开启了项目维度，只检查项目维度，不检查落地页维度
        if(Objects.equals(repeatVisitorCheckEnum, RepeatVisitorCheckEnum.PMP)){
            log.info("用户开启了项目维度，只检查当前项目的加粉情况");
            //当前项目的所有落地页，查询项目级别
            key = RedisConstant.LANDING_PAGE_ROBOT_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + advertiserAccountGroupId  + ":" + wechatExternalUserid;
        }else{
            //开启了账户维度维度
            key = RedisConstant.LANDING_PAGE_ROBOT_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + wechatExternalUserid;
            dataFilteringPmpIds.addAll(Arrays.asList(cwaloDto.getDataFilteringPmpIds()));
        }

        Object obj = objectRedisTemplate.opsForValue().get(key);
        log.info("微信客服机器人链路校验重复访客禁止添加, key = {}, obj = {} ", key, obj);
        if (Objects.isNull(obj) && !dataFilteringPmpIds.isEmpty()){
            for (Long pmpid : dataFilteringPmpIds) {
                boolean res = checkRelatePmpAddLimitUserInfo(RedisConstant.LANDING_PAGE_ROBOT_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND, pmpid, wechatExternalUserid, days);
                if (res){
                    log.info("微信客服机器人链路校验重复访客禁止添加,查询关联的项目,查到加粉记录， pmpid = {}, wechatOpenid = {}", pmpid, wechatExternalUserid);
                    return true;
                }
            }
        }
        if (Objects.nonNull(obj) && obj instanceof RepeatVisitorRecordDTO){
            RepeatVisitorRecordDTO repeatVisitorRecordDTO = (RepeatVisitorRecordDTO) obj;
            Instant addTime = repeatVisitorRecordDTO.getAddTime();
            boolean res =  checkTime(days, addTime);
            log.info("校验微信客服机器人链路,重复访客禁止添加,校验历史上加粉时间与当前系统相差的天数, res = {}", res);
            return res;
        }
        return false;
    }

    /**
     * 校验小程序链路是否已经加过粉
     */
    public boolean checkWechatAppletAddLimitUserInfoCache(CheckWcsAddLimitOnceDto cwaloDto, DataFilteringType dataFilteringType, RepeatVisitorCheckEnum repeatVisitorCheckEnum) {
        Long advertiserAccountGroupId = cwaloDto.getPmpId();
        List<Long> dataFilteringPmpIds = new ArrayList<>();
        Long landingPageId = cwaloDto.getLandingPageId();
        String wechatExternalUserid = cwaloDto.getWechatExternalUserid();
        String key = null;
        int days = cwaloDto.getDays();
        String wechatAppletUnionid = cwaloDto.getWechatAppletUnionid();
        if (StringUtils.isBlank(wechatAppletUnionid)){
            log.error("校验小程序链路是否已经加过粉,wechatAppletUnionid不能为空");
            return false;
        }
        //当前落地页
//        if (Objects.isNull(dataFilteringType) || DataFilteringType.THIS_LANDING_PAGE.equals(dataFilteringType)){
//            key = RedisConstant.LANDING_PAGE_WECHAT_APPLET_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + advertiserAccountGroupId + ":" + landingPageId + ":" + wechatAppletUnionid;
//        } else if (Objects.equals(DataFilteringType.THIS_PMP_LANDING_PAGE, dataFilteringType)) {
//            //当前项目的所有落地页，查询项目级别
//            key = RedisConstant.LANDING_PAGE_WECHAT_APPLET_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + advertiserAccountGroupId  + ":" + wechatAppletUnionid;
//        }else if (Objects.equals(DataFilteringType.ALL_PMP_LANDING_PAGE, dataFilteringType)) {
//            //所有项目的落地页，查询全局级别
//            key = RedisConstant.LANDING_PAGE_WECHAT_APPLET_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + wechatAppletUnionid;
//        }


        //开启了项目维度，只检查项目维度，不检查落地页维度
        if(Objects.equals(repeatVisitorCheckEnum, RepeatVisitorCheckEnum.PMP)){
            log.info("校验小程序链路是否已经加过粉,用户开启了项目维度，只检查当前项目的加粉情况");
            //当前项目的所有落地页，查询项目级别
            key = RedisConstant.LANDING_PAGE_WECHAT_APPLET_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + advertiserAccountGroupId  + ":" + wechatAppletUnionid;
        }else{
            //开启了账户维度维度
            key = RedisConstant.LANDING_PAGE_WECHAT_APPLET_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND + wechatAppletUnionid;
            dataFilteringPmpIds.addAll(Arrays.asList(cwaloDto.getDataFilteringPmpIds()));
        }

        Object obj = objectRedisTemplate.opsForValue().get(key);
        log.info("小程序校验重复访客禁止添加, key = {}, obj = {}, dataFilteringPmpIds = {} ", key, obj, dataFilteringPmpIds);
        if (Objects.isNull(obj) && !dataFilteringPmpIds.isEmpty()){
            for (Long pmpid : dataFilteringPmpIds) {
                boolean res = checkRelatePmpAddLimitUserInfo(RedisConstant.LANDING_PAGE_WECHAT_APPLET_WECHAT_CUSTOMER_SERVICE_ADD_FRIEND, pmpid, wechatAppletUnionid, days);
                if (res){
                    log.info("小程序校验重复访客禁止添加,查询关联的项目,查到加粉记录， pmpid = {}, wechatAppletUnionid = {}", pmpid, wechatAppletUnionid);
                    return true;
                }
            }
        }
        if (Objects.nonNull(obj) && obj instanceof RepeatVisitorRecordDTO){
            RepeatVisitorRecordDTO repeatVisitorRecordDTO = (RepeatVisitorRecordDTO) obj;
            Instant addTime = repeatVisitorRecordDTO.getAddTime();
            boolean res =  checkTime(days, addTime);
            log.info("小程序链路,重复访客禁止添加,校验历史上加粉时间与当前系统相差的天数, res = {}", res);
            return res;
        }
        return false;
    }



    /**
     * 基于openId或者wechatExternalUserid查询缓存中是否存在用户的访客信息
     * 校验是否为重复访客
     */
    public boolean checkAddLimitUserInfoCache(Long landingPageId, Long pmpId, String wechatExternalUserid, String wechatOpenid, RepeatVisitorCheckEnum repeatVisitorCheckEnum) {
        boolean flag = false;
        log.info("查询缓存中的访客信息参数->wechatExternalUserid:{},wechatOpenid:{}", wechatExternalUserid, wechatOpenid);
        if (StringUtils.isNotBlank(wechatExternalUserid)) {
            String externalUseridKey = RedisConstant.LANDING_PAGE_SERVICE_ADD_LIMIT_ONCE_WECHAT_EXTERNAL_USERID_KEY + wechatExternalUserid;
            Object externalUseridOb = objectRedisTemplate.opsForValue().get(externalUseridKey);
            flag = execCheckLimit(externalUseridOb, landingPageId, pmpId, repeatVisitorCheckEnum, false, null);
        } else if (StringUtils.isNotBlank(wechatOpenid)) {
            String openIdKey = RedisConstant.LANDING_PAGE_SERVICE_ADD_LIMIT_ONCE_WECHAT_OPENID_KEY + wechatOpenid;
            Object openIdOb = objectRedisTemplate.opsForValue().get(openIdKey);
            flag = execCheckLimit(openIdOb, landingPageId, pmpId, repeatVisitorCheckEnum, false, null);
        }
        return flag;
    }

    //执行校验
    public boolean execCheckLimit(Object o, Long landingPageId, Long pmpId, RepeatVisitorCheckEnum repeatVisitorCheckEnum, Boolean acquisitionCheckFlag, String userId) {
        if (Objects.nonNull(o)) {
            LandingPageAddLimitUserInfo landingPageAddLimitUserInfo = (LandingPageAddLimitUserInfo) o;
            AddEnterpriseWechatStatus addEnterpriseWechatStatus = landingPageAddLimitUserInfo.getAddEnterpriseWechatStatus();
            log.info("获客链接校验加粉, addEnterpriseWechatStatus = {}", addEnterpriseWechatStatus);
            //为未加粉状态/加粉状态为空  则不走下面判断逻辑
            if (!AddEnterpriseWechatStatus.ADDED.equals(addEnterpriseWechatStatus) && !acquisitionCheckFlag) {
                return false;
            }

//            if (Objects.nonNull(landingPageId)) {
//                //有落地页ID就说明时当前落地页的维度禁止
//                //判断落地页维度是否为重复访客,查看缓存中的落地页ID是否与此本次访问的落地页ID一致
//                if (landingPageId.equals(landingPageAddLimitUserInfo.getLandingPageId())) {
//                    log.info("外部联系人缓存中检查重复访客禁止添加,落地页维度为当前落地页,landingPageId = {}", landingPageId);
//                    return true;
//                }
//            } else if (Objects.nonNull(pmpId)) {
//                //没有落地页ID且项目ID不为空就说明是项目维度的禁止
//                if (pmpId.equals(landingPageAddLimitUserInfo.getAdvertiserAccountGroupId())) {
//                    log.info("外部联系人缓存中检查重复访客禁止添加,落地页维度为当前项目,pmpId = {}", pmpId);
//                    return true;
//                }
//            } else {
//                //没有落地页ID且项目ID为空就说明是账户维度禁止
//                String agentId = TenantContextHolder.get();
//                if (StringUtils.isNotBlank(agentId) && agentId.equals(landingPageAddLimitUserInfo.getAgentId())) {
//                    log.info("外部联系人缓存中检查重复访客禁止添加,落地页维度为当前账户,agentId = {}", agentId);
//                    return true;
//                }
//            }

            //开启了项目维度，只检查项目维度，不检查落地页维度
            if(Objects.equals(repeatVisitorCheckEnum, RepeatVisitorCheckEnum.PMP)){
                //没有落地页ID且项目ID不为空就说明是项目维度的禁止
                if (Objects.nonNull(pmpId) && Objects.equals(pmpId, landingPageAddLimitUserInfo.getAdvertiserAccountGroupId())  ) {
                    log.info("获客链接校验加粉, 外部联系人缓存中检查重复访客禁止添加,落地页维度为当前项目,pmpId = {}", pmpId);
                    return AddEnterpriseWechatStatus.ADDED.equals(addEnterpriseWechatStatus);
                }else {
                    if (acquisitionCheckFlag) {
                        //如果是获客链接链路，需要查看在其他项目下面，是否有加粉的记录（1.278.0迭代进行的优化）
                        String pmpCacheKey = RedisConstant.LANDING_PAGE_SERVICE_ACQUISITION_ADD_ONLY_ONCE_KEY + userId + ":" +  pmpId;
                        Object pmpCacheObj = objectRedisTemplate.opsForValue().get(pmpCacheKey);
                        log.info("不同项目，获客链接校验是否已经加过粉, pmpCacheKey = {}, pmpCacheObj = {}", pmpCacheKey, JSONObject.toJSON(pmpCacheObj));
                        if (Objects.nonNull(pmpCacheObj) && pmpCacheObj instanceof LandingPageAddLimitUserInfo) {
                            LandingPageAddLimitUserInfo one = (LandingPageAddLimitUserInfo) pmpCacheObj;
                            if (Objects.equals(one.getAddEnterpriseWechatStatus(), AddEnterpriseWechatStatus.ADDED)){
                                return true;
                            }
                        }
                    }
                }
            }else {
                //账户维度
                String agentId = TenantContextHolder.get();
                if (StringUtils.isNotBlank(agentId) && agentId.equals(landingPageAddLimitUserInfo.getAgentId())) {
                    log.info("获客链接校验加粉, 外部联系人缓存中检查重复访客禁止添加,落地页维度为当前账户,agentId = {}", agentId);
                    if (Objects.equals(landingPageAddLimitUserInfo.getAddEnterpriseWechatStatus(), AddEnterpriseWechatStatus.ADDED)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 查询是否存在已经有加粉
     *
     * @param pid           pid
     * @param uid           uid
     * @param pmpId         项目ID
     * @param landingPageId 落地页ID
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return 满足条件的加粉数量
     */
    public int checkRepeatVisitor(String pid, String uid, Long pmpId, Long landingPageId, String startTime, String endTime, List<Long> dataFilteringPmpIds) {
        StopWatch stopWatch = new StopWatch();
        int num = 0;
        try {
            stopWatch.start();
            log.info("查询是否存在重复访客加粉,pid=[{}], uid=[{}], pmpId=[{}], landingPageId=[{}], startTime=[{}], endTime=[{}], dataFilteringPmpIds = {}", pid, uid, pmpId, landingPageId, startTime, endTime, dataFilteringPmpIds);
            if (StringUtils.isNotBlank(pid) && StringUtils.isNotBlank(uid) && StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
                num = baseMapper.checkRepeatVisitor(pid, uid, pmpId, landingPageId, startTime, endTime, dataFilteringPmpIds);
            }
            stopWatch.stop();
            return num;
        } catch (Exception e) {
            log.error("重复访客,查询是否存在已经有加粉异常,pid=[{}], uid=[{}], pmpId=[{}], landingPageId=[{}], startTime=[{}], endTime=[{}]", pid, uid, pmpId, landingPageId, startTime, endTime);
        } finally {
            log.info("uid = [{}],查询的重复加粉数量 num = [{}],耗时[{}]ms", uid, num, stopWatch.getTotalTimeMillis());
        }
        return num;
    }


    /**
     * 微信名片页限加一次
     *
     * @param cwaloDto
     * @return
     */
    public int pgCheckCustomerAcquisitionsAddLimitOnce(CheckWcsAddLimitOnceDto cwaloDto) {

        String agentId = TenantContextHolder.get();
        //判断是否开启白名单
        String key = RedisConstant.WHITE_LIST_REPEAT_VISITOR_OPEN_FLAG + agentId;
        Object obj = defaultObjectRedisTemplate.opsForValue().get(key);
        log.info("微信名片页限加一次,重复访客校验，走旧版本逻辑,查询缓存白名单开关信息, key = {}, obj = {}", key, obj);
        if (Objects.isNull(obj)){
            return 0;
        }

        if (obj instanceof AgentConf.License) {
            AgentConf.License agentLicense = (AgentConf.License) obj;
            RepeatVisitorCheckEnum repeatVisitorCheckEnum = agentLicense.getRepeatVisitorCheckEnum();
            log.info("微信名片页限加一次，进行重复访客的校验, repeatVisitorCheckEnum = {}", repeatVisitorCheckEnum);
            if (Objects.isNull(repeatVisitorCheckEnum) || Objects.equals(repeatVisitorCheckEnum, RepeatVisitorCheckEnum.CLOSE)) {
                log.info("微信名片页限加一次，未开启白名单，不进行重复访客的校验，直接返回未加粉");
                return 0;
            } else {

                String pid = cwaloDto.getPid();
                String uid = cwaloDto.getUid();
                if (StringUtils.isBlank(uid)) {
                    return 0;
                }
                DataFilteringType dataFilteringType = cwaloDto.getDataFilteringType();
                Long pmpId = cwaloDto.getPmpId();
                Long landingPageId = cwaloDto.getLandingPageId();

                //1.252.0 加上一层缓存查询 查询到记录且校验正确说明为重复添加访客
                boolean checked = checkAcquisitionAddLimitUserCache(landingPageId, pmpId, uid, repeatVisitorCheckEnum);
                if (checked) {
                    log.info("uid重复访客禁止添加，pid =[{}], uid =[{}], pmpId =[{}], landingPageId =[{}]", pid, uid, pmpId, landingPageId);
                    return 1;
                }
                /**
                 * 按设备信息再过滤
                 */
                String url = cwaloDto.getUrl();
                if(StringUtils.isNotBlank(url)){
                    String device = UrlUtils.getDevice(url);
                    if(StringUtils.isNotBlank(device)){
                        if (checkAcquisitionAddLimitUserCache(landingPageId, pmpId, device, repeatVisitorCheckEnum)) {
                            log.info("uid重复访客禁止添加，pid =[{}], uid =[{}], pmpId =[{}], landingPageId =[{}]", pid, url, pmpId, landingPageId);
                            return 1;
                        }
                    }
                }
            }
        }
        return 0;
    }

    /**
     * 基于openId或者wechatExternalUserid查询缓存中是否存在用户的访客信息
     * 校验是否为重复访客
     */
    public boolean checkAcquisitionAddLimitUserCache(Long landingPageId, Long pmpId, String userId, RepeatVisitorCheckEnum repeatVisitorCheckEnum) {
        log.info("查询获客助手缓存中的访客信息参数->uid:{},landingPageId:{},pmpId:{},repeatVisitorCheckEnum = {}", userId, landingPageId, pmpId, repeatVisitorCheckEnum);
        boolean flag = false;
        if (StringUtils.isNotBlank(userId)) {
            String externalUseridKey = RedisConstant.LANDING_PAGE_SERVICE_ACQUISITION_ADD_ONLY_ONCE_KEY + userId;
            Object uidUseridOb = objectRedisTemplate.opsForValue().get(externalUseridKey);
            if (Objects.isNull(uidUseridOb)){
                externalUseridKey = RedisConstant.LANDING_PAGE_SERVICE_ACQUISITION_ADD_ONLY_ONCE_KEY + userId + ":" + pmpId;
                uidUseridOb = objectRedisTemplate.opsForValue().get(externalUseridKey);
            }
            flag = execCheckLimit(uidUseridOb, landingPageId, pmpId, repeatVisitorCheckEnum, true, userId);
        }
        return flag;
    }

}
