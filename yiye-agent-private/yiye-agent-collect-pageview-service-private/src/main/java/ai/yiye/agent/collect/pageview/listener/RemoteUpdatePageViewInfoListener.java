package ai.yiye.agent.collect.pageview.listener;

import ai.yiye.agent.autoconfigure.rabbitmq.config.Constants;
import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.autoconfigure.web.exception.RestException;
import ai.yiye.agent.collect.pageview.config.AgentConf;
import ai.yiye.agent.collect.pageview.redis.LandingPageRedis;
import ai.yiye.agent.collect.pageview.redis.PageViewInfoRedis;
import ai.yiye.agent.collect.pageview.remote.AdvertiserAccountGroupRemote;
import ai.yiye.agent.collect.pageview.sender.EnterpriseWechatCustomerStaticsSender;
import ai.yiye.agent.collect.pageview.sender.LandingPageSender;
import ai.yiye.agent.collect.pageview.sender.SubmitDataSender;
import ai.yiye.agent.collect.pageview.sevice.*;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.common.util.DateTimeUtil;
import ai.yiye.agent.domain.AdvertiserAccountGroup;
import ai.yiye.agent.domain.Customer;
import ai.yiye.agent.domain.CustomerExportTaskDto;
import ai.yiye.agent.domain.LandingPageWechatCustomerService;
import ai.yiye.agent.domain.dto.*;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.enumerations.pageaction.PageActionType;
import ai.yiye.agent.domain.enumerations.pageaction.PageJumpType;
import ai.yiye.agent.domain.landingpage.LandingPageWechatOfficialAccount;
import ai.yiye.agent.domain.landingpage.QiyetuiWorkWechatUser;
import ai.yiye.agent.domain.landingpage.WorkWechatGroupChatUser;
import ai.yiye.agent.domain.landingpage.WorkWechatUser;
import ai.yiye.agent.domain.landingpage.dto.pageaction.LandingPageActionDTO;
import ai.yiye.agent.domain.pageview.PageViewInfo;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Mq远程更新pv信息
 */
@Slf4j
@Component
public class RemoteUpdatePageViewInfoListener {

    @Autowired
    private AgentConf agentConf;
    @Autowired
    private PageViewInfoService pageViewInfoService;
    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private LandingPageService landingPageService;
    @Autowired
    private AdvertiserAccountGroupRemote advertiserAccountGroupRemote;
    @Autowired
    private PageViewInfoRedis pageViewInfoRedis;
    @Autowired
    private SubmitDataSender submitDataSender;
    @Resource
    private LandingPageSender landingPageSender;
    @Resource
    private EnterpriseWechatCustomerStaticsSender enterpriseWechatCustomerStaticsSender;

    @Resource
    private LandingPageWechatCustomerServiceService landingPageWechatCustomerServiceService;

    @Resource
    private LandingPageWechatOfficialAccountService landingPageWechatOfficialAccountService;

    @Resource
    private CustomerService customerService;

    @Resource
    private LandingPageRedis landingPageRedis;

    /**
     * 添加【企业微信】成功
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.UPDATE_ADD_ENTERPRISE_WECHAT_STATUS_SUCCESS_KEY, value = @Queue(value = Constants.UPDATE_ADD_ENTERPRISE_WECHAT_STATUS_SUCCESS_KEY, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.UPDATE_ADD_ENTERPRISE_WECHAT_STATUS_SUCCESS_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void addEnterpriseWechatStatusSuccess(WorkWechatUser workWechatUser) {
        final String pid = workWechatUser.getPid();
        log.info("进入添加企业微信成功消息监听, pid=[{}];workWechatUser={}", pid, JSONObject.toJSONString(workWechatUser));
        //更新pgsql
        PageViewInfo pageViewInfo = pageViewInfoService.findByPid(pid);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.now(), ZoneId.systemDefault());
        pageViewInfo.setPid(pid)
            .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
            .setWorkWechatUserName(workWechatUser.getExternalName())
            .setWorkWechatUserId(workWechatUser.getExternalUserId())
            .setExternalUseridAddAt(workWechatUser.getExternalUseridAddAt())
            .setWechatUserIdMatchingStatus(workWechatUser.getWechatUserIdMatchingStatus())
            //错误信息
            .setWcsMatchingErrorStatus(workWechatUser.getWcsMatchingErrorStatus())
            .setIdentifyQrCodeWcsId(workWechatUser.getIdentifyQrCodeWcsId())
            .setIdentifyQrCodeWcsUserId(workWechatUser.getIdentifyQrCodeWcsUserId())
            .setIdentifyQrCodeWcsUserName(workWechatUser.getIdentifyQrCodeWcsUserName())
            .setCbUserIdMatchingWcsId(workWechatUser.getCbUserIdMatchingWcsId())
            .setCbUserIdMatchingWcsUserName(workWechatUser.getCbUserIdMatchingWcsUserName())
            .setWcsMatchingErrorMessage(workWechatUser.getWcsMatchingErrorMessage())
            .setConvertDataTime(LocalDateTime.of(localDateTime.toLocalDate(), LocalTime.of(localDateTime.getHour(), 0)).toInstant(ZoneOffset.of("+8")))
            .setUpdatedAt(Instant.now());
        log.info("进入加企业微信成功，更新pg数据库 pid={}，workWechatUser={}，pageViewInfo={}", pid, JSONObject.toJSONString(workWechatUser), pageViewInfo);
        try {
            //增加operation_type的设置值
            AdvertiserAccountGroup accountGroup = pageViewInfoRedis.getMarketingAdvertiserAccountGroup(RedisConstant.MARKETING_ADVERTISER_ACCOUNT_GROUP + pageViewInfo.getAdvertiserAccountGroupId(), () -> { //
                // 通过 siteId 查询 落地页相关信息;
                return advertiserAccountGroupRemote.fetchById(pageViewInfo.getAdvertiserAccountGroupId());
            }, 1L, TimeUnit.DAYS);

            if (accountGroup.getReplaceOperation().equals(ReplaceOperationType.OPERATION)) {
                pageViewInfo.setOperationType(OperationType.OPERATION);
            }

        } catch (Exception e) {
            log.error("增加operation_type的设置值错误----{}", e.getMessage(), e);
        }
        pageViewInfoService.insertOrUpdate(pageViewInfo);
        //更新clickhouse
//        PageViewInfoLog pageViewInfoLog = pageViewInfo.toPageViewInfoLog();
        //高并发场景 clickhouse 入库效率低，异步更新数据避免影响后面加粉成功 以及上报功能
//        log.info("进入加企业微信成功，发送mq消息，记录clickhouse数据库 pid={}，workWechatUser={}，pageViewInfo={}，pageViewInfoLog={}", pid, JSONObject.toJSONString(workWechatUser), pageViewInfo, pageViewInfoLog);
        //todo 是否需要去除clickhouse page_view_info
//        pageViewinfoSender.sendUpdatePageViewInfoLog(pageViewInfoLog);
//        log.info("进入加企业微信成功，返回结果 pid={}，workWechatUser={}，pageViewInfo={}，pageViewInfoLog={}", pid, JSONObject.toJSONString(workWechatUser), pageViewInfo, pageViewInfoLog);

        // 加粉成功，缓存加粉记录用于微信客服关键词回复判断是否加粉
//        if (StrUtil.isNotBlank(pageViewInfo.getWechatRobotExternalUserid())) {
//            landingPageRedis.cacheEnterpriseWechatAddFriendRecord(pageViewInfo.getWechatRobotExternalUserid());
//        }else {
//            String firstPagePid = pageViewInfo.getFirstPagePid();
//            //获取一跳页面的机器人信息
//            if(!StringUtils.equals(firstPagePid,pid)){
//                PageViewInfo firstPageViewInfo = pageViewInfoService.findByPid(firstPagePid);
//                if(Objects.nonNull(firstPageViewInfo) && StrUtil.isNotBlank(firstPageViewInfo.getWechatRobotExternalUserid())){
//                    landingPageRedis.cacheEnterpriseWechatAddFriendRecord(pageViewInfo.getWechatRobotExternalUserid());
//                }
//            }
//        }
        //添加企业微信客服成功 发送消息队列，进行落地页指标统计
        log.info("添加企业微信成功，匹配到的状态,status：[{}]", workWechatUser.getWechatUserIdMatchingStatus());
        this.addWechatCustomerServiceRecord(workWechatUser, pageViewInfo);

        //异步进行营销通行为数据上报
        log.info("添加企业微信成功,异步进行营销通行为数据上报,pid=[{}]", pageViewInfo.getPid());
        pageViewInfoService.addEnterpriseWechatStatusSuccessUpload(workWechatUser, pageViewInfo);

        //加粉成功，同步上报到广告监测链接服务
        pageViewInfoService.addEnterpriseWechatSuccessToAdTrace(workWechatUser, pageViewInfo);

    }

    /**
     * 添加企业微信客服成功 发送消息队列，进行落地页指标统计
     *
     * @param pageViewInfo pv信息
     */
    public void addWechatCustomerServiceRecord(WorkWechatUser workWechatUser, PageViewInfo pageViewInfo) {
        try {
            log.info("发送消息队列，进行落地页指标统计,userId:[{}]",workWechatUser.getUserId());
            if (Objects.equals(workWechatUser.getWechatUserIdMatchingStatus(), 1)) {
                //不同客服，配置同一个二维码图片，根据实际回传的userId进行数据统计(特殊情况)
                    if (Objects.nonNull(workWechatUser.getUserId())) {
                        //查询对应的客服ID
                        LandingPageWechatCustomerService lpwcs = landingPageWechatCustomerServiceService.getOne(new LambdaQueryWrapper<LandingPageWechatCustomerService>()
                            .eq(LandingPageWechatCustomerService::getWechatUserId, workWechatUser.getUserId())
                            .eq(Objects.nonNull(pageViewInfo), LandingPageWechatCustomerService::getAdvertiserAccountGroupId, pageViewInfo.getAdvertiserAccountGroupId())
                            .last("limit 1"));
                        if (Objects.nonNull(lpwcs)) {
                            log.info("查询对应客服的id[{}]", lpwcs.getId());
                            pageViewInfo.setWechatCustomerServiceId(lpwcs.getId());
                        }
                    }
                pageViewInfoService.sendMessageOfIdentifyOfficialAccount(pageViewInfo, IndicatorStatisticEventEnum.ADD_ENTERPRISE_WECHAT_SUCCESS);
            }
        } catch (Exception e) {
           log.error("添加企业微信客服成功 发送消息队列，进行落地页指标统计异常", e);
        }
    }

    /**
     * 添加【企微客户群】成功
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.UPDATE_PV_BY_ADD_WORK_WECHAT_GROUP_CHAT_SUCCESS_KEY, value = @Queue(value = Constants.UPDATE_PV_BY_ADD_WORK_WECHAT_GROUP_CHAT_SUCCESS_KEY, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.UPDATE_PV_BY_ADD_WORK_WECHAT_GROUP_CHAT_SUCCESS_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void updatePvByAddWorkWechatGroupChatSuccess(WorkWechatGroupChatUser wwgcu) {
        final String pid = wwgcu.getPid();
        log.info("进入添加企微客户群成功 pid={}，wwgcu={}", pid, JSONObject.toJSONString(wwgcu));
        //更新pgsql
        PageViewInfo pageViewInfo = pageViewInfoService.findByPid(pid);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.now(), ZoneId.systemDefault());
        pageViewInfo.setPid(pid)
            .setAddGroupChatStatus(AddGroupChatStatus.ADDED)
            //微信昵称                     串联合并客资 - 与客服建立好友关系或加群的访客微信昵称
            .setWorkWechatUserName(StringUtils.isBlank(pageViewInfo.getWorkWechatUserName()) ? wwgcu.getWechatUserName() : pageViewInfo.getWorkWechatUserName())
            //用户企业微信userid           串联合并客资 - 与客服建立好友关系或加群的访客对应的用户企业微信userid  或 用户通过微信客服机器人访问当前页面在进入微信客服会话窗口获取的用户userid
            .setWechatExternalUserid(StringUtils.isBlank(pageViewInfo.getWechatExternalUserid()) ? wwgcu.getWehcatUserUserId() : pageViewInfo.getWechatExternalUserid())
            //企业微信客户群名称            串联合并客资 - 添加企业微信客户群名称
            .setWorkWechatGroupChatName(StringUtils.isBlank(pageViewInfo.getWorkWechatGroupChatName()) ? wwgcu.getWorkWechatGroupChatName() : pageViewInfo.getWorkWechatGroupChatName())
            //企业微信客户群ID             串联合并客资 - 添加企业微信客户群id
            .setEnterpriseWechatGroupChatId(StringUtils.isBlank(pageViewInfo.getEnterpriseWechatGroupChatId()) ? wwgcu.getWorkWechatGroupChatId() : pageViewInfo.getEnterpriseWechatGroupChatId())
            //添加企业微信客户群方式        串联合并客资 - 添加企业微信客户群方式
            .setJoinGroupChatScene(wwgcu.getJoinGroupChatScene())
            //微信OpenID                 原字段 - 微信公众号Openid    优选当前落地页渠道获取的公众号OpenID  其次获取串联客资的另一落地页渠道访问时获取的公众号OpenID
            .setWechatAppletOpenid(StringUtils.isBlank(pageViewInfo.getWechatAppletOpenid()) ? wwgcu.getWechatAppletOpenid() : pageViewInfo.getWechatAppletOpenid())
            //微信UnionID                原字段 - 微信公众号UnionID    优选当前落地页渠道获取的公众号UnionID  其次获取串联客资的另一落地页渠道访问时获取的公众号UnionID
            .setWechatAppletUnionid(StringUtils.isBlank(pageViewInfo.getWechatAppletUnionid()) ? wwgcu.getWechatAppletUnionid() : pageViewInfo.getWechatAppletUnionid())
            //匹配后的参数
            .setMatchingSuccessOutsideChatId(wwgcu.getMatchingSuccessOutsideChatId())
            .setMatchingSuccessYiyeGroupId(wwgcu.getMatchingSuccessYiyeGroupId())
            //错误信息
            .setConvertDataTime(LocalDateTime.of(localDateTime.toLocalDate(), LocalTime.of(localDateTime.getHour(), 0)).toInstant(ZoneOffset.of("+8")))
            .setUpdatedAt(Instant.now());
        log.info("进入加企业微信客户群成功，更新pg数据库 pid={}，workWechatUser={}，pageViewInfo={}", pid, JSONObject.toJSONString(wwgcu), pageViewInfo);
        pageViewInfoService.insertOrUpdate(pageViewInfo);
        log.info("进入加企业微信客户群成功，返回结果 pid={}，workWechatUser={}，pageViewInfo={}，pageViewInfoLog={}", pid, JSONObject.toJSONString(wwgcu), pageViewInfo, wwgcu);

        //添加企业微信群成功 发送消息队列，进行落地页指标统计
        pageViewInfoService.sendMessageOfIdentifyOfficialAccount(pageViewInfo, IndicatorStatisticEventEnum.ADD_ENTERPRISE_WECHAT_GROUP_CHAT_SUCCESS);
    }


    /**
     * 新版企业推添加【企业微信】成功
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.QIYETUI_UPDATE_ADD_ENTERPRISE_WECHAT_STATUS_SUCCESS_KEY, value = @Queue(value = Constants.QIYETUI_UPDATE_ADD_ENTERPRISE_WECHAT_STATUS_SUCCESS_KEY, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.QIYETUI_UPDATE_ADD_ENTERPRISE_WECHAT_STATUS_SUCCESS_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void addQiyetuiEnterpriseWechatStatusSuccess(QiyetuiWorkWechatUser qiyetuiWorkWechatUser) {
        final String pid = qiyetuiWorkWechatUser.getPid();
        log.info("进入企业推加企业微信成功 pid={}，workWechatUser={}", pid, JSONObject.toJSONString(qiyetuiWorkWechatUser));
        //更新pgsql
        PageViewInfo pageViewInfo = pageViewInfoService.findByPid(pid);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.now(), ZoneId.systemDefault());
        pageViewInfo.setPid(pid)
            .setAddEnterpriseWechatStatus(AddEnterpriseWechatStatus.ADDED)
            .setWorkWechatUserName(qiyetuiWorkWechatUser.getExternalName())
            .setWorkWechatUserId(qiyetuiWorkWechatUser.getExternalUserId())
            .setWechatUserIdMatchingStatus(qiyetuiWorkWechatUser.getWechatUserIdMatchingStatus())
            //错误信息
            .setWcsMatchingErrorStatus(qiyetuiWorkWechatUser.getWcsMatchingErrorStatus())
            .setIdentifyQrCodeWcsId(qiyetuiWorkWechatUser.getIdentifyQrCodeWcsId())
            .setIdentifyQrCodeWcsUserId(qiyetuiWorkWechatUser.getIdentifyQrCodeWcsUserId())
            .setIdentifyQrCodeWcsUserName(qiyetuiWorkWechatUser.getIdentifyQrCodeWcsUserName())
            .setCbUserIdMatchingWcsId(qiyetuiWorkWechatUser.getCbUserIdMatchingWcsId())
            .setCbUserIdMatchingWcsUserName(qiyetuiWorkWechatUser.getCbUserIdMatchingWcsUserName())
            .setWcsMatchingErrorMessage(qiyetuiWorkWechatUser.getWcsMatchingErrorMessage())
            .setConvertDataTime(LocalDateTime.of(localDateTime.toLocalDate(), LocalTime.of(localDateTime.getHour(), 0)).toInstant(ZoneOffset.of("+8")))
            .setUpdatedAt(Instant.now())
            .setQiyePersonnelId(qiyetuiWorkWechatUser.getQiyePersonnelId()); //企业推2.0 personnelId 为全局唯一ID
        //兼容：表单/订单 串联 企业推加粉成功 修改企业推当前页面曝光的parentSubmitDataId
        if (Objects.nonNull(qiyetuiWorkWechatUser.getParentSubmitDataId()) && 0 != qiyetuiWorkWechatUser.getParentSubmitDataId()) {
            pageViewInfo.setParentSubmitDataId(qiyetuiWorkWechatUser.getParentSubmitDataId());
        }
        log.info("进入加企业微信成功，更新pg数据库 pid={}，workWechatUser={}，pageViewInfo={}", pid, JSONObject.toJSONString(qiyetuiWorkWechatUser), pageViewInfo);
        try {
            //增加operation_type的设置值
            AdvertiserAccountGroup accountGroup = pageViewInfoRedis.getMarketingAdvertiserAccountGroup(RedisConstant.MARKETING_ADVERTISER_ACCOUNT_GROUP + pageViewInfo.getAdvertiserAccountGroupId(), () -> { //
                // 通过 siteId 查询 落地页相关信息;
                return advertiserAccountGroupRemote.fetchById(pageViewInfo.getAdvertiserAccountGroupId());
            }, 1L, TimeUnit.DAYS);

            if (accountGroup.getReplaceOperation().equals(ReplaceOperationType.OPERATION)) {
                pageViewInfo.setOperationType(OperationType.OPERATION);
            }

        } catch (Exception e) {
            log.error("增加operation_type的设置值错误----{}", e.getMessage(), e);
        }
        pageViewInfoService.insertOrUpdate(pageViewInfo);
        //更新clickhouse
//        PageViewInfoLog pageViewInfoLog = pageViewInfo.toPageViewInfoLog();
        //高并发场景 clickhouse 入库效率低，异步更新数据避免影响后面加粉成功 以及上报功能
//        log.info("进入加企业微信成功，发送mq消息，记录clickhouse数据库 pid={}，workWechatUser={}，pageViewInfo={}，pageViewInfoLog={}", pid, JSONObject.toJSONString(qiyetuiWorkWechatUser), pageViewInfo, pageViewInfoLog);
        //todo 是否需要去除clickhouse page_view_info
//        pageViewinfoSender.sendUpdatePageViewInfoLog(pageViewInfoLog);
//        log.info("进入加企业微信成功，返回结果 pid={}，workWechatUser={}，pageViewInfo={}，pageViewInfoLog={}", pid, JSONObject.toJSONString(qiyetuiWorkWechatUser), pageViewInfo, pageViewInfoLog);

        //添加企业微信客服成功 发送消息队列，进行落地页指标统计
        log.info("企业推添加企业微信成功，匹配状态：[{}]", qiyetuiWorkWechatUser.getWechatUserIdMatchingStatus());
        if (Objects.equals(qiyetuiWorkWechatUser.getWechatUserIdMatchingStatus(), 1)) {
            pageViewInfoService.sendMessageOfIdentifyOfficialAccount(pageViewInfo, IndicatorStatisticEventEnum.ADD_ENTERPRISE_WECHAT_SUCCESS);
            //加粉成功，异步上报营销通
            pageViewInfoService.qiYeTuiAddEnterpriseWechatSuccessUpload(qiyetuiWorkWechatUser,pageViewInfo);

            //加粉成功，异步上传到广告监测服务
            pageViewInfoService.qiYeTuiAddEnterpriseWechatSuccessToAdTrace(qiyetuiWorkWechatUser,pageViewInfo);
        }
    }


    /**
     * 关注【微信公众号】成功
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.UPDATE_FOLLOW_WECHAT_OFFICIAL_ACCOUNT_STATUS_KEY, value = @Queue(value = Constants.UPDATE_FOLLOW_WECHAT_OFFICIAL_ACCOUNT_STATUS_KEY, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.UPDATE_FOLLOW_WECHAT_OFFICIAL_ACCOUNT_STATUS_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void followWechatOfficialAccount(PageViewMqDto pageViewMqDto) {
        log.info("关注【微信公众号】成功 pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pageViewMqDto.getPid(), pageViewMqDto.getFollowOfficialAccountAppId(), JSONObject.toJSONString(pageViewMqDto));
        final String pid = pageViewMqDto.getPid();
        final String followOfficialAccountAppId = pageViewMqDto.getFollowOfficialAccountAppId();
        //更新pgsql
        PageViewInfo pageViewInfo = pageViewInfoService.findByPid(pid);
        if (Objects.isNull(pageViewInfo)) {
            log.info("关注【微信公众号】无pv记录 pid={}", pageViewMqDto.getPid());
            return;
        }
        pageViewInfo.setPid(pid).setFollowOfficialAccountStatus(FollowStatus.FOLLOW).setUpdatedAt(Instant.now());
        //只有当公众号静默获取unionid失败后才会设置回调时候返回的unionid参数
        if (StringUtils.isBlank(pageViewInfo.getWechatUnionid())) {
            pageViewInfo.setWechatUnionid(pageViewMqDto.getWechatUnionid());
        }
        if (StringUtils.isBlank(pageViewInfo.getWechatOpenid())) {
            pageViewInfo.setWechatOpenid(pageViewMqDto.getWechatOpenid());
        }
        LandingPageWechatOfficialAccount lpwoa = landingPageWechatOfficialAccountService.getOne(
            new LambdaQueryWrapper<LandingPageWechatOfficialAccount>()
                .select(LandingPageWechatOfficialAccount::getNickName, LandingPageWechatOfficialAccount::getOpenAppId)
                .eq(LandingPageWechatOfficialAccount::getAppId, followOfficialAccountAppId)
                .orderByDesc(LandingPageWechatOfficialAccount::getCreatedAt).last(" limit 1")
        );
        if (!Objects.isNull(lpwoa)) {
            pageViewInfo.setThisPageWechatOfficialAccountName(lpwoa.getNickName())
                .setThisPageWechatOfficialOpenWeixinId(lpwoa.getOpenAppId())
                .setThisPageWechatOfficialAccountAppId(followOfficialAccountAppId);
        }
        //补偿：公众号动态二维码未长按，但是能关注；修改关注状态，发送长按上报
        if ((Objects.isNull(pageViewInfo.getOfficialIdentifyQrCodeStatus()) || OfficialIdentifyQrCodeStatus.UNRECOGNIZED.equals(pageViewInfo.getOfficialIdentifyQrCodeStatus())) &&
            (!Objects.isNull(pageViewMqDto.getUpdateFollowOfficialAccount()) && pageViewMqDto.getUpdateFollowOfficialAccount())
        ) {
            pageViewInfo.setClickJumpToWoaStatus(YesOrNoEnum.YES)
                .setFollowOfficialAccountAppId(followOfficialAccountAppId)
                .setOfficialIdentifyQrCodeStatus(OfficialIdentifyQrCodeStatus.IDENTIFIED);
            //上报长按公众号二维码行为
            log.info("长按识别公众号二维码行为上报 发送mq消息 pid={},pageViewInfo={}", pageViewInfo.getPid(), JSONObject.toJSONString(pageViewInfo));
            submitDataSender.sendIdentifyOfficialQrCodeUpload(pageViewInfo.getPid());

            log.info("长按识别公众号二维码,进行落地页指标统计,pid={}", pageViewInfo.getPid());
            pageViewInfoService.sendMessageOfIdentifyOfficialAccount(pageViewInfo, IndicatorStatisticEventEnum.IDENTIFY_OFFICIAL_QR_CODE);

        }

        try {
            //增加operation_type的设置值
            AdvertiserAccountGroup accountGroup = pageViewInfoRedis.getMarketingAdvertiserAccountGroup(RedisConstant.MARKETING_ADVERTISER_ACCOUNT_GROUP + pageViewInfo.getAdvertiserAccountGroupId(), () -> { //
                // 通过 siteId 查询 落地页相关信息;
                return advertiserAccountGroupRemote.fetchById(pageViewInfo.getAdvertiserAccountGroupId());
            }, 1L, TimeUnit.DAYS);

            if (accountGroup.getReplaceOperation().equals(ReplaceOperationType.OPERATION)) {
                pageViewInfo.setOperationType(OperationType.OPERATION);
            }

        } catch (Exception e) {
            log.error("增加operation_type的设置值错误----{}", e.getMessage(), e);
        }
        log.info("关注【微信公众号】成功，更新pg数据库 pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pid, followOfficialAccountAppId, JSONObject.toJSONString(pageViewMqDto));
        pageViewInfoService.insertOrUpdate(pageViewInfo);
        //更新clickhouse
//        PageViewInfoLog pageViewInfoLog = pageViewInfo.toPageViewInfoLog();
        //高并发场景 clickhouse 入库效率低，异步更新数据避免影响后面加粉成功 以及上报功能
//        log.info("关注【微信公众号】成功，发送mq消息，记录clickhouse数据库 pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pid, followOfficialAccountAppId, JSONObject.toJSONString(pageViewMqDto));
        //todo 是否需要去除clickhouse page_view_info
//        pageViewinfoSender.sendUpdatePageViewInfoLog(pageViewInfoLog);
        log.info("关注【微信公众号】成功，返回结果 pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pid, followOfficialAccountAppId, JSONObject.toJSONString(pageViewMqDto));

        // 关注公众号成功，缓存公众号关注记录用于微信客服关键词回复判断是否加粉
        if (StrUtil.isNotBlank(pageViewInfo.getWechatRobotExternalUserid())) {
            landingPageRedis.cacheOfficialAccountFollowRecord(pageViewInfo.getWechatRobotExternalUserid());
        } else {
            //获取一跳页面的机器人信息
            String firstPagePid = pageViewInfo.getFirstPagePid();
            if(!StringUtils.equals(firstPagePid,pid)){
                PageViewInfo firstPageViewInfo = pageViewInfoService.findByPid(firstPagePid);
                if(Objects.nonNull(firstPageViewInfo) && StrUtil.isNotBlank(firstPageViewInfo.getWechatRobotExternalUserid())){
                    landingPageRedis.cacheOfficialAccountFollowRecord(pageViewInfo.getWechatRobotExternalUserid());
                }
            }
        }

        //关注公众号成功 发送消息队列，进行落地页指标统计
        pageViewInfoService.sendMessageOfIdentifyOfficialAccount(pageViewInfo, IndicatorStatisticEventEnum.FOLLOW_OFFICIAL_ACCOUNT);

        //异步进行营销通行为数据上报
        this.pageActionUpload(pageViewInfo);

        //关注公众号，异步发生消息队列到广告监测服务
        pageViewInfoService.synToAdTraceOfIdentifyOfficialAccount(pageViewInfo);
    }



    /**
     * 关注公众号，进行营销通行为数据上报
     *
     * @param pv
     */
    public void pageActionUpload(PageViewInfo pv) {
        try {
            if (Objects.nonNull(pv)) {
                LandingPageActionDTO landingPageActionDTO = new LandingPageActionDTO();
                landingPageActionDTO.setActionType(PageActionType.FOLLOW_GZH_SC.getActionType());
                landingPageActionDTO.setAgentId(pv.getAgentId());
                landingPageActionDTO.setTraceUrl(pv.getUrl());
                landingPageActionDTO.setAdvertiserAccountId(pv.getAdvertiserAccountGroupId());
                landingPageActionDTO.setLandingPageId(pv.getLandingPageId());
                landingPageActionDTO.setReferrer(pv.getReferrer());
                landingPageActionDTO.setUa(pv.getUa());
                landingPageActionDTO.setFirstPid(pv.getFirstPagePid());
                landingPageActionDTO.setTs(System.currentTimeMillis());
                landingPageActionDTO.setPid(pv.getPid());
                landingPageActionDTO.setParentPid(pv.getParentPid());
                landingPageActionDTO.setAgentId(TenantContextHolder.get());
                landingPageActionDTO.setPageId(String.valueOf(pv.getLandingPageId()).concat("-").concat(TenantContextHolder.get()));
                //处理跳转类型
                if (Objects.isNull(landingPageActionDTO.getParentPid()) || Objects.equals(landingPageActionDTO.getPid(), landingPageActionDTO.getParentPid())) {
                    landingPageActionDTO.setPageJumpType(PageJumpType.FIRST_PAGE.getJumpType());
                } else {
                    landingPageActionDTO.setPageJumpType(PageJumpType.SECOND_PAGE.getJumpType());
                }
                log.info("关注公众号,消息投递进行营销通行为数据上报,landingPageActionDTO={}", JSONObject.toJSONString(landingPageActionDTO));
                landingPageSender.sendPageActionUpLoad(landingPageActionDTO);
            }
        } catch (Exception e) {
            log.error("关注公众号，进行营销通行为数据上报异常,pid=[{}]", pv.getPid(), e);
        }
    }


    /**
     * 电商商品购买成功 - 更新pv订单状态
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.UPDATE_SUCCESSFUL_PURCHASE_OF_ONLINE_SHOP_GOODS_STATUS_KEY, value = @Queue(value = Constants.UPDATE_SUCCESSFUL_PURCHASE_OF_ONLINE_SHOP_GOODS_STATUS_KEY, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.UPDATE_SUCCESSFUL_PURCHASE_OF_ONLINE_SHOP_GOODS_STATUS_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void updateSuccessfulPurchaseOfOnlineShopGoodsStatus(PageViewMqDto pageViewMqDto) {
        log.info("电商商品购买成功-更新pv订单状态 pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pageViewMqDto.getPid(), pageViewMqDto.getFollowOfficialAccountAppId(), JSONObject.toJSONString(pageViewMqDto));
        final String pid = pageViewMqDto.getPid();
        final String followOfficialAccountAppId = pageViewMqDto.getFollowOfficialAccountAppId();
        RLock fairLock = null;
        try {
            log.info("==============【电商商品购买成功-更新pv订单状态-获取redis锁开始：pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pageViewMqDto.getPid(), pageViewMqDto.getFollowOfficialAccountAppId(), JSONObject.toJSONString(pageViewMqDto));
            //获取锁 加锁是为了避免出现并发问题
            fairLock = redissonClient.getFairLock(RedisConstant.ONLINE_SHOP_PAGE_VIEW_LOCK_KEY + pid);
            //尝试加锁，最多等待10秒
            boolean res = fairLock.tryLock(10, 10, TimeUnit.SECONDS);
            if (!res) {
                log.info("==============【电商商品购买成功-更新pv订单状态-获取redis锁失败，pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pageViewMqDto.getPid(), pageViewMqDto.getFollowOfficialAccountAppId(), JSONObject.toJSONString(pageViewMqDto));
                throw new RestException("电商商品购买成功-更新pv订单状态-获取redis锁失败");
            }
            log.info("==============【电商商品购买成功-更新pv订单状态-获取redis锁成功，pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pageViewMqDto.getPid(), pageViewMqDto.getFollowOfficialAccountAppId(), JSONObject.toJSONString(pageViewMqDto));
            //更新pgsql
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.now(), ZoneId.systemDefault());
            PageViewInfo pageViewInfo = pageViewInfoService.findByPid(pid);
            //电商购买的订单总价、电商商品购买个数和电商购买订单数需要进行累加
            BigDecimal onlineShopBuyGoodsOrderPrice = pageViewInfo.getOnlineShopBuyGoodsOrderPrice();
            if (onlineShopBuyGoodsOrderPrice == null) {
                onlineShopBuyGoodsOrderPrice = pageViewMqDto.getOnlineShopBuyGoodsOrderPrice();
            } else {
                onlineShopBuyGoodsOrderPrice = onlineShopBuyGoodsOrderPrice.add(pageViewMqDto.getOnlineShopBuyGoodsOrderPrice());
            }
            Integer onlineShopBuyGoodsProductCount = pageViewInfo.getOnlineShopBuyGoodsProductCount();
            if (onlineShopBuyGoodsProductCount == null) {
                onlineShopBuyGoodsProductCount = pageViewMqDto.getOnlineShopBuyGoodsProductCount();
            } else {
                onlineShopBuyGoodsProductCount = onlineShopBuyGoodsProductCount + pageViewMqDto.getOnlineShopBuyGoodsProductCount();
            }
            Integer onlineShopBuyOrderCount = pageViewInfo.getOnlineShopBuyOrderCount();
            if (onlineShopBuyOrderCount == null) {
                onlineShopBuyOrderCount = 1;
            } else {
                onlineShopBuyOrderCount = onlineShopBuyOrderCount + 1;
            }
            pageViewInfo.setPid(pid)
                .setJumpToTaobaoAppStatus(pageViewInfo.getJumpToTaobaoAppStatus())
                .setOrderStatus(pageViewMqDto.getOrderStatus())
                .setPaymentType(pageViewMqDto.getPaymentType())
                .setOnlineShopBuyGoodsStatus(pageViewMqDto.getOnlineShopBuyGoodsStatus())
                .setOnlineShopBuyGoodsOrderId(pageViewMqDto.getOrderNo())
                .setOnlineShopBuyGoodsOrderPrice(onlineShopBuyGoodsOrderPrice)
                .setOnlineShopBuyGoodsProductCount(onlineShopBuyGoodsProductCount)
                .setOnlineShopBuyOrderCount(onlineShopBuyOrderCount)
                .setConvertDataTime(LocalDateTime.of(localDateTime.toLocalDate(), LocalTime.of(localDateTime.getHour(), 0)).toInstant(ZoneOffset.of("+8")))
                .setUpdatedAt(Instant.now());
            try {
                //增加operation_type的设置值
                AdvertiserAccountGroup accountGroup = pageViewInfoRedis.getMarketingAdvertiserAccountGroup(RedisConstant.MARKETING_ADVERTISER_ACCOUNT_GROUP + pageViewInfo.getAdvertiserAccountGroupId(), () -> { //
                    // 通过 siteId 查询 落地页相关信息;
                    return advertiserAccountGroupRemote.fetchById(pageViewInfo.getAdvertiserAccountGroupId());
                }, 1L, TimeUnit.DAYS);

                if (accountGroup.getReplaceOperation().equals(ReplaceOperationType.OPERATION)) {
                    pageViewInfo.setOperationType(OperationType.OPERATION);
                }
            } catch (Exception e) {
                log.error("增加operation_type的设置值错误----{}", e.getMessage(), e);
            }

            log.info("电商商品购买成功-更新pv订单状态，更新pg数据库 pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pid, followOfficialAccountAppId, JSONObject.toJSONString(pageViewMqDto));
            pageViewInfoService.insertOrUpdate(pageViewInfo);
            //更新clickhouse
//            PageViewInfoLog pageViewInfoLog = pageViewInfo.toPageViewInfoLog();
            //高并发场景 clickhouse 入库效率低，异步更新数据避免影响后面加粉成功 以及上报功能
//            log.info("电商商品购买成功-更新pv订单状态，发送mq消息，记录clickhouse数据库 pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pid, followOfficialAccountAppId, JSONObject.toJSONString(pageViewMqDto));
            //todo 是否需要去除clickhouse page_view_info
//            pageViewinfoSender.sendUpdatePageViewInfoLog(pageViewInfoLog);
            if (pageViewMqDto.getOnlineShopBuyGoodsProductCount() != null) {
                //落地页指标统计
                this.landingPageIndexStatistics(pageViewInfo, pageViewMqDto);
            }
            log.info("电商商品购买成功-更新pv订单状态，返回结果 pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pid, followOfficialAccountAppId, JSONObject.toJSONString(pageViewMqDto));
        } catch (Exception e) {
            log.error("电商商品购买成功-更新pv订单状态-异常，pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pageViewMqDto.getPid(), pageViewMqDto.getFollowOfficialAccountAppId(), JSONObject.toJSONString(pageViewMqDto), e);
            throw new RestException("电商商品购买成功-更新pv订单状态-异常", e);
        } finally {
            TenantContextHolder.clearContext();
            try {
                if (fairLock != null) {
                    fairLock.unlock();
                }
            } catch (Exception e) {
                log.error("电商商品购买成功-更新pv订单状态-释放redis锁异常，pid={}，followOfficialAccountAppId={}，pageViewMqDto={}", pageViewMqDto.getPid(), pageViewMqDto.getFollowOfficialAccountAppId(), JSONObject.toJSONString(pageViewMqDto), e);
            }
        }
    }

    /**
     * 上报完成后 - 修改pv上报状态
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.UPDATE_AD_UPLOAD_STATUS_KEY, value = @Queue(value = Constants.UPDATE_AD_UPLOAD_STATUS_KEY, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.UPDATE_AD_UPLOAD_STATUS_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void updateAdUploadStatus(String pid) {
        log.info("上报完成后修改pv上报状态 pid={}", pid);
        PageViewInfo pageViewInfo = pageViewInfoService.findByPid(pid);
        if (Objects.isNull(pageViewInfo)) {
            return;
        }
        pageViewInfo.setAdUploadStatus(YesOrNoEnum.YES).setUpdatedAt(Instant.now());
        pageViewInfoService.insertOrUpdate(pageViewInfo);
        //发送新查询统计
        LandingPageIndicatorStatisticsDTO eventDto = LandingPageIndicatorStatisticsDTO.toLandingPageIndicatorStatisticsDTO(pageViewInfo, IndicatorStatisticEventEnum.AD_UPLOAD);
        enterpriseWechatCustomerStaticsSender.sendLandingPageIndicatorStatistics(eventDto);
    }

    /**
     * 电商商品购买，进行落地页指标统计
     */
    public void landingPageIndexStatistics(PageViewInfo pageViewInfo,PageViewMqDto pageViewMqDto){
        try {
            if(Objects.nonNull(pageViewInfo) && Objects.nonNull(pageViewMqDto)) {
                LandingPageIndicatorStatisticsDTO statisticsDTO = new LandingPageIndicatorStatisticsDTO();
                statisticsDTO.setAdvertiserAccountGroupId(pageViewInfo.getAdvertiserAccountGroupId())
                    .setLandingPageId(pageViewInfo.getLandingPageId())
                    .setChannelId(pageViewInfo.getChannelId())
                    .setOnlineShopBuyGoodsSuccessNum(pageViewMqDto.getOnlineShopBuyGoodsProductCount().longValue())
                    .setPid(pageViewInfo.getPid())
                    .setDouyinCustomerSource(pageViewInfo.getDouyinCustomerSource())
                    .setUa(pageViewInfo.getUa())
                    .setSid(pageViewInfo.getSid())
                    .setUid(pageViewInfo.getUid())
                    .setIp(pageViewInfo.getIp())
                    .setParentPid(pageViewInfo.getParentPid())
                    .setFirstPagePid(pageViewInfo.getFirstPagePid())
                    .setIndicatorStatisticEventEnum(IndicatorStatisticEventEnum.ORDER_PAY_SUCCESS_TO_ON_LINE_SHOP)
                    .setPlatformId(Platform.getEnumById(pageViewInfo.getPlatformId()))
                    .setProvince(pageViewInfo.getProvince())
                    .setCity(pageViewInfo.getCity())
                    .setDevice(pageViewInfo.getDevice())
                    .setOs(pageViewInfo.getOs())
                    .setOsType(pageViewInfo.getOsType())
                    .setBrowser(pageViewInfo.getBrowser())
                    .setBrowserType(pageViewInfo.getBrowserType())
                    .setNetworkType(pageViewInfo.getNetworkType())
                    .setUrl(pageViewInfo.getUrl())
                    .setClickId(pageViewInfo.getClickId())
                    .setCreativeId(pageViewInfo.getCreativeId())
                    .setAdAccountId(pageViewInfo.getAccountId())
                    .setAdgroupId(pageViewInfo.getAdgroupId())
                    .setCampaignId(pageViewInfo.getCampaignId())
                    .setLengthOfStay(pageViewInfo.getLengthOfStay())
                    .setWechatOfficialHistoryArticleStatus(pageViewInfo.getWechatOfficialHistoryArticleStatus())
                    .setLandingPageType(pageViewInfo.getLandingPageType())
                    .setLinkType(pageViewInfo.getLinkType())
                    .setAdvertiseSource(pageViewInfo.getAdvertiseSource())
                    .setJumpToTaobaoAppStatus(pageViewInfo.getJumpToTaobaoAppStatus())
                    .setOnlineShopBuyGoodsStatus(pageViewInfo.getOnlineShopBuyGoodsStatus())
                    .setWechatUserIdMatchingStatus(pageViewInfo.getWechatUserIdMatchingStatus())
                    .setPaymentType(pageViewInfo.getPaymentType())
                    .setFlowSource(pageViewInfo.getFlowSource());
                //查询客资
                if(StringUtils.isNotBlank(pageViewInfo.getPid())){
                    Customer customer = customerService.queryByPid(pageViewInfo.getPid());
                    if(Objects.nonNull(customer)){
                        statisticsDTO.setTransactionAmount(customer.getTotalPrice());
                    }
                }
                if (Objects.nonNull(pageViewInfo.getCreatedAt())){
                    statisticsDTO.setPageViewTime(LocalDateTime.ofInstant(pageViewInfo.getCreatedAt(), ZoneId.systemDefault())).setConvertTime(LocalDateTime.now());
                }else {
                    statisticsDTO.setPageViewTime(LocalDateTime.now()).setConvertTime(LocalDateTime.now());
                }
                landingPageSender.sendLandingPageIndicatorStatistics(statisticsDTO);
            }
        }catch (Exception e) {
            log.error("电商商品购买，进行落地页指标统计异常", e);
        }
    }


    /**
     * 上报完成后，根据蓝链匹配的pv，修改pv信息
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.UPLOAD_SUCCESS_MATCHING_PV_UPDATE_PAGE_VIEW_INFO_KEY, value = @Queue(value = Constants.UPLOAD_SUCCESS_MATCHING_PV_UPDATE_PAGE_VIEW_INFO_KEY, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.UPLOAD_SUCCESS_MATCHING_PV_UPDATE_PAGE_VIEW_INFO_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void sendMatchingPvUpdatePageViewInfo(UpdateFlowSourceDto ufsDto) {
        log.info("上报完成后根据蓝链匹配的pv修改pv信息 updateFlowSourceDto={}；", (Objects.isNull(ufsDto) ? null : JSONObject.toJSONString(ufsDto)));
        if (Objects.isNull(ufsDto) || StringUtil.isBlank(ufsDto.getThisPid())) {
            return;
        }
        final PageViewInfo matchingPvInfo = ufsDto.getMatchingPvInfo();
        if (Objects.isNull(matchingPvInfo)) {
            return;
        }
        FlowSource flowSource = Platform.OCEAN_ENGINE.equals(Platform.getEnumById(matchingPvInfo.getPlatformId())) ? matchingPvInfo.getFlowSource() : null;
        PageViewInfo pageViewInfo = pageViewInfoService.findByPid(ufsDto.getThisPid());
        pageViewInfo.setFlowSource(flowSource)
            .setUpdatedAt(Instant.now())
            .setAccountId(matchingPvInfo.getAccountId())
            .setCampaignId(matchingPvInfo.getCampaignId())
            .setAdgroupId(matchingPvInfo.getAdgroupId())
            .setPlatformId(matchingPvInfo.getPlatformId())
            .setFirstPagePid(matchingPvInfo.getFirstPagePid())
            .setParentPid(matchingPvInfo.getPid())
            //复制上级页面曝光数据
            .setLinkType(matchingPvInfo.getLinkType())
            .setWechatCustomerServiceRobotId(matchingPvInfo.getWechatCustomerServiceRobotId())
            .setOpenKfId(matchingPvInfo.getOpenKfId())
            .setWechatCustomerServiceRobotName(matchingPvInfo.getWechatCustomerServiceRobotName())
            .setWechatRobotExternalUserid(matchingPvInfo.getWechatRobotExternalUserid())
            ////设置url参数给蓝链链接（不确定，暂时先不加，代码保留）
            //.setUrl(UrlUtils.appendMatchingUrlParamsToBeforeUrl(pageViewInfo.getUrl(), matchingPvInfo.getUrl(), UrlUtils.splitUrlToColumnList(pageViewInfo.getUrl())))
            //.setReferrer(UrlUtils.appendMatchingUrlParamsToBeforeUrl(pageViewInfo.getReferrer(), matchingPvInfo.getReferrer(), UrlUtils.splitUrlToColumnList(pageViewInfo.getReferrer())))
        ;
        try {
            //增加operation_type的设置值
            AdvertiserAccountGroup accountGroup = pageViewInfoRedis.getMarketingAdvertiserAccountGroup(RedisConstant.MARKETING_ADVERTISER_ACCOUNT_GROUP + pageViewInfo.getAdvertiserAccountGroupId(), () -> { //
                // 通过 siteId 查询 落地页相关信息;
                return advertiserAccountGroupRemote.fetchById(pageViewInfo.getAdvertiserAccountGroupId());
            }, 1L, TimeUnit.DAYS);

            if (accountGroup.getReplaceOperation().equals(ReplaceOperationType.OPERATION)) {
                pageViewInfo.setOperationType(OperationType.OPERATION);
            }
        } catch (Exception e) {
            log.error("增加operation_type的设置值错误----{}", e.getMessage(), e);
        }

        pageViewInfoService.insertOrUpdate(pageViewInfo);
        //更新clickhouse
//        PageViewInfoLog pageViewInfoLog = pageViewInfo.toPageViewInfoLog();
        //高并发场景 clickhouse 入库效率低，异步更新数据避免影响后面加粉成功 以及上报功能
        //todo 是否需要去除clickhouse page_view_info
//        pageViewinfoSender.sendUpdatePageViewInfoLog(pageViewInfoLog);
    }


    /**
     * 进入【微信客服机器人会话】，更新进入会话状态
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.UPDATE_INTO_WECHAT_CUSTOMER_SERVICE_SESSION_STATUS_KEY, value = @Queue(value = Constants.UPDATE_INTO_WECHAT_CUSTOMER_SERVICE_SESSION_STATUS_KEY, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.UPDATE_INTO_WECHAT_CUSTOMER_SERVICE_SESSION_STATUS_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void sendUpdateIntoWechatCustomerServiceSessionStatus(WechatCustomerRobotUpdatePageViewMqDto wcrupmDto) {
        log.info("上报完成后根据蓝链匹配的pv修改pv信息 updateFlowSourceDto={}；", (Objects.isNull(wcrupmDto) ? null : JSONObject.toJSONString(wcrupmDto)));
        if (Objects.isNull(wcrupmDto) || StringUtil.isBlank(wcrupmDto.getPid())) {
            return;
        }
        PageViewInfo pageViewInfo = pageViewInfoService.findByPid(wcrupmDto.getPid());
        if(Objects.isNull(pageViewInfo)){
            log.error("上报完成后根据蓝链匹配的pv修改pv信息，未找到对应的pv信息，pid={}",wcrupmDto.getPid());
            return ;
        }
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.now(), ZoneId.systemDefault());
        LambdaUpdateWrapper<PageViewInfo> updatePvInfo = new LambdaUpdateWrapper<PageViewInfo>()
            .set(PageViewInfo::getUpdatedAt, Instant.now())
            /**
             * 微信客服机器人，id（一叶系统内）
             */
            .set(PageViewInfo::getWechatCustomerServiceRobotId, wcrupmDto.getWechatCustomerServiceRobotId())
            /**
             * 微信客服机器人，开放平台id
             */
            .set(PageViewInfo::getOpenKfId, wcrupmDto.getOpenKfid())
            .set(PageViewInfo::getCorpid, wcrupmDto.getCorpid())
            /**
             * 微信客服机器人，名称
             */
            .set(PageViewInfo::getWechatCustomerServiceRobotName, wcrupmDto.getWechatCustomerServiceRobotName())
            /**
             * 微信客服机器人访客userId
             */
            .set(PageViewInfo::getWechatRobotExternalUserid, wcrupmDto.getWechatRobotExternalUserid())
            /**
             * 是否进入微信客服会话状态
             */
            .set(PageViewInfo::getIntoWechatCustomerServiceSessionStatus, YesOrNoEnum.YES)

            .set(PageViewInfo::getConvertDataTime, LocalDateTime.of(localDateTime.toLocalDate(), LocalTime.of(localDateTime.getHour(), 0)).toInstant(ZoneOffset.of("+8")))
        ;
        try {
            //增加operation_type的设置值
            AdvertiserAccountGroup accountGroup = pageViewInfoRedis.getMarketingAdvertiserAccountGroup(RedisConstant.MARKETING_ADVERTISER_ACCOUNT_GROUP + pageViewInfo.getAdvertiserAccountGroupId(), () -> { //
                // 通过 siteId 查询 落地页相关信息;
                return advertiserAccountGroupRemote.fetchById(pageViewInfo.getAdvertiserAccountGroupId());
            }, 1L, TimeUnit.DAYS);
            if (accountGroup.getReplaceOperation().equals(ReplaceOperationType.OPERATION)) {
                updatePvInfo.set(PageViewInfo::getOperationType, OperationType.OPERATION);
            }
        } catch (Exception e) {
            log.error("增加operation_type的设置值错误----{}", e.getMessage(), e);
        }
        pageViewInfoService.update(updatePvInfo
            .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoUpdateTime()))
            .eq(PageViewInfo::getPid, wcrupmDto.getPid()));

        //发送消息到clickhouse进行进线数的统计
        pageViewInfoService.synSendIntoWechatCustomerServiceSessionInfo(pageViewInfo, IndicatorStatisticEventEnum.INTO_WECHAT_CUSTOMER_SERVICE_SESSION_STATUS);

        //pageViewInfoService.insertOrUpdate(pageViewInfo);
        //更新clickhouse
//        PageViewInfoLog pageViewInfoLog = pageViewInfo.toPageViewInfoLog();
        //高并发场景 clickhouse 入库效率低，异步更新数据避免影响后面加粉成功 以及上报功能
        //todo 是否需要去除clickhouse page_view_info
//        pageViewinfoSender.sendUpdatePageViewInfoLog(pageViewInfoLog);
    }


    /**
     * 小程序立即预约更新曝光数据 - 更新pv订单状态
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.WECHAT_APPLE_PHONE_QUEUE, value = @Queue(value = Constants.WECHAT_APPLE_PHONE_QUEUE, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.WECHAT_APPLE_PHONE_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void updatePageViewBywechatApplePhone(Customer customer) {
        String pid = customer.getPid();
        PageViewInfo pageViewInfo = pageViewInfoService.findByPid(pid);

        //原有的逻辑，默认值是设置填单
        FillStatus fill = FillStatus.FILL;
        //如果是字节小程序，表单类型为空，不需要设置pv的填单状态
        if (Objects.equals(customer.getCustomerType(),SubmitType.DOUYIN_APPLET) && Objects.isNull(customer.getFormType())){
            log.info("表单类型为空，不需要设置pv的填单状态, pid = {}", pid );
            fill = FillStatus.NOT_FILL;
        }
        pageViewInfo.setFill(fill)
            .setSubmitDataId(Objects.isNull(pageViewInfo.getSubmitDataId()) ? customer.getSubmitDataId() : pageViewInfo.getSubmitDataId())
            .setCustomerId(Objects.isNull(pageViewInfo.getCustomerId()) ? customer.getId() : pageViewInfo.getCustomerId())
            .setWidgetTemplateType(Objects.isNull(pageViewInfo.getWidgetTemplateType()) ? customer.getCustomerType() : pageViewInfo.getWidgetTemplateType());
        pageViewInfo.setFillAt(Objects.isNull(pageViewInfo.getFillAt()) ? Instant.now() : pageViewInfo.getFillAt());
        try {
            //增加operation_type的设置值
            AdvertiserAccountGroup accountGroup = pageViewInfoRedis.getMarketingAdvertiserAccountGroup(RedisConstant.MARKETING_ADVERTISER_ACCOUNT_GROUP + pageViewInfo.getAdvertiserAccountGroupId(), () -> { //
                // 通过 siteId 查询 落地页相关信息;
                return advertiserAccountGroupRemote.fetchById(pageViewInfo.getAdvertiserAccountGroupId());
            }, 1L, TimeUnit.DAYS);

            if (accountGroup.getReplaceOperation().equals(ReplaceOperationType.OPERATION)) {
                pageViewInfo.setOperationType(OperationType.OPERATION);
            }

        } catch (Exception e) {
            log.error("增加operation_type的设置值错误----{}", e.getMessage(), e);
        }
        pageViewInfoService.insertOrUpdate(pageViewInfo);
        //更新clickhouse
//        PageViewInfoLog pageViewInfoLog = pageViewInfo.toPageViewInfoLog();
        //高并发场景 clickhouse 入库效率低，异步更新数据避免影响后面加粉成功 以及上报功能
        //todo 是否需要去除clickhouse page_view_info
//        pageViewinfoSender.sendUpdatePageViewInfoLog(pageViewInfoLog);
    }

    /**
     * 巨量原生页推送UA信息更新pv
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.JU_LIANG_YUAN_SHENG_YE_UPDATE_PV_UA_INFO_QUEUE, value = @Queue(value = Constants.JU_LIANG_YUAN_SHENG_YE_UPDATE_PV_UA_INFO_QUEUE, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.JU_LIANG_YUAN_SHENG_YE_UPDATE_PV_UA_INFO_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void juLiangYuanShengYeUpdatePvUaInfoQueue(TraceDataDto traceDataDto) {
        try {
            TenantContextHolder.set(traceDataDto.getAgentId());
            final String uid = traceDataDto.getReqId();
            final String uaStr = (StringUtils.isNotBlank(StringUtils.trim(traceDataDto.getOs())) ? StringUtils.trim(traceDataDto.getOs()) : "") + "|" + (StringUtils.isNotBlank(StringUtils.trim(traceDataDto.getAppName())) ? StringUtils.trim(traceDataDto.getAppName()) : "") + "|" + (StringUtils.isNotBlank(StringUtils.trim(traceDataDto.getVersion())) ? StringUtils.trim(traceDataDto.getVersion()) : "");
            final FlowSource flowSource = FlowSource.getJuLiangYuanShengYeFlowSourceByUaStr(Platform.OCEAN_ENGINE, uaStr);
            if (StringUtil.isNotBlank(uid)) {
                List<PageViewInfo> pageViewInfoList = pageViewInfoService.list(new LambdaQueryWrapper<PageViewInfo>().ge(PageViewInfo::getCreatedAt, traceDataDto.getBeforeNow()).le(PageViewInfo::getCreatedAt, traceDataDto.getAfterNow()).eq(PageViewInfo::getUid, uid));
                for (PageViewInfo pageViewInfo : pageViewInfoList) {
                    pageViewInfo.setFlowSource(flowSource).setUa(uaStr).setUpdatedAt(Instant.now());
                    try {
                        //增加operation_type的设置值
                        AdvertiserAccountGroup accountGroup = pageViewInfoRedis.getMarketingAdvertiserAccountGroup(RedisConstant.MARKETING_ADVERTISER_ACCOUNT_GROUP + pageViewInfo.getAdvertiserAccountGroupId(), () -> { //
                            // 通过 siteId 查询 落地页相关信息;
                            return advertiserAccountGroupRemote.fetchById(pageViewInfo.getAdvertiserAccountGroupId());
                        }, 1L, TimeUnit.DAYS);

                        if (accountGroup.getReplaceOperation().equals(ReplaceOperationType.OPERATION)) {
                            pageViewInfo.setOperationType(OperationType.OPERATION);
                        }

                    } catch (Exception e) {
                        log.error("增加operation_type的设置值错误----{}", e.getMessage(), e);
                    }
                    pageViewInfoService.insertOrUpdate(pageViewInfo);
                    //更新clickhouse
//                    PageViewInfoLog pageViewInfoLog = pageViewInfo.toPageViewInfoLog();
                    //高并发场景 clickhouse 入库效率低，异步更新数据避免影响后面加粉成功 以及上报功能
                    //todo 是否需要去除clickhouse page_view_info
//                    pageViewinfoSender.sendUpdatePageViewInfoLog(pageViewInfoLog);
                }
            }
        } catch (Exception e) {
            log.error("巨量原生页推送UA信息更新pv异常 traceDataDto={}", JSONObject.toJSONString(traceDataDto), e);
        }
    }

    /**
     * 导出访客细查
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.PAGE_VIEW_SEND_UPLOAD_PAGE_VIEW_INFO_FILE_TO_OSS_QUEUE, value = @Queue(value = Constants.PAGE_VIEW_SEND_UPLOAD_PAGE_VIEW_INFO_FILE_TO_OSS_QUEUE, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.PAGE_VIEW_SEND_UPLOAD_PAGE_VIEW_INFO_FILE_TO_OSS_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void sendUploadPageViewInfoFileToOss(CustomerExportTaskDto taskDto) {
        landingPageService.uploadFileToOss(taskDto);
    }

    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.WECHAT_CUSTOMER_SERVICE_ROBOT_OFFICIAL_ACCOUNT_CLICK_JUMP_QUEUE,
            value = @Queue(value = Constants.WECHAT_CUSTOMER_SERVICE_ROBOT_OFFICIAL_ACCOUNT_CLICK_JUMP_QUEUE, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.WECHAT_CUSTOMER_SERVICE_ROBOT_OFFICIAL_ACCOUNT_CLICK_JUMP_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void wechatCustomerServiceRobotOfficialAccountClickJump(WechatCustomerRobotUpdatePageViewMqDto wcrupvMqDtp) {
        log.info("接收微信客服机器人公众号点击跳转队列 wcrupvMqDtp={}；", JSONObject.toJSONString(wcrupvMqDtp));
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.now(), ZoneId.systemDefault());
        pageViewInfoService.lambdaUpdate()
            .set(PageViewInfo::getClickJumpToWoaStatus, YesOrNoEnum.YES)
            .set(PageViewInfo::getFollowOfficialAccountAppId, wcrupvMqDtp.getFollowOfficialAccountAppId())
            .set(PageViewInfo::getThisPageWechatOfficialAccountAppId, wcrupvMqDtp.getThisPageWechatOfficialAccountAppId())
            .set(PageViewInfo::getThisPageWechatOfficialAccountName, wcrupvMqDtp.getThisPageWechatOfficialAccountName())
            .set(PageViewInfo::getThisPageWechatOfficialOpenWeixinId, wcrupvMqDtp.getThisPageWechatOfficialOpenWeixinId())
            .set(PageViewInfo::getConvertDataTime, LocalDateTime.of(localDateTime.toLocalDate(), LocalTime.of(localDateTime.getHour(), 0)).toInstant(ZoneOffset.of("+8")))
            .set(PageViewInfo::getUpdatedAt, Instant.now())
            .ge(PageViewInfo::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getPageViewInfoUpdateTime()))
            .eq(PageViewInfo::getPid, wcrupvMqDtp.getPid())
            .update();
        PageViewInfo pageViewInfo = pageViewInfoService.findByPid(wcrupvMqDtp.getPid());
        pageViewInfoRedis.saveFollowOfficialAccountStatusByWechatRobotExternalUserid(pageViewInfo, wcrupvMqDtp);
    }

    /**
     * 修改PV广告来源标识
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.UPDATE_PV_ADVERTISE_INFO_QUEUE, value = @Queue(value = Constants.UPDATE_PV_ADVERTISE_INFO_QUEUE, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.UPDATE_PV_ADVERTISE_INFO_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void updatePvAdvertiseInfo(AdvertiseInfoDTO dto) {
        final String pid = dto.getPid();
        log.info("进入修改PV广告来源标识 pid={}，dto={}", pid, JSONObject.toJSONString(dto));
        //更新pgsql
        boolean update = pageViewInfoService.update(new LambdaUpdateWrapper<PageViewInfo>()
            .eq(PageViewInfo::getPid, pid)
            .ge(PageViewInfo::getCreatedAt, Instant.now().minus(7, ChronoUnit.DAYS))
            .set(PageViewInfo::getAdvertiseSource, dto.getAdvertiseSource())
            .set(PageViewInfo::getAdvertiseSourceReason, dto.getAdvertiseSourceReason())
            .set(PageViewInfo::getUpdatedAt, Instant.now()));
        log.info("进入修改PV广告来源标识 更新pg数据库 pid={}，dto={}，update={}", pid, JSONObject.toJSONString(dto), update);
    }

    /**
     * 更新PV问答模板选项
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.PAGEVIEW_QA_TEMPLATE_OPTION_QUEUE, value = @Queue(value = Constants.PAGEVIEW_QA_TEMPLATE_OPTION_QUEUE, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.PAGEVIEW_QA_TEMPLATE_OPTION_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void qaTemplateOption(QaTemplateOptionDTO dto) {
        String pid = dto.getPid();
        String option = dto.getOption();
        log.info("更新PV问答模板选项 pid={}，option={}", pid, option);
        if (StringUtils.isBlank(pid) || StringUtils.isBlank(option)) {
            return;
        }
        //更新pgsql
        boolean update = pageViewInfoService.update(new LambdaUpdateWrapper<PageViewInfo>()
            .eq(PageViewInfo::getPid, pid)
            .ge(PageViewInfo::getCreatedAt, Instant.now().minus(7, ChronoUnit.DAYS))
            .set(PageViewInfo::getQaTemplateOption, StringUtils.left(dto.getOption(), 150))
            .set(PageViewInfo::getUpdatedAt, Instant.now()));
        log.info("更新PV问答模板选项 更新pg数据库 pid={}，dto={}，update={}", pid, JSONObject.toJSONString(dto), update);
    }

    /**
     * 更新PV表单内容
     */
    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.PAGEVIEW_FORM_DESCRIPTION_QUEUE, value = @Queue(value = Constants.PAGEVIEW_FORM_DESCRIPTION_QUEUE, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.PAGEVIEW_FORM_DESCRIPTION_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void formDescription(FormDescriptionDTO dto) {
        String pid = dto.getPid();
        String formDescription = dto.getFormDescription();
        log.info("更新PV表单内容 pid={}，formDescription={}", pid, formDescription);
        if (StringUtils.isBlank(pid) || StringUtils.isBlank(formDescription)) {
            return;
        }
        //更新pgsql
        boolean update = pageViewInfoService.update(new LambdaUpdateWrapper<PageViewInfo>()
            .eq(PageViewInfo::getPid, pid)
            .ge(PageViewInfo::getCreatedAt, Instant.now().minus(7, ChronoUnit.DAYS))
            .set(PageViewInfo::getFormDescription, StringUtils.left(formDescription, 150))
            .set(PageViewInfo::getUpdatedAt, Instant.now()));
        log.info("更新PV表单内容 更新pg数据库 pid={}，dto={}，update={}", pid, JSONObject.toJSONString(dto), update);
    }

    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.UPDATE_SUCCESS_SEND_WELCOME_MSG_STATUS_KEY, value = @Queue(value = Constants.UPDATE_SUCCESS_SEND_WELCOME_MSG_STATUS_KEY, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.UPDATE_SUCCESS_SEND_WELCOME_MSG_STATUS_EXCHANGE, type = ExchangeTypes.TOPIC)
        )
    })
    @RabbitHandler
    public void updatePvSuccessSendWelcomeMsgStatus(AdvertiseInfoDTO dto) {
        log.info("监听到消息，进入修改PV成功发送欢迎语的状态，dto={}",JSONObject.toJSONString(dto));
        pageViewInfoService.updatePvSuccessSendWelcomeMsgStatus(dto);

    }


    @RabbitListener(bindings = {
        @QueueBinding(
            key = Constants.TAOBAO_DSP_EVENT_PAGE_VIEW_KEY, value = @Queue(value = Constants.TAOBAO_DSP_EVENT_PAGE_VIEW_EXCHANGE, durable = "true", autoDelete = "false", exclusive = "false"),
            exchange = @Exchange(name = Constants.TAOBAO_DSP_EVENT_PAGE_VIEW_EXCHANGE)
        )
    })
    @RabbitHandler
    public void updatePvTaobaoDspEvent(PageViewTaobaoDspDto dto) {
        final String pid = dto.getPid();
        log.info("更新 taobao dsp pv 状态 pid={}，dto={}", pid, JSONObject.toJSONString(dto));
        PageViewInfo pageViewInfo = pageViewInfoService.getByPid(pid);
        if(Objects.isNull(pageViewInfo)){
            return;
        }
        //1.0统计
        boolean update = pageViewInfoService.update(new LambdaUpdateWrapper<PageViewInfo>()
            .eq(PageViewInfo::getPid, pid)
            .ge(PageViewInfo::getCreatedAt, Instant.now().minus(7, ChronoUnit.DAYS))
            .set(PageViewInfo::getDeviceMd5, dto.getDeviceMd5())
            .set(IndicatorStatisticEventEnum.TAOBAO_PAGE_VIEW.equals(dto.getEventType()), PageViewInfo::getTaobaoPageViewStatus, YesOrNoEnum.YES)
            .set(IndicatorStatisticEventEnum.TAOBAO_ORDER_PAYMENT.equals(dto.getEventType()), PageViewInfo::getTaobaoOrderPaymentStatus, YesOrNoEnum.YES)
            .set(IndicatorStatisticEventEnum.TAOBAO_FIRST_VISIT_VENUE.equals(dto.getEventType()),PageViewInfo::getTaobaoFirstVisitVenueStatus, YesOrNoEnum.YES)
            .set(IndicatorStatisticEventEnum.TAOBAO_CANCEL_ORDER_PAYMENT.equals(dto.getEventType()),PageViewInfo::getTaobaoCancelOrderPaymentStatus,YesOrNoEnum.YES)
            .set(IndicatorStatisticEventEnum.TAOBAO_PRODUCT_CLICK.equals(dto.getEventType()),PageViewInfo::getTaobaoProductClickStatus,YesOrNoEnum.YES)
            .set(IndicatorStatisticEventEnum.TAOBAO_HIGH_COMMISSION_ORDER_PAYMENT.equals(dto.getEventType()),PageViewInfo::getTaobaoHighCommissionOrderPaymentStatus,YesOrNoEnum.YES)
            .set(IndicatorStatisticEventEnum.TAOBAO_RED_ENVELOPE_RECEIVE.equals(dto.getEventType()),PageViewInfo::getTaobaoRedEnvelopeReceiveStatus,YesOrNoEnum.YES)
            );
        log.info("更新 taobao dsp pv 更新pg数据库 pid={}，dto={}，update={}", pid, JSONObject.toJSONString(dto), update);

        //2.0统计
        LandingPageIndicatorStatisticsDTO landingPageIndicatorStatisticsDTO = new LandingPageIndicatorStatisticsDTO();
        landingPageIndicatorStatisticsDTO.setIndicatorStatisticEventEnum(dto.getEventType())
            .setLandingPageId(pageViewInfo.getLandingPageId())
            .setChannelId(pageViewInfo.getChannelId())
            .setAdvertiserAccountGroupId(pageViewInfo.getAdvertiserAccountGroupId());
        if (Objects.nonNull(pageViewInfo.getCreatedAt())){
            landingPageIndicatorStatisticsDTO.setPageViewTime(LocalDateTime.ofInstant(pageViewInfo.getCreatedAt(), ZoneId.systemDefault())).setConvertTime(LocalDateTime.now());
        }else {
            landingPageIndicatorStatisticsDTO.setPageViewTime(LocalDateTime.now()).setConvertTime(LocalDateTime.now());
        }
        landingPageIndicatorStatisticsDTO
            .setPid(pageViewInfo.getPid())
            .setSid(pageViewInfo.getSid())
            .setDouyinCustomerSource(pageViewInfo.getDouyinCustomerSource())
            .setUid(pageViewInfo.getUid())
            .setIp(pageViewInfo.getIp())
            .setUa(pageViewInfo.getUa())
            .setParentPid(pageViewInfo.getParentPid())
            .setFirstPagePid(pageViewInfo.getFirstPagePid())
            .setPlatformId(Platform.getEnumById(pageViewInfo.getPlatformId()))
            .setProvince(pageViewInfo.getProvince())
            .setCity(pageViewInfo.getCity())
            .setDevice(pageViewInfo.getDevice())
            .setOs(pageViewInfo.getOs())
            .setOsType(pageViewInfo.getOsType())
            .setBrowser(pageViewInfo.getBrowser())
            .setBrowserType(pageViewInfo.getBrowserType())
            .setNetworkType(pageViewInfo.getNetworkType())
            .setUrl(pageViewInfo.getUrl())
            .setClickId(pageViewInfo.getClickId())
            .setLengthOfStay(pageViewInfo.getLengthOfStay())
            .setCreativeId(pageViewInfo.getCreativeId())
            .setAdAccountId(pageViewInfo.getAccountId())
            .setAdgroupId(pageViewInfo.getAdgroupId())
            .setCampaignId(pageViewInfo.getCampaignId())
            .setWechatOfficialHistoryArticleStatus(pageViewInfo.getWechatOfficialHistoryArticleStatus())
            .setLandingPageType(pageViewInfo.getLandingPageType())
            .setLinkType(pageViewInfo.getLinkType())
            .setAdvertiseSource(pageViewInfo.getAdvertiseSource())
            .setJumpToTaobaoAppStatus(pageViewInfo.getJumpToTaobaoAppStatus())
            .setOnlineShopBuyGoodsStatus(pageViewInfo.getOnlineShopBuyGoodsStatus())
            .setWechatUserIdMatchingStatus(pageViewInfo.getWechatUserIdMatchingStatus())
            .setPaymentType(pageViewInfo.getPaymentType())
            .setFlowSource(pageViewInfo.getFlowSource());
        //新版统计
        landingPageSender.sendLandingPageIndicatorStatistics(landingPageIndicatorStatisticsDTO);

        //更新客资上的taobao dsp 字段
        if(StringUtils.isNotBlank(pageViewInfo.getPid())){
            Customer customer = customerService.queryByPid(pageViewInfo.getPid());
            if(Objects.nonNull(customer)){
                Long landingPageId = pageViewInfo.getLandingPageId();
                Long channelId = pageViewInfo.getChannelId();
                String url = pageViewInfo.getUrl();
                customerService.update(new LambdaUpdateWrapper<Customer>()
                    .ge(Customer::getCreatedAt, DateTimeUtil.getBeforeDayInstantByDays(agentConf.getCustomerUpdateTime()))
                    .eq(Customer::getPid, pageViewInfo.getPid())
                    .set(Customer::getTaobaoDspLandingPageId,landingPageId)
                    .set(Customer::getTaobaoDspLandingPageChannelId,channelId)
                    .set(Customer::getTaobaoDspUrl,url));
            }
        }
    }
}
