package ai.yiye.agent.collect.pageview.redis;

import ai.yiye.agent.autoconfigure.redis.RedisConstant;
import ai.yiye.agent.collect.pageview.config.PageViewConf;
import ai.yiye.agent.collect.pageview.dto.LandingPageDspCacheDTO;
import ai.yiye.agent.collect.pageview.remote.LandingPageRemote;
import ai.yiye.agent.collect.pageview.sevice.LandingPageService;
import ai.yiye.agent.domain.LandingPage;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 落地页缓存
 */
@Slf4j
@Component
public class LandingPageRedis extends RedisConstant {

    @Resource
    private PageViewConf pageViewConf;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private LandingPageService landingPageService;

    @Resource
    private LandingPageRemote landingPageRemote;

    @Resource
    private RedisTemplate<String, Object> defaultObjectRedisTemplate;

    /**
     * 获取落地页token
     */
    public String getToken(Long landingPageId) {
        if (landingPageId == null) {
            return null;
        }
        String key = LANDING_PAGE_TOKEN + landingPageId;
        //从redis获取
        String redisValue = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(redisValue)) {
            return redisValue;
        }
        //从数据库获取
        LandingPage one = landingPageService.getOne(new LambdaQueryWrapper<LandingPage>().eq(LandingPage::getId, landingPageId).last(" limit 1"));
        if (one == null) {
            return null;
        }
        String token = one.getToken();
        stringRedisTemplate.opsForValue().set(key, token, 2, TimeUnit.HOURS);
        return token;
    }

    /**
     * 获取落地页是否开启DSP上报
     */
    public LandingPageDspCacheDTO getTaobaoDspCache(Long landingPageId) {
        if (landingPageId == null) {
            return null;
        }
        String key = LANDING_PAGE_TAOBAO_DSP + landingPageId;
        //从redis获取
        String redisValue = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(redisValue)) {
            return JSONObject.parseObject(redisValue, LandingPageDspCacheDTO.class);
        }
        //从数据库获取
        LandingPage one = landingPageService.lambdaQuery()
            .select(LandingPage::getEnableReportDsp, LandingPage::getTaobaoDspType, LandingPage::getTaobaoDspConfigIds, LandingPage::getAdvertiserAccountGroupId)
            .eq(LandingPage::getId, landingPageId)
            .last(" limit 1")
            .one();
        if (one == null) {
            return null;
        }
        LandingPageDspCacheDTO cache = new LandingPageDspCacheDTO();
        cache.setTaobaoDspType(one.getTaobaoDspType());
        cache.setEnableReportDsp(one.getEnableReportDsp());
        cache.setTaobaoDspConfigIds(one.getTaobaoDspConfigIds());
        cache.setAdvertiserAccountGroupId(one.getAdvertiserAccountGroupId());
        stringRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(cache), 1, TimeUnit.HOURS);
        return cache;
    }

    public boolean channelShow(String agentId, String token, String channelParam) {
        if (StringUtils.isEmpty(agentId) || StringUtils.isEmpty(token) || StringUtils.isEmpty(channelParam)) {
            return false;
        }
        //渠道是否可以访问
        return this.channelShow(pageViewConf.getCacheKey() + RedisConstant.getLpCacheKey() + pageViewConf.getCommonCacheKey() + token + ":" + channelParam, () -> {
            return landingPageRemote.getCanShowChannel(agentId, token, channelParam);
        }, 1L, TimeUnit.DAYS);
    }

    public boolean channelShow(String k, Supplier<Boolean> value, Long time, TimeUnit timeUnit) {
        Object object = Optional.ofNullable(
            defaultObjectRedisTemplate.opsForValue().get(k)
        ).orElseGet(() -> {
            Boolean aBoolean = value.get();
            defaultObjectRedisTemplate.opsForValue().set(k, aBoolean, time, timeUnit);
            return aBoolean;
        });
        if (!Objects.isNull(object) && (object instanceof Boolean)) {
            return (Boolean) object;
        }
        return false;
    }

    /**
     * 缓存企微加粉记录
     */
//    public void cacheEnterpriseWechatAddFriendRecord(String externalUserId) {
//        String key = RedisConstant.LANDING_PAGE_ENTERPRISE_WECHAT_ADD_FRIEND_RECORD + externalUserId;
//        stringRedisTemplate.opsForValue().set(key, "", 7L, TimeUnit.DAYS);
//    }

    /**
     * 缓存公众号关注记录
     */
    public void cacheOfficialAccountFollowRecord( String wechatRobotExternalUserid) {
        String key = RedisConstant.LANDING_PAGE_OFFICIAL_ACCOUNT_FOLLOW_RECORD + wechatRobotExternalUserid;
        stringRedisTemplate.opsForValue().set(key, "", 7L, TimeUnit.DAYS);
    }

}
