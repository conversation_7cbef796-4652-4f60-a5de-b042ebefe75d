package ai.yiye.agent.autoconfigure.rabbitmq.config;

public class Constants {

    public static final String SUBMIT_FINISH_QUEUE = "yiye-agent-public.collect-submit.finish.queue";
    public static final String SUBMIT_FINISH_EXCHANGE = "yiye-agent-public.submit-datum-topic.exchange";
    public static final String SUBMIT_FINISH_PAGEVIEW_QUEUE = "yiye-agent-public.collect-submit.pageview.queue";
    public static final String SUBMIT_FINISH_PAGEVIEW_KEY = "yiye-agent-public.collect-submit.*.queue";


    public static final String PAGEVIEW_EXCHANGE = "yiye-agent-public.collect-pageview-topic.exchange";
    public static final String PAGEVIEW_QUEUE = "yiye-agent-public.collect-pageview.queue";
    public static final String PAGEVIEW_KEY = "yiye-agent-public.collect-pageview.queue";

    //私域pv收集队列
    public static final String PRIVATE_PAGEVIEW_EXCHANGE = "yiye-agent-private.collect-pageview-topic.exchange";
    public static final String PRIVATE_PAGEVIEW_QUEUE = "yiye-agent-private.collect-pageview.queue";

    //巨量api上报回传receive_url回调后续解析队列
    public static final String SEND_OCEAN_ENGINE_RECEIVE_URL_CALLBACK_EXCHANGE = "yiye-agent-private.collect-pageview.send-ocean-engine-receive-url-callback.exchange";
    public static final String SEND_OCEAN_ENGINE_RECEIVE_URL_CALLBACK_QUEUE = "yiye-agent-private.collect-pageview.send-ocean-engine-receive-url-callback.queue";

    //私域pv公众号二维码长按识别收集队列
    public static final String PRIVATE_PAGEVIEW_OFFICIAL_EXCHANGE = "yiye-agent-private.collect-pageview-official-topic.exchange";
    public static final String PRIVATE_PAGEVIEW_OFFICIAL_QUEUE = "yiye-agent-private.collect-pageview-official.queue";


    // 停留时长和访问深度收集队列
    public static final String LENGTH_DEPTH_QUEUE = "yiye-agent-private.length-depth.queue";
    public static final String LENGTH_DEPTH_EXCHANGE = "yiye-agent-private.length-depth-topic.exchange";

    //跳转关注公众号首页，pv收集
    public static final String PRIVATE_PAGEVIEW_FOLLOW_OFFICIAL_ACCOUNT_EXCHANGE = "yiye-agent-private.collect-pageview-follow-official-account-topic.exchange";
    public static final String PRIVATE_PAGEVIEW_FOLLOW_OFFICIAL_ACCOUNT_QUEUE = "yiye-agent-private.collect-pageview.follow-official-account.queue";

    //长按识别公众号二维码，pv收集
    public static final String PRIVATE_PAGEVIEW_FOLLOW_OFFICIAL_ACCOUNT_IDENTIFY_EXCHANGE = "yiye-agent-private.collect-pageview-follow-official-account-identify-topic.exchange";
    public static final String PRIVATE_PAGEVIEW_FOLLOW_OFFICIAL_ACCOUNT_IDENTIFY_QUEUE = "yiye-agent-private.collect-pageview.follow-official-account-identify.queue";


    //支付
    public static final String SUBMIT_PAYMENT_EXCHANGE = "yiye-agent-private.submit-datum-payment-topic.exchange";
    public static final String BINDING_SUBMIT_CUSTOMER_PAYMENT_KEY = "yiye-agent-private.collect.submit.payment.queue";
    public static final String BINDING_QUEUE = "yiye-agent-private.collect.submit.payment-pageview.queue";

    public static final String GENERATE_TRACE_DATA_QUEUE = "yiye-agent-private.trace-data.sync.queue";
    public static final String UPLOAD_SUBMIT_DATA_QUEUE = "yiye-agent-private.submit-data.upload.queue";
    public static final String SUBMIT_BINDING_QUEUE = "yiye-agent-private.collect.submit.finish.queue";
    public static final String PAY_CUSTOMER_UPLOAD_QUEUE = "yiye-agent-private.pay.customer.upload.queue";

    public static final String AD_TRACK_URL_AUTOMATIC_CONSTRUCT_EXCHANGE = "yiye-agent-private.marketing.ad-track-url-automatic-construct.exchange";
    public static final String AD_TRACK_URL_AUTOMATIC_CONSTRUCT_QUEUE = "yiye-agent-private.marketing.ad-track-url-automatic-construct.queue";

    //账户授权mq类
    public static final String AD_ACCOUNT_OAUTH_EXCHANGE = "yiye-agent-private.marketing.ad-account-oauth.exchange";
    public static final String AD_ACCOUNT_OAUTH_QUEUE = "yiye-agent-private.marketing.ad-account-oauth.queue";


    /**
     * 后链路 MAPI 上报 队列
     */
    public static final String BACKEND_CUSTOMER_UPLOAD_MAPI_QUEUE = "yiye-agent-private.backend.customer.upload.mapi.queue";

    /**
     * 后链路 CRM 上报 客资上报key
     */
    public static final String BACKEND_CUSTOMER_UPLOAD_KEY = "yiye-agent-private.backend.customer.upload.queue";

    public static final String SUBMIT_FINISH_PAGEVIEW_QUEUE_PRIVATE = "yiye-agent-private.collect-submit.pageview.queue";
    /**
     * 后链路 填单上报
     */
    public static final String BACKEND_SUBMIT_EXCHANGE = "yiye-agent-private.backend.submit-datum-topic.exchange";


    /**
     * 后链路 星链上报 队列
     */
    public static final String BACKEND_SUBMIT_FINISH_UPLOAD_QUEUE = "yiye-agent-private.backend.collect-submit.upload.queue";
    /**
     * 后链路 填单上报 exchange
     */
    public static final String TOPIC_EXCHANGE_PRIVATE = "yiye-agent-private.submit-datum-topic.exchange";

    public static final String GENERATE_CUSTOMER_QUEUE = "yiye-agent-private.customer.generate.queue";
    //客户人群
    public static final String AUDIENCES_CUSTOM_PRIVATE_EXCHANGE = "yiye-agent-private.custom-audience-exchange";
    public static final String AUDIENCES_CUSTOM_PUBLIC_EXCHANGE = "yiye-agent-public.custom-audience-exchange";
    public static final String AUDIENCES_CUSTOM_PRIVATE_TOPIC = "yiye-agent-private.custom-audience-topic";
    public static final String AUDIENCES_CUSTOM_PRIVATE_QUEUE = "yiye-agent-private.custom-audience-queue";
    //穿山甲流量包（每个用户都不一样）
    public static final String FLOW_PACKAGE_PRIVATE_EXCHANGE = "yiye-agent-private.flow-package-exchange";
    public static final String FLOW_PACKAGE_PUBLIC_EXCHANGE = "yiye-agent-public.flow-package-exchange";
    //获取类目
    public static final String TARGETING_CATEGORY_PRIVATE_EXCHANGE = "yiye-agent-private.targeting-category-exchange";
    public static final String TARGETING_CATEGORY_PUBLIC_EXCHANGE = "yiye-agent-public.targeting-category-exchange";

    //识别二维码
    public static final String IDENTIFY_QR_CODE_STATUS_EXCHANGE = "yiye-agent-private.identify-qr-code-status-topic.exchange";
    //识别二维码上报
    public static final String IDENTIFY_QR_CODE_STATUS_KEY = "yiye-agent-private.identify-qr-code-status.upload.queue";

    //链接跳转
    public static final String LINK_JUMP_STATUS_EXCHANGE = "yiye-agent-private.link-jump-status-topic.exchange";
    //链接跳转上报
    public static final String LINK_JUMP_STATUS_KEY = "yiye-agent-private.link-jump-status.upload.queue";

    //识别二维码添加状态
    public static final String ADD_IDENTIFY_QR_CODE_STATUS_KEY = "yiye-agent-private.identify-qr-code-status.add.queue";
    //识别二维码记录
    public static final String IDENTIFY_QR_CODE_STATUS_RECORD_KEY = "yiye-agent-private.identify-qr-code-status.record.queue";

    //识别公众号二维码
    public static final String IDENTIFY_OFFICIAL_QR_CODE_STATUS_EXCHANGE = "yiye-agent-private.identify-official-qr-code-status-topic.exchange";
    //识别公众号二维码上报
    public static final String IDENTIFY_OFFICIAL_QR_CODE_STATUS_KEY = "yiye-agent-private.identify-official-qr-code-status.upload.queue";

    /**
     * 企业微信客户开口次数上报
     */
    public static final String WORK_WEIXIN_OPEN_NUM_UPLOAD_EXCHANGE = "yiye-agent-private.enterprise-wechat-open-num.upload.exchange";
    public static final String WORK_WEIXIN_OPEN_NUM_UPLOAD_KEY = "yiye-agent-private.enterprise-wechat-open-num.upload.queue";

    /**
     * 企业微信客户回复关键词上报
     */
    public static final String WORK_WEIXIN_WECHAT_USER_RECOVER_KEYWORD_UPLOAD_EXCHANGE = "yiye-agent-private.enterprise-wechat-wechat-user-recover-keyword.upload.exchange";
    public static final String WORK_WEIXIN_WECHAT_USER_RECOVER_KEYWORD_UPLOAD_KEY = "yiye-agent-private.enterprise-wechat-wechat-user-recover-keyword.upload.queue";

    /**
     * 企业微信销售回复关键词上报
     */
    public static final String WORK_WEIXIN_CUSTOMER_SERVICE_RECOVER_KEYWORD_UPLOAD_EXCHANGE = "yiye-agent-private.enterprise-wechat-customer-service-recover-keyword.upload.exchange";
    public static final String WORK_WEIXIN_CUSTOMER_SERVICE_RECOVER_KEYWORD_UPLOAD_KEY = "yiye-agent-private.enterprise-wechat-customer-service-recover-keyword.upload.queue";

    /**
     * 长按识别二维码（企业微信群）
     */
    public static final String WORK_WEIXIN_IDENTIFY_ENTERPRISE_WECHAT_GROUP_CHAT_QR_CODE_UPLOAD_EXCHANGE = "yiye-agent-private.identify-enterprise-wechat-group-chat-qr-code-keyword.upload.exchange";
    public static final String WORK_WEIXIN_IDENTIFY_ENTERPRISE_WECHAT_GROUP_CHAT_QR_CODE_UPLOAD_KEY = "yiye-agent-private.identify-enterprise-wechat-group-chat-qr-code.upload.queue";

    /**
     * 成功添加企业微信群
     */
    public static final String WORK_WEIXIN_ADD_ENTERPRISE_WECHAT_GROUP_CHAT_SUCCESS_UPLOAD_EXCHANGE = "yiye-agent-private.add-enterprise-wechat-group-chat-success.upload.exchange";
    public static final String WORK_WEIXIN_ADD_ENTERPRISE_WECHAT_GROUP_CHAT_SUCCESS_UPLOAD_KEY = "yiye-agent-private.add-enterprise-wechat-group-chat-success.upload.queue";

    /**
     * 主动私信授权成功 上报
     */
    public static final String MESSAGE_AUTHORIZATION_SUCCESS_UPLOAD_EXCHANGE = "yiye-agent-private.message-authorization-success.upload.exchange";
    public static final String MESSAGE_AUTHORIZATION_SUCCESS_UPLOAD_KEY = "yiye-agent-private.message-authorization-success.upload.queue";

    /**
     * 表单提交完成 上报
     */
    public static final String FORM_SUBMIT_SUCCESS_UPLOAD_EXCHANGE = "yiye-agent-private.form-submit-success.upload.exchange";
    public static final String FORM_SUBMIT_SUCCESS_UPLOAD_KEY = "yiye-agent-private.form-submit-success.upload.queue";

    /**
     * 授权手机号 上报
     */
    public static final String AUTH_PHONE_SUCCESS_UPLOAD_EXCHANGE = "yiye-agent-private.auth-phone-success.upload.exchange";
    public static final String AUTH_PHONE_SUCCESS_UPLOAD_KEY = "yiye-agent-private.auth-phone-success.upload.queue";

    /**
     * whatsapp跳转 上报队列
     */
    public static final String SEND_JUMP_WHATSAPP_SUCCESS_UPLOAD_EXCHANGE = "yiye-agent-private.send-jump-whatsapp-success-upload.exchange";
    public static final String SEND_JUMP_WHATSAPP_SUCCESS_UPLOAD_KEY = "yiye-agent-private.send-jump-whatsapp-success-upload.queue";

    /**
     * whatsapp建联 上报队列
     */
    public static final String SEND_WHATSAPP_USER_ADD_BUSINESS_FRIEND_SUCCESS_UPLOAD_EXCHANGE = "yiye-agent-private.send-whatsapp-user-add-business-friend-success.upload.exchange";
    public static final String SEND_WHATSAPP_USER_ADD_BUSINESS_FRIEND_SUCCESS_UPLOAD_KEY = "yiye-agent-private.send-whatsapp-user-add-business-friend-success.upload.queue";

    /**
     * whatsapp用户开口 上报队列
     */
    public static final String SEND_WHATSAPP_USER_OPEN_MOUTH_SUCCESS_UPLOAD_EXCHANGE = "yiye-agent-private.send-whatsapp-user-open-mouth-success.upload.exchange";
    public static final String SEND_WHATSAPP_USER_OPEN_MOUTH_SUCCESS_UPLOAD_KEY = "yiye-agent-private.send-whatsapp-user-open-mouth-success.upload.queue";

    /**
     * 复制成功（使用淘客组件，成功复制内容）上报
     */
    public static final String COPY_CONTENT_SUCCESS_UPLOAD_EXCHANGE = "yiye-agent-private.copy.content.success.upload.exchange";
    public static final String COPY_CONTENT_SUCCESS_UPLOAD_KEY = "yiye-agent-private.copy.content.success.upload.queue";


    /**
     * 成功淘客组件并上报
     */
    public static final String COPY_CONTENT_SUCCESS_UPLOAD_NEW_EXCHANGE = "yiye-agent-private.copy-content-success-new.upload.exchange";

    public static final String COPY_CONTENT_SUCCESS_UPLOAD_NEW_KEY = "yiye-agent-private.copy-content-success-new.upload.queue";


    /**
     * 点击跳转领取超级618红包
     */
    public static final String JUMP_TO_SUPER_RED_ENVELOPE_EXCHANGE = "yiye-agent-private.jump_to_super_red_envelope.exchange";

    public static final String JUMP_TO_SUPER_RED_ENVELOPE_KEY = "yiye-agent-private.jump_to_super_red_envelope.queue";



    /**
     * 淘宝电影小程序内下单成功回调后进行上报
     */
    public static final String TAO_BAO_MOVIE_APPLET_ORDER_SUCCESS_UPLOAD_EXCHANGE = "yiye-agent-private.tao-bao-movie-applet-order-success.exchange";

    public static final String TAO_BAO_MOVIE_APPLET_ORDER_SUCCESS_UPLOAD_KEY = "yiye-agent-private.tao-bao-movie-applet-order-success.queue";



    //添加粉丝
    public static final String ADD_ENTERPRISE_WECHAT_STATUS_EXCHANGE = "yiye-agent-private.add-enterprise-wechat-status-topic.exchange";
    public static final String ADD_ENTERPRISE_WECHAT_STATUS_KEY = "yiye-agent-private.add-enterprise-wechat-status.upload.queue";
    //成功添加企业微信数自动下线策略
    public static final String ADD_ENTERPRISE_WECHAT_STATUS_AUTO_RULE_KEY = "yiye-agent-private.add-enterprise-wechat.auto-rule.queue";
    //加粉成功（修改状态）
    public static final String UPDATE_ADD_ENTERPRISE_WECHAT_STATUS_SUCCESS_EXCHANGE = "yiye-agent-private.update-add-enterprise-wechat-status-success-topic.exchange";
    public static final String UPDATE_ADD_ENTERPRISE_WECHAT_STATUS_SUCCESS_KEY = "yiye-agent-private.update-add-enterprise-wechat-status-success.queue";

    //企业推加粉成功
    public static final String QIYETUI_UPDATE_ADD_ENTERPRISE_WECHAT_STATUS_SUCCESS_EXCHANGE = "yiye-agent-private.qiyetui-update-add-enterprise-wechat-status-success-topic.exchange";
    public static final String QIYETUI_UPDATE_ADD_ENTERPRISE_WECHAT_STATUS_SUCCESS_KEY = "yiye-agent-private.qiyetui-update-add-enterprise-wechat-status-success.queue";

    //添加【企业微信客户群】成功
    public static final String UPDATE_PV_BY_ADD_WORK_WECHAT_GROUP_CHAT_SUCCESS_EXCHANGE = "yiye-agent-private.update-pv-by-add-work-wechat-group-chat-success.exchange";
    public static final String UPDATE_PV_BY_ADD_WORK_WECHAT_GROUP_CHAT_SUCCESS_KEY = "yiye-agent-private.update-pv-by-add-work-wechat-group-chat-success.queue";

    //关注微信公众号
    public static final String FOLLOW_WECHAT_OFFICIAL_ACCOUNT_STATUS_EXCHANGE = "yiye-agent-private.follow-wechat-official-account-status-topic.exchange";
    public static final String FOLLOW_WECHAT_OFFICIAL_ACCOUNT_STATUS_KEY = "yiye-agent-private.follow-wechat-official-account-status.upload.queue";
    //关注微信公众号（mq更新状态：关注【微信公众号】成功）
    public static final String UPDATE_FOLLOW_WECHAT_OFFICIAL_ACCOUNT_STATUS_EXCHANGE = "yiye-agent-private.update-follow-wechat-official-account-status-topic.exchange";
    public static final String UPDATE_FOLLOW_WECHAT_OFFICIAL_ACCOUNT_STATUS_KEY = "yiye-agent-private.update-follow-wechat-official-account-status.upload.queue";

    //关注微信公众号：发送公众号消息
    public static final String SEND_FOLLOW_WECHAT_OFFICIAL_ACCOUNT_MESSAGE_EXCHANGE = "yiye-agent-private.send-follow-wechat-official-account-message-topic.exchange";
    public static final String SEND_FOLLOW_WECHAT_OFFICIAL_ACCOUNT_MESSAGE_KEY = "yiye-agent-private.send-follow-wechat-official-account-message.queue";

    //微信公众号事件回调处理
    public static final String WECHAT_OFFICIAL_ACCOUNT_PROCESS_MESSAGE_EXCHANGE = "yiye-agent-private.wechat-official-account-process-message.exchange";
    public static final String WECHAT_OFFICIAL_ACCOUNT_PROCESS_MESSAGE_QUEUE = "yiye-agent-private.wechat-official-account-process-message.queue";

    //小程序事件回调处理
    public static final String WECHAT_APPLET_PROCESS_MESSAGE_EXCHANGE = "yiye-agent-private.wechat-applet-process-message.exchange";
    public static final String WECHAT_APPLET_PROCESS_MESSAGE_QUEUE = "yiye-agent-private.wechat-applet-process-message.queue";

    //修改PV广告来源标识
    public static final String UPDATE_PV_ADVERTISE_INFO_EXCHANGE = "yiye-agent-private.update-pv-advertise-info.exchange";
    public static final String UPDATE_PV_ADVERTISE_INFO_QUEUE = "yiye-agent-private.update-pv-advertise-info.queue";


    //进入机器人成功发送欢迎语
    public static final String UPDATE_SUCCESS_SEND_WELCOME_MSG_STATUS_EXCHANGE = "yiye-agent-private.update_success_send_welcome_msg_status_exchange.exchange";
    public static final String UPDATE_SUCCESS_SEND_WELCOME_MSG_STATUS_KEY = "yiye-agent-private.update_success_send_welcome_msg_status_key.queue";

    //进入【微信客服机器人会话】，更新进入会话状态
    public static final String UPDATE_INTO_WECHAT_CUSTOMER_SERVICE_SESSION_STATUS_EXCHANGE = "yiye-agent-private.update-into-wechat-customer-service-session-status-topic.exchange";
    public static final String UPDATE_INTO_WECHAT_CUSTOMER_SERVICE_SESSION_STATUS_KEY = "yiye-agent-private.update-into-wechat-customer-service-session-status.queue";

    //微信机器人发送消息，异步进行消息记录保存
    public static final String RECORD_ROBOT_CUSTOMER_ACQUISITION_LINK_EXCHANGE = "yiye-agent-private.record-robot-customer-acquisition-link.exchange";

    public static final String RECORD_ROBOT_CUSTOMER_ACQUISITION_LINK_EXCHANGE_KEY = "yiye-agent-private.record-robot-customer-acquisition-link.queue";

    //新增公共参数
    public static final String INSERT_LANDING_PAGE_COMMON_PARAMS_EXCHANGE = "yiye-agent-private.landing-page-common-params-insert-topic.exchange";
    public static final String INSERT_LANDING_PAGE_COMMON_PARAMS_KEY = "yiye-agent-private.landing-page-common-params-insert.queue";

    //发送群聊机器人消息
    public static final String SEND_GROUP_CHAT_ROBOT_MESSAGE_EXCHANGE = "yiye-agent-private.send-group-chat-robot-message-topic.exchange";
    public static final String SEND_GROUP_CHAT_ROBOT_MESSAGE_KEY = "yiye-agent-private.send-group-chat-robot-message.queue";

    //电商商品浏览（点击商品链接跳转电商APP）-上报
    public static final String CLICK_URL_JUMP_GO_TO_ON_LINE_SHOP_UPLOAD_EXCHANGE = "yiye-agent-private.click-url-jump-go-to-on-line-shop-upload-topic.exchange";
    public static final String CLICK_URL_JUMP_GO_TO_ON_LINE_SHOP_UPLOAD_KEY = "yiye-agent-private.click-url-jump-go-to-on-line-shop-upload.queue";

    //电商商品购买成功-上报
    public static final String SUCCESSFUL_PURCHASE_OF_ONLINE_SHOP_GOODS_UPLOAD_EXCHANGE = "yiye-agent-private.successful-purchase-of-online-shop-goods-upload-topic.exchange";
    public static final String SUCCESSFUL_PURCHASE_OF_ONLINE_SHOP_GOODS_UPLOAD_KEY = "yiye-agent-private.successful-purchase-of-online-shop-goods-upload.queue";
    //电商商品购买成功-更新点击跳转淘宝app下单状态
    public static final String UPDATE_JUMP_TO_TAOBAO_APP_STATUS_EXCHANGE = "yiye-agent-private.update-jump-to-taobao-app-status-topic.exchange";
    public static final String UPDATE_JUMP_TO_TAOBAO_APP_STATUS_KEY = "yiye-agent-private.update-jump-to-taobao-app-status.queue";
    //电商商品购买成功-匹配曝光成功-生成填单信息、客资信息
    public static final String SUCCESSFUL_PURCHASE_OF_ONLINE_SHOP_GOODS_MATCHING_PV_INFO_CREATE_CUSTOMER_EXCHANGE = "yiye-agent-private.successful-purchase-of-online-shop-goods-matching-pv-info-create-customer-topic.exchange";
    public static final String SUCCESSFUL_PURCHASE_OF_ONLINE_SHOP_GOODS_MATCHING_PV_INFO_CREATE_CUSTOMER_KEY = "yiye-agent-private.successful-purchase-of-online-shop-goods-matching-pv-info-create-customer-status.queue";
    //电商商品购买成功-更新pv订单状态
    public static final String UPDATE_SUCCESSFUL_PURCHASE_OF_ONLINE_SHOP_GOODS_STATUS_EXCHANGE = "yiye-agent-private.update-successful-purchase-of-online-shop-goods-status-topic.exchange";
    public static final String UPDATE_SUCCESSFUL_PURCHASE_OF_ONLINE_SHOP_GOODS_STATUS_KEY = "yiye-agent-private.update-successful-purchase-of-online-shop-goods-status.queue";

    //上报完成后，根据蓝链匹配的pv，修改pv信息
    public static final String UPLOAD_SUCCESS_MATCHING_PV_UPDATE_PAGE_VIEW_INFO_EXCHANGE = "yiye-agent-private.upload.success.matching.pv.update.page.info.exchange";
    public static final String UPLOAD_SUCCESS_MATCHING_PV_UPDATE_PAGE_VIEW_INFO_KEY = "yiye-agent-private.upload.success.matching.pv.update.page.view.info.queue";
    //上报完成后，修改pv上报状态
    public static final String UPDATE_AD_UPLOAD_STATUS_EXCHANGE = "yiye-agent-private.update-ad-upload-status.exchange";
    public static final String UPDATE_AD_UPLOAD_STATUS_KEY = "yiye-agent-private.update-ad-upload-status.queue";
    //上报完成后，根据蓝链匹配的pv，修改填单、客资信息
    public static final String UPLOAD_SUCCESS_MATCHING_PV_UPDATE_SUBMIT_DATA_INFO_EXCHANGE = "yiye-agent-private.upload.success.matching.pv.update.submit.data.info.exchange";
    public static final String UPLOAD_SUCCESS_MATCHING_PV_UPDATE_SUBMIT_DATA_INFO_KEY = "yiye-agent-private.upload.success.matching.pv.update.submit.data.info.queue";

    //上报完成后，巨量api上报回传receive_url回调后续解析队列
    public static final String OCEAN_ENGINE_RECEIVE_URL_CALLBACK_QUEUE_EXCHANGE = "yiye-agent-private.collect-submit.ocean-engine-receive-url-callback-queue.exchange";
    public static final String OCEAN_ENGINE_RECEIVE_URL_CALLBACK_QUEUE_KEY = "yiye-agent-private.collect-submit.ocean-engine-receive-url-callback-queue.queue";

    //clickhouse pv 状态更新
    public static final String UPDATE_PAGE_VIEW_INFO_LOG_EXCHANGE = "yiye-agent-private.collect-pageview-log-topic.exchange";
    public static final String UPDATE_PAGE_VIEW_INFO_LOG_KEY = "yiye-agent-private.collect-pageview-log-topic.queue";
    //添加企业微信队列
    public static final String ENTERPRISEWECHATCUSTOMERFILE_EXCHANGE = "yiye-agent-private.add-enterprise-wechat-customer-file.upload.queue";
    public static final String ENTERPRISEWECHATCUSTOMERFILE_KEY = "yiye-agent-private.add-enterprise-wechat-customer-file.upload.queue";

    //【添加企业微信成功】创建【客资】
    public static final String ADD_WORK_WECHAT_SUCCESS_ADD_SUBMIT_EXCHANGE = "yiye-agent-private.add-work-wechat-success-add-submit-topic.exchange";
    public static final String BINDING_ADD_WORK_WECHAT_SUCCESS_ADD_SUBMIT_KEY = "yiye-agent-private.collect.submit.add-work-wechat-success-add-submit.queue";
    //小程序上传代码
    public static final String UPDATE_WECHAT_APPLET_UPLOAD_EXCHANGE = "yiye-agent-private.wpdate.wechat.applet.upload.exchange";
    public static final String UPDATE_WECHAT_APPLET_UPLOAD_KEY = "yiye-agent-private.wpdate.wechat.applet.upload.key";

    public static final String BOSS_INIT_CUSTOMER_ENV = "yiye-agent-boss.init-customer-env.exchange";

    /**
     * 企业微信标签上报
     */
    public static final String WORK_WEIXIN_TAG_EXCHANGE = "yiye-agent-private.enterprise-wechat-tag-new.exchange";
    public static final String WORK_WEIXIN_TAG_QUEUE = "yiye-agent-private.enterprise-wechat-tag-new.queue";

    /**
     * 企业微信实时标签处理
     */
    public static final String WORK_WEIXIN_TAG_UPDATE_EXCHANGE = "yiye-agent-private.enterprise-wechat-tag_update.exchange";
    public static final String WORK_WEIXIN_TAG_UPDATE_QUEUE = "yiye-agent-private.enterprise-wechat-tag_update.queue";

    /**
     * 企业API自动打标签后通知标签上报处理
     */
    public static final String WORK_WEIXIN_TAG_UPLOAD_EXEC_EXCHANGE = "yiye-agent-private.enterprise-wechat-tag-upload-exec.exchange";
    public static final String WORK_WEIXIN_TAG_UPLOAD_EXEC_QUEUE = "yiye-agent-private.enterprise-wechat-tag-upload-exec.queue";

    /**
     * 企微代开发回调消息处理
     */
    public static final String WORK_WEIXIN_DEVELOP_TICKET_EXCHANGE = "yiye-agent-private.enterprise-wechat-develop-ticket.exchange";
    public static final String WORK_WEIXIN_DEVELOP_TICKET_QUEUE = "yiye-agent-private.enterprise-wechat-develop-ticket.queue";

    /**
     * 企微代开发回调消息处理-包含解密操作
     */
    public static final String WORK_WEIXIN_DEVELOP_TICKET_NEW_EXCHANGE = "yiye-agent-private.enterprise-wechat-develop-ticket-new.exchange";
    public static final String WORK_WEIXIN_DEVELOP_TICKET_NEW_QUEUE = "yiye-agent-private.enterprise-wechat-develop-ticket-new.queue";

    /**
     * 企业微信第三方应用客服组件回调消息处理
     */
    public static final String ENTERPRISE_WECHAT_THIRD_PARTY_CUSTOMER_SERVICE_COMPONENT_TICKET_EXCHANGE = "yiye-agent-private.enterprise-wechat-third-party-customer-service-component-ticket.exchange";
    public static final String ENTERPRISE_WECHAT_THIRD_PARTY_CUSTOMER_SERVICE_COMPONENT_TICKET_QUEUE = "yiye-agent-private.enterprise-wechat-third-party-customer-service-component-ticket.queue";

    /**
     * 企业微信服务商回调通知
     */
    public static final String ENTERPRISE_WECHAT_SERVICE_PROVIDER_TICKET_EXCHANGE = "yiye-agent-private.enterprise-wechat-service-provider-callback.exchange";
    public static final String ENTERPRISE_WECHAT_SERVICE_PROVIDER_TICKET_QUEUE = "yiye-agent-private.enterprise-wechat-service-provider-callback.queue";


    /**
     * 抖音小程序ticket、授权处理
     */
    public static final String DOUYIN_COMMON_MESSAGE_EXCHANGE = "yiye-agent-private.douyin_common_message.exchange";
    public static final String DOUYIN_COMMON_MESSAGE_QUEUE = "yiye-agent-private.douyin_common_message.queue";

    /**
     * 抖音小程序代码提交审核
     */
    public static final String DOUYIN_CODE_SUBMIT_EXCHANGE = "yiye-agent-private.douyin_code_submit.exchange";
    public static final String DOUYIN_CODE_SUBMIT_QUEUE = "yiye-agent-private.douyin_code_submit.queue";

    /**
     * 企业微信会话存档 标签编辑事件处理
     */
    public static final String ENTERPRISE_SESSION_TAG_CHANGE_EXCHANGE = "yiye-agent-private.enterprise_session_tag_change.exchange";

    public static final String ENTERPRISE_SESSION_TAG_CHANGE_QUEUE = "yiye-agent-private.enterprise_session_tag_change.queue";

    /**
     * 企业微信会话存档 客服删除客户 清理标签
     */
    public static final String ENTERPRISE_SESSION_TAG_DELETE_EXCHANGE = "yiye-agent-private.enterprise_session_tag_delete.exchange";

    public static final String ENTERPRISE_SESSION_TAG_DELETE_QUEUE = "yiye-agent-private.enterprise_session_tag_delete.queue";

    /**
     * 企业微信会话存档 企微添加事件处理
     */
    public static final String ENTERPRISE_SESSION_ADD_CUSTOMER_EXCHANGE = "yiye-agent-private.enterprise_session_add_customer.exchange";

    public static final String ENTERPRISE_SESSION_ADD_CUSTOMER_QUEUE = "yiye-agent-private.enterprise_session_add_customer.queue";

    /**
     * 企业微信会话存档 企微删除事件处理
     */
    public static final String ENTERPRISE_SESSION_DEL_CUSTOMER_EXCHANGE = "yiye-agent-private.enterprise_session_del_customer.exchange";

    public static final String ENTERPRISE_SESSION_DEL_CUSTOMER_QUEUE = "yiye-agent-private.enterprise_session_del_customer.queue";

    public static final String ENTERPRISE_SESSION_ADD_RECORD_CUSTOMER_EXCHANGE = "yiye-agent-private.enterprise_session_add_record_customer.exchange";

    public static final String ENTERPRISE_SESSION_ADD_RECORD_CUSTOMER_QUEUE = "yiye-agent-private.enterprise_session_add_record_customer.queue";


    /**
     * 浏览页面上报
     */
    public static final String PAGE_VIEW_UPLOAD_EXCHANGE = "yiye-agent-private.pageview-upload.exchange";
    public static final String PAGE_VIEW_UPLOAD_QUEUE = "yiye-agent-private.pageview-upload.queue";

    public static final String PAGE_VIEW_BY_LPID_UPLOAD_EXCHANGE = "yiye-agent-private.pageview-by-lpid-upload.exchange";
    public static final String PAGE_VIEW_BY_LPID_UPLOAD_QUEUE = "yiye-agent-private.pageview-by-lpid-upload.queue";

    /**
     * 淘宝电影转链
     */
    public static final String TAOBAO_MOVIE_LINK_RECORD_EXCHANGE = "yiye-agent-private.taobao-movie-link-record.exchange";
    public static final String TAOBAO_MOVIE_LINK_RECORD_QUEUE = "yiye-agent-private.taobao-movie-link-record.queue";

    /**
     * 淘宝CID订单推送处理
     */
    public static final String TAOBAO_MOVIE_WEBHOOK_HANDLE_EXCHANGE = "yiye-agent-private.taobao-movie-webhook-handle.exchange";
    public static final String TAOBAO_MOVIE_WEBHOOK_HANDLE_QUEUE = "yiye-agent-private.taobao-movie-webhook-handle.queue";

    /**
     * 淘宝高效转链
     */
    public static final String TAOKE_LINK_EXCHANGE = "yiye-agent-private.taoke-link.exchange";
    public static final String TAOKE_LINK_QUEUE = "yiye-agent-private.taoke-link.queue";

    /**
     * 京东转链
     */
    public static final String JD_LINK_EXCHANGE = "yiye-agent-private.jd-link.exchange";
    public static final String JD_LINK_QUEUE = "yiye-agent-private.jd-link.queue";

    /**
     * 京东订单匹配
     */
    public static final String JD_ORDER_MATCH_EXCHANGE = "yiye-agent-private.jd-order-match.exchange";
    public static final String JD_ORDER_MATCH_QUEUE = "yiye-agent-private.jd-order-match.queue";

    /**
     * 拼多多转链
     */
    public static final String PDD_LINK_EXCHANGE = "yiye-agent-private.pdd-link.exchange";
    public static final String PDD_LINK_QUEUE = "yiye-agent-private.pdd-link.queue";

    /**
     * 拼多多订单匹配
     */
    public static final String PDD_ORDER_MATCH_EXCHANGE = "yiye-agent-private.pdd-order-match.exchange";
    public static final String PDD_ORDER_MATCH_QUEUE = "yiye-agent-private.pdd-order-match.queue";

    /**
     * 会话存档-员工信息同步
     */
    public static final String SESSION_ARCHIVE_EMPLOYEE_SYNC_EXCHANGE = "yiye-agent-private.session-archive-employee-sync.exchange";
    public static final String SESSION_ARCHIVE_EMPLOYEE_SYNC_QUEUE = "yiye-agent-private.session-archive-employee-sync.queue";

    /**
     * 企微群信息同步
     */
    public static final String WECHAT_GROUP_CHAT_SYNC_EXCHANGE = "yiye-agent-private.wechat-group-chat-sync.exchange";
    public static final String WECHAT_GROUP_CHAT_SYNC_QUEUE = "yiye-agent-private.wechat-group-chat-sync.queue";

    /**
     * 企微群回调处理
     */
    public static final String WECHAT_GROUP_CHAT_CALLBACK_EXCHANGE = "yiye-agent-private.wechat-group-chat-callback.exchange";
    public static final String WECHAT_GROUP_CHAT_CALLBACK_QUEUE = "yiye-agent-private.wechat-group-chat-callback.queue";

    /**
     * 落地页指标统计
     */
    public static final String LANDING_PAGE_INDICATOR_STATISTICS_EXCHANGE = "yiye-agent-private.landing-page-indicator-statistics-clickhouse.exchange";
    public static final String LANDING_PAGE_INDICATOR_STATISTICS_QUEUE = "yiye-agent-private.landing-page-indicator-statistics-clickhouse.queue";

    public static final String TAO_BAO_MOVIE_EVENT_SUCCESS_UPLOAD_EXCHANGE = "yiye-agent-private.tao-bao-movie-event-success-upload.exchange";
    public static final String TAO_BAO_MOVIE_EVENT_SUCCESS_UPLOAD_QUEUE = "yiye-agent-private.tao-bao-movie-event-success-upload.queue";


    /**
     * 落地页指标重试
     */
    public static final String LANDING_PAGE_INDICATOR_STATISTICS_RETRY_EXCHANGE = "yiye-agent-private.landing-page-indicator-statistics-clickhouse-retry.exchange";

    public static final String LANDING_PAGE_INDICATOR_STATISTICS_RETRY_QUEUE = "yiye-agent-private.landing-page-indicator-statistics-clickhouse-retry.queue";

    /**
     * 拉取投放账户广告数据失败，进行重新补偿
     */
    public static final String MARKET_AD_DATA_PULL_FAIL_RE_SOLID_EXCHANGE = "yiye-agent-private.market_ad_data_pull_fail_re_solid.exchange";

    public static final String MARKET_AD_DATA_PULL_FAIL_RE_SOLID_QUEUE = "yiye-agent-private.market_ad_data_pull_fail_re_solid.queue";

    /**
     * PMP消息产生的 通知
     */
    public static final String NOTICE_CREATE_EXCHANGE = "yiye-agent-private.notice-create.exchange";
    public static final String NOTICE_CREATE_QUEUE = "yiye-agent-private.notice-create.queue";


    public static final String NOTICE_DOMAIN_EXPIRE_EXCHANGE = "yiye-agent-private.notice-domain_expire.exchange";
    public static final String NOTICE_DOMAIN_EXPIRE_QUEUE = "yiye-agent-private.notice-domain_expire.queue";


    /**
     * operationLog
     */
    public static final String OPERATOR_LOG_GENERATE = "yiye-agent-private.operator-log-generate-new.queue";

    public static final String FORBID_APPLET_NOTICE_EXCHANGE = "yiye-agent-private.forbid-applet-notice.exchange";
    public static final String FORBID_APPLET_NOTICE_QUEUE = "yiye-agent-private.forbid-applet-notice.queue";

    /**
     * 短信相关
     */
    public static final String QUEUE_SMS_SEND_EXCHANGE = "yiye.agent.private.direct";
    public static final String QUEUE_SMS_SEND = "yiye.agent.private.sms.send";
    /**
     * 微信客服消息回复的mq
     */
    public static final String WECHAT_ROBOT_CUSTOMER_MESSAGE_EXCHANGE = "yiye-agent-private.wechat-robot-customer-message.exchange";
    public static final String WECHAT_ROBOT_CUSTOMER_MESSAGE_KEY = "yiye-agent-private.wechat-robot-customer-message.queue";
    /**
     * 企业推回调数据保存
     */
    public static final String WECHAT_QIYETUI_CALLBACK_EXCHANGE = "yiye-agent-private.wechat-qiyetui-callback.exchange";
    public static final String WECHAT_QIYETUI_CALLBACK_KEY = "yiye-agent-private.wechat-qiyetui-callback.queue";

    /**
     * 新版企业推回调数据保存
     */
    public static final String WECHAT_DATA_QIYETUI_CALLBACK_EXCHANGE = "yiye-agent-private.wechat-data-qiyetui-callback.exchange";
    public static final String WECHAT_DATA_QIYETUI_CALLBACK_KEY = "yiye-agent-private.wechat-data-qiyetui-callback.queue";

    /**
     * 企业推回调数据匹配
     */
    public static final String WECHAT_QIYETUI_CALLBACK_MATCH_EXCHANGE = "yiye-agent-private.wechat-qiyetui-callback-match.exchange";
    public static final String WECHAT_QIYETUI_CALLBACK_MATCH_KEY = "yiye-agent-private.wechat-qiyetui-callback-match.queue";

    /**
     * 企业推回调数据匹配
     */
    public static final String WECHAT_DATA_QIYETUI_CALLBACK_MATCH_EXCHANGE = "yiye-agent-private.wechat-data-qiyetui-callback-match.exchange";
    public static final String WECHAT_DATA_QIYETUI_CALLBACK_MATCH_KEY = "yiye-agent-private.wechat-data-qiyetui-callback-match.queue";

    /**
     * 企业推事件上报
     */
    public static final String WECHAT_DATA_QIYETUI_EVENT_REPORT_EXCHANGE = "yiye-agent-private.wechat-data-qiyetui-event-report.exchange";
    public static final String WECHAT_DATA_QIYETUI_EVENT_REPORT_KEY = "yiye-agent-private.wechat-data-qiyetui-event-report.queue";

    /**
     * 企业推小程序静默授权获取openid修改pv信息
     */
    public static final String WECHAT_DATA_QIYETUI_UPDATE_PV_OPENID_EXCHANGE = "yiye-agent-private.wechat-data-qiyetui-update-openid.exchange";
    public static final String WECHAT_DATA_QIYETUI_UPDATE_PV_OPENID_KEY = "yiye-agent-private.wechat-data-qiyetui-update-openid.queue";



    /**
     * 企业推落地页审核记录保存
     */
    public static final String WECHAT_DATA_QIYETUI_AUDIT_RECORD_EXCHANGE = "yiye-agent-private.wechat-data-qiyetui-audit-record.exchange";
    public static final String WECHAT_DATA_QIYETUI_AUDIT_RECORD_KEY = "yiye-agent-private.wechat-data-qiyetui-audit-record.queue";


    public static final String WECHAT_DATA_SESSION_MESSAGE_EXCHANGE = "yiye-agent-private.wechat-data-session-message.exchange";
    public static final String WECHAT_DATA_SESSION_MESSAGE_KEY = "yiye-agent-private.wechat-data-session-message.queue";

    /**
     * 企业微信标签策略（自动打标）
     */
    public static final String ENTERPRISE_WECHAT_TAG_STRATEGY_EXCHANGE = "yiye-agent-private.enterprise-wechat-tag-strategy.exchange";
    public static final String ENTERPRISE_WECHAT_TAG_STRATEGY_KEY = "yiye-agent-private.enterprise-wechat-tag-strategy.queue";


    /**
     * 企业微信标签策略（自动打标）(延迟队列)
     */
    public static final String ENTERPRISE_WECHAT_TAG_STRATEGY_DELAY_EXCHANGE = "yiye-agent-private.enterprise-wechat-tag-strategy-delay.exchange";
    public static final String ENTERPRISE_WECHAT_TAG_STRATEGY_DELAY_KEY = "yiye-agent-private.enterprise-wechat-tag-strategy-delay.queue";

    /**
     * 企业微信会话存档消息匹配客资上报
     */
    public static final String WECHAT_DATA_SESSION_MESSAGE_UPLOAD_EXCHANGE = "yiye-agent-private.wechat-data-session-message.upload.exchange";
    public static final String WECHAT_DATA_SESSION_MESSAGE_UPLOAD_KEY = "yiye-agent-private.wechat-data-session-message.upload.queue";

    /**
     * 会话存档回调事件
     */
    public static final String WECHAT_DATA_SESSION_MESSAGE_CALLBACK_EXCHANGE = "yiye-agent-private.wechat-data-session-message-callback.exchange";
    public static final String WECHAT_DATA_SESSION_MESSAGE_CALLBACK_KEY = "yiye-agent-private.wechat-data-session-message-callback.queue";

    /**
     * 会话存档文件临时存储事件
     */
    public static final String WECHAT_DATA_SESSION_MESSAGE_FILE_EXCHANGE = "yiye-agent-private.wechat-data-session-message-file.exchange";
    public static final String WECHAT_DATA_SESSION_MESSAGE_FILE_KEY = "yiye-agent-private.wechat-data-session-message-file.queue";



    /**
     * 异步保存生成schema的参数
     */
    public static final String WECHAT_APPLET_GENERATE_SCHEME_VISIT_EXCHANGE = "yiye-agent-private.wechat-applet-generate-scheme-visit.exchange";
    public static final String WECHAT_APPLET_GENERATE_SCHEME_VISIT_KEY = "yiye-agent-private.wechat-applet-generate-scheme-visit.queue";

    /**
     * 异步保存进入小程序授权的参数
     */
    public static final String WECHAT_APPLET_AUTH_SESSION_VISIT_EXCHANGE = "yiye-agent-private.wechat-applet-auth-session-visit.exchange";
    public static final String WECHAT_APPLET_AUTH_SESSION_VISIT_KEY = "yiye-agent-private.wechat-applet-auth-session-visit.queue";

    /**
     * 小程序立即预约更新曝光数据
     */
    public static final String WECHAT_APPLE_PHONE_EXCHANGE = "yiye-agent-private.wechat-apple-phone.exchange";
    public static final String WECHAT_APPLE_PHONE_QUEUE = "yiye-agent-private.wechat-apple-phone-page-view.queue";

    /**
     * 巨量原生页推送UA信息更新pv
     */
    public static final String JU_LIANG_YUAN_SHENG_YE_UPDATE_PV_UA_INFO_EXCHANGE = "yiye-agent-private.ju-liang-yuan-sheng-ye-update-pv-ua-info.exchange";
    public static final String JU_LIANG_YUAN_SHENG_YE_UPDATE_PV_UA_INFO_QUEUE = "yiye-agent-private.ju-liang-yuan-sheng-ye-update-pv-ua-info.queue";

    /**
     * 告警服务监听
     */
    public static final String WARN_SEND_EXCHANGE = "yiye-agent-private.warn-send.exchange";
    public static final String WARN_SEND_QUEUE = "yiye-agent-private.warn-send.queue";

    /**
     * 告警服务监听
     */
    public static final String ERROR_SEND_EXCHANGE = "yiye-agent-private.work-wechat-api-error-send.exchange";
    public static final String ERROR_SEND_QUEUE = "yiye-agent-private.work-wechat-api-error-send.queue";

    /**
     * 落地页修改触发客资落地页冗余字段修改
     */
    public static final String LANDING_PAGE_CUSTOMER_EXCHANGE = "yiye-agent-private.landing-page-customer.exchange";
    public static final String LANDING_PAGE_CUSTOMER_QUEUE = "yiye-agent-private.landing-page-customer.queue";

    /**
     * 落地页修改触发客资落地页冗余字段修改
     */
    public static final String LANDING_PAGE_CHANNEL_CUSTOMER_EXCHANGE = "yiye-agent-private.landing-channel-customer.exchange";
    public static final String LANDING_PAGE_CHANNEL_CUSTOMER_QUEUE = "yiye-agent-private.landing-channel-customer.queue";


    public static final String WARN_MESSAGE_EXCHANGE = "yiye-agent-private.message-warn-notice.exchange";
    public static final String WARN_MESSAGE_QUEUE = "yiye-agent-private.message-warn-notice.queue";

    /**
     * 上报记录合并入新表
     */
    public static final String UPLOAD_SUBMIT_NEXT_EXCHANGE = "yiye-agent-private.collect.submit.next.exchange";
    public static final String UPLOAD_SUBMIT_NEXT_QUEUE = "yiye-agent-private.collect.submit.next.queue";

    /**
     * 向已开户的用户私库中添加新浪微博媒体平台配置信息
     */
    public static final String DB_SERVER_INSERT_OR_UPDATE_DATA_EXCHANGE = "yiye-agent-private.db.server.insert.or.update.data.exchange";
    public static final String DB_SERVER_INSERT_OR_UPDATE_DATA_QUEUE = "yiye-agent-private.db.server.insert.or.update.data.queue";

    public static final String DB_SERVER_CREATE_TABLE_AND_COPY_DATA_BY_SOURCE_TABLE_EXCHANGE = "yiye-agent-private.db.server.create.table.and.copy.data.by.source.table.exchange";
    public static final String DB_SERVER_CREATE_TABLE_AND_COPY_DATA_BY_SOURCE_TABLE_QUEUE = "yiye-agent-private.db.server.create.table.and.copy.data.by.source.table.queue";

    public static final String DB_SERVER_INSERT_OLD_TABLE_DATA_TO_NEW_TABLE_AND_TRUNCATE_EXCHANGE = "yiye-agent-private.db.server.insert.old.table.data.to.new.table.and.truncate.exchange";
    public static final String DB_SERVER_INSERT_OLD_TABLE_DATA_TO_NEW_TABLE_AND_TRUNCATE_QUEUE = "yiye-agent-private.db.server.insert.old.table.data.to.new.and.truncatetable.queue";

    //MQ发送邮件
    public static final String MESSAGE_SEND_EMAIL_EXCHANGE = "yiye-agent-private.message-send-email-topic.exchange";
    public static final String MESSAGE_SEND_EMAIL_QUEUE = "yiye-agent-private.message-send-email.queue";

    //站外信发送
    public static final String OUT_SITE_MSG_SEND_EXCHANGE = "yiye-agent-private.out-site-msg-send.exchange";
    public static final String OUT_SITE_MSG_SEND_QUEUE = "yiye-agent-private.out-site-msg-send.queue";

    //导出访客细察，异步上传文件OSS服务器
    public static final String PAGE_VIEW_SEND_UPLOAD_PAGE_VIEW_INFO_FILE_TO_OSS_EXCHANGE = "yiye-agent-private.page.view.send.upload.page.view.info.file.to.oss.exchange";
    public static final String PAGE_VIEW_SEND_UPLOAD_PAGE_VIEW_INFO_FILE_TO_OSS_QUEUE = "yiye-agent-private.page.view.send.upload.page.view.info.file.to.oss.queue";

    //导出客资，异步上传文件OSS服务器
    public static final String SEND_UPLOAD_CUSTOMER_FILE_TO_OSS_EXCHANGE = "yiye-agent-private.send.upload.customer.file.to.oss.exchange";
    public static final String SEND_UPLOAD_CUSTOMER_FILE_TO_OSS_QUEUE = "yiye-agent-private.send.upload.customer.file.to.oss.queue";

    //长按识别企业微信群聊二维码
    public static final String PAGE_VIEW_SEND_IDENTIFY_GROUP_CHAT_QR_CODE_STATUS_EXCHANGE = "yiye-agent-private.page.view.send.identify.group.chat.qr.code.status.exchange";
    public static final String PAGE_VIEW_SEND_IDENTIFY_GROUP_CHAT_QR_CODE_STATUS_QUEUE = "yiye-agent-private.page.view.send.identify.group.chat.qr.code.status.queue";


    //处理快照
    public static final String DEAL_WITH_SNAPSHOT_EXCHANGE = "yiye-agent-private.snapshot.deal-with-topic.exchange";
    public static final String DEAL_WITH_SNAPSHOT_KEY = "yiye-agent-private.snapshot.deal-with.queue";

    //快照流回调
    public static final String DEAL_WITH_CALLBACK_SNAPSHOT_EXCHANGE = "yiye-agent-private.snapshot.deal-with-callback-topic.exchange";
    public static final String DEAL_WITH_CALLBACK_SNAPSHOT_KEY = "yiye-agent-private.snapshot.deal-with-callback.queue";

    //处理公众号历史文章页快照
    public static final String DEAL_WITH_WECHAT_OFFICIAL_HISTORY_SNAPSHOT_EXCHANGE = "yiye-agent-private.snapshot.deal-with-wechat-official-history-topic.exchange";
    public static final String DEAL_WITH_WECHAT_OFFICIAL_HISTORY_SNAPSHOT_KEY = "yiye-agent-private.snapshot.deal-with-wechat-official-history.queue";

    //公众号历史文章页快照流回调
    public static final String DEAL_WITH_CALLBACK_WECHAT_OFFICIAL_HISTORY_SNAPSHOT_EXCHANGE = "yiye-agent-private.snapshot.deal-with-callback-wechat-official-history-topic.exchange";
    public static final String DEAL_WITH_CALLBACK_WECHAT_OFFICIAL_HISTORY_SNAPSHOT_KEY = "yiye-agent-private.snapshot.deal-with-callback-wechat-official-history.queue";

    //处理空白页文章页快照
    public static final String DEAL_WITH_WECHAT_OFFICIAL_MID_SNAPSHOT_EXCHANGE = "yiye-agent-private.snapshot.deal-with-wechat-official-mid-topic.exchange";
    public static final String DEAL_WITH_WECHAT_OFFICIAL_MID_SNAPSHOT_KEY = "yiye-agent-private.snapshot.deal-with-wechat-official-mid.queue";

    //空白页页快照流回调
    public static final String DEAL_WITH_CALLBACK_WECHAT_OFFICIAL_MID_SNAPSHOT_EXCHANGE = "yiye-agent-private.snapshot.deal-with-callback-wechat-official-mid-topic.exchange";
    public static final String DEAL_WITH_CALLBACK_WECHAT_OFFICIAL_MID_SNAPSHOT_KEY = "yiye-agent-private.snapshot.deal-with-callback-wechat-official-mid.queue";


    //AIP 渲染任务
    public static final String LANDING_PAGE_AIP_GENERATE_EXCHANGE = "yiye-agent-private.landing-page-aip-generate.exchange";
    public static final String LANDING_PAGE_AIP_GENERATE_KEY = "yiye-agent-private.landing-page-aip-generate.queue";

    //AIP 清理任务 通过落地页清理
    public static final String LANDING_PAGE_AIP_CLEAR_CACHE_EXCHANGE = "yiye-agent-private.landing-page-aip-clear-cache.exchange";
    public static final String LANDING_PAGE_AIP_CLEAR_CACHE_KEY = "yiye-agent-private.landing-page-aip-clear-cache.queue";
    //AIP 清理任务 通过渠道清理
    public static final String LANDING_PAGE_AIP_CLEAR_CACHE_BY_CHANNEL_EXCHANGE = "yiye-agent-private.landing-page-channel-aip-clear-cache.exchange";
    public static final String LANDING_PAGE_AIP_CLEAR_CACHE_BY_CHANNEL_KEY = "yiye-agent-private.landing-page-channel-aip-clear-cache.queue";
    /**
     * 企业推创建客资后 补全字段 交换机
     */
    public static final String QIYETUI_CUSTOMER_RETRY_EXCHANGE = "yiye-agent-private.qiyetui-customer-retry.exchange";
    /**
     * 企业推创建客资后 补全字段 队列
     */
    public static final String QIYETUI_CUSTOMER_RETRY_KEY = "yiye-agent-private.qiyetui-customer-retry.queue";



    /**
     * 企业推客资 查询会话记录
     */
    public static final String QIYETUI_CUSTOMER_SESSION_MATCH_EXCHANGE = "yiye-agent-private.qiyetui-customer-session-match.exchange";
    /**
     * 企业推客资 查询会话记录 队列
     */
    public static final String QIYETUI_CUSTOMER_SESSION_MATCH_KEY = "yiye-agent-private.qiyetui-customer-session-match.queue";

    /**
     * 代开发应用创建客资后 匹配企业推客资 补全字段 交换机
     */
    public static final String GENERATION_DEVELOPMENT_QIYETUI_CUSTOMER_RETRY_EXCHANGE = "yiye-agent-private.generation-development-qiyetui-customer-retry.exchange";
    /**
     * 代开发应用创建客资后 匹配企业推客资 补全字段 队列
     */
    public static final String GENERATION_DEVELOPMENT_QIYETUI_CUSTOMER_RETRY_KEY = "yiye-agent-private.generation-development-qiyetui-customer-retry.queue";

    public static final String ENTERPRISE_CUSTOMER_BATCH_FLUSH_EXCHANGE="yiye-agent-private.enterprise-customer-batch-flush.exchange";
    /**
     * 客资状态更新mq
     */
    public static final String ENTERPRISE_CUSTOMER_BATCH_FLUSH_PRIVATE_KEY="yiye-agent-private.enterprise-customer-batch-flush.queue";


    /**
     * 企微代开发 群消息回调更新群动态二维码信息
     */
    public static final String WORK_WEIXIN_DEVELOP_GROUP_UPDATE_QRCODE_EXCHANGE = "yiye-agent-private.enterprise-wechat-develop-group-update-qrcode.exchange";
    public static final String WORK_WEIXIN_DEVELOP_GROUP_UPDATE_QRCODE_QUEUE = "yiye-agent-private.enterprise-wechat-develop-group-update-qrcode.queue";
    /**
     * 企业微信客服链路加粉事件 - 交换机
     */
    public static final String ENTERPRISE_WECHAT_CUSTOMER_SERVICE_EVENT_EXCHANGE = "yiye-agent-private.enterprise-wechat-customer-service-event-clickhouse.exchange";

    /**
     * 企业微信客服链路加粉事件 - 队列
     */
    public static final String ENTERPRISE_WECHAT_CUSTOMER_SERVICE_EVENT_KEY = "yiye-agent-private.enterprise-wechat-customer-service-key-clickhouse.queue";

    /**
     * 微信客服列表指标统计重试队列
     */
    public static final String ENTERPRISE_WECHAT_CUSTOMER_SERVICE_EVENT_RETRY_KEY = "yiye-agent-private.enterprise-wechat-customer-service-key-retry-clickhouse.queue";

    /**
     * 微信客服列表指标统计重试交换机
     */
    public static final String ENTERPRISE_WECHAT_CUSTOMER_SERVICE_EVENT_RETRY_EXCHANGE = "yiye-agent-private.enterprise-wechat-customer-service-event-retry-clickhouse.exchange";


    /**
     * 通过落地页组件直接唤起添加企业微信客户名片链路上报事件推送
     */
    public static final String LANDING_PAGE_WIDGET_ADD_WECHAT_SUCCESS_UPLOAD_EXCHANGE = "yiye-agent-private.landing-page-widget-add-enterprise-wechat-success.upload.exchange";
    public static final String LANDING_PAGE_WIDGET_ADD_WECHAT_SUCCESS_UPLOAD_KEY = "yiye-agent-private.landing-page-widget-add-enterprise-wechat-success.upload.queue";


    /**
     * 根据【删除类型】删除中台用户缓存
     */
    public static final String PRIVATE_CHANGE_UCENTER_SERVICE_CLEAR_USER_LOGIN_CACHE_EXCHANGE = "yiye-agent-private.change-ucenter-service-clear-user-login-cache.exchange";
    public static final String PRIVATE_CHANGE_UCENTER_SERVICE_CLEAR_USER_LOGIN_CACHE_KEY = "yiye-agent-private.change-ucenter-service-clear-user-login-cache.queue";


    /**
     * 初始化获客链接
     */
    public static final String WORK_WEIXIN_INIT_CUSTOMER_ACQUISITION_EXCHANGE = "yiye-agent-private.enterprise-wechat-init-customer-acquisition.exchange";
    public static final String WORK_WEIXIN_INIT_CUSTOMER_ACQUISITION_QUEUE = "yiye-agent-private.enterprise-wechat-init-customer-acquisition.queue";

    /**
     * 新增客服创建获客链接
     */
    public static final String WORK_WEIXIN_CREATE_CUSTOMER_ACQUISITION_EXCHANGE = "yiye-agent-private.enterprise-wechat-create-customer-acquisition.exchange";
    public static final String WORK_WEIXIN_CREATE_CUSTOMER_ACQUISITION_QUEUE = "yiye-agent-private.enterprise-wechat-create-customer-acquisition.queue";

    /**
     * 获取获客链接后修改pv信息
     */
    public static final String WORK_WEIXIN_CUSTOMER_ACQUISITION_UPDATE_PV_EXCHANGE = "yiye-agent-private.enterprise-wechat-customer-acquisition-updatepv.exchange";
    public static final String WORK_WEIXIN_CUSTOMER_ACQUISITION_UPDATE_PV_QUEUE = "yiye-agent-private.enterprise-wechat-customer-acquisition-updatepv.queue";

    //微信客服机器人，发送获客链接后，修改对应pv的信息
    public static final String ROBOT_WORK_WEIXIN_CUSTOMER_ACQUISITION_UPDATE_PV_EXCHANGE = "yiye-agent-private.robot-enterprise-wechat-customer-acquisition-updatepv.exchange";
    public static final String ROBOT_WORK_WEIXIN_CUSTOMER_ACQUISITION_UPDATE_PV_QUEUE = "yiye-agent-private.robot-enterprise-wechat-customer-acquisition-updatepv.queue";

    /**
     * 获取机器人活码后修改pv信息
     */
    public static final String ROBOT_LIVE_CODE_UPDATE_PV_EXCHANGE = "yiye-agent-private.robot-live-code-update-pv.exchange";
    public static final String ROBOT_LIVE_CODE_UPDATE_PV_QUEUE = "yiye-agent-private.robot-live-code-update-pv.queue";

    /**
     * 发送客服二维码（微信客服机器人）后修改pv信息
     */
    public static final String ROBOT_SEND_QR_CODE_UPDATE_PV_EXCHANGE = "yiye-agent-private.robot-send-qr-code-update-pv.exchange";
    public static final String ROBOT_SEND_QR_CODE_UPDATE_PV_QUEUE = "yiye-agent-private.robot-send-qr-code-update-pv.queue";

    /**
     * 远程删除获客链接
     */
    public static final String WORK_WEIXIN_CUSTOMER_ACQUISITION_DELETE_EXCHANGE = "yiye-agent-private.enterprise-wechat-customer-acquisition-delete.exchange";
    public static final String WORK_WEIXIN_CUSTOMER_ACQUISITION_DELETE_QUEUE = "yiye-agent-private.enterprise-wechat-customer-acquisition-delete.queue";

    /**
     * 获客助手链接防刷
     */
    public static final String WORK_WEIXIN_CUSTOMER_ACQUISITION_CHECK_REPEAT_EXCHANGE = "yiye-agent-private.enterprise-wechat-customer-acquisition-repeat-check-delay.exchange";

    public static final String WORK_WEIXIN_CUSTOMER_ACQUISITION_CHECK_REPEAT_KEY = "yiye-agent-private.enterprise-wechat-customer-acquisition-repeat-check-delay.queue";

    //校验删除远端获客链接
    public static final String WORK_WEIXIN_CUSTOMER_ACQUISITION_CHECK_DELETE_EXCHANGE = "yiye-agent-private.enterprise-wechat-customer-acquisition-check-delete.exchange";
    public static final String WORK_WEIXIN_CUSTOMER_ACQUISITION_CHECK_DELETE_QUEUE = "yiye-agent-private.enterprise-wechat-customer-acquisition-check-delete.queue";


    public static final String WORK_WEIXIN_CUSTOMER_ACQUISITION_CHECK_CROSS_ACCOUNT_DELETE_EXCHANGE = "yiye-agent-private.enterprise-wechat-customer-acquisition-check-delete-cross_account.exchange";
    public static final String WORK_WEIXIN_CUSTOMER_ACQUISITION_CHECK_CROSS_ACCOUNT_DELETE_QUEUE = "yiye-agent-private.enterprise-wechat-customer-acquisition-check-delete-cross_account.queue";

    //批量删除获客链接
    public static final String BATCH_DELETE_WORK_WEIXIN_CUSTOMER_ACQUISITION_EXCHANGE = "yiye-agent-private.batch-delete-work-weixin-customer-acquisition.exchange";

    //批量删除获客链接
    public static final String BATCH_DELETE_WORK_WEIXIN_CUSTOMER_ACQUISITION_QUEUE = "yiye-agent-private.batch-delete-work-weixin-customer-acquisition.queue";

    //延迟处理校验删除远端获客链接
    public static final String WORK_WEIXIN_CUSTOMER_ACQUISITION_CHECK_DELETE_DELAY_EXCHANGE = "yiye-agent-private.enterprise-wechat-customer-acquisition-check-delete-delay.exchange";
    public static final String WORK_WEIXIN_CUSTOMER_ACQUISITION_CHECK_DELETE_DELAY_QUEUE = "yiye-agent-private.enterprise-wechat-customer-acquisition-check-delete-delay.queue";

    //微信小程序-飞书告警通知：下线、下线后启用备用小程序、下线后当前项目下无可用小程序
    public static final String WECHAT_APPLET_FEI_SHU_MESSAGE_QUEUE = "yiye-agent-private.wechat-applet-fei-shu-message.queue";
    public static final String WECHAT_APPLET_FEI_SHU_MESSAGE_EXCHANGE = "yiye-agent-private.wechat-applet-fei-shu-message.exchange";

    //微信小程序-飞书告警通知：当前项目下无可用小程序
    public static final String THIS_PROJECT_NOT_WECHAT_APPLET_FEI_SHU_MESSAGE_QUEUE = "yiye-agent-private.this-project-not-wechat-applet-fei-shu-message.queue";
    public static final String THIS_PROJECT_NOT_WECHAT_APPLET_FEI_SHU_MESSAGE_EXCHANGE = "yiye-agent-private.this-project-not-wechat-applet-fei-shu-message.exchange";
    //微信小程序-飞书告警通知：当前项目下无可用小程序-延时统计
    public static final String THIS_PROJECT_NOT_WECHAT_APPLET_FEI_SHU_MESSAGE_DELAY_QUEUE = "yiye-agent-private.this-project-not-wechat-applet-fei-shu-message-delay.queue";
    public static final String THIS_PROJECT_NOT_WECHAT_APPLET_FEI_SHU_MESSAGE_DELAY_EXCHANGE = "yiye-agent-private.this-project-not-wechat-applet-fei-shu-message-delay.exchange";

    //进行log比对的消息
    public static final String USER_OPERATION_LOG_DETAIL_COMPARE_QUEUE = "yiye-agent-private.user-operation-log-detail-compare-queue.queue";
    public static final String USER_OPERATION_LOG_DETAIL_COMPARE_EXCHANGE = "yiye-agent-private.user-operation-log-detail-compare-queue.exchange";

    //进行操作日志log导出的消息
    public static final String USER_OPERATION_LOG_DETAIL_EXPORT_QUEUE = "yiye-agent-private.user-operation-log-detail-export-queue.queue";
    public static final String USER_OPERATION_LOG_DETAIL_EXPORT_EXCHANGE = "yiye-agent-private.user-operation-log-detail-export-queue.exchange";

    //上报配置变更记录日志导出
    public static final String UPLOAD_RECORD_CHANGE_RECORD_EXPORT_QUEUE = "yiye-agent-private.upload-record-change-record-export.queue";
    public static final String UPLOAD_RECORD_CHANGE_RECORD_EXPORT_EXCHANGE = "yiye-agent-private.upload-record-change-record-export.exchange";

    //保存客服上下线的消息
    public static final String SYSTEM_OPERATION_LOG_DETAIL_COMPARE_QUEUE = "yiye-agent-private.system-operation-log-detail-compare-queue.queue";
    public static final String SYSTEM_OPERATION_LOG_DETAIL_COMPARE_EXCHANGE = "yiye-agent-private.system-operation-log-detail-compare-queue.exchange";

    //保存异常监测的消息
    public static final String SYSTEM_OPERATION_LOG_ABNORMAL_MONITOR_QUEUE = "yiye-agent-private.system-operation-log-abnormal-monitor.queue";
    public static final String SYSTEM_OPERATION_LOG_ABNORMAL_MONITOR_EXCHANGE = "yiye-agent-private.system-operation-log-abnormal-monitor.exchange";

    //保存自动开启上线失败的消息
    public static final String SYSTEM_OPERATION_LOG_AUTO_ONLINE_FAIL_QUEUE = "yiye-agent-private.system-operation-log-auto-online-fail.queue";
    public static final String SYSTEM_OPERATION_LOG_AUTO_ONLINE_FAIL_EXCHANGE = "yiye-agent-private.system-operation-log-auto-online-fail.exchange";

    //保存获客助手链接异常的消息
    public static final String SYSTEM_OPERATION_LOG_ACQUISITION_LINK_ABNORMAL_QUEUE = "yiye-agent-private.system-operation-log-acquisition-link-abnormal.queue";
    public static final String SYSTEM_OPERATION_LOG_ACQUISITION_LINK_ABNORMAL_EXCHANGE = "yiye-agent-private.system-operation-log-acquisition-link-abnormal.exchange";

    //保存获客助手链接创建的消息
    public static final String SYSTEM_OPERATION_LOG_ACQUISITION_LINK_CREATE_QUEUE = "yiye-agent-private.system-operation-log-acquisition-link-create.queue";
    public static final String SYSTEM_OPERATION_LOG_ACQUISITION_LINK_CREATE_EXCHANGE = "yiye-agent-private.system-operation-log-acquisition-link-create.exchange";

    //保存获客助手链接变更的消息
    public static final String SYSTEM_OPERATION_LOG_ACQUISITION_LINK_CHANGE_QUEUE = "yiye-agent-private.system-operation-log-acquisition-link-change.queue";
    public static final String SYSTEM_OPERATION_LOG_ACQUISITION_LINK_CHANGE_EXCHANGE = "yiye-agent-private.system-operation-log-acquisition-link-change.exchange";


    //保存获客助手链接删除的消息
    public static final String SYSTEM_OPERATION_LOG_ACQUISITION_LINK_DELETE_QUEUE = "yiye-agent-private.system-operation-log-acquisition-link-delete.queue";
    public static final String SYSTEM_OPERATION_LOG_ACQUISITION_LINK_DELETE_EXCHANGE = "yiye-agent-private.system-operation-log-acquisition-link-delete.exchange";

    //保存客服机器人上下线的消息
    public static final String OPERATION_LOG_ROBOT_USAGE_STATUS_QUEUE = "yiye-agent-private.operation-log-robot-usage-status.queue";
    public static final String OPERATION_LOG_ROBOT_USAGE_STATUS_EXCHANGE = "yiye-agent-private.operation-log-robot-usage-status.exchange";

    //客服触发自动化规则或异常监测下线的消息
    public static final String CUSTOMER_SERVICE_OFFLINE_QUEUE = "yiye-agent-private.customer-service-offline.queue";
    public static final String CUSTOMER_SERVICE_OFFLINE_EXCHANGE = "yiye-agent-private.customer-service-offline.exchange";

    //微信客服分组下无在线客服的消息
    public static final String CUSTOMER_SERVICE_GROUP_NOTICE_QUEUE = "yiye-agent-private.customer-service-group-notice.queue";
    public static final String CUSTOMER_SERVICE_GROUP_NOTICE_EXCHANGE = "yiye-agent-private.customer-service-group-notice.exchange";

    //保存客服对应企微成员名称变更的消息
    public static final String CUSTOMER_SERVICE_WECHAT_NAME_CHANGE_QUEUE = "yiye-agent-private.customer-service-wechat-name-change.queue";
    public static final String CUSTOMER_SERVICE_WECHAT_NAME_CHANGE_EXCHANGE = "yiye-agent-private.customer-service-wechat-name-change.exchange";

    //同步可见范围
    public static final String SYNC_WORK_WECHAT_USER_VISIBLE_RANGE_QUEUE = "yiye-agent-private.sync-work-wechat-user-visible-range.queue";
    public static final String SYNC_WORK_WECHAT_USER_VISIBLE_RANGE_EXCHANGE = "yiye-agent-private.sync-work-wechat-user-visible-range.exchange";

    //企微获客链接
    public static final String WORK_WECHAT_CUSTOMER_ACQUISITION_LINK_QUEUE = "yiye-agent-private.work-wechat-customer-acquisition-link.queue";
    public static final String WORK_WECHAT_CUSTOMER_ACQUISITION_LINK_EXCHANGE = "yiye-agent-private.work-wechat-customer-acquisition-link.exchange";

    //获客助手异常自动生成
    public static final String CUSTOMER_ACQUISITION_ABNORMAL_AUTO_GENERATE_QUEUE = "yiye-agent-private.customer-acquisition-abnormal-auto-generate.queue";
    public static final String CUSTOMER_ACQUISITION_ABNORMAL_AUTO_GENERATE_EXCHANGE = "yiye-agent-private.customer-acquisition-abnormal-auto-generate.exchange";

    //企微客服敏感信息
    public static final String WORK_WECHAT_CUSTOMER_SENSITIVE_INFO_QUEUE = "yiye-agent-private.work-wechat-customer-sensitive-info.queue";
    public static final String WORK_WECHAT_CUSTOMER_SENSITIVE_INFO_EXCHANGE = "yiye-agent-private.work-wechat-customer-sensitive-info.exchange";

    //删除企微客服敏感信息
    public static final String WORK_WECHAT_CUSTOMER_SENSITIVE_INFO_DELETE_QUEUE = "yiye-agent-private.work-wechat-customer-sensitive-info-delete.queue";
    public static final String WORK_WECHAT_CUSTOMER_SENSITIVE_INFO_DELETE_EXCHANGE = "yiye-agent-private.work-wechat-customer-sensitive-info-delete.exchange";

    //企微客服接口许可信息
    public static final String WORK_WECHAT_CUSTOMER_LICENSE_INFO_QUEUE = "yiye-agent-private.work-wechat-customer-license-info.queue";
    public static final String WORK_WECHAT_CUSTOMER_LICENSE_INFO_EXCHANGE = "yiye-agent-private.work-wechat-customer-license-info.exchange";

    //删除企微客服接口许可信息
    public static final String WORK_WECHAT_CUSTOMER_LICENSE_INFO_DELETE_QUEUE = "yiye-agent-private.work-wechat-customer-license-info-delete.queue";
    public static final String WORK_WECHAT_CUSTOMER_LICENSE_INFO_DELETE_EXCHANGE = "yiye-agent-private.work-wechat-customer-license-info-delete.exchange";

    //根据企微维度同步客服接口许可信息
    public static final String WORK_WECHAT_CUSTOMER_LICENSE_INFO_SYNC_QUEUE = "yiye-agent-private.work-wechat-customer-license-info-sync.queue";
    public static final String WORK_WECHAT_CUSTOMER_LICENSE_INFO_SYNC_EXCHANGE = "yiye-agent-private.work-wechat-customer-license-info-sync.exchange";

    //同步项目下所有企微获客助手额度信息
    public static final String CUSTOMER_ACQUISITION_QUOTA_SYNC_QUEUE = "yiye-agent-private.customer-acquisition-quota-sync.queue";
    public static final String CUSTOMER_ACQUISITION_QUOTA_SYNC_EXCHANGE = "yiye-agent-private.customer-acquisition-quota-sync.exchange";

    //异常监测-按获客助手点击添加申请好友多次但未进粉
    public static final String ABNORMAL_MONITOR_CUSTOMER_ACQUISITION_QUEUE = "yiye-agent-private.abnormal-monitor-customer-acquisition.queue";
    public static final String ABNORMAL_MONITOR_CUSTOMER_ACQUISITION_EXCHANGE = "yiye-agent-private.abnormal-monitor-customer-acquisition.exchange";

    //异常监测-按获客助手点击添加申请好友多次但未进粉-延迟队列
    public static final String ABNORMAL_MONITOR_CUSTOMER_ACQUISITION_DELAY_QUEUE = "yiye-agent-private.abnormal-monitor-customer-acquisition-delay.queue";
    public static final String ABNORMAL_MONITOR_CUSTOMER_ACQUISITION_DELAY_EXCHANGE = "yiye-agent-private.abnormal-monitor-customer-acquisition-delay.exchange";

    //异常监测-按获客链接点击多次但未进粉
    public static final String ABNORMAL_MONITOR_CUSTOMER_ACQUISITION_CLICK_QUEUE = "yiye-agent-private.abnormal-monitor-customer-acquisition-click.queue";
    public static final String ABNORMAL_MONITOR_CUSTOMER_ACQUISITION_CLICK_EXCHANGE = "yiye-agent-private.abnormal-monitor-customer-acquisition-click.exchange";

    //异常监测-按获客链接点击多次但未进粉-延迟队列
    public static final String ABNORMAL_MONITOR_CUSTOMER_ACQUISITION_CLICK_DELAY_QUEUE = "yiye-agent-private.abnormal-monitor-customer-acquisition-click-delay.queue";
    public static final String ABNORMAL_MONITOR_CUSTOMER_ACQUISITION_CLICK_DELAY_EXCHANGE = "yiye-agent-private.abnormal-monitor-customer-acquisition-click-delay.exchange";

    //抖音-启动webhook
    public static final String DOUYIN_WEBHOOK_QUEUE = "yiye-agent-private.douyin-webhook.queue";
    public static final String DOUYIN_WEBHOOK_EXCHANGE = "yiye-agent-private.douyin-webhook.exchange";

    //更新PV问答模板选项
    public static final String PAGEVIEW_QA_TEMPLATE_OPTION_QUEUE = "yiye-agent-private.pageview-qa-template-option.queue";
    public static final String PAGEVIEW_QA_TEMPLATE_OPTION_EXCHANGE = "yiye-agent-private.pageview-qa-template-option.exchange";

    //更新PV表单内容
    public static final String PAGEVIEW_FORM_DESCRIPTION_QUEUE = "yiye-agent-private.pageview-form-description.queue";
    public static final String PAGEVIEW_FORM_DESCRIPTION_EXCHANGE = "yiye-agent-private.pageview-form-description.exchange";

    //微信客服分组变动
    public static final String WECHAT_CUSTOMER_SERVICE_GROUP_CHANGE_QUEUE = "yiye-agent-private.wechat-customer-service-group-change.queue";
    public static final String WECHAT_CUSTOMER_SERVICE_GROUP_CHANGE_EXCHANGE = "yiye-agent-private.wechat-customer-service-group-change.exchange";

    /**
     * 腾讯官方下程序审核失败飞书告警通知
     */
    public static final String TENCENT_WECHAT_APPLET_AUDIT_FAIL_FEI_SHU_MESSAGE_QUEUE = "yiye-agent-private.tencent-wechat-audit-fail-applet-fei-shu-message.queue";
    public static final String TENCENT_WECHAT_APPLET_AUDIT_FAIL_FEI_SHU_MESSAGE_EXCHANGE = "yiye-agent-private.tencent-wechat-audit-fail-applet-fei-shu-message.exchange";

    /**
     * 营销通行为上报
     */
    public static final String LANDING_PAGE_ACTION_TRACE_UPLOAD_MESSAGE_QUEUE = "yiye-agent-private.landing-page-action-trace-delay-upload-message.queue";

    public static final String LANDING_PAGE_ACTION_TRACE_UPLOAD_MESSAGE_EXCHANGE = "yiye-agent-private.landing-page-action-trace-delay-upload-message.exchange";

    /**
     * 回调事件进行营销通行为上报
     */
    public static final String LANDING_PAGE_ACTION_CALLBACK_UPLOAD_MESSAGE_QUEUE = "yiye-agent-private.landing-page-action-callback-upload-message.queue";

    public static final String LANDING_PAGE_ACTION_CALLBACK_UPLOAD_MESSAGE_EXCHANGE = "yiye-agent-private.landing-page-action-callback-upload-message.exchange";

    /**
     * 落地页模板配置
     */
    public static final String LANDING_PAGE_TEMPLATE_MESSAGE_QUEUE = "yiye-agent-private.landing-page-template-message.queue";

    public static final String LANDING_PAGE_TEMPLATE_MESSAGE_EXCHANGE = "yiye-agent-private.landing-page-template-message.exchange";


    /**
     * 营销通行为上报，对落地页配置信息进行MQ缓冲的队列
     */
    public static final String LANDING_PAGE_ACTION_TEMPLATE_MESSAGE_QUEUE = "yiye-agent-private.landing-page-buffer-template-message.queue";

    public static final String LANDING_PAGE_ACTION_TEMPLATE_MESSAGE_EXCHANGE = "yiye-agent-private.landing-page-buffer-template-message.exchange";


    /**
     * 营销通行为数据落库
     */
    public static final String LANDING_PAGE_ACTION_TRACE_RECORD_MESSAGE_QUEUE = "yiye-agent-private.landing-page-action-trace-record-message.queue";

    public static final String LANDING_PAGE_ACTION_TRACE_RECORD_MESSAGE_EXCHANGE = "yiye-agent-private.landing-page-action-trace-record-message.exchange";

    /**
     * 同步落地页上报媒体类型
     */
    public static final String LANDING_PAGE_SITE_TYPE_MESSAGE_QUEUE = "yiye-agent-private.landing-page-site-type-message.queue";

    public static final String LANDING_PAGE_SITE_TYPE_MESSAGE_EXCHANGE = "yiye-agent-private.landing-page-site-type-message.exchange";



    //异步生成联系我二维码 - 用于落地页实时展示消耗
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_GENERATE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-generate-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_GENERATE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-generate-exchange";

    //异步生成联系我二维码 - 用于客服初始化
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_INIT_GENERATE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-init-generate-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_INIT_GENERATE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-init-generate-exchange";

    //异步创建联系我二维码校验清除旧数据生成新数据 - 客服初始化
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_INIT_GENERATE_AND_CLEAN_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-init-generate-clean-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_INIT_GENERATE_AND_CLEAN_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-init-generate-clean-exchange";


    //延时生成联系我二维码
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_DELAY_GENERATE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-delay-generate-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_DELAY_GENERATE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-delay-generate-exchange";

    //由于并发限制 延迟初始化部分二维码
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_DELAY_INIT_GENERATE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-delay-init-generate-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_DELAY_INIT_GENERATE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-delay-init-generate-exchange";



    //异步修改联系我二维码使用状态
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_CHANGE_QRCODE_STATUS_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-change-qrcode-status-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_CHANGE_QRCODE_STATUS_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-change-qrcode-status-exchange";


    //异步删除联系我二维码 - 取消可见范围
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_DELETE_QRCODE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-delete-qrcode-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-delete-qrcode-exchange";


    //异步单个删除联系我二维码
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_SINGLE_DELETE_QRCODE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-single-delete-qrcode-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_SINGLE_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-single-delete-qrcode-exchange";

    //异步延迟单个删除联系我二维码
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_SINGLE_DELAY_DELETE_QRCODE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-single-delay-delete-qrcode-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_SINGLE_DELAY_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-single-delay-delete-qrcode-exchange";


    //取消授权-清除联系我二维码缓存及数据库数据
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_CANCEL_AUTH_DELETE_QRCODE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-cancel-auth-delete-qrcode-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_CANCEL_AUTH_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-cancel-auth-delete-qrcode-exchange";


    //代开发授权-清除联系我二维码历史数据
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_AUTH_DELETE_HISTORY_QRCODE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-auth-delete-history-qrcode-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_AUTH_DELETE_HISTORY_QRCODE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-auth-delete-history-qrcode-exchange";

    //基于agentId清除已使用的超时的联系我二维码
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_CLEAN_AGENT_QRCODE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-clean-agent-qrcode-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_CLEAN_AGENT_QRCODE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-clean-agent-qrcode-exchange";


    /**
     * 联系我二维码生成失败飞书告警通知
     */
    public static final String WECHAT_CUSTOMER_CONTACT_CREATE_FAIL_FEI_SHU_MESSAGE_QUEUE = "yiye-agent-private.wechat-customer-contact-create-fail-fei-shu-message.queue";
    public static final String WECHAT_CUSTOMER_CONTACT_CREATE_FAIL_FEI_SHU_MESSAGE_EXCHANGE = "yiye-agent-private.wechat-customer-contact-create-fail-fei-shu-message.exchange";


    //异步同步联系我二维码生成状态至多账户私库下
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_QRCODE_GENERATE_STATUS_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-qrcode-generate-status-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_QRCODE_GENERATE_STATUS_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-qrcode-generate-status-exchange";


    //渠道二维码加粉回调异步发送删除渠道二维码 - 立即删除还是延迟删除
    public static final String DELETE_ADD_CALLBACK_CUSTOMER_CONTACT_QUEUE = "yiye-agent-private.delete-add-callback-customer-contact-queue";

    public static final String DELETE_ADD_CALLBACK_CUSTOMER_CONTACT_EXCHANGE = "yiye-agent-private.delete-add-callback-customer-contact-exchange";




    /**
     * 巨量广告监测回传(pv曝光)
     */
    public static final String LANDING_PAGE_AD_TRACE_MESSAGE_QUEUE = "yiye-agent-private.landing-page-ad-trace-queue";

    public static final String LANDING_PAGE_AD_TRACE_MESSAGE_EXCHANGE = "yiye-agent-private.landing-page-ad-trace-exchange";


    /**
     * 快手广告监测
     */
    public static final String LANDING_PAGE_KUAI_SHOU_AD_TRACE_MESSAGE_QUEUE = "yiye-agent-private.landing-page-kuai-shou-ad-trace-queue";

    public static final String LANDING_PAGE_KUAI_SHOU_AD_TRACE_MESSAGE_EXCHANGE = "yiye-agent-private.landing-page-kuai-shou-ad-trace-exchange";


    /**
     * pv信息缓存队列
     */
    public static final String LANDING_PAGE_AD_TRACE_PAGE_VIEW_MESSAGE_QUEUE = "yiye-agent-private.landing-page-ad-trace-pageview-queue";

    public static final String LANDING_PAGE_AD_TRACE_PAGE_VIEW_MESSAGE_EXCHANGE = "yiye-agent-private.landing-page-ad-trace-pageview-exchange";

    /**
     * 落地页曝光，广告监测pv队列
     */
    public static final String LANDING_PAGE_VIEW_MESSAGE_QUEUE = "yiye-agent-private.landing-page-view-queue";

    public static final String LANDING_PAGE_VIEW_MESSAGE_EXCHANGE = "yiye-agent-private.landing-page-view-exchange";


    /**
     * 回调事件进行营销通行为上报
     */
    public static final String AD_TRACE_CALLBACK_UPLOAD_MESSAGE_QUEUE = "yiye-agent-private.ad-trace-callback-upload-message.queue";

    public static final String AD_TRACE_CALLBACK_UPLOAD_MESSAGE_EXCHANGE = "yiye-agent-private.ad-trace-callback-upload-message.exchange";



    /**
     * 查询客服信息及所在分组，发送互动广告前进行数据组装
     */
    public static final String WECHAT_CUSTOMER_SERVICE_CHANGE_SEARCH_SEND_TRAFFIC_ENGINE_QUEUE = "yiye-agent-private.wechat-customer-service-change-search-send-traffic-engine.queue";
    public static final String WECHAT_CUSTOMER_SERVICE_CHANGE_SEARCH_SEND_TRAFFIC_ENGINE_EXCHANGE = "yiye-agent-private.wechat-customer-service-change-search-send-traffic-engine.exchange";

    /**
     * 互动广告客资处理队列
     */
    public static final String ADD_TRAFFIC_ENGINE_CUSTOMER_QUEUE = "yiye-agent-private.add-traffic-engine-customer.queue";
    public static final String ADD_TRAFFIC_ENGINE_CUSTOMER_EXCHANGE = "yiye-agent-private.add-traffic-engine-customer.exchange";

    /**
     * 互动广告客服分组初始化
     */
    public static final String TRAFFIC_CUSTOMER_GROUP_INIT_QUEUE = "yiye-agent-private.traffic-engine-customer-group-init.queue";
    public static final String TRAFFIC_CUSTOMER_GROUP_INIT_EXCHANGE = "yiye-agent-private.traffic-engine-customer-group-init.exchange";

    /**
     * 代开发accessToken变更发送至互动广告
     */
    public static final String TRAFFIC_CORP_ACCESSTOKEN_CHANGE_QUEUE = "yiye-agent-private.traffic-engine-corp-accesstoken-change.queue";
    public static final String TRAFFIC_CORP_ACCESSTOKEN_CHANGE_EXCHANGE = "yiye-agent-private.traffic-engine-corp-accesstoken-change.exchange";

    /**
     * 企业微信标签更改
     */
    public static final String ENTERPRISE_WECHAT_TAG_CHANGE_MESSAGE_QUEUE = "yiye-agent-private.enterprise-wechat-tag-change.queue";
    public static final String ENTERPRISE_WECHAT_TAG_CHANGE_MESSAGE_EXCHANGE = "yiye-agent-private.enterprise-wechat-tag-change.exchange";

    /**
     * 微信公众号-自动回复-向微信公众号服务器推送自动回复消息
     */
    public static final String SEND_MESSAGE_TO_WECHAT_OFFICAL_ACCOUNT_KEY = "yiye-agent-private.send-message-to-wechat-offical-account.queue";
    public static final String SEND_MESSAGE_TO_WECHAT_OFFICAL_ACCOUNT_EXCHANGE = "yiye-agent-private.send-message-to-wechat-offical-account.exchange";

    public static final String SEND_MESSAGE_TO_RECORD_WECHAT_OFFICIAL_ACCOUNT_QR_CODE_SEND_KEY = "yiye-agent-private.send-message-to-record-wechat-offical-account-qr-code-send.queue";
    public static final String SEND_MESSAGE_TO_RECORD_WECHAT_OFFICIAL_ACCOUNT_QR_CODE_SEND_EXCHANGE = "yiye-agent-private.send-message-to-record-wechat-offical-account-qr-code-send.exchange";

    /**
     * 微信公众号-自动回复-向微信公众号服务器推送自动回复消息（延时队列）
     */
    public static final String SEND_MESSAGE_TO_WECHAT_OFFICAL_ACCOUNT_DELAY_KEY = "yiye-agent-private.send-message-to-wechat-offical-account-delay.queue";
    public static final String SEND_MESSAGE_TO_WECHAT_OFFICAL_ACCOUNT_DELAY_EXCHANGE = "yiye-agent-private.send-message-to-wechat-offical-account-delay.exchange";

    /**
     * 微信机器人-推送消息（延时队列）
     */
    public static final String SEND_MESSAGE_TO_WECHAT_ROBOT_DELAY_KEY = "yiye-agent-private.send-message-to-wechat-robot-delay.queue";
    public static final String SEND_MESSAGE_TO_WECHAT_ROBOT_DELAY_EXCHANGE = "yiye-agent-private.send-message-to-wechat-robot-delay.exchange";

    /**
     * 微信机器人-推送消息（实时队列）
     */
    public static final String SEND_MESSAGE_TO_WECHAT_ROBOT_REAL_KEY = "yiye-agent-private.send-message-to-wechat-robot-real.queue";
    public static final String SEND_MESSAGE_TO_WECHAT_ROBOT_REAL_EXCHANGE = "yiye-agent-private.send-message-to-wechat-robot-real.exchange";


    public static final String SEND_ROBOT_LIVE_CODE_RECORD_KEY = "yiye-agent-private.send-robot-live-code-record.queue";
    public static final String SEND_ROBOT_LIVE_CODE_RECORD_EXCHANGE = "yiye-agent-private.send-robot-live-code-record.exchange";

    public static final String SEND_ROBOT_MULTIPLE_LIVE_CODE_RECORD_KEY = "yiye-agent-private.send-robot-multiple-live-code-record.queue";
    public static final String SEND_ROBOT_MULTIPLE_LIVE_CODE_RECORD_EXCHANGE = "yiye-agent-private.send-robot-multiple-live-code-record.exchange";

    /**
     * 微信客服机器人发送挽留消息记录
     */
    public static final String SEND_ROBOT_ENTERPRISE_WECHAT_CUSTOMER_RETAIN_RECORD_KEY = "yiye-agent-private.send-robot-enterprise-wechat-customer-retain-record.queue";

    public static final String SEND_ROBOT_ENTERPRISE_WECHAT_CUSTOMER_RETAIN_RECORD_EXCHANGE = "yiye-agent-private.send-robot-enterprise-wechat-customer-retain-record.exchange";

    /**
     * 初始化数据加tag
     */
    public static final String INIT_CUSTOMER_TAG_EXCHANGE = "yiye-agent-private.init-customer-tag.exchange";
    public static final String INIT_CUSTOMER_TAG_KEY = "yiye-agent-private.init-customer-tag.queue";

    public static final String INIT_CUSTOMER_TAG_DELAY_EXCHANGE = "yiye-agent-private.init-customer-tag-delay.exchange";
    public static final String INIT_CUSTOMER_TAG_DELAY_KEY = "yiye-agent-private.init-customer-tag-delay.queue";

    /**
     * 微信客服机器人公众号点击跳转队列
     */
    public static final String WECHAT_CUSTOMER_SERVICE_ROBOT_OFFICIAL_ACCOUNT_CLICK_JUMP_EXCHANGE = "yiye-agent-private.wechat-customer-service-robot-official-account-click-jump.exchange";
    public static final String WECHAT_CUSTOMER_SERVICE_ROBOT_OFFICIAL_ACCOUNT_CLICK_JUMP_QUEUE = "yiye-agent-private.wechat-customer-service-robot-official-account-click-jump.queue";

    //异步创建公众号内联系我二维码校验清除旧数据生成新数据 - 客服初始化
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_INIT_GENERATE_AND_CLEAN_QUEUE = "yiye-agent-private.official-wechat-customer-contact-init-generate-clean-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_INIT_GENERATE_AND_CLEAN_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-init-generate-clean-exchange";

    //异步生成公众号内联系我二维码 - 用于客服初始化
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_INIT_GENERATE_QUEUE = "yiye-agent-private.official-wechat-customer-contact-init-generate-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_INIT_GENERATE_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-init-generate-exchange";

    //异步同步公众号联系我二维码生成状态至多账户私库下
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_QRCODE_GENERATE_STATUS_QUEUE = "yiye-agent-private.official-wechat-customer-contact-qrcode-generate-status-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_QRCODE_GENERATE_STATUS_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-qrcode-generate-status-exchange";


    //异步单个删除公众号联系我二维码
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_SINGLE_DELETE_QRCODE_QUEUE = "yiye-agent-private.official-wechat-customer-contact-single-delete-qrcode-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_SINGLE_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-single-delete-qrcode-exchange";

    //由于并发限制 延迟初始化部分公众号渠道二维码
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_DELAY_INIT_GENERATE_QUEUE = "yiye-agent-private.official-wechat-customer-contact-delay-init-generate-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_DELAY_INIT_GENERATE_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-delay-init-generate-exchange";

    //异步删除公众号联系我二维码 - 取消可见范围
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_DELETE_QRCODE_QUEUE = "yiye-agent-private.official-wechat-customer-contact-delete-qrcode-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-delete-qrcode-exchange";


    //取消授权-公众号清除联系我二维码缓存及数据库数据
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_CANCEL_AUTH_DELETE_QRCODE_QUEUE = "yiye-agent-private.official-wechat-customer-contact-cancel-auth-delete-qrcode-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_CANCEL_AUTH_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-cancel-auth-delete-qrcode-exchange";


    //基于agentId清除已使用的超时的公众号联系我二维码
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_CLEAN_AGENT_QRCODE_QUEUE = "yiye-agent-private.official-wechat-customer-contact-clean-agent-qrcode-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_CLEAN_AGENT_QRCODE_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-clean-agent-qrcode-exchange";

    //异步修改公众号联系我二维码使用状态
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_CHANGE_QRCODE_STATUS_QUEUE = "yiye-agent-private.official-wechat-customer-contact-change-qrcode-status-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_CHANGE_QRCODE_STATUS_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-change-qrcode-status-exchange";

    //异步修改公众号客服分组多人活码使用状态
    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_CHANGE_QRCODE_STATUS_QUEUE = "yiye-agent-private.official-wechat-customer-group-contact-change-qrcode-status-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_CHANGE_QRCODE_STATUS_EXCHANGE = "yiye-agent-private.official-wechat-customer-group-contact-change-qrcode-status-exchange";

    //异步生成公众号联系我二维码 - 用于落地页实时展示消耗
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_GENERATE_QUEUE = "yiye-agent-private.official-wechat-customer-contact-generate-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_GENERATE_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-generate-exchange";

    //实时消耗二维码 - 延时生成公众号联系我二维码
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_DELAY_GENERATE_QUEUE = "yiye-agent-private.official-wechat-customer-contact-delay-generate-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_DELAY_GENERATE_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-delay-generate-exchange";


    //异步修改公众号联系我二维码使用状态
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_CHANGE_QRCODE_MATERIALID_QUEUE = "yiye-agent-private.official-wechat-customer-contact-change-qrcode-materialid-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_CHANGE_QRCODE_MATERIALID_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-change-qrcode-materialid-exchange";

    //异步修改公众号联系我二维码使用状态
    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_CHANGE_QRCODE_MATERIALID_QUEUE = "yiye-agent-private.official-wechat-customer-group-contact-change-qrcode-materialid-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_CHANGE_QRCODE_MATERIALID_EXCHANGE = "yiye-agent-private.official-wechat-customer-group-contact-change-qrcode-materialid-exchange";


    //获取公众号渠道二维码后修改pageView自归因参数
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_CHANGE_PAGEVIEW_STATE_QUEUE = "yiye-agent-private.official-wechat-customer-contact-change-pageview-state-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_CHANGE_PAGEVIEW_STATE_EXCHANGE = "yiye-agent-private.official-wechat-customer-contact-change-pageview-state-exchange";
    //根据pid修改自归因参数 - 用于无法监听页面长按事件对二维码展示以及加粉成功后 补偿pv自归因与客服数据
    public static final String CUSTOMER_CONTACT_CHANGE_PAGEVIEW_STATE_QUEUE = "yiye-agent-private.customer-contact-change-pageview-state-queue";

    public static final String CUSTOMER_CONTACT_CHANGE_PAGEVIEW_STATE_EXCHANGE = "yiye-agent-private.customer-contact-change-pageview-state-exchange";
    //根据pid修改长按数据 - 用于无法监听页面长按事件对二维码展示以及加粉成功后 补偿pv长按统计
    public static final String PRIVATE_PAGEVIEW_IDENTIFY_REPORT_EXCHANGE = "yiye-agent-private.collect-pageview-identify-report.exchange";
    public static final String PRIVATE_PAGEVIEW_IDENTIFY_REPORT_QUEUE = "yiye-agent-private.collect-pageview-identify-report.queue";

    //发送点击whatsapp链接跳转成功pv状态更新队列
    public static final String PRIVATE_PAGEVIEW_SEND_UPDATE_JUMP_WHATSAPP_SUCCESS_EXCHANGE = "yiye-agent-private.collect-pageview-send-jump-whatsapp-success.exchange";
    public static final String PRIVATE_PAGEVIEW_SEND_UPDATE_JUMP_WHATSAPP_SUCCESS_QUEUE = "yiye-agent-private.collect-pageview-send-update-jump-whatsapp-success.queue";

    //发送pv修改whatsapp建联状态队列
    public static final String PRIVATE_PAGEVIEW_SEND_UPDATE_ADD_WHATSAPP_FRIEND_SUCCESS_EXCHANGE = "yiye-agent-private.collect-pageview-send-update-add-whatsapp-friend-success.exchange";
    public static final String PRIVATE_PAGEVIEW_SEND_UPDATE_ADD_WHATSAPP_FRIEND_SUCCESS_QUEUE = "yiye-agent-private.collect-pageview-send-update-add-whatsapp-friend-success.queue";

    //发送pv修改whatsapp二次开口状态队列
    public static final String PRIVATE_PAGEVIEW_SEND_UPDATE_WHATSAPP_USER_OPEN_MOUTH_SUCCESS_EXCHANGE = "yiye-agent-private.collect-pageview-send-update-whatsapp-user-open-mouth-success.exchange";
    public static final String PRIVATE_PAGEVIEW_SEND_UPDATE_WHATSAPP_USER_OPEN_MOUTH_SUCCESS_QUEUE = "yiye-agent-private.collect-pageview-send-update-whatsapp-user-open-mouth-success.queue";

    //变更项目客服分组多人活码
    public static final String UPDATE_OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_QUEUE = "yiye-agent-private.official-wechat-customer-group-contact-update-queue";

    public static final String UPDATE_OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_EXCHANGE = "yiye-agent-private.official-wechat-customer-group-contact-update-exchange";

    //变更项目客服分组多人活码失败重试队列
    public static final String UPDATE_OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_RETRY_QUEUE = "yiye-agent-private.official-wechat-customer-group-update-retry-queue";

    public static final String UPDATE_OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_RETRY_EXCHANGE = "yiye-agent-private.official-wechat-customer-group-update-retry-exchange";

    //初始化客服公众号内多人活码队列
    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_INIT_QUEUE = "yiye-agent-private.official-wechat-customer-group-contact-init-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_INIT_EXCHANGE = "yiye-agent-private.official-wechat-customer-group-contact-init-exchange";

    //异步生成公众号内多人联系我二维码 - 用于客服初始化
    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_INIT_GENERATE_QUEUE = "yiye-agent-private.official-wechat-customer-group-contact-init-generate-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_INIT_GENERATE_EXCHANGE = "yiye-agent-private.official-wechat-customer-group-contact-init-generate-exchange";

    //基于agentId清除已使用的超时的公众号多人联系我二维码
    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_CLEAN_AGENT_QRCODE_QUEUE = "yiye-agent-private.official-wechat-customer-group-contact-clean-agent-qrcode-queue";

    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_CLEAN_AGENT_QRCODE_EXCHANGE = "yiye-agent-private.official-wechat-customer-group-contact-clean-agent-qrcode-exchange";

    //异步单个删除公众号多人联系我二维码（同步）
    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_SINGLE_DELETE_QRCODE_QUEUE = "yiye-agent-private.official-wechat-customer-group-contact-single-delete-qrcode-queue";
    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_SINGLE_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.official-wechat-customer-group-contact-single-delete-qrcode-exchange";
    //异步单个删除公众号多人联系我二维码（延时）
    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_SINGLE_DELETE_QRCODE_DELAYED_QUEUE = "yiye-agent-private.official-wechat-customer-group-contact-single-delete-qrcode-delayed-queue";
    public static final String OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_SINGLE_DELETE_QRCODE_DELAYED_EXCHANGE = "yiye-agent-private.official-wechat-customer-group-contact-single-delete-qrcode-delayed-exchange";


    //实时生成h5联系我二维码 - 用于落地页实时展示消耗
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_REAL_GENERATE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-contact-real-generate-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_CONTACT_REAL_GENERATE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-contact-real-generate-exchange";


    //实时生成公众号单人活码 - 用于落地页实时展示消耗
    public static final String LANDING_PAGE_OFFICIAL_WECHAT_CUSTOMER_CONTACT_REAL_GENERATE_QUEUE = "yiye-agent-private.landing-page-official-wechat-customer-contact-real-generate-queue";

    public static final String LANDING_PAGE_OFFICIAL_WECHAT_CUSTOMER_CONTACT_REAL_GENERATE_EXCHANGE = "yiye-agent-private.landing-page-official-wechat-customer-contact-real-generate-exchange";

    //实时生成公众号多人活码 - 用于落地页实时展示消耗
    public static final String LANDING_PAGE_OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_REAL_GENERATE_QUEUE = "yiye-agent-private.landing-page-official-wechat-customer-group-contact-real-generate-queue";

    public static final String LANDING_PAGE_OFFICIAL_WECHAT_CUSTOMER_GROUP_CONTACT_REAL_GENERATE_EXCHANGE = "yiye-agent-private.landing-page-official-wechat-customer-group-contact-real-generate-exchange";

    //异步删除机器人活码
    public static final String ROBOT_CUSTOMER_CONTACT_DELETE_QRCODE_QUEUE = "yiye-agent-private.robot-customer-contact-delete-qrcode-queue";
    public static final String ROBOT_CUSTOMER_CONTACT_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.robot-customer-contact-delete-qrcode-exchange";


    //异步删除机器人活码（动态渠道二维码）
    public static final String ROBOT_DYNAMIC_CUSTOMER_CONTACT_DELETE_QRCODE_QUEUE = "yiye-agent-private.robot-dynamic-customer-contact-delete-qrcode-queue";
    public static final String ROBOT_DYNAMIC_CUSTOMER_CONTACT_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.robot-dynamic-customer-contact-delete-qrcode-exchange";


    //异步批量删除机器人活码
    public static final String ROBOT_CUSTOMER_CONTACT_BATCH_DELETE_QRCODE_QUEUE = "yiye-agent-private.robot-customer-contact-batch-delete-qrcode-queue";
    public static final String ROBOT_CUSTOMER_CONTACT_BATCH_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.robot-customer-contact-batch-delete-qrcode-exchange";

    //job任务：公众号 token刷新
    public static final String SEND_JOB_REFRESH_WECHAT_OFFICIAL_ACCOUNT_COMPONENT_ACCESS_TOKEN_KEY = "yiye-agent-private.send-job-refresh-wechat-official-account-component-access-token.queue";
    public static final String SEND_JOB_REFRESH_WECHAT_OFFICIAL_ACCOUNT_COMPONENT_ACCESS_TOKEN_EXCHANGE = "yiye-agent-private.send-job-refresh-wechat-official-account-component-access-token.exchange";

    //job任务：小程序 token刷新
    public static final String SEND_JOB_REFRESH_WECHAT_APPLET_COMPONENT_ACCESS_TOKEN_KEY = "yiye-agent-private.send-job-refresh-wechat-applet-component-access-token.queue";
    public static final String SEND_JOB_REFRESH_WECHAT_APPLET_COMPONENT_ACCESS_TOKEN_EXCHANGE = "yiye-agent-private.send-job-refresh-wechat-applet-component-access-token.exchange";

    //job任务：小程序是否被封
    public static final String SEND_JOB_SYNC_WECHAT_APPLET_CONFIG_EXECUTOR_KEY = "yiye-agent-private.send-job-sync-wechat-applet-config-executor.queue";
    public static final String SEND_JOB_SYNC_WECHAT_APPLET_CONFIG_EXECUTOR_EXCHANGE = "yiye-agent-private.send-job-sync-wechat-applet-config-executor.exchange";

    //job任务：小程序审核状态监测
    public static final String SEND_JOB_WECHAT_APPLET_CONFIG_VERSION_SUBMIT_AUDIT_EXECUTOR_KEY = "yiye-agent-private.send-job-wechat-applet-config-version-submit-audit-executor.queue";
    public static final String SEND_JOB_WECHAT_APPLET_CONFIG_VERSION_SUBMIT_AUDIT_EXECUTOR_EXCHANGE = "yiye-agent-private.send-job-wechat-applet-config-version-submit-audit-executor.exchange";

    //job任务：AIP 原生页状态同步
    public static final String SEND_JOB_UPDATA_ILYNX_STATUS_JOB_EXECUTE_KEY = "yiye-agent-private.send-job-updata-ilynx-status-job-execute.queue";
    public static final String SEND_JOB_UPDATA_ILYNX_STATUS_JOB_EXECUTE_EXCHANGE = "yiye-agent-private.send-job-updata-ilynx-status-job-execute.exchange";

    //    //删除n天以前webhook发送记录
    //    public static final String SEND_DELETE_MESSAGE_WEBHOOK_SEND_RECORD_DATA_QUEUE = "yiye-agent-private.send-delete-message-webhook-send-record-data.queue";
    //    public static final String SEND_DELETE_MESSAGE_WEBHOOK_SEND_RECORD_DATA_EXCHANGE = "yiye-agent-private.send-delete-message-webhook-send-record-data.exchange";

    //客服分组客服变更事件
    public static final String LANDING_PAGE_CUSTOMER_SERVICE_GROUP_CHANGE_QUEUE = "yiye-agent-private.landing-page-customer-service-group-change.queue";
    public static final String LANDING_PAGE_CUSTOMER_SERVICE_GROUP_CHANGE_EXCHANGE = "yiye-agent-private.landing-page-customer-service-group-change.exchange";

    //发送回调消息
    public static final String AGENT_CALLBACK_INFO_SEND_QUEUE = "yiye-agent-private.agent-callback-info-send.queue";
    public static final String AGENT_CALLBACK_INFO_SEND_EXCHANGE = "yiye-agent-private.agent-callback-info-send.exchange";


    /**
     * webhook推送客资相关key
     */
    //【添加企业微信成功】创建【客资】
    public static final String SEND_WEBHOOK_ADD_WORK_WECHAT_SUCCESS_ADD_SUBMIT_EXCHANGE = "yiye-agent-private.send-webhook-add-work-wechat-success-add-submit.exchange";
    public static final String SEND_WEBHOOK_ADD_WORK_WECHAT_SUCCESS_ADD_SUBMIT_QUEUE = "yiye-agent-private.send-webhook-add-work-wechat-success-add-submit.queue";
    //填单支付状态变更推送webhook（修复1.232.0绑定队列错误）
    public static final String SEND_PAYMENT_STATUS_TO_WEB_HOOK_BACK_EXCHANGE = "yiye-agent-private.send-payment-status-to-web-hook-back.exchange";
    public static final String SEND_PAYMENT_STATUS_TO_WEB_HOOK_BINDING_BACK_QUEUE = "yiye-agent-private.send-payment-status-to-web-hook-binding-back.queue";
    public static final String SEND_PAYMENT_STATUS_TO_UPDATE_PAGE_VIEW_PAYMENT_STATUS_BACK_QUEUE = "yiye-agent-private.send-payment-status-to-update-page-view-payment-status-back.queue";
    public static final String SEND_PAYMENT_STATUS_TO_UPDATE_SUBMIT_PAYMENT_STATUS_BACK_QUEUE = "yiye-agent-private.send-payment-status-to-update-submit-payment-status-back.queue";
    public static final String SEND_PAYMENT_STATUS_TO_WEB_HOOK_BACK_QUEUE = "yiye-agent-private.send-payment-status-to-web-hook-back.queue";
    //填单订单状态变更推送webhook
    public static final String SEND_CUSTOMER_ORDER_CHANGE_TO_WEBHOOK_QUEUE = "yiye-agent-private.send-customer-order-change-to-webhook.queue";
    public static final String SEND_CUSTOMER_ORDER_CHANGE_TO_WEBHOOK_EXCHANGE = "yiye-agent-private.send-customer-order-change-to-webhook.exchange";
    //新增填单、修改填单、初始化填单、填单上报、订单上报、更新pv填单信息、推送webhook填单信息
    public static final String SEND_SUBMIT_FINISH_BACK_EXCHANGE = "yiye-agent-private.collect.submit.finish-back.exchange";
    public static final String SEND_SUBMIT_FINISH_BACK_BINDING_QUEUE = "yiye-agent-private.collect.submit.finish-back.binding.queue";
    public static final String SEND_SUBMIT_FINISH_TO_UPDATE_PAGEVIEW_QUEUE = "yiye-agent-private.collect.submit.finish-to-update-pageview.queue";
    public static final String SEND_SUBMIT_FINISH_TO_GENERATE_CUSTOMER_QUEUE = "yiye-agent-private.collect.submit.finish-to-generate-customer.queue";
    public static final String SEND_SUBMIT_FINISH_TO_UPLOAD_SUBMIT_QUEUE = "yiye-agent-private.collect.submit.finish-to-upload-submit.queue";
    public static final String SEND_SUBMIT_FINISH_TO_WEBHOOK_QUEUE = "yiye-agent-private.collect.submit.finish-to-webhook.queue";
    //客资上报媒体推送webhook上报记录（2、customer服务解析完毕发送webhook进行推送）
    public static final String SEND_CUSTOMER_UPLOAD_RECORD_TO_WEBHOOK_QUEUE = "yiye-agent-private.send-customer-upload-record-to-webhook.queue";
    public static final String SEND_CUSTOMER_UPLOAD_RECORD_TO_WEBHOOK_EXCHANGE = "yiye-agent-private.send-customer-upload-record-to-webhook.exchange";


    //发送企业推审核失败回调消息
    public static final String AGENT_QIYETUI_AUDIT_FAIL_CALLBACK_INFO_SEND_QUEUE = "yiye-agent-private.agent-qiyetui-audit-fail-callback-info-send.queue";
    public static final String AGENT_QIYETUI_AUDIT_FAIL_CALLBACK_INFO_SEND_EXCHANGE = "yiye-agent-private.agent-qiyetui-audit-fail-callback-info-send.exchange";

    //发送渠道审核失败回调消息
    public static final String AGENT_AUDIT_FAIL_CALLBACK_INFO_SEND_QUEUE = "yiye-agent-private.agent-audit-fail-callback-info-send.queue";
    public static final String AGENT_AUDIT_FAIL_CALLBACK_INFO_SEND_EXCHANGE = "yiye-agent-private.agent-audit-fail-callback-info-send.exchange";


    //记录api操作客服记录
    public static final String AGENT_CHANGE_CUSTOMER_SERVICE_ONLINE_LOG_QUEUE = "yiye-agent-private.agent-change-customer-service-online-log.queue";
    public static final String AGENT_CHANGE_CUSTOMER_SERVICE_ONLINE_LOG_EXCHANGE = "yiye-agent-private.agent-change-customer-service-online-log.exchange";

    /**
     * 微信客服自定义排序变动监听队列
     */
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_SORT_NUM_QUEUE = "yiye-agent-private.landing-page-wechat-customer-service-sort-num-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_SORT_NUM_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-service-sort-num-exchange";

    /**
     *
     *分组变动
     */
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_GROUP_CHANGE_SORT_NUM_QUEUE = "yiye-agent-private.landing-page-wechat-customer-service-group-change-sort-num-queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_GROUP_CHANGE_SORT_NUM_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-service-group-change-sort-num-exchange";

    /**
     * 企微明文代开发消息处理器异步处理
     */

    //授权相关
    public static final String ENTERPRISE_WECHAT_AUTH_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-auth-callback-message-handler-queue";
    //添加企业客户
    public static final String ENTERPRISE_WECHAT_ADD_CUSTOMER_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-add-customer-callback-message-handler-queue";
    //客户删除
    public static final String ENTERPRISE_WECHAT_DELETE_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-delete-callback-message-handler-queue";
    //标签
    public static final String ENTERPRISE_WECHAT_TAG_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-tag-callback-message-handler-queue";
    //微信客服事件管理
    public static final String ENTERPRISE_WECHAT_EDIT_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-edit-callback-message-handler-queue";
    //微信客服事件管理
    public static final String ENTERPRISE_WECHAT_KF_MSG_OR_EVENT_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-kf-msg-or-event-callback-message-handler-queue";
    //获客助手
    public static final String ENTERPRISE_WECHAT_ACQUISITION_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-acquisition-callback-message-handler-queue";
    //微信客服授权变更
    public static final String ENTERPRISE_WECHAT_CUSTOMER_SERVICE_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-customer-service-callback-message-handler-queue";
    //更新成员事件
    public static final String ENTERPRISE_WECHAT_UPDATE_USER_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-update-user-callback-message-handler-queue";
    //客户群变更事件
    public static final String ENTERPRISE_WECHAT_CHANGE_EXTERNAL_CHAT_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-change-external-chat-callback-message-handler-queue";
    //服务商-收银台-下单成功通知、取消订单、改单通知、支付成功通知、退款通知、取消订单通知
    public static final String ENTERPRISE_WECHAT_CHANGE_API_LICENSE_CANCEL_ORDER_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-change-api-license-cancel-order-handler-queue";

    public static final String ENTERPRISE_WECHAT_CALLBACK_MESSAGE_HANDLER_EXCHANGE = "yiye-agent-private.enterprise-wechat-callback-message-deal-handler-exchange";

    //企业微信变更为 不可用 自动清理相关数据
    public static final String ENTERPRISE_WECHAT_CLEAN_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-clean-message-handler-queue";
    //企业微信变更为 不可用 自动清理相关数据
    public static final String ENTERPRISE_WECHAT_CLEAN_HANDLER_EXCHANGE = "yiye-agent-private.enterprise-wechat-clean-message-handler-exchange";


    //异步查询更新企业微信用户状态
    public static final String ENTERPRISE_WECHAT_USER_STATUS_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-user-status-handler-queue";
    public static final String ENTERPRISE_WECHAT_USER_STATUS_HANDLER_EXCHANGE = "yiye-agent-private.enterprise-wechat-user-status-handler-exchange";

    /**
     * 企微密文待开发消息处理器异步处理
     */
    //授权相关
    public static final String ENTERPRISE_WECHAT_UPGRADE_AUTH_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-auth-callback-message-handler-queue";
    //添加企业客户
    public static final String ENTERPRISE_WECHAT_UPGRADE_ADD_CUSTOMER_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-add-customer-callback-message-handler-queue";
    //客户删除
    public static final String ENTERPRISE_WECHAT_UPGRADE_DELETE_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-delete-callback-message-handler-queue";
    //标签
    public static final String ENTERPRISE_WECHAT_UPGRADE_TAG_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-tag-callback-message-handler-queue";
    //编辑企业客户事件
    public static final String ENTERPRISE_WECHAT_UPGRADE_EDIT_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-edit-callback-message-handler-queue";
    //微信客服事件管理
    public static final String ENTERPRISE_WECHAT_UPGRADE_KF_MSG_OR_EVENT_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-kf-msg-or-event-callback-message-handler-queue";
    //获客助手
    public static final String ENTERPRISE_WECHAT_UPGRADE_ACQUISITION_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-acquisition-callback-message-handler-queue";
    //微信客服授权变更
    public static final String ENTERPRISE_WECHAT_UPGRADE_CUSTOMER_SERVICE_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-customer-service-callback-message-handler-queue";
    //更新成员事件
    public static final String ENTERPRISE_WECHAT_UPGRADE_UPDATE_USER_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-update-user-callback-message-handler-queue";
    //客户群变更事件
    public static final String ENTERPRISE_WECHAT_UPGRADE_CHANGE_EXTERNAL_CHAT_CALLBACK_MESSAGE_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-change-external-chat-callback-message-handler-queue";
    //服务商-收银台-下单成功通知、取消订单、改单通知、支付成功通知、退款通知、取消订单通知
    public static final String ENTERPRISE_WECHAT_UPGRADE_CHANGE_API_LICENSE_CANCEL_ORDER_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-change-api-license-cancel-order-handler-queue";
    //服务商-接口调用许可证购买支付成功通知
    public static final String ENTERPRISE_WECHAT_UPGRADE_CHANGE_API_LICENSE_PAY_SUCCESS_HANDLER_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-change-api-license-pay-success-handler-queue";

    public static final String ENTERPRISE_WECHAT_UPGRADE_CALLBACK_MESSAGE_HANDLER_EXCHANGE = "yiye-agent-private.enterprise-wechat-upgrade-callback-message-deal-handler-exchange";

    //编辑企业客户事件-正常队列
    public static final String ENTERPRISE_WECHAT_UPGRADE_EDIT_CALLBACK_PROCESS_EXCHANGE = "yiye-agent-private.enterprise-wechat-upgrade-edit-callback-process.exchange";
    public static final String ENTERPRISE_WECHAT_UPGRADE_EDIT_CALLBACK_PROCESS_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-edit-callback-process.queue";

    //编辑企业客户事件-备用队列
    public static final String ENTERPRISE_WECHAT_UPGRADE_EDIT_CALLBACK_PROCESS_STANDBY_EXCHANGE = "yiye-agent-private.enterprise-wechat-upgrade-edit-callback-process-standby.exchange";
    public static final String ENTERPRISE_WECHAT_UPGRADE_EDIT_CALLBACK_PROCESS_STANDBY_QUEUE = "yiye-agent-private.enterprise-wechat-upgrade-edit-callback-process-standby.queue";

    /**
     * 企业微信标签清除
     */
    public static final String ENTERPRISE_WECHAT_TAG_CLEAR_MESSAGE_QUEUE = "yiye-agent-private.enterprise-wechat-tag-clear.queue";
    public static final String ENTERPRISE_WECHAT_TAG_CLEAR_MESSAGE_EXCHANGE = "yiye-agent-private.enterprise-wechat-tag-clear.exchange";

    /**
     * 企微代开发回调后 检测客资落地页，为客户打标签 - 企业微信客户标签
     */
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_LANDINGPAGE_EXCHANGE = "yiye-agent-private.make-enterprise-wechat-tag-landingpage.exchange";
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_LANDINGPAGE_KEY = "yiye-agent-private.make-enterprise-wechat-tag-landingpage.queue";

    /**
     * 企微代开发回调后 检测是否为非广告来源，为客户打标签 - 企业微信客户标签
     */
    public static final String MAKE_UN_AD_SOURCE_ENTERPRISE_WECHAT_TAG_EXCHANGE = "yiye-agent-private.make-un-ad-source-enterprise-wechat-tag.exchange";

    public static final String MAKE_UN_AD_SOURCE_ENTERPRISE_WECHAT_TAG_KEY = "yiye-agent-private.make-un-ad-source-enterprise-wechat-tag.queue";

    /**
     * 企微代开发回调后 检测是否为非广告来源，为客户打标签 - 企业微信客户标签
     */
    public static final String MAKE_UN_AD_SOURCE_ENTERPRISE_WECHAT_TAG_DELAY_EXCHANGE = "yiye-agent-private.make-un-ad-source-enterprise-wechat-tag-delay.exchange";

    public static final String MAKE_UN_AD_SOURCE_ENTERPRISE_WECHAT_TAG_DELAY_KEY = "yiye-agent-private.make-un-ad-source-enterprise-wechat-tag-delay.queue";

    /**
     * 企微代开发回调后 检测客资落地页，为客户打标签 - 企业微信客户标签 - 延迟打标签
     */
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_LANDINGPAGE_DELAY_EXCHANGE = "yiye-agent-private.make-enterprise-wechat-tag-landingpage-delay.exchange";
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_LANDINGPAGE_DELAY_KEY = "yiye-agent-private.make-enterprise-wechat-tag-landingpage-delay.queue";


    /**
     * 企微代开发回调后 检测客资公众号助手回复消息规则，为客户打标签 - 企业微信客户标签
     */
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_OFFICIAL_REPLY_EXCHANGE = "yiye-agent-private.make-enterprise-wechat-tag-official-reply.exchange";
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_OFFICIAL_REPLY_KEY = "yiye-agent-private.make-enterprise-wechat-tag-official-reply.queue";

    /**
     * 企微代开发回调后 检测客资公众号助手回复消息规则，为客户打标签 - 企业微信客户标签 - 延迟打标签
     */
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_OFFICIAL_REPLY_DELAY_EXCHANGE = "yiye-agent-private.make-enterprise-wechat-tag-official-reply-delay.exchange";
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_OFFICIAL_REPLY_DELAY_KEY = "yiye-agent-private.make-enterprise-wechat-tag-official-reply-delay.queue";

    /**
     *企业微信项目解绑或取消授权 清除当前项目下对应企微的标签选择
     */
    public static final String ENTERPRISE_WECHAT_UNBIND_TAG_CLEAN_EXCHANGE = "yiye-agent-private.enterprise-wechat-unbind-tag-clean.exchange";
    public static final String ENTERPRISE_WECHAT_UNBIND_TAG_CLEAN_KEY = "yiye-agent-private.enterprise-wechat-unbind-tag-clean.queue";

    /**
     *发送上报配置变更操作记录
     */
    public static final String SEND_UPLOAD_CONFIG_CHANGE_EXCHANGE = "yiye-agent-private.send-upload-config-change.exchange";
    public static final String SEND_UPLOAD_CONFIG_CHANGE_KEY = "yiye-agent-private.send-upload-config-change.queue";


    /**
     * 新版本查询清洗pv历史数据异常重新清洗的队列
     */
    public static final String PAGE_VIEW_EVENT_INFO_HISTORY_DATA_STATISTICS_MESSAGE_QUEUE  = "page-view-event-info-history-data-statistics-message-exchange.queue";

    public static final String PAGE_VIEW_EVENT_INFO_HISTORY_DATA_STATISTICS_MESSAGE_EXCHANGE  = "page-view-event-info-history-data-statistics-message-exchange.exchange";

    /**
     * 新版本查询清洗微信客服列表历史数据异常重新清洗的队列
     */
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_HISTORY_DATA_STATISTICS_MESSAGE_QUEUE  = "landing-page-wechat-customer-service-history-data-statistics-message-exchange.queue";

    public static final String LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_HISTORY_DATA_STATISTICS_MESSAGE_EXCHANGE  = "landing-page-wechat-customer-service-history-data-statistics-message-exchange.exchange";

    /**
     * 异步合成微信客服联系我二维码背景图队列
     */
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_COMPOSITE_CONTACT_ME_QR_CODE_BACKGROUND_IMAGE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-service-composite-contact-me-qr-code-background-image-queue";
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_COMPOSITE_CONTACT_ME_QR_CODE_BACKGROUND_IMAGE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-service-composite-contact-me-qr-code-background-image-exchange";

    /**
     * 异步保存微信客服联系我二维码背景图队列
     */
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_SAVE_CONTACT_ME_QR_CODE_BACKGROUND_IMAGE_QUEUE = "yiye-agent-private.landing-page-wechat-customer-service-save-contact-me-qr-code-background-image-queue";
    public static final String LANDING_PAGE_WECHAT_CUSTOMER_SERVICE_SAVE_CONTACT_ME_QR_CODE_BACKGROUND_IMAGE_EXCHANGE = "yiye-agent-private.landing-page-wechat-customer-service-save-contact-me-qr-code-background-image-exchange";

    /**
     * 企微代开发回调后 检测对应机器人是否配置自动打标签，为客户打标签 - 企业微信客户标签
     */
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_ROBOT_CONTACT_EXCHANGE = "yiye-agent-private.make-enterprise-wechat-tag-robot-contact.exchange";
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_ROBOT_CONTACT_KEY = "yiye-agent-private.make-enterprise-wechat-tag-robot-contact.queue";

    /**
     * 企微代开发回调后 检测对应机器人是否配置自动打标签，为客户打标签 - 企业微信客户标签 - 延迟打标签
     */
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_ROBOT_CONTACT_DELAY_EXCHANGE = "yiye-agent-private.make-enterprise-wechat-tag-robot-contact-delay.exchange";
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_ROBOT_CONTACT_DELAY_KEY = "yiye-agent-private.make-enterprise-wechat-tag-robot-contact-delay.queue";

    /**
     * 企微代开发回调后 检测问答模板是否配置自动打标签，为客户打标签 - 企业微信客户标签
     */
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_WIDGET_TEMPLATE_EXCHANGE = "yiye-agent-private.make-enterprise-wechat-tag-widget-template.exchange";
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_WIDGET_TEMPLATE_KEY = "yiye-agent-private.make-enterprise-wechat-tag-widget-template.queue";


    /**
     * 企微代开发回调后 检测问答模板是否配置自动打标签，为客户打标签 - 企业微信客户标签 - 延迟打标签
     */
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_WIDGET_TEMPLATE_DELAY_EXCHANGE = "yiye-agent-private.make-enterprise-wechat-tag-widget-template-delay.exchange";
    public static final String MAKE_ENTERPRISE_WECHAT_TAG_BY_WIDGET_TEMPLATE_DELAY_KEY = "yiye-agent-private.make-enterprise-wechat-tag-widget-template-delay.queue";

    /**
     * 广告数据补偿
     */
    public static final String AD_DATA_REPORT_COMPENSATION_EXCHANGE = "yiye-agent-private.marketing-ad-data-report-compensation.exchange";

    /**
     * 更新广告基础数据
     */
    public static final String MARKETING_BASE_DATA_EXCHANGE = "yiye-agent-private.marketing-base-data.exchange";

    /**
     * 补偿广告报表数据
     */
    public static final String MARKETING_AD_REPORT_COMPENSATE_EXCHANGE = "yiye-agent-private.marketing-ad-report-compensate.exchange";

    /**
     * 修正广告报表数据
     */
    public static final String MARKETING_AD_REPORT_EXCHANGE = "yiye-agent-private.marketing-ad-report.exchange";

    /**
     * 拉取账户报表数据
     */
    public static final String MARKETING_ACCOUNT_REPORT_EXCHANGE = "yiye-agent-private.marketing-account-report.exchange";

    /**
     * 修正账户报表数据
     */
    public static final String MARKETING_ACCOUNT_REPORT_CORRECT_EXCHANGE = "yiye-agent-private.marketing-account-report-correct.exchange";

    /**
     * 补偿账户报表数据
     */
    public static final String MARKETING_ACCOUNT_REPORT_COMPENSATE_EXCHANGE = "yiye-agent-private.marketing-account-report-compensate.exchange";

    /**
     * 账户报表数据推送
     */
    public static final String MARKETING_ACCOUNT_REPORT_DATA_PUSH_EXCHANGE = "yiye-agent-private.marketing-account-report-data-push.exchange";

    /**
     * 投放广告数据统计报表统计队列
     */
    public static final String MARKETING_ADVERTISE_STATISTIC_REPORT_QUEUE = "yiye-agent-private.marketing-advertise-statistic-report-queue";
    public static final String MARKETING_ADVERTISE_STATISTIC_REPORT_EXCHANGE = "yiye-agent-private.marketing-advertise-statistic-report-exchange";

    /**
     * 投放广告数据统计报表统计队列（新）
     */
    public static final String MARKETING_ADVERTISE_STATISTIC_REPORT_QUEUE_NEW = "yiye-agent-private.marketing-advertise-statistic-report-queue-new";
    public static final String MARKETING_ADVERTISE_STATISTIC_REPORT_EXCHANGE_NEW = "yiye-agent-private.marketing-advertise-statistic-report-exchange-new";

    /**
     * 延迟处理代开发回调加粉
     */
    public static final String WORK_WECHAT_ADD_DELAY_HANDLE_QUEUE = "yiye-agent-private.work-wechat-add-delay-handle-queue";
    public static final String WORK_WECHAT_ADD_DELAY_HANDLE_EXCHANGE = "yiye-agent-private.work-wechat-add-delay-handle-exchange";

    /**
     * 发送修改渠道二维码使用的落地页ID和uid
     */
    public static final String UPDATE_CUSTOMER_CONTACT_LANDING_PAGE_QUEUE = "yiye-agent-private.update-customer-contact-landing-page-queue";
    public static final String UPDATE_CUSTOMER_CONTACT_LANDING_PAGE_EXCHANGE = "yiye-agent-private.update-customer-contact-landing-page-exchange";

    /**
     * 订单支付成功，投递消息到clickhouse进行事件统计
     */
    public static final String LANDING_PAGE_INDICATOR_STATISTICS_ORDER_PAY_SUCCESS_EXCHANGE = "yiye-agent-private.landing-page-indicator-statistics-order-pay-success-clickhouse.exchange";
    public static final String LANDING_PAGE_INDICATOR_STATISTICS_ORDER_PAY_SUCCESS_QUEUE = "yiye-agent-private.landing-page-indicator-statistics-order-pay-success-clickhouse.queue";


    /**
     * 企业微信客服链路加粉事件 - 交换机
     */
    public static final String ENTERPRISE_WECHAT_CUSTOMER_SERVICE_QR_CODE_SHOW_EVENT_EXCHANGE = "yiye-agent-private.enterprise-wechat-customer-service-qr-code-show-event-clickhouse.exchange";

    /**
     * 企业微信客服链路加粉事件 - 队列
     */
    public static final String ENTERPRISE_WECHAT_CUSTOMER_SERVICE_QR_CODE_SHOW_EXCHANGE_EVENT_KEY = "yiye-agent-private.enterprise-wechat-customer-service-qr-code-show-event-clickhouse.queue";

    /**
     * 根据机器人清除机器人分组缓存
     */
    public static final String CLEAN_ENTERPRISE_WECHAT_ROBOT_GROUP_CACHE_EXCHANGE = "yiye-agent-private.clean-enterprise-wechat-robot-group-cache.exchange";

    public static final String CLEAN_ENTERPRISE_WECHAT_ROBOT_GROUP_CACHE_KEY = "yiye-agent-private.clean-enterprise-wechat-robot-group-cache.queue";

    /**
     * 检查企业微信客服机器人账户异常状态
     */
    public static final String ENTERPRISE_WECHAT_ROBOT_CUSTOMER_CHECK_ACCOUNT_ABNORMAL_EXCHANGE = "yiye-agent-private.enterprise-wechat-robot-customer-check-account-abnormal.exchange";

    public static final String ENTERPRISE_WECHAT_ROBOT_CUSTOMER_CHECK_ACCOUNT_ABNORMAL_KEY = "yiye-agent-private.enterprise-wechat-robot-customer-check-account-abnormal.queue";

    /**
     * 清理账户下的落地页缓存
     */
    public static final String LANDING_PAGE_CACHE_CLEAN_BY_AGENT_ID_EXCHANGE = "yiye-agent-private.landing-page-cache-clean-by-agent-id.exchange";

    public static final String LANDING_PAGE_CACHE_CLEAN_BY_AGENT_ID_KEY = "yiye-agent-private.landing-page-cache-clean-by-agent-id.queue";


    /**
     * 企业微信客服机器人禁止访客重复添加修改加粉状态
     */
    public static final String ENTERPRISE_WECHAT_ROBOT_CUSTOMER_ADD_LIMIT_ONCE_EXCHANGE = "yiye-agent-private.enterprise-wechat-robot-customer-add-limit-once.exchange";

    public static final String ENTERPRISE_WECHAT_ROBOT_CUSTOMER_ADD_LIMIT_ONCE_KEY = "yiye-agent-private.enterprise-wechat-robot-customer-add-limit-once.queue";


    /**
     * 接收ma消息 推送队列进行解析
     */
    public static final String DOUYIN_MA_CALLBACK_MESSAGE_EXCHANGE = "yiye-agent-private.douyin-ma-callback-message.exchange";
    public static final String DOUYIN_MA_CALLBACK_MESSAGE_KEY = "yiye-agent-private.douyin-ma-callback-message.queue";

    /**
     * 抖音IM主动私信授权回调处理
     */
    public static final String DOUYIN_WEBHOOK_MESSAGE_HANDLE_EXCHANGE = "yiye-agent-private.douyin-webhook-message-handle.exchange";
    public static final String DOUYIN_WEBHOOK_MESSAGE_HANDLE_KEY = "yiye-agent-private.douyin-webhook-message-handle.queue";

    /**
     * 抖音webhook消息回调存储
     */
    public static final String DOUYIN_WEBHOOK_DATA_CALLBACK_SAVE_EXCHANGE = "yiye-agent-private.douyin-webhook-data-callback-save.exchange";
    public static final String DOUYIN_WEBHOOK_DATA_CALLBACK_SAVE_KEY = "yiye-agent-private.douyin-webhook-data-callback-save.queue";

    /**
     * 抖音webhook im主动私信授权处理
     */
    public static final String DOUYIN_WEBHOOK_IM_DATA_HANDLE_EXCHANGE = "yiye-agent-private.douyin-webhook-im-data-handle.exchange";
    public static final String DOUYIN_WEBHOOK_IM_DATA_HANDLE_KEY = "yiye-agent-private.douyin-webhook-im-data-handle.queue";

    /**
     * 抖音webhook im主动私信授权推送MA
     */
    public static final String DOUYIN_WEBHOOK_IM_DATA_SEND_MA_EXCHANGE = "yiye-agent-private.douyin-webhook-im-data-send-ma.exchange";
    public static final String DOUYIN_WEBHOOK_IM_DATA_SEND_MA_KEY = "yiye-agent-private.douyin-webhook-im-data-send-ma.queue";

    /**
     * 新表单提交推送MA
     */
    public static final String DOUYIN_CUSTOMER_SEND_MA_EXCHANGE = "yiye-agent-private.douyin-customer-send-ma.exchange";
    public static final String DOUYIN_CUSTOMER_SEND_MA_KEY = "yiye-agent-private.douyin-customer-send-ma.queue";

    /**
     * 抖音webhook-处理员工状态
     */
    public static final String HANDLE_DOUYIN_EMPLOYEE_STATUS_EXCHANGE = "yiye-agent-private.handle-douyin-employee-status.exchange";
    public static final String HANDLE_DOUYIN_EMPLOYEE_STATUS_KEY = "yiye-agent-private.handle-douyin-employee-status.queue";

    /**
     * 抖音小程序订单支付回调处理
     */
    public static final String DOUYIN_ORDER_WEBHOOK_MESSAGE_HANDLE_EXCHANGE = "yiye-agent-private.douyin-order-webhook-message-handle.exchange";
    public static final String DOUYIN_ORDER_WEBHOOK_MESSAGE_HANDLE_KEY = "yiye-agent-private.douyin-order-webhook-message-handle.queue";

    /**
     * 字节小程序一键获号后，发送事件进行新查询统计的队列
     */
    public static final String CUSTOMER_DOU_YIN_AUTH_PHONE_EXCHANGE = "yiye-agent-private.customer-dou-yin-auth-phone.exchange";
    public static final String CUSTOMER_DOU_YIN_AUTH_PHONE_KEY = "yiye-agent-private.customer-dou-yin-auth-phone.queue";

    /**
     * 发送 接收处理whatsapp消息回调 队列
     */
    public static final String LANDINGPAGE_SEND_WHATSAPP_HTTP_CHAT_APP_INBOUND_EXCHANGE = "yiye-agent-private.landingpage-send-whatsapp-http-chat-app-inbound.exchange";
    public static final String LANDINGPAGE_SEND_WHATSAPP_HTTP_CHAT_APP_INBOUND_KEY = "yiye-agent-private.landingpage-send-whatsapp-http-chat-app-inbound.queue";

    /**
     * 发送 修改whatsapp已发送消息状态 队列
     */
    public static final String LANDINGPAGE_SEND_WHATSAPP_HTTP_CHAT_APP_STATUS_EXCHANGE = "yiye-agent-private.landingpage-send-whatsapp-http-chat-app-status.exchange";
    public static final String LANDINGPAGE_SEND_WHATSAPP_HTTP_CHAT_APP_STATUS_KEY = "yiye-agent-private.landingpage-send-http-chat-app-status.queue";

    /**
     * 通过阿里云发送WhatsApp消息 队列
     */
    public static final String SEND_WHATSAPP_MESSAGE_BY_ALIYUN_EXCHANGE = "yiye-agent-private.landingpage-send-whatsapp-message-by-aliyun.exchange";
    public static final String SEND_WHATSAPP_MESSAGE_BY_ALIYUN_KEY = "yiye-agent-private.landingpage-send-whatsapp-message-by-aliyun.queue";

    /**
     * 企业微信客服机器人恢复队列
     */
    public static final String ENTERPRISE_WECHAT_ROBOT_CUSTOMER_RECOVERY_EXCHANGE = "yiye-agent-private.enterprise-wechat-robot-customer-recovery.exchange";
    public static final String ENTERPRISE_WECHAT_ROBOT_CUSTOMER_RECOVERY_KEY = "yiye-agent-private.enterprise-wechat-robot-customer-recovery.queue";

    /**
     * 企业微信客服机器人恢复队列
     */
    public static final String ENTERPRISE_WECHAT_ROBOT_CUSTOMER_RECOVERY_DELAY_EXCHANGE = "yiye-agent-private.enterprise-wechat-robot-customer-recovery.delay.exchange";
    public static final String ENTERPRISE_WECHAT_ROBOT_CUSTOMER_RECOVERY_DELAY_KEY = "yiye-agent-private.enterprise-wechat-robot-customer-recovery.delay.queue";

    /**
     * 企业微信客服机器人异常通知队列
     */
    public static final String ENTERPRISE_WECHAT_ROBOT_CUSTOMER_ABNORMAL_NOTIFICATION_EXCHANGE = "yiye-agent-private.enterprise-wechat-robot-customer-abnormal-notification.exchange";
    public static final String ENTERPRISE_WECHAT_ROBOT_CUSTOMER_ABNORMAL_NOTIFICATION_KEY = "yiye-agent-private.enterprise-wechat-robot-customer-abnormal-notification.queue";


    /**
     * 企业微信客服机器人销毁队列
     */
    public static final String ENTERPRISE_WECHAT_ROBOT_CUSTOMER_DESTROY_EXCHANGE = "yiye-agent-private.enterprise-wechat-robot-customer-destroy.exchange";
    public static final String ENTERPRISE_WECHAT_ROBOT_CUSTOMER_DESTROY_KEY = "yiye-agent-private.enterprise-wechat-robot-customer-destroy.queue";

    /**
     * 异步批量创建机器人活码
     */
    public static final String ROBOT_CUSTOMER_CONTACT_BATCH_CREATE_QRCODE_EXCHANGE = "yiye-agent-private.robot-customer-contact-batch-create-qrcode-exchange";
    public static final String ROBOT_CUSTOMER_CONTACT_BATCH_CREATE_QRCODE_QUEUE = "yiye-agent-private.robot-customer-contact-batch-create-qrcode-queue";


    /**
     * 异步批量创建机器人活码(动态渠道二维码)
     */
    public static final String ROBOT_CUSTOMER_CONTACT_BATCH_CREATE_DYNAMIC_QRCODE_EXCHANGE = "yiye-agent-private.robot-customer-contact-batch-create-dynamic-qrcode-exchange";
    public static final String ROBOT_CUSTOMER_CONTACT_BATCH_CREATE_DYNAMIC_QRCODE_QUEUE = "yiye-agent-private.robot-customer-contact-batch-create-dynamic-qrcode-queue";


    /**
     * 异步批量创建机器人活码(动态渠道二维码)
     */
    public static final String ROBOT_CUSTOMER_CONTACT_COMPENSATION_DYNAMIC_QRCODE_EXCHANGE = "yiye-agent-private.robot-customer-contact-compensation-dynamic-qrcode-exchange";
    public static final String ROBOT_CUSTOMER_CONTACT_COMPENSATION_DYNAMIC_QRCODE_QUEUE = "yiye-agent-private.robot-customer-contact-compensation-dynamic-qrcode-queue";


    //落地页页面事件记录队列

    public static final String PAGE_VIEW_INDICATOR_RECORD_EXCHANGE = "yiye-agent-private.pageview-indicator-record.exchange";

    public static final String PAGE_VIEW_INDICATOR_RECORD_QUEUE = "yiye-agent-private.pageview-indicator-record.queue";

    /**
     * 填充企业微信手机号码备注队列
     */
    public static final String FILL_WECOM_REMARK_MOBILES_EXCHANGE = "yiye-agent-private.fill-wecom-remark-mobiles.exchange";
    public static final String FILL_WECOM_REMARK_MOBILES_QUEUE = "yiye-agent-private.fill-wecom-remark-mobiles.queue";

    /**
     * 填充企业微信描述（问答模板选项）
     */
    public static final String FILL_WECOM_DESCRIPTION_EXCHANGE = "yiye-agent-private.fill-wecom-description.exchange";
    public static final String FILL_WECOM_DESCRIPTION_QUEUE = "yiye-agent-private.fill-wecom-description.queue";

    /**
     * 备注企微信息
     */
    public static final String REMARKS_ENTERPRISE_WECHAT_INFO_EXCHANGE = "yiye-agent-private.remarks-enterprise-wechat-info.exchange";
    public static final String REMARKS_ENTERPRISE_WECHAT_INFO_QUEUE = "yiye-agent-private.remarks-enterprise-wechat-info.queue";

    /**
     * 落地页-复制落地页及渠道设置
     */
    public static final String LANDINGPAGE_CHANNEL_COPY_EXCHANGE = "yiye-agent-private.landingpage-channel-copy.exchange";
    public static final String LANDINGPAGE_CHANNEL_COPY_QUEUE = "yiye-agent-private.landingpage-channel-copy.queue";

    /**
     * 发送新增【关注公众号粉丝】信息队列
     */
    public static final String WECHAT_OFFICIAL_CUSTOMER_ADD_EXCHANGE = "yiye-agent-private.wechat.official.customer.add.exchange";
    public static final String WECHAT_OFFICIAL_CUSTOMER_ADD_QUEUE = "yiye-agent-private.wechat.official.customer.add.queue";

    /**
     * 发送更新【公众号粉丝取消关注】信息队列
     */
    public static final String WECHAT_OFFICIAL_CUSTOMER_UPDATE_FOLLOWSTATUS_EXCHANGE = "yiye-agent-private.wechat.official.customer.update.followstatus.exchange";
    public static final String WECHAT_OFFICIAL_CUSTOMER_UPDATE_FOLLOWSTATUS_QUEUE = "yiye-agent-private.wechat.official.customer.update.followstatus.queue";

    /**
     * 发送更新【公众号粉丝关联客资】信息队列
     */
    public static final String WECHAT_OFFICIAL_CUSTOMER_UPDATE_CUSTOMER_EXCHANGE = "yiye-agent-private.wechat.official.customer.update.customer.exchange";
    public static final String WECHAT_OFFICIAL_CUSTOMER_UPDATE_CUSTOMER_QUEUE = "yiye-agent-private.wechat.official.customer.update.customer.queue";

    /**
     * 发送公众号助手【公众号粉丝列表-更新删除好友时间】队列
     */
    public static final String WECHAT_OFFICIAL_CUSTOMER_UPDATE_DELETE_CUSTOMER_TIME_EXCHANGE = "yiye-agent-private.wechat.official.customer.update.delete.customer.time.exchange";
    public static final String WECHAT_OFFICIAL_CUSTOMER_UPDATE_DELETE_CUSTOMER_TIME_QUEUE = "yiye-agent-private.wechat.official.customer.update.delete.customer.time.queue";

    /**
     * 企微打标签重试 队列
     */
    public static final String WORK_WEIXIN_API_CLIENT_MARK_TAG_EXCHANGE = "yiye-agent-private.work-weixin-api-client.mark-tag.exchange";
    public static final String WORK_WEIXIN_API_CLIENT_MARK_TAG_QUEUE = "yiye-agent-private.work-weixin-api-client.mark-tag.queue";

    /**
     * 字节小程序订单支付成功回调
     */
    public static final String DOU_YIN_PAY_SUCCESS_RECORD_ORDER_EXCHANGE = "yiye-agent-private.dou-yin-pay-success-record-order.exchange";

    public static final String DOU_YIN_PAY_SUCCESS_RECORD_ORDER_QUEUE = "yiye-agent-private.dou-yin-pay-success-record-order.queue";



    //获客助手开口次数上报事件队列

    public static final String ACQUISITION_OPEN_NUM_UPLOAD_EXCHANGE = "yiye-agent-private.acquisition-open-num.upload.exchange";
    public static final String ACQUISITION_OPEN_NUM_UPLOAD_KEY = "yiye-agent-private.acquisition-open-num.upload.queue";

    //

    public static final String PAGE_VIEW_DOU_YIN_APPLET_JUMP_RECORD_EXCHANGE = "yiye-agent-private.page-view-dou-yin-applet-jump.exchange";

    public static final String PAGE_VIEW_DOU_YIN_APPLET_JUMP_QUEUE = "yiye-agent-private.page-view-dou-yin-applet-jump.queue";

    //成功发送客服二维码（微信客服机器人）上报
    public static final String ROBOT_SEND_QR_CODE_UPLOAD_EXCHANGE = "yiye-agent-private.robot-send-qr-code.upload.exchange";
    public static final String ROBOT_SEND_QR_CODE_UPLOAD_KEY = "yiye-agent-private.robot-send-qr-code.upload.queue";

    //微信客服机器人发送图片上报
    public static final String ROBOT_SEND_PICTURE_UPLOAD_EXCHANGE = "yiye-agent-private.robot-send-picture.upload.exchange";
    public static final String ROBOT_SEND_PICTURE_UPLOAD_KEY = "yiye-agent-private.robot-send-picture.upload.queue";

    //微信客服机器人发送图片进行发送数的统计
    public static final String ROBOT_SEND_PICTURE_STATISTICS_EXCHANGE = "yiye-agent-private.robot-send-picture.statistics.exchange";
    public static final String ROBOT_SEND_PICTURE_STATISTICS_KEY = "yiye-agent-private.robot-send-picture.statistics.queue";

    public static final String TAOBAO_DSP_EVENT_UPLOAD_EXCHANGE = "yiye-agent-private.taobao-dsp-event.upload.exchange";
    public static final String TAOBAO_DSP_EVENT_UPLOAD_KEY = "yiye-agent-private.taobao-dsp-event.upload.queue";

    public static final String TAOBAO_DSP_EVENT_PAGE_VIEW_EXCHANGE = "yiye-agent-private.taobao-dsp-event.page-view.exchange";
    public static final String TAOBAO_DSP_EVENT_PAGE_VIEW_KEY = "yiye-agent-private.taobao-dsp-event.page-view.queue";
    /**
     * 点击跳转淘宝电影小程序
     */
    public static final String PAGE_VIEW_TAO_BAO_MOVIE_APPLET_JUMP_RECORD_EXCHANGE = "yiye-agent-private.page-view-tao-bao-movie-applet-jump.exchange";

    public static final String PAGE_VIEW_TAO_BAO_MOVIE_APPLET_JUMP_QUEUE = "yiye-agent-private.page-view-tao-bao-movie-applet-jump.queue";

    //成功添加企业微信后入群上报
    public static final String ADD_ENTERPRISE_WECHAT_JOIN_GROUP_UPLOAD_EXCHANGE = "yiye-agent-private.add-enterprise-wechat-join-group.upload.exchange";
    public static final String ADD_ENTERPRISE_WECHAT_JOIN_GROUP_UPLOAD_KEY = "yiye-agent-private.add-enterprise-wechat-join-group.upload.queue";

    //成功添加企业微信后入群上报（延迟队列）
    public static final String ADD_ENTERPRISE_WECHAT_JOIN_GROUP_UPLOAD_DELAY_EXCHANGE = "yiye-agent-private.add-enterprise-wechat-join-group-delay.upload.exchange";
    public static final String ADD_ENTERPRISE_WECHAT_JOIN_GROUP_UPLOAD_DELAY_KEY = "yiye-agent-private.add-enterprise-wechat-join-group-delay.upload.queue";

    //关注公众号后发码加粉并入群上报
    public static final String FOLLOW_OFFICIAL_JOIN_GROUP_UPLOAD_EXCHANGE = "yiye-agent-private.follow-official-join-group.upload.exchange";
    public static final String FOLLOW_OFFICIAL_JOIN_GROUP_UPLOAD_KEY = "yiye-agent-private.follow-official-join-group.upload.queue";

    //关注公众号后发码加粉并入群上报（延迟队列）
    public static final String FOLLOW_OFFICIAL_JOIN_GROUP_UPLOAD_DELAY_EXCHANGE = "yiye-agent-private.follow-official-join-group-delay.upload.exchange";
    public static final String FOLLOW_OFFICIAL_JOIN_GROUP_UPLOAD_DELAY_KEY = "yiye-agent-private.follow-official-join-group-delay.upload.queue";

    //中青看点数据上报
    public static final String YOUTH_KANDIAN_UPLOAD_EXCHANGE = "yiye-agent-private.youth-kandian-upload.exchange";
    public static final String YOUTH_KANDIAN_UPLOAD_KEY = "yiye-agent-private.youth-kandian-upload.queue";

    //关注公众号后发码添加企业微信上报事件队列
    public static final String OFFICIAL_FOLLOW_ADD_CUSTOMER_EXCHANGE = "yiye-agent-private.official-follow-add-customer.upload.exchange";
    public static final String OFFICIAL_FOLLOW_ADD_CUSTOMER_KEY = "yiye-agent-private.official-follow-add-customer.upload.queue";

    //公众号助手粉丝记录webhook推送
    public static final String OFFICIAL_ACCOUNT_CUSTOMER_WEBHOOK_EXCHANGE = "yiye-agent-private.official-account-customer-webhook.exchange";
    public static final String OFFICIAL_ACCOUNT_CUSTOMER_WEBHOOK_KEY = "yiye-agent-private.official-account-customer-webhook.queue";


    //公众号助手粉丝记录webhook延迟推送
    public static final String OFFICIAL_ACCOUNT_CUSTOMER_WEBHOOK_RETRY_EXCHANGE = "yiye-agent-private.official-account-customer-webhook-retry.exchange";
    public static final String OFFICIAL_ACCOUNT_CUSTOMER_WEBHOOK_RETRY_KEY = "yiye-agent-private.official-account-customer-webhook-retry.queue";

    //公众号助手粉丝记录数据处理
    public static final String OFFICIAL_ACCOUNT_CUSTOMER_DATA_WEBHOOK_HANDLE_EXCHANGE = "yiye-agent-private.official-account-customer-data-webhook-handle.exchange";
    public static final String OFFICIAL_ACCOUNT_CUSTOMER_DATA_WEBHOOK_HANDLE_KEY = "yiye-agent-private.official-account-customer-data-webhook-handle.queue";

    //饿了么回调事件
    public static final String LANDING_PAGE_ELM_EVENT_CALLBACK_EXCHANGE = "yiye-agent-private.landing-page-elm-event-callback.exchange";
    public static final String LANDING_PAGE_ELM_EVENT_CALLBACK_KEY = "yiye-agent-private.landing-page-elm-event-callback.queue";

    //饿了么回调事件
    public static final String LANDING_PAGE_ELM_EVENT_CALLBACK_UPLOAD_EXCHANGE = "yiye-agent-private.landing-page-elm-event-callback-upload.exchange";
    public static final String LANDING_PAGE_ELM_EVENT_CALLBACK_UPLOAD_KEY = "yiye-agent-private.landing-page-elm-event-callback-upload.queue";

    //发送公众号取消授权清理活码队列
    public static final String OFFICIAL_AUTH_CANCEL_CUSTOMER_CONTACT_EXCHANGE = "yiye-agent-private.official-auth-cancle-customer-contact.exchange";
    public static final String OFFICIAL_AUTH_CANCEL_CUSTOMER_CONTACT_KEY = "yiye-agent-private.official-auth-cancle-customer-contact.queue";

    //发送检测客服删除后公众号渠道二维码信息清除
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_CLEAN_EXCHANGE = "yiye-agent-private.check-official-wechat-customer-contact-info-clean.exchange";
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_CLEAN_QUEUE = "yiye-agent-private.check-official-wechat-customer-contact-info-clean.queue";

    //发送异步生成公众号渠道二维码队列
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_BATCH_EXCHANGE = "yiye-agent-private.check-official-wechat-customer-contact-batch.exchange";
    public static final String OFFICIAL_WECHAT_CUSTOMER_CONTACT_BATCH_QUEUE = "yiye-agent-private.check-official-wechat-customer-contact-batch.queue";

    /**
     * 删除企业微信好友
     */
    public static final String DELETE_ENTERPRISE_FRIEND_EXCHANGE = "yiye-agent-private.delete-enterprise-friend-exchange";
    public static final String DELETE_ENTERPRISE_FRIEND_QUEUE = "yiye-agent-private.delete-enterprise-friend-queue";

    /**
     * 直接上报（删除企业微信好友、）
     */
    public static final String SUBMIT_DATA_DIRECTLY_UPLOAD_BY_PID_EXCHANGE = "yiye-agent-private.submit-data-directly-upload-by-pid-exchange";
    public static final String SUBMIT_DATA_DIRECTLY_UPLOAD_BY_PID_QUEUE = "yiye-agent-private.submit-data-directly-upload-by-pid-queue";


    //根据项目id清理落地页渠道是否可访问缓存
    public static final String SEND_CLEAR_CHANNEL_CAN_SHOW_CACHE_EXCHANGE = "yiye-agent-private.send-clear-channel-can-show-cache.exchange";
    public static final String SEND_CLEAR_CHANNEL_CAN_SHOW_CACHE_QUEUE = "yiye-agent-private.send-clear-channel-can-show-cache.queue";

    //异步创建微信客服机器人内联系我二维码校验清除旧数据生成新数据 - 客服初始化
    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_INIT_GENERATE_AND_CLEAN_QUEUE = "yiye-agent-private.robot-dynamic-wechat-customer-contact-init-generate-clean-queue";

    public static final String ROBOT_DYNAMIC_CUSTOMER_CONTACT_INIT_GENERATE_AND_CLEAN_EXCHANGE = "yiye-agent-private.robot-dynamic-wechat-customer-contact-init-generate-clean-exchange";

    //异步生成微信客服机器人内联系我二维码 - 用于客服初始化
    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_INIT_GENERATE_QUEUE = "yiye-agent-private.robot-dynamic-wechat-customer-contact-init-generate-queue";

    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_INIT_GENERATE_EXCHANGE = "yiye-agent-private.robot-dynamic-wechat-customer-contact-init-generate-exchange";


    //异步同步客服机器人联系我二维码生成状态至多账户私库下
    public static final String ROBOT_WECHAT_CUSTOMER_CONTACT_DYNAMIC_QRCODE_GENERATE_STATUS_QUEUE = "yiye-agent-private.robot-wechat-customer-contact-dynamic-qrcode-generate-status-queue";

    public static final String ROBOT_WECHAT_CUSTOMER_CONTACT_DYNAMIC_QRCODE_GENERATE_STATUS_EXCHANGE = "yiye-agent-private.robot-wechat-customer-contact-dynamic-qrcode-generate-status-exchange";


    //异步单个删除微信客服机器人动态渠道二维码
    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_SINGLE_DELETE_QRCODE_QUEUE = "yiye-agent-private.robot-wechat-customer-dynamic-contact-single-delete-qrcode-queue";

    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_SINGLE_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.official-wechat-customer-dynamic-contact-single-delete-qrcode-exchange";


    //异步修改机器人动态渠道二维码的使用状态
    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_CHANGE_QRCODE_STATUS_QUEUE = "yiye-agent-private. robot-wechat-customer-dynamic-contact-change-qrcode-status-queue";

    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_CHANGE_QRCODE_STATUS_EXCHANGE = "yiye-agent-private. robot-wechat-customer-dynamic-contact-change-qrcode-status-exchange";


    //异步修改机器人动态渠道二维码素材的使用状态
    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_CHANGE_QRCODE_MATERIAL_ID_QUEUE = "yiye-agent-private.robot-wechat-customer-dynamic-contact-change-qrcode-materialid-queue";

    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_CHANGE_QRCODE_MATERIAL_ID_EXCHANGE = "yiye-agent-private.robot-wechat-customer-dynamic-contact-change-qrcode-materialid-exchange";


    //异步删除微信客服机器人动态渠道二维码相关的记录
    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_DELETE_QUEUE = "yiye-agent-private.robot-wechat-customer-dynamic-contact-delete-queue";

    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_DELETE_EXCHANGE = "yiye-agent-private.robot-wechat-customer-dynamic-contact-delete-exchange";


    //异步删除客服机器人动态渠道二维码 - 取消可见范围
    public static final String ROBOT_WECHAT_CUSTOMER_CONTACT_DELETE_DYNAMIC_QRCODE_QUEUE = "yiye-agent-private.robot-wechat-customer-contact-delete-dynamic-qrcode-queue";

    public static final String ROBOT_WECHAT_CUSTOMER_CONTACT_DELETE_DYNAMIC_QRCODE_EXCHANGE = "yiye-agent-private.robot-wechat-customer-contact-delete-dynamic-qrcode-exchange";


    //取消授权-公众号清除联系我二维码缓存及数据库数据
    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_CANCEL_AUTH_DELETE_QRCODE_QUEUE = "yiye-agent-private.robot-wechat-customer-contact-cancel-auth-delete-qrcode-queue";

    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_CANCEL_AUTH_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.robot-wechat-customer-contact-cancel-auth-delete-qrcode-exchange";


    //延迟删除微信客服机器人动态渠道二维码(10分钟)
    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_SINGLE_DELAY_DELETE_QRCODE_QUEUE = "yiye-agent-private.robot-wechat-customer-dynamic-contact-delay-delete-qrcode-queue";

    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_SINGLE_DELAY_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.official-wechat-customer-dynamic-contact-delay-delete-qrcode-exchange";


    /**
     * 编辑落地页信息，发送用户上传的固定二维码图片信息进行素材上传和缓存
     */
    public static final String ROBOT_WECHAT_CUSTOMER_FIX_CONTACT_QRCODE_QUEUE = "yiye-agent-private.robot-wechat-customer-fix-contact-qrcode-queue";
    public static final String ROBOT_WECHAT_CUSTOMER_FIX_CONTACT_QRCODE_EXCHANGE= "yiye-agent-private.robot-wechat-customer-fix-contact-qrcode-exchange";



    /**
     * 上报成功之后，进行打callback无效的标签
     */
    public static final String UPLOAD_SUCCESS_ENTERPRISE_WECHAT_TAG_STRATEGY_EXCHANGE = "yiye-agent-private.upload-success-enterprise-wechat-tag-strategy.exchange";

    public static final String UPLOAD_SUCCESS_ENTERPRISE_WECHAT_TAG_STRATEGY_KEY = "yiye-agent-private.upload-success-enterprise-wechat-tag-strategy.queue";


    /**
     * 微信客服机器人内，实时消耗补充活码的队列
     */
    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_REAL_TIME_GENERATE_QUEUE = "yiye-agent-private.robot-dynamic-wechat-customer-contact-real-time-generate-queue";

    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_REAL_TIME_GENERATE_EXCHANGE = "yiye-agent-private.robot-dynamic-wechat-customer-contact-real-time-generate-exchange";


    /**
     * 微信客服机器人内，调用企业微信API接口创建活码超频，进行延迟重试
     */
    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_DELAY_GENERATE_QUEUE = "yiye-agent-private.robot-dynamic-wechat-customer-contact-delay-generate-queue";

    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_DELAY_GENERATE_EXCHANGE = "yiye-agent-private.robot-dynamic-wechat-customer-contact-delay-generate-exchange";


    /**
     * 微信客服机器人内，异步合成背景图片
     */
    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_SYNTHETIC_BACKGROUND_IMG_QUEUE = "yiye-agent-private.robot-dynamic-wechat-customer-contact-synthetic-background-img-queue";

    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_SYNTHETIC_BACKGROUND_IMG_EXCHANGE = "yiye-agent-private.robot-dynamic-wechat-customer-contact-synthetic-background-img-exchange";

    /**
     * 微信客服机器人发送活码，针对素材失效的重新上传的队列
     */
    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_EXPIRE_RE_UPLOAD_QUEUE = "yiye-agent-private.robot-dynamic-wechat-customer-contact-expire-re-upload-queue";

    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_EXPIRE_RE_UPLOAD_EXCHANGE = "yiye-agent-private.robot-dynamic-wechat-customer-contact-expire-re-upload-exchange";


    public static final String EDIT_ROBOT_CUSTOMER_SERVICE_CHECK_GENERATE_MULTIPLE_LIVE_CODE_QUEUE = "yiye-agent-private.edit-robot-customer-service-check-generate-multiple-live-code-queue";

    public static final String EDIT_ROBOT_CUSTOMER_SERVICE_CHECK_GENERATE_MULTIPLE_LIVE_CODE_EXCHANGE = "yiye-agent-private.edit-robot-customer-service-check-generate-multiple-live-code-exchange";


    /**
     * 公众号个微归因数据推送
     */
    public static final String OFFICIAL_MICROMILLIGRAM_DATA_QUEUE = "yiye-agent-private.official-micromilligram-data-queue";
    public static final String OFFICIAL_MICROMILLIGRAM_DATA_EXCHANGE = "yiye-agent-private.official-micromilligram-data-exchange";

    /**
     * 公众号个微归因数据推送延迟
     */
    public static final String OFFICIAL_MICROMILLIGRAM_DATA_DELAY_QUEUE = "yiye-agent-private.official-micromilligram-data-delay-queue";
    public static final String OFFICIAL_MICROMILLIGRAM_DATA_DELAY_EXCHANGE = "yiye-agent-private.official-micromilligram-data-delay-exchange";


    /**
     * 清楚企业微信端的动态渠道二维码
     */
    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_REMOTE_CLEAR_QUERY = "yiye-agent-private.robot_dynamic_wechat_customer_contact_remote_clear_queue";

    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_REMOTE_CLEAR_EXCHANGE = "yiye-agent-private.robot_dynamic_wechat_customer_contact_remote_clear_exchange";

    //自动回复（微信客服机器人）上报
    public static final String AUTO_REPLY_ROBOT_UPLOAD_EXCHANGE = "yiye-agent-private.auto-reply-robot.upload.exchange";
    public static final String AUTO_REPLY_ROBOT_UPLOAD_KEY = "yiye-agent-private.auto-reply-robot.upload.queue";


    //批量生成动态渠道二维码日志
    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_GENERATE_LOG_QUERY = "yiye-agent-private.robot_dynamic_wechat_customer_contact_generate_log_query";

    public static final String ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_GENERATE_LOG_EXCHANGE = "yiye-agent-private.robot_dynamic_wechat_customer_contact_generate_log_exchange";

    //批量删除微信客服动态渠道二维码
    public static final String BATCH_DELETE_ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_QUERY = "yiye-agent-private.batch_delete_robot_dynamic_wechat_customer_contact_query";

    public static final String BATCH_DELETE_ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_EXCHANGE = "yiye-agent-private.batch_delete_robot_dynamic_wechat_customer_contact_exchange";


    //合成服务——发送合成动态渠道二维码背景图请求的队列
    public static final String SNAPSHOT_ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_BACKGROUND_IMG_GENERATE_REQUEST_QUERY = "yiye-agent-private.snapshot_robot_dynamic_wechat_customer_contact_background_img_generate_request_query";

    //合成服务——发送合成动态渠道二维码背景图请求的交换机
    public static final String SNAPSHOT_ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_BACKGROUND_IMG_GENERATE_REQUEST_EXCHANGE = "yiye-agent-private.snapshot_robot_dynamic_wechat_customer_contact_background_img_generate_request_exchange";

    //合成服务——发送合成动态渠道二维码背景图合成后的结果的队列
    public static final String SNAPSHOT_ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_BACKGROUND_IMG_GENERATE_RESULT_QUERY = "yiye-agent-private.snapshot_robot_dynamic_wechat_customer_contact_background_img_generate_result_query";

    //合成服务——发送合成动态渠道二维码背景图请求合成后的结果的交换机
    public static final String SNAPSHOT_ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_BACKGROUND_IMG_GENERATE_RESULT_EXCHANGE = "yiye-agent-private.snapshot_robot_dynamic_wechat_customer_contact_background_img_generate_result_exchange";


    //检查微信客服机器人动态渠道二维码是否需要重新初始化或者续期
    public static final String CHECK_ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_QR_CODE_RE_UPLOAD_QUERY = "yiye-agent-private.check_robot_dynamic_wechat_customer_contact_qr_code_re_upload_query";

    public static final String CHECK_ROBOT_DYNAMIC_WECHAT_CUSTOMER_CONTACT_QR_CODE_RE_UPLOAD_EXCHANGE = "yiye-agent-private.check_robot_dynamic_wechat_customer_contact_qr_code_re_upload_exchange";


    //定时任务定时异步删除机器人动态渠道活码
    public static final String ROBOT_DYNAMIC_CUSTOMER_CONTACT_TIMING_DELETE_QRCODE_QUEUE = "yiye-agent-private.robot-dynamic-customer-contact-timing-delete-qrcode-queue";
    public static final String ROBOT_DYNAMIC_CUSTOMER_CONTACT_TIMING_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.robot-dynamic-customer-contact-timing-delete-qrcode-exchange";


    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_EXPIRE_DELETE_QRCODE_QUEUE = "yiye-agent-private.robot-wechat-customer-dynamic-contact-expire-delete-qrcode-queue";

    public static final String ROBOT_WECHAT_CUSTOMER_DYNAMIC_CONTACT_EXPIRE_DELETE_QRCODE_EXCHANGE = "yiye-agent-private.official-wechat-customer-dynamic-contact-expire-delete-qrcode-exchange";

    public static final String TAOBAO_DSP_UPLOAD_EXCHANGE = "yiye-agent-private.taobao_dsp_upload_exchange";
    public static final String TAOBAO_DSP_UPLOAD_QUEUE = "yiye-agent-private.taobao_dsp_upload_queue";
    public static final String TAOBAO_DSP_EVENT_HANDLE_EXCHANGE = "yiye-agent-private.taobao_dsp_event_handle_exchange";
    public static final String TAOBAO_DSP_EVENT_HANDLE_QUEUE = "yiye-agent-private.taobao_dsp_event_handle_queue";


    //获客助手开口次数上回调统计队列

    public static final String ACQUISITION_OPEN_NUM_STATISTICS_EXCHANGE = "yiye-agent-private.acquisition-open-num.statistics.exchange";
    public static final String ACQUISITION_OPEN_NUM_STATISTICS_KEY = "yiye-agent-private.acquisition-open-num.statistics.queue";

    public static final String ORGANIZATION_RECHARGE_FLOW_EXPORT_EXCHANGE = "yiye-agent-private.organization-recharge-flow-export.exchange";
    public static final String ORGANIZATION_RECHARGE_FLOW_EXPORT_KEY = "yiye-agent-private.organization-recharge-flow-export.queue";
    public static final String ORGANIZATION_CONSUME_RECORD_EXPORT_EXCHANGE = "yiye-agent-private.organization-consume-record-export.exchange";
    public static final String ORGANIZATION_CONSUME_RECORD_EXPORT_KEY = "yiye-agent-private.organization-consume-record-export.queue";

    //导出许可证详情列表
    public static final String LANDING_PAGE_LICENSE_USAGE_DETAILS_EXPORT_EXCHANGE = "yiye-agent-private.landing-page-license-usage-details-export.exchange";
    public static final String LANDING_PAGE_LICENSE_USAGE_DETAILS_EXPORT_QUEUE = "yiye-agent-private.landing-page-license-usage-details-export.queue";
}
