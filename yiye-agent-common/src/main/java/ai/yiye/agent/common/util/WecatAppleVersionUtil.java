package ai.yiye.agent.common.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @create 2022/9/14 8:08 下午
 */
public class WecatAppleVersionUtil {


//    public static String getMinVersion(String firstVersion, String endVersion) {
//        String version = null;
//        String nextVersion = null;
//        if (StringUtils.isNotBlank(firstVersion)) {
//            version = StringUtils.trim(StringUtils.substringBefore(StringUtils.trim(firstVersion), " "));
//        }
//        if (StringUtils.isNotBlank(endVersion)) {
//            nextVersion = StringUtils.trim(StringUtils.substringBefore(StringUtils.trim(endVersion), " "));
//        }
//        if (StringUtils.isNotBlank(version) && StringUtils.isNotBlank(nextVersion)) {
//            return StringUtils.compareIgnoreCase(version, nextVersion) <= 0 ? version : nextVersion;
//        } else {
//            return StringUtils.isNotBlank(version) ? version : nextVersion;
//        }
//    }


//    public static String getMaxVersion(String firstVersion, String endVersion) {
//        String version = null;
//        String nextVersion = null;
//        if (StringUtils.isNotBlank(firstVersion)) {
//            version = StringUtils.trim(StringUtils.substringBefore(StringUtils.trim(firstVersion), " "));
//        }
//        if (StringUtils.isNotBlank(endVersion)) {
//            nextVersion = StringUtils.trim(StringUtils.substringBefore(StringUtils.trim(endVersion), " "));
//        }
//        if (StringUtils.isNotBlank(version) && StringUtils.isNotBlank(nextVersion)) {
//            return StringUtils.compareIgnoreCase(version, nextVersion) >= 0 ? version : nextVersion;
//        } else {
//            return StringUtils.isNotBlank(version) ? version : nextVersion;
//        }
////        return StringUtils.isNotBlank(version) ? version : nextVersion;
//    }

    public static void main(String[] args) {

//        System.out.println(StringUtils.trim(StringUtils.substringBefore(StringUtils.trim("   1.177.4  dev ")," ")));
//        System.out.println(StringUtils.trim(StringUtils.substringBeforeLast(null," ")));
//        System.out.println(StringUtils.trim(StringUtils.substringBeforeLast(" 1.177.0 dev "," ")));
//        System.out.println(StringUtils.trim(StringUtils.substringBeforeLast(" 1.177.0  dev"," ")));
        System.out.println(StringUtils.equals(getMinVersion("2.0.0", "1.177.1"), "1.177.1"));
        System.out.println(StringUtils.equals(getMinVersion("1.21.0", "1.177.1"), "1.21.0"));
        System.out.println(StringUtils.equals(getMinVersion("1.177.11", "1.177.2"), "1.177.2"));
        System.out.println(StringUtils.equals(getMinVersion("1.173.11", "1.177.1"), "1.173.11"));
        System.out.println(StringUtils.equals(getMinVersion("21.177.11", "1.177.1"), "1.177.1"));
        System.out.println(StringUtils.equals(getMinVersion("21.177.11", "21.177.21"), "21.177.11"));
        System.out.println(StringUtils.equals(getMinVersion("1.177.1", "1.173.11"), "1.173.11"));
        System.out.println(StringUtils.equals(getMinVersion("1.177.1", null), "1.177.1"));
        System.out.println(StringUtils.equals(getMinVersion(null, "1.177.1"), "1.177.1"));
//
//
        System.out.println(StringUtils.equals(getMaxVersion("1.223.10", "1.223.8"), "1.223.10"));
        System.out.println(StringUtils.equals(getMaxVersion("1.173.11", "1.177.1"), "1.177.1"));
        System.out.println(StringUtils.equals(getMaxVersion("1.177.1", "1.173.11"), "1.177.1"));
        System.out.println(StringUtils.equals(getMaxVersion("1.177.1", null), "1.177.1"));
        System.out.println(StringUtils.equals(getMaxVersion(null, "1.177.1"), "1.177.1"));


        System.out.println(StringUtils.equals(getMaxVersion("1.177.0 dev", "1.177.1 dev"), "1.177.1"));
        System.out.println(StringUtils.equals(getMaxVersion("1.173.11 dev", "1.177.1 dev"), "1.177.1"));
        System.out.println(StringUtils.equals(getMaxVersion("1.177.1 dev", "1.173.11 dev"), "1.177.1"));
        System.out.println(StringUtils.equals(getMaxVersion("1.177.1 dev", null), "1.177.1"));
        System.out.println(StringUtils.equals(getMaxVersion(null, "1.177.1 dev"), "1.177.1"));
//        List<String> lists=Arrays.asList("1.179.0","1.183.1","1.183.10","1.183.0");
//        System.out.println(getMaxVersionFromList(lists));

        System.out.println(getMinVersion("1.223.10", "1.223.8"));
        System.out.println(getMaxVersion("1.223.10", "1.223.8"));
    }


    public static String getMaxVersion(String version1,String version2) {
        String version = null;
        String nextVersion = null;
        if (StringUtils.isNotBlank(version1)) {
            version = StringUtils.trim(StringUtils.substringBefore(StringUtils.trim(version1), " "));
        }
        if (StringUtils.isNotBlank(version2)) {
            nextVersion = StringUtils.trim(StringUtils.substringBefore(StringUtils.trim(version2), " "));
        }
        if (StringUtils.isNotBlank(version) && StringUtils.isNotBlank(nextVersion)) {
            int result = compare(version, nextVersion);
            return result >= 0 ? version : nextVersion;
        }else{
            return StringUtils.isNotBlank(version) ? version : nextVersion;
        }
    }

    public static String getMinVersion(String version1,String version2) {
        String version = null;
        String nextVersion = null;
        if (StringUtils.isNotBlank(version1)) {
            version = StringUtils.trim(StringUtils.substringBefore(StringUtils.trim(version1), " "));
        }
        if (StringUtils.isNotBlank(version2)) {
            nextVersion = StringUtils.trim(StringUtils.substringBefore(StringUtils.trim(version2), " "));
        }
        if (StringUtils.isNotBlank(version) && StringUtils.isNotBlank(nextVersion)) {
            int result = compare(version, nextVersion);
            return result <= 0 ? version : nextVersion;
        }else{
            return StringUtils.isNotBlank(version) ? version : nextVersion;
        }
    }


    public static int compare(String version1, String version2) {
        String[] arr1 = version1.split("\\.");
        String[] arr2 = version2.split("\\.");

        int i = 0;
        while (i < arr1.length || i < arr2.length) {
            if (i < arr1.length && i < arr2.length) {
                if (Integer.parseInt(arr1[i]) < Integer.parseInt(arr2[i])) {
                    return -1;
                } else if (Integer.parseInt(arr1[i]) > Integer.parseInt(arr2[i])) {
                    return 1;
                }
            } else if (i < arr1.length) {
                if (Integer.parseInt(arr1[i]) != 0) {
                    return 1;
                }
            } else if (i < arr2.length) {
                if (Integer.parseInt(arr2[i]) != 0) {
                    return -1;
                }
            }

            i++;
        }

        return 0;
    }
}
