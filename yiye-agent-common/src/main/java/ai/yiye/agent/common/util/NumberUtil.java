package ai.yiye.agent.common.util;


import lombok.extern.slf4j.Slf4j;

import java.text.DecimalFormat;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/15 15:06
 */
@Slf4j
public class NumberUtil {
    /**
     * 中文數字转阿拉伯数组【十万九千零六十  --> 109060】
     * <AUTHOR>
     * @param chineseNumber
     * @return
     */
    public static int chineseNumber2Int(String chineseNumber){
        //在做所有的判断之前，将字符串中的大于号和小于号去掉
        chineseNumber=chineseNumber.replace(">","").replace("<","");
        int result = 0;
        int temp = 1;//存放一个单位的数字如：十万
        int count = 0;//判断是否有chArr
        char[] cnArr = new char[]{'一','二','三','四','五','六','七','八','九'};
        char[] chArr = new char[]{'十','百','千','万','亿'};
        for (int i = 0; i < chineseNumber.length(); i++) {
            boolean b = true;//判断是否是chArr
            char c = chineseNumber.charAt(i);
            for (int j = 0; j < cnArr.length; j++) {//非单位，即数字
                if (c == cnArr[j]) {
                    if(0 != count){//添加下一个单位之前，先把上一个单位值添加到结果中
                        result += temp;
                        temp = 1;
                        count = 0;
                    }
                    // 下标+1，就是对应的值
                    temp = j + 1;
                    b = false;
                    break;
                }
            }
            if(b){//单位{'十','百','千','万','亿'}
                for (int j = 0; j < chArr.length; j++) {
                    if (c == chArr[j]) {
                        switch (j) {
                            case 0:
                                temp *= 10;
                                break;
                            case 1:
                                temp *= 100;
                                break;
                            case 2:
                                temp *= 1000;
                                break;
                            case 3:
                                temp *= 10000;
                                break;
                            case 4:
                                temp *= 100000000;
                                break;
                            default:
                                break;
                        }
                        count++;
                    }
                }
            }
            if (i == chineseNumber.length() - 1) {//遍历到最后一个字符
                result += temp;
            }
        }
        return result;
    }

    /**
     只留最大位的转换
     * <AUTHOR> Wang
     * @date 2020/12/23 17:32
     * @return中文數字转阿拉伯数组【十万九千零六十  --> 109060】
     * <AUTHOR>
     * @param chineseNumber
     * @return
     */
    public static String Long2ChineseNumber(Long chineseNumber){
        DecimalFormat df = new DecimalFormat("0.0");
        if(chineseNumber/100000000>1){
            float num =(float)chineseNumber/100000000;
           return df.format(num)+"亿";
        }
        if(chineseNumber/10000>1){
            float num =(float)chineseNumber/10000;
            return df.format(num)+"万";
        }else{
            return "<1万";
        }
    }

    /**
     * 将多个连续的数字合并成多个区间，如下所示：
     * 输入参数：[5, 6, 7, 8, 9, 10, 12, 14, 44, 45, 46, 47, 48]
     * 输出结果：[5-10, 12, 14, 44-48]
     */
    public static List<String> mergeContinuityNumberToSection(Integer[] ids) {
        return CommonUtil.mergeContinuityNumberToSection(ids);
    }


    public static int versionCompare(String ver1, String ver2){
        if(ver1.equals(ver2)){
            return 0;
        }
        String[] verArr1 = ver1.split("\\.");
        String[] verArr2 = ver2.split("\\.");
        int maxflag = 1;
        int minLen = 0;
        if(verArr1.length > verArr2.length){
            minLen = verArr2.length;
        }else{
            minLen = verArr1.length;
            maxflag = 2;
        }
        for(int i = 0; i < minLen; i++){
            if(Integer.valueOf(verArr1[i]) - Integer.valueOf(verArr2[i]) > 0){
                return 1;
            }else if(Integer.valueOf(verArr1[i]) - Integer.valueOf(verArr2[i]) < 0){
                return -1;
            }
        }
        if(maxflag == 1){
            for (int j = minLen; j < verArr1.length; j++) {
                if(Integer.valueOf(verArr1[j]).intValue() > 0){
                    return 1;
                }
            }
        }else{
            for (int k = minLen; k < verArr2.length; k++) {
                if(Integer.valueOf(verArr2[k]).intValue() > 0){
                    return -1;
                }
            }
        }
        return 0;
    }

}
