test_cases:
  - user_agent_string: 'Luminary/1.0'
    family: 'Luminary'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Luminary/70 CFNetwork/978.0.7 Darwin/18.5.0'
    family: 'Luminary'
    major: '70'
    minor:
    patch:

  - user_agent_string: 'Luminary/70 CFNetwork/975.0.3 Darwin/18.2.0'
    family: 'Luminary'
    major: '70'
    minor:
    patch:

  - user_agent_string: 'Luminary/1.0.3 build 71/iOS 12.2'
    family: 'Luminary'
    major: '1'
    minor: '0'
    patch: '3'

  - user_agent_string: 'Luminary/1.0.2 build 57/Android SDK 28'
    family: 'Luminary'
    major: '1'
    minor: '0'
    patch: '2'

  - user_agent_string: 'Luminary/1.0.2 build 57/Android SDK 23'
    family: 'Luminary'
    major: '1'
    minor: '0'
    patch: '2'

  - user_agent_string: 'Luminary/70 CFNetwork/976 Darwin/18.2.0'
    family: 'Luminary'
    major: '70'
    minor:
    patch:

  - user_agent_string: 'Luminary/70 CFNetwork/978.0.7 Darwin/18.6.0'
    family: 'Luminary'
    major: '70'
    minor:
    patch:

  - user_agent_string: 'Luminary/1.0.2 build 57/Android SDK 26'
    family: 'Luminary'
    major: '1'
    minor: '0'
    patch: '2'

  - user_agent_string: 'LuminaryStage/3311 CFNetwork/978.0.7 Darwin/18.5.0'
    family: 'Luminary'
    major: '3311'
    minor:
    patch:

  - user_agent_string: 'fakeLuminary/1.0'
    family: 'Other'
    major:
    minor:
    patch:

  - user_agent_string: 'atc/1.0 watchOS/5.1.3 model/Watch3,4 hwp/t8004 build/16S535 (6; dt:156)'
    family: 'Apple Watch App'
    major: '3'
    minor: '4'
    patch:

  - user_agent_string: 'atc/1.0 watchOS/5.2 model/Watch4,4 hwp/t8006 build/16T225 (6; dt:193)'
    family: 'Apple Watch App'
    major: '4'
    minor: '4'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows; U; en-US) AppleWebKit/531.9 (KHTML, like Gecko) AdobeAIR/2.5.1'
    family: 'AdobeAIR'
    major: '2'
    minor: '5'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64; Quest 2) AppleWebKit/537.36 (KHTML, like Gecko) OculusBrowser/26.2.0.0.10 SamsungBrowser/4.0 Chrome/110.0.5481.192 VR Safari/537.36'
    family: 'Oculus Browser'
    major: '26'
    minor: '2'
    patch: '10'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_3; en-us; Silk/1.1.0-80) AppleWebKit/533.16 (KHTML, like Gecko) Version/5.0 Safari/533.16 Silk-Accelerated=true'
    family: 'Amazon Silk'
    major: '1'
    minor: '1'
    patch: '0-80'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; en-us; KFTT Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Silk/2.0 Safari/535.19 Silk-Accelerated=false'
    family: 'Amazon Silk'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; en-us; KFOT Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Silk/2.1 Safari/535.19 Silk-Accelerated=true'
    family: 'Amazon Silk'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; en-us; KFTT Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Silk/2.2 Safari/535.19 Silk-Accelerated=true'
    family: 'Amazon Silk'
    major: '2'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.0.3; en-gb; KFTT Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.25 like Chrome/34.0.1847.137 Mobile Safari/537.36'
    family: 'Amazon Silk'
    major: '3'
    minor: '25'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 2.2.2; en-gb; HTC Desire Build/FRG83G) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1'
    family: 'Android'
    major: '2'
    minor: '2'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 2.3.3; en-fr; HTC/WildfireS/1.33.163.2 Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1'
    family: 'Android'
    major: '2'
    minor: '3'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 2.3.4; en-us; Kindle Fire Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1'
    family: 'Android'
    major: '2'
    minor: '3'
    patch: '4'

  - user_agent_string: 'Mozilla/5.0 (Linux;U;Android 2.3.5;en-us;TECNO T3 Build/master) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1'
    family: 'Android'
    major: '2'
    minor: '3'
    patch: '5'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 3.0.1; en-us; GT-P7510 Build/HRI83) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13'
    family: 'Android'
    major: '3'
    minor: '0'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; KFTT Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30'
    family: 'Android'
    major: '4'
    minor: '0'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; KFOT Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30'
    family: 'Android'
    major: '4'
    minor: '0'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; Amaze_4G Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30'
    family: 'Android'
    major: '4'
    minor: '0'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; PJ83100/2.20.502.7 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.0'
    family: 'Android'
    major: '4'
    minor: '0'
    patch: '4'

  - user_agent_string: 'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh_CN) AppleWebKit/534.7 (KHTML, like Gecko) Chrome/7.0 baidubrowser/1.x Safari/534.7'
    family: 'Baidu Browser'
    major: '1'
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E; baidubrowser 1.x)'
    family: 'Baidu Browser'
    major: '1'
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.15 Safari/535.11 QQBrowser/6.13.13719.201'
    family: 'QQ Browser'
    major: '6'
    minor: '13'
    patch: '13719'

  - user_agent_string: 'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0; QQBrowser/7.6.21433.400)'
    family: 'QQ Browser'
    major: '7'
    minor: '6'
    patch: '21433'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.1.1; zh-cn; MI 2S Build/JRO03L) AppleWebKit/537.36 (KHTML, like Gecko)Version/4.0 MQQBrowser/5.0 Mobile Safari/537.36'
    family: 'QQ Browser Mobile'
    major: '5'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone 5; CPU iPhone OS 7_0_6 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/6.0 MQQBrowser/5.0.5 Mobile/11B651 Safari/8536.25'
    family: 'QQ Browser Mobile'
    major: '5'
    minor: '0'
    patch: '5'

  - user_agent_string: 'MQQBrowser/371 Mozilla/5.0 (iPhone 4S; CPU iPhone OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10A523 Safari/7534.48.3'
    family: 'QQ Browser Mobile'
    major: '371'
    minor:
    patch:

  - user_agent_string: 'MQQBrowser/1.0/Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; NOKIA; RM-910apacprc200)'
    family: 'QQ Browser Mobile'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'QQBrowser (Linux; U; zh-cn; HTC Hero Build/FRF91)'
    family: 'QQ Browser'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (BB10; Touch) AppleWebKit/537.3+ (KHTML, like Gecko) Version/10.0.9.388 Mobile Safari/537.3+'
    family: 'BlackBerry WebKit'
    major: '10'
    minor: '0'
    patch: '9'

  - user_agent_string: 'Mozilla/5.0 (PlayBook; U; RIM Tablet OS 1.0.0; en-US) AppleWebKit/534.8+ (KHTML, like Gecko) Version/0.0.1 Safari/534.8+'
    family: 'BlackBerry WebKit'
    major: '1'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (BlackBerry; U; BlackBerry 9800; en-GB) AppleWebKit/534.1+ (KHTML, like Gecko) Version/6.0.0.141 Mobile Safari/534.1+'
    family: 'BlackBerry WebKit'
    major: '6'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (BlackBerry; U; BlackBerry 9800; en-US) AppleWebKit/534.1  (KHTML, like Gecko) Version/6.0.0.91 Mobile Safari/534.1 '
    family: 'BlackBerry WebKit'
    major: '6'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; BOLT/2.101) AppleWebKit/530  (KHTML, like Gecko) Version/4.0 Safari/530.17'
    family: 'BOLT'
    major: '2'
    minor: '101'
    patch:

  - user_agent_string: 'Bunjalloo/0.7.6(Nintendo DS;U;en)'
    family: 'Bunjalloo'
    major: '0'
    minor: '7'
    patch: '6'

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0; chromeframe; SLCC1; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729)'
    family: 'IE'
    major: '8'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; chromeframe; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; Sleipnir 2.8.5)3.0.30729)'
    family: 'Sleipnir'
    major: '2'
    minor: '8'
    patch: '5'

  - user_agent_string: 'Mozilla/5.0 (compatible; Windows NT 6.1; Catchpoint) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.81 Safari/537.36'
    family: 'Catchpoint'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Windows NT 6.1; Catchpoint bot) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.81 Safari/537.36'
    family: 'Catchpoint bot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Macintosh) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.2987.98 Safari/537.36 CreativeCloud/4.8.1.435'
    family: 'Adobe CreativeCloud'
    major: '4'
    minor: '8'
    patch: '1'

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; chromeframe/11.0.660.0)'
    family: 'Chrome Frame'
    major: '11'
    minor: '0'
    patch: '660'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 9; Pixel 2 XL Build/PPP5.180610.010; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/68.0.3440.85 Mobile Safari/537.36'
    family: 'Chrome Mobile WebView'
    major: '68'
    minor: '0'
    patch: '3440'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 7.1.2; Nexus 5X Build/N2G47W; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/58.0.3029.83 Mobile Safari/537.36'
    family: 'Chrome Mobile WebView'
    major: '58'
    minor: '0'
    patch: '3029'
    patch_minor: '83'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 4.4.4; SHV31 Build/S2280) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/******** Mobile Safari/537.36'
    family: 'Chrome Mobile WebView'
    major: '33'
    minor: '0'
    patch: '0'
    patch_minor: '0'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 4.2; Galaxy Nexus Build/JOP40C) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19'
    family: 'Chrome Mobile'
    major: '18'
    minor: '0'
    patch: '1025'
    patch_minor: '166'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 4.1.1; SPH-L710 Build/JRO03L) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19'
    family: 'Chrome Mobile'
    major: '18'
    minor: '0'
    patch: '1025'
    patch_minor: '166'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 4.4.2; Nexus 5 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.122 Mobile Safari/537.36'
    family: 'Chrome Mobile'
    major: '35'
    minor: '0'
    patch: '1916'
    patch_minor: '122'

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux i686; en-US) AppleWebKit/534.16 (KHTML, like Gecko) Ubuntu/10.10 Chromium/10.0.648.133 Chrome/10.0.648.133 Safari/534.16'
    family: 'Chromium'
    major: '10'
    minor: '0'
    patch: '648'
    patch_minor: '133'

  - user_agent_string: 'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) AppleWebKit/532.5 (KHTML, like Gecko) Comodo_Dragon/4.1.1.11 Chrome/4.1.249.1042 Safari/532.5'
    family: 'Comodo Dragon'
    major: '4'
    minor: '1'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.9.1.16) Gecko/20110302 Conkeror/0.9.2 (Debian-0.9.2+git100804-1)'
    family: 'Conkeror'
    major: '0'
    minor: '9'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64; rv:2.0) Gecko/20110408 conkeror/0.9.3'
    family: 'Conkeror'
    major: '0'
    minor: '9'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (SAMSUNG; SAMSUNG-GT-S8500/S8500XXJEE; U; Bada/1.0; nl-nl) AppleWebKit/533.1 (KHTML, like Gecko) Dolfin/2.0 Mobile WVGA SMM-MMS/1.2.0 OPN-B'
    family: 'Dolfin'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)'
    family: 'FacebookBot'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'LinkedInBot/1.0 (compatible; Mozilla/5.0; Jakarta Commons-HttpClient/3.1 +http://www.linkedin.com)'
    family: 'LinkedInBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Google (+https://developers.google.com/+/web/snippet/)'
    family: 'GooglePlusBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Firefox/11.0 (via ggpht.com GoogleImageProxy)'
    family: 'GmailImageProxy'
    major:
    minor:
    patch:

  - user_agent_string: 'Twitterbot/1.0'
    family: 'Twitterbot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'WhatsApp/2.17.70 W'
    family: 'WhatsApp'
    major: '2'
    minor: '17'
    patch: '70'

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.9.1.1pre) Gecko/20090717 Ubuntu/9.04 (jaunty) Shiretoko/3.5.1pre'
    family: 'Firefox (Shiretoko)'
    major: '3'
    minor: '5'
    patch: '1pre'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux i686 (x86_64); rv:2.0b4) Gecko/20100818 Firefox/4.0b4'
    family: 'Firefox Beta'
    major: '4'
    minor: '0'
    patch: 'b4'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64; rv:2.0b8pre) Gecko/20101031 Firefox-4.0/4.0b8pre'
    family: 'Firefox Beta'
    major: '4'
    minor: '0'
    patch: 'b8pre'

  - user_agent_string: 'Mozilla/5.0 (X11; U; SunOS i86pc; en-US; rv:1.8.0.5) Gecko/20060728 Firefox/1.5.0.5'
    family: 'Firefox'
    major: '1'
    minor: '5'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux x86_64; en-US; rv:1.9.2.12) Gecko/20101027 Ubuntu/10.04 (lucid) Firefox/3.6.12'
    family: 'Firefox'
    major: '3'
    minor: '6'
    patch: '12'

  - user_agent_string: 'Mozilla/5.0 (Mobile; rv:15.0) Gecko/15.0 Firefox/15.0'
    family: 'Firefox Mobile'
    major: '15'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/534+ (KHTML, like Gecko) FireWeb/1.0.0.0'
    family: 'FireWeb'
    major: '1'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)'
    family: 'Googlebot'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (DTV) AppleWebKit/531.2+ (KHTML, like Gecko) Espial/6.1.6 AQUOSBrowser/2.0 (US01DTV;V;0001;0001)'
    family: 'Espial'
    major: '6'
    minor: '1'
    patch: '6'

  - user_agent_string: 'iBrowser/Mini2.8 (Nokia5130c-2/07.97)'
    family: 'iBrowser Mini'
    major: '2'
    minor: '8'
    patch:

  - user_agent_string: 'ICE Browser/5.05 (Java 1.4.0; Windows 2000 5.0 x86)'
    family: 'ICE Browser'
    major: '5'
    minor: '05'
    patch:

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64; rv:2.0) Gecko/20110417 IceCat/4.0'
    family: 'IceCat'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; XBLWP7; ZuneWP7)'
    family: 'IE Large Screen'
    major: '9'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows Phone OS 7.0; Trident/3.1; IEMobile/7.0; SAMSUNG; SGH-i917)'
    family: 'IE Mobile'
    major: '7'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; SGH-i917)'
    family: 'IE Mobile'
    major: '9'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 800)'
    family: 'IE Mobile'
    major: '9'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 920)'
    family: 'IE Mobile'
    major: '10'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB6; .NET CLR 2.0.50727; .NET CLR 1.1.4322)'
    family: 'IE'
    major: '8'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 WebTV/2.6 (compatible; MSIE 4.0)'
    family: 'IE'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 5.17; Mac_PowerPC)'
    family: 'IE'
    major: '5'
    minor: '17'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; ARM; Trident/6.0)'
    family: 'IE'
    major: '10'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB6; chromeframe; .NET CLR 2.0.50727; .NET CLR 1.1.4322; .NET CLR 3.0.04506.648; .NET CLR 3.5.21022; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)'
    family: 'IE'
    major: '8'
    minor: '0'
    patch:

  - user_agent_string: 'Java/1.6.0_43'
    family: 'Java'
    major: '6'
    minor: '0'
    patch: '43'

  - user_agent_string: 'Java/1.7.0_71'
    family: 'Java'
    major: '7'
    minor: '0'
    patch: '71'

  - user_agent_string: 'Java/1.8.0_25'
    family: 'Java'
    major: '8'
    minor: '0'
    patch: '25'

  - user_agent_string: 'Java/17.0.6'
    family: 'Java'
    major: '17'
    minor: '0'
    patch: '6'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; en-US) AppleWebKit/528.5+ (KHTML, like Gecko, Safari/528.5+) Version/4.0 Kindle/3.0 (screen 600x800; rotate)'
    family: 'Kindle'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux; de-DE) AppleWebKit/527  (KHTML, like Gecko, Safari/419.3) konqueror/4.3.1'
    family: 'Konqueror'
    major: '4'
    minor: '3'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.9.2.17) Gecko/20110414 Lightning/1.0b3pre Thunderbird/3.1.10'
    family: 'Lightning'
    major: '1'
    minor: '0'
    patch: 'b3pre'

  - user_agent_string: 'Mozilla/5.0 (Linux x86_64) AppleWebKit/534.26+ WebKitGTK+/1.4.1 luakit/f3a2dbe'
    family: 'LuaKit'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.47 Safari/535.11 MRCHROME'
    family: 'Mail.ru Chromium Browser'
    major: '17'
    minor: '0'
    patch: '963'

  - user_agent_string: 'Midori/0.2 (X11; Linux; U; en-us) WebKit/531.2 '
    family: 'Midori'
    major: '0'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPad; U; CPU OS 3_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B367 Safari/531.21.10'
    family: 'Mobile Safari'
    major: '4'
    minor: '0'
    patch: '4'

  - user_agent_string: 'Mozilla/5.0 (iPod; U; CPU iPhone OS 4_3_2 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8H7 Safari/6533.18.5'
    family: 'Mobile Safari'
    major: '5'
    minor: '0'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ManagedBrowser/20181024.1'
    family: 'Mobile Safari UI/WKWebView'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Safari/605.1.15'
    family: 'Safari'
    major: '12'
    minor: '1'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148'
    family: 'Mobile Safari UI/WKWebView'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ManagedBrowser/20181024.1'
    family: 'Mobile Safari UI/WKWebView'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPod touch; CPU iPhone OS 9_3_2 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Mobile/13F69'
    family: 'Mobile Safari UI/WKWebView'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_2 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Mobile/11D257'
    family: 'Mobile Safari UI/WKWebView'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US; rv:1.9.3a1) Gecko/20100208 MozillaDeveloperPreview/3.7a1 (.NET CLR 3.5.30729)'
    family: 'MozillaDeveloperPreview'
    major: '3'
    minor: '7'
    patch: 'a1'

  - user_agent_string: 'NCSA_Mosaic/2.0 (Windows 3.1)'
    family: 'NCSA Mosaic'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (PLAYSTATION 3; 3.55)'
    family: 'NetFront'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (PLAYSTATION 3 4.31) AppleWebKit/531.22.8 (KHTML, like Gecko)'
    family: 'NetFront NX'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (PSP (PlayStation Portable); 2.00)'
    family: 'NetFront'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (PlayStation Vita 1.81) AppleWebKit/531.22.8 (KHTML, like Gecko) Silk/3.2'
    family: 'NetFront NX'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; Linux 2.6.10) NetFront/3.3 Kindle/1.0 (screen 600x800)'
    family: 'NetFront'
    major: '3'
    minor: '3'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Nintendo 3DS; U; ; en) Version/1.7498.US'
    family: 'NetFront NX'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Nintendo WiiU) AppleWebKit/534.52 (KHTML, like Gecko) NX/2.1.0.8.21 NintendoBrowser/1.0.0.7494.US'
    family: 'NetFront NX'
    major: '2'
    minor: '1'
    patch: '0'

  - user_agent_string: 'HUAWEI-M750/001.00 ACS-NetFront/3.2'
    family: 'NetFront'
    major: '3'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/4.0 (BREW 3.1.5; U; en-us; Sanyo; NetFront/3.5.1/AMB) Boost SCP3810'
    family: 'NetFront'
    major: '3'
    minor: '5'
    patch: '1'

  - user_agent_string: 'NetFront/3.5.1 (BREW 3.1.5; U; en-us; LG; NetFront/3.5.1/WAP) Sprint LN240 MMP/2.0 Profile/MIDP-2.1 Configuration/CLDC-1.1'
    family: 'NetFront'
    major: '3'
    minor: '5'
    patch: '1'

  - user_agent_string: 'Mozilla/4.0 (Brew MP 1.0.2; U; en-us; Sanyo; NetFront/3.5.1/AMB) Sprint E4100'
    family: 'NetFront'
    major: '3'
    minor: '5'
    patch: '1'

  - user_agent_string: 'PantechP6010/JNUS11072011 BMP/1.0.2 DeviceId/141020 NetFront/4.1 OMC/1.5.3 Profile/MIDP-2.1 Configuration/CLDC-1.1'
    family: 'NetFront'
    major: '4'
    minor: '1'
    patch:

  - user_agent_string: 'NetFront/4.2 (BMP 1.0.4; U; en-us; LG; NetFront/4.2/AMB) Boost LG272 MMP/2.0 Profile/MIDP-2.1 Configuration/CLDC-1.1'
    family: 'NetFront'
    major: '4'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/5.0 (SymbianOS/9.4; U; Series60/5.0 Nokia5800d-1/21.0.025; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413'
    family: 'Nokia Browser'
    major: '7'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Symbian/3; Series60/5.2 NokiaN8-00/013.016; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 BrowserNG/7.2.8.10 3gpp-gba'
    family: 'Nokia Browser'
    major: '7'
    minor: '2'
    patch: '8'

  - user_agent_string: 'Mozilla/5.0 (Symbian/3; Series60/5.2 NokiaN8-00/012.002; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.0 Mobile Safari/533.4 3gpp-gba'
    family: 'Nokia Browser'
    major: '7'
    minor: '3'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Symbian/3; Series60/5.3 Nokia701/************; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.4.1.14 Mobile Safari/533.4 3gpp-gba'
    family: 'Nokia Browser'
    major: '7'
    minor: '4'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (MeeGo; NokiaN9) AppleWebKit/534.13 (KHTML, like Gecko) NokiaBrowser/8.5.0 Mobile Safari/534.13'
    family: 'Nokia Browser'
    major: '8'
    minor: '5'
    patch: '0'

  - user_agent_string: 'ALCATEL-OT510A/382 ObigoInternetBrowser/Q05A'
    family: 'Obigo'
    major:
    minor:
    patch:

  - user_agent_string: 'Huawei/1.0/0HuaweiG2800/WAP2.0/Obigo-Browser/Q03C MMS/Obigo-MMS/1.2'
    family: 'Obigo'
    major:
    minor:
    patch:

  - user_agent_string: 'PantechP7040/JLUS04042011 Browser/Obigo/Q05A OMC/1.5.3 Profile/MIDP-2.1 Configuration/CLDC-1.1'
    family: 'Obigo'
    major: '05'
    minor:
    patch:

  - user_agent_string: 'OneBrowser/3.0 (SAMSUNG-GT-S5253/S5253DDKJ2)'
    family: 'ONE Browser'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'OneBrowser/3.0 (NokiaC2-00/03.42)'
    family: 'ONE Browser'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'SAMSUNG-C3053/1.0 Openwave/6.2.3 Profile/MIDP-2.0 Configuration/CLDC-1.1 UP.Browser/6.2.3.3.c.1.101 (GUI) MMP/2.0'
    family: 'Openwave'
    major: '6'
    minor: '2'
    patch: '3'

  - user_agent_string: 'Opera/9.80 (VRE; Opera Mini/4.2/28.2794; U; en) Presto/2.8.119 Version/11.10'
    family: 'Opera Mini'
    major: '4'
    minor: '2'
    patch:

  - user_agent_string: 'Opera/9.80 (BREW; Opera Mini/5.1.191/27.2202; U; en) Presto/2.8.119 240X400 LG VN271'
    family: 'Opera Mini'
    major: '5'
    minor: '1'
    patch: '191'

  - user_agent_string: 'Opera/9.80 (Series 60; Opera Mini/6.24455/25.677; U; fr) Presto/2.5.25 Version/10.54'
    family: 'Opera Mini'
    major: '6'
    minor: '24455'
    patch:

  - user_agent_string: 'Opera/9.80 (BlackBerry; Opera Mini/7.0.31437/28.3030; U; en) Presto/2.8.119 Version/11.10'
    family: 'Opera Mini'
    major: '7'
    minor: '0'
    patch: '31437'

  - user_agent_string: 'Opera/9.80 (J2ME/MIDP; Opera Mini/9.80 (S60; SymbOS; Opera Mobi/23.348; U; en) Presto/2.5.25 Version/10.54'
    family: 'Opera Mini'
    major: '9'
    minor: '80'
    patch:

  - user_agent_string: 'Opera/9.80 (J2ME/MIDP; Opera Mini/9.80 (J2ME/22.478; U; en) Presto/2.5.25 Version/10.54'
    family: 'Opera Mini'
    major: '9'
    minor: '80'
    patch:

  - user_agent_string: 'Opera/9.80 (J2ME/MIDP; Opera Mini/9 (Compatible; MSIE:9.0; iPhone; BlackBerry9700; AppleWebKit/24.746; U; en) Presto/2.5.25 Version/10.54'
    family: 'Opera Mini'
    major: '9'
    minor:
    patch:

  - user_agent_string: 'Opera/9.80 (Android; Opera Mini/7.6.35766/35.5706; U; en) Presto/2.8.119 Version/11.10'
    family: 'Opera Mini'
    major: '7'
    minor: '6'
    patch: '35766'

  - user_agent_string: 'Opera/9.80 (S60; SymbOS; Opera Mobi/275; U; es-ES) Presto/2.4.13 Version/10.00'
    family: 'Opera Mobile'
    major: '10'
    minor: '00'
    patch:

  - user_agent_string: 'Opera/9.80 (Android 3.2; Linux; Opera Tablet/ADR-1106291546; U; en) Presto/2.8.149 Version/11.10'
    family: 'Opera Tablet'
    major: '11'
    minor: '10'
    patch:

  - user_agent_string: 'Opera/9.30 (Nintendo Wii; U; ; 3642; en)'
    family: 'Opera'
    major: '9'
    minor: '30'
    patch:

  - user_agent_string: 'Opera/9.50 (Nintendo DSi; Opera/507; U; en-US)'
    family: 'Opera'
    major: '9'
    minor: '50'
    patch:

  - user_agent_string: 'Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.5.24 Version/10.53'
    family: 'Opera'
    major: '10'
    minor: '53'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.20 Safari/537.36 OPR/15.0.1147.18 (Edition Next)'
    family: 'Opera'
    major: '15'
    minor: '0'
    patch: '1147'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android; 4.1.2; GT-I9100 Build/000000) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1234.12 Mobile Safari/537.22 OPR/************'
    family: 'Opera Mobile'
    major: '14'
    minor: '0'
    patch: '123'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.10 Safari/537.36 OPR/27.0.1689.22 (Edition developer)'
    family: 'Opera'
    major: '27'
    minor: '0'
    patch: '1689'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 7_0_3 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Coast/3.1.0.79792 Mobile/11B511 Safari/7534.48.3'
    family: 'Opera Coast'
    major: '3'
    minor: '1'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_1 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) OPiOS/8.0.1.80062 Mobile/11D201 Safari/9537.53'
    family: 'Opera Mini'
    major: '8'
    minor: '0'
    patch: '1'

  - user_agent_string: 'SomethingWeNeverKnewExisted'
    family: 'Other'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Series40; NokiaC2-03/07.48; Profile/MIDP-2.1 Configuration/CLDC-1.1) Gecko/20100401 S40OviBrowser/*******.33'
    family: 'Ovi Browser'
    major: '2'
    minor: '2'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Series40; NokiaX2-05/08.35; Profile/MIDP-2.1 Configuration/CLDC-1.1) Gecko/20100401 S40OviBrowser/********.14'
    family: 'Ovi Browser'
    major: '2'
    minor: '0'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 5.1; rv:2.0) Gecko/20110407 Firefox/4.0.3 PaleMoon/4.0.3'
    family: 'Pale Moon'
    major: '4'
    minor: '0'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64; rv:3.0) Goanna/20170207 PaleMoon/27.1.0'
    family: 'Pale Moon'
    major: '27'
    minor: '1'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64; rv:3.0) Gecko/20100101 Goanna/20170207 PaleMoon/27.1.0'
    family: 'Pale Moon'
    major: '27'
    minor: '1'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64; rv:45.9) Gecko/20100101 Goanna/3.0 Firefox/45.9 PaleMoon/27.1.0'
    family: 'Pale Moon'
    major: '27'
    minor: '1'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (LG-T500 AppleWebkit/531 Browser/Phantom/V2.0 Widget/LGMW/3.0 MMS/LG-MMS-V1.0/1.2 Java/ASVM/1.1 Profile/MIDP-2.1 Configuration/CLDC-1.1)'
    family: 'Phantom Browser'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Pingdom.com_bot_version_1.4_(http://www.pingdom.com/)'
    family: 'PingdomBot'
    major: '1'
    minor: '4'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Unknown; Linux x86_64) AppleWebKit/534.34 (KHTML, like Gecko) PingdomTMS/0.8.5 Safari/534.34'
    family: 'PingdomBot'
    major: '0'
    minor: '8'
    patch: '5'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/61.0.3163.100 Chrome/61.0.3163.100 Safari/537.36 PingdomPageSpeed/1.0 (pingbot/2.0; +http://www.pingdom.com/)'
    family: 'PingdomBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'NewRelicPinger/1.0 (1025794)'
    family: 'NewRelicPingerBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/98 Safari/537.4 (StatusCake)'
    family: 'StatusCakeBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/3.0 (Planetweb/2.100 JS SSL US; Dreamcast US)'
    family: 'Planetweb'
    major: '2'
    minor: '100'
    patch:

  - user_agent_string: 'PyAMF/0.6.1'
    family: 'PyAMF'
    major: '0'
    minor: '6'
    patch: '1'

  - user_agent_string: 'python-requests/0.14 CPython/2.6 Linux/2.6-43-server'
    family: 'Python Requests'
    major: '0'
    minor: '14'
    patch:

  - user_agent_string: 'Mozilla/5.0 (X11i; Linux; C) AppleWebKikt/533.3 (KHTML, like Gecko) QtCarBrowser Safari/533.3'
    family: 'QtCarBrowser'
    major: '1'
    minor:
    patch:

  - user_agent_string: 'Rackspace Monitoring/1.1 (https://monitoring.api.rackspacecloud.com)'
    family: 'RackspaceBot'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (X11; U; BSD Four; en-US) AppleWebKit/533.3 (KHTML, like Gecko) rekonq Safari/533.3'
    family: 'Rekonq'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (X11; Linux i686) AppleWebKit/534.34 (KHTML, like Gecko) rekonq/1.0 Safari/534.34'
    family: 'Rekonq'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) AppleWebKit/534.3 (KHTML, like Gecko) RockMelt/0.8.34.841 Chrome/6.0.472.63 Safari/534.3'
    family: 'RockMelt'
    major: '0'
    minor: '8'
    patch: '34'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; U; PPC Mac OS X; en-us) AppleWebKit/418.8 (KHTML, like Gecko) Safari/419.3'
    family: 'Safari'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_5; en-us) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5'
    family: 'Safari'
    major: '5'
    minor: '0'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_5_7; en-us) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Safari/530.17 Skyfire/2.0'
    family: 'Skyfire'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.21 (KHTML, like Gecko) Snowshoe/1.0.0 Safari/537.21'
    family: 'Snowshoe'
    major: '1'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US; rv:1.9.2.17) Gecko/20110414 Thunderbird/3.1.10 ThunderBrowse/3.3.5'
    family: 'ThunderBrowse'
    major: '3'
    minor: '3'
    patch: '5'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64; rv:24.0) Gecko/20100101 Thunderbird/24.2.0 Lightning/2.6.4'
    family: 'Thunderbird'
    major: '24'
    minor: '2'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64; rv:24.0) Gecko/20100101 Thunderbird/24.2.0'
    family: 'Thunderbird'
    major: '24'
    minor: '2'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; rv:45.0) Gecko/20100101 Thunderbird/45.0'
    family: 'Thunderbird'
    major: '45'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.2; WOW64; rv:7.0.1) Gecko/20151105 Postbox/4.0.8'
    family: 'Postbox'
    major: '4'
    minor: '0'
    patch: '8'

  - user_agent_string: 'Mozilla/4.0 (compatible; Lotus-Notes/6.0; Windows-NT)'
    family: 'Lotus Notes'
    major: '6'
    minor: '0'
    patch:

  - user_agent_string: 'Superhuman'
    family: 'Superhuman'
    major:
    minor:
    patch:

  - user_agent_string: 'YahooMobileMail/1.0 (Android Mail; 1.3.10) (supersonic;HTC;PC36100;2.3.5/GRJ90)'
    family: 'YahooMobileMail'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Barca/2.8.2'
    family: 'Barca'
    major: '2'
    minor: '8'
    patch: '2'

  - user_agent_string: 'BarcaPro/1.4.12'
    family: 'Barca'
    major: '1'
    minor: '4'
    patch: '12'

  - user_agent_string: 'The Bat! 4.0.0.22'
    family: 'The Bat!'
    major: '4'
    minor: '0'
    patch: '0'

  - user_agent_string: 'MailBar/1.3.2 (Mac OS X Version 10.11.1 (Build 15B42))'
    family: 'MailBar'
    major: '1'
    minor: '3'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/534.34 (KHTML, like Gecko) kmail2/4.14.2 Safari/534.34'
    family: 'kmail2'
    major: '4'
    minor: '14'
    patch: '2'

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; .NET CLR 1.1.4322; .NET CLR 2.0.50727; MSOffice 12)'
    family: 'Outlook'
    major: '2007'
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; Trident/4.0; InfoPath.2; MSOffice 14)'
    family: 'Outlook'
    major: '2010'
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/6.0; Microsoft Outlook 15.0.4420)'
    family: 'Outlook'
    major: '2013'
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_2) AppleWebKit/536.26.14 (KHTML, like Gecko)'
    family: 'Apple Mail'
    major: '536'
    minor: '26'
    patch: '14'

  - user_agent_string: 'Airmail 1.0 rv:148 (Macintosh; Mac OS X 10.8.3; en_BE)'
    family: 'Airmail'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Airmail 1.0.6 rv:196 (Macintosh; Mac OS X 10.8.4; en_GB)'
    family: 'Airmail'
    major: '1'
    minor: '0'
    patch: '6'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.220 Whale/******** Safari/537.36'
    family: 'Whale'
    major: '1'
    minor: '3'
    patch: '45'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU OS 10_2_1 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) 1Password/6.4.5 (like Version/10.2.1 Mobile/14D27 Safari/600.1.4)'
    family: '1Password'
    major: '6'
    minor: '4'
    patch: '5'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.220 Whale/******** Safari/537.36'
    family: 'Whale'
    major: '1'
    minor: '3'
    patch: '50'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_2_6 like Mac OS X) AppleWebKit/604.5.6 (KHTML, like Gecko) Whale/0.9.1.679 Mobile/15D100 Safari/604.5.6'
    family: 'Whale'
    major: '0'
    minor: '9'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 6.0.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Whale/******* Mobile Safari/537.36'
    family: 'Whale'
    major: '0'
    minor: '9'
    patch: '5'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 7.1.2; Nexus 6P Build/WHALE) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.220 Whale/******** Mobile Safari/537.36 sidebar webpanel'
    family: 'Whale'
    major: '1'
    minor: '3'
    patch: '50'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 7.1.2; Nexus 6P Build/WHALE) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.220 Whale/******** Mobile Safari/537.36'
    family: 'Whale'
    major: '1'
    minor: '3'
    patch: '50'

  - user_agent_string: 'J2ME/UCWEB7.0.3.45/139/7682'
    family: 'UC Browser'
    major: '7'
    minor: '0'
    patch: '3'

  - user_agent_string: 'NOKIA6120c/UC Browser7.4.0.65/28/352'
    family: 'UC Browser'
    major: '7'
    minor: '4'
    patch: '0'

  - user_agent_string: 'UCWEB/3.0 (iPhone; CPU OS_6; en-US)AppleWebKit/534.1 U3/3.0.0 Mobile'
    family: 'UC Browser'
    major: '3'
    minor: '0'
    patch: '0'

  - user_agent_string: 'UCWEB/2.0 (Linux; U; Opera Mini/7.1.32052/30.2697; en-US; GT-S5302) U2/1.0.0 UCBrowser/9.3.0.440 Mobile'
    family: 'UC Browser'
    major: '9'
    minor: '3'
    patch: '0'

  - user_agent_string: 'IUC(U;iOS 5.1.1;Zh-cn;320*480;)/UCWEB7.9.0.94/41/997'
    family: 'UC Browser'
    major: '7'
    minor: '9'
    patch: '0'

  - user_agent_string: 'Nokia5320di/UCWEB8.0.3.99/28/999'
    family: 'UC Browser'
    major: '8'
    minor: '0'
    patch: '3'

  - user_agent_string: 'Nokia201/2.0 (11.21) Profile/MIDP-2.1 Configuration/CLDC-1.1 Mozilla/5.0 (Java; U; en-us; nokia201) UCBrowser8.3.0.154/70/355/UCWEB Mobile'
    family: 'UC Browser'
    major: '8'
    minor: '3'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (S60V5; U; en-us; NokiaC5-03) AppleWebKit/530.13 (KHTML, like Gecko) UCBrowser/*********/50/352/UCWEB Mobile'
    family: 'UC Browser'
    major: '8'
    minor: '7'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 UBrowser/5.7.14488.1025 Safari/537.36'
    family: 'UC Browser'
    major: '5'
    minor: '7'
    patch: '14488'

  - user_agent_string: 'Alcatel-OH5/1.0 UP.Browser/6.1.0.7.7 (GUI) MMP/1.0'
    family: 'UP.Browser'
    major: '6'
    minor: '1'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/534+ (KHTML, like Gecko) Version/5.1.1 Safari/534.51.22'
    family: 'WebKit Nightly'
    major: '534'
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.1+ (KHTML, like Gecko) Version/5.1.1 Safari/534.51.22'
    family: 'WebKit Nightly'
    major: '537'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_4) AppleWebKit/537.8+ (KHTML, like Gecko) Version/6.0 Safari/536.25'
    family: 'WebKit Nightly'
    major: '537'
    minor: '8'
    patch:

  - user_agent_string: 'Mozilla/5.0 (hp-tablet; Linux; hpwOS/3.0.0; U; en-US) AppleWebKit/534.6 (KHTML, like Gecko) wOSBrowser/233.58 Safari/534.6 TouchPad/1.0'
    family: 'webOS Browser'
    major: '3'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (webOS/1.2; U; en-US) AppleWebKit/525.27.1 (KHTML, like Gecko) Version/1.0 Safari/525.27.1 Desktop/1.0'
    family: 'webOS Browser'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/5.0 (hp-tablet; Linux; hpwOS/3.0.5; U; en-US) AppleWebKit/534.6 (KHTML, like Gecko) wOSBrowser/234.83 Safari/534.6 TouchPad/1.0'
    family: 'webOS Browser'
    major: '3'
    minor: '0'
    patch: '5'

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux i686; nl-NL) AppleWebKit/534.3 (KHTML, like Gecko) WeTab-Browser Safari/534.3'
    family: 'WeTab'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1104.222 YaBrowser/1.5.1104.222 Safari/537.4'
    family: 'Yandex Browser'
    major: '1'
    minor: '5'
    patch: '1104'

  - user_agent_string: 'Mozilla/5.0 YottaaMonitor;'
    family: 'YottaaMonitor'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X) AppleWebKit/534.34 (KHTML, like Gecko) PhantomJS/1.6.0 Safari/534.34'
    family: 'PhantomJS'
    major: '1'
    minor: '6'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (IE 11.0; Windows NT 6.3; Trident/7.0; .NET4.0E; .NET4.0C; rv:11.0) like Gecko'
    family: 'IE'
    major: '11'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; MSIE 9.0; AOL 9.7; AOLBuild 4343.19; Windows NT 6.1; WOW64; Trident/5.0; FunWebProducts)'
    family: 'AOL'
    major: '9'
    minor: '7'
    patch: '4343'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.3; Win64; x64; Trident/7.0; rv:11.0) like Gecko'
    family: 'IE'
    major: '11'
    minor: '0'
    patch:

  - user_agent_string: 'HbbTV/1.1.1 (;Samsung;SmartTV2013;T-FXPDEUC-1102.2;;) WebKit'
    family: 'HbbTV'
    major: '1'
    minor: '1'
    patch: '1'

  - user_agent_string: 'HbbTV/1.2.1 (;Panasonic;VIERA 2013;3.672;4101-0003 0002-0000;)'
    family: 'HbbTV'
    major: '1'
    minor: '2'
    patch: '1'

  - user_agent_string: 'HbbTV/1.1.1 (;;;;;) firetv-firefox-plugin 1.1.20'
    family: 'HbbTV'
    major: '1'
    minor: '1'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; Win64; x64; Trident/7.0; rv:11.0) like Gecko/20100101 Firefox/12.0'
    family: 'IE'
    major: '11'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.89 Vivaldi/1.0.83.38 Safari/537.36'
    family: 'Vivaldi'
    major: '1'
    minor: '0'
    patch: '83'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.135 Safari/537.36 Edge/12.9600'
    family: 'Edge'
    major: '12'
    minor: '9600'
    patch:

  - user_agent_string: 'Mozilla/4.0 (Vodafone/1.0/LG-GU280/v10a Browser/Obigo-Q7.3 MMS/LG-MMS-V1.0/1.2 Java/ASVM/1.1 Profile/MIDP-2.1 Configuration/CLDC-1.1)'
    family: 'Obigo'
    major: '7'
    minor: '3'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 6.0; Windows CE; IEMobile 6.12) Vodafone/1.0/HTC_Elf/1.11.164.2'
    family: 'IE Mobile'
    major: '6'
    minor: '12'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 6.0; Windows CE; IEMobile 8.12; MSIEMobile 6.0) Vodafone/1.0/HTC_HD2/1.44.162.6 (70494)'
    family: 'IE Mobile'
    major: '8'
    minor: '12'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 920)'
    family: 'IE Mobile'
    major: '11'
    minor: '0'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 Vodafone/1.0/SamsungSGHi560/I560AEHB1 Profile/MIDP-2.0 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413'
    family: 'Nokia OSS Browser'
    major: '3'
    minor: '1'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Vodafone/1.0/LG-KC910/V08h Browser/Teleca-Q7.1 MMS/LG-MMS-V1.0/1.2 MediaPlayer/LGPlayer/1.0 Java/ASVM/1.1 Profile/MIDP-2.1 Configuration/CLDC-1.1)'
    family: 'Teleca Browser'
    major:
    minor:
    patch:
    patch_minor:

  - user_agent_string: 'Vodafone/1.0/0Vodafone715/B116 Browser/Obigo-Browser/Q04A MMS/Obigo-MMS/Q04A SyncML/HW-SyncML/1.0 Java/QVM/4.1 Profile/MIDP-2.0 Configuration/CLDC-1.1'
    family: 'Obigo'
    major:
    minor:
    patch:
    patch_minor:

  - user_agent_string: 'Opera/9.80 (Android 1.6; Linux; Opera Mobi/ADR-1107051709; U; en) Presto/2.8.149 Version/11.10'
    family: 'Opera Mobile'
    major: '11'
    minor: '10'
    patch:
    patch_minor:

  - user_agent_string: 'Opera/9.80 (Windows Mobile; WCE; Opera Mobi/WMD-50430; U; en) Presto/2.4.13 Version/10.00'
    family: 'Opera Mobile'
    major: '10'
    minor: '00'
    patch:
    patch_minor:

  - user_agent_string: '(Opera) Vodafone/1.0/HPiPAQDataMessenger/1.00.00 Browser/Opera/9.5 Profile/MIDP-2.0 Configuration/CLDC-1.1 Opera/9.5 (Microsoft Windows; PPC; Opera Mobi/15142; U; en)'
    family: 'Opera Mobile'
    major: '9'
    minor: '5'
    patch:
    patch_minor:

  - user_agent_string: 'HTC MAX 4G Opera/9.5 (Microsoft Windows; PPC; Opera Mobi/1409; U; ru)'
    family: 'Opera Mobile'
    major: '9'
    minor: '5'
    patch:
    patch_minor:

  - user_agent_string: 'iBrowser/3.0/Mozilla/5.0 (Linux; U; Android 2.3.6; yy-yy; Karbonn A2 Build/GRK39F) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1'
    family: 'Android'
    major: '2'
    minor: '3'
    patch: '6'
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Series40; NokiaC3-01/05.60; Profile/MIDP-2.1 Configuration/CLDC-1.1) Gecko/20100401 S40OviBrowser/*******.31'
    family: 'Ovi Browser'
    major: '2'
    minor: '2'
    patch: '0'
    patch_minor:

  - user_agent_string: 'iRAPP/1.16.0 NokiaN95_8GB/31.0.015; Series60/3.1 Profile/MIDP-2.0 Configuration/CLDC-1.'
    family: 'iRAPP'
    major: '1'
    minor: '16'
    patch:
    patch_minor:

  - user_agent_string: 'iRAPP/1.3.0 Nokia5230/50.0.101 Series60/5.0 Profile/MIDP-2.1 Configuration/CLDC-1.1 3gpp-gba'
    family: 'iRAPP'
    major: '1'
    minor: '3'
    patch:
    patch_minor:

  - user_agent_string: 'iRAPP/3.5.0 NokiaN8-00/************ Series60/5.3 Profile/MIDP-2.1 Configuration/CLDC-1.1 3gpp-gba'
    family: 'iRAPP'
    major: '3'
    minor: '5'
    patch:
    patch_minor:

  - user_agent_string: 'LG-GD710/V10f; Mozilla/5.0 (Profile/MIDP-2.0 Configuration/CLDC-1.1; Opera Mini/att/4.2.14812; U; en) Opera 9.50'
    family: 'Opera Mini'
    major: '4'
    minor: '2'
    patch: '14812'
    patch_minor:

  - user_agent_string: 'SAMSUNG-SGH-A897/A897UCJC1; Mozilla/5.0 (Profile/MIDP-2.0 Configuration/CLDC-1.1; Opera Mini/att/4.2.15304; U; fr-US) Opera 9.50'
    family: 'Opera Mini'
    major: '4'
    minor: '2'
    patch: '15304'
    patch_minor:

  - user_agent_string: 'MQQBrowser/39 Mozilla/5.0 (iPhone 4S; CPU iPhone OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10A523 Safari/7534.48.3'
    family: 'QQ Browser Mobile'
    major: '39'
    minor:
    patch:
    patch_minor:

  - user_agent_string: 'MQQBrowser/391 Mozilla/5.0 (iPhone 4S; CPU iPhone OS 5_0 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9A334 Safari/7534.48.3'
    family: 'QQ Browser Mobile'
    major: '391'
    minor:
    patch:
    patch_minor:

  - user_agent_string: 'MQQBrowser/20 (Linux; U; 2.3.3; en-us; HTC Desire S Build/GRI40;480*800)'
    family: 'QQ Browser Mobile'
    major: '20'
    minor:
    patch:
    patch_minor:

  - user_agent_string: 'MQQBrowser/Mini2.8 (ZTE-X990/X990_V2_Z12_ESFR_D18F100)'
    family: 'QQ Browser Mini'
    major: '2'
    minor: '8'
    patch:
    patch_minor:

  - user_agent_string: 'MQQBrowser/Mini3.1 (SonyEricssonJ105i/R1HA035)'
    family: 'QQ Browser Mini'
    major: '3'
    minor: '1'
    patch:
    patch_minor:

  - user_agent_string: 'QQBrowser/14 (Linux; U; 2.2.2; en-us; Motorola XT316 BUILD/FRG83G) Mobile/0050'
    family: 'QQ Browser'
    major: '14'
    minor:
    patch:
    patch_minor:

  - user_agent_string: 'Dolphin 6.5.1 (iPad; iPhone OS 6.1.3; de_DE)'
    family: 'Dolphin'
    major: '6'
    minor: '5'
    patch: '1'
    patch_minor:

  - user_agent_string: 'Dolphin 7.4 (iPhone; iPhone OS 7.0.2; de_DE)'
    family: 'Dolphin'
    major: '7'
    minor: '4'
    patch:
    patch_minor:

  - user_agent_string: 'Dolphin 7.5.1 (iPhone; iPhone OS 7.0.3; de_DE)'
    family: 'Dolphin'
    major: '7'
    minor: '5'
    patch: '1'
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 2.1-update1; zh-cn; XT701 Build/STCU_31.05.4) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17 DolphinHDCN/7.0.1'
    family: 'Dolphin'
    major: '7'
    minor: '0'
    patch: '1'
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 2.2; en-gb; GT-P1000 Build/FROYO) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 DolphinHDCN/6.3.1'
    family: 'Dolphin'
    major: '6'
    minor: '3'
    patch: '1'
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; Nexus 7 Build/JRO03D) AppleWebKit/534.30 (KHTML, like Gecko) Dolphin/INT-1.0.4 Mobile Safari/534.30'
    family: 'Dolphin'
    major: '1'
    minor: '0'
    patch: '4'
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_3; en-us) AppleWebKit/533.16 (KHTML, like Gecko) Version/5.0 Safari/533.16 DolphinHDCN/6.1.0'
    family: 'Dolphin'
    major: '6'
    minor: '1'
    patch: '0'
    patch_minor:

  - user_agent_string: 'Mozilla/5.0(miniGUI/3.x; U; Linux i686; en-US) AppleWebKit/534.26 (KHTML, like Gecko) mDolphin/3.0 chrome/10.0 Safria/534.26'
    family: 'mDolphin'
    major: '3'
    minor: '0'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0(miniGUI/3.x; U; Linux i686; en; ) AppleWebKit/533.9.0 (KHTML, like Gecko) mDolphin/3.0.0 Safria/533.9.0'
    family: 'mDolphin'
    major: '3'
    minor: '0'
    patch: '0'
    patch_minor:

  - user_agent_string: 'CFNetwork, iPhone OS 5.1.1, iPhone4,1'
    family: 'CFNetwork'
    major:
    minor:
    patch:
    patch_minor:

  - user_agent_string: 'CFNetwork, iPhone OS 7.0.4, iPhone5,2'
    family: 'CFNetwork'
    major:
    minor:
    patch:
    patch_minor:

  - user_agent_string: 'CFNetwork, iPhone OS 7.0, iPhone4,1'
    family: 'CFNetwork'
    major:
    minor:
    patch:
    patch_minor:

  - user_agent_string: 'Safari5530.17 CFNetwork/438.12 Darwin/9.7.0 (i386) (Macmini2,1)'
    family: 'CFNetwork'
    major: '438'
    minor: '12'
    patch:
    patch_minor:

  - user_agent_string: 'Safari/6533.18.5 CFNetwork/454.9.8 Darwin/10.4.0 (i386) (MacBookPro7,1)'
    family: 'Safari'
    major: '6533'
    minor: '18'
    patch: '5'
    patch_minor:

  - user_agent_string: 'Safari/7536.30.1 CFNetwork/520.5.1 Darwin/11.4.2 (i386) (MacBook3,1)'
    family: 'Safari'
    major: '7536'
    minor: '30'
    patch: '1'
    patch_minor:

  - user_agent_string: 'Reader Notifier/5 CFNetwork/596.3.3 Darwin/12.3.0 (x86_64) (MacBookPro7,1)'
    family: 'Reader Notifier'
    major: '5'
    minor:
    patch:
    patch_minor:

  - user_agent_string: 'Safari/9537.71 CFNetwork/673.0.2 Darwin/13.0.1 (x86_64) (MacBookPro11,1)'
    family: 'Safari'
    major: '9537'
    minor: '71'
    patch:
    patch_minor:

  - user_agent_string: 'DEPoker-iPad/1.0.2 CFNetwork/548.1.4 Darwin/11.0.0'
    family: 'DEPoker'
    major: '1'
    minor: '0'
    patch: '2'
    patch_minor:

  - user_agent_string: 'JDSports-iPad/1.1 CFNetwork/672.0.8 Darwin/14.0.0'
    family: 'JDSports'
    major: '1'
    minor: '1'
    patch:
    patch_minor:

  - user_agent_string: 'AngryBirdsBlack-iPhone/1.1.0 CFNetwork/548.1.4 Darwin/11.0.0'
    family: 'AngryBirdsBlack'
    major: '1'
    minor: '1'
    patch: '0'
    patch_minor:

  - user_agent_string: 'Bing for iPad/1.1.2 CFNetwork/485.13.9 Darwin/11.0.0'
    family: 'Bing for iPad'
    major: '1'
    minor: '1'
    patch: '2'
    patch_minor:

  - user_agent_string: 'NightstandPaid-iPad/1.3.1 CFNetwork/548.1.4 Darwin/11.0.0'
    family: 'NightstandPaid'
    major: '1'
    minor: '3'
    patch: '1'
    patch_minor:

  - user_agent_string: 'Glo-De-iPad/1.4.7 CFNetwork/672.0.2 Darwin/14.0.0'
    family: 'Glo-De'
    major: '1'
    minor: '4'
    patch: '7'
    patch_minor:

  - user_agent_string: 'Island for iPhone/1.95 CFNetwork/672.0.2 Darwin/14.0.0'
    family: 'Island for iPhone'
    major: '1'
    minor: '95'
    patch:
    patch_minor:

  - user_agent_string: 'WormsiPhone-iPad/2.3 CFNetwork/548.1.4 Darwin/11.0.0'
    family: 'WormsiPhone'
    major: '2'
    minor: '3'
    patch:
    patch_minor:

  - user_agent_string: 'Rummy LITE iPad/2.3.0 CFNetwork/609.1.4 Darwin/13.0.0'
    family: 'Rummy LITE iPad'
    major: '2'
    minor: '3'
    patch: '0'
    patch_minor:

  - user_agent_string: 'MobileRSSFree-iPad/3.1 CFNetwork/467.12 Darwin/10.3.1'
    family: 'MobileRSSFree'
    major: '3'
    minor: '1'
    patch:
    patch_minor:

  - user_agent_string: 'MobileRSSFree-iPad/3.1.4 CFNetwork/485.13.9 Darwin/11.0.0'
    family: 'MobileRSSFree'
    major: '3'
    minor: '1'
    patch: '4'
    patch_minor:

  - user_agent_string: 'babbelIndonesian-iPad/4.0.1 CFNetwork/672.0.8 Darwin/14.0.0'
    family: 'babbelIndonesian'
    major: '4'
    minor: '0'
    patch: '1'
    patch_minor:

  - user_agent_string: 'WeltMobile-iPad/4.2 CFNetwork/609.1.4 Darwin/13.0.0'
    family: 'WeltMobile'
    major: '4'
    minor: '2'
    patch:
    patch_minor:

  - user_agent_string: 'IMPlusFull-iPad/7.9.1 CFNetwork/548.0.4 Darwin/11.0.0'
    family: 'IMPlusFull'
    major: '7'
    minor: '9'
    patch: '1'
    patch_minor:

  - user_agent_string: 'Cooliris/1.3 CFNetwork/342.1 Darwin/9.4.1'
    family: 'Cooliris'
    major: '1'
    minor: '3'
    patch:
    patch_minor:

  - user_agent_string: 'Poof/1.0 CFNetwork/485.12.7 Darwin/10.4.0'
    family: 'Poof'
    major: '1'
    minor: '0'
    patch:
    patch_minor:

  - user_agent_string: 'Parking Mania Free/******* CFNetwork/548.0.4 Darwin/11.0.0'
    family: 'Parking Mania Free'
    major: '1'
    minor: '9'
    patch: '5'
    patch_minor: '0'

  - user_agent_string: 'Planet Boing!/1.4.8 CFNetwork/609.1.4 Darwin/13.0.0'
    family: 'Planet Boing!'
    major: '1'
    minor: '4'
    patch: '8'
    patch_minor:

  - user_agent_string: 'PlayTube/1.7 CFNetwork/672.0.2 Darwin/14.0.0'
    family: 'PlayTube'
    major: '1'
    minor: '7'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 2.2.1; es-us) AppleWebKit/534.12 (KHTML, like Gecko) Puffin/1.3.2913S Mobile Safari/534.12'
    family: 'Puffin'
    major: '1'
    minor: '3'
    patch: '2913'
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 3.2.1; es-es) AppleWebKit/534.35 (KHTML, like Gecko) Chrome/11.0.696.65 Safari/534.35 Puffin/2.0.6440M Mobile'
    family: 'Puffin'
    major: '2'
    minor: '0'
    patch: '6440'
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 3.2; en-gb) AppleWebKit/534.35 (KHTML, like Gecko) Chrome/11.0.696.65 Safari/534.35 Puffin/2.0.5932M Mobile'
    family: 'Puffin'
    major: '2'
    minor: '0'
    patch: '5932'
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.1.1; en-us) AppleWebKit/534.35 (KHTML, like Gecko)  Chrome/11.0.696.65 Safari/534.35 Puffin/2.9909AT Mobile'
    family: 'Puffin'
    major: '2'
    minor: '9909'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.2.2; de-de) AppleWebKit/534.35 (KHTML, like Gecko)  Chrome/11.0.696.65 Safari/534.35 Puffin/3.11558AT Mobile'
    family: 'Puffin'
    major: '3'
    minor: '11558'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.2.2; en-us) AppleWebKit/534.35 (KHTML, like Gecko)  Chrome/11.0.696.65 Safari/534.35 Puffin/3.11558AP Mobile'
    family: 'Puffin'
    major: '3'
    minor: '11558'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux i686; th-TH@calendar=gregorian) AppleWebKit/534.12 (KHTML, like Gecko) Puffin/1.3.2665MS Safari/534.12'
    family: 'Puffin'
    major: '1'
    minor: '3'
    patch: '2665'
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux x86_64; ar-AE) AppleWebKit/534.35 (KHTML, like Gecko)  Chrome/11.0.696.65 Safari/534.35 Puffin/3.10990IT'
    family: 'Puffin'
    major: '3'
    minor: '10990'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux x86_64; ar-SA) AppleWebKit/534.35 (KHTML, like Gecko)  Chrome/11.0.696.65 Safari/534.35 Puffin/3.11546IP'
    family: 'Puffin'
    major: '3'
    minor: '11546'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux x86_64; ar-ae) AppleWebKit/534.35 (KHTML, like Gecko)  Chrome/11.0.696.65 Safari/534.35 Puffin/2.10977AP'
    family: 'Puffin'
    major: '2'
    minor: '10977'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux x86_64; de-at) AppleWebKit/534.35 (KHTML, like Gecko)  Chrome/11.0.696.65 Safari/534.35 Puffin/2.10977AT'
    family: 'Puffin'
    major: '2'
    minor: '10977'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (iPad; U; CPU OS 7_0_6 like Mac OS X; de-DE) AppleWebKit/534.35 (KHTML, like Gecko)  Chrome/11.0.696.65 Safari/534.35 Puffin/3.11558IT Mobile'
    family: 'Puffin'
    major: '3'
    minor: '11558'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (iPhone; U; CPU iPhone OS 7_0_6 like Mac OS X; de-DE) AppleWebKit/534.35 (KHTML, like Gecko)  Chrome/11.0.696.65 Safari/534.35 Puffin/3.11558IP Mobile'
    family: 'Puffin'
    major: '3'
    minor: '11558'
    patch:
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (iPod; U; CPU iPhone OS 4_3_5 like Mac OS X; en-US) AppleWebKit/534.12 (KHTML, like Gecko) Puffin/1.3.3102MS Mobile Safari/534.12'
    family: 'Puffin'
    major: '1'
    minor: '3'
    patch: '3102'
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (iPod; U; CPU iPhone OS 5_1_1 like Mac OS X; de-DE) AppleWebKit/534.35 (KHTML, like Gecko)  Chrome/11.0.696.65 Safari/534.35 Puffin/3.9174IP Mobile'
    family: 'Puffin'
    major: '3'
    minor: '9174'
    patch:
    patch_minor:

  - user_agent_string: 'MacAppStore/2.0 (Macintosh; OS X 10.10.2; 14C81f) AppleWebKit/0600.3.10.2'
    family: 'MacAppStore'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'iTunes/12.0.1 (Macintosh; OS X 10.9.2) AppleWebKit/537.74.9'
    family: 'iTunes'
    major: '12'
    minor: '0'
    patch: '1'

  - user_agent_string: 'Ant/Ant-Nutch-1.1 (Ant Nutch Crawler; http://www.ant.com; <EMAIL>)'
    family: 'Ant-Nutch'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'Axtaris Web Crawler/Axtaris-1.0.1 (Hello from Axtaris.org | Web-Crawler; http://axtaris.org; crawler at axtaris dot org)'
    family: 'Axtaris'
    major: '1'
    minor: '0'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; rv:11.0) like Gecko PTST/1.0'
    family: 'WebPageTest.org bot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.0 Safari/537.36 PTST/1.0'
    family: 'WebPageTest.org bot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 6.0.1; Moto G (4) Build/MPJ24.139-64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.146 Mobile Safari/537.36 PTST/180521.140508'
    family: 'WebPageTest.org bot'
    major: '180521'
    minor: '140508'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 6.0.1; Moto G (4) Build/MPJ24.139-64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Mobile Safari/537.36 PTST/391'
    family: 'WebPageTest.org bot'
    major: '391'
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (X11; Datanyze; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Safari/537.36'
    family: 'Datanyze'
    major:
    minor:
    patch:

  - user_agent_string: 'CazoodleBot/CazoodleBot-0.1 (CazoodleBot Crawler; http://www.cazoodle.com/cazoodlebot; <EMAIL>)'
    family: 'CazoodleBot'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Isara-Search/Isara-1.0 (A non-profit web crawler operated by a charity organization.; www.isara.org; <EMAIL>)'
    family: 'Isara'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'noobot Spider/Noobot-1.2 (noobot-bot; http://www.noobot.fr)'
    family: 'Noobot'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'asked/Nutch-0.8 (web crawler; http://asked.jp; epicurus at gmail dot com)'
    family: 'Nutch'
    major: '0'
    minor: '8'
    patch:

  - user_agent_string: 'nutch crawler/Nutch-2.2'
    family: 'Nutch'
    major: '2'
    minor: '2'
    patch:

  - user_agent_string: 'Spider/Nutch-2.3-SNAPSHOT (Webcrawler)'
    family: 'Nutch'
    major: '2'
    minor: '3'
    patch:

  - user_agent_string: 'SheenBot/SheenBot-1.0.4 (Sheen web crawler)'
    family: 'SheenBot'
    major: '1'
    minor: '0'
    patch: '4'

  - user_agent_string: 'SaladSpoon/ShopSalad 1.0 (Search Engine crawler for ShopSalad.com; http://shopsalad.com/en/partners.html; crawler AT shopsalad.com)'
    family: 'ShopSalad'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'TailsweepBlogCrawler/Tailsweep-2.9-SNAPSHOT (http://www.tailsweep.com/; bot at [tailsweep] dot com)'
    family: 'Tailsweep'
    major: '2'
    minor: '9'
    patch:

  - user_agent_string: 'SAE/fetchurl-22wy0njxnm WordPress/3.2.1; http://1.webfront.sinaapp.com'
    family: 'fetchurl'
    major: '22'
    minor:
    patch:

  - user_agent_string: 'RedBot/redbot-1.0 (Rediff.com Crawler; redbot at rediff dot com)'
    family: 'redbot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'IS Alpha/zcspider-0.1'
    family: 'zcspider'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; 008/0.85; http://www.80legs.com/webcrawler.html) Gecko/2008032620'
    family: '008'
    major: '0'
    minor: '85'
    patch:

  - user_agent_string: 'Altresium/4.6 (+http://www.altresium.com/bot.html)'
    family: 'Altresium'
    major: '4'
    minor: '6'
    patch:

  - user_agent_string: 'Argus/1.1 (Nutch; http://www.simpy.com/bot.html; feedback at simpy dot com)'
    family: 'Argus'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'Argus/2.8.65 CFNetwork/609 Darwin/13.0.0'
    family: 'Argus'
    major: '2'
    minor: '8'
    patch: '65'

  - user_agent_string: 'DoCoMo/2.0 P05A(c100;TB;W24H15) (compatible; BaiduMobaider/1.0;  http://www.baidu.jp/spider/)'
    family: 'BaiduMobaider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'BoardReader/1.0 (http://boardreader.com/info/robots.htm)-Machine1'
    family: 'BoardReader'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'DNSGroup/0.1 (DNS Group Crawler; http://www.dnsgroup.com/; <EMAIL>)'
    family: 'DNSGroup'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'DataparkSearch/4.35-02122005 ( http://www.dataparksearch.org/)'
    family: 'DataparkSearch'
    major: '4'
    minor: '35'
    patch:

  - user_agent_string: 'EDI/1.6.0 (Edacious & Intelligent Web Crawler)'
    family: 'EDI'
    major: '1'
    minor: '6'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (compatible; Goodzer/2.0; <EMAIL>)'
    family: 'Goodzer'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Grub/2.0 (Grub.org crawler; http://www.grub.org/; <EMAIL>)'
    family: 'Grub'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'INGRID/2.0 (http://spsearch.ilse.nl/; Startpagina dochter links spider)'
    family: 'INGRID'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'INGRID/3.0 MT (<EMAIL>; http://aanmelden.ilse.nl/?aanmeld_mode=webhints)'
    family: 'INGRID'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Infohelfer/1.2.0; +http://www.infohelfer.de/crawler.php)'
    family: 'Infohelfer'
    major: '1'
    minor: '2'
    patch: '0'

  - user_agent_string: 'LOOQ/0.1 alfa (LOOQ Crawler for european sites; http://looq.eu; root (at) looq dot eu)'
    family: 'LOOQ'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'LinkedInBot/1.0 (compatible; Mozilla/5.0; Jakarta Commons-HttpClient/3.1  http://www.linkedin.com)'
    family: 'LinkedInBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; PathDefender/1.0; +http://www.pathdefender.com/help/crawler)'
    family: 'PathDefender'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Peew/1.0; http://www.peew.de/crawler/)'
    family: 'Peew'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'PostPost/1.0 ( http://postpo.st/crawlers)'
    family: 'PostPost'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Steeler/1.3 (http://www.tkl.iis.u-tokyo.ac.jp/~crawler/)'
    family: 'Steeler'
    major: '1'
    minor: '3'
    patch:

  - user_agent_string: 'Steeler/3.3 (http://www.tkl.iis.u-tokyo.ac.jp/~crawler/)'
    family: 'Steeler'
    major: '3'
    minor: '3'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Steeler/3.5; http://www.tkl.iis.u-tokyo.ac.jp/~crawler/)'
    family: 'Steeler'
    major: '3'
    minor: '5'
    patch:

  - user_agent_string: 'VSE/1.0 (<EMAIL>)'
    family: 'VSE'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'VSE/1.0 (<EMAIL>)'
    family: 'VSE'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; WebCrunch/1.2; +http://webcrunch.net/crawler)'
    family: 'WebCrunch'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'WebZIP/7.0 (http://www.spidersoft.com)'
    family: 'WebZIP'
    major: '7'
    minor: '0'
    patch:

  - user_agent_string: 'Y!J-BRI/0.0.1 crawler ( http://help.yahoo.co.jp/help/jp/search/indexing/indexing-15.html )'
    family: 'Y!J-BRI'
    major: '0'
    minor: '0'
    patch: '1'

  - user_agent_string: 'Y!J-BRW/1.0 crawler (http://help.yahoo.co.jp/help/jp/search/indexing/indexing-15.html)'
    family: 'Y!J-BRW'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'YahooSeeker/1.0 (compatible; Mozilla 4.0; MSIE 5.5; http://help.yahoo.com/help/us/shop/merchant/)'
    family: 'YahooSeeker'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'envolk/1.7 ( http://www.envolk.com/envolkspider.html)'
    family: 'envolk'
    major: '1'
    minor: '7'
    patch:

  - user_agent_string: 'sproose/1.0beta (sproose bot; http://www.sproose.com/bot.html; <EMAIL>)'
    family: 'sproose'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'wminer/1.2 (North America Web Crawler; http://www.wminer.com/bot; info at wminer dot com)'
    family: 'wminer'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 4.0; MSIECrawler; Windows 95)'
    family: 'MSIECrawler'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/4.0; FDM; MSIECrawler; Media Center PC 5.0)'
    family: 'MSIECrawler'
    major: '9'
    minor: '0'
    patch:

  - user_agent_string: 'Apache-HttpClient/4.0-beta2 (java 1.5)'
    family: 'Apache-HttpClient'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 2.1-update1; de-de; E10i Build/2.1.1.C.0.0) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17 Google-HTTP-Java-Client/1.10.3-beta (gzip)'
    family: 'Google-HTTP-Java-Client'
    major: '1'
    minor: '10'
    patch: '3'

  - user_agent_string: 'Google-HTTP-Java-Client/1.13.1-beta (gzip)'
    family: 'Google-HTTP-Java-Client'
    major: '1'
    minor: '13'
    patch: '1'

  - user_agent_string: 'Google-YouTubeSample/1.0 Google-HTTP-Java-Client/1.7.0-beta (gzip) Google-HTTP-Java-Client/1.7.0-beta (gzip)'
    family: 'Google-HTTP-Java-Client'
    major: '1'
    minor: '7'
    patch: '0'

  - user_agent_string: 'JNLP/1.7.0 javaws/********** (<internal>) Java/1.7.0_17'
    family: 'JNLP'
    major: '1'
    minor: '7'
    patch: '0'

  - user_agent_string: 'JNLP/6.0 javaws/1.6.0_14 (b08) Java/1.6.0_14'
    family: 'JNLP'
    major: '6'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Python-urllib2)'
    family: 'Python-urllib'
    major:
    minor:
    patch:

  - user_agent_string: 'Python-urllib/1.15'
    family: 'Python-urllib'
    major: '1'
    minor: '15'
    patch:

  - user_agent_string: 'IMVU Client/498.0 Python-urllib/2.7'
    family: 'Python-urllib'
    major: '2'
    minor: '7'
    patch:

  - user_agent_string: 'Python-urllib/3.4'
    family: 'Python-urllib'
    major: '3'
    minor: '4'
    patch:

  - user_agent_string: 'TLSProber/0.8'
    family: 'TLSProber'
    major: '0'
    minor: '8'
    patch:

  - user_agent_string: 'Asynchronous WinHTTP/1.0'
    family: 'WinHTTP'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: '1470.net crawler'
    family: '1470.net crawler'
    major:
    minor:
    patch:

  - user_agent_string: '50.nu/0.01 (  http://50.nu/bot.html )'
    family: '50.nu'
    major: '0'
    minor: '01'
    patch:

  - user_agent_string: '8bo Crawler Bot'
    family: '8bo Crawler Bot'
    major:
    minor:
    patch:

  - user_agent_string: 'Aboundex/0.2 (http://www.aboundex.com/crawler/)'
    family: 'Aboundex'
    major: '0'
    minor: '2'
    patch:

  - user_agent_string: 'Accoona-AI-Agent/1.1.1 (crawler at accoona dot com)'
    family: 'Accoona-AI-Agent'
    major: '1'
    minor: '1'
    patch: '1'

  - user_agent_string: 'Accoona-Biz-Agent/1.1.1 <EMAIL>'
    family: 'Accoona-Biz-Agent'
    major: '1'
    minor: '1'
    patch: '1'

  - user_agent_string: 'AdsBot-Google'
    family: 'AdsBot-Google'
    major:
    minor:
    patch:

  - user_agent_string: 'AppEngine-Google; ( http://code.google.com/appengine)'
    family: 'AppEngine-Google'
    major:
    minor:
    patch:

  - user_agent_string: 'Inne: Mozilla/2.0 (compatible; Ask Jeeves/Teoma;  http://sp.ask.com/docs/about/tech_crawling.html)'
    family: 'Ask Jeeves'
    major:
    minor:
    patch:

  - user_agent_string: 'BaiDuSpider'
    family: 'BaiDuSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'Baiduspider'
    family: 'Baiduspider'
    major:
    minor:
    patch:

  - user_agent_string: "'Mozilla/5.0 (compatible; Baiduspider/2.0; +ht'"
    family: 'Baiduspider'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Baiduspider-cpro; +http://www.baidu.com/search/spider.html)'
    family: 'Baiduspider-cpro'
    major:
    minor:
    patch:

  - user_agent_string: 'Baiduspider-image ( http://www.baidu.com/search/spider.htm)'
    family: 'Baiduspider-image'
    major:
    minor:
    patch:

  - user_agent_string: 'Baiduspider-testbranch'
    family: 'Baiduspider-testbranch'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/534  (KHTML, like Gecko) BingPreview/1.0b'
    family: 'BingPreview'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'BlogBridge 6.7 (http://www.blogbridge.com/) 1.6.0_18'
    family: 'BlogBridge'
    major: '6'
    minor: '7'
    patch:

  - user_agent_string: 'BoardReader Blog Indexer(http://boardreader.com)'
    family: 'BoardReader Blog Indexer'
    major:
    minor:
    patch:

  - user_agent_string: 'BoardReader Favicon Fetcher /1.0 <EMAIL>'
    family: 'BoardReader Favicon Fetcher'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla 4.0(compatible; BotSeer/1.0;  http://botseer.ist.psu.edu)'
    family: 'BotSeer'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'CRAWL-E/0.6.4'
    family: 'CRAWL-E'
    major: '0'
    minor: '6'
    patch: '4'

  - user_agent_string: 'Mozilla/5.0 (compatible; Charlotte/1.0b; <EMAIL>)'
    family: 'Charlotte'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Checklinks/1.3 (pywikipedia robot; http://toolserver.org/~dispenser/view/Checklinks)'
    family: 'Checklinks'
    major: '1'
    minor: '3'
    patch:

  - user_agent_string: 'Comodo HTTP(S) Crawler - http://www.instantssl.com/crawler'
    family: 'Comodo HTTP(S) Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Comodo-Webinspector-Crawler 2.1'
    family: 'Comodo-Webinspector-Crawler'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'Comodo-Webinspector-Crawler 2.2.2, http://www.comodorobot.com'
    family: 'Comodo-Webinspector-Crawler'
    major: '2'
    minor: '2'
    patch: '2'

  - user_agent_string: 'ConveraCrawler/0.9c ( http://www.authoritativeweb.com/crawl)'
    family: 'ConveraCrawler'
    major: '0'
    minor: '9'
    patch:

  - user_agent_string: 'CrawlConvera0.1 (<EMAIL>)'
    family: 'CrawlConvera'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; MSIE or Firefox mutant; not on Windows server; + http://tab.search.daum.net/aboutWebSearch.html) Daumoa/3.0'
    family: 'Daumoa'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Firefox compatible; MS IE compatible;  http://search.daum.net/) Daumoa-feedfetcher/2.0'
    family: 'Daumoa-feedfetcher'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Feed Seeker Bot (RSS Feed Seeker http://www.MyNewFavoriteThing.com/fsb.php)'
    family: 'Feed Seeker Bot'
    major:
    minor:
    patch:

  - user_agent_string: 'Flamingo_SearchEngine (+http://www.flamingosearch.com/bot)'
    family: 'Flamingo_SearchEngine'
    major:
    minor:
    patch:

  - user_agent_string: 'FollowSite Bot ( http://www.followsite.com/bot.html )'
    family: 'FollowSite Bot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Genieo/1.0 http://www.genieo.com/webfilter.html'
    family: 'Genieo'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible;  MSIE 5.01; GomezAgent 2.0; Windows NT)'
    family: 'GomezAgent'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Googlebot v2.1 (+http://www.google.com/bot.html) (http://www.openwebspider.org/)'
    family: 'Googlebot'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Googlebot/2.1;  http://www.google.com/bot.html)'
    family: 'Googlebot'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'Googlebot/2.2'
    family: 'Googlebot'
    major: '2'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows; Mozilla 4.0(compatible; Googlebot/5.0;  http://www.google.com/googlebot); en-US;)'
    family: 'Googlebot'
    major: '5'
    minor: '0'
    patch:

  - user_agent_string: 'Googlebot-Image/1.0'
    family: 'Googlebot-Image'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Googlebot-Mobile (compatible; Googlebot-Mobile/2.1; +http://www.google.com/bot.html)'
    family: 'Googlebot-Mobile'
    major:
    minor:
    patch:

  - user_agent_string: 'DoCoMo//2.0 N905i(c100;TB;W24H16) (compatible; Googlebot-Mobile/2.1; +http://www.google.com/bot.html)'
    family: 'Googlebot-Mobile'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'Googlebot-News'
    family: 'Googlebot-News'
    major:
    minor:
    patch:

  - user_agent_string: 'Googlebot-Video/1.0'
    family: 'Googlebot-Video'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Googlebot-richsnippets'
    family: 'Googlebot-richsnippets'
    major:
    minor:
    patch:

  - user_agent_string: 'Google Web Preview'
    family: 'Other'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.78 Safari/537.36'
    family: 'Chrome'
    major: '60'
    minor: '0'
    patch: '3112'
    patch_minor: '78'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112 Safari/537.36'
    family: 'Chrome'
    major: '60'
    minor: '0'
    patch: '3112'
    patch_minor:

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko; Google Web Preview) Chrome/27.0 .1453 Safari/537.36.'
    family: 'Chrome'
    major: '27'
    minor: '0'
    patch:
    patch_minor:

  - user_agent_string: 'HiddenMarket-1.0-beta (www.hiddenmarket.net/crawler.php)'
    family: 'HiddenMarket'
    major:
    minor:
    patch:

  - user_agent_string: 'HooWWWer/2.1.0 ( http://cosco.hiit.fi/search/hoowwwer/ | mailto:crawler-info<at>hiit.fi)'
    family: 'HooWWWer'
    major: '2'
    minor: '1'
    patch: '0'

  - user_agent_string: 'ICC-Crawler(Mozilla-compatible; http://kc.nict.go.jp/icc/crawl.html; icc-crawl(at)ml(dot)nict(dot)go(dot)jp)'
    family: 'ICC-Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'ICC-Crawler/2.0 (Mozilla-compatible; ; http://kc.nict.go.jp/project1/crawl.html)'
    family: 'ICC-Crawler'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'IconSurf/2.0 favicon finder (see http://iconsurf.com/robot.html)'
    family: 'IconSurf'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'IlTrovatore/1.2 (IlTrovatore; http://www.iltrovatore.it/bot.html; <EMAIL>)'
    family: 'IlTrovatore'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'IlTrovatore-Setaccio ( http://www.iltrovatore.it)'
    family: 'IlTrovatore-Setaccio'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; InfuzApp/1.0; +http://www.infuz.com/bot.html)'
    family: 'InfuzApp'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'InternetArchive-1.0'
    family: 'InternetArchive'
    major:
    minor:
    patch:

  - user_agent_string: 'InternetArchive/0.8-dev (Nutch; http://lucene.apache.org/nutch/bot.html; <EMAIL>)'
    family: 'InternetArchive'
    major: '0'
    minor: '8'
    patch:

  - user_agent_string: 'KDDI-CA34 UP.Browser/6.2.0.10.2.2(GUI)MMP/2.0 (compatible; KDDI-Googlebot-Mobile/2.1; http://www.google.com/bot.html)'
    family: 'KDDI-Googlebot-Mobile'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'kalooga/KaloogaBot (Kalooga; http://www.kalooga.com/info.html?page=crawler)'
    family: 'KaloogaBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Kraken/0.1; http://linkfluence.net/; <EMAIL>)'
    family: 'Kraken'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Kurzor/1.0 (Kurzor; http://adcenter.hu/docs/en/bot.html; <EMAIL>)'
    family: 'Kurzor'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'LEIA/3.01pr (LEIAcrawler; <EMAIL>; http://www.gseek.com)'
    family: 'LEIA'
    major: '3'
    minor: '01'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 6.0; Windows compatible LesnikBot)'
    family: 'LesnikBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Linguee Bot (<EMAIL>)'
    family: 'Linguee Bot'
    major:
    minor:
    patch:

  - user_agent_string: 'LinkAider (http://linkaider.com/crawler/)'
    family: 'LinkAider'
    major:
    minor:
    patch:

  - user_agent_string: 'Lite Bot0316B'
    family: 'Lite Bot'
    major:
    minor:
    patch:

  - user_agent_string: 'Llaut/1.0 (http://mnm.uib.es/~gallir/llaut/bot.html)'
    family: 'Llaut'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Linux x86_64; Mail.RU_Bot/Fast/2.0; +http://go.mail.ru/help/robots)'
    family: 'Mail.RU_Bot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Linux x86_64; Mail.RU_Bot/2.0; +http://go.mail.ru/help/robots)'
    family: 'Mail.RU_Bot'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Mediapartners-Google'
    family: 'Mediapartners-Google'
    major:
    minor:
    patch:

  - user_agent_string: 'DoCoMo/2.0 SH905i(c100;TB;W24H16) (compatible; Mediapartners-Google/2.1; +http://www.google.com/bot.html)'
    family: 'Mediapartners-Google'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'Microsoft Bing Mobile SocialStreams Bot'
    family: 'Microsoft Bing Mobile SocialStreams Bot'
    major:
    minor:
    patch:

  - user_agent_string: 'Microsoft MSN SocialStreams Bot,gzip(gfe),gzip(gfe)'
    family: 'Microsoft MSN SocialStreams Bot'
    major:
    minor:
    patch:

  - user_agent_string: 'NING/1.0'
    family: 'NING'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Netvibes (http://www.netvibes.com)'
    family: 'Netvibes'
    major:
    minor:
    patch:

  - user_agent_string: 'NewsGator/2.0 Bot (http://www.newsgator.com)'
    family: 'NewsGator'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'NewsGator/3.0,gzip(gfe),gzip(gfe)'
    family: 'NewsGator'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'NewsGator FetchLinks extension/0.2.0 (http://graemef.com)'
    family: 'NewsGator FetchLinks extension'
    major: '0'
    minor: '2'
    patch: '0'

  - user_agent_string: 'NewsGatorOnline/2.0 (http://www.newsgator.com)'
    family: 'NewsGatorOnline'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'SapphireWebCrawler/1.0 (Sapphire Web Crawler using Nutch; http://boston.lti.cs.cmu.edu/crawler/; <EMAIL>)'
    family: 'Nutch; http:'
    major:
    minor:
    patch:

  - user_agent_string: 'NutchCVS/0.06-dev (Nutch running at UW; http://www.nutch.org/docs/en/bot.html; <EMAIL>)'
    family: 'NutchCVS'
    major: '0'
    minor: '06'
    patch:

  - user_agent_string: 'NutchOSUOSL/0.05-dev (Nutch; http://www.nutch.org/docs/en/bot.html; <EMAIL>)'
    family: 'NutchOSUOSL'
    major: '0'
    minor: '05'
    patch:

  - user_agent_string: 'NutchOrg/0.03-dev (Nutch; http://www.nutch.org/docs/bot.html; <EMAIL>)'
    family: 'NutchOrg'
    major: '0'
    minor: '03'
    patch:

  - user_agent_string: 'ObjectsSearch/0.2 (ObjectsSearch; http://www.ObjectsSearch.com/bot.html; <EMAIL>)'
    family: 'ObjectsSearch'
    major: '0'
    minor: '2'
    patch:

  - user_agent_string: 'Orbiter ( http://www.dailyorbit.com/bot.htm)'
    family: 'Orbiter'
    major:
    minor:
    patch:

  - user_agent_string: 'PagePeeker.com'
    family: 'PagePeeker'
    major:
    minor:
    patch:

  - user_agent_string: 'PagesInventory (robot +http://www.pagesinventory.com)'
    family: 'PagesInventory'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; PaxleFramework/0.1.1;  http://www.paxle.net/en/bot)'
    family: 'PaxleFramework'
    major: '0'
    minor: '1'
    patch: '1'

  - user_agent_string: 'Peeplo Screenshot Bot/0.20 ( abuse at peeplo dot_com )'
    family: 'Peeplo Screenshot Bot'
    major: '0'
    minor: '20'
    patch:

  - user_agent_string: 'PlantyNet_WebRobot_V1.9 <EMAIL>'
    family: 'PlantyNet_WebRobot'
    major:
    minor:
    patch:

  - user_agent_string: 'Pompos/1.2 http://pompos.iliad.fr'
    family: 'Pompos'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'Reaper/1.3.6 (Linux; U; Android 4.3; de-DE; GT-I9300 Build/JSS15J)'
    family: 'Reaper'
    major: '1'
    minor: '3'
    patch: '6'

  - user_agent_string: 'Reaper/2.07 ( http://www.sitesearch.ca/reaper)'
    family: 'Reaper'
    major: '2'
    minor: '07'
    patch:

  - user_agent_string: 'RedCarpet/1.0 (http://www.redcarpet-inc.com/robots.html)'
    family: 'RedCarpet'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'RedCarpet/2.1 CFNetwork/672.0.2 Darwin/14.0.0'
    family: 'RedCarpet'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'Riddler (http://riddler.io/about.html)'
    family: 'Riddler'
    major:
    minor:
    patch:

  - user_agent_string: 'Scrapy/0.16.4 (+http://scrapy.org)'
    family: 'Scrapy'
    major: '0'
    minor: '16'
    patch: '4'

  - user_agent_string: 'Scrapy/0.22.2 (+http://scrapy.org)'
    family: 'Scrapy'
    major: '0'
    minor: '22'
    patch: '2'

  - user_agent_string: 'Simpy/1.1 (Simpy; http://www.simpy.com/?ref=bot; feedback at simpy dot com)'
    family: 'Simpy'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/3.0 (Slurp.so/1.0; <EMAIL>; http://www.inktomi.com/slurp.html)'
    family: 'Slurp'
    major:
    minor:
    patch:

  - user_agent_string: 'Slurp/2.0'
    family: 'Slurp'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Yahoo!; U; Slurp/3.0; http://help.yahoo.com/help/us/ysearch/slurp) Mozilla/5.0 ()'
    family: 'Slurp'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Speedy Spider; http://www.entireweb.com/about/search_tech/speedy_spider/)'
    family: 'Speedy Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Squrl Java/1.6.0_22'
    family: 'Squrl Java'
    major: '1'
    minor: '6'
    patch: '0'

  - user_agent_string: 'TheUsefulbot_2.3.62 (<EMAIL>)'
    family: 'TheUsefulbot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux x86_64; de-DE; rv:1.9.0.19) Gecko/2010091808 ThumbShotsBot (KFSW 3.0.6-3)'
    family: 'ThumbShotsBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Thumbshots.ru; +http://thumbshots.ru/bot) Firefox/3'
    family: 'Thumbshots.ru'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Vagabondo/Wapspider; webcrawler at wise-guys dot nl; http://webagent.wise-guys.nl/)'
    family: 'Vagabondo'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Vagabondo/2.1; webcrawler at wise-guys dot nl; http://webagent.wise-guys.nl/)'
    family: 'Vagabondo'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible;  Vagabondo/2.2; webcrawler at wise-guys dot nl; http://webagent.wise-guys.nl/)'
    family: 'Vagabondo'
    major: '2'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible;  Vagabondo/2.3; webcrawler at wise-guys dot nl; http://webagent.wise-guys.nl/)'
    family: 'Vagabondo'
    major: '2'
    minor: '3'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible;  Vagabondo/4.0Beta; webcrawler at wise-guys dot nl; http://webagent.wise-guys.nl/)'
    family: 'Vagabondo'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 5.0; Windows 95) VoilaBot BETA 1.2 (http://www.voila.com/)'
    family: 'VoilaBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Votay bot/4.0;  http://www.votay.com/arts/comics/)'
    family: 'Votay bot'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; WASALive Bot ; http://blog.wasalive.com/wasalive-bots/)'
    family: 'WASALive Bot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPad; U; CPU iPhone OS 7_0 like Mac OS X; de-DE) WIRED/********.87970'
    family: 'WIRE'
    major:
    minor:
    patch:

  - user_agent_string: 'WIRE/0.11 (Linux; i686; Robot,Spider,Crawler,<EMAIL>)'
    family: 'WIRE'
    major: '0'
    minor: '11'
    patch:

  - user_agent_string: 'WIRE/1.0 (Linux;i686;Bot,Robot,Spider,Crawler)'
    family: 'WIRE'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows; Windows NT 6.1; WOW64; rv:2.0b8pre; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; MS-RTC LM 8; OfficeLiveConnector.1.4; OfficeLivePatch.1.3; SLCC1; SLCC2; Media Center PC 6.0; GTB6.4; InfoPath.2; en-US; FunWebProducts; Zango **********; SV1; PRTG Network Monitor (www.paessler.com)) Gecko/20101114 Firefox/4.0b8pre QuickTime/7.6.2 Songbird/1.1.2 Web-sniffer/1.0.36 lftp/3.7.4 libwww-perl/5.820 GSiteCrawler/v1.12 rev. 260 Snoopy v1.2'
    family: 'Web-sniffer'
    major: '1'
    minor: '0'
    patch: '36'

  - user_agent_string: 'Mozilla/5.0 (compatible; WebThumbnail/2.2; Website Thumbnail Generator; +http://webthumbnail.org)'
    family: 'WebThumb'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux i686 (x86_64); en-US; rv:1.9.0.17) Gecko WebThumb/1.0'
    family: 'WebThumb'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'WhatWeb/0.4.8'
    family: 'WhatWeb'
    major: '0'
    minor: '4'
    patch: '8'

  - user_agent_string: 'Jetpack by WordPress.com'
    family: 'WordPress'
    major:
    minor:
    patch:

  - user_agent_string: 'WordPress/1.2.1 PHP/4.3.9-1'
    family: 'WordPress'
    major: '1'
    minor: '2'
    patch: '1'

  - user_agent_string: 'Pachacutec/0.5 (qpImageBuilders.com); WordPress/3.1; http://contacto-latino.com/news'
    family: 'WordPress'
    major: '3'
    minor: '1'
    patch:

  - user_agent_string: 'Wotbox/0.7-alpha (<EMAIL>; http://www.wotbox.com)'
    family: 'Wotbox'
    major: '0'
    minor: '7'
    patch:

  - user_agent_string: 'Wotbox/2.01 ( http://www.wotbox.com/bot/)'
    family: 'Wotbox'
    major: '2'
    minor: '01'
    patch:

  - user_agent_string: 'Xenu Link Sleuth 1.1f'
    family: 'Xenu Link Sleuth'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: "Xenu's Link Sleuth 1.0p"
    family: "Xenu's Link Sleuth"
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Xerka WebBot v1.0.0 [AIDO_CREA_ab]'
    family: 'Xerka WebBot'
    major: '1'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (compatible; Yahoo! Slurp China; http://misc.yahoo.com.cn/help.html)'
    family: 'Yahoo! Slurp'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Yahoo! Slurp/3.0 ; http://help.yahoo.com/help/us/ysearch/slurp)'
    family: 'Yahoo! Slurp'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'LG-C1500 UP.Browser/6.2.3 (GUI) MMP/1.0 (compatible;YahooSeeker/M1A1-R2D2; http://help.yahoo.com/help/us/ysearch/crawling/crawling-01.html)'
    family: 'YahooSeeker'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots),gzip(gfe) AppEngine-Google; (+http://code.google.com/appengine; appid: twitter-mirror),gzip(gfe),gzip(gfe),gzip(gfe)'
    family: 'YandexBot'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'Yeti-FeedItemCrawler/1.0 (NHN Corp.;+http://help.naver.com/robots/)'
    family: 'Yeti'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; YodaoBot/1.0; http://www.yodao.com/help/webmaster/spider/; )'
    family: 'YodaoBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible;YodaoBot-Image/1.0;http://www.youdao.com/help/webmaster/spider/;)'
    family: 'YodaoBot-Image'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'YowedoBot/Yowedo 1.0 (Search Engine crawler for yowedo.com; http://yowedo.com/en/partners.html; <EMAIL>)'
    family: 'Yowedo'
    major:
    minor:
    patch:

  - user_agent_string: 'Zao-Crawler'
    family: 'Zao'
    major:
    minor:
    patch:

  - user_agent_string: 'ZeBot_www.ze.bz (<EMAIL>)'
    family: 'ZeBot_www.ze.bz'
    major:
    minor:
    patch:

  - user_agent_string: 'ZooShot 0.42'
    family: 'ZooShot'
    major: '0'
    minor: '42'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE; ZyBorg; Win32)'
    family: 'ZyBorg'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 compatible ZyBorg/1.0 (<EMAIL>; http://www.WISEnutbot.com)'
    family: 'ZyBorg'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'AltaVista Intranet V2.0 Compaq <NAME_EMAIL>'
    family: 'altavista'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; archive.bibalex.org_bot; +http://www.test.com)'
    family: 'archive.bibalex.org_bot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; archive.org_bot/heritrix-1.15.1-x  http://pandora.nla.gov.au/crawl.html)'
    family: 'archive.org_bot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; special_archiver/3.2.0 +http://www.loc.gov/webarchiving/notice_to_webmasters.html)'
    family: 'archiver'
    major: '3'
    minor: '2'
    patch: '0'

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.2; .NET CLR 1.1.4322; .NET CLR 2.0.50215; baiduspider)'
    family: 'baiduspider'
    major:
    minor:
    patch:

  - user_agent_string: 'baiduspider-mobile-gate'
    family: 'baiduspider-mobile-gate'
    major:
    minor:
    patch:

  - user_agent_string: 'bingbot'
    family: 'bingbot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; bingbot/2.0  http://www.bing.com/bingbot.htm)'
    family: 'bingbot'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'boitho.com-dc/0.4 ( http://www.boitho.com/dcbot.html )'
    family: 'boitho.com-dc'
    major: '0'
    minor: '4'
    patch:

  - user_agent_string: 'boitho.com-dc/0.86 ( http://www.boitho.com/dcbot.html )'
    family: 'boitho.com-dc'
    major: '0'
    minor: '86'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; clumboot; +http://localhost/clumpit/bot.php)'
    family: 'clumboot'
    major:
    minor:
    patch:

  - user_agent_string: 'findlinks/2.6 ( http://wortschatz.uni-leipzig.de/findlinks/)'
    family: 'findlinks'
    major: '2'
    minor: '6'
    patch:

  - user_agent_string: 'Mozilla/4.0 compatible FurlBot/Furl Search 2.0 (FurlBot; http://www.furl.net; <EMAIL>)'
    family: 'furlbot'
    major:
    minor:
    patch:

  - user_agent_string: 'gonzo1[D] mailto:<EMAIL>'
    family: 'gonzo1'
    major:
    minor:
    patch:

  - user_agent_string: 'grub-client-1.5.3; (grub-client-1.5.3; Crawl your own stuff with http://grub.org)'
    family: 'grub-client'
    major:
    minor:
    patch:

  - user_agent_string: 'gsa-crawler'
    family: 'gsa-crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; cdlwas_bot; heritrix/1.14.1 +http://webarchives.cdlib.org/p/webmasters)'
    family: 'heritrix'
    major: '1'
    minor: '14'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (compatible; heritrix/3.2.0 +http://suki.ling.helsinki.fi/eng/project.html)'
    family: 'heritrix'
    major: '3'
    minor: '2'
    patch: '0'

  - user_agent_string: 'holmes/2.3'
    family: 'holmes'
    major: '2'
    minor: '3'
    patch:

  - user_agent_string: 'holmes/3.12.4 (http://morfeo.centrum.cz/bot)'
    family: 'holmes'
    major: '3'
    minor: '12'
    patch: '4'

  - user_agent_string: 'htdig/3.1.6 (<EMAIL>)'
    family: 'htdig'
    major: '3'
    minor: '1'
    patch: '6'

  - user_agent_string: 'ia_archiver/8.7 (Windows NT 5.0; )'
    family: 'ia_archiver'
    major: '8'
    minor: '7'
    patch:

  - user_agent_string: 'ichiro/1.0 (<EMAIL>)'
    family: 'ichiro'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'ichiro/5.0 (http://help.goo.ne.jp/door/crawler.html)'
    family: 'ichiro'
    major: '5'
    minor: '0'
    patch:

  - user_agent_string: 'DoCoMo/2.0 P900i(c100;TB;W24H11)(compatible; ichiro/mobile goo;  http://help.goo.ne.jp/door/crawler.html)'
    family: 'ichiro/mobile'
    major:
    minor:
    patch:

  - user_agent_string: '08_800w_web1 (<EMAIL>)'
    family: 'larbin'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 1.1.4322; LYCOSA;+http://lycosa.se)'
    family: 'lycos'
    major:
    minor:
    patch:

  - user_agent_string: 'masidani_bot_v0.3 (<EMAIL>)'
    family: 'masidani_bot'
    major:
    minor:
    patch:

  - user_agent_string: 'mozDex/0.05-dev (mozDex; http://www.mozdex.com/bot.html; <EMAIL>)'
    family: 'mozDex'
    major: '0'
    minor: '05'
    patch:

  - user_agent_string: 'msnbot/2.0 (+http://search.msn.com/msnbot.htm)'
    family: 'msnbot'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.9.2.13) Gecko/20101203 Firefox/3.6.13 (.NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; msnbot/2.1) Jakarta Commons-HttpClient/3.0-rc3 PHPCrawl GStreamer souphttpsrc libsoup/2.27.4 PycURL/7.19.0 XML-RPC for PHP 2.2.1 GoogleFriendConnect/1.0 HTMLParser/1.6 gPodder/0.15.2 ( http://gpodder.org/) anw webtool LoadControl/1.3 WinHttp urlgrabber/3.1.0'
    family: 'msnbot'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'msnbot-media/1.0 ( http://search.msn.com/msnbot.htm)'
    family: 'msnbot-media'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'msnbot-media/2.0b (+http://search.msn.com/msnbot.htm)'
    family: 'msnbot-media'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'MSRBOT (http://research.microsoft.com/research/sv/msrbot)'
    family: 'msrbot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0; scooter; .NET CLR 1.0.3705)'
    family: 'scooter'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; focuseekbot)'
    family: 'seekbot'
    major:
    minor:
    patch:

  - user_agent_string: 'semanticdiscovery/0.2(http://www.semanticdiscovery.com/sd/robot.html)'
    family: 'semanticdiscovery'
    major: '0'
    minor: '2'
    patch:

  - user_agent_string: 'semanticdiscovery/2.0(http://www.semanticdiscovery.com/robot.html)'
    family: 'semanticdiscovery'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'voyager/1.0'
    family: 'voyager'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'voyager/2.0 (http://www.kosmix.com/crawler.html)'
    family: 'voyager'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'http://www.almaden.ibm.com/cs/crawler'
    family: 'www.almaden.ibm.com'
    major:
    minor:
    patch:

  - user_agent_string: '449 Overture-WebCrawler/3.8/Fresh (atw-crawler at fast dot no; http://fast.no/support/crawler.asp'
    family: '449 Overture-WebCrawler'
    major: '3'
    minor: '8'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; 80bot/0.71; http://www.80legs.com/spider.html;) Gecko/2008032620'
    family: '80bot'
    major: '0'
    minor: '71'
    patch:

  - user_agent_string: 'A6-Indexer/1.0 (http://www.a6corp.com/a6-web-scraping-policy/)'
    family: 'A6-Indexer'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'AcquiloSpider/1.0,gzip(gfe),gzip(gfe)'
    family: 'AcquiloSpider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Aleksika Spider/1.0 ( http://www.aleksika.com/)'
    family: 'Aleksika Spider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'AmorankSpider/0.1; +http://amorank.com/webcrawler.html'
    family: 'AmorankSpider'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'AnomaliesBot/0.06-dev (The Anomalies Network Search Spider; http://www.anomalies.net; <EMAIL>)'
    family: 'AnomaliesBot'
    major: '0'
    minor: '06'
    patch:

  - user_agent_string: 'Automattic Analytics Crawler/0.1; http://wordpress.com/crawler/'
    family: 'Automattic Analytics Crawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Automattic Analytics Crawler/0.2;+http://wordpress.com/crawler/'
    family: 'Automattic Analytics Crawler'
    major: '0'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; BLEXBot/1.0; +http://webmeup.com/crawler.html)'
    family: 'BLEXBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'BabalooSpider/1.2 (BabalooSpider; http://www.babaloo.si; <EMAIL>)'
    family: 'BabalooSpider'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'BabalooSpider/1.3 (BabalooSpider; http://www.babaloo.si; <EMAIL>)'
    family: 'BabalooSpider'
    major: '1'
    minor: '3'
    patch:

  - user_agent_string: 'BebopBot/2.5.1 (compatible; media crawler V1;  http://www.apassion4jazz.net/bebopbot.html;)'
    family: 'BebopBot'
    major: '2'
    minor: '5'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (compatible; BlinkaCrawler/1.0;  http://www.blinka.jp/crawler/)'
    family: 'BlinkaCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'BlogRangerCrawler/1.0'
    family: 'BlogRangerCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Brekiri crawler/1.0'
    family: 'Brekiri crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'BurstFindCrawler/1.1 (crawler.burstfind.com; http://crawler.burstfind.com; <EMAIL>)'
    family: 'BurstFindCrawler'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'CCBot/1.0 ( http://www.commoncrawl.org/bot.html)'
    family: 'CCBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'CCBot/2.0 (http://commoncrawl.org/faq/)'
    family: 'CCBot'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'CamontSpider/1.0  http://epweb2.ph.bham.ac.uk/user/slater/camont/info.html'
    family: 'CamontSpider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'CazoodleBot/0.1 (CazoodleBot Crawler; http://www.cazoodle.com; <EMAIL>)'
    family: 'CazoodleBot'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; CloudServerMarketSpider/1.0; +http://www.cloudservermarket.com/spider.html)'
    family: 'CloudServerMarketSpider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; CompSpyBot/1.0; +http://www.compspy.com/spider.html)'
    family: 'CompSpyBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'ConveraMultiMediaCrawler/0.1 ( http://www.authoritativeweb.com/crawl)'
    family: 'ConveraMultiMediaCrawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'CosmixCrawler/0.1'
    family: 'CosmixCrawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Crawl/0.1 langcrawl/0.1'
    family: 'Crawl'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; CrawlBot/1.0.0)'
    family: 'CrawlBot'
    major: '1'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Crawllybot/0.1/0.1 (Crawllybot/0.1; http://www.crawlly.com; <EMAIL>)'
    family: 'Crawllybot'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Crawly/1.9;  http://92.51.162.40/crawler.html)'
    family: 'Crawly'
    major: '1'
    minor: '9'
    patch:

  - user_agent_string: 'Crawlzilla/1.0 (Crawlzilla; http://www.crawlzilla.com; <EMAIL>)'
    family: 'Crawlzilla'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'CydralSpider/3.2 (Cydral Image Search; http://www.cydral.com)'
    family: 'CydralSpider'
    major: '3'
    minor: '2'
    patch:

  - user_agent_string: 'DealGates Bot/1.1 by Luc Michalski (http://spider.dealgates.com/bot.html)'
    family: 'DealGates Bot'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'Denodo IECrawler/4.5,gzip(gfe),gzip(gfe)'
    family: 'Denodo IECrawler'
    major: '4'
    minor: '5'
    patch:

  - user_agent_string: 'Diffbot/0.1'
    family: 'Diffbot'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'DomainCrawler/2.0 (<EMAIL>; http://www.domaincrawler.com/bot'
    family: 'DomainCrawler'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'DotBot/1.0.1 (http://www.dotnetdotcom.org/, <EMAIL>)'
    family: 'DotBot'
    major: '1'
    minor: '0'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (compatible; DotBot/1.1; http://www.dotnetdotcom.org/, <EMAIL>)'
    family: 'DotBot'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'DotSpotsBot/0.2 (crawler; support at dotspots.com)'
    family: 'DotSpotsBot'
    major: '0'
    minor: '2'
    patch:

  - user_agent_string: 'ERACrawler/1.0'
    family: 'ERACrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; EventGuruBot/1.0; +http://www.eventguru.com/spider.html)'
    family: 'EventGuruBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Ex-Crawler/0.1.5a; powered by ex-crawler; +http://www.ex-crawler.de/) Java/1.6.0_20,gzip(gfe),gzip(gfe)'
    family: 'Ex-Crawler'
    major: '0'
    minor: '1'
    patch: '5'

  - user_agent_string: 'ExactSeek Crawler/0.1'
    family: 'ExactSeek Crawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'ExactSeekCrawler/1.0'
    family: 'ExactSeekCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'FANGCrawl/0.01'
    family: 'FANGCrawl'
    major: '0'
    minor: '01'
    patch:

  - user_agent_string: 'FAST Enterprise Crawler/6 (www.fastsearch.com)'
    family: 'FAST Enterprise Crawler'
    major: '6'
    minor:
    patch:

  - user_agent_string: 'FAST-WebCrawler/2.2.11 (<EMAIL>; http://www.fast.no/faq/faqfastwebsearch/faqfastwebcrawler.html)'
    family: 'FAST-WebCrawler'
    major: '2'
    minor: '2'
    patch: '11'

  - user_agent_string: 'FAST-WebCrawler/3.7 (atw-crawler at fast dot no; http://fast.no/support/crawler.asp)'
    family: 'FAST-WebCrawler'
    major: '3'
    minor: '7'
    patch:

  - user_agent_string: 'NokiaN70/. FASTMobileCrawl/6.6 Profile/MIDP-2.0 Configuration/CLDC-1.1'
    family: 'FASTMobileCrawl'
    major: '6'
    minor: '6'
    patch:

  - user_agent_string: 'FlaxCrawler/1.0,gzip(gfe),gzip(gfe)'
    family: 'FlaxCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'FyberSpider/1.3 (http://www.fybersearch.com/fyberspider.php)'
    family: 'FyberSpider'
    major: '1'
    minor: '3'
    patch:

  - user_agent_string: 'GarlikCrawler/1.2 (http://garlik.com/'
    family: 'GarlikCrawler'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'GematchCrawler/2.1 (http://www.gematch.com/crawler.html)'
    family: 'GematchCrawler'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'Gigabot/2.0/gigablast.com/spider.html'
    family: 'Gigabot'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Gigabot/3.0 (http://www.gigablast.com/spider.html)'
    family: 'Gigabot'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'GingerCrawler/1.0 (Language Assistant for Dyslexics; www.gingersoftware.com/crawler_agent.htm; support at ginger software dot com)'
    family: 'GingerCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'GoGuidesBot/0.0.1 (GoGuides Indexing Spider; http://www.goguides.org/spider.html)'
    family: 'GoGuidesBot'
    major: '0'
    minor: '0'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (compatible; GrapeshotCrawler/2.0; +http://www.grapeshot.co.uk/crawler.php)'
    family: 'GrapeshotCrawler'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'HPI FeedCrawler/0.1 (+http://www.hpi.uni-potsdam.de/meinel/bross/feedcrawler)'
    family: 'HPI FeedCrawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'HRCrawler/2.0 (XF86; MacOS x86_64) AppleWebKit/537.31 (KHTML, like Gecko)'
    family: 'HRCrawler'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Hailoobot/1.2;  http://www.hailoo.com/spider.html)'
    family: 'Hailoobot'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'Hatena::Crawler/0.01'
    family: 'Hatena::Crawler'
    major: '0'
    minor: '01'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; HiveCrawler/1.2 http://www.businessinsider.com)'
    family: 'HiveCrawler'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Host-Spy Crawler/1.2; +http://www.host-spy.com/)'
    family: 'Host-Spy Crawler'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'HttpSpider/0.91'
    family: 'HttpSpider'
    major: '0'
    minor: '91'
    patch:

  - user_agent_string: 'HuaweiSymantecSpider/1.0 <EMAIL> (compatible; MSIE 7.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR ; http://www.huaweisymantec.com/cn/IRL/spider)'
    family: 'HuaweiSymantecSpider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'IRLbot/1.0 ( http://irl.cs.tamu.edu/crawler)'
    family: 'IRLbot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'IRLbot/3.0 (compatible; MSIE 6.0; http://irl.cs.tamu.edu/crawler)'
    family: 'IRLbot'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; Iplexx Spider/1.1)'
    family: 'Iplexx Spider'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'Jambot/0.1.1 (Jambot; http://www.jambot.com/blog; <EMAIL>)'
    family: 'Jambot'
    major: '0'
    minor: '1'
    patch: '1'

  - user_agent_string: 'Jomjaibot/1.0 Crawl (+http://www.jomjaibot.com/)'
    family: 'Jomjaibot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'KIT webcrawler/0.2.4'
    family: 'KIT webcrawler'
    major: '0'
    minor: '2'
    patch: '4'

  - user_agent_string: 'Kyoto-Crawler/2.0 (Mozilla-compatible; kyoto-crawler-contact(at)nlp(dot)kuee(dot)kyoto-u(dot)ac(dot)jp; http://nlp.ist.i.kyoto-u.ac.jp/)'
    family: 'Kyoto-Crawler'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'LB-Crawler/1.0'
    family: 'LB-Crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'LSSRocketCrawler/1.0 LightspeedSystems'
    family: 'LSSRocketCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.7) Gecko/20040707 Lightningspider/0.9.2'
    family: 'Lightningspider'
    major: '0'
    minor: '9'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (compatible; LikaholixCrawler/1.0; +http://mylikes.com/about/crawler)'
    family: 'LikaholixCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Logict IPv6 Crawler/1.0 (http://ipv6search.logict.net),gzip(gfe),gzip(gfe)'
    family: 'Logict IPv6 Crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'MPICrawler/0.1 +http://kermit.news.cs.nyu.edu/crawler.html,gzip(gfe),gzip(gfe)'
    family: 'MPICrawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Opera/8.01 (J2ME/MIDP; MXit WebBot/*********) Opera Mini/3.1'
    family: 'MXit WebBot'
    major: '1'
    minor: '8'
    patch: '3'

  - user_agent_string: 'MetaGeneratorCrawler/1.3.8 (www.metagenerator.info)'
    family: 'MetaGeneratorCrawler'
    major: '1'
    minor: '3'
    patch: '8'

  - user_agent_string: 'Mozilla/5.0 (compatible; MetamojiCrawler/1.0; +http://www.metamoji.com/jp/crawler.html'
    family: 'MetamojiCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'MonkeyCrawl/0.05 (MonkeyCrawl; http://www.monkeymethods.org;  )'
    family: 'MonkeyCrawl'
    major: '0'
    minor: '05'
    patch:

  - user_agent_string: 'Mozilla crawl/5.0 (compatible; fairshare.cc +http://fairshare.cc)'
    family: 'Mozilla crawl'
    major: '5'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; NLCrawler/2.0.15; Linux 2.6.3-7; i686; en_US)KHTML/3.4.89 (like Gecko)'
    family: 'NLCrawler'
    major: '2'
    minor: '0'
    patch: '15'

  - user_agent_string: 'NMG Spider/0.3 (szukanko.com)'
    family: 'NMG Spider'
    major: '0'
    minor: '3'
    patch:

  - user_agent_string: 'NalezenCzBot/1.0 (http://www.nalezen.cz/about-crawler)'
    family: 'NalezenCzBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'NationalDirectory-WebSpider/1.3'
    family: 'NationalDirectory-WebSpider'
    major: '1'
    minor: '3'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; NetSeer crawler/2.0; +http://www.netseer.com/crawler.html; <EMAIL>)'
    family: 'NetSeer crawler'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'NetWhatCrawler/0.06-dev (NetWhatCrawler from NetWhat.com; http://www.netwhat.com; <EMAIL>)'
    family: 'NetWhatCrawler'
    major: '0'
    minor: '06'
    patch:

  - user_agent_string: 'compatible; Netseer crawler/2.0; +http://www.netseer.com/crawler.html; <EMAIL>'
    family: 'Netseer crawler'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'New-Sogou-Spider/1.0 (compatible; MSIE 5.5; Windows 98)'
    family: 'New-Sogou-Spider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'NewzCrawler/1.9 (compatible; MSIE 6.00; Newz Crawler 1.9; http://www.newzcrawler.com/ )'
    family: 'NewzCrawler'
    major: '1'
    minor: '9'
    patch:

  - user_agent_string: 'NodejsSpider/1.0'
    family: 'NodejsSpider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; .None-Crawler/0.1 +http://domains.ericbinek.None/)'
    family: 'None-Crawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'OmniExplorer_Bot/6.70 ( http://www.omni-explorer.com) WorldIndexer'
    family: 'OmniExplorer_Bot'
    major: '6'
    minor: '70'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; OpenCrawler/*******; http://code.google.com/p/opencrawler/),gzip(gfe),gzip(gfe)'
    family: 'OpenCrawler'
    major: '0'
    minor: '1'
    patch: '6'

  - user_agent_string: 'OpenWebSpider/0.6 (http://www.openwebspider.org)'
    family: 'OpenWebSpider'
    major: '0'
    minor: '6'
    patch:

  - user_agent_string: 'Overture-WebCrawler/3.8/Fresh (atw-crawler at fast dot no; http://fast.no/support/crawler.asp)'
    family: 'Overture-WebCrawler'
    major: '3'
    minor: '8'
    patch:

  - user_agent_string: 'PArchiveCrawler/1.0'
    family: 'PArchiveCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'PercolateCrawler/4 (<EMAIL>)'
    family: 'PercolateCrawler'
    major: '4'
    minor:
    patch:

  - user_agent_string: 'Pete-Spider/1.1'
    family: 'Pete-Spider'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'PicSpider/1.1 (<EMAIL>; http://www.bildkiste.de)'
    family: 'PicSpider'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'PluckItCrawler/1.0 (compatible; Mozilla 4.0; MSIE 5.5; http://www.pluck.com;)'
    family: 'PluckItCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; PornSpider/1.0; +http://www.pornspider.net)'
    family: 'PornSpider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'PortalBSpider/2.0 (<EMAIL>)'
    family: 'PortalBSpider'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; ProCogBot/1.0; +http://www.procog.com/spider.html)'
    family: 'ProCogBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'ProloCrawler/1.0 (http://www.prolo.com)'
    family: 'ProloCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'PsSpider/0.7 (<EMAIL>)'
    family: 'PsSpider'
    major: '0'
    minor: '7'
    patch:

  - user_agent_string: 'RSSIncludeBot/1.0 (http://www.rssinclude.com/spider)'
    family: 'RSSIncludeBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; Trident/4.0; Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1) ;  Embedded Web Browser from: http://bsalsa.com/; RSScrawler/4.0 (compatible; MSIE 6.0; Windows NT 5.0); .NET CLR 2.0.50727'
    family: 'RSScrawler'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'RSpider/1.0'
    family: 'RSpider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'RyzeCrawler/1.1.0 (+http://www.ryze.nl/crawler/)'
    family: 'RyzeCrawler'
    major: '1'
    minor: '1'
    patch: '0'

  - user_agent_string: 'SMXCrawler/1.0 (www.socialmetrix.com)'
    family: 'SMXCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; SRCCN!Spider/1.1; +http://site.srccn.com/spider.html)'
    family: 'SRCCN!Spider'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; SWEBot/1.0; +http://swebot-crawler.net)'
    family: 'SWEBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Sangfor Spider/0.7'
    family: 'Sangfor Spider'
    major: '0'
    minor: '7'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Sailfish 3.0; Mobile; rv:45.0) Gecko/45.0 Firefox/45.0 SailfishBrowser/1.0'
    family: 'Sailfish Browser'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Sailfish 3.0; Mobile; rv:45.0) Gecko/45.0 Firefox/45.0 SailfishBrowser/1.2.3'
    family: 'Sailfish Browser'
    major: '1'
    minor: '2'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 5.0.1; SAMSUNG GT-I9506-ORANGE Build/LRX22C) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/2.1 Chrome/34.0.1847.76 Mobile Safari/537.36'
    family: 'Samsung Internet'
    major: '2'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 5.0.2; SAMSUNG SM-T800 Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.0 Chrome/38.0.2125.102 Safari/537.36'
    family: 'Samsung Internet'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-G920F Build/LMY47X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.2 Chrome/38.0.2125.102 Mobile Safari/537.36'
    family: 'Samsung Internet'
    major: '3'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 5.0.2; SAMSUNG SM-T710 Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.5 Chrome/38.0.2125.102 Safari/537.36'
    family: 'Samsung Internet'
    major: '3'
    minor: '5'
    patch:

  - user_agent_string: 'ScSpider/0.2'
    family: 'ScSpider'
    major: '0'
    minor: '2'
    patch:

  - user_agent_string: 'ScollSpider/2.0 ( http://www.webwobot.com/ScollSpider.php)'
    family: 'ScollSpider'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Screaming Frog SEO Spider/1.10'
    family: 'Screaming Frog SEO Spider'
    major: '1'
    minor: '10'
    patch:

  - user_agent_string: 'Screaming Frog SEO Spider/2,01'
    family: 'Screaming Frog SEO Spider'
    major: '2'
    minor:
    patch:

  - user_agent_string: 'SearchSpider/1.2.10'
    family: 'SearchSpider'
    major: '1'
    minor: '2'
    patch: '10'

  - user_agent_string: 'Searchspider/1.2 (SearchSpider; http://www.searchspider.com; <EMAIL>)'
    family: 'Searchspider'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'SimpleCrawler/0.1'
    family: 'SimpleCrawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'SlugBug Spider/0.1 beta (SlugBug.com search engine; http://www.slugbug.com)'
    family: 'SlugBug Spider'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'SmartAndSimpleWebCrawler/1.3 (https://crawler.dev.java.net),gzip(gfe),gzip(gfe)'
    family: 'SmartAndSimpleWebCrawler'
    major: '1'
    minor: '3'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Linux; Socialradarbot/2.0; en-US; <EMAIL>)'
    family: 'Socialradarbot'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Sogou Orion spider/4.0( http://www.sogou.com/docs/help/webmasters.htm#07)'
    family: 'Sogou Orion spider'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'Sogou Pic Spider/3.0( http://www.sogou.com/docs/help/webmasters.htm#07)'
    family: 'Sogou Pic Spider'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'Sogou Push Spider/3.0( http://www.sogou.com/docs/help/webmasters.htm#07)'
    family: 'Sogou Push Spider'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'Sogou develop spider/4.0( http://www.sogou.com/docs/help/webmasters.htm#07)'
    family: 'Sogou develop spider'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'Sogou head spider/3.0( http://www.sogou.com/docs/help/webmasters.htm#07)'
    family: 'Sogou head spider'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'Sogou web spider/3.0( http://www.sogou.com/docs/help/webmasters.htm#07)'
    family: 'Sogou web spider'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'Sogou-Test-Spider/4.0 (compatible; MSIE 5.5; Windows 98)'
    family: 'Sogou-Test-Spider'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Sosoimagespider/2.0; +http://help.soso.com/soso-image-spider.htm)'
    family: 'Sosoimagespider'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Sosospider/2.0; +http://help.soso.com/webspider.htm)'
    family: 'Sosospider'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Spider/5.0'
    family: 'Spider'
    major: '5'
    minor: '0'
    patch:

  - user_agent_string: 'SpokeSpider/1.0 (http://support.spoke.com/webspider/) Mozilla/5.0 (not really)'
    family: 'SpokeSpider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; StoneSunSpider/1.1)'
    family: 'StoneSunSpider'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; StreamScraper/1.0; +http://code.google.com/p/streamscraper/)'
    family: 'StreamScraper'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 5.5; SuperSpider/139; Windows 98; Win 9x 4.90)'
    family: 'SuperSpider'
    major: '139'
    minor:
    patch:

  - user_agent_string: 'T3census-Crawler/1.0'
    family: 'T3census-Crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'TECOMAC-Crawler/0.3'
    family: 'TECOMAC-Crawler'
    major: '0'
    minor: '3'
    patch:

  - user_agent_string: 'Tasapspider/0.9'
    family: 'Tasapspider'
    major: '0'
    minor: '9'
    patch:

  - user_agent_string: 'TinEye-bot/0.02 (see http://www.tineye.com/crawler.html)'
    family: 'TinEye-bot'
    major: '0'
    minor: '02'
    patch:

  - user_agent_string: 'Top10Ranking Spider/3.1 ( http://www.top10Ranking.nl/, Top10ranking.nl heeft op een aantal woorden uw posities in Google gecheckt)'
    family: 'Top10Ranking Spider'
    major: '3'
    minor: '1'
    patch:

  - user_agent_string: 'TouTrix crawler/1.0'
    family: 'TouTrix crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; TridentSpider/3.1)'
    family: 'TridentSpider'
    major: '3'
    minor: '1'
    patch:

  - user_agent_string: 'TurnitinBot/1.5 (http://www.turnitin.com/robot/crawlerinfo.html)'
    family: 'TurnitinBot'
    major: '1'
    minor: '5'
    patch:

  - user_agent_string: 'TurnitinBot/3.0 (http://www.turnitin.com/robot/crawlerinfo.html)'
    family: 'TurnitinBot'
    major: '3'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Linux i686; en-US; URLfilterDB-crawler/1.0) ufdb/1.0'
    family: 'URLfilterDB-crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (URLfilterDB-crawler/1.1) ufdb/1.0'
    family: 'URLfilterDB-crawler'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'VinjaVideoSpider/1.1'
    family: 'VinjaVideoSpider'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'VisBot/2.0 (Visvo.com Crawler; http://www.visvo.com/bot.html; <EMAIL>)'
    family: 'VisBot'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; VodpodCrawler/1.0; +http://vodpod.com/site/help)'
    family: 'VodpodCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'WSDLSpider/1.0 (http://www.wsdlworld.com)'
    family: 'WSDLSpider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; WangIDSpider/1.0; +http://www.wangid.com/spider.html)'
    family: 'WangIDSpider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Web-Robot/5.0 (en-US; web-robot.com/policy.html) Web-Robot Crawler/2.0.3'
    family: 'Web-Robot'
    major: '5'
    minor: '0'
    patch:

  - user_agent_string: 'WebAlta Crawler/1.3.18 (http://www.webalta.net/ru/about_webmaster.html) (Windows; U; Windows NT 5.1; ru-RU)'
    family: 'WebAlta Crawler'
    major: '1'
    minor: '3'
    patch: '18'

  - user_agent_string: 'WebAlta Crawler/2.0 (http://www.webalta.net/ru/about_webmaster.html) (Windows; U; Windows NT 5.1; ru-RU)'
    family: 'WebAlta Crawler'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'WebIndexer/1-dev (Web Indexer; mailto://<EMAIL>; <EMAIL>)'
    family: 'WebIndexer'
    major: '1'
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Webbot/0.1; http://www.webbot.ru/bot.html)'
    family: 'Webbot'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Webspider/1.0 (web spider;  )'
    family: 'Webspider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'WinWebBot/1.0; (Balaena Ltd, UK); http://www.balaena.com/winwebbot.html; <EMAIL>;)'
    family: 'WinWebBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'WinkBot/0.06 (Wink.com search engine web crawler; http://www.wink.com/Wink:WinkBot; <EMAIL>)'
    family: 'WinkBot'
    major: '0'
    minor: '06'
    patch:

  - user_agent_string: 'Yahoo-MMCrawler/3.x (mm dash crawler at trd dot overture dot com)'
    family: 'Yahoo-MMCrawler'
    major: '3'
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Yahoo-MMCrawler/4.0; mailto:<EMAIL>)'
    family: 'Yahoo-MMCrawler'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'Yahoo-Newscrawler/3.9 (news-search-crawler at yahoo-inc dot com)'
    family: 'Yahoo-Newscrawler'
    major: '3'
    minor: '9'
    patch:

  - user_agent_string: 'Yahoo-VerticalCrawler-FormerWebCrawler/3.9 crawler at trd dot overture dot com; http://www.alltheweb.com/help/webmaster/crawler'
    family: 'Yahoo-VerticalCrawler-FormerWebCrawler'
    major: '3'
    minor: '9'
    patch:

  - user_agent_string: 'YoonoCrawler/1.0 (<EMAIL>)'
    family: 'YoonoCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; YoudaoBot/1.0; http://www.youdao.com/help/webmaster/spider/; )'
    family: 'YoudaoBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; ZemlyaCrawl/1.0; +http://zemlyaozer.com/bot)'
    family: 'ZemlyaCrawl'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: "Zeusbot/0.8.1 (Ulysseek's web-crawling robot; http://www.zeusbot.com; <EMAIL>)"
    family: 'Zeusbot'
    major: '0'
    minor: '8'
    patch: '1'

  - user_agent_string: 'agbot/1.0 (AgHaven.com search engine crawler; http://search.aghaven.com; <EMAIL>)'
    family: 'agbot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; archive_crawler/3.0.0-SNAPSHOT-20091205.013431  http://www.archive.org/details/archive_crawler)'
    family: 'archive_crawler'
    major: '3'
    minor: '0'
    patch: '0'

  - user_agent_string: 'audioCrawlerBot/1.0 (http://www.audiocrawler.com/)'
    family: 'audioCrawlerBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'awesomebar_scraper/1.0'
    family: 'awesomebar_scraper'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'blog_crawler/1.0'
    family: 'blog_crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; ca-crawler/1.0)'
    family: 'ca-crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'crawl/0.4 langcrawl/0.1'
    family: 'crawl'
    major: '0'
    minor: '4'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; crawler/3.0.0 +http://www.notconfigured.com/)'
    family: 'crawler'
    major: '3'
    minor: '0'
    patch: '0'

  - user_agent_string: 'dCrawlBot/1.0.1120'
    family: 'dCrawlBot'
    major: '1'
    minor: '0'
    patch: '1120'

  - user_agent_string: 'deepcrawler/3.1 (http://www.queusearch.com/whatis_deepcrawler.php),gzip(gfe),gzip(gfe)'
    family: 'deepcrawler'
    major: '3'
    minor: '1'
    patch:

  - user_agent_string: 'envolk[ITS]spider/1.6 ( http://www.envolk.com/envolkspider.html)'
    family: 'envolk[ITS]spider'
    major: '1'
    minor: '6'
    patch:

  - user_agent_string: 'fastlwspider/1.0'
    family: 'fastlwspider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'i1searchbot/2.0 (i1search web crawler; http://www.i1search.com; <EMAIL>)'
    family: 'i1searchbot'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; iaskspider/1.0; MSIE 6.0)'
    family: 'iaskspider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'iaskspider/2.0( http://iask.com/help/help_index.html)'
    family: 'iaskspider'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Liquida.it-Crawler/1.0 ( <EMAIL> +http://www.liquida.it )'
    family: 'it-Crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'it2media-domain-crawler/1.0 on crawler-prod.it2media.de'
    family: 'it2media-domain-crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'jrCrawler/1.0b'
    family: 'jrCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; loc-crawl/1.10.1 +http://www.google.com)'
    family: 'loc-crawl'
    major: '1'
    minor: '10'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (compatible; loc-crawler/0.11.0 +http://loc.gov),gzip(gfe),gzip(gfe),gzip(gfe)'
    family: 'loc-crawler'
    major: '0'
    minor: '11'
    patch: '0'

  - user_agent_string: 'DoCoMo/2.0 P904i( m65bot/0.1; c; http://m65.jp/bot.html )'
    family: 'm65bot'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'magpie-crawler/1.1 (U; Linux amd64; en-GB;  http://www.brandwatch.net)'
    family: 'magpie-crawler'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'noxtrumbot/1.0 (<EMAIL>)'
    family: 'noxtrumbot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'nyobot/1.1 (Noyb.com search engine crawler; http://www.noyb.com; <EMAIL>),gzip(gfe),gzip(gfe)'
    family: 'nyobot'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; oBot/2.3.1; +http://filterdb.iss.net/crawler/)'
    family: 'oBot'
    major: '2'
    minor: '3'
    patch: '1'

  - user_agent_string: 'omgilibot/0.3 +http://www.omgili.com/Crawler.html'
    family: 'omgilibot'
    major: '0'
    minor: '3'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; rogerBot/1.0; UrlCrawler; http://www.seomoz.org/dp/rogerbot)'
    family: 'rogerBot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'rogerbot/1.1 (http://moz.com/help/pro/what-is-rogerbot-, <EMAIL>)'
    family: 'rogerbot'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'semantics webbot/1.0'
    family: 'semantics webbot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; socialbm_bot/1.0; +http://spider.socialbm.net)'
    family: 'socialbm_bot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'tCrawler/0.1,gzip(gfe),gzip(gfe),gzip(gfe)'
    family: 'tCrawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'tivraSpider/1.0 (<EMAIL>)'
    family: 'tivraSpider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'webscraper/1.0'
    family: 'webscraper'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'wume_crawler/1.1 (http://wume.cse.lehigh.edu/~xiq204/crawler/)'
    family: 'wume_crawler'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'Abrave Spider v5.3 Robot 2 (http://robot.abrave.com)'
    family: '3 Robot'
    major: '2'
    minor:
    patch:

  - user_agent_string: 'Apexoo Spider 1.1'
    family: 'Apexoo Spider'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'BT Crawler 1.0,gzip(gfe),gzip(gfe),gzip(gfe)'
    family: 'BT Crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Bot Blocker Crawler 1.0 (btw, IncrediBILL says "HI!")'
    family: 'Bot Blocker Crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'BurstFind Crawler 1.1 - www.burstfind.com'
    family: 'BurstFind Crawler'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'Comodo Spider 1.1'
    family: 'Comodo Spider'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'Comodo Spider 1.2'
    family: 'Comodo Spider'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'Crawler 0.1,gzip(gfe),gzip(gfe)'
    family: 'Crawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'ExB Language Crawler 2.1.5 (+http://www.exb.de/crawler)'
    family: 'ExB Language Crawler'
    major: '2'
    minor: '1'
    patch: '5'

  - user_agent_string: 'Mozilla/5.0 (compatible; FAST Crawler 6.3)'
    family: 'FAST Crawler'
    major: '6'
    minor: '3'
    patch:

  - user_agent_string: 'FAST EnterpriseCrawler 6'
    family: 'FAST EnterpriseCrawler'
    major: '6'
    minor:
    patch:

  - user_agent_string: 'schibstedsokbot (compatible; Mozilla/5.0; MSIE 5.0; FAST FreshCrawler 6; Contact: <EMAIL>;)'
    family: 'FAST FreshCrawler'
    major: '6'
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; FatBot 2.0; http://www.thefind.com/crawler)'
    family: 'FatBot'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'Feedjit Favicon Crawler 1.0'
    family: 'Feedjit Favicon Crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'HubSpot Crawler 1.0 http://www.hubspot.com/'
    family: 'HubSpot Crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Jaxified Crawler 1.0a (+http://www.jaxified.com/),gzip(gfe),gzip(gfe)'
    family: 'Jaxified Crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'LinksCrawler 0.1beta'
    family: 'LinksCrawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Liquida Spider 1.0 +http://liquida.com/'
    family: 'Liquida Spider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'My Spider 1.0,gzip(gfe),gzip(gfe)'
    family: 'My Spider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'NWSpider 0.9'
    family: 'NWSpider'
    major: '0'
    minor: '9'
    patch:

  - user_agent_string: 'Netchart Adv Crawler 1.0'
    family: 'Netchart Adv Crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (NimbleCrawler 0.1) obeys NimbleCrawler <NAME_EMAIL>'
    family: 'NimbleCrawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows;) NimbleCrawler 2.0.1 obeys UserAgent NimbleCrawler For problems contact: <EMAIL>'
    family: 'NimbleCrawler'
    major: '2'
    minor: '0'
    patch: '1'

  - user_agent_string: 'OMGCrawler 1.0'
    family: 'OMGCrawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'SeekOn Spider 1.9(+http://www.seekon.com/spider.html)'
    family: 'SeekOn Spider'
    major: '1'
    minor: '9'
    patch:

  - user_agent_string: 'Toms Spider 0.3'
    family: 'Toms Spider'
    major: '0'
    minor: '3'
    patch:

  - user_agent_string: 'Tutorial Crawler 1.4'
    family: 'Tutorial Crawler'
    major: '1'
    minor: '4'
    patch:

  - user_agent_string: 'RB2B-bot v0.1 (Using Fast Enterprise Crawler 6 <EMAIL>)'
    family: 'Using Fast Enterprise Crawler'
    major: '6'
    minor:
    patch:

  - user_agent_string: 'WocBot/Mozilla/5.0 (Wocodi Web Crawler 1.0; http://www.wocodi.com/crawler; <EMAIL>)'
    family: 'Wocodi Web Crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0; WordSurfer Spider 2.2;))'
    family: 'WordSurfer Spider'
    major: '2'
    minor: '2'
    patch:

  - user_agent_string: 'XSpider 0.01;http://uncool.oicp.net/spider.html'
    family: 'XSpider'
    major: '0'
    minor: '01'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 7.0) XSpider 7'
    family: 'XSpider'
    major: '7'
    minor:
    patch:

  - user_agent_string: 'Xaldon WebSpider 2.7.b6'
    family: 'Xaldon WebSpider'
    major: '2'
    minor: '7'
    patch:

  - user_agent_string: 'echocrawl 2.0'
    family: 'echocrawl'
    major: '2'
    minor: '0'
    patch:

  - user_agent_string: 'enicura crawler 1.0,gzip(gfe),gzip(gfe)'
    family: 'enicura crawler'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; lworld spider 1.0; Windows NT 5.1)'
    family: 'lworld spider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'FAST-mSEARCH Crawler 0.1 (<EMAIL>)'
    family: 'FAST-mSEARCH Crawler'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: '123metaspider-Bot (Version: 1.04, powered by www.123metaspider.com)'
    family: '123metaspider-Bot'
    major:
    minor:
    patch:

  - user_agent_string: '360Spider'
    family: '360Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows; U; Win98; en-US; rv:1.8webcrawler) http://skateboarddirectory.com'
    family: '8webcrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'AESOP_com_SpiderMan'
    family: 'AESOP_com_SpiderMan'
    major:
    minor:
    patch:

  - user_agent_string: "AISearchBot (Email: <EMAIL>; If your web site doesn't want to be crawled, please send us a email.)"
    family: 'AISearchBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; DAWINCI ANTIPLAG SPIDER)'
    family: 'ANTIPLAG SPIDER'
    major:
    minor:
    patch:

  - user_agent_string: 'AcquiaCrawler,gzip(gfe),gzip(gfe)'
    family: 'AcquiaCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Adaxas Spider (http://www.adaxas.net/)'
    family: 'Adaxas Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0) AddSugarSpiderBot www.idealobserver.com'
    family: 'AddSugarSpiderBot'
    major:
    minor:
    patch:

  - user_agent_string: 'AdnormCrawler www.adnorm.com/crawler'
    family: 'AdnormCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'www.website-analyzer.net Website Analyzer Crawler'
    family: 'Analyzer Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Apexoo Spider (http://www.apexoo.com/spider/)'
    family: 'Apexoo Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'AppCodes crawler - looking for iOS app mentions. More info: <EMAIL>. Robots.txt id: AppCodesCrawler'
    family: 'AppCodes crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'ArchitextSpider'
    family: 'ArchitextSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'Arikus_Spider'
    family: 'Arikus_Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 6.0 compatible; Asterias Crawler v4;  http://www.singingfish.com/help/spider.html; <EMAIL>); SpiderThread  Revision: 3.0'
    family: 'Asterias Crawler'
    major: '4'
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible: AstraSpider V.2.1 : astrafind.com)'
    family: 'AstraSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'Autonomy Spider'
    family: 'Autonomy Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'axadine/  (Axadine Crawler; http://www.axada.de/;  )'
    family: 'Axadine Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; BIXOCRAWLER; +http://wiki.github.com/bixo/bixo/bixocrawler; <EMAIL>)'
    family: 'BIXOCRAWLER'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; BOTW Spider;  http://botw.org)'
    family: 'BOTW Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'BacklinkCrawler (http://www.backlinktest.com/crawler.html)'
    family: 'BacklinkCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'BaiduImagespider ( http://help.baidu.jp/system/05.html)'
    family: 'BaiduImagespider'
    major:
    minor:
    patch:

  - user_agent_string: 'BarraHomeCrawler (<EMAIL>)'
    family: 'BarraHomeCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'ZoomInfo::Beehive Crawler'
    family: 'Beehive Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'BeijingCrawler'
    family: 'BeijingCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'BejiBot Crawler (BNL Services; http://www.bejjan.net/crawler/)'
    family: 'BejiBot'
    major:
    minor:
    patch:

  - user_agent_string: 'BravoBrian SpiderEngine MarcoPolo'
    family: 'BravoBrian SpiderEngine'
    major:
    minor:
    patch:

  - user_agent_string: 'BrightCrawler (http://www.brightcloud.com/brightcrawler.asp)'
    family: 'BrightCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'BuildCMS crawler (http://www.buildcms.com/crawler)'
    family: 'BuildCMS crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'ByWebSite-Search-Spider,gzip(gfe),gzip(gfe)'
    family: 'ByWebSite-Search-Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'CFG_SPIDER_USER_AGENT'
    family: 'CFG_SPIDER_USER_AGENT'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (CMS Crawler: http://www.cmscrawler.com)'
    family: 'CMS Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'CMS crawler ( http://buytaert.net/crawler/)'
    family: 'CMS crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'CRAZYWEBCRAWLER 0.9.0, http://www.crazywebcrawler.com'
    family: 'CRAZYWEBCRAWLER'
    major: '0'
    minor: '9'
    patch: '0'

  - user_agent_string: 'CSimpleSpider Robot'
    family: 'CSimpleSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'Cityreview Robot (+http://www.cityreview.org/crawler/)'
    family: 'Cityreview Robot'
    major:
    minor:
    patch:

  - user_agent_string: 'Exalead Cloudview Crawler,gzip(gfe),gzip(gfe),gzip(gfe)'
    family: 'Cloudview Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Comodo-Certificates-Spider'
    family: 'Comodo-Certificates-Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Computer_and_Automation_Research_Institute_Crawler (<EMAIL>)'
    family: 'Computer_and_Automation_Research_Institute_Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Http Connector Spider, contact Alcatel-Lucent IDOL Search'
    family: 'Connector Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Content Crawler'
    family: 'Content Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'CowCrawler/CowCrawler-dev (+http://beta.cow.com),gzip(gfe),gzip(gfe)'
    family: 'CowCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; CrawlDaddy v0.3.0 abot v1.1.1.0 http://code.google.com/p/abot)'
    family: 'CrawlDaddy'
    major: '0'
    minor: '3'
    patch: '0'

  - user_agent_string: 'CrawlFire - you can disable this Robot: http://pastebin.de/25277'
    family: 'CrawlFire'
    major:
    minor:
    patch:

  - user_agent_string: 'CrawlWave/1.2 (crawlwave[at]circular.gr http://www.spiderwave.aueb.gr/'
    family: 'CrawlWave'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'CrawlerBoy Pinpoint.com'
    family: 'CrawlerBoy'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 5.0; Windows 98; DigExt; Crayon Crawler)'
    family: 'Crayon Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'DRKSpider - Website link validator - http://www.drk.com.ar/spider/,gzip(gfe),gzip(gfe),gzip(gfe)'
    family: 'DRKSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'DefaultCrawlTest/0.6 (Ram Crawl Test; devarajaswami at yahoo dot com)'
    family: 'DefaultCrawlTest'
    major: '0'
    minor: '6'
    patch:

  - user_agent_string: 'FFC Trap Door Spider'
    family: 'Door Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'DoubleVerify Crawler'
    family: 'DoubleVerify Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'EasouSpider'
    family: 'EasouSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'EcoGrader Crawler: Beta'
    family: 'EcoGrader Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Adknowledge Engage Crawler'
    family: 'Engage Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'ByWebSite Search Engine Spider'
    family: 'Engine Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'EverbeeCrawler'
    family: 'EverbeeCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Ex-Crawler/v0.1.5ALPHA SVN rev58; powered by ex-crawler; +http://www.ex-crawler.de/) Java/1.6.0_20,gzip(gfe),gzip(gfe)'
    family: 'Ex-Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'ExactSeek_Spider'
    family: 'ExactSeek_Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; FastCrawler3, <EMAIL>)'
    family: 'FastCrawler3'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Finderbots finder bot; +http://wiki.github.com/bixo/bixo/bixocrawler; <EMAIL>)'
    family: 'Finderbots'
    major:
    minor:
    patch:

  - user_agent_string: 'Findexa Crawler (http://www.findexa.no/gulesider/article26548.ece)'
    family: 'Findexa Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/3.0 (compatible; Fluffy the spider; http://www.searchhippo.com/; <EMAIL>)'
    family: 'Fluffy the spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 5.01; Windows NT 5.0) (Atsameip FreeCrawl!)'
    family: 'FreeCrawl'
    major:
    minor:
    patch:

  - user_agent_string: 'Fujiko Spider (<EMAIL>)'
    family: 'Fujiko Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; FunkyCrawler; )'
    family: 'FunkyCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'GSiteCrawler/v1.12 rev. 260 (http://gsitecrawler.com/)'
    family: 'GSiteCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'GeekTools Crawler - http://domains.geek-tools.org'
    family: 'GeekTools Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla 4.0 - GetMeLinked Spider www.GetMeLinked.com Web Directory'
    family: 'GetMeLinked Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Gnam Gnam Spider'
    family: 'Gnam Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; GoGuidesBot; http://www.goguides.org/spider.html)'
    family: 'GoGuidesBot'
    major:
    minor:
    patch:

  - user_agent_string: 'GoScraper'
    family: 'GoScraper'
    major:
    minor:
    patch:

  - user_agent_string: 'GrowerIdeas Crawler/GrowerIdeas-nutch-1.6 (Crawls URLs for indexing content for our new search startup which aims to provide simple and smart search across curated content. For more info <NAME_EMAIL>. If you think'
    family: 'GrowerIdeas Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Huaweisymantecspider (compatible; MSIE 7.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727),gzip(gfe)'
    family: 'Huaweisymantecspider'
    major:
    minor:
    patch:

  - user_agent_string: 'BlogPulse (ISSpider-3.0)'
    family: 'ISSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'IWE Spider v.01 - www.pavka.com.au'
    family: 'IWE Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'IXE Crawler'
    family: 'IXE Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Imperia-LinkSpider,gzip(gfe),gzip(gfe)'
    family: 'Imperia-LinkSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'netEstate Impressumscrawler (+http://www.netestate.de/De/Loesungen/Impressumscrawler)'
    family: 'Impressumscrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Inar_spider2 (<EMAIL>)'
    family: 'Inar_spider2'
    major:
    minor:
    patch:

  - user_agent_string: 'IOI/2.0 (ISC Open Index crawler; http://index.isc.org/; <EMAIL>)'
    family: 'Index crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 5.5; Windows 98; Infocrawler; Alexa Toolbar)'
    family: 'Infocrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; Inspyder-Crawler; http://www.inspyder.com)'
    family: 'Inspyder-Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Willow Internet Crawler by Twotrees V2.1'
    family: 'Internet Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (SKIZZLE! Distributed Internet Spider v1.0 - www.SKIZZLE.com)'
    family: 'Internet Spider'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'IssueCrawler'
    family: 'IssueCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'JUST-CRAWLER( http://www.justsystems.com/jp/tech/crawler/)'
    family: 'JUST-CRAWLER'
    major:
    minor:
    patch:

  - user_agent_string: 'Jayde Crawler. http://www.jayde.com'
    family: 'Jayde Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'JikeSpider; +http://shoulu.jike.com/spider.html'
    family: 'JikeSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible;WI Job Roboter Spider Version 3;+http://www.webintegration.at)'
    family: 'Job Roboter'
    major:
    minor:
    patch:

  - user_agent_string: 'JobSpider_BA/1.1'
    family: 'JobSpider_BA'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'KiwiStatus (NZS.com)/0.2 (NZS.com KiwiStatus Spider,  Local Search New Zealand; http://www.nzs.com; bot-at-nzs dot com)'
    family: 'KiwiStatus Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Kotoss Crawler http://hitokoto.kotoss.com,gzip(gfe),gzip(gfe)'
    family: 'Kotoss Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Kyluka crawl; <EMAIL>; http://www.kyluka.com/static/crawl.html)'
    family: 'Kyluka crawl'
    major:
    minor:
    patch:

  - user_agent_string: 'LNSpiderguy'
    family: 'LNSpiderguy'
    major:
    minor:
    patch:

  - user_agent_string: 'LarbinWebCrawler (<EMAIL>)'
    family: 'LarbinWebCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'LargeSmall Crawler'
    family: 'LargeSmall Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Lijit Crawler (+http://www.lijit.com/robot/crawler)'
    family: 'Lijit Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'DocWeb Link Crawler (http://doc.php.net)'
    family: 'Link Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Lipperhey Spider; http://www.lipperhey.com/)'
    family: 'Lipperhey Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'LookUpCrawler - lookupcanada.ca [ZSEBOT]'
    family: 'LookUpCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Lycos_Spider_(modspider)'
    family: 'Lycos_Spider_'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 5.5; Windows NT 5.0) METASpider'
    family: 'METASpider'
    major:
    minor:
    patch:

  - user_agent_string: 'MQbot http://metaquerier.cs.uiuc.edu/crawler'
    family: 'MQbot'
    major:
    minor:
    patch:

  - user_agent_string: 'MSIndianWebcrawl'
    family: 'MSIndianWebcrawl'
    major:
    minor:
    patch:

  - user_agent_string: 'MSR-ISRCCrawler'
    family: 'MSR-ISRCCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'MedSpider v0.0.1'
    family: 'MedSpider'
    major: '0'
    minor: '0'
    patch: '1'

  - user_agent_string: 'Social Media Crawler using your Home URL on Twitter,Facebook,Myspace,Linkedin by ProfileCapture - contact <EMAIL> to report any problems with my crawling. http://profilecapture.com'
    family: 'Media Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible); MSIE 5.0; Medialab Spider'
    family: 'Medialab Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'DomainsDB.net MetaCrawler v.0.9.7b (http://domainsdb.net/)'
    family: 'MetaCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'FAST MetaWeb Crawler (helpdesk at fastsearch dot com)'
    family: 'MetaWeb Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'CC Metadata Scaper http://wiki.creativecommons.org/Metadata_Scraper'
    family: 'Metadata_Scraper'
    major:
    minor:
    patch:

  - user_agent_string: "MicrosoftPrototypeCrawler (How's my crawling? mailto:<EMAIL>)"
    family: 'MicrosoftPrototypeCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; MixrankBot; <EMAIL>)'
    family: 'MixrankBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Sogou Mobile Spider1.0 (http://wap.sogou.com)'
    family: 'Mobile Spider1'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla-Firefox-Spider(Wenanry)'
    family: 'Mozilla-Firefox-Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'MultiCrawler, http://sw.deri.org/2006/04/multicrawler/robots.html'
    family: 'MultiCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows; U; Windows NT 6.1; en; rv:1.9.1.3; MySpaceScraper) Gecko/20090824 Firefox/3.5.3 (.NET CLR 3.5.30729)'
    family: 'MySpaceScraper'
    major:
    minor:
    patch:

  - user_agent_string: 'Norbert the Spider(Burf.com)'
    family: 'Norbert the Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'NuSearch Spider www.nusearch.com'
    family: 'NuSearch Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Nusearch Spider (compatible; MSIE 6.0)'
    family: 'Nusearch Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'OpenWebSpider (link collector; http://links.port30.se/cia.html) v (http://www.openwebspider.org/)'
    family: 'OpenWebSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; OpenX Spider; http://www.openx.org)'
    family: 'OpenX Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; OpenindexSpider; +http://www.openindex.io/en/webmasters/spider.html)'
    family: 'OpenindexSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'Orgbybot/OrgbyBot v1.2 (Spidering the net for Orgby; http://www.orgby.com/  ; Orgby.com Search Engine)'
    family: 'Orgbybot'
    major:
    minor:
    patch:

  - user_agent_string: 'PDFBot (<EMAIL>)'
    family: 'PDFBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0; SLCC1; .NET CLR 2.0.50727; .NET CLR 1.1.4322; InfoPath.2; .NET CLR 3.5.21022; .NET CLR 3.5.30729; MS-RTC LM 8; OfficeLiveConnector.1.4; OfficeLivePatch.1.3; .NET CLR 3.0.30729) Jakarta Commons-HttpClient/3.0-rc3 PHPCrawl GStreamer souphttpsrc libsoup/2.27.4 PycURL/7.19.0 XML-RPC for PHP 2.2.1 GoogleFriendConnect/1.0 HTMLParser/1.6 gPodder/0.15.2 ( http://gpodder.org/) anw webtool LoadControl/1.3 WinHttp urlgrabber/3.1.0'
    family: 'PHPCrawl'
    major:
    minor:
    patch:

  - user_agent_string: 'Fast PartnerSite Crawler'
    family: 'PartnerSite Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Patwebbot (http://www.herz-power.de/technik.html)'
    family: 'Patwebbot'
    major:
    minor:
    patch:

  - user_agent_string: 'PeerFactor Crawler'
    family: 'PeerFactor Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'PhoenixWebBot Beta'
    family: 'PhoenixWebBot'
    major:
    minor:
    patch:

  - user_agent_string: 'pipeLiner/0.10 (PipeLine Spider; http://www.pipeline-search.com/webmaster.html)'
    family: 'PipeLine Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'ProjectWF-java-test-crawler'
    family: 'ProjectWF-java-test-crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Punk Spider/PunkSPIDER-v0.1'
    family: 'Punk Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'QuerySeekerSpider ( http://queryseeker.com/bot.html )'
    family: 'QuerySeekerSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'QuickFinder Crawler'
    family: 'QuickFinder Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'OpenLink Virtuoso RDF crawler'
    family: 'RDF crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Jaxified Public RSS Crawler ( http://www.jaxified.com/)'
    family: 'RSS Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'RSS-SPIDER (RSS Feed Seeker http://www.MyNewFavoriteThing.com/fsb.php)'
    family: 'RSS-SPIDER'
    major:
    minor:
    patch:

  - user_agent_string: 'RavenCrawler'
    family: 'RavenCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'ReadPath_Spider'
    family: 'ReadPath_Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'LinkStar Research Crawler (http://linkstar.com/),gzip(gfe),gzip(gfe)'
    family: 'Research Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Research spider - <EMAIL>'
    family: 'Research spider'
    major:
    minor:
    patch:

  - user_agent_string: 'RoboCrawl (www.canadiancontent.net)'
    family: 'RoboCrawl'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.2; WOW64) Runet-Research-Crawler (itrack.ru/research/cmsrate; <EMAIL>)'
    family: 'Runet-Research-Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Teragram/SAS Crawler'
    family: 'SAS Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'SCrawlTest/CR1 (CWD; http://sproose.com; <EMAIL>)'
    family: 'SCrawlTest'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; SISTRIX Crawler; http://crawler.sistrix.net/)'
    family: 'SISTRIX Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.101 Safari/537.36; SSL-Crawler: http://crawler.dcsec.uni-hannover.de'
    family: 'SSL-Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'SWAT Crawler. AGH University project. In case of problem contact: <EMAIL>. Thanks.'
    family: 'SWAT Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'SandCrawler - Compatibility Testing'
    family: 'SandCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Sangfor Spider'
    family: 'Sangfor Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/3.0 (compatible; ScollSpider; http://www.webwobot.com)'
    family: 'ScollSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'ScreenerBot Crawler Beta 2.0 (+http://www.ScreenerBot.com)'
    family: 'ScreenerBot'
    major:
    minor:
    patch:

  - user_agent_string: 'SearchSpider.com/1.1'
    family: 'SearchSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'ownCloud Server Crawler'
    family: 'Server Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Shim-Crawler'
    family: 'Shim-Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'ShowyouBot (http://showyou.com/crawler)'
    family: 'crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.0) SiteCheck-sitecrawl by Siteimprove.com'
    family: 'SiteCheck-sitecrawl'
    major:
    minor:
    patch:

  - user_agent_string: 'SiteCrawler,gzip(gfe),gzip(gfe)'
    family: 'SiteCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.5 (compatible; MSIE 5.5; Windows NT 5.1) Sitespider+ b432.1'
    family: 'Sitespider'
    major:
    minor:
    patch:

  - user_agent_string: 'SocialSpider-Finder/0.2'
    family: 'SocialSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'Sosoblogspider+(+http://help.soso.com/soso-blog-spider.htm)'
    family: 'Sosoblogspider'
    major:
    minor:
    patch:

  - user_agent_string: 'Sosoimagespider ( http://help.soso.com/soso-image-spider.htm)'
    family: 'Sosoimagespider'
    major:
    minor:
    patch:

  - user_agent_string: 'Sosospider+(+help.soso.com/webspider.htm)'
    family: 'Sosospider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; SpeedySpider; www.entireweb.com)'
    family: 'SpeedySpider'
    major:
    minor:
    patch:

  - user_agent_string: 'SpiderKU/0.9'
    family: 'SpiderKU'
    major: '0'
    minor: '9'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; SpiderLing (a SPIDER for LINGustic research); +http://nlp.fi.muni.cz/projects/biwec/)'
    family: 'SpiderLing'
    major:
    minor:
    patch:

  - user_agent_string: 'SpiderMan 3.0.1-2-11-111 (CP/M;8-bit)'
    family: 'SpiderMan'
    major: '3'
    minor: '0'
    patch: '1'

  - user_agent_string: 'Mozilla/4.0 (compatible; SpiderView 1.0;unix)'
    family: 'SpiderView'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Spider_Monkey/7.06 (SpiderMonkey.ca info at http://SpiderMonkey.ca /sm.shtml)'
    family: 'Spider_Monkey'
    major: '7'
    minor: '06'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Spiderlytics/1.0; +<EMAIL>)'
    family: 'Spiderlytics'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'PKU Student Spider'
    family: 'Student Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Symfony Spider (http://symfony.com/spider)'
    family: 'Symfony Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Synthesio Crawler release MonaLisa (contact at synthesio dot fr)'
    family: 'Synthesio Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'TAMU_CS_IRL_CRAWLER/1.0'
    family: 'TAMU_CS_IRL_CRAWLER'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'TTop-Crawler,gzip(gfe),gzip(gfe)'
    family: 'TTop-Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'TayaCrawler (Beta; v0.1; <EMAIL>)'
    family: 'TayaCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'TelemetrySpider2/0.1 linux'
    family: 'TelemetrySpider2'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Theme Spider ( http://www.themespider.com/spider.html)'
    family: 'Theme Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Thesis_php_crawler'
    family: 'Thesis_php_crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Trends Crawler, Real time trends bot (<EMAIL>)'
    family: 'Trends Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'TwitSpider'
    family: 'TwitSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'Twitmunin Crawler http://www.twitmunin.com'
    family: 'Twitmunin Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; TwitterCrawler)'
    family: 'TwitterCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'UCMore Crawler App'
    family: 'UCMore Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'UOLCrawler (<EMAIL>)'
    family: 'UOLCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Links4US-Crawler, ( http://links4us.com/)'
    family: 'US-Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'USyd-NLP-Spider (http://www.it.usyd.edu.au/~vinci/bot.html)'
    family: 'USyd-NLP-Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; UnisterBot; <EMAIL>)'
    family: 'UnisterBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0, VM-Crawler/cs version info  ofni noisrev sc'
    family: 'VM-Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'WEBB Crawler - see: http://badcheese.com/robots.html'
    family: 'WEBB Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'WSDL Crawler'
    family: 'WSDL Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'EmeraldShield.com WebBot'
    family: 'com WebBot'
    major:
    minor:
    patch:

  - user_agent_string: 'WebCompanyCrawler'
    family: 'WebCompanyCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'WebCrawler v1.3'
    family: 'WebCrawler'
    major: '1'
    minor: '3'
    patch:

  - user_agent_string: 'WebVulnCrawl.blogspot.com/1.0 libwww-perl/5.803'
    family: 'WebVulnCrawl'
    major:
    minor:
    patch:

  - user_agent_string: 'CyberPatrol SiteCat Webbot (http://www.cyberpatrol.com/cyberpatrolcrawler.asp)'
    family: 'SiteCat Webbot'
    major:
    minor:
    patch:

  - user_agent_string: 'EricssonR320/R1A (Fast Wireless Crawler)'
    family: 'Wireless Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'X-Crawler'
    family: 'X-Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'XYZ Spider'
    family: 'XYZ Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Y!J-BRJ/YATS crawler (http://help.yahoo.co.jp/help/jp/search/indexing/indexing-15.html)'
    family: 'YATS crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Y!J-BRO/YFSJ crawler (compatible; Mozilla 4.0; MSIE 5.5; http://help.yahoo.co.jp/help/jp/search/indexing/indexing-15.html; YahooFeedSeekerJp/2.0)'
    family: 'YFSJ crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'YRL_ODP_CRAWLER'
    family: 'YRL_ODP_CRAWLER'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Android 3.0; YRSpider; +http://www.yunrang.com/yrspider.html)'
    family: 'YRSpider'
    major:
    minor:
    patch:

  - user_agent_string: "YebolBot (Email: <EMAIL>; If the web crawling affects your web service, or you don't like to be crawled by us, please email us. We'll stop crawling immediately.)"
    family: 'YebolBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; Yet-Another-Spider; )'
    family: 'Yet-Another-Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'YisouSpider'
    family: 'YisouSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'NL-Crawler'
    family: 'NL-Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; YoudaoBot-rts/1.0; http://www.youdao.com/help/webmaster/spider/; )'
    family: 'YoudaoBot'
    major:
    minor:
    patch:

  - user_agent_string: 'ZIBB Crawler (email address / WWW address)'
    family: 'ZIBB Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'ZillaCrawler'
    family: 'ZillaCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'ZoomSpider - wrensoft.com'
    family: 'ZoomSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'Zspider (+http://www.zhanzhangsou.com/index.htm)'
    family: 'Zspider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; acedo crawler extension)'
    family: 'acedo crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'acquia-crawler (detected bad behaviour? please tell <NAME_EMAIL>),gzip(gfe),gzip(gfe)'
    family: 'acquia-crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.21 (KHTML, like Gecko) adspider Safari/537.21'
    family: 'adspider'
    major:
    minor:
    patch:

  - user_agent_string: 'amphetameme crawler (<EMAIL>)'
    family: 'amphetameme crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; ayna-crawler  http://www.ayna.com)'
    family: 'ayna-crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Bender; http://sites.google.com/site/bendercrawler)'
    family: 'bendercrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'snap.com beta crawler v0'
    family: 'beta crawler'
    major: '0'
    minor:
    patch:

  - user_agent_string: 'blackspider'
    family: 'blackspider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; bmdspider; windows 5.1)'
    family: 'bmdspider'
    major:
    minor:
    patch:

  - user_agent_string: 'boitho crawler'
    family: 'boitho crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'cis455crawler'
    family: 'cis455crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'http://www.Syntryx.com/ ANT Chassis 9.27; Mozilla/4.0 compatible crawler'
    family: 'compatible crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'crawler4j (http://code.google.com/p/crawler4j/)'
    family: 'crawler4j'
    major:
    minor:
    patch:

  - user_agent_string: 'dtSearchSpider'
    family: 'dtSearchSpider'
    major:
    minor:
    patch:

  - user_agent_string: 'Enterprise_Search/1.0 110 (http://www.innerprise.net/es-spider.asp)'
    family: 'es-spider'
    major:
    minor:
    patch:

  - user_agent_string: 'eseek-crawler.0.5 (<EMAIL>)'
    family: 'eseek-crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'exooba/exooba crawler (exooba; exooba)'
    family: 'exooba crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'flatlandbot/allspark (Flatland Industries Web Spider; http://www.flatlandindustries.com/flatlandbot; <EMAIL>)'
    family: 'flatlandbot'
    major:
    minor:
    patch:

  - user_agent_string: 'haupia-crawler'
    family: 'haupia-crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'hitcrawler_0.1 (<EMAIL>)'
    family: 'hitcrawler_0'
    major:
    minor:
    patch:

  - user_agent_string: 'iaskspider2 (<EMAIL>)'
    family: 'iaskspider2'
    major:
    minor:
    patch:

  - user_agent_string: 'visaduhoc.info Crawler'
    family: 'info Crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'ip-web-crawler.com'
    family: 'ip-web-crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'jikespider "Mozilla/5.0'
    family: 'jikespider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 5.5; Windows NT 5.0) compatible; <EMAIL>'
    family: 'kototoi-crawl'
    major:
    minor:
    patch:

  - user_agent_string: 'lb-spider'
    family: 'lb-spider'
    major:
    minor:
    patch:

  - user_agent_string: 'ldspider (BTC 2011 crawl, <EMAIL>, http://code.google.com/p/ldspider/wiki/Robots)'
    family: 'ldspider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; lemurwebcrawler <EMAIL>; +http://boston.lti.cs.cmu.edu/crawler_12/)'
    family: 'lemurwebcrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'lmspider (<EMAIL>)'
    family: 'lmspider'
    major:
    minor:
    patch:

  - user_agent_string: 'lworldspider'
    family: 'lworldspider'
    major:
    minor:
    patch:

  - user_agent_string: 'trunk.<NAME_EMAIL>'
    family: 'ly spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Vodafone mCrawler (<EMAIL>)'
    family: 'mCrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'media-percbotspider <<EMAIL>>'
    family: 'media-percbotspider'
    major:
    minor:
    patch:

  - user_agent_string: 'DoCoMo/2.0 N902iS(c100;TB;W24H12)(compatible; moba-crawler; http://crawler.dena.jp/)'
    family: 'moba-crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Nokia6680/1.0 ((4.04.07) SymbianOS/8.0 Series60/2.6 Profile/MIDP-2.0 Configuration/CLDC-1.1 (for mobile crawler) )'
    family: 'mobile crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'n4p_bot (<EMAIL>)'
    family: 'n4p_bot'
    major:
    minor:
    patch:

  - user_agent_string: 'na-Webcrawler (<EMAIL>)'
    family: 'na-Webcrawler'
    major:
    minor:
    patch:

  - user_agent_string: "nuSearch Spider <a href='http://www.nusearch.com'>www.nusearch.com</a> (compatible; MSIE 4.01; Windows NT)"
    family: 'nuSearch Spider'
    major:
    minor:
    patch:

  - user_agent_string: 'parallelContextFocusCrawler1.1parallelContextFocusCrawler1.1'
    family: 'parallelContextFocusCrawler1'
    major:
    minor:
    patch:

  - user_agent_string: 'persomm-spider/v1.0'
    family: 'persomm-spider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0; http://www.pregnancycrawler.com)'
    family: 'pregnancycrawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; ptd-crawler;  http://bixolabs.com/crawler/ptd/; <EMAIL>)'
    family: 'ptd-crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; pub-crawler; +http://wiki.github.com/bixo/bixo/bixocrawler; <EMAIL>)'
    family: 'pub-crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'pythonic-crawler (<EMAIL>)'
    family: 'pythonic-crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.2; WOW64) Russian CMS rating crawler (itrack.ru/cmsrate, <EMAIL>)'
    family: 'rating crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'camoca/camoca-n.1.2 (super-agent; search crawler; info at does not exist dot com)'
    family: 'search crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; sgbot v0.01a, <EMAIL>)'
    family: 'sgbot'
    major: '0'
    minor: '01'
    patch:

  - user_agent_string: 'spiderpig/0.1,gzip(gfe),gzip(gfe)'
    family: 'spiderpig'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'ssearch_bot (sSearch Crawler; http://www.semantissimo.de)'
    family: 'ssearch_bot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; suggybot v0.01a, http://blog.suggy.com/was-ist-suggy/suggy-webcrawler/)'
    family: 'suggybot'
    major: '0'
    minor: '01'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; uMBot-FC/1.0; mailto: <EMAIL>)'
    family: 'uMBot'
    major:
    minor:
    patch:

  - user_agent_string: 'unchaos_crawler_2.0.2 (<EMAIL>)'
    family: 'unchaos_crawler_2'
    major:
    minor:
    patch:

  - user_agent_string: 'updated/0.1-alpha (updated crawler; http://www.updated.com; <EMAIL>)'
    family: 'updated crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Yahoo-Test/4.0 mailto:<EMAIL>)'
    family: 'vertical-crawl'
    major:
    minor:
    patch:

  - user_agent_string: '<EMAIL>'
    family: 'yp-crawl'
    major:
    minor:
    patch:

  - user_agent_string: 'yrspider (Mozilla/5.0 (compatible; YRSpider;  http://www.yunrang.com/yrspider.html))'
    family: 'yrspider'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Yahoo-Test/4.0 <EMAIL>)'
    family: 'ysm-keystone-crawl'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; zyklop; +http://www.seoratio.de/zyklop-crawler/)'
    family: 'zyklop-crawler'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.2.1; en-gb; CUBOT ONE Build/JOP40D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30'
    family: 'Android'
    major: '4'
    minor: '2'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9) AppleWebKit/537.71 (KHTML, like Gecko) Version/7.0 Safari/537.71 (Rival IQ, rivaliq.com)'
    family: 'Rival IQ'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; en-gb) AppleWebKit/418.9.1 (KHTML, like Gecko) SiteCon/8.10.9'
    family: 'SiteCon'
    major: '8'
    minor: '10'
    patch: '9'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 8_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) FxiOS/1.0 Mobile/12F69 Safari/600.1.4'
    family: 'Firefox iOS'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPad; CPU iPhone OS 8_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) FxiOS/1.0 Mobile/12F69 Safari/600.1.4'
    family: 'Firefox iOS'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_4) AppleWebKit/537.36 (KHTML, like Gecko) Spotify/********* Safari/537.36'
    family: 'Spotify'
    major: '1'
    minor: '0'
    patch: '9'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Spotify/********* Safari/537.36'
    family: 'Spotify'
    major: '1'
    minor: '0'
    patch: '9'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/537.36 (KHTML, like Gecko) Spotify/******** Safari/537.36'
    family: 'Spotify'
    major: '1'
    minor: '0'
    patch: '4'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Spotify/********* Safari/537.36'
    family: 'Spotify'
    major: '1'
    minor: '0'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_5) AppleWebKit/537.36 (KHTML, like Gecko) Spotify/1.0.8.59 Safari/537.36'
    family: 'Spotify'
    major: '1'
    minor: '0'
    patch: '8'

  - user_agent_string: 'Mozilla/5.0 (Android 5.0; Tablet; rv:41.0) Gecko/41.0 Firefox/41.0'
    family: 'Firefox Mobile'
    major: '41'
    minor: '0'
    patch:

  - user_agent_string: '[FBAN/FB4A;FBAV/3.4;FBBV/258875;FBDM/{density=0.75,width=240,height=320};FBLC/tr_TR;FBCR/o2 - de;FBPN/com.facebook.katana;FBDV/LG-E400;FBSV/2.3.6;]'
    family: 'Facebook'
    major: '3'
    minor: '4'
    patch:

  - user_agent_string: '[FBAN/FB4A;FBAV/2.3;FBBV/149649;FBDM/{density=1.5,width=480,height=800};FBLC/es_ES;FBCR/;FBPN/com.facebook.katana;FBDV/LG-P920;FBSV/2.2.2;]'
    family: 'Facebook'
    major: '2'
    minor: '3'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 2.3.4; en-us; SCH-R720 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 [FBAN/FB4A;FBAV/1.8.1;FBPN/com.facebook.katana;FBDV/SCH-R720;FBSV/2.3.4;FBDM/{density=1.0,width=320,height='
    family: 'Facebook'
    major: '1'
    minor: '8'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; 706_v92_jbla_fhd Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 [FB_IAB/FB4A;FBAV/*********.15;]'
    family: 'Facebook'
    major: '24'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B329 [FBAN/FBIOS;FBAV/6.5.1;FBBV/377040;FBDV/iPhone4,1;FBMD/iPhone;FBSN/iPhone OS;FBSV/6.1.3;FBSS/2; FBCR/Telekom.de;FBID/phone;FBLC/de_DE;'
    family: 'Facebook'
    major: '6'
    minor: '5'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B329 [FBAN/FBIOS;FBAV/6.2;FBBV/228172;FBDV/iPhone5,1;FBMD/iPhone;FBSN/iPhone OS;FBSV/6.1.3;FBSS/2; FBCR/o2-de;FBID/phone;FBLC/pt_BR;FBOP/1]'
    family: 'Facebook'
    major: '6'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPad; CPU OS 5_0 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9A334 [FBAN/FBIOS;FBAV/6.5.1;FBBV/377040;FBDV/iPad2,1;FBMD/iPad;FBSN/iPhone OS;FBSV/5.0;FBSS/1; FBCR/;FBID/tablet;FBLC/de_DE;FBOP/1]'
    family: 'Facebook'
    major: '6'
    minor: '5'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (iPad; U; CPU iPhone OS 4_3_2 like Mac OS X; de_DE) AppleWebKit (KHTML, like Gecko) Mobile [FBAN/FBForIPhone;FBAV/4.0.2;FBBV/4020.0;FBDV/iPad1,1;FBMD/iPad;FBSN/iPhone OS;FBSV/4.3.2;FBSS/1; FBCR/;FBID/tablet;FBLC/de_DE;FBSF/1.0]'
    family: 'Facebook'
    major: '4'
    minor: '0'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (iPad; U; CPU iPhone OS 4_3_5 like Mac OS X; de_DE) AppleWebKit (KHTML, like Gecko) Mobile [FBAN/FBForIPhone;FBAV/4.0.3;FBBV/4030.0;FBDV/iPad2,2;FBMD/iPad;FBSN/iPhone OS;FBSV/4.3.5;FBSS/1; FBCR/Telekom.de;FBID/tablet;FBLC/de_DE;FBS'
    family: 'Facebook'
    major: '4'
    minor: '0'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 5_1_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9B206 [FBAN/FBIOS;FBAV/6.1;FBBV/201075;FBDV/iPhone3,1;FBMD/iPhone;FBSN/iPhone OS;FBSV/5.1.1;FBSS/2; FBCR/Vodafone.de;FBID/phone;FBLC/en_US;FBOP/1]'
    family: 'Facebook'
    major: '6'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 8_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Mobile/12D508 [FBAN/GroupsForiOS;FBAV/9.0;FBBV/7752968;FBDV/iPhone7,2;FBMD/iPhone;FBSN/iPhone OS;FBSV/8.2;FBSS/2; FBCR/Telekom.de;FBID/phone;FBLC/de_'
    family: 'Facebook'
    major: '9'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_1 like Mac OS X; fr_FR) AppleWebKit (KHTML, like Gecko) Mobile [FBAN/FBForIPhone;FBAV/4.0;FBBV/4000.0;FBDV/iPhone1,2;FBMD/iPhone;FBSN/iPhone OS;FBSV/4.1;FBSS/1; FBCR/Carrier;FBID/phone;FBLC/fr_FR;FBSF/1.'
    family: 'Facebook'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'Dalvik/1.2.0 (Linux; U; Android 2.2.2; HTC Desire Build/FRG83G) [FBAN/Orca-Android;FBAV/2.6.1-release;FBLC/de_DE;FBBV/288543;FBCR/o2 - de;FBMF/HTC;FBBD/htc_wwe;FBDV/HTC Desire;FBSV/2.2.2]'
    family: 'Facebook'
    major: '2'
    minor: '6'
    patch: '1'

  - user_agent_string: '[FBAN/FB4A;FBAV/3.6;FBBV/330148;FBDM/{density=0.75,width=240,height=320};FBLC/de_DE;FBCR/o2 - de;FBPN/com.facebook.katana;FBDV/GT-S5570;FBSV/2.2.1;FBCA/armeabi:unknown;]'
    family: 'Facebook'
    major: '3'
    minor: '6'
    patch:

  - user_agent_string: '[FBAN/PAAA;FBAV/1.7;FBDM/{density=2.0,width=720,height=1280};FBLC/es_ES;FB_FW/2;FBSN/Android;FBCR/FONIC;FBDV/GT-I9300;FBSV/4.1.2;]'
    family: 'Facebook'
    major: '1'
    minor: '7'
    patch:

  - user_agent_string: '[FBAN/PAAA;FBAV/1.9;FBDM/{density=2.0,width=720,height=1280};FBLC/de_DE;FB_FW/2;FBSN/Android;FBCR/o2 - de;FBDV/GT-I9300;FBSV/4.3;]'
    family: 'Facebook'
    major: '1'
    minor: '9'
    patch:

  - user_agent_string: '[FBAN/FB4A;FBAV/130.0.321;FBBV/149649;FBDM/{density=1.5,width=480,height=800};FBLC/es_ES;FBCR/;FBPN/com.facebook.katana;FBDV/LG-P920;FBSV/2.2.2;]'
    family: 'Facebook'
    major: '130'
    minor: '0'
    patch: '321'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15G77 [FBAN/FBIOS;FBDV/iPhone10,4;FBMD/iPhone;FBSN/iOS;FBSV/11.4.1;FBSS/2;FBCR/A1;FBID/phone;FBLC/de_DE;FBOP/5;FBRV/122166081]'
    family: 'Facebook'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 10_2_1 like Mac OS X) AppleWebKit/602.4.6 (KHTML, like Gecko) Mobile/14D27 [FBAN/MessengerForiOS;FBAV/**********.70;FBBV/63293619;FBDV/iPhone7,1;FBMD/iPhone;FBSN/iOS;FBSV/10.2.1;FBSS/3;FBCR/Viettel;FBID/phone;FBLC/vi_VN;FBOP/5;FBRV/0]'
    family: 'Facebook Messenger'
    major: '124'
    minor: '0'
    patch: '0'
    patch_minor: '50'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 6.0.1; SM-A910F Build/MMB29M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/58.0.3029.83 Mobile Safari/537.36 [FB_IAB/MESSENGER;FBAV/**********.84;]'
    family: 'Facebook Messenger'
    major: '120'
    minor: '0'
    patch: '0'
    patch_minor: '14'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15G77 [FBAN/FBIOS;FBAV/**********.99;FBBV/127868476;FBDV/iPhone7,2;FBMD/iPhone;FBSN/iOS;FBSV/11.4.1;FBSS/2;FBCR/OrangeBotswana;FBID/phone;FBLC/en_GB;FBOP/5;FBRV/128807018]'
    family: 'Facebook'
    major: '194'
    minor: '0'
    patch: '0'
    patch_minor: '38'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_4 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B350 [Pinterest/iOS]'
    family: 'Pinterest'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 4.4.2; A3-A11 Build/KOT49H) AppleWebKit/537.36 (KHTML like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36 [Pinterest/Android]'
    family: 'Pinterest'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10A525 [Pinterest/iOS]'
    family: 'Pinterest'
    major:
    minor:
    patch:

  - user_agent_string: 'Pinterest for Android Tablet/1.8.4 (SGP321; 4.3)'
    family: 'Pinterest'
    major: '1'
    minor: '8'
    patch: '4'

  - user_agent_string: 'Pinterest for Android Tablet/4.3.1 (A7600-H; 4.4.2)'
    family: 'Pinterest'
    major: '4'
    minor: '3'
    patch: '1'

  - user_agent_string: 'Pinterest for Android/1.1.1 (endeavoru; 4.1.1)'
    family: 'Pinterest'
    major: '1'
    minor: '1'
    patch: '1'

  - user_agent_string: 'Pinterest for Android/3.6.2 (klte; 4.4.2)'
    family: 'Pinterest'
    major: '3'
    minor: '6'
    patch: '2'

  - user_agent_string: 'Pinterest/0.1'
    family: 'Pinterest'
    major: '0'
    minor: '1'
    patch:

  - user_agent_string: 'Pinterest/0.2 (+http://www.pinterest.com/)'
    family: 'Pinterest'
    major: '0'
    minor: '2'
    patch:

  - user_agent_string: 'Pinterest for Android/1.1.12 (endeavoru; 4.1.1)'
    family: 'Pinterest'
    major: '1'
    minor: '1'
    patch: '12'

  - user_agent_string: 'Pinterest/3.2 CFNetwork/672.0.8 Darwin/14.0.0'
    family: 'Pinterest'
    major: '3'
    minor: '2'
    patch:

  - user_agent_string: 'Pinterest/3.3.3 CFNetwork/609.1.4 Darwin/13.0.0'
    family: 'Pinterest'
    major: '3'
    minor: '3'
    patch: '3'

  - user_agent_string: 'Pinterest/3356 CFNetwork/711.0.6 Darwin/14.0.0'
    family: 'Pinterest'
    major: '3356'
    minor:
    patch:

  - user_agent_string: 'Pinterest/4.1.3 CFNetwork/672.1.14 Darwin/14.0.0'
    family: 'Pinterest'
    major: '4'
    minor: '1'
    patch: '3'

  - user_agent_string: 'Pinterest/0.2 (+https://www.pinterest.com/bot.html)'
    family: 'Pinterestbot'
    major: '0'
    minor: '2'
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; Pinterestbot/1.0; +https://www.pinterest.com/bot.html)'
    family: 'Pinterestbot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.96 Mobile Safari/537.36 (compatible; Pinterestbot/1.0; +https://www.pinterest.com/bot.html)'
    family: 'Pinterestbot'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/538.1 (KHTML, like Gecko) qutebrowser/0.2.1 Safari/538.1'
    family: 'qutebrowser'
    major: '0'
    minor: '2'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_4) AppleWebKit/537.36 (KHTML, like Gecko) pagedraw/0.1.0 Chrome/49.0.2623.75 Electron/0.37.8 Safari/537.36'
    family: 'Electron'
    major: '0'
    minor: '37'
    patch: '8'

  - user_agent_string: 'Mozilla/5.0 (iPad; U; CPU OS 4_3_2 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Mobile'
    family: 'Mobile Safari UI/WKWebView'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPad; U; CPU OS 4_3_2 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8H7 Safari'
    family: 'Mobile Safari'
    major: '5'
    minor: '0'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X) AppleWebKit/538.1 (KHTML, like Gecko) QupZilla/1.6.3 Safari/538.1'
    family: 'QupZilla'
    major: '1'
    minor: '6'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (OS/2 Warp 4.5) AppleWebKit/537.21 (KHTML, like Gecko) QupZilla/1.6.4 Safari/537.21'
    family: 'QupZilla'
    major: '1'
    minor: '6'
    patch: '4'

  - user_agent_string: 'Mozilla/5.0 (Unknown; UNIX BSD/SYSV system) AppleWebKit/534.34 (KHTML, like Gecko) QupZilla/1.7.0 Safari/534.34'
    family: 'QupZilla'
    major: '1'
    minor: '7'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.21 (KHTML, like Gecko) QupZilla/1.6.1 Safari/537.21'
    family: 'QupZilla'
    major: '1'
    minor: '6'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/538.1 (KHTML, like Gecko) Otter/0.9.03 beta 3 Safari/538.1'
    family: 'Otter'
    major: '0'
    minor: '9'
    patch: '03'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/538.1 (KHTML, like Gecko) Otter/0.9.04'
    family: 'Otter'
    major: '0'
    minor: '9'
    patch: '04'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.21 (KHTML, like Gecko) Otter/0.9.04-dev'
    family: 'Otter'
    major: '0'
    minor: '9'
    patch: '04'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/538.1 (KHTML, like Gecko) Otter/0.3.01-dev Safari/538.1'
    family: 'Otter'
    major: '0'
    minor: '3'
    patch: '01'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/538.1 (KHTML, like Gecko) Otter/0.9.03 beta 3 Safari/538.1'
    family: 'Otter'
    major: '0'
    minor: '9'
    patch: '03'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/538.1 (KHTML, like Gecko) Otter/0.9.05'
    family: 'Otter'
    major: '0'
    minor: '9'
    patch: '05'

  - user_agent_string: 'Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; NOKIA; Lumia 930) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.71 Mobile Safari/537.36 Edge/12.0'
    family: 'Edge Mobile'
    major: '12'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 EdgiOS/********* Mobile/15E148 Safari/604.1'
    family: 'Edge Mobile'
    major: '44'
    minor: '5'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (iPad; CPU OS 12_5_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 EdgiOS/46.3.26 Mobile/15E148 Safari/605.1.15'
    family: 'Edge Mobile'
    major: '46'
    minor: '3'
    patch: '26'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 8.1.0; Pixel Build/OPM4.171019.021.D1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.109 Mobile Safari/537.36 EdgA/42.0.0.2057'
    family: 'Edge Mobile'
    major: '42'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_2) AppleWebKit/537.36 (KHTML, like Gecko) brave/0.7.11 Chrome/47.0.2526.110 Brave/0.36.5 Safari/537.36'
    family: 'Brave'
    major: '0'
    minor: '7'
    patch: '11'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) brave/0.7.12 Chrome/47.0.2526.110 Brave/0.36.7 Safari/537.36 '
    family: 'Brave'
    major: '0'
    minor: '7'
    patch: '12'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) brave/0.7.10 Chrome/47.0.2526.110 Brave/0.36.5 Safari/537.36'
    family: 'Brave'
    major: '0'
    minor: '7'
    patch: '10'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome Safari/537.36'
    family: 'HeadlessChrome'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_1) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/62.0.3202.89 Safari/537.36'
    family: 'HeadlessChrome'
    major: '62'
    minor: '0'
    patch: '3202'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/59.0.3071.109 HeadlessChrome/59.0.3071.109 Safari/537.36'
    family: 'HeadlessChrome'
    major: '59'
    minor: '0'
    patch: '3071'

  - user_agent_string: 'Roku/DVP-6.2 (096.02E06005A)'
    family: 'Roku'
    major: '6'
    minor: '2'
    patch:

  - user_agent_string: 'Roku/DVP-5.0 (025.00E08043A)'
    family: 'Roku'
    major: '5'
    minor: '0'
    patch:

  - user_agent_string: 'Roku/DVP-5.1 (025.01E01195A)'
    family: 'Roku'
    major: '5'
    minor: '1'
    patch:

  - user_agent_string: 'Microsoft Office/12.0 (Windows NT 6.1; Microsoft Office Outlook 12.0.6739; Pro)'
    family: 'Outlook'
    major: '2007'
    minor:
    patch:

  - user_agent_string: 'Microsoft Office/14.0 (Windows NT 6.1; Microsoft Outlook 14.0.5128; Pro)'
    family: 'Outlook'
    major: '2010'
    minor:
    patch:

  - user_agent_string: 'Microsoft Office/16.0 (Microsoft Outlook Mail 16.0.6525; Pro)'
    family: 'Outlook'
    major: '2016'
    minor:
    patch:

  - user_agent_string: 'Microsoft Office/16.0 (Windows NT 10.0; Microsoft Outlook 16.0.6326; Pro)'
    family: 'Outlook'
    major: '2016'
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/8.0; .NET4.0C; .NET4.0E; .NET CLR 2.0.50727; .NET CLR 3.0.30729; .NET CLR 3.5.30729; Microsoft Outlook 16.0.6366; ms-office; MSOffice 16)'
    family: 'Outlook'
    major: '2016'
    minor:
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; ms-office; MSOffice 16)'
    family: 'Outlook'
    major: '2016'
    minor:
    patch:

  - user_agent_string: 'Outlook-Express/7.0 (MSIE 7.0; Windows NT 6.1; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; TmstmpExt)'
    family: 'Windows Live Mail'
    major:
    minor:
    patch:

  - user_agent_string: 'Outlook-Express/7.0 (MSIE 7.0; Windows NT 5.1; Trident/4.0; AskTB5.6; TmstmpExt)'
    family: 'Windows Live Mail'
    major:
    minor:
    patch:

  - user_agent_string: 'Outlook-Express/7.0 (MSIE 7.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; Media Center PC 6.0; OfficeLiveConnector.1.4; OfficeLivePatch.1.3; InfoPath.3; FDM; TmstmpExt)'
    family: 'Windows Live Mail'
    major:
    minor:
    patch:

  - user_agent_string: 'Outlook-Express/7.0 (MSIE 6.0; Windows NT 5.1; SV1; GTB6.3; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30; InfoPath.2; .NET CLR 3.0.04506.648; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; OfficeLiveConnector.1.3; OfficeLivePatch.0.0; TmstmpExt)'
    family: 'Windows Live Mail'
    major:
    minor:
    patch:

  - user_agent_string: 'Outlook-Express/7.0 (MSIE 8; Windows NT 5.1; Trident/4.0; GTB7.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; TmstmpExt)'
    family: 'Windows Live Mail'
    major:
    minor:
    patch:

  - user_agent_string: 'Outlook-Express/7.0 (MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; InfoPath.1; TmstmpExt)'
    family: 'Windows Live Mail'
    major:
    minor:
    patch:

  - user_agent_string: 'Outlook-Express/7.0 (MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; HPDTDF; .NET4.0C; BRI/2; AskTbLOL/5.12.5.17640; TmstmpExt)'
    family: 'Windows Live Mail'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 5.0.1; GT-I9505 Build/LRX22C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2526.111 YaBrowser/16.2.1.1239.00 Mobile Safari/537.36'
    family: 'Yandex Browser'
    major: '16'
    minor: '2'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 4.4.2; SM-G800F Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.135 MobileIron/1.6.0 Mobile Safari/537.36'
    family: 'MobileIron'
    major: '1'
    minor: '6'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 4.4.4; GT-I9195I Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.130 Crosswalk/14.43.343.17 Mobile Safari/537.36'
    family: 'Crosswalk'
    major: '14'
    minor: '43'
    patch: '343'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 6.0.1; Z831 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.2454.94 Mobile Crosswalk/11.45.2454.20160425 Mobile Safari/537.36'
    family: 'Crosswalk'
    major: '11'
    minor: '45'
    patch: '2454'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Mobile/15A372 Safari Line/7.12.0'
    family: 'LINE'
    major: '7'
    minor: '12'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 5.1; FTJ152B Build/LMY47D; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/51.0.2704.81 Mobile Safari/537.36 Line/6.4.1'
    family: 'LINE'
    major: '6'
    minor: '4'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 4.1.2; GT-S7710 Build/JZO54K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile'
    family: 'Chrome Mobile'
    major: '18'
    minor: '0'
    patch: '1025'
    patch_minor: '166'

  - user_agent_string: 'Mozilla/5.0 (iPad; U; CPU OS 5_1_1 like Mac OS X; en-us) AppleWebKit/534.46.0 (KHTML, like Gecko) Chrome/19.0.1084.60 Mobile/9B206 Safari/7534.48.3'
    family: 'Chrome Mobile'
    major: '19'
    minor: '0'
    patch: '1084'
    patch_minor: '60'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 4.4.4; de-de; SM-G850F Build/KTU84P) AppleWebKit/537.16 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.16 Chrome/********'
    family: 'Chrome Mobile WebView'
    major: '33'
    minor: '0'
    patch: '0'
    patch_minor: '0'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 6.0.1; ru-ru; Redmi 4 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/61.0.3163.128 Mobile Safari/537.36 XiaoMi/MiuiBrowser/10.3.6-g'
    family: 'MiuiBrowser'
    major: '10'
    minor: '3'
    patch: '6'

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 7.1.2; ru-ru; Redmi 4A Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/61.0.3163.128 Mobile Safari/537.36 XiaoMi/Mint Browser/1.3.3'
    family: 'Mint Browser'
    major: '1'
    minor: '3'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (Web0S; Linux/SmartTV) AppleWebKit/537.41 (KHTML, like Gecko) Large Screen WebAppManager Safari/537.41'
    family: 'Safari'
    major:
    minor:
    patch:

  - user_agent_string: 'MacOutlook/15.27.0.161010 (Intelx64 Mac OS X Version 10.11.6 (Build 15G1108))'
    family: 'MacOutlook'
    major: '15'
    minor: '27'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_6) AppleWebKit/601.7.8 (KHTML, like Gecko) Slack_SSB/2.0.3'
    family: 'Slack Desktop Client'
    major: '2'
    minor: '0'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_4) AppleWebKit/537.36 (KHTML, like Gecko) AtomShell/2.6.0 Chrome/56.0.2924.87 Electron/1.6.3 Safari/537.36 MacAppStore/16.5.0 Slack_SSB/2.6.0'
    family: 'Slack Desktop Client'
    major: '2'
    minor: '6'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_6) AppleWebKit/537.36 (KHTML, like Gecko) Slack/2.6.0-beta18998559 Chrome/56.0.2924.87 AtomShell/1.6.3 Safari/537.36 Slack_SSB/2.6.0'
    family: 'Slack Desktop Client'
    major: '2'
    minor: '6'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_4) AppleWebKit/603.1.30 (KHTML, like Gecko) HipChat/732 (modern)'
    family: 'HipChat Desktop Client'
    major: '732'
    minor:
    patch:

  - user_agent_string: 'HipChat Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) QtWebEngine/5.7.0 Chrome/49.0.2623.111 Safari/537.36'
    family: 'HipChat Desktop Client'
    major:
    minor:
    patch:

  - user_agent_string: 'Microsoft-CryptoAPI/6.1'
    family: 'Microsoft-CryptoAPI'
    major: '6'
    minor: '1'
    patch:

  - user_agent_string: 'Microsoft SkyDriveSync 17.3.6517.0809 ship; Windows NT 6.1 Service Pack 1 (7601)'
    family: 'Microsoft SkyDriveSync'
    major: '17'
    minor: '3'
    patch: '6517'

  - user_agent_string: 'ExchangeServicesClient/14.02.0051.000'
    family: 'ExchangeServicesClient'
    major: '14'
    minor: '02'
    patch: '0051'

  - user_agent_string: 'Mac OS X/10.11.6 (15G1004); ExchangeWebServices/6.0 (243); Mail/9.3 (3124)'
    family: 'ExchangeWebServices'
    major: '6'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0+(iPhone;+CPU+iPhone+OS+9_3_1+like+Mac+OS+X)+AppleWebKit/601.1.46+(KHTML,+like+Gecko)+Version/9.0+Mobile/13E238+Safari/601.1'
    family: 'Mobile Safari'
    major: '9'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0+(iPad;+CPU+OS+9_3_1+like+Mac+OS+X)+AppleWebKit/601.1.46+(KHTML,+like+Gecko)+Version/9.0+Mobile/13E238+Safari/601.1'
    family: 'Mobile Safari'
    major: '9'
    minor: '0'
    patch:

  - user_agent_string: 'Slackbot-LinkExpanding 1.0 (+https://api.slack.com/robots)'
    family: 'Slackbot-LinkExpanding'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Slack-ImgProxy 1.136 (+https://api.slack.com/robots)'
    family: 'Slack-ImgProxy'
    major: '1'
    minor: '136'
    patch:

  - user_agent_string: 'okhttp/3.4.2'
    family: 'okhttp'
    major: '3'
    minor: '4'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_0) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1300.0 Iron/23.0.1300.0 Safari/537.11'
    family: 'Iron'
    major: '23'
    minor: '0'
    patch: '1300'

  - user_agent_string: 'Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/534.13 (KHTML, like Gecko) Iron/9.0.600.2 Chrome/9.0.600.2 Safari/534.13'
    family: 'Iron'
    major: '9'
    minor: '0'
    patch: '600'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2750.0 Iron Safari/537.36'
    family: 'Iron'
    major: '52'
    minor: '0'
    patch: '2750'

  - user_agent_string: 'Mozilla/5.0 (Android; U; en-US) AppleWebKit/533.19.4 (KHTML, like Gecko) AdobeAIR/23.0'
    family: 'AdobeAIR'
    major: '23'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Macintosh; U; Intel Mac OS X; en-US) AppleWebKit/533.19.4 (KHTML, like Gecko) AdobeAIR/19.0'
    family: 'AdobeAIR'
    major: '19'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iOS; U; en-US) AppleWebKit/533.19.4 (KHTML, like Gecko) AdobeAIR/19.0'
    family: 'AdobeAIR'
    major: '19'
    minor: '0'
    patch:

  - user_agent_string: 'Kurio/3.0.8 Build 65303(Android Kitkat 4.4.4; Phone)'
    family: 'Kurio App'
    major: '3'
    minor: '0'
    patch: '8'

  - user_agent_string: 'BacaBerita App/5.5.0 (Linux; U; Android 4.4.4; en-us) Mobile Safari'
    family: 'BacaBerita App'
    major: '5'
    minor: '5'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) MxBrowser/4.5.2.2000 Chrome/30.0.1551.0 Safari/537.36'
    family: 'Maxthon'
    major: '4'
    minor: '5'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Maxthon/4.4.5.1000 Chrome/30.0.1599.101 Safari/537.36'
    family: 'Maxthon'
    major: '4'
    minor: '4'
    patch: '5'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 7.0; SM-G930P Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/58.0.3029.83 Mobile Safari/537.36 MxBrowser/4.5.10.7000'
    family: 'Maxthon'
    major: '4'
    minor: '5'
    patch: '10'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.6 (KHTML, like Gecko) Chrome/18.0.1025.133 Safari/537.6 Midori/0.5'
    family: 'Midori'
    major: '0'
    minor: '5'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPad; U; CPU like Mac OS X; FIT_LANG_REPLACE) AppleWebKit/532+ (KHTML, like Gecko) Version/3.0 Mobile/1A538b Safari/419.3 Midori/0.4'
    family: 'Midori'
    major: '0'
    minor: '4'
    patch:

  - user_agent_string: 'curl/7.29.0'
    family: 'curl'
    major: '7'
    minor: '29'
    patch: '0'

  - user_agent_string: 'Debian APT-HTTP/1.3 (1.0.1ubuntu2)'
    family: 'Debian APT-HTTP'
    major: '1'
    minor: '3'
    patch:

  - user_agent_string: 'jupdate'
    family: 'jupdate'
    major:
    minor:
    patch:

  - user_agent_string: 'libcurl-agent/1.0'
    family: 'libcurl-agent'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'libwww-perl/6.05'
    family: 'libwww-perl'
    major: '6'
    minor: '05'
    patch:

  - user_agent_string: 'Microsoft-CryptoAPI/6.3'
    family: 'Microsoft-CryptoAPI'
    major: '6'
    minor: '3'
    patch:

  - user_agent_string: 'OpenBSD ftp'
    family: 'OpenBSD ftp'
    major:
    minor:
    patch:

  - user_agent_string: 'SophosAgent/1.0 (type= spa )'
    family: 'SophosAgent'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'SophosUpdateManager/1.5.7.50 SDDS/2.0 (u= EO2ANA123G c= 6342da15-f351-4ab7-9656-3f5f2d50885d )'
    family: 'SophosUpdateManager'
    major: '1'
    minor: '5'
    patch: '7'

  - user_agent_string: 'Ubuntu APT-HTTP/1.3 (0.7.20.2ubuntu6)'
    family: 'Ubuntu APT-HTTP'
    major: '1'
    minor: '3'
    patch:

  - user_agent_string: 'urlgrabber/3.10 yum/3.4.3'
    family: 'urlgrabber'
    major: '3'
    minor: '10'
    patch:

  - user_agent_string: 'urlgrabber/3.9.1 yum/3.2.29'
    family: 'urlgrabber'
    major: '3'
    minor: '9'
    patch: '1'

  - user_agent_string: 'Wget/1.14 (linux-gnu)'
    family: 'Wget'
    major: '1'
    minor: '14'
    patch:

  - user_agent_string: 'wget2/1.99.2'
    family: 'wget2'
    major: '1'
    minor: '99'
    patch: '2'

  - user_agent_string: 'Windows-Update-Agent/7.9.9600.17729 Client-Protocol/1.21'
    family: 'Windows-Update-Agent'
    major: '7'
    minor: '9'
    patch: '9600'

  - user_agent_string: 'Windows-Update-Agent/7.9.9600.18094 Client-Protocol/1.21'
    family: 'Windows-Update-Agent'
    major: '7'
    minor: '9'
    patch: '9600'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 5.1.1; MI NOTE Pro Build/LMY47V; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.116 Mobile Safari/537.36 baidubrowser/7.7.13.0 (Baidu; P1 5.1.1)'
    family: 'Baidu Browser'
    major: '7'
    minor: '7'
    patch: '13'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Dragon/36.1.1.21 Chrome/36.0.1985.97 Safari/537.36'
    family: 'Dragon'
    major: '36'
    minor: '1'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Windows; U; Windows CE 5.1; rv:1.8.1a3) Gecko/20060610 Minimo/0.016'
    family: 'Minimo'
    major: '0'
    minor: '016'
    patch:

  - user_agent_string: 'Opera/9.80 (MAUI Runtime; Opera Mini/4.4.39008/37.9178; U; en) Presto/2.12.423 Version/12.16'
    family: 'Opera Mini'
    major: '4'
    minor: '4'
    patch: '39008'

  - user_agent_string: 'Mozilla/5.0 (compatible; 008/0.83; http://www.80legs.com/spider.html) Gecko/2008032620'
    family: '008'
    major: '0'
    minor: '83'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 9_3_4 like Mac OS X) AppleWebKit/601.1 (KHTML, like Gecko) Outlook-iOS-Android/1.0 Mobile/13G35 Safari/601.1.46")'
    family: 'Outlook-iOS-Android'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.21 Safari/537.36 MMS/1.0.2459.0'
    family: 'Opera Neon'
    major: '1'
    minor: '0'
    patch: '2459'

  - user_agent_string: 'PANTECH-EUROPA-U4000-orange/1.0 Obigo/Q04C MMS/1.2.0 profile/MIDP-2.0 configuration/CLDC-1.1'
    family: 'Obigo'
    major: '04'
    minor:
    patch:

  - user_agent_string: 'masscan/1.0 (https://github.com/robertdavidgraham/masscan)'
    family: 'masscan'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 SznProhlizec/4.3.0-251281'
    family: 'Seznam prohlížeč'
    major: '4'
    minor: '3'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 7.1.2; Redmi 4X Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/64.0.3282.137 Mobile Safari/537.36 SznProhlizec/5.2.1a'
    family: 'Seznam prohlížeč'
    major: '5'
    minor: '2'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.87 Safari/537.36 SznProhlizec/3.8.4 NWjs/0.19.6'
    family: 'Seznam prohlížeč'
    major: '3'
    minor: '8'
    patch: '4'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 10_2_1 like Mac OS X) AppleWebKit/602.4.6 (KHTML, like Gecko) Mobile/14D27 SznProhlizec/4.4i'
    family: 'Seznam prohlížeč'
    major: '4'
    minor: '4'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) coc_coc_browser/42.0 CoRom/36.0.1985.144 Chrome/36.0.1985.144 Safari/537.36'
    family: 'Coc Coc'
    major: '42'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) coc_coc_browser/49.0 Chrome/43.0.2357.138 Safari/537.36'
    family: 'Coc Coc'
    major: '49'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) coc_coc_browser/50.0.125 Chrome/44.0.2403.125 Safari/537.36'
    family: 'Coc Coc'
    major: '50'
    minor: '0'
    patch: '125'

  - user_agent_string: 'Mozilla/5.0 (compatible; Qwantify/2.4w; +https://www.qwant.com/)/2.4w'
    family: 'Qwantify'
    major: '2'
    minor: '4'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.135 Safari/537.36 Edge/12'
    family: 'Edge'
    major: '12'
    minor:
    patch:

  - user_agent_string: 'Bloglovin/1.0 (http://www.bloglovin.com; 1000 subscribers)'
    family: 'Bloglovin'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Feedbin feed-id:1033517 - 1000 subscribers'
    family: 'Feedbin'
    major:
    minor:
    patch:

  - user_agent_string: 'Tiny Tiny RSS/16.3 (http://tt-rss.org/)'
    family: 'Tiny Tiny RSS'
    major: '16'
    minor: '3'
    patch:

  - user_agent_string: 'Mtps Feed Aggregation System'
    family: 'Mtps Feed Aggregation System'
    major:
    minor:
    patch:

  - user_agent_string: 'Stringer'
    family: 'Stringer'
    major:
    minor:
    patch:

  - user_agent_string: 'Box Sync/4.0.7848;Darwin/10.13;i386/64bit'
    family: 'Box Sync'
    major: '4'
    minor: '0'
    patch: '7848'

  - user_agent_string: 'Box/1.2.93;Darwin/10.13;i386/64bit'
    family: 'Box'
    major: '1'
    minor: '2'
    patch: '93'

  - user_agent_string: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_0) AppleWebKit/537.36 (KHTML, like Gecko) BoxNotes/1.3.0 Chrome/56.0.2924.87 Electron/1.6.8 Safari/537.36'
    family: 'BoxNotes'
    major: '1'
    minor: '3'
    patch: '0'

  - user_agent_string: 'Box Sync/4.0.7848;Windows/8.1;x86 Family 6 Model 158 Stepping 9, GenuineIntel/32bit'
    family: 'Box Sync'
    major: '4'
    minor: '0'
    patch: '7848'

  - user_agent_string: 'Box Sync/4.0.7848;Windows/10;Intel64 Family 6 Model 158 Stepping 9, GenuineIntel/64bit'
    family: 'Box Sync'
    major: '4'
    minor: '0'
    patch: '7848'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 6.3) AppleWebKit/537.36 (KHTML, like Gecko) BoxNotes/1.3.0 Chrome/56.0.2924.87 Electron/1.6.8 Safari/537.36'
    family: 'BoxNotes'
    major: '1'
    minor: '3'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) BoxNotes/1.3.0 Chrome/56.0.2924.87 Electron/1.6.8 Safari/537.36'
    family: 'BoxNotes'
    major: '1'
    minor: '3'
    patch: '0'

  - user_agent_string: 'Box/1.2.93;Windows/10;Intel64 Family 6 Model 158 Stepping 9, GenuineIntel/64bit'
    family: 'Box'
    major: '1'
    minor: '2'
    patch: '93'

  - user_agent_string: 'Evolution/3.26.2.1'
    family: 'Evolution'
    major: '3'
    minor: '26'
    patch: '2.1'

  - user_agent_string: 'RCM CardDAV plugin/2.0.4'
    family: 'RCM CardDAV plugin'
    major: '2'
    minor: '0'
    patch: '4'

  - user_agent_string: 'RCM CardDAV plugin/0.9.2-dev'
    family: 'RCM CardDAV plugin'
    major: '0'
    minor: '9'
    patch: '2-dev'

  - user_agent_string: 'DAVdroid/1.9.2-gplay (2017/11/04; dav4android; okhttp3) Android/7.1.2'
    family: 'DAVdroid'
    major: '1'
    minor: '9'
    patch: '2'

  - user_agent_string: 'DAVdroid/1.9-ose (2017/10/19; dav4android; okhttp3) Android/7.1.2'
    family: 'DAVdroid'
    major: '1'
    minor: '9'
    patch:

  - user_agent_string: 'Mozilla/5.0 (Windows) mirall/2.3.2 (build 1) (Nextcloud)'
    family: 'Nextcloud'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux) mirall/2.3.2 (Nextcloud)'
    family: 'Nextcloud'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Android) ownCloud-android/2.0.0'
    family: 'Owncloud'
    major: '2'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 10_0_2 like Mac OS X) AppleWebKit/602.1.50 (KHTML, like Gecko) AppleNews/608.0.1 Version/2.0.1'
    family: 'Mobile Safari UI/WKWebView'
    major: '2'
    minor: '0'
    patch: '1'

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.3; WOW64; Trident/7.0; .NET4.0E; .NET4.0C; InfoPath.3)'
    family: 'IE'
    major: '11'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.2; Trident/6.0; .NET4.0E; .NET4.0C; .NET CLR 3.5.30729; .NET CLR 2.0.50727; .NET CLR 3.0.30729)'
    family: 'IE'
    major: '10'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E; InfoPath.2)'
    family: 'IE'
    major: '9'
    minor: '0'
    patch:

  - user_agent_string: 'Tableau/1.0 (1025794)'
    family: 'Tableau'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_2_5 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/11.2.5 Mobile/9B179 Safari/7534.48.3 OktaMobile/5.10.2'
    family: 'OktaMobile'
    major: '5'
    minor: '10'
    patch: '2'

  - user_agent_string: 'BUbiNG (+http://law.di.unimi.it/BUbiNG.html)'
    family: 'BUbiNG'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (compatible; SemrushBot/1.2~bl; +http://www.semrush.com/bot.html)'
    family: 'SemrushBot'
    major: '1'
    minor: '2'
    patch:

  - user_agent_string: 'Outlook-iOS/665.29827.prod.iphone (2.63.0)'
    family: 'Outlook-iOS'
    major: '2'
    minor: '63'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 7.0; LG-TP260 Build/NRD90U; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/64.0.3282.137 Mobile Safari/537.36 Instagram *********.92 Android (24/7.0; 320dpi; 720x1193; LGE/lge; LG-TP260; lv517; lv517; en_US; 93117667)'
    family: 'Instagram'
    major: '33'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_2_5 like Mac OS X) AppleWebKit/604.5.6 (KHTML, like Gecko) Mobile/15D60 Instagram *********.96 (iPhone9,3; iOS 11_2_5; en_AU; en-AU; scale=2.00; gamut=wide; 750x1334)'
    family: 'Instagram'
    major: '33'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_2_6 like Mac OS X) AppleWebKit/604.5.6 (KHTML, like Gecko) Mobile/15D100 Flipboard/4.2.2'
    family: 'Flipboard'
    major: '4'
    minor: '2'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 7.0; SM-G610F Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36 Flipboard/4.1.9/4323,4.1.9.4323'
    family: 'Flipboard'
    major: '4'
    minor: '1'
    patch: '9'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 7.0; SM-G930F Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/64.0.3282.137 Mobile Safari/537.36 Onefootball/Android/9.10.6'
    family: 'Onefootball'
    major: '9'
    minor: '10'
    patch: '6'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 7.0; SM-A520F Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/58.0.3029.83 Mobile Safari/537.36 Flipboard-Briefing/2.7.28'
    family: 'Flipboard-Briefing'
    major: '2'
    minor: '7'
    patch: '28'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 Phantom/ios/***********'
    family: 'Phantom'
    major: '22'
    minor: '06'
    patch: '08'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 12; SM-G991B Build/SP1A.210812.016; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/102.0.5005.125 Mobile Safari/537.36 Phantom/android/***********'
    family: 'Phantom'
    major: '22'
    minor: '06'
    patch: '08'

  - user_agent_string: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/8.0; .NET4.0C; .NET4.0E; .NET CLR 2.0.50727; .NET CLR 3.0.30729; .NET CLR 3.5.30729)'
    family: 'IE'
    major: '11'
    minor: '0'
    patch:

  - user_agent_string: 'ESPN Radio/3.2.113 CFNetwork/485.12.30 Darwin/10.4.0'
    family: 'ESPN'
    major: '3'
    minor: '2'
    patch: '113'

  - user_agent_string: 'ESPN Radio 4.7.4 rv:1032 (iPhone; iPhone OS 9.2.1; en_US)'
    family: 'ESPN'
    major: '4'
    minor: '7'
    patch: '4'
    patch_minor: '1032'

  - user_agent_string: 'ESPN Radio 4.5.1 (iPhone; iPhone OS 5.1.1; en_US)'
    family: 'ESPN'
    major: '4'
    minor: '5'
    patch: '1'

  - user_agent_string: 'ESPN Radio 4.0 (iPhone; iPhone OS 7.1.2; en_AU)'
    family: 'ESPN'
    major: '4'
    minor: '0'
    patch:

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64; rv:55.0) Gecko/20100101 Firefox/55.2.2 Waterfox/55.2.2'
    family: 'Waterfox'
    major: '55'
    minor: '2'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (X11; Linux x86_64; rv:55.0) Gecko/20100101 Goanna/4.0 Firefox/55.0 Basilisk/20171113'
    family: 'Basilisk'
    major: '55'
    minor: '0'
    patch: '20171113'

  - user_agent_string: 'Go-http-client/1.1'
    family: 'Go-http-client'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'scalaj-http/1.0'
    family: 'scalaj-http'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'reqwest/0.8.1'
    family: 'reqwest'
    major: '0'
    minor: '8'
    patch: '1'

  - user_agent_string: 'akka-http/10.0.10'
    family: 'akka-http'
    major: '10'
    minor: '0'
    patch: '10'

  - user_agent_string: 'Python/3.6 aiohttp/3.5.4'
    family: 'Python aiohttp'
    major: '3'
    minor: '5'
    patch: '4'

  - user_agent_string: 'unirest-java/1.3.11'
    family: 'unirest-java'
    major: '1'
    minor: '3'
    patch: '11'

  - user_agent_string: 'axios/0.18.0'
    family: 'axios'
    major: '0'
    minor: '18'
    patch: '0'

  - user_agent_string: 'got/9.6.0 (https://github.com/sindresorhus/got)'
    family: 'got'
    major: '9'
    minor: '6'
    patch: '0'

  - user_agent_string: 'S3Gof3r'
    family: 'S3Gof3r'
    major:
    minor:
    patch:

  - user_agent_string: 'rusoto/0.36.0 rust/1.35.0 linux'
    family: 'rusoto'
    major: '0'
    minor: '36'
    patch: '0'

  - user_agent_string: 'ibm-cos-sdk-java/2.3.0 Linux/4.9.0-8-amd64 Java_HotSpot(TM)_64-Bit_Server_VM/9.0.4+11/9.0.4'
    family: 'ibm-cos-sdk-java'
    major: '2'
    minor: '3'
    patch: '0'

  - user_agent_string: 'aws-sdk-dotnet-45/3.3.11.0 aws-sdk-dotnet-core/3.3.17.10 .NET_Runtime/4.0 .NET_Framework/4.0 OS/Microsoft_Windows_NT_6.2.9200.0 ClientSync'
    family: 'aws-sdk-dotnet-45'
    major: '3'
    minor: '3'
    patch: '11'
    patch_minor: '0'

  - user_agent_string: 'Boto/2.48.0 Python/2.7.14 Linux/4.2.0-41-generic'
    family: 'Boto'
    major: '2'
    minor: '48'
    patch: '0'

  - user_agent_string: 'aws-cli/1.14.9 Python/2.7.12 Linux/4.9.76-3.78.amzn1.x86_64 botocore/1.8.13'
    family: 'aws-cli'
    major: '1'
    minor: '14'
    patch: '9'

  - user_agent_string: 'Boto3/1.6.2 Python/3.4.3 Linux/4.4.35-33.55.amzn1.x86_64 Botocore/1.9.2 Resource'
    family: 'Boto3'
    major: '1'
    minor: '6'
    patch: '2'

  - user_agent_string: 'ElasticMapReduce/1.0.0 emrfs/s3n {}, aws-sdk-java/1.11.129 Linux/4.4.35-33.55.amzn1.x86_64 OpenJDK_64-Bit_Server_VM/25.141-b16/1.8.0_141 scala/2.11.8'
    family: 'aws-sdk-java'
    major: '1'
    minor: '11'
    patch: '129'

  - user_agent_string: 'Hadoop 2.6.0-cdh5.14.0, aws-sdk-java/1.11.134 Linux/4.4.0-1052-aws OpenJDK_64-Bit_Server_VM/25.151-b12/1.8.0_151'
    family: 'aws-sdk-java'
    major: '1'
    minor: '11'
    patch: '134'

  - user_agent_string: 'Hadoop 2.8.3-amzn-0, aws-sdk-java/1.11.267 Linux/4.9.77-31.58.amzn1.x86_64 OpenJDK_64-Bit_Server_VM/25.161-b14 java/1.8.0_161 scala/2.11.8'
    family: 'aws-sdk-java'
    major: '1'
    minor: '11'
    patch: '267'

  - user_agent_string: 'aws-sdk-java/1.11.226 Mac_OS_X/10.12.6 Java_HotSpot(TM)_64-Bit_Server_VM/25.131-b11 java/1.8.0_131 scala/2.11.11'
    family: 'aws-sdk-java'
    major: '1'
    minor: '11'
    patch: '226'

  - user_agent_string: 'aws-sdk-ruby2/2.2.18 ruby/2.1.5 x86_64-linux'
    family: 'aws-sdk-ruby2'
    major: '2'
    minor: '2'
    patch: '18'

  - user_agent_string: 'aws-sdk-cpp/1.0.64 Linux/4.4.0-66-generic x86_64'
    family: 'aws-sdk-cpp'
    major: '1'
    minor: '0'
    patch: '64'

  - user_agent_string: 'aws-sdk-go/1.4.12 (go1.6; linux; amd64) S3Manager'
    family: 'aws-sdk-go'
    major: '1'
    minor: '4'
    patch: '12'

  - user_agent_string: 'aws-sdk-nodejs/2.141.0 win32/v8.4.0'
    family: 'aws-sdk-nodejs'
    major: '2'
    minor: '141'
    patch: '0'

  - user_agent_string: 'JetS3t/0.9.0 (Linux/4.4.0-1044-aws; amd64; en; JVM 1.8.0_131)'
    family: 'JetS3t'
    major: '0'
    minor: '9'
    patch: '0'

  - user_agent_string: 's3fs/1.80 (commit hash 6be3236; OpenSSL)'
    family: 's3fs'
    major: '1'
    minor: '80'
    patch:

  - user_agent_string: 'Cyberduck/6.3.0.27105 (Windows 10/10.0) (x86)'
    family: 'Cyberduck'
    major: '6'
    minor: '3'
    patch: '0'

  - user_agent_string: 'S3 Browser 7-4-5 https://s3browser.com'
    family: 'S3 Browser'
    major: '7'
    minor: '4'
    patch: '5'

  - user_agent_string: 'S3 Browser 8.6.7 https://s3browser.com'
    family: 'S3 Browser'
    major: '8'
    minor: '6'
    patch: '7'

  - user_agent_string: 'rclone/v1.34'
    family: 'rclone'
    major: '1'
    minor: '34'
    patch:

  - user_agent_string: 'PycURL/7.43.0 libcurl/7.38.0 OpenSSL/1.0.1t zlib/1.2.8 libidn/1.29 libssh2/1.4.3 librtmp/2.3'
    family: 'PycURL'
    major: '7'
    minor: '43'
    patch: '0'

  - user_agent_string: 'Axel 2.4 (Linux)'
    family: 'Axel'
    major: '2'
    minor: '4'
    patch:

  - user_agent_string: 'lftp/4.7.7'
    family: 'lftp'
    major: '4'
    minor: '7'
    patch: '7'

  - user_agent_string: 'aria2/1.19.0'
    family: 'aria2'
    major: '1'
    minor: '19'
    patch: '0'

  - user_agent_string: 'SalesforceMobileSDK/5.3.0 android mobile/7.1.1 (XT1635-02) Salesforce1/15.2 Native uid_bef1747905d064c6 ftr_ Cordova/6.2.3'
    family: 'Salesforce'
    major: '15'
    minor: '2'
    patch:

  - user_agent_string: 'AnyConnect/4.1.1234'
    family: 'AnyConnect'
    major: '4'
    minor: '1'
    patch: '1234'

  - user_agent_string: 'YahooMailProxy; https://help.yahoo.com/kb/yahoo-mail-proxy-SLN28749.html'
    family: 'YahooMailProxy'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_2_6 like Mac OS X) AppleWebKit/604.5.6 (KHTML, like Gecko) Snapchat/********** (iPhone8,1; iOS 11.2.6; gzip)'
    family: 'Snapchat'
    major: '10'
    minor: '38'
    patch: '0'
    patch_minor: '25'

  - user_agent_string: 'Mozilla/5.0 (iPad; U; CPU OS 4_3_5 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Mobile/8L1 Twitter for iPad'
    family: 'Twitter'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 9; Nokia 2.1 Build/PKQ1.181105.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36 TwitterAndroid'
    family: 'Twitter'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_2_1 like Mac OS X) AppleWebKit/604.4.7 (KHTML, like Gecko) Mobile/15C153 Twitter for iPhone/7.19'
    family: 'Twitter'
    major: '7'
    minor: '19'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_2 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) GSA/4.2.2.38484 Mobile/11D257 Safari/9537.53'
    family: 'Google'
    major: '4'
    minor: '2'
    patch: '2'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_0_2 like Mac OS X) AppleWebKit/604.1.34 (KHTML, like Gecko) GSA/36.0.169645775 Mobile/15A421 Safari/604.1'
    family: 'Google'
    major: '36'
    minor: '0'
    patch: '169645775'

  - user_agent_string: 'ViaFree-DK/3.8.3 (com.MTGx.ViaFree.dk; build:7383; iOS 12.1.0) Alamofire/4.7.0'
    family: 'ViaFree'
    major: '3'
    minor: '8'
    patch: '3'

  - user_agent_string: 'Viafree-tvOS-DK/3.7.1 (com.MTGx.ViaFree.dk; build:7341; tvOS 12.1.0) Alamofire/4.7.0'
    family: 'ViaFree'
    major: '3'
    minor: '7'
    patch: '1'

  - user_agent_string: 'OC/15.0.5071.1000 (Skype for Business)'
    family: 'Skype'
    major: '15'
    minor: '0'
    patch: '5071'
    patch_minor: '1000'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 8.1.0; TA-1024 Build/OPR1.170623.026; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/71.0.3578.99 Mobile Safari/537.36 GSA/*********.arm64'
    family: 'Google'
    major: '8'
    minor: '65'
    patch: '5'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 8.0.0; SM-G960F Build/R16NW; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/71.0.3578.99 Mobile Safari/537.36 GSA/*********.arm64'
    family: 'Google'
    major: '8'
    minor: '65'
    patch: '5'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 9; Pixel 3 Build/PQ1A.181205.006; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/71.0.3578.99 Mobile Safari/537.36 GSA/*********.arm64'
    family: 'Google'
    major: '8'
    minor: '65'
    patch: '5'

  - user_agent_string: 'Microsoft Office Word 2014'
    family: 'Word'
    major:
    minor:
    patch:

  - user_agent_string: 'Ghost/2.13.1+moya (https://github.com/TryGhost/Ghost)'
    family: 'Ghost'
    major: '2'
    minor: '13'
    patch: '1'

  - user_agent_string: 'Ghost/2.10.8 (https://github.com/TryGhost/Ghost)'
    family: 'Ghost'
    major: '2'
    minor: '10'
    patch: '8'

  - user_agent_string: 'PAN GlobalProtect/5.2.4 Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/602.1 (KHTML, like Gecko) PanGPUI Version/10.0 Safari/602.1'
    family: 'GlobalProtect'
    major: '5'
    minor: '2'
    patch: '4'

  - user_agent_string: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3763.0 Safari/537.36 Edg/**********'
    family: 'Edge'
    major: '75'
    minor: '0'
    patch: '131'
    patch_minor: '0'

  - user_agent_string: 'Mozilla/5.0 (iPad; CPU OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 TopBuzz com.alex.NewsMaster/8.2.1 (iPad; iOS 12.3.1; en; WIFI)'
    family: 'TopBuzz'
    major: '8'
    minor: '2'
    patch: '1'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 9; SM-G950U Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/76.0.3809.111 Mobile Safari/537.36 JsSdk/2 TopBuzz/9.4.3 NetType/4G'
    family: 'TopBuzz'
    major: '9'
    minor: '4'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 9; SM-J737A Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.143 Mobile Safari/537.36 JsSdk/2 TopBuzz/9.1.4 NetType/4G'
    family: 'TopBuzz'
    major: '9'
    minor: '1'
    patch: '4'

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_3 like Mac OS X) AppleWebKit/603.3.8 (KHTML, like Gecko) Mobile/14G60 TopBuzz com.topbuzz.videoen/8.2.2 (iPhone; iOS 10.3.3; en; WIFI; CTRadioAccessTechnologyLTE)'
    family: 'TopBuzz'
    major: '8'
    minor: '2'
    patch: '2'

  - user_agent_string: 'Snapchat/********* (iPhone10,1; iOS 11.2; gzip)'
    family: 'Snapchat'
    major: '10'
    minor: '29'
    patch: '1'

  - user_agent_string: 'OgScrper/1.0.0'
    family: 'OgScrper'
    major: '1'
    minor: '0'
    patch: '0'

  - user_agent_string: 'Android: Client/0.0.0-indev (Android SDK built for x86; Android 9)'
    family: 'Android'
    major: '9'
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 9; zh-cn; vivo X21 Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/9.9 Mobile Safari/537.36'
    family: 'QQ Browser Mobile'
    major: '9'
    minor: '9'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_2 like Mac OS X) AppleWebKit/603.2.4 (KHTML, like Gecko) Mobile/14F89 Safari/603.2.4 EdgiOS/*********'
    family: 'Edge Mobile'
    major: '41'
    minor: '1'
    patch: '35'
    patch_minor: '1'

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 8.0; Pixel XL Build/OPP3.170518.006) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.0 Mobile Safari/537.36 EdgA/*********'
    family: 'Edge Mobile'
    major: '41'
    minor: '1'
    patch: '35'
    patch_minor: '1'

  - user_agent_string: 'Mozilla/5.0 (compatible;AspiegelBot)'
    family: 'AspiegelBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 7.0;) AppleWebKit/537.36 (KHTML, like Gecko) Mobile Safari/537.36 (compatible; AspiegelBot)'
    family: 'AspiegelBot'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 QGIS/31100'
    family: 'QGIS'
    major: '3'
    minor: '11'
    patch: '0'

  - user_agent_string: 'Mozilla/5.0 QGIS/2.18.20'
    family: 'QGIS'
    major: '2'
    minor: '18'
    patch: '20'

  - user_agent_string: 'JOSM/1.5 (15492 nl) Windows 7 64-Bit Java/1.8.0_221'
    family: 'JOSM'
    major: '1'
    minor: '5'
    patch:

  - user_agent_string: 'FME/2018.7.34.18312  libcurl/7.57.0 WinSSL zlib/1.2.11 WinIDN libssh2/1.7.0'
    family: 'FME'
    major: '2018.7'
    minor: '34'
    patch: '18312'

  - user_agent_string: 'GeoEvent Server 10.5.1'
    family: 'GeoEvent Server'
    major: '10'
    minor: '5'
    patch: '1'

  - user_agent_string: 'ArcGIS Pro 2.4.2 (000000000) - ArcGIS Pro'
    family: 'ArcGIS Pro'
    major: '2'
    minor: '4'
    patch: '2'

  - user_agent_string: 'ArcGIS Client Using WinInet'
    family: 'ArcMap'
    major:
    minor:
    patch:

  - user_agent_string: 'ArcGISRuntime-Android/10.3.0 (Android 4.4; armeabi-v7a; SAMSUNG-GT-I9195) arcgis-collector/18.0.3 (00000000-0000-0000-0000-000000000000)'
    family: 'Collector for ArcGIS'
    major: '18'
    minor: '0'
    patch: '3'

  - user_agent_string: 'ArcGISRuntime-iOS/10.3.0 (iOS 10.2.1; iPad5,4) arcgis-collector/19.0.2 (00000000-0000-0000-0000-000000000000)'
    family: 'Collector for ArcGIS'
    major: '19'
    minor: '0'
    patch: '2'

  - user_agent_string: 'ArcGISRuntime-iOS/100.4 (iOS 11.4; iPad7,6) arcgis-aurora/18.1.0 (00000000-0000-0000-0000-000000000000)'
    family: 'Collector for ArcGIS'
    major: '18'
    minor: '1'
    patch: '0'

  - user_agent_string: 'ArcGISRuntime-NET/10.3.0 (Windows 10.0.18362; x64; UAP; Windows.Desktop) arcgis-collector/18.0.2 (00000000-0000-0000-0000-000000000000)'
    family: 'Collector for ArcGIS'
    major: '18'
    minor: '0'
    patch: '2'

  - user_agent_string: 'Collector-Android-10.3.7/ArcGIS.Android-10.2.5/7.0/SAMSUNG-SM-T815'
    family: 'Collector for ArcGIS'
    major: '10'
    minor: '3'
    patch: '7'

  - user_agent_string: 'Collector-iOS-10.4.0:ArcGISiOS-10.2.4+Collector/13.1.2/iPad5,4'
    family: 'Collector for ArcGIS'
    major: '10'
    minor: '4'
    patch: '0'

  - user_agent_string: 'Collector/6212016 CFNetwork/978.0.7 Darwin/18.7.0'
    family: 'Collector for ArcGIS'
    major: '6212016'
    minor:
    patch:

  - user_agent_string: 'ArcGISRuntime-Android/100.2.2 (Android 6.0; armeabi-v7a; SAMSUNG-SM-T800) arcgis-explorer/18.1.0 (00000000-0000-0000-0000-000000000000)'
    family: 'Explorer for ArcGIS'
    major: '18'
    minor: '1'
    patch: '0'

  - user_agent_string: 'ArcGISRuntime-iOS/100.1 (iPhone OS 9.3.5; iPad2,2) arcgis-explorer/17.1.2 (00000000-0000-0000-0000-000000000000)'
    family: 'Explorer for ArcGIS'
    major: '17'
    minor: '1'
    patch: '2'

  - user_agent_string: 'Explorer/1544 CFNetwork/1107.1 Darwin/19.0.0'
    family: 'Explorer for ArcGIS'
    major: '1544'
    minor:
    patch:

  - user_agent_string: 'Explorer-Android-10.2.10/ArcGIS.Android-10.2.8/5.1.1/SAMSUNG-SM-G361F'
    family: 'Explorer for ArcGIS'
    major: '10'
    minor: '2'
    patch: '10'

  - user_agent_string: 'Explorer-iOS-10.2.10:ArcGISiOS-10.2.4+Collector/13.1.3/iPhone9,3'
    family: 'Explorer for ArcGIS'
    major: '10'
    minor: '2'
    patch: '10'

  - user_agent_string: 'ArcGISRuntime-Android/100.1.1 (Android 7.0; arm64-v8a; SAMSUNG-SM-G930F) arcgis-workforce/17.0.1 (00000000-0000-0000-0000-000000000000)'
    family: 'Workforce for ArcGIS'
    major: '17'
    minor: '0'
    patch: '1'

  - user_agent_string: 'Workforce-iOS-17.0.1:ArcGISiOS-100.0.0.1529+dev/13.2/iPad6,12'
    family: 'Workforce for ArcGIS'
    major: '17'
    minor: '0'
    patch: '1'

  - user_agent_string: 'AR/10.2.6.1704 .Net/Desktop (Win32NT/6.1.7601.65536; Win64) arcgisearth/1.5'
    family: 'ArcGIS Earth'
    major: '1'
    minor: '5'
    patch:

  - user_agent_string: 'ArcGISRuntime-iOS/100.6 (iOS 12.3.1; iPad6,3) com.esri.earth.phone/1.0.0'
    family: 'ArcGIS Earth'
    major: '1'
    minor: '0'
    patch: '0'

  - user_agent_string: 'ArcGISiOS-10.2.4/12.1/iPad5,2'
    family: 'ArcGIS Runtime SDK for iOS'
    major: '10'
    minor: '2'
    patch: '4'

  - user_agent_string: 'ArcGISRuntime-iOS/100.1.1 (iOS 12.4.1; iPhone10,4)'
    family: 'ArcGIS Runtime SDK for iOS'
    major: '100'
    minor: '1'
    patch: '1'

  - user_agent_string: 'ArcGIS.Android-10.2.4/8.0.0/SAMSUNG-SM-A530F'
    family: 'ArcGIS Runtime SDK for Android'
    major: '10'
    minor: '2'
    patch: '4'

  - user_agent_string: 'ArcGIS.Android.10.2'
    family: 'ArcGIS Runtime SDK for Android'
    major: '10'
    minor: '2'
    patch:

  - user_agent_string: 'ArcGISRuntime-Android/100.4 (Android 10.0; arm64-v8a; ONEPLUS-HD1903)'
    family: 'ArcGIS Runtime SDK for Android'
    major: '100'
    minor: '4'
    patch:

  - user_agent_string: 'ArcGISRuntime-Qt/100.3 (Windows 10; x86_64; Qt 5.12.1; C++)'
    family: 'ArcGIS Runtime SDK for Qt'
    major: '100'
    minor: '3'
    patch:

  - user_agent_string: 'ArcGISRuntime-NET/100.7 (Windows 10.0.18362; Win64; WOW64; UAP; Windows.Desktop)'
    family: 'ArcGIS Runtime SDK for NET'
    major: '100'
    minor: '7'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.3 Mobile/15E148 DuckDuckGo/7 Safari/605.1.15'
    family: 'DuckDuckGo Mobile'
    major: '7'
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; Android 11) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Mobile DuckDuckGo/5 Safari/537.36'
    family: 'DuckDuckGo Mobile'
    major: '5'
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 [en] (X11, U; OpenVAS-VT 8.0.9)'
    family: 'OpenVAS Scanner'
    major: '8'
    minor: '0'
    patch: '9'

  - user_agent_string: 'Mozilla/5.0 [en] (X11, U; OpenVAS 7.0.10)'
    family: 'OpenVAS Scanner'
    major: '7'
    minor: '0'
    patch: '10'

  - user_agent_string: 'Mozilla/4.75 [en] (X11, U; OpenVAS)'
    family: 'OpenVAS Scanner'
    major:
    minor:
    patch:

  - user_agent_string: 'cf/6.51.0+2acd15650.2020-04-07 (go1.13.8; amd64 linux)'
    family: 'CloudFoundry'
    major: '6'
    minor: '51'
    patch: '0+2acd15650.2020-04-07'

  - user_agent_string: 'RPT-HTTPClient/0.3-3E'
    family: 'HTTPClient'
    major: '0'
    minor: '3'
    patch: '3E'

  - user_agent_string: 'CloudCockpitBackend/2.8.7'
    family: 'CloudCockpitBackend'
    major: '2'
    minor: '8'
    patch: '7'

  - user_agent_string: 'ReactorNetty/0.9.6.RELEASE'
    family: 'ReactorNetty'
    major: '0'
    minor: '9'
    patch: '6'

  - user_agent_string: 'axios/0.18.1'
    family: 'axios'
    major: '0'
    minor: '18'
    patch: '1'

  - user_agent_string: 'Jersey/2.29.1 (HttpUrlConnection 1.8.0_252)  '
    family: 'Jersey'
    major: '2'
    minor: '29'
    patch: '1'

  - user_agent_string: 'Java-EurekaClient/v1.6.2'
    family: 'Java-EurekaClient'
    major: '1'
    minor: '6'
    patch: '2'

  - user_agent_string: 'go-cli 6.46.0+29d6257f1.2019-07-09 / windows'
    family: 'go-cli'
    major: '6'
    minor: '46'
    patch: '0+29d6257f1.2019-07-09'

  - user_agent_string: 'Vert.x-WebClient/3.9.0'
    family: 'Vert.x-WebClient'
    major: '3'
    minor: '9'
    patch: '0'

  - user_agent_string: 'Apache-CXF/3.1.14-sap-07'
    family: 'Apache-CXF'
    major: '3'
    minor: '1'
    patch: '14'

  - user_agent_string: 'Go-CF-client/1.1'
    family: 'Go-CF-client'
    major: '1'
    minor: '1'
    patch:

  - user_agent_string: 'HTTPClient/1.0 (2.8.3, ruby 2.5.5 (2019-03-15))'
    family: 'HTTPClient'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'ping-service'
    family: 'ping-service'
    major:
    minor:
    patch:

  - user_agent_string: 'CloudFoundryJavaClient/unknown (Java; SAP AG/1.8.0_251) ReactorNetty/0.9.6.RELEASE (Netty/4.1.49.Final)'
    family: 'ReactorNetty'
    major: '0'
    minor: '9'
    patch: '6'

  - user_agent_string: 'lua-resty-http/0.13 (Lua) ngx_lua/10013'
    family: 'lua-resty-http'
    major: '0'
    minor: '13'
    patch:

  - user_agent_string: 'AHC/1.0'
    family: 'AHC'
    major: '1'
    minor: '0'
    patch:

  - user_agent_string: 'sap xsuaa'
    family: 'sap xsuaa'
    major:
    minor:
    patch:

  - user_agent_string: 'sap-leonardo-iot-sdk-nodejs / 0.1.4'
    family: 'sap-leonardo-iot-sdk-nodejs'
    major: '0'
    minor: '1'
    patch: '4'

  - user_agent_string: 'Node-oauth'
    family: 'Node-oauth'
    major:
    minor:
    patch:

  - user_agent_string: 'go-resty/1.12.0 (https://github.com/go-resty/resty)'
    family: 'go-resty'
    major: '1'
    minor: '12'
    patch: '0'

  - user_agent_string: 'Site24x7'
    family: 'Site24x7'
    major:
    minor:
    patch:

  - user_agent_string: 'SAP NetWeaver Application Server (1.0;740)'
    family: 'SAP NetWeaver Application Server'
    major: '7'
    minor: '40'
    patch:

  - user_agent_string: 'SAP CPI'
    family: 'SAP CPI'
    major:
    minor:
    patch:

  - user_agent_string: 'JAEGER_SECURITY'
    family: 'JAEGER_SECURITY'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (Linux; U; Android 8.1.0; tr; SNE-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.137 Tenta/2.10.3 Mobile Safari/537.36 Mobile'
    family: 'Tenta Browser'
    major: '2'
    minor: '10'
    patch: '3'

  - user_agent_string: 'Mozilla/5.0 (compatible; monitis - premium monitoring service; http://www.monitis.com)'
    family: 'Monitis'
    major:
    minor:
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E302 Pandora/1902.1'
    family: 'Pandora'
    major: '1902'
    minor: '1'
    patch:

  - user_agent_string: 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 Pandora/1904.1.3'
    family: 'Pandora'
    major: '1904'
    minor: '1'
    patch: '3'

  - user_agent_string: 'Pandora/1904.1 Android/8.0.0 heroqlteusc (ExoPlayerLib1.5.14.1)'
    family: 'Pandora'
    major: '1904'
    minor: '1'
    patch:

  - user_agent_string: 'Pandora/2091 CFNetwork/978.0.7 Darwin/18.5.0'
    family: 'Pandora'
    major: '2091'
    minor:
    patch:

  - user_agent_string: 'PandoraRSSCrawler/1.0 (<EMAIL>)'
    family: 'PandoraRSSCrawler'
    major:  '1'
    minor:  '0'
    patch:

  - user_agent_string: 'MinIO (linux; amd64) minio-go/v6.0.39 mc/2019-10-09T22:54:57Z'
    family: 'minio-go'
    major: '6'
    minor: '0'
    patch: '39'
    patch_minor: ''

  - user_agent_string: 'MinIO (darwin; amd64) minio-go/v6.0.45 mc/2019-12-17T23:26:28Z'
    family: 'minio-go'
    major: '6'
    minor: '0'
    patch: '45'
    patch_minor: ''

  - user_agent_string: 'http.rb/4.1.1'
    family: 'http.rb'
    major: '4'
    minor: '1'
    patch: '1'
    patch_minor: ''

  - user_agent_string: 'ureq/1.5.1'
    family: 'ureq'
    major: '1'
    minor: '5'
    patch: '1'
    patch_minor: ''

  - user_agent_string: 'Transmit/5.6.0'
    family: 'Transmit'
    major: '5'
    minor: '6'
    patch: '0'
    patch_minor: ''

  - user_agent_string: 'GuzzleHttp/6.3.3 PHP/7.1.17-1+0~20180505045738.17+stretch~1.gbpde69c6'
    family: 'GuzzleHttp'
    major: '6'
    minor: '3'
    patch: '3'
    patch_minor: ''

  - user_agent_string: 'Mozilla/4.5 (compatible; HTTrack 3.0x; Windows 98)'
    family: 'HTTrack'
    major:  '3'
    minor:  '0'
    patch:

  - user_agent_string: 'PostmanRuntime/7.20.1'
    family: 'PostmanRuntime'
    major: '7'
    minor: '20'
    patch: '1'

  - user_agent_string: 'Mozilla/4.0 (SerenityOS; x86_64) LibWeb+LibJS (Not KHTML, nor Gecko) LibWeb'
    family: 'SerenityOS Browser'
    major:
    minor:
    patch:

  - user_agent_string: 'surveyon/2.7.6 Mobile (Android: 11; MODEL:CPH2127; PRODUCT:CPH2127T2; MANUFACTURER:OPPO;)'
    family: 'Surveyon'
    major: '2'
    minor: '7'
    patch: '6'

  - user_agent_string: 'HTTPie/3.2.1'
    family: 'HTTPie'
    major: '3'
    minor: '2'
    patch: '1'
