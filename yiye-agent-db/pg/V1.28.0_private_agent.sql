
create table marketing_data_custom_audiences
(

id bigserial not null constraint marketing_data_custom_audiences_pkey primary key,

account_id bigint default NULL ,

platform_id bigint default NULL ,

tencent_audience_id bigint default NULL ,
custom_audience_file_id bigint default NULL ,
name varchar not null,
description varchar not null,
type varchar not null,
custom_status varchar not null,
user_count bigint default NULL ,
audience_spec json,
created_at timestamp,
updated_at timestamp
);

comment on column marketing_data_custom_audiences.id is '客户人群id';
comment on column marketing_data_custom_audiences.platform_id is '平台id';
comment on column marketing_data_custom_audiences.tencent_audience_id is '腾讯标定的客户人群id';
comment on column marketing_data_custom_audiences.custom_audience_file_id is '客户人群文件名称';
comment on column marketing_data_custom_audiences.description is '介绍';
comment on column marketing_data_custom_audiences.type is '客户人群类型';
comment on column marketing_data_custom_audiences.custom_status is '处理状态';
comment on column marketing_data_custom_audiences.user_count is '用户覆盖数';
comment on column marketing_data_custom_audiences.audience_spec is '客户人群信息';
CREATE UNIQUE INDEX uniqueindex_id_plateform_account ON marketing_data_custom_audiences(tencent_audience_id ,platform_id,account_id,account_id);
