CREATE TABLE ucenter_user
(
    "id"                  bigserial primary key,
    "username"            varchar,
    "email"               varchar        not null,
    "phone"               varchar        not null,
    "password"            varchar        not null,
    "department_id"       bigint,
    "permission_group_id" bigint,
    "level"               int4 default 0 not null,
    "role"                int4 default 1 not null,
    "status"              int4 default 1 not null,
    "created_at"          timestamp      not null,
    "updated_at"          timestamp      not null
);

create unique index ux_ucenter_user_email on ucenter_user (email);
create unique index ux_ucenter_user_phone on ucenter_user (phone);

comment on column ucenter_user.username is '用户名';
comment on column ucenter_user.email is '邮箱';
comment on column ucenter_user.phone is '手机号';
comment on column ucenter_user.password is '密码';
comment on column ucenter_user.department_id is '部门id';
comment on column ucenter_user.permission_group_id is '权限组id';
comment on column ucenter_user.level is '级别: 0 普通员工, 1 上级';
comment on column ucenter_user.role is '用户角色: 0 超级管理员, 1 员工';
comment on column ucenter_user.status is '用户状态: 1:正常, 0 删除';
comment on column ucenter_user.created_at is '创建时间';
comment on column ucenter_user.updated_at is '更新时间';

-- 初始化超管账号
INSERT INTO "ucenter_user"("username", "email", "phone", "password", "department_id", "permission_group_id",
                                    "level", "role", "status", "created_at", "updated_at")
VALUES ('yiye_agent_test', '<EMAIL>', '17602545477',
        '$2a$10$t.hKo/Z/7ln.YXxugUI6LOl1esG.q2pRiHdMVSou1UiQcbP6efwea', NULL, NULL, 1, 0, 1, now(), now());


-- 创建ucenter中的部门表
CREATE TABLE ucenter_department
(
    "id"         bigserial primary key,
    "name"       varchar   not null,
    "parent_id"  bigint,
    "created_at" timestamp NOT NULL,
    "updated_at" timestamp NOT NULL,
    "t_order"    int4      not null
);

-- 根据部门名称以及部门的父部门创建唯一索引
CREATE unique INDEX ux_ucenter_department_name_and_parentid on ucenter_department (name, parent_id);

comment on column ucenter_department.id is '部门ID';
comment on column ucenter_department.name is '部门名称';
comment on column ucenter_department.parent_id is '父级id';
comment on column ucenter_department.created_at is '创建时间';
comment on column ucenter_department.updated_at is '修改时间';
comment on column ucenter_department.t_order is '排序字段';

-- 权限表
create table ucenter_permission
(
    id         bigserial primary key,
    name       varchar,
    parent_id  bigint,
    level      integer,
    mapping    character varying[],
    module     varchar,
    t_type     integer default 0,
    created_at timestamp,
    updated_at timestamp
);

comment on table ucenter_permission is '权限';

comment on column ucenter_permission.name is '权限名称';

comment on column ucenter_permission.parent_id is '父级权限';

comment on column ucenter_permission.level is '权限级别: 1=一级导航,2=一级数据权限,3=二级导航,4=二级数据权限,5=操作权限.';

comment on column ucenter_permission.mapping is 'url匹配规则';

comment on column ucenter_permission.module is '模块名: 路由/字段/图表名称';

comment on column ucenter_permission.t_type is '权限类型: 0=操作权限,1=全部账户数据,2=仅分配账户数据,3=子级分配账户数据,4=字段权限,5=图表权限.';

-- 权限组表
create table ucenter_permission_group
(
    id             bigserial primary key,
    name           varchar,
    permission_ids bigint[],
    created_at     timestamp not null,
    updated_at     timestamp not null
);

comment on table ucenter_permission_group is '权限组';
comment on column ucenter_permission_group.name is '权限组名称';

create unique index ux_ucenter_permission_group_name on ucenter_permission_group (name);


-- 投放账户表
create table marketing_advertiser_account
(
    id                          bigserial primary key,
    account_id                  varchar,
    account_name                varchar,
    advertiser_account_group_id bigint,
    platform_name               varchar,
    account_status              integer,
    access_token                varchar,
    refresh_token               varchar,
    refresh_token_expires       timestamp,
    platform_id                 integer,
    parent_id                   integer,
    corporation_name            varchar,
    balance                     int8,
    target                      json,
    ext                         json,
    created_at                  timestamp,
    updated_at                  timestamp,
    is_extract                  bool default false
);
create index idx_maa_account_id on marketing_advertiser_account (account_id);

create index idx_maa_parent_id on marketing_advertiser_account (parent_id);

create index idx_maa_platform_id on marketing_advertiser_account (platform_id);

create index idx_maa_created_at on marketing_advertiser_account (created_at);

create index idx_maa_consumer_id on marketing_advertiser_account (advertiser_account_group_id);

comment on table marketing_advertiser_account is '投放账户表';

comment on column marketing_advertiser_account.id is '广告主id';

comment on column marketing_advertiser_account.account_name is '广告主名称';

comment on column marketing_advertiser_account.account_status is '广告主状态（0有效、1待审核、2审核不通过、3封停）';

comment on column marketing_advertiser_account.access_token is 'token';

comment on column marketing_advertiser_account.refresh_token is '刷新token';

comment on column marketing_advertiser_account.refresh_token_expires is '刷新token的过期时间';

comment on column marketing_advertiser_account.platform_id is '平台id';

comment on column marketing_advertiser_account.parent_id is '所属代理商id';

comment on column marketing_advertiser_account.target is '投放目标';

comment on column marketing_advertiser_account.ext is '扩展字段';

comment on column marketing_advertiser_account.ext is '扩展字段';

comment on column marketing_advertiser_account.is_extract is '是否抽取';
-- 客户表
create table marketing_advertiser_account_group
(
    id         bigserial primary key,
    name       varchar   not null,
    created_at timestamp not null,
    updated_at timestamp not null
);

create unique index ux_marketing_advertiser_account_group_name on marketing_advertiser_account_group (name);

comment on column marketing_advertiser_account_group.id is '客户ID';

comment on column marketing_advertiser_account_group.name is '客户名称';

comment on column marketing_advertiser_account_group.created_at is '创建时间';

comment on column marketing_advertiser_account_group.updated_at is '更新时间';

-- 投放账户与用户关系表
create table marketing_advertiser_account_rel
(
    id                    bigserial primary key,
    advertiser_account_id bigint    not null,
    user_id               bigint,
    created_at            timestamp not null,
    updated_at            timestamp not null
);

create unique index ux_marketing_advertiser_account_rel_accountid_and_userid
    on marketing_advertiser_account_rel (user_id, advertiser_account_id);

comment on column marketing_advertiser_account_rel.id is '关系id';

comment on column marketing_advertiser_account_rel.advertiser_account_id is '投放账户id';

comment on column marketing_advertiser_account_rel.user_id is '用户id';

comment on column marketing_advertiser_account_rel.created_at is '创建时间';

comment on column marketing_advertiser_account_rel.updated_at is '更新时间';

-- 第三方平台配置表
create table marketing_platform_conf
(
    id                      bigserial primary key,
    name                    varchar   not null,
    logo_list               varchar   not null,
    logo_auth               varchar   not null,
    marketing_client_id     varchar,
    marketing_client_secret varchar,
    authorization_url       varchar,
    created_at              timestamp not null,
    updated_at              timestamp not null
);

create index ux_mpc_client_id on marketing_platform_conf (marketing_client_id);
create index idx_mpc_name on marketing_platform_conf (name);

comment on table marketing_platform_conf is '第三方平台配置表';

comment on column marketing_platform_conf.name is '平台名称';

comment on column marketing_platform_conf.logo_list is '列表页logo';

comment on column marketing_platform_conf.logo_auth is '授权页logo';

comment on column marketing_platform_conf.marketing_client_id is '应用id';

comment on column marketing_platform_conf.marketing_client_secret is '应用秘钥';

comment on column marketing_platform_conf.authorization_url is '授权地址';

-- 公域表内私域client_id配置表
create table if not exists agent_conf
(
    id         bigserial primary key,
    agent_id   varchar not null,
    agent_name varchar not null,
    host       varchar not null,
    status     integer default 1,
    created_at timestamp,
    updated_at timestamp
);

create unique index if not exists idx_agent_conf_agent_id on agent_conf (agent_id);

comment on table agent_conf is '私域配置表';

comment on column agent_conf.id is 'id';

comment on column agent_conf.agent_id is '私域id';

comment on column agent_conf.agent_name is '私域名称';

comment on column agent_conf.host is 'api地址';

comment on column agent_conf.status is '状态; 1已启用, 0已停用';

comment on column agent_conf.created_at is '创建时间';

comment on column agent_conf.updated_at is '更新时间';


-- 微信用户表
create table if not exists message_wechat_user
(
    id         bigserial primary key,
    user_id    bigint,
    country    varchar,
    province   varchar,
    city       varchar,
    openid     varchar,
    sex        varchar,
    nickname   varchar,
    headimgurl varchar,
    language   varchar,
    privilege  varchar,
    created_at timestamp not null,
    updated_at timestamp not null,
    config     json
);

comment on table message_wechat_user is '微信用户-用户';

comment on column message_wechat_user.user_id is '用户id';

comment on column message_wechat_user.country is '国家，如中国为CN';

comment on column message_wechat_user.province is '用户个人资料填写的省份';

comment on column message_wechat_user.city is '普通用户个人资料填写的城市';

comment on column message_wechat_user.openid is '授权用户唯一标识';

comment on column message_wechat_user.sex is '用户的性别';

comment on column message_wechat_user.nickname is '用户昵称';

comment on column message_wechat_user.headimgurl is '用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。';

comment on column message_wechat_user.language is '用户语言';

comment on column message_wechat_user.privilege is '用户特权信息，json 数组，如微信沃卡用户为（chinaunicom）';

comment on column message_wechat_user.created_at is '创建时间';

comment on column message_wechat_user.updated_at is '更新时间';

comment on column message_wechat_user.config is '用户微信推送配置：{“linkman-switch”:true}';


--短信日志表
create table message_sms_send_log
(
    id                    bigserial primary key,
    mobile                varchar                                not null,
    content               varchar                                not null,
    send_at               timestamp,
    arrive_at             timestamp,
    count                 integer,
    send_status           varchar,
    sms_signature_id      bigint,
    ext                   json,
    created_at            timestamp,
    updated_at            timestamp,
    flag_id               varchar                                not null,
    msg_id                varchar                                not null,
    sync_status           varchar default '0'::character varying not null,
    advertiser_account_id bigint                                 not null
);

comment on table message_sms_send_log is '短信记录表';

comment on column message_sms_send_log.mobile is '手机号';

comment on column message_sms_send_log.content is '发送内容';

comment on column message_sms_send_log.send_at is '发送时间';

comment on column message_sms_send_log.arrive_at is '到达时间';

comment on column message_sms_send_log.count is '短信条数';

comment on column message_sms_send_log.send_status is '短信状态';

comment on column message_sms_send_log.sms_signature_id is '短信签名id';

comment on column message_sms_send_log.ext is '扩展';

comment on column message_sms_send_log.created_at is '创建时间';

comment on column message_sms_send_log.updated_at is '更新时间';

comment on column message_sms_send_log.flag_id is '短信唯一标识id';

comment on column message_sms_send_log.msg_id is '第三方短信唯一标识';

comment on column message_sms_send_log.sync_status is '短信同步状态 0 未同步 1 已同步';

comment on column message_sms_send_log.advertiser_account_id is '投放账户id';


--短信签名表
create table if not exists message_sms_signature
(
    id                    bigserial primary key,
    name                  varchar           not null,
    advertiser_account_id bigint,
    updated_id            integer,
    version               integer default 0,
    status                integer default 0 not null,
    created_at            timestamp         not null,
    updated_at            timestamp         not null,
    type                  integer,
    ext                   json
);

comment on table message_sms_signature is '短信签名表';

comment on column message_sms_signature.name is '签名名称';

comment on column message_sms_signature.advertiser_account_id is '投放账户id';

comment on column message_sms_signature.updated_id is '更新操作用户id';

comment on column message_sms_signature.version is '版本';

comment on column message_sms_signature.status is '状态（0停用 ；1可用）';

comment on column message_sms_signature.created_at is '创建时间';

comment on column message_sms_signature.updated_at is '更新时间';

comment on column message_sms_signature.type is '签名类型：0 默认签名， 1 自定义签名';

comment on column message_sms_signature.ext is '扩展字段';


--webhook
create table if not exists message_webhook
(
    id                                bigserial primary key,
    url                               varchar   not null,
    created_at                        timestamp not null,
    updated_at                        timestamp not null,
    relation_advertiser_account_names varchar
);

-- 创建唯一索引
create unique index ux_message_webhook_url on message_webhook (url);

comment on table message_webhook is 'webhook内容表';

comment on column message_webhook.url is 'url地址';

comment on column message_webhook.created_at is '创建时间';

comment on column message_webhook.updated_at is '更新时间';

comment on column message_webhook.relation_advertiser_account_names is '关联投放账户和客户名称';


--webhook与投放账户和客户关联表
create table message_webhook_advertiser_account_rel
(
    id                    bigserial primary key,
    webhook_id            bigint    not null,
    advertiser_account_id bigint    not null,
    created_at            timestamp not null,
    updated_at            timestamp not null
);

comment on table message_webhook_advertiser_account_rel is 'webhook与投放账户关系表';

comment on column message_webhook_advertiser_account_rel.webhook_id is 'webhook地址id';

comment on column message_webhook_advertiser_account_rel.advertiser_account_id is '投放账户id';

comment on column message_webhook_advertiser_account_rel.created_at is '创建时间';

comment on column message_webhook_advertiser_account_rel.updated_at is '更新时间';


-- 微信商务号的绑定关系
CREATE TABLE "payment_wechat_official_account_rel"
(
    "id"                          bigserial primary key,
    "official_account_id"         bigint    NOT NULL,
    "advertiser_account_id"       bigint,
    "advertiser_account_group_id" bigint,
    "type"                        int4,
    "created_at"                  timestamp NOT NULL,
    "updated_at"                  timestamp NOT NULL
);
-- 创建唯一索引
CREATE unique INDEX ux_pwoar_officeId_and_accountid_and_type on
    payment_wechat_official_account_rel (official_account_id, advertiser_account_id, type);

CREATE unique INDEX ux_pwoar_officeId_and_groupid_and_type on
    payment_wechat_official_account_rel (official_account_id, advertiser_account_group_id, type);


COMMENT ON COLUMN "payment_wechat_official_account_rel"."id" IS '关系id';
COMMENT ON COLUMN "payment_wechat_official_account_rel"."official_account_id" IS '微信商务号id';
COMMENT ON COLUMN "payment_wechat_official_account_rel"."advertiser_account_id" IS '投放账户ID';
COMMENT ON COLUMN "payment_wechat_official_account_rel"."advertiser_account_group_id" IS '客户ID';
COMMENT ON COLUMN "payment_wechat_official_account_rel"."type" IS '投放关系类型';
COMMENT ON COLUMN "payment_wechat_official_account_rel"."created_at" IS '创建时间';
COMMENT ON COLUMN "payment_wechat_official_account_rel"."updated_at" IS '修改时间';


-- 商户号配置
create table payment_wechat_merchant_account
(
    id                 bigserial primary key,
    wechat_official_id integer,
    name               varchar,
    mch_id             varchar,
    mch_key            varchar,
    status             integer,
    created_at         timestamp not null,
    updated_at         timestamp not null,
    certificate_name   varchar,
    certificate_path   varchar
);

create index idx_wechat_merchant_account_wo_id on payment_wechat_merchant_account (wechat_official_id);

comment on table payment_wechat_merchant_account is '商户号配置';

comment on column payment_wechat_merchant_account.wechat_official_id is '公众号id';

comment on column payment_wechat_merchant_account.name is '商户号名称';

comment on column payment_wechat_merchant_account.mch_id is '商户号id';

comment on column payment_wechat_merchant_account.mch_key is '商户号秘钥';

comment on column payment_wechat_merchant_account.status is '授权状态. 0: 未验证; 1: 已验证';

comment on column payment_wechat_merchant_account.certificate_name is '上传证书原名';

comment on column payment_wechat_merchant_account.certificate_path is '证书保存的路径';

-- 公众号配置
create table payment_wechat_official_account
(
    id           bigserial primary key,
    app_id       varchar,
    name         varchar,
    app_secret   varchar,
    file_name    varchar,
    file_content varchar,
    created_at   timestamp not null,
    updated_at   timestamp not null
);

create index idx_pwoa_app_id on payment_wechat_official_account (app_id);

comment on table payment_wechat_official_account is '公众号配置';

comment on column payment_wechat_official_account.app_id is 'appId';

comment on column payment_wechat_official_account.name is '公众号名称';

comment on column payment_wechat_official_account.app_secret is '公众号秘钥';

comment on column payment_wechat_official_account.file_name is '文件名称';

comment on column payment_wechat_official_account.file_content is '文件内容';


-- 域名表
CREATE TABLE "landing_page_domain"
(
    "id"         bigserial primary key,
    "domain"     varchar   NOT NULL,
    "created_at" timestamp NOT NULL,
    "updated_at" timestamp NOT NULL
);

CREATE unique INDEX ux_landing_page_domain_name on landing_page_domain (domain);

COMMENT ON COLUMN "landing_page_domain"."domain" IS '域名名称';
COMMENT ON COLUMN "landing_page_domain"."created_at" IS '创建时间';
COMMENT ON COLUMN "landing_page_domain"."updated_at" IS '更新时间';
COMMENT ON TABLE "landing_page_domain" IS '域名表';


-- 域名关系表
CREATE TABLE "landing_page_domain_rel"
(
    "id"                    bigserial primary key,
    "domain_id"             int8,
    "advertiser_account_id" int8,
    "created_at"            timestamp NOT NULL,
    "updated_at"            timestamp NOT NULL
);

COMMENT ON COLUMN "landing_page_domain_rel"."domain_id" IS '域名名称id';
COMMENT ON COLUMN "landing_page_domain_rel"."advertiser_account_id" IS '投放账户id';
COMMENT ON COLUMN "landing_page_domain_rel"."created_at" IS '创建时间';
COMMENT ON COLUMN "landing_page_domain_rel"."updated_at" IS '更新时间';
COMMENT ON TABLE "landing_page_domain_rel" IS '域名投放账户关系表';

--- 添加权限
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (100, '账户管理', null, 1, null, 'marketing-advertiser-account-module', 0, '2020-05-20 13:03:44.496237',
        '2020-05-20 13:03:44.496237');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (200, '组织管理', null, 1, null, 'ucenter-module', 0, '2020-05-20 13:03:44.631793', '2020-05-20 13:03:44.631793');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (300, '系统设置', null, 1, null, 'system-setting-module', 0, '2020-05-20 13:03:44.631793',
        '2020-05-20 13:03:44.631793');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (10000, '账户管理', 100, 3,
        array [ '{"url":"/advertiser-accounts/**","methods":["GET"]}','{"url":"/advertiser-account-groups/**","methods":["GET"]}','{"url":"/platforms/**","methods":["GET"]}','{"url":"/departments","methods":["GET"],"type":"EXCLUDE"}','{"url":"/users/collect/**","methods":["GET"],"type":"EXCLUDE"}' ],
        'advertiser-account-list', 0, '2020-05-20 13:03:44.697995', '2020-05-20 13:03:44.697995');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (10001, '全部账户', 100, 2, null, null, 1, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (10002, '仅分配账户', 100, 2, null, null, 2, '2020-05-20 13:03:44.830116', '2020-05-20 13:03:44.830116');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (10003, '子级分配账户', 100, 2, null, null, 3, '2020-05-20 13:03:44.860082', '2020-05-20 13:03:44.860082');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (20000, '员工管理', 200, 3,
        array [ '{"url":"/users/collect/filtering*","methods":["GET"]}','{"url":"/permission-groups*","methods":["GET"],"type":"EXCLUDE"}' ],
        'ucenter-user-list', 0, '2020-05-20 13:03:44.915738', '2020-05-20 13:03:44.915738');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (20002, '权限管理', 200, 3, array [ '{"url":"/permission-groups/collect/filtering","methods":["GET"]}' ],
        'ucenter-permission-and-permission-group-list', 0, '2020-05-20 13:03:45.110658', '2020-05-20 13:03:45.110658');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30000, '通知', 300, 3,
        array [ '{"url":"/wechat-users/fetch/mine","methods":["GET"]}','{"url":"/wechat-users/fetch/change","methods":["GET"],"type":"EXCLUDE"}' ],
        'message-wechat-send', 0, '2020-05-20 12:52:04.835726', '2020-05-20 12:52:04.835726');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30001, '域名绑定', 300, 3,
        array [ '{"url":"/domains/**","methods":["GET"]}','{"url":"/advertiser-accounts","methods":["GET"],"type":"EXCLUDE"}','{"url":"/advertiser-account-groups","methods":["GET"],"type":"EXCLUDE"}','{"url":"/domains/**","methods":["POST","PATCH"]}' ],
        'landing-page-domain-list', 0, '2020-05-22 19:05:09.000000', '2020-05-22 19:05:11.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30002, '接口管理', 300, 3,
        array [ '{"url":"/webhooks/collect/filtering","methods":["GET"]}','{"url":"/advertiser-accounts","methods":["GET"],"type":"EXCLUDE"}','{"url":"/advertiser-account-groups","methods":["GET"],"type":"EXCLUDE"}' ],
        'message-webhook-list', 0, '2020-05-22 19:05:09.000000', '2020-05-22 19:05:11.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30003, '支付设置', 300, 3, array [ '{"url":"/wechat-official-accounts/**","methods":["GET"]}' ], 'payment-module',
        0, '2020-05-22 19:05:09.000000', '2020-05-22 19:05:11.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000000, '添加账户', 10000, 4, array [ '{"url":"/action/auth-begin","methods":["POST"]}' ],
        'marketing-advertiser-account-add', 0, '2020-05-20 13:03:45.177149', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000001, '设置目标', 10000, 4, array [ '{"url":"/advertiser-accounts","methods":["PATCH"]}' ],
        'marketing-advertiser-account-target', 0, '2020-05-28 03:07:55.000000', '2020-05-28 03:07:59.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000002, '分配人员', 10000, 4, array [ '{"url":"/advertiser-account-rels/**","methods":["PATCH"]}' ],
        'marketing-advertiser-account-user-rel', 0, '2020-05-28 03:09:03.000000', '2020-05-28 03:09:04.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000003, '绑定投放账户', 10000, 4, array [ '{"url":"/advertiser-accounts/batch","methods":["PATCH"]}' ],
        'marketing-advertiser-account-binding', 0, '2020-05-28 03:10:02.000000', '2020-05-28 03:10:03.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000004, '解绑', 10000, 4,
        array [ '{"url":"/advertiser-accounts/*/actions/unbinding-group","methods":["PATCH"]}' ],
        'marketing-advertiser-account-group-unbinding', 0, '2020-05-20 13:03:44.697995', '2020-05-20 13:03:44.697995');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000005, '添加客户分组', 10000, 4, array [ '{"url":"/advertiser-account-groups","methods":["POST"]}' ],
        'marketing-advertiser-account-group-add', 0, '2020-05-28 03:11:45.000000', '2020-05-28 03:11:46.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000000, '新建/编辑员工', 20000, 4, array [ '{"url":"/users/**","methods":["POST","PATCH"]}' ],
        'ucenter-user-add-and-edit', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000001, '删除', 20000, 4, array [ '{"url":"/users/**","methods":["DELETE"]}' ], 'ucenter-user-delete', 0,
        '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000002, '密码管理', 20000, 4, array [ '{"url":"/users/**","methods":["PATCH"]}' ], 'ucenter-user-password', 0,
        '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000003, '从属管理', 20000, 4,
        array [ '{"url":"/advertiser-account-rels/relate/user-bind-advertiser-accounts","methods":["PATCH"]}' ],
        'ucenter-user-subordinate-management', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000004, '新建/编辑部门', 20000, 4, array [ '{"url":"/departments/**","methods":["POST","PATCH"]}' ],
        'ucenter-department-add-and-edit', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000005, '删除部门', 20000, 4, array [ '{"url":"/departments/**","methods":["DELETE"]}' ],
        'ucenter-department-delete', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000201, '新建/编辑', 20002, 4,
        array [ '{"url":"/permission-groups/**", "methods":["POST","PATCH","GET"]}','{"url":"/permission-groups/collect/filtering","methods":["GET"],"type":"EXCLUDE"}' ],
        'ucenter-permission-group-add-and-edit', 0, '2020-05-20 13:03:45.342967', '2020-05-20 13:03:45.342967');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000202, '删除', 20002, 4, array [ '{"url":"/permission-groups/**", "methods":["DELETE"]}' ],
        'ucenter-permission-group-delete', 0, '2020-05-20 13:03:45.388573', '2020-05-20 13:03:45.388573');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000101, '新建/编辑', 30001, 4,
        array [ '{"url":"/domains/**","methods":["POST","PATCH"]}','{"url":"/landing-page-domain-rels/**","methods":["GET","POST","PUT"]}' ],
        'landing-page-domain-add-and-edit', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000102, '删除', 30001, 4, array [ '{"url":"/domains/**","methods":["DELETE"]}' ], 'landing-page-domain-delete',
        0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000201, '新建/编辑', 30002, 4,
        array [ '{"url":"/webhooks/**","methods":["POST","PATCH"]}','{"url":"/webhook-advertiser-accounts/**","methods":["POST","GET"]}','{"url":"/webhooks/fetch/check-repeated","methods":["GET"]}' ],
        'message-webhook-add-and-edit', 0, '2020-05-26 09:46:16.818291', '2020-05-26 09:46:16.818291');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000202, '删除', 30002, 4, array [ '{"url":"/webhooks/**","methods":["DELETE"]}' ], 'message-webhook-delete', 0,
        '2020-05-26 09:49:24.816709', '2020-05-26 09:49:24.816709');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000301, '新建/编辑', 30003, 4,
        array [ '{"url":"/wechat-official-accounts/**","methods":["POST","PATCH"]}','{"url":"/wechat-merchant-accounts/**","methods":["POST","PATCH"]}' ],
        'payment-add-and-edit', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000302, '删除', 30003, 4,
        array [ '{"url":"/wechat-official-accounts/**","methods":["DELETE"]}','{"url":"/wechat-merchant-accounts/**","methods":["DELETE"]}' ],
        'payment-delete', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');


alter table marketing_platform_conf
    add redirect_url varchar;
alter table marketing_advertiser_account
    add optimizer_id bigint;
alter table marketing_advertiser_account
    add system_status int default 1;
alter table marketing_advertiser_account
    add parent_account_id bigint;
alter table marketing_advertiser_account
    add agency boolean default false;
alter table marketing_advertiser_account
    add daily_budget bigint;
alter table marketing_advertiser_account
    add user_action_set_id bigint;
alter table marketing_advertiser_account
    drop refresh_token_expires;
alter table marketing_advertiser_account
    drop parent_id;
alter table marketing_advertiser_account
    alter platform_id type bigint;
alter table marketing_advertiser_account
    alter column account_id type bigint using account_id::bigint;
comment on column marketing_advertiser_account.optimizer_id is '优化师id';
comment on column marketing_advertiser_account.system_status is '投放账户系统状态: 0=正常, 1=停用.';
comment on column marketing_advertiser_account.parent_account_id is '所属代理商账号id';

create unique index ux_marketing_platform_account_id on marketing_advertiser_account (platform_id, account_id);
create index ux_marketing_parent_account_id on marketing_advertiser_account (parent_account_id);
create index ux_marketing_optimizer_id on marketing_advertiser_account (optimizer_id);
drop index if exists idx_maa_platform_id;

update ucenter_permission
set level = 5
where level = 4;

truncate ucenter_permission;
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30, '工作台', NULL, 1, NULL, 'workbench-module', 0, '2020-06-08 11:38:55', '2020-06-08 11:38:56');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (100, '账户管理', NULL, 1, NULL, 'marketing-advertiser-account-module', 0, '2020-05-20 13:03:44.496237',
        '2020-05-20 13:03:44.496237');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (130, '人效管理', NULL, 1, NULL, 'user-efficiency-module', 0, '2020-05-20 13:03:44.496237',
        '2020-05-20 13:03:44.496237');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (200, '组织管理', NULL, 1, NULL, 'ucenter-module', 0, '2020-05-20 13:03:44.631793', '2020-05-20 13:03:44.631793');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (300, '系统设置', NULL, 1, NULL, 'system-setting-module', 0, '2020-05-20 13:03:44.631793',
        '2020-05-20 13:03:44.631793');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (400, '落地页', NULL, 1, NULL, 'landing-page-module', 0, '2020-06-08 11:38:55', '2020-06-08 11:38:56');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (500, '客资', NULL, 1, NULL, 'linkman-module', 0, '2020-06-08 11:38:55', '2020-06-08 11:38:56');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (700, '数据资产', NULL, 1, NULL, 'data-asset-module', 0, '2020-06-08 11:38:55', '2020-06-08 11:38:56');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (800, '数据分析', NULL, 1, NULL, 'data-analyse-module', 0, '2020-06-08 11:38:55', '2020-06-08 11:38:56');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000, '全部账户', 30, 2, NULL, NULL, 1, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3001, '仅分配账户', 30, 2, NULL, NULL, 2, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3002, '子级分配账户', 30, 2, NULL, NULL, 3, '2020-05-20 13:03:44.860082', '2020-05-20 13:03:44.860082');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3003, '工作台', 30, 3, NULL, 'workbench-list', 0, '2020-06-08 11:59:00', '2020-06-08 11:59:01');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (10001, '全部账户', 100, 2, NULL, NULL, 1, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (10002, '仅分配账户', 100, 2, NULL, NULL, 2, '2020-05-20 13:03:44.830116', '2020-05-20 13:03:44.830116');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (10003, '子级分配账户', 100, 2, NULL, NULL, 3, '2020-05-20 13:03:44.860082', '2020-05-20 13:03:44.860082');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (13000, '全部账户', 130, 2, NULL, NULL, 1, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (13001, '仅分配账户', 130, 2, NULL, NULL, 2, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (13002, '子级分配账户', 130, 2, NULL, NULL, 3, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (13003, '人效管理', 130, 3, NULL, 'user-efficiency-list', 0, '2020-05-20 13:03:44.759411',
        '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (20000, '人员管理', 200, 3,
        '{"{\"url\":\"/users/collect/filtering*\",\"methods\":[\"GET\"]}","{\"url\":\"/permission-groups*\",\"methods\":[\"GET\"],\"type\":\"EXCLUDE\"}"}',
        'ucenter-user-list', 0, '2020-05-20 13:03:44.915738', '2020-05-20 13:03:44.915738');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (20002, '权限管理', 200, 3, '{"{\"url\":\"/permission-groups/collect/filtering\",\"methods\":[\"GET\"]}"}',
        'ucenter-permission-and-permission-group-list', 0, '2020-05-20 13:03:45.110658', '2020-05-20 13:03:45.110658');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30000, '通知', 300, 3,
        '{"{\"url\":\"/wechat-users/fetch/mine\",\"methods\":[\"GET\"]}","{\"url\":\"/wechat-users/fetch/change\",\"methods\":[\"GET\"],\"type\":\"EXCLUDE\"}"}',
        'message-wechat-send', 0, '2020-05-20 12:52:04.835726', '2020-05-20 12:52:04.835726');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30001, '域名绑定', 300, 3,
        '{"{\"url\":\"/domains/**\",\"methods\":[\"GET\"]}","{\"url\":\"/advertiser-accounts\",\"methods\":[\"GET\"],\"type\":\"EXCLUDE\"}","{\"url\":\"/advertiser-account-groups\",\"methods\":[\"GET\"],\"type\":\"EXCLUDE\"}","{\"url\":\"/domains/**\",\"methods\":[\"POST\",\"PATCH\"]}"}',
        'landing-page-domain-list', 0, '2020-05-22 19:05:09', '2020-05-22 19:05:11');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30002, '接口管理', 300, 3,
        '{"{\"url\":\"/webhooks/collect/filtering\",\"methods\":[\"GET\"]}","{\"url\":\"/advertiser-accounts\",\"methods\":[\"GET\"],\"type\":\"EXCLUDE\"}","{\"url\":\"/advertiser-account-groups\",\"methods\":[\"GET\"],\"type\":\"EXCLUDE\"}"}',
        'message-webhook-list', 0, '2020-05-22 19:05:09', '2020-05-22 19:05:11');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30003, '支付设置', 300, 3, '{"{\"url\":\"/wechat-official-accounts/**\",\"methods\":[\"GET\"]}"}', 'payment-module',
        0, '2020-05-22 19:05:09', '2020-05-22 19:05:11');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (70000, '全部账户', 700, 2, NULL, NULL, 1, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (70001, '仅分配账户', 700, 2, NULL, NULL, 2, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (70002, '子级分配账户', 700, 2, NULL, NULL, 3, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (70003, '数据资产概况', 700, 3, NULL, NULL, 0, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (70004, '数据资产管理', 700, 3, NULL, NULL, 0, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (70005, '监测链接管理', 700, 3, NULL, NULL, 0, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (80000, '数据分析', 800, 3, NULL, 'data-analyse-list', 0, '2020-06-08 11:59:00', '2020-06-08 11:59:01');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (300000, '进入账户', 3003, 5, NULL, 'workbench-advertiser-account-page', 0, '2020-06-08 11:59:00',
        '2020-06-08 11:59:01');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000000, '添加账户', 10000, 5, '{"{\"url\":\"/advertiser-accounts/action/auth-begin\",\"methods\":[\"POST\"]}"}',
        'marketing-advertiser-account-add', 0, '2020-05-20 13:03:45.177149', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000001, '设置目标', 10000, 5, '{"{\"url\":\"/advertiser-accounts\",\"methods\":[\"PATCH\"]}"}',
        'marketing-advertiser-account-target', 0, '2020-05-28 03:07:55', '2020-05-28 03:07:59');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000002, '分配人员', 10000, 5, '{"{\"url\":\"/advertiser-accounts/*/relate/user-relate\",\"methods\":[\"PUT\"]}"}',
        'marketing-advertiser-account-user-rel', 0, '2020-05-28 03:09:03', '2020-05-28 03:09:04');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000003, '绑定投放账户', 10000, 5, '{"{\"url\":\"/advertiser-accounts/batch\",\"methods\":[\"PATCH\"]}"}',
        'marketing-advertiser-account-binding', 0, '2020-05-28 03:10:02', '2020-05-28 03:10:03');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000004, '解绑', 10000, 5,
        '{"{\"url\":\"/advertiser-accounts/actions/unbinding-group/batch\",\"methods\":[\"PATCH\"]}"}',
        'marketing-advertiser-account-group-unbinding', 0, '2020-05-20 13:03:44.697995', '2020-05-20 13:03:44.697995');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000005, '添加客户分组', 10000, 5, '{"{\"url\":\"/advertiser-account-groups\",\"methods\":[\"POST\"]}"}',
        'marketing-advertiser-account-group-add', 0, '2020-05-28 03:11:45', '2020-05-28 03:11:46');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000006, '停用/启用', 10000, 5,
        '{"{\"url\":\"/advertiser-accounts/*/action/enable-advertiser-account\",\"methods\":[\"POST\"]}","{\"url\":\"/advertiser-accounts/*/action/disable-advertiser-account\",\"methods\":[\"POST\"]}"}',
        'marketing-advertiser-account-edit-system-status', 0, '2020-06-15 01:51:29.655712',
        '2020-06-15 01:51:29.655712');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1300000, '花费对比图', 13003, 4, NULL, 'user-efficiency-cost-comparison-chart', 0, NULL, NULL);
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000000, '新建/编辑员工', 20000, 5, '{"{\"url\":\"/users/**\",\"methods\":[\"POST\",\"PATCH\"]}"}',
        'ucenter-user-add-and-edit', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000001, '删除', 20000, 5, '{"{\"url\":\"/users/**\",\"methods\":[\"DELETE\"]}"}', 'ucenter-user-delete', 0,
        '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000002, '密码管理', 20000, 5, '{"{\"url\":\"/users/**\",\"methods\":[\"PATCH\"]}"}', 'ucenter-user-password', 0,
        '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000003, '从属管理', 20000, 5,
        '{"{\"url\":\"/advertiser-account-rels/relate/user-bind-advertiser-accounts\",\"methods\":[\"PATCH\"]}"}',
        'ucenter-user-subordinate-management', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (40004, '回收站', 400, 3, '{"{\"url\":\"/landing-page-trashs/collect/filtering\", \"methods\":[\"GET\"]}"}',
        'landing-page-trash-list', 0, '2020-06-08 11:59:00', '2020-06-08 11:59:01');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (40005, '表单模板', 400, 3, '{"{\"url\":\"/widget-templates/collect/filtering\", \"methods\":[\"GET\"]}"}',
        'form-list', 0, '2020-06-08 11:59:00', '2020-06-08 11:59:01');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (40007, '评论模板', 400, 3, '{"{\"url\":\"/widget-templates/collect/filtering\", \"methods\":[\"GET\"]}"}',
        'comment-form-list', 0, '2020-06-08 11:59:00', '2020-06-08 11:59:01');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000004, '新建/编辑部门', 20000, 5, '{"{\"url\":\"/departments/**\",\"methods\":[\"POST\",\"PATCH\"]}"}',
        'ucenter-department-add-and-edit', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000005, '删除部门', 20000, 5, '{"{\"url\":\"/departments/**\",\"methods\":[\"DELETE\"]}"}',
        'ucenter-department-delete', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000201, '新建/编辑', 20002, 5,
        '{"{\"url\":\"/permission-groups/**\", \"methods\":[\"POST\",\"PATCH\",\"GET\"]}","{\"url\":\"/permission-groups/collect/filtering\",\"methods\":[\"GET\"],\"type\":\"EXCLUDE\"}"}',
        'ucenter-permission-group-add-and-edit', 0, '2020-05-20 13:03:45.342967', '2020-05-20 13:03:45.342967');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000202, '删除', 20002, 5, '{"{\"url\":\"/permission-groups/**\", \"methods\":[\"DELETE\"]}"}',
        'ucenter-permission-group-delete', 0, '2020-05-20 13:03:45.388573', '2020-05-20 13:03:45.388573');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000101, '新建/编辑', 30001, 5,
        '{"{\"url\":\"/domains/**\",\"methods\":[\"POST\",\"PATCH\"]}","{\"url\":\"/landing-page-domain-rels/**\",\"methods\":[\"GET\",\"POST\",\"PUT\"]}"}',
        'landing-page-domain-add-and-edit', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000102, '删除', 30001, 5, '{"{\"url\":\"/domains/**\",\"methods\":[\"DELETE\"]}"}', 'landing-page-domain-delete',
        0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000201, '新建/编辑', 30002, 5,
        '{"{\"url\":\"/webhooks/**\",\"methods\":[\"POST\",\"PATCH\"]}","{\"url\":\"/webhook-advertiser-accounts/**\",\"methods\":[\"POST\",\"GET\"]}","{\"url\":\"/webhooks/fetch/check-repeated\",\"methods\":[\"GET\"]}"}',
        'message-webhook-add-and-edit', 0, '2020-05-26 09:46:16.818291', '2020-05-26 09:46:16.818291');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000202, '删除', 30002, 5, '{"{\"url\":\"/webhooks/**\",\"methods\":[\"DELETE\"]}"}', 'message-webhook-delete', 0,
        '2020-05-26 09:49:24.816709', '2020-05-26 09:49:24.816709');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000301, '新建/编辑', 30003, 5,
        '{"{\"url\":\"/wechat-official-accounts/**\",\"methods\":[\"POST\",\"PATCH\"]}","{\"url\":\"/wechat-merchant-accounts/**\",\"methods\":[\"POST\",\"PATCH\"]}","{\"url\":\"/wechat-official-accounts/action/check-repeat\",\"methods\":[\"GET\"]}","{\"url\":\"/wechat-merchant-accounts/action/check-repeat\",\"methods\":[\"GET\"]}"}',
        'payment-add-and-edit', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000302, '删除', 30003, 5,
        '{"{\"url\":\"/wechat-official-accounts/**\",\"methods\":[\"DELETE\"]}","{\"url\":\"/wechat-merchant-accounts/**\",\"methods\":[\"DELETE\"]}"}',
        'payment-delete', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500001, '电话', 50000, 4, NULL, 'phone', 4, '2020-06-09 13:41:35', '2020-06-09 13:41:37');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500002, 'QQ', 50000, 4, NULL, 'qq', 4, '2020-06-09 13:41:35', '2020-06-09 13:41:37');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500003, '邮箱', 50000, 4, NULL, 'email', 4, '2020-06-09 13:41:35', '2020-06-09 13:41:37');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500004, '微信', 50000, 4, NULL, 'wechat', 4, '2020-06-09 13:41:35', '2020-06-09 13:41:37');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500006, '所属落地页', 50000, 4, NULL, 'landingPageName', 4, '2020-06-09 13:41:35', '2020-06-09 13:41:37');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (7000400, '创建人群包', 70004, 5, NULL, 'data-asset-bundle-add', 0, '2020-05-20 13:03:45.177149',
        '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (7000401, '删除', 70004, 5, NULL, 'data-asset-bundle-delete', 0, '2020-05-20 13:03:45.177149',
        '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (7000402, '应用', 70004, 5, NULL, 'data-asset-bundle-upload', 0, '2020-05-20 13:03:45.177149',
        '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (7000403, '下载', 70004, 5, NULL, 'data-asset-bundle-download', 0, '2020-05-20 13:03:45.177149',
        '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (7000500, '点击配置转发', 70005, 5, NULL, 'data-asset-ad-trace-forward', 0, '2020-05-20 13:03:45.177149',
        '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500000, '姓名', 50000, 4, NULL, 'name', 4, '2020-06-09 13:41:35', '2020-06-09 13:41:37');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500012, '数量', 50000, 4, NULL, 'number', 4, '2020-06-23 09:38:29', '2020-06-23 09:38:30');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500013, '单价', 50000, 4, NULL, 'price', 4, '2020-06-23 09:38:48', '2020-06-23 09:38:50');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500014, '总价', 50000, 4, NULL, 'totalPrice', 4, '2020-06-23 09:39:16', '2020-06-23 09:39:17');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500015, '地址', 50000, 4, NULL, 'showAddress', 4, '2020-06-23 09:39:33', '2020-06-23 09:39:34');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500005, '所属表单', 50000, 4, NULL, 'formName', 4, '2020-06-09 13:41:35', '2020-06-09 13:41:37');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (50000, '客资列表', 500, 3, '{"{\"url\": \"/customers\", \"methods\": [\"GET\"]}"}', 'linkman-list', 0,
        '2020-06-08 11:59:00', '2020-06-08 11:59:01');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500016, '所属订单', 50000, 4, NULL, 'orderFormName', 4, '2020-06-23 09:40:01', '2020-06-23 09:40:03');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500020, '付款方式', 50000, 4, NULL, 'paymentType', 4, '2020-06-23 09:41:37', '2020-06-23 09:41:39');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500018, '备注', 50000, 4, NULL, 'remarks', 4, '2020-06-23 09:40:50', '2020-06-23 09:40:52');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500017, '下单备注', 50000, 4, NULL, 'receiveRemark', 4, '2020-06-23 09:40:25', '2020-06-23 09:40:27');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500007, '订单编号', 50000, 4, NULL, 'orderNumber', 4, '2020-06-23 09:34:59', '2020-06-23 09:35:01');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500009, '规格1', 50000, 4, NULL, 'standard1', 4, '2020-06-23 09:35:52', '2020-06-23 09:35:59');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500010, '规格2', 50000, 4, NULL, 'standard2', 4, '2020-06-23 09:35:52', '2020-06-23 09:35:59');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500011, '规格3', 50000, 4, NULL, 'standard3', 4, '2020-06-23 09:35:52', '2020-06-23 09:35:59');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500019, '订单状态', 50000, 4, NULL, 'orderStatus', 4, '2020-06-23 09:41:14', '2020-06-23 09:41:16');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (40003, '落地页列表', 400, 3,
        '{"{\"url\":\"/landing-pages/collect/**\", \"methods\":[\"GET\"]}","{\"url\":\"/landing-page-groups/collect/**\", \"methods\":[\"GET\"]}"}',
        'landing-page-list', 0, '2020-06-08 11:59:00', '2020-06-08 11:59:01');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000401, '彻底删除', 40004, 5,
        '{"{\"url\":\"/landing-pages/*\", \"methods\":[\"DELETE\"]}","{\"url\":\"/landing-pages/action/clean-trash/by-advertiser-account?**\", \"methods\":[\"DELETE\"]}"}',
        'landing-page-trash-clear', 0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000501, '删除', 40005, 5, '{"{\"url\":\"/widget-templates/*\", \"methods\":[\"DELETE\"]}"}', 'form-delete', 0,
        '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000302, '删除落地页', 40003, 5, '{"{\"url\":\"/landing-pages/*/action/trash\", \"methods\":[\"PATCH\"]}"}',
        'landing-page-delete', 0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000301, '新建/编辑分组', 40003, 5,
        '{"{\"url\":\"/landing-page-groups\", \"methods\":[\"POST\"]}","{\"url\":\"/landing-page-groups/*\", \"methods\":[\"PATCH\"]}"}',
        'landing-page-group-add-and-edit', 0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000304, '复制', 40003, 5, '{"{\"url\":\"/landing-pages/*/action/copy\", \"methods\":[\"POST\"]}"}',
        'landing-page-copy', 0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000305, '分享', 40003, 5, '{"{\"url\":\"/landing-pages/*/action/share\", \"methods\":[\"POST\"]}"}',
        'landing-page-share', 0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000303, '删除分组', 40003, 5, '{"{\"url\":\"/landing-page-groups/*\", \"methods\":[\"DELETE\"]}"}',
        'landing-page-group-delete', 0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000400, '恢复', 40004, 5, '{"{\"url\":\"/landing-pages/*/action/recover\", \"methods\":[\"PATCH\"]}"}',
        'landing-page-trash-recover', 0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000306, '移动至', 40003, 5,
        '{"{\"url\":\"/landing-pages/action/move-to/batch-group?*\", \"methods\":[\"POST\"]}"}',
        'landing-page-edit-group', 0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000601, '删除', 40006, 5, '{"{\"url\":\"/widget-templates/*\", \"methods\":[\"DELETE\"]}"}', 'order-form-delete',
        0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000700, '新建/编辑', 40007, 5, '{"{\"url\":\"/widget-templates\", \"methods\":[\"POST\",\"PATCH\"]}"}',
        'comment-form-add-and-edit', 0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5500008, '支付商户号', 50000, 4, NULL, 'mchId', 4, '2020-06-23 09:35:26', '2020-06-23 09:35:28');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (40006, '订单模板', 400, 3, '{"{\"url\":\"/widget-templates/collect/filtering\", \"methods\":[\"GET\"]}"}',
        'order-form-list', 0, '2020-06-08 11:59:00', '2020-06-08 11:59:01');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000701, '删除', 40007, 5, '{"{\"url\":\"/widget-templates\", \"methods\":[\"DELETE\"]}"}', 'comment-form-delete',
        0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000300, '新建/编辑落地页', 40003, 5,
        '{"{\"url\":\"/landing-pages\", \"methods\":[\"POST\"]}","{\"url\":\"/landing-pages/*\", \"methods\":[\"PUT\"]}"}',
        'landing-page-add-and-edit', 0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000500, '新建/编辑', 40005, 5,
        '{"{\"url\":\"/widget-templates\", \"methods\":[\"POST\"]}","{\"url\":\"/widget-templates/*\", \"methods\":[\"PATCH\"]}"}',
        'form-add-and-edit', 0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (4000600, '新建/编辑', 40006, 5,
        '{"{\"url\":\"/widget-templates\", \"methods\":[\"POST\"]}","{\"url\":\"/widget-templates/*\", \"methods\":[\"PATCH\"]}"}',
        'order-form-add-and-edit', 0, '2020-05-20 13:03:45.177', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5000001, '备注', 50000, 5, '{"{\"url\":\"/customers/*/action/remark\", \"methods\":[\"PATCH\"]}"}',
 'customer-remark-edit', 0, '2020-06-29 05:40:32.138089',
        '2020-06-29 05:40:32.138089');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5000002, '查看行为轨迹', 50000, 5, '{"{\"url\":\"/trace-datas/*\", \"methods\":[\"GET\"]}"}',
 'customer-trace-get', 0, '2020-06-29 05:40:32.268697',
        '2020-06-29 05:40:32.268697');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5000003, '新建/编辑分组', 50000, 5, '{"{\"url\":\"/customer-groups\", \"methods\":[\"POST\"]}","{\"url\":\"/customer-groups\", \"methods\":[\"PATCH\"]}"}',
 'customer-group-add-and-edit', 0, '2020-06-29 05:40:32.395543',
        '2020-06-29 05:40:32.395543');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5000004, '删除分组', 50000, 5, '{"{\"url\":\"/customer-groups/*\", \"methods\":[\"DELETE\"]}"}', 'customer-group-delete', 0, '2020-06-29 05:40:32.527771',
        '2020-06-29 05:40:32.527771');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5000005, '移动至', 50000, 5, '{"{\"url\":\"/customers/action/move-to\", \"methods\":[\"PATCH\"]}"}', 'customer-group-move', 0, '2020-06-29 05:40:32.663009',
        '2020-06-29 05:40:32.663009');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (10000, '账户管理', 100, 3,
        '{"{\"url\":\"/advertiser-accounts/collect/filtering/from/management\",\"methods\":[\"GET\"]}","{\"url\":\"/advertiser-account-groups/**\",\"methods\":[\"GET\"]}","{\"url\":\"/platforms/**\",\"methods\":[\"GET\"]}","{\"url\":\"/departments\",\"methods\":[\"GET\"],\"type\":\"EXCLUDE\"}","{\"url\":\"/users/collect/**\",\"methods\":[\"GET\"],\"type\":\"EXCLUDE\"}"}',
        'advertiser-account-list', 0, '2020-05-20 13:03:44.697995', '2020-05-20 13:03:44.697995');


-- landing Page
create table landing_page
(
    id                      bigserial primary key,
    name                    varchar,
    title                   varchar,
    content                 varchar,
    bg_color                varchar,
    token                   varchar,
    wechat_date             varchar,
    wechat_link_content     varchar,
    wechat_link_address     varchar,
    advertiser_account_id   bigint,
    unread_count            integer default 0,
    created_at              timestamp not null,
    updated_at              timestamp not null,
    version                 integer default 0,
    landing_page_width      integer,
    landing_page_height     integer,
    landing_page_size       double precision,
    bs_id                   varchar,
    recovery_at             timestamp,
    recovery_id             bigint,
    landing_page_top        integer default '-1'::integer,
    landing_page_group_top  integer default '-1'::integer,
    wechat_share_desc       varchar,
    wechat_share_image_path varchar,
    support                 json,
    bg_pic                  varchar,
    limit_filling           boolean default false,
    wechat_app_id           varchar,
    show_open_id            boolean default false,
    status                  integer default 1,
    landing_page_group_id   integer default 0,
    creator_id              bigint    not null
);
create index lp_idx_aaid on landing_page (advertiser_account_id);

create sequence landing_page_top_seq;


comment on table landing_page is '落地页表';

comment on column landing_page.id is '落地页id';

comment on column landing_page.name is '落地页名称';

comment on column landing_page.title is '落地页标题';

comment on column landing_page.content is '落地页内容';

comment on column landing_page.bg_color is '背景色';

comment on column landing_page.token is '落地页唯一标识';

comment on column landing_page.wechat_date is '微信时间';

comment on column landing_page.wechat_link_content is '微信链接内容';

comment on column landing_page.wechat_link_address is '微信链接地址';

comment on column landing_page.advertiser_account_id is '投放账户id';

comment on column landing_page.unread_count is '落地页反馈未阅读的数量';

comment on column landing_page.created_at is '创建时间';

comment on column landing_page.updated_at is '更新时间';

comment on column landing_page.version is '版本';

comment on column landing_page.landing_page_width is '落地页宽度';

comment on column landing_page.landing_page_height is '落地页高度';

comment on column landing_page.landing_page_size is '落地页大小';

comment on column landing_page.bs_id is '百度数据统计id';

comment on column landing_page.recovery_at is '移入回收站时间';

comment on column landing_page.recovery_id is '移入回收站操作人id';

comment on column landing_page.landing_page_top is '全部落地页置顶';

comment on column landing_page.landing_page_group_top is '分组落地页置顶';

comment on column landing_page.wechat_share_desc is '微信分享描述';

comment on column landing_page.wechat_share_image_path is '微信分享图片地址';

comment on column landing_page.support is '客服配置';

comment on column landing_page.bg_pic is '背景图片地址';

comment on column landing_page.limit_filling is '是否开启限填一次';

comment on column landing_page.wechat_app_id is '落地页访问时需要获取openid的appid';

comment on column landing_page.show_open_id is '是否勾选获取openid';

comment on column landing_page.status is '状态（0 不可用；1 可用）';

comment on column landing_page.landing_page_group_id is '分组id';

comment on column landing_page.creator_id is '创建者id';


-- landing Page Group
create table landing_page_group
(
    id                    bigserial primary key,
    name                  varchar,
    advertiser_account_id bigint,
    created_at            timestamp,
    updated_at            timestamp
);
create index lpg_idx_aaid on landing_page_group (advertiser_account_id);

create unique index lpg_un_idx_aai_name on landing_page_group (advertiser_account_id, name);

comment on table landing_page_group is '落地页分组表';

comment on column landing_page_group.name is '分组名称';

comment on column landing_page_group.advertiser_account_id is '投放账户id';


-- 落地页组件模板表
create table landing_page_widget_template
(
    id                    bigserial primary key,
    name                  varchar,
    content               varchar,
    advertiser_account_id bigint,
    created_at            timestamp not null,
    updated_at            timestamp not null,
    status                integer default 1,
    version               integer default 0,
    wt_type               integer,
    ext                   json,
    used_columns          varchar,
    used_column_descs     varchar,
    creator_id            bigint    not null
);

create index lpft_idx_aaid on landing_page_widget_template (advertiser_account_id);

create unique index lpft_un_idx_aaid on landing_page_widget_template (advertiser_account_id, name, wt_type);

comment on table landing_page_widget_template is '落地页组件模板表';

comment on column landing_page_widget_template.name is '模板名称';

comment on column landing_page_widget_template.content is '模板内容';

comment on column landing_page_widget_template.advertiser_account_id is '投放账户id';

comment on column landing_page_widget_template.created_at is '创建时间';

comment on column landing_page_widget_template.updated_at is '更新时间';

comment on column landing_page_widget_template.status is '状态（0 不可用；1 可用）';

comment on column landing_page_widget_template.version is '模板版本';

comment on column landing_page_widget_template.wt_type is '模板类型';

comment on column landing_page_widget_template.ext is '扩展字段';

comment on column landing_page_widget_template.creator_id is '创建者id';


-- 落地页与组件模板关系表
create table landing_page_widget_template_rel
(
    id                              bigserial primary key,
    landing_page_id                 bigint,
    landing_page_widget_template_id bigint,
    uuid                            varchar,
    created_at                      timestamp,
    updated_at                      timestamp,
    ext                             json,
    action_type                     varchar,
    action_name                     varchar,
    action_target                   varchar
);
create unique index lptr_uk_truuid on landing_page_widget_template_rel (landing_page_id, landing_page_widget_template_id, uuid);

create index lptr_idx_lpid on landing_page_widget_template_rel (landing_page_id);

create index lptr_tid on landing_page_widget_template_rel (landing_page_widget_template_id);

comment on table landing_page_widget_template_rel is '落地页与组件模板关系表';

comment on column landing_page_widget_template_rel.landing_page_id is '落地页id';

comment on column landing_page_widget_template_rel.landing_page_widget_template_id is '模板id';

comment on column landing_page_widget_template_rel.uuid is '落地页与模板关系唯一id';

comment on column landing_page_widget_template_rel.ext is '扩展字段';



CREATE TABLE
    customer
(
    id                     BIGSERIAL                      NOT NULL,
    submit_data_id         BIGINT,
    landing_page_id        BIGINT,
    customer_type          INTEGER,
    landing_page_widget_id BIGINT,
    customer_group_id      BIGINT,
    optimizer_id           BIGINT,
    name                   CHARACTER VARYING,
    phone                  CHARACTER VARYING,
    company                CHARACTER VARYING,
    sex                    CHARACTER VARYING,
    email                  CHARACTER VARYING,
    qq                     CHARACTER VARYING,
    wechat                 CHARACTER VARYING,
    checkbox               CHARACTER VARYING,
    used_columns           CHARACTER VARYING,
    used_column_descs      CHARACTER VARYING,
    content                TEXT,
    ua                     CHARACTER VARYING,
    referrer               CHARACTER VARYING,
    url                    CHARACTER VARYING,
    ip                     CHARACTER VARYING,
    province               CHARACTER VARYING,
    city                   CHARACTER VARYING,
    location               CHARACTER VARYING,
    advertiser_account_id  BIGINT,
    duration               BIGINT,
    uid                    CHARACTER VARYING,
    sid                    CHARACTER VARYING,
    pid                    CHARACTER VARYING,
    os                     CHARACTER VARYING,
    browser                CHARACTER VARYING,
    os_type                CHARACTER VARYING,
    device                 CHARACTER VARYING,
    network_type           CHARACTER VARYING,
    browser_type           CHARACTER VARYING,
    remarks                CHARACTER VARYING,
    unread                 INTEGER DEFAULT 1,
    total_price            NUMERIC,
    price                  NUMERIC,
    NUMBER                 INTEGER,
    address                CHARACTER VARYING,
    receive_province       CHARACTER VARYING,
    receive_city           CHARACTER VARYING,
    order_number           CHARACTER VARYING,
    standard1              CHARACTER VARYING,
    standard2              CHARACTER VARYING,
    standard3              CHARACTER VARYING,
    receive_remark         CHARACTER VARYING,
    receive_area           CHARACTER VARYING,
    show_address           CHARACTER VARYING,
    latitude               CHARACTER VARYING,
    longitude              CHARACTER VARYING,
    stay_time              BIGINT,
    access_depth           NUMERIC,
    select_province        CHARACTER VARYING,
    select_city            CHARACTER VARYING,
    select_area            CHARACTER VARYING,
    select_options         CHARACTER VARYING,
    single_choice          CHARACTER VARYING,
    multi_select           CHARACTER VARYING,
    input_box              CHARACTER VARYING,
    single_picture         CHARACTER VARYING,
    multi_picture          CHARACTER VARYING,
    two_stage_select       CHARACTER VARYING,
    auditable              BOOLEAN DEFAULT false,
    date_box               CHARACTER VARYING,
    order_status           INTEGER DEFAULT 0,
    payment_type           INTEGER,
    merchant_name          CHARACTER VARYING,
    mch_id                 CHARACTER VARYING,
    app_id                 CHARACTER VARYING,
    max_refund             NUMERIC,
    refund_count           INTEGER DEFAULT 0,
    created_at             TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updated_at             TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    ext                    JSON,
    PRIMARY KEY (id)
);
COMMENT ON COLUMN customer.submit_data_id
    IS
        '填单id';
COMMENT ON COLUMN customer.landing_page_widget_id
    IS
        '落地页组件模板ID';
COMMENT ON COLUMN customer.customer_group_id
    IS
        '客资分组id';
COMMENT ON COLUMN customer.optimizer_id IS '优化师id';
COMMENT ON COLUMN customer.checkbox
    IS
        '复选框内容';
COMMENT ON COLUMN customer.used_columns
    IS
        '表单字段';
COMMENT ON COLUMN customer.used_column_descs
    IS
        '表单字段对应的名称';
COMMENT ON COLUMN customer.content
    IS
        '详情';
COMMENT ON COLUMN customer.ua
    IS
        'user-agent';
COMMENT ON COLUMN customer.referrer
    IS
        '填单页面的父级页面';
COMMENT ON COLUMN customer.url
    IS
        '填单页面链接';
COMMENT ON COLUMN customer.advertiser_account_id
    IS
        '投放账户ID';
COMMENT ON COLUMN customer.browser_type
    IS
        '浏览器类型';
COMMENT ON COLUMN customer.remarks
    IS
        '备注信息';
COMMENT ON COLUMN customer.unread
    IS
        '0: 已读　１：未读';
COMMENT ON COLUMN customer.total_price
    IS
        '总价';
COMMENT ON COLUMN customer.price
    IS
        '单价';
COMMENT ON COLUMN customer.number
    IS
        '数量';
COMMENT ON COLUMN customer.address
    IS
        '详细地区';
COMMENT ON COLUMN customer.receive_province
    IS
        '省';
COMMENT ON COLUMN customer.receive_city
    IS
        '市';
COMMENT ON COLUMN customer.order_number
    IS
        '订单编号';
COMMENT ON COLUMN customer.standard1
    IS
        '规格1';
COMMENT ON COLUMN customer.standard2
    IS
        '规格2';
COMMENT ON COLUMN customer.standard3
    IS
        '规格3';
COMMENT ON COLUMN customer.receive_remark
    IS
        '收货备注';
COMMENT ON COLUMN customer.receive_area
    IS
        '区';
COMMENT ON COLUMN customer.latitude
    IS
        '维度';
COMMENT ON COLUMN customer.longitude
    IS
        '经度';
COMMENT ON COLUMN customer.stay_time
    IS
        '停留时长';
COMMENT ON COLUMN customer.access_depth
    IS
        '访问深度';
COMMENT ON COLUMN customer.select_province
    IS
        '地区下拉框中选择的省';
COMMENT ON COLUMN customer.select_city
    IS
        '地区下拉框中选择的市';
COMMENT ON COLUMN customer.select_area
    IS
        '地区下拉框中选择的区';
COMMENT ON COLUMN customer.select_options
    IS
        '选择下拉字段';
COMMENT ON COLUMN customer.single_choice
    IS
        '单选按钮';
COMMENT ON COLUMN customer.multi_select
    IS
        '多选按钮';
COMMENT ON COLUMN customer.input_box
    IS
        '输入框';
COMMENT ON COLUMN customer.single_picture
    IS
        '图片单选';
COMMENT ON COLUMN customer.multi_picture
    IS
        '图片多选';
COMMENT ON COLUMN customer.two_stage_select
    IS
        '二级下拉框';
COMMENT ON COLUMN customer.auditable
    IS
        '是否行为轨迹';
COMMENT ON COLUMN customer.date_box
    IS
        '日期';
COMMENT ON COLUMN customer.order_status
    IS
        '订单状态: 0空;1货到付款;2未支付;3已支付;4已退款;5部分退款';
COMMENT ON COLUMN customer.merchant_name
    IS
        '支付时的商户名称';
COMMENT ON COLUMN customer.mch_id
    IS
        '该订单使用的商户号id';
COMMENT ON COLUMN customer.app_id
    IS
        '该订单使用的公众号id';
COMMENT ON COLUMN customer.max_refund
    IS
        '最大的退款金额';
COMMENT ON COLUMN customer.refund_count
    IS
        '退款次数';
COMMENT ON COLUMN customer.ext
    IS
        '扩展字段';


create index customer_landing_page_widget_id on customer (landing_page_widget_id);
create index customer_lpid on customer (landing_page_id);
create index customer_submit_data_id on customer (submit_data_id);
create index index_customer_advertiser_account_id on customer (advertiser_account_id);
create index index_customer_created_at on customer (created_at);


CREATE TABLE
    customer_group
(
    id                    SERIAL                         NOT NULL,
    advertiser_account_id BIGINT,
    group_name            CHARACTER VARYING,
    created_at            TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updated_at            TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    PRIMARY KEY (id)
);
COMMENT ON TABLE customer_group
    IS
        '客资分组表';
COMMENT ON COLUMN customer_group.advertiser_account_id
    IS
        '投放账户ID';
COMMENT ON COLUMN customer_group.group_name
    IS
        '分组名称';


create index customer_group_advertiser_account_id on customer_group (advertiser_account_id);
create unique index customer_group_advertiser_account_id_group_name_uindex on customer_group (advertiser_account_id, group_name);


CREATE TABLE
    customer_wechat_refund_record
(
    id                  BIGSERIAL                      NOT NULL,
    customer_id         BIGINT,
    refund_order_number CHARACTER VARYING(32),
    refund_money        NUMERIC,
    description         CHARACTER VARYING,
    refund_status       INTEGER,
    reject_message      JSON,
    created_at          TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updated_at          TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    PRIMARY KEY (id)
);
COMMENT ON COLUMN customer_wechat_refund_record.customer_id
    IS
        '客资id';
COMMENT ON COLUMN customer_wechat_refund_record.refund_order_number
    IS
        '退款订单编号';
COMMENT ON COLUMN customer_wechat_refund_record.refund_money
    IS
        '退款金额';
COMMENT ON COLUMN customer_wechat_refund_record.description
    IS
        '退款原因';
COMMENT ON COLUMN customer_wechat_refund_record.refund_status
    IS
        '退款状态(0:失败，1:成功)';
COMMENT ON COLUMN customer_wechat_refund_record.reject_message
    IS
        '失败原因';


CREATE TABLE
    customer_upload_record
(
    id                              BIGSERIAL                      NOT NULL,
    landing_page_id                 BIGINT,
    landing_page_widget_template_id BIGINT,
    advertiser_account_id           BIGINT,
    submit_data_id                  BIGINT,
    pid                             CHARACTER VARYING,
    url                             CHARACTER VARYING,
    send_data                       CHARACTER VARYING,
    result_data                     CHARACTER VARYING,
    phone                           CHARACTER VARYING,
    description                     CHARACTER VARYING,
    created_at                      TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updated_at                      TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    PRIMARY KEY (id)
);
COMMENT ON COLUMN customer_upload_record.landing_page_id
    IS
        '落地页ID';
COMMENT ON COLUMN customer_upload_record.landing_page_widget_template_id
    IS
        '落地页模板ID';
COMMENT ON COLUMN customer_upload_record.advertiser_account_id
    IS
        '投放账号ID';
COMMENT ON COLUMN customer_upload_record.pid
    IS
        'pid';
COMMENT ON COLUMN customer_upload_record.url
    IS
        '落地页Url';
COMMENT ON COLUMN customer_upload_record.send_data
    IS
        '上报参数';
COMMENT ON COLUMN customer_upload_record.result_data
    IS
        '上报返回结果';
COMMENT ON COLUMN customer_upload_record.phone
    IS
        '手机号';
COMMENT ON COLUMN customer_upload_record.description
    IS
        '上报描述';


CREATE TABLE
    trace_page_dom
(
    id                    BIGSERIAL                      NOT NULL,
    pid                   CHARACTER VARYING,
    uid                   CHARACTER VARYING,
    sid                   CHARACTER VARYING,
    ip                    CHARACTER VARYING,
    landing_page_id       INTEGER,
    token                 CHARACTER VARYING,
    advertiser_account_id BIGINT,
    uuid                  CHARACTER VARYING,
    dom                   TEXT,
    origin_dom            TEXT,
    total_height          BIGINT,
    t                     BIGINT,
    url                   CHARACTER VARYING,
    screen                CHARACTER VARYING,
    navigator             CHARACTER VARYING,
    created_at            TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updated_at            TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    customer_id           BIGINT,
    PRIMARY KEY (id)
);
COMMENT ON TABLE trace_page_dom
    IS
        '落地页dom结构表';
COMMENT ON COLUMN trace_page_dom.pid
    IS
        '曝光唯一标识';
COMMENT ON COLUMN trace_page_dom.uid
    IS
        '用户唯一标识';
COMMENT ON COLUMN trace_page_dom.sid
    IS
        '会话唯一标识';
COMMENT ON COLUMN trace_page_dom.landing_page_id
    IS
        '落地页id';
COMMENT ON COLUMN trace_page_dom.token
    IS
        '落地页唯一标识';
COMMENT ON COLUMN trace_page_dom.uuid
    IS
        '该次会话的引用的文件的路径标识 唯一性';
COMMENT ON COLUMN trace_page_dom.origin_dom
    IS
        '页面dom节点原始数据';
COMMENT ON COLUMN trace_page_dom.total_height
    IS
        '页面最大高度';
COMMENT ON COLUMN trace_page_dom.url
    IS
        '页面url';
COMMENT ON COLUMN trace_page_dom.screen
    IS
        '屏幕分辨率';
COMMENT ON COLUMN trace_page_dom.navigator
    IS
        '浏览器信息';


create index trace_page_dom_pid on trace_page_dom (pid);
create index idx_trace_page_dom_time on trace_page_dom (date_trunc('day', created_at));

CREATE TABLE trace_page_action
(
    id                    BIGSERIAL primary key,
    pid                   VARCHAR,
    uid                   VARCHAR,
    sid                   VARCHAR,
    ip                    VARCHAR,
    landing_page_id       INTEGER,
    token                 VARCHAR,
    advertiser_account_id BIGINT,
    uuid                  VARCHAR,
    actions               TEXT,
    origin_actions        TEXT,
    action_types          VARCHAR,
    url                   VARCHAR,
    created_at            timestamp not null,
    updated_at            timestamp not null
);
comment on table trace_page_action is '页面actions表';
COMMENT ON COLUMN trace_page_action.pid IS '曝光唯一标识';
COMMENT ON COLUMN trace_page_action.uid IS '用户唯一标识';
COMMENT ON COLUMN trace_page_action.sid IS '会话唯一标识';
COMMENT ON COLUMN trace_page_action.landing_page_id IS '落地页id';
COMMENT ON COLUMN trace_page_action.token IS '落地页唯一标识';
COMMENT ON COLUMN trace_page_action.advertiser_account_id IS '投放账户ID';
COMMENT ON COLUMN trace_page_action.uuid IS '该次会话的引用的文件的路径标识 唯一性';
COMMENT ON COLUMN trace_page_action.origin_actions IS '页面dom节点原始数据';
COMMENT ON COLUMN trace_page_action.action_types IS 'action中的type 以;分隔';
COMMENT ON COLUMN trace_page_action.url IS '页面url';


create index tracepageaction_pid on trace_page_action (pid);
create index idx_trace_page_action_time on trace_page_action (date_trunc('day', created_at));


--表单提交后，记录所有actions
CREATE TABLE trace_page_submit_action
(
    id                    BIGSERIAL                      NOT NULL,
    pid                   VARCHAR                        NOT NULL,
    uid                   VARCHAR,
    sid                   VARCHAR,
    ip                    VARCHAR,
    landing_page_id       INTEGER,
    token                 VARCHAR,
    advertiser_account_id BIGINT,
    uuid                  VARCHAR,
    actions               TEXT,
    origin_actions        TEXT,
    action_types          VARCHAR,
    url                   VARCHAR,
    created_at            TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updated_at            TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    submit_data_type      INTEGER,
    submit_data_id        INTEGER,
    customer_id           INTEGER
) partition by HASH ( pid );

create index idx_trace_page_submit_action_pid
    on trace_page_submit_action (pid);

CREATE TABLE trace_page_submit_action_0 PARTITION OF trace_page_submit_action FOR VALUES WITH
    (
    modulus 4,
    remainder 0
    );


CREATE TABLE trace_page_submit_action_1 PARTITION OF trace_page_submit_action FOR VALUES WITH
    (
    modulus 4,
    remainder 1
    );

CREATE TABLE trace_page_submit_action_2 PARTITION OF trace_page_submit_action FOR VALUES WITH
    (
    modulus 4,
    remainder 2
    );

CREATE TABLE trace_page_submit_action_3 PARTITION OF trace_page_submit_action FOR VALUES WITH
    (
    modulus 4,
    remainder 3
    );

COMMENT ON TABLE trace_page_submit_action is '表单提交后，保存所有actions表';
COMMENT ON COLUMN trace_page_submit_action.pid IS '曝光唯一标识';
COMMENT ON COLUMN trace_page_submit_action.uid IS '用户唯一标识';
COMMENT ON COLUMN trace_page_submit_action.sid IS '会话唯一标识';
COMMENT ON COLUMN trace_page_submit_action.landing_page_id IS '落地页id';
COMMENT ON COLUMN trace_page_submit_action.token IS '落地页唯一标识';
COMMENT ON COLUMN trace_page_submit_action.advertiser_account_id IS '投放账户id';
COMMENT ON COLUMN trace_page_submit_action.uuid IS '该次会话的引用的文件的路径标识，唯一';
COMMENT ON COLUMN trace_page_submit_action.origin_actions IS '页面dom节点原始数据';
COMMENT ON COLUMN trace_page_submit_action.action_types IS 'action中的type 以;分隔';
COMMENT ON COLUMN trace_page_submit_action.url IS '页面url';
COMMENT ON COLUMN trace_page_submit_action.submit_data_type IS '填单类型1:表单,2:订单';
COMMENT ON COLUMN trace_page_submit_action.submit_data_id IS '填单id';
COMMENT ON COLUMN trace_page_submit_action.customer_id IS '客资id';

--表单提交后，记录dom
CREATE TABLE trace_page_submit_dom
(
    id                    BIGSERIAL primary key,
    pid                   VARCHAR,
    uid                   VARCHAR,
    sid                   VARCHAR,
    ip                    VARCHAR,
    landing_page_id       INTEGER,
    token                 VARCHAR,
    advertiser_account_id BIGINT,
    uuid                  VARCHAR,
    dom                   TEXT,
    origin_dom            TEXT,
    total_height          BIGINT,
    t                     BIGINT,
    url                   VARCHAR,
    screen                VARCHAR,
    navigator             VARCHAR,
    created_at            TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    updated_at            TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL,
    submit_data_type      INTEGER,
    submit_data_id        INTEGER,
    customer_id           INTEGER,
    auditable             BOOLEAN DEFAULT false
);



COMMENT ON TABLE trace_page_submit_dom is '表单提交后，保存所有dom表';
COMMENT ON COLUMN trace_page_submit_dom.pid IS '曝光唯一标识';
COMMENT ON COLUMN trace_page_submit_dom.uid IS '用户唯一标识';
COMMENT ON COLUMN trace_page_submit_dom.sid IS '会话唯一标识';
COMMENT ON COLUMN trace_page_submit_dom.landing_page_id IS '落地页id';
COMMENT ON COLUMN trace_page_submit_dom.token IS '落地页唯一标识';
COMMENT ON COLUMN trace_page_submit_dom.uuid IS '该次会话的引用的文件的路径标识 唯一性';
COMMENT ON COLUMN trace_page_submit_dom.origin_dom IS '页面dom节点原始数据';
COMMENT ON COLUMN trace_page_submit_dom.dom IS '页面dom';
COMMENT ON COLUMN trace_page_submit_dom.total_height IS '页面最大高度';
COMMENT ON COLUMN trace_page_submit_dom.t IS '时间戳';
COMMENT ON COLUMN trace_page_submit_dom.url IS '页面url';
COMMENT ON COLUMN trace_page_submit_dom.submit_data_type IS '填单类型1:表单,2:订单';
COMMENT ON COLUMN trace_page_submit_dom.submit_data_id IS '填单id';
COMMENT ON COLUMN trace_page_submit_dom.customer_id IS '客资id';

create index tracepagesubmitdom_pid on trace_page_submit_dom (pid);



-- auto-generated definition
create table submit_data
(
    id                              bigserial not null
            primary key,
    landing_page_id                 bigint,
    landing_page_widget_template_id bigint,
    submit_type                     integer,
    used_columns                    varchar,
    used_column_descs               varchar,
    content                         text,
    ua                              varchar,
    referrer                        varchar,
    url                             varchar,
    ip                              varchar,
    ip_number                       bigint,
    province                        varchar,
    city                            varchar,
    location                        varchar,
    advertiser_account_id           bigint,
    created_at                      timestamp not null,
    updated_at                      timestamp not null,
    duration                        integer default 0,
    uid                             varchar,
    sid                             varchar,
    pid                             varchar,
    os                              varchar,
    browser                         varchar,
    browser_type                    varchar,
    os_type                         varchar,
    device                          varchar,
    network_type                    varchar,
    medium                          varchar,
    fill_content                    varchar,
    uuid                            varchar,
    latitude                        varchar,
    longitude                       varchar,
    form_content                    varchar,
    order_ext                       jsonb,
    form_ext                        jsonb,
    other_ext                       jsonb,
    order_status                    integer,
    optimizer_id                    bigint
);

comment on column submit_data.submit_type is '0:表单，1：订单';

comment on column submit_data.used_columns is '表单字段';

comment on column submit_data.used_column_descs is '表单字段对应的名称';

comment on column submit_data.content is '详情';

comment on column submit_data.ua is 'user-agent';

comment on column submit_data.referrer is '填单页面的父级页面';

comment on column submit_data.url is '填单页面链接';

comment on column submit_data.ip_number is 'ip转化成bigint类型';

comment on column submit_data.advertiser_account_id is '客户公司id';

comment on column submit_data.duration is '填单耗时';

comment on column submit_data.latitude is '维度';

comment on column submit_data.longitude is '经度';

comment on column submit_data.order_ext is 'price:价格，originPrice：总价，number：数量，memo：备注，specification：规格，paymentType：付款方式，merchantName：支付商户号名称，merchantId：支付商户号id，officialId，支付公众号id';

comment on column submit_data.other_ext is '扩展字段';

comment on column submit_data.optimizer_id is '优化师id';




create index sd_lpid
    on submit_data (landing_page_id);

create index sd_template_id
    on submit_data (landing_page_widget_template_id);

create index sd_pid
    on submit_data (pid);

create index sd_submit_type
    on submit_data (submit_type);

create index sd_adcid
    on submit_data (advertiser_account_id);

create index idx_submit_data_on_ext_page_view
    on submit_data ((other_ext -> 'pageViewExt'::text));


alter table payment_wechat_merchant_account rename column status to validate;

alter table message_wechat_user add column unbound_at timestamp;
comment on column message_wechat_user.unbound_at is '解绑时间';
CREATE TABLE "marketing_advertiser_account_target_daily_report" (
  "id"  bigserial primary key,
  "platform_id" int4 NOT NULL,
  "advertiser_account_id" int8 NOT NULL,
  "target" json NOT NULL,
  "day_at" timestamp NOT NULL,
  "created_at" timestamp NOT NULL,
  "is_finish" bool NOT NULL,
  "optimizer_id" int8 NOT NULL
)
;

COMMENT ON COLUMN "marketing_advertiser_account_target_daily_report"."id" IS '主键';

COMMENT ON COLUMN "marketing_advertiser_account_target_daily_report"."platform_id" IS '平台id';

COMMENT ON COLUMN "marketing_advertiser_account_target_daily_report"."advertiser_account_id" IS '投放账户id';

COMMENT ON COLUMN "marketing_advertiser_account_target_daily_report"."target" IS '目标达成等相关信息';

COMMENT ON COLUMN "marketing_advertiser_account_target_daily_report"."day_at" IS '某一天  2020-06-25 00:00:00';

COMMENT ON COLUMN "marketing_advertiser_account_target_daily_report"."created_at" IS '创建时间';

COMMENT ON COLUMN "marketing_advertiser_account_target_daily_report"."is_finish" IS '是否达标';

COMMENT ON COLUMN "marketing_advertiser_account_target_daily_report"."optimizer_id" IS '主优化师';

-- 创建投放账户目标日表唯一索引
CREATE UNIQUE INDEX IDX_MARKETING_ADVERTISER_ACCOUNT_TARGET_DAILY_REPORT ON marketing_advertiser_account_target_daily_report (advertiser_account_id, platform_id, day_at);

-- 投放账户日报表
CREATE TABLE "marketing_advertiser_account_daily_report"
(
    "id"                    bigserial primary key,
    "platform_id"           int4      not null,
    "advertiser_account_id" int8      NOT NULL,
    "account_id"            int8,
    "optimizer_id"          int8,
    "status"                int4,
    "target"                json,
    "cost"                  bigint,
    "is_finish"             bool,
    "day_at"                timestamp NOT NULL,
    "created_at"            timestamp NOT NULL
)
;
COMMENT ON COLUMN "marketing_advertiser_account_daily_report"."id" IS '主键';

COMMENT ON COLUMN "marketing_advertiser_account_daily_report"."platform_id" IS '平台ID';

COMMENT ON COLUMN "marketing_advertiser_account_daily_report"."advertiser_account_id" IS '投放账户ID';

COMMENT ON COLUMN "marketing_advertiser_account_daily_report"."optimizer_id" IS '主优化师ID';

COMMENT ON COLUMN "marketing_advertiser_account_daily_report"."day_at" IS '日时间';

COMMENT ON COLUMN "marketing_advertiser_account_daily_report"."created_at" IS '创建时间';

COMMENT ON COLUMN "marketing_advertiser_account_daily_report"."status" IS '投放账户和状态';

COMMENT ON COLUMN "marketing_advertiser_account_daily_report"."is_finish" IS '是否达标';

COMMENT ON COLUMN "marketing_advertiser_account_daily_report"."target" IS '目标';

COMMENT ON COLUMN "marketing_advertiser_account_daily_report"."cost" IS '花费';

CREATE UNIQUE INDEX IDX_MARKETING_ADVERTISER_ACCOUNT_DAILY_REPORT ON marketing_advertiser_account_daily_report (advertiser_account_id, platform_id, day_at);


create table marketing_data_adgroup
(
    id              bigserial not null
        constraint marketing_data_adgroup_pkey
            primary key,
    adgroup_id      bigint,
    account_id      bigint,
    platform_id     bigint,
    campaign_id     bigint,
    name varchar,
    bid bigint,
    billing_event varchar,
    ad_status varchar,
    learning_status varchar,
    targeting_id bigint,
    targeting json,
    time_series varchar,
    begin_at timestamp(6),
    end_at timestamp(6),
    created_at timestamp(6),
    updated_at timestamp(6),
    status varchar,
    is_deleted integer,
    ext json,
    optimization_goal varchar,
    advertiser_account_id bigint,
    bidding_event varchar(255),
    optimizer_id bigint,
    advertiser_account_status int4
);

comment on column marketing_data_adgroup.id is 'ID';

comment on column marketing_data_adgroup.adgroup_id is '广告组ID';

comment on column marketing_data_adgroup.account_id is '投放账号ID';

comment on column marketing_data_adgroup.platform_id is '平台ID';

comment on column marketing_data_adgroup.name is '广告组名称';

comment on column marketing_data_adgroup.bid is '出价';

comment on column marketing_data_adgroup.billing_event is '出价方式';

comment on column marketing_data_adgroup.ad_status is '广告状态';

comment on column marketing_data_adgroup.learning_status is '学习期';

comment on column marketing_data_adgroup.targeting_id is '定向包id';

comment on column marketing_data_adgroup.targeting is '定向内容-没有使用定向包的广告使用（与targeting_id 二选一使用）';

comment on column marketing_data_adgroup.time_series is '投放时间';

comment on column marketing_data_adgroup.begin_at is '投放开始时间';

comment on column marketing_data_adgroup.end_at is '投放结束时间';

comment on column marketing_data_adgroup.created_at is '创建时间';

comment on column marketing_data_adgroup.optimization_goal is '优化目标';

comment on column marketing_data_adgroup.bidding_event is '智能出价方式';

comment on column marketing_data_adgroup.advertiser_account_status is '投放账户状态';


create unique index idx_marketing_data_adgroup
    on marketing_data_adgroup (campaign_id, adgroup_id, account_id, platform_id);

create table marketing_data_campaign
(
    id bigserial not null
        constraint marketing_data_campaign_pkey
            primary key,
    campaign_id bigint,
    campaign_name varchar,
    account_id bigint,
    platform_id bigint,
    budget_mode varchar,
    daily_budget bigint,
    promotion_goal varchar(64),
    created_at timestamp(6),
    updated_at timestamp(0),
    status varchar,
    ext json,
    is_deleted integer,
    advertiser_account_id bigint,
    optimizer_id bigint,
    advertiser_account_status int4
);

comment on table marketing_data_campaign is '广告计划表';

comment on column marketing_data_campaign.campaign_id is '计划名称';

comment on column marketing_data_campaign.account_id is '广告主id';

comment on column marketing_data_campaign.platform_id is '平台id';

comment on column marketing_data_campaign.budget_mode is '预算类型';

comment on column marketing_data_campaign.daily_budget is '日预算';

comment on column marketing_data_campaign.promotion_goal is '推广目标';

comment on column marketing_data_campaign.created_at is '创建时间';

comment on column marketing_data_campaign.updated_at is '更新时间';

comment on column marketing_data_campaign.status is '广告状态';

comment on column marketing_data_campaign.ext is 'json数据';

comment on column marketing_data_campaign.is_deleted is '是否删除';

comment on column marketing_data_campaign.advertiser_account_status is '投放账户状态';


create index marketing_data_campaign_account_id_idx
    on marketing_data_campaign (account_id);

create index marketing_data_campaign_campaign_id_idx
    on marketing_data_campaign (campaign_id);

create index marketing_data_campaign_created_at_idx
    on marketing_data_campaign (created_at);

create unique index idx_marketing_data_campaign
    on marketing_data_campaign (campaign_id, account_id, platform_id);

create unique index idx_private_marketing_data_campaign
    on marketing_data_campaign (campaign_id, account_id, platform_id);

create table marketing_data_creative
(
    id bigserial not null
        constraint marketing_data_creative_pkey
            primary key,
    creative_id bigint,
    account_id bigint,
    platform_id bigint,
    campaign_id bigint,
    adgroup_id bigint,
    creative_name varchar,
    creative_type varchar,
    created_at timestamp(6),
    updated_at timestamp(6),
    ext json,
    landing_page_id bigint,
    is_deleted integer,
    advertiser_account_id bigint,
    creative_copy varchar(255),
    optimizer_id bigint,
    advertiser_account_status int4
);

comment on table marketing_data_creative is '广告创意表';

comment on column marketing_data_creative.creative_id is '创意id';

comment on column marketing_data_creative.account_id is '账户id';

comment on column marketing_data_creative.platform_id is '平台id';

comment on column marketing_data_creative.campaign_id is '计划id';

comment on column marketing_data_creative.adgroup_id is '广告组id';

comment on column marketing_data_creative.creative_name is '创意名称';

comment on column marketing_data_creative.creative_type is '创意类型';

comment on column marketing_data_creative.created_at is '创建时间';

comment on column marketing_data_creative.updated_at is '更新时间';

comment on column marketing_data_creative.landing_page_id is '落地页ID';

comment on column marketing_data_creative.is_deleted is '是否删除';

comment on column marketing_data_creative.creative_copy is '创意文案';

comment on column marketing_data_creative.advertiser_account_status is '投放账户状态';


create index marketing_data_creative_account_id_idx
    on marketing_data_creative (account_id);

create index marketing_data_creative_adgroup_id_idx
    on marketing_data_creative (adgroup_id);

create index marketing_data_creative_campaign_id_idx
    on marketing_data_creative (campaign_id);

create index marketing_data_creative_creative_id_idx
    on marketing_data_creative (creative_id);

create unique index idx_marketing_data_creative
    on marketing_data_creative (creative_id, account_id, platform_id, campaign_id);

create table marketing_data_material
(
    id bigserial not null
        constraint marketing_data_material_pkey
            primary key,
    platform_id bigint,
    clarity_score integer,
    image_md5 varchar,
    image_id varchar,
    image_url varchar,
    aesthetic_score integer,
    created_at timestamp(6),
    updated_at timestamp(6),
    ext json,
    optimizer_id bigint,
    hue integer,
    advertiser_account_status int4
);

comment on table marketing_data_material is '素材表';

comment on column marketing_data_material.platform_id is '平台id';

comment on column marketing_data_material.clarity_score is '清晰度';

comment on column marketing_data_material.image_md5 is '图片MD5';

comment on column marketing_data_material.image_id is '图片id';

comment on column marketing_data_material.image_url is '图片url';

comment on column marketing_data_material.aesthetic_score is '美观度';

comment on column marketing_data_material.advertiser_account_status is '投放账户状态';

comment on column marketing_data_material.hue is '图片主色
    RED(0),
    YELLOW(1),
    GREEN(2),
    CYAN(3),
    BLUE(4),
    PURPLE(5),
    BLACK(6),
    WHITE(7),
    GREY(8),
    ORANGE(9),
    PINK(10),
    BROWN(11);';


create index marketing_data_material_image_id_idx
    on marketing_data_material (image_id);

create unique index idx_marketing_data_material
    on marketing_data_material (image_md5);

create table marketing_data_material_creative_rel
(
    id bigserial not null
        constraint marketing_data_material_creative_rel_pkey
            primary key,
    image_id varchar,
    creative_id bigint,
    platform_id bigint
);

comment on column marketing_data_material_creative_rel.id is 'ID';

comment on column marketing_data_material_creative_rel.image_id is '素材ID';

comment on column marketing_data_material_creative_rel.creative_id is '创意ID';

comment on column marketing_data_material_creative_rel.platform_id is '平台ID';


create unique index marketing_data_material_creat_image_id_creative_id_platform_idx
    on marketing_data_material_creative_rel (image_id, creative_id, platform_id);

create unique index idx_marketing_data_creative_material
    on marketing_data_material_creative_rel (image_id, creative_id, platform_id);

create table marketing_data_participle_creative_rel
(
    id bigserial not null
        constraint marketing_data_participle_creative_rel_pkey
            primary key,
    creative_id bigint,
    image_id varchar(64),
    participle_id bigint,
    type integer,
    platform_id bigint,
    fraction integer,
    ext json
);

comment on column marketing_data_participle_creative_rel.id is 'ID';

comment on column marketing_data_participle_creative_rel.creative_id is '创意ID';

comment on column marketing_data_participle_creative_rel.image_id is '素材ID';

comment on column marketing_data_participle_creative_rel.participle_id is '分词ID';

comment on column marketing_data_participle_creative_rel.type is '关系类型 图片ocr分词 创意文案分词 创意标签类型
0 ocr 1 分案  2 兴趣标签  3图片多标签 ';

comment on column marketing_data_participle_creative_rel.platform_id is '平台ID';

comment on column marketing_data_participle_creative_rel.fraction is '打分（0~100） 多标签 type = 3时有效 ';

comment on column marketing_data_participle_creative_rel.ext is 'type = 3 时有效';


create unique index idx_marketing_data_creative_participle_rel
    on marketing_data_participle_creative_rel (creative_id, image_id, participle_id, type, platform_id);

create table marketing_data_participle
(
    id bigserial not null
        constraint marketing_data_participle_pkey
            primary key,
    name varchar not null
);

comment on column marketing_data_participle.id is 'ID';

comment on column marketing_data_participle.name is '具体的词语';


create unique index idx_participle_name
    on marketing_data_participle (name);

create unique index idx_marketing_data_participle
    on marketing_data_participle (name);

create table marketing_data_report
(
    id bigserial not null
        constraint marketing_data_report_pkey
            primary key,
    account_id bigint,
    campaign_id bigint,
    adgroup_id bigint,
    creative_id bigint,
    platform_id bigint,
    targeting_id bigint,
    view_num bigint,
    convert_num           bigint,
    convert_cost          bigint,
    cost                  bigint,
    click_num             bigint,
    deep_convert_num      bigint,
    deep_convert_cost     bigint,
    play_num              bigint,
    valid_play_num        bigint,
    valid_play_cost       bigint,
    average_play_time     bigint,
    video_play25_num      bigint,
    video_play50_num      bigint,
    video_play75_num      bigint,
    video_play100_num     bigint,
    download_num          bigint,
    day_time              timestamp(6),
    data_time             timestamp(6),
    ext                   json,
    follow_num            bigint,
    hour                  integer,
    week                  integer,
    advertiser_account_id bigint,
    ad_id                 bigint,
    optimizer_id          bigint,
    wifi_play_num         bigint,
    advertiser_account_status int4
);

comment on column marketing_data_report.follow_num is '关注量';

comment on column marketing_data_report.wifi_play_num is 'wifi播放数';


create unique index marketing_data_report_account_id_campaign_id_adgroup_id_pla_idx
    on marketing_data_report (account_id, campaign_id, adgroup_id, data_time, platform_id, ad_id);

create table marketing_data_audience
(
    id bigserial not null
        constraint marketing_data_audience_pkey
            primary key,
    account_id bigint,
    campaign_id bigint,
    adgroup_id bigint,
    type varchar,
    cost_num bigint,
    show_num bigint,
    click_num bigint,
    convert_num bigint,
    created_at timestamp(6),
    ext json,
    type_name varchar(255),
    platform_id integer,
    advertiser_account_id bigint,
    optimizer_id bigint,
    download_finish bigint,
    advertiser_account_status int4
);

comment on table marketing_data_audience is '受众数据表';

comment on column marketing_data_audience.account_id is '账户id';

comment on column marketing_data_audience.campaign_id is '计划id';

comment on column marketing_data_audience.adgroup_id is '广告id';

comment on column marketing_data_audience.type is '类型';

comment on column marketing_data_audience.cost_num is '花费-分';

comment on column marketing_data_audience.show_num is '展示数';

comment on column marketing_data_audience.click_num is '点击数';

comment on column marketing_data_audience.convert_num is '转化数';

comment on column marketing_data_audience.created_at is '时间';

comment on column marketing_data_audience.platform_id is '平台id';

comment on column marketing_data_audience.advertiser_account_id is '账户id-yiye';

comment on column marketing_data_audience.advertiser_account_id is '投放账户状态';


create index marketing_data_audience_account_id_idx
    on marketing_data_audience (account_id);

create index marketing_data_audience_time_at_idx
    on marketing_data_audience (created_at);

create unique index idx_marketing_data_audience_dex_ex
    on marketing_data_audience (account_id, adgroup_id, type, type_name, created_at);

create table marketing_data_advertise
(
    id bigserial not null
        constraint marketing_data_advertise_pkey
            primary key,
    campaign_id bigint,
    adgroup_id bigint,
    account_id bigint,
    platform_id integer,
    ad_id bigint,
    ad_name varchar,
    adcreative_id bigint,
    adcreative json,
    reject_message varchar,
    created_at timestamp(6),
    updated_at timestamp(0),
    status varchar,
    is_deleted integer,
    ext json,
    optimizer_id bigint,
    advertiser_account_status int4
);

comment on table marketing_data_advertise is '广告表';

comment on column marketing_data_advertise.campaign_id is '广告计划id';

comment on column marketing_data_advertise.adgroup_id is '广告组id';

comment on column marketing_data_advertise.account_id is '广告主id';

comment on column marketing_data_advertise.platform_id is '投放平台id';

comment on column marketing_data_advertise.ad_id is '广告id';

comment on column marketing_data_advertise.ad_name is '广告名称';

comment on column marketing_data_advertise.adcreative_id is '广告创意ID';

comment on column marketing_data_advertise.adcreative is '广告创意';

comment on column marketing_data_advertise.created_at is '创建时间';

comment on column marketing_data_advertise.updated_at is '更新时间';

comment on column marketing_data_advertise.status is '广告状态';

comment on column marketing_data_advertise.is_deleted is '是否删除';

comment on column marketing_data_advertise.ext is 'json数据';

comment on column marketing_data_advertise.advertiser_account_status is '投放账户状态';


create index marketing_data_advertise_account_id_idx
    on marketing_data_advertise (account_id);

create index marketing_data_advertise_ad_id_idx
    on marketing_data_advertise (ad_id);

create index marketing_data_advertise_created_at_idx
    on marketing_data_advertise (created_at);

create unique index idx_marketing_data_advertise
    on marketing_data_advertise (platform_id, account_id, campaign_id, adgroup_id, adcreative_id, ad_id);

create table marketing_data_targeting
(
    id bigserial not null
        constraint marketing_data_targeting_pkey
            primary key,
    targeting_id bigint,
    targeting_name varchar,
    account_id bigint,
    platform_id integer,
    campaign_id bigint,
    adgroup_id bigint,
    created_at timestamp(6),
    updated_at timestamp(0),
    is_deleted integer,
    ext json,
    advertiser_account_id bigint,
    optimizer_id bigint,
    advertiser_account_status int4
);

comment on column marketing_data_targeting.targeting_id is '定向包ID';

comment on column marketing_data_targeting.targeting_name is '定向包名称';

comment on column marketing_data_targeting.account_id is '投放账户ID';

comment on column marketing_data_targeting.platform_id is '平台ID';

comment on column marketing_data_targeting.campaign_id is '广告计划id';

comment on column marketing_data_targeting.adgroup_id is '广告组id';

comment on column marketing_data_targeting.created_at is '创建时间';

comment on column marketing_data_targeting.updated_at is '修改时间';

comment on column marketing_data_targeting.is_deleted is '是否删除';

comment on column marketing_data_targeting.ext is '附加数据';

comment on column marketing_data_targeting.advertiser_account_status is '投放账户状态';


create index marketing_data_targeting_account_id_idx
    on marketing_data_targeting (account_id);

create index marketing_data_advertise_targeting_id_idx
    on marketing_data_targeting (targeting_id);

create index marketing_data_targeting_created_at_idx
    on marketing_data_targeting (created_at);

create unique index idx_marketing_data_targeting
    on marketing_data_targeting (targeting_id, account_id, platform_id);

create table page_view_info
(
    id bigserial not null
        constraint page_view_info_pkey
            primary key,
    advertiser_account_id bigint not null,
    platform_id integer not null,
    account_id bigint not null,
    optimizer_id bigint,
    landing_page_id bigint not null,
    widget_template_id bigint,
    widget_template_type integer,
    ip varchar,
    province varchar,
    city varchar,
    operator varchar,
    ua varchar,
    uid varchar,
    sid varchar,
    pid varchar,
    referrer varchar,
    origin varchar,
    url varchar,
    brand varchar,
    price_range varchar,
    device varchar,
    device_price integer,
    os varchar,
    os_type varchar,
    browser varchar,
    browser_type varchar,
    network_type varchar,
    screen_size varchar,
    screen_dpi varchar,
    length_of_stay double precision,
    access_depth double precision,
    view_content varchar,
    longitude varchar,
    latitude varchar,
    submit_data_id bigint,
    wechat_openid varchar,
    fill integer,
    fill_content varchar,
    fill_at timestamp(6),
    created_at timestamp(6) not null,
    updated_at timestamp(6),
    click_id varchar,
    ext json
);


create unique index idx_unq_page_view_info_unique_index_pid
    on page_view_info (pid);

create index idx_page_view_info_account_id_landing_page_id
    on page_view_info (account_id, landing_page_id);

create table marketing_data_operation
(
    id bigserial not null
        constraint marketing_data_operation_pkey
            primary key,
    account_id bigint,
    content_title varchar(255),
    object_type varchar(255),
    object_id bigint,
    object_name varchar(255),
    created_at timestamp(6),
    content_log varchar(255),
    operator varchar(255),
    opt_ip varchar(255),
    platform_id integer,
    optimizer_id bigint,
    advertiser_account_status int4
);


create index marketing_data_operation_account_id_idx
    on marketing_data_operation (account_id);

create unique index marketing_data_operation_account_id_object_type_object_id_c_idx
    on marketing_data_operation (account_id, object_type, object_id, created_at);

create index marketing_data_operation_create_at_idx
    on marketing_data_operation (created_at);

alter table customer
    add column length_of_stay double precision;
comment on column customer.length_of_stay is 'pv停留时长';


alter table submit_data
    add column length_of_stay double precision;
comment on column submit_data.length_of_stay is 'pv停留时长';

---- 数据分析权限
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (8000000, '投放时段分布', 80000, 4, null, 'marketing-data-report-widget-00', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (8000001, '数据概况', 80000, 4, null, 'marketing-data-report-widget-01', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (8000002, '操作影响力', 80000, 4, null, 'marketing-data-report-widget-02', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (8000003, '广告数溢出占比（环图）', 80000, 4, null, 'marketing-data-report-widget-03', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (8000004, '广告成本溢出占比（环图）', 80000, 4, null, 'marketing-data-report-widget-04', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (8000005, '成本溢出分析（曲线折线图）', 80000, 4, null, 'marketing-data-report-widget-05', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (8000006, '指标对比分析', 80000, 4, null, 'marketing-data-report-widget-06', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (8000007, '多轴联动指标趋势分析', 80000, 4, null, 'marketing-data-report-widget-07', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000008, '上小时同比涨幅', 80000, 4, null, 'marketing-data-report-widget-08', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000009, '创意筛选器', 80000, 4, null, 'marketing-data-report-widget-09', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000010, '广告信息看板', 80000, 4, null, 'marketing-data-report-widget-10', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000011, '新建广告数趋势', 80000, 4, null, 'marketing-data-report-widget-11', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000012, '定向包数据分析（环图）', 80000, 4, null, 'marketing-data-report-widget-12', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000013, '定向包数据分析（柱状图）', 80000, 4, null, 'marketing-data-report-widget-13', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000014, '图片多标签', 80000, 4, null, 'marketing-data-report-widget-14', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000015, '图片OCR标签', 80000, 4, null, 'marketing-data-report-widget-15', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000016, '文案关键词', 80000, 4, null, 'marketing-data-report-widget-16', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000017, '创意兴趣标签', 80000, 4, null, 'marketing-data-report-widget-17', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000018, '创意图片评价-清晰度', 80000, 4, null, 'marketing-data-report-widget-18', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000019, '创意图片评价-美观度', 80000, 4, null, 'marketing-data-report-widget-19', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000020, '创意图片色彩分析', 80000, 4, null, 'marketing-data-report-widget-20', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000021, '创意类型占比', 80000, 4, null, 'marketing-data-report-widget-21', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000022, '视频分析', 80000, 4, null, 'marketing-data-report-widget-22', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000023, '视频播放进度分析', 80000, 4, null, 'marketing-data-report-widget-23', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000024, '视频播放网络环境', 80000, 4, null, 'marketing-data-report-widget-24', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000025, '转化漏斗', 80000, 4, null, 'marketing-data-report-widget-25', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000026, '访问深度', 80000, 4, null, 'marketing-data-report-widget-26', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000027, '停留时长', 80000, 4, null, 'marketing-data-report-widget-27', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000028, '落地页预览', 80000, 4, null, 'marketing-data-report-widget-28', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000029, '广告效果分析', 80000, 4, null, 'marketing-data-report-widget-29', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000030, '广告燃尽分析', 80000, 4, null, 'marketing-data-report-widget-30', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000031, '地域分布（媒）', 80000, 4, null, 'marketing-data-report-widget-31', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000032, '性别分布（媒）', 80000, 4, null, 'marketing-data-report-widget-32', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000033, '平台分布（媒）', 80000, 4, null, 'marketing-data-report-widget-33', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000034, '网络分布（媒）', 80000, 4, null, 'marketing-data-report-widget-34', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000035, '年龄分布（媒）', 80000, 4, null, 'marketing-data-report-widget-35', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000036, '兴趣分类分布（媒）', 80000, 4, null, 'marketing-data-report-widget-36', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000037, '平台分布（叶）', 80000, 4, null, 'marketing-data-report-widget-37', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000038, '运营商占比（叶）', 80000, 4, null, 'marketing-data-report-widget-38', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000039, '设备价格（叶）', 80000, 4, null, 'marketing-data-report-widget-39', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000040, '网络类型（叶）', 80000, 4, null, 'marketing-data-report-widget-40', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000041, '访客数与重复访客数占比（叶）', 80000, 4, null, 'marketing-data-report-widget-41', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000042, '停留时长占比（叶）', 80000, 4, null, 'marketing-data-report-widget-42', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000043, '访问来源分布（叶）', 80000, 4, null, 'marketing-data-report-widget-43', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000044, '浏览器分布TOP10（叶）', 80000, 4, null, 'marketing-data-report-widget-44', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000045, '地域分布（叶）', 80000, 4, null, 'marketing-data-report-widget-45', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000046, '屏幕尺寸（叶）', 80000, 4, null, 'marketing-data-report-widget-46', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000047, '屏幕分辨率（叶）', 80000, 4, null, 'marketing-data-report-widget-47', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (8000048, '平均停留时长趋势（叶）', 80000, 4, null, 'marketing-data-report-widget-48', 5, now(), now());
-- 操作权限
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (8000049, '下载访客细查', 80000, 5, '{"{\"url\":\"/pageviews/export\",\"methods\":[\"GET\"]}"}', 'coustomSrutiny', 0, now(), now());
-- 人效分析权限
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (1300000, '花费对比图', 13003, 4, null, 'user-efficiency-report-widget-00', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (1300001, '人均花费波动图', 13003, 4, null, 'user-efficiency-report-widget-01', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (1300002, '总花费变化趋势图', 13003, 4, null, 'user-efficiency-report-widget-02', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (1300003, '新增广告数对比图', 13003, 4, null, 'user-efficiency-report-widget-03', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (1300004, '新增广告数变化趋势图', 13003, 4, null, 'user-efficiency-report-widget-04', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at) VALUES (1300005, '操作频次对比图', 13003, 4, null, 'user-efficiency-report-widget-05', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1300006, '操作频次变化趋势图', 13003, 4, null, 'user-efficiency-report-widget-06', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1300007, '账户达标情况', 13003, 4, null, 'user-efficiency-report-widget-07', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1300008, '达标不达标对比饼图', 13003, 4, null, 'user-efficiency-report-widget-08', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1300009, '花费构成', 13003, 4, null, 'user-efficiency-report-widget-09', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1300010, 'KPI雷达图', 13003, 4, null, 'user-efficiency-report-widget-10', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1300011, 'KP雷达对比图', 13003, 4, null, 'user-efficiency-report-widget-11', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1300012, '字段统计', 13003, 4, null, 'user-efficiency-report-widget-12', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1300013, '活跃账户/账户变化趋势图', 13003, 4, null, 'user-efficiency-report-widget-13', 5, now(), now());
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1300014, '目标达成趋势图', 13003, 4, null, 'user-efficiency-report-widget-14', 5, now(), now());

alter table submit_data
    add column access_depth double precision;
alter table customer
    add column length_of_stay double precision;
alter table submit_data
    add column length_of_stay double precision;
alter table trace_page_action
    add column depth numeric;

ALTER TABLE PAGE_VIEW_INFO ADD COLUMN ADVERTISER_ACCOUNT_STATUS INT4;
COMMENT ON COLUMN PAGE_VIEW_INFO.ADVERTISER_ACCOUNT_STATUS IS '投放账号状态 （0有效、1待审核、2审核不通过、3封停）';

-- 修改素材数据表
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN SIGNATURE VARCHAR(255);
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN URL VARCHAR(255);
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN PREVIEW_URL VARCHAR(255);
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN WIDTH INT4;
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN HEIGHT INT4;
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN SIZE INT8;
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN VIDEO_FRAMES INT4;
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN VIDEO_FPS NUMERIC;
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN video_Bit_Rate INT4;
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN AUDIO_BIT_RATE INT4;
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN VIDEO_CODEC VARCHAR(255);
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN AUDIO_CODEC VARCHAR(255);
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN IMAGE_DURATION_MILLISECOND INT4;
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN AUDIO_DURATION_MILLISECOND INT4;
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN FORMAT VARCHAR(255);
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN MATERIAL_TYPE INT4;
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN MATERIAL_ID VARCHAR(255);
ALTER TABLE MARKETING_DATA_MATERIAL ADD COLUMN ACCOUNT_ID INT8;

-- 修改创意与素材的关联表
ALTER TABLE MARKETING_DATA_MATERIAL_CREATIVE_REL ADD COLUMN MATERIAL_ID VARCHAR(255);

-- 重建索引素材库相关索引
DROP INDEX IDX_MARKETING_DATA_MATERIAL ;

CREATE UNIQUE INDEX IDX_MARKETING_DATA_MATERIAL
    ON MARKETING_DATA_MATERIAL (SIGNATURE, ACCOUNT_ID, PLATFORM_ID);

DROP INDEX IDX_MARKETING_DATA_CREATIVE_MATERIAL;

CREATE UNIQUE INDEX IDX_MARKETING_DATA_CREATIVE_MATERIAL
    ON MARKETING_DATA_MATERIAL_CREATIVE_REL (MATERIAL_ID, CREATIVE_ID, PLATFORM_ID);

-- 修改素材表字段过长的问题
ALTER TABLE MARKETING_DATA_MATERIAL ALTER COLUMN  URL TYPE VARCHAR;
ALTER TABLE MARKETING_DATA_MATERIAL ALTER COLUMN  PREVIEW_URL TYPE VARCHAR;
ALTER TABLE MARKETING_DATA_MATERIAL ALTER COLUMN  IMAGE_URL TYPE VARCHAR;
CREATE TABLE report_component (
 id bigserial NOT NULL,
 layout text NULL,
 "type" int4 NULL,
 account_id varchar(100) NULL,
 platform_id varchar(100) NULL,
 user_id int8 null
);

CREATE OR REPLACE VIEW ucenter_department_view
AS WITH RECURSIVE le(id, name, parent_id, ancestors, depath) AS (
         SELECT ud.id,
            ud.name,
            ud.parent_id,
            '-1'::text AS ancestors,
            1 AS depath
           FROM ucenter_department ud
          WHERE ud.parent_id = '-1'::integer
        UNION ALL
         SELECT e2.id,
            e2.name,
            e2.parent_id,
            concat_ws(','::text, e3.ancestors, e3.id) AS concat_ws,
            e3.depath + 1
           FROM ucenter_department e2,
            le e3
          WHERE e3.id = e2.parent_id
        )
 SELECT le.id,
    le.name,
    le.parent_id,
    le.ancestors,
    le.depath
   FROM le
  ORDER BY (rpad(le.id::character varying::text, 5, '0'::text));

INSERT INTO report_component ( layout, "type", account_id, platform_id, user_id) VALUES('[{"id":8000010,"title":"广告信息看板","dataurl":"/agent/report/countAdByStatus","x":0,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"e5411e21-875d-4acc-82ef-ab25a706d890","type":"txt2","value":"投放中","selects":[{"key":"status","value":"审核不通过","options":["投放中","计划暂停","新建审核中","修改审核中","已完成","计划新建","审核不通过","账户余额不足","超出预算","未到达投放时间","不在投放时段","已被广告组暂停","广告组超出预算","已删除","不在学习期中","学习中","学习成功","学习失败"]}],"moved":false},{"id":8000010,"title":"广告信息看板","dataurl":"/agent/report/countAdByStatus","x":2,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"26858fbc-1c0e-48cb-9682-678f44e72e7c","type":"txt2","value":"学习中","selects":[{"key":"status","value":"审核不通过","options":["投放中","计划暂停","新建审核中","修改审核中","已完成","计划新建","审核不通过","账户余额不足","超出预算","未到达投放时间","不在投放时段","已被广告组暂停","广告组超出预算","已删除","不在学习期中","学习中","学习成功","学习失败"]}],"moved":false},{"id":8000010,"title":"广告信息看板","dataurl":"/agent/report/countAdByStatus","x":4,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"9ad3ce37-e481-486b-90bd-186188c1b52d","type":"txt2","value":"审核不通过","selects":[{"key":"status","value":"审核不通过","options":["投放中","计划暂停","新建审核中","修改审核中","已完成","计划新建","审核不通过","账户余额不足","超出预算","未到达投放时间","不在投放时段","已被广告组暂停","广告组超出预算","已删除","不在学习期中","学习中","学习成功","学习失败"]}],"moved":false},{"id":8000010,"title":"广告信息看板","dataurl":"/agent/report/countAdByStatus","x":6,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"378672e3-8422-4e75-b9fa-8b96bfe9a7e9","type":"txt2","value":"投放中","selects":[{"key":"status","value":"审核不通过","options":["投放中","计划暂停","新建审核中","修改审核中","已完成","计划新建","审核不通过","账户余额不足","超出预算","未到达投放时间","不在投放时段","已被广告组暂停","广告组超出预算","已删除","不在学习期中","学习中","学习成功","学习失败"]}],"moved":false},{"id":8000010,"title":"广告信息看板","dataurl":"/agent/report/countAdByStatus","x":8,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"58e3f5b6-7a2b-4030-a454-9593f06d00a0","type":"txt2","value":"学习中","selects":[{"key":"status","value":"审核不通过","options":["投放中","计划暂停","新建审核中","修改审核中","已完成","计划新建","审核不通过","账户余额不足","超出预算","未到达投放时间","不在投放时段","已被广告组暂停","广告组超出预算","已删除","不在学习期中","学习中","学习成功","学习失败"]}],"moved":false},{"id":8000010,"title":"广告信息看板","dataurl":"/agent/report/countAdByStatus","x":10,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"ec0a9266-be2a-40d1-ac9b-1c3ee96d6672","type":"txt2","value":"审核不通过","selects":[{"key":"status","value":"审核不通过","options":["投放中","计划暂停","新建审核中","修改审核中","已完成","计划新建","审核不通过","账户余额不足","超出预算","未到达投放时间","不在投放时段","已被广告组暂停","广告组超出预算","已删除","不在学习期中","学习中","学习成功","学习失败"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":0,"y":2,"w":2,"h":2,"minW":2,"minH":2,"i":"73c92331-f5f3-48e9-97ff-993f9da3fa8c","type":"txt","value":"花费","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":2,"y":2,"w":2,"h":2,"minW":2,"minH":2,"i":"a62f94d3-537b-4004-8c7d-a71ad09ac033","type":"txt","value":"曝光量","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":4,"y":2,"w":2,"h":2,"minW":2,"minH":2,"i":"81df62be-0e5d-4e71-991a-0c078d8c0feb","type":"txt","value":"点击量","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":6,"y":2,"w":2,"h":2,"minW":2,"minH":2,"i":"6d07cdbb-8382-4ad4-8bfc-3c6aac02736c","type":"txt","value":"点击率","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":8,"y":2,"w":2,"h":2,"minW":2,"minH":2,"i":"404b89f0-25ff-4c1e-a35c-3cabecc85f6b","type":"txt","value":"目标转化率","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":10,"y":2,"w":2,"h":2,"minW":2,"minH":2,"i":"ccc02e51-77fb-4766-a5cf-5dda1fcb8966","type":"txt","value":"目标转化成本","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000003,"title":"广告数量环图","unit":"%","dataurl":"/agent/report/adNumOverflow","x":0,"y":4,"w":3,"h":5,"minW":3,"minH":5,"i":"044f66d7-393d-4b28-b4f5-eece7dc11d31","type":"pie2","drillDown":true,"moved":false},{"id":8000004,"title":"广告溢出率环图","unit":"%","dataurl":"/agent/report/adRateOverflow","x":3,"y":4,"w":3,"h":5,"minW":3,"minH":5,"i":"531850d7-96ee-46be-a72b-ca3e9b9741d5","type":"pie2","drillDown":true,"moved":false},{"id":8000011,"title":"新建广告数趋势","dataurl":"/agent/report/countNewAds","x":9,"y":4,"w":3,"h":5,"minW":3,"minH":6,"i":"563ab520-0d3d-4f1a-8463-61340c8fd8e5","unit":"个","type":"dline","selects":[{"key":"calDateType","value":"分日","options":["分日","分时"]}],"moved":false},{"id":8000005,"title":"成本溢出分析","unit":"元","dataurl":"/agent/report/adBiddingAndCostTrends","x":6,"y":4,"w":3,"h":5,"minW":3,"minH":5,"i":"b0b21bd7-f698-43b7-b31a-c2b35480a705","type":"line","selects":[{"key":"calDateType","value":"分日","options":["分日","分时"]}],"moved":false},{"page":"投放分析","id":8000006,"title":"指标对比分析","dataurl":"/agent/report/doubleMetricData","x":0,"y":9,"w":9,"h":7,"minW":6,"minH":6,"i":"11e9932b-8229-4cb8-a9a6-e768e34f8dd4","type":"dline","selects":[{"color":"green","key":"metric1","value":"曝光量","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]},{"color":"yellow","key":"metric2","value":"点击量","options":["点击量","曝光量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]},{"key":"calDateType","value":"分日","options":["分日","分时"]}],"moved":false},{"page":"投放分析","id":8000008,"title":"上小时同比涨幅","dataurl":"/agent/report/upAndDownOnHour","x":9,"y":9,"w":3,"h":7,"minW":3,"minH":6,"i":"82f04298-3878-45f9-9df7-0047d9317584","type":"bar","selects":[{"key":"metric","value":"曝光量","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]}],"moved":false},{"id":8000030,"title":"广告燃尽分析","dataurl":"/agent/report/adBurnout","x":6,"y":16,"w":6,"h":12,"minW":6,"minH":12,"i":"7d72e6c3-5626-41ee-a2ac-e98597b4c681","type":"dscatter","unit":"个","drillDown":true,"selects":[{"key":"metric","value":"曝光量","options":["曝光量","点击量","花费"]}],"moved":false},{"id":8000012,"title":"定向包数据分析","dataurl":"/agent/report/adTargetPacketPie","x":0,"y":28,"w":6,"h":6,"minW":6,"minH":6,"i":"7ef4412e-ae27-4c39-b85b-51df5fd6d122","unit":"个","type":"pie","selects":[{"key":"metric","value":"曝光量","options":["曝光量","点击量","目标转化量","花费"]}],"moved":false},{"id":8000013,"title":"定向包数据分析","dataurl":"/agent/report/adTargetPacketBar","x":6,"y":28,"w":6,"h":6,"minW":6,"minH":6,"i":"69058918-343a-40a4-b15d-b2099234fbc5","unit":"%","type":"bar2","selects":[{"key":"metric","value":"点击率","options":["点击率","转化率","转化成本"]}],"moved":false},{"id":8000029,"title":"广告效果分析","dataurl":"/agent/report/adEffect","x":0,"y":34,"w":8,"h":9,"minW":6,"minH":6,"i":"532cd7dd-2c29-48b7-a071-19a57d7ec334","type":"scatter","drillDown":true,"moved":false},{"page":"投放分析","id":8000000,"title":"投放时段分布","type":"heatmap","dataurl":"/agent/report/distributionOfLaunchPeriod","x":8,"y":34,"w":4,"h":9,"minW":4,"minH":8,"i":"a8b4a9bc-6ed5-4af6-bc03-adf0d319b2c3","selects":[{"key":"time","value":"过去7天","options":["请选择","过去7天","过去30天","本周","上周","本月"]},{"key":"metric","value":"曝光量","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]}],"moved":false},{"id":8000009,"title":"创意筛选器","dataurl":"/agent/report/adConsume","x":0,"y":16,"w":6,"h":12,"minW":6,"minH":12,"unit":"个","i":"525e378a-edbf-4480-99b7-88bcce74cafe","type":"rank","selects":[{"key":"metric","value":"曝光量","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]}],"moved":false}]', 1, '1', '1', 1);
INSERT INTO report_component (layout, "type", account_id, platform_id, user_id) VALUES('[{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":0,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"32c9a389-f724-4d0d-82a9-37628d3c87eb","type":"txt","value":"花费","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":2,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"61dacb32-ebf1-494b-ba09-e93f3248ee48","type":"txt","value":"曝光量","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":4,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"c679c10e-1eca-4dd8-b49f-9c1c44e80a16","type":"txt","value":"点击量","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":8,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"49f8f81e-66bf-4aa5-9a30-f0b6e8ff57e5","type":"txt","value":"目标转化率","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":6,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"ca85b29c-489b-4869-9ea2-c699f49678bc","type":"txt","value":"点击率","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":10,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"bcf936c3-0351-499d-bb2d-05f43e1eb1c8","type":"txt","value":"目标转化成本","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000004,"title":"广告溢出率环图","unit":"%","dataurl":"/agent/report/adRateOverflow","x":0,"y":2,"w":3,"h":6,"minW":3,"minH":5,"i":"5617d768-bee7-4d1b-b0c2-a915e5d0a335","type":"pie2","drillDown":true,"moved":false},{"id":8000004,"title":"广告溢出率环图","unit":"%","dataurl":"/agent/report/adRateOverflow","x":3,"y":2,"w":3,"h":6,"minW":3,"minH":5,"i":"d520cce4-f92a-44e8-941c-f95ce7552fd1","type":"pie2","drillDown":true,"moved":false},{"id":8000005,"title":"成本溢出分析","unit":"元","dataurl":"/agent/report/adBiddingAndCostTrends","x":6,"y":2,"w":3,"h":6,"minW":3,"minH":5,"i":"d7ab48f0-d381-4ef5-bb38-65c7da3fe5ce","type":"line","selects":[{"key":"calDateType","value":"分日","options":["分日","分时"]}],"moved":false},{"id":8000025,"title":"转化漏斗","unit":"个","dataurl":"/agent/report/generalView","x":9,"y":2,"w":3,"h":6,"minW":3,"minH":6,"i":"85178402-d914-493a-9232-73911c90a212","type":"funnel","moved":false},{"page":"投放分析","id":8000006,"title":"指标对比分析","dataurl":"/agent/report/doubleMetricData","x":0,"y":8,"w":9,"h":6,"minW":6,"minH":6,"i":"0c628240-e940-45be-8ca4-9dd52280b835","type":"dline","selects":[{"color":"green","key":"metric1","value":"曝光量","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]},{"color":"yellow","key":"metric2","value":"点击量","options":["点击量","曝光量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]},{"key":"calDateType","value":"分日","options":["分日","分时"]}],"moved":false},{"page":"投放分析","id":8000008,"title":"上小时同比涨幅","dataurl":"/agent/report/upAndDownOnHour","x":9,"y":8,"w":3,"h":6,"minW":3,"minH":6,"i":"07e70506-1f5d-4f74-a996-4f613054cbef","type":"bar","selects":[{"key":"metric","value":"曝光量","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]}],"moved":false},{"id":8000012,"title":"定向包数据分析","dataurl":"/agent/report/adTargetPacketPie","x":0,"y":14,"w":6,"h":6,"minW":6,"minH":6,"i":"c724c686-e132-4838-835a-39d5be63f4fb","unit":"个","type":"pie","selects":[{"key":"metric","value":"曝光量","options":["曝光量","点击量","目标转化量","花费"]}],"moved":false},{"id":8000013,"title":"定向包数据分析","dataurl":"/agent/report/adTargetPacketBar","x":6,"y":14,"w":6,"h":6,"minW":6,"minH":6,"i":"60f11a8e-a052-420a-be3c-088190659c9a","unit":"%","type":"bar2","selects":[{"key":"metric","value":"点击率","options":["点击率","转化率","转化成本"]}],"moved":false},{"id":8000030,"title":"广告燃尽分析","dataurl":"/agent/report/adBurnout","x":0,"y":20,"w":6,"h":12,"minW":6,"minH":12,"i":"e3b48cd3-e56d-40dd-b00f-ad86d570df37","type":"dscatter","unit":"个","drillDown":true,"selects":[{"key":"metric","value":"曝光量","options":["曝光量","点击量","花费"]}],"moved":false},{"page":"投放分析","id":8000000,"title":"投放时段分布","type":"heatmap","dataurl":"/agent/report/distributionOfLaunchPeriod","x":6,"y":20,"w":6,"h":12,"minW":4,"minH":8,"i":"791bcb34-3b4b-4fa7-b48f-f5b8b5b0d044","selects":[{"key":"time","value":"过去7天","options":["请选择","过去7天","过去30天","本周","上周","本月"]},{"key":"metric","value":"曝光量","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]}],"moved":false}]', 2, '1', '1', 1);
INSERT INTO report_component (layout, "type", account_id, platform_id, user_id) VALUES('[{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":0,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"f94c5771-173a-4b04-9b00-e48f41643c0e","type":"txt","value":"花费","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":2,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"fe53903e-3ab4-42ff-8741-15b05fdac7dd","type":"txt","value":"曝光量","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":4,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"c2918398-184a-4f17-948e-7eec49463194","type":"txt","value":"点击量","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":6,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"803e4a93-18e3-4f70-bc66-1d813f6484b1","type":"txt","value":"点击率","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":8,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"97161d9f-3c51-4a40-b27d-1678d32c7caa","type":"txt","value":"目标转化率","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000001,"title":"数据概况","dataurl":["/agent/report/generalSingleView","/agent/report/generalViewRollRate"],"x":10,"y":0,"w":2,"h":2,"minW":2,"minH":2,"i":"701a299c-8839-41d6-ae66-73b89179375d","type":"txt","value":"目标转化成本","selects":[{"key":"type","value":"目标转化成本","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展现均价","落地页浏览量","落地页访客数","落地页填单数","落地页未填单数","重复访客数","重复访客率","平均停留时长","平均访问深度"]}],"moved":false},{"id":8000019,"title":"创意图片评价-美观度","dataurl":"/agent/report/creativeImgBeautyScore","x":4,"y":2,"w":4,"h":6,"minW":4,"minH":6,"i":"c8fba3c2-a768-4b1e-a746-6c6f222aa312","type":"pie2","moved":false},{"id":8000018,"title":"创意图片评价-清晰度","dataurl":"/agent/report/creativeImgHDScore","x":8,"y":2,"w":4,"h":6,"minW":4,"minH":6,"i":"845c00da-791a-490a-9ca3-8f841e3b46b5","type":"pie2","moved":false},{"id":8000021,"title":"创意类型占比","dataurl":"/agent/report/creativeType","x":0,"y":2,"w":4,"h":6,"minW":4,"minH":6,"i":"db182e82-e532-477d-a27d-8fff830cc7a1","type":"pie","moved":false},{"id":8000022,"title":"视频分析","dataurl":"/agent/report/creativeMetric","x":0,"y":8,"w":2,"h":3,"minW":2,"minH":2,"i":"42693c2c-ee4b-4ee9-b2e5-530c1692c277","type":"txt2","value":"视频播放数","selects":[{"key":"metric","value":"视频播放数","options":["视频播放数","视频有效播放数","视频有效播放成本","视频有效播放率","平均单次播放时间"]}],"moved":false},{"id":8000022,"title":"视频分析","dataurl":"/agent/report/creativeMetric","x":2,"y":8,"w":2,"h":3,"minW":2,"minH":2,"i":"e42ac95a-93f4-496a-97d7-41abae6831b2","type":"txt2","value":"视频有效播放数","selects":[{"key":"metric","value":"视频有效播放数","options":["视频播放数","视频有效播放数","视频有效播放成本","视频有效播放率","平均单次播放时间"]}],"moved":false},{"id":8000022,"title":"视频分析","dataurl":"/agent/report/creativeMetric","x":4,"y":8,"w":2,"h":3,"minW":2,"minH":2,"i":"6009c60a-86b3-46e1-9140-98035d4fa10b","type":"txt2","value":"视频有效播放率","selects":[{"key":"metric","value":"视频有效播放率","options":["视频播放数","视频有效播放数","视频有效播放成本","视频有效播放率","平均单次播放时间"]}],"moved":false},{"id":8000022,"title":"视频分析","dataurl":"/agent/report/creativeMetric","x":0,"y":11,"w":2,"h":3,"minW":2,"minH":2,"i":"70257b00-62bf-4fde-b229-c10a9d5e7dae","type":"txt2","value":"视频有效播放成本","selects":[{"key":"metric","value":"视频有效播放成本","options":["视频播放数","视频有效播放数","视频有效播放成本","视频有效播放率","平均单次播放时间"]}],"moved":false},{"id":8000022,"title":"视频分析","dataurl":"/agent/report/creativeMetric","x":2,"y":11,"w":4,"h":3,"minW":2,"minH":2,"i":"5198330e-bfd9-4ec6-a89b-7fcac7440363","type":"txt2","value":"平均单次播放时间","selects":[{"key":"metric","value":"平均单次播放时间","options":["视频播放数","视频有效播放数","视频有效播放成本","视频有效播放率","平均单次播放时间"]}],"moved":false},{"id":8000023,"title":"视频播放进度分析","dataurl":"/agent/report/creativeVideoPercent","x":6,"y":8,"w":3,"h":6,"minW":3,"minH":6,"i":"cce851dc-a474-4196-98cd-a2bb6c8a7e88","type":"pie2","moved":false},{"id":8000024,"title":"视频播放网络环境","dataurl":"/agent/report/creativeVideoNetwork","x":9,"y":8,"w":3,"h":6,"minW":3,"minH":6,"unit":"次","i":"33a27e8e-4257-4406-8237-f81042bdd848","type":"pie2","moved":false},{"id":8000009,"title":"创意筛选器","dataurl":"/agent/report/adConsume","x":0,"y":14,"w":6,"h":12,"minW":6,"minH":12,"unit":"个","i":"169f6b38-c60f-474e-9f3e-e181e62e84f2","type":"rank","selects":[{"key":"metric","value":"曝光量","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]}],"moved":false},{"page":"创意分析","id":8000014,"title":"图片多标签","dataurl":"/agent/report/creativeImgMultiTags","x":0,"y":26,"w":6,"h":6,"minW":6,"minH":6,"i":"de202b4d-8217-49d3-98b2-74610da43834","unit":"个","type":"bar","selects":[{"key":"metric","value":"曝光量","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]}],"moved":false},{"page":"创意分析","id":8000015,"title":"图片OCR标签","dataurl":"/agent/report/creativeOcrTags","x":6,"y":26,"w":6,"h":6,"minW":6,"minH":6,"i":"1930456a-0aa1-49af-bdc0-0d6e5b943215","type":"bar","unit":"个","selects":[{"key":"metric","value":"曝光量","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]}],"moved":false},{"page":"创意分析","id":8000016,"title":"文案关键词","dataurl":"/agent/report/creativeCopyTags","type":"bar","x":6,"y":14,"w":6,"h":6,"minW":6,"minH":6,"unit":"个","i":"58452419-03ed-4002-a584-ebd0eb47900c","selects":[{"key":"metric","value":"曝光量","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]}],"moved":false},{"page":"创意分析","id":8000017,"title":"创意兴趣标签","dataurl":"/agent/report/creativeInterestTags","x":6,"y":20,"w":6,"h":6,"minW":6,"minH":6,"i":"4e8540f8-0a70-465d-b954-087f8568502a","type":"bar","unit":"个","selects":[{"key":"metric","value":"曝光量","options":["曝光量","点击量","点击率","目标转化量","目标转化率","目标转化成本","花费","点击均价","千次展示均价"]}],"moved":false}]', 4, '1', '1', 1);
INSERT INTO report_component (layout, "type", account_id, platform_id, user_id) VALUES('[{"id":8000031,"title":"地域分布（媒）","unit":"元","dataurl":"/agent/report/adPersonasArea","x":0,"y":0,"w":4,"h":12,"minW":4,"minH":12,"i":"e761f1f3-51fe-46cd-96dd-0cca9e260125","type":"mapbar","selects":[{"key":"type","value":"province","options":[{"value":"province","label":"省份"},{"value":"city","label":"城市"}]},{"key":"metric","value":"花费","options":["花费","曝光量","点击量","点击率","APP下载完成量","APP下载率","目标转化量","目标转化率","目标转化成本"]}],"moved":false},{"id":8000035,"title":"年龄分布（媒）","unit":"元","dataurl":"/agent/report/adPersonasAge","x":8,"y":0,"w":4,"h":6,"minW":4,"minH":6,"i":"806c050a-4f2a-4582-bf65-6cfbd7296438","type":"pie","desc":"指标选择**率时，样式图为柱状图，其余为环图","switch":"率","selects":[{"key":"metric","value":"花费","options":["花费","曝光量","点击量","点击率","APP下载完成量","APP下载率","目标转化量","目标转化率","目标转化成本"]}],"moved":false},{"id":8000032,"title":"性别分布（媒）","unit":"元","dataurl":"/agent/report/adPersonasGender","x":8,"y":6,"w":4,"h":6,"minW":4,"minH":6,"i":"b232bbfc-aefd-4e28-ab9a-7c8be4b040b8","type":"pie","desc":"指标选择**率时，样式图为柱状图，其余为环图","switch":"率","selects":[{"key":"metric","value":"花费","options":["花费","曝光量","点击量","点击率","APP下载完成量","APP下载率","目标转化量","目标转化率","目标转化成本"]}],"moved":false},{"id":8000036,"title":"兴趣分类分布（媒）","unit":"元","dataurl":"/agent/report/adPersonasTag","x":0,"y":12,"w":12,"h":6,"minW":6,"minH":6,"i":"877ed37a-a0e4-4ce5-88f8-3c4bbd4f9ad2","type":"bar","selects":[{"key":"metric","value":"花费","options":["花费","曝光量","点击量","点击率","APP下载完成量","APP下载率","目标转化量","目标转化率","目标转化成本"]}],"moved":false},{"id":8000033,"title":"平台分布（媒）","unit":"元","dataurl":"/agent/report/adPersonasPlatform","x":0,"y":18,"w":3,"h":6,"minW":3,"minH":6,"i":"7323db65-59dd-494c-b1e7-56925e7cf3fc","type":"pie","desc":"（指标选择**率时，样式图为柱状图，其余为环图）","switch":"率","selects":[{"key":"metric","value":"曝光量","options":["花费","曝光量","点击量","点击率","APP下载完成量","APP下载率","目标转化量","目标转化率","目标转化成本"]}],"moved":false},{"id":8000037,"title":"平台分布（叶）","unit":"个","dataurl":"/agent/report/adPersonasPlatformYiye","x":3,"y":18,"w":3,"h":6,"minW":3,"minH":6,"i":"9dd5e6cc-01c4-49e2-87ed-0999c36f5b8f","type":"pie","desc":"指标选择**率时，样式图为柱状图，其余为环图","switch":"率","selects":[{"key":"metric","value":"浏览量","options":["浏览量","访客数","填单数","填单率"]},{"key":"type","value":"platform_id"}],"moved":false},{"id":8000034,"title":"网络分布（媒）","unit":"元","dataurl":"/agent/report/adPersonasAc","x":6,"y":18,"w":3,"h":6,"minW":3,"minH":6,"i":"47193b2c-7e80-42ce-92ef-487b2eb6b58d","type":"pie","desc":"指标选择**率时，样式图为柱状图，其余为环图","switch":"率","selects":[{"key":"metric","value":"花费","options":["花费","曝光量","点击量","点击率","APP下载完成量","APP下载率","目标转化量","目标转化率","目标转化成本"]}],"moved":false},{"id":8000040,"title":"网络类型（叶）","unit":"个","dataurl":"/agent/report/adPersonasAcYiye","x":9,"y":18,"w":3,"h":6,"minW":3,"minH":6,"i":"76bf51fa-ecb3-4538-aff1-89439ceba4c0","type":"pie","desc":"指标选择**率时，样式图为柱状图，其余为环图","switch":"率","selects":[{"key":"metric","value":"浏览量","options":["浏览量","访客数","填单数","填单率"]},{"key":"type","value":"network_type"}],"moved":false},{"id":8000038,"title":"运营商占比（叶）","unit":"个","dataurl":"/agent/report/adPersonasDistributionOfOperators","x":0,"y":24,"w":4,"h":6,"minW":4,"minH":6,"i":"1352daa4-dfd9-4c38-81b5-aa11f712e2d5","type":"pie","desc":"（指标选择**率时，样式图为柱状图，其余为环图）","switch":"率","selects":[{"key":"metric","value":"浏览量","options":["浏览量","访客数","填单数","填单率"]},{"key":"type","value":"operator"}],"moved":false},{"id":8000039,"title":"设备价格（叶）","unit":"个","dataurl":"/agent/report/adPersonasDistributionOfDevicePrice","x":8,"y":24,"w":4,"h":6,"minW":4,"minH":6,"i":"60e251e7-13f3-417e-8703-88cf7a274fb4","type":"pie","desc":"（指标选择**率时，样式图为柱状图，其余为环图）","switch":"率","selects":[{"key":"metric","value":"浏览量","options":["浏览量","访客数","填单数","填单率"]},{"key":"type","value":"device_price"}],"moved":false},{"id":8000044,"title":"浏览器分布TOP10（叶）","unit":"个","dataurl":"/agent/report/adPersonasDistributionOfBrowser","x":4,"y":24,"w":4,"h":6,"minW":4,"minH":6,"i":"d9639d3e-cbcb-4e88-9edf-f59e540833b4","type":"bar","selects":[{"key":"metric","value":"浏览量","options":["浏览量","访客数","填单数","填单率"]},{"key":"type","value":"browser"}],"moved":false},{"id":8000042,"title":"停留时长占比（叶）","dataurl":"/agent/report/adPersonasStayProportion","x":0,"y":30,"w":4,"h":6,"minW":4,"minH":6,"i":"c196d7fe-0e36-4fec-995d-893baae5ddc3","type":"pie","desc":"（指标选择**率时，样式图为柱状图，其余为环图）","switch":"率","selects":[{"key":"metric","value":"浏览量","options":["浏览量","访客数","填单数","填单率"]},{"key":"type","value":"length_of_stay"}],"moved":false},{"id":8000043,"title":"访问来源分布（叶）","unit":"个","dataurl":"/agent/report/adPersonasSourceProportion","x":4,"y":30,"w":4,"h":6,"minW":4,"minH":6,"i":"ac8487ca-9b48-4d85-9c9f-dbfde607d516","type":"pie","desc":"（指标选择**率时，样式图为柱状图，其余为环图）","switch":"率","selects":[{"key":"metric","value":"浏览量","options":["浏览量","访客数","填单数","填单率"]}],"moved":false},{"id":8000041,"title":"访客数与重复访客数占比（叶）","dataurl":"/agent/report/adPersonasRepeatProportion","x":8,"y":30,"w":4,"h":6,"minW":4,"minH":6,"i":"f290ab38-a265-4b4e-bf2d-9f05d34650f1","type":"pie","selects":[{"key":"metric","value":"访客数"},{"key":"sign","value":1}],"moved":false},{"id":8000048,"title":"平均停留时长趋势（叶）","unit":"秒","dataurl":"/agent/report/adPersonasAvgStayProportion","x":0,"y":36,"w":12,"h":8,"minW":12,"minH":8,"i":"75c0bef0-5d22-4908-bdff-c968ba8a43b8","type":"dline","moved":false},{"id":8000045,"title":"地域分布（叶）","unit":"个","dataurl":"/agent/report/adPersonasAreaYiye","x":4,"y":0,"w":4,"h":12,"minW":4,"minH":12,"i":"ffd63624-caf3-40ec-871e-1ae0fb372442","type":"mapbar","selects":[{"key":"metric","value":"浏览量","options":["浏览量","访客数","填单数","填单率"]},{"key":"type","value":"province","options":[{"value":"province","label":"省份"},{"value":"city","label":"城市"}]}],"moved":false}]', 3, '1', '1', 1);
create table marketing_audience_bundle
(
    id           bigserial primary key,
    name         varchar,
    description  varchar,
    rule         json,
    type         int,
    amount       bigint default 0,
    assets       json,
    asset_status int    default 0,
    status       int    default 1,
    creator_id   bigint,
    created_at   timestamp,
    updated_at   timestamp
);

comment on column marketing_audience_bundle.name is '名称';
comment on column marketing_audience_bundle.description is '描述';
comment on column marketing_audience_bundle.rule is '人群筛选规则';
comment on column marketing_audience_bundle.type is '格式: 0=IMEI-MD5,1=IDFA-MD5,2=OAID.';
comment on column marketing_audience_bundle.amount is '人群数量';
comment on column marketing_audience_bundle.assets is '打包文件信息';
comment on column marketing_audience_bundle.asset_status is '打包状态: 0=打包中,1=成功,2=失败.';
comment on column marketing_audience_bundle.status is '逻辑删除列: 0=删除,1=未删.';
comment on column marketing_audience_bundle.creator_id is '创建人';
comment on column marketing_audience_bundle.created_at is '创建时间';
comment on column marketing_audience_bundle.updated_at is '更新时间';
comment on table marketing_audience_bundle is '人群包表';

create unique index ux_marketing_audience_bundle_name on marketing_audience_bundle (name, creator_id) where status = 1;
create index idx_marketing_audience_bundle_type_status on marketing_audience_bundle (type, status);

create table marketing_audience_bundle_log
(
    id                          bigserial primary key,
    audience_bundle_id          bigint not null,
    platform_id                 int,
    platform_name               varchar,
    advertiser_account_id       bigint,
    external_audience_bundle_id varchar,
    result                      int,
    created_at                  timestamp,
    updated_at                  timestamp
);

comment on column marketing_audience_bundle_log.audience_bundle_id is '所属人群包id';
comment on column marketing_audience_bundle_log.platform_id is '平台id';
comment on column marketing_audience_bundle_log.platform_name is '平台名称';
comment on column marketing_audience_bundle_log.advertiser_account_id is '投放账号表id';
comment on column marketing_audience_bundle_log.external_audience_bundle_id is '平台人群包id';
comment on column marketing_audience_bundle_log.result is '上传结果: 0=上传中,1=上传成功,2=上传失败';
comment on column marketing_audience_bundle_log.created_at is '创建时间';
comment on column marketing_audience_bundle_log.updated_at is '更新时间';
comment on table marketing_audience_bundle_log is '人群包上传记录表';

create index idx_marketing_audience_bundle_log_audience_bundle_id on marketing_audience_bundle_log (audience_bundle_id);

create table marketing_ad_trace_forward
(
    id         bigserial primary key,
    account_id bigint,
    url        varchar,
    type       int,
    created_at timestamp,
    updated_at timestamp
);

comment on column marketing_ad_trace_forward.account_id is '投放账户id';
comment on column marketing_ad_trace_forward.url is '转发链接';
comment on column marketing_ad_trace_forward.type is '类型: 0=点击,1=有效播放,2=视频播完,3=曝光.';
comment on column marketing_ad_trace_forward.created_at is '创建时间';
comment on column marketing_ad_trace_forward.updated_at is '更新时间';
comment on table marketing_ad_trace_forward is '广告追踪转发表';

create index idx_marketing_ad_trace_forward_advertiser_account_id_type on marketing_ad_trace_forward (account_id, type);

update ucenter_permission
set module = 'marketing-data-asset-overview'
where id = 70003;
update ucenter_permission
set module = 'marketing-data-asset-list',mapping = '{"{\"url\":\"/audience-bundles\",\"methods\":[\"GET\"]}"}'
where id = 70004;
update ucenter_permission
set module  = 'marketing-ad-trace-forward-list',
    mapping = '{"{\"url\":\"/ad-trace-forwards/config\",\"methods\":[\"GET\"]}"}'
where id = 70005;
update ucenter_permission
set module = 'marketing-audience-bundle-add',mapping = '{"{\"url\":\"/audience-bundles\",\"methods\":[\"POST\"]}"}'
where id = 7000400;
update ucenter_permission
set module = 'marketing-audience-bundle-delete',mapping = '{"{\"url\":\"/audience-bundles/*\",\"methods\":[\"DELETE\"]}"}'
where id = 7000401;
update ucenter_permission
set module  = 'marketing-audience-bundle-upload',
    mapping = '{"{\"url\":\"/audience-bundles/*/action/upload\",\"methods\":[\"POST\"]}"}'
where id = 7000402;
update ucenter_permission
set module = 'marketing-audience-bundle-download'
where id = 7000403;
update ucenter_permission
set module  = 'marketing-ad-trace-forward-edit',
    mapping = '{"{\"url\":\"/ad-trace-forwards/collect/by-advertiser-account\",\"methods\":[\"GET\"]}","{\"url\":\"/ad-trace-forwards/by-advertiser-account/batch\",\"methods\":[\"PATCH\"]}"}'
where id = 7000500;
update ucenter_permission
set mapping =array ['{"url":"/advertiser-accounts/collect/filtering/from/management","methods":["GET"]}']
where id = 10000;

update ucenter_permission
set mapping = array [
    '{"url":"/landing-pages", "methods":["POST"]}',
    '{"url":"/landing-pages/*", "methods":["PUT"]}',
    '{"url":"/wechat-official-accounts/collect/filter?filtering*", "methods":["GET"]}'
    ]
where id = 4000300;

update ucenter_permission
set mapping = array [
    '{"url":"/wechat-official-accounts/collect/filter?page*","methods":["GET"]}'
    ]
where id = 30003;

-- 表单模板
update ucenter_permission
set mapping = array [
    '{"url":"/widget-templates/collect/filtering/FORM_TYPE", "methods":["GET"]}'
    ]
where id = 40005;

-- 表单新建/编辑
update ucenter_permission
set mapping = array [
    '{"url":"/widget-templates/FORM_TYPE", "methods":["POST"]}',
    '{"url":"/widget-templates/FORM_TYPE/*", "methods":["PUT"]}'
    ]
where id = 4000500;

-- 表单删除
update ucenter_permission
set mapping = array [
    '{"url":"/widget-templates/FORM_TYPE/*", "methods":["DELETE"]}'
    ]
where id = 4000501;

-- 订单模板
update ucenter_permission
set mapping = array [
    '{"url":"/widget-templates/collect/filtering/ORDER_TYPE", "methods":["GET"]}'
    ]
where id = 40006;

-- 订单模板新建/编辑
update ucenter_permission
set mapping = array [
    '{"url":"/widget-templates/ORDER_TYPE", "methods":["POST"]}',
    '{"url":"/widget-templates/ORDER_TYPE/*", "methods":["PUT"]}'
    ]
where id = 4000600;

-- 订单模板删除
update ucenter_permission
set mapping = array [
    '{"url":"/widget-templates/ORDER_TYPE/*", "methods":["DELETE"]}'
    ]
where id = 4000601;

-- 评论模板
update ucenter_permission
set mapping = array [
    '{"url":"/widget-templates/collect/filtering/COMMENT_TYPE", "methods":["GET"]}'
    ]
where id = 40007;

-- 评论模板新建/编辑
update ucenter_permission
set mapping = array [
    '{"url":"/widget-templates/COMMENT_TYPE", "methods":["POST"]}',
    '{"url":"/widget-templates/COMMENT_TYPE/*", "methods":["PUT"]}'
    ]
where id = 4000700;

-- 评论模板删除
update ucenter_permission
set mapping = array [
    '{"url":"/widget-templates/COMMENT_TYPE", "methods":["DELETE"]}'
    ]
where id = 4000701;

UPDATE ucenter_permission
SET "name"   = '公司',
    "module" = 'company'
WHERE "id" = 5500005;
UPDATE ucenter_permission
SET "name"   = '性别',
    "module" = 'sex'
WHERE "id" = 5500006;
UPDATE ucenter_permission
SET "name"   = '地区',
    "module" = 'selectAddress'
WHERE "id" = 5500016;
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (5000006, '退款', 50000, 5, '{"{\"url\":\"/customers/refund-records\", \"methods\":[\"POST\"]}"}',
        'customer-refund', 0, now(), now());

update ucenter_permission
set name = '点击数据转发'
where id = 7000500;


comment on column marketing_advertiser_account.balance is '投放账户余额';

-- public_default private
update marketing_platform_conf set name = '巨量引擎' where name = '今日头条';

update marketing_platform_conf set name = '微信广告' where name = '微信';


-- private public
update marketing_advertiser_account set platform_name = '微信广告' where platform_name = '微信';
delete from message_wechat_user
create table marketing_advertiser_account_industry
(
    id         bigserial,
    name       varchar not null,
    parent_id  bigint,
    level      integer,
    created_at timestamp,
    updated_at timestamp
);

comment on column marketing_advertiser_account_industry.name is '行业名称';

comment on column marketing_advertiser_account_industry.parent_id is '行业从属关系';

comment on column marketing_advertiser_account_industry.level is '行业级别';


alter table marketing_advertiser_account_group
    add column advertiser_account_industry_id bigint;

comment on column marketing_advertiser_account_group.advertiser_account_industry_id is '行业id';


INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (1, '综合电商平台', 1, 0, '2020-07-18 15:59:33.998000', '2020-07-18 15:59:33.998000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (7, '招商加盟服务', 1, 0, '2020-07-18 15:59:34.388000', '2020-07-18 15:59:34.388000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (19, '婚恋', 1, 0, '2020-07-18 15:59:34.437000', '2020-07-18 15:59:34.437000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (24, '摄影摄像机构及服务', 1, 0, '2020-07-18 15:59:34.459000', '2020-07-18 15:59:34.459000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (28, '教育', 1, 0, '2020-07-18 15:59:34.474000', '2020-07-18 15:59:34.474000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (39, '金融', 1, 0, '2020-07-18 15:59:34.531000', '2020-07-18 15:59:34.531000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (65, '旅游', 1, 0, '2020-07-18 15:59:34.653000', '2020-07-18 15:59:34.653000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (75, '护肤彩妆', 1, 0, '2020-07-18 15:59:34.796000', '2020-07-18 15:59:34.796000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (81, '食品', 1, 0, '2020-07-18 15:59:34.819000', '2020-07-18 15:59:34.819000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (3, '跨境电商', 2, 1, '2020-07-18 15:59:34.367000', '2020-07-18 16:00:02.630000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (5, '电商导购', 2, 1, '2020-07-18 15:59:34.377000', '2020-07-18 16:00:02.640000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (6, '二手电商平台', 2, 1, '2020-07-18 15:59:34.383000', '2020-07-18 16:00:02.645000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (8, '美容减肥加盟', 2, 7, '2020-07-18 15:59:34.392000', '2020-07-18 16:00:02.661000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (9, '餐饮服务加盟', 2, 7, '2020-07-18 15:59:34.396000', '2020-07-18 16:00:02.666000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (10, '教育培训加盟', 2, 7, '2020-07-18 15:59:34.400000', '2020-07-18 16:00:02.671000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (11, '成人用品加盟', 2, 7, '2020-07-18 15:59:34.405000', '2020-07-18 16:00:02.676000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (12, '医药保健加盟', 2, 7, '2020-07-18 15:59:34.409000', '2020-07-18 16:00:02.680000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (13, '汽车产品加盟', 2, 7, '2020-07-18 15:59:34.413000', '2020-07-18 16:00:02.685000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (14, '招商加盟（平台类）', 2, 7, '2020-07-18 15:59:34.416000', '2020-07-18 16:00:02.689000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (15, '生活服务加盟', 2, 7, '2020-07-18 15:59:34.420000', '2020-07-18 16:00:02.693000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (16, '养殖加盟', 2, 7, '2020-07-18 15:59:34.424000', '2020-07-18 16:00:02.698000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (17, '服饰礼品加盟', 2, 7, '2020-07-18 15:59:34.429000', '2020-07-18 16:00:02.703000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (18, '娱乐休闲加盟', 2, 7, '2020-07-18 15:59:34.433000', '2020-07-18 16:00:02.708000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (20, '婚恋平台', 2, 19, '2020-07-18 15:59:34.440000', '2020-07-18 16:00:02.721000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (21, '婚介所', 2, 19, '2020-07-18 15:59:34.444000', '2020-07-18 16:00:02.726000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (22, '婚庆服务', 2, 19, '2020-07-18 15:59:34.449000', '2020-07-18 16:00:02.730000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (23, '婚纱礼服', 2, 19, '2020-07-18 15:59:34.452000', '2020-07-18 16:00:02.734000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (25, '婚纱摄影', 2, 24, '2020-07-18 15:59:34.463000', '2020-07-18 16:00:02.787000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (26, '亲子摄影', 2, 24, '2020-07-18 15:59:34.467000', '2020-07-18 16:00:02.792000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (29, '学历教育', 2, 28, '2020-07-18 15:59:34.478000', '2020-07-18 16:00:02.810000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (30, '教育综合平台', 2, 28, '2020-07-18 15:59:34.482000', '2020-07-18 16:00:02.814000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (31, '基础教育（非学历机构）', 2, 28, '2020-07-18 15:59:34.486000', '2020-07-18 16:00:02.818000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (32, '职业教育（非学历机构）', 2, 28, '2020-07-18 15:59:34.490000', '2020-07-18 16:00:02.822000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (33, '语言培训（非学历机构）', 2, 28, '2020-07-18 15:59:34.494000', '2020-07-18 16:00:02.826000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (34, '学历培训（非学历机构）', 2, 28, '2020-07-18 15:59:34.497000', '2020-07-18 16:00:02.830000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (35, '才艺培训', 2, 28, '2020-07-18 15:59:34.501000', '2020-07-18 16:00:02.835000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (36, '教学辅助工具', 2, 28, '2020-07-18 15:59:34.505000', '2020-07-18 16:00:02.839000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (37, '留学', 2, 28, '2020-07-18 15:59:34.509000', '2020-07-18 16:00:02.844000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (38, '早期教育', 2, 28, '2020-07-18 15:59:34.528000', '2020-07-18 16:00:02.849000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (40, '基金', 2, 39, '2020-07-18 15:59:34.535000', '2020-07-18 16:00:02.863000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (41, '证券', 2, 39, '2020-07-18 15:59:34.539000', '2020-07-18 16:00:02.888000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (42, '银行服务', 2, 39, '2020-07-18 15:59:34.543000', '2020-07-18 16:00:02.907000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (43, '保险', 2, 39, '2020-07-18 15:59:34.546000', '2020-07-18 16:00:02.912000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (44, '贵金属', 2, 39, '2020-07-18 15:59:34.551000', '2020-07-18 16:00:02.916000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (45, '典当', 2, 39, '2020-07-18 15:59:34.554000', '2020-07-18 16:00:02.920000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (46, '担保', 2, 39, '2020-07-18 15:59:34.558000', '2020-07-18 16:00:02.925000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (47, 'P2P网贷平台', 2, 39, '2020-07-18 15:59:34.562000', '2020-07-18 16:00:02.929000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (48, '投资咨询', 2, 39, '2020-07-18 15:59:34.566000', '2020-07-18 16:00:02.934000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (49, '信托', 2, 39, '2020-07-18 15:59:34.570000', '2020-07-18 16:00:02.938000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (50, '资产管理', 2, 39, '2020-07-18 15:59:34.573000', '2020-07-18 16:00:03.055000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (51, '标准金融', 2, 39, '2020-07-18 15:59:34.600000', '2020-07-18 16:00:03.058000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (52, '大宗商品交易/现货', 2, 39, '2020-07-18 15:59:34.604000', '2020-07-18 16:00:03.062000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (53, '银行信用卡', 2, 39, '2020-07-18 15:59:34.608000', '2020-07-18 16:00:03.065000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (54, '第三方支付', 2, 39, '2020-07-18 15:59:34.611000', '2020-07-18 16:00:03.069000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (55, '小额贷款', 2, 39, '2020-07-18 15:59:34.615000', '2020-07-18 16:00:03.074000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (56, '消费金融', 2, 39, '2020-07-18 15:59:34.619000', '2020-07-18 16:00:03.089000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (57, '汽车金融', 2, 39, '2020-07-18 15:59:34.622000', '2020-07-18 16:00:03.092000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (58, '期货期权', 2, 39, '2020-07-18 15:59:34.626000', '2020-07-18 16:00:03.096000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (59, '外汇', 2, 39, '2020-07-18 15:59:34.629000', '2020-07-18 16:00:03.099000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (60, '征信', 2, 39, '2020-07-18 15:59:34.633000', '2020-07-18 16:00:03.158000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (62, '金融辅助工具', 2, 39, '2020-07-18 15:59:34.641000', '2020-07-18 16:00:03.166000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (63, '金融综合线上平台', 2, 39, '2020-07-18 15:59:34.645000', '2020-07-18 16:00:03.170000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (64, '众筹', 2, 39, '2020-07-18 15:59:34.648000', '2020-07-18 16:00:03.177000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (66, '酒店住宿', 2, 65, '2020-07-18 15:59:34.657000', '2020-07-18 16:00:03.188000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (67, '旅行社', 2, 65, '2020-07-18 15:59:34.664000', '2020-07-18 16:00:03.191000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (68, '景点/景区-境内', 2, 65, '2020-07-18 15:59:34.668000', '2020-07-18 16:00:03.195000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (69, '景点/景区-境外', 2, 65, '2020-07-18 15:59:34.690000', '2020-07-18 16:00:03.259000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (70, 'OTA线上平台', 2, 65, '2020-07-18 15:59:34.694000', '2020-07-18 16:00:03.263000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (71, '旅游局-境内', 2, 65, '2020-07-18 15:59:34.697000', '2020-07-18 16:00:03.267000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (72, '旅游局-境外', 2, 65, '2020-07-18 15:59:34.701000', '2020-07-18 16:00:03.272000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (73, '航空旅行服务', 2, 65, '2020-07-18 15:59:34.706000', '2020-07-18 16:00:03.279000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (74, '游轮旅行服务', 2, 65, '2020-07-18 15:59:34.792000', '2020-07-18 16:00:03.282000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (76, '非特殊用途化妆品', 2, 75, '2020-07-18 15:59:34.800000', '2020-07-18 16:00:03.292000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (77, '特殊用途化妆品', 2, 75, '2020-07-18 15:59:34.804000', '2020-07-18 16:00:03.296000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (78, '美容工具', 2, 75, '2020-07-18 15:59:34.807000', '2020-07-18 16:00:03.300000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (79, '护肤彩妆线上平台', 2, 75, '2020-07-18 15:59:34.811000', '2020-07-18 16:00:03.303000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (80, '护肤彩妆卖场及门店', 2, 75, '2020-07-18 15:59:34.815000', '2020-07-18 16:00:03.307000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (82, '休闲食品', 2, 81, '2020-07-18 15:59:34.822000', '2020-07-18 16:00:03.317000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (83, '粮油调味速食', 2, 81, '2020-07-18 15:59:34.826000', '2020-07-18 16:00:03.321000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (84, '软饮料和冲饮', 2, 81, '2020-07-18 15:59:34.830000', '2020-07-18 16:00:03.325000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (85, '生鲜', 2, 81, '2020-07-18 15:59:34.895000', '2020-07-18 16:00:03.331000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (86, '酒品', 2, 81, '2020-07-18 15:59:34.899000', '2020-07-18 16:00:03.334000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (88, '茶叶', 2, 81, '2020-07-18 15:59:34.907000', '2020-07-18 16:00:03.366000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (89, '乳制品', 2, 81, '2020-07-18 15:59:34.911000', '2020-07-18 16:00:03.370000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (92, '运动户外', 1, 0, '2020-07-18 15:59:35.005000', '2020-07-18 15:59:35.005000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (98, '珠宝钟表', 1, 0, '2020-07-18 15:59:35.111000', '2020-07-18 15:59:35.111000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (103, '医疗健康', 1, 0, '2020-07-18 15:59:35.213000', '2020-07-18 15:59:35.213000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (123, '商务服务', 1, 0, '2020-07-18 15:59:35.513000', '2020-07-18 15:59:35.513000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (141, '亲子', 1, 0, '2020-07-18 15:59:35.637000', '2020-07-18 15:59:35.637000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (150, '餐饮美食', 1, 0, '2020-07-18 15:59:35.670000', '2020-07-18 15:59:35.670000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (161, '零售百货', 1, 0, '2020-07-18 15:59:35.732000', '2020-07-18 15:59:35.732000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (166, '生活服务', 1, 0, '2020-07-18 15:59:35.750000', '2020-07-18 15:59:35.750000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (91, '食品卖场及门店', 2, 81, '2020-07-18 15:59:35.001000', '2020-07-18 16:00:03.464000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (93, '户外装备', 2, 92, '2020-07-18 15:59:35.009000', '2020-07-18 16:00:03.474000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (94, '健身器械', 2, 92, '2020-07-18 15:59:35.014000', '2020-07-18 16:00:03.478000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (95, '体育用品', 2, 92, '2020-07-18 15:59:35.099000', '2020-07-18 16:00:03.483000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (96, '运动户外线上平台', 2, 92, '2020-07-18 15:59:35.103000', '2020-07-18 16:00:03.487000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (97, '运动户外卖场及门店', 2, 92, '2020-07-18 15:59:35.107000', '2020-07-18 16:00:03.491000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (99, '珠宝', 2, 98, '2020-07-18 15:59:35.115000', '2020-07-18 16:00:03.500000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (100, '钟表', 2, 98, '2020-07-18 15:59:35.202000', '2020-07-18 16:00:03.504000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (101, '珠宝钟表线上平台', 2, 98, '2020-07-18 15:59:35.206000', '2020-07-18 16:00:03.508000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (102, '珠宝钟表卖场及门店', 2, 98, '2020-07-18 15:59:35.209000', '2020-07-18 16:00:03.512000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (104, '保健用品', 2, 103, '2020-07-18 15:59:35.216000', '2020-07-18 16:00:03.524000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (105, '保健食品', 2, 103, '2020-07-18 15:59:35.220000', '2020-07-18 16:00:03.528000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (106, '美容减肥保健用品', 2, 103, '2020-07-18 15:59:35.224000', '2020-07-18 16:00:03.532000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (107, '美容减肥保健食品', 2, 103, '2020-07-18 15:59:35.227000', '2020-07-18 16:00:03.535000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (108, '特殊保健品', 2, 103, '2020-07-18 15:59:35.231000', '2020-07-18 16:00:03.539000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (109, '心理健康', 2, 103, '2020-07-18 15:59:35.235000', '2020-07-18 16:00:03.543000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (110, '药品交易', 2, 103, '2020-07-18 15:59:35.239000', '2020-07-18 16:00:03.567000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (111, '药品生产研发', 2, 103, '2020-07-18 15:59:35.304000', '2020-07-18 16:00:03.571000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (112, '药品信息', 2, 103, '2020-07-18 15:59:35.308000', '2020-07-18 16:00:03.575000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (113, '医疗器械生产', 2, 103, '2020-07-18 15:59:35.311000', '2020-07-18 16:00:03.578000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (115, '假肢生产装配', 2, 103, '2020-07-18 15:59:35.319000', '2020-07-18 16:00:03.589000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (116, '美容整形机构', 2, 103, '2020-07-18 15:59:35.323000', '2020-07-18 16:00:03.592000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (117, '体检机构', 2, 103, '2020-07-18 15:59:35.407000', '2020-07-18 16:00:03.596000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (118, '综合医院', 2, 103, '2020-07-18 15:59:35.411000', '2020-07-18 16:00:03.600000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (119, '专科医疗', 2, 103, '2020-07-18 15:59:35.415000', '2020-07-18 16:00:03.603000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (120, '口腔医疗', 2, 103, '2020-07-18 15:59:35.419000', '2020-07-18 16:00:03.669000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (121, '健康资讯平台', 2, 103, '2020-07-18 15:59:35.423000', '2020-07-18 16:00:03.673000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (122, '医疗线上平台', 2, 103, '2020-07-18 15:59:35.509000', '2020-07-18 16:00:03.677000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (124, '管理咨询', 2, 123, '2020-07-18 15:59:35.516000', '2020-07-18 16:00:03.690000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (125, '策划咨询', 2, 123, '2020-07-18 15:59:35.520000', '2020-07-18 16:00:03.695000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (126, '对公代理服务', 2, 123, '2020-07-18 15:59:35.524000', '2020-07-18 16:00:03.700000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (127, '财务税务', 2, 123, '2020-07-18 15:59:35.528000', '2020-07-18 16:00:03.703000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (128, '出国服务', 2, 123, '2020-07-18 15:59:35.531000', '2020-07-18 16:00:03.708000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (129, '调查', 2, 123, '2020-07-18 15:59:35.536000', '2020-07-18 16:00:03.712000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (130, '拍卖', 2, 123, '2020-07-18 15:59:35.539000', '2020-07-18 16:00:03.716000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (131, '公关', 2, 123, '2020-07-18 15:59:35.543000', '2020-07-18 16:00:03.720000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (132, '配音', 2, 123, '2020-07-18 15:59:35.546000', '2020-07-18 16:00:03.723000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (133, '翻译', 2, 123, '2020-07-18 15:59:35.609000', '2020-07-18 16:00:03.727000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (134, '布景会展', 2, 123, '2020-07-18 15:59:35.612000', '2020-07-18 16:00:03.731000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (135, '招聘猎头', 2, 123, '2020-07-18 15:59:35.616000', '2020-07-18 16:00:03.735000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (136, '营销机构', 2, 123, '2020-07-18 15:59:35.620000', '2020-07-18 16:00:03.771000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (137, '印刷', 2, 123, '2020-07-18 15:59:35.623000', '2020-07-18 16:00:03.775000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (139, '建站服务', 2, 123, '2020-07-18 15:59:35.630000', '2020-07-18 16:00:03.783000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (140, '艺人经纪', 2, 123, '2020-07-18 15:59:35.634000', '2020-07-18 16:00:03.790000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (142, '童装童鞋', 2, 141, '2020-07-18 15:59:35.641000', '2020-07-18 16:00:03.799000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (143, '母婴用品', 2, 141, '2020-07-18 15:59:35.645000', '2020-07-18 16:00:03.803000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (144, '婴幼儿食品', 2, 141, '2020-07-18 15:59:35.649000', '2020-07-18 16:00:03.808000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (145, '母婴护理', 2, 141, '2020-07-18 15:59:35.652000', '2020-07-18 16:00:03.874000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (146, '儿童用品', 2, 141, '2020-07-18 15:59:35.656000', '2020-07-18 16:00:03.878000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (147, '育儿社区（孕期/胎教/优生）', 2, 141, '2020-07-18 15:59:35.659000', '2020-07-18 16:00:03.881000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (148, '亲子线上平台', 2, 141, '2020-07-18 15:59:35.662000', '2020-07-18 16:00:03.885000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (149, '亲子卖场及门店', 2, 141, '2020-07-18 15:59:35.666000', '2020-07-18 16:00:03.888000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (151, '烘焙甜品', 2, 150, '2020-07-18 15:59:35.674000', '2020-07-18 16:00:03.901000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (152, '咖啡厅', 2, 150, '2020-07-18 15:59:35.677000', '2020-07-18 16:00:03.904000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (153, '快餐', 2, 150, '2020-07-18 15:59:35.681000', '2020-07-18 16:00:03.908000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (154, '正餐-西餐', 2, 150, '2020-07-18 15:59:35.685000', '2020-07-18 16:00:03.912000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (155, '正餐-火锅', 2, 150, '2020-07-18 15:59:35.688000', '2020-07-18 16:00:03.976000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (156, '正餐-自助餐', 2, 150, '2020-07-18 15:59:35.714000', '2020-07-18 16:00:03.980000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (157, '正餐-烧烤', 2, 150, '2020-07-18 15:59:35.717000', '2020-07-18 16:00:03.983000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (158, '正餐-日韩料理', 2, 150, '2020-07-18 15:59:35.721000', '2020-07-18 16:00:03.986000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (159, '正餐-东南亚料理', 2, 150, '2020-07-18 15:59:35.725000', '2020-07-18 16:00:03.990000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (160, '正餐-其他', 2, 150, '2020-07-18 15:59:35.728000', '2020-07-18 16:00:04.078000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (162, '超市', 2, 161, '2020-07-18 15:59:35.736000', '2020-07-18 16:00:04.087000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (163, '便利店', 2, 161, '2020-07-18 15:59:35.739000', '2020-07-18 16:00:04.091000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (165, '综合百货', 2, 161, '2020-07-18 15:59:35.747000', '2020-07-18 16:00:04.181000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (167, '房屋维修', 2, 166, '2020-07-18 15:59:35.816000', '2020-07-18 16:00:04.192000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (168, '家电维修', 2, 166, '2020-07-18 15:59:35.820000', '2020-07-18 16:00:04.198000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (169, '家政服务', 2, 166, '2020-07-18 15:59:35.823000', '2020-07-18 16:00:04.202000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (170, '运动健身', 2, 166, '2020-07-18 15:59:35.827000', '2020-07-18 16:00:04.205000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (171, '休闲娱乐', 2, 166, '2020-07-18 15:59:35.830000', '2020-07-18 16:00:04.209000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (172, '演出票务', 2, 166, '2020-07-18 15:59:35.834000', '2020-07-18 16:00:04.212000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (173, '卡券消费', 2, 166, '2020-07-18 15:59:35.918000', '2020-07-18 16:00:04.216000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (174, '开锁', 2, 166, '2020-07-18 15:59:35.922000', '2020-07-18 16:00:04.219000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (175, '物流服务', 2, 166, '2020-07-18 15:59:35.926000', '2020-07-18 16:00:04.223000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (176, '物业服务', 2, 166, '2020-07-18 15:59:35.929000', '2020-07-18 16:00:04.226000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (177, '生活服务线上平台', 2, 166, '2020-07-18 15:59:35.933000', '2020-07-18 16:00:04.283000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (190, '法律服务', 1, 0, '2020-07-18 15:59:36.141000', '2020-07-18 15:59:36.141000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (194, '房地产', 1, 0, '2020-07-18 15:59:36.237000', '2020-07-18 15:59:36.237000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (198, '交通运输', 1, 0, '2020-07-18 15:59:36.251000', '2020-07-18 15:59:36.251000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (203, '节能环保', 1, 0, '2020-07-18 15:59:36.334000', '2020-07-18 15:59:36.334000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (209, '安全安保', 1, 0, '2020-07-18 15:59:36.438000', '2020-07-18 15:59:36.438000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (215, '家居装修建材', 1, 0, '2020-07-18 15:59:36.461000', '2020-07-18 15:59:36.461000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (223, '通讯及IT服务', 1, 0, '2020-07-18 15:59:36.488000', '2020-07-18 15:59:36.488000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (229, '日用百货', 1, 0, '2020-07-18 15:59:36.636000', '2020-07-18 15:59:36.636000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (240, '彩票', 1, 0, '2020-07-18 15:59:36.844000', '2020-07-18 15:59:36.844000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (242, '机构协会', 1, 0, '2020-07-18 15:59:36.851000', '2020-07-18 15:59:36.851000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (247, '网络资讯及社区', 1, 0, '2020-07-18 15:59:36.869000', '2020-07-18 15:59:36.869000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (250, '在线视听与阅读', 1, 0, '2020-07-18 15:59:36.879000', '2020-07-18 15:59:36.879000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (256, '自媒体', 1, 0, '2020-07-18 15:59:36.902000', '2020-07-18 15:59:36.902000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (179, '乐器', 2, 166, '2020-07-18 15:59:36.021000', '2020-07-18 16:00:04.290000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (180, '鲜花植物', 2, 166, '2020-07-18 15:59:36.024000', '2020-07-18 16:00:04.294000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (181, '宠物/宠物周边', 2, 166, '2020-07-18 15:59:36.028000', '2020-07-18 16:00:04.300000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (182, '出行服务', 2, 166, '2020-07-18 15:59:36.031000', '2020-07-18 16:00:04.303000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (183, '出行票务预订', 2, 166, '2020-07-18 15:59:36.035000', '2020-07-18 16:00:04.307000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (184, '搬家', 2, 166, '2020-07-18 15:59:36.038000', '2020-07-18 16:00:04.311000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (186, '非汽车交易', 2, 166, '2020-07-18 15:59:36.127000', '2020-07-18 16:00:04.318000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (187, '美发', 2, 166, '2020-07-18 15:59:36.130000', '2020-07-18 16:00:04.321000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (188, '美容', 2, 166, '2020-07-18 15:59:36.134000', '2020-07-18 16:00:04.325000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (189, '生活周边', 2, 166, '2020-07-18 15:59:36.138000', '2020-07-18 16:00:04.385000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (191, '司法鉴定', 2, 190, '2020-07-18 15:59:36.226000', '2020-07-18 16:00:04.394000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (192, '律师事务所', 2, 190, '2020-07-18 15:59:36.229000', '2020-07-18 16:00:04.397000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (193, '公证', 2, 190, '2020-07-18 15:59:36.233000', '2020-07-18 16:00:04.402000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (195, '房地产开发商', 2, 194, '2020-07-18 15:59:36.241000', '2020-07-18 16:00:04.493000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (196, '房地产中介', 2, 194, '2020-07-18 15:59:36.244000', '2020-07-18 16:00:04.496000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (197, '房地产线上平台', 2, 194, '2020-07-18 15:59:36.248000', '2020-07-18 16:00:04.500000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (199, '航空服务', 2, 198, '2020-07-18 15:59:36.261000', '2020-07-18 16:00:04.591000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (200, '铁路服务', 2, 198, '2020-07-18 15:59:36.266000', '2020-07-18 16:00:04.595000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (201, '公路服务', 2, 198, '2020-07-18 15:59:36.328000', '2020-07-18 16:00:04.599000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (202, '水路服务', 2, 198, '2020-07-18 15:59:36.331000', '2020-07-18 16:00:04.605000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (204, '污染处理', 2, 203, '2020-07-18 15:59:36.338000', '2020-07-18 16:00:04.614000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (205, '废旧回收', 2, 203, '2020-07-18 15:59:36.341000', '2020-07-18 16:00:04.618000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (206, '节能设备', 2, 203, '2020-07-18 15:59:36.345000', '2020-07-18 16:00:04.621000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (207, '环保设备', 2, 203, '2020-07-18 15:59:36.430000', '2020-07-18 16:00:04.625000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (208, '环境评测', 2, 203, '2020-07-18 15:59:36.434000', '2020-07-18 16:00:04.628000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (210, '防盗报警', 2, 209, '2020-07-18 15:59:36.441000', '2020-07-18 16:00:04.698000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (211, '保安安保', 2, 209, '2020-07-18 15:59:36.445000', '2020-07-18 16:00:04.702000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (213, '门禁考勤', 2, 209, '2020-07-18 15:59:36.453000', '2020-07-18 16:00:04.711000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (214, '交通消防', 2, 209, '2020-07-18 15:59:36.457000', '2020-07-18 16:00:04.715000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (216, '家装建材', 2, 215, '2020-07-18 15:59:36.465000', '2020-07-18 16:00:04.724000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (217, '家具', 2, 215, '2020-07-18 15:59:36.469000', '2020-07-18 16:00:04.727000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (218, '家居饰品', 2, 215, '2020-07-18 15:59:36.473000', '2020-07-18 16:00:04.731000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (219, '家纺', 2, 215, '2020-07-18 15:59:36.476000', '2020-07-18 16:00:04.735000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (220, '装修与设计', 2, 215, '2020-07-18 15:59:36.479000', '2020-07-18 16:00:04.738000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (221, '家居建材卖场及门店', 2, 215, '2020-07-18 15:59:36.482000', '2020-07-18 16:00:04.741000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (222, '家具建材线上平台', 2, 215, '2020-07-18 15:59:36.485000', '2020-07-18 16:00:04.745000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (224, '通信运营商', 2, 223, '2020-07-18 15:59:36.533000', '2020-07-18 16:00:04.753000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (225, '游戏充值', 2, 223, '2020-07-18 15:59:36.540000', '2020-07-18 16:00:04.757000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (226, '增值服务及云服务', 2, 223, '2020-07-18 15:59:36.543000', '2020-07-18 16:00:04.795000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (227, '通信设备及IT服务', 2, 223, '2020-07-18 15:59:36.547000', '2020-07-18 16:00:04.799000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (228, '通信设备卖场及门店', 2, 223, '2020-07-18 15:59:36.550000', '2020-07-18 16:00:04.803000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (230, '家居日用', 2, 229, '2020-07-18 15:59:36.640000', '2020-07-18 16:00:04.812000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (231, '厨具', 2, 229, '2020-07-18 15:59:36.643000', '2020-07-18 16:00:04.897000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (232, '洗护用具', 2, 229, '2020-07-18 15:59:36.647000', '2020-07-18 16:00:04.901000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (233, '女性护理', 2, 229, '2020-07-18 15:59:36.651000', '2020-07-18 16:00:04.905000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (234, '休闲玩具', 2, 229, '2020-07-18 15:59:36.738000', '2020-07-18 16:00:04.908000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (235, '成人用品', 2, 229, '2020-07-18 15:59:36.745000', '2020-07-18 16:00:04.912000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (236, '沐浴清洁', 2, 229, '2020-07-18 15:59:36.749000', '2020-07-18 16:00:04.915000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (237, '口腔护理', 2, 229, '2020-07-18 15:59:36.752000', '2020-07-18 16:00:05.000000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (239, '日用百货卖场及门店', 2, 229, '2020-07-18 15:59:36.841000', '2020-07-18 16:00:05.014000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (241, '彩票', 2, 240, '2020-07-18 15:59:36.848000', '2020-07-18 16:00:05.023000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (243, '境内政府机关', 2, 242, '2020-07-18 15:59:36.855000', '2020-07-18 16:00:05.032000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (244, '协会团体', 2, 242, '2020-07-18 15:59:36.859000', '2020-07-18 16:00:05.037000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (245, '公益组织', 2, 242, '2020-07-18 15:59:36.862000', '2020-07-18 16:00:05.040000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (246, '境外政府组织', 2, 242, '2020-07-18 15:59:36.866000', '2020-07-18 16:00:05.044000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (248, '在线新闻及资讯', 2, 247, '2020-07-18 15:59:36.873000', '2020-07-18 16:00:05.052000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (249, '综合线上社区', 2, 247, '2020-07-18 15:59:36.876000', '2020-07-18 16:00:05.056000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (251, '影音视频', 2, 250, '2020-07-18 15:59:36.883000', '2020-07-18 16:00:05.065000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (252, '音乐', 2, 250, '2020-07-18 15:59:36.886000', '2020-07-18 16:00:05.069000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (253, '网络直播', 2, 250, '2020-07-18 15:59:36.890000', '2020-07-18 16:00:05.073000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (254, '阅读与在线FM收听', 2, 250, '2020-07-18 15:59:36.894000', '2020-07-18 16:00:05.076000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (255, '在线动漫', 2, 250, '2020-07-18 15:59:36.898000', '2020-07-18 16:00:05.081000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (257, '科教文化', 2, 256, '2020-07-18 15:59:36.906000', '2020-07-18 16:00:05.091000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (258, '社会', 2, 256, '2020-07-18 15:59:36.909000', '2020-07-18 16:00:05.094000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (259, '时尚美容', 2, 256, '2020-07-18 15:59:36.913000', '2020-07-18 16:00:05.098000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (260, '情感美文', 2, 256, '2020-07-18 15:59:36.943000', '2020-07-18 16:00:05.101000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (261, '财经', 2, 256, '2020-07-18 15:59:36.946000', '2020-07-18 16:00:05.104000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (262, '旅游', 2, 256, '2020-07-18 15:59:36.950000', '2020-07-18 16:00:05.107000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (263, '运动体育', 2, 256, '2020-07-18 15:59:36.954000', '2020-07-18 16:00:05.111000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (264, '亲子育儿', 2, 256, '2020-07-18 15:59:36.957000', '2020-07-18 16:00:05.115000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (265, '美食', 2, 256, '2020-07-18 15:59:36.960000', '2020-07-18 16:00:05.118000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (266, '汽车房产', 2, 256, '2020-07-18 15:59:36.964000', '2020-07-18 16:00:05.122000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (268, '汽车', 1, 0, '2020-07-18 15:59:36.971000', '2020-07-18 15:59:36.971000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (276, '软件工具', 1, 0, '2020-07-18 15:59:37.001000', '2020-07-18 15:59:37.001000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (285, '游戏', 1, 0, '2020-07-18 15:59:37.033000', '2020-07-18 15:59:37.033000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (303, '服饰鞋帽箱包', 1, 0, '2020-07-18 15:59:37.092000', '2020-07-18 15:59:37.092000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (314, '工业工程', 1, 0, '2020-07-18 15:59:37.133000', '2020-07-18 15:59:37.133000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (318, '农林牧渔', 1, 0, '2020-07-18 15:59:37.150000', '2020-07-18 15:59:37.150000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (323, '出版传媒', 1, 0, '2020-07-18 15:59:37.167000', '2020-07-18 15:59:37.167000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (328, '数码家电', 1, 0, '2020-07-18 15:59:37.183000', '2020-07-18 15:59:37.183000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (2, '综合电商', 2, 1, '2020-07-18 15:59:34.284000', '2020-07-18 16:00:02.620000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (4, '返利平台', 2, 1, '2020-07-18 15:59:34.372000', '2020-07-18 16:00:02.635000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (27, '综合写真与其它摄影服务', 2, 24, '2020-07-18 15:59:34.470000', '2020-07-18 16:00:02.796000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (61, '商业保理', 2, 39, '2020-07-18 15:59:34.637000', '2020-07-18 16:00:03.162000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (87, '烟草', 2, 81, '2020-07-18 15:59:34.903000', '2020-07-18 16:00:03.362000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (90, '食品线上平台', 2, 81, '2020-07-18 15:59:34.997000', '2020-07-18 16:00:03.375000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (114, '医疗器械销售', 2, 103, '2020-07-18 15:59:35.315000', '2020-07-18 16:00:03.585000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (138, '广告包装', 2, 123, '2020-07-18 15:59:35.627000', '2020-07-18 16:00:03.779000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (164, '购物中心', 2, 161, '2020-07-18 15:59:35.743000', '2020-07-18 16:00:04.096000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (178, '爱好收藏', 2, 166, '2020-07-18 15:59:35.936000', '2020-07-18 16:00:04.287000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (185, '占卜起名', 2, 166, '2020-07-18 15:59:36.123000', '2020-07-18 16:00:04.314000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (212, '警用装备', 2, 209, '2020-07-18 15:59:36.449000', '2020-07-18 16:00:04.708000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (238, '日用百货线上平台', 2, 229, '2020-07-18 15:59:36.755000', '2020-07-18 16:00:05.004000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (267, '生活', 2, 256, '2020-07-18 15:59:36.968000', '2020-07-18 16:00:05.125000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (269, '汽车线上平台', 2, 268, '2020-07-18 15:59:36.975000', '2020-07-18 16:00:05.133000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (270, '车辆养护', 2, 268, '2020-07-18 15:59:36.979000', '2020-07-18 16:00:05.136000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (271, '汽车用品', 2, 268, '2020-07-18 15:59:36.982000', '2020-07-18 16:00:05.139000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (272, '汽车厂商', 2, 268, '2020-07-18 15:59:36.986000', '2020-07-18 16:00:05.143000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (273, '二手车经营', 2, 268, '2020-07-18 15:59:36.989000', '2020-07-18 16:00:05.146000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (274, '驾校', 2, 268, '2020-07-18 15:59:36.993000', '2020-07-18 16:00:05.150000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (275, '汽车-4S店/经销商', 2, 268, '2020-07-18 15:59:36.997000', '2020-07-18 16:00:05.153000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (277, '办公商务软件', 2, 276, '2020-07-18 15:59:37.004000', '2020-07-18 16:00:05.161000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (278, '系统软件', 2, 276, '2020-07-18 15:59:37.010000', '2020-07-18 16:00:05.165000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (279, '摄影摄像', 2, 276, '2020-07-18 15:59:37.013000', '2020-07-18 16:00:05.168000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (280, '天气日历', 2, 276, '2020-07-18 15:59:37.016000', '2020-07-18 16:00:05.172000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (281, '社交', 2, 276, '2020-07-18 15:59:37.019000', '2020-07-18 16:00:05.175000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (282, '网络电话', 2, 276, '2020-07-18 15:59:37.023000', '2020-07-18 16:00:05.179000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (283, '地图导航', 2, 276, '2020-07-18 15:59:37.026000', '2020-07-18 16:00:05.182000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (284, '体育健康', 2, 276, '2020-07-18 15:59:37.029000', '2020-07-18 16:00:05.186000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (286, '角色扮演', 2, 285, '2020-07-18 15:59:37.036000', '2020-07-18 16:00:05.195000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (287, '策略', 2, 285, '2020-07-18 15:59:37.039000', '2020-07-18 16:00:05.199000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (288, '体育', 2, 285, '2020-07-18 15:59:37.042000', '2020-07-18 16:00:05.202000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (289, '棋牌', 2, 285, '2020-07-18 15:59:37.045000', '2020-07-18 16:00:05.206000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (290, '音乐', 2, 285, '2020-07-18 15:59:37.049000', '2020-07-18 16:00:05.210000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (291, '模拟经营', 2, 285, '2020-07-18 15:59:37.051000', '2020-07-18 16:00:05.213000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (292, '休闲', 2, 285, '2020-07-18 15:59:37.055000', '2020-07-18 16:00:05.218000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (293, '益智', 2, 285, '2020-07-18 15:59:37.058000', '2020-07-18 16:00:05.222000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (294, '射击', 2, 285, '2020-07-18 15:59:37.062000', '2020-07-18 16:00:05.225000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (295, '养成', 2, 285, '2020-07-18 15:59:37.065000', '2020-07-18 16:00:05.229000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (296, '格斗', 2, 285, '2020-07-18 15:59:37.069000', '2020-07-18 16:00:05.232000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (297, '冒险', 2, 285, '2020-07-18 15:59:37.072000', '2020-07-18 16:00:05.236000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (298, '卡牌', 2, 285, '2020-07-18 15:59:37.075000', '2020-07-18 16:00:05.240000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (299, '竞速', 2, 285, '2020-07-18 15:59:37.078000', '2020-07-18 16:00:05.243000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (301, 'MOBA', 2, 285, '2020-07-18 15:59:37.085000', '2020-07-18 16:00:05.252000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (302, '电子竞技', 2, 285, '2020-07-18 15:59:37.088000', '2020-07-18 16:00:05.256000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (304, '服装', 2, 303, '2020-07-18 15:59:37.095000', '2020-07-18 16:00:05.264000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (305, '贴身内衣', 2, 303, '2020-07-18 15:59:37.099000', '2020-07-18 16:00:05.268000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (306, '服装配饰', 2, 303, '2020-07-18 15:59:37.103000', '2020-07-18 16:00:05.272000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (307, '鞋类', 2, 303, '2020-07-18 15:59:37.107000', '2020-07-18 16:00:05.275000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (308, '袜类', 2, 303, '2020-07-18 15:59:37.112000', '2020-07-18 16:00:05.278000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (309, '帽类', 2, 303, '2020-07-18 15:59:37.116000', '2020-07-18 16:00:05.282000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (310, '服饰鞋帽原料', 2, 303, '2020-07-18 15:59:37.120000', '2020-07-18 16:00:05.286000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (311, '箱包', 2, 303, '2020-07-18 15:59:37.123000', '2020-07-18 16:00:05.291000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (312, '服饰鞋帽箱包线上平台', 2, 303, '2020-07-18 15:59:37.127000', '2020-07-18 16:00:05.295000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (313, '服饰鞋帽箱包卖场及门店', 2, 303, '2020-07-18 15:59:37.130000', '2020-07-18 16:00:05.298000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (315, '能源化工', 2, 314, '2020-07-18 15:59:37.137000', '2020-07-18 16:00:05.307000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (316, '机械设备', 2, 314, '2020-07-18 15:59:37.142000', '2020-07-18 16:00:05.310000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (317, '电子电工', 2, 314, '2020-07-18 15:59:37.146000', '2020-07-18 16:00:05.315000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (319, '兽医兽药', 2, 318, '2020-07-18 15:59:37.153000', '2020-07-18 16:00:05.325000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (320, '农药化肥', 2, 318, '2020-07-18 15:59:37.156000', '2020-07-18 16:00:05.329000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (321, '种植养殖', 2, 318, '2020-07-18 15:59:37.160000', '2020-07-18 16:00:05.333000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (322, '园林景观', 2, 318, '2020-07-18 15:59:37.164000', '2020-07-18 16:00:05.336000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (324, '书籍杂志', 2, 323, '2020-07-18 15:59:37.170000', '2020-07-18 16:00:05.345000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (326, '电视电台', 2, 323, '2020-07-18 15:59:37.177000', '2020-07-18 16:00:05.351000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (327, '动漫制作及周边', 2, 323, '2020-07-18 15:59:37.180000', '2020-07-18 16:00:05.354000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (329, '电脑', 2, 328, '2020-07-18 15:59:37.186000', '2020-07-18 16:00:05.365000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (330, '手机', 2, 328, '2020-07-18 15:59:37.189000', '2020-07-18 16:00:05.369000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (331, '摄影摄像设备', 2, 328, '2020-07-18 15:59:37.192000', '2020-07-18 16:00:05.372000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (332, '数码配件', 2, 328, '2020-07-18 15:59:37.195000', '2020-07-18 16:00:05.376000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (333, '影音设备', 2, 328, '2020-07-18 15:59:37.199000', '2020-07-18 16:00:05.379000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (334, '办公设备', 2, 328, '2020-07-18 15:59:37.203000', '2020-07-18 16:00:05.383000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (335, '电子教育', 2, 328, '2020-07-18 15:59:37.206000', '2020-07-18 16:00:05.388000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (336, '智能设备', 2, 328, '2020-07-18 15:59:37.210000', '2020-07-18 16:00:05.391000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (337, '大家电', 2, 328, '2020-07-18 15:59:37.215000', '2020-07-18 16:00:05.395000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (338, '生活电器', 2, 328, '2020-07-18 15:59:37.218000', '2020-07-18 16:00:05.399000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (339, '电子游戏设备', 2, 328, '2020-07-18 15:59:37.221000', '2020-07-18 16:00:05.402000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (340, '数码家电线上平台', 2, 328, '2020-07-18 15:59:37.226000', '2020-07-18 16:00:05.406000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (341, '数码家电卖场及门店', 2, 328, '2020-07-18 15:59:37.229000', '2020-07-18 16:00:05.410000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (300, '综合游戏平台', 2, 285, '2020-07-18 15:59:37.082000', '2020-07-18 16:00:05.248000');
INSERT INTO marketing_advertiser_account_industry (id, name, level, parent_id, created_at, updated_at) VALUES (325, '音像影视', 2, 323, '2020-07-18 15:59:37.173000', '2020-07-18 16:00:05.348000');
---公域and私域---
-- 人员管理
update ucenter_permission
set mapping = array [
    '{"url":"/users/collect/filtering?page*","methods":["GET"]}'
    ]
where id = 20000;

-- 员工新建/编辑
update ucenter_permission
set mapping = array [
    '{"url":"/users","methods":["POST"]}',
    '{"url":"/users/*","methods":["PATCH"]}'
    ]
where id = 2000000;
-- 删除
update ucenter_permission
set mapping = array [
    '{"url":"/users/*","methods":["DELETE"]}'
    ]
where id = 2000001;

update ucenter_permission
set mapping = array [
    '{"url":"/users/*/action/reset-password","methods":["PATCH"]}'
    ]
where id = 2000002;

update ucenter_permission
set mapping = '{"{\"url\":\"/permission-groups/**\", \"methods\":[\"POST\",\"PATCH\"]}"}'
where id = 2000201;
