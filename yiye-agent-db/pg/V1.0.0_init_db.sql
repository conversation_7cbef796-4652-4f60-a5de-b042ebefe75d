CREATE TABLE ucenter_user
(
    "id"                  bigserial primary key,
    "username"            varchar,
    "email"               varchar        not null,
    "phone"               varchar        not null,
    "password"            varchar        not null,
    "department_id"       bigint,
    "permission_group_id" bigint,
    "level"               int4 default 0 not null,
    "role"                int4 default 1 not null,
    "status"              int4 default 1 not null,
    "created_at"          timestamp      not null,
    "updated_at"          timestamp      not null
);

create unique index ux_ucenter_user_email on ucenter_user (email);
create unique index ux_ucenter_user_phone on ucenter_user (phone);

comment on column ucenter_user.username is '用户名';
comment on column ucenter_user.email is '邮箱';
comment on column ucenter_user.phone is '手机号';
comment on column ucenter_user.password is '密码';
comment on column ucenter_user.department_id is '部门id';
comment on column ucenter_user.permission_group_id is '权限组id';
comment on column ucenter_user.level is '级别: 0 普通员工, 1 上级';
comment on column ucenter_user.role is '用户角色: 0 超级管理员, 1 员工';
comment on column ucenter_user.status is '用户状态: 1:正常, 0 删除';
comment on column ucenter_user.created_at is '创建时间';
comment on column ucenter_user.updated_at is '更新时间';

-- 初始化超管账号
INSERT INTO "ucenter_user"("username", "email", "phone", "password", "department_id", "permission_group_id",
                                    "level", "role", "status", "created_at", "updated_at")
VALUES ('yiye_agent_test', '<EMAIL>', '17602545477',
        '$2a$10$t.hKo/Z/7ln.YXxugUI6LOl1esG.q2pRiHdMVSou1UiQcbP6efwea', NULL, NULL, 1, 0, 1, now(), now());


-- 创建ucenter中的部门表
CREATE TABLE ucenter_department
(
    "id"         bigserial primary key,
    "name"       varchar   not null,
    "parent_id"  bigint,
    "created_at" timestamp NOT NULL,
    "updated_at" timestamp NOT NULL,
    "t_order"    int4      not null
);

-- 根据部门名称以及部门的父部门创建唯一索引
CREATE unique INDEX ux_ucenter_department_name_and_parentid on ucenter_department (name, parent_id);

comment on column ucenter_department.id is '部门ID';
comment on column ucenter_department.name is '部门名称';
comment on column ucenter_department.parent_id is '父级id';
comment on column ucenter_department.created_at is '创建时间';
comment on column ucenter_department.updated_at is '修改时间';
comment on column ucenter_department.t_order is '排序字段';

-- 权限表
create table ucenter_permission
(
    id         bigserial primary key,
    name       varchar,
    parent_id  bigint,
    level      integer,
    mapping    character varying[],
    module     varchar,
    t_type     integer default 0,
    created_at timestamp,
    updated_at timestamp
);

comment on table ucenter_permission is '权限';

comment on column ucenter_permission.name is '权限名称';

comment on column ucenter_permission.parent_id is '父级权限';

comment on column ucenter_permission.level is '权限级别: 1=一级导航,2=一级数据权限,3=二级导航,4=二级数据权限,5=操作权限.';

comment on column ucenter_permission.mapping is 'url匹配规则';

comment on column ucenter_permission.module is '模块名: 路由/字段/图表名称';

comment on column ucenter_permission.t_type is '权限类型: 0=操作权限,1=全部账户数据,2=仅分配账户数据,3=子级分配账户数据,4=字段权限,5=图表权限.';

-- 权限组表
create table ucenter_permission_group
(
    id             bigserial primary key,
    name           varchar,
    permission_ids bigint[],
    created_at     timestamp not null,
    updated_at     timestamp not null
);

comment on table ucenter_permission_group is '权限组';
comment on column ucenter_permission_group.name is '权限组名称';

create unique index ux_ucenter_permission_group_name on ucenter_permission_group (name);


-- 投放账户表
create table marketing_advertiser_account
(
    id                          bigserial primary key,
    account_id                  varchar,
    account_name                varchar,
    advertiser_account_group_id bigint,
    platform_name               varchar,
    account_status              integer,
    access_token                varchar,
    refresh_token               varchar,
    refresh_token_expires       timestamp,
    platform_id                 integer,
    parent_id                   integer,
    corporation_name            varchar,
    balance                     int8,
    target                      json,
    ext                         json,
    created_at                  timestamp,
    updated_at                  timestamp,
    is_extract                  bool default false
);
create index idx_maa_account_id on marketing_advertiser_account (account_id);

create index idx_maa_parent_id on marketing_advertiser_account (parent_id);

create index idx_maa_platform_id on marketing_advertiser_account (platform_id);

create index idx_maa_created_at on marketing_advertiser_account (created_at);

create index idx_maa_consumer_id on marketing_advertiser_account (advertiser_account_group_id);

comment on table marketing_advertiser_account is '投放账户表';

comment on column marketing_advertiser_account.id is '广告主id';

comment on column marketing_advertiser_account.account_name is '广告主名称';

comment on column marketing_advertiser_account.account_status is '广告主状态（0有效、1待审核、2审核不通过、3封停）';

comment on column marketing_advertiser_account.access_token is 'token';

comment on column marketing_advertiser_account.refresh_token is '刷新token';

comment on column marketing_advertiser_account.refresh_token_expires is '刷新token的过期时间';

comment on column marketing_advertiser_account.platform_id is '平台id';

comment on column marketing_advertiser_account.parent_id is '所属代理商id';

comment on column marketing_advertiser_account.target is '投放目标';

comment on column marketing_advertiser_account.ext is '扩展字段';

comment on column marketing_advertiser_account.ext is '扩展字段';

comment on column marketing_advertiser_account.is_extract is '是否抽取';
-- 客户表
create table marketing_advertiser_account_group
(
    id         bigserial primary key,
    name       varchar   not null,
    created_at timestamp not null,
    updated_at timestamp not null
);

create unique index ux_marketing_advertiser_account_group_name on marketing_advertiser_account_group (name);

comment on column marketing_advertiser_account_group.id is '客户ID';

comment on column marketing_advertiser_account_group.name is '客户名称';

comment on column marketing_advertiser_account_group.created_at is '创建时间';

comment on column marketing_advertiser_account_group.updated_at is '更新时间';

-- 投放账户与用户关系表
create table marketing_advertiser_account_rel
(
    id                    bigserial primary key,
    advertiser_account_id bigint    not null,
    user_id               bigint,
    created_at            timestamp not null,
    updated_at            timestamp not null
);

create unique index ux_marketing_advertiser_account_rel_accountid_and_userid
    on marketing_advertiser_account_rel (user_id, advertiser_account_id);

comment on column marketing_advertiser_account_rel.id is '关系id';

comment on column marketing_advertiser_account_rel.advertiser_account_id is '投放账户id';

comment on column marketing_advertiser_account_rel.user_id is '用户id';

comment on column marketing_advertiser_account_rel.created_at is '创建时间';

comment on column marketing_advertiser_account_rel.updated_at is '更新时间';

-- 第三方平台配置表
create table marketing_platform_conf
(
    id                      bigserial primary key,
    name                    varchar   not null,
    logo_list               varchar   not null,
    logo_auth               varchar   not null,
    marketing_client_id     varchar,
    marketing_client_secret varchar,
    authorization_url       varchar,
    created_at              timestamp not null,
    updated_at              timestamp not null
);

create index ux_mpc_client_id on marketing_platform_conf (marketing_client_id);
create index idx_mpc_name on marketing_platform_conf (name);

comment on table marketing_platform_conf is '第三方平台配置表';

comment on column marketing_platform_conf.name is '平台名称';

comment on column marketing_platform_conf.logo_list is '列表页logo';

comment on column marketing_platform_conf.logo_auth is '授权页logo';

comment on column marketing_platform_conf.marketing_client_id is '应用id';

comment on column marketing_platform_conf.marketing_client_secret is '应用秘钥';

comment on column marketing_platform_conf.authorization_url is '授权地址';

-- 公域表内私域client_id配置表
create table if not exists agent_conf
(
    id         bigserial primary key,
    agent_id   varchar not null,
    agent_name varchar not null,
    host       varchar not null,
    status     integer default 1,
    created_at timestamp,
    updated_at timestamp
);

create unique index if not exists idx_agent_conf_agent_id on agent_conf (agent_id);

comment on table agent_conf is '私域配置表';

comment on column agent_conf.id is 'id';

comment on column agent_conf.agent_id is '私域id';

comment on column agent_conf.agent_name is '私域名称';

comment on column agent_conf.host is 'api地址';

comment on column agent_conf.status is '状态; 1已启用, 0已停用';

comment on column agent_conf.created_at is '创建时间';

comment on column agent_conf.updated_at is '更新时间';


-- 微信用户表
create table if not exists message_wechat_user
(
    id         bigserial primary key,
    user_id    bigint,
    country    varchar,
    province   varchar,
    city       varchar,
    openid     varchar,
    sex        varchar,
    nickname   varchar,
    headimgurl varchar,
    language   varchar,
    privilege  varchar,
    created_at timestamp not null,
    updated_at timestamp not null,
    config     json
);

comment on table message_wechat_user is '微信用户-用户';

comment on column message_wechat_user.user_id is '用户id';

comment on column message_wechat_user.country is '国家，如中国为CN';

comment on column message_wechat_user.province is '用户个人资料填写的省份';

comment on column message_wechat_user.city is '普通用户个人资料填写的城市';

comment on column message_wechat_user.openid is '授权用户唯一标识';

comment on column message_wechat_user.sex is '用户的性别';

comment on column message_wechat_user.nickname is '用户昵称';

comment on column message_wechat_user.headimgurl is '用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。';

comment on column message_wechat_user.language is '用户语言';

comment on column message_wechat_user.privilege is '用户特权信息，json 数组，如微信沃卡用户为（chinaunicom）';

comment on column message_wechat_user.created_at is '创建时间';

comment on column message_wechat_user.updated_at is '更新时间';

comment on column message_wechat_user.config is '用户微信推送配置：{“linkman-switch”:true}';


--短信日志表
create table message_sms_send_log
(
    id                    bigserial primary key,
    mobile                varchar                                not null,
    content               varchar                                not null,
    send_at               timestamp,
    arrive_at             timestamp,
    count                 integer,
    send_status           varchar,
    sms_signature_id      bigint,
    ext                   json,
    created_at            timestamp,
    updated_at            timestamp,
    flag_id               varchar                                not null,
    msg_id                varchar                                not null,
    sync_status           varchar default '0'::character varying not null,
    advertiser_account_id bigint                                 not null
);

comment on table message_sms_send_log is '短信记录表';

comment on column message_sms_send_log.mobile is '手机号';

comment on column message_sms_send_log.content is '发送内容';

comment on column message_sms_send_log.send_at is '发送时间';

comment on column message_sms_send_log.arrive_at is '到达时间';

comment on column message_sms_send_log.count is '短信条数';

comment on column message_sms_send_log.send_status is '短信状态';

comment on column message_sms_send_log.sms_signature_id is '短信签名id';

comment on column message_sms_send_log.ext is '扩展';

comment on column message_sms_send_log.created_at is '创建时间';

comment on column message_sms_send_log.updated_at is '更新时间';

comment on column message_sms_send_log.flag_id is '短信唯一标识id';

comment on column message_sms_send_log.msg_id is '第三方短信唯一标识';

comment on column message_sms_send_log.sync_status is '短信同步状态 0 未同步 1 已同步';

comment on column message_sms_send_log.advertiser_account_id is '投放账户id';


--短信签名表
create table if not exists message_sms_signature
(
    id                    bigserial primary key,
    name                  varchar           not null,
    advertiser_account_id bigint,
    updated_id            integer,
    version               integer default 0,
    status                integer default 0 not null,
    created_at            timestamp         not null,
    updated_at            timestamp         not null,
    type                  integer,
    ext                   json
);

comment on table message_sms_signature is '短信签名表';

comment on column message_sms_signature.name is '签名名称';

comment on column message_sms_signature.advertiser_account_id is '投放账户id';

comment on column message_sms_signature.updated_id is '更新操作用户id';

comment on column message_sms_signature.version is '版本';

comment on column message_sms_signature.status is '状态（0停用 ；1可用）';

comment on column message_sms_signature.created_at is '创建时间';

comment on column message_sms_signature.updated_at is '更新时间';

comment on column message_sms_signature.type is '签名类型：0 默认签名， 1 自定义签名';

comment on column message_sms_signature.ext is '扩展字段';


--webhook
create table if not exists message_webhook
(
    id                                bigserial primary key,
    url                               varchar   not null,
    created_at                        timestamp not null,
    updated_at                        timestamp not null,
    relation_advertiser_account_names varchar
);

-- 创建唯一索引
create unique index ux_message_webhook_url on message_webhook (url);

comment on table message_webhook is 'webhook内容表';

comment on column message_webhook.url is 'url地址';

comment on column message_webhook.created_at is '创建时间';

comment on column message_webhook.updated_at is '更新时间';

comment on column message_webhook.relation_advertiser_account_names is '关联投放账户和客户名称';


--webhook与投放账户和客户关联表
create table message_webhook_advertiser_account_rel
(
    id                    bigserial primary key,
    webhook_id            bigint    not null,
    advertiser_account_id bigint    not null,
    created_at            timestamp not null,
    updated_at            timestamp not null
);

comment on table message_webhook_advertiser_account_rel is 'webhook与投放账户关系表';

comment on column message_webhook_advertiser_account_rel.webhook_id is 'webhook地址id';

comment on column message_webhook_advertiser_account_rel.advertiser_account_id is '投放账户id';

comment on column message_webhook_advertiser_account_rel.created_at is '创建时间';

comment on column message_webhook_advertiser_account_rel.updated_at is '更新时间';


-- 微信商务号的绑定关系
CREATE TABLE "payment_wechat_official_account_rel"
(
    "id"                          bigserial primary key,
    "official_account_id"         bigint    NOT NULL,
    "advertiser_account_id"       bigint,
    "advertiser_account_group_id" bigint,
    "type"                        int4,
    "created_at"                  timestamp NOT NULL,
    "updated_at"                  timestamp NOT NULL
);
-- 创建唯一索引
CREATE unique INDEX ux_pwoar_officeId_and_accountid_and_type on
    payment_wechat_official_account_rel (official_account_id, advertiser_account_id, type);

CREATE unique INDEX ux_pwoar_officeId_and_groupid_and_type on
    payment_wechat_official_account_rel (official_account_id, advertiser_account_group_id, type);


COMMENT ON COLUMN "payment_wechat_official_account_rel"."id" IS '关系id';
COMMENT ON COLUMN "payment_wechat_official_account_rel"."official_account_id" IS '微信商务号id';
COMMENT ON COLUMN "payment_wechat_official_account_rel"."advertiser_account_id" IS '投放账户ID';
COMMENT ON COLUMN "payment_wechat_official_account_rel"."advertiser_account_group_id" IS '客户ID';
COMMENT ON COLUMN "payment_wechat_official_account_rel"."type" IS '投放关系类型';
COMMENT ON COLUMN "payment_wechat_official_account_rel"."created_at" IS '创建时间';
COMMENT ON COLUMN "payment_wechat_official_account_rel"."updated_at" IS '修改时间';


-- 商户号配置
create table payment_wechat_merchant_account
(
    id                 bigserial primary key,
    wechat_official_id integer,
    name               varchar,
    mch_id             varchar,
    mch_key            varchar,
    status             integer,
    created_at         timestamp not null,
    updated_at         timestamp not null,
    certificate_name   varchar,
    certificate_path   varchar
);

create index idx_wechat_merchant_account_wo_id on payment_wechat_merchant_account (wechat_official_id);

comment on table payment_wechat_merchant_account is '商户号配置';

comment on column payment_wechat_merchant_account.wechat_official_id is '公众号id';

comment on column payment_wechat_merchant_account.name is '商户号名称';

comment on column payment_wechat_merchant_account.mch_id is '商户号id';

comment on column payment_wechat_merchant_account.mch_key is '商户号秘钥';

comment on column payment_wechat_merchant_account.status is '授权状态. 0: 未验证; 1: 已验证';

comment on column payment_wechat_merchant_account.certificate_name is '上传证书原名';

comment on column payment_wechat_merchant_account.certificate_path is '证书保存的路径';

-- 公众号配置
create table payment_wechat_official_account
(
    id           bigserial primary key,
    app_id       varchar,
    name         varchar,
    app_secret   varchar,
    file_name    varchar,
    file_content varchar,
    created_at   timestamp not null,
    updated_at   timestamp not null
);

create index idx_pwoa_app_id on payment_wechat_official_account (app_id);

comment on table payment_wechat_official_account is '公众号配置';

comment on column payment_wechat_official_account.app_id is 'appId';

comment on column payment_wechat_official_account.name is '公众号名称';

comment on column payment_wechat_official_account.app_secret is '公众号秘钥';

comment on column payment_wechat_official_account.file_name is '文件名称';

comment on column payment_wechat_official_account.file_content is '文件内容';


-- 域名表
CREATE TABLE "landing_page_domain"
(
    "id"         bigserial primary key,
    "domain"     varchar   NOT NULL,
    "created_at" timestamp NOT NULL,
    "updated_at" timestamp NOT NULL
);

CREATE unique INDEX ux_landing_page_domain_name on landing_page_domain (domain);

COMMENT ON COLUMN "landing_page_domain"."domain" IS '域名名称';
COMMENT ON COLUMN "landing_page_domain"."created_at" IS '创建时间';
COMMENT ON COLUMN "landing_page_domain"."updated_at" IS '更新时间';
COMMENT ON TABLE "landing_page_domain" IS '域名表';


-- 域名关系表
CREATE TABLE "landing_page_domain_rel"
(
    "id"                    bigserial primary key,
    "domain_id"             int8,
    "advertiser_account_id" int8,
    "created_at"            timestamp NOT NULL,
    "updated_at"            timestamp NOT NULL
);

COMMENT ON COLUMN "landing_page_domain_rel"."domain_id" IS '域名名称id';
COMMENT ON COLUMN "landing_page_domain_rel"."advertiser_account_id" IS '投放账户id';
COMMENT ON COLUMN "landing_page_domain_rel"."created_at" IS '创建时间';
COMMENT ON COLUMN "landing_page_domain_rel"."updated_at" IS '更新时间';
COMMENT ON TABLE "landing_page_domain_rel" IS '域名投放账户关系表';

--- 添加权限
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (100, '账户管理', null, 1, null, 'marketing-advertiser-account-module', 0, '2020-05-20 13:03:44.496237',
        '2020-05-20 13:03:44.496237');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (200, '组织管理', null, 1, null, 'ucenter-module', 0, '2020-05-20 13:03:44.631793', '2020-05-20 13:03:44.631793');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (300, '系统设置', null, 1, null, 'system-setting-module', 0, '2020-05-20 13:03:44.631793',
        '2020-05-20 13:03:44.631793');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (10000, '账户管理', 100, 3,
        array [ '{"url":"/advertiser-accounts/**","methods":["GET"]}','{"url":"/advertiser-account-groups/**","methods":["GET"]}','{"url":"/platforms/**","methods":["GET"]}','{"url":"/departments","methods":["GET"],"type":"EXCLUDE"}','{"url":"/users/collect/**","methods":["GET"],"type":"EXCLUDE"}' ],
        'advertiser-account-list', 0, '2020-05-20 13:03:44.697995', '2020-05-20 13:03:44.697995');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (10001, '全部账户', 100, 2, null, null, 1, '2020-05-20 13:03:44.759411', '2020-05-20 13:03:44.759411');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (10002, '仅分配账户', 100, 2, null, null, 2, '2020-05-20 13:03:44.830116', '2020-05-20 13:03:44.830116');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (10003, '子级分配账户', 100, 2, null, null, 3, '2020-05-20 13:03:44.860082', '2020-05-20 13:03:44.860082');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (20000, '员工管理', 200, 3,
        array [ '{"url":"/users/collect/filtering*","methods":["GET"]}','{"url":"/permission-groups*","methods":["GET"],"type":"EXCLUDE"}' ],
        'ucenter-user-list', 0, '2020-05-20 13:03:44.915738', '2020-05-20 13:03:44.915738');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (20002, '权限管理', 200, 3, array [ '{"url":"/permission-groups/collect/filtering","methods":["GET"]}' ],
        'ucenter-permission-and-permission-group-list', 0, '2020-05-20 13:03:45.110658', '2020-05-20 13:03:45.110658');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30000, '通知', 300, 3,
        array [ '{"url":"/wechat-users/fetch/mine","methods":["GET"]}','{"url":"/wechat-users/fetch/change","methods":["GET"],"type":"EXCLUDE"}' ],
        'message-wechat-send', 0, '2020-05-20 12:52:04.835726', '2020-05-20 12:52:04.835726');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30001, '域名绑定', 300, 3,
        array [ '{"url":"/domains/**","methods":["GET"]}','{"url":"/advertiser-accounts","methods":["GET"],"type":"EXCLUDE"}','{"url":"/advertiser-account-groups","methods":["GET"],"type":"EXCLUDE"}','{"url":"/domains/**","methods":["POST","PATCH"]}' ],
        'landing-page-domain-list', 0, '2020-05-22 19:05:09.000000', '2020-05-22 19:05:11.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30002, '接口管理', 300, 3,
        array [ '{"url":"/webhooks/collect/filtering","methods":["GET"]}','{"url":"/advertiser-accounts","methods":["GET"],"type":"EXCLUDE"}','{"url":"/advertiser-account-groups","methods":["GET"],"type":"EXCLUDE"}' ],
        'message-webhook-list', 0, '2020-05-22 19:05:09.000000', '2020-05-22 19:05:11.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (30003, '支付设置', 300, 3, array [ '{"url":"/wechat-official-accounts/**","methods":["GET"]}' ], 'payment-module',
        0, '2020-05-22 19:05:09.000000', '2020-05-22 19:05:11.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000000, '添加账户', 10000, 4, array [ '{"url":"/action/auth-begin","methods":["POST"]}' ],
        'marketing-advertiser-account-add', 0, '2020-05-20 13:03:45.177149', '2020-05-20 13:03:45.177149');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000001, '设置目标', 10000, 4, array [ '{"url":"/advertiser-accounts","methods":["PATCH"]}' ],
        'marketing-advertiser-account-target', 0, '2020-05-28 03:07:55.000000', '2020-05-28 03:07:59.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000002, '分配人员', 10000, 4, array [ '{"url":"/advertiser-account-rels/**","methods":["PATCH"]}' ],
        'marketing-advertiser-account-user-rel', 0, '2020-05-28 03:09:03.000000', '2020-05-28 03:09:04.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000003, '绑定投放账户', 10000, 4, array [ '{"url":"/advertiser-accounts/batch","methods":["PATCH"]}' ],
        'marketing-advertiser-account-binding', 0, '2020-05-28 03:10:02.000000', '2020-05-28 03:10:03.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000004, '解绑', 10000, 4,
        array [ '{"url":"/advertiser-accounts/*/actions/unbinding-group","methods":["PATCH"]}' ],
        'marketing-advertiser-account-group-unbinding', 0, '2020-05-20 13:03:44.697995', '2020-05-20 13:03:44.697995');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (1000005, '添加客户分组', 10000, 4, array [ '{"url":"/advertiser-account-groups","methods":["POST"]}' ],
        'marketing-advertiser-account-group-add', 0, '2020-05-28 03:11:45.000000', '2020-05-28 03:11:46.000000');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000000, '新建/编辑员工', 20000, 4, array [ '{"url":"/users/**","methods":["POST","PATCH"]}' ],
        'ucenter-user-add-and-edit', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000001, '删除', 20000, 4, array [ '{"url":"/users/**","methods":["DELETE"]}' ], 'ucenter-user-delete', 0,
        '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000002, '密码管理', 20000, 4, array [ '{"url":"/users/**","methods":["PATCH"]}' ], 'ucenter-user-password', 0,
        '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000003, '从属管理', 20000, 4,
        array [ '{"url":"/advertiser-account-rels/relate/user-bind-advertiser-accounts","methods":["PATCH"]}' ],
        'ucenter-user-subordinate-management', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000004, '新建/编辑部门', 20000, 4, array [ '{"url":"/departments/**","methods":["POST","PATCH"]}' ],
        'ucenter-department-add-and-edit', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000005, '删除部门', 20000, 4, array [ '{"url":"/departments/**","methods":["DELETE"]}' ],
        'ucenter-department-delete', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000201, '新建/编辑', 20002, 4,
        array [ '{"url":"/permission-groups/**", "methods":["POST","PATCH","GET"]}','{"url":"/permission-groups/collect/filtering","methods":["GET"],"type":"EXCLUDE"}' ],
        'ucenter-permission-group-add-and-edit', 0, '2020-05-20 13:03:45.342967', '2020-05-20 13:03:45.342967');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (2000202, '删除', 20002, 4, array [ '{"url":"/permission-groups/**", "methods":["DELETE"]}' ],
        'ucenter-permission-group-delete', 0, '2020-05-20 13:03:45.388573', '2020-05-20 13:03:45.388573');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000101, '新建/编辑', 30001, 4,
        array [ '{"url":"/domains/**","methods":["POST","PATCH"]}','{"url":"/landing-page-domain-rels/**","methods":["GET","POST","PUT"]}' ],
        'landing-page-domain-add-and-edit', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000102, '删除', 30001, 4, array [ '{"url":"/domains/**","methods":["DELETE"]}' ], 'landing-page-domain-delete',
        0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000201, '新建/编辑', 30002, 4,
        array [ '{"url":"/webhooks/**","methods":["POST","PATCH"]}','{"url":"/webhook-advertiser-accounts/**","methods":["POST","GET"]}','{"url":"/webhooks/fetch/check-repeated","methods":["GET"]}' ],
        'message-webhook-add-and-edit', 0, '2020-05-26 09:46:16.818291', '2020-05-26 09:46:16.818291');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000202, '删除', 30002, 4, array [ '{"url":"/webhooks/**","methods":["DELETE"]}' ], 'message-webhook-delete', 0,
        '2020-05-26 09:49:24.816709', '2020-05-26 09:49:24.816709');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000301, '新建/编辑', 30003, 4,
        array [ '{"url":"/wechat-official-accounts/**","methods":["POST","PATCH"]}','{"url":"/wechat-merchant-accounts/**","methods":["POST","PATCH"]}' ],
        'payment-add-and-edit', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
INSERT INTO ucenter_permission (id, name, parent_id, level, mapping, module, t_type, created_at, updated_at)
VALUES (3000302, '删除', 30003, 4,
        array [ '{"url":"/wechat-official-accounts/**","methods":["DELETE"]}','{"url":"/wechat-merchant-accounts/**","methods":["DELETE"]}' ],
        'payment-delete', 0, '2020-05-20 13:03:45.267446', '2020-05-20 13:03:45.267446');
