

alter table marketing_data_material_type alter account_id drop not null;
alter table marketing_data_material_type add account_type int default 0;
alter table marketing_data_material_type add system_account_id bigint ;
comment on column marketing_data_material_type.account_type is '账户类型（1=cmp账户，0=EMP账户）';
COMMENT ON COLUMN marketing_data_material_type.system_account_id IS '一叶对应账户ID，cmp=客户ID，emp=投放账户对应一叶账户ID';
update marketing_data_material_type  as m set system_account_id = a.id from(select * from marketing_advertiser_account ) as a where a.account_id = m.account_id;


-- 素材分析文案与创意关联表marketing_data_material_text_creative_rel
CREATE TABLE marketing_data_material_text_creative_rel (
  id bigserial  primary key,
  copy_md5 varchar,
  creative_id bigint,
  platform_id bigint
)
;

CREATE UNIQUE INDEX idx_marketing_data_material_text_creative_rel_md5_creative_platform ON marketing_data_material_text_creative_rel  (
  copy_md5,
  creative_id,
  platform_id
);

COMMENT ON COLUMN marketing_data_material_text_creative_rel.id IS 'ID';

COMMENT ON COLUMN marketing_data_material_text_creative_rel.creative_id IS '创意ID，关联marketing_data_creative表creative_id';

COMMENT ON COLUMN marketing_data_material_text_creative_rel.platform_id IS '平台ID,1巨量引擎2腾讯广告3微信广告;';

COMMENT ON COLUMN marketing_data_material_text_creative_rel.copy_md5 IS '文案内容md5，关联表marketing_data_material_text表copy_md5';

COMMENT ON TABLE marketing_data_material_text_creative_rel IS '素材分析文案与创意关联表';


-- 素材分析文案marketing_data_material_text
CREATE TABLE marketing_data_material_text (
  id bigserial primary key,
  account_id bigint,
  platform_id bigint,
  creative_copy varchar,
  copy_md5 varchar,
  size integer,
  optimizer_id integer,
  advertiser_account_id bigint,
  created_at timestamp(6),
  updated_at timestamp(6)
)
;

CREATE UNIQUE INDEX idx_marketing_data_material_text_copy_md5_account_id_platform_id ON marketing_data_material_text  (
  copy_md5,
  account_id,
  platform_id
);


COMMENT ON COLUMN marketing_data_material_text.id IS 'ID';

COMMENT ON COLUMN marketing_data_material_text.platform_id IS '平台ID,1巨量引擎2腾讯广告3微信广告;';

COMMENT ON COLUMN marketing_data_material_text.account_id IS '投放账号';

COMMENT ON COLUMN marketing_data_material_text.advertiser_account_id IS '一叶平台投放账户id';

COMMENT ON COLUMN marketing_data_material_text.creative_copy IS '文案内容';

COMMENT ON COLUMN marketing_data_material_text.copy_md5 IS '文案内容md5';

COMMENT ON COLUMN marketing_data_material_text.size IS '文案长度';

COMMENT ON COLUMN marketing_data_material_text.optimizer_id IS '创意制作人';

COMMENT ON COLUMN marketing_data_material_text.created_at IS '创建时间';

COMMENT ON COLUMN marketing_data_material_text.updated_at IS '更新时间';

COMMENT ON TABLE marketing_data_material_text IS '素材分析文案表(媒体拉取)';


-- 创意表marketing_data_creative加字段

ALTER TABLE marketing_data_creative ADD COLUMN site   varchar[];

COMMENT ON COLUMN marketing_data_creative.site IS '版位集合';


ALTER TABLE marketing_data_creative ADD COLUMN material_text  varchar[];

COMMENT ON COLUMN marketing_data_creative.material_text IS '文案';


-- 素材拉取表marketing_data_material加字段
ALTER TABLE marketing_data_material ADD COLUMN advertiser_account_id bigint;

COMMENT ON COLUMN marketing_data_material.advertiser_account_id IS '一叶账号id';

-- 数据初始化
-- 素材库拉取初始一叶投放账户id
update marketing_data_material  as m set advertiser_account_id = a.id
from(
        select * from marketing_advertiser_account
) as a where a.account_id = m.account_id and a.platform_id = m.platform_id;

-- 创意版位初始化数据

update marketing_data_creative  as c set site = a.site
from(
        select mdc.creative_id as creative_id,
        mdc.platform_id as platform_id,
        array_agg(distinct conf.site_set) as site
        from marketing_data_creative mdc
        join marketing_data_advertise mda on mda.adcreative_id = mdc.creative_id and mda.platform_id = mdc.platform_id
        join marketing_data_adgroup mdg on mda.adgroup_id = mdg.adgroup_id
				join marketing_site_conf conf on mdg.sites  @> ARRAY[conf.id] ::Integer[] and mdg.platform_id = conf.platform_id
				group by mdc.creative_id,mdc.platform_id
) as a where a.creative_id = c.creative_id and a.platform_id = c.platform_id;
