-- 删除原
DROP TABLE IF EXISTS SynchronizationDataBaseName.landing_page;
-- 增加字段 non_ad_scene_settings,purpose,scene_settings,no_ad_scene_action,no_ad_scene_wechat_customer_service_group_id
CREATE TABLE IF NOT EXISTS landing_page
(
    id                                          Int64,
    name                                        String comment '落地页名称',
    title                                       String comment '落地页标题',
    token                                       String comment '落地页唯一标识',
    created_at                                  DateTime comment '创建时间',
    updated_at                                  DateTime comment '更新时间',
    version                                     Int32 default 0 comment '版本',
    content                                     String comment '内容',
    landing_page_type                           Int32 default 0 comment '落地页类型 0:普通h5 1:原生小程序页面',
    landing_page_group_id                       Int32 default 0 comment '分组id',
    advertiser_account_group_id                 Int64 comment '客户id',
    create_status                               Int32 default 1 comment '创建状态 0:创建中 1:创建成功 1:创建失败',
    remarks_enterprise_wechat_info              String comment '备注企微信息',
    flow_source_jump_page_status                Int32 default 0 comment '1-表示开启，0表示未开启， 是否开启来源跳转页统计标识',
    delete_status                               Int32 default 0 comment '是否逻辑删除：0-未删除  1-已删除'
) ENGINE = PostgreSQL('SynchronizationHostAndPort', 'SynchronizationDbName', 'landing_page', 'SynchronizationUsername', 'SynchronizationPassword', 'SynchronizationDataBaseName')
    comment '落地页表';
