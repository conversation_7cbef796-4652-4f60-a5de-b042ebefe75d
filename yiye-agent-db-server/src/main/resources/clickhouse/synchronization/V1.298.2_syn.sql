DROP TABLE IF EXISTS SynchronizationDataBaseName.landing_page_wechat_customer_service;

create table IF NOT EXISTS landing_page_wechat_customer_service
(
    id Int64 comment '自增主键',
    advertiser_account_group_id Int64 comment 'pmp项目id，关联表：marketing_advertiser_account_group.id',
    wechat_user_name String comment '企业微信 - 名称',
    wechat_user_id String comment '对应企业成员账号（userid）',
    qr_code_weight Int32 comment '二维码 - 权重',
    qr_code_img_url String comment '二维码图片 - 链接地址',
    online_status Int32 comment '上线状态：0-禁用  1-启用',
    created_at DateTime comment '创建时间',
    updated_at DateTime comment '更新时间',
    auto_rule_status Int32 comment '自动化规则状态',
    offline_status Int32 comment '下线状态',
    record_before_change String comment '最后一次变更前的记录',
    corp_id String comment '企业微信id，关联表字段：enterprise_wechats.corpid',
    wechat_name String comment '微信客服 - 企业微信 - 成员名称',
    wechat_mobile String comment '微信客服 - 企业微信 - 成员手机号',
    wechat_email String comment '微信客服 - 企业微信 - 邮箱',
    wechat_avatar String comment '微信客服 - 企业微信 - 成员头像url',
    plaintext_wechat_user_id String comment '明文的userid',
    wechat_customer_acquisition_link_id String comment '企微的获客链接id',
    wechat_customer_acquisition_link String comment '企微获客链接',
    wechat_customer_acquisition_link_status Int32 comment '企微获客链接状态 0:无 1:生成中 2:正常 3:异常',
    wechat_customer_acquisition_link_reason String comment '企微获客链接创建失败原因',
    landing_page_wechat_customer_contact_status Int32 comment '联系我二维码生成状态 0:未生成，1:生成中,2:已生成',
    landing_page_wechat_customer_contact_qr_code String comment '联系我 二维码链接',
    landing_page_wechat_customer_contact_config_id String comment '联系我 二维码链接配置ID-修改删除时使用',
    contact_custom_avatar String comment '联系我 二维码自定义头像',
    landing_page_wechat_customer_contact_failure_reason String comment '联系我 二维码创建失败原因',
    abnormal_monitor_status Int32 comment '异常监测状态',
    not_support_auto_online Int32 comment '异常监测触发下线后不支持自动上线',
    landing_page_wechat_customer_contact_verify Int32 comment '是否开启添加客服验证 0:否 1:是',
    wechat_customer_acquisition_link_verify Int32 comment '企微获客链接是否验证 0:是 1:否',
    official_wechat_customer_contact_status Int32 comment '公众号内企微联系我二维码生成状态 0:未生成，1:生成中,2:已生成 3:生成失败',
    official_wechat_customer_contact_qr_code String comment '公众号内渠道联系我 二维码最终合成图片url',
    official_wechat_customer_contact_background_url String comment '',
    official_wechat_customer_contact_config_id String comment '联系我 二维码链接配置ID-修改删除时使用 用于客服列表展示',
    official_wechat_customer_contact_failure_reason String comment '公众号内联系我 二维码创建失败原因',
    official_wechat_customer_contact_verify Int32 comment '公众号内是否开启添加客服验证 0:否 1:是',
    official_wechat_customer_contact_state String comment '同主体公众号渠道二维码参数',
    official_wechat_customer_contact_material_id String comment '同主体公众号渠道二维码上传素材id',
    official_wechat_customer_subject_type Int32 comment '公众号内加粉设置 0:相同主体 1:不同主体',
    official_wechat_customer_contact_app_id String comment '公众号渠道二维码绑定的公众号appid',
    official_wechat_customer_contact_app_nick_name String comment '公众号渠道二维码绑定的公众号名称',
    official_wechat_customer_contact_qiniu_path String comment '公众号内渠道联系我 二维码最终合成图片url七牛云path',
    qr_code_type Int32 comment '二维码类型[0-图片上传, 1-接口生成]',
    qr_code_verify_status Int32 comment '二维码验证状态[0-关闭, 1-开启]',
    qr_code_config_record_id Int64 comment '二维码configId',
    robot_customer_contact_status Int32 comment '渠道二维码（微信客服机器人内加粉）生成状态：0:未生成，1:生成中,2:已生成,3:创建失败',
    robot_customer_contact_qr_code String comment '渠道二维码（微信客服机器人内加粉）链接',
    robot_customer_contact_background_url String comment '渠道二维码（微信客服机器人内加粉）最终合成图片url',
    robot_customer_contact_qiniu_path String comment '渠道二维码（微信客服机器人内加粉）最终合成图片七牛云path',
    robot_customer_contact_config_id String comment '渠道二维码（微信客服机器人内加粉）链接配置ID-修改删除时使用',
    robot_customer_contact_failure_reason String comment '渠道二维码（微信客服机器人内加粉）创建失败原因',
    robot_customer_contact_verify Int32 comment '渠道二维码（微信客服机器人内加粉）添加客服是否验证',
    robot_customer_contact_state String comment '渠道二维码（微信客服机器人内加粉）参数',
    robot_customer_contact_material_id Int64 comment '渠道二维码（微信客服机器人内加粉）上传素材id',
    online_action_status Int32 comment '上线行为状态',
    support_auto_online_status Int32 comment '下线后支持自动化上线状态 0:关闭 1:开启',
    support_auto_online Int32 comment '下线后支持自动化上线',
    specify_online_time String comment '指定时间点上线',
    department_id Array(Int64) DEFAULT []  comment '部门id',
    department_name Array(String) DEFAULT [] comment '部门名称',
    offline_time DateTime comment '下线时间',
    license_query_time DateTime comment '接口状态最近查询时间',
    license_status Int32 comment '接口许可状态',
    license_expire_time DateTime comment '接口许可到期时间',
    robot_customer_dynamic_contact_status    Int32     comment  '动态渠道二维码（微信客服机器人内加粉）生成状态：0:未生成，1:生成中,2:已生成,3:创建失败',
    robot_customer_dynamic_contact_qr_code   String    comment  '动态渠道二维码（微信客服机器人内加粉）链接',
    robot_customer_dynamic_contact_background_url     String    comment  '动态渠道二维码（微信客服机器人内加粉）最终合成图片url',
    robot_customer_dynamic_contact_qiniu_path         String    comment  '动态渠道二维码（微信客服机器人内加粉）最终合成图片七牛云path',
    robot_customer_dynamic_contact_config_id          String    comment  '动态渠道二维码（微信客服机器人内加粉）链接配置ID-修改删除时使用',
    robot_customer_dynamic_contact_failure_reason     String    comment  '动态渠道二维码（微信客服机器人内加粉）创建失败原因',
    robot_customer_dynamic_contact_verify             String    comment  '动态渠道二维码（微信客服机器人内加粉）添加客服是否验证',
    robot_customer_dynamic_contact_state              String    comment  '动态动态渠道二维码（微信客服机器人内加粉）参数',
    robot_customer_dynamic_contact_material_id        String    comment  '动态渠道二维码（微信客服机器人内加粉）上传素材id'
) engine = PostgreSQL(
                         'SynchronizationHostAndPort', 'SynchronizationDbName', 'landing_page_wechat_customer_service', 'SynchronizationUsername', 'SynchronizationPassword', 'SynchronizationDataBaseName'
                     ) COMMENT '落地页 - 微信客服管理 - 表';
