create table if not exists marketing_advertiser_account_agent_rel
(
    id                      bigserial primary key,
    agent_id                varchar not null,
    account_id              varchar not null,
    platform_id             int not null,
    created_at              timestamp default now() not null,
    updated_at              timestamp default now() not null
);
comment on column marketing_advertiser_account_agent_rel.agent_id is 'agentId';
comment on column marketing_advertiser_account_agent_rel.account_id is '投放账户id';
comment on column marketing_advertiser_account_agent_rel.platform_id is '平台id';
create unique index marketing_advertiser_account_agent_rel_unique_index on marketing_advertiser_account_agent_rel (account_id, platform_id, agent_id);

create table if not exists advertise_backlink_statistic_report
(
    id                                          bigserial primary key,
    platform_id                                 int not null,
    promotion_id                                varchar not null,
    project_id                                  varchar not null,
    statistic_time                              timestamp not null,
    statistic_caliber                           int not null,
    page_view_num                               bigint not null,
    fill_count_num                              bigint not null,
    fill_count_rate                             float not null,
    order_num                                   bigint not null,
    order_count_rate                            float not null,
    order_finish_num                            bigint not null,
    order_finish_rate                           float not null,
    order_transaction_amount                    float not null,
    identify_qr_code_num                        bigint not null,
    identify_qr_code_rate                       float not null,
    add_work_wechat_num                         bigint not null,
    add_work_wechat_rate                        float not null,
    official_identify_qr_code_num               bigint not null,
    official_identify_qr_code_rate              float not null,
    follow_official_account_num                 bigint not null,
    follow_official_account_rate                float not null,
    online_shop_buy_goods_success_num           bigint not null,
    online_shop_buy_goods_success_rate          float not null,
    online_shop_buy_goods_success_amount        float not null,
    created_at                                  timestamp default now() not null,
    updated_at                                  timestamp default now() not null
);
create unique index advertise_backlink_statistic_report_unique_index on advertise_backlink_statistic_report (project_id, promotion_id, statistic_time, statistic_caliber, platform_id);
create index if not exists advertise_backlink_statistic_report_project_id_index on advertise_backlink_statistic_report (project_id);
create index if not exists advertise_backlink_statistic_report_platform_id_index on advertise_backlink_statistic_report (platform_id);
create index if not exists advertise_backlink_statistic_report_promotion_id_index on advertise_backlink_statistic_report (promotion_id);
create index if not exists advertise_backlink_statistic_report_statistic_time_index on advertise_backlink_statistic_report (statistic_time);
create index if not exists advertise_backlink_statistic_report_statistic_caliber_index on advertise_backlink_statistic_report (statistic_caliber);
comment on column advertise_backlink_statistic_report.platform_id is '投放平台';
comment on column advertise_backlink_statistic_report.promotion_id is '广告id';
comment on column advertise_backlink_statistic_report.project_id is '广告项目ID';
comment on column advertise_backlink_statistic_report.statistic_time is '统计时间';
comment on column advertise_backlink_statistic_report.page_view_num is 'pv次数';
comment on column advertise_backlink_statistic_report.fill_count_num is '表单提交数';
comment on column advertise_backlink_statistic_report.fill_count_rate is '表单提交率';
comment on column advertise_backlink_statistic_report.order_num is '订单提交数';
comment on column advertise_backlink_statistic_report.order_count_rate is '订单提交率';
comment on column advertise_backlink_statistic_report.order_finish_num is '订单完成数';
comment on column advertise_backlink_statistic_report.order_finish_rate is '订单完成率';
comment on column advertise_backlink_statistic_report.order_transaction_amount is '订单成交金额';
comment on column advertise_backlink_statistic_report.identify_qr_code_num is '长按二维码识别数(微信/企业微信)';
comment on column advertise_backlink_statistic_report.identify_qr_code_rate is '长按二维码识别率(微信/企业微信)';
comment on column advertise_backlink_statistic_report.add_work_wechat_num is '成功添加企业微信数';
comment on column advertise_backlink_statistic_report.add_work_wechat_rate is '成功添加企业微信率';
comment on column advertise_backlink_statistic_report.official_identify_qr_code_num is '长按二维码识别数(公众号)';
comment on column advertise_backlink_statistic_report.official_identify_qr_code_rate is '长按二维码识别率(公众号)';
comment on column advertise_backlink_statistic_report.follow_official_account_num is '微信公众号关注数';
comment on column advertise_backlink_statistic_report.follow_official_account_rate is '微信公众号关注率';
comment on column advertise_backlink_statistic_report.online_shop_buy_goods_success_num is '电商商品购买数';
comment on column advertise_backlink_statistic_report.online_shop_buy_goods_success_rate is '电商商品购买率';
comment on column advertise_backlink_statistic_report.online_shop_buy_goods_success_amount is '电商商品成交金额';
comment on column advertise_backlink_statistic_report.statistic_caliber is '统计口径';

create table if not exists advertise_statistic_report
(
    id                                          bigserial primary key,
    platform_id                                 int not null,
    statistic_caliber                           int not null,
    statistic_time                              timestamp not null,
    account_id                                  varchar not null,
    project_id                                  varchar not null,
    promotion_id                                varchar not null,
    cost                                        float not null,
    view_num                                    bigint not null,
    thousand_impress_avg_price                  float not null,
    click_num                                   bigint not null,
    click_rate                                  float not null,
    avg_price                                   float not null,
    convert_num                                 bigint not null,
    target_convert_rate                         float not null,
    target_convert_cost                         float not null,
    deep_convert_num                            bigint not null,
    deep_convert_rate                           float not null,
    deep_convert_cost                           float not null,
    page_view_num                               bigint not null,
    fill_count_num                              bigint not null,
    fill_count_rate                             float not null,
    order_num                                   bigint not null,
    order_count_rate                            float not null,
    order_finish_num                            bigint not null,
    order_finish_rate                           float not null,
    order_transaction_amount                    float not null,
    identify_qr_code_num                        bigint not null,
    identify_qr_code_rate                       float not null,
    add_work_wechat_num                         bigint not null,
    add_work_wechat_rate                        float not null,
    official_identify_qr_code_num               bigint not null,
    official_identify_qr_code_rate              float not null,
    follow_official_account_num                 bigint not null,
    follow_official_account_rate                float not null,
    online_shop_buy_goods_success_num           bigint not null,
    online_shop_buy_goods_success_rate          float not null,
    online_shop_buy_goods_success_amount        float not null,
    created_at                                  timestamp default now() not null,
    updated_at                                  timestamp default now() not null
);
create unique index if not exists advertise_statistic_report_unique_index on advertise_statistic_report (account_id, project_id, promotion_id, statistic_time, statistic_caliber, platform_id);
create index if not exists advertise_statistic_report_statistic_time_index on advertise_statistic_report (statistic_time);
create index if not exists advertise_statistic_report_account_id_index on advertise_statistic_report (account_id);
create index if not exists advertise_statistic_report_statistic_caliber_index on advertise_statistic_report (statistic_caliber);
create index if not exists advertise_statistic_report_platform_id_index on advertise_statistic_report (platform_id);
create index if not exists advertise_statistic_report_promotion_id_index on advertise_statistic_report (promotion_id);
create index if not exists advertise_statistic_report_project_id_index on advertise_statistic_report (project_id);
comment on column advertise_statistic_report.account_id is '账户id';
comment on column advertise_statistic_report.project_id is '项目id';
comment on column advertise_statistic_report.cost is '消耗';
comment on column advertise_statistic_report.view_num is '展示数';
comment on column advertise_statistic_report.thousand_impress_avg_price is '平均千次展现费用';
comment on column advertise_statistic_report.click_num is '点击数';
comment on column advertise_statistic_report.click_rate is '点击率';
comment on column advertise_statistic_report.avg_price is '平均点击单价';
comment on column advertise_statistic_report.convert_num is '转化数';
comment on column advertise_statistic_report.target_convert_rate is '转化率';
comment on column advertise_statistic_report.target_convert_cost is '平均转化成本';
comment on column advertise_statistic_report.deep_convert_num is '深度转化数';
comment on column advertise_statistic_report.deep_convert_rate is '深度转化率';
comment on column advertise_statistic_report.deep_convert_cost is '深度转化成本';
comment on column advertise_statistic_report.platform_id is '投放平台';
comment on column advertise_statistic_report.promotion_id is '广告id';
comment on column advertise_statistic_report.statistic_time is '统计时间';
comment on column advertise_statistic_report.page_view_num is 'pv次数';
comment on column advertise_statistic_report.fill_count_num is '表单提交数';
comment on column advertise_statistic_report.fill_count_rate is '表单提交率';
comment on column advertise_statistic_report.order_num is '订单提交数';
comment on column advertise_statistic_report.order_count_rate is '订单提交率';
comment on column advertise_statistic_report.order_finish_num is '订单完成数';
comment on column advertise_statistic_report.order_finish_rate is '订单完成率';
comment on column advertise_statistic_report.order_transaction_amount is '订单成交金额';
comment on column advertise_statistic_report.identify_qr_code_num is '长按二维码识别数(微信/企业微信)';
comment on column advertise_statistic_report.identify_qr_code_rate is '长按二维码识别率(微信/企业微信)';
comment on column advertise_statistic_report.add_work_wechat_num is '成功添加企业微信数';
comment on column advertise_statistic_report.add_work_wechat_rate is '成功添加企业微信率';
comment on column advertise_statistic_report.official_identify_qr_code_num is '长按二维码识别数(公众号)';
comment on column advertise_statistic_report.official_identify_qr_code_rate is '长按二维码识别率(公众号)';
comment on column advertise_statistic_report.follow_official_account_num is '微信公众号关注数';
comment on column advertise_statistic_report.follow_official_account_rate is '微信公众号关注率';
comment on column advertise_statistic_report.online_shop_buy_goods_success_num is '电商商品购买数';
comment on column advertise_statistic_report.online_shop_buy_goods_success_rate is '电商商品购买率';
comment on column advertise_statistic_report.online_shop_buy_goods_success_amount is '电商商品成交金额';
comment on column advertise_statistic_report.statistic_caliber is '统计口径';



create table if not exists advertise_backlink_statistic_report_new
(
    id                                          bigserial primary key,
    platform_id                                 int not null,
    promotion_id                                varchar not null,
    project_id                                  varchar not null,
    statistic_time                              timestamp not null,
    statistic_caliber                           int not null,
    page_view_num                               bigint not null,
    fill_count_num                              bigint not null,
    fill_count_rate                             float not null,
    order_num                                   bigint not null,
    order_count_rate                            float not null,
    order_finish_num                            bigint not null,
    order_finish_rate                           float not null,
    order_transaction_amount                    float not null,
    identify_qr_code_num                        bigint not null,
    identify_qr_code_rate                       float not null,
    add_work_wechat_num                         bigint not null,
    add_work_wechat_rate                        float not null,
    official_identify_qr_code_num               bigint not null,
    official_identify_qr_code_rate              float not null,
    follow_official_account_num                 bigint not null,
    follow_official_account_rate                float not null,
    online_shop_buy_goods_success_num           bigint not null,
    online_shop_buy_goods_success_rate          float not null,
    online_shop_buy_goods_success_amount        float not null,
    created_at                                  timestamp default now() not null,
    updated_at                                  timestamp default now() not null
);
create unique index advertise_backlink_statistic_report_new_unique_index on advertise_backlink_statistic_report_new (project_id, promotion_id, statistic_time, statistic_caliber, platform_id);
create index if not exists advertise_backlink_statistic_report_new_platform_id_index on advertise_backlink_statistic_report_new (platform_id);
create index if not exists advertise_backlink_statistic_report_new_promotion_id_index on advertise_backlink_statistic_report_new (promotion_id);
create index if not exists advertise_backlink_statistic_report_new_statistic_time_index on advertise_backlink_statistic_report_new (statistic_time);
create index if not exists advertise_backlink_statistic_report_new_statistic_caliber_index on advertise_backlink_statistic_report_new (statistic_caliber);
create index if not exists advertise_backlink_statistic_report_new_project_id_index on advertise_backlink_statistic_report_new (project_id);
comment on column advertise_backlink_statistic_report_new.platform_id is '投放平台';
comment on column advertise_backlink_statistic_report_new.promotion_id is '广告id';
comment on column advertise_backlink_statistic_report_new.project_id is '广告项目ID';
comment on column advertise_backlink_statistic_report_new.statistic_time is '统计时间';
comment on column advertise_backlink_statistic_report_new.page_view_num is 'pv次数';
comment on column advertise_backlink_statistic_report_new.fill_count_num is '表单提交数';
comment on column advertise_backlink_statistic_report_new.fill_count_rate is '表单提交率';
comment on column advertise_backlink_statistic_report_new.order_num is '订单提交数';
comment on column advertise_backlink_statistic_report_new.order_count_rate is '订单提交率';
comment on column advertise_backlink_statistic_report_new.order_finish_num is '订单完成数';
comment on column advertise_backlink_statistic_report_new.order_finish_rate is '订单完成率';
comment on column advertise_backlink_statistic_report_new.order_transaction_amount is '订单成交金额';
comment on column advertise_backlink_statistic_report_new.identify_qr_code_num is '长按二维码识别数(微信/企业微信)';
comment on column advertise_backlink_statistic_report_new.identify_qr_code_rate is '长按二维码识别率(微信/企业微信)';
comment on column advertise_backlink_statistic_report_new.add_work_wechat_num is '成功添加企业微信数';
comment on column advertise_backlink_statistic_report_new.add_work_wechat_rate is '成功添加企业微信率';
comment on column advertise_backlink_statistic_report_new.official_identify_qr_code_num is '长按二维码识别数(公众号)';
comment on column advertise_backlink_statistic_report_new.official_identify_qr_code_rate is '长按二维码识别率(公众号)';
comment on column advertise_backlink_statistic_report_new.follow_official_account_num is '微信公众号关注数';
comment on column advertise_backlink_statistic_report_new.follow_official_account_rate is '微信公众号关注率';
comment on column advertise_backlink_statistic_report_new.online_shop_buy_goods_success_num is '电商商品购买数';
comment on column advertise_backlink_statistic_report_new.online_shop_buy_goods_success_rate is '电商商品购买率';
comment on column advertise_backlink_statistic_report_new.online_shop_buy_goods_success_amount is '电商商品成交金额';
comment on column advertise_backlink_statistic_report_new.statistic_caliber is '统计口径';

create table if not exists advertise_statistic_report_new
(
    id                                          bigserial primary key,
    platform_id                                 int not null,
    statistic_caliber                           int not null,
    statistic_time                              timestamp not null,
    account_id                                  varchar not null,
    project_id                                  varchar not null,
    promotion_id                                varchar not null,
    cost                                        float not null,
    view_num                                    bigint not null,
    thousand_impress_avg_price                  float not null,
    click_num                                   bigint not null,
    click_rate                                  float not null,
    avg_price                                   float not null,
    convert_num                                 bigint not null,
    target_convert_rate                         float not null,
    target_convert_cost                         float not null,
    deep_convert_num                            bigint not null,
    deep_convert_rate                           float not null,
    deep_convert_cost                           float not null,
    page_view_num                               bigint not null,
    fill_count_num                              bigint not null,
    fill_count_rate                             float not null,
    order_num                                   bigint not null,
    order_count_rate                            float not null,
    order_finish_num                            bigint not null,
    order_finish_rate                           float not null,
    order_transaction_amount                    float not null,
    identify_qr_code_num                        bigint not null,
    identify_qr_code_rate                       float not null,
    add_work_wechat_num                         bigint not null,
    add_work_wechat_rate                        float not null,
    official_identify_qr_code_num               bigint not null,
    official_identify_qr_code_rate              float not null,
    follow_official_account_num                 bigint not null,
    follow_official_account_rate                float not null,
    online_shop_buy_goods_success_num           bigint not null,
    online_shop_buy_goods_success_rate          float not null,
    online_shop_buy_goods_success_amount        float not null,
    created_at                                  timestamp default now() not null,
    updated_at                                  timestamp default now() not null
);

create unique index if not exists advertise_statistic_report_new_unique_index on advertise_statistic_report_new (account_id, project_id, promotion_id, statistic_time, statistic_caliber, platform_id);
create index if not exists advertise_statistic_report_new_statistic_time_index on advertise_statistic_report_new (statistic_time);
create index if not exists advertise_statistic_report_new_account_id_index on advertise_statistic_report_new (account_id);
create index if not exists advertise_statistic_report_new_statistic_caliber_index on advertise_statistic_report_new (statistic_caliber);
create index if not exists advertise_statistic_report_new_platform_id_index on advertise_statistic_report_new (platform_id);
create index if not exists advertise_statistic_report_new_promotion_id_index on advertise_statistic_report_new (promotion_id);
create index if not exists advertise_statistic_report_new_project_id_index on advertise_statistic_report_new (project_id);

comment on column advertise_statistic_report_new.account_id is '账户id';
comment on column advertise_statistic_report_new.project_id is '项目id';
comment on column advertise_statistic_report_new.cost is '消耗';
comment on column advertise_statistic_report_new.view_num is '展示数';
comment on column advertise_statistic_report_new.thousand_impress_avg_price is '平均千次展现费用';
comment on column advertise_statistic_report_new.click_num is '点击数';
comment on column advertise_statistic_report_new.click_rate is '点击率';
comment on column advertise_statistic_report_new.avg_price is '平均点击单价';
comment on column advertise_statistic_report_new.convert_num is '转化数';
comment on column advertise_statistic_report_new.target_convert_rate is '转化率';
comment on column advertise_statistic_report_new.target_convert_cost is '平均转化成本';
comment on column advertise_statistic_report_new.deep_convert_num is '深度转化数';
comment on column advertise_statistic_report_new.deep_convert_rate is '深度转化率';
comment on column advertise_statistic_report_new.deep_convert_cost is '深度转化成本';
comment on column advertise_statistic_report_new.platform_id is '投放平台';
comment on column advertise_statistic_report_new.promotion_id is '广告id';
comment on column advertise_statistic_report_new.statistic_time is '统计时间';
comment on column advertise_statistic_report_new.page_view_num is 'pv次数';
comment on column advertise_statistic_report_new.fill_count_num is '表单提交数';
comment on column advertise_statistic_report_new.fill_count_rate is '表单提交率';
comment on column advertise_statistic_report_new.order_num is '订单提交数';
comment on column advertise_statistic_report_new.order_count_rate is '订单提交率';
comment on column advertise_statistic_report_new.order_finish_num is '订单完成数';
comment on column advertise_statistic_report_new.order_finish_rate is '订单完成率';
comment on column advertise_statistic_report_new.order_transaction_amount is '订单成交金额';
comment on column advertise_statistic_report_new.identify_qr_code_num is '长按二维码识别数(微信/企业微信)';
comment on column advertise_statistic_report_new.identify_qr_code_rate is '长按二维码识别率(微信/企业微信)';
comment on column advertise_statistic_report_new.add_work_wechat_num is '成功添加企业微信数';
comment on column advertise_statistic_report_new.add_work_wechat_rate is '成功添加企业微信率';
comment on column advertise_statistic_report_new.official_identify_qr_code_num is '长按二维码识别数(公众号)';
comment on column advertise_statistic_report_new.official_identify_qr_code_rate is '长按二维码识别率(公众号)';
comment on column advertise_statistic_report_new.follow_official_account_num is '微信公众号关注数';
comment on column advertise_statistic_report_new.follow_official_account_rate is '微信公众号关注率';
comment on column advertise_statistic_report_new.online_shop_buy_goods_success_num is '电商商品购买数';
comment on column advertise_statistic_report_new.online_shop_buy_goods_success_rate is '电商商品购买率';
comment on column advertise_statistic_report_new.online_shop_buy_goods_success_amount is '电商商品成交金额';
comment on column advertise_statistic_report_new.statistic_caliber is '统计口径';
