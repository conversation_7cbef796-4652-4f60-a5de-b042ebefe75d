-- 落地页自定义列
update marketing_customer_field set field_no=9 where name ='微信公众号关注数' and default_data =0 and type =9 and setting_category ='加粉数据';
update marketing_customer_field set field_no=10 where name ='微信公众号关注率' and default_data =0 and type =9 and setting_category ='加粉数据';
update marketing_customer_field set field_no=11 where name ='长按二维码识别数(公众号)' and default_data =0 and type =9 and setting_category ='加粉数据';
update marketing_customer_field set field_no=12 where name ='长按二维码识别率(公众号)' and default_data =0 and type =9 and setting_category ='加粉数据';
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 9, 7, NULL, '加粉数据', 't', 'identifyGroupQrCodeNum', '长按二维码识别数（企业微信群）', '', NULL, now( ), now( ), 0, 0, 0, NULL, NULL, 10, 5, '页面内企业微信群二维码被长按识别的次数', 't', 200, 112, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 9, 7, NULL, '加粉数据', 't', 'identifyGroupQrCodeRate', '长按二维码识别率（企业微信群）', '%', NULL, now( ), now( ), 0, 0, 0, NULL, NULL, 10, 6, '长按企业微信群二维码占浏览数的比例  计算公式：加企业微信群数/落地页PV数*100%', 't', 200, 112, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 9, 7, NULL, '加粉数据', 't', 'addWorkWechatGroupNum', '加企业微信群数', '', NULL, now( ), now( ), 0, 0, 0, NULL, NULL, 10, 7, '页面内成功添加企业微信群数', 't', 200, 112, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 9, 7, NULL, '加粉数据', 't', 'addWorkWechatGroupRate', '加企业微信群率', '%', NULL, now( ), now( ), 0, 0, 0, NULL, NULL, 10, 8, '加企业微信群数占浏览数的比例  计算公式：加企业微信群数/落地页PV数*100%', 't', 200, 112, 0 );
update marketing_customer_field set field_interpretation ='页面内微信或企业微信二维码被长按识别的次数 ，使用动态二维码 - 添加二维码图片中的二维码被长按识别次数的统计数归至该字段展示' where name ='长按二维码识别数(微信/企业微信)' and type =9;
-- 渠道自定义列
update marketing_customer_field set field_no=9 where name ='微信公众号关注数' and default_data =0 and type =10 and setting_category ='加粉数据';
update marketing_customer_field set field_no=10 where name ='微信公众号关注率' and default_data =0 and type =10 and setting_category ='加粉数据';
update marketing_customer_field set field_no=11 where name ='长按二维码识别数(公众号)' and default_data =0 and type =10 and setting_category ='加粉数据';
update marketing_customer_field set field_no=12 where name ='长按二维码识别率(公众号)' and default_data =0 and type =10 and setting_category ='加粉数据';
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 10, 7, NULL, '加粉数据', 't', 'identifyGroupQrCodeNum', '长按二维码识别数（企业微信群）', '', NULL, now( ), now( ), 0, 0, 0, NULL, NULL, 10, 5, '页面内企业微信群二维码被长按识别的次数', 't', 200, 112, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 10, 7, NULL, '加粉数据', 't', 'identifyGroupQrCodeRate', '长按二维码识别率（企业微信群）', '%', NULL, now( ), now( ), 0, 0, 0, NULL, NULL, 10, 6, '长按企业微信群二维码占浏览数的比例  计算公式：加企业微信群数/落地页PV数*100%', 't', 200, 112, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 10, 7, NULL, '加粉数据', 't', 'addWorkWechatGroupNum', '加企业微信群数', '', NULL, now( ), now( ), 0, 0, 0, NULL, NULL, 10, 7, '页面内成功添加企业微信群数', 't', 200, 112, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 10, 7, NULL, '加粉数据', 't', 'addWorkWechatGroupRate', '加企业微信群率', '%', NULL, now( ), now( ), 0, 0, 0, NULL, NULL, 10, 8, '加企业微信群数占浏览数的比例  计算公式：加企业微信群数/落地页PV数*100%', 't', 200, 112, 0 );
update marketing_customer_field set field_interpretation ='页面内微信或企业微信二维码被长按识别的次数 ，使用动态二维码 - 添加二维码图片中的二维码被长按识别次数的统计数归至该字段展示' where name ='长按二维码识别数(微信/企业微信)' and type =10;
-- 账户级别自定义列加字段
update marketing_customer_field set field_no=field_no+7 where type =4 and default_data=0 and field_no>=22 and sub_category='基础数据(中台)';
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 4, 0, '基础数据(中台)', '行为转化', 'f', 'identifyGroupQrCodeNum', '长按二维码识别数（企业微信群）', NULL, NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 22, '一叶落地页长按二维码识别数（企业微信群）', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 4, 0, '基础数据(中台)', '行为转化', 'f', 'identifyGroupQrCodeRate', '长按二维码识别率（企业微信群）', '%', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 23, '长按二维码识别数（企业微信群）/落地页PV*100%,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 4, 0, '基础数据(中台)', '行为转化', 'f', 'identifyGroupQrCodeCost', '长按二维码识别成本（企业微信群）', '元', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 24, '花费/长按二维码识别数（企业微信群）,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 4, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupNum', '加企业微信群数', NULL, NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 25, '一叶落地页成功企业微信群数', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 4, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupRate', '加企业微信群率', '%', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 26, '加企业微信群数/落地页PV*100%,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 4, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupIncomingRate', '加企业微信群进线率', '%', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 27, '加企业微信群数/转化数*100%,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 4, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupCost', '加企业微信群成本', '元', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 28, '花费/加企业微信群数,保留小数点后两位', 't', 240, 220, 0 );
-- 广告组级别自定义列加字段
update marketing_customer_field set field_no=field_no+7 where type =5 and default_data=0 and field_no>=22 and sub_category='基础数据(中台)';
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 5, 0, '基础数据(中台)', '行为转化', 'f', 'identifyGroupQrCodeNum', '长按二维码识别数（企业微信群）', NULL, NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 22, '一叶落地页长按二维码识别数（企业微信群）', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 5, 0, '基础数据(中台)', '行为转化', 'f', 'identifyGroupQrCodeRate', '长按二维码识别率（企业微信群）', '%', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 23, '长按二维码识别数（企业微信群）/落地页PV*100%,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 5, 0, '基础数据(中台)', '行为转化', 'f', 'identifyGroupQrCodeCost', '长按二维码识别成本（企业微信群）', '元', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 24, '花费/长按二维码识别数（企业微信群）,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 5, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupNum', '加企业微信群数', NULL, NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 25, '一叶落地页成功企业微信群数', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 5, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupRate', '加企业微信群率', '%', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 26, '加企业微信群数/落地页PV*100%,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 5, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupIncomingRate', '加企业微信群进线率', '%', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 27, '加企业微信群数/转化数*100%,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 5, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupCost', '加企业微信群成本', '元', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 28, '花费/加企业微信群数,保留小数点后两位', 't', 240, 220, 0 );
-- 计划级别自定义列加字段
update marketing_customer_field set field_no=field_no+7 where type =6 and default_data=0 and field_no>=22 and sub_category='基础数据(中台)';
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 6, 0, '基础数据(中台)', '行为转化', 'f', 'identifyGroupQrCodeNum', '长按二维码识别数（企业微信群）', NULL, NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 22, '一叶落地页长按二维码识别数（企业微信群）', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 6, 0, '基础数据(中台)', '行为转化', 'f', 'identifyGroupQrCodeRate', '长按二维码识别率（企业微信群）', NULL, NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 23, '长按二维码识别数（企业微信群）/落地页PV*100%,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 6, 0, '基础数据(中台)', '行为转化', 'f', 'identifyGroupQrCodeCost', '长按二维码识别成本（企业微信群）', '元', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 24, '花费/长按二维码识别数（企业微信群）,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 6, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupNum', '加企业微信群数', NULL, NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 25, '一叶落地页成功企业微信群数', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 6, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupRate', '加企业微信群率', '%', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 26, '加企业微信群数/落地页PV*100%,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 6, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupIncomingRate', '加企业微信群进线率', '%', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 27, '加企业微信群数/转化数*100%,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 6, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupCost', '加企业微信群成本', '元', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 28, '花费/加企业微信群数,保留小数点后两位', 't', 240, 220, 0 );
-- 项目概况自定义列增加字段
update marketing_customer_field set field_no=14 where type =0 and default_data=0 and field='addWorkWechatNum'  and sub_category='基础指标(中台)';
update marketing_customer_field set field_no=15 where type =0 and default_data=0 and field='addWorkWechatRate'  and sub_category='基础指标(中台)';
update marketing_customer_field set field_no=16 where type =0 and default_data=0 and field='addWorkWechatCost'  and sub_category='基础指标(中台)';
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 0, 0, '基础指标(中台)', '行为转化', 'f', 'identifyGroupQrCodeNum', '长按二维码识别数（企业微信群）', NULL, NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 17, '一叶落地页长按二维码识别数（企业微信群)', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 0, 0, '基础指标(中台)', '行为转化', 'f', 'identifyGroupQrCodeRate', '长按二维码识别率（企业微信群）', '%', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 18, '长按二维码识别数（企业微信群）/落地页PV*100%,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 0, 0, '基础指标(中台)', '行为转化', 'f', 'identifyGroupQrCodeCost', '长按二维码识别成本（企业微信群）', '元', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 19, '长按二维码识别数（企业微信群）/落地页PV*100%,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 0, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupNum', '加企业微信群数', NULL, NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 20, '一叶落地页成功企业微信群数', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 0, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupRate', '加企业微信群率', '%', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 21, '加企业微信群数/落地页PV*100%,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES ( 0, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatGroupCost', '加企业微信群成本', '元', NULL, now(), now(), 0, 0, 0, '{0}', 0, 2, 22, '花费/加企业微信群数,保留小数点后两位', 't', 240, 220, 0 );
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") values(  0, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeNum', '长按二维码识别数(公众号)', null, null, now(), now(), 0, 0, 0, null, null, 0, 23, '一叶落地页长按二维码识别数（公众号）', true, 240, 220,0);
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") values(  0, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeRate', '长按二维码识别率(公众号)', '%', null, now(), now(), 0, 0, 0, null, null, 0, 24, '长按二维码识别数（公众号）/落地页PV*100%,保留小数点后两位', true, 240, 220,0);
INSERT INTO "marketing_customer_field"( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") values(  0, 0, '基础数据(中台)', '行为转化', 'f', 'enterpriseIncreaseIncomingCost', '长按二维码识别成本(公众号)', '元', null, now(), now(), 0, 0, 0, null, null, 0, 25, '花费/长按二维码识别数（公众号）,保留小数点后两位', true, 240, 220,0);
update marketing_customer_field set field_no=26 where type =0 and default_data=0 and field='followOfficialAccountNum'  and sub_category='基础指标(中台)';
update marketing_customer_field set field_no=27 where type =0 and default_data=0 and field='followOfficialAccountRate'  and sub_category='基础指标(中台)';
update marketing_customer_field set field_no=28 where type =0 and default_data=0 and field='followOfficialAccountCost'  and sub_category='基础指标(中台)';
update marketing_customer_field set field_no=29 where type =0 and default_data=0 and field='onlineShopBuyGoodsSuccessNum'  and sub_category='基础指标(中台)';
update marketing_customer_field set field_no=30 where type =0 and default_data=0 and field='onlineShopBuyGoodsSuccessRate'  and sub_category='基础指标(中台)';
update marketing_customer_field set field_no=31 where type =0 and default_data=0 and field='onlineShopBuyGoodsSuccessAmount'  and sub_category='基础指标(中台)';
update marketing_customer_field set field_no=32 where type =0 and default_data=0 and field='onlineShopBuyGoodsSuccessCost'  and sub_category='基础指标(中台)';
update marketing_customer_field set field_no=33 where type =0 and default_data=0 and field='onlineShopBuyGoodsSuccessRoi'  and sub_category='基础指标(中台)';
-- 数据概况
update validity_summary_field set no=no+9 where type =3 and user_id =0 and no>=154;
INSERT INTO validity_summary_field ("type", "no", "field", "name", "color", "unit", "created_at", "updated_at", "user_id", "formula_id", "value_color", "bg_color") VALUES ( 3, 154, 'identifyGroupQrCodeNum', '长按二维码识别数（企业微信群）', '#333333', '', now(), now(), 0, NULL, '#51C75B', 'rgba(250, 250, 250, 1)');
INSERT INTO validity_summary_field ("type", "no", "field", "name", "color", "unit", "created_at", "updated_at", "user_id", "formula_id", "value_color", "bg_color") VALUES ( 3, 155, 'identifyGroupQrCodeRate', '长按二维码识别率（企业微信群）', '#333333', '%', now(), now(), 0, NULL, '#51C75B', 'rgba(24, 144, 255, 0.0588235294117647)');
INSERT INTO validity_summary_field ("type", "no", "field", "name", "color", "unit", "created_at", "updated_at", "user_id", "formula_id", "value_color", "bg_color") VALUES ( 3, 156, 'identifyGroupQrCodeCost', '长按二维码识别成本（企业微信群）', '#333333', '', now(), now(), 0, NULL, '#51C75B', 'rgba(81, 199, 91, 0.0588235294117647)');
INSERT INTO validity_summary_field ("type", "no", "field", "name", "color", "unit", "created_at", "updated_at", "user_id", "formula_id", "value_color", "bg_color") VALUES ( 3, 157, 'addWorkWechatGroupNum', '加企业微信群数', '#333333', '', now(), now(), 0, NULL, '#51C75B', 'rgba(250, 250, 250, 1)');
INSERT INTO validity_summary_field ("type", "no", "field", "name", "color", "unit", "created_at", "updated_at", "user_id", "formula_id", "value_color", "bg_color") VALUES ( 3, 158, 'addWorkWechatGroupRate', '加企业微信群率', '#333333', '%', now(), now(), 0, NULL, '#51C75B', 'rgba(24, 144, 255, 0.0588235294117647)');
INSERT INTO validity_summary_field ("type", "no", "field", "name", "color", "unit", "created_at", "updated_at", "user_id", "formula_id", "value_color", "bg_color") VALUES ( 3, 159, 'addWorkWechatGroupCost', '加企业微信群成本', '#333333', '', now(), now(), 0, NULL, '#51C75B', 'rgba(81, 199, 91, 0.0588235294117647)');
INSERT INTO validity_summary_field ("type", "no", "field", "name", "color", "unit", "created_at", "updated_at", "user_id", "formula_id", "value_color", "bg_color") VALUES ( 3, 160, 'officialIdentifyQrCodeNum', '长按二维码识别数（公众号）', '#333333', '', now(), now(), 0, NULL, '#51C75B', 'rgba(250, 250, 250, 1)');
INSERT INTO validity_summary_field ("type", "no", "field", "name", "color", "unit", "created_at", "updated_at", "user_id", "formula_id", "value_color", "bg_color") VALUES ( 3, 161, 'officialIdentifyQrCodeRate', '长按二维码识别率（公众号）', '#333333', '%', now(), now(), 0, NULL, '#51C75B', 'rgba(24, 144, 255, 0.0588235294117647)');
INSERT INTO validity_summary_field ("type", "no", "field", "name", "color", "unit", "created_at", "updated_at", "user_id", "formula_id", "value_color", "bg_color") VALUES ( 3, 162, 'enterpriseIncreaseIncomingCost', '长按二维码识别成本（公众号）', '#333333', '', now(), now(), 0, NULL, '#51C75B', 'rgba(81, 199, 91, 0.0588235294117647)');
update marketing_customer_field set name ='长按二维码识别数（微信/企业微信）' where type =3  and field='identifyQrCodeNum'  and sub_category='基础指标(中台)';
update marketing_customer_field set name ='长按二维码识别率（微信/企业微信）' where type =3  and field='identifyQrCodeRate'  and sub_category='基础指标(中台)';
update marketing_customer_field set name ='长按二维码识别成本（微信/企业微信）' where type =3  and field='identifyQrcodeCost'  and sub_category='基础指标(中台)';
-- boss后台自定义列
update marketing_customer_field set field_no=field_no+2 where type =14 and default_data=0 and field_no>=25 and setting_category='PV消耗及转化';
update marketing_customer_field set field_no=field_no+2 where type =14 and default_data=0 and field_no>=29 and setting_category='PV消耗及转化';
update marketing_customer_field set field_no=field_no+4 where type =14 and default_data=0 and field_no>=33 and setting_category='PV消耗及转化';
delete from marketing_customer_field where field='identifyQrCodeNum' and type =14 and default_data=0;
delete from marketing_customer_field where field='identifyQrCodeRate' and type =14 and default_data=0;
INSERT INTO marketing_customer_field ( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "default_width", "min_width", "field_interpretation", "able_sort", "default_data") VALUES ( 14, 0, NULL, 'PV消耗及转化', 't', 'officialIdentifyQrCodeNum', '长按二维码识别数(公众号)', NULL, NULL, now(), now(), 0, 0, 0, NULL, NULL, 2, 25, 160, 96, '该账户下页面内公众号二维码被长按识别的次数', 't', 0);
INSERT INTO marketing_customer_field ( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "default_width", "min_width", "field_interpretation", "able_sort", "default_data") VALUES ( 14, 0, NULL, 'PV消耗及转化', 't', 'officialIdentifyQrCodeRate', '长按二维码识别率(公众号)', '%', NULL, now(), now(), 0, 0, 0, NULL, NULL, 2, 26, 160, 96, '该账户页面内公众号二维码被长按识别的次数占浏览数的比例  计算公式：公众号二维码被长按识别的次数/落地页PV数*100%', 't', 0);
INSERT INTO marketing_customer_field ( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "default_width", "min_width", "field_interpretation", "able_sort", "default_data") VALUES ( 14, 0, NULL, 'PV消耗及转化', 't', 'identifyQrCodeNum', '长按二维码识别数（微信/企业微信）', NULL, NULL, now(), now(), 0, 0, 0, NULL, NULL, 2, 29, 160, 96, '该账户下页面内企业微信二维码被长按识别的次数', 't', 0);
INSERT INTO marketing_customer_field ( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "default_width", "min_width", "field_interpretation", "able_sort", "default_data") VALUES ( 14, 0, NULL, 'PV消耗及转化', 't', 'identifyQrCodeRate', '长按二维码识别率（微信/企业微信）', '%', NULL, now(), now(), 0, 0, 0, NULL, NULL, 2, 30, 160, 96, '该账户页面内企业微信二维码被长按识别的次数占浏览数的比例  计算公式：企业微信二维码被长按识别的次数/落地页PV数*100%', 't', 0);
update marketing_customer_field set field_no=31 where type =14 and default_data=0 and field='addWorkWechatNum' and setting_category='PV消耗及转化';
update marketing_customer_field set field_no=32 where type =14 and default_data=0 and field='addWorkWechatRate' and setting_category='PV消耗及转化';
update marketing_customer_field set field_no=33 where type =14 and default_data=0 and field='identifyQrCodeAddWorkWechatRate' and setting_category='PV消耗及转化';
INSERT INTO marketing_customer_field ( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "default_width", "min_width", "field_interpretation", "able_sort", "default_data") VALUES ( 14, 0, NULL, 'PV消耗及转化', 't', 'identifyGroupQrCodeNum', '长按二维码识别数（企业微信群）', NULL, NULL, now(), now(), 0, 0, 0, NULL, NULL, 2, 34, 160, 96, '该账户下页面内企业微信群二维码被长按识别的次数', 't', 0);
INSERT INTO marketing_customer_field ( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "default_width", "min_width", "field_interpretation", "able_sort", "default_data") VALUES ( 14, 0, NULL, 'PV消耗及转化', 't', 'identifyGroupQrCodeRate', '长按二维码识别率（企业微信群）', NULL, NULL, now(), now(), 0, 0, 0, NULL, NULL, 2, 35, 160, 96, '该账户页面内企业微信群二维码被长按识别的次数占浏览数的比例  计算公式：企业微信群二维码被长按识别的次数/落地页PV数*100%', 't', 0);
INSERT INTO marketing_customer_field ( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "default_width", "min_width", "field_interpretation", "able_sort", "default_data") VALUES ( 14, 0, NULL, 'PV消耗及转化', 't', 'addWorkWechatGroupNum', '加企业微信群数', NULL, NULL, now(), now(), 0, 0, 0, NULL, NULL, 2, 36, 160, 96, '一叶落地页成功企业微信群数', 't', 0);
INSERT INTO marketing_customer_field ( "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "default_width", "min_width", "field_interpretation", "able_sort", "default_data") VALUES ( 14, 0, NULL, 'PV消耗及转化', 't', 'addWorkWechatGroupRate', '加企业微信群率', '%', NULL, now(), now(), 0, 0, 0, NULL, NULL, 2, 37, 160, 96, '该账户下添加企业微信群数占浏览数的比例  计算公式：添加企业微信群数/落地页PV数*100%', 't', 0);

-- 客资列表自定义列
insert into "marketing_customer_field"("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data")
values (11, 4, null, '加粉数据', 'f', 'enterpriseWechatUserTypeName', '加群用户类型', '', null, now(), now(), 0, 0, 0, null, null, 10, 6, null, 'f', 160, 120, 0);
insert into "marketing_customer_field"("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data")
values (11, 4, null, '加粉数据', 'f', 'groupNickname', '群内昵称', '', null, now(), now(), 0, 0, 0, null, null, 10, 6, null, 'f', 160, 120, 0);
insert into "marketing_customer_field"("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data")
values (11, 4, null, '加粉数据', 'f', 'groupChatName', '客户群名称', '', null, now(), now(), 0, 0, 0, null, null, 10, 6, null, 'f', 160, 120, 0);
insert into "marketing_customer_field"("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data")
values (11, 4, null, '加粉数据', 'f', 'groupChatId', '客户群ID', '', null, now(), now(), 0, 0, 0, null, null, 10, 6, null, 'f', 160, 120, 0);
insert into "marketing_customer_field"("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data")
values (11, 4, null, '加粉数据', 'f', 'groupChatJoinSceneName', '入群方式', '', null, now(), now(), 0, 0, 0, null, null, 10, 6, null, 'f', 160, 120, 0);
insert into "marketing_customer_field"("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data")
values (11, 4, null, '加粉数据', 'f', 'groupChatLandingPageName', '所属落地页（成功添加企业微信客户群）', '', null, now(), now(), 0, 0, 0, null, null, 10, 10, null, 'f', 160, 120, 0);
insert into "marketing_customer_field"("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data")
values (11, 4, null, '加粉数据', 'f', 'groupChatChannelName', '所属渠道（成功添加企业微信客户群）', '', null, now(), now(), 0, 0, 0, null, null, 10, 11, null, 'f', 160, 120, 0);
insert into "marketing_customer_field"("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data")
values (11, 4, null, '加粉数据', 'f', 'groupChatUrl', '访问URL（成功添加企业微信客户群）', '', null, now(), now(), 0, 0, 0, null, null, 10, 12, null, 'f', 160, 120, 0);
update marketing_customer_field set name = '客服名称/客户群名称' where field = 'wechatAppletGroupChatName' and type = 11 ;
update marketing_customer_field set name = '客服名称' where field = 'wechatAppletGroupChatName' and type = 11 ;
update marketing_customer_field set setting_category = '加粉/加群数据' where setting_category = '加粉数据' and type = 11 ;
delete from marketing_customer_field where type = 11 and default_data!=0;

-- boss平台同步数据
alter table boss_advertiser_account_group_day_report add column if not exists official_identify_qr_code_num int8;
comment on column boss_advertiser_account_group_day_report.official_identify_qr_code_num is '长按二维码识别数(公众号)';
alter table boss_advertiser_account_group_day_report add column if not exists identify_group_qr_code_num int8;
comment on column boss_advertiser_account_group_day_report.identify_group_qr_code_num is '长按二维码识别数（企业微信群）';
alter table boss_advertiser_account_group_day_report add column if not exists add_work_wechat_group_num int8;
comment on column boss_advertiser_account_group_day_report.add_work_wechat_group_num is '加企业微信群数';

-- 会话存档对应的客户的状态，新增
alter table enterprise_session_employee_customers add column if not exists cus_del_employee_status int4;
comment on column enterprise_session_employee_customers.cus_del_employee_status is '客户删除成员状态';

alter table enterprise_session_employee_customers add column if not exists employee_del_cus_status int4;
comment on column enterprise_session_employee_customers.employee_del_cus_status is '成员删除客户状态';


alter table enterprise_session_employee_customers add column if not exists remark varchar ;
comment on column enterprise_session_employee_customers.remark is '对该客户的备注';

alter table enterprise_session_employee_customers add column if not exists description varchar;
comment on column enterprise_session_employee_customers.description is '对该客户的描述';

-- 城市区域码新增字段
alter table landing_page_wechat_customer_city_code add column if not exists city_code_type int4 default 1;
comment on column landing_page_wechat_customer_city_code.city_code_type is '城市区域码类型';

-- 关联表新增字段
alter table landing_page_wechat_customer_city_code_rel add column if not exists city_code_type int4 default 1;
comment on column landing_page_wechat_customer_city_code_rel.city_code_type is '城市区域码类型';

-- 城市区域码开启兜底
alter table landing_page_wechat_customer_city_code add column if not exists undertake int4 default 1;
comment on column landing_page_wechat_customer_city_code.undertake is '是否开启兜底 1为打开兜底，2为不打开兜底';

-- 关联表新增开启是否兜底
alter table landing_page_wechat_customer_city_code_rel add column if not exists undertake int4 default 1;
comment on column landing_page_wechat_customer_city_code_rel.undertake is '是否开启兜底 1为打开兜底，2为不打开兜底';

-- 数据固化表新增sql
alter table marketing_data_solidification_ad_show_report add column if not exists identify_group_qr_code_num int8;
comment on column marketing_data_solidification_ad_show_report.identify_group_qr_code_num is '长按二维码识别数（企业微信群）';
alter table marketing_data_solidification_ad_show_report add column if not exists add_work_wechat_group_num int8;
comment on column marketing_data_solidification_ad_show_report.add_work_wechat_group_num is '加企业微信群数';
alter table marketing_data_solidification_ad_convert_report add column if not exists identify_group_qr_code_num int8;
comment on column marketing_data_solidification_ad_convert_report.identify_group_qr_code_num is '长按二维码识别数（企业微信群）';
alter table marketing_data_solidification_ad_convert_report add column if not exists add_work_wechat_group_num int8;
comment on column marketing_data_solidification_ad_convert_report.add_work_wechat_group_num is '加企业微信群数';

-- pv曝光数据表
alter table page_view_info add column if not exists yiye_group_chat_qr_code_id bigint default null;
comment on column page_view_info.yiye_group_chat_qr_code_id is '一叶系统-企业微信群二维码id，pv曝光长按由前端传入保存，关联表：landing_page_wechat_group_chat.id';
alter table page_view_info add column if not exists yiye_group_chat_qr_code_group_id bigint default null;
comment on column page_view_info.yiye_group_chat_qr_code_group_id is '一叶系统-企业微信群二维码-分组id，pv曝光长按由前端传入保存，关联表：landing_page_wechat_group_chat_group_rel.landing_page_wechat_group_chat_group_id';
alter table page_view_info add column if not exists enterprise_wechat_group_chat_id varchar default null;
comment on column page_view_info.enterprise_wechat_group_chat_id is '第三方-企业微信群id，pv曝光长按由前端传入保存，关联表：landing_page_wechat_group_chat.chat_id';
alter table page_view_info add column if not exists identify_group_chat_qr_code_status int4 default null;
comment on column page_view_info.identify_group_chat_qr_code_status is '是否长按企业微信群二维码：0-未主动识别二维码  1-主动识别二维码成功';
alter table page_view_info add column if not exists add_group_chat_status int4 default null;
comment on column page_view_info.add_group_chat_status is '是否添加企业微信群成功：0-未加群  1-加群成功';
alter table page_view_info add column if not exists matching_success_yiye_group_id bigint default null;
comment on column page_view_info.matching_success_yiye_group_id is '回调匹配成功-一叶系统-企业微信群二维码id，pv曝光长按由前端传入保存，关联表：landing_page_wechat_group_chat.id';
alter table page_view_info add column if not exists matching_success_outside_chat_id varchar default null;
comment on column page_view_info.matching_success_outside_chat_id is '回调匹配成功-第三方-企业微信群id，pv曝光长按由前端传入保存，关联表：landing_page_wechat_group_chat.chat_id';
alter table page_view_info add column if not exists join_group_chat_scene int4 default null;
comment on column page_view_info.join_group_chat_scene is '入群方式';
alter table page_view_info add column if not exists work_wechat_group_chat_name varchar default null;
comment on column page_view_info.work_wechat_group_chat_name is '添加企业微信客户群名称（匹配回调后获得）';
alter table page_view_info add column if not exists qr_code_show_wcg_id bigint default null;
comment on column page_view_info.qr_code_show_wcg_id is '当前落地页展示的是哪个群二维码关联表：landing_page_wechat_group_chat.chat_id';

-- 填单表
alter table submit_data add column if not exists is_add_wechat_group_chat_customer boolean default false;
comment on column submit_data.is_add_wechat_group_chat_customer is '是否为【加群客资】';
alter table submit_data add column if not exists enterprise_wechat_user_type int4 default null;
comment on column submit_data.enterprise_wechat_user_type is '加群用户类型（企业内部成员 / 外部联系人）';
alter table submit_data add column if not exists group_nickname varchar default null;
comment on column submit_data.group_nickname is '群内昵称（客户添加企业微信群后设置的群内展示名称）';
alter table submit_data add column if not exists group_chat_name varchar default null;
comment on column submit_data.group_chat_name is '客户群名称（客户添加企业微信群的群名称）';
alter table submit_data add column if not exists yiye_group_id varchar default null;
comment on column submit_data.yiye_group_id is '一叶企业微信客户群配置表id，关联表：landing_page_wechat_group_chat.id';
alter table submit_data add column if not exists group_chat_id varchar default null;
comment on column submit_data.group_chat_id is '客户群ID（客户添加企业微信群的群ID），关联表：landing_page_wechat_group_chat.chat_id';
alter table submit_data add column if not exists group_chat_join_scene int4 default null;
comment on column submit_data.group_chat_join_scene is '入群方式（客户添加企业微信群的方式）';
alter table submit_data add column if not exists group_chat_landing_page_id int8 default null;
comment on column submit_data.group_chat_landing_page_id is '所属落地页（成功添加企业微信客户群）';
alter table submit_data add column if not exists group_chat_channel_id int8 default null;
comment on column submit_data.group_chat_channel_id is '所属渠道（成功添加企业微信客户群）';
alter table submit_data add column if not exists group_chat_url varchar default null;
comment on column submit_data.group_chat_url is '访问URL（成功添加企业微信客户群）';
alter table submit_data add column if not exists add_wechat_group_chat_status int4 default 0;
comment on column submit_data.add_wechat_group_chat_status is '企业微信加群成功状态 默认0不成功，1为成功';
alter table submit_data add column if not exists  wechat_group_chat_id_matching_status int4 default 0;
comment on column submit_data.wechat_group_chat_id_matching_status is '企业微信加群匹配状态 默认0不成功，1为成功';

-- 客资表
alter table customer add column if not exists is_add_wechat_group_chat_customer boolean default false;
comment on column customer.is_add_wechat_group_chat_customer is '是否为【加群客资】';
alter table customer add column if not exists enterprise_wechat_user_type int4 default null;
comment on column customer.enterprise_wechat_user_type is '加群用户类型（企业内部成员 / 外部联系人）';
alter table customer add column if not exists group_nickname varchar default null;
comment on column customer.group_nickname is '群内昵称（客户添加企业微信群后设置的群内展示名称）';
alter table customer add column if not exists group_chat_name varchar default null;
comment on column customer.group_chat_name is '客户群名称（客户添加企业微信群的群名称）';
alter table customer add column if not exists yiye_group_id varchar default null;
comment on column customer.yiye_group_id is '一叶企业微信客户群配置表id，关联表：landing_page_wechat_group_chat.id';
alter table customer add column if not exists group_chat_id varchar default null;
comment on column customer.group_chat_id is '客户群ID（客户添加企业微信群的群ID），关联表：landing_page_wechat_group_chat.chat_id';
alter table customer add column if not exists group_chat_join_scene int4 default null;
comment on column customer.group_chat_join_scene is '入群方式（客户添加企业微信群的方式）';
alter table customer add column if not exists group_chat_landing_page_id int8 default null;
comment on column customer.group_chat_landing_page_id is '所属落地页（成功添加企业微信客户群）';
alter table customer add column if not exists group_chat_channel_id int8 default null;
comment on column customer.group_chat_channel_id is '所属渠道（成功添加企业微信客户群）';
alter table customer add column if not exists group_chat_url varchar default null;
comment on column customer.group_chat_url is '访问URL（成功添加企业微信客户群）';
alter table customer add column if not exists  add_wechat_group_chat_status int4 default 0;
comment on column customer.add_wechat_group_chat_status is '企业微信加群成功状态 默认0不成功，1为成功';
alter table customer add column if not exists  wechat_group_chat_id_matching_status int4 default 0;
comment on column customer.wechat_group_chat_id_matching_status is '企业微信加群匹配状态 默认0不成功，1为成功';

-- 企业微回调 - 企业微信群详情记录表
drop table if exists enterprise_wechat_group_details;
drop table if exists enterprise_wechat_group_record;
create table enterprise_wechat_group_record (
    id                          bigserial not null primary key,
    corp_id                     varchar default null,
    chat_id                     varchar default null,
    name                        varchar default null,
    owner                       varchar default null,
    create_time                 timestamp default null,
    notice                      varchar default null,
    admin_user_id_list          varchar[] default '{}',
    created_at                  timestamp not null DEFAULT now(),
    updated_at                  timestamp not null DEFAULT now()
);
comment on table enterprise_wechat_group_record is '企业微回调 - 企业微信群详情记录表';
comment on column enterprise_wechat_group_record.corp_id is '企业微信公司id';
comment on column enterprise_wechat_group_record.chat_id is '客户群ID';
comment on column enterprise_wechat_group_record.name is '群名';
comment on column enterprise_wechat_group_record.owner is '群主ID';
comment on column enterprise_wechat_group_record.create_time is '群的创建时间';
comment on column enterprise_wechat_group_record.notice is '群公告';
comment on column enterprise_wechat_group_record.admin_user_id_list is '群管理员userid';
comment on column enterprise_wechat_group_record.created_at is '创建时间';
comment on column enterprise_wechat_group_record.updated_at is '更新时间';

-- 企业微回调 - 企业微信群 - 群成员 - 详情记录表
drop table if exists enterprise_wechat_group_users_record;
create table if not exists enterprise_wechat_group_users_record (
    id                          bigserial not null primary key,
    corp_id                     varchar default null,
    group_id                    int8 default null,
    chat_id                     varchar default null,
    userid                      varchar default null,
    type                        int4 default null,
    openid                      varchar default null,
    unionid                     varchar default null,
    join_time                   timestamp default null,
    join_scene                  int4 default null,
    invitor_userid              json,
    group_nickname              varchar default null,
    name                        varchar default null,
    pending_id                  varchar default null,
    created_at                  timestamp not null DEFAULT now(),
    updated_at                  timestamp not null DEFAULT now()
);
comment on table enterprise_wechat_group_users_record is '落地页-企业微信客服-企业标签规则-分组-表';
comment on column enterprise_wechat_group_users_record.id is '自增主键';
comment on column enterprise_wechat_group_users_record.corp_id is '企业微信公司id';
comment on column enterprise_wechat_group_users_record.group_id is '企业微信群详情记录表id，关联表字段：enterprise_wechat_group_details.id';
comment on column enterprise_wechat_group_users_record.chat_id is '企业微信客户群id';
comment on column enterprise_wechat_group_users_record.userid is '群成员id';
comment on column enterprise_wechat_group_users_record.type is '成员类型：1 - 企业成员  2 - 外部联系人';
comment on column enterprise_wechat_group_users_record.openid is '微信客户openid（通过外部联系人userid调用接口置换获得）';
comment on column enterprise_wechat_group_users_record.unionid is '外部联系人在微信开放平台的唯一身份标识（微信unionid），通过此字段企业可将外部联系人与公众号/小程序用户关联起来。仅当群成员类型是微信用户（包括企业成员未添加好友），且企业绑定了微信开发者ID有此字段（查看绑定方法）。第三方不可获取，上游企业不可获取下游企业客户的unionid字段';
comment on column enterprise_wechat_group_users_record.join_time is '入群时间';
comment on column enterprise_wechat_group_users_record.join_scene is '入群方式：1 - 由群成员邀请入群（直接邀请入群）  2 - 由群成员邀请入群（通过邀请链接入群）  3 - 通过扫描群二维码入群';
comment on column enterprise_wechat_group_users_record.invitor_userid is '邀请者的userid';
comment on column enterprise_wechat_group_users_record.group_nickname is '在群里的昵称';
comment on column enterprise_wechat_group_users_record.name is '企业微客户群成员名称';
comment on column enterprise_wechat_group_users_record.pending_id is 'pending_id';
comment on column enterprise_wechat_group_users_record.created_at is '创建时间';
comment on column enterprise_wechat_group_users_record.updated_at is '更新时间';
