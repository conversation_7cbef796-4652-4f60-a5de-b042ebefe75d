delete from "marketing_customer_field" where type = 9 and field = 'adUploadNum' and name = '广告回传数';
INSERT INTO "marketing_customer_field" ("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES
(9, 2, NULL, '访问数据', 'f', 'adUploadNum', '广告回传数', '', NULL, NULL, NULL, 0, 0, 0, NULL, NULL, 8, 19, '广告回传成功的数量，同一PV多次（使用深转出价上报）上报仅计首次', 't', 200, 112, 0);
delete from "marketing_customer_field" where type = 9 and field = 'adUploadRate' and name = '广告回传率';
INSERT INTO "marketing_customer_field" ("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES
(9, 2, NULL, '访问数据', 'f', 'adUploadRate', '广告回传率', '%', NULL, NULL, NULL, 0, 0, 0, NULL, NULL, 8, 20, '广告回传数/PV*100% ，精确至小数点后两位', 't', 200, 112, 0);
delete from "marketing_customer_field" where type = 9 and default_data != 0;


delete from "marketing_customer_field" where type = 10 and field = 'adUploadNum' and name = '广告回传数';
INSERT INTO "marketing_customer_field" ("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES
(10, 2, NULL, '访问数据', 'f', 'adUploadNum', '广告回传数', '', NULL, NULL, NULL, 0, 0, 0, NULL, NULL, 8, 19, '广告回传成功的数量，同一PV多次（使用深转出价上报）上报仅计首次', 'f', 200, 112, 0);
delete from "marketing_customer_field" where type = 10 and field = 'adUploadRate' and name = '广告回传率';
INSERT INTO "marketing_customer_field" ("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES
(10, 2, NULL, '访问数据', 'f', 'adUploadRate', '广告回传率', '%', NULL, NULL, NULL, 0, 0, 0, NULL, NULL, 8, 20, '广告回传数/PV*100% ，精确至小数点后两位', 'f', 200, 112, 0);
delete from "marketing_customer_field" where type = 10 and default_data != 0;


alter table landing_page_wechat_customer_service add column if not exists wechat_customer_acquisition_link_name varchar;
comment on column landing_page_wechat_customer_service.wechat_customer_acquisition_link_name is '获客助手链接名称';

alter table work_wechat_customer_acquisition_link add column if not exists wechat_customer_acquisition_link_name varchar;
comment on column work_wechat_customer_acquisition_link.wechat_customer_acquisition_link_name is '获客助手链接名称';


alter table landing_page_wechat_customer_service add column if not exists acquisition_choose_enum int4;
comment on column landing_page_wechat_customer_service.acquisition_choose_enum is '获客助手链接名称单选框';

alter table work_wechat_customer_acquisition_link add column if not exists acquisition_choose_enum int4;
comment on column work_wechat_customer_acquisition_link.acquisition_choose_enum is '获客助手链接名称单选框';


alter table landing_page_pmp_params_config add column if not exists user_id bigint;
comment on column landing_page_pmp_params_config.user_id is '子账户用户ID';


delete from marketing_customer_field where type = 11 and name = '微信用户头像';
INSERT INTO marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id,
                                      created_at, updated_at, user_id, platform_num, is_freeze, platform_ids,
                                      sub_category_no, setting_category_no, field_no, field_interpretation, able_sort,
                                      default_width, min_width, default_data)
VALUES (11, 7, null, '加粉/加群数据', false, 'externalUserAvatar', '微信用户头像', '', null,
        now(),now(), 0, 0, 0, null, null, 10, 22, null, false, 208, 112,
        0);



alter table page_view_info add column if not exists jump_to_super_red_envelope_status int4 default 0;
comment on column page_view_info.jump_to_super_red_envelope_status is '跳转领取618超级红包的状态,0-否，1-是';

alter table page_view_info add column if not exists success_send_welcome_msg_status int4 default 0;
comment on column page_view_info.success_send_welcome_msg_status is '是否成功发送欢迎语的状态,0-否，1-是';

alter table page_view_info add column if not exists ad_upload_status int4 default 0;
comment on column page_view_info.ad_upload_status is '是否已发生上报：0-否，1-是';


alter table marketing_project_overview_ad_show_data_solidification_report add column if not exists send_image_or_qr_code_num bigint;
comment on column marketing_project_overview_ad_show_data_solidification_report.send_image_or_qr_code_num is '成功发码/图片消息数（微信客服机器人）';

alter table marketing_project_overview_ad_show_data_solidification_report add column if not exists into_wechat_customer_service_session_num bigint;
comment on column marketing_project_overview_ad_show_data_solidification_report.into_wechat_customer_service_session_num is '进入微信客服机器人数';

alter table marketing_project_overview_ad_show_data_solidification_report add column if not exists mini_program_news_num bigint;
comment on column marketing_project_overview_ad_show_data_solidification_report.mini_program_news_num is '成功发送小程序消息数（微信客服机器人）';

alter table marketing_project_overview_ad_show_data_solidification_report add column if not exists jump_to_super_red_envelope_num bigint;
comment on column marketing_project_overview_ad_show_data_solidification_report.jump_to_super_red_envelope_num is '跳转领取618超级红包数';

alter table marketing_project_overview_ad_show_data_solidification_report add column if not exists success_send_welcome_msg_num bigint;
comment on column marketing_project_overview_ad_show_data_solidification_report.success_send_welcome_msg_num is '成功发送欢迎语数';

alter table marketing_project_overview_ad_show_data_solidification_report add column if not exists ad_upload_num bigint;
comment on column marketing_project_overview_ad_show_data_solidification_report.ad_upload_num is '上报回传数';


alter table marketing_project_ad_convert_data_solidification_report add column if not exists into_wechat_customer_service_session_num bigint;
comment on column marketing_project_ad_convert_data_solidification_report.into_wechat_customer_service_session_num is '进入微信客服机器人数';

alter table marketing_project_ad_convert_data_solidification_report add column if not exists send_image_or_qr_code_num bigint;
comment on column marketing_project_ad_convert_data_solidification_report.send_image_or_qr_code_num is '成功发码/图片消息数（微信客服机器人）';

alter table marketing_project_ad_convert_data_solidification_report add column if not exists mini_program_news_num bigint;
comment on column marketing_project_ad_convert_data_solidification_report.mini_program_news_num is '成功发送小程序消息数（微信客服机器人）';

alter table marketing_project_ad_convert_data_solidification_report add column if not exists jump_to_super_red_envelope_num bigint;
comment on column marketing_project_ad_convert_data_solidification_report.jump_to_super_red_envelope_num is '跳转领取618超级红包数';

alter table marketing_project_ad_convert_data_solidification_report add column if not exists success_send_welcome_msg_num bigint;
comment on column marketing_project_ad_convert_data_solidification_report.success_send_welcome_msg_num is '成功发送欢迎语数';

alter table marketing_project_ad_convert_data_solidification_report add column if not exists ad_upload_num bigint;
comment on column marketing_project_ad_convert_data_solidification_report.ad_upload_num is '上报回传数';

alter table marketing_project_ad_convert_data_solid_report_clickhouse add column if not exists into_wechat_customer_service_session_num bigint;
comment on column marketing_project_ad_convert_data_solid_report_clickhouse.into_wechat_customer_service_session_num is '进入微信客服机器人数';

alter table marketing_project_ad_convert_data_solid_report_clickhouse add column if not exists send_image_or_qr_code_num bigint;
comment on column marketing_project_ad_convert_data_solid_report_clickhouse.send_image_or_qr_code_num is '成功发码/图片消息数（微信客服机器人）';

alter table marketing_project_ad_convert_data_solid_report_clickhouse add column if not exists mini_program_news_num bigint;
comment on column marketing_project_ad_convert_data_solid_report_clickhouse.mini_program_news_num is '成功发送小程序消息数（微信客服机器人）';

alter table marketing_project_ad_convert_data_solid_report_clickhouse add column if not exists jump_to_super_red_envelope_num bigint;
comment on column marketing_project_ad_convert_data_solid_report_clickhouse.jump_to_super_red_envelope_num is '跳转领取618超级红包数';

alter table marketing_project_ad_convert_data_solid_report_clickhouse add column if not exists success_send_welcome_msg_num bigint;
comment on column marketing_project_ad_convert_data_solid_report_clickhouse.success_send_welcome_msg_num is '成功发送欢迎语数';

alter table marketing_project_ad_convert_data_solid_report_clickhouse add column if not exists ad_upload_num bigint;
comment on column marketing_project_ad_convert_data_solid_report_clickhouse.ad_upload_num is '上报回传数';

alter table marketing_project_ad_show_data_solidification_report_clickhouse add column if not exists into_wechat_customer_service_session_num bigint;
comment on column marketing_project_ad_show_data_solidification_report_clickhouse.into_wechat_customer_service_session_num is '进入微信客服机器人数';

alter table marketing_project_ad_show_data_solidification_report_clickhouse add column if not exists send_image_or_qr_code_num bigint;
comment on column marketing_project_ad_show_data_solidification_report_clickhouse.send_image_or_qr_code_num is '成功发码/图片消息数（微信客服机器人）';

alter table marketing_project_ad_show_data_solidification_report_clickhouse add column if not exists mini_program_news_num bigint;
comment on column marketing_project_ad_show_data_solidification_report_clickhouse.mini_program_news_num is '成功发送小程序消息数（微信客服机器人）';

alter table marketing_project_ad_show_data_solidification_report_clickhouse add column if not exists jump_to_super_red_envelope_num bigint;
comment on column marketing_project_ad_show_data_solidification_report_clickhouse.jump_to_super_red_envelope_num is '跳转领取618超级红包数';

alter table marketing_project_ad_show_data_solidification_report_clickhouse add column if not exists success_send_welcome_msg_num bigint;
comment on column marketing_project_ad_show_data_solidification_report_clickhouse.success_send_welcome_msg_num is '成功发送欢迎语数';

alter table marketing_project_ad_show_data_solidification_report_clickhouse add column if not exists ad_upload_num bigint;
comment on column marketing_project_ad_show_data_solidification_report_clickhouse.ad_upload_num is '上报回传数';



alter table advertise_backlink_statistic_report add column if not exists send_qr_code_or_img_num int8 default 0;
comment on column advertise_backlink_statistic_report.send_qr_code_or_img_num is '二维码或图片发送次数(客服机器人)';
alter table advertise_backlink_statistic_report add column if not exists send_mini_program_card_num int8 default 0;
comment on column advertise_backlink_statistic_report.send_mini_program_card_num is '小程序卡片发送次数(客服机器人)';
alter table advertise_backlink_statistic_report add column if not exists jump_to_super_red_envelope_num int8 default 0;
comment on column advertise_backlink_statistic_report.jump_to_super_red_envelope_num is '618红包跳转次数';
alter table advertise_backlink_statistic_report add column if not exists success_send_welcome_msg_num int8 default 0;
comment on column advertise_backlink_statistic_report.success_send_welcome_msg_num is '发送欢迎语次数(客服机器人)';
alter table advertise_backlink_statistic_report add column if not exists into_wechat_customer_service_session_num int8 default 0;
comment on column advertise_backlink_statistic_report.into_wechat_customer_service_session_num is '进入会话次数(客服机器人)';

alter table advertise_statistic_report add column if not exists send_qr_code_or_img_num int8 default 0;
comment on column advertise_statistic_report.send_qr_code_or_img_num is '二维码或图片发送次数(客服机器人)';
alter table advertise_statistic_report add column if not exists send_mini_program_card_num int8 default 0;
comment on column advertise_statistic_report.send_mini_program_card_num is '小程序卡片发送次数(客服机器人)';
alter table advertise_statistic_report add column if not exists jump_to_super_red_envelope_num int8 default 0;
comment on column advertise_statistic_report.jump_to_super_red_envelope_num is '618红包跳转次数';
alter table advertise_statistic_report add column if not exists success_send_welcome_msg_num int8 default 0;
comment on column advertise_statistic_report.success_send_welcome_msg_num is '发送欢迎语次数(客服机器人)';
alter table advertise_statistic_report add column if not exists into_wechat_customer_service_session_num int8 default 0;
comment on column advertise_statistic_report.into_wechat_customer_service_session_num is '进入会话次数(客服机器人)';

alter table advertise_backlink_statistic_report_new add column if not exists send_qr_code_or_img_num int8 default 0;
comment on column advertise_backlink_statistic_report_new.send_qr_code_or_img_num is '二维码或图片发送次数(客服机器人)';
alter table advertise_backlink_statistic_report_new add column if not exists send_mini_program_card_num int8 default 0;
comment on column advertise_backlink_statistic_report_new.send_mini_program_card_num is '小程序卡片发送次数(客服机器人)';
alter table advertise_backlink_statistic_report_new add column if not exists jump_to_super_red_envelope_num int8 default 0;
comment on column advertise_backlink_statistic_report_new.jump_to_super_red_envelope_num is '618红包跳转次数';
alter table advertise_backlink_statistic_report_new add column if not exists success_send_welcome_msg_num int8 default 0;
comment on column advertise_backlink_statistic_report_new.success_send_welcome_msg_num is '发送欢迎语次数(客服机器人)';
alter table advertise_backlink_statistic_report_new add column if not exists into_wechat_customer_service_session_num int8 default 0;
comment on column advertise_backlink_statistic_report_new.into_wechat_customer_service_session_num is '进入会话次数(客服机器人)';

alter table advertise_statistic_report_new add column if not exists send_qr_code_or_img_num int8 default 0;
comment on column advertise_statistic_report_new.send_qr_code_or_img_num is '二维码或图片发送次数(客服机器人)';
alter table advertise_statistic_report_new add column if not exists send_mini_program_card_num int8 default 0;
comment on column advertise_statistic_report_new.send_mini_program_card_num is '小程序卡片发送次数(客服机器人)';
alter table advertise_statistic_report_new add column if not exists jump_to_super_red_envelope_num int8 default 0;
comment on column advertise_statistic_report_new.jump_to_super_red_envelope_num is '618红包跳转次数';
alter table advertise_statistic_report_new add column if not exists success_send_welcome_msg_num int8 default 0;
comment on column advertise_statistic_report_new.success_send_welcome_msg_num is '发送欢迎语次数(客服机器人)';
alter table advertise_statistic_report_new add column if not exists into_wechat_customer_service_session_num int8 default 0;
comment on column advertise_statistic_report_new.into_wechat_customer_service_session_num is '进入会话次数(客服机器人)';
delete from "marketing_customer_field" where type = 16 and field in('jumpToSuperRedEnvelopeNum',
                                                                    'jumpToSuperRedEnvelopeRate',
                                                                    'jumpToSuperRedEnvelopeCost',
                                                                    'intoWechatCustomerServiceSessionNum',
                                                                    'intoWechatCustomerServiceSessionRate',
                                                                    'intoWechatCustomerServiceSessionCost',
                                                                    'successSendWelcomeMsgNum',
                                                                    'successSendWelcomeMsgRate',
                                                                    'successSendWelcomeMsgCost',
                                                                    'sendMiniProgramCardNum',
                                                                    'sendMiniProgramCardRate',
                                                                    'sendMiniProgramCardCost',
                                                                    'sendQrCodeOrImgNum',
                                                                    'sendQrCodeOrImgRate',
                                                                    'sendQrCodeOrImgCost');
INSERT INTO marketing_customer_field ( type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
VALUES
    (16, 0, '基础数据(中台)', '行为转化', false, 'jumpToSuperRedEnvelopeNum', '跳转领取618超级红包数', null, null, now(), now(), 0, 0, 0, '{0}', 0, 2, 28, '落地页内点击/自动跳转点击动作 - 领取618超级红包的次数（同一PV多次跳转仅计一次）', true, 160, 112, 0),
    (16, 0, '基础数据(中台)', '行为转化', false, 'jumpToSuperRedEnvelopeRate', '跳转领取618超级红包率', '%', null, now(), now(), 0, 0, 0, '{0}', 0, 2, 29, '跳转领取618超级红包数/点击数*100%   保留小数点后两位', true, 240, 112, 0),
    (16, 0, '基础数据(中台)', '行为转化', false, 'jumpToSuperRedEnvelopeCost', '跳转领取618超级红包成本', null, null, now(), now(), 0, 0, 0, '{0}', 0, 2, 30, '消耗/跳转领取618超级红包数  保留小数点后两位', true, 240, 112, 0),
    (16, 0, '微信客服(中台)', '行为转化', false, 'intoWechatCustomerServiceSessionNum', '进入微信客服机器人次数', null, null, now(), now(), 0, 0, 0, '{0}', 1, 3, 1, '进入微信客服机器人次数', true, 160, 112, 0),
    (16, 0, '微信客服(中台)', '行为转化', false, 'intoWechatCustomerServiceSessionRate', '进入微信客服机器人率', '%', null, now(), now(), 0, 0, 0, '{0}', 1, 3, 2, '进入微信客服机器人数/点击数*100%   保留小数点后两位', true, 240, 112, 0),
    (16, 0, '微信客服(中台)', '行为转化', false, 'intoWechatCustomerServiceSessionCost', '进入微信客服机器人成本', null, null, now(), now(), 0, 0, 0, '{0}', 1, 3, 3, '消耗/进入微信客服机器人数  保留小数点后两位', true, 240, 112, 0),
    (16, 0, '微信客服(中台)', '行为转化', false, 'successSendWelcomeMsgNum', '发送欢迎语数（微信客服）', null, null, now(), now(), 0, 0, 0, '{0}', 1, 3, 4, '微信客服机器人发送欢迎语的次数', true, 160, 112, 0),
    (16, 0, '微信客服(中台)', '行为转化', false, 'successSendWelcomeMsgRate', '发送欢迎语率（微信客服）', '%', null, now(), now(), 0, 0, 0, '{0}', 1, 3, 5, '发送欢迎语数（微信客服机器人）/点击数*100%   保留小数点后两位', true, 240, 112, 0),
    (16, 0, '微信客服(中台)', '行为转化', false, 'successSendWelcomeMsgCost', '发送欢迎语成本（微信客服）', null, null, now(), now(), 0, 0, 0, '{0}', 1, 3, 6, '消耗/发送欢迎语数（微信客服机器人）  保留小数点后两位', true, 240, 112, 0),
    (16, 0, '微信客服(中台)', '行为转化', false, 'sendMiniProgramCardNum', '发送小程序消息数（微信客服）', null, null, now(), now(), 0, 0, 0, '{0}', 1, 3, 7, '微信客服机器人发送消息类型为小程序消息的次数', true, 160, 112, 0),
    (16, 0, '微信客服(中台)', '行为转化', false, 'sendMiniProgramCardRate', '发送小程序消息率（微信客服）', '%', null, now(), now(), 0, 0, 0, '{0}', 1, 3, 8, '发送小程序消息数（微信客服机器人）/点击数*100%   保留小数点后两位', true, 240, 112, 0),
    (16, 0, '微信客服(中台)', '行为转化', false, 'sendMiniProgramCardCost', '发送小程序消息成本（微信客服）', null, null, now(), now(), 0, 0, 0, '{0}', 1, 3, 9, '消耗/发送小程序消息数（微信客服机器人）  保留小数点后两位', true, 240, 112, 0),
    (16, 0, '微信客服(中台)', '行为转化', false, 'sendQrCodeOrImgNum', '发送图片/二维码消息数（微信客服）', null, null, now(), now(), 0, 0, 0, '{0}', 1, 3, 10, '微信客服机器人发送消息类型为图片（或二维码）消息或的次数', true, 160, 112, 0),
    (16, 0, '微信客服(中台)', '行为转化', false, 'sendQrCodeOrImgRate', '发送图片/二维码消息率（微信客服）', '%', null, now(), now(), 0, 0, 0, '{0}', 1, 3, 11, '发送图片/二维码消息数（微信客服机器人）/点击数*100%   保留小数点后两位', true, 240, 112, 0),
    (16, 0, '微信客服(中台)', '行为转化', false, 'sendQrCodeOrImgCost', '发送图片/二维码消息成本（微信客服）', null, null, now(), now(), 0, 0, 0, '{0}', 1, 3, 12, '消耗/发送图片/二维码消息数（微信客服机器人）  保留小数点后两位', true, 240, 112, 0);

alter table landing_page_pmp_params_config add column if not exists data_push_field jsonb DEFAULT '[]'::jsonb;
comment on column landing_page_pmp_params_config.data_push_field is '数据推送字段';



--落地页自定义列--

delete from marketing_customer_field where type = 9 and field in ('jumpToSuperRedEnvelopeNum','jumpToSuperRedEnvelopeRate','successSendWelcomeMsgNum', 'successSendWelcomeMsgRate');
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (9, 44, null, '访问数据', false, 'jumpToSuperRedEnvelopeNum', '跳转领取618超级红包数', '', null, now(), now(), 0, 0, 0, null, null, 8, 21, '落地页内点击/自动跳转点击动作 - 领取618超级红包的次数（同一PV多次跳转仅计一次）', true, 208, 112, 0),
       (9, 44, null, '访问数据', false, 'jumpToSuperRedEnvelopeRate', '跳转领取618超级红包率', '%', null, now(), now(), 0, 0, 0, null, null, 8, 22, '跳转领取618超级红包数/浏览数PV*100%', true, 208, 112, 0),
       (9, 44, null, '访问数据', false, 'successSendWelcomeMsgNum', '发送欢迎语数（微信客服）', '', null, now(), now(), 0, 0, 0, null, null, 8, 23, '微信客服机器人发送欢迎语的次数', true, 208, 112, 0),
       (9, 44, null, '访问数据', false, 'successSendWelcomeMsgRate', '发送欢迎语率（微信客服）', '%', null, now(), now(), 0, 0, 0, null, null, 8, 24, '成功发送欢迎语数（微信客服机器人）/PV*100%', true, 208, 112, 0);




--渠道自定义列--

delete from marketing_customer_field where type = 10 and field in ('jumpToSuperRedEnvelopeNum','jumpToSuperRedEnvelopeRate','successSendWelcomeMsgNum','successSendWelcomeMsgRate') ;
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (10, 44, null, '访问数据', false, 'jumpToSuperRedEnvelopeNum', '跳转领取618超级红包数', '', null, now(), now(), 0, 0, 0, null, null, 8, 21, '落地页内点击/自动跳转点击动作 - 领取618超级红包的次数（同一PV多次跳转仅计一次）', true, 208, 112, 0),
       (10, 44, null, '访问数据', false, 'jumpToSuperRedEnvelopeRate', '跳转领取618超级红包率', '%', null, now(), now(), 0, 0, 0, null, null, 8, 22, '跳转领取618超级红包数/浏览数PV*100%', true, 208, 112, 0),
       (10, 44, null, '访问数据', false, 'successSendWelcomeMsgNum', '发送欢迎语数（微信客服）', '', null, now(), now(), 0, 0, 0, null, null, 8, 23, '微信客服机器人发送欢迎语的次数', true, 208, 112, 0),
       (10, 44, null, '访问数据', false, 'successSendWelcomeMsgRate', '发送欢迎语率（微信客服）', '%', null, now(), now(), 0, 0, 0, null, null, 8, 24, '成功发送欢迎语数（微信客服机器人）/PV*100%', true, 208, 112, 0);


--项目概况（数据概况新增）---

delete from validity_summary_field where type = 3 and field in ('jumpToSuperRedEnvelopeNum','jumpToSuperRedEnvelopeRate','jumpToSuperRedEnvelopeCost','intoWechatCustomerServiceSessionNum','intoWechatCustomerServiceSessionRate','intoWechatCustomerServiceSessionCost',
                                                               'successSendWelcomeMsgNum','successSendWelcomeMsgRate','successSendWelcomeMsgCost','sendMiniProgramCardNum','sendMiniProgramCardRate','sendMiniProgramCardCost',
                                                               'sendImageOrQrCodeNum','sendImageOrQrCodeRate','sendImageOrQrCodeCost', 'sendQrCodeOrImgNum','sendQrCodeOrImgRate','sendQrCodeOrImgCost',
                                                                'miniProgramNewsNum','miniProgramNewsRate','miniProgramNewsCost');

INSERT INTO validity_summary_field(type, no, field, name, color, unit, created_at, updated_at, user_id, formula_id, value_color, bg_color,field_definition) VALUES
  (3, 188, 'jumpToSuperRedEnvelopeNum', '跳转领取618超级红包数', '#333333', '', NULL, NULL, 0, NULL, '#333333', 'rgba(250, 250, 250, 1)','落地页内点击/自动跳转点击动作 - 领取618超级红包的次数（同一PV多次跳转仅计一次）'),
  (3, 189, 'jumpToSuperRedEnvelopeRate', '跳转领取618超级红包率', '#333333', '%', NULL, NULL, 0, NULL, '#1890FF', 'rgba(24, 144, 255, 0.0588235294117647)','跳转领取618超级红包数/浏览数PV*100%'),
  (3, 190, 'jumpToSuperRedEnvelopeCost', '跳转领取618超级红包成本', '#333333', '', NULL, NULL, 0, NULL, '#51C75B', 'rgba(81, 199, 91, 0.0588235294117647)','总消耗/跳转领取618超级红包数'),
  (3, 191, 'intoWechatCustomerServiceSessionNum', '进入微信客服机器人数', '#333333', '', NULL, NULL, 0, NULL, '#333333', 'rgba(250, 250, 250, 1)','进入微信客服机器人次数'),
  (3, 192, 'intoWechatCustomerServiceSessionRate', '进入微信客服机器人率', '#333333', '%', NULL, NULL, 0, NULL, '#1890FF', 'rgba(24, 144, 255, 0.0588235294117647)','进入微信客服机器人数/点击数*100%'),
  (3, 193, 'intoWechatCustomerServiceSessionCost', '进入微信客服机器人成本', '#333333', '', NULL, NULL, 0, NULL, '#51C75B', 'rgba(81, 199, 91, 0.0588235294117647)','总消耗/进入微信客服机器人次数'),
  (3, 194, 'successSendWelcomeMsgNum', '发送欢迎语数(微信客服)', '#333333', '', NULL, NULL, 0, NULL, '#333333', 'rgba(250, 250, 250, 1)','微信客服机器人发送欢迎语的次数'),
  (3, 195, 'successSendWelcomeMsgRate', '发送欢迎语率(微信客服)', '#333333', '%', NULL, NULL, 0, NULL, '#1890FF', 'rgba(24, 144, 255, 0.0588235294117647)','微信客服机器人发送欢迎语的次数/浏览数PV*100%'),
  (3, 196, 'successSendWelcomeMsgCost', '发送欢迎语成本(微信客服)', '#333333', '', NULL, NULL, 0, NULL, '#51C75B', 'rgba(81, 199, 91, 0.0588235294117647)','总消耗/微信客服机器人发送欢迎语的次数'),
  (3, 197, 'miniProgramNewsNum', '发送小程序消息数(微信客服)', '#333333', '', NULL, NULL, 0, NULL, '#333333', 'rgba(250, 250, 250, 1)','微信客服机器人发送消息类型为小程序消息的次数'),
  (3, 198, 'miniProgramNewsRate', '发送小程序消息率(微信客服)', '#333333', '%', NULL, NULL, 0, NULL, '#1890FF', 'rgba(24, 144, 255, 0.0588235294117647)','微信客服机器人发送消息类型为小程序消息的次数/浏览数PV*100%'),
  (3, 199, 'miniProgramNewsCost', '发送小程序消息成本(微信客服)', '#333333', '', NULL, NULL, 0, NULL, '#51C75B', 'rgba(81, 199, 91, 0.0588235294117647)','总消耗/微信客服机器人发送消息类型为小程序消息的次数'),
  (3, 200, 'sendImageOrQrCodeNum', '发送图片/二维码消息数(微信客服)', '#333333', '', NULL, NULL, 0, NULL, '#333333', 'rgba(250, 250, 250, 1)','微信客服机器人发送消息类型为图片/二维码的次数'),
  (3, 201, 'sendImageOrQrCodeRate', '发送图片/二维码消息率(微信客服)', '#333333', '%', NULL, NULL, 0, NULL, '#1890FF', 'rgba(24, 144, 255, 0.0588235294117647)','微信客服机器人发送消息类型为图片/二维码的次数/浏览数PV*100%'),
  (3, 202, 'sendImageOrQrCodeCost', '发送图片/二维码消息成本(微信客服)', '#333333', '', NULL, NULL, 0, NULL, '#51C75B', 'rgba(81, 199, 91, 0.0588235294117647)','总消耗/微信客服机器人发送消息类型为图片/二维码的次数');



--项目概况（日历）---
delete from marketing_customer_field where type = 0 and field in ( 'jumpToSuperRedEnvelopeNum','jumpToSuperRedEnvelopeRate','jumpToSuperRedEnvelopeCost');

insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values
(0, 0, '基础指标(中台)', '行为转化', false, 'jumpToSuperRedEnvelopeNum', '跳转领取618超级红包数', '', null, now(), now(), 0, 0, 0, null, 1, 2, 60, '落地页内点击/自动跳转点击动作 - 领取618超级红包的次数（同一PV多次跳转仅计一次）', false, 208, 112, 0),
(0, 0, '基础指标(中台)', '行为转化', false, 'jumpToSuperRedEnvelopeRate', '跳转领取618超级红包率', '%', null, now(), now(), 0, 0, 0, null, 1, 2, 61, '跳转领取618超级红包数/浏览数PV*100%', false, 208, 112, 0),
(0, 0, '基础指标(中台)', '行为转化', false, 'jumpToSuperRedEnvelopeCost', '跳转领取618超级红包成本', '', null, now(), now(), 0, 0, 0, null, 1, 2, 62, '总消耗/跳转领取618超级红包数', false, 208, 112, 0);


delete from marketing_customer_field where type = 0 and field in('intoWechatCustomerServiceSessionNum','intoWechatCustomerServiceSessionRate', 'intoWechatCustomerServiceSessionCost','successSendWelcomeMsgNum',
                                                                 'successSendWelcomeMsgRate','successSendWelcomeMsgCost','sendMiniProgramCardNum','sendMiniProgramCardRate','sendMiniProgramCardCost',
                                                                 'miniProgramNewsNum','miniProgramNewsRate','miniProgramNewsCost',
                                                                 'sendImageOrQrCodeNum', 'sendImageOrQrCodeRate','sendImageOrQrCodeCost', 'sendQrCodeOrImgNum','sendQrCodeOrImgRate','sendQrCodeOrImgCost' );

insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (0, 1, '微信客服(中台)', '行为转化', false, 'intoWechatCustomerServiceSessionNum', '进入微信客服机器人数', '', null, now(), now(), 0, 0, 0, null, 1, 2, 63, '进入微信客服机器人次数', false, 208, 112, 0),
(0, 1, '微信客服(中台)', '行为转化', false, 'intoWechatCustomerServiceSessionRate', '进入微信客服机器人率', '%', null, now(), now(), 0, 0, 0, null, 1, 2, 64, '进入微信客服机器人次数/浏览数PV*100%', false, 208, 112, 0),
(0, 1, '微信客服(中台)', '行为转化', false, 'intoWechatCustomerServiceSessionCost', '进入微信客服机器人数成本', '', null, now(), now(), 0, 0, 0, null, 1, 2, 65, '总消耗/进入微信客服机器人次数*100%', false, 208, 112, 0),
(0, 1, '微信客服(中台)', '行为转化', false, 'successSendWelcomeMsgNum', '发送欢迎语数(微信客服)', '', null, now(), now(), 0, 0, 0, null, 1, 2, 66, '微信客服机器人发送欢迎语的次数', false, 208, 112, 0),
(0, 1, '微信客服(中台)', '行为转化', false, 'successSendWelcomeMsgRate', '发送欢迎语率(微信客服)', '%', null, now(), now(), 0, 0, 0, null, 1, 2, 67, '微信客服机器人发送欢迎语的次数/浏览数PV*100%', false, 208, 112, 0),
(0, 1, '微信客服(中台)', '行为转化', false, 'successSendWelcomeMsgCost', '发送欢迎语成本(微信客服)', '', null, now(), now(), 0, 0, 0, null, 1, 2, 68, '总消耗/微信客服机器人发送欢迎语的次数*100%', false, 208, 112, 0),
(0, 1, '微信客服(中台)', '行为转化', false, 'miniProgramNewsNum', '发送小程序消息数(微信客服)', '', null, now(), now(), 0, 0, 0, null, 1, 2, 69, '微信客服机器人发送消息类型为小程序消息的次数', false, 208, 112, 0),
(0, 1, '微信客服(中台)', '行为转化', false, 'miniProgramNewsRate', '发送小程序消息率(微信客服)', '%', null, now(), now(), 0, 0, 0, null, 1, 2, 70, '微信客服机器人发送消息类型为小程序消息的次数/浏览数PV*100%', false, 208, 112, 0),
(0, 1, '微信客服(中台)', '行为转化', false, 'miniProgramNewsCost', '发送小程序消息成本(微信客服)', '', null, now(), now(), 0, 0, 0, null, 1, 2, 71, '总消耗/微信客服机器人发送消息类型为小程序消息的次数', false, 208, 112, 0),
(0, 1, '微信客服(中台)', '行为转化', false, 'sendImageOrQrCodeNum', '发送图片/二维码消息数(微信客服)', '', null, now(), now(), 0, 0, 0, null, 1, 2, 72, '微信客服机器人发送消息类型为图片（或二维码）消息的次数', false, 208, 112, 0),
(0, 1, '微信客服(中台)', '行为转化', false, 'sendImageOrQrCodeRate', '发送图片/二维码消息率(微信客服)', '%', null, now(), now(), 0, 0, 0, null, 1, 2, 73, '微信客服机器人发送消息类型为图片（或二维码）消息的次数/浏览数PV*100%', false, 208, 112, 0),
(0, 1, '微信客服(中台)', '行为转化', false, 'sendImageOrQrCodeCost', '发送图片/二维码消息成本(微信客服)', '', null, now(), now(), 0, 0, 0, null, 1, 2, 74, '总消耗/微信客服机器人发送消息类型为图片（或二维码）消息的次数', false, 208, 112, 0);
