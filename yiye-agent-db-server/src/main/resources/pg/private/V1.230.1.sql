DROP TABLE IF EXISTS robot_customer_contact;
create table if not exists robot_customer_contact (
    id bigserial primary key,
    landing_page_wechat_customer_service_id int8,
    background_url varchar,
    background_width int4,
    background_height int4,
    qr_code_width int4,
    qr_code_height int4,
    qr_code_index_left int4,
    qr_code_index_top int4,
    delete_status int4 default 0,
    created_at timestamp default now() not null,
    updated_at timestamp default now() not null
);
comment on table robot_customer_contact is '微信客服机器人活码';
comment on column robot_customer_contact.landing_page_wechat_customer_service_id is '客服id';
comment on column robot_customer_contact.background_url is '背景图';
comment on column robot_customer_contact.background_width is '背景图宽度';
comment on column robot_customer_contact.background_height is '背景图长度';
comment on column robot_customer_contact.qr_code_width is '二维码宽度';
comment on column robot_customer_contact.qr_code_height is '二维码长度';
comment on column robot_customer_contact.qr_code_index_left is '二维码位置宽度';
comment on column robot_customer_contact.qr_code_index_top is '二维码位置高度';
comment on column robot_customer_contact.delete_status is '逻辑删除标记,0-未删除,1-已删除（隐藏）,2-永久删除（逻辑删除）';
comment on column robot_customer_contact.created_at is '创建时间';
comment on column robot_customer_contact.updated_at is '修改时间';
create unique index if not exists ux_robot_customer_contact_service_id on robot_customer_contact (landing_page_wechat_customer_service_id);
create index if not exists robot_customer_contact_created_at_index on robot_customer_contact (created_at);

alter table landing_page_wechat_customer_service add column if not exists robot_customer_contact_status integer default 0;
comment on column landing_page_wechat_customer_service.robot_customer_contact_status is '渠道二维码（微信客服机器人内加粉）生成状态：0:未生成，1:生成中,2:已生成,3:创建失败';

alter table landing_page_wechat_customer_service add column if not exists robot_customer_contact_qr_code varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_contact_qr_code is '渠道二维码（微信客服机器人内加粉）链接';

alter table landing_page_wechat_customer_service add column if not exists robot_customer_contact_background_url varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_contact_background_url is '渠道二维码（微信客服机器人内加粉）最终合成图片url';

alter table landing_page_wechat_customer_service add column if not exists robot_customer_contact_qiniu_path varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_contact_qiniu_path is '渠道二维码（微信客服机器人内加粉）最终合成图片七牛云path';

alter table landing_page_wechat_customer_service add column if not exists robot_customer_contact_config_id varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_contact_config_id is '渠道二维码（微信客服机器人内加粉）链接配置ID-修改删除时使用';

alter table landing_page_wechat_customer_service add column if not exists robot_customer_contact_failure_reason varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_contact_failure_reason is '渠道二维码（微信客服机器人内加粉）创建失败原因';

alter table landing_page_wechat_customer_service add column if not exists robot_customer_contact_verify integer default 0;
comment on column landing_page_wechat_customer_service.robot_customer_contact_verify is '渠道二维码（微信客服机器人内加粉）添加客服是否验证';

alter table landing_page_wechat_customer_service add column if not exists robot_customer_contact_state varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_contact_state is '渠道二维码（微信客服机器人内加粉）参数';

alter table landing_page_wechat_customer_service add column if not exists robot_customer_contact_material_id bigint default null;
comment on column landing_page_wechat_customer_service.robot_customer_contact_material_id is '渠道二维码（微信客服机器人内加粉）上传素材id';

ALTER TABLE landing_page_wechat_customer_service_abnormal_monitor ADD COLUMN if not exists robot_send_code_config_count int4;
COMMENT ON COLUMN landing_page_wechat_customer_service_abnormal_monitor.robot_send_code_config_count IS '微信客服机器人内发码配置次数';

ALTER TABLE enterprise_wechat_customer_msg_children_template ADD COLUMN if not exists live_code_service_group_id bigint;
comment on column enterprise_wechat_customer_msg_children_template.live_code_service_group_id is '客服活码-客服分组id';

alter table enterprise_wechat_customer_msg_template add column if not exists delay_time integer default 0;
comment on column enterprise_wechat_customer_msg_template.delay_time is '延时推送间隔时间（单位：秒）';

alter table enterprise_wechat_customer_msg_children_template add column if not exists jump_type integer default 0;
comment on column enterprise_wechat_customer_msg_children_template.jump_type is '跳转类型 0:已创建落地页 1:客服获客助手链接';

ALTER TABLE enterprise_wechat_customer_msg_children_template ADD COLUMN if not exists acquisition_link_service_group_id bigint;
comment on column enterprise_wechat_customer_msg_children_template.acquisition_link_service_group_id is '客服获客助手链接-客服分组id';

ALTER TABLE landing_page_wechat_customer_service_abnormal_monitor ADD COLUMN if not exists robot_send_code_config_count int4;
COMMENT ON COLUMN landing_page_wechat_customer_service_abnormal_monitor.robot_send_code_config_count IS '微信客服机器人内发码配置次数';

DROP TABLE IF EXISTS robot_customer_live_code_record;
create table if not exists robot_customer_live_code_record
(
    id         		                    bigserial not null primary key,
    open_kf_id   		                varchar,
	scene                               varchar,
    pid                                 varchar,
    click_id                            varchar,
	external_user_id                    varchar,
	corp_id                             varchar,
	wechat_customer_service_id          int8,
    wechat_customer_service_group_id    int8,
    advertiser_account_group_id         int8,
	wechat_customer_service_user_id     varchar,
	add_enterprise_wechat_status        integer default 0,
    created_at 		                    timestamp not null DEFAULT now(),
    updated_at 		                    timestamp not null DEFAULT now()
);
comment on table robot_customer_live_code_record is '微信客服机器人活码发送记录表';
comment on column robot_customer_live_code_record.id is '自增主键';
comment on column robot_customer_live_code_record.open_kf_id is '机器人客服id';
comment on column robot_customer_live_code_record.scene is 'scene';
comment on column robot_customer_live_code_record.pid is 'pid';
comment on column robot_customer_live_code_record.external_user_id is '客户userid';
comment on column robot_customer_live_code_record.corp_id is '企业微信id';
comment on column robot_customer_live_code_record.wechat_customer_service_id is '客服id';
comment on column robot_customer_live_code_record.wechat_customer_service_group_id is '客服分组id';
comment on column robot_customer_live_code_record.advertiser_account_group_id is '项目id';
comment on column robot_customer_live_code_record.wechat_customer_service_user_id is '客服userid';
comment on column robot_customer_live_code_record.add_enterprise_wechat_status is '加粉状态,0:为加粉；1-已加粉';
comment on column robot_customer_live_code_record.created_at is '创建时间';
comment on column robot_customer_live_code_record.updated_at is '更新时间';

DROP TRIGGER if EXISTS insert_robot_customer_live_code_record_trigger on robot_customer_live_code_record;
-- 分表策略、按月划分
CREATE
OR REPLACE FUNCTION auto_insert_into_robot_customer_live_code_record_value()
  RETURNS trigger AS
$BODY$
DECLARE
time_column_name  text ;      -- 父表中用于分区的时间字段的名称[必须首先初始化!!]
idx_column_name_1  text ;
idx_column_name_2  text ;
idx_column_name_3  text ;
idx_column_name_4  text ;
idx_column_name_5  text ;
idx_column_name_6  text ;
idx_column_name_7  text ;
idx_column_name_8  text ;
idx_column_name_9  text ;
idx_column_name_10  text ;
    curMM
varchar(6);   -- 'YYYYMM'字串,用做分区子表的后缀
    isExist
boolean;    -- 分区子表,是否已存在
    startTime
text;
    endTime
text;
    strSQL
text;

BEGIN
    -- 调用前,必须首先初始化(时间字段名):time_column_name [直接从调用参数中获取!!]
    time_column_name
:= TG_ARGV[0];
    idx_column_name_1
:= TG_ARGV[1];
    idx_column_name_2
:= TG_ARGV[2];
    idx_column_name_3
:= TG_ARGV[3];

    -- 判断对应分区表 是否已经存在?
EXECUTE 'SELECT $1.' || time_column_name INTO strSQL USING NEW;
curMM
:= to_char( strSQL::timestamp , 'YYYYMM' );
--select count(*) INTO isExist from pg_class where relname = (TG_RELNAME||'_'||curMM);
select count(*)
INTO isExist
from information_schema.tables
where table_schema = TG_TABLE_SCHEMA
  and table_name = (TG_RELNAME || '_' || curMM);
-- 若不存在, 则插入前需 先创建子分区
IF
( isExist = false ) THEN
        -- 创建子分区表
        startTime := curMM||'01 00:00:00';
        endTime
:= to_char( startTime::timestamp + interval '1 month', 'YYYY-MM-DD HH24:MI:SS');
        strSQL
:= 'CREATE TABLE IF NOT EXISTS '||TG_RELNAME||'_'||curMM||
                  ' ( CHECK('||time_column_name||'>='''|| startTime ||''' AND '
                             ||time_column_name||'< '''|| endTime ||''' )
                          ) INHERITS ('||TG_RELNAME||') ;'  ;
EXECUTE strSQL;

-- 创建索引
strSQL
:= 'CREATE INDEX rclcr_'||curMM||'_INDEX_'||time_column_name||' ON '
                  ||TG_RELNAME||'_'||curMM||' ('||time_column_name||');' ;
EXECUTE strSQL;

strSQL
:= 'CREATE INDEX rclcr_'||curMM||'_INDEX_'||idx_column_name_1||' ON '
                  ||TG_RELNAME||'_'||curMM||' ('||idx_column_name_1||');' ;
EXECUTE strSQL;

strSQL
:= 'CREATE INDEX rclcr_'||curMM||'_INDEX_'||idx_column_name_2||' ON '
                  ||TG_RELNAME||'_'||curMM||' ('||idx_column_name_2||','||idx_column_name_3||','||time_column_name||');' ;
EXECUTE strSQL;

strSQL
:= 'CREATE INDEX rclcr_'||curMM||'_INDEX_'||idx_column_name_3||' ON '
                  ||TG_RELNAME||'_'||curMM||' ('||idx_column_name_3||','||idx_column_name_2||','||time_column_name||');' ;
EXECUTE strSQL;

END IF;

    -- 插入数据到子分区!
    strSQL
:= 'INSERT INTO '||TG_RELNAME||'_'||curMM||' SELECT $1.*' ;
EXECUTE strSQL USING NEW;
RETURN NULL;
END
$BODY$
LANGUAGE plpgsql;

CREATE TRIGGER insert_robot_customer_live_code_record_trigger
    BEFORE INSERT
    ON robot_customer_live_code_record
    FOR EACH ROW
    EXECUTE PROCEDURE auto_insert_into_robot_customer_live_code_record_value('created_at','pid','external_user_id','wechat_customer_service_user_id');

DROP TABLE IF EXISTS robot_customer_acquisition_link_record;
create table if not exists robot_customer_acquisition_link_record
(
    id         		                    bigserial not null primary key,
    open_kf_id   		                varchar,
	scene                               varchar,
    pid                                 varchar,
    click_id                            varchar,
	external_user_id                    varchar,
	corp_id                             varchar,
	wechat_customer_service_id          int8,
    wechat_customer_service_group_id    int8,
    advertiser_account_group_id         int8,
	wechat_customer_service_user_id     varchar,
	add_enterprise_wechat_status        integer default 0,
	state                               varchar,
    created_at 		                    timestamp not null DEFAULT now(),
    updated_at 		                    timestamp not null DEFAULT now()
);
comment on table robot_customer_acquisition_link_record is '微信客服机器人获客链接发送记录表';
comment on column robot_customer_acquisition_link_record.id is '自增主键';
comment on column robot_customer_acquisition_link_record.open_kf_id is '机器人客服id';
comment on column robot_customer_acquisition_link_record.scene is 'scene';
comment on column robot_customer_acquisition_link_record.pid is 'pid';
comment on column robot_customer_acquisition_link_record.external_user_id is '客户userid';
comment on column robot_customer_acquisition_link_record.corp_id is '企业微信id';
comment on column robot_customer_acquisition_link_record.wechat_customer_service_id is '客服id';
comment on column robot_customer_acquisition_link_record.wechat_customer_service_group_id is '客服分组id';
comment on column robot_customer_acquisition_link_record.advertiser_account_group_id is '项目id';
comment on column robot_customer_acquisition_link_record.wechat_customer_service_user_id is '客服userid';
comment on column robot_customer_acquisition_link_record.add_enterprise_wechat_status is '加粉状态,0:为加粉；1-已加粉';
comment on column robot_customer_acquisition_link_record.state is '自归因参数';
comment on column robot_customer_acquisition_link_record.created_at is '创建时间';
comment on column robot_customer_acquisition_link_record.updated_at is '更新时间';

DROP TRIGGER if EXISTS insert_robot_customer_acquisition_link_record_trigger on robot_customer_acquisition_link_record;
-- 分表策略、按月划分
CREATE
OR REPLACE FUNCTION auto_insert_into_robot_customer_acquisition_link_record_value()
  RETURNS trigger AS
$BODY$
DECLARE
time_column_name  text ;      -- 父表中用于分区的时间字段的名称[必须首先初始化!!]
idx_column_name_1  text ;
idx_column_name_2  text ;
idx_column_name_3  text ;
idx_column_name_4  text ;
idx_column_name_5  text ;
idx_column_name_6  text ;
idx_column_name_7  text ;
idx_column_name_8  text ;
idx_column_name_9  text ;
idx_column_name_10  text ;
    curMM
varchar(6);   -- 'YYYYMM'字串,用做分区子表的后缀
    isExist
boolean;    -- 分区子表,是否已存在
    startTime
text;
    endTime
text;
    strSQL
text;

BEGIN
    -- 调用前,必须首先初始化(时间字段名):time_column_name [直接从调用参数中获取!!]
    time_column_name
:= TG_ARGV[0];
    idx_column_name_1
:= TG_ARGV[1];
    idx_column_name_2
:= TG_ARGV[2];
    idx_column_name_3
:= TG_ARGV[3];
    idx_column_name_4
:= TG_ARGV[4];

    -- 判断对应分区表 是否已经存在?
EXECUTE 'SELECT $1.' || time_column_name INTO strSQL USING NEW;
curMM
:= to_char( strSQL::timestamp , 'YYYYMM' );
--select count(*) INTO isExist from pg_class where relname = (TG_RELNAME||'_'||curMM);
select count(*)
INTO isExist
from information_schema.tables
where table_schema = TG_TABLE_SCHEMA
  and table_name = (TG_RELNAME || '_' || curMM);
-- 若不存在, 则插入前需 先创建子分区
IF
( isExist = false ) THEN
        -- 创建子分区表
        startTime := curMM||'01 00:00:00';
        endTime
:= to_char( startTime::timestamp + interval '1 month', 'YYYY-MM-DD HH24:MI:SS');
        strSQL
:= 'CREATE TABLE IF NOT EXISTS '||TG_RELNAME||'_'||curMM||
                  ' ( CHECK('||time_column_name||'>='''|| startTime ||''' AND '
                             ||time_column_name||'< '''|| endTime ||''' )
                          ) INHERITS ('||TG_RELNAME||') ;'  ;
EXECUTE strSQL;

-- 创建索引
strSQL
:= 'CREATE INDEX rcalr_'||curMM||'_INDEX_'||time_column_name||' ON '
                  ||TG_RELNAME||'_'||curMM||' ('||time_column_name||');' ;
EXECUTE strSQL;

strSQL
:= 'CREATE INDEX rcalr_'||curMM||'_INDEX_'||idx_column_name_1||' ON '
                  ||TG_RELNAME||'_'||curMM||' ('||idx_column_name_1||');' ;
EXECUTE strSQL;

strSQL
:= 'CREATE INDEX rcalr_'||curMM||'_INDEX_'||idx_column_name_2||' ON '
                  ||TG_RELNAME||'_'||curMM||' ('||idx_column_name_2||','||idx_column_name_3||','||time_column_name||');' ;
EXECUTE strSQL;

strSQL
:= 'CREATE INDEX rcalr_'||curMM||'_INDEX_'||idx_column_name_3||' ON '
                  ||TG_RELNAME||'_'||curMM||' ('||idx_column_name_3||','||idx_column_name_2||','||time_column_name||');' ;
EXECUTE strSQL;

strSQL
:= 'CREATE INDEX rcalr_'||curMM||'_INDEX_'||idx_column_name_4||' ON '
                  ||TG_RELNAME||'_'||curMM||' ('||idx_column_name_4||');' ;
EXECUTE strSQL;

END IF;

    -- 插入数据到子分区!
    strSQL
:= 'INSERT INTO '||TG_RELNAME||'_'||curMM||' SELECT $1.*' ;
EXECUTE strSQL USING NEW;
RETURN NULL;
END
$BODY$
LANGUAGE plpgsql;

CREATE TRIGGER insert_robot_customer_acquisition_link_record_trigger
    BEFORE INSERT
    ON robot_customer_acquisition_link_record
    FOR EACH ROW
    EXECUTE PROCEDURE auto_insert_into_robot_customer_acquisition_link_record_value('created_at','pid','external_user_id','wechat_customer_service_user_id','state');


update marketing_customer_field set name = '动态渠道二维码（页面内加粉）' where field = 'landingPageWechatCustomerContactQrCode' and type = 12;

UPDATE marketing_customer_field set field_no = 1  where field = 'onlineStatus' and type = 12  and setting_category_no = 13;
UPDATE marketing_customer_field set field_no = 2  where field = 'autoRuleStatus' and type = 12  and setting_category_no = 13;
UPDATE marketing_customer_field set field_no = 3  where field = 'wechatUserId' and type = 12  and setting_category_no = 13;
UPDATE marketing_customer_field set field_no = 4  where field = 'groupNames' and type = 12  and setting_category_no = 13;
UPDATE marketing_customer_field set field_no = 5  where field = 'qrCodeWeight' and type = 12  and setting_category_no = 13;
UPDATE marketing_customer_field set field_no = 6  where field = 'qrCodeImgUrl' and type = 12  and setting_category_no = 13;
UPDATE marketing_customer_field set field_no = 7  where field = 'officialWechatCustomerContactAppId' and type = 12  and setting_category_no = 13;
UPDATE marketing_customer_field set field_no = 8  where field = 'landingPageWechatCustomerContactQrCode' and type = 12  and setting_category_no = 13;
UPDATE marketing_customer_field set field_no = 10  where field = 'officialWechatCustomerContactQrCode' and type = 12  and setting_category_no = 13;
UPDATE marketing_customer_field set field_no = 11  where field = 'wechatCustomerAcquisitionLinkStatus' and type = 12  and setting_category_no = 13;
UPDATE marketing_customer_field set field_no = 12  where field = 'abnormalMonitorContent' and type = 12  and setting_category_no = 13;

insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (12, 13, NULL, '基础属性', false, 'robotCustomerContactQrCode', '渠道二维码（微信客服机器人内加粉）', '', NULL, now(), now(), 0, 0, 0, NULL, NULL, 13, 9, '用于微信客服机器人内直接发送客服二维码图片，非发送链接等其他页面内加粉场景使用', false, 160, 112,0);
