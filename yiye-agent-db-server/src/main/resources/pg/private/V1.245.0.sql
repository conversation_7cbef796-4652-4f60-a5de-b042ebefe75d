ALTER TABLE wechat_official_account_assistant_menu_navigation ADD COLUMN IF NOT EXISTS url varchar;
comment on column wechat_official_account_assistant_menu_navigation.url is '点击跳转url';

ALTER TABLE landing_page_wechat_customer_service ADD COLUMN IF NOT EXISTS offline_time timestamp;
comment on column landing_page_wechat_customer_service.offline_time is '下线时间';

alter table enterprise_wechat_customer_msg_children_template add column if not exists image_type integer default 0;
comment on column enterprise_wechat_customer_msg_children_template.image_type is '图片类型 0:客服活码 1:图片';

alter table enterprise_wechat_customer_msg_children_template add column if not exists same_visitor_add integer default 1;
comment on column enterprise_wechat_customer_msg_children_template.same_visitor_add is '禁止访客重复添加 0开启 1关闭 默认为关闭';

alter table landing_page_widget_template add column if not exists dialogic_type integer default 0;
comment on column landing_page_widget_template.dialogic_type is '对话式类型 0:底部悬浮 1:跟随问题';

alter table landing_page_pmp_params_config add column if not exists type integer default 0;
comment on column landing_page_pmp_params_config.type is '类型 0:小程序 1:微信客服';

create table boss_agent_qiyetui_pv_day_report
(
    id               bigserial,
    pv_num           bigint,
    scheme_num       bigint,
    agent_id         varchar   not null,
    corpid           varchar   not null,
    qiye_tui_corp_id varchar,
    day_at           timestamp not null,
    created_at       timestamp,
    updated_at       timestamp
);
comment on table boss_agent_qiyetui_pv_day_report is '营销通数据统计日报';
comment on column boss_agent_qiyetui_pv_day_report.pv_num is '营销通页面pv访问量';
comment on column boss_agent_qiyetui_pv_day_report.pv_num is '营销通scheme成功吊起量';
comment on column boss_agent_qiyetui_pv_day_report.agent_id is '客户id';
comment on column boss_agent_qiyetui_pv_day_report.corpid is '企业微信corpid';
comment on column boss_agent_qiyetui_pv_day_report.qiye_tui_corp_id is '企业推密文corpid';
comment on column boss_agent_qiyetui_pv_day_report.day_at is '某天的报表';
comment on column boss_agent_qiyetui_pv_day_report.created_at is '报表生成的时间';
comment on column boss_agent_qiyetui_pv_day_report.updated_at is '报表更新的时间';

create unique index IF NOT EXISTS unidx_boss_agent_qiyetui_agent_id_corp_id_index on boss_agent_qiyetui_pv_day_report(agent_id, corpid, day_at);

create table boss_agent_qiyetui_pv_day_report_new
(
    id               bigserial,
    pv_num           bigint,
    scheme_num       bigint,
    agent_id         varchar   not null,
    corpid           varchar   not null,
    qiye_tui_corp_id varchar,
    day_at           timestamp not null,
    created_at       timestamp,
    updated_at       timestamp
);
comment on table boss_agent_qiyetui_pv_day_report_new is '营销通数据统计日报';
comment on column boss_agent_qiyetui_pv_day_report_new.pv_num is '营销通页面pv访问量';
comment on column boss_agent_qiyetui_pv_day_report_new.pv_num is '营销通scheme成功吊起量';
comment on column boss_agent_qiyetui_pv_day_report_new.agent_id is '客户id';
comment on column boss_agent_qiyetui_pv_day_report_new.corpid is '企业微信corpid';
comment on column boss_agent_qiyetui_pv_day_report_new.qiye_tui_corp_id is '企业推密文corpid';
comment on column boss_agent_qiyetui_pv_day_report_new.day_at is '某天的报表';
comment on column boss_agent_qiyetui_pv_day_report_new.created_at is '报表生成的时间';
comment on column boss_agent_qiyetui_pv_day_report_new.updated_at is '报表更新的时间';

create unique index IF NOT EXISTS unidx_boss_agent_qiyetui_new_agent_id_corp_id_index on boss_agent_qiyetui_pv_day_report_new (agent_id, corpid, day_at);
