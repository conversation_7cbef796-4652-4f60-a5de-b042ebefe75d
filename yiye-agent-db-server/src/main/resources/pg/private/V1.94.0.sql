CREATE TABLE landing_page_upload_strategy (
id bigserial primary key,
platform_id int,
strategy_name varchar,
user_id bigint,
advertiser_account_group_id bigint,
created_at timestamp(6),
updated_at timestamp(6)
);
COMMENT ON TABLE landing_page_upload_strategy IS '上报策略表';
COMMENT ON COLUMN landing_page_upload_strategy.platform_id IS '媒体ID';
COMMENT ON COLUMN landing_page_upload_strategy.strategy_name IS '策略名称';
COMMENT ON COLUMN landing_page_upload_strategy.user_id IS '用户ID';
COMMENT ON COLUMN landing_page_upload_strategy.advertiser_account_group_id IS '项目ID';
COMMENT ON COLUMN landing_page_upload_strategy.created_at IS '创建时间';
COMMENT ON COLUMN landing_page_upload_strategy.updated_at IS '更新时间';

create index idx_upload_strategy_pmpid on landing_page_upload_strategy(advertiser_account_group_id);


CREATE TABLE landing_page_upload_son_strategy (
id bigserial primary key,
upload_strategy_id bigint,
son_strategy_name varchar,
execute_operation json,
execute_date_type int,
execute_begin_date timestamp(6),
execute_end_date timestamp(6),
execute_time_type int,
begin_end_time_quantum integer[],
many_time_quantum varchar,
created_at timestamp(6),
updated_at timestamp(6)
);
COMMENT ON TABLE landing_page_upload_son_strategy IS '上报子策略表';
COMMENT ON COLUMN landing_page_upload_son_strategy.upload_strategy_id IS '策略ID--[对应策略表landing_page_upload_strategy 主键ID]';
COMMENT ON COLUMN landing_page_upload_son_strategy.son_strategy_name IS '子策略名称';
COMMENT ON COLUMN landing_page_upload_son_strategy.execute_operation IS '执行操作(json)';
COMMENT ON COLUMN landing_page_upload_son_strategy.execute_date_type IS '执行日期类型(0-长期投放，1-指定开始结束日期)';
COMMENT ON COLUMN landing_page_upload_son_strategy.execute_begin_date IS '执行开始日期';
COMMENT ON COLUMN landing_page_upload_son_strategy.execute_end_date IS '执行结束日期';
COMMENT ON COLUMN landing_page_upload_son_strategy.execute_time_type IS '执行时间类型(0-全天，1-指定开始结束时间，2-指定多个时段)';
COMMENT ON COLUMN landing_page_upload_son_strategy.begin_end_time_quantum IS '指定开始时间和结束时间（数组）';
COMMENT ON COLUMN landing_page_upload_son_strategy.many_time_quantum IS '指定多个时段';
COMMENT ON COLUMN landing_page_upload_son_strategy.created_at IS '创建时间';
COMMENT ON COLUMN landing_page_upload_son_strategy.updated_at IS '更新时间';

create index idx_upload_son_strategy_usid on landing_page_upload_son_strategy(upload_strategy_id);


CREATE TABLE landing_page_strategy_condition_group (
id bigserial primary key,
upload_strategy_id bigint,
upload_son_strategy_id bigint,
group_number int,
condition_type int,
execute_condition_type varchar,
compare_type int,
compare_value decimal,
pull_down_value int,
compare_value_type int,
created_at timestamp(6),
updated_at timestamp(6)
);
COMMENT ON TABLE landing_page_strategy_condition_group IS '策略条件组表';
COMMENT ON COLUMN landing_page_strategy_condition_group.upload_strategy_id IS '上报策略ID--[对应子策略表landing_page_upload_son_strategy 主键ID]';
COMMENT ON COLUMN landing_page_strategy_condition_group.upload_son_strategy_id IS '上报子策略ID';
COMMENT ON COLUMN landing_page_strategy_condition_group.group_number IS '组号（1-n），为正整数';
COMMENT ON COLUMN landing_page_strategy_condition_group.condition_type IS '条件类型（0=基础策略条件，1=自定义条件）';
COMMENT ON COLUMN landing_page_strategy_condition_group.execute_condition_type IS '执行条件类型';
COMMENT ON COLUMN landing_page_strategy_condition_group.compare_type IS '比较类型(0-等于，1-大于，2-大于等于，3-小于，4-小于等于)';
COMMENT ON COLUMN landing_page_strategy_condition_group.compare_value IS '比较值---基础条件对应的输入框值';
COMMENT ON COLUMN landing_page_strategy_condition_group.pull_down_value IS '下拉选择值---自定义条件对应的选项值(可为null)';
COMMENT ON COLUMN landing_page_strategy_condition_group.compare_value_type IS '比较值类型(0-无，1-元，2-秒，3-百分比)';
COMMENT ON COLUMN landing_page_strategy_condition_group.created_at IS '创建时间';
COMMENT ON COLUMN landing_page_strategy_condition_group.updated_at IS '更新时间';

create index idx_strategy_condition_group_usid on landing_page_strategy_condition_group(upload_strategy_id);
create index idx_strategy_condition_group_ussid on landing_page_strategy_condition_group(upload_son_strategy_id);


CREATE TABLE landing_page_upload_strategy_advertise_rel (
id bigserial primary key,
platform_id int,
upload_strategy_id bigint,
yiye_adgroup_id bigint not null unique,
advertiser_account_group_id bigint,
created_at timestamp(6),
updated_at timestamp(6)
);
COMMENT ON TABLE landing_page_upload_strategy_advertise_rel IS '上报策略与广告关联表';
COMMENT ON COLUMN landing_page_upload_strategy_advertise_rel.platform_id IS '媒体ID';
COMMENT ON COLUMN landing_page_upload_strategy_advertise_rel.upload_strategy_id IS '上报策略ID';
COMMENT ON COLUMN landing_page_upload_strategy_advertise_rel.yiye_adgroup_id IS '一叶广告组ID';
COMMENT ON COLUMN landing_page_upload_strategy_advertise_rel.advertiser_account_group_id IS '项目ID';
COMMENT ON COLUMN landing_page_upload_strategy_advertise_rel.created_at IS '创建时间';
COMMENT ON COLUMN landing_page_upload_strategy_advertise_rel.updated_at IS '更新时间';

create unique index idx_upload_strategy_adrel_yyadid on landing_page_upload_strategy_advertise_rel(yiye_adgroup_id);


CREATE TABLE landing_page_upload_strategy_platform_config_rel (
id bigserial primary key,
platform_id int,
config_type int,
config_key varchar,
config_name varchar,
config_value_type int,
pull_down_values json,
comparison_operators json,
config_value_unit_type int
);
COMMENT ON TABLE landing_page_upload_strategy_platform_config_rel IS '上报策略平台与配置项关系表';
COMMENT ON COLUMN landing_page_upload_strategy_platform_config_rel.platform_id IS '媒体ID';
COMMENT ON COLUMN landing_page_upload_strategy_platform_config_rel.config_type IS '配置类型（0=基础策略条件，1=自定义条件，2=策略执行操作）';
COMMENT ON COLUMN landing_page_upload_strategy_platform_config_rel.config_key IS '配置项的枚举值';
COMMENT ON COLUMN landing_page_upload_strategy_platform_config_rel.config_name IS '配置名称';
COMMENT ON COLUMN landing_page_upload_strategy_platform_config_rel.config_value_type IS '值的设置类型（0=输入框，1=下拉选择）';
COMMENT ON COLUMN landing_page_upload_strategy_platform_config_rel.pull_down_values IS '下拉选择列表枚举展示映射关系';
COMMENT ON COLUMN landing_page_upload_strategy_platform_config_rel.comparison_operators IS '比较符枚举展示映射关系';
COMMENT ON COLUMN landing_page_upload_strategy_platform_config_rel.config_value_unit_type IS '值单位类型（0=无，1=元，2=秒，3=百分比）';



INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'viewNum', '曝光量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'viewNum', '曝光量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'viewNum', '曝光量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'clickNum', '点击量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'clickNum', '点击量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'clickNum', '点击量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'clickRate', '点击率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'clickRate', '点击率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'clickRate', '点击率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'cost', '花费', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'cost', '花费', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'cost', '花费', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'thousandImpressAvgPrice', '千次展现均价', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'thousandImpressAvgPrice', '千次展现均价', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'thousandImpressAvgPrice', '千次展现均价', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'avgPrice', '点击均价', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'avgPrice', '点击均价', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'avgPrice', '点击均价', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appDownloadFinishCount', 'APP下载完成量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appDownloadFinishCount', 'APP下载完成量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appDownloadFinishCount', 'APP下载完成量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appDownloadRate', 'APP下载率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appDownloadRate', 'APP下载率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appDownloadRate', 'APP下载率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appDownloadCost', 'APP下载成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appDownloadCost', 'APP下载成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appDownloadCost', 'APP下载成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appInstallCount', 'APP安装量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appInstallCount', 'APP安装量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appInstallCount', 'APP安装量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appInstallCost', 'APP安装成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appInstallCost', 'APP安装成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appInstallCost', 'APP安装成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appInstallRate', 'APP安装率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appInstallRate', 'APP安装率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appInstallRate', 'APP安装率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appActivationNum', 'APP激活总量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appActivationNum', 'APP激活总量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appActivationNum', 'APP激活总量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appDownloadActivationRate', 'APP下载激活率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appDownloadActivationRate', 'APP下载激活率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appDownloadActivationRate', 'APP下载激活率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appClickActivationRate', 'APP点击激活率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appClickActivationRate', 'APP点击激活率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appClickActivationRate', 'APP点击激活率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appActivationRegisterRate', 'APP激活注册率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appActivationRegisterRate', 'APP激活注册率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appActivationRegisterRate', 'APP激活注册率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appDownloadActivationCost', 'APP激活成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appDownloadActivationCost', 'APP激活成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appDownloadActivationCost', 'APP激活成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appRegisterNum', '注册量(APP)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appRegisterNum', '注册量(APP)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appRegisterNum', '注册量(APP)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appRegisterCost', '注册成本(APP)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appRegisterCost', '注册成本(APP)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appRegisterCost', '注册成本(APP)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appRegisterRate', '注册率(APP)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appRegisterRate', '注册率(APP)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appRegisterRate', '注册率(APP)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'orderAmount', '订单金额(网页)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'orderAmount', '订单金额(网页)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'orderUnitPrice', '下单客单价', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'orderUnitPrice', '下单客单价', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'orderROI', '下单花费回报（ROI）', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'orderROI', '下单花费回报（ROI）', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appRetainedPersonNum', 'APP次日留存量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appRetainedPersonNum', 'APP次日留存量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appRetainedPersonNum', 'APP次日留存量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appRetainedRate', 'APP次日留存率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appRetainedRate', 'APP次日留存率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appRetainedCost', 'APP次日留存成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appRetainedCost', 'APP次日留存成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appRetainedCost', 'APP次日留存成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'formAppointmentNum', '表单预约量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'formAppointmentNum', '表单预约量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'formAppointmentRate', '表单预约率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'formAppointmentRate', '表单预约率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'formAppointmentCost', '表单预约成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'formAppointmentCost', '表单预约成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'formAppointmentPersonCount', '表单预约人数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'saleClueNum', '销售线索量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'saleClueNum', '销售线索量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'saleCluePersonNum', '销售线索人数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'saleClueConvertRate', '销售线索转化率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'saleClueConvertRate', '销售线索转化率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'saleClueCost', '销售线索成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'saleClueCost', '销售线索成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'validClueNum', '有效线索量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'validClueConvertRate', '有效线索转化率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'validCluePersonNum', '有效线索人数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'validCluePersonNum', '有效线索人数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'validClueCost1', '有效线索成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'paymentNum', '付费行为量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'paymentCost', '付费行为成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'paymentAmount', '付费金额', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'firstPaymentPersonNum', '首次付费行为人数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'firstPaymentPersonNum', '首次付费行为人数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'officialFocusNum', '公众号关注量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'officialFocusNum', '公众号关注量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'officialFocusCost1', '公众号关注成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'officialFocusCost1', '公众号关注成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'officialFocusCount', '公众号关注数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'officialFocusCount', '公众号关注数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'officialFocusCount', '公众号关注数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'officialFocusCost2', '公众号关注成本(中台)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'officialFocusCost2', '公众号关注成本(中台)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'officialFocusCost2', '公众号关注成本(中台)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'validClueCount', '有效线索数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'validClueCount', '有效线索数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'validClueCount', '有效线索数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'validClueCost2', '有效线索成本(中台)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'validClueCost2', '有效线索成本(中台)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'validClueCost2', '有效线索成本(中台)', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'callLinkCount', '电话建联数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'callLinkCount', '电话建联数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'callLinkCount', '电话建联数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'callLinkCost', '电话建联成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'callLinkCost', '电话建联成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'callLinkCost', '电话建联成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'personWechatLinkCount', '个微建联数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'personWechatLinkCount', '个微建联数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'personWechatLinkCount', '个微建联数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'personWechatLinkCost', '个微建联成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'personWechatLinkCost', '个微建联成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'personWechatLinkCost', '个微建联成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appointmentCount', '预约数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appointmentCount', '预约数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appointmentCount', '预约数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'appointmentCost', '预约成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'appointmentCost', '预约成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'appointmentCost', '预约成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'auditionCount', '试听数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'auditionCount', '试听数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'auditionCount', '试听数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'tryListenCost', '试听成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'tryListenCost', '试听成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'tryListenCost', '试听成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'auditionedClassCount', '试听完课数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'auditionedClassCount', '试听完课数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'auditionedClassCount', '试听完课数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'auditionedClassCost', '试听完课成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'auditionedClassCost', '试听完课成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'auditionedClassCost', '试听完课成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'trialCount', '试用数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'trialCount', '试用数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'trialCount', '试用数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'trialCost', '试用成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'trialCost', '试用成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'trialCost', '试用成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'paymentDepositCount', '支付定金数量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'paymentDepositCount', '支付定金数量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'paymentDepositCount', '支付定金数量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'paymentDepositCost', '支付定金成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'paymentDepositCost', '支付定金成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'paymentDepositCost', '支付定金成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'payCount', '支付数量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'payCount', '支付数量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'payCount', '支付数量', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'payCost', '支付成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'payCost', '支付成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'payCost', '支付成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'convertCount', '转化数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'convertCount', '转化数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'convertCount', '转化数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'convertCost', '转化成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'convertCost', '转化成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'convertCost', '转化成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'registerCount', '注册数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'registerCount', '注册数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'registerCount', '注册数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'registerCost', '注册成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'registerCost', '注册成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'registerCost', '注册成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'activationCount', '激活数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'activationCount', '激活数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'activationCount', '激活数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'activationCost', '激活成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'activationCost', '激活成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'activationCost', '激活成本', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'landingPagePv', '浏览数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'landingPagePv', '浏览数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'landingPagePv', '浏览数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'landingPageUv', '访客数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'landingPageUv', '访客数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'landingPageUv', '访客数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'landingAvgStay', '平均停留时长', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 2 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'landingAvgStay', '平均停留时长', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 2 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'landingAvgStay', '平均停留时长', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 2 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'fillCountNum', '填单数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'fillCountNum', '填单数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'fillCountNum', '填单数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'fillCountRate', '填单率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'fillCountRate', '填单率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'fillCountRate', '填单率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'orderNum', '订单提交数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'orderNum', '订单提交数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'orderNum', '订单提交数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'orderCountRate', '订单提交率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'orderCountRate', '订单提交率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'orderCountRate', '订单提交率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'orderFinishNum', '订单完成数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'orderFinishNum', '订单完成数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'orderFinishNum', '订单完成数', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 0, 'orderFinishRate', '订单完成率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 0, 'orderFinishRate', '订单完成率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 0, 'orderFinishRate', '订单完成率', 0, NULL, '[{"key":"EQUAL","value":"等于"},{"key":"GREATER","value":"大于"},{"key":"GREATER_OR_EQUAL","value":"大于等于"},{"key":"LESS","value":"小于"},{"key":"LESS_OR_EQUAL","value":"小于等于"}]', 3 );



INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 2, 'UPLOAD', '上报', 1, '[{"key":"TEN","value":"10%"},{"key":"TWENTY","value":"20%"},{"key":"THIRTY","value":"30%"},{"key":"FORTY","value":"40%"},{"key":"FIFTY","value":"50%"},
{"key":"SIXTY","value":"60%"},{"key":"SEVENTY","value":"70%"},{"key":"EIGHTY","value":"80%"},{"key":"NINETY","value":"90%"},{"key":"ONE_HUNDRED","value":"100%"}]', NULL, 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 2, 'UPLOAD', '上报', 1, '[{"key":"TEN","value":"10%"},{"key":"TWENTY","value":"20%"},{"key":"THIRTY","value":"30%"},{"key":"FORTY","value":"40%"},{"key":"FIFTY","value":"50%"},
{"key":"SIXTY","value":"60%"},{"key":"SEVENTY","value":"70%"},{"key":"EIGHTY","value":"80%"},{"key":"NINETY","value":"90%"},{"key":"ONE_HUNDRED","value":"100%"}]', NULL, 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 2, 'UPLOAD', '上报', 1, '[{"key":"TEN","value":"10%"},{"key":"TWENTY","value":"20%"},{"key":"THIRTY","value":"30%"},{"key":"FORTY","value":"40%"},{"key":"FIFTY","value":"50%"},
{"key":"SIXTY","value":"60%"},{"key":"SEVENTY","value":"70%"},{"key":"EIGHTY","value":"80%"},{"key":"NINETY","value":"90%"},{"key":"ONE_HUNDRED","value":"100%"}]', NULL, 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 2, 'BID_INCREASE', '出价上调', 1, '[{"key":"TEN","value":"10%"},{"key":"TWENTY","value":"20%"},{"key":"THIRTY","value":"30%"},{"key":"FORTY","value":"40%"},{"key":"FIFTY","value":"50%"},
{"key":"SIXTY","value":"60%"},{"key":"SEVENTY","value":"70%"},{"key":"EIGHTY","value":"80%"},{"key":"NINETY","value":"90%"},{"key":"ONE_HUNDRED","value":"100%"}]', NULL, 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 2, 'BID_INCREASE', '出价上调', 1, '[{"key":"TEN","value":"10%"},{"key":"TWENTY","value":"20%"},{"key":"THIRTY","value":"30%"},{"key":"FORTY","value":"40%"},{"key":"FIFTY","value":"50%"},
{"key":"SIXTY","value":"60%"},{"key":"SEVENTY","value":"70%"},{"key":"EIGHTY","value":"80%"},{"key":"NINETY","value":"90%"},{"key":"ONE_HUNDRED","value":"100%"}]', NULL, 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 2, 'BID_INCREASE', '出价上调', 1, '[{"key":"TEN","value":"10%"},{"key":"TWENTY","value":"20%"},{"key":"THIRTY","value":"30%"},{"key":"FORTY","value":"40%"},{"key":"FIFTY","value":"50%"},
{"key":"SIXTY","value":"60%"},{"key":"SEVENTY","value":"70%"},{"key":"EIGHTY","value":"80%"},{"key":"NINETY","value":"90%"},{"key":"ONE_HUNDRED","value":"100%"}]', NULL, 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 2, 'BID_REDUCE', '出价下调', 1, '[{"key":"TEN","value":"10%"},{"key":"TWENTY","value":"20%"},{"key":"THIRTY","value":"30%"},{"key":"FORTY","value":"40%"},{"key":"FIFTY","value":"50%"},
{"key":"SIXTY","value":"60%"},{"key":"SEVENTY","value":"70%"},{"key":"EIGHTY","value":"80%"},{"key":"NINETY","value":"90%"}]', NULL, 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 2, 'BID_REDUCE', '出价下调', 1, '[{"key":"TEN","value":"10%"},{"key":"TWENTY","value":"20%"},{"key":"THIRTY","value":"30%"},{"key":"FORTY","value":"40%"},{"key":"FIFTY","value":"50%"},
{"key":"SIXTY","value":"60%"},{"key":"SEVENTY","value":"70%"},{"key":"EIGHTY","value":"80%"},{"key":"NINETY","value":"90%"}]', NULL, 0 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 2, 'BID_REDUCE', '出价下调', 1, '[{"key":"TEN","value":"10%"},{"key":"TWENTY","value":"20%"},{"key":"THIRTY","value":"30%"},{"key":"FORTY","value":"40%"},{"key":"FIFTY","value":"50%"},
{"key":"SIXTY","value":"60%"},{"key":"SEVENTY","value":"70%"},{"key":"EIGHTY","value":"80%"},{"key":"NINETY","value":"90%"}]', NULL, 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 1, 2, 'BID_VALUE', '出价值', 0, NULL, NULL, 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 2, 'BID_VALUE', '出价值', 0, NULL, NULL, 1 );
INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 3, 2, 'BID_VALUE', '出价值', 0, NULL, NULL, 1 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 2, 'DEEP_BID_INCREASE', '深度出价上调', 1, '[{"key":"TEN","value":"10%"},{"key":"TWENTY","value":"20%"},{"key":"THIRTY","value":"30%"},{"key":"FORTY","value":"40%"},{"key":"FIFTY","value":"50%"},
{"key":"SIXTY","value":"60%"},{"key":"SEVENTY","value":"70%"},{"key":"EIGHTY","value":"80%"},{"key":"NINETY","value":"90%"},{"key":"ONE_HUNDRED","value":"100%"}]', NULL, 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 2, 'DEEP_BID_REDUCE', '深度出价下调', 1, '[{"key":"TEN","value":"10%"},{"key":"TWENTY","value":"20%"},{"key":"THIRTY","value":"30%"},{"key":"FORTY","value":"40%"},{"key":"FIFTY","value":"50%"},
{"key":"SIXTY","value":"60%"},{"key":"SEVENTY","value":"70%"},{"key":"EIGHTY","value":"80%"},{"key":"NINETY","value":"90%"}]', NULL, 0 );

INSERT INTO "landing_page_upload_strategy_platform_config_rel" ( "platform_id", "config_type", "config_key", "config_name", "config_value_type", "pull_down_values", "comparison_operators", "config_value_unit_type" ) VALUES
( 2, 2, 'DEEP_BID_VALUE', '深度出价值', 0, NULL, NULL, 1 );


create index idx_adgroup_aaid on marketing_data_adgroup(advertiser_account_id);
create index idx_account_group_rel_aaid on marketing_advertiser_account_group_rel(advertiser_account_id);
create index idx_account_group_rel_aagid on marketing_advertiser_account_group_rel(advertiser_account_group_id);
