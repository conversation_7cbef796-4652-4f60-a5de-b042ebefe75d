delete  from marketing_customer_field where type = 12 and field = 'robotCustomerDynamicContactQrCode';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (12, 13, NULL, '基础属性', false, 'robotCustomerDynamicContactQrCode', '动态渠道二维码（微信客服机器人内加粉）', '', NULL, now(), now(), 0, 0, 0, NULL, NULL, 13, 10, '用于微信客服机器人内直接发送客服二维码图片（支持在异主体微信客服机器内发送）,非发送链接等其他页面内加粉场景使用', false, 160, 112,0);

create table if not exists robot_customer_dynamic_contact (
                                                              id bigserial primary key,
                                                              landing_page_wechat_customer_service_id int8,
                                                              background_url varchar,
                                                              background_width int4,
                                                              background_height int4,
                                                              qr_code_width int4,
                                                              qr_code_height int4,
                                                              qr_code_index_left int4,
                                                              qr_code_index_top int4,
                                                              delete_status int4 default 0,
                                                              robot_customer_dynamic_contact_verify  int4 default 0,
                                                              created_at timestamp default now() not null,
    updated_at timestamp default now() not null
    );
comment on table robot_customer_dynamic_contact is '微信客服机器人活码配置表(动态渠道二维码)';
comment on column robot_customer_dynamic_contact.landing_page_wechat_customer_service_id is '客服id';
comment on column robot_customer_dynamic_contact.background_url is '背景图';
comment on column robot_customer_dynamic_contact.background_url is '背景图';
comment on column robot_customer_dynamic_contact.background_width is '背景图宽度';
comment on column robot_customer_dynamic_contact.robot_customer_dynamic_contact_verify is '添加好友是否需要认证';
comment on column robot_customer_dynamic_contact.qr_code_width is '二维码宽度';
comment on column robot_customer_dynamic_contact.qr_code_height is '二维码长度';
comment on column robot_customer_dynamic_contact.qr_code_index_left is '二维码位置宽度';
comment on column robot_customer_dynamic_contact.qr_code_index_top is '二维码位置高度';
comment on column robot_customer_dynamic_contact.delete_status is '逻辑删除标记,0-未删除,1-已删除（隐藏）,2-永久删除（逻辑删除）';
comment on column robot_customer_dynamic_contact.created_at is '创建时间';
comment on column robot_customer_dynamic_contact.updated_at is '修改时间';
create unique index if not exists ux_robot_customer_dynamic_contact_service_id on robot_customer_dynamic_contact (landing_page_wechat_customer_service_id);
create index if not exists robot_customer_dynamic_contact_created_at_index on robot_customer_dynamic_contact (created_at);




create table if not exists work_wechat_user_visible_range_robot_customer_contact
(
    id               bigserial primary key,
    corp_id          varchar,
    user_id          varchar,
    contact_verify         integer   default 0,
    generate_status         integer   default 0,
    customer_contact_state          varchar,
    qr_code        varchar,
    customer_contact_background_url    varchar,
    material_id varchar,
    config_id      varchar,
    qiniu_path varchar,
    subject_type int default 0,
    failure_reason varchar,
    agent_id varchar,
    created_at              timestamp default now() not null,
    updated_at              timestamp default now() not null
    );

comment on table work_wechat_user_visible_range_robot_customer_contact is '企微代开发可见范围客服微信机器人内联系我二维码生成状态实时存储表';

comment on column work_wechat_user_visible_range_robot_customer_contact.id is 'id主键';

comment on column work_wechat_user_visible_range_robot_customer_contact.corp_id is '企微corpid';

comment on column work_wechat_user_visible_range_robot_customer_contact.user_id is '客服userid';

comment on column work_wechat_user_visible_range_robot_customer_contact.contact_verify is '是否开启添加客服验证 0:否 1:是';

comment on column work_wechat_user_visible_range_robot_customer_contact.generate_status is '联系我二维码生成状态 0:未生成，1:生成中,2:已生成 3：创建失败';

comment on column work_wechat_user_visible_range_robot_customer_contact.customer_contact_state is '渠道二维码自归因参数';

comment on column work_wechat_user_visible_range_robot_customer_contact.qr_code is '联系我 二维码链接 用于客服列表展示';

comment on column work_wechat_user_visible_range_robot_customer_contact.material_id is '联系我 二维码合并图片 上传素材ID';

comment on column work_wechat_user_visible_range_robot_customer_contact.customer_contact_background_url is '二维码最终合成图片url';

comment on column work_wechat_user_visible_range_robot_customer_contact.config_id is '联系我 二维码链接配置ID-修改删除时使用 用于客服列表展示';

comment on column work_wechat_user_visible_range_robot_customer_contact.qiniu_path is '联系我 二维码最终合成图片七牛云path';

comment on column work_wechat_user_visible_range_robot_customer_contact.failure_reason is '联系我 二维码创建失败原因';

comment on column work_wechat_user_visible_range_robot_customer_contact.created_at is '创建时间';

comment on column work_wechat_user_visible_range_robot_customer_contact.updated_at is '更新时间';


create index if not EXISTS idx_robot_customer_contact_corpid
    on work_wechat_user_visible_range_robot_customer_contact (corp_id);

create index if not EXISTS idx_robot_customer_contact_user_id
    on work_wechat_user_visible_range_robot_customer_contact (user_id);

create index if not EXISTS idx_robot_customer_contact_config_id
    on work_wechat_user_visible_range_robot_customer_contact (config_id);

create unique index robot_customer_contact_corpid_userid_idx
    on work_wechat_user_visible_range_robot_customer_contact (corp_id, user_id);



alter table landing_page_wechat_customer_service add column if not exists robot_customer_dynamic_contact_status integer default 0;
comment on column landing_page_wechat_customer_service.robot_customer_dynamic_contact_status is '动态渠道二维码（微信客服机器人内加粉）生成状态：0:未生成，1:生成中,2:已生成,3:创建失败';


alter table landing_page_wechat_customer_service add column if not exists robot_customer_dynamic_contact_qr_code varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_dynamic_contact_qr_code is '动态渠道二维码（微信客服机器人内加粉）链接';


alter table landing_page_wechat_customer_service add column if not exists robot_customer_dynamic_contact_background_url varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_dynamic_contact_background_url is '动态渠道二维码（微信客服机器人内加粉）最终合成图片url';


alter table landing_page_wechat_customer_service add column if not exists robot_customer_dynamic_contact_qiniu_path varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_dynamic_contact_qiniu_path is '动态渠道二维码（微信客服机器人内加粉）最终合成图片七牛云path';


alter table landing_page_wechat_customer_service add column if not exists robot_customer_dynamic_contact_config_id varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_dynamic_contact_config_id is '动态渠道二维码（微信客服机器人内加粉）链接配置ID-修改删除时使用';


alter table landing_page_wechat_customer_service add column if not exists robot_customer_dynamic_contact_failure_reason varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_dynamic_contact_failure_reason is '动态渠道二维码（微信客服机器人内加粉）创建失败原因';


alter table landing_page_wechat_customer_service add column if not exists robot_customer_dynamic_contact_verify varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_dynamic_contact_verify is '动态渠道二维码（微信客服机器人内加粉）添加客服是否验证';


alter table landing_page_wechat_customer_service add column if not exists robot_customer_dynamic_contact_state varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_dynamic_contact_state is '动态动态渠道二维码（微信客服机器人内加粉）参数';


alter table landing_page_wechat_customer_service add column if not  exists robot_customer_dynamic_contact_material_id varchar default null;
comment on column landing_page_wechat_customer_service.robot_customer_dynamic_contact_material_id is '动态渠道二维码（微信客服机器人内加粉）上传素材id';



create table if not exists robot_dynamic_customer_contact_generate_record
(
    id                          bigserial not null primary key,
    corp_id                      varchar    not null,
    contact_verify               int default 0,
    landing_page_wechat_customer_service_id int default 0,
    landing_page_wechat_customer_service_wechat_user_id varchar   default null,
    dynamic_robot_customer_contact_status int default 0,
    robot_wechat_customer_contact_state  varchar  not null,
    dynamic_robot_wechat_customer_contact_qr_code  varchar   default null,
    contact_image_url varchar   default null,
    material_id varchar,
    contact_image_qiniu_path varchar   default null,
    generate_failure_reason varchar   default null,
    agent_id  varchar not null,
    config_id varchar,
    expire_at                    timestamp DEFAULT null,
    used_at                     timestamp DEFAULT null,
    used_status                 int default 0,
    created_at                  timestamp not null DEFAULT now(),
    updated_at                  timestamp not null DEFAULT now()
    );
comment on table robot_dynamic_customer_contact_generate_record is '微信客服机器人内加粉 —— 动态渠道二维码实时存储表';
comment on column robot_dynamic_customer_contact_generate_record.id is '自增主键';
comment on column robot_dynamic_customer_contact_generate_record.corp_id is '企微corpid';
comment on column robot_dynamic_customer_contact_generate_record.material_id is 'material_id';
comment on column robot_dynamic_customer_contact_generate_record.config_id is '新增联系方式的配置id';
comment on column robot_dynamic_customer_contact_generate_record.landing_page_wechat_customer_service_wechat_user_id is '微信客服对应的user_id';
comment on column robot_dynamic_customer_contact_generate_record.landing_page_wechat_customer_service_id is '微信客服ID';
comment on column robot_dynamic_customer_contact_generate_record.created_at is '创建时间';
comment on column robot_dynamic_customer_contact_generate_record.updated_at is '修改时间';
comment on column robot_dynamic_customer_contact_generate_record.expire_at is '临时素材过期时间';
comment on column robot_dynamic_customer_contact_generate_record.used_at is '二维码被使用的时间';
comment on column robot_dynamic_customer_contact_generate_record.used_status is '二维码被使用的状态,0-未使用; 1-已使用';
comment on column robot_dynamic_customer_contact_generate_record.contact_verify is '是否开启添加客服验证 0:否 1:是';
comment on column robot_dynamic_customer_contact_generate_record.dynamic_robot_customer_contact_status is '联系我二维码生成状态 0:未生成，1:生成中,2:已生成 3：创建失败';
comment on column robot_dynamic_customer_contact_generate_record.robot_wechat_customer_contact_state is '动态渠道二维码参数';
comment on column robot_dynamic_customer_contact_generate_record.dynamic_robot_wechat_customer_contact_qr_code is '联系我 二维码链接 用于客服列表展示';
comment on column robot_dynamic_customer_contact_generate_record.contact_image_url is '二维码最终合成图片url';
comment on column robot_dynamic_customer_contact_generate_record.contact_image_qiniu_path is '动态渠道二维码最终合成图片七牛云path';
comment on column robot_dynamic_customer_contact_generate_record.generate_failure_reason is '联系我 二维码创建失败原因';
comment on column robot_dynamic_customer_contact_generate_record.agent_id is '所属账户';

create index if not exists robot_dynamic_customer_contact_corpid_index on robot_dynamic_customer_contact_generate_record(corp_id);
create index if not exists robot_dynamic_customer_contact_state_index on robot_dynamic_customer_contact_generate_record(robot_wechat_customer_contact_state);
create index if not exists robot_dynamic_customer_contact_material_id_index on robot_dynamic_customer_contact_generate_record(material_id);
create index if not exists robot_dynamic_customer_contact_user_id_index on robot_dynamic_customer_contact_generate_record(landing_page_wechat_customer_service_wechat_user_id);
create index if not exists robot_dynamic_customer_contact_create_time_index on robot_dynamic_customer_contact_generate_record(created_at);
