-- account
-- 属性指标
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 1, null, '属性指标', 't', 'platformId', '媒体', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 2, null, '属性指标', 't', 'accountName', '账户名称', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 2, null, '属性指标', 't', 'accountId', '账户ID', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 2);
-- 投放数据
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 4, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 0, 1, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 5, '展点信息(媒体)', '投放数据', 't', 'thousandImpressAvgPrice', '千次展示均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 6, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 7, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 8, '展点信息(媒体)', '投放数据', 't', 'avgPrice', '点击均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 7);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 3, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 1, 1, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '花费信息(媒体)', '投放数据', 'f', 'balance', '账户余额', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 1, 1, 1);
-- 行为转化
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 9, '基础指标(中台)', '行为转化', 't', 'landingPagePv', '落地页PV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '基础指标(中台)', '行为转化', 'f', 'landingPageUv', '落地页UV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 10, '基础指标(中台)', '行为转化', 't', 'fillCountNum', '填单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountRate', '填单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 11, '基础指标(中台)', '行为转化', 't', 'fillCountCost', '填单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 12, '基础指标(中台)', '行为转化', 't', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 13, '基础指标(中台)', '行为转化', 't', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 14, '基础指标(中台)', '行为转化', 't', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 15, '基础指标(中台)', '行为转化', 't', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 10);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '网页行为(中台)', '行为转化', 'f', 'landingAvgStay', '平均停留时长', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 2, 0);

-- 业务转化
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'formOrderConvertRate', '表单-下单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentNum', '表单预约量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 1, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentPersonCount', '表单预约人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentRate', '表单预约率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentCost', '表单预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'buttonFormConvert', '按钮-表单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'orderAmount', '订单金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'orderUnitPrice', '下单客单价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'orderROI', '下单ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentNum', '付费行为量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentCost', '付费行为成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 10);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentAmount', '付费金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'firstPaymentPersonNum', '首次付费行为人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'followNum', '关注数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 13);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'focusCost', '关注成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 14);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusNum', '公众号关注量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 15);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusCost1', '公众号关注成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 16);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueNum', '销售线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 24);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'saleCluePersonNum', '销售线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{3}', 1, 3, 25);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueConvertRate', '销售线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 26);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueCost', '销售线索成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 27);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueNum', '有效线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 28);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'validCluePersonNum', '有效线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 1, 3, 29);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueConvertRate', '有效线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 30);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueCost1', '有效线索成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 31);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCount', '公众号关注数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCost2', '公众号关注成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCount', '有效线索数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCost2', '有效线索成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCount', '电话建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCost', '电话建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCount', '个微建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCost', '个微建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCount', '预约数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCost', '预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'auditionCount', '试听数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 10);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'tryListenCost', '试听成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCount', '试听完课数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCost', '试听完课成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 13);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'trialCount', '试用数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 14);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'trialCost', '试用成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 15);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCount', '支付定金数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 16);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCost', '支付定金成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 17);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'payCount', '支付数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 18);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'payCost', '支付成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 19);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'convertCount', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 20);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'convertCost', '转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 21);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'registerCount', '注册数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 22);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'registerCost', '注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 23);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'activationCount', '激活数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 24);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, '销售线索(中台)', '业务转化', 'f', 'activationCost', '激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 25);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadFinishCount', 'APP下载完成量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 3, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadRate', 'APP下载率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadCost', 'APP下载成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallCount', 'APP安装量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallRate', 'APP安装率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallCost', 'APP安装成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appActivationNum', 'APP激活总量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 3, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appClickActivationRate', 'APP点击激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadActivationRate', 'APP下载激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadActivationCost', 'APP激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterNum', 'APP注册量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 10);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterRate', 'APP注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 3, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appActivationRegisterRate', 'APP激活注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{1}', 3, 3, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterCost', 'APP注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 13);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedPersonNum', 'APP次日留存量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 14);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedRate', 'APP次日留存率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 15);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (4, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedCost', 'APP次日留存成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 16);


-- campaign
-- 属性指标
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 1, null, '属性指标', 't', 'platformId', '媒体', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 2, null, '属性指标', 't', 'accountName', '账户名称', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 2, null, '属性指标', 't', 'accountId', '账户ID', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 3, null, '属性指标', 't', 'campaignName', '计划名称', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 3, null, '属性指标', 't', 'campaignId', '计划ID', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 4, null, '属性指标', 't', 'campaignStatus', '计划状态', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, null, '属性指标', 'f', 'promotionGoal', '推广目标', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 6);
-- 投放数据
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 6, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 0, 1, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 7, '展点信息(媒体)', '投放数据', 't', 'thousandImpressAvgPrice', '千次展示均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 8, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 9, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 10, '展点信息(媒体)', '投放数据', 't', 'avgPrice', '点击均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 7);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 5, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 1, 1, 0);

-- 行为转化
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 11, '基础指标(中台)', '行为转化', 't', 'landingPagePv', '落地页PV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '基础指标(中台)', '行为转化', 'f', 'landingPageUv', '落地页UV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 12, '基础指标(中台)', '行为转化', 't', 'fillCountNum', '填单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountRate', '填单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 13, '基础指标(中台)', '行为转化', 't', 'fillCountCost', '填单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '基础指标(中台)', '行为转化', 'f', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 14, '基础指标(中台)', '行为转化', 't', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 15, '基础指标(中台)', '行为转化', 't', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 10);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '网页行为(中台)', '行为转化', 'f', 'landingAvgStay', '平均停留时长', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 2, 0);

-- 业务转化
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'formOrderConvertRate', '表单-下单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentNum', '表单预约量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 1, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentPersonCount', '表单预约人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentRate', '表单预约率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentCost', '表单预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'buttonFormConvert', '按钮-表单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'orderAmount', '订单金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'orderUnitPrice', '下单客单价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'orderROI', '下单ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentNum', '付费行为量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentCost', '付费行为成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 10);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentAmount', '付费金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'firstPaymentPersonNum', '首次付费行为人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'followNum', '关注数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 13);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'focusCost', '关注成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 14);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusNum', '公众号关注量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 15);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusCost1', '公众号关注成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 16);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueNum', '销售线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 24);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'saleCluePersonNum', '销售线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{3}', 1, 3, 25);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueConvertRate', '销售线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 26);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueCost', '销售线索成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 27);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueNum', '有效线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 28);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'validCluePersonNum', '有效线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 1, 3, 29);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueConvertRate', '有效线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 30);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueCost1', '有效线索成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 31);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCount', '公众号关注数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCost2', '公众号关注成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCount', '有效线索数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCost2', '有效线索成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCount', '电话建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCost', '电话建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCount', '个微建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCost', '个微建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCount', '预约数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCost', '预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'auditionCount', '试听数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 10);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'tryListenCost', '试听成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCount', '试听完课数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCost', '试听完课成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 13);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'trialCount', '试用数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 14);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'trialCost', '试用成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 15);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCount', '支付定金数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 16);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCost', '支付定金成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 17);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'payCount', '支付数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 18);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'payCost', '支付成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 19);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'convertCount', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 20);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'convertCost', '转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 21);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'registerCount', '注册数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 22);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'registerCost', '注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 23);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'activationCount', '激活数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 24);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, '销售线索(中台)', '业务转化', 'f', 'activationCost', '激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 25);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadFinishCount', 'APP下载完成量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 3, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadRate', 'APP下载率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadCost', 'APP下载成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallCount', 'APP安装量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallRate', 'APP安装率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallCost', 'APP安装成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appActivationNum', 'APP激活总量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 3, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appClickActivationRate', 'APP点击激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadActivationRate', 'APP下载激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadActivationCost', 'APP激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterNum', 'APP注册量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 10);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterRate', 'APP注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 3, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appActivationRegisterRate', 'APP激活注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{1}', 3, 3, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterCost', 'APP注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 13);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedPersonNum', 'APP次日留存量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 14);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedRate', 'APP次日留存率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 15);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (5, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedCost', 'APP次日留存成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 16);


-- ad
-- 属性指标
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, null, '属性指标', 'f', 'platformId', '媒体', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, null, '属性指标', 'f', 'accountName', '账户名称', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, null, '属性指标', 'f', 'accountId', '账户ID', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 1, null, '属性指标', 't', 'campaignName', '计划名称', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 1, null, '属性指标', 't', 'campaignId', '计划ID', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, null, '属性指标', 'f', 'campaignStatus', '计划状态', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, null, '属性指标', 'f', 'promotionGoal', '推广目标', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 2, null, '属性指标', 't', 'adName', '广告名称', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 2, null, '属性指标', 't', 'adId', '广告ID', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 3, null, '属性指标', 't', 'adStatus', '广告状态', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 12, null, '属性指标', 't', 'optimizationGoal', '优化目标', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 10);

-- 投放数据
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 5, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 0, 1, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 6, '展点信息(媒体)', '投放数据', 't', 'thousandImpressAvgPrice', '千次展示均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 7, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 8, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 9, '展点信息(媒体)', '投放数据', 't', 'avgPrice', '点击均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 7);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 4, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 1, 1, 0);


-- 行为转化
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 10, '基础指标(中台)', '行为转化', 't', 'landingPagePv', '落地页PV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 11, '基础指标(中台)', '行为转化', 't', 'landingPageUv', '落地页UV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountNum', '填单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountRate', '填单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountCost', '填单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础指标(中台)', '行为转化', 'f', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 10);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '网页行为(中台)', '行为转化', 'f', 'landingAvgStay', '平均停留时长', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 2, 0);

-- 业务转化
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 13, '基础转化(媒体)', '业务转化', 't', 'convertNum', '目标转化量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 14, '基础转化(媒体)', '业务转化', 't', 'targetConvertRate', '目标转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 15, '基础转化(媒体)', '业务转化', 't', 'targetConvertCost', '目标转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础转化(媒体)', '业务转化', 'f', 'deepConvertNum', '深度转化量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础转化(媒体)', '业务转化', 'f', 'deepConvertRate', '深度转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础转化(媒体)', '业务转化', 'f', 'deepConvertCost', '深度转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础转化(媒体)', '业务转化', 'f', 'placeOrderNum', '下单量(网页)', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础转化(媒体)', '业务转化', 'f', 'placeOrderRate', '下单率(网页)', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '基础转化(媒体)', '业务转化', 'f', 'placeOrderCost', '下单成本(网页)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 3, 8);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'formOrderConvertRate', '表单-下单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentNum', '表单预约量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 1, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentPersonCount', '表单预约人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentRate', '表单预约率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentCost', '表单预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'buttonFormConvert', '按钮-表单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'orderAmount', '订单金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'orderUnitPrice', '下单客单价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'orderROI', '下单ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentNum', '付费行为量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentCost', '付费行为成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 10);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentAmount', '付费金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'firstPaymentPersonNum', '首次付费行为人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'followNum', '关注数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 13);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'focusCost', '关注成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 14);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusNum', '公众号关注量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 15);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusCost1', '公众号关注成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 16);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueNum', '销售线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 24);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'saleCluePersonNum', '销售线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{3}', 1, 3, 25);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueConvertRate', '销售线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 26);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueCost', '销售线索成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 27);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueNum', '有效线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 28);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'validCluePersonNum', '有效线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 1, 3, 29);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueConvertRate', '有效线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 30);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueCost1', '有效线索成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 31);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCount', '公众号关注数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCost2', '公众号关注成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCount', '有效线索数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCost2', '有效线索成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCount', '电话建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCost', '电话建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCount', '个微建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCost', '个微建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCount', '预约数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCost', '预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'auditionCount', '试听数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 10);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'tryListenCost', '试听成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCount', '试听完课数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCost', '试听完课成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 13);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'trialCount', '试用数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 14);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'trialCost', '试用成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 15);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCount', '支付定金数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 16);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCost', '支付定金成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 17);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'payCount', '支付数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 18);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'payCost', '支付成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 19);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'convertCount', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 20);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'convertCost', '转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 21);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'registerCount', '注册数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 22);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'registerCost', '注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 23);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'activationCount', '激活数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 24);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, '销售线索(中台)', '业务转化', 'f', 'activationCost', '激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 25);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadFinishCount', 'APP下载完成量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 3, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadRate', 'APP下载率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadCost', 'APP下载成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallCount', 'APP安装量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallRate', 'APP安装率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallCost', 'APP安装成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appActivationNum', 'APP激活总量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 3, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appClickActivationRate', 'APP点击激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadActivationRate', 'APP下载激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadActivationCost', 'APP激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterNum', 'APP注册量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 10);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterRate', 'APP注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 3, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appActivationRegisterRate', 'APP激活注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{1}', 3, 3, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterCost', 'APP注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 13);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedPersonNum', 'APP次日留存量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 14);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedRate', 'APP次日留存率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 15);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (6, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedCost', 'APP次日留存成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 16);


-- creative
-- 属性指标
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, null, '属性指标', 'f', 'platformId', '媒体', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, null, '属性指标', 'f', 'accountName', '账户名称', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, null, '属性指标', 'f', 'accountId', '账户ID', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, null, '属性指标', 'f', 'campaignName', '计划名称', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, null, '属性指标', 'f', 'campaignId', '计划ID', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 1, null, '属性指标', 't', 'adName', '广告名称', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 1, null, '属性指标', 't', 'adId', '广告ID', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, null, '属性指标', 'f', 'adStatus', '广告状态', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 2, null, '属性指标', 't', 'creativeName', '创意名称', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 2, null, '属性指标', 't', 'creativeId', '创意ID', '', NULL, NULL, NULL, 0, 4, 1, '{1,2,3,8}', null, 0, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, null, '属性指标', 'f', 'creativeType', '创意类型', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', null, 0, 13);
-- 投放数据
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 4, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 0, 1, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 5, '展点信息(媒体)', '投放数据', 't', 'thousandImpressAvgPrice', '千次展示均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 6, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 7, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 8, '展点信息(媒体)', '投放数据', 't', 'avgPrice', '点击均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 7);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 3, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 1, 1, 0);

-- 行为转化
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 9, '基础指标(中台)', '行为转化', 't', 'landingPagePv', '落地页PV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '基础指标(中台)', '行为转化', 'f', 'landingPageUv', '落地页UV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 10, '基础指标(中台)', '行为转化', 't', 'fillCountNum', '填单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountRate', '填单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 11, '基础指标(中台)', '行为转化', 't', 'fillCountCost', '填单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 12, '基础指标(中台)', '行为转化', 't', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 13, '基础指标(中台)', '行为转化', 't', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 14, '基础指标(中台)', '行为转化', 't', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 15, '基础指标(中台)', '行为转化', 't', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 10);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '网页行为(中台)', '行为转化', 'f', 'landingAvgStay', '平均停留时长', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 2, 0);

-- 业务转化
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'formOrderConvertRate', '表单-下单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentNum', '表单预约量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 1, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentPersonCount', '表单预约人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentRate', '表单预约率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentCost', '表单预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'buttonFormConvert', '按钮-表单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'orderAmount', '订单金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'orderUnitPrice', '下单客单价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'orderROI', '下单ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentNum', '付费行为量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentCost', '付费行为成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 10);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentAmount', '付费金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'firstPaymentPersonNum', '首次付费行为人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'followNum', '关注数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 13);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'focusCost', '关注成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 14);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusNum', '公众号关注量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 15);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusCost1', '公众号关注成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 16);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueNum', '销售线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 24);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'saleCluePersonNum', '销售线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{3}', 1, 3, 25);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueConvertRate', '销售线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 26);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueCost', '销售线索成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 27);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueNum', '有效线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 28);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'validCluePersonNum', '有效线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 1, 3, 29);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueConvertRate', '有效线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 30);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueCost1', '有效线索成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 31);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCount', '公众号关注数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCost2', '公众号关注成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCount', '有效线索数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCost2', '有效线索成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCount', '电话建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCost', '电话建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCount', '个微建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCost', '个微建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCount', '预约数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCost', '预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'auditionCount', '试听数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 10);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'tryListenCost', '试听成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCount', '试听完课数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCost', '试听完课成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 13);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'trialCount', '试用数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 14);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'trialCost', '试用成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 15);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCount', '支付定金数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 16);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCost', '支付定金成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 17);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'payCount', '支付数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 18);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'payCost', '支付成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 19);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'convertCount', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 20);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'convertCost', '转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 21);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'registerCount', '注册数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 22);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'registerCost', '注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 23);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'activationCount', '激活数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 24);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, '销售线索(中台)', '业务转化', 'f', 'activationCost', '激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 25);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadFinishCount', 'APP下载完成量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 3, 3, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadRate', 'APP下载率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 1);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadCost', 'APP下载成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 2);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallCount', 'APP安装量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 3);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallRate', 'APP安装率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 4);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallCost', 'APP安装成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 5);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appActivationNum', 'APP激活总量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 3, 3, 6);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appClickActivationRate', 'APP点击激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 7);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadActivationRate', 'APP下载激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 8);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadActivationCost', 'APP激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 9);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterNum', 'APP注册量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 10);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterRate', 'APP注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 3, 11);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appActivationRegisterRate', 'APP激活注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{1}', 3, 3, 12);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterCost', 'APP注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 13);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedPersonNum', 'APP次日留存量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 14);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedRate', 'APP次日留存率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 15);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no") VALUES (7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedCost', 'APP次日留存成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 16);

-- 数据固化表新增字段
alter table marketing_data_solidification add column convert_num bigint;
comment on column marketing_data_solidification.convert_num is '目标转化量';


create table marketing_advertiser_screen_field
(
    id         bigserial not null
        primary key,
    platform_id bigint,
    customer_field_page_type bigint,
    type bigint,
    no bigint,
    field varchar,
    describe varchar,
    unit varchar
);
comment on table marketing_advertiser_screen_field is '计划广告创意筛选条件表';
comment on column marketing_advertiser_screen_field.platform_id is '平台id';
comment on column marketing_advertiser_screen_field.customer_field_page_type is '4:账户, 5:计划, 6:广告, 7:创意';
comment on column marketing_advertiser_screen_field.type is '4:账户, 5:计划, 6:广告, 7:创意';
comment on column marketing_advertiser_screen_field.no is '字段序号';
comment on column marketing_advertiser_screen_field.field is '字段名';
comment on column marketing_advertiser_screen_field.describe is '描述';
comment on column marketing_advertiser_screen_field.unit is '单位';
create index idx_marketing_advertise_screen_type
    on marketing_advertiser_screen_field (platform_id);

-- 计划下的计划筛选条件
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,describe, unit) values (1,5,5,0,'CAMPAIGN_STATUS','计划状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,describe, unit) values (1,5,5,1,'PROMOTIONAL_OBJECTIVES','推广目标','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,describe, unit) values (2,5,5,0,'CAMPAIGN_STATUS','计划状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,describe, unit) values (2,5,5,1,'PROMOTIONAL_OBJECTIVES','推广目标','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,describe, unit) values (3,5,5,1,'PROMOTIONAL_OBJECTIVES','推广目标','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,describe, unit) values (8,5,5,0,'CAMPAIGN_STATUS','计划状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,describe, unit) values (8,5,5,1,'PROMOTIONAL_OBJECTIVES','推广目标','');

-- 巨量下的广告下的筛选条件
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,6,5,0,'CAMPAIGN_STATUS','计划状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,6,5,1,'PROMOTIONAL_OBJECTIVES','推广目标','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,6,6,0,'ADVERTISE_STATUS','广告状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,6,6,1,'viewNum','曝光量','次');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,6,6,2,'cost','花费','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,6,6,3,'convertNum','目标转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,6,6,4,'targetConvertCost','目标转化成本','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,6,6,5,'deepConvertNum','深度转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,6,6,6,'deepConvertCost','深度转化成本','元');
-- 腾讯
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,6,5,0,'CAMPAIGN_STATUS','计划状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,6,5,1,'PROMOTIONAL_OBJECTIVES','推广目标','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,6,6,0,'ADVERTISE_STATUS','广告状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,6,6,1,'viewNum','曝光量','次');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,6,6,2,'cost','花费','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,6,6,3,'convertNum','目标转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,6,6,4,'targetConvertCost','目标转化成本','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,6,6,5,'deepConvertNum','深度转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,6,6,6,'deepConvertCost','深度转化成本','元');
-- mp(没有广告计划筛选)
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,6,5,0,'PROMOTIONAL_OBJECTIVES','推广目标','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,6,6,0,'ADVERTISE_STATUS','广告状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,6,6,1,'viewNum','曝光量','次');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,6,6,2,'cost','花费','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,6,6,3,'convertNum','目标转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,6,6,4,'targetConvertCost','目标转化成本','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,6,6,5,'deepConvertNum','深度转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,6,6,6,'deepConvertCost','深度转化成本','元');
-- vivo
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,6,5,0,'CAMPAIGN_STATUS','计划状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,6,5,1,'PROMOTIONAL_OBJECTIVES','推广目标','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,6,6,0,'ADVERTISE_STATUS','广告状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,6,6,1,'viewNum','曝光量','次');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,6,6,2,'cost','花费','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,6,6,3,'convertNum','目标转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,6,6,4,'targetConvertCost','目标转化成本','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,6,6,5,'deepConvertNum','深度转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,6,6,6,'deepConvertCost','深度转化成本','元');

-- 巨量创意类型
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,7,6,0,'ADVERTISE_STATUS','广告状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,7,6,1,'viewNum','曝光量','次');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,7,6,2,'cost','花费','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,7,6,3,'convertNum','目标转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,7,6,4,'targetConvertCost','目标转化成本','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,7,6,5,'deepConvertNum','深度转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,7,6,6,'deepConvertCost','深度转化成本','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (1,7,7,0,'CREATIVE_TYPE','创意类型','');

-- 腾讯创意类型
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,7,6,0,'ADVERTISE_STATUS','广告状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,7,6,1,'viewNum','曝光量','次');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,7,6,2,'cost','花费','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,7,6,3,'convertNum','目标转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,7,6,4,'targetConvertCost','目标转化成本','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,7,6,5,'deepConvertNum','深度转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (2,7,6,6,'deepConvertCost','深度转化成本','元');

-- 微信创意类型
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,7,6,0,'ADVERTISE_STATUS','广告状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,7,6,1,'viewNum','曝光量','次');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,7,6,2,'cost','花费','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,7,6,3,'convertNum','目标转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,7,6,4,'targetConvertCost','目标转化成本','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,7,6,5,'deepConvertNum','深度转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (3,7,6,6,'deepConvertCost','深度转化成本','元');

-- vivo创意类型
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,7,6,0,'ADVERTISE_STATUS','广告状态','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,7,6,1,'viewNum','曝光量','次');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,7,6,2,'cost','花费','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,7,6,3,'convertNum','目标转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,7,6,4,'targetConvertCost','目标转化成本','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,7,6,5,'deepConvertNum','深度转化量','');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,7,6,6,'deepConvertCost','深度转化成本','元');
insert into marketing_advertiser_screen_field (platform_id, customer_field_page_type, type,no,field,  describe, unit) values (8,7,7,0,'CREATIVE_TYPE','创意类型','');

create table marketing_advertiser_screen
(
    id         bigserial not null
        primary key,
    platform_id         bigint,
    advertise_screen_type       bigint,
    field       varchar,
    field_value       varchar,
    describe varchar
);
comment on table marketing_advertiser_screen is '计划广告创意筛选条件详情表';
comment on column marketing_advertiser_screen.platform_id is '平台id';
comment on column marketing_advertiser_screen.advertise_screen_type is '筛选类型';
comment on column marketing_advertiser_screen.field is '字段名称';
comment on column marketing_advertiser_screen.field_value is '字段值';
comment on column marketing_advertiser_screen.describe is '描述';
create index idx_marketing_advertise_screen_screen_type
    on marketing_advertiser_screen (advertise_screen_type);

-- 头条的广告计划状态
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,0,'CAMPAIGN_STATUS','CAMPAIGN_STATUS_ENABLE','启用');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,0,'CAMPAIGN_STATUS','CAMPAIGN_STATUS_DISABLE','暂停');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,0,'CAMPAIGN_STATUS','CAMPAIGN_STATUS_DELETE','删除');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,0,'CAMPAIGN_STATUS','CAMPAIGN_STATUS_ALL','所有(包含已删除)');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,0,'CAMPAIGN_STATUS','CAMPAIGN_STATUS_NOT_DELETE','所有(不包含已删除)');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,0,'CAMPAIGN_STATUS','CAMPAIGN_STATUS_ADVERTISER_BUDGET_EXCEED','超出广告主日预算');
-- 腾讯-计划状态
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,0,'CAMPAIGN_STATUS','AD_STATUS_NORMAL','有效');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,0,'CAMPAIGN_STATUS','AD_STATUS_SUSPEND','暂停');

-- vivo-计划状态
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,0,'CAMPAIGN_STATUS','6','投放中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,0,'CAMPAIGN_STATUS','1','已删除');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,0,'CAMPAIGN_STATUS','2','已暂停');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,0,'CAMPAIGN_STATUS','3','余额不足');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,0,'CAMPAIGN_STATUS','4','到达账户日限额');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,0,'CAMPAIGN_STATUS','5','到达计划日限额');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,0,'CAMPAIGN_STATUS','0','所有数据');
-- 巨量-推广目标
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,1,'PROMOTIONAL_OBJECTIVES','LINK','销售线索收集');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,1,'PROMOTIONAL_OBJECTIVES','APP','应用推广');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,1,'PROMOTIONAL_OBJECTIVES','DPA','商品目录推广');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,1,'PROMOTIONAL_OBJECTIVES','GOODS','商品推广（鲁班）');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,1,'PROMOTIONAL_OBJECTIVES','SHOP','电商店铺推广');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,1,'PROMOTIONAL_OBJECTIVES','STORE','门店推广');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,1,'PROMOTIONAL_OBJECTIVES','AWEME','抖音号推广');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,1,'PROMOTIONAL_OBJECTIVES','ARTICAL','头条文章推广');
-- 腾讯-推广目标
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_LINK','网页');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_LINK_WECHAT','网页（微信推广）');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_ECOMMERCE','电商推广');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_APP_ANDROID','Android应用');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_APP_IOS','IOS应用');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_APP_ANDROID_MYAPP','应用宝推广');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_APP_ANDROID_UNION','Android应用（联盟推广）');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_LOCAL_ADS_WECHAT','本地广告(微信推广)');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_QQ_BROWSER_MINI_PROGRAM','QQ 浏览器小程序');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_QQ_MESSAGE','QQ 消息');
-- mp-推广目标
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_APP_ANDROID','安卓下载类广告');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_APP_IOS ','ios 下载类广告');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_FOLLOW_WECHAT','关注类广告');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_FOLLOW_WECHAT','品牌活动推广-文章');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_LINK_WECHAT','品牌活动推广-链接');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_CARD_WECHAT','卡券类广告');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_LOCAL_ADS_WECHAT','lbs 类广告');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_ECOMMERCE','电商推广类目');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_LEAD_ADS_WECHAT','表单推广类目');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_MINI_GAME_WECHAT','小游戏类目');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_LINK_JD','京东电商推广');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_COUPON_WECHAT_PAY','微信支付代金券');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,1,'PROMOTIONAL_OBJECTIVES','PROMOTED_OBJECT_TYPE_UNSUPPORTED','暂不支持枚举值');
-- vivo-推广目标
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,1,'PROMOTIONAL_OBJECTIVES','2','应用下载');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,1,'PROMOTIONAL_OBJECTIVES','1','普通网址');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,1,'PROMOTIONAL_OBJECTIVES','3','动态商品');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,1,'PROMOTIONAL_OBJECTIVES','8','快生态');
-- 巨量-广告状态
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_DELIVERY_OK','投放中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_DISABLE','计划暂停');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_AUDIT','新建审核中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_REAUDIT','修改审核中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_DONE','已完成（投放达到结束时间）');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_CREATE','计划新建');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_AUDIT_DENY','审核不通过');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_BALANCE_EXCEED','账户余额不足');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_BUDGET_EXCEED','超出预算');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_NOT_START','未到达投放时间');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_NO_SCHEDULE','不在投放时段');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_CAMPAIGN_DISABLE','已被广告组暂停');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_CAMPAIGN_EXCEED','广告组超出预算');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_DELETE','已删除');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_ALL','所有包含已删除');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_NOT_DELETE','所有不包含已删除');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,2,'ADVERTISE_STATUS','AD_STATUS_ADVERTISER_BUDGET_EXCEED','超出广告主日预算');
-- 腾讯-广告状态
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_UNKNOWN','未知状态');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_PENDING','审核中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_DENIED','审核不通过');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_FROZEN','冻结');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_SUSPEND','暂停中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_READY','未到投放时间');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_ACTIVE','投放中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_STOP','投放结束');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_PREPARE','准备中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_DELETED','已删除');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_ACTIVE_ACCOUNT_FROZEN','广告被暂停（账户资金被冻结）');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_ACTIVE_ACCOUNT_EMPTY','广告被暂停（账户余额不足）');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_ACTIVE_ACCOUNT_LIMIT','广告被暂停（账户达日限额）');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_ACTIVE_CAMPAIGN_LIMIT','广告被暂停（推广计划达日限额）');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_ACTIVE_CAMPAIGN_SUSPEND','广告被暂停（推广计划暂停）');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_ACTIVE_AD_LIMIT','广告被暂停（广告达日限额）');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_PART_READY','部分待投放');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (2,2,'ADVERTISE_STATUS','STATUS_PART_ACTIVE','部分投放中');
-- mp-广告状态
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_PENDING','审核中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_DENIED','未通过');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_READY','待投放');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_NORMAL','投放中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_SUSPEND ','暂停投放');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_FINISH ','投放结束');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_CREATING','创建中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_CANCEL','取消投放');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_UNKNOWN','异常状态');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_COOPERATION_PENDING','合作确认中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_COOPERATION_FAIL','合作未达成');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_HOST_ARTICLE_PREPARING','文章编辑中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_HOST_ARTICLE_PENDING','文章待确认');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_TO_LOCK_FLOW','待锁定');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_LOCK_FLOW_FAIL','排期失败');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_LOCK_FLOW_SUCCESS','已锁量待提交');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_LOCK_FLOW_ING','等待排期结果');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (3,2,'ADVERTISE_STATUS','AD_STATUS_UNSUPPORTED','暂不支持枚举值');
-- vivo-广告状态
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,2,'ADVERTISE_STATUS','0','所有数据');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,2,'ADVERTISE_STATUS','1','已删除');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,2,'ADVERTISE_STATUS','2','已暂停');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,2,'ADVERTISE_STATUS','3','计划暂停(原因)');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,2,'ADVERTISE_STATUS','4','APP状态异常');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,2,'ADVERTISE_STATUS','5','到达广告组日限额');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,2,'ADVERTISE_STATUS','6','未到投放日期');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,2,'ADVERTISE_STATUS','7','未到投放时段');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,2,'ADVERTISE_STATUS','8','投放已结束');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,2,'ADVERTISE_STATUS','9','投放中');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,2,'ADVERTISE_STATUS','10','APP状态异常，渠道包不可用');
-- 巨量-创意类型
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_SMALL','小图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_LARGE','大图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_GROUP','组图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_VIDEO','横版视频');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_GIF','GIF图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_LARGE_VERTICAL','大图竖图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_VIDEO_VERTICAL','竖版视频');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','TOUTIAO_SEARCH_AD_IMAGE','搜索大图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','SEARCH_AD_SMALL_IMAGE','搜索小图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_UNION_SPLASH','穿山甲开屏图片');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_UNION_SPLASH_VIDEO','穿山甲开屏视频');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_DISPLAY_WINDOW','搜索橱窗');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','MATERIAL_IMAGE_MODE_TITLE','标题类型');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_AWEME_LIVE','直播画面类型');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_PLAYABLE_HORIZONTAL','横版试玩素材类型');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (1,3,'CREATIVE_TYPE','CREATIVE_IMAGE_MODE_PLAYABLE_VERTICAL','竖版试玩素材类型');
-- vivo-创意类型
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10000','横版大图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10001','竖版大图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10002','小图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10003','组图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10004','静态图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10005','横版视频');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10007','激励视频');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10010','竖版视频');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10020','无图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10100','cpd无图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10101','cpd单图');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10102','cpd文字链');
insert into marketing_advertiser_screen(platform_id, advertise_screen_type, field,field_value, describe) values (8,3,'CREATIVE_TYPE','10103','cpd三图');

update marketing_dynamic_field set statistic_field='convert_count' where field='cover_status';


-- 优化目标映射表
create table marketing_convert_type (
        id                          bigserial not null,
        platform_id                 BIGINT,
        name                        varchar,
        description                 varchar,
        constraint uk_es_platformId_name
            unique (platform_id, name)
);
comment on table marketing_convert_type is '优化目标映射表';
comment on column marketing_convert_type.platform_id is '平台id';
comment on column marketing_convert_type.name is '目标名称';
comment on column marketing_convert_type.description is '描述';

INSERT INTO "marketing_convert_type" VALUES (1, 2, 'OPTIMIZATIONGOAL_NONE', 'none');
INSERT INTO "marketing_convert_type" VALUES (2, 2, 'OPTIMIZATIONGOAL_BRAND_CONVERSION', '品牌转化');
INSERT INTO "marketing_convert_type" VALUES (3, 2, 'OPTIMIZATIONGOAL_FOLLOW', '关注');
INSERT INTO "marketing_convert_type" VALUES (4, 2, 'OPTIMIZATIONGOAL_CLICK', '点击');
INSERT INTO "marketing_convert_type" VALUES (5, 2, 'OPTIMIZATIONGOAL_IMPRESSION', '曝光');
INSERT INTO "marketing_convert_type" VALUES (6, 2, 'OPTIMIZATIONGOAL_APP_DOWNLOAD', '下载');
INSERT INTO "marketing_convert_type" VALUES (7, 2, 'OPTIMIZATIONGOAL_APP_ACTIVATE', '激活');
INSERT INTO "marketing_convert_type" VALUES (8, 2, 'OPTIMIZATIONGOAL_ONE_DAY_RETENTION', '次日留存');
INSERT INTO "marketing_convert_type" VALUES (9, 2, 'OPTIMIZATIONGOAL_ECOMMERCE_ORDER', '下单');
INSERT INTO "marketing_convert_type" VALUES (10, 2, 'OPTIMIZATIONGOAL_ECOMMERCE_CART', '加入购物车');
INSERT INTO "marketing_convert_type" VALUES (11, 2, 'OPTIMIZATIONGOAL_VIEW_COMMODITY_PAGE', '商品详情页浏览');
INSERT INTO "marketing_convert_type" VALUES (12, 2, 'OPTIMIZATIONGOAL_ONLINE_CONSULTATION', '在线咨询');
INSERT INTO "marketing_convert_type" VALUES (13, 2, 'OPTIMIZATIONGOAL_TELEPHONE_CONSULTATION', '电话拨打');
INSERT INTO "marketing_convert_type" VALUES (14, 2, 'OPTIMIZATIONGOAL_PAGE_RESERVATION', '表单预约');
INSERT INTO "marketing_convert_type" VALUES (15, 2, 'OPTIMIZATIONGOAL_DELIVERY', '发货');
INSERT INTO "marketing_convert_type" VALUES (16, 2, 'OPTIMIZATIONGOAL_MESSAGE_AFTER_FOLLOW', '公众号内发消息');
INSERT INTO "marketing_convert_type" VALUES (17, 2, 'OPTIMIZATIONGOAL_CLICK_MENU_AFTER_FOLLOW', '公众号内点击菜单栏');
INSERT INTO "marketing_convert_type" VALUES (18, 2, 'OPTIMIZATIONGOAL_CONFIRM_EFFECTIVE_LEADS_CONSULT', '有效在线咨询');
INSERT INTO "marketing_convert_type" VALUES (19, 2, 'OPTIMIZATIONGOAL_CONFIRM_EFFECTIVE_LEADS_PHONE', '有效电话拨打');
INSERT INTO "marketing_convert_type" VALUES (20, 2, 'OPTIMIZATIONGOAL_LEADS_COLLECT', '综合线索收集');
INSERT INTO "marketing_convert_type" VALUES (21, 2, 'OPTIMIZATIONGOAL_FIRST_PURCHASE', '首次付费');
INSERT INTO "marketing_convert_type" VALUES (22, 2, 'OPTIMIZATIONGOAL_APPLY', '进件');
INSERT INTO "marketing_convert_type" VALUES (23, 2, 'OPTIMIZATIONGOAL_PRE_CREDIT', '预授信');
INSERT INTO "marketing_convert_type" VALUES (24, 2, 'OPTIMIZATIONGOAL_CREDIT', '授信');
INSERT INTO "marketing_convert_type" VALUES (25, 2, 'OPTIMIZATIONGOAL_WITHDRAW_DEPOSITS', '提现');
INSERT INTO "marketing_convert_type" VALUES (26, 2, 'OPTIMIZATIONGOAL_PROMOTION_VIEW_KEY_PAGE', '关键页面访问');
INSERT INTO "marketing_convert_type" VALUES (27, 2, 'OPTIMIZATIONGOAL_MOBILE_APP_CREATE_ROLE', '小游戏创角');
INSERT INTO "marketing_convert_type" VALUES (28, 2, 'OPTIMIZATIONGOAL_CANVAS_CLICK', '跳转按钮点击');
INSERT INTO "marketing_convert_type" VALUES (29, 2, 'OPTIMIZATIONGOAL_PROMOTION_CLAIM_OFFER', '领券');
INSERT INTO "marketing_convert_type" VALUES (30, 2, 'OPTIMIZATIONGOAL_ECOMMERCE_ADD_TO_WISHLIST', '商品收藏');
INSERT INTO "marketing_convert_type" VALUES (31, 2, 'OPTIMIZATIONGOAL_CONFIRM_EFFECTIVE_LEADS_RESERVATION', '有效表单预约');
INSERT INTO "marketing_convert_type" VALUES (32, 2, 'OPTIMIZATIONGOAL_PAGE_RECEIPT', '签收');
INSERT INTO "marketing_convert_type" VALUES (33, 2, 'OPTIMIZATIONGOAL_PAGE_SCAN_CODE', '加企业微信客服');
INSERT INTO "marketing_convert_type" VALUES (34, 2, 'OPTIMIZATIONGOAL_SELECT_COURSE', '选课');
INSERT INTO "marketing_convert_type" VALUES (35, 2, 'OPTIMIZATIONGOAL_CONFIRM_POTENTIAL_CUSTOMER_PHONE', '电话潜在客户');
INSERT INTO "marketing_convert_type" VALUES (36, 2, 'OPTIMIZATIONGOAL_MOBILE_APP_AD_INCOME', '广告变现');
INSERT INTO "marketing_convert_type" VALUES (37, 2, 'OPTIMIZATIONGOAL_MOBILE_APP_ACCREDIT', '小游戏授权');
INSERT INTO "marketing_convert_type" VALUES (38, 2, 'OPTIMIZATIONGOAL_PURCHASE_MEMBER_CARD', '首次会员购买');
INSERT INTO "marketing_convert_type" VALUES (39, 2, 'OPTIMIZATIONGOAL_EXTERNAL_LINK_CLICK', '外链点击');
INSERT INTO "marketing_convert_type" VALUES (40, 2, 'OPTIMIZATIONGOAL_BUY_COUPONS', '购券');
INSERT INTO "marketing_convert_type" VALUES (41, 2, 'OPTIMIZATIONGOAL_LEAVE_INFORMATION', '留资');
INSERT INTO "marketing_convert_type" VALUES (42, 2, 'OPTIMIZATIONGOAL_CORE_ACTION', '关键行为');
INSERT INTO "marketing_convert_type" VALUES (43, 2, 'OPTIMIZATIONGOAL_ONE_DAY_RETENTION_RATIO', '次留率');
INSERT INTO "marketing_convert_type" VALUES (44, 2, 'OPTIMIZATIONGOAL_ECOMMERCE_CHECKOUT', 'H5付费次数');
INSERT INTO "marketing_convert_type" VALUES (45, 2, 'OPTIMIZATIONGOAL_PAGE_EFFECTIVE_ONLINE_CONSULT', '有效在线咨询');
INSERT INTO "marketing_convert_type" VALUES (46, 2, 'OPTIMIZATIONGOAL_APP_PURCHASE', 'App付费次数');
INSERT INTO "marketing_convert_type" VALUES (47, 2, 'OPTIMIZATIONGOAL_APP_REGISTER', 'App注册');
INSERT INTO "marketing_convert_type" VALUES (48, 2, 'OPTIMIZATIONGOAL_PROMOTION_CLICK_KEY_PAGE', 'H5注册');
INSERT INTO "marketing_convert_type" VALUES (49, 2, 'OPTIMIZATIONGOAL_PAGE_EFFECTIVE_PHONE_CALL', '有效电话拨打');
INSERT INTO "marketing_convert_type" VALUES (50, 2, 'OPTIMIZATIONGOAL_LEADS', '表单预约');
INSERT INTO "marketing_convert_type" VALUES (60, 1, 'AD_CONVERT_TYPE_QQ', 'qq咨询');
INSERT INTO "marketing_convert_type" VALUES (51, 1, 'AD_CONVERT_TYPE_PHONE', '电话拨打');
INSERT INTO "marketing_convert_type" VALUES (52, 1, 'AD_CONVERT_TYPE_FORM', '表单提交');
INSERT INTO "marketing_convert_type" VALUES (53, 1, 'AD_CONVERT_TYPE_MAP_SEARCH', '地图搜索');
INSERT INTO "marketing_convert_type" VALUES (54, 1, 'AD_CONVERT_TYPE_DOWNLOAD_FINISH', '下载完成');
INSERT INTO "marketing_convert_type" VALUES (55, 1, 'AD_CONVERT_TYPE_BUTTON', '按钮跳转');
INSERT INTO "marketing_convert_type" VALUES (56, 1, 'AD_CONVERT_TYPE_XPATH', 'xpath类型转换');
INSERT INTO "marketing_convert_type" VALUES (57, 1, 'AD_CONVERT_TYPE_VIEW', '关键页面浏览');
INSERT INTO "marketing_convert_type" VALUES (58, 1, 'AD_CONVERT_TYPE_ACTIVE', '激活');
INSERT INTO "marketing_convert_type" VALUES (59, 1, 'AD_CONVERT_TYPE_DOWNLOAD_START', '下载按钮download_start');
INSERT INTO "marketing_convert_type" VALUES (61, 1, 'AD_CONVERT_TYPE_LOTTERY', '抽奖');
INSERT INTO "marketing_convert_type" VALUES (62, 1, 'AD_CONVERT_TYPE_VOTE', '投票');
INSERT INTO "marketing_convert_type" VALUES (63, 1, 'AD_CONVERT_TYPE_ACTIVE_REGISTER', '激活且注册');
INSERT INTO "marketing_convert_type" VALUES (64, 1, 'AD_CONVERT_TYPE_PAY', '激活且付费');
INSERT INTO "marketing_convert_type" VALUES (65, 1, 'AD_CONVERT_TYPE_INSTALL_FINISH', '安装完成');
INSERT INTO "marketing_convert_type" VALUES (66, 1, 'AD_CONVERT_TYPE_PHONE_CONFIRM', '智能电话-确认拨打');
INSERT INTO "marketing_convert_type" VALUES (67, 1, 'AD_CONVERT_TYPE_PHONE_CONNECT', '智能电话-确认接通');
INSERT INTO "marketing_convert_type" VALUES (68, 1, 'AD_CONVERT_TYPE_PHONE_EFFECTIVE', '智能电话-有效接通');
INSERT INTO "marketing_convert_type" VALUES (69, 1, 'AD_CONVERT_TYPE_CONSULT_EFFECTIVE', '有效咨询');
INSERT INTO "marketing_convert_type" VALUES (71, 1, 'AD_CONVERT_TYPE_APP_UV', 'app内访问');
INSERT INTO "marketing_convert_type" VALUES (72, 1, 'AD_CONVERT_TYPE_APP_CART', 'app内添加购物车（电商）');
INSERT INTO "marketing_convert_type" VALUES (73, 1, 'AD_CONVERT_TYPE_APP_PAY', 'app内付费');
INSERT INTO "marketing_convert_type" VALUES (74, 1, 'AD_CONVERT_TYPE_SALES_LEAD', '销售线索');
INSERT INTO "marketing_convert_type" VALUES (76, 1, 'AD_CONVERT_TYPE_CUSTOMER_EFFECTIVE', '有效获客');
INSERT INTO "marketing_convert_type" VALUES (77, 1, 'AD_CONVERT_TYPE_EFFECTIVE_COPY', '关键页面到达&有效内容复制');
INSERT INTO "marketing_convert_type" VALUES (78, 1, 'AD_CONVERT_TYPE_COUPON', '卡券领取');
INSERT INTO "marketing_convert_type" VALUES (79, 1, 'AD_CONVERT_TYPE_APP_DETAIL_UV', 'app内详情页到站uv');
INSERT INTO "marketing_convert_type" VALUES (80, 1, 'AD_CONVERT_TYPE_RSS', '账号关注');
INSERT INTO "marketing_convert_type" VALUES (81, 1, 'AD_CONVERT_TYPE_FORM_CONNECT', '表单提交-已接通');
INSERT INTO "marketing_convert_type" VALUES (82, 1, 'AD_CONVERT_TYPE_FORM_ANSWER', '有效沟通');
INSERT INTO "marketing_convert_type" VALUES (83, 1, 'AD_CONVERT_TYPE_DIALBACK', '提交回呼电话');
INSERT INTO "marketing_convert_type" VALUES (85, 1, 'AD_CONVERT_TYPE_DIALBACK_CONNECT', '回呼电话-确认接通');
INSERT INTO "marketing_convert_type" VALUES (86, 1, 'AD_CONVERT_TYPE_FORM_DEEP', '分层表单');
INSERT INTO "marketing_convert_type" VALUES (87, 1, 'AD_CONVERT_TYPE_UPDATE_LEVEL', '激活且升级');
INSERT INTO "marketing_convert_type" VALUES (88, 1, 'AD_CONVERT_TYPE_CREATE_GAMEROLE', '激活且创建角色');
INSERT INTO "marketing_convert_type" VALUES (89, 1, 'AD_CONVERT_TYPE_NEXT_DAY_OPEN', '激活且次留');
INSERT INTO "marketing_convert_type" VALUES (90, 1, 'AD_CONVERT_TYPE_INVALID_CLUE', '无效线索');
INSERT INTO "marketing_convert_type" VALUES (91, 1, 'AD_CONVERT_TYPE_INTENTION_CLUE', '有意向客户');
INSERT INTO "marketing_convert_type" VALUES (92, 1, 'AD_CONVERT_TYPE_HIGH_VALUE_CLUE', '高价值客户');
INSERT INTO "marketing_convert_type" VALUES (93, 1, 'AD_CONVERT_TYPE_PAID_CLUE', '已成单');
INSERT INTO "marketing_convert_type" VALUES (95, 1, 'AD_CONVERT_TYPE_LIKE_ACTION', '视频点赞');
INSERT INTO "marketing_convert_type" VALUES (96, 1, 'AD_CONVERT_TYPE_FOLLOW_ACTION', '账户关注');
INSERT INTO "marketing_convert_type" VALUES (97, 1, 'AD_CONVERT_TYPE_COMMENT_ACTION', '视频评论');
INSERT INTO "marketing_convert_type" VALUES (98, 1, 'AD_CONVERT_TYPE_LOCATION_ACTION', 'POI点击');
INSERT INTO "marketing_convert_type" VALUES (99, 1, 'AD_CONVERT_TYPE_SHOPPING_ACTION', '购物车点击');
INSERT INTO "marketing_convert_type" VALUES (100, 1, 'AD_CONVERT_TYPE_REDIRECT_TO_SHOP', '调起店铺');
INSERT INTO "marketing_convert_type" VALUES (101, 1, 'AD_CONVERT_TYPE_LINK_ACTION', 'link点击');
INSERT INTO "marketing_convert_type" VALUES (103, 1, 'AD_CONVERT_TYPE_SUCCESSORDER_ACTION', '小店转化');
INSERT INTO "marketing_convert_type" VALUES (104, 1, 'AD_CONVERT_TYPE_POI_COLLECT', 'poi地址点击');
INSERT INTO "marketing_convert_type" VALUES (105, 1, 'AD_CONVERT_TYPE_POI_ADDRESS_CLICK', 'poi收藏');
INSERT INTO "marketing_convert_type" VALUES (106, 1, 'AD_CONVERT_TYPE_RESERVATION', 'poi预定');
INSERT INTO "marketing_convert_type" VALUES (107, 1, 'AD_CONVERT_TYPE_MESSAGE_ACTION', '私信消息');
INSERT INTO "marketing_convert_type" VALUES (108, 1, 'AD_CONVERT_TYPE_SHARE_ACTION', '分享');
INSERT INTO "marketing_convert_type" VALUES (110, 1, 'AD_CONVERT_TYPE_CLICK_SHOPWINDOW', '访问主页商品橱窗');
INSERT INTO "marketing_convert_type" VALUES (111, 1, 'AD_CONVERT_TYPE_CLICK_DOWNLOAD', '访问主页下载应用');
INSERT INTO "marketing_convert_type" VALUES (112, 1, 'AD_CONVERT_TYPE_CLICK_CALL_DY', '点击主页内电话拨打');
INSERT INTO "marketing_convert_type" VALUES (113, 1, 'AD_CONVERT_TYPE_CLICK_WEBSITE', '访问主页官网');
INSERT INTO "marketing_convert_type" VALUES (114, 1, 'AD_CONVERT_PAGE_VIEW', '访问目标页面');
INSERT INTO "marketing_convert_type" VALUES (115, 1, 'AD_CONVERT_TYPE_MESSAGE', '短信');
INSERT INTO "marketing_convert_type" VALUES (116, 1, 'AD_CONVERT_TYPE_REDIRECT', '页面跳转');
INSERT INTO "marketing_convert_type" VALUES (117, 1, 'AD_CONVERT_TYPE_SHOPPING', '商品购买');
INSERT INTO "marketing_convert_type" VALUES (118, 1, 'AD_CONVERT_TYPE_CONSULT', '在线咨询');
INSERT INTO "marketing_convert_type" VALUES (119, 1, 'AD_CONVERT_TYPE_WECHAT', '微信');
INSERT INTO "marketing_convert_type" VALUES (121, 1, 'AD_CONVERT_TYPE_MULTIPLE', '多转化事件');
INSERT INTO "marketing_convert_type" VALUES (122, 1, 'AD_CONVERT_TYPE_POI_MULTIPLE', 'POI门店多转化目标');
INSERT INTO "marketing_convert_type" VALUES (123, 1, 'AD_CONVERT_TYPE_MULTI_NATIVE_ACTION', '互动');
INSERT INTO "marketing_convert_type" VALUES (125, 1, 'AD_CONVERT_TYPE_PRE_LOAN_CREDIT', '互联网金融-预授信');
INSERT INTO "marketing_convert_type" VALUES (126, 1, 'AD_CONVERT_TYPE_LOAN_CREDIT', '互联网金融-授信');
INSERT INTO "marketing_convert_type" VALUES (127, 1, 'AD_CONVERT_TYPE_IDCARD_INFORMATION', '身份证信息填写完成');
INSERT INTO "marketing_convert_type" VALUES (128, 1, 'AD_CONVERT_TYPE_BANKCARD_INFORMATION', '银行卡信息填写完成');
INSERT INTO "marketing_convert_type" VALUES (129, 1, 'AD_CONVERT_TYPE_PERSONAL_INFORMATION', '补充个人信息填写完成');
INSERT INTO "marketing_convert_type" VALUES (130, 1, 'AD_CONVERT_TYPE_CERTIFICATION_INFORMATION', '用户活体认证信息上传完成');
INSERT INTO "marketing_convert_type" VALUES (131, 1, 'AD_CONVERT_TYPE_LT_ROI', '广告变现ROI');
INSERT INTO "marketing_convert_type" VALUES (132, 1, 'AD_CONVERT_TYPE_LIVE_HOMEPAGE', '直播导流');
INSERT INTO "marketing_convert_type" VALUES (134, 1, 'AD_CONVERT_TYPE_FEED_LIVE_HOMEPAGE', '火山feed进入直播页');
INSERT INTO "marketing_convert_type" VALUES (135, 1, 'AD_CONVERT_TYPE_AUTHORIZATION', '授权（电商）');
INSERT INTO "marketing_convert_type" VALUES (136, 1, 'AD_CONVERT_TYPE_COMMODITY_CLICK', '快上电商推广目的');
INSERT INTO "marketing_convert_type" VALUES (137, 1, 'AD_CONVERT_TYPE_CONSULT_CLUE', '留咨咨询');
INSERT INTO "marketing_convert_type" VALUES (138, 1, 'AD_CONVERT_TYPE_BOOST', '自然助推');
INSERT INTO "marketing_convert_type" VALUES (139, 1, 'AD_CONVERT_TYPE_STAY_TIME', '店铺停留');
INSERT INTO "marketing_convert_type" VALUES (140, 1, 'AD_CONVERT_TYPE_PURCHASE_OF_GOODS', '商品签收');
INSERT INTO "marketing_convert_type" VALUES (141, 1, 'AD_CONVERT_TYPE_PURCHASE_ROI', '付费ROI');
INSERT INTO "marketing_convert_type" VALUES (143, 1, 'AD_CONVERT_TYPE_LIVE_FOLLOW_ACITON', '直播间关注');
INSERT INTO "marketing_convert_type" VALUES (144, 1, 'AD_CONVERT_TYPE_LIVE_COMMENT_ACTION', '直播间评论');
INSERT INTO "marketing_convert_type" VALUES (145, 1, 'AD_CONVERT_TYPE_LIVE_GIFT_ACTION', '直播间内打赏');
INSERT INTO "marketing_convert_type" VALUES (146, 1, 'AD_CONVERT_TYPE_LIVE_SLIDECART_CLICK_ACTION', '直播间查看购物车');
INSERT INTO "marketing_convert_type" VALUES (147, 1, 'AD_CONVERT_TYPE_LIVE_CLICK_PRODUCT_ACTION', '直播间查看商品');
INSERT INTO "marketing_convert_type" VALUES (148, 1, 'AD_CONVERT_TYPE_LIVE_ENTER_ACTION', '直播间观看');
INSERT INTO "marketing_convert_type" VALUES (150, 1, 'AD_CONVERT_TYPE_NOTIFY_DOWNLOAD', '预约下载');
INSERT INTO "marketing_convert_type" VALUES (151, 1, 'AD_CONVERT_TYPE_PREMIUM_PAYMENT', '保险支付');
INSERT INTO "marketing_convert_type" VALUES (152, 1, 'AD_CONVERT_TYPE_MESSAGE_CLICK', '私信点击');
INSERT INTO "marketing_convert_type" VALUES (153, 1, 'AD_CONVERT_TYPE_UG_ROI', '内广roi');
INSERT INTO "marketing_convert_type" VALUES (154, 1, 'AD_CONVERT_TYPE_ENTER_HOMEPAGE', '进入个人主页');
INSERT INTO "marketing_convert_type" VALUES (155, 1, 'AD_CONVERT_TYPE_SHOPPING_CART', '商品购物车点击');
INSERT INTO "marketing_convert_type" VALUES (156, 1, 'AD_CONVERT_TYPE_WECHAT_REGISTER', '微信内注册');
INSERT INTO "marketing_convert_type" VALUES (157, 1, 'AD_CONVERT_TYPE_WECHAT_PAY', '微信内付费');
INSERT INTO "marketing_convert_type" VALUES (159, 1, 'AD_CONVERT_TYPE_LIVE_STAY_TIME', '直播间停留');
INSERT INTO "marketing_convert_type" VALUES (160, 1, 'AD_CONVERT_TYPE_NEW_FOLLOW_ACTION', '粉丝增长');
INSERT INTO "marketing_convert_type" VALUES (161, 1, 'AD_CONVERT_TYPE_APPLET_CLICK', '小程序互动');
INSERT INTO "marketing_convert_type" VALUES (162, 1, 'AD_CONVERT_TYPE_MESSAGE_SERVICE', '私信服务');
INSERT INTO "marketing_convert_type" VALUES (70, 1, 'AD_CONVERT_TYPE_APP_ORDER', 'app内下单（电商）');
INSERT INTO "marketing_convert_type" VALUES (75, 1, 'AD_CONVERT_TYPE_GAME_ADDICTION', '关键行为（原深度转化）');
INSERT INTO "marketing_convert_type" VALUES (84, 1, 'AD_CONVERT_TYPE_DIALBACK_CONFIRM', '回呼电话-确认拨打');
INSERT INTO "marketing_convert_type" VALUES (94, 1, 'AD_CONVERT_TYPE_NATIVE_ACTION', '原生互动');
INSERT INTO "marketing_convert_type" VALUES (102, 1, 'AD_CONVERT_TYPE_DEEP_PURCHASE', '多次付费');
INSERT INTO "marketing_convert_type" VALUES (109, 1, 'AD_CONVERT_TYPE_CLICK_LANDING_PAGE', '访问推广详情页');
INSERT INTO "marketing_convert_type" VALUES (120, 1, 'AD_CONVERT_TYPE_OTHER', '其他');
INSERT INTO "marketing_convert_type" VALUES (124, 1, 'AD_CONVERT_TYPE_LOAN_COMPLETION', '互联网金融-完件');
INSERT INTO "marketing_convert_type" VALUES (133, 1, 'AD_CONVERT_TYPE_REDIRECT_TO_STORE', '店铺导流');
INSERT INTO "marketing_convert_type" VALUES (142, 1, 'AD_CONVERT_TYPE_LIVE_NATIVE_ACITON', '直播间原生互动');
INSERT INTO "marketing_convert_type" VALUES (149, 1, 'AD_CONVERT_TYPE_LIVE_SUCCESSORDER_ACTION', '直播间成单');
INSERT INTO "marketing_convert_type" VALUES (158, 1, 'AD_CONVERT_TYPE_MESSAGE_INTERACTION', '沟通互动');
INSERT INTO "marketing_convert_type" VALUES (163, 1, 'AD_CONVERT_TYPE_MESSAGE_CLUE', '私信留资');
INSERT INTO "marketing_convert_type" VALUES (164, 1, 'AD_CONVERT_TYPE_LIVE_FANS_ACTION', '直播间加入粉丝团');
INSERT INTO "marketing_convert_type" VALUES (165, 1, 'AD_CONVERT_TYPE_CLUE_CONFIRM', '回访_信息确认');
INSERT INTO "marketing_convert_type" VALUES (166, 1, 'AD_CONVERT_TYPE_CLUE_INTERFLOW', '回访_加为好友');
INSERT INTO "marketing_convert_type" VALUES (167, 1, 'AD_CONVERT_TYPE_CLUE_HIGH_INTENTION', '回访_高潜成交');
INSERT INTO "marketing_convert_type" VALUES (168, 1, 'AD_CONVERT_TYPE_SUBMIT_CERTIFICATION', '提交认证');
INSERT INTO "marketing_convert_type" VALUES (169, 1, 'AD_CONVERT_TYPE_FIRST_RENTAL_ORDER', '首次发单');
INSERT INTO "marketing_convert_type" VALUES (170, 1, 'AD_CONVERT_TYPE_LIVE_COMPONENT_CLICK', '组件点击');
INSERT INTO "marketing_convert_type" VALUES (171, 1, 'AD_CONVERT_TYPE_LIVE_BUSINESS_FITTING', '直播间组件点击');
INSERT INTO "marketing_convert_type" VALUES (172, 1, 'AD_CONVERT_TYPE_CLUE_PAY_SUCCEED', '支付_存在意向');
INSERT INTO "marketing_convert_type" VALUES (173, 1, 'AD_CONVERT_TYPE_OTO_STAY_TIME', '团单浏览');
INSERT INTO "marketing_convert_type" VALUES (174, 1, 'AD_CONVERT_TYPE_OTO_PAY', '团购支付');
INSERT INTO "marketing_convert_type" VALUES (175, 1, 'AD_CONVERT_TYPE_PREMIUM_ROI', '保费ROI');
INSERT INTO "marketing_convert_type" VALUES (176, 1, 'AD_CONVERT_TYPE_MESSAGE_ORDER_SUCCESS', '私信成单');
INSERT INTO "marketing_convert_type" VALUES (177, 1, 'AD_CONVERT_TYPE_MESSAGE_JOIN_GROUP', '私信用户入群');
INSERT INTO "marketing_convert_type" VALUES (178, 1, 'AD_CONVERT_TYPE_LIVE_JOIN_GROUP', '粉丝入群');
INSERT INTO "marketing_convert_type" VALUES (179, 1, 'AD_CONVERT_TYPE_LIVE_APPOINTMENT', '预约直播');
INSERT INTO "marketing_convert_type" VALUES (180, 1, 'AD_CONVERT_TYPE_FOLLOW_LIVE_ENTER', '粉丝访问直播');
INSERT INTO "marketing_convert_type" VALUES (181, 1, 'AD_CONVERT_TYPE_FOLLOW_CLICK_PRODUCT', '关注并加购');
INSERT INTO "marketing_convert_type" VALUES (182, 1, 'AD_CONVERT_TYPE_FOLLOW_VIDEO_PLAY_FINISH', '粉丝观看');
INSERT INTO "marketing_convert_type" VALUES (183, 1, 'AD_CONVERT_TYPE_GAMESTATION_DOWNLOAD_DOUPLUS', '游戏站下载 - DOU +');
