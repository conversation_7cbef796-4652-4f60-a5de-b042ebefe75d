-- 落地页 - 上报 - 上报转化目标类型 - 表
alter table landing_page_upload_configuration_types add column if not exists compare_type int default null;
comment on column landing_page_upload_configuration_types.compare_type is '触发条件（客户开口次数）-比较类型(0-等于，1-大于，2-大于等于，3-小于，4-小于等于)';
alter table landing_page_upload_configuration_types add column if not exists work_wechat_open_num int8 default 1;
comment on column landing_page_upload_configuration_types.work_wechat_open_num is '企业微信客户开口次数（大于等于）：数字类型，默认1次';
alter table landing_page_upload_configuration_types add column if not exists sex varchar default null;
comment on column landing_page_upload_configuration_types.sex is '性别：全部、男、女';
alter table landing_page_upload_configuration_types add column if not exists fuzzy_matching_field int default 0;
comment on column landing_page_upload_configuration_types.fuzzy_matching_field is '是否模糊匹配字段';
alter table landing_page_upload_configuration_types add column if not exists key_word varchar[] DEFAULT '{}';
comment on column landing_page_upload_configuration_types.key_word is '关键词：字符串，默认为null';

-- 落地页 - 上报 - 上报转化目标类型 - 表
alter table customer_upload_record add column if not exists compare_type int default null;
comment on column customer_upload_record.compare_type is '触发条件（客户开口次数）-比较类型(0-等于，1-大于，2-大于等于，3-小于，4-小于等于)';
alter table customer_upload_record add column if not exists work_wechat_open_num int8 default 1;
comment on column customer_upload_record.work_wechat_open_num is '企业微信客户开口次数（大于等于）：数字类型，默认1次';
alter table customer_upload_record add column if not exists sex varchar default null;
comment on column customer_upload_record.sex is '性别：null/ALL-全部、FEMALE-女、MALE-男、UNKNOWN-未知';
alter table customer_upload_record add column if not exists fuzzy_matching_field int default 0;
comment on column customer_upload_record.fuzzy_matching_field is '是否模糊匹配字段';
alter table customer_upload_record add column if not exists key_word varchar[] DEFAULT '{}';
comment on column customer_upload_record.key_word is '关键词：字符串，默认为null';

-- 落地页-企业微信客服-企业标签-分组-表
drop table if exists enterprise_wechat_tag_config_group;
create table if not exists enterprise_wechat_tag_config_group (
    id                              bigserial not null primary key,
    corpid                          varchar default null,
    work_wechat_tag_group_id        varchar default null,
    name                            varchar default null,
    order_number                    integer default 0,
    work_wechat_tag_created_author  integer default 0,
    created_at                      timestamp not null DEFAULT now(),
    updated_at                      timestamp not null DEFAULT now()
);
comment on table enterprise_wechat_tag_config_group is '落地页-企业微信客服-企业标签规则-分组-表';
comment on column enterprise_wechat_tag_config_group.id is '自增主键';
comment on column enterprise_wechat_tag_config_group.corpid is '企业微信公司id，关联表：enterprise_wechats.corpid';
comment on column enterprise_wechat_tag_config_group.work_wechat_tag_group_id is '分组ID（第三方企业微信侧分组id）';
comment on column enterprise_wechat_tag_config_group.name is '分组名称';
comment on column enterprise_wechat_tag_config_group.order_number is '标签组排序的次序值，order值大的排序靠前';
comment on column enterprise_wechat_tag_config_group.work_wechat_tag_created_author is '企业微信标签库-标签创建者标记';
comment on column enterprise_wechat_tag_config_group.created_at is '创建时间';
comment on column enterprise_wechat_tag_config_group.updated_at is '更新时间';

-- 落地页-企业微信客服-企业标签-表
drop table if exists enterprise_wechat_tag_config;
create table if not exists enterprise_wechat_tag_config (
    id                              bigserial not null primary key,
    corpid                          varchar default null,
    tag_group_id                    bigint default null,
    work_wechat_tag_group_id        varchar default null,
    work_wechat_tag_id              varchar default null,
    tag_name                        varchar default null,
    sex                             varchar default 'ALL',
    time_conditions                 int4 default 0,
    time_hour_value                 int4 default 0,
    time_minute_value               int4 default 1,
    trigger_conditions              int4 default 0,
    compare_type                    int default 2,
    compare_value                   int default null,
    fuzzy_matching_field            int default 0,
    key_word                        varchar[] DEFAULT '{}',
    order_number                    integer default 0,
    work_wechat_tag_created_author  integer default 0,
    created_at                      timestamp not null DEFAULT now(),
    updated_at                      timestamp not null DEFAULT now()
);
comment on table enterprise_wechat_tag_config is '落地页-企业微信客服-企业标签规则-表';
comment on column enterprise_wechat_tag_config.id is '自增主键';
comment on column enterprise_wechat_tag_config.corpid is '企业微信公司id，关联表：enterprise_wechats.corpid';
comment on column enterprise_wechat_tag_config.tag_group_id is '分组id，关联表：enterprise_wechat_tag_group.id';
comment on column enterprise_wechat_tag_config.work_wechat_tag_group_id is '分组ID（第三方企业微信侧分组id）';
comment on column enterprise_wechat_tag_config.work_wechat_tag_id is '标签ID（第三方企业微信侧标签id）';
comment on column enterprise_wechat_tag_config.tag_name is '标签名称';
comment on column enterprise_wechat_tag_config.sex is '性别条件';
comment on column enterprise_wechat_tag_config.time_conditions is '时间条件-选项值';
comment on column enterprise_wechat_tag_config.time_hour_value is '时间条件-时间值（小时）';
comment on column enterprise_wechat_tag_config.time_minute_value is '时间条件-时间值（分钟）';
comment on column enterprise_wechat_tag_config.trigger_conditions is '触发条件（客户开口次数、客户回复关键词、销售回复关键词）-选项值';
comment on column enterprise_wechat_tag_config.compare_type is '触发条件（客户开口次数）-比较类型(0-等于，1-大于，2-大于等于，3-小于，4-小于等于)';
comment on column enterprise_wechat_tag_config.compare_value is '触发条件（客户开口次数）-比较值';
comment on column enterprise_wechat_tag_config.fuzzy_matching_field is '触发条件（客户回复关键词、销售回复关键词）-是否模糊匹配-选项值';
comment on column enterprise_wechat_tag_config.key_word is '触发条件（客户回复关键词、销售回复关键词）-是否模糊匹配-匹配值';
comment on column enterprise_wechat_tag_config.order_number is '标签排序的次序值，order值大的排序靠前';
comment on column enterprise_wechat_tag_config.work_wechat_tag_created_author is '企业微信标签库-标签创建者标记';
comment on column enterprise_wechat_tag_config.created_at is '创建时间';
comment on column enterprise_wechat_tag_config.updated_at is '更新时间';


-- 企业微信-会话存档-员工表
drop table if exists enterprise_session_employee;
create table if not exists enterprise_session_employee (
    id                              bigserial not null primary key,
    advertiser_account_group_id     bigint,
    agent_id                        varchar,
    session_archive_id              bigint,
    corpid                          bigint      default null,
    user_id                         varchar   default null,
    open_user_id                    varchar   default null,
    user_name                       varchar default null,
    sex                             int    default null,
    status                          int    default 0,
    ext                             json,
    types                           integer[] DEFAULT '{}',
    created_at                      timestamp not null DEFAULT now(),
    updated_at                      timestamp not null DEFAULT now()
);
comment on table enterprise_session_employee is '企业微信-会话存档-员工表';
comment on column enterprise_session_employee.id is '自增主键';
comment on column enterprise_session_employee.advertiser_account_group_id is 'pmp项目id，关联表：marketing_advertiser_account_group.id';
comment on column enterprise_session_employee.agent_id is 'agent_id';
comment on column enterprise_session_employee.corpid is '企业微信公司id，关联表：enterprise_wechats.corpid';
comment on column enterprise_session_employee.session_archive_id is '企业微信会话存档id，关联表：enterprise_wechat_session_archive.id';
comment on column enterprise_session_employee.user_id is '员工ID';
comment on column enterprise_session_employee.open_user_id is '加密的员工ID';
comment on column enterprise_session_employee.user_name is '员工名称';
comment on column enterprise_session_employee.sex is '性别';
comment on column enterprise_session_employee.status is '在职状态 0:在职 1:离职';
comment on column enterprise_session_employee.ext is '请求接口返回的全部参数';
comment on column enterprise_session_employee.types is '类型 1表示办公版；2表示服务版；3表示企业版';
comment on column enterprise_session_employee.created_at is '创建时间';
comment on column enterprise_session_employee.updated_at is '更新时间';
CREATE INDEX if not exists enterprise_session_employee_employee_id_index ON enterprise_session_employee (user_id);

create unique index if not exists enterprise_session_employee_unique_index on enterprise_session_employee (user_id,session_archive_id);

-- 企业微信-会话存档-客户表
drop table if exists enterprise_session_employee_customers;
create table if not exists enterprise_session_employee_customers (
    id                              bigserial not null primary key,
    enterprise_wechat_id            bigint   default null,
    corpid                          varchar      default null,
    employee_id                     varchar      default null,
    user_id                         varchar default null,
    open_user_id                    varchar default null,
    user_name                       varchar default null,
    sex                             int    default null,
    head_image_url                  varchar default null,
    status                          int    default 0,
    work_wechat_user_open_num       int    default 0,
    total_work_wechat_user_open_num int    default 0,
    external_user_open_num          int    default 0,
    total_external_user_open_num    int    default 0,
    add_at                          timestamp default null,
    created_at                      timestamp not null DEFAULT now(),
    updated_at                      timestamp not null DEFAULT now()
    );
comment on table enterprise_session_employee_customers is '企业微信-会话存档-客户表';
comment on column enterprise_session_employee_customers.id is '自增主键';
comment on column enterprise_session_employee_customers.corpid is '企业微信公司id，关联表：enterprise_wechats.corpid';
comment on column enterprise_session_employee_customers.employee_id is '员工ID';
comment on column enterprise_session_employee_customers.user_id is '客户ID';
comment on column enterprise_session_employee_customers.open_user_id is '服务商下客户id';
comment on column enterprise_session_employee_customers.user_name is '客户名称';
comment on column enterprise_session_employee_customers.sex is '性别 0:未知 1:男 2:女';
comment on column enterprise_session_employee_customers.head_image_url is '头像';
comment on column enterprise_session_employee_customers.status is '是否开启会话存档 0：开启 1：关闭';
comment on column enterprise_session_employee_customers.work_wechat_user_open_num is '客服（员工/销售）开口次数（每次回复此客户一条消息递增1）';
comment on column enterprise_session_employee_customers.total_work_wechat_user_open_num is '总的（从最开始加好友）  客服（员工/销售）开口次数（每次回复此客户一条消息递增1）';
comment on column enterprise_session_employee_customers.external_user_open_num is '外部联系人（客户）开口次数（每次回复一条消息递增1）';
comment on column enterprise_session_employee_customers.total_external_user_open_num is '总的（从最开始加好友） 外部联系人（客户）开口次数（每次回复一条消息递增1）';
comment on column enterprise_session_employee_customers.add_at is '好友添加时间';
comment on column enterprise_session_employee_customers.created_at is '创建时间';
comment on column enterprise_session_employee_customers.updated_at is '更新时间';

CREATE INDEX if not exists enterprise_session_employee_customers_employee_id_index ON enterprise_session_employee_customers (employee_id);
CREATE INDEX if not exists enterprise_session_employee_customers_user_id_index ON enterprise_session_employee_customers (user_id);
CREATE INDEX if not exists enterprise_session_employee_customers_corpid_and_open_user_id_index ON enterprise_session_employee_customers (corpid,open_user_id);

-- 企业微信-员工客户-聊天记录表
drop table if exists enterprise_session_employee_customer_chat_records;
create table if not exists enterprise_session_employee_customer_chat_records (
    id                              bigserial not null primary key,
    corpid                          bigint      default null,
    enterprise_wechat_corp_id       varchar     default null,
    seq                             varchar     default null,
    msgid                           varchar     default null,
    send_time                       timestamp   not null,
    employee_id                     varchar      default null,
    open_employee_id                varchar      default null,
    user_id                         varchar default null,
    open_user_id                    varchar default null,
    chat_type                       int    default null,
    send_type                       int    default null,
    content                         json    default '{}'::json,
    file_expire_at                  timestamp DEFAULT null,
    revoke_status                   int4     default 0,
    created_at                      timestamp not null DEFAULT now(),
    updated_at                      timestamp not null DEFAULT now()
    );
comment on table enterprise_session_employee_customer_chat_records is '企业微信-员工客户-聊天记录表';
comment on column enterprise_session_employee_customer_chat_records.id is '自增主键';
comment on column enterprise_session_employee_customer_chat_records.send_time is '发送时间';
comment on column enterprise_session_employee_customer_chat_records.corpid is '企业微信公司id，关联表：enterprise_wechats.corpid';
comment on column enterprise_session_employee_customer_chat_records.enterprise_wechat_corp_id is '企业微信corpId';
comment on column enterprise_session_employee_customer_chat_records.seq is '消息的seq值，标识消息的序号';
comment on column enterprise_session_employee_customer_chat_records.msgid is '企微消息记录id';
comment on column enterprise_session_employee_customer_chat_records.employee_id is '员工ID';
comment on column enterprise_session_employee_customer_chat_records.open_employee_id is '加密员工ID';
comment on column enterprise_session_employee_customer_chat_records.user_id is '客户ID';
comment on column enterprise_session_employee_customer_chat_records.open_user_id is '服务商下客户userid';
comment on column enterprise_session_employee_customer_chat_records.chat_type is '消息类型';
comment on column enterprise_session_employee_customer_chat_records.send_type is '发送方 0：成员  1：客户';
comment on column enterprise_session_employee_customer_chat_records.content is '消息内容';
comment on column enterprise_session_employee_customer_chat_records.file_expire_at is '文件过期时间';
comment on column enterprise_session_employee_customer_chat_records.revoke_status is '消息撤回状态 0:正常 1:撤回';
comment on column enterprise_session_employee_customer_chat_records.created_at is '创建时间';
comment on column enterprise_session_employee_customer_chat_records.updated_at is '更新时间';

CREATE INDEX if not exists enterprise_session_employee_customer_chat_records_employee_id_index ON enterprise_session_employee_customer_chat_records (employee_id);
CREATE INDEX if not exists enterprise_session_employee_customer_chat_records_open_employee_id_index ON enterprise_session_employee_customer_chat_records (open_employee_id);
CREATE INDEX if not exists enterprise_session_employee_customer_chat_records_user_id_index ON enterprise_session_employee_customer_chat_records (user_id);
CREATE INDEX if not exists enterprise_session_employee_customer_chat_records_msgid_index ON enterprise_session_employee_customer_chat_records (msgid);
CREATE INDEX if not exists enterprise_session_employee_customer_chat_records_send_time_index ON enterprise_session_employee_customer_chat_records (send_time);
CREATE INDEX if not exists enterprise_session_employee_customer_chat_records_open_user_id_index ON enterprise_session_employee_customer_chat_records (open_user_id);

-- 企业微信-会话存档-微信客户-客户标签表
drop table if exists enterprise_session_customer_tag;
create table if not exists enterprise_session_customer_tag (
    id                              bigserial not null primary key,
    enterprise_wechat_id            bigint      default null,
    corpid                          varchar   default null,
    tag_id                          varchar   not null,
    tag_name                        varchar   not null,
    employee_id                     varchar      default null,
    user_id                         varchar default null,
    open_user_id                    varchar default null,
    build_time                      timestamp    default null,
    build_type                      int default null,
    created_at                      timestamp not null DEFAULT now(),
    updated_at                      timestamp not null DEFAULT now()
    );
comment on table enterprise_session_customer_tag is '企业微信-会话存档-微信客户-客户标签表';
comment on column enterprise_session_customer_tag.id is '自增主键';
comment on column enterprise_session_customer_tag.enterprise_wechat_id is '企业微信公司id，关联表：enterprise_wechats.corpid';
comment on column enterprise_session_customer_tag.corpid is '企业微信corpid';
comment on column enterprise_session_customer_tag.tag_id is '标签ID';
comment on column enterprise_session_customer_tag.tag_name is '标签名称';
comment on column enterprise_session_customer_tag.employee_id is '员工ID';
comment on column enterprise_session_customer_tag.user_id is '客户ID';
comment on column enterprise_session_customer_tag.open_user_id is '服务商下userID';
comment on column enterprise_session_customer_tag.build_time is '标签创建时间（第三方）';
comment on column enterprise_session_customer_tag.build_type is '创建标签方式：0-第三方-企业微信默认标签  1-一叶系统打的标签';
comment on column enterprise_session_customer_tag.created_at is '创建时间';
comment on column enterprise_session_customer_tag.updated_at is '更新时间';

CREATE INDEX if not exists enterprise_session_customer_tag_employee_id_index ON enterprise_session_customer_tag (employee_id);
CREATE INDEX if not exists enterprise_session_customer_tag_user_id_index ON enterprise_session_customer_tag (user_id);
CREATE INDEX if not exists enterprise_session_customer_tag_open_user_id_index ON enterprise_session_customer_tag (open_user_id);
CREATE unique index if not exists enterprise_session_customer_tag_cropid_open_user_id_employee_id_tag_id_unindex ON enterprise_session_customer_tag (corpid,open_user_id,employee_id,tag_id);
