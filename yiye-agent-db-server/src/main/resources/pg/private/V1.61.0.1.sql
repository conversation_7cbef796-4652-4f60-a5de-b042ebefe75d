CREATE TABLE marketing_material_designer_team_rel
(
    id             bigserial primary key,
    material_id    bigint  not null,
    signature      varchar not null,
    team_id        bigint,
    role_id        bigint,
    designer_id    bigint,
    team_ratio     decimal(10, 4),
    role_ratio     decimal(10, 4),
    designer_ratio decimal(10, 4),
    created_at     timestamp(6),
    updated_at     timestamp(6)
);
COMMENT ON COLUMN marketing_material_designer_team_rel.id IS 'ID';
COMMENT ON COLUMN marketing_material_designer_team_rel.material_id IS '一叶素材id';
COMMENT ON COLUMN marketing_material_designer_team_rel.signature IS '素材唯一md5';
COMMENT ON COLUMN marketing_material_designer_team_rel.team_id IS '团队id';
COMMENT ON COLUMN marketing_material_designer_team_rel.role_id IS '角色id';
COMMENT ON COLUMN marketing_material_designer_team_rel.designer_id IS '设计师id';
COMMENT ON COLUMN marketing_material_designer_team_rel.team_ratio IS '团队占比';
COMMENT ON COLUMN marketing_material_designer_team_rel.role_ratio IS '角色占比';
COMMENT ON COLUMN marketing_material_designer_team_rel.designer_ratio IS '人员占比';
COMMENT ON COLUMN marketing_material_designer_team_rel.created_at IS '创建时间';
COMMENT ON COLUMN marketing_material_designer_team_rel.updated_at IS '修改时间';

create index idx_marketing_material_designer_team_rel_material_id on marketing_material_designer_team_rel (material_id);
create index idx_marketing_material_designer_team_rel_signature on marketing_material_designer_team_rel (signature);
