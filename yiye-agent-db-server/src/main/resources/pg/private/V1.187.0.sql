-- 落地页动态二维码自定义列
update marketing_customer_field set field_no = 3 where name = '创建时间' and type = 9 ;
update marketing_customer_field set field_no = 4 where name = '更新时间' and type = 9 ;
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (9, 0, null, '基础属性', false, 'dynamicCode', '动态二维码', null, null, now(), now(), 0, 0, 0, null, null, 5, 2, '动态二维码', false, 160, 112,0);
delete from marketing_customer_field where type = 9 and default_data!=0;

-- 落地页审核状态
alter table landing_page add column if not exists review bigint default 0;
comment on column landing_page.review is '是否可编辑 0:未审核 1:审核中 2:审核失败 3:审核成功';

-- 落地页动态二维码客服分组ID
alter table landing_page add column if not exists wechat_customer_service_group_id bigint;
comment on column landing_page.wechat_customer_service_group_id is '客服分组ID';

-- 落地页企业推PageId
alter table landing_page add column if not exists qiyetui_page_id varchar;
comment on column landing_page.qiyetui_page_id is '企业推PageId';

-- 客服表添加企业推用ID
ALTER TABLE "customer"
    ADD COLUMN "qiye_personnel_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "customer"."qiye_personnel_id" IS '企业推唯一用户ID';

-- PV表添加企业推用ID
ALTER TABLE "page_view_info"
    ADD COLUMN "qiye_personnel_id" varchar COLLATE "pg_catalog"."default";
COMMENT ON COLUMN "page_view_info"."qiye_personnel_id" IS '企业推唯一用户ID';



-- 客资列表自定义列-新增企业推唯一用户id
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (11, 0, null, '基础属性', 'f', 'qiyePersonnelId', '企业推用户ID', '', null, now(), now(), 0, 0, 0, null, null, 5, 4, '企业推用户ID', 'f', NULL, 112,0);
update marketing_customer_field set field_no = 0 where type = 11 and setting_category = '基础属性' and field = 'customerGroupName' and default_data = 0;
update marketing_customer_field set field_no = 1 where type = 11 and setting_category = '基础属性' and field = 'wechatOpenid' and default_data = 0;
update marketing_customer_field set field_no = 2 where type = 11 and setting_category = '基础属性' and field = 'wechatUnionid' and default_data = 0;
update marketing_customer_field set field_no = 3 where type = 11 and setting_category = '基础属性' and field = 'qiyePersonnelId' and default_data = 0;
update marketing_customer_field set field_no = 4 where type = 11 and setting_category = '基础属性' and field = 'platformSources' and default_data = 0;
update marketing_customer_field set field_no = 5 where type = 11 and setting_category = '基础属性' and field = 'remarks' and default_data = 0;
update marketing_customer_field set field_no = 6 where type = 11 and setting_category = '基础属性' and field = 'flowSourceName' and default_data = 0;
delete from marketing_customer_field where type = 11 and default_data=1;

-- 调取企业推scheme记录表
create table qiye_scheme_call_record
(
    id                                  bigserial not null primary key,
    pid                                 varchar,
    status                              integer,
    qiye_personnel_id                    varchar,
    advertiser_account_group_id         int8,
    created_at                        timestamp(6) default now()
);

comment on table qiye_scheme_call_record is '企业推小程序scheme调取记录表';
comment on column qiye_scheme_call_record.id is 'id';
comment on column qiye_scheme_call_record.pid is '调取时传递的pid';
comment on column qiye_scheme_call_record.status is '调取状态 0:成功 1:失败';
comment on column qiye_scheme_call_record.qiye_personnel_id is '企业推唯一用户ID';
comment on column qiye_scheme_call_record.advertiser_account_group_id is '项目组ID';
comment on column qiye_scheme_call_record.created_at is '创建时间';

create index if not exists idx_scheme_call_pid on qiye_scheme_call_record (pid);

create table qiyetui_wechat_applet_config
(
    id                                  bigserial not null primary key,
    applet_original_id                  varchar,
    wechat_applet_appid                 varchar,
    status                              integer default 0,
    wechat_applet_name                  varchar,
    path                                varchar,
    created_at                         timestamp(6) default now()
);

comment on table qiyetui_wechat_applet_config is '企业推小程序列表';
comment on column qiyetui_wechat_applet_config.id is 'id';
comment on column qiyetui_wechat_applet_config.applet_original_id is '小程序原始id';
comment on column qiyetui_wechat_applet_config.wechat_applet_appid is 'appId';
comment on column qiyetui_wechat_applet_config.status is '当前状态 0:正常 1:禁用';
comment on column qiyetui_wechat_applet_config.wechat_applet_name is '小程序名称';
comment on column qiyetui_wechat_applet_config.path is '小程序路径';
comment on column qiyetui_wechat_applet_config.created_at is '创建时间';


create table qiyetui_wechat_applet_suppression_record
(
    id                                  bigserial not null primary key,
    applet_id                           integer,
    applet_appid                        varchar,
    created_at                          timestamp(6) default now()
);

comment on table qiyetui_wechat_applet_suppression_record is '企业推小程序列表';
comment on column qiyetui_wechat_applet_suppression_record.id is 'id';
comment on column qiyetui_wechat_applet_suppression_record.applet_id is '企业推小程序ID';
comment on column qiyetui_wechat_applet_suppression_record.applet_appid is '企业推小程序appId';
comment on column qiyetui_wechat_applet_suppression_record.created_at is '创建时间';



-- 企业推提审PageId agent关联表
create table qiyetui_page_agent_rel
(
    id                                  bigserial not null primary key,
    agent_id                            varchar,
    page_id                             varchar,
    created_at                        timestamp(6) default now()
);

comment on table qiyetui_page_agent_rel is '企业推提审PageId agent关联表';
comment on column qiyetui_page_agent_rel.id is 'id';
comment on column qiyetui_page_agent_rel.agent_id is 'agent_id';
comment on column qiyetui_page_agent_rel.page_id is '提审返回的pageId';
comment on column qiyetui_page_agent_rel.created_at is '创建时间';

create index if not exists idx_qiyetui_page on qiyetui_page_agent_rel (page_id);



-- 企业推回调接口记录增加版本
alter table qiyetui_callback_record add column if not exists version int default 0;
comment on column qiyetui_callback_record.version is '版本 0：旧版 1：新版';

-- BOSS项目维度日报表增加企业推相关统计字段
ALTER TABLE "boss_advertiser_account_group_day_report"
    ADD COLUMN "qiye_request_num" int8 DEFAULT 0,
  ADD COLUMN "qiye_request_success_num" int8 DEFAULT 0,
  ADD COLUMN "qiye_request_fail_num" int8 DEFAULT 0,
  ADD COLUMN "qiye_pv_num" int8 DEFAULT 0,
  ADD COLUMN "qiye_mini_pv_num" int8 DEFAULT 0;

COMMENT ON COLUMN "boss_advertiser_account_group_day_report"."qiye_request_num" IS '企业推小程序scheme请求数';
COMMENT ON COLUMN "boss_advertiser_account_group_day_report"."qiye_request_success_num" IS '企业推小程序scheme请求成功数';
COMMENT ON COLUMN "boss_advertiser_account_group_day_report"."qiye_request_fail_num" IS '企业推小程序scheme请求失败数';
COMMENT ON COLUMN "boss_advertiser_account_group_day_report"."qiye_pv_num" IS '企业推落地页PV';
COMMENT ON COLUMN "boss_advertiser_account_group_day_report"."qiye_mini_pv_num" IS '企业推小程序消耗PV';
