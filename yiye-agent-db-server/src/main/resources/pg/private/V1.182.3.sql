TRUNCATE TABLE  marketing_customer_field;

INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (26, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadRate', 'APP下载率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (27, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadActivationRate', 'APP下载激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (28, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appRetainedRate', 'APP次日留存率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (46, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadFinishCount', 'APP下载完成量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 3, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'buttonFormConvert', '按钮-表单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (2, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'formOrderConvertRate', '表单-下单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (5, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'formAppointmentPersonCount', '表单预约人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (7, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'validCluePersonNum', '有效线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 1, 3, 29, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (16, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'formAppointmentNum', '表单预约量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 1, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (17, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'validClueNum', '有效线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 28, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (25, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'validClueConvertRate', '有效线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 30, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (19, 0, 0, '销售转化(中台)', '业务转化', 'f', 'callLinkCount', '电话建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (20, 0, 0, '销售转化(中台)', '业务转化', 'f', 'appointmentCount', '预约数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (22, 0, 0, '销售转化(中台)', '业务转化', 'f', 'personWechatLinkCount', '个微建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (23, 0, 0, '销售转化(中台)', '业务转化', 'f', 'validClueCount', '有效线索数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (32, 0, 0, '销售转化(中台)', '业务转化', 'f', 'convertCount', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 20, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (36, 0, 0, '销售转化(中台)', '业务转化', 'f', 'officialFocusCount', '公众号关注数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (37, 0, 0, '销售转化(中台)', '业务转化', 'f', 'auditionCount', '试听数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (9, 0, 1, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 0, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (29, 0, 2, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (18, 0, 0, '网页行为(中台)', '行为转化', 'f', 'landingAvgStay', '平均停留时长', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 2, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (3, 0, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (6, 0, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountNum', '填单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (42, 0, 0, '基础指标(中台)', '行为转化', 'f', 'landingPagePv', '落地页PV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (43, 0, 0, '基础指标(中台)', '行为转化', 'f', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (47, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appInstallCount', 'APP安装量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (48, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appActivationNum', 'APP激活总量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 3, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (49, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appRetainedPersonNum', 'APP次日留存量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (55, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appRegisterRate', 'APP注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (56, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appActivationRegisterRate', 'APP激活注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{1}', 3, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (57, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appInstallRate', 'APP安装率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (58, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appClickActivationRate', 'APP点击激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (84, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appRegisterNum', 'APP注册量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (88, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueConvertRate', '销售线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 26, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (89, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueConvertRate', '有效线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 30, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (91, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appInstallRate', 'APP安装率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (92, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appClickActivationRate', 'APP点击激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (50, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'formAppointmentRate', '表单预约率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (59, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'saleClueConvertRate', '销售线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 26, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (62, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'paymentNum', '付费行为量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (63, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'officialFocusNum', '公众号关注量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (64, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'saleClueNum', '销售线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 24, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (69, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'followNum', '关注数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (85, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'firstPaymentPersonNum', '首次付费行为人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (86, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'saleCluePersonNum', '销售线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{3}', 1, 3, 25, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (60, 0, 0, '销售转化(中台)', '业务转化', 'f', 'trialCount', '试用数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (71, 0, 0, '销售转化(中台)', '业务转化', 'f', 'paymentDepositCount', '支付定金数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (72, 0, 0, '销售转化(中台)', '业务转化', 'f', 'registerCount', '注册数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 22, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (74, 0, 0, '销售转化(中台)', '业务转化', 'f', 'payCount', '支付数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 18, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (87, 2, 8, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (51, 0, 4, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (52, 0, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountRate', '填单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (53, 0, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (54, 0, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (90, 2, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (65, 0, 0, '基础指标(中台)', '行为转化', 'f', 'landingPageUv', '落地页UV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (93, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentPersonCount', '表单预约人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (94, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueNum', '有效线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 28, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (95, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueCost1', '有效线索成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 31, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (96, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadCost', 'APP下载成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (101, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadFinishCount', 'APP下载完成量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 3, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (102, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appInstallCount', 'APP安装量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (104, 2, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCount', '有效线索数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (105, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentCost', '表单预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (106, 2, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCost2', '公众号关注成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (107, 2, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCost2', '有效线索成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (108, 2, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCount', '电话建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (109, 2, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCost', '电话建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (110, 2, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCount', '个微建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (111, 2, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCost', '个微建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (112, 2, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCount', '预约数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (113, 2, 0, '销售线索(中台)', '业务转化', 'f', 'auditionCount', '试听数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (114, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appRetainedPersonNum', 'APP次日留存量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (115, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'buttonCount', '按钮数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 23, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (119, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'formOrderConvertRate', '表单-下单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (121, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadRate', 'APP下载率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (123, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentRate', '表单预约率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (124, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'buttonFormConvert', '按钮-表单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (126, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadActivationRate', 'APP下载激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (127, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appRegisterRate', 'APP注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (128, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appActivationRegisterRate', 'APP激活注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{1}', 3, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (129, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appRetainedRate', 'APP次日留存率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (130, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'thumbUpCost', '点赞成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 22, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (131, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'thumbUpCount', '点赞数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 21, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (132, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusCost1', '公众号关注成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (134, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueNum', '销售线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 24, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (135, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'orderAmount', '订单金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (136, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'orderUnitPrice', '下单客单价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (137, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'orderROI', '下单ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (138, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentNum', '付费行为量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (139, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentAmount', '付费金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (140, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'firstPaymentPersonNum', '首次付费行为人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (141, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusNum', '公众号关注量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (142, 2, 0, '销售线索(中台)', '业务转化', 'f', 'convertCount', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 20, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (143, 2, 0, '销售线索(中台)', '业务转化', 'f', 'convertCost', '转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 21, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (144, 2, 0, '销售线索(中台)', '业务转化', 'f', 'registerCount', '注册数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 22, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (150, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentNum', '表单预约量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 1, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (151, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'followNum', '关注数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (152, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'validCluePersonNum', '有效线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 1, 3, 29, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (153, 2, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCost', '预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (154, 2, 0, '销售线索(中台)', '业务转化', 'f', 'tryListenCost', '试听成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (155, 2, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCost', '试听完课成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (156, 2, 0, '销售线索(中台)', '业务转化', 'f', 'trialCount', '试用数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (157, 2, 0, '销售线索(中台)', '业务转化', 'f', 'trialCost', '试用成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (158, 2, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCount', '支付定金数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (159, 2, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCost', '支付定金成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 17, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (160, 2, 0, '销售线索(中台)', '业务转化', 'f', 'payCount', '支付数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 18, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (161, 2, 0, '销售线索(中台)', '业务转化', 'f', 'payCost', '支付成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 19, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (162, 2, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCount', '公众号关注数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (163, 2, 0, '销售线索(中台)', '业务转化', 'f', 'registerCost', '注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 23, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (164, 2, 0, '销售线索(中台)', '业务转化', 'f', 'activationCount', '激活数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 24, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (165, 2, 0, '销售线索(中台)', '业务转化', 'f', 'activationCost', '激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 25, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (168, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appInstallCost', 'APP安装成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (169, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appActivationNum', 'APP激活总量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 3, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (170, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'discussCost', '评论成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 20, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (171, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadActivationCost', 'APP激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (172, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appRegisterNum', 'APP注册量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (173, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'discussCount', '评论数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 19, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (174, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'shareCost', '分享成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 18, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (175, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appRegisterCost', 'APP注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (176, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'shareCount', '分享数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 17, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (177, 2, 0, 'app转化(媒体)', '业务转化', 'f', 'appRetainedCost', 'APP次日留存成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (178, 2, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCount', '试听完课数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (179, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueCost', '销售线索成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 27, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (180, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentCost', '付费行为成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (181, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'focusCost', '关注成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (184, 2, 0, '销售线索(媒体)', '业务转化', 'f', 'saleCluePersonNum', '销售线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{3}', 1, 3, 25, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (195, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueCost', '销售线索成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 27, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (197, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'formOrderConvertRate', '表单-下单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (198, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'buttonFormConvert', '按钮-表单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (200, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueConvertRate', '销售线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 26, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (201, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appRegisterCost', 'APP注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (202, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appActivationRegisterRate', 'APP激活注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{1}', 3, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (204, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadFinishCount', 'APP下载完成量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 3, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (205, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentNum', '表单预约量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 1, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (206, 3, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCost2', '公众号关注成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (207, 3, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCount', '有效线索数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (208, 3, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCost2', '有效线索成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (209, 3, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCount', '电话建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (211, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentNum', '付费行为量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (215, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'discussCost', '评论成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 20, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (216, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'discussCount', '评论数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 19, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (218, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'firstPaymentPersonNum', '首次付费行为人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (219, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusNum', '公众号关注量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (220, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusCost1', '公众号关注成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (221, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appRetainedPersonNum', 'APP次日留存量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (225, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueConvertRate', '有效线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 30, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (226, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appInstallRate', 'APP安装率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (227, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appClickActivationRate', 'APP点击激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (228, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadActivationRate', 'APP下载激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (229, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadRate', 'APP下载率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (230, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appRegisterRate', 'APP注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (231, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appRetainedRate', 'APP次日留存率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (187, 2, 5, NULL, '属性指标', 't', 'creativeName', '创意名称', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (234, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentRate', '表单预约率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (240, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueNum', '有效线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 28, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (241, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueCost1', '有效线索成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 31, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (242, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'orderROI', '下单ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (244, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadCost', 'APP下载成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (245, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appInstallCount', 'APP安装量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (246, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appInstallCost', 'APP安装成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (247, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appActivationNum', 'APP激活总量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 3, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (248, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadActivationCost', 'APP激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (249, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appRegisterNum', 'APP注册量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (251, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'validCluePersonNum', '有效线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 1, 3, 29, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (252, 3, 0, 'app转化(媒体)', '业务转化', 'f', 'appRetainedCost', 'APP次日留存成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (253, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentPersonCount', '表单预约人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (254, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentCost', '表单预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (255, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'followNum', '关注数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (257, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'shareCost', '分享成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 18, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (258, 3, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCount', '个微建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (259, 3, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCost', '个微建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (260, 3, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCount', '公众号关注数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (261, 3, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCount', '预约数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (262, 3, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCost', '预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (263, 3, 0, '销售线索(中台)', '业务转化', 'f', 'auditionCount', '试听数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (264, 3, 0, '销售线索(中台)', '业务转化', 'f', 'tryListenCost', '试听成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (265, 3, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCount', '试听完课数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (266, 3, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCost', '试听完课成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (267, 3, 0, '销售线索(中台)', '业务转化', 'f', 'trialCount', '试用数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (268, 3, 0, '销售线索(中台)', '业务转化', 'f', 'trialCost', '试用成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (269, 3, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCount', '支付定金数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (270, 3, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCost', '支付定金成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 17, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (271, 3, 0, '销售线索(中台)', '业务转化', 'f', 'payCount', '支付数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 18, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (272, 3, 0, '销售线索(中台)', '业务转化', 'f', 'payCost', '支付成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 19, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (273, 3, 0, '销售线索(中台)', '业务转化', 'f', 'convertCount', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 20, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (274, 3, 0, '销售线索(中台)', '业务转化', 'f', 'convertCost', '转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 21, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (275, 3, 0, '销售线索(中台)', '业务转化', 'f', 'registerCount', '注册数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 22, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (276, 3, 0, '销售线索(中台)', '业务转化', 'f', 'registerCost', '注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 23, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (277, 3, 0, '销售线索(中台)', '业务转化', 'f', 'activationCount', '激活数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 24, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (278, 3, 0, '销售线索(中台)', '业务转化', 'f', 'activationCost', '激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 25, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (279, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'shareCount', '分享数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 17, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (280, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'orderAmount', '订单金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (281, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'orderUnitPrice', '下单客单价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (282, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentAmount', '付费金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (283, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'focusCost', '关注成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (284, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentCost', '付费行为成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (285, 3, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCost', '电话建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (286, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueNum', '销售线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 24, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (287, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'buttonCount', '按钮数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 23, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (288, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'thumbUpCost', '点赞成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 22, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (289, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'thumbUpCount', '点赞数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 21, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (290, 3, 0, '销售线索(媒体)', '业务转化', 'f', 'saleCluePersonNum', '销售线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{3}', 1, 3, 25, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (301, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appClickActivationRate', 'APP点击激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (302, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'orderAmount', '订单金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (303, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentPersonCount', '表单预约人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (305, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'orderROI', '下单ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (306, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appActivationNum', 'APP激活总量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 3, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (307, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentAmount', '付费金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (308, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'shareCount', '分享数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 17, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (309, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueNum', '有效线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 28, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (310, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadActivationCost', 'APP激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (311, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appRegisterNum', 'APP注册量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (312, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueNum', '销售线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 24, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (316, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'buttonCount', '按钮数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 23, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (322, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueCost1', '有效线索成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 31, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (323, 1, 0, '销售线索(中台)', '业务转化', 'f', 'convertCount', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 20, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (324, 1, 0, '销售线索(中台)', '业务转化', 'f', 'trialCount', '试用数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (291, 3, 1, NULL, '属性指标', 't', 'platform', '媒体', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (325, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadActivationRate', 'APP下载激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (326, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appRegisterRate', 'APP注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (327, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appActivationRegisterRate', 'APP激活注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{1}', 3, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (328, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueConvertRate', '销售线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 26, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (329, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appInstallRate', 'APP安装率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (330, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'formOrderConvertRate', '表单-下单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (331, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentRate', '表单预约率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (332, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'buttonFormConvert', '按钮-表单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (333, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'validClueConvertRate', '有效线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 30, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (337, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadRate', 'APP下载率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (338, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appRetainedRate', 'APP次日留存率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (343, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusCost1', '公众号关注成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (344, 1, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCount', '试听完课数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (345, 1, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCost2', '公众号关注成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (346, 1, 0, '销售线索(中台)', '业务转化', 'f', 'payCount', '支付数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 18, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (347, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'thumbUpCount', '点赞数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 21, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (348, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appRegisterCost', 'APP注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (349, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadFinishCount', 'APP下载完成量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 3, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (350, 1, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCost', '电话建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (351, 1, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCost', '个微建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (352, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentNum', '付费行为量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (353, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'firstPaymentPersonNum', '首次付费行为人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (354, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'officialFocusNum', '公众号关注量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (355, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'saleClueCost', '销售线索成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 27, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (356, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'discussCost', '评论成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 20, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (357, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadCost', 'APP下载成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (358, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appInstallCount', 'APP安装量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (359, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'discussCount', '评论数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 19, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (360, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appInstallCost', 'APP安装成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (361, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'shareCost', '分享成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 18, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (362, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentNum', '表单预约量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 1, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (363, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'formAppointmentCost', '表单预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (364, 1, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCount', '有效线索数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (365, 1, 0, '销售线索(中台)', '业务转化', 'f', 'callLinkCount', '电话建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (367, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'validCluePersonNum', '有效线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 1, 3, 29, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (368, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'thumbUpCost', '点赞成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 22, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (370, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'orderUnitPrice', '下单客单价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (371, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'followNum', '关注数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (372, 1, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCount', '支付定金数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (373, 1, 0, '销售线索(中台)', '业务转化', 'f', 'paymentDepositCost', '支付定金成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 17, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (374, 1, 0, '销售线索(中台)', '业务转化', 'f', 'trialCost', '试用成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (375, 1, 0, '销售线索(中台)', '业务转化', 'f', 'auditionedClassCost', '试听完课成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (376, 1, 0, '销售线索(中台)', '业务转化', 'f', 'tryListenCost', '试听成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (377, 1, 0, '销售线索(中台)', '业务转化', 'f', 'validClueCost2', '有效线索成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (378, 1, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCost', '预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (379, 1, 0, '销售线索(中台)', '业务转化', 'f', 'personWechatLinkCount', '个微建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (380, 1, 0, '销售线索(中台)', '业务转化', 'f', 'appointmentCount', '预约数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (381, 1, 0, '销售线索(中台)', '业务转化', 'f', 'auditionCount', '试听数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (382, 1, 0, '销售线索(中台)', '业务转化', 'f', 'activationCost', '激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 25, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (383, 1, 0, '销售线索(中台)', '业务转化', 'f', 'payCost', '支付成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 19, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (384, 1, 0, '销售线索(中台)', '业务转化', 'f', 'registerCost', '注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 23, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (385, 1, 0, '销售线索(中台)', '业务转化', 'f', 'convertCost', '转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 21, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (386, 1, 0, '销售线索(中台)', '业务转化', 'f', 'activationCount', '激活数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 24, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (387, 1, 0, '销售线索(中台)', '业务转化', 'f', 'officialFocusCount', '公众号关注数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (388, 1, 0, '销售线索(中台)', '业务转化', 'f', 'registerCount', '注册数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 22, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (389, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appRetainedCost', 'APP次日留存成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (390, 1, 0, 'app转化(媒体)', '业务转化', 'f', 'appRetainedPersonNum', 'APP次日留存量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (395, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'focusCost', '关注成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (396, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'paymentCost', '付费行为成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (405, 1, 0, '销售线索(媒体)', '业务转化', 'f', 'saleCluePersonNum', '销售线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{3}', 1, 3, 25, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (427, 4, 0, '基础数据(中台)', '行为转化', 'f', 'landingAvgStay', '平均停留时长', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3, '平均停留时长', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (418, 4, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountNum', '表单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4, '一叶落地页表单提交成功数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (419, 4, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountRate', '表单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5, '表单提交数 / 落地页PV*100%,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (420, 4, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountCost', '表单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6, '花费/表单提交数,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (695, 7, 0, NULL, '属性指标', 'f', 'platformId', '媒体', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (696, 7, 0, NULL, '属性指标', 'f', 'accountName', '账户名称', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (697, 7, 0, NULL, '属性指标', 'f', 'accountId', '账户ID', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (698, 7, 0, NULL, '属性指标', 'f', 'campaignName', '计划名称', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (699, 7, 0, NULL, '属性指标', 'f', 'campaignId', '计划ID', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (703, 7, 2, NULL, '属性指标', 't', 'creativeName', '创意名称', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (704, 7, 2, NULL, '属性指标', 't', 'creativeId', '创意ID', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (705, 7, 0, NULL, '属性指标', 'f', 'creativeType', '创意类型', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (701, 7, 1, NULL, '属性指标', 't', 'adgroupId', '广告ID', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (700, 7, 1, NULL, '属性指标', 't', 'adgroupName', '广告名称', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (702, 7, 0, NULL, '属性指标', 'f', 'adgroupStatus', '广告状态', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (775, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadFinishCount', 'APP下载完成量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 3, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (776, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadRate', 'APP下载率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (777, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadCost', 'APP下载成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (778, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallCount', 'APP安装量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (779, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallRate', 'APP安装率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (780, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appInstallCost', 'APP安装成本', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (782, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appClickActivationRate', 'APP点击激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (783, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadActivationRate', 'APP下载激活率', '%', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (784, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appDownloadActivationCost', 'APP激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (785, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterNum', 'APP注册量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (786, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterRate', 'APP注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (781, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appActivationNum', 'APP激活总量', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 3, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (787, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appActivationRegisterRate', 'APP激活注册率', '%', NULL, NULL, NULL, 0, 4, 0, '{1}', 3, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (788, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRegisterCost', 'APP注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (789, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedPersonNum', 'APP次日留存量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (790, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedRate', 'APP次日留存率', '%', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 3, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (791, 7, 0, 'APP转化(媒体)', '业务转化', 'f', 'appRetainedCost', 'APP次日留存成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (38, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadCost', 'APP下载成本', '元', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (45, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appDownloadActivationCost', 'APP激活成本', '元', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (75, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appRegisterCost', 'APP注册成本', '元', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (82, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appRetainedCost', 'APP次日留存成本', '元', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (83, 0, 0, 'app转化(媒体)', '业务转化', 'f', 'appInstallCost', 'APP安装成本', '元', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 3, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (15, 0, 0, '展点信息(媒体)', '投放数据', 'f', 'thousandImpressAvgPrice', '千次展示均价', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (33, 0, 0, '展点信息(媒体)', '投放数据', 'f', 'avgPrice', '点击均价', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (8, 0, 3, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '元', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 1, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (794, 0, 0, '基础指标(中台)', '行为转化', 'f', 'identifyQrcodeCost', '长按二维码识别成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (795, 0, 0, '基础指标(中台)', '行为转化', 'f', 'addWorkWechatNum', '加企业微信数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (796, 0, 0, '基础指标(中台)', '行为转化', 'f', 'addWorkWechatRate', '加企业微信率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (797, 0, 0, '基础指标(中台)', '行为转化', 'f', 'addWorkWechatCost', '加企业微信成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (4, 0, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishCost', '订单完成成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (34, 0, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountCost', '填单提交成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (44, 0, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountCost', '订单提交成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (811, 0, 13, '基础转化(媒体)', '业务转化', 'f', 'convertNum', '目标转化量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (812, 0, 14, '基础转化(媒体)', '业务转化', 'f', 'targetConvertRate', '目标转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (813, 0, 15, '基础转化(媒体)', '业务转化', 'f', 'targetConvertCost', '目标转化成本', '元', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (814, 0, 0, '基础转化(媒体)', '业务转化', 'f', 'deepConvertNum', '深度转化量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (815, 0, 0, '基础转化(媒体)', '业务转化', 'f', 'deepConvertRate', '深度转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (816, 0, 0, '基础转化(媒体)', '业务转化', 'f', 'deepConvertCost', '深度转化成本', '元', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (817, 0, 0, '基础转化(媒体)', '业务转化', 'f', 'placeOrderNum', '下单量(网页)', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (818, 0, 0, '基础转化(媒体)', '业务转化', 'f', 'placeOrderRate', '下单率(网页)', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (819, 0, 0, '基础转化(媒体)', '业务转化', 'f', 'placeOrderCost', '下单成本(网页)', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (839, 4, 13, '转化数据(媒体)', '投放数据', 'f', 'convertNum', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 1, 0, '转化数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (914, 7, 13, '基础转化(媒体)', '业务转化', 'f', 'convertNum', '目标转化量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (915, 7, 14, '基础转化(媒体)', '业务转化', 'f', 'targetConvertRate', '目标转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (916, 7, 15, '基础转化(媒体)', '业务转化', 'f', 'targetConvertCost', '目标转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (917, 7, 0, '基础转化(媒体)', '业务转化', 'f', 'deepConvertNum', '深度转化量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (918, 7, 0, '基础转化(媒体)', '业务转化', 'f', 'deepConvertRate', '深度转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (919, 7, 0, '基础转化(媒体)', '业务转化', 'f', 'deepConvertCost', '深度转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (920, 7, 0, '基础转化(媒体)', '业务转化', 'f', 'placeOrderNum', '下单量(网页)', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 0, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (921, 7, 0, '基础转化(媒体)', '业务转化', 'f', 'placeOrderRate', '下单率(网页)', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (922, 7, 0, '基础转化(媒体)', '业务转化', 'f', 'placeOrderCost', '下单成本(网页)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (897, 7, 0, '基础指标(中台)', '行为转化', 'f', 'identifyQrcodeCost', '长按二维码识别成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (898, 7, 0, '基础指标(中台)', '行为转化', 'f', 'addWorkWechatNum', '加企业微信数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (899, 7, 0, '基础指标(中台)', '行为转化', 'f', 'addWorkWechatRate', '加企业微信率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (900, 7, 0, '基础指标(中台)', '行为转化', 'f', 'addWorkWechatCost', '加企业微信成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (895, 7, 0, '基础指标(中台)', '行为转化', 'f', 'identifyQrCodeNum', '长按二维码识别数(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 11, '页面内微信或企业微信二维码被长按识别的次数', 't', 240, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (896, 7, 0, '基础指标(中台)', '行为转化', 'f', 'identifyQrCodeRate', '长按二维码识别率(微信/企业微信)', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 12, '长按微信或企业微信二维码识别数占总浏览数的比例\n计算公式：长按微信或企业微信二维码识别数/总浏览数*100%', 't', 240, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (724, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'formOrderConvertRate', '表单-下单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (725, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'formAppointmentNum', '表单预约量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 1, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (726, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'formAppointmentPersonCount', '表单预约人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (727, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'formAppointmentRate', '表单预约率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (728, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'formAppointmentCost', '表单预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (729, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'buttonFormConvert', '按钮-表单转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (730, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'orderAmount', '订单金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (731, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'orderUnitPrice', '下单客单价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (732, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'orderROI', '下单ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (733, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'paymentNum', '付费行为量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (734, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'paymentCost', '付费行为成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (735, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'paymentAmount', '付费金额', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (736, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'firstPaymentPersonNum', '首次付费行为人数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (737, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'followNum', '关注数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (738, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'focusCost', '关注成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (739, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'officialFocusNum', '公众号关注量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (740, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'officialFocusCost1', '公众号关注成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (741, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'saleClueNum', '销售线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 24, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (742, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'saleCluePersonNum', '销售线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{3}', 1, 3, 25, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (743, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'saleClueConvertRate', '销售线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 26, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (744, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'saleClueCost', '销售线索成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 27, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (745, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'validClueNum', '有效线索量', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 28, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (746, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'validCluePersonNum', '有效线索人数', '', NULL, NULL, NULL, 0, 4, 0, '{2,3}', 1, 3, 29, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (747, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'validClueConvertRate', '有效线索转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 30, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (748, 7, 0, '销售转化(媒体)', '业务转化', 'f', 'validClueCost1', '有效线索成本(媒体)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 31, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (13, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'orderUnitPrice', '下单客单价', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (14, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'orderROI', '下单ROI', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (30, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'paymentAmount', '付费金额', '元', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (31, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'officialFocusCost1', '公众号关注成本(媒体)', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (61, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'formAppointmentCost', '表单预约成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (66, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'focusCost', '关注成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (67, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'paymentCost', '付费行为成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (68, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'saleClueCost', '销售线索成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 27, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (76, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'orderAmount', '订单金额', '元', NULL, NULL, NULL, 0, 4, 0, '{2}', 1, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (77, 0, 0, '销售转化(媒体)', '业务转化', 'f', 'validClueCost1', '有效线索成本(媒体)', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 3, 31, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (79, 0, 0, '销售转化(中台)', '业务转化', 'f', 'activationCount', '激活数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 24, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (81, 0, 0, '销售转化(中台)', '业务转化', 'f', 'auditionedClassCount', '试听完课数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (749, 7, 0, '销售转化(中台)', '业务转化', 'f', 'officialFocusCount', '公众号关注数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (750, 7, 0, '销售转化(中台)', '业务转化', 'f', 'officialFocusCost2', '公众号关注成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (751, 7, 0, '销售转化(中台)', '业务转化', 'f', 'validClueCount', '有效线索数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (752, 7, 0, '销售转化(中台)', '业务转化', 'f', 'validClueCost2', '有效线索成本(中台)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (753, 7, 0, '销售转化(中台)', '业务转化', 'f', 'callLinkCount', '电话建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (754, 7, 0, '销售转化(中台)', '业务转化', 'f', 'callLinkCost', '电话建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (755, 7, 0, '销售转化(中台)', '业务转化', 'f', 'personWechatLinkCount', '个微建联数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (756, 7, 0, '销售转化(中台)', '业务转化', 'f', 'personWechatLinkCost', '个微建联成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (757, 7, 0, '销售转化(中台)', '业务转化', 'f', 'appointmentCount', '预约数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (758, 7, 0, '销售转化(中台)', '业务转化', 'f', 'appointmentCost', '预约成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (759, 7, 0, '销售转化(中台)', '业务转化', 'f', 'auditionCount', '试听数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (760, 7, 0, '销售转化(中台)', '业务转化', 'f', 'tryListenCost', '试听成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (761, 7, 0, '销售转化(中台)', '业务转化', 'f', 'auditionedClassCount', '试听完课数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (762, 7, 0, '销售转化(中台)', '业务转化', 'f', 'auditionedClassCost', '试听完课成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (763, 7, 0, '销售转化(中台)', '业务转化', 'f', 'trialCount', '试用数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (764, 7, 0, '销售转化(中台)', '业务转化', 'f', 'trialCost', '试用成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (765, 7, 0, '销售转化(中台)', '业务转化', 'f', 'paymentDepositCount', '支付定金数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 16, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (766, 7, 0, '销售转化(中台)', '业务转化', 'f', 'paymentDepositCost', '支付定金成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 17, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (767, 7, 0, '销售转化(中台)', '业务转化', 'f', 'payCount', '支付数量', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 18, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (768, 7, 0, '销售转化(中台)', '业务转化', 'f', 'payCost', '支付成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 19, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (769, 7, 0, '销售转化(中台)', '业务转化', 'f', 'convertCount', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 20, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (770, 7, 0, '销售转化(中台)', '业务转化', 'f', 'convertCost', '转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 21, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (771, 7, 0, '销售转化(中台)', '业务转化', 'f', 'registerCount', '注册数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 22, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (772, 7, 0, '销售转化(中台)', '业务转化', 'f', 'registerCost', '注册成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 23, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (773, 7, 0, '销售转化(中台)', '业务转化', 'f', 'activationCount', '激活数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 24, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (774, 7, 0, '销售转化(中台)', '业务转化', 'f', 'activationCost', '激活成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 25, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (10, 0, 0, '销售转化(中台)', '业务转化', 'f', 'validClueCost2', '有效线索成本(中台)', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (11, 0, 0, '销售转化(中台)', '业务转化', 'f', 'officialFocusCost2', '公众号关注成本(中台)', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (12, 0, 0, '销售转化(中台)', '业务转化', 'f', 'paymentDepositCost', '支付定金成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 17, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (21, 0, 0, '销售转化(中台)', '业务转化', 'f', 'personWechatLinkCost', '个微建联成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (24, 0, 0, '销售转化(中台)', '业务转化', 'f', 'appointmentCost', '预约成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (35, 0, 0, '销售转化(中台)', '业务转化', 'f', 'callLinkCost', '电话建联成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (39, 0, 0, '销售转化(中台)', '业务转化', 'f', 'activationCost', '激活成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 25, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (40, 0, 0, '销售转化(中台)', '业务转化', 'f', 'trialCost', '试用成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 15, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (41, 0, 0, '销售转化(中台)', '业务转化', 'f', 'registerCost', '注册成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 23, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (70, 0, 0, '销售转化(中台)', '业务转化', 'f', 'convertCost', '转化成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 21, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (73, 0, 0, '销售转化(中台)', '业务转化', 'f', 'payCost', '支付成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 19, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (78, 0, 0, '销售转化(中台)', '业务转化', 'f', 'auditionedClassCost', '试听完课成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 13, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (80, 0, 0, '销售转化(中台)', '业务转化', 'f', 'tryListenCost', '试听成本', '元', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 3, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (798, 0, 0, '销售转化(中台)', '业务转化', 'f', 'clueFillNum', '线索填单数', '', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 26, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (799, 0, 0, '销售转化(中台)', '业务转化', 'f', 'clueFillRate', '线索填单率', '%', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 27, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (800, 0, 0, '销售转化(中台)', '业务转化', 'f', 'clueFillCost', '线索填单成本', '元', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 28, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (801, 0, 0, '销售转化(中台)', '业务转化', 'f', 'clueConnectNum', '线索接通数', '', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 29, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (802, 0, 0, '销售转化(中台)', '业务转化', 'f', 'clueConnectRate', '线索接通率', '%', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 30, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (803, 0, 0, '销售转化(中台)', '业务转化', 'f', 'clueConnectCost', '线索接通成本', '元', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 31, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (804, 0, 0, '销售转化(中台)', '业务转化', 'f', 'clueEffectiveNum', '线索有效数', '', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 32, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (805, 0, 0, '销售转化(中台)', '业务转化', 'f', 'clueEffectiveRate', '线索有效率', '%', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 33, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (806, 0, 0, '销售转化(中台)', '业务转化', 'f', 'clueEffectiveCost', '线索有效成本', '元', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 34, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (807, 0, 0, '销售转化(中台)', '业务转化', 'f', 'signClassNum', '报班数', '', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 35, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (808, 0, 0, '销售转化(中台)', '业务转化', 'f', 'signClassRate', '报班率', '%', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 36, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (809, 0, 0, '销售转化(中台)', '业务转化', 'f', 'signClassCost', '报班成本', '元', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 37, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (810, 0, 0, '销售转化(中台)', '业务转化', 'f', 'totalConvertRate', '总转化率', '%', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 38, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (901, 7, 0, '销售转化(中台)', '业务转化', 'f', 'clueFillNum', '线索填单数', '', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 26, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (902, 7, 0, '销售转化(中台)', '业务转化', 'f', 'clueFillRate', '线索填单率', '%', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 27, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (903, 7, 0, '销售转化(中台)', '业务转化', 'f', 'clueFillCost', '线索填单成本', '', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 28, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (904, 7, 0, '销售转化(中台)', '业务转化', 'f', 'clueConnectNum', '线索接通数', '', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 29, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (905, 7, 0, '销售转化(中台)', '业务转化', 'f', 'clueConnectRate', '线索接通率', '%', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 30, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (906, 7, 0, '销售转化(中台)', '业务转化', 'f', 'clueConnectCost', '线索接通成本', '', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 31, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (907, 7, 0, '销售转化(中台)', '业务转化', 'f', 'clueEffectiveNum', '线索有效数', '', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 32, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (908, 7, 0, '销售转化(中台)', '业务转化', 'f', 'clueEffectiveRate', '线索有效率', '%', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 33, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (909, 7, 0, '销售转化(中台)', '业务转化', 'f', 'clueEffectiveCost', '线索有效成本', '', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 34, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (910, 7, 0, '销售转化(中台)', '业务转化', 'f', 'signClassNum', '报班数', '', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 35, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (911, 7, 0, '销售转化(中台)', '业务转化', 'f', 'signClassRate', '报班率', '%', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 36, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (912, 7, 0, '销售转化(中台)', '业务转化', 'f', 'signClassCost', '报班成本', '', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 37, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (913, 7, 0, '销售转化(中台)', '业务转化', 'f', 'totalConvertRate', '总转化率', '%', NULL, '2021-09-23 14:34:06.387', '2021-09-23 14:34:06.387', 0, 4, 0, '{0}', 2, 3, 38, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (186, 2, 4, NULL, '属性指标', 't', 'adgroupId', '广告id', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (293, 3, 3, NULL, '属性指标', 't', 'adgroupId', '广告id', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (393, 1, 3, NULL, '属性指标', 't', 'adgroupId', '广告id', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (944, 8, 0, NULL, '属性指标', 'f', 'campaignId', '计划ID', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (945, 8, 0, NULL, '属性指标', 'f', 'campaignName', '计划名称', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (946, 8, 0, NULL, '属性指标', 'f', 'accountId', '账户ID', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (947, 8, 0, NULL, '属性指标', 'f', 'accountName', '账户名称', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (948, 8, 0, NULL, '属性指标', 'f', 'platformId', '媒体', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (962, 9, 1, NULL, '基础属性', 't', 'landingPageGroupName', '分组', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 5, 1, '落地页所属分组', 'f', 150, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (963, 9, 16, NULL, '基础属性', 't', 'createdAt', '创建时间', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 5, 2, '落地页创建时间', 't', 90, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (964, 9, 17, NULL, '基础属性', 't', 'updatedAt', '更新时间', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 5, 3, '落地页最后一次更新时间', 't', 90, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (965, 9, 2, NULL, '访问数据', 't', 'pageViewNum', '浏览数(PV)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 1, '页面被浏览的次数', 't', 150, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (943, 8, 0, NULL, '属性指标', 't', 'adgroupName', '广告名称', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (942, 8, 1, NULL, '属性指标', 't', 'adgroupId', '广告ID', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (941, 8, 2, NULL, '属性指标', 't', 'keyword', '关键词名称', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (940, 8, 3, NULL, '属性指标', 't', 'keywordId', '关键词ID', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (951, 8, 0, '展点信息(媒体)', '投放数据', 'f', 'topPageViews', '上方位展现', '', NULL, NULL, NULL, 0, 1, 0, '{5}', 0, 1, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (952, 8, 0, '展点信息(媒体)', '投放数据', 'f', 'topPClicks', '上方位点击', '', NULL, NULL, NULL, 0, 1, 0, '{5}', 0, 1, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (953, 8, 0, '展点信息(媒体)', '投放数据', 'f', 'topPay', '上方位消费', '', NULL, NULL, NULL, 0, 1, 0, '{5}', 0, 1, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (958, 8, 6, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 0, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (957, 8, 7, '展点信息(媒体)', '投放数据', 't', 'thousandImpressAvgPrice', '千次展示均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (966, 9, 0, NULL, '访问数据', 'f', 'visitorNum', '访客数(UV)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 2, '页面访客数', 'f', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (967, 9, 0, NULL, '访问数据', 'f', 'repeatVisitorNum', '重复访客数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 3, '重复访问的访客数量', 'f', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (968, 9, 0, NULL, '访问数据', 'f', 'repeatVisitorRate', '重复访客率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 4, '重复访客数占访客数的比例\n计算公式：重复访客数/访客数*100%', 'f', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (969, 9, 0, NULL, '访问数据', 'f', 'averageLengthOfStay', '平均停留时长(秒)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 5, '平均每次浏览页面的停留时间', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (970, 9, 9, NULL, '表单数据', 't', 'formSubmitNum', '表单提交数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 7, 1, '页面内提交表单的数量', 't', 150, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (971, 9, 10, NULL, '表单数据', 't', 'formSubmitRate', '表单提交率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 7, 2, '表单填单数占浏览数的比例\n计算公式：表单提交数 / 浏览数*100% ', 't', 150, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (972, 9, 11, NULL, '订单数据', 't', 'orderSubmitNum', '订单提交数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 8, 1, '页面内提交订单的数量', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (973, 9, 12, NULL, '订单数据', 't', 'orderSubmitRate', '订单提交率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 8, 2, '订单提交数数占浏览数的比例\n计算公式：订单提交数 / 浏览数*100%', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (974, 9, 13, NULL, '订单数据', 't', 'paymentNum', '订单完成数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 8, 3, '订单支付成功；包含0元购和货到付款', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (975, 9, 14, NULL, '订单数据', 't', 'paymentRate', '订单完成率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 8, 4, '订单完成数占订单提交数的比例\n计算公式：订单完成数 / 订单提交数*100%', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (976, 9, 15, NULL, '订单数据', 't', 'comprehensivePaymentRate', '订单综合完成率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 8, 5, '订单完成数占浏览数的比例\n计算公式：订单完成数 / 浏览数*100%', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (977, 9, 0, NULL, '优惠券数据', 'f', 'numberOfOrdersCompletedForCouponNum', '使用优惠券的订单完成数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 9, 1, '访客领取优惠券并且下单支付成功笔数(含使用优惠券后的0元购和货到付款)', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (978, 9, 0, NULL, '优惠券数据', 'f', 'numberOfOrdersCompletedForCouponRate', '使用优惠券的订单完成率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 9, 2, '使用优惠券的订单完成数占订单完成数的比例\n计算公式：折扣订单完成数/总的订单完成数*100%', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (981, 9, 5, NULL, '加粉数据', 't', 'addWorkWechatNum', '加企业微信数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 3, '页面内成功添加企业微信数', 't', 170, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (982, 9, 6, NULL, '加粉数据', 't', 'addWorkWechatRate', '加企业微信率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 4, '加企业微信数占浏览数的比例', 't', 170, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (983, 9, 7, NULL, '加粉数据', 't', 'followOfficialAccountNum', '微信公众号关注数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 5, '页面内成功关注微信公众号数', 't', 200, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (984, 9, 8, NULL, '加粉数据', 't', 'followOfficialAccountRate', '微信公众号关注率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 6, '	微信公众号关注数占浏览数的比例\n计算公式：微信公众号关注数/浏览数*100%', 't', 200, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (985, 10, 2, NULL, '基础属性', 't', 'remark', '渠道信息', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 5, 1, '自定义添加的渠道信息', 'f', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (986, 10, 1, NULL, '基础属性', 't', 'url', '渠道链接', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 5, 2, '渠道链接URL', 'f', 280, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (987, 10, 3, NULL, '基础属性', 't', 'uploadConfiguration', '上报配置', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 5, 3, '渠道链接配置的上报账户/媒体信息', 'f', 200, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (988, 10, 19, NULL, '基础属性', 't', 'createdAt', '创建时间', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 5, 4, '渠道链接创建时间', 't', 100, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (989, 10, 4, NULL, '访问数据', 't', 'pageViewNum', '浏览数(PV)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 1, '页面被浏览的次数', 't', 120, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (990, 10, 5, NULL, '访问数据', 't', 'visitorNum', '访客数(UV)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 2, '页面访客数', 'f', 120, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (991, 10, 0, NULL, '访问数据', 'f', 'repeatVisitorNum', '重复访客数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 3, '重复访问的访客数量', 'f', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (992, 10, 0, NULL, '访问数据', 'f', 'repeatVisitorRate', '重复访客率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 4, '重复访客数占访客数的比例\n计算公式：重复访客数/访客数*100%', 'f', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (993, 10, 0, NULL, '访问数据', 'f', 'averageLengthOfStay', '平均停留时长(秒)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 5, '平均每次浏览页面的停留时间', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (994, 10, 12, NULL, '表单数据', 't', 'formSubmitNum', '表单提交数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 7, 1, '页面内提交表单的数量', 't', 120, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (995, 10, 13, NULL, '表单数据', 't', 'formSubmitRate', '表单提交率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 7, 2, '表单填单数占浏览数的比例\n计算公式：表单提交数 / 浏览数*100% ', 't', 120, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (996, 10, 14, NULL, '订单数据', 't', 'orderSubmitNum', '订单提交数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 8, 1, '页面内提交订单的数量', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (997, 10, 15, NULL, '订单数据', 't', 'orderSubmitRate', '订单提交率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 8, 2, '订单提交数数占浏览数的比例\n计算公式：订单提交数 / 浏览数*100%', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (998, 10, 16, NULL, '订单数据', 't', 'paymentNum', '订单完成数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 8, 3, '订单支付成功；包含0元购和货到付款', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (999, 10, 17, NULL, '订单数据', 't', 'paymentRate', '订单完成率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 8, 4, '订单完成数占订单提交数的比例\n计算公式：订单完成数 / 订单提交数*100%', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1000, 10, 18, NULL, '订单数据', 't', 'comprehensivePaymentRate', '订单综合完成率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 8, 5, '订单完成数占浏览数的比例\n计算公式：订单完成数 / 浏览数*100%', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1001, 10, 0, NULL, '优惠券数据', 'f', 'numberOfOrdersCompletedForCouponNum', '使用优惠券的订单完成数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 9, 1, '访客领取优惠券并且下单支付成功笔数(含使用优惠券后的0元购和货到付款)', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1002, 10, 0, NULL, '优惠券数据', 'f', 'numberOfOrdersCompletedForCouponRate', '使用优惠券的订单完成率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 9, 2, '使用优惠券的订单完成数占订单完成数的比例\n计算公式：折扣订单完成数/总的订单完成数*100%', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1005, 10, 8, NULL, '加粉数据', 't', 'addWorkWechatNum', '加企业微信数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 3, '页面内成功添加企业微信数', 't', 120, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1006, 10, 9, NULL, '加粉数据', 't', 'addWorkWechatRate', '加企业微信率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 4, '加企业微信数占浏览数的比例', 't', 120, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1007, 10, 10, NULL, '加粉数据', 't', 'followOfficialAccountNum', '微信公众号关注数', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 5, '页面内成功关注微信公众号数', 't', 140, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1008, 10, 11, NULL, '加粉数据', 't', 'followOfficialAccountRate', '微信公众号关注率', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 6, '	微信公众号关注数占浏览数的比例\n计算公式：微信公众号关注数/浏览数*100%', 't', 140, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1009, 11, 0, NULL, '基础属性', 'f', 'customerGroupName', '所属分组', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 5, 1, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1010, 11, 0, NULL, '基础属性', 'f', 'wechatOpenid', '微信OpenID', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 5, 2, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1011, 11, 0, NULL, '基础属性', 'f', 'wechatUnionid', '微信UnionID', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 5, 3, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1012, 11, 9, NULL, '基础属性', 't', 'platformSources', '媒体来源', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 5, 4, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1013, 11, 23, NULL, '基础属性', 't', 'remarks', '备注', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 5, 5, NULL, 'f', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1014, 11, 1, NULL, '表单订单属性', 't', 'name', '姓名', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 1, NULL, 'f', 122, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1015, 11, 2, NULL, '表单订单属性', 't', 'phone', '电话', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 2, NULL, 'f', 122, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1016, 11, 0, NULL, '表单订单属性', 'f', 'identityCardNo', '身份证', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 3, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1017, 11, 0, NULL, '表单订单属性', 'f', 'qq', 'QQ', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 4, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1018, 11, 0, NULL, '表单订单属性', 'f', 'wechat', '微信(表单收集)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 5, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1019, 11, 0, NULL, '表单订单属性', 'f', 'email', '邮箱', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 6, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1020, 11, 0, NULL, '表单订单属性', 'f', 'orderNumber', '订单编号', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 7, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1021, 11, 0, NULL, '表单订单属性', 'f', 'orderStatus', '订单状态', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 8, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1022, 11, 0, NULL, '表单订单属性', 'f', 'paymentType', '付款方式', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 9, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1023, 11, 0, NULL, '表单订单属性', 'f', 'mchId', '支付商户号', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 10, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1024, 11, 0, NULL, '表单订单属性', 'f', 'standard1', '商品规格1', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 11, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1025, 11, 0, NULL, '表单订单属性', 'f', 'standard2', '商品规格2', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 12, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1026, 11, 0, NULL, '表单订单属性', 'f', 'standard3', '商品规格3', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 13, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1027, 11, 0, NULL, '表单订单属性', 'f', 'number', '数量', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 14, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1028, 11, 0, NULL, '表单订单属性', 'f', 'price', '单价', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 15, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1029, 11, 0, NULL, '表单订单属性', 'f', 'totalPrice', '总价', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 16, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1030, 11, 0, NULL, '表单订单属性', 'f', 'address', '地址', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 17, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1031, 11, 0, NULL, '表单订单属性', 'f', 'receiveRemark', '下单备注', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 18, NULL, 'f', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1032, 11, 0, NULL, '表单订单属性', 'f', 'formName', '所属表单', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 19, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1033, 11, 0, NULL, '表单订单属性', 'f', 'orderFormName', '所属订单', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 19, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1034, 11, 15, NULL, '表单订单属性', 't', 'landingPageName', '所属落地页(表单/订单)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 20, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1035, 11, 16, NULL, '表单订单属性', 't', 'channelName', '所属渠道(表单/订单提交)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 21, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1036, 11, 17, NULL, '表单订单属性', 't', 'url', '访问URL(表单/订单提交)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 11, 22, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1037, 11, 3, NULL, '加粉数据', 't', 'wechatAppletName', '昵称', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 1, NULL, 'f', 122, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1038, 11, 5, NULL, '加粉数据', 't', 'wechatAppletExternalUserid', '用户企业微信userid', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 2, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1039, 11, 6, NULL, '加粉数据', 't', 'wechatAppletGroupChatName', '客服名称/客服群名称', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 3, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1040, 11, 7, NULL, '加粉数据', 't', 'wechatAppletUserid', '客服userid/客服群ID', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 4, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1041, 11, 8, NULL, '加粉数据', 't', 'wechatAppletAddWay', '添加企业微信方式', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 5, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1042, 11, 4, NULL, '加粉数据', 't', 'tagName', '企业微信标签', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 6, NULL, 'f', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1043, 11, 12, NULL, '加粉数据', 't', 'wechatAppletLandingPageName', '所属落地页(成功添加企业微信)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 7, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1044, 11, 13, NULL, '加粉数据', 't', 'wechatAppletLandingPageChannelName', '所属渠道(成功添加企业微信)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 8, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1045, 11, 14, NULL, '加粉数据', 't', 'wechatAppletLandingPageViewUrl', '访问URL(成功添加企业微信)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 9, NULL, 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1046, 11, 10, NULL, '上报状态', 't', 'uploadPlatformId', '上报媒体', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 12, 1, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1047, 11, 11, NULL, '上报状态', 't', 'uploadState', '上报状态', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 12, 2, NULL, 'f', 320, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1048, 11, 18, NULL, '访问数据', 't', 'ip', 'IP', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 1, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1049, 11, 19, NULL, '访问数据', 't', 'province', 'IP归属省份', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 2, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1050, 11, 20, NULL, '访问数据', 't', 'city', 'IP归属城市', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 3, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1051, 11, 21, NULL, '访问数据', 't', 'accessDepth', '访问深度', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 4, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1052, 11, 22, NULL, '访问数据', 't', 'lengthOfStay', '停留时长', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 6, 5, NULL, 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (294, 3, 2, NULL, '属性指标', 't', 'accountId', '投放账户', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (392, 1, 4, NULL, '属性指标', 't', 'creativeName', '创意名称', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (394, 1, 1, NULL, '属性指标', 't', 'platform', '媒体', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (404, 1, 2, NULL, '属性指标', 't', 'accountId', '投放账户', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1053, 9, 19, NULL, '订单数据', 't', 'onlineShopBuyGoodsSuccessNum', '电商商品购买数', '', NULL, '2022-10-19 20:31:25.915168', '2022-10-19 20:31:25.915168', 0, 0, 0, NULL, NULL, 8, 3, '订单详情返回下单成功的数量', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1054, 9, 20, NULL, '订单数据', 't', 'onlineShopBuyGoodsSuccessRate', '电商商品购买率', '%', NULL, '2022-10-19 20:31:25.915168', '2022-10-19 20:31:25.915168', 0, 0, 0, NULL, NULL, 8, 3, '下单成功的数量/PV*100%', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1055, 10, 19, NULL, '订单数据', 't', 'onlineShopBuyGoodsSuccessNum', '电商商品购买数', '', NULL, '2022-10-19 20:31:25.915168', '2022-10-19 20:31:25.915168', 0, 0, 0, NULL, NULL, 8, 3, '订单详情返回下单成功的数量', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1056, 10, 20, NULL, '订单数据', 't', 'onlineShopBuyGoodsSuccessRate', '电商商品购买率', '%', NULL, '2022-10-19 20:31:25.915168', '2022-10-19 20:31:25.915168', 0, 0, 0, NULL, NULL, 8, 3, '下单成功的数量/PV*100%', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1077, 12, 1, NULL, '基础属性', 't', 'onlineStatus', '上线状态', '', NULL, '2022-10-19 20:31:28.230618', '2022-10-19 20:31:28.230618', 0, 0, 0, NULL, NULL, 13, 2, '上线状态', 't', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1076, 11, 0, NULL, '表单订单属性', 'f', 'provinceCityAreaCounty', '地区', '', NULL, '2022-10-19 20:31:27.57118', '2022-10-19 20:31:27.57118', 0, 0, 0, NULL, NULL, 11, 16, '省市区/县', 'f', 208, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1078, 12, 2, NULL, '基础属性', 't', 'autoRuleStatus', '自动化规则', '', NULL, '2022-10-19 20:31:28.230618', '2022-10-19 20:31:28.230618', 0, 0, 0, NULL, NULL, 13, 2, '自动化规则', 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1079, 12, 3, NULL, '基础属性', 't', 'wechatUserId', '对应企业成员账号', '', NULL, '2022-10-19 20:31:28.230618', '2022-10-19 20:31:28.230618', 0, 0, 0, NULL, NULL, 13, 2, '对应企业成员账号', 'f', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1080, 12, 4, NULL, '基础属性', 't', 'groupNames', '所属分组', '', NULL, '2022-10-19 20:31:28.230618', '2022-10-19 20:31:28.230618', 0, 0, 0, NULL, NULL, 13, 2, '所属分组', 'f', 200, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1081, 12, 5, NULL, '基础属性', 't', 'qrCodeWeight', '权重', '', NULL, '2022-10-19 20:31:28.230618', '2022-10-19 20:31:28.230618', 0, 0, 0, NULL, NULL, 13, 2, '权重', 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1082, 12, 6, NULL, '基础属性', 't', 'qrCodeImgUrl', '二维码', '', NULL, '2022-10-19 20:31:28.230618', '2022-10-19 20:31:28.230618', 0, 0, 0, NULL, NULL, 13, 2, '二维码', 'f', 112, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1083, 12, 7, NULL, '数据统计', 't', 'qrCodeShowNum', '二维码展示数', '', NULL, '2022-10-19 20:31:28.230618', '2022-10-19 20:31:28.230618', 0, 0, 0, NULL, NULL, 14, 2, '二维码展示数', 't', 200, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1084, 12, 8, NULL, '数据统计', 't', 'identifyQrCodeNum', '二维码长按识别数', '', NULL, '2022-10-19 20:31:28.230618', '2022-10-19 20:31:28.230618', 0, 0, 0, NULL, NULL, 14, 2, '二维码长按识别数', 't', 200, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1086, 12, 10, NULL, '数据统计', 't', 'landAddWorkWechatNum', '落地页链路成功加企业微信数', '', NULL, '2022-10-19 20:31:28.230618', '2022-10-19 20:31:28.230618', 0, 0, 0, NULL, NULL, 14, 2, '落地页链路成功加企业微信数', 't', 200, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1087, 12, 11, NULL, '数据统计', 't', 'otherAddWorkWechatNum', '其它方式成功加企业微信数', '', NULL, '2022-10-19 20:31:28.230618', '2022-10-19 20:31:28.230618', 0, 0, 0, NULL, NULL, 14, 2, '其它方式成功加企业微信数', 't', 200, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1088, 12, 12, NULL, '数据统计', 't', 'addWorkWechatRate', '成功加企业微信率', '%', NULL, '2022-10-19 20:31:28.230618', '2022-10-19 20:31:28.230618', 0, 0, 0, NULL, NULL, 14, 2, '成功加企业微信率', 't', 200, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1089, 12, 9, NULL, '数据统计', 't', 'qrcodeIdentityRate', '二维码长按识别率', '%', NULL, '2022-10-19 20:31:29.150153', '2022-10-19 20:31:29.150153', 0, 0, 0, NULL, NULL, 14, 2, '二维码长按识别率', 't', 200, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1090, 11, 23, NULL, '基础属性', 'f', 'flowSourceName', '流量来源', '', NULL, '2022-10-19 20:31:29.380042', '2022-10-19 20:31:29.380042', 0, 0, 0, NULL, NULL, 5, 6, NULL, 'f', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1091, 9, 23, NULL, '加粉数据', 'f', 'officialIdentifyQrCodeNum', '长按二维码识别数(公众号)', '', NULL, '2022-10-19 20:31:29.380042', '2022-10-19 20:31:29.380042', 0, 0, 0, NULL, NULL, 10, 7, '页面内公众号二维码被长按识别的次数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1093, 10, 23, NULL, '加粉数据', 'f', 'officialIdentifyQrCodeNum', '长按二维码识别数(公众号)', '', NULL, '2022-10-19 20:31:29.380042', '2022-10-19 20:31:29.380042', 0, 0, 0, NULL, NULL, 10, 7, '页面内公众号二维码被长按识别的次数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (979, 9, 3, NULL, '加粉数据', 't', 'identifyQrCodeNum', '长按二维码识别数(微信/企业微信)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 1, '页面内微信或企业微信二维码被长按识别的次数', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1003, 10, 6, NULL, '加粉数据', 't', 'identifyQrCodeNum', '长按二维码识别数(微信/企业微信)', '', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 1, '页面内微信或企业微信二维码被长按识别的次数', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (980, 9, 4, NULL, '加粉数据', 't', 'identifyQrCodeRate', '长按二维码识别率(微信/企业微信)', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 2, '长按微信或企业微信二维码识别数占总浏览数的比例\n计算公式：长按微信或企业微信二维码识别数/总浏览数*100%', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1004, 10, 7, NULL, '加粉数据', 't', 'identifyQrCodeRate', '长按二维码识别率(微信/企业微信)', '%', NULL, '2022-10-19 20:31:25.035627', '2022-10-19 20:31:25.035627', 0, 0, 0, NULL, NULL, 10, 2, '长按微信或企业微信二维码识别数占总浏览数的比例\n计算公式：长按微信或企业微信二维码识别数/总浏览数*100%', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1092, 9, 24, NULL, '加粉数据', 'f', 'officialIdentifyQrCodeRate', '长按二维码识别率(公众号)', '%', NULL, '2022-10-19 20:31:29.380042', '2022-10-19 20:31:29.380042', 0, 0, 0, NULL, NULL, 10, 8, '长按公众号二维码识别数占总浏览数的比例\n计算公式：长按公众号二维码识别数/总浏览数*100%', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1094, 10, 24, NULL, '加粉数据', 'f', 'officialIdentifyQrCodeRate', '长按二维码识别率(公众号)', '%', NULL, '2022-10-19 20:31:29.380042', '2022-10-19 20:31:29.380042', 0, 0, 0, NULL, NULL, 10, 8, '长按公众号二维码识别数占总浏览数的比例\n计算公式：长按公众号二维码识别数/总浏览数*100%', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (939, 8, 4, NULL, '属性指标', 't', 'status', '关键词状态', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 11, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (938, 8, 5, NULL, '属性指标', 't', 'matchType', '匹配模式', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 12, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (182, 2, 2, NULL, '属性指标', 't', 'platform', '媒体', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (183, 2, 1, NULL, '属性指标', 't', 'previewUrl', '封面缩略图', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (185, 2, 3, NULL, '属性指标', 't', 'accountId', '投放账户', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (292, 3, 4, NULL, '属性指标', 't', 'creativeName', '创意名称', '', NULL, NULL, NULL, 0, 4, 1, '{0}', NULL, 0, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (97, 2, 0, '视频播放(媒体)', '投放数据', 'f', 'videoPlay25Num', '25%进度播放数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (98, 2, 0, '视频播放(媒体)', '投放数据', 'f', 'videoPlay100Num', '播放完成数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (99, 2, 0, '视频播放(媒体)', '投放数据', 'f', 'playNum', '播放数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 2, 1, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (100, 2, 0, '视频播放(媒体)', '投放数据', 'f', 'validPlayNum', '有效播放数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 2, 1, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (120, 2, 0, '视频播放(媒体)', '投放数据', 'f', 'validPlayRate', '有效播放率', '%', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (145, 2, 0, '视频播放(媒体)', '投放数据', 'f', 'avgPlayTime', '平均播放时长', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 2, 1, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (146, 2, 0, '视频播放(媒体)', '投放数据', 'f', 'videoPlay75Num', '75%进度播放数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (147, 2, 0, '视频播放(媒体)', '投放数据', 'f', 'videoPlay50Num', '50%进度播放数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (148, 2, 0, '视频播放(媒体)', '投放数据', 'f', 'validPlayCost', '有效普播放成本', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (199, 3, 0, '视频播放(媒体)', '投放数据', 'f', 'validPlayRate', '有效播放率', '%', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (210, 3, 0, '视频播放(媒体)', '投放数据', 'f', 'playNum', '播放数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 2, 1, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (213, 3, 0, '视频播放(媒体)', '投放数据', 'f', 'videoPlay50Num', '50%进度播放数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (214, 3, 0, '视频播放(媒体)', '投放数据', 'f', 'avgPlayTime', '平均播放时长', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 2, 1, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (236, 3, 0, '视频播放(媒体)', '投放数据', 'f', 'validPlayCost', '有效普播放成本', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (238, 3, 0, '视频播放(媒体)', '投放数据', 'f', 'videoPlay75Num', '75%进度播放数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (239, 3, 0, '视频播放(媒体)', '投放数据', 'f', 'videoPlay100Num', '播放完成数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (243, 3, 0, '视频播放(媒体)', '投放数据', 'f', 'validPlayNum', '有效播放数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 2, 1, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (256, 3, 0, '视频播放(媒体)', '投放数据', 'f', 'videoPlay25Num', '25%进度播放数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (314, 1, 0, '视频播放(媒体)', '投放数据', 'f', 'videoPlay75Num', '75%进度播放数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (317, 1, 0, '视频播放(媒体)', '投放数据', 'f', 'videoPlay25Num', '25%进度播放数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (319, 1, 0, '视频播放(媒体)', '投放数据', 'f', 'playNum', '播放数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 2, 1, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (334, 1, 0, '视频播放(媒体)', '投放数据', 'f', 'validPlayRate', '有效播放率', '%', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (339, 1, 0, '视频播放(媒体)', '投放数据', 'f', 'validPlayCost', '有效普播放成本', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (340, 1, 0, '视频播放(媒体)', '投放数据', 'f', 'validPlayNum', '有效播放数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 2, 1, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (341, 1, 0, '视频播放(媒体)', '投放数据', 'f', 'videoPlay100Num', '播放完成数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (342, 1, 0, '视频播放(媒体)', '投放数据', 'f', 'avgPlayTime', '平均播放时长', '', NULL, NULL, NULL, 0, 4, 0, '{1,2}', 2, 1, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (366, 1, 0, '视频播放(媒体)', '投放数据', 'f', 'videoPlay50Num', '50%进度播放数', '', NULL, NULL, NULL, 0, 4, 0, '{2}', 2, 1, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (707, 7, 5, '展点信息(媒体)', '投放数据', 't', 'thousandImpressAvgPrice', '千次展示均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (708, 7, 6, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (709, 7, 7, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (710, 7, 8, '展点信息(媒体)', '投放数据', 't', 'avgPrice', '点击均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (706, 7, 4, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 0, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (956, 8, 8, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,5,8}', 0, 1, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (955, 8, 9, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (954, 8, 10, '展点信息(媒体)', '投放数据', 't', 'avgPrice', '点击均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (116, 2, 0, '展点信息(媒体)', '投放数据', 'f', 'thousandImpressAvgPrice', '千次展示均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (117, 2, 10, '展点信息(媒体)', '投放数据', 't', 'avgPrice', '点击均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (118, 2, 6, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 0, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (133, 2, 7, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (194, 3, 7, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (222, 3, 0, '展点信息(媒体)', '投放数据', 'f', 'thousandImpressAvgPrice', '千次展示均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (223, 3, 5, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 0, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (224, 3, 9, '展点信息(媒体)', '投放数据', 't', 'avgPrice', '点击均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (235, 3, 6, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (315, 1, 7, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (321, 1, 0, '展点信息(媒体)', '投放数据', 'f', 'thousandImpressAvgPrice', '千次展示均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (391, 1, 6, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (398, 1, 5, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 0, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (402, 1, 9, '展点信息(媒体)', '投放数据', 't', 'avgPrice', '点击均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (711, 7, 3, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 1, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (959, 8, 11, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 1, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (192, 2, 9, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 1, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (296, 3, 8, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 1, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (403, 1, 8, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3,8}', 1, 1, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (103, 2, 0, '基础指标(中台)', '行为转化', 'f', 'landingPageUv', '落地页UV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (122, 2, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (125, 2, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountRate', '填单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (149, 2, 0, '基础指标(中台)', '行为转化', 'f', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (166, 2, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (167, 2, 0, '网页行为(中台)', '行为转化', 'f', 'landingAvgStay', '平均停留时长', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 2, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (188, 2, 14, '基础指标(中台)', '行为转化', 't', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (189, 2, 15, '基础指标(中台)', '行为转化', 't', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (190, 2, 12, '基础指标(中台)', '行为转化', 't', 'fillCountNum', '填单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (191, 2, 13, '基础指标(中台)', '行为转化', 't', 'fillCountCost', '填单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (193, 2, 11, '基础指标(中台)', '行为转化', 't', 'landingPagePv', '落地页PV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (196, 3, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (203, 3, 0, '基础指标(中台)', '行为转化', 'f', 'landingPageUv', '落地页UV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (212, 3, 0, '网页行为(中台)', '行为转化', 'f', 'landingAvgStay', '平均停留时长', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 2, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (217, 3, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (232, 3, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountRate', '填单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (233, 3, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (237, 3, 0, '基础指标(中台)', '行为转化', 'f', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (250, 3, 14, '基础指标(中台)', '行为转化', 't', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (295, 3, 11, '基础指标(中台)', '行为转化', 't', 'fillCountNum', '填单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (297, 3, 13, '基础指标(中台)', '行为转化', 't', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (298, 3, 12, '基础指标(中台)', '行为转化', 't', 'fillCountCost', '填单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (299, 3, 10, '基础指标(中台)', '行为转化', 't', 'landingPagePv', '落地页PV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (300, 1, 13, '基础指标(中台)', '行为转化', 't', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (304, 1, 0, '网页行为(中台)', '行为转化', 'f', 'landingAvgStay', '平均停留时长', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 2, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (313, 1, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (318, 1, 0, '基础指标(中台)', '行为转化', 'f', 'landingPageUv', '落地页UV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (320, 1, 0, '基础指标(中台)', '行为转化', 'f', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (335, 1, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountRate', '填单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (336, 1, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (369, 1, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (397, 1, 10, '基础指标(中台)', '行为转化', 't', 'landingPagePv', '落地页PV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (399, 1, 12, '基础指标(中台)', '行为转化', 't', 'fillCountCost', '填单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (400, 1, 14, '基础指标(中台)', '行为转化', 't', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (401, 1, 11, '基础指标(中台)', '行为转化', 't', 'fillCountNum', '填单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (712, 7, 9, '基础指标(中台)', '行为转化', 't', 'landingPagePv', '落地页PV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (713, 7, 0, '基础指标(中台)', '行为转化', 'f', 'landingPageUv', '落地页UV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (714, 7, 10, '基础指标(中台)', '行为转化', 't', 'fillCountNum', '填单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 2, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (715, 7, 0, '基础指标(中台)', '行为转化', 'f', 'fillCountRate', '填单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (716, 7, 11, '基础指标(中台)', '行为转化', 't', 'fillCountCost', '填单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (717, 7, 12, '基础指标(中台)', '行为转化', 't', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (718, 7, 0, '基础指标(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (719, 7, 13, '基础指标(中台)', '行为转化', 't', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (720, 7, 14, '基础指标(中台)', '行为转化', 't', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (721, 7, 0, '基础指标(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 9, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (722, 7, 15, '基础指标(中台)', '行为转化', 't', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 10, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (723, 7, 0, '网页行为(中台)', '行为转化', 'f', 'landingAvgStay', '平均停留时长', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 2, 2, 0, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (792, 0, 0, '基础指标(中台)', '行为转化', 'f', 'identifyQrCodeNum', '长按二维码识别数(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 11, '页面内微信或企业微信二维码被长按识别的次数', 't', 240, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (793, 0, 0, '基础指标(中台)', '行为转化', 'f', 'identifyQrCodeRate', '长按二维码识别率(微信/企业微信)', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 12, '长按微信或企业微信二维码识别数占总浏览数的比例\n计算公式：长按微信或企业微信二维码识别数/总浏览数*100%', 't', 240, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (960, 0, 0, '基础指标(中台)', '行为转化', 'f', 'followOfficialAccountNum', '微信公众号关注数', '', NULL, '2022-10-19 20:31:24.917537', '2022-10-19 20:31:24.917537', 0, 1, 0, '{0}', 0, 2, 17, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (961, 0, 0, '基础指标(中台)', '行为转化', 'f', 'followOfficialAccountRate', '微信公众号关注率', '%', NULL, '2022-10-19 20:31:24.917537', '2022-10-19 20:31:24.917537', 0, 1, 0, '{0}', 0, 2, 18, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1060, 0, 27, '基础指标(中台)', '行为转化', 'f', 'followOfficialAccountCost', '微信公众号关注成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1061, 0, 25, '基础指标(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessRate', '电商商品购买率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1057, 0, 8, '基础指标(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessNum', '电商商品购买数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1059, 0, 9, '基础指标(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessCost', '电商商品单笔购买成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1062, 0, 10, '基础指标(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessAmount', '电商商品成交金额', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1063, 0, 11, '基础指标(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessRoi', '电商商品成交ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 14, NULL, 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (596, 6, 2, NULL, '属性指标', 't', 'adgroupName', '广告名称', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 7, '广告名称', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (496, 5, 1, NULL, '属性指标', 't', 'platformId', '媒体', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 0, '媒体', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (497, 5, 2, NULL, '属性指标', 't', 'accountName', '账户名称', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 1, '账户名称', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (498, 5, 2, NULL, '属性指标', 't', 'accountId', '账户ID', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 2, '账户ID', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (499, 5, 3, NULL, '属性指标', 't', 'campaignName', '计划名称', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 3, '计划名称', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (500, 5, 3, NULL, '属性指标', 't', 'campaignId', '计划ID', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 4, '计划ID', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (501, 5, 4, NULL, '属性指标', 't', 'campaignStatus', '计划状态', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 5, '计划状态', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (502, 5, 0, NULL, '属性指标', 'f', 'promotionGoal', '推广目标', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 6, '推广目标', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (599, 6, 0, NULL, '属性指标', 'f', 'optimizationGoal', '优化目标', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 10, '优化目标', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (589, 6, 0, NULL, '属性指标', 'f', 'platformId', '媒体', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 0, '媒体', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (590, 6, 0, NULL, '属性指标', 'f', 'accountName', '账户名称', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 1, '账户名称', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (591, 6, 0, NULL, '属性指标', 'f', 'accountId', '账户ID', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 2, '账户ID', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (592, 6, 1, NULL, '属性指标', 't', 'campaignName', '计划名称', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 3, '计划名称', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (593, 6, 1, NULL, '属性指标', 't', 'campaignId', '计划ID', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 4, '计划ID', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (594, 6, 0, NULL, '属性指标', 'f', 'campaignStatus', '计划状态', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 5, '计划状态', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (595, 6, 0, NULL, '属性指标', 'f', 'promotionGoal', '推广目标', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 6, '推广目标', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (597, 6, 2, NULL, '属性指标', 't', 'adgroupId', '广告ID', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 8, '广告ID', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (598, 6, 3, NULL, '属性指标', 't', 'adgroupStatus', '广告状态', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', NULL, 0, 9, '广告状态', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (600, 6, 5, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 0, 1, 0, '曝光量', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (407, 4, 2, NULL, '属性指标', 't', 'accountName', '账户名称', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 1, '账户名称', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (408, 4, 2, NULL, '属性指标', 't', 'accountId', '账户ID', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 2, '账户ID', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (406, 4, 1, NULL, '属性指标', 't', 'platformId', '媒体', '', NULL, NULL, NULL, 0, 5, 1, '{1,2,3,5,8}', NULL, 0, 0, '媒体', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (601, 6, 6, '展点信息(媒体)', '投放数据', 't', 'thousandImpressAvgPrice', '千次展示均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 1, '千次展示均价', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (410, 4, 5, '展点信息(媒体)', '投放数据', 't', 'thousandImpressAvgPrice', '千次展示均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 1, '千次展示均价', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (411, 4, 6, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 2, '点击量', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (412, 4, 7, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3, '点击率', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (503, 5, 6, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 0, 1, 0, '曝光量', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (504, 5, 7, '展点信息(媒体)', '投放数据', 't', 'thousandImpressAvgPrice', '千次展示均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 1, '千次展示均价', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (505, 5, 8, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 2, '点击量', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (506, 5, 9, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3, '点击率', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (507, 5, 10, '展点信息(媒体)', '投放数据', 't', 'avgPrice', '平均点击均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 4, '平均点击均价', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (602, 6, 7, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击量', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 2, '点击量', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (603, 6, 8, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 3, '点击率', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (604, 6, 9, '展点信息(媒体)', '投放数据', 't', 'avgPrice', '平均点击均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 4, '平均点击均价', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (409, 4, 4, '展点信息(媒体)', '投放数据', 't', 'viewNum', '曝光量', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 0, 1, 0, '曝光量', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (413, 4, 8, '展点信息(媒体)', '投放数据', 't', 'avgPrice', '平均点击均价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 4, '平均点击均价', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1095, 4, 0, '花费信息(媒体)', '投放数据', 'f', 'balance', '账户余额', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 1, 1, 1, '账户余额', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (508, 5, 5, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 1, 1, 0, '花费', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (605, 6, 4, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 1, 1, 0, '花费', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (414, 4, 3, '花费信息(媒体)', '投放数据', 't', 'cost', '花费', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 1, 1, 0, '花费', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1096, 5, 0, '花费信息(媒体)', '投放数据', 'f', 'balance', '账户余额', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 1, 1, 1, '账户余额', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1097, 6, 0, '花费信息(媒体)', '投放数据', 'f', 'balance', '账户余额', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 1, 1, 1, '账户余额', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1098, 4, 0, '广告出价及预算', '投放数据', 'f', 'accountBudget', '账户预算', '元', NULL, '2022-09-18 04:15:50.458348', '2022-09-18 04:15:50.458348', 0, 0, 0, NULL, 2, 1, 4, '账户预算', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1099, 5, 0, '广告出价及预算', '投放数据', 'f', 'adGroupBudget', '广告组预算', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 2, 1, 0, '广告组预算', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1100, 5, 0, '广告出价及预算', '投放数据', 'f', 'accountBudget', '账户预算', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 2, 1, 1, '账户预算', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1101, 6, 0, '广告出价及预算', '投放数据', 'f', 'adBid', '广告出价', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 2, 1, 0, '广告出价', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1102, 6, 0, '广告出价及预算', '投放数据', 'f', 'deepConversionBidding', '深度转化出价', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 2, 1, 1, '深度转化出价', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1103, 6, 0, '广告出价及预算', '投放数据', 'f', 'advertisingBudget', '广告预算', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 2, 1, 2, '广告预算', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1104, 6, 0, '广告出价及预算', '投放数据', 'f', 'adGroupBudget', '广告组预算', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 2, 1, 0, '广告组预算', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1105, 6, 0, '广告出价及预算', '投放数据', 'f', 'accountBudget', '账户预算', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 2, 1, 1, '账户预算', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (618, 6, 13, '转化数据(媒体)', '投放数据', 't', 'convertNum', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 1, 0, '转化数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (867, 5, 13, '转化数据(媒体)', '投放数据', 'f', 'convertNum', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 1, 0, '转化数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (619, 6, 14, '转化数据(媒体)', '投放数据', 't', 'targetConvertRate', '转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 1, 1, '转化率', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (840, 4, 14, '转化数据(媒体)', '投放数据', 'f', 'targetConvertRate', '转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 1, 1, '转化率', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (868, 5, 14, '转化数据(媒体)', '投放数据', 'f', 'targetConvertRate', '转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 1, 1, '转化率', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (620, 6, 15, '转化数据(媒体)', '投放数据', 't', 'targetConvertCost', '平均转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 1, 2, '平均转化成本', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (841, 4, 15, '转化数据(媒体)', '投放数据', 'f', 'targetConvertCost', '平均转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 1, 2, '平均转化成本', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (869, 5, 15, '转化数据(媒体)', '投放数据', 'f', 'targetConvertCost', '平均转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 1, 2, '平均转化成本', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (621, 6, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertNum', '深度转化数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 1, 3, '深度转化数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (842, 4, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertNum', '深度转化数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 1, 3, '深度转化数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (870, 5, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertNum', '深度转化数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 1, 3, '深度转化数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (622, 6, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertRate', '深度转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 1, 4, '深度转化率', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (843, 4, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertRate', '深度转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 1, 4, '深度转化率', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (871, 5, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertRate', '深度转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 3, 1, 4, '深度转化率', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (623, 6, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertCost', '深度转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 1, 5, '深度转化成本', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (844, 4, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertCost', '深度转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 1, 5, '深度转化成本', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (872, 5, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertCost', '深度转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 3, 1, 5, '深度转化成本', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1142, 4, 0, '视频数据(媒体)', '投放数据', 'f', 'playNum', '播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 0, '播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1143, 4, 0, '视频数据(媒体)', '投放数据', 'f', 'validPlayNum', '有效播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 1, '有效播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1144, 4, 0, '视频数据(媒体)', '投放数据', 'f', 'videoPlay25Num', '25%进度播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 2, '25%进度播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1145, 4, 0, '视频数据(媒体)', '投放数据', 'f', 'videoPlay50Num', '50%进度播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 3, '50%进度播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1146, 4, 0, '视频数据(媒体)', '投放数据', 'f', 'videoPlay75Num', '75%进度播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 4, '75%进度播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1147, 4, 0, '视频数据(媒体)', '投放数据', 'f', 'videoPlay100Num', '99%进度播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 5, '99%进度播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1148, 4, 0, '视频数据(媒体)', '投放数据', 'f', 'thumbUpCount', '点赞数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 6, '点赞数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1149, 4, 0, '视频数据(媒体)', '投放数据', 'f', 'discussCount', '评论提交数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 7, '评论提交数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1150, 4, 0, '视频数据(媒体)', '投放数据', 'f', 'shareCount', '分享数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 8, '分享数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1151, 5, 0, '视频数据(媒体)', '投放数据', 'f', 'playNum', '播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 0, '播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1152, 5, 0, '视频数据(媒体)', '投放数据', 'f', 'validPlayNum', '有效播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 1, '有效播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1153, 5, 0, '视频数据(媒体)', '投放数据', 'f', 'videoPlay25Num', '25%进度播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 2, '25%进度播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1154, 5, 0, '视频数据(媒体)', '投放数据', 'f', 'videoPlay50Num', '50%进度播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 3, '50%进度播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1155, 5, 0, '视频数据(媒体)', '投放数据', 'f', 'videoPlay75Num', '75%进度播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 4, '75%进度播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1156, 5, 0, '视频数据(媒体)', '投放数据', 'f', 'videoPlay100Num', '99%进度播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 5, '99%进度播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1157, 5, 0, '视频数据(媒体)', '投放数据', 'f', 'thumbUpCount', '点赞数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 6, '点赞数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1158, 5, 0, '视频数据(媒体)', '投放数据', 'f', 'discussCount', '评论提交数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 7, '评论提交数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1159, 5, 0, '视频数据(媒体)', '投放数据', 'f', 'shareCount', '分享数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 8, '分享数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1160, 6, 0, '视频数据(媒体)', '投放数据', 'f', 'playNum', '播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 0, '播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1161, 6, 0, '视频数据(媒体)', '投放数据', 'f', 'validPlayNum', '有效播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 1, '有效播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1162, 6, 0, '视频数据(媒体)', '投放数据', 'f', 'videoPlay25Num', '25%进度播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 2, '25%进度播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1163, 6, 0, '视频数据(媒体)', '投放数据', 'f', 'videoPlay50Num', '50%进度播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 3, '50%进度播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1164, 6, 0, '视频数据(媒体)', '投放数据', 'f', 'videoPlay75Num', '75%进度播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 4, '75%进度播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1165, 6, 0, '视频数据(媒体)', '投放数据', 'f', 'videoPlay100Num', '99%进度播放数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 5, '99%进度播放数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1166, 6, 0, '视频数据(媒体)', '投放数据', 'f', 'thumbUpCount', '点赞数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 6, '点赞数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1167, 6, 0, '视频数据(媒体)', '投放数据', 'f', 'discussCount', '评论提交数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 7, '评论提交数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1168, 6, 0, '视频数据(媒体)', '投放数据', 'f', 'shareCount', '分享数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, NULL, 4, 1, 8, '分享数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (421, 4, 0, '基础数据(中台)', '行为转化', 'f', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7, '一叶落地页订单提交成功数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (422, 4, 0, '基础数据(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8, '订单提交数/落地页PV*100%,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (510, 5, 0, '基础数据(中台)', '行为转化', 'f', 'landingPageUv', '落地页UV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1, '一叶落地页的访客数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (520, 5, 0, '基础数据(中台)', '行为转化', 'f', 'landingAvgStay', '平均停留时长', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3, '平均停留时长', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (511, 5, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountNum', '表单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4, '一叶落地页表单提交成功数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (512, 5, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountRate', '表单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5, '表单提交数 / 落地页PV*100%,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (513, 5, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountCost', '表单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6, '花费/表单提交数,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (514, 5, 0, '基础数据(中台)', '行为转化', 'f', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7, '一叶落地页订单提交成功数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (515, 5, 0, '基础数据(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8, '订单提交数/落地页PV*100%,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (516, 5, 0, '基础数据(中台)', '行为转化', 'f', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 9, '花费/订单提交数,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (820, 4, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrCodeNum', '长按二维码识别数(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 15, '一叶落地页长按二维码识别数（微信/企业微信）', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (821, 4, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrCodeRate', '长按二维码识别率(微信/企业微信)', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 16, '长按二维码识别数（微信/企业微信）/落地页PV*100%,保留小数点后两位', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (822, 4, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrcodeCost', '长按二维码识别成本(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 17, '花费/长按二维码识别数（微信/企业微信）,保留小数点后两位', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (423, 4, 0, '基础数据(中台)', '行为转化', 'f', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 9, '花费/订单提交数,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (424, 4, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 10, '一叶落地页的订单提交成功并完成支付的数量', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (425, 4, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 11, '订单完成数/落地页PV*100%,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (426, 4, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 12, '花费/订单完成数,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (509, 5, 0, '基础数据(中台)', '行为转化', 'f', 'landingPagePv', '落地页PV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0, '一叶落地页的浏览数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (517, 5, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 10, '一叶落地页的订单提交成功并完成支付的数量', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (518, 5, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 11, '订单完成数/落地页PV*100%,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (519, 5, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 12, '花费/订单完成数,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (606, 6, 0, '基础数据(中台)', '行为转化', 'f', 'landingPagePv', '落地页PV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0, '一叶落地页的浏览数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (607, 6, 0, '基础数据(中台)', '行为转化', 'f', 'landingPageUv', '落地页UV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1, '一叶落地页的访客数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (617, 6, 0, '基础数据(中台)', '行为转化', 'f', 'landingAvgStay', '平均停留时长', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3, '平均停留时长', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (608, 6, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountNum', '表单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4, '一叶落地页表单提交成功数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (823, 4, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatNum', '成功添加企业微信数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 18, '一叶落地页成功添加企业微信数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (824, 4, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatRate', '成功添加企业微信率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 19, '加企业微信数/落地页PV*100%,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (825, 4, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatCost', '成功添加企业微信成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 21, '花费/成功添加企业微信数,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (850, 5, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrcodeCost', '长按二维码识别成本(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 17, '花费/长按二维码识别数（微信/企业微信）,保留小数点后两位', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (878, 6, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrcodeCost', '长按二维码识别成本(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 17, '花费/长按二维码识别数（微信/企业微信）,保留小数点后两位', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (851, 5, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatNum', '成功添加企业微信数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 18, '一叶落地页成功添加企业微信数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (848, 5, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrCodeNum', '长按二维码识别数(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 15, '一叶落地页长按二维码识别数（微信/企业微信）', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (876, 6, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrCodeNum', '长按二维码识别数(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 15, '一叶落地页长按二维码识别数（微信/企业微信）', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (849, 5, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrCodeRate', '长按二维码识别率(微信/企业微信)', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 16, '长按二维码识别数（微信/企业微信）/落地页PV*100%,保留小数点后两位', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (877, 6, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrCodeRate', '长按二维码识别率(微信/企业微信)', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 16, '长按二维码识别数（微信/企业微信）/落地页PV*100%,保留小数点后两位', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (416, 4, 0, '基础数据(中台)', '行为转化', 'f', 'landingPagePv', '落地页PV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0, '一叶落地页的浏览数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (417, 4, 0, '基础数据(中台)', '行为转化', 'f', 'landingPageUv', '落地页UV', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1, '一叶落地页的访客数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1106, 4, 0, '基础数据(中台)', '行为转化', 'f', 'trafficRatio', '正样本流量占比', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 2, '抖音+今日头条流量来源PV数 / 落地页PV*100%,保留至小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1107, 5, 0, '基础数据(中台)', '行为转化', 'f', 'trafficRatio', '正样本流量占比', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 2, '抖音+今日头条流量来源PV数 / 落地页PV*100%,保留至小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1108, 6, 0, '基础数据(中台)', '行为转化', 'f', 'trafficRatio', '正样本流量占比', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 2, '抖音+今日头条流量来源PV数 / 落地页PV*100%,保留至小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (609, 6, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountRate', '表单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5, '表单提交数 / 落地页PV*100%,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (610, 6, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountCost', '表单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6, '花费/表单提交数,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (611, 6, 0, '基础数据(中台)', '行为转化', 'f', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7, '一叶落地页订单提交成功数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (612, 6, 0, '基础数据(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8, '订单提交数/落地页PV*100%,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (613, 6, 0, '基础数据(中台)', '行为转化', 'f', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 9, '花费/订单提交数,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (614, 6, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 10, '一叶落地页的订单提交成功并完成支付的数量', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (615, 6, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 11, '订单完成数/落地页PV*100%,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (616, 6, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 12, '花费/订单完成数,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1109, 4, 0, '基础数据(中台)', '行为转化', 'f', 'orderTransactionAmount', '订单成交金额', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 13, '该账户/广告组/广告计划下对应订单完成支付金额总和', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1110, 4, 0, '基础数据(中台)', '行为转化', 'f', 'orderTransactionRoi', '订单成交ROI', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 14, '订单成交金额/广告花费,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1111, 5, 0, '基础数据(中台)', '行为转化', 'f', 'orderTransactionAmount', '订单成交金额', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 13, '该账户/广告组/广告计划下对应订单完成支付金额总和', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1112, 5, 0, '基础数据(中台)', '行为转化', 'f', 'orderTransactionRoi', '订单成交ROI', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 14, '订单成交金额/广告花费,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1113, 6, 0, '基础数据(中台)', '行为转化', 'f', 'orderTransactionAmount', '订单成交金额', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 13, '该账户/广告组/广告计划下对应订单完成支付金额总和', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1114, 6, 0, '基础数据(中台)', '行为转化', 'f', 'orderTransactionRoi', '订单成交ROI', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 14, '订单成交金额/广告花费,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (879, 6, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatNum', '成功添加企业微信数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 18, '一叶落地页成功添加企业微信数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (852, 5, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatRate', '成功添加企业微信率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 19, '加企业微信数/落地页PV*100%,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (880, 6, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatRate', '成功添加企业微信率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 19, '加企业微信数/落地页PV*100%,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1115, 4, 0, '基础数据(中台)', '行为转化', 'f', 'enterpriseIncreaseIncomingRate', '成功添加企业微信进线率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 20, '加企业微信数/转化数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1116, 5, 0, '基础数据(中台)', '行为转化', 'f', 'enterpriseIncreaseIncomingRate', '成功添加企业微信进线率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 20, '加企业微信数/转化数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1117, 6, 0, '基础数据(中台)', '行为转化', 'f', 'enterpriseIncreaseIncomingRate', '成功添加企业微信进线率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 20, '加企业微信数/转化数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (853, 5, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatCost', '成功添加企业微信成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 21, '花费/成功添加企业微信数,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (881, 6, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatCost', '成功添加企业微信成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 21, '花费/成功添加企业微信数,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1118, 4, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeNum', '长按二维码识别数(公众号)', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 22, '一叶落地页长按二维码识别数（公众号）', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1119, 4, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeRate', '长按二维码识别率(公众号)', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 23, '长按二维码识别数（公众号）/落地页PV*100%,保留小数点后两位', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1120, 4, 0, '基础数据(中台)', '行为转化', 'f', 'enterpriseIncreaseIncomingCost', '长按二维码识别成本(公众号)', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 24, '花费/长按二维码识别数（公众号）,保留小数点后两位', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1121, 4, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountNum', '微信公众号关注数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 25, '一叶落地页成功关注公众号数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1122, 4, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountRate', '微信公众号关注率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 26, '关注公众号数/落地页PV*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1123, 4, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountIncomingRate', '微信公众号关注进线率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 27, '微信公众号关注数/转化数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1124, 4, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountCost', '微信公众号关注成本', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 28, '花费/关注公众号数,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1125, 5, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeNum', '长按二维码识别数(公众号)', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 22, '一叶落地页长按二维码识别数（公众号）', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1126, 5, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeRate', '长按二维码识别率(公众号)', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 23, '长按二维码识别数（公众号）/落地页PV*100%,保留小数点后两位', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1127, 5, 0, '基础数据(中台)', '行为转化', 'f', 'enterpriseIncreaseIncomingCost', '长按二维码识别成本(公众号)', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 24, '花费/长按二维码识别数（公众号）,保留小数点后两位', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1128, 5, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountNum', '微信公众号关注数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 25, '一叶落地页成功关注公众号数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1129, 5, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountRate', '微信公众号关注率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 26, '关注公众号数/落地页PV*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1130, 5, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountIncomingRate', '微信公众号关注进线率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 27, '微信公众号关注数/转化数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1131, 5, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountCost', '微信公众号关注成本', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 28, '花费/关注公众号数,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1132, 6, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeNum', '长按二维码识别数(公众号)', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 22, '一叶落地页长按二维码识别数（公众号）', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1133, 6, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeRate', '长按二维码识别率(公众号)', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 23, '长按二维码识别数（公众号）/落地页PV*100%,保留小数点后两位', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1134, 6, 0, '基础数据(中台)', '行为转化', 'f', 'enterpriseIncreaseIncomingCost', '长按二维码识别成本(公众号)', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 24, '花费/长按二维码识别数（公众号）,保留小数点后两位', 't', 240, 220);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1135, 6, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountNum', '微信公众号关注数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 25, '一叶落地页成功关注公众号数', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1136, 6, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountRate', '微信公众号关注率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 26, '关注公众号数/落地页PV*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1137, 6, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountIncomingRate', '微信公众号关注进线率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 27, '微信公众号关注数/转化数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1138, 6, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountCost', '微信公众号关注成本', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 28, '花费/关注公众号数,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1064, 4, 16, '基础数据(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessNum', '电商商品购买数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 29, '一叶落地页成功购买电商商品订单数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1068, 5, 16, '基础数据(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessNum', '电商商品购买数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 29, '一叶落地页成功购买电商商品订单数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1072, 6, 16, '基础数据(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessNum', '电商商品购买数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 29, '一叶落地页成功购买电商商品订单数', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1139, 4, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessRate', '电商商品购买率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 30, '电商商品购买数/落地页PV*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1140, 5, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessRate', '电商商品购买率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 30, '电商商品购买数/落地页PV*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1141, 6, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessRate', '电商商品购买率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 30, '电商商品购买数/落地页PV*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1065, 4, 17, '基础数据(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessCost', '电商商品单笔购买成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 31, '花费/电商商品购买数', 't', 160, 140);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1069, 5, 17, '基础数据(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessCost', '电商商品单笔购买成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 31, '花费/电商商品购买数', 't', 160, 140);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1073, 6, 17, '基础数据(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessCost', '电商商品单笔购买成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 31, '花费/电商商品购买数', 't', 160, 140);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1066, 4, 18, '基础数据(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessAmount', '电商商品成交金额', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 32, '该账户/广告组/广告计划下对应电商客资下单金额总和', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1070, 5, 18, '基础数据(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessAmount', '电商商品成交金额', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 32, '该账户/广告组/广告计划下对应电商客资下单金额总和', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1074, 6, 18, '基础数据(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessAmount', '电商商品成交金额', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 32, '该账户/广告组/广告计划下对应电商客资下单金额总和', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1067, 4, 19, '基础数据(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessRoi', '电商商品成交ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 33, '电商商品成交金额/广告花费,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1071, 5, 19, '基础数据(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessRoi', '电商商品成交ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 33, '电商商品成交金额/广告花费,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1075, 6, 19, '基础数据(中台)', '行为转化', 't', 'onlineShopBuyGoodsSuccessRoi', '电商商品成交ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 33, '电商商品成交金额/广告花费,保留小数点后两位', 't', NULL, NULL);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1169, 4, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOpenConsultationNum', '企业微信-开口咨询数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 0, '添加企业微信后开口咨询数，须在企业微信添加【开口咨询】标签', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1170, 4, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOpenConsultationRate', '企业微信-开口咨询率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 1, '开口咨询数/成功添加企业微信数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1171, 4, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOpenConsultationCost', '企业微信-开口咨询成本', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 2, '花费/开口咨询数,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1172, 4, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatQuotationConsultationNum', '企业微信-报价咨询数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 3, '添加企业微信后报价咨询数，须在企业微信添加【报价咨询】标签', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1173, 4, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatQuotationConsultationRate', '企业微信-报价咨询率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 4, '报价咨询数/成功添加企业微信数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1174, 4, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatQuotationConsultationCost', '企业微信-报价咨询成本', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 5, '花费/报价咨询数,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1175, 4, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOrderFilledNum', '企业微信-订单成交数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 6, '添加企业微信后订单成交数，须在企业微信添加【订单成交】标签', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1176, 4, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOrderFilledRate', '企业微信-订单成交率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 7, '订单成交数/成功添加企业微信数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1177, 4, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOrderFilledCost', '企业微信-订单成交成本', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 8, '花费/订单成交数,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1178, 5, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOpenConsultationNum', '企业微信-开口咨询数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 0, '添加企业微信后开口咨询数，须在企业微信添加【开口咨询】标签', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1179, 5, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOpenConsultationRate', '企业微信-开口咨询率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 1, '开口咨询数/成功添加企业微信数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1180, 5, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOpenConsultationCost', '企业微信-开口咨询成本', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 2, '花费/开口咨询数,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1181, 5, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatQuotationConsultationNum', '企业微信-报价咨询数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 3, '添加企业微信后报价咨询数，须在企业微信添加【报价咨询】标签', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1182, 5, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatQuotationConsultationRate', '企业微信-报价咨询率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 4, '报价咨询数/成功添加企业微信数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1183, 5, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatQuotationConsultationCost', '企业微信-报价咨询成本', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 5, '花费/报价咨询数,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1184, 5, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOrderFilledNum', '企业微信-订单成交数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 6, '添加企业微信后订单成交数，须在企业微信添加【订单成交】标签', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1185, 5, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOrderFilledRate', '企业微信-订单成交率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 7, '订单成交数/成功添加企业微信数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1186, 5, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOrderFilledCost', '企业微信-订单成交成本', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 8, '花费/订单成交数,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1187, 6, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOpenConsultationNum', '企业微信-开口咨询数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 0, '添加企业微信后开口咨询数，须在企业微信添加【开口咨询】标签', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1188, 6, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOpenConsultationRate', '企业微信-开口咨询率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 1, '开口咨询数/成功添加企业微信数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1189, 6, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOpenConsultationCost', '企业微信-开口咨询成本', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 2, '花费/开口咨询数,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1190, 6, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatQuotationConsultationNum', '企业微信-报价咨询数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 3, '添加企业微信后报价咨询数，须在企业微信添加【报价咨询】标签', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1191, 6, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatQuotationConsultationRate', '企业微信-报价咨询率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 4, '报价咨询数/成功添加企业微信数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1192, 6, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatQuotationConsultationCost', '企业微信-报价咨询成本', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 5, '花费/报价咨询数,保留小数点后两位', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1193, 6, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOrderFilledNum', '企业微信-订单成交数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 6, '添加企业微信后订单成交数，须在企业微信添加【订单成交】标签', 't', 160, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1194, 6, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOrderFilledRate', '企业微信-订单成交率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 7, '订单成交数/成功添加企业微信数*100%,保留小数点后两位', 't', 240, 112);
INSERT INTO "marketing_customer_field"("id", "type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width") VALUES (1195, 6, 0, '加粉后端转化(中台)', '行为转化', 'f', 'workWechatOrderFilledCost', '企业微信-订单成交成本', '元', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 1, 2, 8, '花费/订单成交数,保留小数点后两位', 't', 160, 112);


alter table marketing_customer_field add column if not exists default_data int4 default 1;
comment on column marketing_customer_field.default_data is '是否为默认数据，0为默认，1为客户自定义';

update marketing_customer_field set default_data =0;
