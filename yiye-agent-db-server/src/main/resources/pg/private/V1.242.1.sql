DROP TABLE IF EXISTS marketing_data_project;
create table if not exists marketing_data_project
(
    id         		                    bigserial not null primary key,
    project_id   		                int8,
	account_id                          varchar,
    platform_id                         int8,
    advertiser_account_id               int8,
    name                                varchar,
	status                              varchar,
	project_status                      int4,
	landing_type                        varchar,
	pricing                             varchar,
	ext                                 json,
	advertiser_account_status           int4,
    created_at 		                    timestamp,
    updated_at 		                    timestamp
);
comment on table marketing_data_project is '项目';
comment on column marketing_data_project.id is '自增主键';
comment on column marketing_data_project.project_id is '项目ID';
comment on column marketing_data_project.account_id is '广告账户ID';
comment on column marketing_data_project.platform_id is '投放媒体';
comment on column marketing_data_project.advertiser_account_id is '一叶平台投放账户ID';
comment on column marketing_data_project.name is '项目名称';
comment on column marketing_data_project.status is '项目状态（媒体返回）';
comment on column marketing_data_project.project_status is '项目状态（一叶映射）';
comment on column marketing_data_project.landing_type is '推广目标';
comment on column marketing_data_project.pricing is '出价方式';
comment on column marketing_data_project.ext is 'json数据';
comment on column marketing_data_project.advertiser_account_status is '投放账户状态';
comment on column marketing_data_project.created_at is '创建时间';
comment on column marketing_data_project.updated_at is '更新时间';

-- 添加索引
create unique index if not exists marketing_data_project_unique_index on marketing_data_project (account_id, project_id, platform_id);
create index if not exists idx_marketing_data_project_project_id on marketing_data_project (project_id);
create index if not exists idx_marketing_data_project_advertiser_account_id on marketing_data_project (advertiser_account_id);


DROP TABLE IF EXISTS marketing_data_promotion;
create table if not exists marketing_data_promotion
(
    id         		                    bigserial not null primary key,
    promotion_id   		                int8,
	account_id                          varchar,
    platform_id                         int8,
    project_id   		                int8,
    advertiser_account_id               int8,
    name                                varchar,
	status                              varchar,
	promotion_status                    int4,
	bid                                 int8,
	cpa_bid                             int8,
	deep_cpabid                         int8,
	learning_phase                      varchar,
	ext                                 json,
	advertiser_account_status           int4,
    created_at 		                    timestamp,
    updated_at 		                    timestamp
);
comment on table marketing_data_promotion is '广告';
comment on column marketing_data_promotion.id is '自增主键';
comment on column marketing_data_promotion.promotion_id is '广告ID';
comment on column marketing_data_promotion.account_id is '广告账户ID';
comment on column marketing_data_promotion.platform_id is '投放媒体';
comment on column marketing_data_promotion.project_id is '项目ID';
comment on column marketing_data_promotion.advertiser_account_id is '一叶平台投放账户ID';
comment on column marketing_data_promotion.name is '广告名称';
comment on column marketing_data_promotion.status is '广告投放状态（媒体返回）';
comment on column marketing_data_promotion.promotion_status is '广告投放状态（一叶映射）';
comment on column marketing_data_promotion.bid is '出价';
comment on column marketing_data_promotion.cpa_bid is 'cpa_bid';
comment on column marketing_data_promotion.deep_cpabid is '深度转化出价';
comment on column marketing_data_promotion.learning_phase is '学习期状态';
comment on column marketing_data_promotion.ext is 'json数据';
comment on column marketing_data_promotion.advertiser_account_status is '投放账户状态';
comment on column marketing_data_promotion.created_at is '创建时间';
comment on column marketing_data_promotion.updated_at is '更新时间';

-- 添加索引
create unique index if not exists marketing_data_promotion_unique_index on marketing_data_promotion (account_id, promotion_id, project_id, platform_id);
create index if not exists idx_marketing_data_promotion_promotion_id on marketing_data_promotion (promotion_id);
create index if not exists idx_marketing_data_promotion_project_id on marketing_data_promotion (project_id);
create index if not exists idx_marketing_data_promotion_advertiser_account_id on marketing_data_promotion (advertiser_account_id);

alter table marketing_advertiser_account add column if not exists first_open_extract_time timestamp;
comment on column marketing_advertiser_account.first_open_extract_time is '首次开启拉取数据时间';

alter table marketing_advertiser_account add column if not exists last_extract_time timestamp;
comment on column marketing_advertiser_account.last_extract_time is '最近一次拉取数据时间';


-- 账户层级自定义列（投放面板 - 巨量 - 账户）
delete from marketing_customer_field where type = 16;
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础属性(媒体)', '属性指标', 't', 'accountName', '账户', '', NULL, now(), now(), 0, 5, 1, '{1,2,3,5,8}', 0, 0, 0, '投放账户名称及ID', 'f', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础属性(媒体)', '属性指标', 'f', 'balance', '账户余额', '', NULL, now(), now(), 0, 0, 0, NULL, 0, 0, 1, '投放账户余额', 'f', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础属性(媒体)', '属性指标', 'f', 'managerName', '管理员', '', NULL, now(), now(), 0, 5, 0, '{1,2,3,5,8}', 0, 0, 2, '投放账户管理员', 'f', NULL, NULL, 0);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 1, '展点信息(媒体)', '投放数据', 't', 'cost', '消耗', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 0, 1, 0, '表示广告在投放期内的消耗金额', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 2, '展点信息(媒体)', '投放数据', 't', 'viewNum', '展示数', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 0, 1, 1, '广告展示给用户的次数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '展点信息(媒体)', '投放数据', 'f', 'thousandImpressAvgPrice', '平均千次展现费用', '', NULL, NULL, NULL, 0, 1, 0, '{0}', 0, 1, 2, '广告平均每一千次展现所付出的费用，计算公式是：总消耗/展示数*1000', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 3, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 3, '当用户点击广告素材时，触发点击事件，该事件被认为是一次有效的广告点击', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 4, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 4, '广告被点击的次数占展示次数的百分比。计算方法：点击数/展示数*100%', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '展点信息(媒体)', '投放数据', 'f', 'avgPrice', '平均点击单价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 5, '广告主为每次点击付出的费用成本，计算公式是：总消耗/点击数', 't', NULL, NULL, 0);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 5, '转化数据(媒体)', '投放数据', 't', 'convertNum', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 1, 0, '转化数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '转化数据(媒体)', '投放数据', 'f', 'targetConvertRate', '转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 1, 1, '转化数占点击次数的百分比。计算方式：转化数/点击数*100%', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 6, '转化数据(媒体)', '投放数据', 't', 'targetConvertCost', '平均转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 1, 2, '每个转化所付出的平均成本，计算方式：总消耗/转化数。当天数据可能会有波动。', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertNum', '深度转化数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 1, 3, '深度转化数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertRate', '深度转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 1, 4, '深度转化的次数占转化次数的百分比。计算方式：深度转化数/转化数*100%', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertCost', '深度转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 1, 5, '每个深度转化所付出的平均成本，计算方法：总消耗/深度转化数。当天数据可能会有波动，次日早8点后稳定。', 't', NULL, NULL, 0);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountNum', '表单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0, '一叶落地页表单提交成功数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountRate', '表单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1, '表单提交数 / 点击数*100%  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountCost', '表单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 2, '消耗/表单提交数  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3, '一叶落地页订单提交成功数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4, '订单提交数/点击数*100%  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5, '消耗/订单提交数  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6, '一叶落地页的订单提交成功并完成支付的数量(包含货到付款）', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7, '订单完成数/点击数*100%  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8, '消耗/订单完成数  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'orderTransactionAmount', '订单成交金额', '', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 9, '订单完成支付金额总和', 't', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'orderTransactionRoi', '订单成交ROI', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 10, '订单成交金额/广告消耗  保留小数点后两位', 't', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrCodeNum', '长按二维码识别数(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 11, '一叶落地页长按二维码识别数（微信/企业微信）', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrCodeRate', '长按二维码识别率(微信/企业微信)', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 12, '长按二维码识别数（微信/企业微信）/点击数*100%  保留小数点后两位', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrcodeCost', '长按二维码识别成本(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 13, '消耗/长按二维码识别数（微信/企业微信）  保留小数点后两位', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 7, '基础数据(中台)', '行为转化', 't', 'addWorkWechatNum', '成功添加企业微信数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 14, '一叶落地页成功添加企业微信数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatRate', '成功添加企业微信率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 15, '加企业微信数/点击数*100%  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 8, '基础数据(中台)', '行为转化', 't', 'addWorkWechatCost', '成功添加企业微信成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 16, '消耗/成功添加企业微信数  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeNum', '长按二维码识别数(公众号)', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 17, '一叶落地页长按二维码识别数（公众号）', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeRate', '长按二维码识别率(公众号)', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 18, '长按二维码识别数（公众号）/点击数*100%  保留小数点后两位', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'enterpriseIncreaseIncomingCost', '长按二维码识别成本(公众号)', '', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 19, '消耗/长按二维码识别数（公众号）  保留小数点后两位', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountNum', '微信公众号关注数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 20, '一叶落地页成功关注公众号数', 't', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountRate', '微信公众号关注率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 21, '关注公众号数/点击数*100%  保留小数点后两位', 't', 240, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountCost', '微信公众号关注成本', '', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 22, '消耗/关注公众号数  保留小数点后两位', 't', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessNum', '电商商品购买数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 23, '一叶落地页成功购买电商商品订单数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessRate', '电商商品购买率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 24, '电商商品购买数/点击数*100%  保留小数点后两位', 't', 240, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessCost', '电商商品单笔购买成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 25, '消耗/电商商品购买数', 't', 160, 140, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessAmount', '电商商品成交金额', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 26, '该账户/广告组/广告计划下对应电商客资下单金额总和', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (16, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessRoi', '电商商品成交ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 27, '电商商品成交金额/广告消耗  保留小数点后两位', 't', NULL, NULL, 0);


-- 项目层级自定义列（投放面板 - 巨量 - 项目）
delete from marketing_customer_field where type = 17;
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 2, '基础属性(媒体)', '属性指标', 't', 'accountName', '账户', '', NULL, now(), now(), 0, 5, 0, '{1,2,3,5,8}', 0, 0, 0, '投放账户名称及ID', 'f', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础属性(媒体)', '属性指标', 'f', 'managerName', '管理员', '', NULL, now(), now(), 0, 5, 0, '{1,2,3,5,8}', 0, 0, 1, '投放账户管理员', 'f', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础属性(媒体)', '属性指标', 't', 'projectName', '项目', '', NULL, now(), now(), 0, 5, 1, '{1,2,3,5,8}', 0, 0, 2, '项目名称及ID（对应巨量引擎广告旧版中的“广告组”）', 'f', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 1, '基础属性(媒体)', '属性指标', 't', 'projectStatus', '项目状态', '', NULL, now(), now(), 0, 5, 0, '{1,2,3,5,8}', 0, 0, 3, '项目对应的投放状态', 'f', NULL, NULL, 0);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 3, '展点信息(媒体)', '投放数据', 't', 'cost', '消耗', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 0, 1, 0, '表示广告在投放期内的消耗金额', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 4, '展点信息(媒体)', '投放数据', 't', 'viewNum', '展示数', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 0, 1, 1, '广告展示给用户的次数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '展点信息(媒体)', '投放数据', 'f', 'thousandImpressAvgPrice', '平均千次展现费用', '', NULL, NULL, NULL, 0, 1, 0, '{0}', 0, 1, 2, '广告平均每一千次展现所付出的费用，计算公式是：总消耗/展示数*1000', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 5, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 3, '当用户点击广告素材时，触发点击事件，该事件被认为是一次有效的广告点击', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 6, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 4, '广告被点击的次数占展示次数的百分比。计算方法：点击数/展示数*100%', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '展点信息(媒体)', '投放数据', 'f', 'avgPrice', '平均点击单价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 5, '广告主为每次点击付出的费用成本，计算公式是：总消耗/点击数', 't', NULL, NULL, 0);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 7, '转化数据(媒体)', '投放数据', 't', 'convertNum', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 1, 0, '转化数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '转化数据(媒体)', '投放数据', 'f', 'targetConvertRate', '转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 1, 1, '转化数占点击次数的百分比。计算方式：转化数/点击数*100%', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 8, '转化数据(媒体)', '投放数据', 't', 'targetConvertCost', '平均转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 1, 2, '每个转化所付出的平均成本，计算方式：总消耗/转化数。当天数据可能会有波动。', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertNum', '深度转化数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 1, 3, '深度转化数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertRate', '深度转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 1, 4, '深度转化的次数占转化次数的百分比。计算方式：深度转化数/转化数*100%', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertCost', '深度转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 1, 5, '每个深度转化所付出的平均成本，计算方法：总消耗/深度转化数。当天数据可能会有波动，次日早8点后稳定。', 't', NULL, NULL, 0);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountNum', '表单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0, '一叶落地页表单提交成功数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountRate', '表单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1, '表单提交数 / 点击数*100%  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountCost', '表单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 2, '消耗/表单提交数  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3, '一叶落地页订单提交成功数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4, '订单提交数/点击数*100%  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5, '消耗/订单提交数  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6, '一叶落地页的订单提交成功并完成支付的数量(包含货到付款）', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7, '订单完成数/点击数*100%  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8, '消耗/订单完成数  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'orderTransactionAmount', '订单成交金额', '', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 9, '订单完成支付金额总和', 't', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'orderTransactionRoi', '订单成交ROI', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 10, '订单成交金额/广告消耗  保留小数点后两位', 't', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrCodeNum', '长按二维码识别数(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 11, '一叶落地页长按二维码识别数（微信/企业微信）', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrCodeRate', '长按二维码识别率(微信/企业微信)', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 12, '长按二维码识别数（微信/企业微信）/点击数*100%  保留小数点后两位', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrcodeCost', '长按二维码识别成本(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 13, '消耗/长按二维码识别数（微信/企业微信）  保留小数点后两位', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 9, '基础数据(中台)', '行为转化', 't', 'addWorkWechatNum', '成功添加企业微信数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 14, '一叶落地页成功添加企业微信数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatRate', '成功添加企业微信率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 15, '加企业微信数/点击数*100%  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 10, '基础数据(中台)', '行为转化', 't', 'addWorkWechatCost', '成功添加企业微信成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 16, '消耗/成功添加企业微信数  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeNum', '长按二维码识别数(公众号)', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 17, '一叶落地页长按二维码识别数（公众号）', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeRate', '长按二维码识别率(公众号)', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 18, '长按二维码识别数（公众号）/点击数*100%  保留小数点后两位', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'enterpriseIncreaseIncomingCost', '长按二维码识别成本(公众号)', '', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 19, '消耗/长按二维码识别数（公众号）  保留小数点后两位', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountNum', '微信公众号关注数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 20, '一叶落地页成功关注公众号数', 't', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountRate', '微信公众号关注率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 21, '关注公众号数/点击数*100%  保留小数点后两位', 't', 240, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountCost', '微信公众号关注成本', '', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 22, '消耗/关注公众号数  保留小数点后两位', 't', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessNum', '电商商品购买数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 23, '一叶落地页成功购买电商商品订单数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessRate', '电商商品购买率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 24, '电商商品购买数/点击数*100%  保留小数点后两位', 't', 240, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessCost', '电商商品单笔购买成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 25, '消耗/电商商品购买数', 't', 160, 140, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessAmount', '电商商品成交金额', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 26, '该账户/广告组/广告计划下对应电商客资下单金额总和', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (17, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessRoi', '电商商品成交ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 27, '电商商品成交金额/广告消耗  保留小数点后两位', 't', NULL, NULL, 0);


-- 广告层级自定义列（投放面板 - 巨量 - 广告）
delete from marketing_customer_field where type = 18;
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 3, '基础属性(媒体)', '属性指标', 't', 'accountName', '账户', '', NULL, now(), now(), 0, 5, 0, '{1,2,3,5,8}', 0, 0, 0, '投放账户名称及ID', 'f', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础属性(媒体)', '属性指标', 'f', 'managerName', '管理员', '', NULL, now(), now(), 0, 5, 0, '{1,2,3,5,8}', 0, 0, 1, '投放账户管理员', 'f', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 2, '基础属性(媒体)', '属性指标', 't', 'projectName', '项目', '', NULL, now(), now(), 0, 5, 0, '{1,2,3,5,8}', 0, 0, 2, '项目名称及ID（对应巨量引擎广告旧版中的“广告组”）', 'f', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础属性(媒体)', '属性指标', 'f', 'projectStatus', '项目状态', '', NULL, now(), now(), 0, 5, 0, '{1,2,3,5,8}', 0, 0, 3, '项目对应的投放状态', 'f', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础属性(媒体)', '属性指标', 't', 'promotionName', '广告', '', NULL, now(), now(), 0, 5, 1, '{1,2,3,5,8}', 0, 0, 4, '广告名称及ID（对应巨量引擎广告旧版中的“计划”）', 'f', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 1, '基础属性(媒体)', '属性指标', 't', 'promotionStatus', '广告状态', '', NULL, now(), now(), 0, 5, 0, '{1,2,3,5,8}', 0, 0, 5, '广告对应的投放状态', 'f', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础属性(媒体)', '属性指标', 'f', 'learningPhase', '学习期状态', '', NULL, now(), now(), 0, 5, 0, '{1,2,3,5,8}', 0, 0, 6, '对应广告计划学习期状态', 'f', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础属性(媒体)', '属性指标', 'f', 'bid', '出价', '', NULL, now(), now(), 0, 5, 0, '{1,2,3,5,8}', 0, 0, 7, '对应广告计划所设置的出价', 'f', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础属性(媒体)', '属性指标', 'f', 'deepCpabid', '深度转化出价', '', NULL, now(), now(), 0, 5, 0, '{1,2,3,5,8}', 0, 0, 8, '对应广告计划所设置的深度转化出价', 'f', NULL, NULL, 0);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 4, '展点信息(媒体)', '投放数据', 't', 'cost', '消耗', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 0, 1, 0, '表示广告在投放期内的消耗金额', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 5, '展点信息(媒体)', '投放数据', 't', 'viewNum', '展示数', '', NULL, NULL, NULL, 0, 5, 0, '{1,2,3,5,8}', 0, 1, 1, '广告展示给用户的次数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '展点信息(媒体)', '投放数据', 'f', 'thousandImpressAvgPrice', '平均千次展现费用', '', NULL, NULL, NULL, 0, 1, 0, '{0}', 0, 1, 2, '广告平均每一千次展现所付出的费用，计算公式是：总消耗/展示数*1000', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 6, '展点信息(媒体)', '投放数据', 't', 'clickNum', '点击数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,8}', 0, 1, 3, '当用户点击广告素材时，触发点击事件，该事件被认为是一次有效的广告点击', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 7, '展点信息(媒体)', '投放数据', 't', 'clickRate', '点击率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 4, '广告被点击的次数占展示次数的百分比。计算方法：点击数/展示数*100%', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '展点信息(媒体)', '投放数据', 'f', 'avgPrice', '平均点击单价', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 1, 5, '广告主为每次点击付出的费用成本，计算公式是：总消耗/点击数', 't', NULL, NULL, 0);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 8, '转化数据(媒体)', '投放数据', 't', 'convertNum', '转化数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 1, 0, '转化数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '转化数据(媒体)', '投放数据', 'f', 'targetConvertRate', '转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 1, 1, '转化数占点击次数的百分比。计算方式：转化数/点击数*100%', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 9, '转化数据(媒体)', '投放数据', 't', 'targetConvertCost', '平均转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 1, 2, '每个转化所付出的平均成本，计算方式：总消耗/转化数。当天数据可能会有波动。', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertNum', '深度转化数', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 1, 3, '深度转化数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertRate', '深度转化率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 1, 1, 4, '深度转化的次数占转化次数的百分比。计算方式：深度转化数/转化数*100%', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '转化数据(媒体)', '投放数据', 'f', 'deepConvertCost', '深度转化成本', '', NULL, NULL, NULL, 0, 4, 0, '{1,2,3}', 1, 1, 5, '每个深度转化所付出的平均成本，计算方法：总消耗/深度转化数。当天数据可能会有波动，次日早8点后稳定。', 't', NULL, NULL, 0);

INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountNum', '表单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 0, '一叶落地页表单提交成功数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountRate', '表单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 1, '表单提交数 / 点击数*100%  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'fillCountCost', '表单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 2, '消耗/表单提交数  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'orderNum', '订单提交数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 3, '一叶落地页订单提交成功数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'orderCountRate', '订单提交率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 4, '订单提交数/点击数*100%  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'orderCountCost', '订单提交成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 5, '消耗/订单提交数  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishNum', '订单完成数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 6, '一叶落地页的订单提交成功并完成支付的数量(包含货到付款）', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishRate', '订单完成率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 7, '订单完成数/点击数*100%  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'orderFinishCost', '订单完成成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 8, '消耗/订单完成数  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'orderTransactionAmount', '订单成交金额', '', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 9, '订单完成支付金额总和', 't', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'orderTransactionRoi', '订单成交ROI', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 10, '订单成交金额/广告消耗  保留小数点后两位', 't', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrCodeNum', '长按二维码识别数(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 11, '一叶落地页长按二维码识别数（微信/企业微信）', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrCodeRate', '长按二维码识别率(微信/企业微信)', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 12, '长按二维码识别数（微信/企业微信）/点击数*100%  保留小数点后两位', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'identifyQrcodeCost', '长按二维码识别成本(微信/企业微信)', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 13, '消耗/长按二维码识别数（微信/企业微信）  保留小数点后两位', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 10, '基础数据(中台)', '行为转化', 't', 'addWorkWechatNum', '成功添加企业微信数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 14, '一叶落地页成功添加企业微信数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'addWorkWechatRate', '成功添加企业微信率', '%', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 15, '加企业微信数/点击数*100%  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 11, '基础数据(中台)', '行为转化', 't', 'addWorkWechatCost', '成功添加企业微信成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 16, '消耗/成功添加企业微信数  保留小数点后两位', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeNum', '长按二维码识别数(公众号)', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 17, '一叶落地页长按二维码识别数（公众号）', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'officialIdentifyQrCodeRate', '长按二维码识别率(公众号)', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 18, '长按二维码识别数（公众号）/点击数*100%  保留小数点后两位', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'enterpriseIncreaseIncomingCost', '长按二维码识别成本(公众号)', '', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 19, '消耗/长按二维码识别数（公众号）  保留小数点后两位', 't', 240, 220, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountNum', '微信公众号关注数', NULL, NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 20, '一叶落地页成功关注公众号数', 't', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountRate', '微信公众号关注率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 21, '关注公众号数/点击数*100%  保留小数点后两位', 't', 240, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'followOfficialAccountCost', '微信公众号关注成本', '', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 22, '消耗/关注公众号数  保留小数点后两位', 't', 160, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessNum', '电商商品购买数', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 23, '一叶落地页成功购买电商商品订单数', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessRate', '电商商品购买率', '%', NULL, '2022-10-19 20:31:30.848447', '2022-10-19 20:31:30.848447', 0, 0, 0, '{0}', 0, 2, 24, '电商商品购买数/点击数*100%  保留小数点后两位', 't', 240, 112, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessCost', '电商商品单笔购买成本', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 25, '消耗/电商商品购买数', 't', 160, 140, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessAmount', '电商商品成交金额', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 26, '该账户/广告组/广告计划下对应电商客资下单金额总和', 't', NULL, NULL, 0);
INSERT INTO marketing_customer_field("type", "no", "sub_category", "setting_category", "checked", "field", "name", "unit", "formula_id", "created_at", "updated_at", "user_id", "platform_num", "is_freeze", "platform_ids", "sub_category_no", "setting_category_no", "field_no", "field_interpretation", "able_sort", "default_width", "min_width", "default_data") VALUES (18, 0, '基础数据(中台)', '行为转化', 'f', 'onlineShopBuyGoodsSuccessRoi', '电商商品成交ROI', '', NULL, NULL, NULL, 0, 4, 0, '{0}', 0, 2, 27, '电商商品成交金额/广告消耗  保留小数点后两位', 't', NULL, NULL, 0);


drop table if exists marketing_data_ocean_ad_report;
create table if not exists marketing_data_ocean_ad_report
(
    id               bigserial,
    account_id       varchar,
    project_id       bigint,
    promotion_id     bigint,
    stat_cost        double precision,
    show_cnt         bigint,
    click_cnt        bigint,
    convert_cnt      bigint,
    deep_convert_cnt bigint,
    stat_hour_time   timestamp,
    stat_day_time    timestamp,
    create_time      timestamp default CURRENT_TIMESTAMP,
    update_time      timestamp default CURRENT_TIMESTAMP
);
comment on table marketing_data_ocean_ad_report is '实时数据推送表';
comment on column marketing_data_ocean_ad_report.id is '主键id';
comment on column marketing_data_ocean_ad_report.account_id is '账户ID';
comment on column marketing_data_ocean_ad_report.project_id is '项目ID';
comment on column marketing_data_ocean_ad_report.promotion_id is '广告ID';
comment on column marketing_data_ocean_ad_report.stat_cost is '消耗';
comment on column marketing_data_ocean_ad_report.show_cnt is '展示数';
comment on column marketing_data_ocean_ad_report.click_cnt is '点击数';
comment on column marketing_data_ocean_ad_report.convert_cnt is '转化数';
comment on column marketing_data_ocean_ad_report.deep_convert_cnt is '深度转化数';
comment on column marketing_data_ocean_ad_report.stat_hour_time is '数据产生时间-hour';
comment on column marketing_data_ocean_ad_report.stat_day_time is '数据产生时间-day';
comment on column marketing_data_ocean_ad_report.create_time is '记录创建时间';
comment on column marketing_data_ocean_ad_report.update_time is '记录修改时间';
create unique index if not exists ux_stat_hour_time_marketing_data_ocean_ad_report on marketing_data_ocean_ad_report (account_id, project_id, promotion_id, stat_hour_time);
create index if not exists idx_stat_day_time_marketing_data_ocean_ad_report on marketing_data_ocean_ad_report (stat_day_time);
