alter table landing_page_pmp_params_config add column if not exists feishu_robot_key varchar;
comment on column landing_page_pmp_params_config.feishu_robot_key is '飞书自定义机器人key';

alter table landing_page_pmp_params_config add column if not exists wecom_robot_key varchar;
comment on column landing_page_pmp_params_config.wecom_robot_key is '企业微信自定义机器人key';

alter table landing_page_pmp_params_config add column if not exists dingtalk_robot_key varchar;
comment on column landing_page_pmp_params_config.dingtalk_robot_key is '钉钉自定义机器人key';

alter table agent_conf add column if not exists sina_weibo_settlement_company_name varchar;
comment on column agent_conf.sina_weibo_settlement_company_name is '新浪微博广告账户结算主体公司名称';

alter table landing_page_douyin_applet_config add column if not exists navigate_mini_programs varchar[] default '{}';
comment on column landing_page_douyin_applet_config.navigate_mini_programs is '跳转小程序配置';

alter table landing_page add column if not exists fill_wecom_remark_mobiles int4 default 1;
comment on column landing_page.fill_wecom_remark_mobiles is '用户加粉后在企微备注信息中添加手机号码信息';

alter table landing_page_preview add column if not exists fill_wecom_remark_mobiles int4 default 1;
comment on column landing_page_preview.fill_wecom_remark_mobiles is '用户加粉后在企微备注信息中添加手机号码信息';




alter table landing_page_widget_template add if not exists order_template_type int4 default 0;
comment on column landing_page_widget_template.order_template_type is '订单模板类型 0:H5订单 1:字节小程序原生订单';

alter table landing_page_widget_template add if not exists relate_orders int8[] default '{}';
comment on column landing_page_widget_template.relate_orders is '关联的限购订单（对应landing_page_widget_template.id的集合）';

alter table page_view_info add column if not exists order_template_type int4 default 1002;
comment on column page_view_info.order_template_type is '订单模板类型 0:H5订单 1:字节小程序原生订单(1002是未知,为了防止新查询有问题，默认值不用null处理)';

alter table submit_data add if not exists order_template_type int4 default 1002;
comment on column submit_data.order_template_type is '订单模板类型 0:H5订单 1:字节小程序原生订单(1002是未知,为了防止新查询有问题，默认值不用null处理)';

alter table customer add if not exists order_template_type int4 default 1002;
comment on column customer.order_template_type is '订单模板类型 0:H5订单 1:字节小程序原生订单(1002是未知,为了防止新查询有问题，默认值不用null处理)';

--boss后台聚合报表--
alter table boss_advertiser_account_group_day_report add if not exists douyin_applet_order_submit_num int8 default 0;
comment on column boss_advertiser_account_group_day_report.douyin_applet_order_submit_num is '字节小程序原生订单提交数';

alter table boss_advertiser_account_group_day_report add if not exists douyin_applet_order_finish_num int8 default 0;
comment on column boss_advertiser_account_group_day_report.douyin_applet_order_finish_num is '字节小程序原生订单完成数';

alter table boss_advertiser_account_group_day_report add if not exists order_submit_num int8 default 0;
comment on column boss_advertiser_account_group_day_report.order_submit_num is '订单提交数';

alter table boss_advertiser_account_group_day_report add if not exists order_finish_num int8 default 0;
comment on column boss_advertiser_account_group_day_report.order_finish_num is '订单完成数';

alter table boss_advertiser_account_group_day_report add if not exists online_shop_buy_goods_success_num int8 default 0;
comment on column boss_advertiser_account_group_day_report.online_shop_buy_goods_success_num is '电商商品购买数';


--boss后台聚合报表（新查询）--
alter table boss_advertiser_account_group_day_report_new add if not exists douyin_applet_order_submit_num int8 default 0;
comment on column boss_advertiser_account_group_day_report_new.douyin_applet_order_submit_num is '字节小程序原生订单提交数';

alter table boss_advertiser_account_group_day_report_new add if not exists douyin_applet_order_finish_num int8 default 0;
comment on column boss_advertiser_account_group_day_report_new.douyin_applet_order_finish_num is '字节小程序原生订单完成数';

alter table boss_advertiser_account_group_day_report_new add if not exists order_submit_num int8 default 0;
comment on column boss_advertiser_account_group_day_report_new.order_submit_num is '订单提交数';

alter table boss_advertiser_account_group_day_report_new add if not exists order_finish_num int8 default 0;
comment on column boss_advertiser_account_group_day_report_new.order_finish_num is '订单完成数';

alter table boss_advertiser_account_group_day_report_new add if not exists online_shop_buy_goods_success_num int8 default 0;
comment on column boss_advertiser_account_group_day_report_new.online_shop_buy_goods_success_num is '电商商品购买数';

--项目概况报表----

--1、曝光口径报表--
alter table marketing_project_overview_ad_show_data_solidification_report add if not exists douyin_applet_order_submit_num int8 default 0;
comment on column marketing_project_overview_ad_show_data_solidification_report.douyin_applet_order_submit_num is '字节小程序原生订单提交数';

alter table marketing_project_overview_ad_show_data_solidification_report add if not exists douyin_applet_order_finish_num int8 default 0;
comment on column marketing_project_overview_ad_show_data_solidification_report.douyin_applet_order_finish_num is '字节小程序原生订单完成数';

alter table marketing_project_overview_ad_show_data_solidification_report add if not exists douyin_applet_order_amount decimal(10,2) default 0;
comment on column marketing_project_overview_ad_show_data_solidification_report.douyin_applet_order_amount is '字节小程序原生订单成交金额';


--2、转化口径报表--
alter table marketing_project_ad_convert_data_solidification_report add if not exists douyin_applet_order_submit_num int8 default 0;
comment on column marketing_project_ad_convert_data_solidification_report.douyin_applet_order_submit_num is '字节小程序原生订单提交数';

alter table marketing_project_ad_convert_data_solidification_report add if not exists douyin_applet_order_finish_num int8 default 0;
comment on column marketing_project_ad_convert_data_solidification_report.douyin_applet_order_finish_num is '字节小程序原生订单完成数';

alter table marketing_project_ad_convert_data_solidification_report add if not exists douyin_applet_order_amount decimal(10,2) default 0;
comment on column marketing_project_ad_convert_data_solidification_report.douyin_applet_order_amount is '字节小程序原生订单成交金额';

--1、曝光口径报表（新查询）--
alter table marketing_project_ad_show_data_solidification_report_clickhouse add if not exists douyin_applet_order_submit_num int8 default 0;
comment on column marketing_project_ad_show_data_solidification_report_clickhouse.douyin_applet_order_submit_num is '字节小程序原生订单提交数';

alter table marketing_project_ad_show_data_solidification_report_clickhouse add if not exists douyin_applet_order_finish_num int8 default 0;
comment on column marketing_project_ad_show_data_solidification_report_clickhouse.douyin_applet_order_finish_num is '字节小程序原生订单完成数';

alter table marketing_project_ad_show_data_solidification_report_clickhouse add if not exists douyin_applet_order_amount decimal(10,2) default 0;
comment on column marketing_project_ad_show_data_solidification_report_clickhouse.douyin_applet_order_amount is '字节小程序原生订单成交金额';

--2、转化口径报表（新查询）--
alter table marketing_project_ad_convert_data_solid_report_clickhouse add if not exists douyin_applet_order_submit_num int8 default 0;
comment on column marketing_project_ad_convert_data_solid_report_clickhouse.douyin_applet_order_submit_num is '字节小程序原生订单提交数';

alter table marketing_project_ad_convert_data_solid_report_clickhouse add if not exists douyin_applet_order_finish_num int8 default 0;
comment on column marketing_project_ad_convert_data_solid_report_clickhouse.douyin_applet_order_finish_num is '字节小程序原生订单完成数';

alter table marketing_project_ad_convert_data_solid_report_clickhouse add if not exists douyin_applet_order_amount decimal(10,2) default 0;
comment on column marketing_project_ad_convert_data_solid_report_clickhouse.douyin_applet_order_amount is '字节小程序原生订单成交金额';


--更新落地页和渠道列表自定义列--
update marketing_customer_field set name = 'H5订单提交数' , field_no  = 1  where type in (9,10) and setting_category = '订单数据' and field = 'orderSubmitNum';
update marketing_customer_field set name = 'H5订单提交率' , field_no = 2 where type in (9,10) and setting_category = '订单数据' and field = 'orderSubmitRate';
update marketing_customer_field set name = 'H5订单完成数' , field_no = 3 where type in (9,10) and setting_category = '订单数据' and field = 'paymentNum';
update marketing_customer_field set name = 'H5订单完成率' , field_no = 4 where type in (9,10) and setting_category = '订单数据' and field = 'paymentRate';
update marketing_customer_field set field_no = 9 where type in (9,10) and setting_category = '订单数据' and field = 'comprehensivePaymentRate';
update marketing_customer_field set field_no = 10 where type in (9,10) and setting_category = '订单数据' and field = 'onlineShopBuyGoodsSuccessNum';
update marketing_customer_field set field_no = 11 where type in (9,10) and setting_category = '订单数据' and field = 'onlineShopBuyGoodsSuccessRate';


--更新项目概况日历表自定义列--
update marketing_customer_field set name = 'H5订单提交数'  where type = 0 and setting_category = '行为转化' and field = 'orderNum';
update marketing_customer_field set name = 'H5订单提交率'  where type = 0 and setting_category = '行为转化' and field = 'orderCountRate';
update marketing_customer_field set name = 'H5订单提交成本'  where type = 0 and setting_category = '行为转化' and field = 'orderCountCost';
update marketing_customer_field set name = 'H5订单完成数'  where type = 0 and setting_category = '行为转化' and field = 'orderFinishNum';
update marketing_customer_field set name = 'H5订单完成率'  where type = 0 and setting_category = '行为转化' and field = 'orderFinishRate';
update marketing_customer_field set name = 'H5订单完成成本'  where type = 0 and setting_category = '行为转化' and field = 'orderFinishCost';

--更新项目概况-数据概况的自定义列--
update validity_summary_field set name = 'H5订单提交数' where type = 3 AND field = 'orderNum';
update validity_summary_field set name = 'H5订单提交率' where type = 3 AND field = 'orderCountRate';
update validity_summary_field set name = 'H5订单提交成本' where type = 3 AND field = 'orderCountCost';
update validity_summary_field set name = 'H5订单完成数' where type = 3 AND field = 'orderFinishNum';
update validity_summary_field set name = 'H5订单完成率' where type = 3 AND field = 'orderFinishRate';
update validity_summary_field set name = 'H5订单完成成本' where type = 3 AND field = 'orderFinishCost';
update validity_summary_field set name = 'H5订单成交金额' where type = 3 AND field = 'orderAmount';



--新增自定义列---
----落地页----
delete from marketing_customer_field where type = 9 and field = 'douyinAppletOrderSubmitNum';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (9, 16, null, '订单数据', false, 'douyinAppletOrderSubmitNum', '字节小程序原生订单提交数', '', null, now(), now(), 0, 0, 0, null, null, 8, 5, '页面内通过字节小程序原生订单模板提交订单的数量', false, 208, 112, 0);

delete from marketing_customer_field where type = 9 and field = 'douyinAppletOrderSubmitRate' ;
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (9, 17, null, '订单数据', false, 'douyinAppletOrderSubmitRate', '字节小程序原生订单提交率', '%', null, now(), now(), 0, 0, 0, null, null, 8, 6, '字节小程序原生订单提交数数占浏览数的比例计算公式：订单提交数 / 浏览数*100%', false, 208, 112, 0);

delete from marketing_customer_field where type = 9 and field = 'douyinAppletOrderFinishNum';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (9, 18, null, '订单数据', false, 'douyinAppletOrderFinishNum', '字节小程序原生订单完成数', '', null, now(), now(), 0, 0, 0, null, null, 8, 7, '页面内通过字节小程序原生订单模板,支付成功的订单数量', false, 208, 112, 0);

delete from marketing_customer_field where type = 9 and field = 'douyinAppletOrderFinishRate' ;
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (9, 19, null, '订单数据', false, 'douyinAppletOrderFinishRate', '字节小程序原生订单完成率', '%', null, now(), now(), 0, 0, 0, null, null, 8, 8, '订单完成数占订单提交数的比例计算公式：订单完成数 / 订单提交数*100%(页面内通过字节小程序原生订单模板)', false, 208, 112, 0);

delete from marketing_customer_field where type = 9 and default_data != 0;

--渠道列表自定义列--
delete from marketing_customer_field where type = 10 and field = 'douyinAppletOrderSubmitNum';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (10, 16, null, '订单数据', false, 'douyinAppletOrderSubmitNum', '字节小程序原生订单提交数', '', null, now(), now(), 0, 0, 0, null, null, 8, 5, '页面内通过字节小程序原生订单模板提交订单的数量', false, 208, 112, 0);

delete from marketing_customer_field where type = 10 and field = 'douyinAppletOrderSubmitRate' ;
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (10, 17, null, '订单数据', false, 'douyinAppletOrderSubmitRate', '字节小程序原生订单提交率', '%', null, now(), now(), 0, 0, 0, null, null, 8, 6, '字节小程序原生订单提交数数占浏览数的比例计算公式：订单提交数 / 浏览数*100%', false, 208, 112, 0);

delete from marketing_customer_field where type = 10 and field = 'douyinAppletOrderFinishNum';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (10, 18, null, '订单数据', false, 'douyinAppletOrderFinishNum', '字节小程序原生订单完成数', '', null, now(), now(), 0, 0, 0, null, null, 8, 7, '页面内通过字节小程序原生订单模板,支付成功的订单数量', false, 208, 112, 0);

delete from marketing_customer_field where type = 10 and field = 'douyinAppletOrderFinishRate' ;
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (10, 19, null, '订单数据', false, 'douyinAppletOrderFinishRate', '字节小程序原生订单完成率', '%', null, now(), now(), 0, 0, 0, null, null, 8, 8, '订单完成数占订单提交数的比例计算公式：订单完成数 / 订单提交数*100%(页面内通过字节小程序原生订单模板)', false, 208, 112, 0);

delete from marketing_customer_field where type = 10 and default_data != 0;


-----------项目概况（日历表）新增自定义列------------------
delete from marketing_customer_field where type = 0 and field = 'douyinAppletOrderSubmitNum';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (0, 8, '基础指标(中台)', '行为转化', false, 'douyinAppletOrderSubmitNum', '字节小程序原生订单提交数', '', null, now(), now(), 0, 0, 0, null, null, 2, 9, '页面内通过字节小程序原生订单模板提交订单的数量', false, 208, 112, 0);


delete from marketing_customer_field where type = 0 and field = 'douyinAppletOrderSubmitRate';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (0, 9, '基础指标(中台)', '行为转化', false, 'douyinAppletOrderSubmitRate', '字节小程序原生订单提交率', '%', null, now(), now(), 0, 0, 0, null, null, 2, 10, '字节小程序原生订单提交数/落地页PV*100% 保留小数点后两位', false, 208, 112, 0);

delete from marketing_customer_field where type = 0 and field = 'douyinAppletOrderSubmitCost';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (0, 10, '基础指标(中台)', '行为转化', false, 'douyinAppletOrderSubmitCost', '字节小程序原生订单提交成本', '', null, now(), now(), 0, 0, 0, null, null, 2, 11, '消耗/字节小程序原生订单提交数 保留小数点后两位', false, 208, 112, 0);


delete from marketing_customer_field where type = 0 and field = 'douyinAppletOrderFinishNum';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (0, 11, '基础指标(中台)', '行为转化', false, 'douyinAppletOrderFinishNum', '字节小程序原生订单完成数', '', null, now(), now(), 0, 0, 0, null, null, 2, 12, '字节小程序原生订单提交成功并完成支付的数量', false, 208, 112, 0);


delete from marketing_customer_field where type = 0 and field = 'douyinAppletOrderFinishRate';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (0, 12, '基础指标(中台)', '行为转化', false, 'douyinAppletOrderFinishRate', '字节小程序原生订单完成率', '%', null, now(), now(), 0, 0, 0, null, null, 2, 13, '字节小程序原生订单完成数/落地页PV*100% 保留小数点后两位', false, 208, 112, 0);

delete from marketing_customer_field where type = 0 and field = 'douyinAppletOrderFinishCost';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data)
values (0, 13, '基础指标(中台)', '行为转化', false, 'douyinAppletOrderFinishCost', '字节小程序原生订单完成成本', '', null, now(), now(), 0, 0, 0, null, null, 2, 14, '字节小程序原生订单完成数/落地页PV*100% 保留小数点后两位', false, 208, 112, 0);

delete from marketing_customer_field where type = 0 and default_data != 0;

-----更新自定义列顺序------
update marketing_customer_field set field_no = 1 where type = 0 and setting_category = '行为转化' and field = 'landingPagePv';
update marketing_customer_field set field_no = 2, name = 'H5订单提交数' where type = 0 and setting_category = '行为转化' and field = 'orderNum';
update marketing_customer_field set field_no = 3, name = 'H5订单提交率' where type = 0 and setting_category = '行为转化' and field = 'orderCountRate';
update marketing_customer_field set field_no = 4, name = 'H5订单提交成本' where type = 0 and setting_category = '行为转化' and field = 'orderCountCost';
update marketing_customer_field set field_no = 5, name = 'H5订单完成数' where type = 0 and setting_category = '行为转化' and field = 'orderFinishNum';
update marketing_customer_field set field_no = 6, name = 'H5订单完成率' where type = 0 and setting_category = '行为转化' and field = 'orderFinishRate';
update marketing_customer_field set field_no = 7, name = 'H5订单完成成本' where type = 0 and setting_category = '行为转化' and field = 'orderFinishCost';
update marketing_customer_field set field_no = 15 where type = 0 and setting_category = '行为转化' and field = 'fillCountNum';
update marketing_customer_field set field_no = 16 where type = 0 and setting_category = '行为转化' and field = 'fillCountRate';
update marketing_customer_field set field_no = 17 where type = 0 and setting_category = '行为转化' and field = 'clueFormSubmitNum';
update marketing_customer_field set field_no = 18 where type = 0 and setting_category = '行为转化' and field = 'clueFormSubmitRate';
update marketing_customer_field set field_no = 19 where type = 0 and setting_category = '行为转化' and field = 'douyinAppletNativeFormSubmitNum';
update marketing_customer_field set field_no = 20 where type = 0 and setting_category = '行为转化' and field = 'douyinAppletNativeFormSubmitRate';
update marketing_customer_field set field_no = 21 where type = 0 and setting_category = '行为转化' and field = 'formSubmitTotalNum';
update marketing_customer_field set field_no = 22 where type = 0 and setting_category = '行为转化' and field = 'formSubmitTotalRate';
update marketing_customer_field set field_no = 23 where type = 0 and setting_category = '行为转化' and field = 'onlineShopBuyGoodsSuccessNum';
update marketing_customer_field set field_no = 24 where type = 0 and setting_category = '行为转化' and field = 'onlineShopBuyGoodsSuccessRate';
update marketing_customer_field set field_no = 25 where type = 0 and setting_category = '行为转化' and field = 'onlineShopBuyGoodsSuccessAmount';
update marketing_customer_field set field_no = 26 where type = 0 and setting_category = '行为转化' and field = 'onlineShopBuyGoodsSuccessCost';
update marketing_customer_field set field_no = 27 where type = 0 and setting_category = '行为转化' and field = 'addWorkWechatNum';
update marketing_customer_field set field_no = 28 where type = 0 and setting_category = '行为转化' and field = 'addWorkWechatRate';
update marketing_customer_field set field_no = 29 where type = 0 and setting_category = '行为转化' and field = 'addWorkWechatCost';
update marketing_customer_field set field_no = 30 where type = 0 and setting_category = '行为转化' and field = 'identifyQrCodeNum';
update marketing_customer_field set field_no = 31 where type = 0 and setting_category = '行为转化' and field = 'identifyQrCodeRate';
update marketing_customer_field set field_no = 32 where type = 0 and setting_category = '行为转化' and field = 'identifyQrcodeCost';
update marketing_customer_field set field_no = 33 where type = 0 and setting_category = '行为转化' and field = 'officialIdentifyQrCodeNum';
update marketing_customer_field set field_no = 34 where type = 0 and setting_category = '行为转化' and field = 'officialIdentifyQrCodeRate';
update marketing_customer_field set field_no = 35 where type = 0 and setting_category = '行为转化' and field = 'enterpriseIncreaseIncomingCost';
update marketing_customer_field set field_no = 36 where type = 0 and setting_category = '行为转化' and field = 'followOfficialAccountNum';
update marketing_customer_field set field_no = 37 where type = 0 and setting_category = '行为转化' and field = 'followOfficialAccountRate';
update marketing_customer_field set field_no = 38 where type = 0 and setting_category = '行为转化' and field = 'followOfficialAccountCost';
update marketing_customer_field set field_no = 39 where type = 0 and setting_category = '行为转化' and field = 'phoneNumberRecievedNum';
update marketing_customer_field set field_no = 40 where type = 0 and setting_category = '行为转化' and field = 'phoneNumberRecievedRate';
update marketing_customer_field set field_no = 41 where type = 0 and setting_category = '行为转化' and field = 'activeMessageAuthorizationNum';
update marketing_customer_field set field_no = 42 where type = 0 and setting_category = '行为转化' and field = 'activeMessageAuthorizationRate';

--项目概况顶部的数据概况自定义列--

delete from validity_summary_field where type = 3 and field = 'douyinAppletOrderSubmitNum';
INSERT INTO validity_summary_field(type, no, field, name, color, unit, created_at, updated_at, user_id, formula_id, value_color, bg_color,field_definition) VALUES (3, 24, 'douyinAppletOrderSubmitNum', '字节小程序原生订单提交数', '#333333', '', NULL, NULL, 0, NULL, '#333333', 'rgba(250, 250, 250, 1)','页面内通过字节小程序原生订单模板提交订单的数量');

delete from validity_summary_field where type = 3 and field = 'douyinAppletOrderSubmitRate';
INSERT INTO validity_summary_field(type, no, field, name, color, unit, created_at, updated_at, user_id, formula_id, value_color, bg_color,field_definition) VALUES (3, 25, 'douyinAppletOrderSubmitRate', '字节小程序原生订单提交率', '#333333', '%', NULL, NULL, 0, NULL, '#1890FF', 'rgba(24, 144, 255, 0.0588235294117647)','字节小程序原生订单提交数占浏览数的比例计算公式：订单提交数 / 浏览数*100%');

delete from validity_summary_field where type = 3 and field = 'douyinAppletOrderSubmitCost';
INSERT INTO validity_summary_field(type, no, field, name, color, unit, created_at, updated_at, user_id, formula_id, value_color, bg_color,field_definition) VALUES (3, 26, 'douyinAppletOrderSubmitCost', '字节小程序原生订单提交成本', '#333333', '', NULL, NULL, 0, NULL, '#51C75B', 'rgba(81, 199, 91, 0.0588235294117647)','花费/页面内通过字节小程序原生订单模板提交订单的数量，保留2位小数');

delete from validity_summary_field where type = 3 and field = 'douyinAppletOrderFinishNum';
INSERT INTO validity_summary_field(type, no, field, name, color, unit, created_at, updated_at, user_id, formula_id, value_color, bg_color,field_definition) VALUES (3, 24, 'douyinAppletOrderFinishNum', '字节小程序原生订单完成数', '#333333', '', NULL, NULL, 0, NULL, '#333333', 'rgba(250, 250, 250, 1)','页面内通过字节小程序原生订单模板支付成功的订单数量');

delete from validity_summary_field where type = 3 and field = 'douyinAppletOrderFinishRate';
INSERT INTO validity_summary_field(type, no, field, name, color, unit, created_at, updated_at, user_id, formula_id, value_color, bg_color,field_definition) VALUES (3, 25, 'douyinAppletOrderFinishRate', '字节小程序原生订单完成率', '#333333', '%', NULL, NULL, 0, NULL, '#1890FF', 'rgba(24, 144, 255, 0.0588235294117647)','字节小程序原生订单支付成功的订单数数占浏览数的比例计算公式：订单提交数 / 浏览数*100%');

delete from validity_summary_field where type = 3 and field = 'douyinAppletOrderFinishCost';
INSERT INTO validity_summary_field(type, no, field, name, color, unit, created_at, updated_at, user_id, formula_id, value_color, bg_color,field_definition) VALUES (3, 26, 'douyinAppletOrderFinishCost', '字节小程序原生订单完成成本', '#333333', '', NULL, NULL, 0, NULL, '#51C75B', 'rgba(81, 199, 91, 0.0588235294117647)','花费/页面内通过字节小程序原生订单模板支付成功订单的数量，保留2位小数');

delete from validity_summary_field where type = 3 and default_data != 0;

---BOSS后台新增订单相关的自定义列----

delete from marketing_customer_field where type = 14 and field = 'orderSubmitNum';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (14, 0, null, 'PV消耗及转化', true, 'orderSubmitNum', 'H5订单提交数',  '', null, now(), now(), 0, 0, 0, null, null, 2,55, '页面内通过H5订单模板提交订单的数量', true, 160, 96,0);

delete from marketing_customer_field where type = 14 and field = 'orderSubmitRate';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (14, 0, null, 'PV消耗及转化', true, 'orderSubmitRate', 'H5订单提交率',  '%', null, now(), now(), 0, 0, 0, null, null, 2,56, 'H5页面订单提交数/落地页PV*100% 保留小数点后两位', true, 160, 96,0);

delete from marketing_customer_field where type = 14 and field = 'orderFinishNum';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (14, 0, null, 'PV消耗及转化', true, 'orderFinishNum', 'H5订单完成数',  '', null, now(), now(), 0, 0, 0, null, null, 2,57, '页面内通H5订单模板,支付成功的订单数量', true, 160, 96,0);

delete from marketing_customer_field where type = 14 and field = 'orderFinishRate';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (14, 0, null, 'PV消耗及转化', true, 'orderFinishRate', 'H5订单完成率',  '%', null, now(), now(), 0, 0, 0, null, null, 2,58, 'H5页面的订单完成数/落地页PV*100% 保留小数点后两位', true, 160, 96,0);


delete from marketing_customer_field where type = 14 and field = 'douyinAppletOrderSubmitNum';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (14, 0, null, 'PV消耗及转化', true, 'douyinAppletOrderSubmitNum', '字节小程序原生订单提交数',  '', null, now(), now(), 0, 0, 0, null, null, 2,59, '页面内通过字节小程序原生订单模板提交订单的数量', true, 160, 96,0);

delete from marketing_customer_field where type = 14 and field = 'douyinAppletOrderSubmitRate';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (14, 0, null, 'PV消耗及转化', true, 'douyinAppletOrderSubmitRate', '字节小程序原生订单提交率',  '%', null, now(), now(), 0, 0, 0, null, null, 2,60, '字节小程序原生订单提交数/落地页PV*100% 保留小数点后两位', true, 160, 96,0);

delete from marketing_customer_field where type = 14 and field = 'douyinAppletOrderFinishNum';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (14, 0, null, 'PV消耗及转化', true, 'douyinAppletOrderFinishNum', '字节小程序原生订单完成数',  '', null, now(), now(), 0, 0, 0, null, null, 2,61, '页面内通过字节小程序原生订单模板,支付成功的订单数量', true, 160, 96,0);

delete from marketing_customer_field where type = 14 and field = 'douyinAppletOrderFinishRate';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (14, 0, null, 'PV消耗及转化', true, 'douyinAppletOrderFinishRate', '字节小程序原生订单完成率',  '%', null, now(), now(), 0, 0, 0, null, null, 2,62, '字节小程序原生订单完成数/落地页PV*100% 保留小数点后两位', true, 160, 96,0);

delete from marketing_customer_field where type = 14 and field = 'onlineShopBuyGoodsSuccessNum';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (14, 0, null, 'PV消耗及转化', true, 'onlineShopBuyGoodsSuccessNum', '电商商品购买数',  '', null, now(), now(), 0, 0, 0, null, null, 2,63, '订单详情返回下单成功的数量', true, 160, 96,0);

delete from marketing_customer_field where type = 14 and field = 'onlineShopBuyGoodsSuccessRate';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (14, 0, null, 'PV消耗及转化', true, 'onlineShopBuyGoodsSuccessRate', '电商商品购买率',  '%', null, now(), now(), 0, 0, 0, null, null, 2,64, '下单成功的数量/PV*100%', true, 160, 96,0);

delete from marketing_customer_field where type = 14 and field = 'comprehensivePaymentRate';
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width,default_data) values
    (14, 0, null, 'PV消耗及转化', true, 'comprehensivePaymentRate', '综合订单完成率',  '%', null, now(), now(), 0, 0, 0, null, null, 2,65, '下单成功的数量/PV*100%', true, 160, 96,0);
delete from validity_summary_field where type = 14 and default_data != 0;
