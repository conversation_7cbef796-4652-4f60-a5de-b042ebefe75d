create table if not exists  marketing_advertiser_account_permission(
   id   bigserial primary key,
   user_id bigint null,
   agent_id varchar not null,
   advertiser_account_group_id bigint not null,
   functional_module  int not null,
   project_module int not null,
   permission int  null,
   delete_status int DEFAULT 0,
   created_at     timestamp default now() not null,
   updated_at     timestamp default now() not null
);


comment on table marketing_advertiser_account_permission is '项目成员权限表';
comment on column marketing_advertiser_account_permission.id is 'id主键';
comment on column marketing_advertiser_account_permission.agent_id is '账户ID';
comment on column marketing_advertiser_account_permission.user_id is '用户ID';
comment on column marketing_advertiser_account_permission.advertiser_account_group_id is 'pmpid';
comment on column marketing_advertiser_account_permission.project_module is '板块,对应枚举类ProjectModuleType';
comment on column marketing_advertiser_account_permission.functional_module is '功能模块，对应枚举类FunctionModuleType';
comment on column marketing_advertiser_account_permission.delete_status is '0-正常;1-删除';
comment on column marketing_advertiser_account_permission.permission is '权限';
comment on column marketing_advertiser_account_permission.created_at is '创建时间';
comment on column marketing_advertiser_account_permission.updated_at is '更新时间';

create index if not exists idx_agent_id_pmp on marketing_advertiser_account_permission (agent_id,advertiser_account_group_id);
create index if not exists idx_pmp_id on marketing_advertiser_account_permission (advertiser_account_group_id);
ALTER TABLE marketing_advertiser_account_permission ADD CONSTRAINT "pmpid_ agentid_userid_permission_pkey" unique(agent_id, advertiser_account_group_id, user_id, permission, project_module, functional_module);


create table if not exists official_account_assistant_quota(
   id   bigserial primary key,
   agent_id varchar not null,
   advertiser_account_group_id bigint not null,
   allocated_quota int default 0,
   consumption_quota int default 0,
   delete_status int DEFAULT 0,
   created_at     timestamp default now() not null,
   updated_at     timestamp default now() not null
);

comment on table official_account_assistant_quota is '公众号助手配额表';
comment on column official_account_assistant_quota.id is 'id主键';
comment on column official_account_assistant_quota.agent_id is '账户ID';
comment on column official_account_assistant_quota.advertiser_account_group_id is 'pmpid';
comment on column official_account_assistant_quota.allocated_quota is '当前项目已配置名额';
comment on column official_account_assistant_quota.consumption_quota is '实际消耗名额';
comment on column official_account_assistant_quota.created_at is '创建时间';
comment on column official_account_assistant_quota.updated_at is '更新时间';
comment on column official_account_assistant_quota.delete_status is '0-正常;1-删除';

create unique index if not exists idx_agent_id_pmp on official_account_assistant_quota (agent_id,advertiser_account_group_id);
create index if not exists idx_pmp_id on official_account_assistant_quota (advertiser_account_group_id);



create table if not exists wechat_official_account_assistant_rel
(
    id                                             bigserial
    primary key,
    agent_id                                        varchar,
    app_id                                          varchar,
    advertiser_account_group_id                     bigint,
    advertiser_account_group_name                   varchar,
    share                                           integer default 0,
    add_user_name                                       varchar,
    created_at                                     timestamp default now() not null,
    updated_at                                     timestamp default now() not null
    );
comment on table wechat_official_account_assistant_rel is '微信公众号助手关联表';
comment on column wechat_official_account_assistant_rel.id is 'id主键';
comment on column wechat_official_account_assistant_rel.agent_id is '账户ID';
comment on column wechat_official_account_assistant_rel.app_id is '公众号appid';
comment on column wechat_official_account_assistant_rel.advertiser_account_group_id is 'pmpid';
comment on column wechat_official_account_assistant_rel.advertiser_account_group_name is 'pmp名称';
comment on column wechat_official_account_assistant_rel.share is '是否为共享 0:否 1:是';
comment on column wechat_official_account_assistant_rel.add_user_name is '首次添加时的用户名';
comment on column wechat_official_account_assistant_rel.created_at is '创建时间';
comment on column wechat_official_account_assistant_rel.updated_at is '更新时间';

create index if not exists idx_official_account_assistant_agent_id_pmp on wechat_official_account_assistant_rel (agent_id,advertiser_account_group_id);
create index if not exists idx_official_account_assistant_app_id on wechat_official_account_assistant_rel (app_id);



create table if not exists wechat_official_account_assistant_reply
(
    id                                             bigserial
    primary key,
    recover_type                                    int,
    menu_navigation_id                              bigint,
    reply_type                                      int,
    status                                          int default 0,
    advertiser_account_group_id                     bigint,
    app_id                                          varchar,
    name                                            varchar,
    follow_channel                                  integer[] not null,
    official_follow_source                                   integer not null,
    follow_source_platform                          integer[],
    message_num                                     integer,
    delayed_push                                    integer default 0,
    created_at                                     timestamp default now() not null,
    updated_at                                     timestamp default now() not null
    );
comment on table wechat_official_account_assistant_reply is '微信公众号助手自动回复表';
comment on column wechat_official_account_assistant_reply.id is 'id主键';
comment on column wechat_official_account_assistant_reply.recover_type is '自动回复消息类型 0：自动回复 1:菜单导航';
comment on column wechat_official_account_assistant_reply.menu_navigation_id is '菜单导航表id,只有在菜单导航时才有';
comment on column wechat_official_account_assistant_reply.reply_type is '只有自动回复才有值,菜单导航没有, 0:关注回复 1:关键词回复 2:默认回复';
comment on column wechat_official_account_assistant_reply.status is '是否启用 0：启用 1:禁用';
comment on column wechat_official_account_assistant_reply.advertiser_account_group_id is 'pmpid';
comment on column wechat_official_account_assistant_reply.app_id is '公众号appid 公众号助手关联表只充当关联，实际上回复消息和公众号一一对应，之后实现共享功能只需要在公众号助手关联表加记录';
comment on column wechat_official_account_assistant_reply.name is '自动回复名称';
comment on column wechat_official_account_assistant_reply.follow_channel is '关注渠道';
comment on column wechat_official_account_assistant_reply.official_follow_source is '关注来源 0:来源于广告 1:非广告来源 2:全部来源';
comment on column wechat_official_account_assistant_reply.follow_source_platform is '关注来源为来源广告时对应选择的广告媒体';
comment on column wechat_official_account_assistant_reply.message_num is '消息条数';
comment on column wechat_official_account_assistant_reply.delayed_push is '是否开启延迟推送 0:不开启 1:开启';
comment on column wechat_official_account_assistant_reply.created_at is '创建时间';
comment on column wechat_official_account_assistant_reply.updated_at is '更新时间';

create index if not exists idx_official_account_assistant_reply_app_id on wechat_official_account_assistant_reply (app_id);
create index if not exists idx_official_account_assistant_reply_pmp_id on wechat_official_account_assistant_reply (advertiser_account_group_id);
create index if not exists idx_official_account_assistant_menu_navigation_id on wechat_official_account_assistant_reply (menu_navigation_id);

create table if not exists wechat_official_account_assistant_reply_message
(
    id                                             bigserial
    primary key,
    app_id                                       varchar,
    wechat_official_account_assistant_reply_id   bigint,
    message_type                                   integer default 0,
    message                                        varchar,
    material_file_id                               bigint,
    sort                                           integer,
    delay_time                                     integer default 0,
    created_at                                     timestamp default now() not null,
    updated_at                                     timestamp default now() not null
    );
comment on table wechat_official_account_assistant_reply_message is '微信公众号助手自动回复消息表';
comment on column wechat_official_account_assistant_reply_message.id is 'id主键';
comment on column wechat_official_account_assistant_reply_message.app_id is '微信公众号appid';
comment on column wechat_official_account_assistant_reply_message.wechat_official_account_assistant_reply_id is '微信公众号助手回复表ID-wechat_official_account_assistant_recover.id';
comment on column wechat_official_account_assistant_reply_message.message_type is '消息类型 0:txt 1:图片';
comment on column wechat_official_account_assistant_reply_message.message is '消息内容';
comment on column wechat_official_account_assistant_reply_message.material_file_id is '图片素材库ID';
comment on column wechat_official_account_assistant_reply_message.sort is '排序';
comment on column wechat_official_account_assistant_reply_message.delay_time is '延时推送间隔时间（单位：秒）';
comment on column wechat_official_account_assistant_reply_message.created_at is '创建时间';
comment on column wechat_official_account_assistant_reply_message.updated_at is '更新时间';

create index if not exists idx_official_account_assistant_recover_message_reply_id on wechat_official_account_assistant_reply_message (wechat_official_account_assistant_reply_id);
create index if not exists idx_official_account_assistant_recover_message_app_id on wechat_official_account_assistant_reply_message (app_id);




create table if not exists wechat_official_account_assistant_menu_navigation
(
    id                                             bigserial
    primary key,
    app_id                                         varchar,
    type                                           varchar,
    name                                           varchar,
    key                                            varchar,
    parent_id                                      bigint,
    sort                                           integer,
    next_level_exists                              integer default 0,
    created_at                                     timestamp default now() not null,
    updated_at                                     timestamp default now() not null
    );
comment on table wechat_official_account_assistant_menu_navigation is '微信公众号助手菜单表';
comment on column wechat_official_account_assistant_menu_navigation.id is 'id主键';
comment on column wechat_official_account_assistant_menu_navigation.app_id is '微信公众号助手app_id';
comment on column wechat_official_account_assistant_menu_navigation.type is '菜单事件类型 click 目前只做点击事件';
comment on column wechat_official_account_assistant_menu_navigation.name is '菜单名称';
comment on column wechat_official_account_assistant_menu_navigation.key is '点击事件后回调给微信后台的值 menukey:agentid:pmpid:菜单id:子菜单id';
comment on column wechat_official_account_assistant_menu_navigation.parent_id is '父级菜单id';
comment on column wechat_official_account_assistant_menu_navigation.next_level_exists is '是否存在下级菜单';
comment on column wechat_official_account_assistant_menu_navigation.created_at is '创建时间';
comment on column wechat_official_account_assistant_menu_navigation.updated_at is '更新时间';

create index if not exists idx_official_account_assistant_menu_navigation_app_id on wechat_official_account_assistant_menu_navigation (app_id);

alter table page_view_info add column if not exists advertise_source int4 default null;
comment on column page_view_info.advertise_source is '是否广告来源：0-否 1-是';

alter table page_view_info add column if not exists advertise_source_reason int4 default null;
comment on column page_view_info.advertise_source_reason is '广告来源判断原因';

alter table landing_page_channels add column if not exists launch_platform int4 default null;
comment on column landing_page_channels.launch_platform is '投放媒体';

alter table landing_page_preview add column if not exists non_ad_scene_settings boolean default false;
comment on column landing_page_preview.non_ad_scene_settings is '非广告场景设置';

alter table landing_page add column if not exists non_ad_scene_settings boolean default false;
comment on column landing_page.non_ad_scene_settings is '非广告场景设置';

alter table landing_page_preview add column if not exists purpose int4 default null;
comment on column landing_page_preview.purpose is '落地页用途';

alter table landing_page add column if not exists purpose int4 default null;
comment on column landing_page.purpose is '落地页用途';

alter table landing_page_preview add column if not exists scene_settings int4 default null;
comment on column landing_page_preview.scene_settings is '场景设置';

alter table landing_page add column if not exists scene_settings int4 default null;
comment on column landing_page.scene_settings is '场景设置';

alter table landing_page_preview add column if not exists no_ad_scene_action int4 default null;
comment on column landing_page_preview.no_ad_scene_action is '执行动作';

alter table landing_page add column if not exists no_ad_scene_action int4 default null;
comment on column landing_page.no_ad_scene_action is '执行动作';

alter table landing_page_preview add column if not exists no_ad_scene_wechat_customer_service_group_id int8 default null;
comment on column landing_page_preview.no_ad_scene_wechat_customer_service_group_id is '非广告场景设置-客服分组ID';

alter table landing_page add column if not exists no_ad_scene_wechat_customer_service_group_id int8 default null;
comment on column landing_page.no_ad_scene_wechat_customer_service_group_id is '非广告场景设置-客服分组ID';

-- 落地页渠道自定义列-投放媒体
insert into marketing_customer_field (type, no, sub_category, setting_category, checked, field, name, unit, formula_id, created_at, updated_at, user_id, platform_num, is_freeze, platform_ids, sub_category_no, setting_category_no, field_no, field_interpretation, able_sort, default_width, min_width, default_data) values
(10, 3, null, '基础属性', true, 'launchPlatform', '投放媒体', '', null, now(), now(), 0, 0, 0, null, null, 5, 3, '投放媒体', false, 280, 112, 0);
update marketing_customer_field set field_no = 4 where name = '上报配置' and type = 10;
update marketing_customer_field set field_no = 5 where name = '创建时间' and type = 10;
delete from marketing_customer_field where type = 10 and default_data = 1;

-- 公众号 - 素材表
create table if not exists wechat_official_account_material_file (
    id              bigserial primary key,
    app_id          varchar not null,
    temp_or_always  integer default 0 not null,
    file_type       varchar not null,
    media_id        varchar not null,
    image_url       varchar not null,
    expire_at       timestamp,
    created_at      timestamp default now() not null,
    updated_at      timestamp default now() not null
);
comment on table wechat_official_account_material_file is '公众号 - 素材表';
comment on column wechat_official_account_material_file.id is 'id主键';
comment on column wechat_official_account_material_file.app_id is '微信公众号app_id';
comment on column wechat_official_account_material_file.temp_or_always is '是否为[临时/永久]素材：默认临时素材';
comment on column wechat_official_account_material_file.file_type is '素材类型（媒体文件类型）：分别有图片（image）、语音（voice）、视频（video）和缩略图（thumb）';
comment on column wechat_official_account_material_file.media_id is '素材id（微信公众号上传临时素材返回的media_id）';
comment on column wechat_official_account_material_file.expire_at is '素材过期时间，永久素材此处为空';
comment on column wechat_official_account_material_file.created_at is '创建时间';
comment on column wechat_official_account_material_file.updated_at is '更新时间';

-- 微信公众号 - 粉丝 - 用户基本信息 - 表
create table if not exists landing_page_wechat_official_account_user (
    id                      bigserial primary key,
    app_id                  varchar not null,
    subscribe               integer,
    openid                  varchar not null,
    nickname                varchar,
    language                varchar,
    headimgurl              varchar,
    subscribe_time          bigint,
    unionid                 varchar,
    external_userid         varchar,
    remark                  varchar,
    groupid                 varchar,
    tagid_list              bigint[] default '{}',
    subscribe_scene         integer,
    qr_scene                integer,
    qr_scene_str            varchar,
    matching_pid            varchar,
    landing_page_id         bigint,
    channel_id              bigint,
    click_id                varchar,
    url                     varchar,
    referrer                varchar,
    platform                integer,
    official_follow_source  integer default 1,
    follow_source_platform  integer,
    created_at      timestamp default now() not null,
    updated_at      timestamp default now() not null
);
comment on table landing_page_wechat_official_account_user is '微信公众号 - 粉丝 - 用户基本信息';
comment on column landing_page_wechat_official_account_user.id is 'id主键';
comment on column landing_page_wechat_official_account_user.app_id is '微信公众号appid';
comment on column landing_page_wechat_official_account_user.subscribe is '用户是否订阅该公众号标识，值为0时，代表此用户没有关注该公众号，拉取不到其余信息';
comment on column landing_page_wechat_official_account_user.openid is '用户的标识，对当前公众号唯一';
comment on column landing_page_wechat_official_account_user.nickname is '用户的昵称(2021年12月27日之后不再输出)';
comment on column landing_page_wechat_official_account_user.language is '用户的语言，简体中文为zh_CN';
comment on column landing_page_wechat_official_account_user.headimgurl is '用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。(2021年12月27日之后不再输出)';
comment on column landing_page_wechat_official_account_user.subscribe_time is '用户关注时间，为时间戳。如果用户曾多次关注，则取最后关注时间';
comment on column landing_page_wechat_official_account_user.unionid is '只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段';
comment on column landing_page_wechat_official_account_user.external_userid is '';
comment on column landing_page_wechat_official_account_user.remark is '公众号运营者对粉丝的备注，公众号运营者可在微信公众平台用户管理界面对粉丝添加备注';
comment on column landing_page_wechat_official_account_user.groupid is '用户所在的分组ID（暂时兼容用户分组旧接口）';
comment on column landing_page_wechat_official_account_user.tagid_list is '用户被打上的标签ID列表';
comment on column landing_page_wechat_official_account_user.subscribe_scene is '返回用户关注的渠道来源';
comment on column landing_page_wechat_official_account_user.qr_scene is '二维码扫码场景（开发者自定义）';
comment on column landing_page_wechat_official_account_user.qr_scene_str is '二维码扫码场景描述（开发者自定义）';
comment on column landing_page_wechat_official_account_user.matching_pid is '关注成功后匹配对应的pid';
comment on column landing_page_wechat_official_account_user.landing_page_id is '落地页id';
comment on column landing_page_wechat_official_account_user.channel_id is '渠道id';
comment on column landing_page_wechat_official_account_user.click_id is 'clickid-用于判断是否为广告来源';
comment on column landing_page_wechat_official_account_user.url is '访问url';
comment on column landing_page_wechat_official_account_user.referrer is '访问来源--访问页面从哪个页面跳转过来的';
comment on column landing_page_wechat_official_account_user.platform is '媒体来源';
comment on column landing_page_wechat_official_account_user.official_follow_source is '关注来源-对应值：0-来源于广告 1-非广告来源 默认：非广告来源';
comment on column landing_page_wechat_official_account_user.follow_source_platform is '关注来源-来源广告-对应值';
comment on column landing_page_wechat_official_account_user.created_at is '创建时间';
comment on column landing_page_wechat_official_account_user.updated_at is '更新时间';

create index if not exists idx_lpwoau_app_id_openid on landing_page_wechat_official_account_user (app_id, openid);
