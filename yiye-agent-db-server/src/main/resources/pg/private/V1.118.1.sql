CREATE TABLE "marketing_data_search_keyword_solidification" (
                                                                                "id" bigserial primary key,
                                                                                "account_id" varchar(64) COLLATE "pg_catalog"."default",
                                                                                "ad_id" int8,
                                                                                "adgroup_id" int8,
                                                                                "creative_id" int8,
                                                                                "targeting_id" int8,
                                                                                "advertiser_account_id" int8,
                                                                                "campaign_id" int8,
                                                                                "platform_id" int8,
                                                                                "optimizer_id" int8,
                                                                                "view_num" int8,
                                                                                "convert_count" int8,
                                                                                "convert_cost" numeric(10,2),
                                                                                "cost" numeric(10,2),
                                                                                "click_num" int8,
                                                                                "deep_convert_num" int8,
                                                                                "deep_convert_cost" numeric(10,2),
                                                                                "play_num" int8,
                                                                                "valid_play_num" int8,
                                                                                "valid_play_cost" numeric(10,2),
                                                                                "average_play_time" numeric(10,2),
                                                                                "video_play25_num" int8,
                                                                                "video_play50_num" int8,
                                                                                "video_play75_num" int8,
                                                                                "video_play100_num" int8,
                                                                                "download_num" int8,
                                                                                "day_time" timestamp(6),
                                                                                "data_time" timestamp(6),
                                                                                "wifi_play_num" int8,
                                                                                "advertiser_account_status" int8,
                                                                                "valid_play_rate" numeric(10,2),
                                                                                "sum_play_time" int8,
                                                                                "view_user_num" int8,
                                                                                "compensation_amount" numeric(10,2),
                                                                                "click_user_num" int8,
                                                                                "form_appointment_num" int8,
                                                                                "form_appointment_person_count" int8,
                                                                                "place_order_num" int8,
                                                                                "order_amount" numeric(10,2),
                                                                                "payment_num" int8,
                                                                                "pay_amount" numeric(10,2),
                                                                                "first_payment_person_num" int8,
                                                                                "register_count" int8,
                                                                                "official_focus_num" int8,
                                                                                "share_num" int8,
                                                                                "comment_num" int8,
                                                                                "praise_num" int8,
                                                                                "button_count" int8,
                                                                                "week" int8,
                                                                                "hour" int8,
                                                                                "sale_clue_num" int8,
                                                                                "sale_clue_person_num" int8,
                                                                                "valid_clue_num" int8,
                                                                                "valid_clue_person_num" int8,
                                                                                "click_image_num" int8,
                                                                                "promotion_page_view_user_num" int8,
                                                                                "promotion_page_view_num" int8,
                                                                                "app_activation_count" int8,
                                                                                "app_activated_rate" numeric(10,2),
                                                                                "app_download_rate" numeric(10,2),
                                                                                "app_download_cost" numeric(10,2),
                                                                                "app_install_count" int8,
                                                                                "app_install_cost" numeric(10,2),
                                                                                "app_activated_cost" numeric(10,2),
                                                                                "app_retained_person_num" int8,
                                                                                "app_retention_cost" numeric(10,2),
                                                                                "app_register_count" int8,
                                                                                "app_register_cost" numeric(10,2) DEFAULT nextval('marketing_data_solidification_id_seq1'::regclass),
                                                                                "trial_count" int8,
                                                                                "valid_clue_count" int8 DEFAULT nextval('marketing_data_solidification_id_seq1'::regclass),
                                                                                "auditioned_class_count" int8,
                                                                                "payment_deposit_count" int8,
                                                                                "audition_count" int8,
                                                                                "appointment_count" int8,
                                                                                "pay_count" int8,
                                                                                "person_wechat_link_count" int8,
                                                                                "call_link_count" int8,
                                                                                "app_pay_num" int8,
                                                                                "app_pay_amount" numeric(10,2),
                                                                                "landing_page_pv" int8,
                                                                                "landing_page_uv" int8,
                                                                                "fill_count_num" int8,
                                                                                "order_num" int8,
                                                                                "order_finish_num" int8,
                                                                                "length_of_stay" numeric(10,2),
                                                                                "official_focus_count" int8,
                                                                                "activation_count" int8,
                                                                                "app_download_finish_count" int8,
                                                                                "created_at" timestamp(6),
                                                                                "updated_at" timestamp(6),
                                                                                "focus_count" int8,
                                                                                "follow_num" int8,
                                                                                "register_num" int4,
                                                                                "payment_amount" numeric(10),
                                                                                "convert_num" int8,
                                                                                "identify_qr_code_num" int8,
                                                                                "add_work_wechat_num" int8,
                                                                                "clue_fill_num" int8,
                                                                                "clue_connect_num" int8,
                                                                                "clue_effective_num" int8,
                                                                                "sign_class_num" int8
)
;

CREATE INDEX "index_marketing_data_search_keyword_solidification_data_time" ON "marketing_data_search_keyword_solidification" USING btree (
  "data_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

CREATE INDEX "search_keyword_solidification_day_time_advertiser_id_index" ON "marketing_data_search_keyword_solidification" USING btree (
  "day_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST,
  "advertiser_account_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."account_id" IS '账户Id';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."ad_id" IS '广告Id';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."adgroup_id" IS '广告组id';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."creative_id" IS '创意id';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."targeting_id" IS '定向包id';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."advertiser_account_id" IS '账户id';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."campaign_id" IS '计划id';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."platform_id" IS '平台Id';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."optimizer_id" IS '优化师id';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."view_num" IS '曝光量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."convert_count" IS '转化数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."convert_cost" IS '转化成本';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."cost" IS '花费';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."click_num" IS '点击量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."deep_convert_num" IS '深度转化量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."deep_convert_cost" IS '深度转化成本';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."play_num" IS '播放次数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."valid_play_num" IS '有效播放次数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."valid_play_cost" IS '有效播放成本';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."average_play_time" IS '平均播放时长';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."video_play25_num" IS '25%播放数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."video_play50_num" IS '50%播放数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."video_play75_num" IS '75%播放数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."video_play100_num" IS '100%播放数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."download_num" IS '下载次数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."day_time" IS '日期';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."data_time" IS '时间';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."wifi_play_num" IS 'wifi播放数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."advertiser_account_status" IS '账号状态';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."valid_play_rate" IS '视频有效播放率';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."sum_play_time" IS '视频总播放时长';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."view_user_num" IS '曝光人数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."compensation_amount" IS '赔付金额';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."click_user_num" IS '点击人数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."form_appointment_num" IS '表单预约量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."form_appointment_person_count" IS '表单预约人数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."place_order_num" IS '下单量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."order_amount" IS '订单金额';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."payment_num" IS '付费行为量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."pay_amount" IS '付费金额';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."first_payment_person_num" IS '首次付费行为人数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."register_count" IS '注册量（网页）';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."official_focus_num" IS '公众号关注量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."share_num" IS '分享数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."comment_num" IS '评论数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."praise_num" IS '点赞数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."button_count" IS '按钮数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."week" IS '周';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."hour" IS '小时';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."sale_clue_num" IS '销售线索量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."sale_clue_person_num" IS '销售线索人数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."valid_clue_num" IS '有效线索量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."valid_clue_person_num" IS '有效线索人数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."click_image_num" IS '图片点击次数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."promotion_page_view_user_num" IS '推广页播放人数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."promotion_page_view_num" IS '推广页曝光次数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_activation_count" IS 'APP激活总量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_activated_rate" IS 'APP激活率';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_download_rate" IS 'APP下载率';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_download_cost" IS 'APP下载成本';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_install_count" IS 'APP安装量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_install_cost" IS 'APP安装成本';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_activated_cost" IS 'APP激活成本';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_retained_person_num" IS 'APP次日留存量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_retention_cost" IS 'APP次日留存成本';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_register_count" IS 'APP注册量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_register_cost" IS 'APP注册成本';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."trial_count" IS '试用数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."valid_clue_count" IS '有效线索数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."auditioned_class_count" IS '试听完课数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."payment_deposit_count" IS '支付定金数量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."audition_count" IS '试听数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."appointment_count" IS '预约数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."pay_count" IS '支付数量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."person_wechat_link_count" IS '个微建联数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."call_link_count" IS '电话建联数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_pay_num" IS 'APP付费行为量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_pay_amount" IS 'APP付费金额';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."landing_page_pv" IS '落地页PV';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."landing_page_uv" IS '落地页UV';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."fill_count_num" IS '填单提交数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."order_num" IS '订单提交数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."order_finish_num" IS '订单完成数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."length_of_stay" IS '停留时长';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."official_focus_count" IS '公众号关注数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."activation_count" IS '激活数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."app_download_finish_count" IS 'APP下载完成量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."focus_count" IS '关注量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."follow_num" IS '关注数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."register_num" IS '注册量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."payment_amount" IS '付费金额';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."convert_num" IS '目标转化量';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."identify_qr_code_num" IS '长按二维码识别数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."add_work_wechat_num" IS '加企业微信数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."clue_fill_num" IS '线索填单数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."clue_connect_num" IS '线索接通数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."clue_effective_num" IS '线索有效数';

COMMENT ON COLUMN "marketing_data_search_keyword_solidification"."sign_class_num" IS '报班数';

COMMENT ON TABLE "marketing_data_search_keyword_solidification" IS '数据固化表';
