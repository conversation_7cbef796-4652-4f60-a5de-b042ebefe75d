-- 【填单记录】表submit_data添加如下字段：
-- 企业微信回调数据：昵称 → 用户昵称
ALTER table "submit_data" ADD COLUMN wechat_applet_name varchar DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_name" IS '企业微信回调数据（externalContact.name）：昵称 → 用户昵称';
-- 企业微信回调数据：用户企业微信userid → 用户userid
ALTER table "submit_data" ADD COLUMN wechat_applet_external_userid varchar DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_external_userid" IS '企业微信回调数据（allFieldsMap.ExternalUserID）：用户企业微信userid → 用户（微信客户）userid';
-- 企业微信回调数据：微信unionid（一叶）→ 微信unionid（一叶）
ALTER table "submit_data" ADD COLUMN wechat_applet_unionid varchar DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_unionid" IS '企业微信回调数据（externalContact.unionId）：微信unionid（一叶）→ 微信unionid（一叶）';
-- 企业微信回调数据：客服userid/客户群ID → 客服userid/客户群ID
ALTER table "submit_data" ADD COLUMN wechat_applet_userid varchar DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_userid" IS '企业微信回调数据（allFieldsMap.UserID）：客服userid/客户群ID → 客服userid/客户群ID';
-- 企业微信回调数据：客服/客户群名称 → 客服名称/客服群名称
ALTER table "submit_data" ADD COLUMN wechat_applet_group_chat_name varchar DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_group_chat_name" IS '企业微信回调数据（followedUsers.未知该字段）：客服/客户群名称 → 客服名称/客服群名称';
-- 企业微信回调数据：客户标签信息 → 客户标签信（TODO 该字段需要建关联表）
-- ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_applet_tags varchar[] DEFAULT null;
-- COMMENT ON COLUMN "enterprise_wechat_add_contact_record"."wechat_applet_tags" IS '企业微信回调数据（followedUsers.tags）：客户标签信息 → 客户标签信';
-- 企业微信回调数据：添加企业微信方式 → 成功添加企业微信方式/来源
ALTER table "submit_data" ADD COLUMN wechat_applet_add_way bigint DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_add_way" IS '企业微信回调数据（followedUsers.addWay）：添加企业微信方式 → 成功添加企业微信方式/来源';
-- 企业微信回调数据：所属落地页（成功添加企业微信） → 小程序所属的落地页id
ALTER table "submit_data" ADD COLUMN wechat_applet_landing_page_id bigint DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_landing_page_id" IS '本系统数据（landing_page.id）：所属落地页（成功添加企业微信） → 小程序所属的落地页';
-- 企业微信回调数据：所属渠道（成功添加企业微信） → 小程序所属的渠道id
ALTER table "submit_data" ADD COLUMN wechat_applet_landing_page_channel_id bigint DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_landing_page_channel_id" IS '本系统数据（landing_page_channels.id）：所属渠道（成功添加企业微信） → 小程序所属的渠道名';
-- 企业微信回调数据：访问URL（成功添加企业微信） → 小程序访问的url
ALTER table "submit_data" ADD COLUMN wechat_applet_landing_page_view_url varchar DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_landing_page_view_url" IS '本系统数据（page_view_info.url）：访问URL（成功添加企业微信） → 小程序访问的url';
-- 本系统数据（landing_page_wechat_customer_service.id）：微信客服id；归因数据：（根据【enterprise_wechat_add_contact_record.wechat_applet_userid】匹配【landing_page_wechat_customer_service.wechat_user_id】得到【landing_page_wechat_customer_service.id】）
ALTER table "submit_data" ADD COLUMN wechat_customer_service_id varchar DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_customer_service_id" IS '本系统数据（landing_page_wechat_customer_service.id）：微信客服id；归因数据：（根据【enterprise_wechat_add_contact_record.wechat_applet_userid】匹配【landing_page_wechat_customer_service.wechat_user_id】得到【landing_page_wechat_customer_service.id】）';

-- 【客资记录】表customer添加如下字段：
-- 企业微信回调数据：昵称 → 用户昵称
ALTER table "customer" ADD COLUMN wechat_applet_name varchar DEFAULT null;
COMMENT ON COLUMN "customer"."wechat_applet_name" IS '企业微信回调数据（externalContact.name）：昵称 → 用户昵称';
-- 企业微信回调数据：用户企业微信userid → 用户userid
ALTER table "customer" ADD COLUMN wechat_applet_external_userid varchar DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_external_userid" IS '企业微信回调数据（allFieldsMap.ExternalUserID）：用户企业微信userid → 用户（微信客户）userid';
-- 企业微信回调数据：微信unionid（一叶）→ 微信unionid（一叶）
ALTER table "customer" ADD COLUMN wechat_applet_unionid varchar DEFAULT null;
COMMENT ON COLUMN "customer"."wechat_applet_unionid" IS '企业微信回调数据（externalContact.unionId）：微信unionid（一叶）→ 微信unionid（一叶）';
-- 企业微信回调数据：客服userid/客户群ID → 客服userid/客户群ID
ALTER table "customer" ADD COLUMN wechat_applet_userid varchar DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_userid" IS '企业微信回调数据（allFieldsMap.UserID）：客服userid/客户群ID → 客服userid/客户群ID';
-- 企业微信回调数据：客服/客户群名称 → 客服名称/客服群名称
ALTER table "customer" ADD COLUMN wechat_applet_group_chat_name varchar DEFAULT null;
COMMENT ON COLUMN "customer"."wechat_applet_group_chat_name" IS '企业微信回调数据（followedUsers.未知该字段）：客服/客户群名称 → 客服名称/客服群名称';
-- 企业微信回调数据：客户标签信息 → 客户标签信（TODO 该字段需要建关联表）
-- ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_applet_tags varchar[] DEFAULT null;
-- COMMENT ON COLUMN "enterprise_wechat_add_contact_record"."wechat_applet_tags" IS '企业微信回调数据（followedUsers.tags）：客户标签信息 → 客户标签信';
-- 企业微信回调数据：添加企业微信方式 → 成功添加企业微信方式/来源
ALTER table "customer" ADD COLUMN wechat_applet_add_way bigint DEFAULT null;
COMMENT ON COLUMN "customer"."wechat_applet_add_way" IS '企业微信回调数据（followedUsers.addWay）：添加企业微信方式 → 成功添加企业微信方式/来源';
-- 企业微信回调数据：所属落地页（成功添加企业微信） → 小程序所属的落地页id
ALTER table "customer" ADD COLUMN wechat_applet_landing_page_id bigint DEFAULT null;
COMMENT ON COLUMN "customer"."wechat_applet_landing_page_id" IS '本系统数据（landing_page.id）：所属落地页（成功添加企业微信） → 小程序所属的落地页';
-- 企业微信回调数据：所属渠道（成功添加企业微信） → 小程序所属的渠道id
ALTER table "customer" ADD COLUMN wechat_applet_landing_page_channel_id bigint DEFAULT null;
COMMENT ON COLUMN "customer"."wechat_applet_landing_page_channel_id" IS '本系统数据（landing_page_channels.id）：所属渠道（成功添加企业微信） → 小程序所属的渠道名';
-- 企业微信回调数据：访问URL（成功添加企业微信） → 小程序访问的url
ALTER table "customer" ADD COLUMN wechat_applet_landing_page_view_url varchar DEFAULT null;
COMMENT ON COLUMN "customer"."wechat_applet_landing_page_view_url" IS '本系统数据（page_view_info.url）：访问URL（成功添加企业微信） → 小程序访问的url';
-- 本系统数据（landing_page_wechat_customer_service.id）：微信客服id；归因数据：（根据【enterprise_wechat_add_contact_record.wechat_applet_userid】匹配【landing_page_wechat_customer_service.wechat_user_id】得到【landing_page_wechat_customer_service.id】）
ALTER table "customer" ADD COLUMN wechat_customer_service_id varchar DEFAULT null;
COMMENT ON COLUMN "customer"."wechat_customer_service_id" IS '本系统数据（landing_page_wechat_customer_service.id）：微信客服id；归因数据：（根据【enterprise_wechat_add_contact_record.wechat_applet_userid】匹配【landing_page_wechat_customer_service.wechat_user_id】得到【landing_page_wechat_customer_service.id】）';

-- 【企业微信添加外部联系人记录表】表enterprise_wechat_add_contact_record添加如下字段：
-- 企业微信回调数据：昵称 → 用户昵称
ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wx_cp_user_external_contact_info varchar DEFAULT null;
COMMENT ON COLUMN "enterprise_wechat_add_contact_record"."wx_cp_user_external_contact_info" IS '企业微信回调数据：外部客户详情信息';
ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_applet_name varchar DEFAULT null;
COMMENT ON COLUMN "enterprise_wechat_add_contact_record"."wechat_applet_name" IS '企业微信回调数据（externalContact.name）：昵称 → 用户昵称';
-- 企业微信回调数据：用户企业微信userid → 用户userid
ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_applet_external_userid varchar DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_external_userid" IS '企业微信回调数据（allFieldsMap.ExternalUserID）：用户企业微信userid → 用户（微信客户）userid';
-- 企业微信回调数据：微信unionid（一叶）→ 微信unionid（一叶）
ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_applet_unionid varchar DEFAULT null;
COMMENT ON COLUMN "enterprise_wechat_add_contact_record"."wechat_applet_unionid" IS '企业微信回调数据（externalContact.unionId）：微信unionid（一叶）→ 微信unionid（一叶）';
-- 企业微信回调数据：客服userid/客户群ID → 客服userid/客户群ID
ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_applet_userid varchar DEFAULT null;
COMMENT ON COLUMN "submit_data"."wechat_applet_userid" IS '企业微信回调数据（allFieldsMap.UserID）：客服userid/客户群ID → 客服userid/客户群ID';
-- 企业微信回调数据：客服/客户群名称 → 客服名称/客服群名称
ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_applet_group_chat_name varchar DEFAULT null;
COMMENT ON COLUMN "enterprise_wechat_add_contact_record"."wechat_applet_group_chat_name" IS '企业微信回调数据（followedUsers.未知该字段）：客服/客户群名称 → 客服名称/客服群名称';
-- 企业微信回调数据：客户标签信息 → 客户标签信（TODO 该字段需要建关联表）
-- ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_applet_tags varchar[] DEFAULT null;
-- COMMENT ON COLUMN "enterprise_wechat_add_contact_record"."wechat_applet_tags" IS '企业微信回调数据（followedUsers.tags）：客户标签信息 → 客户标签信';
-- 企业微信回调数据：添加企业微信方式 → 成功添加企业微信方式/来源
ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_applet_add_way bigint DEFAULT null;
COMMENT ON COLUMN "enterprise_wechat_add_contact_record"."wechat_applet_add_way" IS '企业微信回调数据（followedUsers.addWay）：添加企业微信方式 → 成功添加企业微信方式/来源';
-- 企业微信回调数据：所属落地页（成功添加企业微信） → 小程序所属的落地页id
ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_applet_landing_page_id bigint DEFAULT null;
COMMENT ON COLUMN "enterprise_wechat_add_contact_record"."wechat_applet_landing_page_id" IS '本系统数据（landing_page.id）：所属落地页（成功添加企业微信） → 小程序所属的落地页';
-- 企业微信回调数据：所属渠道（成功添加企业微信） → 小程序所属的渠道id
ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_applet_landing_page_channel_id bigint DEFAULT null;
COMMENT ON COLUMN "enterprise_wechat_add_contact_record"."wechat_applet_landing_page_channel_id" IS '本系统数据（landing_page_channels.id）：所属渠道（成功添加企业微信） → 小程序所属的渠道名';
-- 企业微信回调数据：访问URL（成功添加企业微信） → 小程序访问的url
ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_applet_landing_page_view_url varchar DEFAULT null;
COMMENT ON COLUMN "enterprise_wechat_add_contact_record"."wechat_applet_landing_page_view_url" IS '本系统数据（page_view_info.url）：访问URL（成功添加企业微信） → 小程序访问的url';
-- 本系统数据（landing_page_wechat_customer_service.id）：微信客服id；归因数据：（根据【enterprise_wechat_add_contact_record.wechat_applet_userid】匹配【landing_page_wechat_customer_service.wechat_user_id】得到【landing_page_wechat_customer_service.id】）
ALTER table "enterprise_wechat_add_contact_record" ADD COLUMN wechat_customer_service_id varchar DEFAULT null;
COMMENT ON COLUMN "enterprise_wechat_add_contact_record"."wechat_customer_service_id" IS '本系统数据（landing_page_wechat_customer_service.id）：微信客服id；归因数据：（根据【enterprise_wechat_add_contact_record.wechat_applet_userid】匹配【landing_page_wechat_customer_service.wechat_user_id】得到【landing_page_wechat_customer_service.id】）';

-- 上级页面填单id（关联表：submit_data.id）
alter table page_view_info add parent_submit_data_id int default 0;
comment on column page_view_info.parent_submit_data_id is '上级页面填单id（关联表：submit_data.id）';