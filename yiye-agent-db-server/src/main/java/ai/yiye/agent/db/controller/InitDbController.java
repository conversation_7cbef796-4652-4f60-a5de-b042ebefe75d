package ai.yiye.agent.db.controller;

import ai.yiye.agent.autoconfigure.mybatis.multidatasource.service.AgentConfService;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.db.remote.AccountGroupRemote;
import ai.yiye.agent.db.remote.AppletConfigRemote;
import ai.yiye.agent.db.remote.AppletPmpRelRemote;
import ai.yiye.agent.db.service.*;
import ai.yiye.agent.domain.*;
import ai.yiye.agent.domain.db.SubTableDesc;
import ai.yiye.agent.domain.db.TableIndexRequestBody;
import ai.yiye.agent.domain.dto.QiyetuiAppRelDto;
import ai.yiye.agent.domain.enumerations.AppletTemplateType;
import ai.yiye.agent.domain.enumerations.PreferredDomainEnum;
import ai.yiye.agent.domain.enumerations.WechatCustomerAcquisitionLinkStatus;
import ai.yiye.agent.domain.landingpage.*;
import ai.yiye.agent.domain.vo.ManualDbVo;
import ai.yiye.agent.marketing.exception.MarketingApiException;
import ai.yiye.agent.weixin.client.OpenWeixinApiClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/9 15:35
 */
@Slf4j
@RestController
@RequestMapping("/db")
public class InitDbController {

    @Autowired
    private InitDbService initDbService;

    @Autowired
    private CommonService commonService;
    @Autowired
    private StringRedisTemplate defaultStringRedisTemplate;
    @Autowired
    private RedisTemplate<String, Object> objectRedisTemplate;
    @Autowired
    private AgentConfService agentConfService;
    @Autowired
    private AppletConfigRemote appletConfigRemote;
    @Autowired
    private AccountGroupRemote accountGroupRemote;
    @Autowired
    private AppletPmpRelRemote appletPmpRelRemote;
    @Autowired
    private LandingPageDomainBindingService landingPageDomainBindingService;
    @Autowired
    private LandingPageDomainPmpRelService landingPageDomainPmpRelService;
    @Autowired
    private PageViewInfoService pageViewInfoService;

    @Autowired
    private OpenWeixinApiClient openWeixinApiClient;
    @Autowired
    private EnterpriseWechatService enterpriseWechatService;
    @Autowired
    private EnterpriseWechatSwitchService enterpriseWechatSwitchService;

    @Autowired
    private LandingPageAppletTemplateTypeService landingPageAppletTemplateTypeService;

    @Autowired
    private LandingPageWechatTemplateService landingPageWechatTemplateService;
    @Autowired
    private LandingPageWechatAppletConfigService landingPageWechatAppletConfigService;

    @Autowired
    private BossAdvertiserAccountGroupService bossAdvertiserAccountGroupService;
    @Autowired
    private AdvertiserAccountGroupService advertiserAccountGroupService;
    @Autowired
    private QiyeTuiAppService qiyeTuiAppService;
    @Autowired
    private EnterpriseWechatOfficialHistoryArticlePageService enterpriseWechatOfficialHistoryArticlePageService;
    @Autowired
    private LandingPageChannelService landingPageChannelService;
    @Autowired
    private EnterpriseWechatRobotCustomerService enterpriseWechatRobotCustomerService;
    @Autowired
    private LandingPageWechatGroupChatAgentRelService landingPageWechatGroupChatAgentRelService;
    @Autowired
    private LandingPageWechatGroupChatService landingPageWechatGroupChatService;
    @Autowired
    private EnterpriseWechatsPmpRelService enterpriseWechatsPmpRelService;
    @Resource
    private LandingPageWechatCustomerServiceService landingPageWechatCustomerServiceService;
    @Resource
    private WorkWechatCustomerAcquisitionLinkService workWechatCustomerAcquisitionLinkService;
    @Value("${yiye.agent.landing-page.acquisitionLinkJob.pageSize:1000}")
    private Long pageSize;

    @Autowired
    private EnterpriseWechatTagConfigService enterpriseWechatTagConfigService;

    @Autowired
    private EnterpriseWechatSessionArchiveService enterpriseWechatSessionArchiveService;

    @Autowired
    private EnterpriseWechatCorpIdToOpenCorpIdService enterpriseWechatCorpIdToOpenCorpIdService;

    @Autowired
    private UploadConfigurationService uploadConfigurationService;

    @Autowired
    private AdvertiserAccountService advertiserAccountService;

    private final static String INIT_APPLET_REL_KEY = "init_applet_rel_key:";
    private final static String INIT_WECHAT_REL_KEY = "init_wechat_rel_key:";

    @PostMapping("/init-db")
    public void initDb(@RequestBody AgentConf agentConf) {
        initDbService.initDb(agentConf);
    }

    @DeleteMapping("/remove/{agentId}")
    public String deleteByAgentId(@PathVariable("agentId") String agentId) {
        return commonService.deleteByAgentId(agentId);
    }

    /**
     * 初始化小程序和pmp之间的关系，1.126.0版本发布后调用一次即可
     */
    @PostMapping("/init/applet-rel")
    public String initAppletRel() {
        List<AgentConf> agentConfs = agentConfService.list(new LambdaQueryWrapper<AgentConf>().select(AgentConf::getAgentId));
        if (CollectionUtils.isEmpty(agentConfs)) {
            throw new MarketingApiException("当前无可用数据源");
        }
        for (int i = 0; i < agentConfs.size(); i++) {
            AgentConf agentConf = agentConfs.get(i);
            String agentId = agentConf.getAgentId();
            TenantContextHolder.set(agentId);
            String s = defaultStringRedisTemplate.boundValueOps(INIT_APPLET_REL_KEY + agentId).get();
            if (!StringUtils.isEmpty(s) && "1".equals(s)) {
                continue;
            }
            List<LandingPageWechatAppletPmpRel> list = new ArrayList<>();
            try {

                //查询当前客户的小程序
                List<LandingPageWechatAppletConfig> appletConfigs = appletConfigRemote.getAppletConfig();
                //查询当前客户的所有pmp
                List<AdvertiserAccountGroup> advertiserAccountGroups = accountGroupRemote.selectAll();
                //将当前客户小程序和pmp关系绑定
                for (LandingPageWechatAppletConfig config : appletConfigs) {
                    Long appletId = config.getId();
                    for (AdvertiserAccountGroup advertiserAccountGroup : advertiserAccountGroups) {
                        Long accountGroupId = advertiserAccountGroup.getId();
                        LandingPageWechatAppletPmpRel landingPageWechatAppletPmpRel = new LandingPageWechatAppletPmpRel();
                        landingPageWechatAppletPmpRel.setLandingPageWechatAppletId(appletId).setAdvertiserAccountGroupId(accountGroupId).setCreateAt(LocalDateTime.now()).setAgentId(agentId);
                        list.add(landingPageWechatAppletPmpRel);
                    }
                }
                TenantContextHolder.set(null);
                Boolean saveBatchs = appletPmpRelRemote.appletRelSaveBatchs(list);
                defaultStringRedisTemplate.boundValueOps(INIT_APPLET_REL_KEY + agentId).set(saveBatchs ? "1" : "0", 30, TimeUnit.MINUTES);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "success";
    }


    /**
     * 143 把自定义域名从私有库迁移到公共库
     */
    @GetMapping("/init-domain")
    public void initLandingPageDomain() {
        initDbService.initLandingPageDomain();
    }

    /**
     * 1.146.0 将落地页于域名的配置信息进行旧数据设置，调用一次
     *
     * @param
     */
    @GetMapping("/init/domain-landing-page-pmp")
    public String initDomainLandingPagePmp() {
        List<AgentConf> agentConfs = agentConfService.list(new LambdaQueryWrapper<AgentConf>().select(AgentConf::getAgentId));

        List<LandingPageDomainBinding> list = landingPageDomainBindingService.list();
        //一个客户可能有多个域名。，根据不同域名分组
        Map<String, List<LandingPageDomainBinding>> collect = list.stream().collect(Collectors.groupingBy(LandingPageDomainBinding::getAgentId));
        //对map进行遍历。
        Iterator<Map.Entry<String, List<LandingPageDomainBinding>>> entries = collect.entrySet().iterator();
        while (entries.hasNext()) {
            //根据agentId批量入库
            List<LandingPageDomainPmpRel> batchInsertList = new ArrayList<>();


            Map.Entry<String, List<LandingPageDomainBinding>> next = entries.next();
            String agentId = next.getKey();
            List<LandingPageDomainBinding> value = next.getValue();
            LandingPageDomainBinding landingPageDomainBinding = value.stream().min(Comparator.comparingInt(LandingPageDomainBinding::getOrderNum)).get();
            //找到了update中最大的，那么就是整个项目的首选域名
            LandingPageDomainPmpRel landingPageDomainPmpRel = new LandingPageDomainPmpRel();
            landingPageDomainPmpRel.setLandingPageDomainId(landingPageDomainBinding.getId());
            landingPageDomainPmpRel.setAgentId(landingPageDomainBinding.getAgentId());
            landingPageDomainPmpRel.setCreatedAt(Instant.now());
            TenantContextHolder.set(landingPageDomainBinding.getAgentId());
            List<AdvertiserAccountGroup> advertiserAccountGroups = accountGroupRemote.selectAll();
            TenantContextHolder.clearContext();
            //初始化数据的时候，
            for (int i = 0; i < advertiserAccountGroups.size(); i++) {
                LandingPageDomainPmpRel landingPageDomainPmpRel1 = new LandingPageDomainPmpRel();
                BeanUtils.copyProperties(landingPageDomainPmpRel, landingPageDomainPmpRel1);
                landingPageDomainPmpRel1.setPreferredDomain(PreferredDomainEnum.PREFERRED);
                landingPageDomainPmpRel1.setAdvertiserAccountGroupId(advertiserAccountGroups.get(i).getId());
                batchInsertList.add(landingPageDomainPmpRel1);
            }
            //首选域名设置完了之后，进行其他的设置
            value.remove(landingPageDomainBinding);
            for (int i = 0; i < value.size(); i++) {
                landingPageDomainPmpRel = new LandingPageDomainPmpRel();
                landingPageDomainPmpRel.setLandingPageDomainId(value.get(i).getId());
                landingPageDomainPmpRel.setAgentId(value.get(i).getAgentId());
                landingPageDomainPmpRel.setCreatedAt(Instant.now());
                for (int i1 = 0; i1 < advertiserAccountGroups.size(); i1++) {
                    LandingPageDomainPmpRel landingPageDomainPmpRel1 = new LandingPageDomainPmpRel();
                    BeanUtils.copyProperties(landingPageDomainPmpRel, landingPageDomainPmpRel1);
                    landingPageDomainPmpRel1.setAdvertiserAccountGroupId(advertiserAccountGroups.get(i1).getId());
                    landingPageDomainPmpRel1.setPreferredDomain(PreferredDomainEnum.NOT_PREFERRED);
                    batchInsertList.add(landingPageDomainPmpRel1);
                }
            }
            //以agentId为维度进行批量插入
            landingPageDomainPmpRelService.saveBatch(batchInsertList);
        }
        return "success";
    }


    @PostMapping("/manualDb")
    public void manaulDb(@RequestBody ManualDbVo manualDbVo) {
        commonService.manaulDb(manualDbVo);
    }

    /**
     * 根据多个账户agentId执行临时脚本
     */
    @PostMapping("/manualDb-by-agentIds")
    public void manaulDbByAgentIds(@RequestBody ManualDbVo manualDbVo) {
        if (CollectionUtils.isEmpty(manualDbVo.getAgentIds())) {
            log.info("根据多个账户agentId执行临时脚本，agentIds为空操作终止 manualDbVo={}", JSONObject.toJSONString(manualDbVo));
            return;
        }
        manualDbVo.getAgentIds().forEach(agentId -> {
            try {
                commonService.manaulDb(new ManualDbVo()
                    .setDataBase(manualDbVo.getDataBase())
                    .setFileName(manualDbVo.getFileName())
                    .setAgentId(agentId)
                );
            } catch (Exception e) {
                log.error("根据多个账户agentId执行临时脚本异常 agentId={}；manualDbVo={}", agentId, JSONObject.toJSONString(manualDbVo), e);
            }
        });
        log.info("根据多个账户agentId执行临时脚本-结束 manualDbVo={}", JSONObject.toJSONString(manualDbVo));
    }

    @PostMapping("/manualBackDb")
    public void manaulBackDb(@RequestBody ManualDbVo manualDbVo) {
        commonService.manaulDb(manualDbVo.getFileName(), manualDbVo.getDataBase(), manualDbVo.getAfresh());
    }

    @GetMapping("/clean-redis")
    public void cleanRedis() {
        initDbService.cleanRedis();
    }

    @PostMapping("synchronization/advertiser-account/auth-user-id")
    public String authUser() {
        List<AgentConf> agentConfs = agentConfService.list(new LambdaQueryWrapper<AgentConf>().select(AgentConf::getAgentId));
        if (CollectionUtils.isEmpty(agentConfs)) {
            throw new MarketingApiException("当前无可用数据源");
        }
        for (int i = 0; i < agentConfs.size(); i++) {
            AgentConf agentConf = agentConfs.get(i);
            String agentId = agentConf.getAgentId();
            TenantContextHolder.set(agentId);
            //获取所有的投放账户
            commonService.updateAuthUserId();
            log.info("synchronization auth cuccess {}", agentId);
        }
        log.info("synchronization auth cuccess...");
        return "success";
    }


    @PostMapping("page-view-info/alter/add/index")
    public String pageViewInfoAlterAddIndex(@Valid @RequestBody TableIndexRequestBody tableIndexRequestBody) {
        //获取子表名称
        List<AgentConf> agentConfs = agentConfService.list(new LambdaQueryWrapper<AgentConf>().select(AgentConf::getAgentId));
        for (int i = 0; i < agentConfs.size(); i++) {
            AgentConf agentConf = agentConfs.get(i);
            try {
                log.info("开始初始化 {} 的 {}  子表索引", agentConf.getAgentId(), tableIndexRequestBody.getTableName());
                List<SubTableDesc> subTableDescs = pageViewInfoService.selectSubTables(agentConf.getAgentId(), tableIndexRequestBody.getTableName());
                String collectSql = subTableDescs.stream().map(subTableDesc -> {
                    String sql = "create index concurrently if not exists  idx_" + subTableDesc.getTableName() + "_" + tableIndexRequestBody.getColumnName() + " on " + subTableDesc.getTableName() + " (" + tableIndexRequestBody.getColumnName() + ");";
                    return sql;
                }).collect(Collectors.joining("\n"));
                log.info("sql {}", collectSql);
                TenantContextHolder.set(agentConf.getAgentId());
                pageViewInfoService.createIndex(collectSql);
            } catch (Exception e) {
                log.error("初始化索引异常 {} ", agentConf.getAgentId(), e);
            } finally {
                TenantContextHolder.clearContext();
            }
        }
        log.info("初始化结束");
        //向子表对应字段添加索引
        return "success";
    }

    @PostMapping("init-enterprise-switch")
    public String initEnterpriseSwitch() {
        try {
            List<AgentConf> agentConfs = agentConfService.list(new LambdaQueryWrapper<AgentConf>());
            Map<String, AgentConf> agentConfMap = agentConfs.stream().collect(Collectors.toMap(AgentConf::getAgentId, Function.identity(), (key1, key2) -> key2));

            List<EnterpriseWechat> enterpriseWechats = enterpriseWechatService.list(new LambdaQueryWrapper<EnterpriseWechat>());
//        Map<String, List<EnterpriseWechat>> collect = enterpriseWechats.stream().collect(Collectors.groupingBy(EnterpriseWechat::getAgentId));
            for (int i = 0; i < enterpriseWechats.size(); i++) {
                EnterpriseWechat enterpriseWechat = enterpriseWechats.get(i);
                EnterpriseWechatSwitch enterpriseWechatSwitch = new EnterpriseWechatSwitch();
                enterpriseWechatSwitch.setCorpid(enterpriseWechat.getCorpid());
                enterpriseWechatSwitch.setStatus(agentConfMap.get(enterpriseWechat.getAgentId()).getEnterpriseWechatSelfSwitch());
                this.enterpriseWechatSwitchService.saveOrUpdateByCorpId(enterpriseWechatSwitch);
            }
            return "success";
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "false";
        }

    }

    @GetMapping("/init-template-type")
    private String initTemplateType() {
        TenantContextHolder.clearContext();
        List<LandingPageAppletTemplateType> list1 = landingPageAppletTemplateTypeService.list(new LambdaQueryWrapper<LandingPageAppletTemplateType>());
        Map<AppletTemplateType, Long> typeIdMap = list1.stream().collect(Collectors.toMap(LandingPageAppletTemplateType::getTemplateType, LandingPageAppletTemplateType::getId, (key1, key2) -> key2));
//        //首先将所有的template中DESC中没有TOOL BUSSINESS 三个模板的设定为业务模板
        List<LandingPageWechatAppletTemplate> list = landingPageWechatTemplateService.list(new LambdaQueryWrapper<LandingPageWechatAppletTemplate>());
        List<LandingPageWechatAppletTemplate> objects = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            LandingPageWechatAppletTemplate landingPageWechatAppletTemplate = list.get(i);
            String userDesc = landingPageWechatAppletTemplate.getUserDesc();
            AppletTemplateType type = null;

            for (AppletTemplateType key : typeIdMap.keySet()) {
                if (userDesc.contains(key.toString())) {
                    type = key;
                    break;
                }
            }
            if (Objects.isNull(type)) {
                landingPageWechatAppletTemplate.setTemplateTypeId(typeIdMap.get(AppletTemplateType.BUSSINESS));
            } else {
                landingPageWechatAppletTemplate.setTemplateTypeId(typeIdMap.get(type));
            }
            landingPageWechatTemplateService.saveOrUpdate(landingPageWechatAppletTemplate);
            objects.add(landingPageWechatAppletTemplate);
        }
        //上述是进行模板的更新，以下是小程序旧数据的处理
        Map<String, LandingPageWechatAppletTemplate> collect = objects.stream().collect(Collectors.toMap(LandingPageWechatAppletTemplate::getUserDesc, Function.identity(), (key1, key2) -> key2));

        List<LandingPageWechatAppletConfig> list2 = landingPageWechatAppletConfigService.list(new LambdaQueryWrapper<LandingPageWechatAppletConfig>().in(LandingPageWechatAppletConfig::getWechatAppletStatus, Arrays.asList(new Integer[]{1, 2})));
        for (int i = 0; i < list2.size(); i++) {

            LandingPageWechatAppletConfig wechatAppletConfig = list2.get(i);
            try {
                String releaseDesc = null;
                String releaseVersionStr = null;
                JSONObject jsonObject = openWeixinApiClient.versionInfo(wechatAppletConfig.getAccessToken(), new HashMap<>());

                //现在的版本号
                JSONObject releaseInfo = jsonObject.getJSONObject("release_info");
                if (Objects.nonNull(releaseInfo)) {
                    releaseVersionStr = releaseInfo.getString("release_version");
                    releaseDesc = releaseInfo.getString("release_desc");
                }
                wechatAppletConfig.setVersion(releaseVersionStr);
                //releaseInfo与上文的desc应该保持一致
                LandingPageWechatAppletTemplate landingPageWechatAppletTemplate = collect.get(releaseDesc);
                if (!Objects.isNull(landingPageWechatAppletTemplate)) {
                    wechatAppletConfig.setTemplateTypeId(landingPageWechatAppletTemplate.getTemplateTypeId());
                    wechatAppletConfig.setTemplateVersionId(landingPageWechatAppletTemplate.getId());
                    landingPageWechatAppletConfigService.updateTemplateTypeAndVersionId(wechatAppletConfig);
                } else {
                    landingPageWechatAppletConfigService.updateVersion(wechatAppletConfig);
                }
            } catch (Exception e) {
                log.error("----{}该小程序异常,后续跳过{}，message{}", wechatAppletConfig.getWechatAppletAppid(), JSON.toJSONString(wechatAppletConfig), e.getMessage(), e);
            }
        }
        //接下来进行agentConf的修改 修改成 {"TOOL":1.173.0,"BUSINESS":1.173.0}   这样的格式
        List<AgentConf> list3 = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getStatus, 1));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("BUSINESS", "1.173.0");
        for (int i = 0; i < list3.size(); i++) {
            AgentConf agentConf = list3.get(i);
            try {
                JSONObject landingPageVersion = agentConf.getLandingPageVersion();
                if (Objects.nonNull(landingPageVersion)) {
                    if (landingPageVersion.getBoolean("1.173.0")) {
                        agentConf.setLandingPageVersion(jsonObject);
                        agentConfService.updateById(agentConf);
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                log.error("{}这个agent初始化失败", JSON.toJSONString(agentConf));
            }
        }
        return "success";
    }

    /**
     * 1.196.0版本初始化whiteTypes数据
     */
    @GetMapping("init-white-types")
    public String initWhiteTypes(@RequestParam(value = "agentId", required = false) String agentId) {
        List<AgentConf> list3;
        if (StringUtils.isEmpty(agentId)) {
            list3 = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getStatus, 1));
        } else {
            list3 = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getAgentId, agentId));
        }
        for (int i = 0; i < list3.size(); i++) {
            AgentConf agentConf = list3.get(i);
            try {
                bossAdvertiserAccountGroupService.updateWhiteTypes(agentConf);
                //更新完公域之后，更新各个私库
                String[] whiteTypes = agentConf.getWhiteTypes();
                TenantContextHolder.set(agentConf.getAgentId());
                advertiserAccountGroupService.initWhiteType(whiteTypes, agentConf.getAgentId());
                TenantContextHolder.clearContext();
            } catch (Exception e) {
                log.error("agentCofig 初始化出现问题{}，问题详情为：{}", JSON.toJSONString(agentConf), e.getMessage(), e);
            } finally {
                TenantContextHolder.clearContext();
            }
        }
        return "success";
    }

    /**
     * 1.196.0版本更新boss的创建时间
     *
     * @param agentId
     * @return
     */
    @GetMapping("init-account-group-created")
    public String initAccountGroupCreated(@RequestParam(value = "agentId", required = false) String agentId) {
        List<AgentConf> list3;
        if (StringUtils.isEmpty(agentId)) {
            list3 = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getStatus, 1));

        } else {
            list3 = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getAgentId, agentId));
        }
        for (int i = 0; i < list3.size(); i++) {
            AgentConf agentConf = list3.get(i);
            TenantContextHolder.set(agentConf.getAgentId());
            List<AdvertiserAccountGroup> advertiserAccountGroups = advertiserAccountGroupService.list(new LambdaQueryWrapper<>());
            TenantContextHolder.clearContext();
            for (int i1 = 0; i1 < advertiserAccountGroups.size(); i1++) {
                bossAdvertiserAccountGroupService.updateCreatedAtUpdatedAt(advertiserAccountGroups.get(i1), agentConf.getAgentId());
            }
        }
        return "success";
    }

    /**
     * 1.201.0版本 qiyetui历史文章页的匹配
     */
    @GetMapping("init/init-history-article")
    public String initHistoryArticle(@RequestParam(value = "agentId", required = false) String agentId) {
        log.error("------------------qiyetui历史文章页的匹配--------------------开始");
        List<QiyetuiApp> qiyetuiAppList = qiyeTuiAppService.getQIyeTuiAppAll();
        for (QiyetuiApp qiyetuiApp : qiyetuiAppList) {
            TenantContextHolder.set(qiyetuiApp.getAgentId());
            try {
                enterpriseWechatOfficialHistoryArticlePageService.update(new LambdaUpdateWrapper<EnterpriseWechatOfficialHistoryArticlePage>()
                    .eq(EnterpriseWechatOfficialHistoryArticlePage::getOpenCorpid, qiyetuiApp.getOpenCorpid())
                    .set(EnterpriseWechatOfficialHistoryArticlePage::getQiyeTuiWechatAppletName, qiyetuiApp.getCorpName()));
            } catch (Exception e) {
                log.error("qiyetui历史文章页的匹配 初始化出错，agentId 为{}", TenantContextHolder.get());
            } finally {
                TenantContextHolder.clearContext();
            }
        }
        log.error("------------------qiyetui历史文章页的匹配--------------------结束");
        return "success";
    }

    /**
     * 1.201.0 对于提审渠道匹配
     */
    @GetMapping("init/init-qiyetui-channel")
    public String initQiyetuiChannel(@RequestParam(value = "agentId", required = false) String agentId) {
        log.error("------------------对于提审渠道匹配--------------------开始");
        List<QiyetuiAppRelDto> relDtos;
        if (StringUtils.isEmpty(agentId)) {
            relDtos = qiyeTuiAppService.getQiYeTuiAppPmpRel(null);
        } else {
            relDtos = qiyeTuiAppService.getQiYeTuiAppPmpRel(agentId);
        }

        //1.修改为采取批量操作  2.需要改为去掉agentConf的遍历
        //先根据agentId区分
        Map<String, List<QiyetuiAppRelDto>> map = relDtos.stream().collect(Collectors.groupingBy(QiyetuiAppRelDto::getAgentId));
        for (Map.Entry<String, List<QiyetuiAppRelDto>> entry : map.entrySet()) {
            TenantContextHolder.set(entry.getKey());
            List<QiyetuiAppRelDto> value = entry.getValue();
            //再根据项目进行区分
            try {
                Map<Long, QiyetuiAppRelDto> collect = value.stream().collect(Collectors.toMap(QiyetuiAppRelDto::getAdvertiserAccountGroupId, Function.identity()));
                for (Map.Entry<Long, QiyetuiAppRelDto> entry1 : collect.entrySet()) {
                    landingPageChannelService.updateQiyeTui(entry1.getKey(), entry1.getValue());
                }
            } catch (Exception e) {
                log.error("对于提审渠道匹配的匹配 初始化出错，agentId 为{},message为{},信息为{}", entry.getKey(), e.getMessage(), e);
            } finally {
                TenantContextHolder.clearContext();
            }
        }
        log.error("------------------对于提审渠道匹配--------------------结束");
        return "success";


    }

    @Deprecated
    @PostMapping("init/init-enterprise-wechat-robot-customer")
    public void initEnterpriseWechatRobotCustomer() {
        log.info(">>>>>>>>>>>>>>>1.223.0 版本  EnterpriseWechatRobotCustomer 初始化开始 <<<<<<<<<<<<<");

        enterpriseWechatRobotCustomerService.truncateData();
        log.info(">>>>>>>>>>>>>>>清理公共库 EnterpriseWechatRobotCustomer数据结束 <<<<<<<<<<<<<");
        List<AgentConf> list = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getStatus, 1));
        list.forEach(agentConf -> {
            String agentId = agentConf.getAgentId();
            TenantContextHolder.set(agentId);
            log.info(">>>>>>>>>>>>>>> 同步 {} EnterpriseWechatRobotCustomer数据到公共库 <<<<<<<<<<<<<", agentId);
            enterpriseWechatRobotCustomerService.initEnterpriseWechatRobotCustomer(agentId);
            TenantContextHolder.clearContext();
            log.info(">>>>>>>>>>>>>>> 同步 {}数据 结束 <<<<<<<<<<<<<", agentId);
        });
        log.info(">>>>>>>>>>>>>>> 同步 EnterpriseWechatRobotCustomer 数据 结束 <<<<<<<<<<<<<");
    }

    @Deprecated
    @PostMapping("init/visible-range-customer-contact")
    public void visibleRangeCustomerContactTransfer() {
        log.info(">>>>>>>>>>>>>>>1.223.0 版本  visible-range-customer-contact 客服联系我二维码生成状态迁移开始 <<<<<<<<<<<<<");

        landingPageWechatAppletConfigService.truncateCustomerContactData();
        log.info(">>>>>>>>>>>>>>>清理公共库 visible-range-customer-contact数据结束 <<<<<<<<<<<<<");
        List<AgentConf> list = agentConfService.list(new LambdaQueryWrapper<AgentConf>().in(AgentConf::getStatus, 0, 1));
        list.forEach(agentConf -> {
            String agentId = agentConf.getAgentId();
            TenantContextHolder.set(agentId);
            log.info(">>>>>>>>>>>>>>> 同步 {} visible-range-customer-contact数据到公共库 <<<<<<<<<<<<<", agentId);
            landingPageWechatAppletConfigService.visibleRangeCustomerContactTransfer();
            TenantContextHolder.clearContext();
            log.info(">>>>>>>>>>>>>>> 同步 {}数据 结束 <<<<<<<<<<<<<", agentId);
        });
        log.info(">>>>>>>>>>>>>>> 同步 visible-range-customer-contact 数据 结束 <<<<<<<<<<<<<");
    }


    /**
     * 初始化全账户落地页客户群关系表数据（仅首次上线执行一次，可重复执行，回退重复需要重新执行）
     */
    @Deprecated
    @PostMapping("init/init-landing-page-chat-agent-rel-data")
    public void initLandingPageChatAgentRelData() {
        log.info(">>>>>>>>>>>>>>>1.223.0 版本  初始化全账户落地页客户群关系表开始 <<<<<<<<<<<<<");
        List<String> agentIds;
        List<AgentConf> agentConfList = agentConfService.list(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getStatus, 1));
        if (CollectionUtils.isEmpty(agentConfList)) {
            return;
        }
        agentIds = agentConfList.stream().map(AgentConf::getAgentId).collect(Collectors.toList());
        log.info("开始执行初始化新版落地页排序数据job agentIds={}；", JSONObject.toJSONString(agentIds));
        for (String agentId : agentIds) {
            try {
                TenantContextHolder.set(agentId);
                List<LandingPageWechatGroupChat> lpwgcList = landingPageWechatGroupChatService.list(new LambdaQueryWrapper<LandingPageWechatGroupChat>()
                    .select(LandingPageWechatGroupChat::getCorpid, LandingPageWechatGroupChat::getChatId, LandingPageWechatGroupChat::getAdvertiserAccountGroupId)
                );
                if (!CollectionUtils.isEmpty(lpwgcList)) {
                    List<LandingPageWechatGroupChatAgentRel> lpwgcarList = new ArrayList<>();
                    lpwgcList.forEach(landingPageWechatGroupChat -> {
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(landingPageWechatGroupChat.getCorpid()) && org.apache.commons.lang3.StringUtils.isNotBlank(landingPageWechatGroupChat.getChatId())) {
                            lpwgcarList.add(new LandingPageWechatGroupChatAgentRel()
                                .setId(null)
                                .setAgentId(agentId)
                                .setCorpid(landingPageWechatGroupChat.getCorpid())
                                .setChatId(landingPageWechatGroupChat.getChatId())
                                .setAdvertiserAccountGroupId(landingPageWechatGroupChat.getAdvertiserAccountGroupId())
                                .setCreatedAt(Instant.now())
                                .setUpdatedAt(Instant.now())
                            );
                        }
                    });
                    if (!CollectionUtils.isEmpty(lpwgcarList)) {
                        landingPageWechatGroupChatAgentRelService.remove(new LambdaQueryWrapper<LandingPageWechatGroupChatAgentRel>().eq(LandingPageWechatGroupChatAgentRel::getAgentId, agentId));
                        landingPageWechatGroupChatAgentRelService.saveBatch(lpwgcarList);
                    }
                }
            } catch (Exception e) {
                log.info("初始化全账户落地页客户群关系表异常，请单独执行 agentId={}；message={}；", agentId, e.getMessage());
            }
        }

        log.info(">>>>>>>>>>>>>>> 初始化全账户落地页客户群关系表结束 <<<<<<<<<<<<<");
    }

    /**
     * 初始化企业微信pmp关系表corpid字段（仅首次上线执行一次，可重复执行，回退重复需要重新执行）
     */
    @Deprecated
    @PostMapping("init/init-enterprise-wechats-pmp-rel-corpid-data")
    public void initEnterpriseWechatsPmpRelCorpidData() {
        log.info(">>>>>>>>>>>>>>>1.223.0 版本  初始化企业微信pmp关系表corpid字段开始 <<<<<<<<<<<<<");
        TenantContextHolder.set(null);
        enterpriseWechatsPmpRelService.initEnterpriseWechatsPmpRelCorpidData();
        log.info(">>>>>>>>>>>>>>> 初始化企业微信pmp关系表corpid字段结束 <<<<<<<<<<<<<");
    }

    @Deprecated
    @PostMapping("init/acquisitionLink")
    public void acquisitionLink() {
        log.info("==============【同步获客链接到公库-开始】==============");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        workWechatCustomerAcquisitionLinkService.truncateData();
        List<AgentConf> list = agentConfService.list(new LambdaQueryWrapper<AgentConf>().in(AgentConf::getStatus, Arrays.asList(0, 1)));
        list.parallelStream().forEach(agentConf ->
            process(agentConf.getAgentId())
        );
        stopWatch.stop();
        log.info("==============【同步获客链接到公库-结束，耗时:{}S】==============", stopWatch.getTotalTimeSeconds());
    }

    /**
     * 执行迁移
     *
     * @param agentId
     */
    private void process(String agentId) {
        log.info("==============【同步获客链接到公库-agentId:{}-开始】==============", agentId);
        StopWatch stopWatchTemp = new StopWatch();
        stopWatchTemp.start();
        try {
            TenantContextHolder.set(agentId);
            long current = 1;
            boolean hasMore = true;
            Page<LandingPageWechatCustomerService> page;
            while (hasMore) {
                page = new Page<>(current, pageSize);
                Page<LandingPageWechatCustomerService> data = landingPageWechatCustomerServiceService.page(page, new LambdaQueryWrapper<LandingPageWechatCustomerService>()
                    .select(LandingPageWechatCustomerService::getId, LandingPageWechatCustomerService::getWechatUserId, LandingPageWechatCustomerService::getCorpId, LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkId
                        , LandingPageWechatCustomerService::getWechatCustomerAcquisitionLink, LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkStatus, LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkReason
                        , LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkVerify)
                    .in(LandingPageWechatCustomerService::getWechatCustomerAcquisitionLinkStatus, Lists.newArrayList(WechatCustomerAcquisitionLinkStatus.GENERATE, WechatCustomerAcquisitionLinkStatus.NORMAL, WechatCustomerAcquisitionLinkStatus.ANOMALY))
                    .isNotNull(LandingPageWechatCustomerService::getWechatUserId)
                    .isNotNull(LandingPageWechatCustomerService::getCorpId)
                    .orderByAsc(LandingPageWechatCustomerService::getId));
                List<LandingPageWechatCustomerService> customerServices = data.getRecords();
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(customerServices)) {
                    List<WorkWechatCustomerAcquisitionLink> links = Lists.newArrayList();
                    for (LandingPageWechatCustomerService customerService : customerServices) {
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(customerService.getWechatUserId()) && org.apache.commons.lang3.StringUtils.isNotBlank(customerService.getCorpId())) {
                            WorkWechatCustomerAcquisitionLink link = new WorkWechatCustomerAcquisitionLink();
                            link.setCorpId(customerService.getCorpId())
                                .setUserId(customerService.getWechatUserId())
                                .setWechatCustomerAcquisitionLinkId(customerService.getWechatCustomerAcquisitionLinkId())
                                .setWechatCustomerAcquisitionLink(customerService.getWechatCustomerAcquisitionLink())
                                .setWechatCustomerAcquisitionLinkStatus(customerService.getWechatCustomerAcquisitionLinkStatus())
                                .setWechatCustomerAcquisitionLinkReason(customerService.getWechatCustomerAcquisitionLinkReason())
                                .setWechatCustomerAcquisitionLinkVerify(customerService.getWechatCustomerAcquisitionLinkVerify());
                            links.add(link);
                        }
                    }
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(links)) {
                        //根据corpId和userId去重
                        links = links.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(
                            Comparator.comparing(x -> x.getCorpId() + x.getUserId()))), ArrayList::new));
                        Lists.partition(links, 200).forEach(temp -> workWechatCustomerAcquisitionLinkService.batchSaveOrUpdate(temp));
                    }
                }
                hasMore = customerServices.size() == pageSize;
                current = current + 1;
            }
        } catch (Exception e) {
            log.error("同步获客链接到公库-agentId:{}-异常", agentId, e);
        } finally {
            TenantContextHolder.clearContext();
            stopWatchTemp.stop();
            log.info("==============【同步获客链接到公库-agentId:{}-结束,耗时:{}S】==============", agentId, stopWatchTemp.getTotalTimeSeconds());
        }
    }

    @Deprecated
    @PostMapping("/init/init-enterprise-session-employee-customer-chat-record")
    public void initEnterpriseSessionEmployeeCustomerChatRecord() {
        log.info(">>>>>>>>>>>>>>>1.223.0版本 初始化企业微信会话存档聊天记录开始<<<<<<<<<<<<<");
        enterpriseWechatSessionArchiveService.initEnterpriseSessionEmployeeCustomerChatRecord();
        log.info(">>>>>>>>>>>>>>>1.223.0版本 初始化企业微信会话存档聊天记录结束<<<<<<<<<<<<<");
    }

    @Deprecated
    @PostMapping("/init/init-enterprise-wechat-tag-config")
    public void initEnterpriseWechatTagConfig() {
        log.info(">>>>>>>>>>>>>>>1.223.0版本 初始化企业微信标签配置开始<<<<<<<<<<<<<");
        enterpriseWechatTagConfigService.initEnterpriseWechatTagConfig();
        log.info(">>>>>>>>>>>>>>>1.223.0版本 初始化企业微信标签配置结束<<<<<<<<<<<<<");
    }

    @Deprecated
    @PostMapping("init/customer-service-official-bind")
    public void customerServiceOfficialBind() {
        log.info(">>>>>>>>>>>>>>>1.227.0 版本  customer-service-official-bind 客服公众号绑定信息迁移开始 <<<<<<<<<<<<<");
        landingPageWechatAppletConfigService.truncateCustomerServiceOfficialBind();
        log.info(">>>>>>>>>>>>>>>清理公共库 visible-range-customer-contact数据结束 <<<<<<<<<<<<<");
        landingPageWechatAppletConfigService.customerServiceOfficialBind();
        log.info(">>>>>>>>>>>>>>>1.227.0 版本  customer-service-official-bind 客服公众号绑定信息迁移结束 <<<<<<<<<<<<<");
    }

    @Deprecated
    @PostMapping("customer-contact/init/check")
    public void enterpriseInitCustomerContactCheck() {
        log.info(">>>>>>>>>>>>>>>1.230.0 版本 开始检测 <<<<<<<<<<<<<");
        enterpriseWechatService.truncateEnterpriseContact();
        log.info(">>>>>>>>>>>>>>>清理公共库 enterprise_wechat_customer_contact数据结束 <<<<<<<<<<<<<");
        enterpriseWechatService.insertEnterpriseContact();
        log.info(">>>>>>>>>>>>>>>检测h5、公众号单人渠道二维码企微使用情况结束 <<<<<<<<<<<<<");
        List<String> agentIds = enterpriseWechatService.searchAgentIdByEnterprise();
        if (CollectionUtils.isEmpty(agentIds)) {
            log.info(">>>>>>>>>>>>>>>检测多人渠道二维码企微使用情况结束 <<<<<<<<<<<<<");
            return;
        }
        agentIds.stream().filter(org.apache.commons.lang3.StringUtils::isNotBlank).forEach(e -> {
            try {
                TenantContextHolder.set(e);
                //查询对应账户公众号客服分组绑定关系表，插入到公库关联表上
                enterpriseWechatOfficialHistoryArticlePageService.searchInsertEnterpriseContact();
            } catch (Exception exception) {
                log.error("检测多人渠道二维码企微使用情况失败,agentId:{}", e, exception);
            } finally {
                TenantContextHolder.clearContext();
            }
        });
        log.info(">>>>>>>>>>>>>>>检测多人渠道二维码企微使用情况结束 <<<<<<<<<<<<<");
    }

    @Deprecated
    @PostMapping("/init/init-enterprise-wechat-corp-id-to-open-corp-id")
    public void initEnterpriseWechatCorpIdToOpenCorpId() {
        log.info(">>>>>>>>>>>>>>>1.230.0版本 初始化企业微信corpIdToOpenCorpId开始<<<<<<<<<<<<<");
        enterpriseWechatCorpIdToOpenCorpIdService.initEnterpriseWechatCorpIdToOpenCorpId();
        log.info(">>>>>>>>>>>>>>>1.230.0版本 初始化企业微信corpIdToOpenCorpId结束<<<<<<<<<<<<<");
    }

    @PostMapping("landing-page-channrl/qiye-audit/init/check")
    public void landingPageChannelAuditInit() {
        log.info(">>>>>>>>>>>>>>>1.231.0 版本 初始化企业推落地页审核状态 开始检测 <<<<<<<<<<<<<");
        List<AgentConf> list = agentConfService.list(new LambdaQueryWrapper<AgentConf>().in(AgentConf::getStatus, Arrays.asList(0, 1)));
        list.stream().forEach(e -> {
            try {
                log.info(">>>>>>>>>>>>>>> {} <<<<<<<<<<<<<", e.getAgentId());
                TenantContextHolder.set(e.getAgentId());
                //截断表
                landingPageChannelService.truncateLandingPageChannelQiyeAudit();
                //初始化
                landingPageChannelService.initLandingPageChannelQiyeAudit();
            } catch (Exception exception) {
                log.error("初始化企业推落地页审核状态失败! agentId:{}", e.getAgentId(), exception);
            } finally {
                TenantContextHolder.clearContext();
            }
        });
        log.info(">>>>>>>>>>>>>>>1.231.0 版本 初始化企业推落地页审核状态 结束 <<<<<<<<<<<<<");
    }

    @Deprecated
    @PostMapping("/init/init-enterprise-wechat-customer-service-robot-url")
    public void initEnterpriseWechatCustomerServiceRobotUrl() {
        log.info(">>>>>>>>>>>>>>>1.233.0版本 初始化企业微信微信客服URL开始<<<<<<<<<<<<<");
        enterpriseWechatRobotCustomerService.initEnterpriseWechatCustomerServiceRobotUrl();
        log.info(">>>>>>>>>>>>>>>1.233.0版本 初始化企业微信微信客服URL结束<<<<<<<<<<<<<");
    }

    @PostMapping("/init/init-enterprise-wechat-tag-upload-configuration-corp-id")
    public void initEnterpriseWechatTagUploadConfigurationCorpId() {
        log.info(">>>>>>>>>>>>>>>1.234.0版本 初始化企业微信标签上报配置corpid开始<<<<<<<<<<<<<");
        uploadConfigurationService.initEnterpriseWechatTagUploadConfigurationCorpId();
        log.info(">>>>>>>>>>>>>>>1.234.0版本 初始化企业微信标签上报配置corpid结束<<<<<<<<<<<<<");
    }

    @PostMapping("/init/init-advertiser-account-agent-rel")
    public void initAdvertiserAccountAgentRel() {
        log.info(">>>>>>>>>>>>>>>1.242.0版本 初始化巨量引擎投放账户与agent关联开始<<<<<<<<<<<<<");
        advertiserAccountService.initAdvertiserAccountAgentRel();
        log.info(">>>>>>>>>>>>>>>1.242.0版本 初始化巨量引擎投放账户与agent关联结束<<<<<<<<<<<<<");
    }

    /**
     * 初始化机器人与分组关联关系
     */
    @Deprecated
    @PostMapping("init/robot/gropu/rel")
    public void initWechatRobotCustomerGroupService() {
        log.info(">>>>>>>>>>>>>>>1.244.0版本 初始化机器人与分组关联关系开始<<<<<<<<<<<<<");
        enterpriseWechatRobotCustomerService.initWechatRobotCustomerGroupService();
        log.info(">>>>>>>>>>>>>>>1.244.0版本 初始化机器人与分组关联关系结束<<<<<<<<<<<<<");
    }

    /**
     * 初始化公库机器人与项目的关联关系
     */
    @PostMapping("init/enterprise-wechat-customer-robot-pmp-rel")
    public void initEnterpriseWechatCustomerRobotPmpRel() {
        log.info(">>>>>>>>>>>>>>>1.244.0版本 初始化微信客服机器人与项目关联关系开始<<<<<<<<<<<<<");
        enterpriseWechatRobotCustomerService.initEnterpriseWechatCustomerRobotPmpRel();
        log.info(">>>>>>>>>>>>>>>1.244.0版本 初始化微信客服机器人与项目关联关系结束<<<<<<<<<<<<<");
    }

    /**
     * 初始化企业微信客服机器人openKfUrl
     */
    @PostMapping("init/enterprise-wechat-customer-robot-open-kf-url")
    public void initEnterpriseWechatCustomerRobotOpenKfUrl() {
        log.info(">>>>>>>>>>>>>>>1.250.0版本 初始化企业微信客服机器人openKfUrl开始<<<<<<<<<<<<<");
        enterpriseWechatRobotCustomerService.initEnterpriseWechatCustomerRobotOpenKfUrl();
        log.info(">>>>>>>>>>>>>>>1.250.0版本 初始化企业微信客服机器人openKfUrl结束<<<<<<<<<<<<<");
    }

    /**
     * 初始化企业微信客服机器人统计报表机器人ID
     */
    @PostMapping("init/enterprise-wechat-customer-statistic-report-robot-id")
    public void initEnterpriseWechatCustomerRobotStatisticReportRobotId() {
        log.info(">>>>>>>>>>>>>>>1.250.0版本 初始化企业微信客服机器人统计报表机器人ID开始<<<<<<<<<<<<<");
        enterpriseWechatRobotCustomerService.initEnterpriseWechatCustomerRobotStatisticReportRobotId();
        log.info(">>>>>>>>>>>>>>>1.250.0版本 初始化企业微信客服机器人统计报表机器人ID结束<<<<<<<<<<<<<");
    }

    /**
     * 初始化企业微信客服机器人统计数据
     */
    @PostMapping("init/enterprise-wechat-customer-statistic-data")
    public void initEnterpriseWechatCustomerRobotStatisticData() {
        log.info(">>>>>>>>>>>>>>>1.250.0版本 初始化企业微信客服机器人统计数据开始<<<<<<<<<<<<<");
        enterpriseWechatRobotCustomerService.initEnterpriseWechatCustomerRobotStatisticData();
        log.info(">>>>>>>>>>>>>>>1.250.0版本 初始化企业微信客服机器人统计数据结束<<<<<<<<<<<<<");
    }

    /**
     * 初始化企业微信客服机器人类型
     */
    @PostMapping("init/enterprise-wechat-customer-type")
    public void initEnterpriseWechatCustomerRobotType() {
        log.info(">>>>>>>>>>>>>>>1.250.0版本 初始化企业微信客服机器人类型开始<<<<<<<<<<<<<");
        enterpriseWechatRobotCustomerService.initEnterpriseWechatCustomerRobotType();
        log.info(">>>>>>>>>>>>>>>1.250.0版本 初始化企业微信客服机器人类型结束<<<<<<<<<<<<<");
    }

    /**
     * 全量放开获客助手
     */
    @PostMapping("init/customer-acquisition")
    public void initCustomerAcquisition() {
        log.info(">>>>>>>>>>>>>>>1.252.0版本 全量开放企微获客助手开始<<<<<<<<<<<<<");
        bossAdvertiserAccountGroupService.appendCustomerAcquisitionUnsetAccount();
        log.info(">>>>>>>>>>>>>>>1.252.0版本 全量开始循环私库企微获客助手<<<<<<<<<<<<<");
        List<AgentConf> list = agentConfService.list(Wrappers.lambdaQuery(AgentConf.class)
            .in(AgentConf::getStatus, Arrays.asList(0, 1))
            .orderByDesc(AgentConf::getId));
        list.forEach(e -> {
            String agentId = e.getAgentId();
            log.info(">>>>>>>>>>>>>>>账户:{} 开始执行<<<<<<<<<<<<<", agentId);
            try {
                TenantContextHolder.set(agentId);
                advertiserAccountGroupService.appendCustomerAcquisitionUnsetAccount();
            } catch (Exception ex) {
                log.error("账户:{}执行失败!", agentId, ex);
            } finally {
                TenantContextHolder.clearContext();
            }
        });
        log.info(">>>>>>>>>>>>>>>1.252.0版本 全量开放企微获客助手结束<<<<<<<<<<<<<");
    }

}

