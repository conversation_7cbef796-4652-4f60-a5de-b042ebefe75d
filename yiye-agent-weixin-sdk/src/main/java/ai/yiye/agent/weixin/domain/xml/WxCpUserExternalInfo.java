package ai.yiye.agent.weixin.domain.xml;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import me.chanjar.weixin.cp.bean.WxCpBaseResp;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/15 17:36
 */
@Getter
@Setter
public class WxCpUserExternalInfo extends WxCpBaseResp {
    @SerializedName("external_contact")
    private ExternalContact externalContact;


    @Getter
    @Setter
    public static class ExternalContact implements Serializable {
        @SerializedName("external_userid")
        private String externalUserid;
        @SerializedName("name")
        private String name;
        @SerializedName("unionid")
        private String unionid;
    }

}
