package ai.yiye.agent.weixin.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ExternalContactRemarkResponseBody {

    private String userid;

    @JsonProperty("external_userid")
    private String externalUserid;

    @JsonProperty("remark_mobiles")
    private List<String> remarkMobiles;

    @JsonProperty("description")
    private String description;

    @JsonProperty("remark")
    private String remark;

}
