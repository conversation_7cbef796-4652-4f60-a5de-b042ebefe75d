package ai.yiye.agent.weixin.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import me.chanjar.weixin.cp.bean.WxCpBaseResp;

@Getter
@Setter
public class AddCorpTagResponseBody extends WxCpBaseResp {

    @JsonProperty("tag_group")
    private TagGroup tagGroup;

    @Getter
    @Setter
    public static class TagGroup {

        @JsonProperty("group_id")
        private String groupId;

        @JsonProperty("group_name")
        private String groupName;

        private Long order;

        @JsonProperty("tag")
        private Tag[] tags;

        @Getter
        @Setter
        public static class Tag {

            private String id;

            private String name;

            private Long order;

        }

    }

}
