package ai.yiye.agent.weixin.util;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: xin
 * @Date: 2023/10/18 11:20
 * @description <功能描述>
 */
@Slf4j
public class WxErrorMessageUtil {

    public static String decorationErrorMessage(boolean urlFlag, String message) {
        String output = message;
        try {
            String replacement = "错误原因查看链接：";
            String patternString = ".*?(\\d{1,3}\\.){3}\\d{1,3}"; // 匹配IP地址及其前面的字符串

            Pattern pattern = Pattern.compile(patternString);
            Matcher matcher = pattern.matcher(message);
            output = matcher.replaceFirst(replacement);
            if (urlFlag) {
                output = output.replaceFirst(".*https://", "https://");
            }
        } catch (Exception e) {
            log.error("====>装饰错误信息失败!message:{}", message, e);
        }
        return output;

    }
}
