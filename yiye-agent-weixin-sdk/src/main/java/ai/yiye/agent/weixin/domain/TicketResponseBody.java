package ai.yiye.agent.weixin.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TicketResponseBody {
    /**
     *  正常状态 其它状态为异常
     *  "errcode":0,
     *  "errmsg":"ok"
     */
    private String errmsg;

    private Integer errcode;

    /**
     * 生成签名所需的jsapi_ticket，最长为512字节
     */
    private String ticket;
    /**
     * 凭证的有效时间（秒）
     */
    private Integer expiresIn;

}
