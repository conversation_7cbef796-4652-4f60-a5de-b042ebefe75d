package ai.yiye.agent.weixin.domain.xml;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/14 18:59
 */

@Data
public class WorkWechatCustomerDetailsResponse {

    public static WorkWechatCustomerDetailsResponse fromJson(String json) {
        return WxCpGsonBuilder.create().fromJson(json, WorkWechatCustomerDetailsResponse.class);
    }

    /**
     * 企业id
     */
    private String corpid;
    private Long enterpriseWechatId;
    /**
     * 返回码
     */
    private Integer errcode;
    /**
     * 对返回码的文本描述内容
     */
    private String errmsg;
    /**
     * 分页的cursor，当跟进人多于500人时返回
     */
    @SerializedName("next_cursor")
    private String nextCursor;
    /**
     * 外部联系人相关信息
     */
    @SerializedName("external_contact")
    private ExternalContact externalContact;
    /**
     * 该成员对此外部联系人 的信息
     */
    @SerializedName("follow_user")
    private List<FollowUser> followUser;
    @SerializedName("follow_info")
    private FollowInfo followInfo;

    @Data
    public static class FollowInfo {
        private String userId;
        private String remark;
        private String description;
        private Long createtime;
        private List<String> tagId;
        private String remarkCorpName;
        private List<String> remarkMobiles;
        private String operUserid;
        private Long addWay;
        private WxCpUser.WechatChannels wechatChannels;

    }


    @Data
    public static class ExternalContact {
        /**
         * 外部联系人的userid
         */
        @SerializedName("external_userid")
        private String externalUserid;
        /**
         * 外部联系人的名称[注1]
         */
        private String name;
        /**
         * 外部联系人的职位，如果外部企业或用户选择隐藏职位，则不返回，仅当联系人类型是企业微信用户时有此字段
         */
        private String position;
        /**
         * 外部联系人头像，代开发自建应用需要管理员授权才可以获取，第三方不可获取，上游企业不可获取下游企业客户该字段
         */
        private String avatar;
        /**
         * 外部联系人所在企业的简称，仅当联系人类型是企业微信用户时有此字段
         */
        @SerializedName("corp_name")
        private String corpName;
        /**
         * 外部联系人所在企业的主体名称，仅当联系人类型是企业微信用户时有此字段
         */
        @SerializedName("corp_full_name")
        private String corpFullName;
        /**
         * 外部联系人的类型，1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户
         */
        private Integer type;
        /**
         * 外部联系人性别 0-未知 1-男性 2-女性
         */
        private Integer gender;
        /**
         * 外部联系人在微信开放平台的唯一身份标识（微信unionid），通过此字段企业可将外部联系人与公众号/小程序用户关联起来。仅当联系人类型是微信用户，且企业或第三方服务商绑定了微信开发者ID有此字段。查看绑定方法。上游企业不可获取下游企业客户的unionid字段
         */
        private String unionid;
        /**
         * 外部联系人的自定义展示信息，可以有多个字段和多种类型，包括文本，网页和小程序，仅当联系人类型是企业微信用户时有此字段，字段详情见对外属性；
         */
        @SerializedName("external_profile")
        private ExternalProfile externalProfile;
        @SerializedName("follow_user")
        private List<FollowUser> followUser;
    }

    @Data
    public static class FollowUser {
        /**
         * 添加了此外部联系人的企业成员userid
         */
        private String userid;
        /**
         * 该成员对此外部联系人的备注
         */
        private String remark;
        /**
         * 该成员对此外部联系人的描述
         */
        private String description;
        /**
         * 该成员添加此外部联系人的时间
         */
        private Long createtime;
        private List<FollowUserTag> tags;
        /**
         * 该成员对此客户备注的企业名称
         */
        @SerializedName("remark_corp_name")
        private String remarkCorpName;
        /**
         * 该成员对此客户备注的手机号码，代开发自建应用需要管理员授权才可以获取，第三方不可获取，上游企业不可获取下游企业客户该字段
         */
        @SerializedName("remark_mobiles")
        private List<String> remarkMobiles;
        /**
         * 发起添加的userid，如果成员主动添加，为成员的userid；如果是客户主动添加，则为客户的外部联系人userid；如果是内部成员共享/管理员分配，则为对应的成员/管理员userid
         */
        @SerializedName("oper_userid")
        private String operUserid;
        /**
         * 该成员添加此客户的来源，具体含义详见来源定义
         */
        @SerializedName("add_way")
        private Integer addWay;
        /**
         * 企业自定义的state参数，用于区分客户具体是通过哪个「联系我」添加，由企业通过创建「联系我」方式指定
         */
        private String state;

        @Data
        public static class FollowUserTag {
            /**
             * 该成员添加此外部联系人所打标签的分组名称（标签功能需要企业微信升级到2.7.5及以上版本）
             */
            @SerializedName("group_name")
            private String groupName;
            /**
             * 该成员添加此外部联系人所打标签名称
             */
            @SerializedName("tag_name")
            private String tagName;
            /**
             * 该成员添加此外部联系人所打企业标签的id，用户自定义类型标签（type=2）不返回
             */
            @SerializedName("tag_id")
            private String tagId;
            /**
             * 该成员添加此外部联系人所打标签类型, 1-企业设置，2-用户自定义，3-规则组标签（仅系统应用返回）
             */
            private Integer type;
        }
    }

    @Data
    public static class ExternalProfile {
        @SerializedName("external_attr")
        private List<ExternalAttr> externalAttr;
    }

    @Data
    public static class ExternalAttr {
        private Integer type;
        private String name;
        private ExternalAttrText text;
        private ExternalAttrWeb web;
        private ExternalAttrMiniprogram miniprogram;
    }

    @Data
    public static class ExternalAttrText {
        private String value;
    }

    @Data
    public static class ExternalAttrWeb {
        private String url;
        private String title;
    }

    @Data
    public static class ExternalAttrMiniprogram {
        private String appid;
        private String pagepath;
        private String title;
    }


}
