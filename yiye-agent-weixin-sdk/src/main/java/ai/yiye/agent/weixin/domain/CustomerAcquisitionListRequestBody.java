package ai.yiye.agent.weixin.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 获客链接列表、获客链接客户列表请求实体
 */
@Data
public class CustomerAcquisitionListRequestBody {

    /**
     * 查询客户列表时传递获客链接id
     */
    @JsonProperty("link_id")
    private String linkId;

    /**
     * 返回的最大记录数，整型，查询获客链接时最大值100,查询客户信息时最大值为1000
     */
    private Integer limit;

    /**
     * 用于分页查询的游标，字符串类型，由上一次调用返回，首次调用可不填
     */
    private String cursor;
}
