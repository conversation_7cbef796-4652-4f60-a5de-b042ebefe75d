package ai.yiye.agent.weixin.client;

import ai.yiye.agent.weixin.domain.xml.WxCpUserExternalGroupChatInfo;
import ai.yiye.agent.weixin.domain.xml.WxCpUserExternalTagList;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.bean.external.WxCpUserWithExternalPermission;

/**
 * <AUTHOR>
 * @create 2021/8/30 9:13 下午
 */

public enum WorkWechatApiModule {

    /**
     * 获取客户群详情
     */
    EXTERNALCONTACT_GROUPCHAT_GET {
        @Override
        public String getPath() {
            return "/externalcontact/groupchat/get";
        }

        @Override
        public WxCpUserExternalGroupChatInfo getResponseBody(String body) {
            return WxCpUserExternalGroupChatInfo.fromJson(body);
        }

        @Override
        public String method() {
            return "POST";
        }

    },
    /**
     * 获取配置了客户联系功能的成员列表
     */
    EXTERNALCONTACT_FOLLOW_USER_LIST_GET {
        @Override
        public String getPath() {
            return "/externalcontact/get_follow_user_list";
        }

        @Override
        public WxCpUserWithExternalPermission getResponseBody(String body) {
            return WxCpUserWithExternalPermission.fromJson(body);
        }

        @Override
        public String method() {
            return "GET";
        }

    },
    /**
     * 获取客户群详情
     */
    EXTERNALCONTACT_GET_CORP_TAG_LIST {
        @Override
        public String getPath() {
            return "/externalcontact/get_corp_tag_list";
        }

        @Override
        public WxCpUserExternalTagList getResponseBody(String body) {
            return WxCpUserExternalTagList.fromJson(body);
        }

        @Override
        public String method() {
            return "POST";
        }
    },
    //获取客户详情
    EXTERNALCONTACT_GET {
        @Override
        public String getPath() {
            return "/externalcontact/get";
        }

        @Override
        public WxCpUserExternalTagList getResponseBody(String body) {
            return WxCpUserExternalTagList.fromJson(body);
        }

        @Override
        public String method() {
            return "GET";
        }
    };


    public <T> T getResponseBody(String body) {
        return null;
    }

    /**
     * 获取路径
     *
     * @return
     */
    public String getPath() {
        return null;
    }

    public String method() {
        return "GET";
    }


}
