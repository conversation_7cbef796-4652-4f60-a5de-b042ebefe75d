package ai.yiye.agent.weixin.domain;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/14 20:27
 */
@Data
public class RobotMenuMessageRequestBody extends RobotBaseMessageRequestBody implements Serializable {

    private String msgtype = "msgtype";
    private MsgMenu msgmenu;


    @Data
    public static class MsgMenu {
        //起始文本
        @JsonProperty("head_content")
        private String headContent;
        //结束文本
        @JsonProperty("tail_content")
        private String tailContent;
        /**
         * 可变菜单内容
         */
        private List<JSONObject> list;

    }
}
