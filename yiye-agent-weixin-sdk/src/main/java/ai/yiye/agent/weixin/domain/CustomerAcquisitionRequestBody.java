package ai.yiye.agent.weixin.domain;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 获取获客链接请求体
 */


@Data
public class CustomerAcquisitionRequestBody {

    /**
     * 获客链接ID - 创建获客链接无需传递
     */
    @JsonProperty("link_id")
    private String linkId;

    /**
     * 链接名称
     */
    @JsonProperty("link_name")
    private String linkName;

    /**
     * 是否无需验证
     */
    @JsonProperty("skip_verify")
    private Boolean skipVerify;

    /**
     * 关联企微成员或部门
     */
    private CustomerAcquisitionRequestItemBody range;
}
