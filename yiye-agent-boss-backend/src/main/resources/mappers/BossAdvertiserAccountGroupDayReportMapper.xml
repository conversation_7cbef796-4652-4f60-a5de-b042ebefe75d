<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.yiye.agent.boss.mapper.BossAdvertiserAccountGroupDayReportMapper">
    <update id="saveOrUpdateSelective">
        INSERT INTO BOSS_ADVERTISER_ACCOUNT_GROUP_DAY_REPORT
        (advertiser_account_group_id,agent_id,landing_page_pv,identify_qr_code_num,add_work_wechat_num,day_time, follow_official_account_num,fund,bill_account_num,billing_proportion)
        VALUES
            (
            #{report.advertiserAccountGroupId,jdbcType=INTEGER},
            #{report.agentId,jdbcType=VARCHAR},
            #{report.landingPagePv,jdbcType=INTEGER},
            #{report.identifyQrCodeNum,jdbcType=INTEGER},
            #{report.addWorkWechatNum,jdbcType=INTEGER},
            #{report.dayTime,jdbcType=TIMESTAMP},
            #{report.followOfficialAccountNum},
            #{report.fund},
            #{report.billAccountNum},
            #{report.billingProportion}
            )
        ON CONFLICT (day_time,advertiser_account_group_id,agent_id)
        DO UPDATE SET
        landing_page_pv = EXCLUDED.landing_page_pv,
        identify_qr_code_num = EXCLUDED.identify_qr_code_num,
        add_work_wechat_num = EXCLUDED.add_work_wechat_num,
        follow_official_account_num = EXCLUDED.follow_official_account_num,
                       fund = EXCLUDED.fund,
                       bill_account_num = EXCLUDED.bill_account_num
    </update>
    <update id="saveOrUpdateBatch" useGeneratedKeys="true" keyProperty="list.id">
        INSERT INTO BOSS_ADVERTISER_ACCOUNT_GROUP_DAY_REPORT
        (advertiser_account_group_id,agent_id,landing_page_pv,identify_qr_code_num,add_work_wechat_num,day_time, follow_official_account_num,
        qiye_request_num,qiye_request_success_num,qiye_request_fail_num,qiye_pv_num,qiye_mini_pv_num,official_identify_qr_code_num,identify_group_qr_code_num,add_work_wechat_group_num,qiye_wechat_official_article_page_view_num,
        qiye_wechat_official_article_request_num,qiye_wechat_official_article_request_success_num,qiye_wechat_official_article_request_fail_num,
        send_sms_num, form_send_sms_num, order_send_sms_num,clue_form_submit_num,form_submit_num,douyin_applet_native_form_submit_num,phone_number_recieved_num,active_message_authorization_num,pop_up_display_num,
        online_shop_buy_goods_success_num,douyin_applet_order_submit_num,douyin_applet_order_finish_num,order_submit_num,order_finish_num,
         ele_pv_num,ele_qr_code_view_num,ele_identify_wechat_qr_code_num,ele_add_wechat_success_num,
         whatsapp_jump_num,whatsapp_add_friend_success_num,whatsapp_user_open_mouth_num,overseas_pv_num,
        whatsapp_customer_prologue_num,whatsapp_customer_send_message_num,
         tao_bao_movie_applet_jump_num,tao_bao_movie_applet_order_num,
         add_group_after_add_customer_service_num, add_group_after_follow_official_account_num,flow_source_jump_page_view_num
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.advertiserAccountGroupId,jdbcType=INTEGER},
            #{item.agentId,jdbcType=INTEGER},
            #{item.landingPagePv,jdbcType=INTEGER},
            #{item.identifyQrCodeNum,jdbcType=INTEGER},
            #{item.addWorkWechatNum,jdbcType=INTEGER},
            #{item.dayTime,jdbcType=TIMESTAMP},
            #{item.followOfficialAccountNum,jdbcType=INTEGER},
            #{item.qiyeRequestNum,jdbcType=INTEGER},
            #{item.qiyeRequestSuccessNum,jdbcType=INTEGER},
            #{item.qiyeRequestFailNum,jdbcType=INTEGER},
            #{item.qiyePvNum,jdbcType=INTEGER},
            #{item.qiyeMiniPvNum,jdbcType=INTEGER},
            #{item.officialIdentifyQrCodeNum,jdbcType=INTEGER},
            #{item.identifyGroupQrCodeNum,jdbcType=INTEGER},
            #{item.addWorkWechatGroupNum,jdbcType=INTEGER},
            #{item.qiyeWechatOfficialArticlePageViewNum,jdbcType=INTEGER},
            #{item.qiyeWechatOfficialArticleRequestNum,jdbcType=INTEGER},
            #{item.qiyeWechatOfficialArticleRequestSuccessNum,jdbcType=INTEGER},
            #{item.qiyeWechatOfficialArticleRequestFailNum,jdbcType=INTEGER},
            #{item.sendSmsNum,jdbcType=INTEGER},
            #{item.formSendSmsNum,jdbcType=INTEGER},
            #{item.orderSendSmsNum,jdbcType=INTEGER},
            #{item.clueFormSubmitNum,jdbcType=INTEGER},
            #{item.formSubmitNum,jdbcType=INTEGER},
            #{item.douyinAppletNativeFormSubmitNum,jdbcType=INTEGER},
            #{item.phoneNumberRecievedNum,jdbcType=INTEGER},
            #{item.activeMessageAuthorizationNum,jdbcType=INTEGER},
            #{item.popUpDisplayNum,jdbcType=INTEGER},
            #{item.onlineShopBuyGoodsSuccessNum,jdbcType=INTEGER},
            #{item.douyinAppletOrderSubmitNum,jdbcType=INTEGER},
            #{item.douyinAppletOrderFinishNum,jdbcType=INTEGER},
            #{item.orderSubmitNum,jdbcType=INTEGER},
            #{item.orderFinishNum,jdbcType=INTEGER},
            #{item.elePvNum,jdbcType=INTEGER},
            #{item.eleQrCodeViewNum,jdbcType=INTEGER},
            #{item.eleIdentifyWechatQrCodeNum,jdbcType=INTEGER},
            #{item.eleAddWechatSuccessNum,jdbcType=INTEGER},
            #{item.whatsappJumpNum,jdbcType=INTEGER},
            #{item.whatsappAddFriendSuccessNum,jdbcType=INTEGER},
            #{item.whatsappUserOpenMouthNum,jdbcType=INTEGER},
            #{item.overseasPvNum,jdbcType=INTEGER},
            #{item.whatsappCustomerPrologueNum,jdbcType=INTEGER},
            #{item.whatsappCustomerSendMessageNum,jdbcType=INTEGER},
            #{item.taoBaoMovieAppletJumpNum,jdbcType=INTEGER},
            #{item.taoBaoMovieAppletOrderNum,jdbcType=INTEGER},
            #{item.addGroupAfterAddCustomerServiceNum,jdbcType=INTEGER},
            #{item.addGroupAfterFollowOfficialAccountNum,jdbcType=INTEGER},
            #{item.flowSourceJumpPageViewNum,jdbcType=INTEGER}
            )
        </foreach>
        ON CONFLICT (day_time,advertiser_account_group_id,agent_id)
        DO UPDATE SET
        landing_page_pv = EXCLUDED.landing_page_pv,
        identify_qr_code_num = EXCLUDED.identify_qr_code_num,
        add_work_wechat_num = EXCLUDED.add_work_wechat_num,
        follow_official_account_num = EXCLUDED.follow_official_account_num,
        qiye_request_num = EXCLUDED.qiye_request_num,
        qiye_request_success_num = EXCLUDED.qiye_request_success_num,
        qiye_request_fail_num = EXCLUDED.qiye_request_fail_num,
        qiye_pv_num = EXCLUDED.qiye_pv_num,
        qiye_mini_pv_num = EXCLUDED.qiye_mini_pv_num,
        official_identify_qr_code_num = EXCLUDED.official_identify_qr_code_num,
        identify_group_qr_code_num = EXCLUDED.identify_group_qr_code_num,
        add_work_wechat_group_num = EXCLUDED.add_work_wechat_group_num,
        qiye_wechat_official_article_page_view_num = EXCLUDED.qiye_wechat_official_article_page_view_num,
        qiye_wechat_official_article_request_num = EXCLUDED.qiye_wechat_official_article_request_num,
        qiye_wechat_official_article_request_success_num = EXCLUDED.qiye_wechat_official_article_request_success_num,
        qiye_wechat_official_article_request_fail_num = EXCLUDED.qiye_wechat_official_article_request_fail_num,
        send_sms_num  = EXCLUDED.send_sms_num,
        form_send_sms_num = EXCLUDED.form_send_sms_num,
        order_send_sms_num = EXCLUDED.order_send_sms_num,
        clue_form_submit_num = EXCLUDED.clue_form_submit_num,
        form_submit_num = EXCLUDED.form_submit_num,
        douyin_applet_native_form_submit_num = EXCLUDED.douyin_applet_native_form_submit_num,
        phone_number_recieved_num = EXCLUDED.phone_number_recieved_num,
        active_message_authorization_num = EXCLUDED.active_message_authorization_num,
        pop_up_display_num = EXCLUDED.pop_up_display_num,
        online_shop_buy_goods_success_num = EXCLUDED.online_shop_buy_goods_success_num,
        douyin_applet_order_submit_num = EXCLUDED.douyin_applet_order_submit_num,
        douyin_applet_order_finish_num = EXCLUDED.douyin_applet_order_finish_num,
        order_submit_num = EXCLUDED.order_submit_num,
        order_finish_num = EXCLUDED.order_finish_num,
        ele_pv_num = EXCLUDED.ele_pv_num,
        ele_qr_code_view_num = EXCLUDED.ele_qr_code_view_num,
        ele_identify_wechat_qr_code_num = EXCLUDED.ele_identify_wechat_qr_code_num,
        ele_add_wechat_success_num = EXCLUDED.ele_add_wechat_success_num,
        whatsapp_jump_num = EXCLUDED.whatsapp_jump_num,
        whatsapp_add_friend_success_num = EXCLUDED.whatsapp_add_friend_success_num,
        whatsapp_user_open_mouth_num = EXCLUDED.whatsapp_user_open_mouth_num,
        overseas_pv_num = EXCLUDED.overseas_pv_num,
        whatsapp_customer_prologue_num = EXCLUDED.whatsapp_customer_prologue_num,
        whatsapp_customer_send_message_num = EXCLUDED.whatsapp_customer_send_message_num,
        tao_bao_movie_applet_jump_num = EXCLUDED.tao_bao_movie_applet_jump_num,
        tao_bao_movie_applet_order_num = EXCLUDED.tao_bao_movie_applet_order_num,
        add_group_after_add_customer_service_num = EXCLUDED.add_group_after_add_customer_service_num,
        add_group_after_follow_official_account_num = EXCLUDED.add_group_after_follow_official_account_num,
        flow_source_jump_page_view_num = EXCLUDED.flow_source_jump_page_view_num
    </update>

    <select id="getReportDayByAgentId" resultType="ai.yiye.agent.boss.vo.BossAccountGroupReportDayVo">
        with dayReport as (select day_time as day_time,
        baagdr.agent_id as agent_id,
        COALESCE(sum(landing_page_pv), 0) as landing_page_pv,
        coalesce(sum(identify_qr_code_num), 0) as identify_qr_code_num,
        coalesce(sum(add_work_wechat_num), 0) as add_work_wechat_num,
        coalesce(sum(follow_official_account_num), 0) as follow_official_account_num,
        coalesce(sum(qiye_request_num), 0) as qiye_request_num,
        coalesce(sum(qiye_request_success_num), 0) as qiye_request_success_num,
        coalesce(sum(qiye_request_fail_num), 0) as qiye_request_fail_num,
        coalesce(sum(qiye_pv_num), 0) as qiye_pv_num,
        coalesce(sum(qiye_mini_pv_num), 0) as qiye_mini_pv_num,
        coalesce(sum(official_identify_qr_code_num), 0) as official_identify_qr_code_num,
        coalesce(sum(identify_group_qr_code_num), 0) as identify_group_qr_code_num,
        coalesce(sum(add_work_wechat_group_num), 0) as add_work_wechat_group_num,
        <!--以下为1.197.0新增三个率 -->
        CASE WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN
        0 ELSE SUM ( COALESCE ( official_identify_qr_code_num, 0 ) ) / ( COALESCE ( SUM ( landing_page_pv ), 0 ) :: NUMERIC ) * 100
        END AS official_identify_qr_code_rate,
        CASE  WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN
        0 ELSE SUM ( COALESCE ( identify_group_qr_code_num, 0 ) ) / ( COALESCE ( SUM ( landing_page_pv ), 0 ) :: NUMERIC ) * 100
        END AS identify_group_qr_code_rate,
        CASE WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN
        0 ELSE SUM ( COALESCE ( add_work_wechat_group_num, 0 ) ) /  ( COALESCE ( SUM ( landing_page_pv ), 0 ) :: NUMERIC ) * 100
        END AS add_work_wechat_group_rate,
        coalesce(sum(qiye_wechat_official_article_page_view_num),0) as qiye_wechat_official_article_page_view_num,
        coalesce(sum(qiye_wechat_official_article_request_num),0) as qiye_wechat_official_article_request_num,
        coalesce(sum(qiye_wechat_official_article_request_success_num),0) as qiye_wechat_official_article_request_success_num,
        coalesce(sum(qiye_wechat_official_article_request_fail_num),0) as qiye_wechat_official_article_request_fail_num,
        coalesce(sum(send_sms_num),0) as send_sms_num,
        coalesce(sum(form_send_sms_num),0) as form_send_sms_num,
        coalesce(sum(order_send_sms_num),0) as order_send_sms_num,

        <!-- 1.263.0 饿了么小程序对接 -->
        coalesce(sum(ele_pv_num),0)as ele_pv_num,
        coalesce(sum(ele_qr_code_view_num),0) as ele_qr_code_view_num,
        coalesce(sum(ele_identify_wechat_qr_code_num),0) as ele_identify_wechat_qr_code_num,
        coalesce(sum(ele_add_wechat_success_num),0) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(sum(whatsapp_jump_num),0) as  whatsapp_jump_num,
        coalesce(sum(whatsapp_add_friend_success_num),0) as  whatsapp_add_friend_success_num,
        coalesce(sum(whatsapp_user_open_mouth_num),0) as  whatsapp_user_open_mouth_num,
        coalesce(sum(overseas_pv_num),0)as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        coalesce(sum(whatsapp_customer_prologue_num),0) as  whatsapp_customer_prologue_num,
        coalesce(sum(whatsapp_customer_send_message_num),0) as  whatsapp_customer_send_message_num,
        <!--以下为1.247.0新增字段 -->
        coalesce(sum(form_submit_num),0) as form_submit_num,
        coalesce(sum(clue_form_submit_num),0) as clue_form_submit_num,
        coalesce(sum(douyin_applet_native_form_submit_num),0) as douyin_applet_native_form_submit_num,
        (coalesce(sum(form_submit_num),0) + coalesce(sum(clue_form_submit_num),0) + coalesce(sum(douyin_applet_native_form_submit_num),0) )as form_submit_total_num,
        coalesce(sum(phone_number_recieved_num),0) as phone_number_recieved_num,
        coalesce(sum(active_message_authorization_num),0) as active_message_authorization_num,
        coalesce(sum(pop_up_display_num),0) as pop_up_display_num,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0  THEN 0  ELSE   coalesce(sum(form_submit_num),0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as form_submit_rate,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN 0   ELSE   (coalesce(sum(form_submit_num), 0) + coalesce(sum(clue_form_submit_num), 0) +  coalesce(sum(douyin_applet_native_form_submit_num), 0)) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as form_submit_total_rate,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN 0  ELSE   coalesce(sum(form_submit_num),0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as form_submit_rate,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN 0 ELSE coalesce(sum(clue_form_submit_num),0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as clue_form_submit_rate,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN 0 ELSE    coalesce(sum(douyin_applet_native_form_submit_num),0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as douyin_applet_native_form_submit_rate,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN 0  ELSE  coalesce(sum(phone_number_recieved_num),0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as phone_number_recieved_rate,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN 0 ELSE     coalesce(sum(active_message_authorization_num),0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as active_message_authorization_rate,
        <!--1.247.0-->

        <!--1.256.0新增订单相关数据-->
        sum(coalesce(order_submit_num,0)) as order_submit_num,
        sum(coalesce(order_finish_num,0)) as order_finish_num,
        sum(coalesce(online_shop_buy_goods_success_num,0)) as online_shop_buy_goods_success_num,
        sum(coalesce(douyin_applet_order_submit_num,0)) as douyin_applet_order_submit_num,
        sum(coalesce(douyin_applet_order_finish_num,0)) as douyin_applet_order_finish_num,

        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(order_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as order_submit_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(order_finish_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as order_finish_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(douyin_applet_order_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as douyin_applet_order_submit_rate,
        case when coalesce(sum(douyin_applet_order_submit_num), 0) = 0 then 0 else sum(coalesce(douyin_applet_order_finish_num, 0)) / sum(coalesce(douyin_applet_order_submit_num, 0)) * 100 end as douyin_applet_order_finish_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(online_shop_buy_goods_success_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as online_shop_buy_goods_success_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else (sum(coalesce(order_finish_num, 0)) + sum(coalesce(douyin_applet_order_finish_num, 0))) / sum(coalesce(landing_page_pv, 0)) * 100 end as comprehensive_payment_rate,
        <!--1.256.0-->

        <!--1.264.0新增淘宝电影相关数据-->
        sum(coalesce(tao_bao_movie_applet_jump_num,0)) as tao_bao_movie_applet_jump_num,
        sum(coalesce(tao_bao_movie_applet_order_num,0)) as tao_bao_movie_applet_order_num,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(tao_bao_movie_applet_jump_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as tao_bao_movie_applet_jump_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(tao_bao_movie_applet_order_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as tao_bao_movie_applet_order_rate,
        <!--1.264.0-->

        <!--1.271.0新增加群相关数据-->
        sum(coalesce(add_group_after_add_customer_service_num,0)) as add_group_after_add_customer_service_num,
        sum(coalesce(add_group_after_follow_official_account_num,0)) as add_group_after_follow_official_account_num,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(add_group_after_add_customer_service_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as add_group_after_add_customer_service_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(add_group_after_follow_official_account_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as add_group_after_follow_official_account_rate,
        <!--1.271.0-->
        sum(coalesce(flow_source_jump_page_view_num,0)) as flow_source_jump_page_view_num,
        case
        when COALESCE(sum(identify_qr_code_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(identify_qr_code_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end
        as identify_qr_code_rate,
        case
        when COALESCE(sum(add_work_wechat_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(add_work_wechat_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end
        as add_work_wechat_rate,
        case
        when COALESCE(sum(follow_official_account_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(follow_official_account_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end
        as follow_official_account_rate,
        case
        when COALESCE(sum(add_work_wechat_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(identify_qr_code_num), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(add_work_wechat_num), 0) / (COALESCE(sum(identify_qr_code_num), 0)::NUMERIC) * 100
        end
        as identify_qr_code_add_work_wechat_rate
        from boss_advertiser_account_group_day_report baagdr
        where baagdr.agent_id = #{vo.agentId}
        and baagdr.day_time is not null
        <if test="vo.startTime != null and vo.endTime != null">
            and baagdr.day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by day_time,baagdr.agent_id
        ),
        unitPrice as (
        select
        coalesce((sum(bo.order_amount) / sum(bacd.combo_count)),0)
        as unit_price,
        baagdr.agent_id as agent_id
        from boss_advertiser_account_group_day_report baagdr
        left join (select agent_id,
        coalesce(sum(order_amount), 0) as order_amount,
        coalesce(sum(increase_pv), 0) as increase_pv
        from boss_order group by agent_id) bo on baagdr.agent_id = bo.agent_id
        left join boss_agent_conf_detail bacd on baagdr.agent_id = bacd.agent_id
        where baagdr.agent_id = #{vo.agentId}
        group by baagdr.agent_id
        ),
        <!--          计费投放账户数（巨量引擎） 授权投放账户消耗金额（巨量引擎 套餐量 套餐使用量 套餐余量-->
        consumeData as  (select day_time as day_time, sum(fund) as fund,max(bill_account_num) as bill_account_num,max(billing_proportion) as billing_proportion,agent_id  from boss_advertiser_account_fund_day_report baafdr
        group by day_time, agent_id
        )
        select dayReport.*,
        case
        when coalesce(add_work_wechat_num, 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        (unitPrice.unit_price * dayReport.landing_page_pv) / add_work_wechat_num
        end as add_fans_cost,
        cd.billing_proportion as  billing_proportion,
        <!--                 投放账户消耗金额 -->
        cd.fund as  billing_account_consume,
        <!--                 付费账户数 -->
        cd.bill_account_num as billing_accounts,
        <!--                  套餐使用量 每日投放账户消耗 * 收费比例 之和 -->
        cd.fund * cd.billing_proportion / 100 as combo_account_consume
        from dayReport left join unitPrice on unitPrice.agent_id = dayReport.agent_id
        left join consumeData cd on dayReport.agent_id = cd.agent_id and dayReport.day_time = cd.day_time
        order by
        <if test="vo.order == 'dayTime'">
            day_time ${vo.sort},
        </if>
        <if test="vo.order == 'landingPagePv'">
            landing_page_pv ${vo.sort},
        </if>
        <if test="vo.order == 'identifyQrCodeNum'">
            identify_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatNum'">
            add_work_wechat_num ${vo.sort},
        </if>
        <if test="vo.order == 'identifyQrCodeRate'">
            identify_qr_code_rate ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatRate'">
            add_work_wechat_rate ${vo.sort},
        </if>
        <if test="vo.order == 'addFansCost'">
            add_fans_cost ${vo.sort},
        </if>
        <if test="vo.order == 'identifyQrCodeAddWorkWechatRate'">
            identify_qr_code_add_work_wechat_rate ${vo.sort},
        </if>
        <if test="vo.order == 'followOfficialAccountNum'">
            follow_official_account_num ${vo.sort},
        </if>
        <if test="vo.order == 'followOfficialAccountRate'">
            follow_official_account_rate ${vo.sort},
        </if>
        <if test="vo.order == 'billingAccounts'">
            billing_accounts ${vo.sort} nulls last,
        </if>
        <if test="vo.order == 'billingAccountConsume'">
            billing_account_consume ${vo.sort} nulls last,
        </if>
        <if test="vo.order == 'comboAccountConsume'">
            combo_account_consume ${vo.sort} nulls last,
        </if>
        <if test="vo.order == 'qiyeRequestNum'">
            qiye_request_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeRequestSuccessNum'">
            qiye_request_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeRequestFailNum'">
            qiye_request_fail_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyePvNum'">
            qiye_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeMiniPvNum'">
            qiye_mini_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'officialIdentifyQrCodeNum'">
            official_identify_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'identifyGroupQrCodeNum'">
            identify_group_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatGroupNum'">
            add_work_wechat_group_num ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatGroupRate'">
            add_work_wechat_group_rate ${vo.sort},
        </if>
        <if test="vo.order == 'identifyGroupQrCodeRate'">
            identify_group_qr_code_rate ${vo.sort},
        </if>
        <if test="vo.order == 'officialIdentifyQrCodeRate'">
            official_identify_qr_code_rate ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticlePageViewNum'">
            qiye_wechat_official_article_page_view_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticleRequestNum'">
            qiye_wechat_official_article_request_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticleRequestSuccessNum'">
            qiye_wechat_official_article_request_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticleRequestFailNum'">
            qiye_wechat_official_article_request_fail_num ${vo.sort},
        </if>
        <if test="vo.order == 'sendSmsNum'">
            send_sms_num ${vo.sort},
        </if>
        <if test="vo.order == 'formSendSmsNum'">
            form_send_sms_num ${vo.sort},
        </if>
        <if test="vo.order == 'orderSendSmsNum'">
            order_send_sms_num ${vo.sort},
        </if>


        <if test="vo.order == 'formSubmitNum'">
            form_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'formSubmitRate'">
            form_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'clueFormSubmitNum'">
            clue_form_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'clueFormSubmitRate'">
            clue_form_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletNativeFormSubmitNum'">
            douyin_applet_native_form_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletNativeFormSubmitRate'">
            douyin_applet_native_form_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'formSubmitTotalNum'">
            form_submit_total_num ${vo.sort},
        </if>

        <if test="vo.order == 'formSubmitTotalRate'">
            form_submit_total_rate ${vo.sort},
        </if>

        <if test="vo.order == 'phoneNumberRecievedNum'">
            phone_number_recieved_num ${vo.sort},
        </if>

        <if test="vo.order == 'phoneNumberRecievedRate'">
            phone_number_recieved_rate ${vo.sort},
        </if>

        <if test="vo.order == 'popUpDisplayNum'">
            pop_up_display_num ${vo.sort},
        </if>

        <if test="vo.order == 'orderSubmitNum'">
            order_submit_num ${vo.sort},
        </if>
        <if test="vo.order == 'orderSubmitRate'">
            order_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'orderFinishNum'">
            order_finish_num ${vo.sort},
        </if>

        <if test="vo.order == 'orderFinishRate'">
            order_finish_rate ${vo.sort},
        </if>

        <if test="vo.order == 'onlineShopBuyGoodsSuccessNum'">
            online_shop_buy_goods_success_num ${vo.sort},
        </if>

        <if test="vo.order == 'onlineShopBuyGoodsSuccessRate'">
            online_shop_buy_goods_success_rate ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderSubmitNum'">
            douyin_applet_order_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderSubmitRate'">
            douyin_applet_order_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderFinishNum'">
            douyin_applet_order_finish_num ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderFinishRate'">
            douyin_applet_order_finish_rate ${vo.sort},
        </if>

        <if test="vo.order == 'comprehensivePaymentRate'">
            comprehensive_payment_rate ${vo.sort},
        </if>
        <if test="vo.order == 'elePvNum'">
            ele_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'eleQrCodeViewNum'">
            ele_qr_code_view_num ${vo.sort},
        </if>
        <if test="vo.order == 'eleIdentifyWechatQrCodeNum'">
            ele_identify_wechat_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'eleAddWechatSuccessNum'">
            ele_add_wechat_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappJumpNum'">
            whatsapp_jump_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappAddFriendSuccessNum'">
            whatsapp_add_friend_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappUserOpenMouthNum'">
            whatsapp_user_open_mouth_num ${vo.sort},
        </if>
        <if test="vo.order == 'overseasPvNum'">
            overseas_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappCustomerPrologueNum'">
            whatsapp_customer_prologue_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappCustomerSendMessageNum'">
            whatsapp_customer_send_message_num ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletJumpNum'">
            tao_bao_movie_applet_jump_num ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletJumpRate'">
            tao_bao_movie_applet_jump_rate ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletOrderNum'">
            tao_bao_movie_applet_order_num ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletOrderRate'">
            tao_bao_movie_applet_order_rate ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterAddCustomerServiceNum'">
            add_group_after_add_customer_service_num ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterAddCustomerServiceRate'">
            add_group_after_add_customer_service_rate ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterFollowOfficialAccountNum'">
            add_group_after_follow_official_account_num ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterFollowOfficialAccountRate'">
            add_group_after_follow_official_account_rate ${vo.sort},
        </if>
        <if test="vo.order == 'flowSourceJumpPageViewNum'">
            flow_source_jump_page_view_num ${vo.sort},
        </if>
        day_time desc
    </select>
    <select id="getTotalReportDayByAgentId" resultType="ai.yiye.agent.boss.vo.BossAccountGroupReportDayVo">
        with dayReport as (select
        baagdr.agent_id as agent_id,
        baagdr.day_time as day_time,
        COALESCE(sum(landing_page_pv), 0) as landing_page_pv,
        coalesce(sum(identify_qr_code_num), 0) as identify_qr_code_num,
        coalesce(sum(follow_official_account_num), 0) as follow_official_account_num,
        coalesce(sum(add_work_wechat_num), 0) as add_work_wechat_num,
        coalesce(sum(qiye_request_num), 0) as qiye_request_num,
        coalesce(sum(qiye_request_success_num), 0) as qiye_request_success_num,
        coalesce(sum(qiye_request_fail_num), 0) as qiye_request_fail_num,
        coalesce(sum(qiye_pv_num), 0) as qiye_pv_num,
        coalesce(sum(qiye_mini_pv_num), 0) as qiye_mini_pv_num,
        coalesce(sum(official_identify_qr_code_num), 0) as official_identify_qr_code_num,
        coalesce(sum(identify_group_qr_code_num), 0) as identify_group_qr_code_num,
        coalesce(sum(add_work_wechat_group_num), 0) as add_work_wechat_group_num,
        coalesce(sum(qiye_wechat_official_article_page_view_num),0) as qiye_wechat_official_article_page_view_num,
        coalesce(sum(qiye_wechat_official_article_request_num),0) as qiye_wechat_official_article_request_num,
        coalesce(sum(qiye_wechat_official_article_request_success_num),0) as qiye_wechat_official_article_request_success_num,
        coalesce(sum(qiye_wechat_official_article_request_fail_num),0) as qiye_wechat_official_article_request_fail_num,
        coalesce(sum(send_sms_num),0) as send_sms_num,
        coalesce(sum(form_send_sms_num),0) as form_send_sms_num,
        coalesce(sum(order_send_sms_num),0) as order_send_sms_num,

        <!---1.247.0新增字段-->
        coalesce(sum(form_submit_num),0) as form_submit_num,
        coalesce(sum(clue_form_submit_num),0) as clue_form_submit_num,
        coalesce(sum(douyin_applet_native_form_submit_num),0) as douyin_applet_native_form_submit_num,
        (coalesce(sum(form_submit_num),0) + coalesce(sum(clue_form_submit_num),0) + coalesce(sum(douyin_applet_native_form_submit_num),0) )as form_submit_total_num,
        coalesce(sum(phone_number_recieved_num),0) as phone_number_recieved_num,
        coalesce(sum(active_message_authorization_num),0) as active_message_authorization_num,
        coalesce(sum(pop_up_display_num),0) as pop_up_display_num,
        <!--1.256.0新增订单相关数据-->
        <!-- 1.263.0 饿了么小程序对接 -->
        coalesce(sum(ele_pv_num),0)as ele_pv_num,
        coalesce(sum(ele_qr_code_view_num),0) as ele_qr_code_view_num,
        coalesce(sum(ele_identify_wechat_qr_code_num),0) as ele_identify_wechat_qr_code_num,
        coalesce(sum(ele_add_wechat_success_num),0) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(sum(whatsapp_jump_num),0) as  whatsapp_jump_num,
        coalesce(sum(whatsapp_add_friend_success_num),0) as  whatsapp_add_friend_success_num,
        coalesce(sum(whatsapp_user_open_mouth_num),0) as  whatsapp_user_open_mouth_num,
        coalesce(sum(overseas_pv_num),0)as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        coalesce(sum(whatsapp_customer_prologue_num),0) as  whatsapp_customer_prologue_num,
        coalesce(sum(whatsapp_customer_send_message_num),0) as  whatsapp_customer_send_message_num,
        <!--1.256.0新增订单相关的数据-->
        coalesce(sum(order_submit_num),0) as order_submit_num,
        coalesce(sum(order_finish_num),0) as order_finish_num,
        coalesce(sum(online_shop_buy_goods_success_num),0) as online_shop_buy_goods_success_num,
        coalesce(sum(douyin_applet_order_submit_num),0) as douyin_applet_order_submit_num,
        coalesce(sum(douyin_applet_order_finish_num),0) as douyin_applet_order_finish_num,
        <!--1.264.0新增淘宝电影相关的数据-->
        coalesce(sum(tao_bao_movie_applet_jump_num),0) as tao_bao_movie_applet_jump_num,
        coalesce(sum(tao_bao_movie_applet_order_num),0) as tao_bao_movie_applet_order_num,

        coalesce(sum(add_group_after_add_customer_service_num),0) as add_group_after_add_customer_service_num,
        coalesce(sum(add_group_after_follow_official_account_num),0) as add_group_after_follow_official_account_num,
        coalesce(sum(flow_source_jump_page_view_num),0) as flow_source_jump_page_view_num

        from boss_advertiser_account_group_day_report baagdr
        where baagdr.agent_id = #{vo.agentId}
        and baagdr.day_time is not null
        <if test="vo.startTime != null and vo.endTime != null">
            and baagdr.day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by baagdr.agent_id,baagdr.day_time),
        unitPrice as (
        select
        coalesce((sum(bo.order_amount) / sum(bacd.combo_count)),0)
        as unit_price,
        baagdr.agent_id as agent_id
        from boss_advertiser_account_group_day_report baagdr
        left join (select agent_id,
        coalesce(sum(order_amount), 0) as order_amount,
        coalesce(sum(increase_pv), 0) as increase_pv
        from boss_order where order_status = 0 or order_status = 1 group by agent_id) bo on baagdr.agent_id = bo.agent_id
        left join boss_agent_conf_detail bacd on baagdr.agent_id = bacd.agent_id
        where baagdr.agent_id = #{vo.agentId}
        group by baagdr.agent_id
        ),
        totalCount as (
        select agent_id
        ,count(DISTINCT(day_time)) as total from boss_advertiser_account_group_day_report
        where agent_id=#{vo.agentId}
        <if test="vo.startTime != null and vo.endTime != null">
            and day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by agent_id
        ),
        <!--          计费投放账户数（巨量引擎） 授权投放账户消耗金额（巨量引擎 套餐量 套餐使用量 套餐余量-->
        consumeData as  (select day_time as day_time, sum(fund) as fund,max(bill_account_num) as bill_account_num,max(billing_proportion) as billing_proportion,agent_id  from boss_advertiser_account_fund_day_report baafdr
        group by day_time, agent_id
        )
        select dayReport.agent_id as agent_id,
        COALESCE(sum(dayReport.landing_page_pv),0) as landing_page_pv,
        coalesce(sum(dayReport.identify_qr_code_num),0) as identify_qr_code_num,
        coalesce(sum(dayReport.follow_official_account_num), 0) as follow_official_account_num,
        COALESCE(sum(dayReport.add_work_wechat_num),0) as add_work_wechat_num,
        coalesce(sum(dayReport.qiye_request_num), 0) as qiye_request_num,
        coalesce(sum(dayReport.qiye_request_success_num), 0) as qiye_request_success_num,
        coalesce(sum(dayReport.qiye_request_fail_num), 0) as qiye_request_fail_num,
        coalesce(sum(dayReport.qiye_pv_num), 0) as qiye_pv_num,
        coalesce(sum(dayReport.qiye_mini_pv_num), 0) as qiye_mini_pv_num,
        coalesce(sum(official_identify_qr_code_num), 0) as official_identify_qr_code_num,
        coalesce(sum(identify_group_qr_code_num), 0) as identify_group_qr_code_num,
        coalesce(sum(add_work_wechat_group_num), 0) as add_work_wechat_group_num,
        <!--以下为1.197.0新增三个率 -->
        case when COALESCE ( SUM ( dayReport.landing_page_pv ), 0 ) = 0 then 0 else coalesce( SUM (official_identify_qr_code_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100 end as official_identify_qr_code_rate,
        case when COALESCE ( SUM ( dayReport.landing_page_pv ), 0 ) = 0 then 0 else coalesce( SUM (identify_group_qr_code_num), 0) /    (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100end as identify_group_qr_code_rate,
        case when COALESCE ( SUM ( dayReport.landing_page_pv ), 0 ) = 0 then 0 else coalesce( SUM (add_work_wechat_group_num), 0) /     (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100 end as add_work_wechat_group_rate,
        coalesce(sum(qiye_wechat_official_article_page_view_num),0) as qiye_wechat_official_article_page_view_num,
        coalesce(sum(qiye_wechat_official_article_request_num),0) as qiye_wechat_official_article_request_num,
        coalesce(sum(qiye_wechat_official_article_request_success_num),0) as qiye_wechat_official_article_request_success_num,
        coalesce(sum(qiye_wechat_official_article_request_fail_num),0) as qiye_wechat_official_article_request_fail_num,
        coalesce(sum(send_sms_num),0) as send_sms_num,
        coalesce(sum(form_send_sms_num),0) as form_send_sms_num,
        coalesce(sum(order_send_sms_num),0) as order_send_sms_num,

        <!--1.247.0新增字段-->
        coalesce(sum(form_submit_num),0) as form_submit_num,
        coalesce(sum(clue_form_submit_num),0) as clue_form_submit_num,
        coalesce(sum(douyin_applet_native_form_submit_num),0) as douyin_applet_native_form_submit_num,
        coalesce(sum(phone_number_recieved_num),0) as phone_number_recieved_num,
        coalesce(sum(form_submit_total_num),0) as form_submit_total_num,
        coalesce(sum(active_message_authorization_num),0) as active_message_authorization_num,
        coalesce(sum(pop_up_display_num),0) as pop_up_display_num,

        case  when COALESCE(sum(dayReport.form_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(dayReport.form_submit_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as form_submit_rate,

        case  when COALESCE(sum(dayReport.clue_form_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(dayReport.clue_form_submit_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as clue_form_submit_rate,

        case  when COALESCE(sum(dayReport.phone_number_recieved_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(dayReport.phone_number_recieved_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as phone_number_recieved_rate,

        case  when COALESCE(sum(dayReport.douyin_applet_native_form_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(dayReport.douyin_applet_native_form_submit_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as douyin_applet_native_form_submit_rate,

        case  when COALESCE(sum(dayReport.active_message_authorization_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(dayReport.active_message_authorization_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as active_message_authorization_rate,

        case  when COALESCE(sum(dayReport.form_submit_total_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
              ELSE COALESCE(sum(dayReport.form_submit_total_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as form_submit_total_rate,
        <!---1.247.0-->
        <!-- 1.263.0 饿了么小程序对接 -->
        coalesce(sum(ele_pv_num),0)as ele_pv_num,
        coalesce(sum(ele_qr_code_view_num),0) as ele_qr_code_view_num,
        coalesce(sum(ele_identify_wechat_qr_code_num),0) as ele_identify_wechat_qr_code_num,
        coalesce(sum(ele_add_wechat_success_num),0) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(sum(whatsapp_jump_num),0) as  whatsapp_jump_num,
        coalesce(sum(whatsapp_add_friend_success_num),0) as  whatsapp_add_friend_success_num,
        coalesce(sum(whatsapp_user_open_mouth_num),0) as  whatsapp_user_open_mouth_num,
        coalesce(sum(overseas_pv_num),0)as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        coalesce(sum(whatsapp_customer_prologue_num),0) as  whatsapp_customer_prologue_num,
        coalesce(sum(whatsapp_customer_send_message_num),0) as  whatsapp_customer_send_message_num,
        <!--1.256.0新增订单相关的数据-->
        coalesce(sum(order_submit_num),0) as order_submit_num,
        coalesce(sum(order_finish_num),0) as order_finish_num,
        coalesce(sum(online_shop_buy_goods_success_num),0) as online_shop_buy_goods_success_num,
        coalesce(sum(douyin_applet_order_submit_num),0) as douyin_applet_order_submit_num,
        coalesce(sum(douyin_applet_order_finish_num),0) as douyin_applet_order_finish_num,

        case  when COALESCE(sum(dayReport.order_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
          ELSE COALESCE(sum(dayReport.order_submit_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
          end as order_submit_rate,

        case  when COALESCE(sum(dayReport.order_finish_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
          ELSE COALESCE(sum(dayReport.order_finish_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
          end as order_finish_rate,

        case  when COALESCE(sum(dayReport.online_shop_buy_goods_success_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
          ELSE COALESCE(sum(dayReport.online_shop_buy_goods_success_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
          end as online_shop_buy_goods_success_rate,

        case  when COALESCE(sum(dayReport.douyin_applet_order_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
          ELSE COALESCE(sum(dayReport.douyin_applet_order_submit_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
          end as douyin_applet_order_submit_rate,

        case  when COALESCE(sum(dayReport.douyin_applet_order_finish_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.douyin_applet_order_submit_num), 0) = CAST(0 AS NUMERIC) THEN 0
          ELSE COALESCE(sum(dayReport.douyin_applet_order_finish_num), 0) / (COALESCE(sum(dayReport.douyin_applet_order_submit_num), 0)::NUMERIC) * 100
          end as douyin_applet_order_finish_rate,

        case  when (COALESCE(sum(dayReport.order_finish_num), 0) + COALESCE(sum(dayReport.douyin_applet_order_finish_num), 0)) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
          ELSE (COALESCE(sum(dayReport.order_finish_num), 0) + COALESCE(sum(dayReport.douyin_applet_order_finish_num), 0)) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
          end as comprehensive_payment_rate,
        <!---1.256.0-->

        <!--1.264.0新增淘宝电影相关的数据-->
        coalesce(sum(tao_bao_movie_applet_jump_num),0) as tao_bao_movie_applet_jump_num,
        coalesce(sum(tao_bao_movie_applet_order_num),0) as tao_bao_movie_applet_order_num,
        case  when COALESCE(sum(dayReport.tao_bao_movie_applet_jump_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(dayReport.tao_bao_movie_applet_jump_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
            end as tao_bao_movie_applet_jump_rate,

        case  when COALESCE(sum(dayReport.tao_bao_movie_applet_order_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(dayReport.tao_bao_movie_applet_order_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
            end as tao_bao_movie_applet_order_rate,
        <!---1.264.0-->

        <!--1.271.0新增加群相关的数据-->
        coalesce(sum(add_group_after_add_customer_service_num),0) as add_group_after_add_customer_service_num,
        coalesce(sum(add_group_after_follow_official_account_num),0) as add_group_after_follow_official_account_num,

        case  when COALESCE(sum(dayReport.add_group_after_add_customer_service_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(dayReport.add_group_after_add_customer_service_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
            end as add_group_after_add_customer_service_rate,

        case  when COALESCE(sum(dayReport.add_group_after_follow_official_account_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(dayReport.add_group_after_follow_official_account_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
            end as add_group_after_follow_official_account_rate,
        <!---1.271.0-->

        case
        when COALESCE(sum(dayReport.identify_qr_code_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(dayReport.identify_qr_code_num), 0) /
        (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end
        as identify_qr_code_rate,
        case
        when COALESCE(sum(dayReport.follow_official_account_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(dayReport.follow_official_account_num), 0) /
        (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end
        as follow_official_account_rate,
        case
        when COALESCE(sum(dayReport.add_work_wechat_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(dayReport.add_work_wechat_num), 0) /
        (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end
        as add_work_wechat_rate,
        case
        when coalesce(sum(dayReport.add_work_wechat_num), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        (avg(unitPrice.unit_price) * COALESCE(sum(dayReport.landing_page_pv),0)) /
        (COALESCE(sum(dayReport.add_work_wechat_num),0)::NUMERIC)
        end as add_fans_cost,
        case
        when COALESCE(sum(dayReport.add_work_wechat_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(dayReport.identify_qr_code_num), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(dayReport.add_work_wechat_num), 0) / (COALESCE(sum(dayReport.identify_qr_code_num), 0)::NUMERIC) *
        100
        end
        as identify_qr_code_add_work_wechat_rate,
        avg(totalCount.total) as total,
        round(COALESCE(sum(landing_page_pv), 0) / count(1)::NUMERIC, 4) as day_avg_landing_page_pv,
        round(coalesce(sum(identify_qr_code_num), 0) / count(1)::NUMERIC, 4) as day_avg_identify_qr_code_num,
        round(coalesce(sum(follow_official_account_num), 0) / count(1)::NUMERIC, 4) as day_avg_follow_official_account_num,
        round(coalesce(sum(add_work_wechat_num), 0) / count(1)::NUMERIC, 4) as day_avg_add_work_wechat_num,
        coalesce(sum(cd.fund), 0) as billing_account_consume,
        coalesce(sum(cd.bill_account_num), 0) as billing_accounts,
        coalesce(sum((cd.fund * cd.billing_proportion) / 100), 0) as combo_account_consume,
        round(COALESCE(sum(cd.bill_account_num), 0) / count(1)::NUMERIC, 4) as day_avg_billing_accounts,
        round(coalesce(sum(cd.fund), 0) / count(1)::NUMERIC, 4) as day_avg_billing_account_consume,
        round(coalesce(sum((cd.fund * cd.billing_proportion) / 100), 0) / count(1)::NUMERIC, 4) as day_avg_combo_account_consume,
        coalesce(sum(dayReport.flow_source_jump_page_view_num),0) as flow_source_jump_page_view_num
        from dayReport
        left join unitPrice on unitPrice.agent_id = dayReport.agent_id
        left join totalCount on unitPrice.agent_id = totalCount.agent_id
        left join consumeData cd on dayReport.agent_id = cd.agent_id and dayReport.day_time = cd.day_time
        group by dayreport.agent_id
    </select>
    <select id="getTimeReportDayByAgentId" resultType="ai.yiye.agent.boss.vo.BossAccountGroupReportDayVo">
        with dayReport as (select baagdr.agent_id as agent_id,
        baagdr.advertiser_account_group_id as advertiser_account_group_id,
        baagdr.day_time as day_time,
        COALESCE(sum(landing_page_pv), 0) as landing_page_pv,
        coalesce(sum(identify_qr_code_num), 0) as identify_qr_code_num,
        coalesce(sum(add_work_wechat_num), 0) as add_work_wechat_num,
        coalesce(sum(follow_official_account_num), 0) as follow_official_account_num,
        coalesce(sum(qiye_request_num), 0) as qiye_request_num,
        coalesce(sum(qiye_request_success_num), 0) as qiye_request_success_num,
        coalesce(sum(qiye_request_fail_num), 0) as qiye_request_fail_num,
        coalesce(sum(qiye_pv_num), 0) as qiye_pv_num,
        coalesce(sum(qiye_mini_pv_num), 0) as qiye_mini_pv_num,
        coalesce(sum(official_identify_qr_code_num), 0) as official_identify_qr_code_num,
        coalesce(sum(identify_group_qr_code_num), 0) as identify_group_qr_code_num,
        coalesce(sum(add_work_wechat_group_num), 0) as add_work_wechat_group_num,
        coalesce(sum(qiye_wechat_official_article_page_view_num),0) as qiye_wechat_official_article_page_view_num,
        coalesce(sum(qiye_wechat_official_article_request_num),0) as qiye_wechat_official_article_request_num,
        coalesce(sum(qiye_wechat_official_article_request_success_num),0) as qiye_wechat_official_article_request_success_num,
        coalesce(sum(qiye_wechat_official_article_request_fail_num),0) as qiye_wechat_official_article_request_fail_num,
        coalesce(sum(send_sms_num),0) as send_sms_num,
        coalesce(sum(form_send_sms_num),0) as form_send_sms_num,
        coalesce(sum(order_send_sms_num),0) as order_send_sms_num,
        <!-- 1.263.0 饿了么小程序对接 -->
        coalesce(sum(ele_pv_num),0)as ele_pv_num,
        coalesce(sum(ele_qr_code_view_num),0) as ele_qr_code_view_num,
        coalesce(sum(ele_identify_wechat_qr_code_num),0) as ele_identify_wechat_qr_code_num,
        coalesce(sum(ele_add_wechat_success_num),0) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(sum(whatsapp_jump_num),0) as  whatsapp_jump_num,
        coalesce(sum(whatsapp_add_friend_success_num),0) as  whatsapp_add_friend_success_num,
        coalesce(sum(whatsapp_user_open_mouth_num),0) as  whatsapp_user_open_mouth_num,
        coalesce(sum(overseas_pv_num),0)as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        coalesce(sum(whatsapp_customer_prologue_num),0) as  whatsapp_customer_prologue_num,
        coalesce(sum(whatsapp_customer_send_message_num),0) as  whatsapp_customer_send_message_num,
        <!--1.247.0新增的字段-->
        coalesce(sum(form_submit_num),0) as form_submit_num,
        coalesce(sum(clue_form_submit_num),0) as clue_form_submit_num,
        coalesce(sum(douyin_applet_native_form_submit_num),0) as douyin_applet_native_form_submit_num,
        coalesce(sum(phone_number_recieved_num),0) as phone_number_recieved_num,
        (coalesce(sum(form_submit_num),0) + coalesce(sum(clue_form_submit_num),0) + coalesce(sum(douyin_applet_native_form_submit_num),0)) as form_submit_total_num,
        coalesce(sum(active_message_authorization_num),0) as active_message_authorization_num,
        coalesce(sum(pop_up_display_num),0) as pop_up_display_num,
        case  when COALESCE(sum(form_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
              ELSE COALESCE(sum(form_submit_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as form_submit_rate,

        case  when COALESCE(sum(phone_number_recieved_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(phone_number_recieved_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as phone_number_recieved_rate,

        case  when COALESCE(sum(clue_form_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
              ELSE COALESCE(sum(clue_form_submit_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as clue_form_submit_rate,

        case  when COALESCE(sum(douyin_applet_native_form_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
              ELSE COALESCE(sum(douyin_applet_native_form_submit_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as douyin_applet_native_form_submit_rate,

        case  when COALESCE(sum(active_message_authorization_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
              ELSE COALESCE(sum(active_message_authorization_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as active_message_authorization_rate,

        case  when  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
              ELSE (coalesce(sum(form_submit_num),0) + coalesce(sum(clue_form_submit_num),0) + coalesce(sum(douyin_applet_native_form_submit_num),0)) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as form_submit_total_rate,
        <!--1.247.0新增的字段-->

        <!--1.256.0新增订单相关字段-->
        coalesce(sum(order_submit_num),0) as order_submit_num,
        coalesce(sum(order_finish_num),0) as order_finish_num,
        coalesce(sum(online_shop_buy_goods_success_num),0) as online_shop_buy_goods_success_num,
        coalesce(sum(douyin_applet_order_submit_num),0) as douyin_applet_order_submit_num,
        coalesce(sum(douyin_applet_order_finish_num),0) as douyin_applet_order_finish_num,

        case  when COALESCE(sum(order_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
          ELSE COALESCE(sum(order_submit_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
          end as order_submit_rate,

        case  when COALESCE(sum(order_finish_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
          ELSE COALESCE(sum(order_finish_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
          end as order_finish_rate,

        case  when COALESCE(sum(online_shop_buy_goods_success_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
          ELSE COALESCE(sum(online_shop_buy_goods_success_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
          end as online_shop_buy_goods_success_rate,

        case  when COALESCE(sum(douyin_applet_order_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
          ELSE COALESCE(sum(douyin_applet_order_submit_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
          end as douyin_applet_order_submit_rate,

        case  when COALESCE(sum(douyin_applet_order_finish_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(douyin_applet_order_submit_num), 0) = CAST(0 AS NUMERIC) THEN 0
          ELSE COALESCE(sum(douyin_applet_order_finish_num), 0) / (COALESCE(sum(douyin_applet_order_submit_num), 0)::NUMERIC) * 100
          end as douyin_applet_order_finish_rate,

        case  when (COALESCE(sum(douyin_applet_order_finish_num), 0) + COALESCE(sum(order_finish_num), 0) ) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
          ELSE (COALESCE(sum(douyin_applet_order_finish_num), 0) + COALESCE(sum(order_finish_num), 0) ) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
          end as comprehensive_payment_rate,
        <!--1.256.0-->

        <!--1.264.0新增淘宝电影相关字段-->
        coalesce(sum(tao_bao_movie_applet_jump_num),0) as tao_bao_movie_applet_jump_num,
        coalesce(sum(tao_bao_movie_applet_order_num),0) as tao_bao_movie_applet_order_num,

        case  when COALESCE(sum(tao_bao_movie_applet_jump_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(tao_bao_movie_applet_jump_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
            end as tao_bao_movie_applet_jump_rate,

        case  when COALESCE(sum(tao_bao_movie_applet_order_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(tao_bao_movie_applet_order_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
            end as tao_bao_movie_applet_order_rate,
        <!--1.264.0-->

        <!--1.271.0新增加群相关字段-->
        coalesce(sum(add_group_after_add_customer_service_num),0) as add_group_after_add_customer_service_num,
        coalesce(sum(add_group_after_follow_official_account_num),0) as add_group_after_follow_official_account_num,

        case  when COALESCE(sum(add_group_after_add_customer_service_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(add_group_after_add_customer_service_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as add_group_after_add_customer_service_rate,

        case  when COALESCE(sum(add_group_after_follow_official_account_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(add_group_after_follow_official_account_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as add_group_after_follow_official_account_rate,
        <!--1.271.0-->
        coalesce(sum(flow_source_jump_page_view_num),0) as flow_source_jump_page_view_num,

        case
        when COALESCE(sum(identify_qr_code_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(identify_qr_code_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end
        as identify_qr_code_rate,
        case
        when COALESCE(sum(follow_official_account_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(follow_official_account_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end
        as follow_official_account_rate,
        case
        when COALESCE(sum(add_work_wechat_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(add_work_wechat_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end
        as add_work_wechat_rate,
        case
        when COALESCE(sum(add_work_wechat_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(identify_qr_code_num), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(add_work_wechat_num), 0) / (COALESCE(sum(identify_qr_code_num), 0)::NUMERIC) * 100
        end
        as identify_qr_code_add_work_wechat_rate,
        coalesce(sum(bill_account_num), 0) as billing_accounts,
        coalesce(sum(fund), 0) as billing_account_consume,
        coalesce(sum(billing_proportion), 0) as billing_proportion
        from boss_advertiser_account_group_day_report baagdr
        where baagdr.agent_id = #{vo.agentId}
        <if test="vo.startTime != null and vo.endTime != null">
            and baagdr.day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by baagdr.day_time,baagdr.advertiser_account_group_id,baagdr.agent_id
        ),
        unitPrice as (
        select
        coalesce((sum(bo.order_amount) / sum(bacd.combo_count)::NUMERIC),0)
        as unit_price,
        baagdr.agent_id as agent_id
        from boss_advertiser_account_group_day_report baagdr
        left join (select agent_id,
        coalesce(sum(order_amount), 0) as order_amount,
        coalesce(sum(increase_pv), 0) as increase_pv
        from boss_order where order_status = 0 or order_status = 1 group by agent_id) bo on baagdr.agent_id = bo.agent_id
        left join boss_agent_conf_detail bacd on baagdr.agent_id = bacd.agent_id
        where baagdr.agent_id = #{vo.agentId}
        group by baagdr.agent_id
        )
        select baag.advertiser_account_group_id as advertiser_account_group_id,
        baag.group_name as group_name,
        baag.replace_operation,
        dayReport.day_time,
        baag.agent_id as agent_id,
        coalesce(landing_page_pv, 0) as landing_page_pv,
        coalesce(identify_qr_code_num, 0) as identify_qr_code_num,
        coalesce(follow_official_account_num, 0) as follow_official_account_num,
        coalesce(add_work_wechat_num, 0) as add_work_wechat_num,
        coalesce(identify_qr_code_rate, 0) as identify_qr_code_rate,
        coalesce(follow_official_account_rate, 0) as follow_official_account_rate,
        coalesce(add_work_wechat_rate, 0) as add_work_wechat_rate,
        coalesce(identify_qr_code_add_work_wechat_rate, 0) as identify_qr_code_add_work_wechat_rate,
        coalesce(qiye_request_num, 0) as qiye_request_num,
        coalesce(qiye_request_success_num, 0) as qiye_request_success_num,
        coalesce(qiye_request_fail_num, 0) as qiye_request_fail_num,
        coalesce(qiye_pv_num, 0) as qiye_pv_num,
        coalesce(qiye_mini_pv_num, 0) as qiye_mini_pv_num,
        coalesce(official_identify_qr_code_num, 0) as official_identify_qr_code_num,
        coalesce(identify_group_qr_code_num, 0) as identify_group_qr_code_num,
        coalesce(add_work_wechat_group_num, 0) as add_work_wechat_group_num,
        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(official_identify_qr_code_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as official_identify_qr_code_rate,
        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(identify_group_qr_code_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as identify_group_qr_code_rate,
        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_work_wechat_group_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as add_work_wechat_group_rate,
        coalesce(dayReport.qiye_wechat_official_article_page_view_num,0) as qiye_wechat_official_article_page_view_num,
        coalesce(dayReport.qiye_wechat_official_article_request_num,0) as qiye_wechat_official_article_request_num,
        coalesce(dayReport.qiye_wechat_official_article_request_success_num,0) as qiye_wechat_official_article_request_success_num,
        coalesce(dayReport.qiye_wechat_official_article_request_fail_num,0) as qiye_wechat_official_article_request_fail_num,
        coalesce(dayReport.send_sms_num,0) as send_sms_num,
        coalesce(dayReport.form_send_sms_num,0) as form_send_sms_num,
        coalesce(dayReport.order_send_sms_num,0) as order_send_sms_num,
        <!-- 1.263.0 饿了么小程序对接 -->
        coalesce(dayReport.ele_pv_num,0)as ele_pv_num,
        coalesce(dayReport.ele_qr_code_view_num,0) as ele_qr_code_view_num,
        coalesce(dayReport.ele_identify_wechat_qr_code_num,0) as ele_identify_wechat_qr_code_num,
        coalesce(dayReport.ele_add_wechat_success_num,0) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(dayReport.whatsapp_jump_num,0) as  whatsapp_jump_num,
        coalesce(dayReport.whatsapp_add_friend_success_num,0) as  whatsapp_add_friend_success_num,
        coalesce(dayReport.whatsapp_user_open_mouth_num,0) as  whatsapp_user_open_mouth_num,
        coalesce(dayReport.overseas_pv_num,0)as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        coalesce(dayReport.whatsapp_customer_prologue_num,0) as  whatsapp_customer_prologue_num,
        coalesce(dayReport.whatsapp_customer_send_message_num,0) as  whatsapp_customer_send_message_num,
        <!--1.247.0新增字段-->
        coalesce(dayReport.form_submit_num,0) as form_submit_num,
        coalesce(dayReport.clue_form_submit_num,0) as clue_form_submit_num,
        coalesce(dayReport.douyin_applet_native_form_submit_num,0) as douyin_applet_native_form_submit_num,
        coalesce(dayReport.phone_number_recieved_num,0) as phone_number_recieved_num,
        coalesce(dayReport.form_submit_total_num,0) as form_submit_total_num,
        coalesce(dayReport.active_message_authorization_num,0) as active_message_authorization_num,
        coalesce(dayReport.pop_up_display_num,0) as pop_up_display_num,

        coalesce(form_submit_rate, 0) as form_submit_rate,
        coalesce(clue_form_submit_rate, 0) as clue_form_submit_rate,
        coalesce(douyin_applet_native_form_submit_rate, 0) as douyin_applet_native_form_submit_rate,
        coalesce(active_message_authorization_rate, 0) as active_message_authorization_rate,
        coalesce(form_submit_total_rate, 0) as form_submit_total_rate,
        coalesce(phone_number_recieved_rate, 0) as phone_number_recieved_rate,
        <!--1.247.0新增字段-->

        <!--1.256.0新增订单相关的字段-->
        coalesce(dayReport.order_submit_num,0) as order_submit_num,
        coalesce(dayReport.order_finish_num,0) as order_finish_num,
        coalesce(dayReport.online_shop_buy_goods_success_num,0) as online_shop_buy_goods_success_num,
        coalesce(dayReport.douyin_applet_order_submit_num,0) as douyin_applet_order_submit_num,
        coalesce(dayReport.douyin_applet_order_finish_num,0) as douyin_applet_order_finish_num,

        coalesce(order_submit_rate, 0) as order_submit_rate,
        coalesce(order_finish_rate, 0) as order_finish_rate,
        coalesce(online_shop_buy_goods_success_rate, 0) as online_shop_buy_goods_success_rate,
        coalesce(douyin_applet_order_submit_rate, 0) as douyin_applet_order_submit_rate,
        coalesce(douyin_applet_order_finish_rate, 0) as douyin_applet_order_finish_rate,
        coalesce(comprehensive_payment_rate, 0) as comprehensive_payment_rate,
        <!--1.256.0-->

        <!--1.264.0新增淘宝电影相关的字段-->
        coalesce(tao_bao_movie_applet_jump_num, 0) as tao_bao_movie_applet_jump_num,
        coalesce(tao_bao_movie_applet_order_num, 0) as tao_bao_movie_applet_order_num,
        coalesce(tao_bao_movie_applet_jump_rate, 0) as tao_bao_movie_applet_jump_rate,
        coalesce(tao_bao_movie_applet_order_rate, 0) as tao_bao_movie_applet_order_rate,
        <!--1.264.0-->

        <!--1.271.0新增加群相关的字段-->
        coalesce(add_group_after_add_customer_service_num, 0) as add_group_after_add_customer_service_num,
        coalesce(add_group_after_follow_official_account_num, 0) as add_group_after_follow_official_account_num,
        coalesce(add_group_after_add_customer_service_rate, 0) as add_group_after_add_customer_service_rate,
        coalesce(add_group_after_follow_official_account_rate, 0) as add_group_after_follow_official_account_rate,
        <!--1.271.0-->
        coalesce(dayReport.flow_source_jump_page_view_num, 0) as flow_source_jump_page_view_num,

        case
        when coalesce(add_work_wechat_num, 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        (unitPrice.unit_price * dayReport.landing_page_pv) / (add_work_wechat_num::NUMERIC)
        end as add_fans_cost,
        coalesce(billing_accounts, 0) as billing_accounts,
        coalesce(billing_account_consume, 0) as billing_account_consume,
        coalesce(billing_proportion, 0) as billing_proportion,
        coalesce((billing_account_consume * billing_proportion) / 100, 0) as combo_account_consume
        from boss_advertiser_account_group baag
        left join dayReport on baag.advertiser_account_group_id=dayReport.advertiser_account_group_id
        left join unitPrice on unitPrice.agent_id = baag.agent_id
        where baag.agent_id = #{vo.agentId}
        <if test="vo.groupName != null and '' != vo.groupName">
            and baag.group_name like concat('%', #{vo.groupName}, '%')
        </if>

        <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@DEFAULT">
            and baag.replace_operation = 0
        </if>
        <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@OPERATION">
            and baag.replace_operation = 1
        </if>
        <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@STOP_OPERATION">
            and baag.replace_operation = 2
        </if>
        order by
        <if test="vo.order == 'dayTime'">
            day_time ${vo.sort},
        </if>
        <if test="vo.order == 'landingPagePv'">
            landing_page_pv ${vo.sort},
        </if>
        <if test="vo.order == 'identifyQrCodeNum'">
            identify_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatNum'">
            add_work_wechat_num ${vo.sort},
        </if>
        <if test="vo.order == 'identifyQrCodeRate'">
            identify_qr_code_rate ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatRate'">
            add_work_wechat_rate ${vo.sort},
        </if>
        <if test="vo.order == 'addFansCost'">
            add_fans_cost ${vo.sort},
        </if>
        <if test="vo.order == 'identifyQrCodeAddWorkWechatRate'">
            identify_qr_code_add_work_wechat_rate ${vo.sort},
        </if>
        <if test="vo.order == 'followOfficialAccountNum'">
            follow_official_account_num ${vo.sort},
        </if>
        <if test="vo.order == 'followOfficialAccountNum'">
            follow_official_account_num ${vo.sort},
        </if>
        <if test="vo.order == 'followOfficialAccountRate'">
            follow_official_account_rate ${vo.sort},
        </if>
        <if test="vo.order == 'billingAccounts'">
            billing_accounts ${vo.sort} nulls last,
        </if>
        <if test="vo.order == 'billingAccountConsume'">
            billing_account_consume ${vo.sort} nulls last,
        </if>
        <if test="vo.order == 'comboAccountConsume'">
            combo_account_consume ${vo.sort} nulls last,
        </if>
        <if test="vo.order == 'qiyeRequestNum'">
            qiye_request_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeRequestSuccessNum'">
            qiye_request_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeRequestFailNum'">
            qiye_request_fail_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyePvNum'">
            qiye_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeMiniPvNum'">
            qiye_mini_pv_num ${vo.sort},
        </if>
        <!--以下为1.197.0新增三个排序 -->

        <if test="vo.order == 'officialIdentifyQrCodeNum'">
            official_identify_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'identifyGroupQrCodeNum'">
            identify_group_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatGroupNum'">
            add_work_wechat_group_num ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatGroupRate'">
            add_work_wechat_group_rate ${vo.sort},
        </if>
        <if test="vo.order == 'identifyGroupQrCodeRate'">
            identify_group_qr_code_rate ${vo.sort},
        </if>
        <if test="vo.order == 'officialIdentifyQrCodeRate'">
            official_identify_qr_code_rate ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticlePageViewNum'">
            dayReport.qiye_wechat_official_article_page_view_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticleRequestNum'">
            dayReport.qiye_wechat_official_article_request_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticleRequestSuccessNum'">
            dayReport.qiye_wechat_official_article_request_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticleRequestFailNum'">
            dayReport.qiye_wechat_official_article_request_fail_num ${vo.sort},
        </if>
        <if test="vo.order == 'sendSmsNum'">
            send_sms_num ${vo.sort},
        </if>
        <if test="vo.order == 'formSendSmsNum'">
            form_send_sms_num ${vo.sort},
        </if>
        <if test="vo.order == 'orderSendSmsNum'">
            order_send_sms_num ${vo.sort},
        </if>
        <!--1.247.0新增字段-->
        <if test="vo.order == 'formSubmitNum'">
            form_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'formSubmitRate'">
            form_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'clueFormSubmitNum'">
            clue_form_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'clueFormSubmitRate'">
            clue_form_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletNativeFormSubmitNum'">
            douyin_applet_native_form_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletNativeFormSubmitRate'">
            douyin_applet_native_form_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'formSubmitTotalNum'">
            form_submit_total_num ${vo.sort},
        </if>

        <if test="vo.order == 'formSubmitTotalRate'">
            form_submit_total_rate ${vo.sort},
        </if>

        <if test="vo.order == 'phoneNumberRecievedNum'">
            phone_number_recieved_num ${vo.sort},
        </if>

        <if test="vo.order == 'phoneNumberRecievedRate'">
            phone_number_recieved_rate ${vo.sort},
        </if>

        <if test="vo.order == 'popUpDisplayNum'">
            pop_up_display_num ${vo.sort},
        </if>

        <if test="vo.order == 'orderSubmitNum'">
            order_submit_num ${vo.sort},
        </if>
        <if test="vo.order == 'orderSubmitRate'">
            order_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'orderFinishNum'">
            order_finish_num ${vo.sort},
        </if>

        <if test="vo.order == 'orderFinishRate'">
            order_finish_rate ${vo.sort},
        </if>

        <if test="vo.order == 'onlineShopBuyGoodsSuccessNum'">
            online_shop_buy_goods_success_num ${vo.sort},
        </if>

        <if test="vo.order == 'onlineShopBuyGoodsSuccessRate'">
            online_shop_buy_goods_success_rate ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderSubmitNum'">
            douyin_applet_order_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderSubmitRate'">
            douyin_applet_order_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderFinishNum'">
            douyin_applet_order_finish_num ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderFinishRate'">
            douyin_applet_order_finish_rate ${vo.sort},
        </if>

        <if test="vo.order == 'comprehensivePaymentRate'">
            comprehensive_payment_rate ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletJumpNum'">
            tao_bao_movie_applet_jump_num ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletJumpRate'">
            tao_bao_movie_applet_order_num ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletOrderNum'">
            tao_bao_movie_applet_jump_rate ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletOrderRate'">
            tao_bao_movie_applet_order_rate ${vo.sort},
        </if>

        <if test="vo.order == 'elePvNum'">
            ele_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'eleQrCodeViewNum'">
            ele_qr_code_view_num ${vo.sort},
        </if>
        <if test="vo.order == 'eleIdentifyWechatQrCodeNum'">
            ele_identify_wechat_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'eleAddWechatSuccessNum'">
            ele_add_wechat_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappJumpNum'">
            whatsapp_jump_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappAddFriendSuccessNum'">
            whatsapp_add_friend_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappUserOpenMouthNum'">
            whatsapp_user_open_mouth_num ${vo.sort},
        </if>
        <if test="vo.order == 'overseasPvNum'">
            overseas_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappCustomerPrologueNum'">
            whatsapp_customer_prologue_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappCustomerSendMessageNum'">
            whatsapp_customer_send_message_num ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterAddCustomerServiceNum'">
            add_group_after_add_customer_service_num ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterAddCustomerServiceRate'">
            add_group_after_add_customer_service_rate ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterFollowOfficialAccountNum'">
            add_group_after_follow_official_account_num ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterFollowOfficialAccountRate'">
            add_group_after_follow_official_account_rate ${vo.sort},
        </if>
        <if test="vo.order == 'flowSourceJumpPageViewNum'">
            flow_source_jump_page_view_num ${vo.sort},
        </if>
        landing_page_pv desc
    </select>

    <!--2.0统计，从clickhouse聚合过来的数据-->
    <select id="getTotalReportDayByAgentIdNew" resultType="ai.yiye.agent.boss.vo.BossAccountGroupReportDayVo">
        with dayReport as (select
        baagdr.agent_id as agent_id,
        baagdr.day_time as day_time,
        COALESCE(sum(landing_page_pv), 0) as landing_page_pv,
        coalesce(sum(identify_qr_code_num), 0) as identify_qr_code_num,
        coalesce(sum(follow_official_account_num), 0) as follow_official_account_num,
        coalesce(sum(add_work_wechat_num), 0) as add_work_wechat_num,
        coalesce(sum(qiye_request_num), 0) as qiye_request_num,
        coalesce(sum(qiye_request_success_num), 0) as qiye_request_success_num,
        coalesce(sum(qiye_request_fail_num), 0) as qiye_request_fail_num,
        coalesce(sum(qiye_pv_num), 0) as qiye_pv_num,
        coalesce(sum(qiye_mini_pv_num), 0) as qiye_mini_pv_num,
        coalesce(sum(official_identify_qr_code_num), 0) as official_identify_qr_code_num,
        coalesce(sum(identify_group_qr_code_num), 0) as identify_group_qr_code_num,
        coalesce(sum(add_work_wechat_group_num), 0) as add_work_wechat_group_num,
        coalesce(sum(qiye_wechat_official_article_page_view_num),0) as qiye_wechat_official_article_page_view_num,
        coalesce(sum(qiye_wechat_official_article_request_num),0) as qiye_wechat_official_article_request_num,
        coalesce(sum(qiye_wechat_official_article_request_success_num),0) as qiye_wechat_official_article_request_success_num,
        coalesce(sum(qiye_wechat_official_article_request_fail_num),0) as qiye_wechat_official_article_request_fail_num,
        coalesce(sum(send_sms_num),0) as send_sms_num,
        coalesce(sum(form_send_sms_num),0) as form_send_sms_num,
        coalesce(sum(order_send_sms_num),0) as order_send_sms_num,

        <!---1.247.0新增字段-->
        coalesce(sum(form_submit_num),0) as form_submit_num,
        coalesce(sum(clue_form_submit_num),0) as clue_form_submit_num,
        coalesce(sum(douyin_applet_native_form_submit_num),0) as douyin_applet_native_form_submit_num,
        (coalesce(sum(form_submit_num),0) + coalesce(sum(clue_form_submit_num),0) + coalesce(sum(douyin_applet_native_form_submit_num),0) )as form_submit_total_num,
        coalesce(sum(phone_number_recieved_num),0) as phone_number_recieved_num,
        coalesce(sum(active_message_authorization_num),0) as active_message_authorization_num,
        coalesce(sum(pop_up_display_num),0) as pop_up_display_num,
        <!-- 1.263.0 饿了么小程序对接 -->
        coalesce(sum(ele_pv_num),0)as ele_pv_num,
        coalesce(sum(ele_qr_code_view_num),0) as ele_qr_code_view_num,
        coalesce(sum(ele_identify_wechat_qr_code_num),0) as ele_identify_wechat_qr_code_num,
        coalesce(sum(ele_add_wechat_success_num),0) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(sum(whatsapp_jump_num),0) as  whatsapp_jump_num,
        coalesce(sum(whatsapp_add_friend_success_num),0) as  whatsapp_add_friend_success_num,
        coalesce(sum(whatsapp_user_open_mouth_num),0) as  whatsapp_user_open_mouth_num,
        coalesce(sum(overseas_pv_num),0)as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        coalesce(sum(whatsapp_customer_prologue_num),0) as  whatsapp_customer_prologue_num,
        coalesce(sum(whatsapp_customer_send_message_num),0) as  whatsapp_customer_send_message_num,
        <!--1.256.0新增订单相关的数据-->
        coalesce(sum(order_submit_num),0) as order_submit_num,
        coalesce(sum(order_finish_num),0) as order_finish_num,
        coalesce(sum(online_shop_buy_goods_success_num),0) as online_shop_buy_goods_success_num,
        coalesce(sum(douyin_applet_order_submit_num),0) as douyin_applet_order_submit_num,
        coalesce(sum(douyin_applet_order_finish_num),0) as douyin_applet_order_finish_num,

        <!--1.264.0新增淘宝电影相关的数据-->
        coalesce(sum(tao_bao_movie_applet_jump_num),0) as tao_bao_movie_applet_jump_num,
        coalesce(sum(tao_bao_movie_applet_order_num),0) as tao_bao_movie_applet_order_num,

        <!--1.271.0-->
        coalesce(sum(add_group_after_add_customer_service_num),0) as add_group_after_add_customer_service_num,
        coalesce(sum(add_group_after_follow_official_account_num),0) as add_group_after_follow_official_account_num,
        coalesce(sum(flow_source_jump_page_view_num),0) as flow_source_jump_page_view_num

        from boss_advertiser_account_group_day_report_new baagdr
        where baagdr.agent_id = #{vo.agentId}
        and baagdr.day_time is not null
        <if test="vo.startTime != null and vo.endTime != null">
            and baagdr.day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by baagdr.agent_id,baagdr.day_time),
        unitPrice as (
        select
        coalesce((sum(bo.order_amount) / sum(bacd.combo_count)),0)
        as unit_price,
        baagdr.agent_id as agent_id
        from boss_advertiser_account_group_day_report_new baagdr
        left join (select agent_id,
        coalesce(sum(order_amount), 0) as order_amount,
        coalesce(sum(increase_pv), 0) as increase_pv
        from boss_order where order_status = 0 or order_status = 1 group by agent_id) bo on baagdr.agent_id = bo.agent_id
        left join boss_agent_conf_detail bacd on baagdr.agent_id = bacd.agent_id
        where baagdr.agent_id = #{vo.agentId}
        group by baagdr.agent_id
        ),
        totalCount as (
        select agent_id
        ,count(DISTINCT(day_time)) as total from boss_advertiser_account_group_day_report_new
        where agent_id=#{vo.agentId}
        <if test="vo.startTime != null and vo.endTime != null">
            and day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by agent_id
        ),
        <!--          计费投放账户数（巨量引擎） 授权投放账户消耗金额（巨量引擎 套餐量 套餐使用量 套餐余量-->
        consumeData as  (select day_time as day_time, sum(fund) as fund,max(bill_account_num) as bill_account_num,max(billing_proportion) as billing_proportion,agent_id  from boss_advertiser_account_fund_day_report baafdr
        group by day_time, agent_id
        )
        select dayReport.agent_id as agent_id,
        COALESCE(sum(dayReport.landing_page_pv),0) as landing_page_pv,
        coalesce(sum(dayReport.identify_qr_code_num),0) as identify_qr_code_num,
        coalesce(sum(dayReport.follow_official_account_num), 0) as follow_official_account_num,
        COALESCE(sum(dayReport.add_work_wechat_num),0) as add_work_wechat_num,
        coalesce(sum(dayReport.qiye_request_num), 0) as qiye_request_num,
        coalesce(sum(dayReport.qiye_request_success_num), 0) as qiye_request_success_num,
        coalesce(sum(dayReport.qiye_request_fail_num), 0) as qiye_request_fail_num,
        coalesce(sum(dayReport.qiye_pv_num), 0) as qiye_pv_num,
        coalesce(sum(dayReport.qiye_mini_pv_num), 0) as qiye_mini_pv_num,
        coalesce(sum(official_identify_qr_code_num), 0) as official_identify_qr_code_num,
        coalesce(sum(identify_group_qr_code_num), 0) as identify_group_qr_code_num,
        coalesce(sum(add_work_wechat_group_num), 0) as add_work_wechat_group_num,
        <!--以下为1.197.0新增三个率 -->
        case when COALESCE ( SUM ( dayReport.landing_page_pv ), 0 ) = 0 then 0 else coalesce( SUM (official_identify_qr_code_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100 end as official_identify_qr_code_rate,
        case when COALESCE ( SUM ( dayReport.landing_page_pv ), 0 ) = 0 then 0 else coalesce( SUM (identify_group_qr_code_num), 0) /    (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100end as identify_group_qr_code_rate,
        case when COALESCE ( SUM ( dayReport.landing_page_pv ), 0 ) = 0 then 0 else coalesce( SUM (add_work_wechat_group_num), 0) /     (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100 end as add_work_wechat_group_rate,
        coalesce(sum(qiye_wechat_official_article_page_view_num),0) as qiye_wechat_official_article_page_view_num,
        coalesce(sum(qiye_wechat_official_article_request_num),0) as qiye_wechat_official_article_request_num,
        coalesce(sum(qiye_wechat_official_article_request_success_num),0) as qiye_wechat_official_article_request_success_num,
        coalesce(sum(qiye_wechat_official_article_request_fail_num),0) as qiye_wechat_official_article_request_fail_num,
        coalesce(sum(send_sms_num),0) as send_sms_num,
        coalesce(sum(form_send_sms_num),0) as form_send_sms_num,
        coalesce(sum(order_send_sms_num),0) as order_send_sms_num,

        <!--1.247.0新增字段-->
        coalesce(sum(form_submit_num),0) as form_submit_num,
        coalesce(sum(clue_form_submit_num),0) as clue_form_submit_num,
        coalesce(sum(douyin_applet_native_form_submit_num),0) as douyin_applet_native_form_submit_num,
        coalesce(sum(phone_number_recieved_num),0) as phone_number_recieved_num,
        coalesce(sum(form_submit_total_num),0) as form_submit_total_num,
        coalesce(sum(active_message_authorization_num),0) as active_message_authorization_num,
        coalesce(sum(pop_up_display_num),0) as pop_up_display_num,

        case  when COALESCE(sum(dayReport.form_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(dayReport.form_submit_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as form_submit_rate,

        case  when COALESCE(sum(dayReport.clue_form_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(dayReport.clue_form_submit_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as clue_form_submit_rate,

        case  when COALESCE(sum(dayReport.phone_number_recieved_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(dayReport.phone_number_recieved_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as phone_number_recieved_rate,

        case  when COALESCE(sum(dayReport.douyin_applet_native_form_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(dayReport.douyin_applet_native_form_submit_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as douyin_applet_native_form_submit_rate,

        case  when COALESCE(sum(dayReport.active_message_authorization_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(dayReport.active_message_authorization_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as active_message_authorization_rate,

        case  when COALESCE(sum(dayReport.form_submit_total_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(dayReport.form_submit_total_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as form_submit_total_rate,
        <!---1.247.0-->
        <!-- 1.263.0 饿了么小程序对接 -->
        coalesce(sum(dayReport.ele_pv_num),0)as ele_pv_num,
        coalesce(sum(dayReport.ele_qr_code_view_num),0) as ele_qr_code_view_num,
        coalesce(sum(dayReport.ele_identify_wechat_qr_code_num),0) as ele_identify_wechat_qr_code_num,
        coalesce(sum(dayReport.ele_add_wechat_success_num),0) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(sum(dayReport.whatsapp_jump_num),0) as  whatsapp_jump_num,
        coalesce(sum(dayReport.whatsapp_add_friend_success_num),0) as  whatsapp_add_friend_success_num,
        coalesce(sum(dayReport.whatsapp_user_open_mouth_num),0) as  whatsapp_user_open_mouth_num,
        coalesce(sum(dayReport.overseas_pv_num),0)as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        coalesce(sum(whatsapp_customer_prologue_num),0) as  whatsapp_customer_prologue_num,
        coalesce(sum(whatsapp_customer_send_message_num),0) as  whatsapp_customer_send_message_num,
        <!--1.256.0新增订单相关的数据-->
        coalesce(sum(order_submit_num),0) as order_submit_num,
        coalesce(sum(order_finish_num),0) as order_finish_num,
        coalesce(sum(online_shop_buy_goods_success_num),0) as online_shop_buy_goods_success_num,
        coalesce(sum(douyin_applet_order_submit_num),0) as douyin_applet_order_submit_num,
        coalesce(sum(douyin_applet_order_finish_num),0) as douyin_applet_order_finish_num,

        case  when COALESCE(sum(dayReport.order_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(dayReport.order_submit_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as order_submit_rate,

        case  when COALESCE(sum(dayReport.order_finish_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(dayReport.order_finish_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as order_finish_rate,

        case  when COALESCE(sum(dayReport.online_shop_buy_goods_success_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(dayReport.online_shop_buy_goods_success_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as online_shop_buy_goods_success_rate,

        case  when COALESCE(sum(dayReport.douyin_applet_order_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(dayReport.douyin_applet_order_submit_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as douyin_applet_order_submit_rate,

        case  when COALESCE(sum(dayReport.douyin_applet_order_finish_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.douyin_applet_order_submit_num), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(dayReport.douyin_applet_order_finish_num), 0) / (COALESCE(sum(dayReport.douyin_applet_order_submit_num), 0)::NUMERIC) * 100
        end as douyin_applet_order_finish_rate,

        case  when (COALESCE(sum(dayReport.order_finish_num), 0) + COALESCE(sum(dayReport.douyin_applet_order_finish_num), 0)) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE (COALESCE(sum(dayReport.order_finish_num), 0) + COALESCE(sum(dayReport.douyin_applet_order_finish_num), 0)) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end as comprehensive_payment_rate,
        <!---1.256.0-->

        <!--1.264.0新增淘宝电影相关的数据-->
        coalesce(sum(tao_bao_movie_applet_jump_num),0) as tao_bao_movie_applet_jump_num,
        coalesce(sum(tao_bao_movie_applet_order_num),0) as tao_bao_movie_applet_order_num,

        case  when COALESCE(sum(dayReport.tao_bao_movie_applet_jump_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(dayReport.tao_bao_movie_applet_jump_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
            end as tao_bao_movie_applet_jump_rate,

        case  when COALESCE(sum(dayReport.tao_bao_movie_applet_order_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(dayReport.tao_bao_movie_applet_order_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
            end as tao_bao_movie_applet_order_rate,
        <!---1.264.0-->

        <!--1.271.0新增加群相关的数据-->
        coalesce(sum(add_group_after_add_customer_service_num),0) as add_group_after_add_customer_service_num,
        coalesce(sum(add_group_after_follow_official_account_num),0) as add_group_after_follow_official_account_num,

        case  when COALESCE(sum(dayReport.add_group_after_add_customer_service_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
         ELSE COALESCE(sum(dayReport.add_group_after_add_customer_service_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
         end as add_group_after_add_customer_service_rate,

        case  when COALESCE(sum(dayReport.add_group_after_follow_official_account_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
         ELSE COALESCE(sum(dayReport.add_group_after_follow_official_account_num), 0) / (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
         end as add_group_after_follow_official_account_rate,
        <!---1.271.0-->
        case
        when COALESCE(sum(dayReport.identify_qr_code_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(dayReport.identify_qr_code_num), 0) /
        (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end
        as identify_qr_code_rate,
        case
        when COALESCE(sum(dayReport.follow_official_account_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(dayReport.follow_official_account_num), 0) /
        (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end
        as follow_official_account_rate,
        case
        when COALESCE(sum(dayReport.add_work_wechat_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(dayReport.landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(dayReport.add_work_wechat_num), 0) /
        (COALESCE(sum(dayReport.landing_page_pv), 0)::NUMERIC) * 100
        end
        as add_work_wechat_rate,
        case
        when coalesce(sum(dayReport.add_work_wechat_num), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        (avg(unitPrice.unit_price) * COALESCE(sum(dayReport.landing_page_pv),0)) /
        (COALESCE(sum(dayReport.add_work_wechat_num),0)::NUMERIC)
        end as add_fans_cost,
        case
        when COALESCE(sum(dayReport.add_work_wechat_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(dayReport.identify_qr_code_num), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(dayReport.add_work_wechat_num), 0) / (COALESCE(sum(dayReport.identify_qr_code_num), 0)::NUMERIC) *
        100
        end
        as identify_qr_code_add_work_wechat_rate,
        avg(totalCount.total) as total,
        round(COALESCE(sum(landing_page_pv), 0) / count(1)::NUMERIC, 4) as day_avg_landing_page_pv,
        round(coalesce(sum(identify_qr_code_num), 0) / count(1)::NUMERIC, 4) as day_avg_identify_qr_code_num,
        round(coalesce(sum(follow_official_account_num), 0) / count(1)::NUMERIC, 4) as day_avg_follow_official_account_num,
        round(coalesce(sum(add_work_wechat_num), 0) / count(1)::NUMERIC, 4) as day_avg_add_work_wechat_num,
        coalesce(sum(cd.fund), 0) as billing_account_consume,
        coalesce(sum(cd.bill_account_num), 0) as billing_accounts,
        coalesce(sum((cd.fund * cd.billing_proportion) / 100), 0) as combo_account_consume,
        round(COALESCE(sum(cd.bill_account_num), 0) / count(1)::NUMERIC, 4) as day_avg_billing_accounts,
        round(coalesce(sum(cd.fund), 0) / count(1)::NUMERIC, 4) as day_avg_billing_account_consume,
        round(coalesce(sum((cd.fund * cd.billing_proportion) / 100), 0) / count(1)::NUMERIC, 4) as day_avg_combo_account_consume,
        coalesce(sum(dayReport.flow_source_jump_page_view_num),0) as flow_source_jump_page_view_num
        from dayReport
        left join unitPrice on unitPrice.agent_id = dayReport.agent_id
        left join totalCount on unitPrice.agent_id = totalCount.agent_id
        left join consumeData cd on dayReport.agent_id = cd.agent_id and dayReport.day_time = cd.day_time
        group by dayreport.agent_id
    </select>


    <!--2.0统计，从clickhouse聚合过来的数据-->
    <select id="getReportDayByAgentIdNew" resultType="ai.yiye.agent.boss.vo.BossAccountGroupReportDayVo">
        with dayReport as (select day_time as day_time,
        baagdr.agent_id as agent_id,
        COALESCE(sum(landing_page_pv), 0) as landing_page_pv,
        coalesce(sum(identify_qr_code_num), 0) as identify_qr_code_num,
        coalesce(sum(add_work_wechat_num), 0) as add_work_wechat_num,
        coalesce(sum(follow_official_account_num), 0) as follow_official_account_num,
        coalesce(sum(qiye_request_num), 0) as qiye_request_num,
        coalesce(sum(qiye_request_success_num), 0) as qiye_request_success_num,
        coalesce(sum(qiye_request_fail_num), 0) as qiye_request_fail_num,
        coalesce(sum(qiye_pv_num), 0) as qiye_pv_num,
        coalesce(sum(qiye_mini_pv_num), 0) as qiye_mini_pv_num,
        coalesce(sum(official_identify_qr_code_num), 0) as official_identify_qr_code_num,
        coalesce(sum(identify_group_qr_code_num), 0) as identify_group_qr_code_num,
        coalesce(sum(add_work_wechat_group_num), 0) as add_work_wechat_group_num,
        <!--以下为1.197.0新增三个率 -->
        CASE WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN
        0 ELSE SUM ( COALESCE ( official_identify_qr_code_num, 0 ) ) / ( COALESCE ( SUM ( landing_page_pv ), 0 ) :: NUMERIC ) * 100
        END AS official_identify_qr_code_rate,
        CASE  WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN
        0 ELSE SUM ( COALESCE ( identify_group_qr_code_num, 0 ) ) / ( COALESCE ( SUM ( landing_page_pv ), 0 ) :: NUMERIC ) * 100
        END AS identify_group_qr_code_rate,
        CASE WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN
        0 ELSE SUM ( COALESCE ( add_work_wechat_group_num, 0 ) ) /  ( COALESCE ( SUM ( landing_page_pv ), 0 ) :: NUMERIC ) * 100
        END AS add_work_wechat_group_rate,
        coalesce(sum(qiye_wechat_official_article_page_view_num),0) as qiye_wechat_official_article_page_view_num,
        coalesce(sum(qiye_wechat_official_article_request_num),0) as qiye_wechat_official_article_request_num,
        coalesce(sum(qiye_wechat_official_article_request_success_num),0) as qiye_wechat_official_article_request_success_num,
        coalesce(sum(qiye_wechat_official_article_request_fail_num),0) as qiye_wechat_official_article_request_fail_num,
        coalesce(sum(send_sms_num),0) as send_sms_num,
        coalesce(sum(form_send_sms_num),0) as form_send_sms_num,
        coalesce(sum(order_send_sms_num),0) as order_send_sms_num,


        <!--以下为1.247.0新增字段 -->
        coalesce(sum(form_submit_num),0) as form_submit_num,
        coalesce(sum(clue_form_submit_num),0) as clue_form_submit_num,
        coalesce(sum(douyin_applet_native_form_submit_num),0) as douyin_applet_native_form_submit_num,
        (coalesce(sum(form_submit_num),0) + coalesce(sum(clue_form_submit_num),0) + coalesce(sum(douyin_applet_native_form_submit_num),0) )as form_submit_total_num,
        coalesce(sum(phone_number_recieved_num),0) as phone_number_recieved_num,
        coalesce(sum(active_message_authorization_num),0) as active_message_authorization_num,
        coalesce(sum(pop_up_display_num),0) as pop_up_display_num,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0  THEN 0  ELSE   coalesce(sum(form_submit_num),0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as form_submit_rate,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN 0   ELSE   (coalesce(sum(form_submit_num), 0) + coalesce(sum(clue_form_submit_num), 0) +  coalesce(sum(douyin_applet_native_form_submit_num), 0)) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as form_submit_total_rate,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN 0  ELSE   coalesce(sum(form_submit_num),0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as form_submit_rate,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN 0 ELSE coalesce(sum(clue_form_submit_num),0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as clue_form_submit_rate,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN 0 ELSE    coalesce(sum(douyin_applet_native_form_submit_num),0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as douyin_applet_native_form_submit_rate,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN 0  ELSE  coalesce(sum(phone_number_recieved_num),0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as phone_number_recieved_rate,

        case WHEN SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN 0 ELSE     coalesce(sum(active_message_authorization_num),0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC)* 100
        end as active_message_authorization_rate,
        <!--1.247.0-->

        <!--1.256.0新增订单相关数据-->
        sum(coalesce(order_submit_num,0)) as order_submit_num,
        sum(coalesce(order_finish_num,0)) as order_finish_num,
        sum(coalesce(online_shop_buy_goods_success_num,0)) as online_shop_buy_goods_success_num,
        sum(coalesce(douyin_applet_order_submit_num,0)) as douyin_applet_order_submit_num,
        sum(coalesce(douyin_applet_order_finish_num,0)) as douyin_applet_order_finish_num,

        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(order_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as order_submit_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(order_finish_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as order_finish_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(douyin_applet_order_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as douyin_applet_order_submit_rate,
        case when coalesce(sum(douyin_applet_order_submit_num), 0) = 0 then 0 else sum(coalesce(douyin_applet_order_finish_num, 0)) / sum(coalesce(douyin_applet_order_submit_num, 0)) * 100 end as douyin_applet_order_finish_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(online_shop_buy_goods_success_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as online_shop_buy_goods_success_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else (sum(coalesce(order_finish_num, 0)) + sum(coalesce(douyin_applet_order_finish_num, 0))) / sum(coalesce(landing_page_pv, 0)) * 100 end as comprehensive_payment_rate,
        <!--1.256.0-->

        <!-- 1.263.0 饿了么小程序对接 -->
        sum(coalesce(ele_pv_num,0))as ele_pv_num,
        sum(coalesce(ele_qr_code_view_num,0)) as ele_qr_code_view_num,
        sum(coalesce(ele_identify_wechat_qr_code_num,0)) as ele_identify_wechat_qr_code_num,
        sum(coalesce(ele_add_wechat_success_num,0)) as ele_add_wechat_success_num,

        <!-- 1.265.0 whatsapp-->
        sum(coalesce(whatsapp_jump_num,0)) as  whatsapp_jump_num,
        sum(coalesce(whatsapp_add_friend_success_num,0)) as  whatsapp_add_friend_success_num,
        sum(coalesce(whatsapp_user_open_mouth_num,0)) as  whatsapp_user_open_mouth_num,
        sum(coalesce(overseas_pv_num,0))as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        sum(coalesce(whatsapp_customer_prologue_num,0)) as  whatsapp_customer_prologue_num,
        sum(coalesce(whatsapp_customer_send_message_num,0)) as  whatsapp_customer_send_message_num,
        <!--1.264.0新增淘宝电影相关数据-->
        sum(coalesce(tao_bao_movie_applet_jump_num,0)) as tao_bao_movie_applet_jump_num,
        sum(coalesce(tao_bao_movie_applet_order_num,0)) as tao_bao_movie_applet_order_num,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(tao_bao_movie_applet_jump_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as tao_bao_movie_applet_jump_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(tao_bao_movie_applet_order_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as tao_bao_movie_applet_order_rate,
        <!--1.264.0-->

        <!--1.271.0新增加群相关数据-->
        sum(coalesce(add_group_after_add_customer_service_num,0)) as add_group_after_add_customer_service_num,
        sum(coalesce(add_group_after_follow_official_account_num,0)) as add_group_after_follow_official_account_num,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(add_group_after_add_customer_service_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as add_group_after_add_customer_service_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(add_group_after_follow_official_account_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as add_group_after_follow_official_account_rate,
        <!--1.271.0-->
        sum(coalesce(flow_source_jump_page_view_num,0)) as flow_source_jump_page_view_num,
        case
        when COALESCE(sum(identify_qr_code_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(identify_qr_code_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end
        as identify_qr_code_rate,
        case
        when COALESCE(sum(add_work_wechat_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(add_work_wechat_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end
        as add_work_wechat_rate,
        case
        when COALESCE(sum(follow_official_account_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(follow_official_account_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end
        as follow_official_account_rate,
        case
        when COALESCE(sum(add_work_wechat_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(identify_qr_code_num), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(add_work_wechat_num), 0) / (COALESCE(sum(identify_qr_code_num), 0)::NUMERIC) * 100
        end
        as identify_qr_code_add_work_wechat_rate
        from boss_advertiser_account_group_day_report_new baagdr
        where baagdr.agent_id = #{vo.agentId}
        and baagdr.day_time is not null
        <if test="vo.startTime != null and vo.endTime != null">
            and baagdr.day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by day_time,baagdr.agent_id
        ),
        unitPrice as (
        select
        coalesce((sum(bo.order_amount) / sum(bacd.combo_count)),0)
        as unit_price,
        baagdr.agent_id as agent_id
        from boss_advertiser_account_group_day_report_new baagdr
        left join (select agent_id,
        coalesce(sum(order_amount), 0) as order_amount,
        coalesce(sum(increase_pv), 0) as increase_pv
        from boss_order group by agent_id) bo on baagdr.agent_id = bo.agent_id
        left join boss_agent_conf_detail bacd on baagdr.agent_id = bacd.agent_id
        where baagdr.agent_id = #{vo.agentId}
        group by baagdr.agent_id
        ),
        <!--          计费投放账户数（巨量引擎） 授权投放账户消耗金额（巨量引擎 套餐量 套餐使用量 套餐余量-->
        consumeData as  (select day_time as day_time, sum(fund) as fund,max(bill_account_num) as bill_account_num,max(billing_proportion) as billing_proportion,agent_id  from boss_advertiser_account_fund_day_report baafdr
        group by day_time, agent_id
        )
        select dayReport.*,
        case
        when coalesce(add_work_wechat_num, 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        (unitPrice.unit_price * dayReport.landing_page_pv) / add_work_wechat_num
        end as add_fans_cost,
        cd.billing_proportion as  billing_proportion,
        <!--                 投放账户消耗金额 -->
        cd.fund as  billing_account_consume,
        <!--                 付费账户数 -->
        cd.bill_account_num as billing_accounts,
        <!--                  套餐使用量 每日投放账户消耗 * 收费比例 之和 -->
        cd.fund * cd.billing_proportion / 100 as combo_account_consume
        from dayReport left join unitPrice on unitPrice.agent_id = dayReport.agent_id
        left join consumeData cd on dayReport.agent_id = cd.agent_id and dayReport.day_time = cd.day_time
        order by
        <if test="vo.order == 'dayTime'">
            day_time ${vo.sort},
        </if>
        <if test="vo.order == 'landingPagePv'">
            landing_page_pv ${vo.sort},
        </if>
        <if test="vo.order == 'identifyQrCodeNum'">
            identify_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatNum'">
            add_work_wechat_num ${vo.sort},
        </if>
        <if test="vo.order == 'identifyQrCodeRate'">
            identify_qr_code_rate ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatRate'">
            add_work_wechat_rate ${vo.sort},
        </if>
        <if test="vo.order == 'addFansCost'">
            add_fans_cost ${vo.sort},
        </if>
        <if test="vo.order == 'identifyQrCodeAddWorkWechatRate'">
            identify_qr_code_add_work_wechat_rate ${vo.sort},
        </if>
        <if test="vo.order == 'followOfficialAccountNum'">
            follow_official_account_num ${vo.sort},
        </if>
        <if test="vo.order == 'followOfficialAccountRate'">
            follow_official_account_rate ${vo.sort},
        </if>
        <if test="vo.order == 'billingAccounts'">
            billing_accounts ${vo.sort} nulls last,
        </if>
        <if test="vo.order == 'billingAccountConsume'">
            billing_account_consume ${vo.sort} nulls last,
        </if>
        <if test="vo.order == 'comboAccountConsume'">
            combo_account_consume ${vo.sort} nulls last,
        </if>
        <if test="vo.order == 'qiyeRequestNum'">
            qiye_request_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeRequestSuccessNum'">
            qiye_request_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeRequestFailNum'">
            qiye_request_fail_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyePvNum'">
            qiye_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeMiniPvNum'">
            qiye_mini_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'officialIdentifyQrCodeNum'">
            official_identify_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'identifyGroupQrCodeNum'">
            identify_group_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatGroupNum'">
            add_work_wechat_group_num ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatGroupRate'">
            add_work_wechat_group_rate ${vo.sort},
        </if>
        <if test="vo.order == 'identifyGroupQrCodeRate'">
            identify_group_qr_code_rate ${vo.sort},
        </if>
        <if test="vo.order == 'officialIdentifyQrCodeRate'">
            official_identify_qr_code_rate ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticlePageViewNum'">
            qiye_wechat_official_article_page_view_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticleRequestNum'">
            qiye_wechat_official_article_request_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticleRequestSuccessNum'">
            qiye_wechat_official_article_request_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticleRequestFailNum'">
            qiye_wechat_official_article_request_fail_num ${vo.sort},
        </if>
        <if test="vo.order == 'sendSmsNum'">
            send_sms_num ${vo.sort},
        </if>
        <if test="vo.order == 'formSendSmsNum'">
            form_send_sms_num ${vo.sort},
        </if>
        <if test="vo.order == 'orderSendSmsNum'">
            order_send_sms_num ${vo.sort},
        </if>


        <if test="vo.order == 'formSubmitNum'">
            form_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'formSubmitRate'">
            form_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'clueFormSubmitNum'">
            clue_form_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'clueFormSubmitRate'">
            clue_form_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletNativeFormSubmitNum'">
            douyin_applet_native_form_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletNativeFormSubmitRate'">
            douyin_applet_native_form_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'formSubmitTotalNum'">
            form_submit_total_num ${vo.sort},
        </if>

        <if test="vo.order == 'formSubmitTotalRate'">
            form_submit_total_rate ${vo.sort},
        </if>

        <if test="vo.order == 'phoneNumberRecievedNum'">
            phone_number_recieved_num ${vo.sort},
        </if>

        <if test="vo.order == 'phoneNumberRecievedRate'">
            phone_number_recieved_rate ${vo.sort},
        </if>

        <if test="vo.order == 'popUpDisplayNum'">
            pop_up_display_num ${vo.sort},
        </if>

        <if test="vo.order == 'orderSubmitNum'">
            order_submit_num ${vo.sort},
        </if>
        <if test="vo.order == 'orderSubmitRate'">
            order_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'orderFinishNum'">
            order_finish_num ${vo.sort},
        </if>

        <if test="vo.order == 'orderFinishRate'">
            order_finish_rate ${vo.sort},
        </if>

        <if test="vo.order == 'onlineShopBuyGoodsSuccessNum'">
            online_shop_buy_goods_success_num ${vo.sort},
        </if>

        <if test="vo.order == 'onlineShopBuyGoodsSuccessRate'">
            online_shop_buy_goods_success_rate ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderSubmitNum'">
            douyin_applet_order_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderSubmitRate'">
            douyin_applet_order_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderFinishNum'">
            douyin_applet_order_finish_num ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderFinishRate'">
            douyin_applet_order_finish_rate ${vo.sort},
        </if>

        <if test="vo.order == 'comprehensivePaymentRate'">
            comprehensive_payment_rate ${vo.sort},
        </if>
        <if test="vo.order == 'elePvNum'">
            ele_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'eleQrCodeViewNum'">
            ele_qr_code_view_num ${vo.sort},
        </if>
        <if test="vo.order == 'eleIdentifyWechatQrCodeNum'">
            ele_identify_wechat_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'eleAddWechatSuccessNum'">
            ele_add_wechat_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappJumpNum'">
            whatsapp_jump_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappAddFriendSuccessNum'">
            whatsapp_add_friend_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappUserOpenMouthNum'">
            whatsapp_user_open_mouth_num ${vo.sort},
        </if>
        <if test="vo.order == 'overseasPvNum'">
            overseas_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappCustomerPrologueNum'">
            whatsapp_customer_prologue_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappCustomerSendMessageNum'">
            whatsapp_customer_send_message_num ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletJumpNum'">
            tao_bao_movie_applet_jump_num ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletJumpRate'">
            tao_bao_movie_applet_jump_rate ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletOrderNum'">
            tao_bao_movie_applet_order_num ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletOrderRate'">
            tao_bao_movie_applet_order_rate ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterAddCustomerServiceNum'">
            add_group_after_add_customer_service_num ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterAddCustomerServiceRate'">
            add_group_after_add_customer_service_rate ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterFollowOfficialAccountNum'">
            add_group_after_follow_official_account_num ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterFollowOfficialAccountRate'">
            add_group_after_follow_official_account_rate ${vo.sort},
        </if>
        <if test="vo.order == 'flowSourceJumpPageViewNum'">
            flow_source_jump_page_view_num ${vo.sort},
        </if>
        day_time desc
    </select>

    <!--2.0统计，数据从clickhouse数据源聚合-->
    <select id="getTimeReportDayByAgentIdNew" resultType="ai.yiye.agent.boss.vo.BossAccountGroupReportDayVo">
        with dayReport as (select baagdr.agent_id as agent_id,
        baagdr.advertiser_account_group_id as advertiser_account_group_id,
        baagdr.day_time as day_time,
        COALESCE(sum(landing_page_pv), 0) as landing_page_pv,
        coalesce(sum(identify_qr_code_num), 0) as identify_qr_code_num,
        coalesce(sum(add_work_wechat_num), 0) as add_work_wechat_num,
        coalesce(sum(follow_official_account_num), 0) as follow_official_account_num,
        coalesce(sum(qiye_request_num), 0) as qiye_request_num,
        coalesce(sum(qiye_request_success_num), 0) as qiye_request_success_num,
        coalesce(sum(qiye_request_fail_num), 0) as qiye_request_fail_num,
        coalesce(sum(qiye_pv_num), 0) as qiye_pv_num,
        coalesce(sum(qiye_mini_pv_num), 0) as qiye_mini_pv_num,
        coalesce(sum(official_identify_qr_code_num), 0) as official_identify_qr_code_num,
        coalesce(sum(identify_group_qr_code_num), 0) as identify_group_qr_code_num,
        coalesce(sum(add_work_wechat_group_num), 0) as add_work_wechat_group_num,
        coalesce(sum(qiye_wechat_official_article_page_view_num),0) as qiye_wechat_official_article_page_view_num,
        coalesce(sum(qiye_wechat_official_article_request_num),0) as qiye_wechat_official_article_request_num,
        coalesce(sum(qiye_wechat_official_article_request_success_num),0) as qiye_wechat_official_article_request_success_num,
        coalesce(sum(qiye_wechat_official_article_request_fail_num),0) as qiye_wechat_official_article_request_fail_num,
        coalesce(sum(send_sms_num),0) as send_sms_num,
        coalesce(sum(form_send_sms_num),0) as form_send_sms_num,
        coalesce(sum(order_send_sms_num),0) as order_send_sms_num,
        <!--1.247.0新增的字段-->
        coalesce(sum(form_submit_num),0) as form_submit_num,
        coalesce(sum(clue_form_submit_num),0) as clue_form_submit_num,
        coalesce(sum(douyin_applet_native_form_submit_num),0) as douyin_applet_native_form_submit_num,
        coalesce(sum(phone_number_recieved_num),0) as phone_number_recieved_num,
        (coalesce(sum(form_submit_num),0) + coalesce(sum(clue_form_submit_num),0) + coalesce(sum(douyin_applet_native_form_submit_num),0)) as form_submit_total_num,
        coalesce(sum(active_message_authorization_num),0) as active_message_authorization_num,
        coalesce(sum(pop_up_display_num),0) as pop_up_display_num,
        case  when COALESCE(sum(form_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(form_submit_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as form_submit_rate,

        case  when COALESCE(sum(phone_number_recieved_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(phone_number_recieved_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as phone_number_recieved_rate,

        case  when COALESCE(sum(clue_form_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(clue_form_submit_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as clue_form_submit_rate,

        case  when COALESCE(sum(douyin_applet_native_form_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(douyin_applet_native_form_submit_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as douyin_applet_native_form_submit_rate,

        case  when COALESCE(sum(active_message_authorization_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(active_message_authorization_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as active_message_authorization_rate,

        case  when  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE (coalesce(sum(form_submit_num),0) + coalesce(sum(clue_form_submit_num),0) + coalesce(sum(douyin_applet_native_form_submit_num),0)) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as form_submit_total_rate,
        <!--1.247.0新增的字段-->
        <!-- 1.263.0 饿了么小程序对接 -->
        coalesce(sum(ele_pv_num),0)as ele_pv_num,
        coalesce(sum(ele_qr_code_view_num),0) as ele_qr_code_view_num,
        coalesce(sum(ele_identify_wechat_qr_code_num),0) as ele_identify_wechat_qr_code_num,
        coalesce(sum(ele_add_wechat_success_num),0) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(sum(whatsapp_jump_num),0) as  whatsapp_jump_num,
        coalesce(sum(whatsapp_add_friend_success_num),0) as  whatsapp_add_friend_success_num,
        coalesce(sum(whatsapp_user_open_mouth_num),0) as  whatsapp_user_open_mouth_num,
        coalesce(sum(overseas_pv_num),0)as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        coalesce(sum(whatsapp_customer_prologue_num),0) as  whatsapp_customer_prologue_num,
        coalesce(sum(whatsapp_customer_send_message_num),0) as  whatsapp_customer_send_message_num,
        <!--1.256.0新增订单相关字段-->
        coalesce(sum(order_submit_num),0) as order_submit_num,
        coalesce(sum(order_finish_num),0) as order_finish_num,
        coalesce(sum(online_shop_buy_goods_success_num),0) as online_shop_buy_goods_success_num,
        coalesce(sum(douyin_applet_order_submit_num),0) as douyin_applet_order_submit_num,
        coalesce(sum(douyin_applet_order_finish_num),0) as douyin_applet_order_finish_num,

        case  when COALESCE(sum(order_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(order_submit_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as order_submit_rate,

        case  when COALESCE(sum(order_finish_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(order_finish_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as order_finish_rate,

        case  when COALESCE(sum(online_shop_buy_goods_success_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(online_shop_buy_goods_success_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as online_shop_buy_goods_success_rate,

        case  when COALESCE(sum(douyin_applet_order_submit_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(douyin_applet_order_submit_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as douyin_applet_order_submit_rate,

        case  when COALESCE(sum(douyin_applet_order_finish_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(douyin_applet_order_submit_num), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(douyin_applet_order_finish_num), 0) / (COALESCE(sum(douyin_applet_order_submit_num), 0)::NUMERIC) * 100
        end as douyin_applet_order_finish_rate,

        case  when (COALESCE(sum(douyin_applet_order_finish_num), 0) + COALESCE(sum(order_finish_num), 0) ) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE (COALESCE(sum(douyin_applet_order_finish_num), 0) + COALESCE(sum(order_finish_num), 0) ) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as comprehensive_payment_rate,
        <!--1.256.0-->

        <!--1.264.0新增淘宝电影相关数据-->
        sum(coalesce(tao_bao_movie_applet_jump_num,0)) as tao_bao_movie_applet_jump_num,
        sum(coalesce(tao_bao_movie_applet_order_num,0)) as tao_bao_movie_applet_order_num,

        case  when COALESCE(sum(tao_bao_movie_applet_jump_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(tao_bao_movie_applet_jump_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
            end as tao_bao_movie_applet_jump_rate,

        case  when COALESCE(sum(tao_bao_movie_applet_order_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
            ELSE COALESCE(sum(tao_bao_movie_applet_order_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
            end as tao_bao_movie_applet_order_rate,
        <!--1.264.0-->

        <!--1.271.0新增加群数据-->
        sum(coalesce(add_group_after_add_customer_service_num,0)) as add_group_after_add_customer_service_num,
        sum(coalesce(add_group_after_follow_official_account_num,0)) as add_group_after_follow_official_account_num,

        case  when COALESCE(sum(add_group_after_add_customer_service_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(add_group_after_add_customer_service_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as add_group_after_add_customer_service_rate,

        case  when COALESCE(sum(add_group_after_follow_official_account_num), 0) = CAST(0 AS NUMERIC) or  COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE COALESCE(sum(add_group_after_follow_official_account_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end as add_group_after_follow_official_account_rate,
        <!--1.271.0-->

        case
        when COALESCE(sum(identify_qr_code_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(identify_qr_code_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end
        as identify_qr_code_rate,
        case
        when COALESCE(sum(follow_official_account_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(follow_official_account_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end
        as follow_official_account_rate,
        case
        when COALESCE(sum(add_work_wechat_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(landing_page_pv), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(add_work_wechat_num), 0) / (COALESCE(sum(landing_page_pv), 0)::NUMERIC) * 100
        end
        as add_work_wechat_rate,
        case
        when COALESCE(sum(add_work_wechat_num), 0) = CAST(0 AS NUMERIC) or
        COALESCE(sum(identify_qr_code_num), 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        COALESCE(sum(add_work_wechat_num), 0) / (COALESCE(sum(identify_qr_code_num), 0)::NUMERIC) * 100
        end
        as identify_qr_code_add_work_wechat_rate,
        coalesce(sum(bill_account_num), 0) as billing_accounts,
        coalesce(sum(fund), 0) as billing_account_consume,
        coalesce(sum(billing_proportion), 0) as billing_proportion,
        coalesce(sum(flow_source_jump_page_view_num), 0) as flow_source_jump_page_view_num
        from boss_advertiser_account_group_day_report_new baagdr
        where baagdr.agent_id = #{vo.agentId}
        <if test="vo.startTime != null and vo.endTime != null">
            and baagdr.day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by baagdr.day_time,baagdr.advertiser_account_group_id,baagdr.agent_id
        ),
        unitPrice as (
        select
        coalesce((sum(bo.order_amount) / sum(bacd.combo_count)::NUMERIC),0)
        as unit_price,
        baagdr.agent_id as agent_id
        from boss_advertiser_account_group_day_report_new baagdr
        left join (select agent_id,
        coalesce(sum(order_amount), 0) as order_amount,
        coalesce(sum(increase_pv), 0) as increase_pv
        from boss_order where order_status = 0 or order_status = 1 group by agent_id) bo on baagdr.agent_id = bo.agent_id
        left join boss_agent_conf_detail bacd on baagdr.agent_id = bacd.agent_id
        where baagdr.agent_id = #{vo.agentId}
        group by baagdr.agent_id
        )
        select baag.advertiser_account_group_id as advertiser_account_group_id,
        baag.group_name as group_name,
        baag.replace_operation,
        dayReport.day_time,
        baag.agent_id as agent_id,
        coalesce(landing_page_pv, 0) as landing_page_pv,
        coalesce(identify_qr_code_num, 0) as identify_qr_code_num,
        coalesce(follow_official_account_num, 0) as follow_official_account_num,
        coalesce(add_work_wechat_num, 0) as add_work_wechat_num,
        coalesce(identify_qr_code_rate, 0) as identify_qr_code_rate,
        coalesce(follow_official_account_rate, 0) as follow_official_account_rate,
        coalesce(add_work_wechat_rate, 0) as add_work_wechat_rate,
        coalesce(identify_qr_code_add_work_wechat_rate, 0) as identify_qr_code_add_work_wechat_rate,
        coalesce(qiye_request_num, 0) as qiye_request_num,
        coalesce(qiye_request_success_num, 0) as qiye_request_success_num,
        coalesce(qiye_request_fail_num, 0) as qiye_request_fail_num,
        coalesce(qiye_pv_num, 0) as qiye_pv_num,
        coalesce(qiye_mini_pv_num, 0) as qiye_mini_pv_num,
        coalesce(official_identify_qr_code_num, 0) as official_identify_qr_code_num,
        coalesce(identify_group_qr_code_num, 0) as identify_group_qr_code_num,
        coalesce(add_work_wechat_group_num, 0) as add_work_wechat_group_num,
        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(official_identify_qr_code_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as official_identify_qr_code_rate,
        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(identify_group_qr_code_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as identify_group_qr_code_rate,
        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_work_wechat_group_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as add_work_wechat_group_rate,
        coalesce(dayReport.qiye_wechat_official_article_page_view_num,0) as qiye_wechat_official_article_page_view_num,
        coalesce(dayReport.qiye_wechat_official_article_request_num,0) as qiye_wechat_official_article_request_num,
        coalesce(dayReport.qiye_wechat_official_article_request_success_num,0) as qiye_wechat_official_article_request_success_num,
        coalesce(dayReport.qiye_wechat_official_article_request_fail_num,0) as qiye_wechat_official_article_request_fail_num,
        coalesce(dayReport.send_sms_num,0) as send_sms_num,
        coalesce(dayReport.form_send_sms_num,0) as form_send_sms_num,
        coalesce(dayReport.order_send_sms_num,0) as order_send_sms_num,
        <!--1.247.0新增字段-->
        coalesce(dayReport.form_submit_num,0) as form_submit_num,
        coalesce(dayReport.clue_form_submit_num,0) as clue_form_submit_num,
        coalesce(dayReport.douyin_applet_native_form_submit_num,0) as douyin_applet_native_form_submit_num,
        coalesce(dayReport.phone_number_recieved_num,0) as phone_number_recieved_num,
        coalesce(dayReport.form_submit_total_num,0) as form_submit_total_num,
        coalesce(dayReport.active_message_authorization_num,0) as active_message_authorization_num,
        coalesce(dayReport.pop_up_display_num,0) as pop_up_display_num,

        coalesce(form_submit_rate, 0) as form_submit_rate,
        coalesce(clue_form_submit_rate, 0) as clue_form_submit_rate,
        coalesce(douyin_applet_native_form_submit_rate, 0) as douyin_applet_native_form_submit_rate,
        coalesce(active_message_authorization_rate, 0) as active_message_authorization_rate,
        coalesce(form_submit_total_rate, 0) as form_submit_total_rate,
        coalesce(phone_number_recieved_rate, 0) as phone_number_recieved_rate,
        <!--1.247.0新增字段-->

        <!--1.256.0新增订单相关的字段-->
        coalesce(dayReport.order_submit_num,0) as order_submit_num,
        coalesce(dayReport.order_finish_num,0) as order_finish_num,
        coalesce(dayReport.online_shop_buy_goods_success_num,0) as online_shop_buy_goods_success_num,
        coalesce(dayReport.douyin_applet_order_submit_num,0) as douyin_applet_order_submit_num,
        coalesce(dayReport.douyin_applet_order_finish_num,0) as douyin_applet_order_finish_num,

        coalesce(order_submit_rate, 0) as order_submit_rate,
        coalesce(order_finish_rate, 0) as order_finish_rate,
        coalesce(online_shop_buy_goods_success_rate, 0) as online_shop_buy_goods_success_rate,
        coalesce(douyin_applet_order_submit_rate, 0) as douyin_applet_order_submit_rate,
        coalesce(douyin_applet_order_finish_rate, 0) as douyin_applet_order_finish_rate,
        coalesce(comprehensive_payment_rate, 0) as comprehensive_payment_rate,
        <!--1.256.0-->
        <!-- 1.263.0 饿了么小程序对接 -->
        coalesce(dayReport.ele_pv_num,0) as ele_pv_num,
        coalesce(dayReport.ele_qr_code_view_num,0) as ele_qr_code_view_num,
        coalesce(dayReport.ele_identify_wechat_qr_code_num,0) as ele_identify_wechat_qr_code_num,
        coalesce(dayReport.ele_add_wechat_success_num,0) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(dayReport.whatsapp_jump_num,0) as  whatsapp_jump_num,
        coalesce(dayReport.whatsapp_add_friend_success_num,0) as  whatsapp_add_friend_success_num,
        coalesce(dayReport.whatsapp_user_open_mouth_num,0) as  whatsapp_user_open_mouth_num,
        coalesce(dayReport.overseas_pv_num,0)as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        coalesce(dayReport.whatsapp_customer_prologue_num,0) as  whatsapp_customer_prologue_num,
        coalesce(dayReport.whatsapp_customer_send_message_num,0) as  whatsapp_customer_send_message_num,
        <!--1.264.0新增淘宝电影相关的字段-->
        coalesce(tao_bao_movie_applet_jump_num, 0) as tao_bao_movie_applet_jump_num,
        coalesce(tao_bao_movie_applet_order_num, 0) as tao_bao_movie_applet_order_num,
        coalesce(tao_bao_movie_applet_jump_rate, 0) as tao_bao_movie_applet_jump_rate,
        coalesce(tao_bao_movie_applet_order_rate, 0) as tao_bao_movie_applet_order_rate,
        <!--1.264.0-->

        <!--1.271.0新增加群相关的字段-->
        coalesce(add_group_after_add_customer_service_num, 0) as add_group_after_add_customer_service_num,
        coalesce(add_group_after_follow_official_account_num, 0) as add_group_after_follow_official_account_num,
        coalesce(add_group_after_add_customer_service_rate, 0) as add_group_after_add_customer_service_rate,
        coalesce(add_group_after_follow_official_account_rate, 0) as add_group_after_follow_official_account_rate,
        <!--1.271.0-->
        coalesce(flow_source_jump_page_view_num, 0) as flow_source_jump_page_view_num,
        case
        when coalesce(add_work_wechat_num, 0) = CAST(0 AS NUMERIC) THEN 0
        ELSE
        (unitPrice.unit_price * dayReport.landing_page_pv) / (add_work_wechat_num::NUMERIC)
        end as add_fans_cost,
        coalesce(billing_accounts, 0) as billing_accounts,
        coalesce(billing_account_consume, 0) as billing_account_consume,
        coalesce(billing_proportion, 0) as billing_proportion,
        coalesce((billing_account_consume * billing_proportion) / 100, 0) as combo_account_consume
        from boss_advertiser_account_group baag
        left join dayReport on baag.advertiser_account_group_id=dayReport.advertiser_account_group_id
        left join unitPrice on unitPrice.agent_id = baag.agent_id
        where baag.agent_id = #{vo.agentId}
        <if test="vo.groupName != null and '' != vo.groupName">
            and baag.group_name like concat('%', #{vo.groupName}, '%')
        </if>

        <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@DEFAULT">
            and baag.replace_operation = 0
        </if>
        <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@OPERATION">
            and baag.replace_operation = 1
        </if>
        <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@STOP_OPERATION">
            and baag.replace_operation = 2
        </if>
        order by
        <if test="vo.order == 'dayTime'">
            day_time ${vo.sort},
        </if>
        <if test="vo.order == 'landingPagePv'">
            landing_page_pv ${vo.sort},
        </if>
        <if test="vo.order == 'identifyQrCodeNum'">
            identify_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatNum'">
            add_work_wechat_num ${vo.sort},
        </if>
        <if test="vo.order == 'identifyQrCodeRate'">
            identify_qr_code_rate ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatRate'">
            add_work_wechat_rate ${vo.sort},
        </if>
        <if test="vo.order == 'addFansCost'">
            add_fans_cost ${vo.sort},
        </if>
        <if test="vo.order == 'identifyQrCodeAddWorkWechatRate'">
            identify_qr_code_add_work_wechat_rate ${vo.sort},
        </if>
        <if test="vo.order == 'followOfficialAccountNum'">
            follow_official_account_num ${vo.sort},
        </if>
        <if test="vo.order == 'followOfficialAccountNum'">
            follow_official_account_num ${vo.sort},
        </if>
        <if test="vo.order == 'followOfficialAccountRate'">
            follow_official_account_rate ${vo.sort},
        </if>
        <if test="vo.order == 'billingAccounts'">
            billing_accounts ${vo.sort} nulls last,
        </if>
        <if test="vo.order == 'billingAccountConsume'">
            billing_account_consume ${vo.sort} nulls last,
        </if>
        <if test="vo.order == 'comboAccountConsume'">
            combo_account_consume ${vo.sort} nulls last,
        </if>
        <if test="vo.order == 'qiyeRequestNum'">
            qiye_request_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeRequestSuccessNum'">
            qiye_request_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeRequestFailNum'">
            qiye_request_fail_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyePvNum'">
            qiye_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeMiniPvNum'">
            qiye_mini_pv_num ${vo.sort},
        </if>
        <!--以下为1.197.0新增三个排序 -->

        <if test="vo.order == 'officialIdentifyQrCodeNum'">
            official_identify_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'identifyGroupQrCodeNum'">
            identify_group_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatGroupNum'">
            add_work_wechat_group_num ${vo.sort},
        </if>
        <if test="vo.order == 'addWorkWechatGroupRate'">
            add_work_wechat_group_rate ${vo.sort},
        </if>
        <if test="vo.order == 'identifyGroupQrCodeRate'">
            identify_group_qr_code_rate ${vo.sort},
        </if>
        <if test="vo.order == 'officialIdentifyQrCodeRate'">
            official_identify_qr_code_rate ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticlePageViewNum'">
            dayReport.qiye_wechat_official_article_page_view_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticleRequestNum'">
            dayReport.qiye_wechat_official_article_request_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticleRequestSuccessNum'">
            dayReport.qiye_wechat_official_article_request_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'qiyeWechatOfficialArticleRequestFailNum'">
            dayReport.qiye_wechat_official_article_request_fail_num ${vo.sort},
        </if>
        <if test="vo.order == 'sendSmsNum'">
            send_sms_num ${vo.sort},
        </if>
        <if test="vo.order == 'formSendSmsNum'">
            form_send_sms_num ${vo.sort},
        </if>
        <if test="vo.order == 'orderSendSmsNum'">
            order_send_sms_num ${vo.sort},
        </if>
        <!--1.247.0新增字段-->
        <if test="vo.order == 'formSubmitNum'">
            form_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'formSubmitRate'">
            form_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'clueFormSubmitNum'">
            clue_form_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'clueFormSubmitRate'">
            clue_form_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletNativeFormSubmitNum'">
            douyin_applet_native_form_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletNativeFormSubmitRate'">
            douyin_applet_native_form_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'formSubmitTotalNum'">
            form_submit_total_num ${vo.sort},
        </if>

        <if test="vo.order == 'formSubmitTotalRate'">
            form_submit_total_rate ${vo.sort},
        </if>

        <if test="vo.order == 'phoneNumberRecievedNum'">
            phone_number_recieved_num ${vo.sort},
        </if>

        <if test="vo.order == 'phoneNumberRecievedRate'">
            phone_number_recieved_rate ${vo.sort},
        </if>

        <if test="vo.order == 'popUpDisplayNum'">
            pop_up_display_num ${vo.sort},
        </if>

        <if test="vo.order == 'orderSubmitNum'">
            order_submit_num ${vo.sort},
        </if>
        <if test="vo.order == 'orderSubmitRate'">
            order_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'orderFinishNum'">
            order_finish_num ${vo.sort},
        </if>

        <if test="vo.order == 'orderFinishRate'">
            order_finish_rate ${vo.sort},
        </if>

        <if test="vo.order == 'onlineShopBuyGoodsSuccessNum'">
            online_shop_buy_goods_success_num ${vo.sort},
        </if>

        <if test="vo.order == 'onlineShopBuyGoodsSuccessRate'">
            online_shop_buy_goods_success_rate ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderSubmitNum'">
            douyin_applet_order_submit_num ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderSubmitRate'">
            douyin_applet_order_submit_rate ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderFinishNum'">
            douyin_applet_order_finish_num ${vo.sort},
        </if>

        <if test="vo.order == 'douyinAppletOrderFinishRate'">
            douyin_applet_order_finish_rate ${vo.sort},
        </if>

        <if test="vo.order == 'comprehensivePaymentRate'">
            comprehensive_payment_rate ${vo.sort},
        </if>
        <if test="vo.order == 'elePvNum'">
            ele_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'eleQrCodeViewNum'">
            ele_qr_code_view_num ${vo.sort},
        </if>
        <if test="vo.order == 'eleIdentifyWechatQrCodeNum'">
            ele_identify_wechat_qr_code_num ${vo.sort},
        </if>
        <if test="vo.order == 'eleAddWechatSuccessNum'">
            ele_add_wechat_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappJumpNum'">
            whatsapp_jump_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappAddFriendSuccessNum'">
            whatsapp_add_friend_success_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappUserOpenMouthNum'">
            whatsapp_user_open_mouth_num ${vo.sort},
        </if>
        <if test="vo.order == 'overseasPvNum'">
            overseas_pv_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappCustomerPrologueNum'">
            whatsapp_customer_prologue_num ${vo.sort},
        </if>
        <if test="vo.order == 'whatsappCustomerSendMessageNum'">
            whatsapp_customer_send_message_num ${vo.sort},
        </if>
        <if test="vo.order == 'taoBaoMovieAppletJumpNum'">
            tao_bao_movie_applet_jump_num ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletJumpRate'">
            tao_bao_movie_applet_order_num ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletOrderNum'">
            tao_bao_movie_applet_jump_rate ${vo.sort},
        </if>

        <if test="vo.order == 'taoBaoMovieAppletOrderRate'">
            tao_bao_movie_applet_order_rate ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterAddCustomerServiceNum'">
            add_group_after_add_customer_service_num ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterAddCustomerServiceRate'">
            add_group_after_add_customer_service_rate ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterFollowOfficialAccountNum'">
            add_group_after_follow_official_account_num ${vo.sort},
        </if>

        <if test="vo.order == 'addGroupAfterFollowOfficialAccountRate'">
            add_group_after_follow_official_account_rate ${vo.sort},
        </if>
        <if test="vo.order == 'flowSourceJumpPageViewNum'">
            flow_source_jump_page_view_num ${vo.sort},
        </if>
        landing_page_pv desc
    </select>
</mapper>
