<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.yiye.agent.boss.mapper.BossCustomerMapper">
    <select id="getEstimateComboDayLeftByAgentId" resultType="ai.yiye.agent.boss.vo.BossCustomerVo">
        <!--最近7天的落地页pv均值 -->
        with lastSevenDayPvData as (
        select agent_id, sum(landing_page_pv) / 7 as last_seven_day_pv from
        (select sum(landing_page_pv) landing_page_pv,agent_id, day_time from boss_advertiser_account_group_day_report
        group by agent_id, day_time) y
        where day_time between now()::timestamp + '-7 day' and now()
        group by agent_id
        ),
        <!--若不满7天，近7天套餐使用量(落地页PV)=所有天数套餐使用量(落地页PV)/所有天数； -->
        lastDayPvData as (
        select agent_id, sum(landing_page_pv) / count(*) as last_day_pv
        from
        (select sum(landing_page_pv) landing_page_pv,agent_id, day_time from boss_advertiser_account_group_day_report
        group by agent_id, day_time) x
        group by agent_id
        )
        select
        case
        date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
        when true
        then case when last_seven_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) /
        last_seven_day_pv, 0), 1) end
        when false
        then case when last_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) / last_day_pv,
        0), 1)
        end
        END as estimate_combo_day_left,
               b.send_warn_info, a.agent_id, b.company_name, b.combo_count, coalesce(total_landing_page_pv, 0) as combo_used
        from
             agent_conf a left join
             boss_agent_conf_detail b
            on a.agent_id = b.agent_id
            left join
        (
        select agent_id, sum(landing_page_pv) as total_landing_page_pv
        from boss_advertiser_account_group_day_report
        group by agent_id
        ) c
        on a.agent_id = c.agent_id
        left join lastSevenDayPvData ls on a.agent_id = ls.agent_id
        left join lastDayPvData ld on a.agent_id = ld.agent_id
        where a.agent_id = #{agentId}
    </select>

    <select id="getCustomerByAgentId" resultType="ai.yiye.agent.domain.boss.BossMailVo">
        <!--最近7天的落地页pv均值 -->
        with lastSevenDayPvData as (
        select agent_id, sum(landing_page_pv) / 7 as last_seven_day_pv from
        (select sum(landing_page_pv) landing_page_pv,agent_id, day_time from boss_advertiser_account_group_day_report
        group by agent_id, day_time) y
        where day_time between now()::timestamp + '-7 day' and now()
        group by agent_id
        ),
        <!--若不满7天，近7天套餐使用量(落地页PV)=所有天数套餐使用量(落地页PV)/所有天数； -->
        lastDayPvData as (
        select agent_id, sum(landing_page_pv) / count(*) as last_day_pv
        from
        (select sum(landing_page_pv) landing_page_pv,agent_id, day_time from boss_advertiser_account_group_day_report
        group by agent_id, day_time) x
        group by agent_id
        )
        select
        case
        date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
        when true
        then case when last_seven_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) /
        last_seven_day_pv, 0), 1) end
        when false
        then case when last_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) / last_day_pv,
        0), 1)
        end
        END as estimate_combo_day_left,

        case
        date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
        when true
        then coalesce(last_seven_day_pv, 0)
        when false
        then coalesce(last_day_pv, 0)
        END as last_seven_day_pv,

        a.agent_id, b.combo_count, coalesce(total_landing_page_pv, 0) as combo_used,
        coalesce((combo_count - total_landing_page_pv), 0) as combo_left
        from
        agent_conf a left join
        boss_agent_conf_detail b
        on a.agent_id = b.agent_id
        left join
        (
        select agent_id, coalesce(sum(landing_page_pv), 0) as total_landing_page_pv
        from boss_advertiser_account_group_day_report
        group by agent_id
        ) c
        on a.agent_id = c.agent_id
        left join lastSevenDayPvData ls on a.agent_id = ls.agent_id
        left join lastDayPvData ld on a.agent_id = ld.agent_id
        where a.agent_id = #{agentId}
    </select>



    <select id="getCustomerByAgentIdNew" resultType="ai.yiye.agent.domain.boss.BossMailVo">
     <!--最近7天的落地页pv均值 -->
    with lastSevenDayPvData as (
        select agent_id, sum(landing_page_pv) / 7 as last_seven_day_pv from
        (select sum(landing_page_pv) landing_page_pv,agent_id, day_time from boss_advertiser_account_group_day_report_new
        group by agent_id, day_time) y
        where day_time between now()::timestamp + '-7 day' and now()
        group by agent_id
    ),
    <!--若不满7天，近7天套餐使用量(落地页PV)=所有天数套餐使用量(落地页PV)/所有天数； -->
    lastDayPvData as (
        select agent_id, sum(landing_page_pv) / count(*) as last_day_pv
        from
        (select sum(landing_page_pv) landing_page_pv,agent_id, day_time from boss_advertiser_account_group_day_report_new
        group by agent_id, day_time) x
        group by agent_id
    )
    select
        case
            date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
            when true
            then case when last_seven_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) /
            last_seven_day_pv, 0), 1) end
            when false
            then case when last_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) / last_day_pv,
            0), 1)
            end
            END as estimate_combo_day_left,

        case
            date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
            when true
            then coalesce(last_seven_day_pv, 0)
            when false
            then coalesce(last_day_pv, 0)
            END as last_seven_day_pv,

        a.agent_id, b.combo_count, coalesce(total_landing_page_pv, 0) as combo_used,
        coalesce((combo_count - total_landing_page_pv), 0) as combo_left
    from
        agent_conf a left join
        boss_agent_conf_detail b
        on a.agent_id = b.agent_id
    left join
    (
        select agent_id, coalesce(sum(landing_page_pv), 0) as total_landing_page_pv
        from boss_advertiser_account_group_day_report_new
        group by agent_id
    ) c
        on a.agent_id = c.agent_id
    left join lastSevenDayPvData ls on a.agent_id = ls.agent_id
    left join lastDayPvData ld on a.agent_id = ld.agent_id
    where a.agent_id = #{agentId}
</select>

    <select id="getCustomerAccountConsumeByAgentId" resultType="ai.yiye.agent.domain.boss.BossMailVo">
        <!--最近7天的落地页pv均值 -->
        with   lastSevenDayFundData as (
        select agent_id, sum(fund) / 7  as last_seven_day_fund from
        (select sum(fund) /100 * billing_proportion  as fund,agent_id, day_time from boss_advertiser_account_fund_day_report
        group by agent_id, day_time,billing_proportion) y
        where day_time between now()::timestamp + '-7 day' and now()
        group by agent_id
        ),
        lastDayFundData as (
        select agent_id, sum(fund)/count(*)  as last_day_fund
        from
        (select sum(fund)  *billing_proportion /100 as fund,agent_id, day_time, billing_proportion from boss_advertiser_account_fund_day_report
        group by agent_id, day_time,billing_proportion) x
        group by agent_id
        ),
        consumeData as  (				 select sum (fund) as fund_consume,agent_id from (
        select
        case when billing_proportion=0 then 0
        else
        sum(fund) * billing_proportion /100
        end
        as fund,agent_id  from boss_advertiser_account_fund_day_report baafdr
        group by agent_id,billing_proportion
        ) as tool group by agent_id
        ),

        fundData as (select sum(fund) as fund,agent_id from  boss_advertiser_account_fund_day_report  group by agent_id)
        select
        case
        date_part('day',cast(now() as TIMESTAMP)-cast(a.created_at as TIMESTAMP)) + 1 >= 7
        when true
        then case when last_seven_day_fund = 0 then 0 else round(coalesce((coalesce(pv_balance + coalesce(d.combo_amount - cd.fund_consume, 0), 0) - coalesce(cd.fund_consume , 0)) /
        last_seven_day_fund, 0), 1) end
        when false
        then case when last_day_fund = 0 then 0 else round(coalesce((coalesce(pv_balance + coalesce(d.combo_amount - cd.fund_consume, 0), 0) - coalesce(cd.fund_consume, 0)) / last_day_fund,
        0), 1)
        end
        end
        as estimate_combo_day_left,
        a.agent_id, d.combo_amount as combo_count, coalesce(fund_consume, 0) as combo_used,
        coalesce((combo_amount - coalesce(fund_consume, 0)), 0) as combo_left
        from
        agent_conf a left join
        boss_agent_conf_detail d
        on a.agent_id = d.agent_id
        left join
        (
        select agent_id, coalesce(sum(fund), 0) as total_fund
        from boss_advertiser_account_fund_day_report
        group by agent_id
        ) c
        on a.agent_id = c.agent_id
        left join lastSevenDayFundData ls on a.agent_id = ls.agent_id
        left join lastDayFundData ld on a.agent_id = ld.agent_id
        left join  consumeData cd on a.agent_id =cd.agent_id
        left join (
        select t.agent_id, combo_id from (
        select max(created_at) created_at, agent_id from boss_order where order_status != 2 and combo_id is not null group by agent_id
        ) t left join boss_order h on t.agent_id = h.agent_id and t.created_at = h.created_at
        )o on a.agent_id = o.agent_id
        left join boss_combo combo on o.combo_id = combo.id
        where a.agent_id = #{agentId}

    </select>

    <select id="pageCustomerByConditions" resultType="ai.yiye.agent.boss.vo.BossCustomerVo">
        <!--最近7天的落地页pv均值 -->
        with lastSevenDayPvData as (
            select agent_id, sum(landing_page_pv) / 7 as last_seven_day_pv from
            (select sum(landing_page_pv) landing_page_pv,agent_id, day_time from boss_advertiser_account_group_day_report
            group by agent_id, day_time) y
            where day_time between now()::timestamp + '-7 day' and now()
            group by agent_id
        ),
        <!--若不满7天，近7天套餐使用量(落地页PV)=所有天数套餐使用量(落地页PV)/所有天数； -->
        lastDayPvData as (
            select agent_id, sum(landing_page_pv) / count(*) as last_day_pv
            from
            (select sum(landing_page_pv) landing_page_pv,agent_id from boss_advertiser_account_group_day_report
            group by agent_id, day_time) x
            group by agent_id
        ),
         lastSevenDayFundData as (
            select agent_id, sum(fund) / 7  as last_seven_day_fund from
            (select sum(fund) /100 * billing_proportion  as fund,agent_id, day_time from boss_advertiser_account_fund_day_report
            group by agent_id, day_time,billing_proportion) y
            where day_time between now()::timestamp + '-7 day' and now()
            group by agent_id
        ),
        lastDayFundData as (
            select agent_id, sum(fund)/count(*)  as last_day_fund
            from
            (select sum(fund)  *billing_proportion /100 as fund,agent_id, day_time, billing_proportion from boss_advertiser_account_fund_day_report
            group by agent_id, day_time,billing_proportion) x
            group by agent_id
        ),
        <!--订单金额之和 -->
        sumOrderAmount as (
            select agent_id, sum(order_amount) as sum_order_amount
            from boss_order where order_status = 0 or order_status = 1
            group by agent_id
        ),
        accountData as  (
            SELECT agent_id AS agent_id,
            bill_account_num,
            row_number() over(partition by agent_id order by day_time desc) as ranks
            from boss_advertiser_account_fund_day_report
        ),
        <!--          计费投放账户数（巨量引擎） 授权投放账户消耗金额（巨量引擎 套餐量 套餐使用量 套餐余量-->
        consumeData as  (
            select sum (fund) as fund_consume,agent_id from (
                select
                case when billing_proportion=0 then 0
                else
                sum(fund) * billing_proportion /100
                end
                as fund,agent_id  from boss_advertiser_account_fund_day_report baafdr
                <if test="vo.startTime != null and vo.endTime != null">
                    where day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
                </if>
                group by agent_id,billing_proportion
            ) as tool group by agent_id
        ),

        fundData as (
             select sum(fund) as fund,agent_id from  boss_advertiser_account_fund_day_report  group by agent_id
              )
        select
             status, created_at, a.agent_id, a.license::json->>'oceanEngineConstructTrackUrlLicenseStatus' as ocean_engine_construct_track_url_license_status,
             a.white_types,portal_url, company_name, remarks, sign_email, coalesce(combo_count, 0)
             as combo_count, coalesce(pv_balance, 0) as pv_balance ,username,phone,receive_email,
             sign_status,
             source as customer_source, customer_source_id, last_combo, billing_mode, billing_proportion,last_combo_id,
             date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 as days_used,
             coalesce(landing_page_pv, 0) as combo_used,
             coalesce((combo_count - coalesce(total_landing_page_pv, 0)), 0) as combo_left,
             A.billing_account_consume,
                    A.billing_accounts,
                    A.total_combo_consume,
                    A.combo_account_consume,
                    A.combo_left_consume,
            case when billing_mode=0 then
            case
            date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
            when true
            then case when last_seven_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) /
            last_seven_day_pv, 0), 1) end
            when false
            then case when last_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) / last_day_pv,
            0), 1)
            end
            end
            else
            case
            date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
            when true
            then case when last_seven_day_fund = 0 then 0 else round(coalesce((coalesce(pv_balance + coalesce(A.combo_left_consume, 0), 0) - coalesce(combo_account_consume , 0)) /
            last_seven_day_fund, 0), 1) end
            when false
            then case when last_day_fund = 0 then 0 else round(coalesce((coalesce(pv_balance + coalesce(A.combo_left_consume, 0), 0) - coalesce(combo_account_consume, 0)) / last_day_fund,
            0), 1)
            end
            end
            END as estimate_combo_day_left,
            coalesce(identify_qr_code_num, 0) as identify_qr_code_num,
             coalesce(add_work_wechat_num, 0) as add_work_wechat_num,
             coalesce(follow_official_account_num, 0) as follow_official_account_num,
            coalesce(qiye_request_num, 0) as qiye_request_num,
            coalesce(qiye_request_success_num, 0) as qiye_request_success_num,
            coalesce(qiye_request_fail_num, 0) as qiye_request_fail_num,
            coalesce(qiye_pv_num, 0) as qiye_pv_num,
            coalesce(qiye_mini_pv_num, 0) as qiye_mini_pv_num,
            <!--以下为1.197.0新增三个数 -->
            coalesce(official_identify_qr_code_num, 0) as official_identify_qr_code_num,
            coalesce(identify_group_qr_code_num, 0) as identify_group_qr_code_num,
            coalesce(add_work_wechat_group_num, 0) as add_work_wechat_group_num,

            coalesce(qiye_wechat_official_article_page_view_num, 0) as qiye_wechat_official_article_page_view_num,
            coalesce(qiye_wechat_official_article_request_num, 0) as qiye_wechat_official_article_request_num,
            coalesce(qiye_wechat_official_article_request_success_num, 0) as qiye_wechat_official_article_request_success_num,
            coalesce(qiye_wechat_official_article_request_fail_num, 0) as qiye_wechat_official_article_request_fail_num,
            coalesce(send_sms_num, 0) as send_sms_num,
            coalesce(form_send_sms_num, 0) as form_send_sms_num,
            coalesce(order_send_sms_num, 0) as order_send_sms_num,
             case when combo_count = 0 then 0 else coalesce(sum_order_amount / combo_count, 0) end as unit_price,
             case when add_work_wechat_num = 0 then 0 when combo_count = 0 then 0 when landing_page_pv = 0 then 0 else
             coalesce(sum_order_amount / combo_count * landing_page_pv / add_work_wechat_num, 0) end as add_fans_cost,
             case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(identify_qr_code_num, 0) /
             coalesce(landing_page_pv, 0) * 100 end as identify_qr_code_rate,
             case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(follow_official_account_num, 0) /
             coalesce(landing_page_pv, 0) * 100 end as follow_official_account_rate,
             case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_work_wechat_num, 0) /
             coalesce(landing_page_pv, 0) * 100 end as add_work_wechat_rate,
             case when coalesce(identify_qr_code_num, 0) = 0 then 0 else coalesce(add_work_wechat_num, 0) /
             coalesce(identify_qr_code_num, 0) * 100 end as identify_qr_code_add_work_wechat_rate,
            <!--以下为1.197.0新增三个率 -->
            case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(official_identify_qr_code_num, 0) /
            coalesce(landing_page_pv, 0) * 100 end as official_identify_qr_code_rate,
            case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(identify_group_qr_code_num, 0) /
            coalesce(landing_page_pv, 0) * 100 end as identify_group_qr_code_rate,
            case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_work_wechat_group_num, 0) /
            coalesce(landing_page_pv, 0) * 100 end as add_work_wechat_group_rate,

            <!--以下为1.247.0新增字段 -->
            coalesce(form_submit_num, 0) as form_submit_num,
            coalesce(clue_form_submit_num, 0) as clue_form_submit_num,
            coalesce(douyin_applet_native_form_submit_num, 0) as douyin_applet_native_form_submit_num,
            (coalesce(form_submit_num, 0) + coalesce(clue_form_submit_num, 0) +  coalesce(douyin_applet_native_form_submit_num, 0)) as form_submit_total_num,
            coalesce(phone_number_recieved_num, 0) as phone_number_recieved_num,
            coalesce(active_message_authorization_num, 0) as active_message_authorization_num,
            coalesce(pop_up_display_num, 0) as pop_up_display_num,
            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(form_submit_num, 0) / coalesce(landing_page_pv, 0) * 100 end as form_submit_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
            else  (coalesce(form_submit_num, 0) + coalesce(clue_form_submit_num, 0) +  coalesce(douyin_applet_native_form_submit_num, 0)) / coalesce(landing_page_pv, 0) * 100 end as form_submit_total_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(clue_form_submit_num, 0) / coalesce(landing_page_pv, 0) * 100 end as clue_form_submit_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(douyin_applet_native_form_submit_num, 0) / coalesce(landing_page_pv, 0) * 100 end as douyin_applet_native_form_submit_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(phone_number_recieved_num, 0) / coalesce(landing_page_pv, 0) * 100 end as phone_number_recieved_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(active_message_authorization_num, 0) / coalesce(landing_page_pv, 0) * 100 end as active_message_authorization_rate,
            <!-- 1.263.0 饿了么小程序对接 -->
            coalesce(ele_pv_num,0) as ele_pv_num,
            coalesce(ele_qr_code_view_num,0) as ele_qr_code_view_num,
            coalesce(ele_identify_wechat_qr_code_num,0) as ele_identify_wechat_qr_code_num,
            coalesce(ele_add_wechat_success_num,0) as ele_add_wechat_success_num,
            <!-- 1.265.0 whatsapp-->
            coalesce(whatsapp_jump_num,0) as  whatsapp_jump_num,
            coalesce(whatsapp_add_friend_success_num,0) as  whatsapp_add_friend_success_num,
            coalesce(whatsapp_user_open_mouth_num,0) as  whatsapp_user_open_mouth_num,
            coalesce(overseas_pv_num,0)as  overseas_pv_num,
            <!-- 1.267.0 whatsapp-->
            coalesce(whatsapp_customer_prologue_num,0) as  whatsapp_customer_prologue_num,
            coalesce(whatsapp_customer_send_message_num,0) as  whatsapp_customer_send_message_num,
            <!--1.256.0 新增的订单数据-->
            coalesce(order_submit_num, 0) as order_submit_num,
            coalesce(order_finish_num, 0) as order_finish_num,
            coalesce(online_shop_buy_goods_success_num, 0) as online_shop_buy_goods_success_num,
            coalesce(douyin_applet_order_submit_num, 0) as douyin_applet_order_submit_num,
            coalesce(douyin_applet_order_finish_num, 0) as douyin_applet_order_finish_num,

            case when coalesce(landing_page_pv, 0) = 0  then 0
               else coalesce(order_submit_num, 0) / coalesce(landing_page_pv, 0) * 100 end as order_submit_rate,

            case when coalesce(landing_page_pv, 0) = 0  then 0
               else coalesce(order_finish_num, 0) / coalesce(landing_page_pv, 0) * 100 end as order_finish_rate,

            case when coalesce(landing_page_pv, 0) = 0  then 0
               else coalesce(online_shop_buy_goods_success_num, 0) / coalesce(landing_page_pv, 0) * 100 end as online_shop_buy_goods_success_rate,

            case when coalesce(landing_page_pv, 0) = 0  then 0
               else coalesce(douyin_applet_order_submit_num, 0) / coalesce(landing_page_pv, 0) * 100 end as douyin_applet_order_submit_rate,

            case when coalesce(douyin_applet_order_submit_num, 0) = 0  then 0
               else coalesce(douyin_applet_order_finish_num, 0) / coalesce(douyin_applet_order_submit_num, 0) * 100 end as douyin_applet_order_finish_rate,

            case when coalesce(landing_page_pv, 0) = 0  then 0
               else (coalesce(order_finish_num, 0) +coalesce(douyin_applet_order_finish_num, 0) ) / coalesce(landing_page_pv, 0) * 100 end as comprehensive_payment_rate,

            <!--1.264.0新增淘宝电影数据-->
            coalesce(tao_bao_movie_applet_jump_num, 0) as tao_bao_movie_applet_jump_num,
            coalesce(tao_bao_movie_applet_order_num, 0) as tao_bao_movie_applet_order_num,
            case when coalesce(landing_page_pv, 0) = 0  then 0
                 else coalesce(tao_bao_movie_applet_jump_num, 0) / coalesce(landing_page_pv, 0) * 100 end as tao_bao_movie_applet_jump_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
                 else coalesce(tao_bao_movie_applet_order_num, 0) / coalesce(landing_page_pv, 0) * 100 end as tao_bao_movie_applet_order_rate,
            <!--1.264.0-->

           <!--1.271.0新增数据-->
            coalesce(add_group_after_add_customer_service_num, 0) as add_group_after_add_customer_service_num,
            coalesce(add_group_after_follow_official_account_num, 0) as add_group_after_follow_official_account_num,
            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(add_group_after_add_customer_service_num, 0) / coalesce(landing_page_pv, 0) * 100 end as add_group_after_add_customer_service_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(add_group_after_follow_official_account_num, 0) / coalesce(landing_page_pv, 0) * 100 end as add_group_after_follow_official_account_rate,
            <!--1.313.0来源落地页pv-->
            coalesce(flow_source_jump_page_view_num, 0) as flow_source_jump_page_view_num,
             agent_type,
             belong_saler,
             belong_csm,
             landing_page_system_domain,
             cname_domain,
             most_binding_count,
             industry_id,
             sign_status,
             enable_data_pull_switch,
             replace_operation,
             case
             billing_mode is not null
             when true
             then case when billing_mode = 0 then coalesce((combo_count - coalesce(total_landing_page_pv, 0)), 0) * case when combo_count = 0 then 0 else coalesce(sum_order_amount / combo_count, 0) end
             else coalesce(pv_balance + coalesce(A.combo_left_consume, 0), 0) end
             when false
             then 0
             END as balance
         from
         (
           select
                 a.status, a.created_at, a.agent_id, a.portal_url,a.white_types, a.license, d.company_name, d.remarks, d.sign_email,
                 d.password,d.username,d.phone,d.receive_email,
                 d.combo_count as combo_count, s.source, s.id as customer_source_id, c.combo_name as last_combo,c.id as last_combo_id,c.billing_mode, c.billing_proportion,
                 ls.last_seven_day_pv, ld.last_day_pv, soa.sum_order_amount as sum_order_amount, d.agent_type, d.belong_saler,
                 d.belong_csm, d.landing_page_system_domain,
                 d.sign_status,
                 d.industry_id,
                        d.replace_operation,
                        d.combo_amount,
                accountData.bill_account_num as billing_accounts,
                d.cname_domain, d.enable_data_pull_switch, d.most_binding_count,d.pv_balance,
                <!--                 投放账户消耗金额 -->
                fundData.fund as  billing_account_consume,
                 <!--                 pv套餐量 -->
                case when c.billing_mode = 1 then d.combo_amount else null end as total_combo_consume,
                 <!--                  套餐使用量 每日投放账户消耗 * 收费比例 之和 -->
                cd.fund_consume as combo_account_consume,
                lsfd.last_seven_day_fund,
                ldfd.last_day_fund,
                fundData.fund as total_fund,
                <!--          套餐余量 -->
                d.combo_amount - cd.fund_consume as combo_left_consume

           from agent_conf a
               left join boss_agent_conf_detail d on a.agent_id = d.agent_id
               left join boss_dict s on d.customer_source = s.id and s.boss_dict_type=0
               left join (
                   select t.agent_id, combo_id from (
                   select max(created_at) created_at, agent_id from boss_order where order_status != 2 and combo_id is not null group by agent_id
                   ) t left join boss_order h on t.agent_id = h.agent_id and t.created_at = h.created_at
               )o on a.agent_id = o.agent_id
               left join boss_combo c on o.combo_id = c.id
               left join lastSevenDayPvData ls on a.agent_id = ls.agent_id
               left join lastDayPvData ld on a.agent_id = ld.agent_id
                left join lastSevenDayFundData lsfd on a.agent_id = lsfd.agent_id
                left join lastDayFundData ldfd on a.agent_id = ldfd.agent_id
               left join sumOrderAmount soa on a.agent_id = soa.agent_id
               left join consumeData cd on a.agent_id = cd.agent_id
                left join fundData on a.agent_id =fundData.agent_id
                left join accountData on a.agent_id=accountData.agent_id and ranks=1
               <where>
                   <if test="vo.status != null">
                       and a.status = #{vo.status}
                   </if>
                   <if test="vo.status == null">
                       and a.status != 3
                   </if>
                   <if test="vo.remarks != null and '' != vo.remarks">
                       and d.remarks like concat('%', #{vo.remarks}, '%')
                   </if>
                   <if test="vo.customerSource != null">
                       and d.customer_source = #{vo.customerSource}
                   </if>
                   <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@DEFAULT">
                       and d.replace_operation = 0
                   </if>
                   <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@OPERATION">
                       and d.replace_operation = 1
                   </if>
                   <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@STOP_OPERATION">
                       and d.replace_operation = 2
                   </if>
                   <if test="vo.companyName != null and '' != vo.companyName">
                       and (d.company_name like concat('%', #{vo.companyName}, '%') or d.agent_id like concat('%', #{vo.companyName}, '%'))
                   </if>
                   <if test="vo.comboName != null and '' != vo.comboName">
                       and c.combo_name like concat('%', #{vo.comboName}, '%')
                   </if>
                   <if test="vo.billingModeId != null">
                       and c.billing_mode = #{vo.billingModeId}
                   </if>
                   <if test="vo.signStatus != null and '' != vo.signStatus">
                       and d.sign_status = #{vo.signStatus}
                   </if>
                   <if test="vo.industryId != null and '' != vo.industryId and vo.industryId.size > 0">
                       and d.industry_id in
                       <foreach collection="vo.industryId" item="item" open="(" close=")" separator=",">
                           #{item}
                       </foreach>
                   </if>
                   <if test="vo.agentIds != null and '' != vo.agentIds and vo.agentIds.size > 0">
                       and d.agent_id in
                       <foreach collection="vo.agentIds" item="item" open="(" close=")" separator=",">
                           #{item}
                       </foreach>
                   </if>
                   <if test="vo.belongSaler != null">
                       and d.belong_saler in
                       <foreach collection="vo.belongSaler" item="saler" open="(" close=")" separator=",">
                           #{saler}
                       </foreach>
                   </if>
                   <if test="vo.belongCsm != null">
                       and d.belong_csm in
                       <foreach collection="vo.belongCsm" item="csm" open="(" close=")" separator=",">
                           #{csm}
                       </foreach>
                   </if>
                   <if test="vo.agentTypeId != null">
                       and d.agent_type = #{vo.agentTypeId}
                   </if>
               </where>
               ) a
               left join
               (
                select agent_id, sum(landing_page_pv) as landing_page_pv, sum(identify_qr_code_num) as identify_qr_code_num,
                   sum(add_work_wechat_num) as add_work_wechat_num, sum(follow_official_account_num) as follow_official_account_num,
                   sum(qiye_request_num) as qiye_request_num,
                   sum(qiye_request_success_num) as qiye_request_success_num,
                   sum(qiye_request_fail_num) as qiye_request_fail_num,
                   sum(qiye_pv_num) as qiye_pv_num,
                   sum(qiye_mini_pv_num) as qiye_mini_pv_num,
                   sum(official_identify_qr_code_num) as official_identify_qr_code_num,
                   sum(identify_group_qr_code_num) as identify_group_qr_code_num,
                   sum(add_work_wechat_group_num) as add_work_wechat_group_num,
                    sum(qiye_wechat_official_article_page_view_num) as qiye_wechat_official_article_page_view_num,
                    sum(qiye_wechat_official_article_request_num) as qiye_wechat_official_article_request_num,
                    sum(qiye_wechat_official_article_request_success_num) as qiye_wechat_official_article_request_success_num,
                    sum(qiye_wechat_official_article_request_fail_num) as qiye_wechat_official_article_request_fail_num,
                    sum(send_sms_num) as send_sms_num,
                    sum(form_send_sms_num) as form_send_sms_num,
                    sum(order_send_sms_num) as order_send_sms_num,

                    <!--1.247.0 新增的统计字段-->
                    sum(form_submit_num) as form_submit_num,
                    sum(clue_form_submit_num) as clue_form_submit_num,
                    sum(douyin_applet_native_form_submit_num) as douyin_applet_native_form_submit_num,
                    sum(phone_number_recieved_num) as phone_number_recieved_num,
                    sum(active_message_authorization_num) as active_message_authorization_num,
                    sum(pop_up_display_num) as pop_up_display_num,
                    <!-- 1.263.0 饿了么小程序对接 -->
                    sum(ele_pv_num) as ele_pv_num,
                    sum(ele_qr_code_view_num) as ele_qr_code_view_num,
                    sum(ele_identify_wechat_qr_code_num) as ele_identify_wechat_qr_code_num,
                    sum(ele_add_wechat_success_num) as ele_add_wechat_success_num,
                    <!-- 1.265.0 whatsapp -->
                    sum(whatsapp_jump_num) as  whatsapp_jump_num,
                    sum(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
                    sum(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,
                    sum(overseas_pv_num) as  overseas_pv_num,
                    <!-- 1.267.0 whatsapp-->
                    sum(whatsapp_customer_prologue_num) as  whatsapp_customer_prologue_num,
                    sum(whatsapp_customer_send_message_num) as  whatsapp_customer_send_message_num,
                    <!--1.256.0新增的订单相关数据字段-->
                    sum(order_submit_num) as order_submit_num,
                    sum(order_finish_num) as order_finish_num,
                    sum(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
                    sum(douyin_applet_order_submit_num) as douyin_applet_order_submit_num,
                    sum(douyin_applet_order_finish_num) as douyin_applet_order_finish_num,
                    <!--1.256.0新增淘宝电影相关的数据-->
                    sum(tao_bao_movie_applet_jump_num) as tao_bao_movie_applet_jump_num,
                    sum(tao_bao_movie_applet_order_num) as tao_bao_movie_applet_order_num,

                    <!--1.271.0新增的淘宝电影相关数据字段-->
                    SUM ( add_group_after_add_customer_service_num) AS add_group_after_add_customer_service_num,
                    SUM ( add_group_after_follow_official_account_num ) AS add_group_after_follow_official_account_num,
                    <!--1.313.0来源跳转页pv-->
                    SUM ( flow_source_jump_page_view_num ) AS flow_source_jump_page_view_num
                from boss_advertiser_account_group_day_report
                   <if test="vo.startTime != null and vo.endTime != null">
                       where day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
                   </if>
                   group by agent_id
               ) b
               on a.agent_id = b.agent_id

               left join
               (
                   select agent_id, sum(landing_page_pv) as total_landing_page_pv
                   from boss_advertiser_account_group_day_report
                   group by agent_id
               ) c
               on a.agent_id = c.agent_id

               <if test="vo.sort != null and vo.order != null">
                   order by
                   <if test="vo.sort =='createdAt'">
                       created_at ${vo.order}
                   </if>
                   <if test="vo.sort == 'daysUsed'">
                       days_used ${vo.order}
                   </if>
                   <if test="vo.sort == 'comboCount'">
                       combo_count ${vo.order}
                   </if>
                   <if test="vo.sort == 'comboUsed'">
                       combo_used ${vo.order}
                   </if>
                   <if test="vo.sort == 'comboLeft'">
                       combo_left ${vo.order}
                   </if>
                   <if test="vo.sort == 'estimateComboDayLeft'">
                       estimate_combo_day_left ${vo.order}
                   </if>
                   <if test="vo.sort == 'identifyQrCodeNum'">
                       identify_qr_code_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'followOfficialAccountNum'">
                       follow_official_account_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'addWorkWechatNum'">
                       add_work_wechat_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'unitPrice'">
                       unit_price ${vo.order}
                   </if>
                   <if test="vo.sort == 'addFansCost'">
                       add_fans_cost ${vo.order}
                   </if>
                   <if test="vo.sort == 'identifyQrCodeRate'">
                       identify_qr_code_rate ${vo.order}
                   </if>
                   <if test="vo.sort == 'followOfficialAccountRate'">
                       follow_official_account_rate ${vo.order}
                   </if>
                   <if test="vo.sort == 'addWorkWechatRate'">
                       add_work_wechat_rate ${vo.order}
                   </if>
                   <if test="vo.sort == 'identifyQrCodeAddWorkWechatRate'">
                       identify_qr_code_add_work_wechat_rate ${vo.order}
                   </if>
                   <if test="vo.sort == 'balance'">
                       balance ${vo.order}
                   </if>
                   <if test="vo.sort == 'billingAccounts'">
                       billing_accounts ${vo.order} nulls last
                   </if>
                   <if test="vo.sort == 'billingAccountConsume'">
                       billing_account_consume ${vo.order} nulls last
                   </if>
                   <if test="vo.sort == 'totalComboConsume'">
                       total_combo_consume ${vo.order} nulls last
                   </if>
                   <if test="vo.sort == 'comboAccountConsume'">
                       combo_account_consume ${vo.order} nulls last
                   </if>
                   <if test="vo.sort == 'comboLeftConsume'">
                       combo_left_consume ${vo.order} nulls last
                   </if>
                   <if test="vo.sort == 'qiyeRequestNum'">
                       qiye_request_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'qiyeRequestSuccessNum'">
                       qiye_request_success_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'qiyeRequestFailNum'">
                       qiye_request_fail_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'qiyePvNum'">
                       qiye_pv_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'qiyeMiniPvNum'">
                       qiye_mini_pv_num ${vo.order}
                   </if>
                   <!--以下为1.197.0新增三个排序 -->

                   <if test="vo.sort == 'officialIdentifyQrCodeNum'">
                       official_identify_qr_code_num ${vo.order} nulls last
                   </if>
                   <if test="vo.sort == 'identifyGroupQrCodeNum'">
                       identify_group_qr_code_num ${vo.order} nulls last
                   </if>
                   <if test="vo.sort == 'addWorkWechatGroupNum'">
                       add_work_wechat_group_num ${vo.order} nulls last
                   </if>
                   <if test="vo.sort == 'officialIdentifyQrCodeRate'">
                       official_identify_qr_code_rate ${vo.order} nulls last
                   </if>
                   <if test="vo.sort == 'identifyGroupQrCodeRate'">
                       identify_group_qr_code_rate ${vo.order} nulls last
                   </if>
                   <if test="vo.sort == 'addWorkWechatGroupRate'">
                       add_work_wechat_group_rate ${vo.order} nulls last
                   </if>
                   <if test="vo.sort == 'qiyeWechatOfficialArticlePageViewNum'">
                       qiye_wechat_official_article_page_view_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'qiyeWechatOfficialArticleRequestNum'">
                       qiye_wechat_official_article_request_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'qiyeWechatOfficialArticleRequestSuccessNum'">
                       qiye_wechat_official_article_request_success_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'qiyeWechatOfficialArticleRequestFailNum'">
                      qiye_wechat_official_article_request_fail_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'sendSmsNum'">
                       send_sms_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'formSendSmsNum'">
                       form_send_sms_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'orderSendSmsNum'">
                       order_send_sms_num ${vo.order}
                   </if>

                   <if test="vo.sort == 'formSubmitNum'">
                       form_submit_num ${vo.order}
                   </if>

                   <if test="vo.sort == 'formSubmitRate'">
                       form_submit_rate ${vo.order}
                   </if>

                   <if test="vo.sort == 'formSubmitTotalNum'">
                       form_submit_total_num ${vo.order}
                   </if>

                   <!--1.247.0新增的排序字段-->
                   <if test="vo.sort == 'formSubmitTotalRate'">
                       form_submit_total_rate ${vo.order}
                   </if>

                   <if test="vo.sort == 'clueFormSubmitNum'">
                       clue_form_submit_num ${vo.order}
                   </if>

                   <if test="vo.sort == 'clueFormSubmitRate'">
                       clue_form_submit_rate ${vo.order}
                   </if>

                   <if test="vo.sort == 'douyinAppletNativeFormSubmitNum'">
                       douyin_applet_native_form_submit_num ${vo.order}
                   </if>

                   <if test="vo.sort == 'douyinAppletNativeFormSubmitRate'">
                       douyin_applet_native_form_submit_rate ${vo.order}
                   </if>

                   <if test="vo.sort == 'phoneNumberRecievedNum'">
                       phone_number_recieved_num ${vo.order}
                   </if>

                   <if test="vo.sort == 'phoneNumberRecievedRate'">
                       phone_number_recieved_rate ${vo.order}
                   </if>

                   <if test="vo.sort == 'activeMessageAuthorizationNum'">
                       active_message_authorization_num ${vo.order}
                   </if>

                   <if test="vo.sort == 'activeMessageAuthorizationRate'">
                       active_message_authorization_rate ${vo.order}
                   </if>

                   <if test="vo.sort == 'popUpDisplayNum'">
                       pop_up_display_num ${vo.order}
                   </if>

                   <!--1.256.0新增字段-->
                   <if test="vo.sort == 'orderSubmitNum'">
                       order_submit_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'orderSubmitRate'">
                       order_submit_rate ${vo.order}
                   </if>
                   <if test="vo.sort == 'orderFinishNum'">
                       order_finish_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'orderFinishRate'">
                       order_finish_rate ${vo.order}
                   </if>
                   <if test="vo.sort == 'douyinAppletOrderSubmitNum'">
                       douyin_applet_order_submit_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'douyinAppletOrderFinishNum'">
                       douyin_applet_order_finish_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'douyinAppletOrderSubmitRate'">
                       douyin_applet_order_submit_rate ${vo.order}
                   </if>
                   <if test="vo.sort == 'douyinAppletOrderFinishRate'">
                       douyin_applet_order_finish_rate ${vo.order}
                   </if>

                   <if test="vo.sort == 'onlineShopBuyGoodsSuccessNum'">
                       online_shop_buy_goods_success_num ${vo.order}
                   </if>

                   <if test="vo.sort == 'onlineShopBuyGoodsSuccessRate'">
                       online_shop_buy_goods_success_rate ${vo.order}
                   </if>

                   <if test="vo.sort == 'comprehensivePaymentRate'">
                       comprehensive_payment_rate ${vo.order}
                   </if>
                   <if test="vo.sort == 'elePvNum'">
                       ele_pv_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'eleQrCodeViewNum'">
                       ele_qr_code_view_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'eleIdentifyWechatQrCodeNum'">
                       ele_identify_wechat_qr_code_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'eleAddWechatSuccessNum'">
                       ele_add_wechat_success_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'whatsappJumpNum'">
                       whatsapp_jump_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'whatsappAddFriendSuccessNum'">
                       whatsapp_add_friend_success_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'whatsappUserOpenMouthNum'">
                       whatsapp_user_open_mouth_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'overseasPvNum'">
                       overseas_pv_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'whatsappCustomerPrologueNum'">
                       whatsapp_customer_prologue_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'whatsappCustomerSendMessageNum'">
                       whatsapp_customer_send_message_num ${vo.order}
                   </if>
                   <if test="vo.sort == 'taoBaoMovieAppletJumpNum'">
                       tao_bao_movie_applet_jump_num ${vo.order}
                   </if>

                   <if test="vo.sort == 'taoBaoMovieAppletJumpRate'">
                       tao_bao_movie_applet_jump_rate ${vo.order}
                   </if>

                   <if test="vo.sort == 'taoBaoMovieAppletOrderNum'">
                       tao_bao_movie_applet_order_num ${vo.order}
                   </if>

                   <if test="vo.sort == 'taoBaoMovieAppletOrderRate'">
                       tao_bao_movie_applet_order_rate ${vo.order}
                   </if>

                   <if test="vo.sort == 'addGroupAfterAddCustomerServiceNum'">
                       add_group_after_add_customer_service_num ${vo.order}
                   </if>

                   <if test="vo.sort == 'addGroupAfterAddCustomerServiceRate'">
                       add_group_after_add_customer_service_rate ${vo.order}
                   </if>

                   <if test="vo.sort == 'addGroupAfterFollowOfficialAccountNum'">
                       add_group_after_follow_official_account_num ${vo.order}
                   </if>

                   <if test="vo.sort == 'addGroupAfterFollowOfficialAccountRate'">
                       add_group_after_follow_official_account_rate ${vo.order}
                   </if>
                    <if test="vo.sort == 'flowSourceJumpPageViewNum'">
                        flow_source_jump_page_view_num ${vo.order}
                    </if>
               </if>
           </select>

    <select id="pageCustomerByConditionsNew" resultType="ai.yiye.agent.boss.vo.BossCustomerVo">
        <!--最近7天的落地页pv均值 -->
        with lastSevenDayPvData as (
            select agent_id, sum(landing_page_pv) / 7 as last_seven_day_pv from
            (select sum(landing_page_pv) landing_page_pv,agent_id, day_time from boss_advertiser_account_group_day_report_new
            group by agent_id, day_time) y
            where day_time between now()::timestamp + '-7 day' and now()
            group by agent_id
        ),

        <!--若不满7天，近7天套餐使用量(落地页PV)=所有天数套餐使用量(落地页PV)/所有天数； -->
        lastDayPvData as (
            select agent_id, sum(landing_page_pv) / count(*) as last_day_pv
            from
            (select sum(landing_page_pv) landing_page_pv,agent_id from boss_advertiser_account_group_day_report_new
            group by agent_id, day_time) x
            group by agent_id
        ),

        lastSevenDayFundData as (
            select agent_id, sum(fund) / 7  as last_seven_day_fund from
            (select sum(fund) /100 * billing_proportion  as fund,agent_id, day_time from boss_advertiser_account_fund_day_report
            group by agent_id, day_time,billing_proportion) y
            where day_time between now()::timestamp + '-7 day' and now()
            group by agent_id
        ),

        lastDayFundData as (
            select agent_id, sum(fund)/count(*)  as last_day_fund
            from
            (select sum(fund)  *billing_proportion /100 as fund,agent_id, day_time, billing_proportion from boss_advertiser_account_fund_day_report
            group by agent_id, day_time,billing_proportion) x
            group by agent_id
        ),

        <!--订单金额之和 -->
        sumOrderAmount as (
            select agent_id, sum(order_amount) as sum_order_amount
            from boss_order where order_status = 0 or order_status = 1
            group by agent_id
        ),

        accountData as  (
            SELECT agent_id AS agent_id,
            bill_account_num,
            row_number() over(partition by agent_id order by day_time desc) as ranks
            from boss_advertiser_account_fund_day_report
        ),

        <!--          计费投放账户数（巨量引擎） 授权投放账户消耗金额（巨量引擎 套餐量 套餐使用量 套餐余量-->
        consumeData as  (				 select sum (fund) as fund_consume,agent_id from (
            select
            case when billing_proportion=0 then 0
            else
            sum(fund) * billing_proportion /100
            end
            as fund,agent_id  from boss_advertiser_account_fund_day_report baafdr
            <if test="vo.startTime != null and vo.endTime != null">
                where day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
            </if>
            group by agent_id,billing_proportion
            ) as tool group by agent_id
        ),

        fundData as (
            select sum(fund) as fund,agent_id from  boss_advertiser_account_fund_day_report  group by agent_id
            )
        select
            status, created_at, a.agent_id, a.license::json->>'oceanEngineConstructTrackUrlLicenseStatus' as ocean_engine_construct_track_url_license_status,
            a.white_types,portal_url, company_name, remarks, sign_email, coalesce(combo_count, 0)
            as combo_count, coalesce(pv_balance, 0) as pv_balance ,username,phone,receive_email,
            sign_status,
            source as customer_source, customer_source_id, last_combo, billing_mode, billing_proportion,last_combo_id,
            date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 as days_used,
            coalesce(landing_page_pv, 0) as combo_used,
            coalesce((combo_count - coalesce(total_landing_page_pv, 0)), 0) as combo_left,
            A.billing_account_consume,
            A.billing_accounts,
            A.total_combo_consume,
            A.combo_account_consume,
            A.combo_left_consume,
            case when billing_mode=0 then
            case
            date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
            when true
            then case when last_seven_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) /
            last_seven_day_pv, 0), 1) end
            when false
            then case when last_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) / last_day_pv,
            0), 1)
            end
            end
            else
            case
            date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
            when true
            then case when last_seven_day_fund = 0 then 0 else round(coalesce((coalesce(pv_balance + coalesce(A.combo_left_consume, 0), 0) - coalesce(combo_account_consume , 0)) /
            last_seven_day_fund, 0), 1) end
            when false
            then case when last_day_fund = 0 then 0 else round(coalesce((coalesce(pv_balance + coalesce(A.combo_left_consume, 0), 0) - coalesce(combo_account_consume, 0)) / last_day_fund,
            0), 1)
            end
            end
            END as estimate_combo_day_left,
            coalesce(identify_qr_code_num, 0) as identify_qr_code_num,
            coalesce(add_work_wechat_num, 0) as add_work_wechat_num,
            coalesce(follow_official_account_num, 0) as follow_official_account_num,
            coalesce(qiye_request_num, 0) as qiye_request_num,
            coalesce(qiye_request_success_num, 0) as qiye_request_success_num,
            coalesce(qiye_request_fail_num, 0) as qiye_request_fail_num,
            coalesce(qiye_pv_num, 0) as qiye_pv_num,
            coalesce(qiye_mini_pv_num, 0) as qiye_mini_pv_num,
            <!--以下为1.197.0新增三个数 -->
            coalesce(official_identify_qr_code_num, 0) as official_identify_qr_code_num,
            coalesce(identify_group_qr_code_num, 0) as identify_group_qr_code_num,
            coalesce(add_work_wechat_group_num, 0) as add_work_wechat_group_num,

            coalesce(qiye_wechat_official_article_page_view_num, 0) as qiye_wechat_official_article_page_view_num,
            coalesce(qiye_wechat_official_article_request_num, 0) as qiye_wechat_official_article_request_num,
            coalesce(qiye_wechat_official_article_request_success_num, 0) as qiye_wechat_official_article_request_success_num,
            coalesce(qiye_wechat_official_article_request_fail_num, 0) as qiye_wechat_official_article_request_fail_num,
            coalesce(send_sms_num, 0) as send_sms_num,
            coalesce(form_send_sms_num, 0) as form_send_sms_num,
            coalesce(order_send_sms_num, 0) as order_send_sms_num,

            <!--以下为1.247.0新增字段 -->
            coalesce(form_submit_num, 0) as form_submit_num,
            coalesce(clue_form_submit_num, 0) as clue_form_submit_num,
            coalesce(douyin_applet_native_form_submit_num, 0) as douyin_applet_native_form_submit_num,
            (coalesce(form_submit_num, 0) + coalesce(clue_form_submit_num, 0) +  coalesce(douyin_applet_native_form_submit_num, 0)) as form_submit_total_num,
            coalesce(phone_number_recieved_num, 0) as phone_number_recieved_num,
            coalesce(active_message_authorization_num, 0) as active_message_authorization_num,
            coalesce(pop_up_display_num, 0) as pop_up_display_num,
            case when coalesce(landing_page_pv, 0) = 0  then 0
                 else coalesce(form_submit_num, 0) / coalesce(landing_page_pv, 0) * 100 end as form_submit_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
                 else  (coalesce(form_submit_num, 0) + coalesce(clue_form_submit_num, 0) +  coalesce(douyin_applet_native_form_submit_num, 0)) / coalesce(landing_page_pv, 0) * 100 end as form_submit_total_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
                 else coalesce(clue_form_submit_num, 0) / coalesce(landing_page_pv, 0) * 100 end as clue_form_submit_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
                 else coalesce(douyin_applet_native_form_submit_num, 0) / coalesce(landing_page_pv, 0) * 100 end as douyin_applet_native_form_submit_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
                 else coalesce(phone_number_recieved_num, 0) / coalesce(landing_page_pv, 0) * 100 end as phone_number_recieved_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
                 else coalesce(active_message_authorization_num, 0) / coalesce(landing_page_pv, 0) * 100 end as active_message_authorization_rate,

            case when combo_count = 0 then 0 else coalesce(sum_order_amount / combo_count, 0) end as unit_price,
            case when add_work_wechat_num = 0 then 0 when combo_count = 0 then 0 when landing_page_pv = 0 then 0 else
            coalesce(sum_order_amount / combo_count * landing_page_pv / add_work_wechat_num, 0) end as add_fans_cost,
            case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(identify_qr_code_num, 0) /
            coalesce(landing_page_pv, 0) * 100 end as identify_qr_code_rate,
            case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(follow_official_account_num, 0) /
            coalesce(landing_page_pv, 0) * 100 end as follow_official_account_rate,
            case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_work_wechat_num, 0) /
            coalesce(landing_page_pv, 0) * 100 end as add_work_wechat_rate,
            case when coalesce(identify_qr_code_num, 0) = 0 then 0 else coalesce(add_work_wechat_num, 0) /
            coalesce(identify_qr_code_num, 0) * 100 end as identify_qr_code_add_work_wechat_rate,
            <!--以下为1.197.0新增三个率 -->
            case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(official_identify_qr_code_num, 0) /
            coalesce(landing_page_pv, 0) * 100 end as official_identify_qr_code_rate,
            case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(identify_group_qr_code_num, 0) /
            coalesce(landing_page_pv, 0) * 100 end as identify_group_qr_code_rate,
            case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_work_wechat_group_num, 0) /
            coalesce(landing_page_pv, 0) * 100 end as add_work_wechat_group_rate,
            <!-- 1.263.0 饿了么小程序对接 -->
            coalesce(ele_pv_num,0) as ele_pv_num,
            coalesce(ele_qr_code_view_num,0) as ele_qr_code_view_num,
            coalesce(ele_identify_wechat_qr_code_num,0) as ele_identify_wechat_qr_code_num,
            coalesce(ele_add_wechat_success_num,0) as ele_add_wechat_success_num,
            <!-- 1.265.0 whatsapp-->
            coalesce(whatsapp_jump_num,0) as  whatsapp_jump_num,
            coalesce(whatsapp_add_friend_success_num,0) as  whatsapp_add_friend_success_num,
            coalesce(whatsapp_user_open_mouth_num,0) as  whatsapp_user_open_mouth_num,
            coalesce(overseas_pv_num)as  overseas_pv_num,
            <!-- 1.267.0 whatsapp-->
            coalesce(whatsapp_customer_prologue_num,0) as  whatsapp_customer_prologue_num,
            coalesce(whatsapp_customer_send_message_num,0) as  whatsapp_customer_send_message_num,
            <!--1.256.0 新增的订单数据-->
            coalesce(order_submit_num, 0) as order_submit_num,
            coalesce(order_finish_num, 0) as order_finish_num,
            coalesce(online_shop_buy_goods_success_num, 0) as online_shop_buy_goods_success_num,
            coalesce(douyin_applet_order_submit_num, 0) as douyin_applet_order_submit_num,
            coalesce(douyin_applet_order_finish_num, 0) as douyin_applet_order_finish_num,

            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(order_submit_num, 0) / coalesce(landing_page_pv, 0) * 100 end as order_submit_rate,

            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(order_finish_num, 0) / coalesce(landing_page_pv, 0) * 100 end as order_finish_rate,

            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(online_shop_buy_goods_success_num, 0) / coalesce(landing_page_pv, 0) * 100 end as online_shop_buy_goods_success_rate,

            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(douyin_applet_order_submit_num, 0) / coalesce(landing_page_pv, 0) * 100 end as douyin_applet_order_submit_rate,

            case when coalesce(douyin_applet_order_submit_num, 0) = 0  then 0
            else coalesce(douyin_applet_order_finish_num, 0) / coalesce(douyin_applet_order_submit_num, 0) * 100 end as douyin_applet_order_finish_rate,

            case when coalesce(landing_page_pv, 0) = 0  then 0
            else (coalesce(order_finish_num, 0) +coalesce(douyin_applet_order_finish_num, 0) ) / coalesce(landing_page_pv, 0) * 100 end as comprehensive_payment_rate,

            <!--1.264.0新增淘宝电影数据-->
            coalesce(tao_bao_movie_applet_jump_num, 0) as tao_bao_movie_applet_jump_num,
            coalesce(tao_bao_movie_applet_order_num, 0) as tao_bao_movie_applet_order_num,
            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(tao_bao_movie_applet_jump_num, 0) / coalesce(landing_page_pv, 0) * 100 end as tao_bao_movie_applet_jump_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
            else coalesce(tao_bao_movie_applet_order_num, 0) / coalesce(landing_page_pv, 0) * 100 end as tao_bao_movie_applet_order_rate,

            <!--1.271.0新增数据-->
            coalesce(add_group_after_add_customer_service_num, 0) as add_group_after_add_customer_service_num,
            coalesce(add_group_after_follow_official_account_num, 0) as add_group_after_follow_official_account_num,
            case when coalesce(landing_page_pv, 0) = 0  then 0
              else coalesce(add_group_after_add_customer_service_num, 0) / coalesce(landing_page_pv, 0) * 100 end as add_group_after_add_customer_service_rate,
            case when coalesce(landing_page_pv, 0) = 0  then 0
              else coalesce(add_group_after_follow_official_account_num, 0) / coalesce(landing_page_pv, 0) * 100 end as add_group_after_follow_official_account_rate,
            <!-- 1.313.0来源落地页pv-->
            coalesce(flow_source_jump_page_view_num, 0) as flow_source_jump_page_view_num,
            agent_type,
            belong_saler,
            belong_csm,
            landing_page_system_domain,
            cname_domain,
            most_binding_count,
            industry_id,
            sign_status,
            enable_data_pull_switch,
            replace_operation,
            case
            billing_mode is not null
            when true
            then case when billing_mode = 0 then coalesce((combo_count - coalesce(total_landing_page_pv, 0)), 0) * case when combo_count = 0 then 0 else coalesce(sum_order_amount / combo_count, 0) end
            else coalesce(pv_balance + coalesce(A.combo_left_consume, 0), 0) end
            when false
            then 0
            END as balance
        from
        (
            select a.status, a.created_at, a.agent_id, a.portal_url,a.white_types, a.license, d.company_name, d.remarks, d.sign_email,
            d.password,d.username,d.phone,d.receive_email,
            d.combo_count as combo_count, s.source, s.id as customer_source_id, c.combo_name as last_combo,c.id as last_combo_id,c.billing_mode, c.billing_proportion,
            ls.last_seven_day_pv, ld.last_day_pv, soa.sum_order_amount as sum_order_amount, d.agent_type, d.belong_saler,
            d.belong_csm, d.landing_page_system_domain,
            d.sign_status,
            d.industry_id,
            d.replace_operation,
            d.combo_amount,
            accountData.bill_account_num as billing_accounts,
            d.cname_domain, d.enable_data_pull_switch, d.most_binding_count,d.pv_balance,
            <!--                 投放账户消耗金额 -->
            fundData.fund as  billing_account_consume,
            <!--                 pv套餐量 -->
            case when c.billing_mode = 1 then d.combo_amount else null end as total_combo_consume,
            <!--                  套餐使用量 每日投放账户消耗 * 收费比例 之和 -->
            cd.fund_consume as combo_account_consume,
            lsfd.last_seven_day_fund,
            ldfd.last_day_fund,
            fundData.fund as total_fund,
            <!--          套餐余量 -->
            d.combo_amount - cd.fund_consume as combo_left_consume

            from agent_conf a
            left join boss_agent_conf_detail d on a.agent_id = d.agent_id
            left join boss_dict s on d.customer_source = s.id and s.boss_dict_type=0
            left join (
            select t.agent_id, combo_id from (
            select max(created_at) created_at, agent_id from boss_order where order_status != 2 and combo_id is not null group by agent_id
            ) t left join boss_order h on t.agent_id = h.agent_id and t.created_at = h.created_at
            )o on a.agent_id = o.agent_id
            left join boss_combo c on o.combo_id = c.id
            left join lastSevenDayPvData ls on a.agent_id = ls.agent_id
            left join lastDayPvData ld on a.agent_id = ld.agent_id
            left join lastSevenDayFundData lsfd on a.agent_id = lsfd.agent_id
            left join lastDayFundData ldfd on a.agent_id = ldfd.agent_id
            left join sumOrderAmount soa on a.agent_id = soa.agent_id
            left join consumeData cd on a.agent_id = cd.agent_id
            left join fundData on a.agent_id =fundData.agent_id
            left join accountData on a.agent_id=accountData.agent_id and ranks=1
            <where>
                <if test="vo.status != null">
                    and a.status = #{vo.status}
                </if>
                <if test="vo.status == null">
                    and a.status != 3
                </if>
                <if test="vo.remarks != null and '' != vo.remarks">
                    and d.remarks like concat('%', #{vo.remarks}, '%')
                </if>
                <if test="vo.customerSource != null">
                    and d.customer_source = #{vo.customerSource}
                </if>
                <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@DEFAULT">
                    and d.replace_operation = 0
                </if>
                <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@OPERATION">
                    and d.replace_operation = 1
                </if>
                <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@STOP_OPERATION">
                    and d.replace_operation = 2
                </if>
                <if test="vo.companyName != null and '' != vo.companyName">
                    and (d.company_name like concat('%', #{vo.companyName}, '%') or d.agent_id like concat('%', #{vo.companyName}, '%'))
                </if>
                <if test="vo.comboName != null and '' != vo.comboName">
                    and c.combo_name like concat('%', #{vo.comboName}, '%')
                </if>
                <if test="vo.billingModeId != null">
                    and c.billing_mode = #{vo.billingModeId}
                </if>
                <if test="vo.signStatus != null and '' != vo.signStatus">
                    and d.sign_status = #{vo.signStatus}
                </if>
                <if test="vo.industryId != null and '' != vo.industryId and vo.industryId.size > 0">
                    and d.industry_id in
                    <foreach collection="vo.industryId" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="vo.agentIds != null and '' != vo.agentIds and vo.agentIds.size > 0">
                    and d.agent_id in
                    <foreach collection="vo.agentIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="vo.belongSaler != null">
                    and d.belong_saler in
                    <foreach collection="vo.belongSaler" item="saler" open="(" close=")" separator=",">
                        #{saler}
                    </foreach>
                </if>
                <if test="vo.belongCsm != null">
                    and d.belong_csm in
                    <foreach collection="vo.belongCsm" item="csm" open="(" close=")" separator=",">
                        #{csm}
                    </foreach>
                </if>
                <if test="vo.agentTypeId != null">
                    and d.agent_type = #{vo.agentTypeId}
                </if>
            </where>
        ) a
        left join
        (
            select agent_id, sum(landing_page_pv) as landing_page_pv, sum(identify_qr_code_num) as identify_qr_code_num,
            sum(add_work_wechat_num) as add_work_wechat_num, sum(follow_official_account_num) as follow_official_account_num,
            sum(qiye_request_num) as qiye_request_num,
            sum(qiye_request_success_num) as qiye_request_success_num,
            sum(qiye_request_fail_num) as qiye_request_fail_num,
            sum(qiye_pv_num) as qiye_pv_num,
            sum(qiye_mini_pv_num) as qiye_mini_pv_num,
            sum(official_identify_qr_code_num) as official_identify_qr_code_num,
            sum(identify_group_qr_code_num) as identify_group_qr_code_num,
            sum(add_work_wechat_group_num) as add_work_wechat_group_num,
            sum(qiye_wechat_official_article_page_view_num) as qiye_wechat_official_article_page_view_num,
            sum(qiye_wechat_official_article_request_num) as qiye_wechat_official_article_request_num,
            sum(qiye_wechat_official_article_request_success_num) as qiye_wechat_official_article_request_success_num,
            sum(qiye_wechat_official_article_request_fail_num) as qiye_wechat_official_article_request_fail_num,
            sum(send_sms_num) as send_sms_num,
            sum(form_send_sms_num) as form_send_sms_num,
            sum(order_send_sms_num) as order_send_sms_num,
            <!--1.247.0 新增的统计字段-->
            sum(form_submit_num) as form_submit_num,
            sum(clue_form_submit_num) as clue_form_submit_num,
            sum(douyin_applet_native_form_submit_num) as douyin_applet_native_form_submit_num,
            sum(phone_number_recieved_num) as phone_number_recieved_num,
            sum(active_message_authorization_num) as active_message_authorization_num,
            sum(pop_up_display_num) as pop_up_display_num,
            <!-- 1.263.0 饿了么小程序对接 -->
            sum(ele_pv_num) as ele_pv_num,
            sum(ele_qr_code_view_num) as ele_qr_code_view_num,
            sum(ele_identify_wechat_qr_code_num) as ele_identify_wechat_qr_code_num,
            sum(ele_add_wechat_success_num) as ele_add_wechat_success_num,
            <!--1.265.0 whatsapp -->
            sum(overseas_pv_num) as  overseas_pv_num,
            sum(whatsapp_jump_num) as  whatsapp_jump_num,
            sum(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
            sum(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,
            <!-- 1.267.0 whatsapp-->
            sum(whatsapp_customer_prologue_num) as  whatsapp_customer_prologue_num,
            sum(whatsapp_customer_send_message_num) as  whatsapp_customer_send_message_num,
            <!--1.256.0新增的订单相关数据字段-->
            sum(order_submit_num) as order_submit_num,
            sum(order_finish_num) as order_finish_num,
            sum(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
            sum(douyin_applet_order_submit_num) as douyin_applet_order_submit_num,
            sum(douyin_applet_order_finish_num) as douyin_applet_order_finish_num,

             <!--1.264.0新增的淘宝电影相关数据字段-->
            SUM ( tao_bao_movie_applet_jump_num) AS tao_bao_movie_applet_jump_num,
            SUM ( tao_bao_movie_applet_order_num ) AS tao_bao_movie_applet_order_num,
            <!--1.271.0新增的淘宝电影相关数据字段-->
            SUM ( add_group_after_add_customer_service_num) AS add_group_after_add_customer_service_num,
            SUM ( add_group_after_follow_official_account_num ) AS add_group_after_follow_official_account_num,
            <!--1.313.0来源跳转页PV-->
            SUM ( flow_source_jump_page_view_num ) AS flow_source_jump_page_view_num
        from boss_advertiser_account_group_day_report_new
            <if test="vo.startTime != null and vo.endTime != null">
                where day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
            </if>
            group by agent_id
        ) b
        on a.agent_id = b.agent_id

        left join
        (
            select agent_id, sum(landing_page_pv) as total_landing_page_pv
            from boss_advertiser_account_group_day_report_new
            group by agent_id
        ) c
        on a.agent_id = c.agent_id

        <if test="vo.sort != null and vo.order != null">
            order by
            <if test="vo.sort =='createdAt'">
                created_at ${vo.order}
            </if>
            <if test="vo.sort == 'daysUsed'">
                days_used ${vo.order}
            </if>
            <if test="vo.sort == 'comboCount'">
                combo_count ${vo.order}
            </if>
            <if test="vo.sort == 'comboUsed'">
                combo_used ${vo.order}
            </if>
            <if test="vo.sort == 'comboLeft'">
                combo_left ${vo.order}
            </if>
            <if test="vo.sort == 'estimateComboDayLeft'">
                estimate_combo_day_left ${vo.order}
            </if>
            <if test="vo.sort == 'identifyQrCodeNum'">
                identify_qr_code_num ${vo.order}
            </if>
            <if test="vo.sort == 'followOfficialAccountNum'">
                follow_official_account_num ${vo.order}
            </if>
            <if test="vo.sort == 'addWorkWechatNum'">
                add_work_wechat_num ${vo.order}
            </if>
            <if test="vo.sort == 'unitPrice'">
                unit_price ${vo.order}
            </if>
            <if test="vo.sort == 'addFansCost'">
                add_fans_cost ${vo.order}
            </if>
            <if test="vo.sort == 'identifyQrCodeRate'">
                identify_qr_code_rate ${vo.order}
            </if>
            <if test="vo.sort == 'followOfficialAccountRate'">
                follow_official_account_rate ${vo.order}
            </if>
            <if test="vo.sort == 'addWorkWechatRate'">
                add_work_wechat_rate ${vo.order}
            </if>
            <if test="vo.sort == 'identifyQrCodeAddWorkWechatRate'">
                identify_qr_code_add_work_wechat_rate ${vo.order}
            </if>
            <if test="vo.sort == 'balance'">
                balance ${vo.order}
            </if>
            <if test="vo.sort == 'billingAccounts'">
                billing_accounts ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'billingAccountConsume'">
                billing_account_consume ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'totalComboConsume'">
                total_combo_consume ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'comboAccountConsume'">
                combo_account_consume ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'comboLeftConsume'">
                combo_left_consume ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'qiyeRequestNum'">
                qiye_request_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeRequestSuccessNum'">
                qiye_request_success_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeRequestFailNum'">
                qiye_request_fail_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyePvNum'">
                qiye_pv_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeMiniPvNum'">
                qiye_mini_pv_num ${vo.order}
            </if>
            <!--以下为1.197.0新增三个排序 -->

            <if test="vo.sort == 'officialIdentifyQrCodeNum'">
                official_identify_qr_code_num ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'identifyGroupQrCodeNum'">
                identify_group_qr_code_num ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'addWorkWechatGroupNum'">
                add_work_wechat_group_num ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'officialIdentifyQrCodeRate'">
                official_identify_qr_code_rate ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'identifyGroupQrCodeRate'">
                identify_group_qr_code_rate ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'addWorkWechatGroupRate'">
                add_work_wechat_group_rate ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'qiyeWechatOfficialArticlePageViewNum'">
                qiye_wechat_official_article_page_view_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeWechatOfficialArticleRequestNum'">
                qiye_wechat_official_article_request_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeWechatOfficialArticleRequestSuccessNum'">
                qiye_wechat_official_article_request_success_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeWechatOfficialArticleRequestFailNum'">
                qiye_wechat_official_article_request_fail_num ${vo.order}
            </if>
            <if test="vo.sort == 'sendSmsNum'">
                send_sms_num ${vo.order}
            </if>
            <if test="vo.sort == 'formSendSmsNum'">
                form_send_sms_num ${vo.order}
            </if>
            <if test="vo.sort == 'orderSendSmsNum'">
                order_send_sms_num ${vo.order}
            </if>


            <if test="vo.sort == 'formSubmitNum'">
                form_submit_num ${vo.order}
            </if>

            <if test="vo.sort == 'formSubmitRate'">
                form_submit_rate ${vo.order}
            </if>

            <if test="vo.sort == 'formSubmitTotalNum'">
                form_submit_total_num ${vo.order}
            </if>

            <if test="vo.sort == 'formSubmitTotalRate'">
                form_submit_total_rate ${vo.order}
            </if>

            <if test="vo.sort == 'clueFormSubmitNum'">
                clue_form_submit_num ${vo.order}
            </if>

            <if test="vo.sort == 'clueFormSubmitRate'">
                clue_form_submit_rate ${vo.order}
            </if>

            <if test="vo.sort == 'douyinAppletNativeFormSubmitNum'">
                douyin_applet_native_form_submit_num ${vo.order}
            </if>

            <if test="vo.sort == 'douyinAppletNativeFormSubmitRate'">
                douyin_applet_native_form_submit_rate ${vo.order}
            </if>

            <if test="vo.sort == 'phoneNumberRecievedNum'">
                phone_number_recieved_num ${vo.order}
            </if>

            <if test="vo.sort == 'phoneNumberRecievedRate'">
                phone_number_recieved_rate ${vo.order}
            </if>

            <if test="vo.sort == 'activeMessageAuthorizationNum'">
                active_message_authorization_num ${vo.order}
            </if>

            <if test="vo.sort == 'activeMessageAuthorizationRate'">
                active_message_authorization_rate ${vo.order}
            </if>

            <if test="vo.sort == 'popUpDisplayNum'">
                pop_up_display_num ${vo.order}
            </if>

            <!--1.256.0新增字段-->
            <if test="vo.sort == 'orderSubmitNum'">
                order_submit_num ${vo.order}
            </if>
            <if test="vo.sort == 'orderSubmitRate'">
                order_submit_rate ${vo.order}
            </if>
            <if test="vo.sort == 'orderFinishNum'">
                order_finish_num ${vo.order}
            </if>
            <if test="vo.sort == 'orderFinishRate'">
                order_finish_rate ${vo.order}
            </if>
            <if test="vo.sort == 'douyinAppletOrderSubmitNum'">
                douyin_applet_order_submit_num ${vo.order}
            </if>
            <if test="vo.sort == 'douyinAppletOrderFinishNum'">
                douyin_applet_order_finish_num ${vo.order}
            </if>
            <if test="vo.sort == 'douyinAppletOrderSubmitRate'">
                douyin_applet_order_submit_rate ${vo.order}
            </if>
            <if test="vo.sort == 'douyinAppletOrderFinishRate'">
                douyin_applet_order_finish_rate ${vo.order}
            </if>

            <if test="vo.sort == 'onlineShopBuyGoodsSuccessNum'">
                online_shop_buy_goods_success_num ${vo.order}
            </if>

            <if test="vo.sort == 'onlineShopBuyGoodsSuccessRate'">
                online_shop_buy_goods_success_rate ${vo.order}
            </if>

            <if test="vo.sort == 'comprehensivePaymentRate'">
                comprehensive_payment_rate ${vo.order}
            </if>
            <if test="vo.sort == 'elePvNum'">
                ele_pv_num ${vo.order}
            </if>
            <if test="vo.sort == 'eleQrCodeViewNum'">
                ele_qr_code_view_num ${vo.order}
            </if>
            <if test="vo.sort == 'eleIdentifyWechatQrCodeNum'">
                ele_identify_wechat_qr_code_num ${vo.order}
            </if>
            <if test="vo.sort == 'eleAddWechatSuccessNum'">
                ele_add_wechat_success_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappJumpNum'">
                whatsapp_jump_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappAddFriendSuccessNum'">
                whatsapp_add_friend_success_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappUserOpenMouthNum'">
                whatsapp_user_open_mouth_num ${vo.order}
            </if>
            <if test="vo.sort == 'overseasPvNum'">
                overseas_pv_num ${vo.order}
            </if>

            <if test="vo.sort == 'whatsappCustomerPrologueNum'">
                whatsapp_customer_prologue_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappCustomerSendMessageNum'">
                whatsapp_customer_send_message_num ${vo.order}
            </if>
            <if test="vo.sort == 'taoBaoMovieAppletJumpNum'">
                tao_bao_movie_applet_jump_num ${vo.order}
            </if>

            <if test="vo.sort == 'taoBaoMovieAppletJumpRate'">
                tao_bao_movie_applet_jump_rate ${vo.order}
            </if>

            <if test="vo.sort == 'taoBaoMovieAppletOrderNum'">
                tao_bao_movie_applet_order_num ${vo.order}
            </if>

            <if test="vo.sort == 'taoBaoMovieAppletOrderRate'">
                tao_bao_movie_applet_order_rate ${vo.order}
            </if>

            <if test="vo.sort == 'addGroupAfterAddCustomerServiceNum'">
                add_group_after_add_customer_service_num ${vo.order}
            </if>

            <if test="vo.sort == 'addGroupAfterAddCustomerServiceRate'">
                add_group_after_add_customer_service_rate ${vo.order}
            </if>

            <if test="vo.sort == 'addGroupAfterFollowOfficialAccountNum'">
                add_group_after_follow_official_account_num ${vo.order}
            </if>

            <if test="vo.sort == 'addGroupAfterFollowOfficialAccountRate'">
                add_group_after_follow_official_account_rate ${vo.order}
            </if>

            <if test="vo.sort == 'flow_source_jump_page_view_num'">
                flow_source_jump_page_view_num ${vo.order}
            </if>

        </if>
    </select>

           <select id="listCustomerByConditions" resultType="ai.yiye.agent.boss.vo.excel.BossCustomerExportVo">
               <!--最近7天的落地页pv -->
               <!--最近7天的落地页pv均值 -->
               with lastSevenDayPvData as (
               select agent_id, sum(landing_page_pv) / 7 as last_seven_day_pv from
               (select sum(landing_page_pv) landing_page_pv,agent_id, day_time from boss_advertiser_account_group_day_report
               group by agent_id, day_time) y
               where day_time between now()::timestamp + '-7 day' and now()
               group by agent_id
               ),
               <!--若不满7天，近7天套餐使用量(落地页PV)=所有天数套餐使用量(落地页PV)/所有天数； -->
               lastDayPvData as (
               select agent_id, sum(landing_page_pv) / count(*) as last_day_pv
               from
               (select sum(landing_page_pv) landing_page_pv,agent_id from boss_advertiser_account_group_day_report
               group by agent_id, day_time) x
               group by agent_id
               ),
               lastSevenDayFundData as (
               select agent_id, sum(fund) / 7  as last_seven_day_fund from
               (select sum(fund) /100 * billing_proportion  as fund,agent_id, day_time from boss_advertiser_account_fund_day_report
               group by agent_id, day_time,billing_proportion) y
               where day_time between now()::timestamp + '-7 day' and now()
               group by agent_id
               ),
               lastDayFundData as (
               select agent_id, sum(fund)/count(*)  as last_day_fund
               from
               (select sum(fund)  *billing_proportion /100 as fund,agent_id, day_time, billing_proportion from boss_advertiser_account_fund_day_report
               group by agent_id, day_time,billing_proportion) x
               group by agent_id
               ),
               <!--订单金额之和 -->
               sumOrderAmount as (
               select agent_id, sum(order_amount) as sum_order_amount
               from boss_order where order_status = 0 or order_status = 1
               group by agent_id
               ),
               accountData as  (
               SELECT agent_id AS agent_id,
               bill_account_num,
               row_number() over(partition by agent_id order by day_time desc) as ranks
               from boss_advertiser_account_fund_day_report
               ),
               <!--          计费投放账户数（巨量引擎） 授权投放账户消耗金额（巨量引擎 套餐量 套餐使用量 套餐余量-->
               consumeData as  (				 select sum (fund) as fund_consume,agent_id from (
               select
               case when billing_proportion=0 then 0
               else
               sum(fund) * billing_proportion /100
               end
               as fund,agent_id  from boss_advertiser_account_fund_day_report baafdr
               <if test="vo.startTime != null and vo.endTime != null">
                   where day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
               </if>
               group by agent_id,billing_proportion
               ) as tool group by agent_id
               ),

               fundData as (select sum(fund) as fund,agent_id from  boss_advertiser_account_fund_day_report  group by agent_id)
               select status as exportStatus, to_char(created_at, 'YYYY-MM-dd HH24:MI:SS') as exportCreatedAt, a.agent_id, portal_url, company_name, remarks, sign_email, coalesce(combo_count, 0)
               as combo_count, coalesce(pv_balance, 0) as pv_balance ,username,phone,receive_email, ocean_engine_construct_track_url_license_status,
               sign_status,
               source as customer_source, customer_source_id, last_combo, billing_mode, billing_proportion,last_combo_id,
               date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 as days_used,
               coalesce(landing_page_pv, 0) as combo_used,
               coalesce((combo_count - coalesce(total_landing_page_pv, 0)), 0) as combo_left,
               A.billing_account_consume,
               A.billing_accounts,
               A.total_combo_consume,
               A.combo_account_consume,
               A.combo_left_consume,
               case when billing_mode=0 then
               case
               date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
               when true
               then case when last_seven_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) /
               last_seven_day_pv, 0), 1) end
               when false
               then case when last_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) / last_day_pv,
               0), 1)
               end
               end
               else
               case
               date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
               when true
               then case when last_seven_day_fund = 0 then 0 else round(coalesce((coalesce(pv_balance + coalesce(A.combo_left_consume, 0), 0) - coalesce(combo_account_consume , 0)) /
               last_seven_day_fund, 0), 1) end
               when false
               then case when last_day_fund = 0 then 0 else round(coalesce((coalesce(pv_balance + coalesce(A.combo_left_consume, 0), 0) - coalesce(combo_account_consume, 0)) / last_day_fund,
               0), 1)
               end
               end
               END as estimate_combo_day_left,
               coalesce(identify_qr_code_num, 0) as identify_qr_code_num,
               coalesce(add_work_wechat_num, 0) as add_work_wechat_num,
               coalesce(follow_official_account_num, 0) as follow_official_account_num,
               coalesce(qiye_request_num, 0) as qiye_request_num,
               coalesce(qiye_request_success_num, 0) as qiye_request_success_num,
               coalesce(qiye_request_fail_num, 0) as qiye_request_fail_num,
               coalesce(qiye_pv_num, 0) as qiye_pv_num,
               coalesce(qiye_mini_pv_num, 0) as qiye_mini_pv_num,
               <!--以下为1.197.0新增三个数 -->
               coalesce(official_identify_qr_code_num, 0) as official_identify_qr_code_num,
               coalesce(identify_group_qr_code_num, 0) as identify_group_qr_code_num,
               coalesce(add_work_wechat_group_num, 0) as add_work_wechat_group_num,
               <!--以下为1.197.0新增三个率 -->
               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(official_identify_qr_code_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as official_identify_qr_code_rate,
               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(identify_group_qr_code_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as identify_group_qr_code_rate,
               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_work_wechat_group_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as add_work_wechat_group_rate,
               coalesce(qiye_wechat_official_article_page_view_num, 0) as qiye_wechat_official_article_page_view_num,
               coalesce(qiye_wechat_official_article_request_num, 0) as qiye_wechat_official_article_request_num,
               coalesce(qiye_wechat_official_article_request_success_num, 0) as qiye_wechat_official_article_request_success_num,
               coalesce(qiye_wechat_official_article_request_fail_num, 0) as qiye_wechat_official_article_request_fail_num,
               coalesce(send_sms_num, 0) as send_sms_num,
               coalesce(form_send_sms_num, 0) as form_send_sms_num,
               coalesce(order_send_sms_num, 0) as order_send_sms_num,
               coalesce(pop_up_display_num, 0) as pop_up_display_num,


               <!--1.247.0 新增的统计字段-->
               coalesce(form_submit_num, 0) as form_submit_num,
               coalesce(clue_form_submit_num, 0) as clue_form_submit_num,
               coalesce(douyin_applet_native_form_submit_num, 0) as douyin_applet_native_form_submit_num,
               coalesce(phone_number_recieved_num, 0) as phone_number_recieved_num,
               coalesce(active_message_authorization_num, 0) as active_message_authorization_num,
               coalesce(form_submit_total_num, 0) as form_submit_total_num,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(form_submit_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as form_submit_rate,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(clue_form_submit_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as clue_form_submit_rate,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(douyin_applet_native_form_submit_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as douyin_applet_native_form_submit_rate,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(phone_number_recieved_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as phone_number_recieved_rate,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(active_message_authorization_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as active_message_authorization_rate,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(form_submit_total_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as form_submit_total_rate,

               <!--1.247.0 新增的统计字段-->
               <!-- 1.263.0 饿了么小程序对接 -->
               coalesce(ele_pv_num,0) as ele_pv_num,
               coalesce(ele_qr_code_view_num,0) as ele_qr_code_view_num,
               coalesce(ele_identify_wechat_qr_code_num,0) as ele_identify_wechat_qr_code_num,
               coalesce(ele_add_wechat_success_num,0) as ele_add_wechat_success_num,
               <!-- 1.265.0 whatsapp-->
               coalesce(whatsapp_jump_num,0) as  whatsapp_jump_num,
               coalesce(whatsapp_add_friend_success_num,0) as  whatsapp_add_friend_success_num,
               coalesce(whatsapp_user_open_mouth_num,0) as  whatsapp_user_open_mouth_num,
               coalesce(overseas_pv_num,0)as  overseas_pv_num,
               <!-- 1.267.0 whatsapp-->
               coalesce(whatsapp_customer_prologue_num,0) as  whatsapp_customer_prologue_num,
               coalesce(whatsapp_customer_send_message_num,0) as  whatsapp_customer_send_message_num,
               <!--1.256.0新增订单相关字段-->
               coalesce(order_submit_num,0) as order_submit_num,
               coalesce(order_finish_num,0) as order_finish_num,
               coalesce(online_shop_buy_goods_success_num, 0) as online_shop_buy_goods_success_num,
               coalesce(douyin_applet_order_submit_num, 0) as douyin_applet_order_submit_num,
               coalesce(douyin_applet_order_finish_num, 0) as douyin_applet_order_finish_num,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(order_submit_num, 0) /
                 coalesce(landing_page_pv, 0) * 100 end as order_submit_rate,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(order_finish_num, 0) /
                 coalesce(landing_page_pv, 0) * 100 end as order_finish_rate,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(online_shop_buy_goods_success_num, 0) /
                 coalesce(landing_page_pv, 0) * 100 end as online_shop_buy_goods_success_rate,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(douyin_applet_order_submit_num, 0) /
                 coalesce(landing_page_pv, 0) * 100 end as douyin_applet_order_submit_rate,

               case when coalesce(douyin_applet_order_submit_num, 0) = 0 then 0 else coalesce(douyin_applet_order_finish_num, 0) /
                 coalesce(douyin_applet_order_submit_num, 0) * 100 end as douyin_applet_order_finish_rate,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else (coalesce(order_finish_num, 0) + coalesce(douyin_applet_order_finish_num, 0)) /
                  coalesce(landing_page_pv, 0) * 100 end as comprehensive_payment_rate,
               <!--1.256.0-->

               <!--1.264.0新增淘宝订单相关字段-->
               coalesce(tao_bao_movie_applet_jump_num, 0) as tao_bao_movie_applet_jump_num,
               coalesce(tao_bao_movie_applet_order_num, 0) as tao_bao_movie_applet_order_num,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(tao_bao_movie_applet_jump_num, 0) /
                    coalesce(landing_page_pv, 0) * 100 end as tao_bao_movie_applet_jump_rate,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(tao_bao_movie_applet_order_num, 0) /
                    coalesce(landing_page_pv, 0) * 100 end as tao_bao_movie_applet_order_rate,
               <!--1.264.0-->

               <!--1.271.0新增加群相关字段-->
               coalesce(add_group_after_add_customer_service_num, 0) as add_group_after_add_customer_service_num,
               coalesce(add_group_after_follow_official_account_num, 0) as add_group_after_follow_official_account_num,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_group_after_add_customer_service_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as add_group_after_add_customer_service_rate,

               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_group_after_follow_official_account_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as add_group_after_follow_official_account_rate,
               <!--1.271.0-->
               coalesce(flow_source_jump_page_view_num, 0) as flow_source_jump_page_view_num,
               case when combo_count = 0 then 0 else coalesce(sum_order_amount / combo_count, 0) end as unit_price,
               case when add_work_wechat_num = 0 then 0 when combo_count = 0 then 0 when landing_page_pv = 0 then 0 else
               coalesce(sum_order_amount / combo_count * landing_page_pv / add_work_wechat_num, 0) end as add_fans_cost,
               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(identify_qr_code_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as identify_qr_code_rate,
               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(follow_official_account_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as follow_official_account_rate,
               case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_work_wechat_num, 0) /
               coalesce(landing_page_pv, 0) * 100 end as add_work_wechat_rate,
               case when coalesce(identify_qr_code_num, 0) = 0 then 0 else coalesce(add_work_wechat_num, 0) /
               coalesce(identify_qr_code_num, 0) * 100 end as identify_qr_code_add_work_wechat_rate,
               agent_type,
               belong_saler,
               belong_csm,
               landing_page_system_domain,
               cname_domain,
               most_binding_count,
               industry_id,
               replace_operation,
               sign_status,
               enable_data_pull_switch,
               case
               billing_mode is not null
               when true
               then case when billing_mode = 0 then coalesce((combo_count - coalesce(total_landing_page_pv, 0)), 0) * case when combo_count = 0 then 0 else coalesce(sum_order_amount / combo_count, 0) end
               else coalesce(pv_balance + coalesce(A.combo_left_consume, 0), 0) end
               when false
               then 0
               END as balance
               from
               (
               select a.status, a.created_at, a.agent_id, a.portal_url, d.company_name, d.remarks, d.sign_email,a.license::json->>'oceanEngineConstructTrackUrlLicenseStatus' as ocean_engine_construct_track_url_license_status,
               d.password,d.username,d.phone,d.receive_email,
               d.combo_count as combo_count, s.source, s.id as customer_source_id, c.combo_name as last_combo,c.id as last_combo_id,c.billing_mode, c.billing_proportion,
               ls.last_seven_day_pv, ld.last_day_pv, soa.sum_order_amount as sum_order_amount, d.agent_type, d.belong_saler,
               d.belong_csm, d.landing_page_system_domain,
               d.sign_status,
               d.industry_id,
               d.replace_operation,
               d.combo_amount,
               accountData.bill_account_num as billing_accounts,
               d.cname_domain, d.enable_data_pull_switch, d.most_binding_count,d.pv_balance,
               <!--                 投放账户消耗金额 -->
               fundData.fund as  billing_account_consume,
               <!--                 pv套餐量 -->
               case when c.billing_mode = 1 then d.combo_amount else null end as total_combo_consume,
               <!--                  套餐使用量 每日投放账户消耗 * 收费比例 之和 -->
               cd.fund_consume as combo_account_consume,
               lsfd.last_seven_day_fund,
               ldfd.last_day_fund,
               fundData.fund as total_fund,
               <!--          套餐余量 -->
               d.combo_amount - cd.fund_consume as combo_left_consume

               from agent_conf a
               left join boss_agent_conf_detail d on a.agent_id = d.agent_id
               left join boss_dict s on d.customer_source = s.id and s.boss_dict_type=0
               left join (
               select t.agent_id, combo_id from (
               select max(created_at) created_at, agent_id from boss_order where order_status != 2 and combo_id is not null group by agent_id
               ) t left join boss_order h on t.agent_id = h.agent_id and t.created_at = h.created_at
               )o on a.agent_id = o.agent_id
               left join boss_combo c on o.combo_id = c.id
               left join lastSevenDayPvData ls on a.agent_id = ls.agent_id
               left join lastDayPvData ld on a.agent_id = ld.agent_id
               left join lastSevenDayFundData lsfd on a.agent_id = lsfd.agent_id
               left join lastDayFundData ldfd on a.agent_id = ldfd.agent_id
               left join sumOrderAmount soa on a.agent_id = soa.agent_id
               left join consumeData cd on a.agent_id = cd.agent_id
               left join fundData on a.agent_id =fundData.agent_id
                left join accountData on a.agent_id=accountData.agent_id and ranks=1
               <where>
            <if test="vo.status != null">
                and a.status = #{vo.status}
            </if>
            <if test="vo.addedValue!=null">
                <foreach collection="vo.addedValue" item="item">
                   and position(#{item} in cast(a.white_types as varchar))>0
                </foreach>
            </if>
            <if test="vo.remarks != null and '' != vo.remarks">
                and d.remarks like concat('%', #{vo.remarks}, '%')
            </if>
            <if test="vo.customerSource != null">
                and d.customer_source = #{vo.customerSource}
            </if>
                   <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@DEFAULT">
                       and d.replace_operation = 0
                   </if>
                   <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@OPERATION">
                       and d.replace_operation = 1
                   </if>
                   <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@STOP_OPERATION">
                       and d.replace_operation = 2
                   </if>
            <if test="vo.companyName != null and '' != vo.companyName">
                and (d.company_name like concat('%', #{vo.companyName}, '%') or a.agent_id like concat('%', #{vo.companyName}, '%'))
            </if>
            <if test="vo.comboName != null and '' != vo.comboName">
                and c.combo_name like concat('%', #{vo.comboName}, '%')
            </if>
            <if test="vo.billingModeId != null">
                and c.billing_mode = #{vo.billingModeId}
            </if>
            <if test="vo.signStatus != null and '' != vo.signStatus">
                and d.sign_status = #{vo.signStatus}
            </if>
            <if test="vo.industryId != null and '' != vo.industryId and vo.industryId.size > 0">
                and d.industry_id in
                <foreach collection="vo.industryId" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.belongSaler != null">
                and d.belong_saler in
                <foreach collection="vo.belongSaler" item="saler" open="(" close=")" separator=",">
                    #{saler}
                </foreach>
            </if>
            <if test="vo.belongCsm != null">
                and d.belong_csm in
                <foreach collection="vo.belongCsm" item="csm" open="(" close=")" separator=",">
                    #{csm}
                </foreach>
            </if>
            <if test="vo.agentTypeId != null">
                and d.agent_type = #{vo.agentTypeId}
            </if>
        </where>
        ) a
        left join
        (
            select agent_id, sum(landing_page_pv) as landing_page_pv,
            sum(identify_qr_code_num) as identify_qr_code_num,
            sum(add_work_wechat_num) as add_work_wechat_num,
            sum(follow_official_account_num) as follow_official_account_num,
            sum(qiye_request_num) as qiye_request_num,
            sum(qiye_request_success_num) as qiye_request_success_num,
            sum(qiye_request_fail_num) as qiye_request_fail_num,
            sum(qiye_pv_num) as qiye_pv_num,
            sum(qiye_mini_pv_num) as qiye_mini_pv_num,
           sum(official_identify_qr_code_num) as official_identify_qr_code_num,
           sum(identify_group_qr_code_num) as identify_group_qr_code_num,
           sum(add_work_wechat_group_num) as add_work_wechat_group_num,
           sum(qiye_wechat_official_article_page_view_num) as qiye_wechat_official_article_page_view_num,
           sum(qiye_wechat_official_article_request_num) as qiye_wechat_official_article_request_num,
           sum(qiye_wechat_official_article_request_success_num) as qiye_wechat_official_article_request_success_num,
           sum(qiye_wechat_official_article_request_fail_num) as qiye_wechat_official_article_request_fail_num,
           sum(send_sms_num) as send_sms_num,
           sum(form_send_sms_num) as form_send_sms_num,
           sum(order_send_sms_num) as order_send_sms_num,
           sum(pop_up_display_num) as pop_up_display_num,

           <!---1.247.0新增的统计字段-->
           sum(form_submit_num) as form_submit_num,
           sum(clue_form_submit_num) as clue_form_submit_num,
           sum(douyin_applet_native_form_submit_num) as douyin_applet_native_form_submit_num,
           sum(phone_number_recieved_num) as phone_number_recieved_num,
           sum(active_message_authorization_num) as active_message_authorization_num,
           (sum(form_submit_num) +  sum(clue_form_submit_num) +sum(douyin_applet_native_form_submit_num) ) as form_submit_total_num,
           <!-- 1.263.0 饿了么小程序对接 -->
           sum(ele_pv_num) as ele_pv_num,
           sum(ele_qr_code_view_num) as ele_qr_code_view_num,
           sum(ele_identify_wechat_qr_code_num) as ele_identify_wechat_qr_code_num,
           sum(ele_add_wechat_success_num) as ele_add_wechat_success_num,
           <!--1.265.0 whatsapp -->
           sum(overseas_pv_num) as  overseas_pv_num,
           sum(whatsapp_jump_num) as  whatsapp_jump_num,
           sum(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
           sum(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,
           <!-- 1.267.0 whatsapp-->
           sum(whatsapp_customer_prologue_num) as  whatsapp_customer_prologue_num,
           sum(whatsapp_customer_send_message_num) as  whatsapp_customer_send_message_num,
           <!--1.256.0新增字段-->
           sum(order_submit_num) as order_submit_num,
           sum(order_finish_num) as order_finish_num,
           sum(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
           sum(douyin_applet_order_submit_num) as douyin_applet_order_submit_num,
           sum(douyin_applet_order_finish_num) as douyin_applet_order_finish_num,
           <!--1.264.0新增字段-->
           sum(tao_bao_movie_applet_jump_num) as tao_bao_movie_applet_jump_num,
           sum(tao_bao_movie_applet_order_num) as tao_bao_movie_applet_order_num,
           <!--1.271.0新增字段-->
           sum(add_group_after_add_customer_service_num) as add_group_after_add_customer_service_num,
           sum(add_group_after_follow_official_account_num) as add_group_after_follow_official_account_num,
           sum(flow_source_jump_page_view_num) as flow_source_jump_page_view_num

        from boss_advertiser_account_group_day_report
        <if test="vo.startTime != null and vo.endTime != null">
            where day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by agent_id
        ) b
        on a.agent_id = b.agent_id

               left join
               (
               select agent_id, sum(landing_page_pv) as total_landing_page_pv
               from boss_advertiser_account_group_day_report
               group by agent_id
               ) c
               on a.agent_id = c.agent_id

        <if test="vo.sort != null and vo.order != null">
            order by
            <if test="vo.sort =='createdAt'">
                created_at ${vo.order}
            </if>
            <if test="vo.sort == 'daysUsed'">
                days_used ${vo.order}
            </if>
            <if test="vo.sort == 'comboCount'">
                combo_count ${vo.order}
            </if>
            <if test="vo.sort == 'comboUsed'">
                combo_used ${vo.order}
            </if>
            <if test="vo.sort == 'comboLeft'">
                combo_left ${vo.order}
            </if>
            <if test="vo.sort == 'estimateComboDayLeft'">
                estimate_combo_day_left ${vo.order}
            </if>
            <if test="vo.sort == 'identifyQrCodeNum'">
                identify_qr_code_num ${vo.order}
            </if>
            <if test="vo.sort == 'followOfficialAccountNum'">
                follow_official_account_num ${vo.order}
            </if>
            <if test="vo.sort == 'addWorkWechatNum'">
                add_work_wechat_num ${vo.order}
            </if>
            <if test="vo.sort == 'unitPrice'">
                unit_price ${vo.order}
            </if>
            <if test="vo.sort == 'addFansCost'">
                add_fans_cost ${vo.order}
            </if>
            <if test="vo.sort == 'identifyQrCodeRate'">
                identify_qr_code_rate ${vo.order}
            </if>
            <if test="vo.sort == 'followOfficialAccountRate'">
                follow_official_account_rate ${vo.order}
            </if>
            <if test="vo.sort == 'addWorkWechatRate'">
                add_work_wechat_rate ${vo.order}
            </if>
            <if test="vo.sort == 'identifyQrCodeAddWorkWechatRate'">
                identify_qr_code_add_work_wechat_rate ${vo.order}
            </if>
            <if test="vo.sort == 'balance'">
                balance ${vo.order}
            </if>
            <if test="vo.sort == 'billingAccounts'">
                billing_accounts ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'billingAccountConsume'">
                billing_account_consume ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'totalComboConsume'">
                total_combo_consume ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'comboAccountConsume'">
                combo_account_consume ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'comboLeftConsume'">
                combo_left_consume ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'qiyeRequestNum'">
                qiye_request_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeRequestSuccessNum'">
                qiye_request_success_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeRequestFailNum'">
                qiye_request_fail_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyePvNum'">
                qiye_pv_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeMiniPvNum'">
                qiye_mini_pv_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeWechatOfficialArticlePageViewNum'">
                qiye_wechat_official_article_page_view_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeWechatOfficialArticleRequestNum'">
                qiye_wechat_official_article_request_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeWechatOfficialArticleRequestSuccessNum'">
                qiye_wechat_official_article_request_success_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeWechatOfficialArticleRequestFailNum'">
                qiye_wechat_official_article_request_fail_num ${vo.order}
            </if>
            <!--以下为1.197.0新增三个排序 -->
            <if test="vo.sort == 'officialIdentifyQrCodeNum'">
                official_identify_qr_code_num ${vo.order}
            </if>
            <if test="vo.sort == 'identifyGroupQrCodeNum'">
                identify_group_qr_code_num ${vo.order}
            </if>
            <if test="vo.sort == 'addWorkWechatGroupNum'">
                add_work_wechat_group_num ${vo.order}
            </if>
            <if test="vo.sort == 'officialIdentifyQrCodeRate'">
                official_identify_qr_code_rate ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'identifyGroupQrCodeRate'">
                identify_group_qr_code_rate ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'addWorkWechatGroupRate'">
                add_work_wechat_group_rate ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'sendSmsNum'">
                send_sms_num ${vo.order}
            </if>
            <if test="vo.sort == 'formSendSmsNum'">
                form_send_sms_num ${vo.order}
            </if>
            <if test="vo.sort == 'orderSendSmsNum'">
                order_send_sms_num ${vo.order}
            </if>

            <if test="vo.sort == 'formSubmitNum'">
                form_submit_num ${vo.order}
            </if>

            <if test="vo.sort == 'formSubmitRate'">
                form_submit_rate ${vo.order}
            </if>

            <if test="vo.sort == 'formSubmitTotalNum'">
                form_submit_total_num ${vo.order}
            </if>

            <if test="vo.sort == 'formSubmitTotalRate'">
                form_submit_total_rate ${vo.order}
            </if>

            <if test="vo.sort == 'clueFormSubmitNum'">
                clue_form_submit_num ${vo.order}
            </if>

            <if test="vo.sort == 'clueFormSubmitRate'">
                clue_form_submit_rate ${vo.order}
            </if>

            <if test="vo.sort == 'douyinAppletNativeFormSubmitNum'">
                douyin_applet_native_form_submit_num ${vo.order}
            </if>

            <if test="vo.sort == 'douyinAppletNativeFormSubmitRate'">
                douyin_applet_native_form_submit_rate ${vo.order}
            </if>

            <if test="vo.sort == 'phoneNumberRecievedNum'">
                phone_number_recieved_num ${vo.order}
            </if>

            <if test="vo.sort == 'phoneNumberRecievedRate'">
                phone_number_recieved_rate ${vo.order}
            </if>

            <if test="vo.sort == 'activeMessageAuthorizationNum'">
                active_message_authorization_num ${vo.order}
            </if>

            <if test="vo.sort == 'activeMessageAuthorizationRate'">
                active_message_authorization_rate ${vo.order}
            </if>
            <if test="vo.sort == 'elePvNum'">
                ele_pv_num ${vo.order}
            </if>
            <if test="vo.sort == 'eleQrCodeViewNum'">
                ele_qr_code_view_num ${vo.order}
            </if>
            <if test="vo.sort == 'eleIdentifyWechatQrCodeNum'">
                ele_identify_wechat_qr_code_num ${vo.order}
            </if>
            <if test="vo.sort == 'eleAddWechatSuccessNum'">
                ele_add_wechat_success_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappJumpNum'">
                whatsapp_jump_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappAddFriendSuccessNum'">
                whatsapp_add_friend_success_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappUserOpenMouthNum'">
                whatsapp_user_open_mouth_num ${vo.order}
            </if>
            <if test="vo.sort == 'overseasPvNum'">
                overseas_pv_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappCustomerPrologueNum'">
                whatsapp_customer_prologue_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappCustomerSendMessageNum'">
                whatsapp_customer_send_message_num ${vo.order}
            </if>

            <if test="vo.sort == 'taoBaoMovieAppletJumpNum'">
                tao_bao_movie_applet_jump_num ${vo.order}
            </if>

            <if test="vo.sort == 'taoBaoMovieAppletJumpRate'">
                tao_bao_movie_applet_jump_Rate ${vo.order}
            </if>

            <if test="vo.sort == 'taoBaoMovieAppletOrderNum'">
                tao_bao_movie_applet_order_num ${vo.order}
            </if>

            <if test="vo.sort == 'taoBaoMovieAppletOrderRate'">
                tao_bao_movie_applet_order_rate ${vo.order}
            </if>

            <if test="vo.sort == 'addGroupAfterAddCustomerServiceNum'">
                add_group_after_add_customer_service_num ${vo.order}
            </if>

            <if test="vo.sort == 'addGroupAfterAddCustomerServiceRate'">
                add_group_after_add_customer_service_rate ${vo.order}
            </if>

            <if test="vo.sort == 'addGroupAfterFollowOfficialAccountNum'">
                add_group_after_follow_official_account_num ${vo.order}
            </if>

            <if test="vo.sort == 'addGroupAfterFollowOfficialAccountRate'">
                add_group_after_follow_official_account_rate ${vo.order}
            </if>
            <if test="vo.sort == 'flowSourceJumpPageViewNum'">
                flow_source_jump_page_view_num ${vo.order}
            </if>
        </if>
    </select>



    <select id="pageCustomerByConditionsTotal" resultType="ai.yiye.agent.boss.vo.BossCustomerVo">
        <!--最近7天的落地页pv -->
        <!--最近7天的落地页pv均值 -->
        with
        <!--若不满7天，近7天套餐使用量(落地页PV)=所有天数套餐使用量(落地页PV)/所有天数； -->
        lastDayPvData as (
        select agent_id, sum(landing_page_pv) / count(*) as last_day_pv
        from
        (select sum(landing_page_pv) landing_page_pv,agent_id from boss_advertiser_account_group_day_report
        <if test="vo.agentIds != null and '' != vo.agentIds and vo.agentIds.size > 0">
            where boss_advertiser_account_group_day_report.agent_id in
            <foreach collection="vo.agentIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by agent_id, day_time) x
        group by agent_id
        ),
        lastDayFundData as (
        select agent_id, sum(fund)/count(*)  as last_day_fund
        from
        (select sum(fund)  *billing_proportion /100 as fund,agent_id, day_time, billing_proportion from boss_advertiser_account_fund_day_report
        group by agent_id, day_time,billing_proportion) x
        group by agent_id
        ),
        <!--订单金额之和 -->
        sumOrderAmount as (
        select agent_id, sum(order_amount) as sum_order_amount
        from boss_order where order_status = 0 or order_status = 1
        group by agent_id
        ),
        accountData as  (
        SELECT agent_id AS agent_id,
        bill_account_num,
        row_number() over(partition by agent_id order by day_time desc) as ranks
        from boss_advertiser_account_fund_day_report
        ),
        fundData as (select sum(fund) as fund,agent_id from  boss_advertiser_account_fund_day_report  group by agent_id)
        select
        sum(coalesce(landing_page_pv, 0)) as combo_used,
        sum(coalesce(identify_qr_code_num, 0) )as identify_qr_code_num,
        sum(coalesce(add_work_wechat_num, 0)) as add_work_wechat_num,
        sum(coalesce(follow_official_account_num, 0)) as follow_official_account_num,
        sum(coalesce(qiye_request_num, 0) )as qiye_request_num,
        sum(coalesce(qiye_request_success_num, 0)) as qiye_request_success_num,
        sum(coalesce(qiye_request_fail_num, 0) )as qiye_request_fail_num,
        sum(coalesce(qiye_pv_num, 0)) as qiye_pv_num,
        sum(coalesce(qiye_mini_pv_num, 0)) as qiye_mini_pv_num,
        <!--以下为1.197.0新增三个数 -->
        SUM (COALESCE ( official_identify_qr_code_num, 0 )) AS official_identify_qr_code_num,
        SUM (COALESCE ( identify_group_qr_code_num, 0 )) AS identify_group_qr_code_num,
        SUM (COALESCE ( add_work_wechat_group_num, 0 )) AS add_work_wechat_group_num,
        <!--以下为1.197.0新增三个率 -->
        CASE WHEN  SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN
        0 ELSE SUM ( COALESCE  ( official_identify_qr_code_num, 0 ) ) /  SUM ( COALESCE ( landing_page_pv, 0 ) ) * 100
        END AS official_identify_qr_code_rate,
        CASE WHEN  SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN
        0 ELSE SUM ( COALESCE  ( identify_group_qr_code_num, 0 ) ) /  SUM ( COALESCE ( landing_page_pv, 0 ) ) * 100
        END AS identify_group_qr_code_rate,
        CASE WHEN   SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN
        0 ELSE SUM ( COALESCE  ( add_work_wechat_group_num, 0 ) ) / SUM ( COALESCE ( landing_page_pv, 0 ) ) * 100
        END AS add_work_wechat_group_rate,

        sum(coalesce(qiye_wechat_official_article_page_view_num,0)) as qiye_wechat_official_article_page_view_num,
        sum(coalesce(qiye_wechat_official_article_request_num,0)) as qiye_wechat_official_article_request_num,
        sum(coalesce(qiye_wechat_official_article_request_success_num,0)) as qiye_wechat_official_article_request_success_num,
        sum(coalesce(qiye_wechat_official_article_request_fail_num,0)) as qiye_wechat_official_article_request_fail_num,
        sum(coalesce(send_sms_num,0)) as send_sms_num,
        sum(coalesce(form_send_sms_num,0)) as form_send_sms_num,
        sum(coalesce(order_send_sms_num,0)) as order_send_sms_num,
        <!--1.256.0 新增的订单数据-->
        sum(coalesce(order_submit_num,0)) as order_submit_num,
        sum(coalesce(order_finish_num,0)) as order_finish_num,
        sum(coalesce(online_shop_buy_goods_success_num,0)) as online_shop_buy_goods_success_num,
        sum(coalesce(douyin_applet_order_submit_num,0)) as douyin_applet_order_submit_num,
        sum(coalesce(douyin_applet_order_finish_num,0)) as douyin_applet_order_finish_num,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(order_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as order_submit_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(order_finish_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as order_finish_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(douyin_applet_order_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as douyin_applet_order_submit_rate,
        case when coalesce(sum(douyin_applet_order_submit_num), 0) = 0 then 0 else sum(coalesce(douyin_applet_order_finish_num, 0)) / sum(coalesce(douyin_applet_order_submit_num, 0)) * 100 end as douyin_applet_order_finish_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(online_shop_buy_goods_success_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as online_shop_buy_goods_success_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else (sum(coalesce(order_finish_num, 0)) + sum(coalesce(douyin_applet_order_finish_num, 0))) / sum(coalesce(landing_page_pv, 0)) * 100 end as comprehensive_payment_rate,

        <!--1.247.0 新增的统计字段-->
        sum(coalesce(form_submit_num,0)) as form_submit_num,
        sum(coalesce(clue_form_submit_num,0)) as clue_form_submit_num,
        sum(coalesce(douyin_applet_native_form_submit_num,0)) as douyin_applet_native_form_submit_num,
        sum(coalesce(phone_number_recieved_num,0)) as phone_number_recieved_num,
        sum(coalesce(active_message_authorization_num,0)) as active_message_authorization_num,
        sum(coalesce(pop_up_display_num,0)) as pop_up_display_num,
        (sum(coalesce(form_submit_num,0)) + sum(coalesce(clue_form_submit_num,0)) + sum(coalesce(douyin_applet_native_form_submit_num,0))) as form_submit_total_num,

        sum(coalesce(add_group_after_add_customer_service_num,0)) as add_group_after_add_customer_service_num,
        sum(coalesce(add_group_after_follow_official_account_num,0)) as add_group_after_follow_official_account_num,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(add_group_after_add_customer_service_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as add_group_after_add_customer_service_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(add_group_after_follow_official_account_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as add_group_after_follow_official_account_rate,
        sum(coalesce(flow_source_jump_page_view_num,0)) as flow_source_jump_page_view_num,
        <!--1.264.0 新增的统计字段-->
        sum(coalesce(tao_bao_movie_applet_jump_num,0)) as tao_bao_movie_applet_jump_num,
        sum(coalesce(tao_bao_movie_applet_order_num,0)) as tao_bao_movie_applet_order_num,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(tao_bao_movie_applet_jump_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as tao_bao_movie_applet_jump_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(tao_bao_movie_applet_order_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as tao_bao_movie_applet_order_rate,

        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(form_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as form_submit_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(clue_form_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as clue_form_submit_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(douyin_applet_native_form_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as douyin_applet_native_form_submit_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(phone_number_recieved_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as phone_number_recieved_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(active_message_authorization_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as active_message_authorization_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else  (sum(coalesce(form_submit_num,0)) + sum(coalesce(clue_form_submit_num,0)) + sum(coalesce(douyin_applet_native_form_submit_num,0)))  / sum(coalesce(landing_page_pv, 0)) * 100 end as form_submit_total_rate,
        <!-- 1.263.0 饿了么小程序对接 -->
        sum(coalesce(ele_pv_num,0)) as ele_pv_num,
        sum(coalesce(ele_qr_code_view_num,0)) as ele_qr_code_view_num,
        sum(coalesce(ele_identify_wechat_qr_code_num,0)) as ele_identify_wechat_qr_code_num,
        sum(coalesce(ele_add_wechat_success_num,0)) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        sum(coalesce(whatsapp_jump_num,0)) as  whatsapp_jump_num,
        sum(coalesce(whatsapp_add_friend_success_num,0)) as  whatsapp_add_friend_success_num,
        sum(coalesce(whatsapp_user_open_mouth_num,0)) as  whatsapp_user_open_mouth_num,
        sum(coalesce(overseas_pv_num,0)) as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        sum(coalesce(whatsapp_customer_prologue_num,0)) as  whatsapp_customer_prologue_num,
        sum(coalesce(whatsapp_customer_send_message_num,0)) as  whatsapp_customer_send_message_num,

        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(identify_qr_code_num, 0)) /
        sum(coalesce(landing_page_pv, 0)) * 100 end as identify_qr_code_rate,
        case when sum(coalesce(landing_page_pv, 0)) = 0 then 0 else sum(coalesce(follow_official_account_num, 0)) /
        sum(coalesce(landing_page_pv, 0)) * 100 end as follow_official_account_rate,
        case when sum(coalesce(landing_page_pv, 0)) = 0 then 0 else sum(coalesce(add_work_wechat_num, 0)) /
        sum(coalesce(landing_page_pv, 0)) * 100 end as add_work_wechat_rate,
        case when sum(coalesce(identify_qr_code_num, 0)) = 0 then 0 else sum(coalesce(add_work_wechat_num, 0)) /
        sum(coalesce(identify_qr_code_num, 0)) * 100 end as identify_qr_code_add_work_wechat_rate
        from
        (
        select a.status, a.created_at, a.agent_id, a.portal_url, d.company_name, d.remarks, d.sign_email,
        d.password,d.username,d.phone,d.receive_email,
         s.source, s.id as customer_source_id, c.combo_name as last_combo,c.id as last_combo_id,c.billing_mode, c.billing_proportion,
        ld.last_day_pv, soa.sum_order_amount as sum_order_amount, d.agent_type, d.belong_saler,
        d.belong_csm, d.landing_page_system_domain,
        d.sign_status,
        d.industry_id,
        d.replace_operation,
        d.combo_amount,
        d.cname_domain, d.enable_data_pull_switch, d.most_binding_count,
        ldfd.last_day_fund,
        fundData.fund as total_fund
        from agent_conf a
        left join boss_agent_conf_detail d on a.agent_id = d.agent_id
        left join boss_dict s on d.customer_source = s.id and s.boss_dict_type=0
        left join (
        select t.agent_id, combo_id from (
        select max(created_at) created_at, agent_id from boss_order where order_status != 2 and combo_id is not null group by agent_id
        ) t left join boss_order h on t.agent_id = h.agent_id and t.created_at = h.created_at
        )o on a.agent_id = o.agent_id
        left join boss_combo c on o.combo_id = c.id
        left join lastDayPvData ld on a.agent_id = ld.agent_id
        left join lastDayFundData ldfd on a.agent_id = ldfd.agent_id
        left join sumOrderAmount soa on a.agent_id = soa.agent_id
        left join fundData on a.agent_id =fundData.agent_id
        left join accountData on a.agent_id=accountData.agent_id and ranks=1
        <where>
            <if test="vo.status != null">
                and a.status = #{vo.status}
            </if>
            <if test="vo.status == null">
                and a.status != 3
            </if>
            <if test="vo.remarks != null and '' != vo.remarks">
                and d.remarks like concat('%', #{vo.remarks}, '%')
            </if>
            <if test="vo.customerSource != null">
                and d.customer_source = #{vo.customerSource}
            </if>
            <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@DEFAULT">
                and d.replace_operation = 0
            </if>
            <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@OPERATION">
                and d.replace_operation = 1
            </if>
            <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@STOP_OPERATION">
                and d.replace_operation = 2
            </if>
            <if test="vo.agentIds != null and '' != vo.agentIds and vo.agentIds.size > 0">
                and a.agent_id in
                <foreach collection="vo.agentIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.companyName != null and '' != vo.companyName">
                and (d.company_name like concat('%', #{vo.companyName}, '%') or a.agent_id like concat('%', #{vo.companyName}, '%'))
            </if>
            <if test="vo.comboName != null and '' != vo.comboName">
                and c.combo_name like concat('%', #{vo.comboName}, '%')
            </if>
            <if test="vo.billingModeId != null">
                and c.billing_mode = #{vo.billingModeId}
            </if>
            <if test="vo.signStatus != null and '' != vo.signStatus">
                and d.sign_status = #{vo.signStatus}
            </if>
            <if test="vo.industryId != null and '' != vo.industryId and vo.industryId.size > 0">
                and d.industry_id in
                <foreach collection="vo.industryId" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.belongSaler != null">
                and d.belong_saler in
                <foreach collection="vo.belongSaler" item="saler" open="(" close=")" separator=",">
                    #{saler}
                </foreach>
            </if>
            <if test="vo.belongCsm != null">
                and d.belong_csm in
                <foreach collection="vo.belongCsm" item="csm" open="(" close=")" separator=",">
                    #{csm}
                </foreach>
            </if>
            <if test="vo.agentTypeId != null">
                and d.agent_type = #{vo.agentTypeId}
            </if>
        </where>
        ) a
        left join
        (
        select
            agent_id, sum(landing_page_pv) as landing_page_pv,
            sum(identify_qr_code_num) as identify_qr_code_num,
            sum(add_work_wechat_num) as add_work_wechat_num,
            sum(follow_official_account_num) as follow_official_account_num,
            sum(qiye_request_num) as qiye_request_num,
            sum(qiye_request_success_num) as qiye_request_success_num,
            sum(qiye_request_fail_num) as qiye_request_fail_num,
            sum(qiye_pv_num) as qiye_pv_num,
            sum(qiye_mini_pv_num) as qiye_mini_pv_num,
            sum(official_identify_qr_code_num) as official_identify_qr_code_num,
            sum(identify_group_qr_code_num) as identify_group_qr_code_num,
            sum(add_work_wechat_group_num) as add_work_wechat_group_num,
            sum(qiye_wechat_official_article_page_view_num) as qiye_wechat_official_article_page_view_num,
            sum(qiye_wechat_official_article_request_num) as qiye_wechat_official_article_request_num,
            sum(qiye_wechat_official_article_request_success_num) as qiye_wechat_official_article_request_success_num,
            sum(qiye_wechat_official_article_request_fail_num) as qiye_wechat_official_article_request_fail_num,
            sum(send_sms_num) as send_sms_num,
            sum(form_send_sms_num) as form_send_sms_num,
            sum(order_send_sms_num) as order_send_sms_num,
            <!---1.247.0新增的统计字段-->
            sum(form_submit_num) as form_submit_num,
            sum(clue_form_submit_num) as clue_form_submit_num,
            sum(douyin_applet_native_form_submit_num) as douyin_applet_native_form_submit_num,
            sum(phone_number_recieved_num) as phone_number_recieved_num,
            sum(active_message_authorization_num) as active_message_authorization_num,
            sum(pop_up_display_num) as pop_up_display_num,
            (sum(form_submit_num) +  sum(clue_form_submit_num) +sum(douyin_applet_native_form_submit_num) ) as form_submit_total_num,
            <!-- 1.263.0 饿了么小程序对接 -->
            sum(ele_pv_num) as ele_pv_num,
            sum(ele_qr_code_view_num) as ele_qr_code_view_num,
            sum(ele_identify_wechat_qr_code_num) as ele_identify_wechat_qr_code_num,
            sum(ele_add_wechat_success_num) as ele_add_wechat_success_num,
            <!--1.265.0 whatsapp -->
            sum(overseas_pv_num) as  overseas_pv_num,
            sum(whatsapp_jump_num) as  whatsapp_jump_num,
            sum(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
            sum(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,
            <!-- 1.267.0 whatsapp-->
            sum(whatsapp_customer_prologue_num) as  whatsapp_customer_prologue_num,
            sum(whatsapp_customer_send_message_num) as  whatsapp_customer_send_message_num,
            <!--1.256.0新增订单相关数据-->
            sum(order_submit_num) as order_submit_num,
            sum(order_finish_num) as order_finish_num,
            sum(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
            sum(douyin_applet_order_submit_num) as douyin_applet_order_submit_num,
            sum(douyin_applet_order_finish_num) as douyin_applet_order_finish_num,
            <!--1.264.0新增淘宝电影小程序相关数据-->
            sum(tao_bao_movie_applet_jump_num) as tao_bao_movie_applet_jump_num,
            sum(tao_bao_movie_applet_order_num) as tao_bao_movie_applet_order_num,
            sum(add_group_after_add_customer_service_num) as add_group_after_add_customer_service_num,
            sum(add_group_after_follow_official_account_num) as add_group_after_follow_official_account_num,
            sum(flow_source_jump_page_view_num) as flow_source_jump_page_view_num
        from boss_advertiser_account_group_day_report
        <if test="vo.startTime != null and vo.endTime != null">
            where day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by agent_id
        ) b
        on a.agent_id = b.agent_id
        left join
        (
        select agent_id, sum(landing_page_pv) as total_landing_page_pv
        from boss_advertiser_account_group_day_report
        group by agent_id
        ) c
        on a.agent_id = c.agent_id

    </select>

    <select id="pageCustomerByConditionsTotalNew" resultType="ai.yiye.agent.boss.vo.BossCustomerVo">
        <!--最近7天的落地页pv -->
        <!--最近7天的落地页pv均值 -->
        with
        <!--若不满7天，近7天套餐使用量(落地页PV)=所有天数套餐使用量(落地页PV)/所有天数； -->
        lastDayPvData as (
        select agent_id, sum(landing_page_pv) / count(*) as last_day_pv
        from
        (select sum(landing_page_pv) landing_page_pv,agent_id from boss_advertiser_account_group_day_report_new
        <if test="vo.agentIds != null and '' != vo.agentIds and vo.agentIds.size > 0">
            where boss_advertiser_account_group_day_report_new.agent_id in
            <foreach collection="vo.agentIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by agent_id, day_time) x
        group by agent_id
        ),
        lastDayFundData as (
        select agent_id, sum(fund)/count(*)  as last_day_fund
        from
        (select sum(fund)  *billing_proportion /100 as fund,agent_id, day_time, billing_proportion from boss_advertiser_account_fund_day_report
        group by agent_id, day_time,billing_proportion) x
        group by agent_id
        ),
        <!--订单金额之和 -->
        sumOrderAmount as (
        select agent_id, sum(order_amount) as sum_order_amount
        from boss_order where order_status = 0 or order_status = 1
        group by agent_id
        ),
        accountData as  (
        SELECT agent_id AS agent_id,
        bill_account_num,
        row_number() over(partition by agent_id order by day_time desc) as ranks
        from boss_advertiser_account_fund_day_report
        ),
        fundData as (select sum(fund) as fund,agent_id from  boss_advertiser_account_fund_day_report  group by agent_id)
        select
        sum(coalesce(landing_page_pv, 0)) as combo_used,
        sum(coalesce(identify_qr_code_num, 0) )as identify_qr_code_num,
        sum(coalesce(add_work_wechat_num, 0)) as add_work_wechat_num,
        sum(coalesce(follow_official_account_num, 0)) as follow_official_account_num,
        sum(coalesce(qiye_request_num, 0) )as qiye_request_num,
        sum(coalesce(qiye_request_success_num, 0)) as qiye_request_success_num,
        sum(coalesce(qiye_request_fail_num, 0) )as qiye_request_fail_num,
        sum(coalesce(qiye_pv_num, 0)) as qiye_pv_num,
        sum(coalesce(qiye_mini_pv_num, 0)) as qiye_mini_pv_num,
        <!--以下为1.197.0新增三个数 -->
        SUM (COALESCE ( official_identify_qr_code_num, 0 )) AS official_identify_qr_code_num,
        SUM (COALESCE ( identify_group_qr_code_num, 0 )) AS identify_group_qr_code_num,
        SUM (COALESCE ( add_work_wechat_group_num, 0 )) AS add_work_wechat_group_num,
        <!--以下为1.197.0新增三个率 -->
        CASE WHEN  SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN
        0 ELSE SUM ( COALESCE  ( official_identify_qr_code_num, 0 ) ) /  SUM ( COALESCE ( landing_page_pv, 0 ) ) * 100
        END AS official_identify_qr_code_rate,
        CASE WHEN  SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN
        0 ELSE SUM ( COALESCE  ( identify_group_qr_code_num, 0 ) ) /  SUM ( COALESCE ( landing_page_pv, 0 ) ) * 100
        END AS identify_group_qr_code_rate,
        CASE WHEN   SUM ( COALESCE ( landing_page_pv, 0 ) ) = 0 THEN
        0 ELSE SUM ( COALESCE  ( add_work_wechat_group_num, 0 ) ) / SUM ( COALESCE ( landing_page_pv, 0 ) ) * 100
        END AS add_work_wechat_group_rate,

        sum(coalesce(qiye_wechat_official_article_page_view_num,0)) as qiye_wechat_official_article_page_view_num,
        sum(coalesce(qiye_wechat_official_article_request_num,0)) as qiye_wechat_official_article_request_num,
        sum(coalesce(qiye_wechat_official_article_request_success_num,0)) as qiye_wechat_official_article_request_success_num,
        sum(coalesce(qiye_wechat_official_article_request_fail_num,0)) as qiye_wechat_official_article_request_fail_num,
        sum(coalesce(send_sms_num,0)) as send_sms_num,
        sum(coalesce(form_send_sms_num,0)) as form_send_sms_num,
        sum(coalesce(order_send_sms_num,0)) as order_send_sms_num,

        <!--1.256.0 新增的订单数据-->
        sum(coalesce(order_submit_num,0)) as order_submit_num,
        sum(coalesce(order_finish_num,0)) as order_finish_num,
        sum(coalesce(online_shop_buy_goods_success_num,0)) as online_shop_buy_goods_success_num,
        sum(coalesce(douyin_applet_order_submit_num,0)) as douyin_applet_order_submit_num,
        sum(coalesce(douyin_applet_order_finish_num,0)) as douyin_applet_order_finish_num,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(order_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as order_submit_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(order_finish_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as order_finish_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(douyin_applet_order_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as douyin_applet_order_submit_rate,
        case when coalesce(sum(douyin_applet_order_submit_num), 0) = 0 then 0 else sum(coalesce(douyin_applet_order_finish_num, 0)) / sum(coalesce(douyin_applet_order_submit_num, 0)) * 100 end as douyin_applet_order_finish_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(online_shop_buy_goods_success_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as online_shop_buy_goods_success_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else (sum(coalesce(order_finish_num, 0)) + sum(coalesce(douyin_applet_order_finish_num, 0))) / sum(coalesce(landing_page_pv, 0)) * 100 end as comprehensive_payment_rate,

        <!-- 1.263.0 饿了么小程序对接 -->
        sum(coalesce(ele_pv_num,0)) as ele_pv_num,
        sum(coalesce(ele_qr_code_view_num,0)) as ele_qr_code_view_num,
        sum(coalesce(ele_identify_wechat_qr_code_num,0)) as ele_identify_wechat_qr_code_num,
        sum(coalesce(ele_add_wechat_success_num,0)) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        sum(coalesce(whatsapp_jump_num,0)) as  whatsapp_jump_num,
        sum(coalesce(whatsapp_add_friend_success_num,0)) as  whatsapp_add_friend_success_num,
        sum(coalesce(whatsapp_user_open_mouth_num,0)) as  whatsapp_user_open_mouth_num,
        sum(coalesce(overseas_pv_num,0)) as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        sum(coalesce(whatsapp_customer_prologue_num,0)) as  whatsapp_customer_prologue_num,
        sum(coalesce(whatsapp_customer_send_message_num,0)) as  whatsapp_customer_send_message_num,
        <!--1.264.0新增淘宝电影小程序相关的数据-->
        sum(coalesce(tao_bao_movie_applet_jump_num,0)) as tao_bao_movie_applet_jump_num,
        sum(coalesce(tao_bao_movie_applet_order_num,0)) as tao_bao_movie_applet_order_num,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(tao_bao_movie_applet_jump_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as tao_bao_movie_applet_jump_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(tao_bao_movie_applet_order_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as tao_bao_movie_applet_order_rate,

        <!--1.247.0 新增的统计字段-->
        sum(coalesce(form_submit_num,0)) as form_submit_num,
        sum(coalesce(clue_form_submit_num,0)) as clue_form_submit_num,
        sum(coalesce(douyin_applet_native_form_submit_num,0)) as douyin_applet_native_form_submit_num,
        sum(coalesce(phone_number_recieved_num,0)) as phone_number_recieved_num,
        sum(coalesce(active_message_authorization_num,0)) as active_message_authorization_num,
        (sum(coalesce(form_submit_num,0)) + sum(coalesce(clue_form_submit_num,0)) + sum(coalesce(douyin_applet_native_form_submit_num,0))) as form_submit_total_num,
        sum(coalesce(pop_up_display_num,0)) as pop_up_display_num,

        sum(coalesce(add_group_after_add_customer_service_num,0)) as add_group_after_add_customer_service_num,
        sum(coalesce(add_group_after_follow_official_account_num,0)) as add_group_after_follow_official_account_num,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(add_group_after_add_customer_service_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as add_group_after_add_customer_service_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(add_group_after_follow_official_account_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as add_group_after_follow_official_account_rate,
        sum(coalesce(flow_source_jump_page_view_num,0)) as flow_source_jump_page_view_num,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(form_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as form_submit_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(clue_form_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as clue_form_submit_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(douyin_applet_native_form_submit_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as douyin_applet_native_form_submit_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(phone_number_recieved_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as phone_number_recieved_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(active_message_authorization_num, 0)) / sum(coalesce(landing_page_pv, 0)) * 100 end as active_message_authorization_rate,
        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else  (sum(coalesce(form_submit_num,0)) + sum(coalesce(clue_form_submit_num,0)) + sum(coalesce(douyin_applet_native_form_submit_num,0)))  / sum(coalesce(landing_page_pv, 0)) * 100 end as form_submit_total_rate,

        case when coalesce(sum(landing_page_pv), 0) = 0 then 0 else sum(coalesce(identify_qr_code_num, 0)) /
        sum(coalesce(landing_page_pv, 0)) * 100 end as identify_qr_code_rate,
        case when sum(coalesce(landing_page_pv, 0)) = 0 then 0 else sum(coalesce(follow_official_account_num, 0)) /
        sum(coalesce(landing_page_pv, 0)) * 100 end as follow_official_account_rate,
        case when sum(coalesce(landing_page_pv, 0)) = 0 then 0 else sum(coalesce(add_work_wechat_num, 0)) /
        sum(coalesce(landing_page_pv, 0)) * 100 end as add_work_wechat_rate,
        case when sum(coalesce(identify_qr_code_num, 0)) = 0 then 0 else sum(coalesce(add_work_wechat_num, 0)) /
        sum(coalesce(identify_qr_code_num, 0)) * 100 end as identify_qr_code_add_work_wechat_rate
        from
        (
        select a.status, a.created_at, a.agent_id, a.portal_url, d.company_name, d.remarks, d.sign_email,
        d.password,d.username,d.phone,d.receive_email,
        s.source, s.id as customer_source_id, c.combo_name as last_combo,c.id as last_combo_id,c.billing_mode, c.billing_proportion,
        ld.last_day_pv, soa.sum_order_amount as sum_order_amount, d.agent_type, d.belong_saler,
        d.belong_csm, d.landing_page_system_domain,
        d.sign_status,
        d.industry_id,
        d.replace_operation,
        d.combo_amount,
        d.cname_domain, d.enable_data_pull_switch, d.most_binding_count,
        ldfd.last_day_fund,
        fundData.fund as total_fund
        from agent_conf a
        left join boss_agent_conf_detail d on a.agent_id = d.agent_id
        left join boss_dict s on d.customer_source = s.id and s.boss_dict_type=0
        left join (
        select t.agent_id, combo_id from (
        select max(created_at) created_at, agent_id from boss_order where order_status != 2 and combo_id is not null group by agent_id
        ) t left join boss_order h on t.agent_id = h.agent_id and t.created_at = h.created_at
        )o on a.agent_id = o.agent_id
        left join boss_combo c on o.combo_id = c.id
        left join lastDayPvData ld on a.agent_id = ld.agent_id
        left join lastDayFundData ldfd on a.agent_id = ldfd.agent_id
        left join sumOrderAmount soa on a.agent_id = soa.agent_id
        left join fundData on a.agent_id =fundData.agent_id
        left join accountData on a.agent_id=accountData.agent_id and ranks=1
        <where>
            <if test="vo.status != null">
                and a.status = #{vo.status}
            </if>
            <if test="vo.status == null">
                and a.status != 3
            </if>
            <if test="vo.remarks != null and '' != vo.remarks">
                and d.remarks like concat('%', #{vo.remarks}, '%')
            </if>
            <if test="vo.customerSource != null">
                and d.customer_source = #{vo.customerSource}
            </if>
            <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@DEFAULT">
                and d.replace_operation = 0
            </if>
            <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@OPERATION">
                and d.replace_operation = 1
            </if>
            <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@STOP_OPERATION">
                and d.replace_operation = 2
            </if>
            <if test="vo.agentIds != null and '' != vo.agentIds and vo.agentIds.size > 0">
                and a.agent_id in
                <foreach collection="vo.agentIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.companyName != null and '' != vo.companyName">
                and (d.company_name like concat('%', #{vo.companyName}, '%') or a.agent_id like concat('%', #{vo.companyName}, '%'))
            </if>
            <if test="vo.comboName != null and '' != vo.comboName">
                and c.combo_name like concat('%', #{vo.comboName}, '%')
            </if>
            <if test="vo.billingModeId != null">
                and c.billing_mode = #{vo.billingModeId}
            </if>
            <if test="vo.signStatus != null and '' != vo.signStatus">
                and d.sign_status = #{vo.signStatus}
            </if>
            <if test="vo.industryId != null and '' != vo.industryId and vo.industryId.size > 0">
                and d.industry_id in
                <foreach collection="vo.industryId" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.belongSaler != null">
                and d.belong_saler in
                <foreach collection="vo.belongSaler" item="saler" open="(" close=")" separator=",">
                    #{saler}
                </foreach>
            </if>
            <if test="vo.belongCsm != null">
                and d.belong_csm in
                <foreach collection="vo.belongCsm" item="csm" open="(" close=")" separator=",">
                    #{csm}
                </foreach>
            </if>
            <if test="vo.agentTypeId != null">
                and d.agent_type = #{vo.agentTypeId}
            </if>
        </where>
        ) a
        left join
        (
        select agent_id, sum(landing_page_pv) as landing_page_pv,
        sum(identify_qr_code_num) as identify_qr_code_num,
        sum(add_work_wechat_num) as add_work_wechat_num,
        sum(follow_official_account_num) as follow_official_account_num,
        sum(qiye_request_num) as qiye_request_num,
        sum(qiye_request_success_num) as qiye_request_success_num,
        sum(qiye_request_fail_num) as qiye_request_fail_num,
        sum(qiye_pv_num) as qiye_pv_num,
        sum(qiye_mini_pv_num) as qiye_mini_pv_num,
        sum(official_identify_qr_code_num) as official_identify_qr_code_num,
        sum(identify_group_qr_code_num) as identify_group_qr_code_num,
        sum(add_work_wechat_group_num) as add_work_wechat_group_num,
        sum(qiye_wechat_official_article_page_view_num) as qiye_wechat_official_article_page_view_num,
        sum(qiye_wechat_official_article_request_num) as qiye_wechat_official_article_request_num,
        sum(qiye_wechat_official_article_request_success_num) as qiye_wechat_official_article_request_success_num,
        sum(qiye_wechat_official_article_request_fail_num) as qiye_wechat_official_article_request_fail_num,
        sum(send_sms_num) as send_sms_num,
        sum(form_send_sms_num) as form_send_sms_num,
        sum(order_send_sms_num) as order_send_sms_num,

        <!---1.247.0新增的统计字段-->
        sum(form_submit_num) as form_submit_num,
        sum(clue_form_submit_num) as clue_form_submit_num,
        sum(douyin_applet_native_form_submit_num) as douyin_applet_native_form_submit_num,
        sum(phone_number_recieved_num) as phone_number_recieved_num,
        sum(active_message_authorization_num) as active_message_authorization_num,
        sum(pop_up_display_num) as pop_up_display_num,
        (sum(form_submit_num) +  sum(clue_form_submit_num) +sum(douyin_applet_native_form_submit_num) ) as form_submit_total_num,
        <!-- 1.263.0 饿了么小程序对接 -->
        sum(ele_pv_num) as ele_pv_num,
        sum(ele_qr_code_view_num) as ele_qr_code_view_num,
        sum(ele_identify_wechat_qr_code_num) as ele_identify_wechat_qr_code_num,
        sum(ele_add_wechat_success_num) as ele_add_wechat_success_num,
        <!--1.265.0 whatsapp -->
        sum(overseas_pv_num) as  overseas_pv_num,
        sum(whatsapp_jump_num) as  whatsapp_jump_num,
        sum(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
        sum(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,
        <!-- 1.267.0 whatsapp-->
        sum(whatsapp_customer_prologue_num) as  whatsapp_customer_prologue_num,
        sum(whatsapp_customer_send_message_num) as  whatsapp_customer_send_message_num,
        <!--1.256.0新增订单相关数据-->
        sum(order_submit_num) as order_submit_num,
        sum(order_finish_num) as order_finish_num,
        sum(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
        sum(douyin_applet_order_submit_num) as douyin_applet_order_submit_num,
        sum(douyin_applet_order_finish_num) as douyin_applet_order_finish_num,

        <!--1.264.0新增淘宝电影相关的数据-->
        sum(tao_bao_movie_applet_jump_num) as tao_bao_movie_applet_jump_num,
        sum(tao_bao_movie_applet_order_num) as tao_bao_movie_applet_order_num,
        sum(add_group_after_add_customer_service_num) as add_group_after_add_customer_service_num,
        sum(add_group_after_follow_official_account_num) as add_group_after_follow_official_account_num,
        sum(flow_source_jump_page_view_num) as flow_source_jump_page_view_num
        from boss_advertiser_account_group_day_report_new
        <if test="vo.startTime != null and vo.endTime != null">
            where day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by agent_id
        ) b
        on a.agent_id = b.agent_id
        left join
        (
        select agent_id, sum(landing_page_pv) as total_landing_page_pv
        from boss_advertiser_account_group_day_report_new
        group by agent_id
        ) c
        on a.agent_id = c.agent_id

    </select>

    <update id="updateByAgentId">
        update boss_agent_conf_detail
        set company_name = #{companyName},
        remarks = #{remarks},
        customer_source = #{customerSource},
        belong_saler = #{belongSaler},
        belong_csm = #{belongCsm},
        enable_data_pull_switch = #{enableDataPullSwitch},
        landing_page_system_domain = #{landingPageSystemDomain},
        most_binding_count = #{mostBindingCount},
        cname_domain = #{cnameDomain},
        sign_status=#{signStatus},
        industry_id=#{industryId}
        where agent_id = #{agentId}
    </update>

    <update id="updateComboCountByAgentId">
        update boss_agent_conf_detail
        set combo_count = coalesce(combo_count, 0) + #{count}
        where agent_id = #{agentId}
    </update>

    <update id="updateComboAmountByAgentId">
        update boss_agent_conf_detail
        set combo_amount = coalesce(combo_amount, 0) + #{amount}
        where agent_id = #{agentId}
    </update>

    <select id="listBelongSaler" resultType="String">
        select distinct(belong_saler)
        from boss_agent_conf_detail
    </select>

    <select id="listBelongCsm" resultType="String">
        select distinct(belong_csm)
        from boss_agent_conf_detail
    </select>

    <select id="listCustomerWithoutOrder" resultType="ai.yiye.agent.domain.boss.BossCustomer">
        select agent_id, company_name
        from boss_agent_conf_detail
        where agent_id not in
              (
                  select distinct(agent_id)
                  from boss_order
              )
    </select>

    <select id="listCustomerWithOder" resultType="ai.yiye.agent.domain.boss.BossCustomer">
        select agent_id, company_name
        from boss_agent_conf_detail
        where agent_id in
              (
                  select distinct(agent_id)
                  from boss_order
              )
    </select>

    <update id="updateAllSystemStatus">
        update marketing_advertiser_account set system_status = 0, extract_flag = false, is_extract = false
    </update>

    <select id="getBossCustomerDomain" resultType="ai.yiye.agent.domain.boss.BossCustomerDomain">
        select a.id, a.agent_id, company_name, remarks, customer_source, username, password, sign_email, phone, receive_email,
               combo_count, belong_saler, belong_csm, agent_type, landing_page_system_domain, enable_data_pull_switch, cname_domain, b.request_type,
               most_binding_count, b.domain as landing_page_domain, b.saas_env
        from boss_agent_conf_detail a left join boss_landing_page_domain b on a.landing_page_system_domain = b.id
        where a.agent_id = #{agentId}
    </select>

    <select id="getAccountEstimateConsumeDayLeftByAgentId" resultType="ai.yiye.agent.boss.vo.BossCustomerVo">
        <!--最近7天的消耗均值 -->
        with   lastSevenDayFundData as (
        select agent_id, sum(fund) / 7  as last_seven_day_fund from
        (select sum(fund) /100 * billing_proportion  as fund,agent_id, day_time from boss_advertiser_account_fund_day_report
        group by agent_id, day_time,billing_proportion) y
        where day_time between now()::timestamp + '-7 day' and now()
        group by agent_id
        ),
        lastDayFundData as (
        select agent_id, sum(fund)/count(*)  as last_day_fund
        from
        (select sum(fund)  *billing_proportion /100 as fund,agent_id, day_time, billing_proportion from boss_advertiser_account_fund_day_report
        group by agent_id, day_time,billing_proportion) x
        group by agent_id
        ),
        consumeData as  (				 select sum (fund) as fund_consume,agent_id from (
        select
        case when billing_proportion=0 then 0
        else
        sum(fund) * billing_proportion /100
        end
        as fund,agent_id  from boss_advertiser_account_fund_day_report baafdr
        group by agent_id,billing_proportion
        ) as tool group by agent_id
        ),

        fundData as (select sum(fund) as fund,agent_id from  boss_advertiser_account_fund_day_report  group by agent_id)
        select
        case
        date_part('day',cast(now() as TIMESTAMP)-cast(a.created_at as TIMESTAMP)) + 1 >= 7
        when true
        then case when last_seven_day_fund = 0 then 0 else round(coalesce((coalesce(pv_balance + coalesce(d.combo_amount - cd.fund_consume, 0), 0) - coalesce(cd.fund_consume , 0)) /
        last_seven_day_fund, 0), 1) end
        when false
        then case when last_day_fund = 0 then 0 else round(coalesce((coalesce(pv_balance + coalesce(d.combo_amount - cd.fund_consume, 0), 0) - coalesce(cd.fund_consume, 0)) / last_day_fund,
        0), 1)
        end
        end
        as estimate_combo_day_left,
        case
        date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
        when true
        then coalesce(last_seven_day_fund, 0)
        when false
        then coalesce(last_day_fund, 0)
        END as last_seven_day_fund,
               d.send_warn_info, a.agent_id, d.company_name, d.combo_amount, coalesce(total_landing_page_fund, 0) as combo_account_consume
        from
        agent_conf a left join
        boss_agent_conf_detail d
        on a.agent_id = d.agent_id
        left join
        (
        select agent_id, sum(fund)/100 * billing_proportion as total_landing_page_fund
        from boss_advertiser_account_fund_day_report
        group by agent_id,billing_proportion
        ) c
        on a.agent_id = c.agent_id
        left join lastSevenDayFundData ls on a.agent_id = ls.agent_id
        left join lastDayFundData ld on a.agent_id = ld.agent_id
        left join  consumeData cd on a.agent_id =cd.agent_id
        left join (
        select t.agent_id, combo_id from (
        select max(created_at) created_at, agent_id from boss_order where order_status != 2 and combo_id is not null group by agent_id
        ) t left join boss_order h on t.agent_id = h.agent_id and t.created_at = h.created_at
        )o on a.agent_id = o.agent_id

        where a.agent_id = #{agentId}
        limit 1


    </select>

    <update id="updateSendWarnInfoByAgentId">
        update boss_agent_conf_detail set send_warn_info = #{isSend} where agent_id = #{agentId}
    </update>
    <update id="updateReplaceType">
        update boss_agent_conf_detail set replace_operation =#{replaceOperationType} where agent_id =#{agentId}
    </update>

    <select id="getPvBalanceByAgentId" resultType="java.math.BigDecimal">
        WITH sumOrderAmount AS ( SELECT agent_id, SUM ( order_amount ) AS sum_order_amount FROM boss_order WHERE order_status = 0 OR order_status = 1 GROUP BY agent_id )
        SELECT COALESCE( ( combo_count - COALESCE ( total_landing_page_pv, 0 ) ), 0 ) * CASE
		WHEN combo_count = 0 THEN 0 ELSE COALESCE ( sum_order_amount / combo_count, 0 ) END AS balance
        FROM
	    (
	    SELECT
	    A.agent_id,
		A.created_at,
		d.combo_count AS combo_count,
		soa.sum_order_amount AS sum_order_amount
	    FROM
		agent_conf
		A LEFT JOIN boss_agent_conf_detail d ON A.agent_id = d.agent_id
		LEFT JOIN sumOrderAmount soa ON A.agent_id = soa.agent_id
	    ) A LEFT JOIN ( SELECT agent_id, SUM ( landing_page_pv ) AS total_landing_page_pv FROM boss_advertiser_account_group_day_report GROUP BY agent_id ) C ON A.agent_id = C.agent_id
        WHERE A.agent_id = #{agentId}
        ORDER BY created_at DESC LIMIT 1
    </select>

    <!--2.0统计，从clickhouse数据源聚合-->
    <select id="listCustomerByConditionsNew" resultType="ai.yiye.agent.boss.vo.excel.BossCustomerExportVo">
        <!--最近7天的落地页pv -->
        <!--最近7天的落地页pv均值 -->
        with lastSevenDayPvData as (
        select agent_id, sum(landing_page_pv) / 7 as last_seven_day_pv from
        (select sum(landing_page_pv) landing_page_pv,agent_id, day_time from boss_advertiser_account_group_day_report_new
        group by agent_id, day_time) y
        where day_time between now()::timestamp + '-7 day' and now()
        group by agent_id
        ),
        <!--若不满7天，近7天套餐使用量(落地页PV)=所有天数套餐使用量(落地页PV)/所有天数； -->
        lastDayPvData as (
        select agent_id, sum(landing_page_pv) / count(*) as last_day_pv
        from
        (select sum(landing_page_pv) landing_page_pv,agent_id from boss_advertiser_account_group_day_report_new
        group by agent_id, day_time) x
        group by agent_id
        ),
        lastSevenDayFundData as (
        select agent_id, sum(fund) / 7  as last_seven_day_fund from
        (select sum(fund) /100 * billing_proportion  as fund,agent_id, day_time from boss_advertiser_account_fund_day_report
        group by agent_id, day_time,billing_proportion) y
        where day_time between now()::timestamp + '-7 day' and now()
        group by agent_id
        ),
        lastDayFundData as (
        select agent_id, sum(fund)/count(*)  as last_day_fund
        from
        (select sum(fund)  *billing_proportion /100 as fund,agent_id, day_time, billing_proportion from boss_advertiser_account_fund_day_report
        group by agent_id, day_time,billing_proportion) x
        group by agent_id
        ),
        <!--订单金额之和 -->
        sumOrderAmount as (
        select agent_id, sum(order_amount) as sum_order_amount
        from boss_order where order_status = 0 or order_status = 1
        group by agent_id
        ),
        accountData as  (
        SELECT agent_id AS agent_id,
        bill_account_num,
        row_number() over(partition by agent_id order by day_time desc) as ranks
        from boss_advertiser_account_fund_day_report
        ),
        <!--          计费投放账户数（巨量引擎） 授权投放账户消耗金额（巨量引擎 套餐量 套餐使用量 套餐余量-->
        consumeData as  (				 select sum (fund) as fund_consume,agent_id from (
        select
        case when billing_proportion=0 then 0
        else
        sum(fund) * billing_proportion /100
        end
        as fund,agent_id  from boss_advertiser_account_fund_day_report baafdr
        <if test="vo.startTime != null and vo.endTime != null">
            where day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by agent_id,billing_proportion
        ) as tool group by agent_id
        ),

        fundData as (select sum(fund) as fund,agent_id from  boss_advertiser_account_fund_day_report  group by agent_id)
        select status as exportStatus, to_char(created_at, 'YYYY-MM-dd HH24:MI:SS') as exportCreatedAt, a.agent_id, portal_url, company_name, remarks, sign_email, coalesce(combo_count, 0)
        as combo_count, coalesce(pv_balance, 0) as pv_balance ,username,phone,receive_email,ocean_engine_construct_track_url_license_status,
        sign_status,
        source as customer_source, customer_source_id, last_combo, billing_mode, billing_proportion,last_combo_id,
        date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 as days_used,
        coalesce(landing_page_pv, 0) as combo_used,
        coalesce((combo_count - coalesce(total_landing_page_pv, 0)), 0) as combo_left,
        A.billing_account_consume,
        A.billing_accounts,
        A.total_combo_consume,
        A.combo_account_consume,
        A.combo_left_consume,
        case when billing_mode=0 then
        case
        date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
        when true
        then case when last_seven_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) /
        last_seven_day_pv, 0), 1) end
        when false
        then case when last_day_pv = 0 then 0 else round(coalesce((combo_count - coalesce(total_landing_page_pv, 0)) / last_day_pv,
        0), 1)
        end
        end
        else
        case
        date_part('day',cast(now() as TIMESTAMP)-cast(created_at as TIMESTAMP)) + 1 >= 7
        when true
        then case when last_seven_day_fund = 0 then 0 else round(coalesce((coalesce(pv_balance + coalesce(A.combo_left_consume, 0), 0) - coalesce(combo_account_consume , 0)) /
        last_seven_day_fund, 0), 1) end
        when false
        then case when last_day_fund = 0 then 0 else round(coalesce((coalesce(pv_balance + coalesce(A.combo_left_consume, 0), 0) - coalesce(combo_account_consume, 0)) / last_day_fund,
        0), 1)
        end
        end
        END as estimate_combo_day_left,
        coalesce(identify_qr_code_num, 0) as identify_qr_code_num,
        coalesce(add_work_wechat_num, 0) as add_work_wechat_num,
        coalesce(follow_official_account_num, 0) as follow_official_account_num,
        coalesce(qiye_request_num, 0) as qiye_request_num,
        coalesce(qiye_request_success_num, 0) as qiye_request_success_num,
        coalesce(qiye_request_fail_num, 0) as qiye_request_fail_num,
        coalesce(qiye_pv_num, 0) as qiye_pv_num,
        coalesce(qiye_mini_pv_num, 0) as qiye_mini_pv_num,
        <!--以下为1.197.0新增三个数 -->
        coalesce(official_identify_qr_code_num, 0) as official_identify_qr_code_num,
        coalesce(identify_group_qr_code_num, 0) as identify_group_qr_code_num,
        coalesce(add_work_wechat_group_num, 0) as add_work_wechat_group_num,
        <!--以下为1.197.0新增三个率 -->
        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(official_identify_qr_code_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as official_identify_qr_code_rate,
        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(identify_group_qr_code_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as identify_group_qr_code_rate,
        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_work_wechat_group_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as add_work_wechat_group_rate,
        coalesce(qiye_wechat_official_article_page_view_num, 0) as qiye_wechat_official_article_page_view_num,
        coalesce(qiye_wechat_official_article_request_num, 0) as qiye_wechat_official_article_request_num,
        coalesce(qiye_wechat_official_article_request_success_num, 0) as qiye_wechat_official_article_request_success_num,
        coalesce(qiye_wechat_official_article_request_fail_num, 0) as qiye_wechat_official_article_request_fail_num,
        coalesce(send_sms_num, 0) as send_sms_num,
        coalesce(form_send_sms_num, 0) as form_send_sms_num,
        coalesce(order_send_sms_num, 0) as order_send_sms_num,
        coalesce(pop_up_display_num, 0) as pop_up_display_num,


        <!--1.247.0 新增的统计字段-->
        coalesce(form_submit_num, 0) as form_submit_num,
        coalesce(clue_form_submit_num, 0) as clue_form_submit_num,
        coalesce(douyin_applet_native_form_submit_num, 0) as douyin_applet_native_form_submit_num,
        coalesce(phone_number_recieved_num, 0) as phone_number_recieved_num,
        coalesce(active_message_authorization_num, 0) as active_message_authorization_num,
        coalesce(form_submit_total_num, 0) as form_submit_total_num,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(form_submit_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as form_submit_rate,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(clue_form_submit_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as clue_form_submit_rate,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(douyin_applet_native_form_submit_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as douyin_applet_native_form_submit_rate,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(phone_number_recieved_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as phone_number_recieved_rate,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(active_message_authorization_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as active_message_authorization_rate,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(form_submit_total_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as form_submit_total_rate,

        <!--1.247.0 新增的统计字段-->
        <!-- 1.263.0 饿了么小程序对接 -->
        coalesce(ele_pv_num,0) as ele_pv_num,
        coalesce(ele_qr_code_view_num,0) as ele_qr_code_view_num,
        coalesce(ele_identify_wechat_qr_code_num,0) as ele_identify_wechat_qr_code_num,
        coalesce(ele_add_wechat_success_num,0) as ele_add_wechat_success_num,
        <!-- 1.265.0 whatsapp-->
        coalesce(whatsapp_jump_num,0) as  whatsapp_jump_num,
        coalesce(whatsapp_add_friend_success_num,0) as  whatsapp_add_friend_success_num,
        coalesce(whatsapp_user_open_mouth_num,0) as  whatsapp_user_open_mouth_num,
        coalesce(overseas_pv_num,0)as  overseas_pv_num,
        <!-- 1.267.0 whatsapp-->
        coalesce(whatsapp_customer_prologue_num,0) as  whatsapp_customer_prologue_num,
        coalesce(whatsapp_customer_send_message_num,0) as  whatsapp_customer_send_message_num,
        <!--1.256.0新增订单相关字段-->
        coalesce(order_submit_num,0) as order_submit_num,
        coalesce(order_finish_num,0) as order_finish_num,
        coalesce(online_shop_buy_goods_success_num, 0) as online_shop_buy_goods_success_num,
        coalesce(douyin_applet_order_submit_num, 0) as douyin_applet_order_submit_num,
        coalesce(douyin_applet_order_finish_num, 0) as douyin_applet_order_finish_num,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(order_submit_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as order_submit_rate,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(order_finish_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as order_finish_rate,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(online_shop_buy_goods_success_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as online_shop_buy_goods_success_rate,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(douyin_applet_order_submit_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as douyin_applet_order_submit_rate,

        case when coalesce(douyin_applet_order_submit_num, 0) = 0 then 0 else coalesce(douyin_applet_order_finish_num, 0) /
        coalesce(douyin_applet_order_submit_num, 0) * 100 end as douyin_applet_order_finish_rate,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else (coalesce(order_finish_num, 0) + coalesce(douyin_applet_order_finish_num, 0)) /
        coalesce(landing_page_pv, 0) * 100 end as comprehensive_payment_rate,
        <!--1.256.0-->

        <!--1.264.0新增淘宝订单相关字段-->
        coalesce(tao_bao_movie_applet_jump_num, 0) as tao_bao_movie_applet_jump_num,
        coalesce(tao_bao_movie_applet_order_num, 0) as tao_bao_movie_applet_order_num,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(tao_bao_movie_applet_jump_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as tao_bao_movie_applet_jump_rate,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(tao_bao_movie_applet_order_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as tao_bao_movie_applet_order_rate,
        <!--1.264.0-->

        <!--1.271.0新增加群相关字段-->
        coalesce(add_group_after_add_customer_service_num, 0) as add_group_after_add_customer_service_num,
        coalesce(add_group_after_follow_official_account_num, 0) as add_group_after_follow_official_account_num,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_group_after_add_customer_service_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as add_group_after_add_customer_service_rate,

        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_group_after_follow_official_account_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as add_group_after_follow_official_account_rate,
        <!--1.271.0-->

        case when combo_count = 0 then 0 else coalesce(sum_order_amount / combo_count, 0) end as unit_price,
        case when add_work_wechat_num = 0 then 0 when combo_count = 0 then 0 when landing_page_pv = 0 then 0 else
        coalesce(sum_order_amount / combo_count * landing_page_pv / add_work_wechat_num, 0) end as add_fans_cost,
        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(identify_qr_code_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as identify_qr_code_rate,
        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(follow_official_account_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as follow_official_account_rate,
        case when coalesce(landing_page_pv, 0) = 0 then 0 else coalesce(add_work_wechat_num, 0) /
        coalesce(landing_page_pv, 0) * 100 end as add_work_wechat_rate,
        case when coalesce(identify_qr_code_num, 0) = 0 then 0 else coalesce(add_work_wechat_num, 0) /
        coalesce(identify_qr_code_num, 0) * 100 end as identify_qr_code_add_work_wechat_rate,
        coalesce(flow_source_jump_page_view_num, 0) as flow_source_jump_page_view_num,
        agent_type,
        belong_saler,
        belong_csm,
        landing_page_system_domain,
        cname_domain,
        most_binding_count,
        industry_id,
        replace_operation,
        sign_status,
        enable_data_pull_switch,
        case
        billing_mode is not null
        when true
        then case when billing_mode = 0 then coalesce((combo_count - coalesce(total_landing_page_pv, 0)), 0) * case when combo_count = 0 then 0 else coalesce(sum_order_amount / combo_count, 0) end
        else coalesce(pv_balance + coalesce(A.combo_left_consume, 0), 0) end
        when false
        then 0
        END as balance
        from
        (
        select a.status, a.created_at, a.agent_id, a.portal_url, d.company_name, d.remarks, d.sign_email,a.license::json->>'oceanEngineConstructTrackUrlLicenseStatus' as ocean_engine_construct_track_url_license_status,
        d.password,d.username,d.phone,d.receive_email,
        d.combo_count as combo_count, s.source, s.id as customer_source_id, c.combo_name as last_combo,c.id as last_combo_id,c.billing_mode, c.billing_proportion,
        ls.last_seven_day_pv, ld.last_day_pv, soa.sum_order_amount as sum_order_amount, d.agent_type, d.belong_saler,
        d.belong_csm, d.landing_page_system_domain,
        d.sign_status,
        d.industry_id,
        d.replace_operation,
        d.combo_amount,
        accountData.bill_account_num as billing_accounts,
        d.cname_domain, d.enable_data_pull_switch, d.most_binding_count,d.pv_balance,
        <!--                 投放账户消耗金额 -->
        fundData.fund as  billing_account_consume,
        <!--                 pv套餐量 -->
        case when c.billing_mode = 1 then d.combo_amount else null end as total_combo_consume,
        <!--                  套餐使用量 每日投放账户消耗 * 收费比例 之和 -->
        cd.fund_consume as combo_account_consume,
        lsfd.last_seven_day_fund,
        ldfd.last_day_fund,
        fundData.fund as total_fund,
        <!--          套餐余量 -->
        d.combo_amount - cd.fund_consume as combo_left_consume

        from agent_conf a
        left join boss_agent_conf_detail d on a.agent_id = d.agent_id
        left join boss_dict s on d.customer_source = s.id and s.boss_dict_type=0
        left join (
        select t.agent_id, combo_id from (
        select max(created_at) created_at, agent_id from boss_order where order_status != 2 and combo_id is not null group by agent_id
        ) t left join boss_order h on t.agent_id = h.agent_id and t.created_at = h.created_at
        )o on a.agent_id = o.agent_id
        left join boss_combo c on o.combo_id = c.id
        left join lastSevenDayPvData ls on a.agent_id = ls.agent_id
        left join lastDayPvData ld on a.agent_id = ld.agent_id
        left join lastSevenDayFundData lsfd on a.agent_id = lsfd.agent_id
        left join lastDayFundData ldfd on a.agent_id = ldfd.agent_id
        left join sumOrderAmount soa on a.agent_id = soa.agent_id
        left join consumeData cd on a.agent_id = cd.agent_id
        left join fundData on a.agent_id =fundData.agent_id
        left join accountData on a.agent_id=accountData.agent_id and ranks=1
        <where>
            <if test="vo.status != null">
                and a.status = #{vo.status}
            </if>
            <if test="vo.addedValue!=null">
                <foreach collection="vo.addedValue" item="item">
                    and position(#{item} in cast(a.white_types as varchar))>0
                </foreach>
            </if>
            <if test="vo.remarks != null and '' != vo.remarks">
                and d.remarks like concat('%', #{vo.remarks}, '%')
            </if>
            <if test="vo.customerSource != null">
                and d.customer_source = #{vo.customerSource}
            </if>
            <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@DEFAULT">
                and d.replace_operation = 0
            </if>
            <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@OPERATION">
                and d.replace_operation = 1
            </if>
            <if test="vo.replaceOperationType == @ai.yiye.agent.domain.enumerations.ReplaceOperationType@STOP_OPERATION">
                and d.replace_operation = 2
            </if>
            <if test="vo.companyName != null and '' != vo.companyName">
                and (d.company_name like concat('%', #{vo.companyName}, '%') or a.agent_id like concat('%', #{vo.companyName}, '%'))
            </if>
            <if test="vo.comboName != null and '' != vo.comboName">
                and c.combo_name like concat('%', #{vo.comboName}, '%')
            </if>
            <if test="vo.billingModeId != null">
                and c.billing_mode = #{vo.billingModeId}
            </if>
            <if test="vo.signStatus != null and '' != vo.signStatus">
                and d.sign_status = #{vo.signStatus}
            </if>
            <if test="vo.industryId != null and '' != vo.industryId and vo.industryId.size > 0">
                and d.industry_id in
                <foreach collection="vo.industryId" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.belongSaler != null">
                and d.belong_saler in
                <foreach collection="vo.belongSaler" item="saler" open="(" close=")" separator=",">
                    #{saler}
                </foreach>
            </if>
            <if test="vo.belongCsm != null">
                and d.belong_csm in
                <foreach collection="vo.belongCsm" item="csm" open="(" close=")" separator=",">
                    #{csm}
                </foreach>
            </if>
            <if test="vo.agentTypeId != null">
                and d.agent_type = #{vo.agentTypeId}
            </if>
        </where>
        ) a
        left join
        (
        select agent_id, sum(landing_page_pv) as landing_page_pv,
        sum(identify_qr_code_num) as identify_qr_code_num,
        sum(add_work_wechat_num) as add_work_wechat_num,
        sum(follow_official_account_num) as follow_official_account_num,
        sum(qiye_request_num) as qiye_request_num,
        sum(qiye_request_success_num) as qiye_request_success_num,
        sum(qiye_request_fail_num) as qiye_request_fail_num,
        sum(qiye_pv_num) as qiye_pv_num,
        sum(qiye_mini_pv_num) as qiye_mini_pv_num,
        sum(official_identify_qr_code_num) as official_identify_qr_code_num,
        sum(identify_group_qr_code_num) as identify_group_qr_code_num,
        sum(add_work_wechat_group_num) as add_work_wechat_group_num,
        sum(qiye_wechat_official_article_page_view_num) as qiye_wechat_official_article_page_view_num,
        sum(qiye_wechat_official_article_request_num) as qiye_wechat_official_article_request_num,
        sum(qiye_wechat_official_article_request_success_num) as qiye_wechat_official_article_request_success_num,
        sum(qiye_wechat_official_article_request_fail_num) as qiye_wechat_official_article_request_fail_num,
        sum(send_sms_num) as send_sms_num,
        sum(form_send_sms_num) as form_send_sms_num,
        sum(order_send_sms_num) as order_send_sms_num,
        sum(pop_up_display_num) as pop_up_display_num,

        <!---1.247.0新增的统计字段-->
        sum(form_submit_num) as form_submit_num,
        sum(clue_form_submit_num) as clue_form_submit_num,
        sum(douyin_applet_native_form_submit_num) as douyin_applet_native_form_submit_num,
        sum(phone_number_recieved_num) as phone_number_recieved_num,
        sum(active_message_authorization_num) as active_message_authorization_num,
        (sum(form_submit_num) +  sum(clue_form_submit_num) +sum(douyin_applet_native_form_submit_num) ) as form_submit_total_num,
        <!-- 1.263.0 饿了么小程序对接 -->
        sum(ele_pv_num) as ele_pv_num,
        sum(ele_qr_code_view_num) as ele_qr_code_view_num,
        sum(ele_identify_wechat_qr_code_num) as ele_identify_wechat_qr_code_num,
        sum(ele_add_wechat_success_num) as ele_add_wechat_success_num,
        <!--1.265.0 whatsapp -->
        sum(overseas_pv_num) as  overseas_pv_num,
        sum(whatsapp_jump_num) as  whatsapp_jump_num,
        sum(whatsapp_add_friend_success_num) as  whatsapp_add_friend_success_num,
        sum(whatsapp_user_open_mouth_num) as  whatsapp_user_open_mouth_num,
        <!-- 1.267.0 whatsapp-->
        sum(whatsapp_customer_prologue_num) as  whatsapp_customer_prologue_num,
        sum(whatsapp_customer_send_message_num) as  whatsapp_customer_send_message_num,
        <!--1.256.0新增字段-->
        sum(order_submit_num) as order_submit_num,
        sum(order_finish_num) as order_finish_num,
        sum(online_shop_buy_goods_success_num) as online_shop_buy_goods_success_num,
        sum(douyin_applet_order_submit_num) as douyin_applet_order_submit_num,
        sum(douyin_applet_order_finish_num) as douyin_applet_order_finish_num,
        <!--1.264.0新增字段-->
        sum(tao_bao_movie_applet_jump_num) as tao_bao_movie_applet_jump_num,
        sum(tao_bao_movie_applet_order_num) as tao_bao_movie_applet_order_num,
        <!--1.271.0新增字段-->
        sum(add_group_after_add_customer_service_num) as add_group_after_add_customer_service_num,
        sum(add_group_after_follow_official_account_num) as add_group_after_follow_official_account_num,
        sum(flow_source_jump_page_view_num) as flow_source_jump_page_view_num

        from boss_advertiser_account_group_day_report_new
        <if test="vo.startTime != null and vo.endTime != null">
            where day_time between to_date(#{vo.startTime}, 'yyyy-MM-dd') and to_date(#{vo.endTime}, 'yyyy-MM-dd')
        </if>
        group by agent_id
        ) b
        on a.agent_id = b.agent_id

        left join
        (
        select agent_id, sum(landing_page_pv) as total_landing_page_pv
        from boss_advertiser_account_group_day_report_new
        group by agent_id
        ) c
        on a.agent_id = c.agent_id

        <if test="vo.sort != null and vo.order != null">
            order by
            <if test="vo.sort =='createdAt'">
                created_at ${vo.order}
            </if>
            <if test="vo.sort == 'daysUsed'">
                days_used ${vo.order}
            </if>
            <if test="vo.sort == 'comboCount'">
                combo_count ${vo.order}
            </if>
            <if test="vo.sort == 'comboUsed'">
                combo_used ${vo.order}
            </if>
            <if test="vo.sort == 'comboLeft'">
                combo_left ${vo.order}
            </if>
            <if test="vo.sort == 'estimateComboDayLeft'">
                estimate_combo_day_left ${vo.order}
            </if>
            <if test="vo.sort == 'identifyQrCodeNum'">
                identify_qr_code_num ${vo.order}
            </if>
            <if test="vo.sort == 'followOfficialAccountNum'">
                follow_official_account_num ${vo.order}
            </if>
            <if test="vo.sort == 'addWorkWechatNum'">
                add_work_wechat_num ${vo.order}
            </if>
            <if test="vo.sort == 'unitPrice'">
                unit_price ${vo.order}
            </if>
            <if test="vo.sort == 'addFansCost'">
                add_fans_cost ${vo.order}
            </if>
            <if test="vo.sort == 'identifyQrCodeRate'">
                identify_qr_code_rate ${vo.order}
            </if>
            <if test="vo.sort == 'followOfficialAccountRate'">
                follow_official_account_rate ${vo.order}
            </if>
            <if test="vo.sort == 'addWorkWechatRate'">
                add_work_wechat_rate ${vo.order}
            </if>
            <if test="vo.sort == 'identifyQrCodeAddWorkWechatRate'">
                identify_qr_code_add_work_wechat_rate ${vo.order}
            </if>
            <if test="vo.sort == 'balance'">
                balance ${vo.order}
            </if>
            <if test="vo.sort == 'billingAccounts'">
                billing_accounts ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'billingAccountConsume'">
                billing_account_consume ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'totalComboConsume'">
                total_combo_consume ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'comboAccountConsume'">
                combo_account_consume ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'comboLeftConsume'">
                combo_left_consume ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'qiyeRequestNum'">
                qiye_request_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeRequestSuccessNum'">
                qiye_request_success_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeRequestFailNum'">
                qiye_request_fail_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyePvNum'">
                qiye_pv_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeMiniPvNum'">
                qiye_mini_pv_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeWechatOfficialArticlePageViewNum'">
                qiye_wechat_official_article_page_view_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeWechatOfficialArticleRequestNum'">
                qiye_wechat_official_article_request_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeWechatOfficialArticleRequestSuccessNum'">
                qiye_wechat_official_article_request_success_num ${vo.order}
            </if>
            <if test="vo.sort == 'qiyeWechatOfficialArticleRequestFailNum'">
                qiye_wechat_official_article_request_fail_num ${vo.order}
            </if>
            <!--以下为1.197.0新增三个排序 -->
            <if test="vo.sort == 'officialIdentifyQrCodeNum'">
                official_identify_qr_code_num ${vo.order}
            </if>
            <if test="vo.sort == 'identifyGroupQrCodeNum'">
                identify_group_qr_code_num ${vo.order}
            </if>
            <if test="vo.sort == 'addWorkWechatGroupNum'">
                add_work_wechat_group_num ${vo.order}
            </if>
            <if test="vo.sort == 'officialIdentifyQrCodeRate'">
                official_identify_qr_code_rate ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'identifyGroupQrCodeRate'">
                identify_group_qr_code_rate ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'addWorkWechatGroupRate'">
                add_work_wechat_group_rate ${vo.order} nulls last
            </if>
            <if test="vo.sort == 'sendSmsNum'">
                send_sms_num ${vo.order}
            </if>
            <if test="vo.sort == 'formSendSmsNum'">
                form_send_sms_num ${vo.order}
            </if>
            <if test="vo.sort == 'orderSendSmsNum'">
                order_send_sms_num ${vo.order}
            </if>

            <if test="vo.sort == 'formSubmitNum'">
                form_submit_num ${vo.order}
            </if>

            <if test="vo.sort == 'formSubmitRate'">
                form_submit_rate ${vo.order}
            </if>

            <if test="vo.sort == 'formSubmitTotalNum'">
                form_submit_total_num ${vo.order}
            </if>

            <if test="vo.sort == 'formSubmitTotalRate'">
                form_submit_total_rate ${vo.order}
            </if>

            <if test="vo.sort == 'clueFormSubmitNum'">
                clue_form_submit_num ${vo.order}
            </if>

            <if test="vo.sort == 'clueFormSubmitRate'">
                clue_form_submit_rate ${vo.order}
            </if>

            <if test="vo.sort == 'douyinAppletNativeFormSubmitNum'">
                douyin_applet_native_form_submit_num ${vo.order}
            </if>

            <if test="vo.sort == 'douyinAppletNativeFormSubmitRate'">
                douyin_applet_native_form_submit_rate ${vo.order}
            </if>

            <if test="vo.sort == 'phoneNumberRecievedNum'">
                phone_number_recieved_num ${vo.order}
            </if>

            <if test="vo.sort == 'phoneNumberRecievedRate'">
                phone_number_recieved_rate ${vo.order}
            </if>

            <if test="vo.sort == 'activeMessageAuthorizationNum'">
                active_message_authorization_num ${vo.order}
            </if>

            <if test="vo.sort == 'activeMessageAuthorizationRate'">
                active_message_authorization_rate ${vo.order}
            </if>
            <if test="vo.sort == 'elePvNum'">
                ele_pv_num ${vo.order}
            </if>
            <if test="vo.sort == 'eleQrCodeViewNum'">
                ele_qr_code_view_num ${vo.order}
            </if>
            <if test="vo.sort == 'eleIdentifyWechatQrCodeNum'">
                ele_identify_wechat_qr_code_num ${vo.order}
            </if>
            <if test="vo.sort == 'eleAddWechatSuccessNum'">
                ele_add_wechat_success_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappJumpNum'">
                whatsapp_jump_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappAddFriendSuccessNum'">
                whatsapp_add_friend_success_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappUserOpenMouthNum'">
                whatsapp_user_open_mouth_num ${vo.order}
            </if>
            <if test="vo.sort == 'overseasPvNum'">
                overseas_pv_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappCustomerPrologueNum'">
                whatsapp_customer_prologue_num ${vo.order}
            </if>
            <if test="vo.sort == 'whatsappCustomerSendMessageNum'">
                whatsapp_customer_send_message_num ${vo.order}
            </if>

            <if test="vo.sort == 'taoBaoMovieAppletJumpNum'">
                tao_bao_movie_applet_jump_num ${vo.order}
            </if>

            <if test="vo.sort == 'taoBaoMovieAppletJumpRate'">
                tao_bao_movie_applet_jump_Rate ${vo.order}
            </if>

            <if test="vo.sort == 'taoBaoMovieAppletOrderNum'">
                tao_bao_movie_applet_order_num ${vo.order}
            </if>

            <if test="vo.sort == 'taoBaoMovieAppletOrderRate'">
                tao_bao_movie_applet_order_rate ${vo.order}
            </if>

            <if test="vo.sort == 'addGroupAfterAddCustomerServiceNum'">
                add_group_after_add_customer_service_num ${vo.order}
            </if>

            <if test="vo.sort == 'addGroupAfterAddCustomerServiceRate'">
                add_group_after_add_customer_service_rate ${vo.order}
            </if>

            <if test="vo.sort == 'addGroupAfterFollowOfficialAccountNum'">
                add_group_after_follow_official_account_num ${vo.order}
            </if>

            <if test="vo.sort == 'addGroupAfterFollowOfficialAccountRate'">
                add_group_after_follow_official_account_rate ${vo.order}
            </if>
            <if test="vo.sort == 'flowSourceJumpPageViewNum'">
                flow_source_jump_page_view_num ${vo.order}
            </if>
        </if>
    </select>
    <select id="agentConflist" resultType="ai.yiye.agent.boss.domain.AgentConfVo">
        select agent_conf.agent_id, agent_conf.status, boss_agent_conf_detail.company_name, agent_conf.created_at
        from agent_conf
                 left join boss_agent_conf_detail on agent_conf.agent_id = boss_agent_conf_detail.agent_id
        where agent_conf.status in (0,1)
    </select>

</mapper>
