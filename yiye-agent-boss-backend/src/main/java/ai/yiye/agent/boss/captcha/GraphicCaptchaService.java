package ai.yiye.agent.boss.captcha;

import ai.yiye.agent.boss.captcha.store.CaptchaStore;
import ai.yiye.agent.boss.web.exception.InvalidCaptchaException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 图形验证码Service
 *
 * <AUTHOR>
 */
@Service
public class GraphicCaptchaService implements CaptchaService {

    private final CaptchaStore captchaStore;

    public GraphicCaptchaService(CaptchaStore captchaStore) {
        this.captchaStore = captchaStore;
    }

    @Override
    public String createCaptcha(String key) {
        String randomChar = CaptchaRandom.DIGIT.getRandomChar(4);
        captchaStore.saveCaptcha(key, randomChar);
        return randomChar;
    }

    @Override
    public void validate(String key, String captcha) {

        String captchaInDb = captchaStore.getAndRemoveCaptcha(key);
        if (captchaInDb == null || !Objects.equals(captcha, captchaInDb)) {
            throw new InvalidCaptchaException();
        }
    }

    @Override
    public CaptchaType support() {
        return CaptchaType.GRAPHIC;
    }

    @Scheduled(cron = "0 0 0 * * ?")
    public void clearCaptchas() {
        captchaStore.removeAll();
    }
}
