package ai.yiye.agent.boss.domain;

import ai.yiye.agent.domain.enumerations.ReplaceOperationType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/11/19 14:51
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("boss_advertiser_account_group")
public class BossAdvertiserAccountGroup {
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 项目id
     */
    private Long advertiserAccountGroupId;
    /**
     * 客户id
     */
    private String agentId;
    /**
     * 项目名
     */
    private String groupName;

    /**
     * 是否为代运营，默认值为0，表示无状态。有三个枚举值，0为无状态，1为运营，2为暂停运营
     */
    private ReplaceOperationType replaceOperation;

}
