package ai.yiye.agent.boss.web.exception;

import lombok.experimental.UtilityClass;

import java.util.function.Supplier;

/**
 * A collection of utility methods that support asserting conditions.
 *
 * <AUTHOR>
 */
@UtilityClass
public class Asserts {

    /**
     * Assert that {@code obj} is not null, otherwise throw RestException.
     */
    public void assertNotNull(Object obj, String message) {
        if (obj == null) {
            throw new RestException(message);
        }
    }

    /**
     * Assert that {@code obj} is not null, otherwise throw RestException.
     */
    public void assertNotNull(Object obj, String message, Object... args) {
        if (obj == null) {
            throw new RestException(message, args);
        }
    }

    /**
     * Assert that {@code obj} is not null, otherwise the supplied RestException.
     */
    public void assertNotNull(Object obj, RuntimeException exception) {
        if (obj == null) {
            throw exception;
        }
    }

    /**
     * Assert that {@code obj} is not null. otherwise the supplied RestException.
     */
    public void assertNotNull(Object obj, Supplier<RuntimeException> exceptionSupplier) {
        if (obj == null) {
            throw exceptionSupplier.get();
        }
    }
}
