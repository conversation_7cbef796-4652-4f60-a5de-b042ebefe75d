package ai.yiye.agent.boss.mapper;

import ai.yiye.agent.domain.OrganizationFlow;
import ai.yiye.agent.domain.enumerations.OrganizationFlowType;
import ai.yiye.agent.domain.vo.OrganizationFlowListVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

public interface OrganizationFlowMapper extends BaseMapper<OrganizationFlow> {

    IPage<OrganizationFlowListVO> listFlows(IPage<OrganizationFlowListVO> page, @Param("organizationId") Long organizationId, @Param("flowType") OrganizationFlowType flowType);
}
