package ai.yiye.agent.boss.service;

import ai.yiye.agent.boss.mapper.BossComboMapper;
import ai.yiye.agent.boss.mapper.BossCustomerMapper;
import ai.yiye.agent.boss.mapper.BossOrderMapper;
import ai.yiye.agent.boss.mapper.PermissionMapper;
import ai.yiye.agent.boss.mybatis.multidatasource.service.AgentConfService;
import ai.yiye.agent.boss.sender.BossCustomerInitDbSender;
import ai.yiye.agent.boss.sender.BossOrderSender;
import ai.yiye.agent.boss.sender.UcenterSender;
import ai.yiye.agent.boss.vo.BossConditionVo;
import ai.yiye.agent.boss.vo.BossOrderVo;
import ai.yiye.agent.boss.web.exception.RestException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.common.redis.RedisConstant;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.domain.Permission;
import ai.yiye.agent.domain.boss.BossCombo;
import ai.yiye.agent.domain.boss.BossCustomer;
import ai.yiye.agent.domain.boss.BossOrder;
import ai.yiye.agent.domain.dto.BossComboAndOrderDto;
import ai.yiye.agent.domain.dto.ChangeUserCacheDto;
import ai.yiye.agent.domain.enumerations.AgentStatusEnum;
import ai.yiye.agent.domain.enumerations.BillingMode;
import ai.yiye.agent.domain.enumerations.BossOrderStatus;
import ai.yiye.agent.domain.enumerations.ChangeUserCacheType;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static ai.yiye.agent.boss.constants.SessionConstants.USER_SESSION_CACHE_KEY;
import static ai.yiye.agent.boss.constants.SessionConstants.USER_SESSION_CACHE_TOKEN_KEY;
import static ai.yiye.agent.domain.enumerations.OrderType.CHANGE;

/**
 * <AUTHOR>
 * @date 2021/11/16 14:39
 */
@Service
@Slf4j
public class BossOrderService extends ServiceImpl<BossOrderMapper, BossOrder> {

    @Autowired
    private BossOrderMapper bossOrderMapper;
    @Autowired
    private BossCustomerInitDbSender bossCustomerInitDbSender;
    @Autowired
    private AgentConfService agentConfService;
    @Autowired
    private BossCustomerService bossCustomerService;

    @Autowired
    private BossComboMapper bossComboMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private BossCustomerMapper bossCustomerMapper;

    @Autowired
    private RedisTemplate<String, Object> objectRedisTemplate;

    @Autowired
    private BossAdvertiserAccountGroupDayReportService bossAdvertiserAccountGroupDayReportService;

    @Resource
    private BossOrderSender bossOrderSender;

    @Resource
    private RedisTemplate<String, Object> defaultObjectRedisTemplate;

    @Autowired
    private UcenterSender ucenterSender;

    public IPage<BossOrderVo> getBossOrderPage(Page<BossOrder> page, BossConditionVo bossConditionVo) {
        // 先把可取消的订单都设置为不可取消
        bossOrderMapper.updateEnableOrderStatus();

        // 查询所有可取消的订单
        List<BossOrder> list = bossOrderMapper.listEnableOrders();
        if (CollectionUtils.isNotEmpty(list)) {
            bossOrderMapper.updateOrderStatus(list);
        }
        return bossOrderMapper.getBossOrderPage(page, bossConditionVo);
    }

    public ResponseEntity<BossOrder> saveBossOrder(BossOrder bossOrder) {
        switch (bossOrder.getOrderType()) {

            // 新增
            case ADD:
                addOrder(bossOrder);
                break;

            // 续费
            case RENEW:
                renewOrder(bossOrder);
                break;

            // 变更
            case CHANGE:
                changeOrder(bossOrder);
                break;

            // 赠量
            case HANDSEL:
                handselOrder(bossOrder);
                break;

            // 减量
            case DECREMENT:
                decrementOrder(bossOrder);
                break;

            default:
                break;
        }
        TenantContextHolder.clearContext();
        baseMapper.insert(bossOrder);
        //订单类型为变更的时候，发送MQ去修正报表相关数据
        if (CHANGE.equals(bossOrder.getOrderType())) {
            bossOrderSender.changeOrder(bossOrder);
        }
        deleteCache(bossOrder.getAgentId());
        return ResponseEntity.ok().build();
    }

    private void deleteCache(String agentId) {
        defaultObjectRedisTemplate.delete(RedisConstant.BOSS_COMBO_NEWEST + agentId);
        defaultObjectRedisTemplate.delete(RedisConstant.BOSS_AGENT_CONF_DETAIL_TAKE_EFFECT_TIME + agentId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void decrementOrder(BossOrder bossOrder) {
        BossCombo bossCombo = bossComboMapper.selectById(bossOrder.getComboId());
        if (BillingMode.PV.equals(bossCombo.getBillingMode())) {
            bossCustomerMapper.updateComboCountByAgentId(-bossOrder.getIncreasePv(), bossOrder.getAgentId());
        } else {
            bossCustomerMapper.updateComboAmountByAgentId(bossOrder.getIncreaseAmount().negate(), bossOrder.getAgentId());
        }
//        bossAdvertiserAccountGroupDayReportService.sendWarnInfo(bossOrder.getAgentId());
    }

    public static void main(String[] args) {
        BossOrder bossOrder = new BossOrder();
        bossOrder.setIncreaseAmount(new BigDecimal("11.11"));
        System.out.println(bossOrder.getIncreaseAmount());
        bossOrder.setIncreaseAmount(bossOrder.getIncreaseAmount().negate());
        System.out.println(bossOrder.getIncreaseAmount());
    }

    @Transactional(rollbackFor = Exception.class)
    public void handselOrder(BossOrder bossOrder) {
        BossCombo bossCombo = bossComboMapper.selectById(bossOrder.getComboId());
        if (BillingMode.PV.equals(bossCombo.getBillingMode())) {
            bossCustomerMapper.updateComboCountByAgentId(bossOrder.getIncreasePv(), bossOrder.getAgentId());
        } else {
            bossCustomerMapper.updateComboAmountByAgentId(bossOrder.getIncreaseAmount(), bossOrder.getAgentId());
        }
        bossAdvertiserAccountGroupDayReportService.updateSendInfo(bossOrder.getAgentId());
    }

    private void changeOrder(BossOrder bossOrder) {
        BossCombo bossCombo = bossComboMapper.selectById(bossOrder.getComboId());
        BossOrder oldOrder = bossOrderMapper.selectOne(new LambdaQueryWrapper<BossOrder>().eq(BossOrder::getAgentId, bossOrder.getAgentId()).ne(BossOrder::getOrderStatus, BossOrderStatus.ALREADY_CANCEL).isNotNull(BossOrder::getComboId).orderByDesc(BossOrder::getCreatedAt).last("limit 1"));
        if (oldOrder == null) {
            return;
        }

        BossCombo oldBossCombo = bossComboMapper.selectById(oldOrder.getComboId());
        if (BillingMode.PV.equals(bossCombo.getBillingMode())) {
            bossCustomerMapper.updateComboCountByAgentId(bossCombo.getComboPv(), bossOrder.getAgentId());
        } else {
            bossCustomerMapper.updateComboAmountByAgentId(bossOrder.getOrderAmount(), bossOrder.getAgentId());
        }
        bossAdvertiserAccountGroupDayReportService.updateSendInfo(bossOrder.getAgentId());

        // 如果新套餐和老套餐的权限相同，则直接更新套餐量即可
        if (Arrays.equals(bossCombo.getPermissionIds(), oldBossCombo.getPermissionIds())) {
            return;
        }

        // 修改权限，删除权限组
        Long[] permissionIds = bossComboMapper.selectById(bossOrder.getComboId()).getPermissionIds();
        List<Permission> permissionList = permissionMapper.listByParentIds(permissionIds);
        TenantContextHolder.set(bossOrder.getAgentId());

        // 在同一个类中外层不使用事物，内层方法使用事物，则事物注解不生效，需要写在其他类中重新走代理
        bossCustomerService.resetPermission(permissionList);

        // 清除该环境的所有token,让用户退出登录
        ucenterSender.ucenterServiceClearUserLoginCache(new ChangeUserCacheDto()
            .setChangeUserCacheType(ChangeUserCacheType.FUZZY_DELETE_BY_AGENT_ID)
            .setAgentId(bossOrder.getAgentId())
        );

        // 删除userCache缓存
        ucenterSender.ucenterServiceClearUserLoginCache(new ChangeUserCacheDto()
            .setChangeUserCacheType(ChangeUserCacheType.FUZZY_DELETE_USER_SESSION_CACHE_KEY_BY_AGENT_ID)
            .setAgentId(bossOrder.getAgentId())
        );

    }

    private void clearCache(String key) {
        Set<String> keys = objectRedisTemplate.keys(key);
        if (CollectionUtils.isEmpty(keys)) {
            TenantContextHolder.clearContext();
            return;
        }
        objectRedisTemplate.delete(keys);
    }

    @Transactional(rollbackFor = Exception.class)
    public void renewOrder(BossOrder bossOrder) {
        updateComboCount(bossOrder);
        bossAdvertiserAccountGroupDayReportService.updateSendInfo(bossOrder.getAgentId());
    }

    private void addOrder(BossOrder bossOrder) {
        if (agentConfService.getOne(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getAgentId, bossOrder.getAgentId()).notIn(AgentConf::getStatus, AgentStatusEnum.OPENING)) == null) {
            AgentConf agentConf = agentConfService.getOne(new LambdaQueryWrapper<AgentConf>().eq(AgentConf::getAgentId, bossOrder.getAgentId()));
            if(!Objects.isNull(agentConf)){
                bossCustomerInitDbSender.sendMessage(agentConf);
            }else{
                log.error("{}该订单携带的agentId数据在数据库中找不到，开户状态有错误", JSON.toJSONString(bossOrder));
            }

        }
        updateComboCount(bossOrder);
        bossCustomerService.update(new LambdaUpdateWrapper<BossCustomer>()
            .eq(BossCustomer::getAgentId, bossOrder.getAgentId())
            .set(BossCustomer::getTakeEffectTime, LocalDateTime.now()));
    }

    private void updateComboCount(BossOrder bossOrder) {
        BossCombo bossCombo = bossComboMapper.selectById(bossOrder.getComboId());
        if (BillingMode.PV.equals(bossCombo.getBillingMode())) {
            bossCustomerMapper.updateComboCountByAgentId(bossCombo.getComboPv(), bossOrder.getAgentId());
        } else {
            bossCustomerMapper.updateComboAmountByAgentId(bossOrder.getOrderAmount(), bossOrder.getAgentId());
        }
    }

    public void udpateBossOrder(BossOrder bossOrder) {
        bossOrderMapper.updateRemarkNoteById(bossOrder.getRemarkNote(), bossOrder.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelBossOrder(Long id) {
        BossOrder bossOrder = bossOrderMapper.selectById(id);
        if (bossOrder == null) {
            return;
        }
        if (Duration.between(bossOrder.getCreatedAt(), Instant.now()).toHours() >= 24) {
            throw new RestException("该订单不可取消");
        }

        switch (bossOrder.getOrderType()) {
            // 续费
            case RENEW:
                cancelRenewOrder(bossOrder);
                break;

            // 变更
            case CHANGE:
                cancelChangeOrder(bossOrder);
                break;

            // 赠量
            case HANDSEL:
                cancelHandselOrder(bossOrder);
                break;

            // 减量
            case DECREMENT:
                cancelDecrementOrder(bossOrder);
                break;

            default:
                break;
        }

        // 设置订单状态为已取消
        bossOrderMapper.updateOrderStatusById(BossOrderStatus.ALREADY_CANCEL.getId(), id);
        //订单类型为变更的时候，发送MQ去修正报表相关数据
        if (CHANGE.equals(bossOrder.getOrderType())) {
            bossOrderSender.cancelChangeOrder(bossOrder);
        }
        deleteCache(bossOrder.getAgentId());
    }

    private void cancelRenewOrder(BossOrder bossOrder) {
        BossCombo bossCombo = bossComboMapper.selectById(bossOrder.getComboId());
        if (BillingMode.PV.equals(bossCombo.getBillingMode())) {
            bossCustomerMapper.updateComboCountByAgentId(-bossCombo.getComboPv(), bossOrder.getAgentId());
        } else {
            bossCustomerMapper.updateComboAmountByAgentId(bossOrder.getOrderAmount().negate(), bossOrder.getAgentId());
        }
//        bossAdvertiserAccountGroupDayReportService.sendWarnInfo(bossOrder.getAgentId());
    }

    private void cancelChangeOrder(BossOrder bossOrder) {
        // 获取最新的套餐
        BossCombo bossCombo = bossComboMapper.selectById(bossOrder.getComboId());

        // 旧的有套餐的订单
        BossOrder oldOrder = bossOrderMapper.selectOne(new LambdaQueryWrapper<BossOrder>().eq(BossOrder::getAgentId, bossOrder.getAgentId()).ne(BossOrder::getId, bossOrder.getId())
            .ne(BossOrder::getOrderStatus, BossOrderStatus.ALREADY_CANCEL)
            .isNotNull(BossOrder::getComboId).orderByDesc(BossOrder::getCreatedAt).last("limit 1"));
        if (oldOrder == null) {
            return;
        }

        BossCombo oldBossCombo = bossComboMapper.selectById(oldOrder.getComboId());
        if (BillingMode.PV.equals(bossCombo.getBillingMode())) {
            bossCustomerMapper.updateComboCountByAgentId(-bossCombo.getComboPv(), bossOrder.getAgentId());
        } else {
            bossCustomerMapper.updateComboAmountByAgentId(bossOrder.getOrderAmount().negate(), bossOrder.getAgentId());
        }
//        bossAdvertiserAccountGroupDayReportService.sendWarnInfo(bossOrder.getAgentId());

        // 如果新套餐和老套餐的权限相同，则直接更新套餐量即可
        if (Arrays.equals(bossCombo.getPermissionIds(), oldBossCombo.getPermissionIds())) {
            return;
        }

        // 修改权限，删除权限组
        Long[] permissionIds = bossComboMapper.selectById(oldBossCombo.getId()).getPermissionIds();
        List<Permission> permissionList = permissionMapper.listByParentIds(permissionIds);
        TenantContextHolder.set(bossOrder.getAgentId());

        // 在同一个类中外层不使用事物，内层方法使用事物，则事物注解不生效，需要写在其他类中重新走代理
        bossCustomerService.resetPermission(permissionList);

        // 清除该环境的所有token,让用户退出登录
        ucenterSender.ucenterServiceClearUserLoginCache(new ChangeUserCacheDto()
            .setChangeUserCacheType(ChangeUserCacheType.FUZZY_DELETE_BY_AGENT_ID)
            .setAgentId(bossOrder.getAgentId())
        );


        // 删除userCache缓存
        ucenterSender.ucenterServiceClearUserLoginCache(new ChangeUserCacheDto()
            .setChangeUserCacheType(ChangeUserCacheType.FUZZY_DELETE_USER_SESSION_CACHE_KEY_BY_AGENT_ID)
            .setAgentId(bossOrder.getAgentId())
        );
    }

    private void cancelHandselOrder(BossOrder bossOrder) {
        if (bossOrder.getComboId() == null) {
            bossCustomerMapper.updateComboCountByAgentId(-bossOrder.getIncreasePv(), bossOrder.getAgentId());
        } else {
            BossCombo bossCombo = bossComboMapper.selectById(bossOrder.getComboId());
            if (BillingMode.PV.equals(bossCombo.getBillingMode())) {
                bossCustomerMapper.updateComboCountByAgentId(-bossOrder.getIncreasePv(), bossOrder.getAgentId());
            } else {
                bossCustomerMapper.updateComboAmountByAgentId(bossOrder.getIncreaseAmount().negate(), bossOrder.getAgentId());
            }
        }
//        bossAdvertiserAccountGroupDayReportService.sendWarnInfo(bossOrder.getAgentId());
    }

    private void cancelDecrementOrder(BossOrder bossOrder) {
        if (bossOrder.getComboId() == null) {
            bossCustomerMapper.updateComboCountByAgentId(bossOrder.getIncreasePv(), bossOrder.getAgentId());
        } else {
            BossCombo bossCombo = bossComboMapper.selectById(bossOrder.getComboId());
            if (BillingMode.PV.equals(bossCombo.getBillingMode())) {
                bossCustomerMapper.updateComboCountByAgentId(bossOrder.getIncreasePv(), bossOrder.getAgentId());
            } else {
                bossCustomerMapper.updateComboAmountByAgentId(bossOrder.getIncreaseAmount(), bossOrder.getAgentId());
            }
        }
    }

    /**
     * 获取指定agentId下的 按照日期降序排列的前多少个订单
     *
     * @param agentConf
     * @param num
     * @return
     */
    public List<BossComboAndOrderDto> getLastestOrder(String agentId, Integer num) {
        return bossOrderMapper.selectLatestComboAndOrder(agentId,num);
    }


}
