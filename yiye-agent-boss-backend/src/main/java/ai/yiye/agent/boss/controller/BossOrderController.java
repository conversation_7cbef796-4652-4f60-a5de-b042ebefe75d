package ai.yiye.agent.boss.controller;

import ai.yiye.agent.boss.service.BossComboService;
import ai.yiye.agent.boss.service.BossOrderService;
import ai.yiye.agent.boss.vo.BossConditionVo;
import ai.yiye.agent.boss.vo.BossOrderVo;
import ai.yiye.agent.domain.boss.BossCombo;
import ai.yiye.agent.domain.boss.BossOrder;
import ai.yiye.agent.domain.enumerations.BossOrderStatus;
import ai.yiye.agent.domain.enumerations.OrderType;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/16 14:37
 */
@RestController
@RequestMapping("/boss-backend/order")
public class BossOrderController {

    @Autowired
    private BossOrderService bossOrderService;
    @Autowired
    private BossComboService bossComboService;

    @GetMapping
    public IPage<BossOrderVo> getBossOrderPage(Page<BossOrder> page, BossConditionVo bossConditionVo) {
        return bossOrderService.getBossOrderPage(page, bossConditionVo);
    }

    @PostMapping
    public ResponseEntity<BossOrder> saveBossOrder(@RequestBody BossOrder bossOrder) {
        return bossOrderService.saveBossOrder(bossOrder);
    }

    @GetMapping("/get-combo")
    public List<BossCombo> getComBoByAgentId(String agentId, OrderType orderType) {
        return bossComboService.getComBoByAgentId(agentId, orderType);
    }

    @PatchMapping("/update")
    public void udpateBossOrder(@RequestBody BossOrder bossOrder) {
        bossOrderService.udpateBossOrder(bossOrder);
    }

    @PatchMapping("/cancel")
    public void cancelBossOrder(@RequestParam("id") Long id) {
        bossOrderService.cancelBossOrder(id);
    }

}

