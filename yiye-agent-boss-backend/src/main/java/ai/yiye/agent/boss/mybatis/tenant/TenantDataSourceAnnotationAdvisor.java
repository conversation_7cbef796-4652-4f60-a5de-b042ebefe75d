package ai.yiye.agent.boss.mybatis.tenant;

import com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor;
import com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationInterceptor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.Pointcut;
import org.springframework.aop.support.annotation.AnnotationMatchingPointcut;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
public class TenantDataSourceAnnotationAdvisor extends DynamicDataSourceAnnotationAdvisor {

    private static final Pointcut POINTCUT = AnnotationMatchingPointcut.forClassAnnotation(Service.class);

    public TenantDataSourceAnnotationAdvisor(@NonNull DynamicDataSourceAnnotationInterceptor dynamicDataSourceAnnotationInterceptor) {
        super(dynamicDataSourceAnnotationInterceptor);
        log.info("------>>注册Mybatis数据源切换切面<<------");
    }

    @Override
    public Pointcut getPointcut() {
        return POINTCUT;
    }
}
