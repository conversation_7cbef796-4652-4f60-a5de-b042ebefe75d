package ai.yiye.agent.boss.jwt;

import ai.yiye.agent.boss.constants.SessionConstants;
import ai.yiye.agent.boss.service.BossUserService;
import ai.yiye.agent.boss.web.exception.LoginTimeOutException;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.boss.BossUser;
import ai.yiye.agent.domain.enumerations.UserTokenKeyType;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class JwtTokenProvider implements TokenProvider {

    @Autowired
    private JwtProperties jwtProperties;

    @Autowired
    private BossUserService bossUserService;

    @Autowired
    private RedisTemplate<String, Object> defaultObjectRedisTemplate;

    @Override
    public Authentication getAuthentication(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(jwtProperties.getKey())
                .build()
                .parseClaimsJws(token)
                .getBody();

        Long uid = claims.get(USER_ID_KEY, Long.class);
        String userTokenType = claims.get(USER_TOKEN_TYPE, String.class);
        UserTokenKeyType userTokenKeyType = UserTokenKeyType.valueOf(userTokenType);
        if (Objects.nonNull(userTokenKeyType) && !UserTokenKeyType.SERVICE_REQUEST_BOSS.equals(userTokenKeyType)){
            String accessToken = claims.get(ACCESS_TOKEN, String.class);
            //判断redis中是否存在，不存在则报登录超时
            String key = SessionConstants.USER_SESSION_CACHE_TOKEN_KEY + ":" + uid + ":" + accessToken;
            Boolean hasKey = defaultObjectRedisTemplate.hasKey(key);
            if (!hasKey){
                throw new LoginTimeOutException();
            }
        }
        BossUser user = bossUserService.getById(uid);
        // 如果从请求头中未获取到,则再次尝试从token中获取
        if (StringUtils.isBlank(TenantContextHolder.get())) {
            String agentId = claims.get(AGENT_ID_KEY, String.class);
            TenantContextHolder.set(agentId);
        }

        return new UsernamePasswordAuthenticationToken(user, token, user.getAuthorities());
    }

    @Override
    public boolean validateToken(String authToken) {
        try {
            Jwts.parserBuilder().setSigningKey(jwtProperties.getKey()).build()
                    .parseClaimsJws(authToken);
            return true;
        } catch (Exception e) {
            log.debug("Invalid JWT token: {}. {}", authToken, e);
            return false;
        }
    }

}
