package ai.yiye.agent.boss.service;

import ai.yiye.agent.boss.mapper.BossAdvertiserAccountFundDayReportMapper;
import ai.yiye.agent.domain.BossAdvertiserAccountFundDayReport;
import ai.yiye.agent.domain.dto.AgentDateFund;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/7/20 20:30
 */
@Service
public class BossAccountFundDayReportService extends ServiceImpl<BossAdvertiserAccountFundDayReportMapper, BossAdvertiserAccountFundDayReport> {
    @Autowired
    private BossAdvertiserAccountFundDayReportMapper mapper;
    public void insertOrUpDateByTimeAndId(AgentDateFund agentDateFund) {
        mapper.insertOrUpDateByTimeAndId(agentDateFund);

    }
}
