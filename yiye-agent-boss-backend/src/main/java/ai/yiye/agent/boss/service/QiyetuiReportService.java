package ai.yiye.agent.boss.service;

import ai.yiye.agent.domain.boss.BossAgentQiyetuiPvDayReport;
import ai.yiye.agent.domain.constants.DbConstants;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/3/2 15:01
 */

public interface QiyetuiReportService {
	@DS(DbConstants.READONLY_PG_POOL_NAME)
	public List<BossAgentQiyetuiPvDayReport> selectQiyetuiReport(String agentId, Instant now, Instant nextDay) ;

	/**
	 * clickhouse 2.0统计
	 * @param agentId
	 * @param now
	 * @param nextDay
	 * @return
	 */
	@DS("clickhouse")
	public List<BossAgentQiyetuiPvDayReport> selectQiyetuiReportNew(String agentId, Instant now, Instant nextDay) ;

	@DS(DbConstants.READONLY_PG_POOL_NAME)
	public List<BossAgentQiyetuiPvDayReport> selectQiyetuiSchemeReport(String agentId, Instant now, Instant nextDay) ;

}
