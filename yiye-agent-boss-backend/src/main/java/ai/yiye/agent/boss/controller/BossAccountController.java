package ai.yiye.agent.boss.controller;

import ai.yiye.agent.boss.service.BossAccountService;
import ai.yiye.agent.boss.service.BossAdvertiserAccountGroupDayReportService;
import ai.yiye.agent.boss.service.BossComboService;
import ai.yiye.agent.boss.service.BossOrderService;
import ai.yiye.agent.boss.vo.AccountBillingVo;
import ai.yiye.agent.boss.vo.BillingAccountsVo;
import ai.yiye.agent.boss.vo.BillingConditionAccountVo;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.boss.BossCombo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/7/18 21:32
 */
@RestController
@RequestMapping("/boss-backend/account/billing-account")
public class BossAccountController {
    @Autowired
    private BossComboService bossComboService;
    @Autowired
    private BossAccountService bossAccountService;
    @Autowired
    private BossAdvertiserAccountGroupDayReportService bossAdvertiserAccountGroupDayReportService;
    @Resource
    private BossOrderService bossOrderService;

    /**
     * 账户计费列表
     *
     * @param page
     * @param billingConditionAccountVo
     * @return
     */
    @PostMapping("list")
    public IPage<BillingAccountsVo> billAccounts(Page<BillingAccountsVo> page, @RequestBody BillingConditionAccountVo billingConditionAccountVo) {
        TenantContextHolder.clearContext();
        BossCombo comBo = bossComboService.getById(billingConditionAccountVo.getComboId());
        TenantContextHolder.set(billingConditionAccountVo.getAgentId());
        if(StringUtils.isEmpty(billingConditionAccountVo.getStartStr())){
            billingConditionAccountVo.setStartTime(LocalDate.now().atStartOfDay().toInstant(ZoneOffset.of("+8")));
            billingConditionAccountVo.setEndTime(Instant.now());
        }else{
            billingConditionAccountVo.setStartTime(new Date(billingConditionAccountVo.getStartStr()).toInstant());
            billingConditionAccountVo.setEndTime(new Date(billingConditionAccountVo.getEndStr()).toInstant());
        }
        IPage<BillingAccountsVo> billingAccountsVoIPage = bossAccountService.billingAccounts(page, billingConditionAccountVo, comBo);
        TenantContextHolder.clearContext();
        return billingAccountsVoIPage;
    }

    @PostMapping("list/total")
    public BillingAccountsVo billAccountsTotal(@RequestBody BillingConditionAccountVo billingConditionAccountVo) {
        BigDecimal billingProportion = bossComboService.getCurrentBillingProportion(billingConditionAccountVo.getAgentId());
        TenantContextHolder.set(billingConditionAccountVo.getAgentId());
        if(StringUtils.isEmpty(billingConditionAccountVo.getStartStr())){
            billingConditionAccountVo.setStartTime(LocalDate.now().atStartOfDay().toInstant(ZoneOffset.of("+8")));
            billingConditionAccountVo.setEndTime(Instant.now());
        }else{
            billingConditionAccountVo.setStartTime(new Date(billingConditionAccountVo.getStartStr()).toInstant());
            billingConditionAccountVo.setEndTime(new Date(billingConditionAccountVo.getEndStr()).toInstant());
        }
        BillingAccountsVo billingAccountsVoIPage = bossAccountService.billingAccountsTotal(billingConditionAccountVo);
        billingAccountsVoIPage.setBillingProportion(billingProportion);
        TenantContextHolder.clearContext();
        return billingAccountsVoIPage;
    }

    /**
     * 修改账户计费状态
     *
     * @param accountBillingVo
     * @return
     */
    @PostMapping("/billing/update")
    public String startBilling(@RequestBody AccountBillingVo accountBillingVo) {
        TenantContextHolder.set(accountBillingVo.getAgentId());
        if(StringUtils.isEmpty(accountBillingVo.getBillingTimeStr())){
            accountBillingVo.setBillingTime(LocalDate.now().atStartOfDay().toInstant(ZoneOffset.of("+8")));
        }else{
            accountBillingVo.setBillingTime(new Date(accountBillingVo.getBillingTimeStr()).toInstant());
        }

        String s = bossAccountService.updateBillingStatus(accountBillingVo);
        TenantContextHolder.clearContext();
        return s;
    }

    /**
     * 导出计费账户列表
     */
    @GetMapping(path = "/export", produces = "application/vnd.ms-excel")
    public void export(BillingConditionAccountVo billingConditionAccountVo, HttpServletResponse response) {
        TenantContextHolder.clearContext();
        BossCombo comBo = bossComboService.getById(billingConditionAccountVo.getComboId());
        TenantContextHolder.set(billingConditionAccountVo.getAgentId());
        billingConditionAccountVo.setBillingProportion(comBo.getBillingProportion());

        if(StringUtils.isEmpty(billingConditionAccountVo.getStartStr())){
            billingConditionAccountVo.setStartTime(LocalDate.now().atStartOfDay().toInstant(ZoneOffset.of("+8")));
            billingConditionAccountVo.setEndTime(Instant.now());
        }else{
            billingConditionAccountVo.setStartTime(new Date(billingConditionAccountVo.getStartStr()).toInstant());
            billingConditionAccountVo.setEndTime(new Date(billingConditionAccountVo.getEndStr()).toInstant());
        }
        bossAccountService.export(billingConditionAccountVo, response);
        TenantContextHolder.clearContext();
    }

    /**
     *
     */
    @GetMapping(path = "test/{agentId}")
    public void testSendWarnInfo(@PathVariable("agentId") String agentId) {
        bossAdvertiserAccountGroupDayReportService.sendWarnInfo(agentId);
        TenantContextHolder.clearContext();
    }



    public static void main(String[] args) throws ParseException {
        System.out.println(Instant.now().truncatedTo(ChronoUnit.DAYS));
//        Date date = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").parse(dateStr);
//
//        System.out.println(date.toInstant());
    }
}
