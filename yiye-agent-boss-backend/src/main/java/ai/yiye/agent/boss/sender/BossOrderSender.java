package ai.yiye.agent.boss.sender;

import ai.yiye.agent.boss.config.consts.BossExchangeConst;
import ai.yiye.agent.domain.boss.BossOrder;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class BossOrderSender {

    @Resource
    private StreamBridge streamBridge;

    public void changeOrder(BossOrder bossOrder) {
        log.info("新增变更订单-bossOrder:{}", JSONObject.toJSONString(bossOrder));
        streamBridge.send(BossExchangeConst.BOSS_ORDER_CHANGE_EXCHANGE, bossOrder);
    }

    public void cancelChangeOrder(BossOrder bossOrder) {
        log.info("取消变更订单-bossOrder:{}", JSONObject.toJSONString(bossOrder));
        streamBridge.send(BossExchangeConst.BOSS_ORDER_CANCEL_CHANGE_EXCHANGE, bossOrder);
    }

}
