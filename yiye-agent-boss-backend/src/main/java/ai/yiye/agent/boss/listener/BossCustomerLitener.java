package ai.yiye.agent.boss.listener;

/**
 * <AUTHOR>
 * @date 2021/11/25 19:25
 */

import ai.yiye.agent.boss.mapper.BossComboMapper;
import ai.yiye.agent.boss.mapper.PermissionMapper;
import ai.yiye.agent.boss.mybatis.multidatasource.service.AgentConfService;
import ai.yiye.agent.boss.service.BossCustomerService;
import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.domain.AgentConf;
import ai.yiye.agent.domain.Permission;
import ai.yiye.agent.domain.boss.BossCustomer;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Consumer;

@Service("BossCustomerInitEnvListener")
public class BossCustomerLitener implements Consumer<AgentConf> {

    @Autowired
    private BossCustomerService bossCustomerService;

    @Autowired
    private BossComboMapper bossComboMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private AgentConfService agentConfService;

    @Override
    public void accept(AgentConf agentConf) {
        String agentId = agentConf.getAgentId();
        BossCustomer bossCustomer = bossCustomerService.getOne(new LambdaQueryWrapper<BossCustomer>().eq(BossCustomer::getAgentId, agentId));
        Long[] permissionIds = bossComboMapper.getLastComboByAgentID(agentId).getPermissionIds();
        List<Permission> permissionList = permissionMapper.listByParentIds(permissionIds);
        TenantContextHolder.set(agentId);
        bossCustomerService.initEnv(permissionList, bossCustomer);
        TenantContextHolder.clearContext();

        // 将public库中的agent_conf表对应的账户状态修改为正常。
        agentConfService.updateStatusByAgentId(agentId);
    }
}
