package ai.yiye.agent.boss.test;

import ai.yiye.agent.boss.YiYeAgentBossBackendApplication;
import ai.yiye.agent.boss.domain.BossCombo;
import ai.yiye.agent.boss.service.BossComboService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/11/16 11:09
 */
@SpringBootTest(classes = YiYeAgentBossBackendApplication.class)
public class BossComboTest {
    @Autowired
    private BossComboService bossComboService;

    @Test
    public void comboTest01(){
        BossCombo combo = new BossCombo().setComboPv(100L).setComboName("测试001").setComboPrice(BigDecimal.valueOf(100.0));
        //bossComboService.saveBossCombo(combo);
    }
}
