<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>yiye-agent-backend</artifactId>
        <groupId>ai.yiye.agent</groupId>
        <version>1.313.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yiye-agent-boss-backend</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>ai.yiye.agent</groupId>
            <artifactId>yiye-agent-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>ai.yiye.agent</groupId>
            <artifactId>yiye-agent-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-captcha</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>ru.yandex.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>ai.yiye.agent</groupId>
            <artifactId>yiye-agent-weixin-sdk</artifactId>
        </dependency>
        <!-- 七牛云java-sdk -->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <version>${qiniu.version}</version>
        </dependency>
    </dependencies>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <executable>true</executable>
                </configuration>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>${git-commit-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <failOnNoGitDirectory>false</failOnNoGitDirectory>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-resources</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/classes</outputDirectory>
                            <useDefaultDelimiters>false</useDefaultDelimiters>
                            <delimiters>
                                <delimiter>#</delimiter>
                            </delimiters>
                            <resources>
                                <resource>
                                    <directory>src/main/resources/</directory>
                                    <filtering>true</filtering>
                                    <includes>
                                        <include>*.yml</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <directory>src/main/resources/</directory>
                                    <filtering>false</filtering>
                                    <excludes>
                                        <exclude>*.yml</exclude>
                                    </excludes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>docker-resources</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/docker/</directory>
                                    <filtering>false</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>${dockerfile-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>docker</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>build</goal>
                            <goal>tag</goal>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <repository>${docker.repository}</repository>
                    <tag>${docker.tag}</tag>
                    <contextDirectory>${project.build.directory}</contextDirectory>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>szsaas</id>
            <properties>
                <spring.profiles.active>szsaas</spring.profiles.active>
                <docker.repository>ccr.ccs.tencentyun.com/yiye-agent/${project.artifactId}</docker.repository>
                <docker.tag>szsaas</docker.tag>
            </properties>
        </profile>
        <profile>
            <id>aspsaas</id>
            <properties>
                <spring.profiles.active>aspsaas</spring.profiles.active>
                <maven.test.skip>true</maven.test.skip>
                <docker.repository>registry.cn-qingdao.aliyuncs.com/yiye-agent/${project.artifactId}</docker.repository>
                <docker.tag>aspsaas-${project.version}</docker.tag>
            </properties>
        </profile>
            <profile>
                <id>dev</id>
                <activation>
                    <activeByDefault>true</activeByDefault>
                </activation>
                <properties>
                    <spring.profiles.active>dev</spring.profiles.active>
                    <docker.repository>ccr.ccs.tencentyun.com/yiye-agent/${project.artifactId}</docker.repository>
                    <docker.tag>dev</docker.tag>
                </properties>
            </profile>
            <profile>
                <id>test</id>
                <properties>
                    <spring.profiles.active>test</spring.profiles.active>
                    <docker.repository>ccr.ccs.tencentyun.com/yiye-agent/${project.artifactId}</docker.repository>
                    <docker.tag>test</docker.tag>
                </properties>
            </profile>
            <profile>
                <id>prod</id>
                <properties>
                    <spring.profiles.active>prod</spring.profiles.active>
                    <maven.test.skip>true</maven.test.skip>
                    <docker.repository>registry.cn-qingdao.aliyuncs.com/yiye-agent/${project.artifactId}</docker.repository>
                    <docker.tag>${project.version}</docker.tag>
                </properties>
            </profile>
    </profiles>
</project>
