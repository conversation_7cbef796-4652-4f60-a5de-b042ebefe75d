package ai.yiye.agent.domain.enumerations;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum WechatGroupChatCallbackType {

    ADD_MEMBER(0, "成员入群", UpdateDetail.ADD_MEMBER),
    DEL_MEMBER(1, "成员退群", UpdateDetail.DEL_MEMBER),
    CHANGE_OWNER(2, "群主变更", UpdateDetail.CHANGE_OWNER),
    CHANGE_NAME(3, "群名变更", UpdateDetail.CHANGE_NAME),
    DISMISS(4, "群解散", null),
    ;

    private Integer code;

    private String name;

    private UpdateDetail updateDetail;

    public static WechatGroupChatCallbackType getEnumByUpdateDetail(UpdateDetail updateDetail) {
        for (WechatGroupChatCallbackType entity : WechatGroupChatCallbackType.values()) {
            if (Objects.equals(entity.getUpdateDetail(), updateDetail)) {
                return entity;
            }
        }
        return null;
    }

}
