package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 企微客服敏感信息 - 表
 */
@Data
@TableName("work_wechat_customer_sensitive_info")
public class WorkWechatCustomerSensitiveInfo implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业微信id，关联表字段：enterprise_wechats.corpid
     */
    private String corpId;

    /**
     * 对应企业成员账号（userid）
     */
    private String userId;

    /**
     * 手机号
     */
    private String wechatMobile;

    /**
     * 邮箱
     */
    private String wechatEmail;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
