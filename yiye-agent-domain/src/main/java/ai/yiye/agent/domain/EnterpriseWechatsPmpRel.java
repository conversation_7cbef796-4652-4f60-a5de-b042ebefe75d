package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.EnterpriseWechatType;
import ai.yiye.agent.domain.enumerations.YesOrNoEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/1/4 11:29
 */

@TableName("enterprise_wechats_pmp_rel")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnterpriseWechatsPmpRel {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("corpid")
    private String corpid;

    @TableField("enterprise_wechats_id")
    private Long enterpriseWechatsId;
    @TableField("advertiser_account_group_id")
    private Long advertiserAccountGroupId;
    @TableField("agent_id")
    private String agentId;
    @TableField("create_at")
    private LocalDateTime createAt;
    //是否是被分享的
    private YesOrNoEnum shared;
    //如果是被分享的，这边要有一个转增来的字符串
    private String sharedText;
    /**
     * 企业微信类型
     * SERVICE_PROVIDER("服务商"),
     * BUILD_BY_ONESELF("自建应用"),
     * GENERATION_DEVELOPMENT("代开发应用");
     */
    private EnterpriseWechatType enterpriseWechatType;

}
