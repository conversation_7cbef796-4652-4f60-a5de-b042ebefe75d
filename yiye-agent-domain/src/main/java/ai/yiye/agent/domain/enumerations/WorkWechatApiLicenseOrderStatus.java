package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WorkWechatApiLicenseOrderStatus implements IEnum<Integer> {

    // 订单状态 0：待支付，1：已支付，2：已取消（未支付，订单已关闭）3：未支付，订单已过期，4：申请退款中，5：退款成功，6：退款被拒绝，7：订单已失效
    PENDING_PAYMENT(0),
    PAID(1),
    CANCELLED(2),
    NOT_PAID_EXPIRED(3),
    REFUND_APPLYING(4),
    REFUND_SUCCESS(5),
    REFUND_REJECTED(6),
    INVALID(7),
    ;
    private final Integer value;

    @Override
    public Integer getValue() {
        return value;
    }

    public static WorkWechatApiLicenseOrderStatus fromValue(Integer value) {
        for (WorkWechatApiLicenseOrderStatus status : WorkWechatApiLicenseOrderStatus.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}
