package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2022/2/22 19:22
 */
public enum LandingPageContentEnum{

    /**
     * 落地页内容为null
     */
    WITHOUT_CONTENT(0, "without_content"),

    /**
     * 落地页内容为[]
     */
    EMPTY_ARRAY_CONTENT(1, "empty_array_content"),

    /**
     * 有落地页内容
     */
    WITH_CONTENT(2, "with_content");

    @EnumValue
    private final int id;

    private final String name;

    LandingPageContentEnum (int id, String name) {
        this.id = id;
        this.name = name;
    }
}
