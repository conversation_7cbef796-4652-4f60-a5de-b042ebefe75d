package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AdvertiserAccountSystemStatus;
import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 巨量引擎极速下载应用
 */
@Data
@TableName("marketing_data_ocean_engine_cdn_app")
public class OceanEngineCdnApp extends AbstractMarketingData {

    @TableField(exist = false)
    public static final String[] CONFLICTS = new String[]{"account_id", "cdn_app_id"};
    @TableField(exist = false)
    protected JSONObject ext;
    @TableId(
        type = IdType.AUTO
    )
    private Long id;
    /**
     * 极速下载包ID
     */
    private Long cdnAppId;
    /**
     * CDN下载地址
     */
    private String cdnUrl;
    /**
     * 下载包地址
     */
    private String tosUrl;
    /**
     * 应用名
     */
    private String name;
    /**
     * 包名
     */
    private String packageName;
    /**
     * 下载包大小
     */
    private String size;
    /**
     * 版本号
     */
    private String version;
    /**
     * 渠道号
     */
    private String channelCode;
    @TableField(
        fill = FieldFill.INSERT
    )
    private Instant createdAt;
    @TableField(
        fill = FieldFill.INSERT_UPDATE
    )
    private Instant updatedAt;
    @TableField(exist = false)
    private Long optimizerId;
    @TableField(exist = false)
    private AdvertiserAccountSystemStatus advertiserAccountStatus;
}
