package ai.yiye.agent.domain;

import ai.yiye.agent.domain.subtablerules.SubTableRules;
import ai.yiye.agent.domain.vo.AdvertiserAccountGroupReport;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 后链路数据固化实体类
 * <AUTHOR>
 * @date 2021/5/10 17:52
 */
@Data
@TableName(value = "marketing_data_solidification")
@SubTableRules(subField = "dataTime")
public class DataSolidification extends AdvertiserAccountGroupReport implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 广告计划ID
     */
    private Long campaignId;

    /**
     * 广告组ID
     */
    private Long adgroupId;

    /**
     * 广告创意ID
     */
    private Long creativeId;

    /**
     * 广告ID(广点通接口冗余)
     */
    private Long adId;

    /**
     * 定向包ID
     */
    private Long targetingId;

    /**
     * 投放平台账号ID
     */
    protected String accountId;

    /**
     * 一叶平台投放账户ID
     */
    protected Long advertiserAccountId;

    /**
     * 数据所属平台ID
     */
    protected Integer platformId;

    /**
     * 主优化师id
     */
    protected Long optimizerId;

    /**
     * 日期
     */
    private Instant dayTime;

    /**
     * 日期
     */
    private Instant dataTime;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
