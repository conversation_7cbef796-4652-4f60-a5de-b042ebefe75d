package ai.yiye.agent.domain;

import ai.yiye.agent.domain.dto.WechatCustomerServiceGroupEnterpriseWechatDto;
import ai.yiye.agent.domain.enumerations.CompareLogFieldEnum;
import ai.yiye.agent.domain.enumerations.YesOrNoEnum;
import ai.yiye.agent.domain.utils.Compare;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;

/**
 * 落地页 - 微信客服管理 - 分组 - 表
 */
@Data
@TableName("landing_page_wechat_customer_service_group")
public class LandingPageWechatCustomerServiceGroup {

    @TableId(type = IdType.AUTO)
    private Long id;

    @NotNull(message = "分组名称为必填项")
    @Compare(compareField=CompareLogFieldEnum.CUSTOMER_SERVICE_GROUP_NAME_CHANGE)
    private String name;

    /**
     * pmp项目id，关联表：marketing_advertiser_account_group.id
     */
    @NotNull(message = "PMP项目分组id为必填项")
    private Long advertiserAccountGroupId;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    @TableField(exist = false)
    private List<WechatCustomerServiceGroupEnterpriseWechatDto> enterpriseWechatList;

    /**
     * 分组内是否有生成微信客服机器人动态渠道二维码的标识
     */
    @TableField(exist = false)
    private YesOrNoEnum generateRobotDynamicQrCodeFlag = YesOrNoEnum.NO;

}
