package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AbnormalMonitorTimingType;
import ai.yiye.agent.domain.enumerations.AbnormalMonitorType;
import ai.yiye.agent.domain.enumerations.SwitchStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 落地页 - 微信客服管理 - 异常监测
 */
@Data
@TableName("landing_page_wechat_customer_service_abnormal_monitor")
public class LandingPageWechatCustomerServiceAbnormalMonitor {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * pmp项目id，关联表：marketing_advertiser_account_group.id
     */
    private Long advertiserAccountGroupId;

    /**
     * 客服id，关联表：landing_page_wechat_customer_service.id
     */
    private Long landingPageWechatCustomerServiceId;

    /**
     * 监测方式
     */
    private AbnormalMonitorType type;

    /**
     * 计时设置
     */
    private AbnormalMonitorTimingType timingType;

    /**
     * 检查周期
     */
    private Integer inspectionCycle;

    /**
     * 二维码长按配置次数
     */
    private Integer identifyQrcodeConfigCount;

    /**
     * 公众号内发码配置次数
     */
    private Integer officialSendCodeConfigCount;

    /**
     * 微信客服机器人内发码配置次数
     */
    private Integer robotSendCodeConfigCount;

    /**
     * 每天成功添加企业微信状态 0:不开启 1:开启
     */
    private SwitchStatus addEnterpriseWechatStatus;

    /**
     * 每天成功添加企业微信配置次数
     */
    private Integer addEnterpriseWechatConfigCount;

    /**
     * 获客助手链接被发起好友添加请求次数
     */
    private Integer customerAcquisitionFriendRequestCount;

    /**
     * 获客链接被点击次数
     */
    private Integer customerAcquisitionClickCount;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    public static String getCompareString(LandingPageWechatCustomerServiceAbnormalMonitor abnormalMonitor) {
        if (AbnormalMonitorType.SET_TIME.equals(abnormalMonitor.getType())) {
            if (AbnormalMonitorTimingType.TAKE_EFFECT_TIME.equals(abnormalMonitor.getTimingType())) {
                return "客服上线后按设定时间每" + getTimeStr(abnormalMonitor.getInspectionCycle()) + "进行监测，在设定时间内无进粉（没有外部联系人成功添加）自动下线";
            } else if (AbnormalMonitorTimingType.IDENTIFY_TIME.equals(abnormalMonitor.getTimingType())) {
                return "客服上线后二维码被首次长按后按设定时间每" + getTimeStr(abnormalMonitor.getInspectionCycle()) + "进行监测，在设定时间内无进粉（没有外部联系人成功添加）自动下线";
            }
        } else if (AbnormalMonitorType.IDENTIFY_COUNT.equals(abnormalMonitor.getType())) {
            return "客服二维码被长按" + abnormalMonitor.getIdentifyQrcodeConfigCount() + "次且最后一次长按后" + getTimeStr(abnormalMonitor.getInspectionCycle()) + "内无进粉（没有外部联系人成功添加）自动下线";
        } else if (AbnormalMonitorType.OFFICIAL_SEND_CODE_COUNT.equals(abnormalMonitor.getType())) {
            String s = "客服动态渠道二维码（公众号内加粉）在公众号内发码" + abnormalMonitor.getOfficialSendCodeConfigCount() + "次且最后一次发码后" + getTimeStr(abnormalMonitor.getInspectionCycle()) + "内无进粉（没有外部联系人成功添加）自动下线";
            if (SwitchStatus.OPEN.equals(abnormalMonitor.getAddEnterpriseWechatStatus()) && abnormalMonitor.getAddEnterpriseWechatConfigCount() != null) {
                s = s + "（每天成功添加企业微信" + abnormalMonitor.getAddEnterpriseWechatConfigCount() + "次后执行异常监测）";
            }
            return s;
        } else if (AbnormalMonitorType.ROBOT_SEND_CODE_COUNT.equals(abnormalMonitor.getType())) {
            return "渠道二维码（微信客服机器人内加粉）发码" + abnormalMonitor.getRobotSendCodeConfigCount() + "次且最后一次发码后" + getTimeStr(abnormalMonitor.getInspectionCycle()) + "内无进粉（没有外部联系人成功添加）自动下线";
        } else if (AbnormalMonitorType.CUSTOMER_ACQUISITION_FRIEND_REQUEST_COUNT.equals(abnormalMonitor.getType())) {
            return "客服获客助手链接被发起好友添加请求" + abnormalMonitor.getCustomerAcquisitionFriendRequestCount() + "次，且最后一次点击添加申请后" + abnormalMonitor.getInspectionCycle() + "秒内无进粉（没有外部联系人成功添加）自动下线";
        } else if (AbnormalMonitorType.CUSTOMER_ACQUISITION_CLICK_COUNT.equals(abnormalMonitor.getType())) {
            return "客服获客助手链接被点击" + abnormalMonitor.getCustomerAcquisitionFriendRequestCount() + "次，且最后一次点击后" + abnormalMonitor.getInspectionCycle() + "秒内无进粉（没有外部联系人成功添加）自动下线";
        }
        return "";
    }

    private static String getTimeStr(Integer inspectionCycle) {
        if (inspectionCycle == null) {
            return "";
        }
        if (inspectionCycle < 60) {
            return inspectionCycle + "分钟";
        }
        return (int) Math.floor(inspectionCycle / 60) + "小时";
    }

    public static void main(String[] args) {
        System.out.println(getTimeStr(10));
        System.out.println(getTimeStr(55));
        System.out.println(getTimeStr(60));
        System.out.println(getTimeStr(120));
        System.out.println(getTimeStr(180));
    }

}

