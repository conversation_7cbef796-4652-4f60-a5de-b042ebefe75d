package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.core.enums.IEnum;

/**
 * <AUTHOR>
 * 操作层级
 * @date 2023/4/7 10:27
 */
public enum UserOperationLogDetailActionLevel  implements IEnum<Integer> {
    /**
     *微信客服层级
     */
    CUSTOMER_SERVICE("CUSTOMER_SERVICE"),
    LANDING_PAGE("LANDING_PAGE"),
    AUTH("AUTH"),
    ROBOT_CUSTOMER("ROBOT_CUSTOMER"),
    ;


    private final String name;

    UserOperationLogDetailActionLevel(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    @Override
    public Integer getValue() {
        return ordinal();
    }


    public static UserOperationLogDetailActionLevel getLevelByName(String name) {
        for (UserOperationLogDetailActionLevel platformFree : UserOperationLogDetailActionLevel.values()) {
            if (platformFree.getName().equals(name)) {
                return platformFree;
            }
        }
        return null;
    }


}
