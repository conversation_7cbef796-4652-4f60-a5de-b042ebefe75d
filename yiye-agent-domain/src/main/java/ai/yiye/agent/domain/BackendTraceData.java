package ai.yiye.agent.domain;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 后链路数据表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/23 17:48
 */
@TableName("marketing_data_backend_trace")
@Data
public class BackendTraceData {

    /**
     * id主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所有请求参数
     */
    protected JSONObject ext;

    /**
     * 客资id，来自表：customer.id
     */
    private Long customerId;

    /**
     * 填单id，来自表：submit_data.id
     */
    private Long submitDataId;

    /**
     * 后链路字段
     */
    private String filed;

    /**
     * 后链路字段名称
     */
    private String filedName;

    /**
     * 后链路字段属性类型
     */
    private Integer filedType;

    /**
     * 后链路字段属性类型名称
     */
    private String filedTypeName;


    /**
     * ********************
     */
    @TableField(exist = false)
    private String duplicateId = customerId + filed;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
