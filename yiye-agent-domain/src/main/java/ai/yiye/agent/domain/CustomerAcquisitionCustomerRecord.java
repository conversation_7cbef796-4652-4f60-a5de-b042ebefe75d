package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 获客链接加粉的客户记录表
 * @Author：lilidong
 * @Date：2023/8/16 10:15
 */
@Data
@TableName("customer_acquisition_customer_record")
public class CustomerAcquisitionCustomerRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户标识
     */
    private String agentId;

    /**
     *企业微信的corpId
     */
    private String corpId;

    /**
     * 外部联系人userId
     */
    private String externalUserId;

    /**
     * 客服的userId
     */
    private String userId;


    /**
     * 是否开口,1-是；0-否
     */
    private String chatStatus;

    /**
     * 一叶生成的state参数
     */
    private String state;


    /**
     * 获客链接ID
     */
    private String linkId;

    /**
     * 获客链接
     */

    private String link;

    /**
     * 接口返回的下一页标识
     */
    private String nextCursor;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
