package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AccessWay;
import ai.yiye.agent.domain.enumerations.AdvertiserAccountSystemStatus;
import ai.yiye.agent.domain.enumerations.Usages;
import ai.yiye.agent.domain.enumerations.UserActionSetType;
import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * 用户数据源表
 * @version 1.0
 * @date 2021/11/12 10:43
 */
@Data
@TableName("marketing_data_user_action_set")
public class MarketingUserActionSet extends AbstractMarketingData {

    @TableField(exist = false)
    public static final String[] CONFLICTS = new String[]{"account_id", "user_action_set_id", "platform_id"};

    protected JSONObject ext;

    @TableId(
        type = IdType.AUTO
    )
    private Long id;

    /**
     * 用户行为源名称，当 type=WEB 时必填，当 type=ANDROID 或 IOS 时，若未填写该字段，则默认通过 mobile_app_id 获取名称
     */
    private String userActionSetName;

    /**
     * 用户行为源 id，通过 [user_action_sets 接口] 创建用户行为源时分配的唯一 id。
     * 请注意，当填写的用户行为数据源类型为 {WECHAT, WECHAT_MINI_PROGRAM, WECHAT_MINI_GAME} 时，必填 user_id 字段中的 wechat_openid (或 wechat_unionid) 及 wechat_app_id。
     */
    private String userActionSetId;

    /**
     * 用户行为源类型
     */
    private UserActionSetType type;

    // ---------------------------------------------------------------------------------------------------------------------------------------
    /**
     * 数据源来源
     */
    private AccessWay accessWay;

    /**
     * 数据接入状态，true 表示已接入，false 表示未接入
     */
    private Boolean activateStatus;

    /**
     * 	用户行为源描述
     */
    private String description;

    /**
     * 是否开启转化归因，true 表示开启，false 表示不开启，不传则默认开启
     */
    private Boolean enableConversionClaim;

    /**
     * 应用 id，IOS：App Store id ； ANDROID：应用宝 id，type=ANDROID 或 IOS 时必填
     */
    private String mobileAppId;

    /**
     * 接入用途类型（行为数据源用途）
     */
    private Integer[] usages;

    @TableField(exist = false)
    private List<Usages> usagesList;

    /**
     * 创建时间（平台的创建时间）
     */
    private String createdTime;

    /**
     * 投放账户状态
     */
    @TableField(exist = false)
    private AdvertiserAccountSystemStatus advertiserAccountStatus;

}

