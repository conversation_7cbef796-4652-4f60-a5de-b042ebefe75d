package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.MediaUploadType;
import ai.yiye.agent.domain.enumerations.OceanEngineUploadType;
import ai.yiye.agent.domain.enumerations.Platform;
import ai.yiye.agent.domain.enumerations.PromotionType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
/**
 * @ClassName : 媒体上报行为配置表
 * <AUTHOR> <PERSON><PERSON>i<PERSON>uan
 * @Date: 2021/4/25 16:26
 */
@Data
@TableName("action_type_config")
public class ActionTypeConfig {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 行为code(枚举落库使用)
     */
    private String code;

    /**
     * 行为value(前端显示使用)
     */
    private String value;

    /**
     * 媒体
     */
    private Platform platformId;

    /**
     * 推广类型
     */
    private PromotionType promotionType;

    /**
     * 描述
     */
    private String remarks;

    /**
     * 巨量引擎 - 上报方式（仅限巨量引擎：不建议使用）
     */
    private OceanEngineUploadType oceanEngineUploadType;

    /**
     * 媒体 - 上报方式（全平台通用：建议使用）
     */
    private MediaUploadType mediaUploadType;

}
