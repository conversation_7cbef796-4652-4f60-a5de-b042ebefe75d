package ai.yiye.agent.domain;

import ai.yiye.agent.domain.typehandlers.TextArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.ArrayTypeHandler;

import java.io.Serializable;
import java.time.Instant;

/**
 * 微信机器人客服 - 回复消息模板 - 表
 */
@TableName("wechat_robot_customer_automatic_reply")
@Data
@NoArgsConstructor
public class WechatRobotCustomerAutomaticReply implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 触发条件：0-任意内容；1-指定关键词；2-手机号码
     */
    private Integer conditionType;

    /**
     * 关键词集合：condition_type=1，取此字段值做校验
     */
    @TableField(typeHandler = TextArrayTypeHandler.class)
    private String[] keyWords;

    /**
     * 手机号码集合：condition_type=2，取此字段值做校验
     */
    @TableField(typeHandler = TextArrayTypeHandler.class)
    private String[] phones;

    /**
     * 消息模板id集合，关联表：wechat_robot_customer_msg_template.id（根据此id，反查获取对应的模板信息集合，设置到实体中的childrens属性中）
     */
    @TableField(typeHandler = ArrayTypeHandler.class)
    private Long[] msgTemplateIds;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 修改时间
     */
    @TableField(exist = false)
    private String filePath;

}
