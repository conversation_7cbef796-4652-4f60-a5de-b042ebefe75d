package ai.yiye.agent.domain;


import ai.yiye.agent.domain.enumerations.DeleteStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 投放账户结算公司主体维护表
 */
@Data
@NoArgsConstructor
@TableName(value = "settlement_company")
public class SettlementCompany implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 广告投放账户ID
     */
    private String accountId;

    /**
     * 用户标识
     */
    private String agentId;

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 项目ID
     */
    private Long advertiserAccountGroupId;


    /**
     * 结算主体对应的公司名称
     */
    private String settlementCompanyName;


    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 逻辑删除标记
     */
    private DeleteStatus deleteStatus;


}
