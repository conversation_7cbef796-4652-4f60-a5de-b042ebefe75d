package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 企业微信corpIdToOpenCorpId
 */
@Data
@TableName("enterprise_wechat_corp_id_to_open_corp_id")
public class EnterpriseWechatCorpIdToOpenCorpId implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业微信id
     */
    private String corpId;

    /**
     * 加密的企业微信id
     */
    private String openCorpId;

    /**
     * 是否为明文代开发
     */
    private Boolean isPlaintext;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
