package ai.yiye.agent.domain;

import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/5 9:51
 * 优化师与账户历史关系表
 */
@Data
@TableName("management_optimizer_account_rel")
public class OptimizerAccountRel{


    @TableField(exist = false)
    public static final String[] CONFLICTS = new String[]{"optimizer_id", "account_id","day_time"};
    @TableId(
        type = IdType.AUTO
    )
    private Long id;
    private Long optimizerId;
    private String accountId;
    private String dayTime;
    // 数据创建时间
    protected Instant createdAt;

    // 数据修改时间
    protected Instant updatedAt;
}
