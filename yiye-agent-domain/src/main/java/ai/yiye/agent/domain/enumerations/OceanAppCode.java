package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.annotation.EnumValue;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public enum OceanAppCode {

    XIGUA(1, "西瓜视频"),
    DOUYIN_HUOSHAN(3, "抖音火山版"),
    DOUYIN(4, "抖音"),
    TOUTIAO(8, "今日头条"),
    CHUANSHANJIA(9, "穿山甲"),
    FANQIE(26, "番茄小说"),
    OHAYOO(27, "ohayoo精品游戏"),
    TONGTOU(33, "通投智选"),
    SERCH(38, "搜索广告"),
    ZHANNEITONGTOU(55, "站内通投"),
    ZHUXIAOBANG(25, "住小帮");

    @EnumValue
    private final Integer id;

    private final String name;

    OceanAppCode(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    private static final Map<String, Integer> MAP = Arrays.stream(values()).collect(Collectors.toMap(OceanAppCode::getName, OceanAppCode::getId, (key1, key2) -> key2));

    public static Integer getIdByName(String name) {
        return MAP.get(name);
    }

}
