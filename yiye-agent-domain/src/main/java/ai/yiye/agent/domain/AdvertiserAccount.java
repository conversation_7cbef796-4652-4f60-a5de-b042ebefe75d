package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.typehandlers.JSONListTypeHandler;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import ai.yiye.agent.domain.util.BigDecimalSerialize;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 投放账户表(MarketingAdvertiserAccount)实体类
 *
 * <AUTHOR>
 * @since 2020-05-18 17:41:54
 */
@Data
@NoArgsConstructor
@TableName(value = "marketing_advertiser_account")
public class AdvertiserAccount implements Serializable {
    /**
     * 广告主id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private String accountId;

    /**
     * 数据初始化状态0未初始化，1初始化中，2完成
     */
    private AdvertiserAccountDataInitStatus dataInitStatus;
    /**
     * 广告主名称
     */
    private String accountName;

    private Long advertiserAccountGroupId;

    private String platformName;

    /**
     * 广告主状态（0有效、1待审核、2审核不通过、3封停）
     *
     */
    private AdvertiserAccountStatus accountStatus;


    private Long dailyBudget;

    /**
     * token
     */
    private String accessToken;
    /**
     * 刷新token
     */
    private String refreshToken;

    /**
     * token过期时间
     */
//    private Instant accessTokenExpiresAt;

    /**
     * 刷新token过期时间
     */
//    private Instant refreshTokenExpiresAt;
    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 所属代理商账号id
     */
    private String parentAccountId;

    private String corporationName;

    /**
     * 是否为代理商
     */
    private Boolean agency;

    private Long optimizerId;

    private AdvertiserAccountSystemStatus systemStatus;
    /**
     * 账户状态0投放账户 1私域账户
     */
    private AdvertiserAccountType accountType;

    /**
     * 投放目标
     */
    @TableField(typeHandler = JSONListTypeHandler.class)
    private JSONArray target;
    /**
     * 扩展字段
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject ext;


    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    private String userActionSetId;

    @TableField(exist = false)
    private String accountGroupName;

    @TableField(exist = false, typeHandler = JSONListTypeHandler.class)
    private JSONArray managerName;
    /**
     * 返回投放账户的关联对象ID
     */
    @TableField(exist = false)
    private Long advertiserRelId;
    /**
     * 是否抽取过数据
     */
    private Boolean isExtract;
    /**
     * 账户余额
     */
    private Long balance;
    /**
     * 曝光量
     */
    @TableField(exist = false)
    private Integer pv;
    /**
     * 点击量
     */
    @TableField(exist = false)
    private Integer click;
    /**
     * 点击率
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    @TableField(exist = false)
    private BigDecimal clickRate;
    /**
     * 目标转化数
     */
    @TableField(exist = false)
    private Integer convert;
    /**
     * 目标转化成本
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    @TableField(exist = false)
    private BigDecimal convertCost;
    /**
     * 目标转化率
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    @TableField(exist = false)
    private BigDecimal convertRate;
    /**
     * 花费
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    @TableField(exist = false)
    private BigDecimal spend;
    /**
     * 填单提交数
     */
    @TableField(exist = false)
    private Integer submitDataCount;
    /**
     * 填单提交成本
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    @TableField(exist = false)
    private BigDecimal submitDataCost;
    /**
     * 订单完成数
     */
    @TableField(exist = false)
    private Integer orderCompleteCount;
    /**
     * 订单完成成本
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    @TableField(exist = false)
    private BigDecimal orderCompleteCost;
    /**
     * 数据拉取使用，唯一标识
     */
    @TableField(exist = false)
    private String requestId;

    /**
     * 投放账户是否拉取标记位
     */
    private Boolean extractFlag;

    /**
     * 账户行业信息
     */
    private String industry;

    /**
     * 微信 - 公众号 - wechat_app_id
     */
    private String wechatAppId;

    /**
     * 微信 - 广告 - 投放账户 - appid （查询微信公众平台广告主基础信息 - 接口文档地址：https://developers.e.qq.com/docs/api/account/advertiser/wechat_advertiser_get?version=1.3&_preview=1）
     */
    @TableField(exist = false)
    private String wechatAccountId;

    /**
     * 返点
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal rebates;

    @TableField(exist = false)
    private String[] advertiserAccountGroupNames;

    @TableField(exist = false)
    private Long[] advertiserAccountGroupIds;

    @TableField(exist = false)
    private Integer deepConvert;

    @JsonSerialize(using = BigDecimalSerialize.class)
    @TableField(exist = false)
    private BigDecimal deepConvertCost;

    /**
     * 深度转化量
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    @TableField(exist = false)
    private BigDecimal deepConvertNum;
    /**
     * 深度目标转化率
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    @TableField(exist = false)
    private BigDecimal deepConvertRate;

    /**
     * 数据初始化类型  [0]：1天，[1]：30天
     */
    @TableField(exist = false)
    private AdvertiserAccountDataInitType initDataType;

    /**
     * 腾讯数据源类型，文档地址：https://developers.e.qq.com/docs/reference/enum?version=1.3&_preview=1#am_user_action_set_type
     */
    @TableField(exist = false)
    private UserActionSetType userActionSetType;

    /**
     * 基础数据拉取，job指定时间参数后，结束日期
     */
    @TableField(exist = false)
    private LocalDate endTime;

    /**
     * 基础数据拉取，job指定时间参数后，拉取时间天数
     */
    @TableField(exist = false)
    private Long duration;
    @TableField(exist = false)
    private String agentId;
    /**
     * 账户有效性
     */
    private ValidType valid;
    //账户授权日期
    private Instant authTime;
    /**
     * authUserId 对应授权用户的id
     */
    private String authUserId;
    /**
     * 当前计费状态
     */
    private BillingModeStatus billingModeStatus;
    /**
     * 最后一次修改计费的时间
     */
    private LocalDateTime effectTime;
    /**
     * 计费生效备注
     */
    private String billingRemark;
    /**
     * 固化计费状态
     */
    private BillingModeStatus effectStatus;

    /**
     * 微博广告 - 超级粉丝通 - 广告账号id（并非真是微博广告主id，广告主id为微博用的UID，这里需要注意）
     */
    private Long customerId;

    //预算，单位：元； 精度：小数点后两位；
    private Float budget;

    //BUDGET_MODE_DAY（日预算）
    //BUDGET_MODE_INFINITE（不限）
    private String budgetMode;

    /**
     * 当前账户是否可拉取 0：可拉取 1：不可拉取
     */
    private AccountPullStatus pullStatus;

    /**
     * 授权过期时间
     */
    @TableField(exist = false)
    private String expireTime;

    @TableField(exist = false)
    private TencentApiVersion tencentApiVersion;

    private String firstIndustryName;

    private String secondIndustryName;

    /**
     * 首次开启拉取数据时间(只会在首次打开数据拉取开关时赋值)
     */
    private LocalDateTime firstOpenExtractTime;

    /**
     * 最近一次拉取数据时间(只会在首次打开数据拉取开关和补偿广告报表数据时赋值)
     */
    private LocalDateTime lastExtractTime;

}
