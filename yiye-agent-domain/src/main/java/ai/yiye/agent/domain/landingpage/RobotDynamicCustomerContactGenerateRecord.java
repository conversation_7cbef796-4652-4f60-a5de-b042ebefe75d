package ai.yiye.agent.domain.landingpage;


import ai.yiye.agent.domain.enumerations.LandingPageWechatCustomerContactStatus;
import ai.yiye.agent.domain.enumerations.LandingPageWechatCustomerContactUsedStatus;
import ai.yiye.agent.domain.enumerations.LandingPageWechatCustomerContactVerifyStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;


/**
 * 微信客服机器人内加粉 —— 动态渠道二维码生成状态实时存储表
 */
@Data
@TableName(value = "robot_dynamic_customer_contact_generate_record")
public class RobotDynamicCustomerContactGenerateRecord implements Serializable  {


    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客服id，来自表：landing_page_wechat_customer_service.id
     */
    private Long landingPageWechatCustomerServiceId;


    /**
     * 素材ID(对应enterprise_wechat_temp_material.id)
     */
    private String materialId;


    /**
     * 素材ID(对应enterprise_wechat_temp_material.material_id)
     */
    private String dynamicMaterialId;


    /**
     * 企微corpid（这个客服二维码码对应的企业微信）
     */
    private String corpId;

    /**
     * 是否开启添加客服验证 0:否 1:是
     */
    private LandingPageWechatCustomerContactVerifyStatus contactVerify;

    /**
     * 联系我二维码生成状态 0:未生成，1:生成中,2:已生成 3：创建失败
     */
    private LandingPageWechatCustomerContactStatus dynamicRobotCustomerContactStatus;


    //动态渠道二维码参数
    private String robotWechatCustomerContactState;

    /**
     * 联系我 二维码链接 用于客服列表展示
     */
    private String dynamicRobotWechatCustomerContactQrCode;

    /**
     * 二维码最终合成图片url
     */
    private String contactImageUrl;

    /**
     * 新增联系方式的配置id
     */
    private String configId;




    //动态渠道二维码最终合成图片七牛云path
    private String contactImageQiniuPath;



    /**
     * 联系我 二维码创建失败原因
     */
    private String generateFailureReason;

    /**
     * 使用时间
     */
    private Instant usedAt;

    /**
     * 企微成员userid
     */
    private String landingPageWechatCustomerServiceWechatUserId;

    /**
     * 动态二维码使用状态
     */
    private LandingPageWechatCustomerContactUsedStatus usedStatus;

    /**
     * 所属账户
     */
    private String agentId;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 临时素材过期时间
     */
    private Instant expireAt;

    /**
     * 企业微信生成的二维码url
     */
    private String qrCodeUrl;

    /**
     * 上传素材所属的企业微信（因为同一个活码会上传到不同的企业微信上面）
     */
    private String uploadMaterialCorpId;

}
