package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AdvertiserAccountSystemStatus;
import ai.yiye.agent.domain.enumerations.SystemStatus;
import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2020/8/17 17:56
 */
@Data
@TableName(value = "marketing_data_advertiser_profile", autoResultMap = true)
public class AdvertiserProfile extends AbstractMarketingData {

    @TableField(exist = false)
    public static final String[] CONFLICTS = new String[]{"account_id", "promoted_object_id", "profile_id"};
    /**
     * 推广目标 id
     */
    private String promotedObjectId;
    /**
     * 推广目标类型
     */
    private String promotedObjectType;
    /**
     * 朋友圈头像及昵称跳转页 id
     */
    private Long profileId;
    /**
     * 朋友圈头像及昵称跳转页类型
     */
    private String profileType;
    /**
     * 昵称
     */
    private String profileName;
    /**
     * 头像图片 id
     */
    private String headImageId;
    /**
     * 朋友圈头像及昵称跳转页简介
     */
    private String description;
    /**
     * 朋友圈头像昵称跳转页 url
     */
    private String profileUrl;
    /**
     * 朋友圈头像及昵称跳转页状态
     */
    private SystemStatus systemStatus;

    /**
     * 头像 url
     */
    private String headImageUrl;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    @TableField(exist = false)
    protected JSONObject ext;

    @TableField(exist = false)
    protected Long optimizerId;

    @TableField(exist = false)
    private AdvertiserAccountSystemStatus advertiserAccountStatus;

}
