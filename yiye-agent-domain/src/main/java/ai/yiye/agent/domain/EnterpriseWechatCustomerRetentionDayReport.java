package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 用户留存分析报表（微信客服维度）
 */
@Data
@TableName("enterprise_wechat_customer_retention_day_report")
public class EnterpriseWechatCustomerRetentionDayReport {

    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 统计日期
     */
    private Instant statisticDate;


    /**
     * 企业微信corpId
     */
    private String corpId;


    /**
     * 微信客服userId
     */
   private String wechatCustomerServiceUserId;

    /**
     * 当日新增客户数
     */
   private Long addCustomerNum;

    /**
     * 当日删除客户数（客服删除客户的数量）
     */
    private Long deleteCustomerNum;


    /**
     * 当日流失客户数（客户删除客服的数量）
     */
    private Long reduceCustomerNum;



    /**
     * 新增客户总数（截至到当日，新增的客户总数）
     */
    private Long addCustomerTotalNum;

    /**
     * 当日删除客户数（客服删除客户的数量）（截至到当日，删除的客户总数）
     */
    private Long deleteCustomerTotalNum;


    /**
     * 当日流失客户数（客户删除客服的数量）（截至到当日，流失的客户总数）
     */
    private Long reduceCustomerTotalNum;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
