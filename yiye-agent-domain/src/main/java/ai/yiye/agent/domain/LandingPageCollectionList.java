package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.DeleteStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 收藏列表（落地页收藏、活动/优惠收藏）
 */
@Data
@TableName("landing_page_collection_list")
public class LandingPageCollectionList {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 一叶用户标识
     */
    private String agentId;

    /**
     * 微信小程序appid
     */
    private String wechatAppletAppid;

    /**
     * 微信小程序unionid
     */
    private String wechatAppletUnionId;

    /**
     * 落地页id
     */
    private Long landingPageId;

    /**
     * 落地页token
     */
    private String token;

    /**
     * 落地页名称
     */
    private String landingPageName;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 渠道标识（_cl）
     */
    private String channelParam;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 落地页删除状态（非0均为已删除不可访问）：0-NORMAL-未删除 1-IS_DELETE-已删除 2-FOREVER_DELETE-永久删除（逻辑删除）
     */
    @TableField(exist = false)
    private DeleteStatus landingPageDeleteStatus;

    /**
     * 落地页渠道删除状态（非0均为已删除不可访问）：0-NORMAL-未删除 1-IS_DELETE-已删除 2-FOREVER_DELETE-永久删除（逻辑删除）
     */
    @TableField(exist = false)
    private DeleteStatus landingPageChannelDeleteStatus;

}
