package ai.yiye.agent.domain;

import ai.yiye.agent.domain.clickhousetypehandlers.JSONStringTypeHandler;
import ai.yiye.agent.domain.enumerations.AdTraceType;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.Instant;

/**
 * 广告监测数据记录表
 *
 * @Author：lilidong
 * @Date：2023/6/14 10:14
 */
@Data
@TableName("advertising_trace")
public class AdvertisingTrace {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private String agentId;

    /**
     * 监测类型
     */
    private AdTraceType traceType;


    /**
     * 监测链接ID
     */
    private String linkId;


    /**
     * 扩展信息
     */
    @TableField(typeHandler = JSONStringTypeHandler.class)
    private JSONObject ext;

    /**
     * 曝光id，来自表：page_view_info.pid
     */
    private String pvPid;

    /**
     * 曝光uid，来自表：page_view_info.uid
     */
    private String pvUid;


    /**
     * 事件资产ID
     */
    private String eventAssetId;

    /**
     * 巨量广告体验版中特有的宏参，代表巨量广告体验版的广告ID
     */
    private String  promotionId;

    /**
     * 巨量广告体验版中特有的宏参，代表巨量广告体验版的广告名称
     */
    private String promotionName;


    /**
     * 巨量广告体验版中特有的宏参，代表巨量广告体验版的项目ID
     */
    private String  projectId;

    /**
     * 巨量广告体验版中特有的宏参，代表巨量广告体验版的项目名称
     */
    private String  projectName;

    /**
     * 针对巨量广告体验版，图片素材宏参数（下发原始素材id）
     */
    private String mid1;

    /**
     * 针对巨量广告体验版，标题素材宏参数（下发原始素材id）
     */
    private String mid2;

    /**
     * 针对巨量广告体验版，视频素材宏参数（下发原始素材id）
     */
    private String mid3;

    /**
     * 针对巨量广告体验版，搭配试玩素材宏参数（下发原始素材id）
     */
    private String mid4;

    /**
     * 针对巨量广告体验版，落地页素材宏参数（下发原始素材id）
     */
    private String mid5;

    /**
     * 针对巨量广告体验版，安卓下载详情页素材宏参数（下发原始素材id）
     */
    private String mid6;

    /**
     * 平台类型
     */
    private Integer platformId;

    /**
     * 投放账户id
     */
    private String accountId;

    /**
     * 广告组id
     */
    private Long adgroupId;

    /**
     * 广告组名称
     */
    private String adgroupName;

    /**
     * clickId，用于串联广告
     */
    private String clickId;


    private String ip;


    /**
     * 广告计划ID,不同广告平台对应：
     * 1、对应巨量的AID
     * 2、对应快手的 __DID__
     */
    private String adId;


    /**
     * 广告计划名称
     */
    private String adName;


    /**
     * 广告创意ID
     */
    private String creativeId;

    /**
     * 广告创意名称
     */
    private String creativeName;

    /**
     * 巨量广告
     * 创意样式
     * 2=小图模式
     * 3=大图模式
     * 4=组图模式
     * 5=视频
     */
    private String cType;

    /**
     * 广告主ID
     */
    private String advertiserId;

    /**
     * 广告投放位置（巨量广告）
     * 今日头条：1-10000，80000-110001
     * 西瓜视频：10001-10099
     * 火山小视频：30001-30099
     * 抖音：40001-40099
     * 番茄小说：26001-26099
     * 穿山甲开屏广告：800000000
     * 穿山甲网盟非开屏广告：900000000
     * 通投广告位：33013
     * 搜索：38016
     * <p>
     * 快手 (1-优选广告，2-信息流广告（旧投放场景，含上下滑大屏广告），3-视频播放页广告，5-联盟广告，6-上下滑大屏广告，7-信息流广告（不含上下滑大屏广告）27-开屏位置 39-搜索广告 24-激励视频)
     */
    private String cSite;

    /**
     * 转化Id
     */
    private String convertId;

    /**
     * 请求下发的ID
     */
    private String requestId;

    /**
     * 是否成功添加企业微信，1-是；0否
     */
    private Integer addWorkWechatSuccess = 0;


    /**
     * 企业微信ID
     */
    private String corpId;


    /**
     * 是否成功关注公众号，1-是；0否
     */
    private Integer followWechatAccountSuccess = 0;



    /**
     * 微信公众号appid，用于标注是哪个公众号添加的关注，关联【公众号表.app_id】
     */
    private String officialAccountAppId;


    /**
     * 请求下发的id&创意id的md5,16位
     */
    private String trackId;

    /**
     * 这次请求的语言,如 zh
     */
    private String sl;


    /**
     * 安卓的设备 ID的md5 摘要，32位
     */
    private String imei;


    /**
     * 快手
     * 安卓系统，对15位数字的 IMEI （比如***************）进行 MD5（备注：安卓广告唯一标示，imei双卡手机可能有两个，取默认的一个）
     */
    private String imei2;


    /**
     * 快手
     * 安卓系统，Android下的IMEI，原文计算SHA1
     */
    private String imei3;


    /**
     * 快手
     * IMEI进行 MD5，适配三方监测方案，imei2 imei4取一个即可，都会替换
     */
    private String imei4;

    /**
     * IOS 6+的设备id字段，32位
     */
    private String idfa;

    /**
     * iOS下的idfa计算MD5，规则为32位十六进制数字+4位连接符“-”的原文（比如：32ED3EE5-9968-4F25-A015-DE3CFF569568），再计算MD5，再转大写。
     */
    private String idfa2;

    /**
     * iOS下的idfa计算SHA1，规则是原文带“-”，计算SHA1
     */
    private String idfa3;

    /**
     * IOS 6+的设备id的md5摘要，32位
     */
    private String idfaMd5;

    /**
     * 快手
     * URL Encode后的JSON数组；其中kenyId为中广协ID（即CAID），kenyId_MD5为CAID原值MD5加密后的结果（32位小写）, version为信通院算法包版本号，支持两个版本同时下发（即最新版和上一版）
     */
    private String kenyidCaa;


    /**
     * 安卓id原值的md5，32位
     */
    private String androidId;

    /**
     * 对 ANDROIDID（举例:8f6581815307be28） 进行 MD5
     */
    private String androidId2;

    /**
     * Android下的AndroidID，原文计算SHA1
     */
    private String androidId3;

    /**
     * Android Q及更高版本的设备号，32位
     */
    private String oaid;

    /**
     * 快手： Android设备标识计算MD5
     */
    private String oaid2;

    /**
     * Android Q及更高版本的设备号的md5摘要，32位
     */
    private String oaidMd5;

    /**
     * 操作系统平台
     */
    private String os;

    /**
     * 移动设备mac地址,转换成大写字母,去掉“:”，并且取md5摘要后的结果
     */
    private String mac;

    /**
     * 移动设备 mac 地址,转换成大写字母,并且取md5摘要后的结果，32位
     */
    private String mac1;


    /**
     * 快手
     * 对 MAC 进行 MD5
     */
    private String mac2;

    /**
     * 快手
     * 对 MAC 去除分隔符之后进行 MD5
     */
    private String mac3;

    /**
     * ipv4
     */
    private String ipv4;

    /**
     * ipv6
     */
    private String ipv6;

    /**
     * 用户代理(User Agent)，一个特殊字符串头，使得服务器能够识别客户使用的操作系统及版本、CPU类型、浏览器及版本、浏览器渲染引擎、浏览器语言、浏览器插件等。
     */
    private String ua;


    /**
     * 位置信息，包含三部分:latitude（纬度），longitude（经度）以及precise（确切信息,精度）
     * 说明：十进制保留1位小数，西经南纬保留负数，用字母 x 分 割纬度与精度 (先纬后经，最后精度），如：35.7x122.4x100.0
     */
    private String geo;

    /**
     * 客户端发生广告点击事件的时间，以毫秒为单位时间戳
     */
    private Long ts;

    /**
     * 一些跟广告信息相关的回调参数，内容是一个加密字符串，在调用事件回传接口的时候会用到
     */
    private String callbackParam;


    /**
     * 直接把调用事件回传接口的url生成出来，广告主可以直接使用
     */
    private String callbackUrl;

    /**
     * 手机型号
     */
    private String model;

    /**
     * 对外广告位编码
     */
    private String unionSite;

    /**
     * 中国广告协会互联网广告标识，包含最新两个版本的CAID和版本号，url encode之后的json字符串(【CAID】和【CAID1、CAID2】的信息一致，使用一种即可；建议使用【CAID】，参数中包含多个信息，后续维护成本低）
     */
    private String caid;

    /**
     * 不同版本的中国广告协会互联网广告标识，CAID1是20230330版，CAID2是20220111版
     */
    private String caid1;

    private String caid2;

    /**
     * 不同版本的中国广告协会互联网广告标识，CAID1是20220111版，CAID2是20211207版
     */
    private String caid1Md5;


    private String caid2Md5;

    /**
     * 素材ID
     */
    private String photoId;

    /**
     * 是否为高级创意，1-是
     */
    private Integer acCreative;

    /**
     * 快手
     * 仅支持搜索流量，winfoid可通过marketing api 中关键词接口获得，对应word_info_id，如果为非搜索流量或智能扩词流量，则winfoid不替换
     */
    private String winfoId;

    /**
     * ad创建时间
     */
    private Instant adCreatedAt;
    /**
     * pv创建时间
     */
    private Instant pvCreatedAt;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 逻辑删除标记 0-正常；1-删除
     */
    private Integer deleteStatus;


}
