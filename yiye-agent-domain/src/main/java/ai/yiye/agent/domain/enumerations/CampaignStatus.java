package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 计划状态枚举
 * <AUTHOR>
 * @date 2021/5/26 18:20
 */
public enum CampaignStatus {

    CAMPAIGN_STATUS_ENABLE("CAMPAIGN_STATUS_ENABLE", "启用"),

    CAMPAIGN_STATUS_DISABLE("CAMPAIGN_STATUS_DISABLE", "暂停"),

    CAMPAIGN_STATUS_DELETE("CAMPAIGN_STATUS_DELETE", "删除"),

    CAMPAIGN_STATUS_ALL("CAMPAIGN_STATUS_ALL", "所有包含已删除"),

    ENA("ENA", "所有包含已删除(状态过滤默认值)"),

    CAMPAIGN_STATUS_ADVERTISER_BUDGET_EXCEED("CAMPAIGN_STATUS_ADVERTISER_BUDGET_EXCEED", "超出广告主日预算"),

    AD_STATUS_NORMAL("AD_STATUS_NORMAL", "有效"),

    AD_STATUS_SUSPEND("AD_STATUS_SUSPEND", "暂停"),

    ZERO("0", "所有数据"),

    ONE("1", "已删除"),

    TWO("2", "已暂停"),

    THREE("3", "余额不足"),

    FOUR("4", "到达账户日限额"),

    FIVE("5", "到达计划日限额"),

    SIX("6", "投放中"),
    /**
     * 下面是百度计划状态枚举
     */
    BAIDU_CAMPAIGN_STATUS_1("21","有效"),
    BAIDU_CAMPAIGN_STATUS_2("22","处于暂停时段"),
    BAIDU_CAMPAIGN_STATUS_3("23","暂停推广"),
    BAIDU_CAMPAIGN_STATUS_4("24","计划预算不足"),
    BAIDU_CAMPAIGN_STATUS_5("25","账户预算不足")
    ;

    @EnumValue
    private final String name;

    @JsonValue
    private final String value;

    public String getName() {
        return name;
    }

    CampaignStatus(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
