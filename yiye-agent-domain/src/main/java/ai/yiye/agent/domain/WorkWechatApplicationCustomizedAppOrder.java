package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 收银台 - 收款工具 - 收款订单列表 - 代开发应用购买详情：pay_order.product_list.customized_app
 *
 * 文档地址：https://developer.work.weixin.qq.com/document/path/99361
 *
 * 相应参数：{
 *     "paid_price": 0,
 *     "order_status": 2,
 *     "create_time": 1745485919,
 *     "order_from": 2,
 *     "custom_corp_name": "金米岛",
 *     "business_type": 2,
 *     "pay_type": 2,
 *     "origin_price": 20000,
 *     "product_list": {
 *         "customized_app": {
 *             "order_type": 0,
 *             "buy_info_list": [
 *                 {
 *                     "paid_price": 20000,
 *                     "suiteid": "dk0906e5299fea31a8",
 *                     "user_count": 10,
 *                     "duration_days": 30,
 *                     "origin_price": 20000,
 *                     "take_effect_date": "20251008"
 *                 }
 *             ]
 *         }
 *     },
 *     "buy_content": "上报助手 - DEV",
 *     "order_id": "N000048103685680A005FEFC11761",
 *     "custom_corpid": "wphTYQSAAAQYuYk93mN-6DokYZr-78iA"
 * }
 */
@Data
@TableName("work_wechat_application_customized_app_order")
public class WorkWechatApplicationCustomizedAppOrder {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业微信id
     */
    private String corpId;

    /**
     * 业务类型，对应枚举：ai.yiye.agent.domain.enumerations.EnterpriseWechatCashierBusinessType
     * 取值范围：
     *     1 - 普通第三方应用
     *     2 - 代开发应用
     *     3 - 行业解决方案
     */
    private Integer businessType;

    /**
     * 订单编号：work_wechat_application_order.order_id
     */
    private String orderId;

    /**
     * 客户企业的corpid，关联表：public.enterprise_wechats.open_corpid
     */
    private String customCorpid;

    /**
     * 购买内容
     */
    private String buyContent;

    /**
     * 订单状态。取值范围为：
     *     1 - 待支付
     *     2 - 已支付
     *     3 - 订单取消
     *     4 - 支付过期
     *     5 - 退款申请中
     *     6 - 已退款
     *     7 - 交易完成
     *     8 - 待企业确认
     *     9 - 已部分退款
     */
    private Integer orderStatus;

    /**
     * 订单来源。取值范围为：
     *     1 - 客户下单
     *     2 - 服务商创建
     */
    private Integer orderFrom;

    /**
     * 客户企业简称
     */
    private String customCorpName;

    /**
     * 订单创建人
     */
    private String creator;

    /**
     * 支付方式
     *     0-客户支付
     *     1-服务商代支付
     *     2-免支付
     */
    private Integer payType;

    /**
     * 创建时间（第三方订单创建时间）（主订单创建时间），关联表：work_wechat_application_order.create_time
     */
    private Instant createTime;

    /**
     * 购买类型，对应枚举：ai.yiye.agent.domain.enumerations.EnterpriseWechatCashierOrderType
     * 取值范围：
     *     0 - 新购
     *     1 - 扩容
     *     2 - 续期
     */
    private Integer orderType;

    /**
     * 套件ID，关联表：enterprise_wechats.suite_id
     */
    private String suiteid;

    /**
     * 应用的购买人数，单位人
     * 当购买类型是新购或扩容时需要填
     * 注意对于扩容类型，表示增加的人数
     * 取值范围：1 ~ 1000000
     */
    private Integer userCount;

    /**
     * 应用的购买时长，单位天
     * 取值范围：1 ~ 1825
     */
    private Integer durationDays;

    /**
     * 原价金额
     */
    private Long originPrice;

    /**
     * 总订单-原价金额（单位：分）
     */
    private Long originPriceAll;

    /**
     * 实付金额
     */
    private Long paidPrice;

    /**
     * 总订单-实付金额（单位：分）。免支付订单实付金额返回0
     */
    private Long paidPriceAll;

    /**
     * 生效日期，格式如：20221212
     */
    private String takeEffectDate;

    /**
     * 生效日期（时间格式）
     */
    private Instant takeEffectDateTime;

    /**
     * 应用实际有效期，计算公式：在【takeEffectDate】日期的基础上，向后推算【durationDays】天
     */
    private Instant actualTakeEffectDate;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
