package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.ExecuteDateType;
import ai.yiye.agent.domain.enumerations.ExecuteTimeType;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;

/**
 * 上报子策略
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_upload_son_strategy", autoResultMap = true)
public class UploadSonStrategy implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 策略ID(对应UploadStrategy的主键ID)
     */
    private Long uploadStrategyId;

    /**
     * 子策略名称
     */
    private String sonStrategyName;

    /**
     * 执行日期类型：0-长期投放，1-指定开始结束日期
     */
    private ExecuteDateType executeDateType;

    /**
     * 执行开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private Instant executeBeginDate;

    /**
     * 执行结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private Instant executeEndDate;

    /**
     * 执行时间类型
     */
    private ExecuteTimeType executeTimeType;

    /**
     * 指定开始时间和结束时间：值可取0-24点
     */
    private Integer[] beginEndTimeQuantum;

    /**
     * 指定多个时段,一共有7*48个0/1,0=未选，1=选择，按星期一到星期日、0-24点依次排序，半个小时为一个值
     */
    private String manyTimeQuantum;

    /**
     * 执行操作
     */
    private JSONArray executeOperation;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 子策略条件组列表
     */
    @TableField(exist = false)
    private ArrayList<StrategyConditionGroup> strategyConditionGroups;

    /**
     * 上报对应客资取值时间范围 - 开始时间
     */
    @TableField(exist = false)
    private Instant customerUploadValueStartTime;

    /**
     * 上报对应客资取值时间范围 - 结束时间
     */
    @TableField(exist = false)
    private Instant customerUploadValueEndTime;

}
