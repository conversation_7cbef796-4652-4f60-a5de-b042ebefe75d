package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AddEnterpriseWechatStatus;
import ai.yiye.agent.domain.enumerations.Sex;
import ai.yiye.agent.domain.enumerations.WechatAppletAddWay;
import ai.yiye.agent.domain.enumerations.WechatCustomerServiceMatchingErrorStatus;
import ai.yiye.agent.domain.typehandlers.TextArrayTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 企业微信添加外部联系人记录表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("enterprise_wechat_add_contact_record")
public class EnterpriseWechatAddContactRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 外部联系人unionid
     */
    private String externalUnionId;

    /**
     * 外部联系人id
     */
    private String externalUserId;

    /**
     * 外部联系人名称
     */
    private String externalName;

    /**
     * corpid（企业微信唯一标识，【关联 / 匹配】表：enterprise_wechats.corp_id）
     */
    private String corpId;

    /**
     * 微信客服当前可见范围人员的userId集合
     */
    @TableField(typeHandler = TextArrayTypeHandler.class)
    private String[] userIds;

    /**
     * 微信消息内容
     */
    private JSONObject wxMessage;

    /**
     * 外部客户详情信息
     */
    private JSONObject wxCpUserExternalContactInfo;

    /**
     * 创建时间
     */
    private Instant createdAt;

    // ----------------------------------------------------- 1.119.0 补充字段 -----------------------------------------------------
    /**
     * 企业微信回调数据（externalContact.name）：昵称 → 用户昵称（外部联系人）
     */
    private String wechatAppletName;

    /**
     * 企业微信回调数据（allFieldsMap.ExternalUserID）：用户企业微信userid → 企微客户（外部联系人）userid
     */
    private String wechatAppletExternalUserid;

    /**
     * 企业微信回调数据（externalContact.unionId）：微信unionid（一叶）→ 微信unionid（一叶）
     */
    private String wechatAppletUnionid;

    /**
     * 企业微信回调数据（allFieldsMap.UserID）：客服userid/客户群ID → 客服userid/客户群ID
     */
    private String wechatAppletUserid;

    /**
     * 企业微信回调数据（根据【allFieldsMap.UserID】匹配本系统数据）（followedUsers.未知该字段）：客服/客户群名称 → 客服名称/客服群名称
     */
    private String wechatAppletGroupChatName;

    /**
     * 匹配成功的，微信客服管理列表id，关联表字段：landing_page_wechat_customer_service.id
     */
    @TableField(exist = false)
    private Long matchLpwcsId;

    //    /**
    //     * 企业微信回调数据（followedUsers.tags）：客户标签信息 → 客户标签信（TODO 该字段需要建关联表）
    //     */
    //    @TableField(typeHandler = TextArrayTypeHandler.class)
    //    private String[] wechatAppletTags;

    /**
     * 企业微信回调数据（followedUsers.addWay）：添加企业微信方式 → 成功添加企业微信方式/来源
     *
     * 0	未知来源
     * 1	扫描二维码
     * 2	搜索手机号
     * 3	名片分享
     * 4	群聊
     * 5	手机通讯录
     * 6	微信联系人
     * 7	来自微信的添加好友申请
     * 8	安装第三方应用时自动添加的客服人员
     * 9	搜索邮箱
     * 10	视频号主页添加
     * 201	内部成员共享
     * 202	管理员/负责人分配
     */
    private WechatAppletAddWay wechatAppletAddWay;

    /**
     * 本系统数据（landing_page.id）：所属落地页（成功添加企业微信） → 小程序所属的落地页
     */
    private Long wechatAppletLandingPageId;

    /**
     * 本系统数据（landing_page_channels.id）：所属渠道（成功添加企业微信） → 小程序所属的渠道名
     */
    private Long wechatAppletLandingPageChannelId;

    /**
     * 本系统数据（page_view_info.url）：访问URL（成功添加企业微信） → 小程序访问的url
     */
    private String wechatAppletLandingPageViewUrl;

    /**
     * 本系统数据（landing_page_wechat_customer_service.id）：微信客服id；归因数据：（根据【enterprise_wechat_add_contact_record.wechat_applet_userid】匹配【landing_page_wechat_customer_service.wechat_user_id】得到【landing_page_wechat_customer_service.id】）
     */
    private Long wechatCustomerServiceId;

    /**
     * 微信客服账号userId匹配状态（关联表：landing_page_wechat_customer_service.wechat_user_id）：0-匹配失败  1-匹配成功
     */
    private Integer wechatUserIdMatchingStatus;

    /**
     * 本系统数据（landing_page_wechat_customer_service.advertiser_account_group_id）：微信客服pmp项目id；归因数据：（根据【enterprise_wechat_add_contact_record.wechat_applet_userid】匹配【landing_page_wechat_customer_service.wechat_user_id】得到【landing_page_wechat_customer_service.advertiser_account_group_id】）
     */
    private Long advertiserAccountGroupId;

    // ---------------------------------------------------------- 1.143.0 ----------------------------------------------------------
    /**
     * 匹配错误的【长按识别二维码 - 对应的 - 微信客服id】
     */
    private Long identifyQrCodeWcsId;

    /**
     * 匹配错误的【长按识别二维码 - 对应的 - 微信客服userId】
     */
    private String identifyQrCodeWcsUserId;

    /**
     * 匹配错误的【长按识别二维码 - 对应的 - 微信客服名称】
     */
    private String identifyQrCodeWcsUserName;

    /**
     * 企业微信回调返回的【微信客服userId - 对应的 - 微信客服id】
     */
    private Long cbUserIdMatchingWcsId;

    /**
     * 企业微信回调返回的【微信客服userId - 对应的 - 微信客服id】
     */
    private String cbUserIdMatchingWcsUserName;

    /**
     * 微信客服数据匹配状态
     */
    private WechatCustomerServiceMatchingErrorStatus wcsMatchingErrorStatus;

    /**
     * 匹配错误的【错误说明】
     */
    private String wcsMatchingErrorMessage;

    //    /**
    //     * 数据源id
    //     */
    //    @TableField(exist = false)
    //    private String agentId;

    /**
     * 加企业微信成功：0-未加粉  1-加粉成功（企业微信拉取完数据后，根据表字段值匹配：page_view_info.ext.wechat_applet_openid、page_view_info.ext.wechat_applet_unionid）
     */
    @TableField(exist = false)
    private AddEnterpriseWechatStatus addEnterpriseWechatStatus;

    /**
     * 加密的企业微信id
     */
    @TableField(exist = false)
    private String openCorpid;

    /**
     * unionid 与 外部联系人id 关联id
     */
    @TableField(exist = false)
    private String pendingId;
    /**
     * 好友添加时间  等同 接口内 createTime
     */
    private Instant externalUseridAddAt;

    /**
     * 企业微信-外部联系人-性别
     */
    @TableField(exist = false)
    private Sex externalUserSex;

    //企微获客链接
    @TableField(exist = false)
    private String wechatCustomerAcquisitionLink;

    //企微的获客链接id
    @TableField(exist = false)
    private String wechatCustomerAcquisitionLinkId;

    //外部联系人头像
    @TableField(exist = false)
    private String avatar;

}
