package ai.yiye.agent.domain;

import lombok.Data;

import java.time.Instant;

/**
 * 收银台 - 收款工具 - 收款订单列表（当前仅保存：代开发应用）
 *
 * 相应参数：[
 *     {
 *         "paid_price": 0,
 *         "order_status": 2,
 *         "create_time": 1745485919,
 *         "order_from": 2,
 *         "pay_type": 2,
 *         "origin_price": 20000,
 *         "buy_content": "上报助手 - DEV",
 *         "order_id": "N000048103685680A005FEFC11761",
 *         "custom_corpid": "wphTYQSAAAQYuYk93mN-6DokYZr-78iA"
 *     },
 *     {
 *         "paid_price": 0,
 *         "order_status": 2,
 *         "creator": "xin",
 *         "create_time": 1738913412,
 *         "order_from": 2,
 *         "pay_type": 2,
 *         "origin_price": 100,
 *         "buy_content": "上报助手 - DEV",
 *         "order_id": "N00004810368567A5B683D40175EF",
 *         "custom_corpid": "wphTYQSAAAIBnXbtUgHMGZefHgvNYYVA"
 *     },
 *     {
 *         "paid_price": 0,
 *         "order_status": 2,
 *         "creator": "WangYanWen",
 *         "create_time": 1728367576,
 *         "order_from": 2,
 *         "pay_type": 2,
 *         "origin_price": 100000,
 *         "buy_content": "上报助手 - DEV",
 *         "order_id": "N0000481036856704CBD7EFC117BB",
 *         "custom_corpid": "wphTYQSAAAQYuYk93mN-6DokYZr-78iA"
 *     },
 *     {
 *         "paid_price": 0,
 *         "order_status": 2,
 *         "creator": "SiYueShiNiDeHuangYan@",
 *         "create_time": 1722837521,
 *         "order_from": 2,
 *         "pay_type": 2,
 *         "origin_price": 200000,
 *         "buy_content": "上下游企业共享应用",
 *         "order_id": "N00004810368566B06A11EFC117ED",
 *         "custom_corpid": "wphTYQSAAAQYuYk93mN-6DokYZr-78iA"
 *     },
 *     {
 *         "paid_price": 0,
 *         "order_status": 2,
 *         "creator": "SiYueShiNiDeHuangYan@",
 *         "create_time": 1722824608,
 *         "order_from": 2,
 *         "pay_type": 2,
 *         "origin_price": 2000,
 *         "buy_content": "上报助手 - DEV",
 *         "order_id": "N00004810368566B037A0D40175B7",
 *         "custom_corpid": "wphTYQSAAAIBnXbtUgHMGZefHgvNYYVA"
 *     },
 *     {
 *         "paid_price": 0,
 *         "order_status": 2,
 *         "creator": "SiYueShiNiDeHuangYan@",
 *         "create_time": 1720088060,
 *         "order_from": 2,
 *         "pay_type": 2,
 *         "origin_price": 100,
 *         "buy_content": "上报助手 - DEV",
 *         "order_id": "N000048103685668675FC3B078F10",
 *         "custom_corpid": "wphTYQSAAAWpGYl2YBrfomFelrRt3X2Q"
 *     },
 *     {
 *         "paid_price": 0,
 *         "order_status": 2,
 *         "creator": "SiYueShiNiDeHuangYan@",
 *         "create_time": 1720087946,
 *         "order_from": 2,
 *         "pay_type": 2,
 *         "origin_price": 100000,
 *         "buy_content": "上报助手 - DEV",
 *         "order_id": "N0000481036856686758A3B078FA4",
 *         "custom_corpid": "wphTYQSAAAWpGYl2YBrfomFelrRt3X2Q"
 *     },
 *     {
 *         "paid_price": 0,
 *         "order_status": 2,
 *         "creator": "xin",
 *         "create_time": 1719301072,
 *         "order_from": 2,
 *         "pay_type": 2,
 *         "origin_price": 2000,
 *         "buy_content": "上报助手 - DEV",
 *         "order_id": "N000048103685667A73D0EFC1177E",
 *         "custom_corpid": "wphTYQSAAAQYuYk93mN-6DokYZr-78iA"
 *     },
 *     {
 *         "paid_price": 0,
 *         "order_status": 2,
 *         "creator": "xin",
 *         "create_time": 1718594871,
 *         "order_from": 2,
 *         "pay_type": 2,
 *         "origin_price": 2000,
 *         "buy_content": "上报助手 - DEV",
 *         "order_id": "N000048103685666FAD37D40175BB",
 *         "custom_corpid": "wphTYQSAAAIBnXbtUgHMGZefHgvNYYVA"
 *     },
 *     {
 *         "paid_price": 0,
 *         "order_status": 2,
 *         "creator": "WangYanWen",
 *         "create_time": 1710931835,
 *         "order_from": 2,
 *         "pay_type": 2,
 *         "origin_price": 50000,
 *         "buy_content": "上报助手 - DEV",
 *         "order_id": "N00004810368565FABF7B3B078F13",
 *         "custom_corpid": "wphTYQSAAAWpGYl2YBrfomFelrRt3X2Q"
 *     },
 *     {
 *         "paid_price": 0,
 *         "order_status": 2,
 *         "creator": "WangYanWen",
 *         "create_time": 1710319410,
 *         "order_from": 2,
 *         "pay_type": 2,
 *         "origin_price": 40000,
 *         "buy_content": "上报助手 - DEV",
 *         "order_id": "N00004810368565F16732EFC1178B",
 *         "custom_corpid": "wphTYQSAAAQYuYk93mN-6DokYZr-78iA"
 *     }
 * ]
 */
@Data
public class WorkWechatApplicationOrder {

    private Long id;

    /**
     * 企业微信id
     */
    private String corpId;

    /**
     * 业务类型，对应枚举：ai.yiye.agent.domain.enumerations.EnterpriseWechatCashierBusinessType
     * 取值范围：
     *     1 - 普通第三方应用
     *     2 - 代开发应用
     *     3 - 行业解决方案
     */
    private Integer businessType;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 创建时间（第三方订单创建时间）
     */
    private Instant createTime;

    /**
     * 客户企业的corpid，关联表：public.enterprise_wechats.open_corpid
     */
    private String customCorpid;

    /**
     * 购买内容
     */
    private String buyContent;

    /**
     * 原价金额（单位：分）
     */
    private Long originPrice;

    /**
     * 实付金额（单位：分）。免支付订单实付金额返回0
     */
    private Long paidPrice;

    /**
     * 订单状态。取值范围为：
     *     1 - 待支付
     *     2 - 已支付
     *     3 - 订单取消
     *     4 - 支付过期
     *     5 - 退款申请中
     *     6 - 已退款
     *     7 - 交易完成
     *     8 - 待企业确认
     *     9 - 已部分退款
     */
    private Integer orderStatus;

    /**
     * 订单来源。取值范围为：
     *     1 - 客户下单
     *     2 - 服务商创建
     */
    private Integer orderFrom;

    /**
     * 客户企业简称
     */
    private String customCorpName;

    /**
     * 订单创建人
     */
    private String creator;

    /**
     * 支付方式
     *     0-客户支付
     *     1-服务商代支付
     *     2-免支付
     */
    private Integer payType;

    /**
     * 创建时间
     */
    private Instant createdAt;

    /**
     * 修改时间
     */
    private Instant updatedAt;

}
