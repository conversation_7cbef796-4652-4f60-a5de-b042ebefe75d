package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.DeleteStatus;
import ai.yiye.agent.domain.enumerations.FunctionModuleType;
import ai.yiye.agent.domain.enumerations.MemberPermissionType;
import ai.yiye.agent.domain.enumerations.ProjectModuleType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * @Author：lilidong
 * @Date：2023/8/23 15:51
 */
@Data
@TableName("marketing_advertiser_account_permission")
public class MarketingAdvertiserAccountPermission {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户标识
     */
    private String agentId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 项目ID
     */
    private Long advertiserAccountGroupId;

    /**
     * 板块
     */
    @EnumValue
    private ProjectModuleType projectModule;


    /**
     * 功能
     */
    @EnumValue
    private FunctionModuleType functionalModule;

    /**
     * 权限
     */
    @EnumValue
    private MemberPermissionType permission;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 删除标记
     */
    private DeleteStatus deleteStatus;

}
