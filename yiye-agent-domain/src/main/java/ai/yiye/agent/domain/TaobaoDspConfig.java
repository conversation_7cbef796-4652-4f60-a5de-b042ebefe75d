package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("taobao_dsp_config")
public class TaobaoDspConfig {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private String pid;

    private String channelId;

    private String taskId;

    private String remark;

    private Long advertiserAccountGroupId;

    private Instant createdAt;

    private Instant updatedAt;
}
