package ai.yiye.agent.domain;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.Setter;

import java.io.Serializable;
import java.time.Instant;

@Data
@TableName("landing_page_domain_rel")
public class LandingPageDomainRel implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long domainId;

    private Long advertiserAccountId;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    @Setter
    private Integer type;

    //投放账户名
    @TableField(exist = false)
    private String accountName;

}
