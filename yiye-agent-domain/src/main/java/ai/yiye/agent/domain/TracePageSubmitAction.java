package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2020/6/12 10:02
 * @version: 1.1.0
 */
@Data
@TableName("trace_page_submit_action")
public class TracePageSubmitAction {

    @TableId(type = IdType.AUTO)
    private Long id;
    private String pid;
    private String uid;
    private String sid;
    private String ip;
    private Long landingPageId;
    private String token;
    private Long advertiserAccountId;
    private String uuid;
    private String actions;
    private String originActions;
    private String actionTypes;
    private String url;
    private Integer submitDataType;
    private Integer submitDataId;
    private Integer customerId;
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
