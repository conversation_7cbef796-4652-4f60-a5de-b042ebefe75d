package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.CheckStatus;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 企业微信 - 可见范围 - 表
 */
@Data
@TableName("landing_page_work_wechat_user_visible_range")
public class LandingPageWorkWechatUserVisibleRange {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业微信id
     */
    private String corpId;

    /**
     * 可见范围 - 成员名称
     */
    private String name;

    /**
     * 可见范围 - 成员userId
     */
    private String userId;

    /**
     * 可见范围 - 成员二维码
     */
    private String qrCode;

    /**
     * 可见范围 - 是否可以被选中 / 选择按钮被置灰
     */
    private CheckStatus checkStatus;

    /**
     * 可见范围 - 请求接口返回的全部参数
     */
    private JSONObject ext;

    /**
     * 最后一次变更前的记录
     */
    private String recordBeforeChange;

    /**
     *  头像
     */
    private String avatar;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 企业微信名称
     */
    @TableField(exist = false)
    private String enterpriseWechatName;

    /**
     * 部门id
     */
    private Long[] departmentId;

    /**
     * 部门名称
     */
    private String[] departmentName;

    /**
     * 手机号
     */
    @TableField(exist = false)
    private String wechatMobile;

    /**
     * 邮箱
     */
    @TableField(exist = false)
    private String wechatEmail;

}
