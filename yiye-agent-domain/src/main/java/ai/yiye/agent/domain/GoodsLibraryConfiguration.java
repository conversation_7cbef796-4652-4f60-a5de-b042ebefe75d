package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * @ClassName : GoodsLibraryConfiguration
 * <AUTHOR> lmg
 * @Description : 商品库配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_goods_library_configuration", autoResultMap = true)
public class GoodsLibraryConfiguration implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 获取代理商商品库列表url
     */
    private String getGoodsUrl;
    /**
     * 商品列表展示信息、顺序及对应字段
     */
    private String goodMessageRelation;
    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
