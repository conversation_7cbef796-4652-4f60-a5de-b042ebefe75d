package ai.yiye.agent.domain.marketing.data;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2021/10/22 14:15
 */

@Data
@TableName(value = "marketing_data_search_keyword_report",autoResultMap = true)
public class AdvertiserKeywordReport extends AbstractMarketingData{
    public static final String[] CONFLICTS = new String[]{"platform_id", "campaign_id", "adgroup_id", "account_id", "data_time","keyword_id"};

    // 广告计划ID
    private Long campaignId;

    // 广告组ID
    private Long adgroupId;
    /**
     * 关键词id
     */
    private Long keywordId;

    // 广告ID(广点通接口冗余)
    private Long adId;

    // 定向包ID
    private Long targetingId;

    // 曝光量
    private Long viewNum;

    // 转换量
    private Long convertNum;

    // 转换成本
    private Long convertCost;

    // 花费
    private Long cost;

    // 点击量
    private Long clickNum;

    // 深度转换数
    private Long deepConvertNum;

    // 深度转换花费
    private Long deepConvertCost;

    // 播放数
    private Long playNum;

    // 有效播放数
    private Long validPlayNum;

    // 有效播放成本
    private Long validPlayCost;

    // 有效视频播放率
    private Float validPlayRate;

    // 平均播放时间
    private Long averagePlayTime;

    // wifi播放量
    private Long wifiPlayNum;

    // 总播放时间
    private Long sumPlayTime;

    // 25%播放数
    private Long videoPlay25Num;

    // 50%播放数
    private Long videoPlay50Num;

    // 75%播放数
    private Long videoPlay75Num;

    // 100%播放数
    private Long videoPlay100Num;

    // 下载量
    private Long downloadNum;

    // 日期
    private Instant dayTime;

    // 时间
    private Instant dataTime;

    // 小时字段
    private Integer hour;

    // 周
    private Integer week;

    // 关注量
    private Long followNum;

    // 平台请求接口ID
    private String traceId;

    // 关注成本
    @TableField(exist = false)
    private Long followCost;

    // wechat关注量
    @TableField(exist = false)
    private Long wechatFollowNum;

    @TableField(exist = false)
    private Instant createdAt;

    @TableField(exist = false)
    private Instant updatedAt;

    // 曝光人数
    private Long viewUserNum;

    // 赔付金额
    private Long compensationAmount;

    // 点击人数
    private Long clickUserNum;

    // 表单预约量
    private Long formAppointmentNum;

    // 表单预约人数
    private Long formAppointmentUserNum;

    // 下单量（网页）
    private Long orderNum;

    // 微信下单量
    @TableField(exist = false)
    private Long wechatOrderNum;

    // 订单金额
    private Long orderAmount;

    // 付费行为量
    private Long payBehaviorNum;

    // 付费金额
    private Long payAmount;

    // 首次付费行为人数
    private Long firstPayUserNum;

    // 微信首次付费行为人数
    @TableField(exist = false)
    private Long wechatFirstPayUserNum;

    // 注册量（网页）
    private Long registerNum;

    // 公众号关注量
    private Long officialAccountFollowNum;

    // 分享数
    private Long shareNum;

    // 评论数
    private Long commentNum;

    // 点赞数
    private Long praiseNum;

    // 广点通按钮数 = lanButtonClickCount + buttonNum
    @TableField(exist = false)
    private Long lanButtonClickCount;

    // 按钮数
    private Long buttonNum;

    // 销售线索量(微信广告时，为表单预约量)
    private Long salesLeadsNum;

    // 销售线索人数
    private Long salesLeadsUserNum;

    // 有效线索量
    private Long effectiveLeadsNum;

    // 有效线索人数
    private Long effectiveLeadsUserNum;

    // 微信有效线索人数
    @TableField(exist = false)
    private Long wechatEffectiveLeadsUserNum;

    // 图片点击次数
    private Long clickImageNum;

    // 推广页播放人数
    private Long promotionPageViewUserNum;

    // 推广页曝光次数
    private Long promotionPageViewNum;

    /**
     * APP激活总量
     */
    private Long appActivatedNum;

    /**
     * APP下载激活率
     * <p>
     * 激活量/下载量*100%
     */
    private BigDecimal appActivatedRate;

    /**
     * APP下载率
     * 下载量/点击量*100%
     */
    private BigDecimal appDownloadRate;

    /**
     * APP下载成本
     * 花费/下载量
     */
    private Float appDownloadCost;

    /**
     * APP安装量
     */
    private Long appInstallNum;

    /**
     * APP安装成本
     * 花费/安装量
     */
    private Float appInstallCost;

    /**
     * APP点击激活率
     * 激活量/点击量*100%
     */
    private BigDecimal appClickActivatedRate;

    /**
     * APP激活成本
     */
    private Float appActivatedCost;

    /**
     * 次日留存量（APP）
     */
    private Long appRetentionNum;

    /**
     * 次日留存率（APP）
     * 次日留存量/激活量*100%
     */
    private BigDecimal appRetentionRate;

    /**
     * 次日留存成本（APP）
     */
    private Float appRetentionCost;

    /**
     * 注册量（APP）
     */
    private Long appRegisterNum;

    /**
     * 注册成本（APP）
     */
    private Float appRegisterCost;

    /**
     * 激活注册率（APP）
     * 注册量/激活量*100%
     */
    private BigDecimal appRegisterRate;

    /**
     * APP安装率
     * APP安装量/点击量*100%
     */
    private BigDecimal appInstallRate;

    /**
     * 点击率
     */
    private BigDecimal clickRate;
    /**
     * 点击均价
     */
    private BigDecimal avgPrice;
    /**
     * 上方展现胜出率
     */
    private BigDecimal topPvWinA;
    /**
     * 上方展现胜出率
     */
    private BigDecimal topPvWinP;
    /**
     * 上方位展现
     */
    private Long topPageViews;
    /**
     * 上方位点击
     */
    private Long topPClicks;
    /**
     * 上方位消费
     */
    private BigDecimal topPay;
}
