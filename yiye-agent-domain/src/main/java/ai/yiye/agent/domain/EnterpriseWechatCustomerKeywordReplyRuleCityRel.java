package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.CommonYesOrNoStatus;
import ai.yiye.agent.domain.enumerations.SwitchStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 微信客服机器人智慧城市类型-关键词回复关联表
 */
@Data
@TableName("enterprise_wechat_customer_keyword_reply_rule_city_rel")
public class EnterpriseWechatCustomerKeywordReplyRuleCityRel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    //是否为兜底策略
    private CommonYesOrNoStatus reveal;

    /**
     * 策略ID
     */
    private String strategyId;

    /**
     * IP来源城市ID数组
     */
    private Long[] ipRegionalSourceCity;

    /**
     * IP来源城市名称
     */
    private String ipRegionalSourceCityName;

    /**
     * IP来源省份ID数组
     */
    private Long[] ipRegionalSourceProvince;

    /**
     * IP来源省份名称
     */
    private String ipRegionalSourceProvinceName;

    /**
     * 项目ID
     */
    private Long advertiserAccountGroupId;

    /**
     * 机器人ID
     */
    private Long enterpriseWechatCustomerId;

    /**
     * enterprise_wechat_customer_auto_answer_rule模板ID
     */
    private Long enterpriseWechatCustomerAutoAnswerRuleId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 是否开启关键词回复
     */
    private SwitchStatus keywordReply;
    /**
     * 关键词回复-是否检验已添加不回复
     */
    private SwitchStatus keywordReplyNotTriggeredWhenAdded;
}
