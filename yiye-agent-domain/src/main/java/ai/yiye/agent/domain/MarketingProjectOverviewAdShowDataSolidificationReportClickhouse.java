package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 项目级别广告曝光口径固化报表(数据源来源于clickhouse数据库，对应新版查询)
 * @Author：lilidong
 * @Date：2024/1/30 15:43
 */
@Data
@TableName("marketing_project_ad_show_data_solidification_report_clickhouse")
public class MarketingProjectOverviewAdShowDataSolidificationReportClickhouse {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 一叶账户 ID
     */
    private Long advertiserAccountId;


    /**
     * 一叶系统的项目ID
     */
    private Long advertiserAccountGroupId;

    /**
     * 口径类型
     */
    private Integer caliberType;

    /**
     * 链路类型 0：前链路；1-后链路
     */
    private Integer linkType;

    /**
     * 广告项目ID
     */
    private Long projectId;

    /**
     * 广告投放账户ID
     */
    private String accountId;

    /**
     * 广告ID
     */
    private Long promotionId;

    /**
     * 广告计划ID
     */
    private Long campaignId;

    /**
     * 平台Id
     */
    private Integer platformId;

    /**
     * 统计日期
     */
    private LocalDateTime dayTime;

    /**
     * 周
     */
    private Integer week;

    /**
     * 小时
     */
    private Integer hour;

    /**
     * 消耗
     */
    private BigDecimal cost;

    /**
     * 展示数
     */
    private Long viewNum;

    /**
     * 平均千次展现费用（总消耗/展示数*1000）
     */
    private BigDecimal avgCostPerThousandImpressions;

    /**
     * 点击数
     */
    private Long clickNum;

    /**
     * 点击率（点击数/展示数*100%）
     */
    private BigDecimal clickRate;

    /**
     * 平均点击单价（总消耗/点击数）
     */
    private BigDecimal avgClickPrice;

    /**
     * 转化数
     */
    private Long convertCount;

    /**
     * 转化率
     */
    private BigDecimal convertRate;

    /**
     * 平均转化成本(总消耗/转化数)
     */
    private BigDecimal avgConvertCost;

    /**
     * 深度转化数
     */
    private Long deepConvertNum;

    /**
     * 深度转化率(深度转化数/转化数*100%)
     */
    private BigDecimal deepConvertRate;

    /**
     * 深度转化成本(总消耗/深度转化数)
     */
    private BigDecimal deepConvertCost;

    /**
     * 落地页PV
     */
    private Long landingPagePv;

    /**
     * 企业推pv
     */
    private Long qiyetuiPageViewNum;

    /**
     * 落地页UV
     */
    private Long landingPageUv;

    /**
     * 赔付金额
     */
    private BigDecimal compensationAmount;

    /**
     * H5表单提交数
     */
    private Long fillCountNum;

    /**
     * H5表单提交率
     */
    private BigDecimal formSubmitRate;

    /**
     * H5表单提交成本
     */
    private BigDecimal formSubmitCost;

    /**
     * 线索通表单提交数
     */
    private Long clueFormSubmitNum;

    /**
     * 字节小程序原生表单提交数
     */
    private Long douyinAppletNativeFormSubmitNum;

    /**
     * 一键获号数
     */
    private Long phoneNumberRecievedNum;

    /**
     * 主动私信授权数
     */
    private Long activeMessageAuthorizationNum;

    /**
     * 订单提交数
     */
    private Long orderSubmitNum;

    /**
     * 订单提交率(订单提交数/落地页PV*100%)
     */
    private BigDecimal orderSubmitRate;

    /**
     * 订单提交成本(消耗/订单提交数)
     */
    private BigDecimal orderSubmitCost;

    /**
     * 订单完成数
     */
    private Long orderFinishNum;

    /**
     * 订单完成率(订单完成数/落地页PV*100%)
     */
    private BigDecimal orderFinishRate;

    /**
     * 订单完成成本
     */
    private BigDecimal orderFinishCost;

    /**
     * 订单成交金额(该账户/广告组/广告计划下对应订单完成支付金额总和)
     */
    private BigDecimal orderTransactionAmount;

    /**
     * 订单成交ROI(订单成交金额/广告消耗)
     */
    private BigDecimal orderTransactionRoi;

    /**
     * 长按二维码识别数（微信/企业微信）
     */
    private Long identifyQrCodeNum;

    /**
     * 长按二维码识别成本（消耗/长按二维码识别数（微信/企业微信）
     */
    private BigDecimal identifyQrCodeCost;

    /**
     * 长按二维码识别率（长按二维码识别数（微信/企业微信）/落地页PV*100% ）
     */
    private BigDecimal identifyQrCodeRate;

    /**
     * 成功添加企业微信数(一叶落地页成功添加企业微信数)
     */
    private Long addWorkWechatNum;

    /**
     * 成功添加企业微信率(加企业微信数/落地页PV*100%)
     */
    private BigDecimal addWorkWechatRate;

    /**
     * 成功添加企业微信成本(消耗/成功添加企业微信数)
     */
    private BigDecimal addWorkWechatCost;

    /**
     * 长按二维码识别数（公众号）
     */
    private Long officialIdentifyQrCodeNum;

    /**
     * 长按二维码识别率（公众号）(长按二维码识别数（公众号）/落地页PV*100%)
     */
    private BigDecimal officialIdentifyQrCodeRate;

    /**
     * 长按二维码识别成本（公众号）(消耗/长按二维码识别数(公众号)
     */
    private BigDecimal officialIdentifyQrCodeCost;

    /**
     * 公众号关注数
     */
    private Long officialFocusNum;

    /**
     * 公众号关注率
     */
    private BigDecimal officialFocusRate;

    /**
     * 微信公众号关注成本
     */
    private BigDecimal officialFocusCost;

    /**
     * 电商商品购买数
     */
    private Long onlineShopBuyGoodsProductCount;

    /**
     * 电商商品购买率(电商商品购买数/落地页PV*100%)
     */
    private BigDecimal onlineShopBuyGoodsProductRate;

    /**
     * 电商商品单笔购买成本(消耗/电商商品购买数)
     */
    private BigDecimal onlineShopBuyGoodsProductCost;

    /**
     * 电商商品成交金额(该账户/广告组/广告计划下对应电商客资下单金额总和)
     */
    private BigDecimal onlineShopBuyGoodsProductTradeMoney;

    /**
     * 电商商品成交ROI(电商商品成交金额/广告消耗 )
     */
    private BigDecimal onlineShopBuyGoodsProductRoi;

    /**
     * 公众号历史文章页PV
     */
    private Long wechatOfficialArticlePageViewNum;

    /**
     * 创建时间
     */
    private Instant createdAt;

    /**
     * 更新时间
     */
    private Instant updatedAt;

    /**
     * '字节小程序原生订单提交数'
     */
    private Long douyinAppletOrderSubmitNum;

    /**
     * ''字节小程序原生订单完成数''
     */
    private Long douyinAppletOrderFinishNum;

    /**
     * 字节小程序订单成交金额
     */
    private BigDecimal douyinAppletOrderAmount;

    /**
     * 淘宝组件复制成功数
     */
    private Long taobaoComponentCopyNum;

    /**
     * 字节小程序跳转数
     */
    private Long douyinAppletJumpNum;

    /**
     * 公众号发码后加粉数
     */
    private Long officialAddCustomerNum;

    /**
     * 饿了么页面访问数
     */
    private Long elePvNum;
    /**
     * 饿了么二维码识展示数
     */
    private Long eleQrCodeViewNum;
    /**
     * 饿了么二维码点击数
     */
    private Long eleIdentifyWechatQrCodeNum;
    /**
     * 饿了么好友添加成功数
     */
    private Long eleAddWechatSuccessNum;
    /**
     * whatsapp 跳转数
     */
    private Long whatsappJumpNum;
    /**
     * whatsapp 好友建联数
     */
    private Long whatsappAddFriendSuccessNum;
    /**
     * whatsapp 好友开口数
     */
    private Long whatsappUserOpenMouthNum;
    /**
     * 点击跳转淘宝电影小程序数
     */
    private Long taoBaoMovieAppletJumpNum;

    /**
     * 淘宝电影小程序订单数
     */
    private Long taoBaoMovieAppletOrderNum;

    /**
     * 加企业微信后入群数
     */
    private Long addGroupAfterAddCustomerServiceNum;

    /**
     * 公众号助手发码加粉后入群数
     */
    private Long addGroupAfterFollowOfficialAccountNum;


    /**
     * 成功发码/图片消息数（微信客服机器人）
     */
    private Long sendImageOrQrCodeNum;

    /**
     * 成功发送小程序消息数（微信客服机器人）
     */
    private Long miniProgramNewsNum;


    /**
     * 跳转领取618超级红包数
     */
    private Long jumpToSuperRedEnvelopeNum;


    /**
     * 成功发送欢迎语数
     */
    private Long successSendWelcomeMsgNum;

    /**
     *
     * 上报回传数
     */
    private Long adUploadNum;

    /**
     * 进入会话数（客服机器人）
     */
    private Long intoWechatCustomerServiceSessionNum;

    /**
     * 首次开口数
     */
    private Long startOpenChatNum;

    /**
     * 3次开口数
     */
    private Long thirdOpenChatNum;

    /**
     * 5次开口数
     */
    private Long fifthOpenChatNum;

    /**
     * 10次开口数
     */
    private Long tenthOpenChatNum;

    /**
     * 来源跳转页PV
     */
    private Long flowSourceJumpPageViewNum;
}
