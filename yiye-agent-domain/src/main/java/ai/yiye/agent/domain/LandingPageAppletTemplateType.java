package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.*;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2022/10/24 14:40
 */
@Data
@TableName("landing_page_applet_template_type")
public class LandingPageAppletTemplateType {
    private Long id;
    //模板被选中的时候的icon
    private String chooseIcon;
    //模板默认icon
    private String defaultIcon;
    //模板title
    private String title;
    //模板详细内容
    private String templateDesc;
    //tip
    private String tip;
    //排序
    private Integer sort;
    //是否默认选中
    private AppletTemplateTypeDefaultType defaultType;
    //模板类型
    private AppletTemplateType templateType;
    //模板tag
    private TemplateTagType tag;
    //是否显示落地页筛选项
    private LandingPageSelectShowType landingPageSelect;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
    /**
     * 是否可选
     */
    private AppletTemplateTypeAbleEnum disabled;

    /**
     * 模板是否下线
     */
    private AppletTemplateTypeStatusEnum typeStatus;
}
