package ai.yiye.agent.domain.enumerations;


import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * 落地页其他指标事件枚举
 */
public enum PageViewIndicatorEventEnum {


    POP_UP_DISPLAY(0, "弹窗展示"),

    DOU_YIN_APPLET_JUMP(1, "字节小程序内成功跳转其他小程序"),

    TAO_BAO_COMPONENT_COPY_SUCCESS(2,"成功复制淘客组件"),


    CLICK_TAO_BAO_MOVIE_APPLET_JUMP_SUCCESS(3, "点击跳转淘宝电影小程序"),

    TAO_BAO_MOVIE_APPLET_ORDER_SUCCESS(4, "淘宝电影小程序内下单成功"),


    JUMP_TO_SUPER_RED_ENVELOPE(5, "跳转领取618超级红包数"),
    ;



    @EnumValue
    private final int code;

    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    PageViewIndicatorEventEnum(int code, String desc){
        this.code = code;
        this.desc = desc;
    }

}
