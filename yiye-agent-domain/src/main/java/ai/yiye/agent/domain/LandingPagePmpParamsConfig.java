package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.LandingPagePmpConfigType;
import ai.yiye.agent.domain.enumerations.SwitchStatus;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * pmp内参数配置表
 */
@Data
@TableName("landing_page_pmp_params_config")
public class LandingPagePmpParamsConfig {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * pmp项目id
     */
    private Long advertiserAccountGroupId;

    /**
     * 类型 0:小程序 1:微信客服
     */
    private LandingPagePmpConfigType type;

    /**
     * 邮箱
     */
    private String[] emails;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 飞书自定义机器人key
     */
    private String feishuRobotKey;

    /**
     * 企业微信自定义机器人key
     */
    private String wecomRobotKey;

    /**
     * 钉钉自定义机器人key
     */
    private String dingtalkRobotKey;

    /**
     * 数据推送字段
     */
    private JSONArray dataPushField;

    /**
     * 成员不足提醒开关 0:关闭 1:开启
     */
    private SwitchStatus memberLessRemindSwitch;

    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String advertiserAccountGroupName;

    /**
     * 用户id
     */
    private Long userId;

    //当前项目维度 用户应该通知的企业微信用户数
    @TableField(exist = false)
    private Integer expireSoonNum = 0;
    @TableField(exist = false)
    private Integer expiredNum = 0;
}
