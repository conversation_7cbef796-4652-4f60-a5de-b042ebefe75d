package ai.yiye.agent.domain;

import ai.yiye.agent.common.util.CommonUtil;
import ai.yiye.agent.domain.enumerations.CommonParamsType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 落地页 - 公共参数表
 */
@Data
@TableName("landing_page_common_params")
public class LandingPageCommonParams {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识key（一般为32位的uuid，参考方法：CommonUtil.getUuidReplaceAll()）
     */
    private String uuidKey;

    /**
     * 参数值
     */
    private String paramsStr;

    /**
     * 公共参数-类型/用途，对应枚举：ai.yiye.agent.domain.enumerations.CommonParamsType
     */
    private CommonParamsType useType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    public LandingPageCommonParams() {

    }

    public LandingPageCommonParams(CommonParamsType commonParamsType, String paramsStr) {
        this.id = null;
        this.uuidKey = CommonUtil.getUuidReplaceAll();
        this.paramsStr = paramsStr;
        this.useType = commonParamsType;
    }

}
