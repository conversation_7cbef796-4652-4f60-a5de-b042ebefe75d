package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("landing_page_group_sort")
public class LandingPageGroupSort {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id，关联：ucenter_user.id
     */
    private Long userId;

    /**
     * pmp项目id，关联表：marketing_advertiser_account_group.id
     */
    private Long advertiserAccountGroupId;

    /**
     * 落地页分组id，关联表：landing_page_group.id
     */
    private Long groupId;

    /**
     * 排序值，值越大，越靠前
     */
    private Integer orderNum;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
