package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AssetStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@TableName("marketing_audience_bundle_log")
public class AudienceBundleLog {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long audienceBundleId;

    private Integer platformId;

    private String platformName;

    private Long advertiserAccountId;

    private String externalAudienceBundleId;

    private AssetStatus result;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    @TableField(exist = false)
    private Long accountId;

    @TableField(exist = false)
    private String accountName;

    @TableField(exist = false)
    private String filePath;
}
