package ai.yiye.agent.domain;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("customer_group")
public class CustomerGroup {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Long advertiserAccountId;
    private String groupName;
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    private Long advertiserAccountGroupId;

}
