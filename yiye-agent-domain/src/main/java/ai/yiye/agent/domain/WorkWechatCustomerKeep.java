package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 企微客服留存数据
 */
@Data
@TableName("work_wechat_customer_keep")
public class WorkWechatCustomerKeep implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业微信id，关联表字段：enterprise_wechats.corpid
     */
    private String corpId;

    /**
     * 对应企业成员账号（userid）
     */
    private String userId;

    /**
     * 客服名称
     */
    private String name;

    /**
     * 手机号
     */
    @TableField(exist = false)
    private String wechatMobile;

    /**
     * 邮箱
     */
    @TableField(exist = false)
    private String wechatEmail;

    /**
     * 部门id
     */
    private Long[] departmentId;

    /**
     * 部门名称
     */
    private String[] departmentName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
