package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.RefundStatus;
import ai.yiye.agent.domain.enumerations.WechatRefundErrorCode;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.egzosn.pay.common.bean.DefaultCurType;
import com.egzosn.pay.common.bean.RefundOrder;
import com.egzosn.pay.wx.bean.WxPayMessage;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * 2 * @Author: wangChuangjia
 * 3 * @Date: 2020/3/25 0025 13:36
 * 4
 */
@Data
@TableName("customer_wechat_refund_record")
public class CustomerWechatRefundRecord implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 关联的客资id
     */
    private Long customerId;

    /**
     * 退款订单编号
     */
    private String refundOrderNumber;

    /**
     * 退款金额
     */
    private BigDecimal refundMoney;

    /**
     * 退款原因
     */
    private String description;

    /**
     * 退款状态
     */
    private RefundStatus refundStatus;

    /**
     * 错误信息
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject rejectMessage;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    public RefundOrder buildRefundOrder(BigDecimal totalAmount, String tradeNo) {
        RefundOrder refundOrder = new RefundOrder();
        if (Objects.isNull(this.refundOrderNumber)) {
            this.refundOrderNumber = UUID.randomUUID().toString().replace("-", "");
        }
        refundOrder.setRefundNo(this.refundOrderNumber);
        refundOrder.setTradeNo(tradeNo);
        //退款金额
        refundOrder.setRefundAmount(this.refundMoney);
        refundOrder.setTotalAmount(totalAmount);
        refundOrder.setOrderDate(new Date());
        refundOrder.setCurType(DefaultCurType.CNY);
        refundOrder.setDescription(this.description);
        return refundOrder;
    }

    public void analysisWechatResult(WxPayMessage refundMessage, Map<String, Object> refundResult) {
        String resultCode = refundMessage.getResultCode();
        RejectMessage rejectMessage = Enum.valueOf(RefundStatus.class, resultCode).createRejectMessage(refundMessage);
        if (Objects.isNull(rejectMessage.getError())) {
            this.refundStatus = RefundStatus.SUCCESS;
        } else {
            this.refundStatus = RefundStatus.FAIL;
        }
        rejectMessage.setResponse(refundResult);
        this.rejectMessage = JSON.parseObject(JSON.toJSONString(rejectMessage));
    }

    public void createErrorRecords() {
        this.refundStatus = RefundStatus.FAIL;
        RejectMessage rejectMessage = new RejectMessage();
        rejectMessage.setError(WechatRefundErrorCode.SYSTEMERROR.toString());
        rejectMessage.setMessage(WechatRefundErrorCode.SYSTEMERROR.getMessage());
        this.rejectMessage = JSON.parseObject(JSON.toJSONString(rejectMessage));
    }

    @Data
    public static class RejectMessage {
        private String error;
        private String message;
        private Object response;
    }
}


