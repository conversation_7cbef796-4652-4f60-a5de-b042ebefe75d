package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.FunctionStatus;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2022/7/18 20:59
 */
@Data
@TableName("system_function")
public class SystemFunction implements Serializable {
    private Long id;
    /**
     * 功能唯一标识
     */
    private String functionName;
    /**
     * 功能状态
     */
    private FunctionStatus functionStatus;
    /**
     * 功能描述
     */
    private String remark;
    /**
     * 下线时间
     */
    private Instant offlineAt;
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
