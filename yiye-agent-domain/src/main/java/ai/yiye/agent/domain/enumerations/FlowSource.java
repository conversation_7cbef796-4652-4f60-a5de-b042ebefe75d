package ai.yiye.agent.domain.enumerations;

import ai.yiye.agent.domain.dto.FsDynamicFilterDto;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流量来源
 */
@Slf4j
public enum FlowSource {

    /**
     * 客资来源——未知媒体
     */
    UN_KNOW(0, "未知 - 其他端口", "UN_KNOW", null),

    //----------------------------------------------- 巨量引擎：1-49 -----------------------------------------------
    /**
     * 今日头条
     */
    JIN_RI_TOU_TIAO(1, "今日头条", "JIN_RI_TOU_TIAO", Platform.OCEAN_ENGINE),

    /**
     * 头条极速版
     */
    JIN_RI_TOU_TIAO_JI_SU_BAN(2, "头条极速版", "JIN_RI_TOU_TIAO_JI_SU_BAN", Platform.OCEAN_ENGINE),

    /**
     * 抖音
     */
    DOU_YIN(3, "抖音", "DOU_YIN", Platform.OCEAN_ENGINE),

    /**
     * 抖音极速版
     */
    DOU_YIN_JI_SU_BAN(4, "抖音极速版", "DOU_YIN_JI_SU_BAN", Platform.OCEAN_ENGINE),

    /**
     * 抖音火山版
     */
    DOU_YIN_HUO_SHAN_BAN(5, "抖音火山版", "DOU_YIN_HUO_SHAN_BAN", Platform.OCEAN_ENGINE),

    /**
     * 西瓜视频
     */
    XI_GUA_SHI_PIN(6, "西瓜视频", "XI_GUA_SHI_PIN", Platform.OCEAN_ENGINE),

    /**
     * 番茄小说
     */
    FAN_QIE_XIAO_SHUO(7, "番茄小说", "FAN_QIE_XIAO_SHUO", Platform.OCEAN_ENGINE),

    /**
     * 幸福里（归纳为【穿山甲】）
     */
    XING_FU_LI(8, "幸福里", "XING_FU_LI", Platform.OCEAN_ENGINE),

    /**
     * 懂车帝（归纳为【穿山甲】）
     */
    DONG_CHE_DI(9, "懂车帝", "DONG_CHE_DI", Platform.OCEAN_ENGINE),

    /**
     * 穿山甲
     */
    CHUAN_SHAN_JIA(10, "其他", "CHUAN_SHAN_JIA", Platform.OCEAN_ENGINE),

    //----------------------------------------------- 快手：50-99 -----------------------------------------------
    /**
     * 快手
     */
    KUAI_SHOU_KWAI(50, "快手", "KUAI_SHOU_KWAI", Platform.KUAISHOU),

    /**
     * 快手极速版
     */
    KUAI_SHOU_KSNEBULA(51, "快手极速版", "KUAI_SHOU_KSNEBULA", Platform.KUAISHOU),

    /**
     * 快手 - 其他
     */
    KUAI_SHOU_OTHER(52, "其他", "KUAI_SHOU_OTHER", Platform.OCEAN_ENGINE),
    /**
     * 1.微信 （clickid =gdt_vid + UA特征参数：MicroMessenger/WeChat）
     */
    WECHAT(53, "微信", "WECHAT", Platform.TENCENT_AD),
    /**
     * 2.QQ （clickid =qz_gdt + UA特征参数：QQ）
     */
    QQ(54, "QQ", "QQ", Platform.TENCENT_AD),
    /**
     * 腾讯新闻（clickid =qz_gdt + UA特征参数：qqnews 优先）
     */
    TENCENT_NEWS(55, "腾讯新闻", "TENCENT_NEWS", Platform.TENCENT_AD),
    /**
     * 腾讯视频（clickid =qz_gdt + UA特征参数：QQLive 优先）
     */
    TENCENT_LIVE(56, "腾讯视频", "TENCENT_LIVE", Platform.TENCENT_AD),
    /**
     * 其他（腾讯）   （未识别以上UA特征的腾讯广告来源）
     */
    TENCENT_OTHER(57, "其他", "TENCENT_OTHER", Platform.TENCENT_AD),
    ;


    @EnumValue
    private final int id;

    private final String name;

    private String extName;

    private Platform platform;

    /**
     * 巨量、字节系-流量来源（对应前端下拉选择框）
     */
    public static final List<FlowSource> OCEAN_ENGINE_FLOW_SOURCE = Arrays.asList(JIN_RI_TOU_TIAO, JIN_RI_TOU_TIAO_JI_SU_BAN, DOU_YIN, DOU_YIN_JI_SU_BAN, DOU_YIN_HUO_SHAN_BAN, XI_GUA_SHI_PIN, FAN_QIE_XIAO_SHUO, CHUAN_SHAN_JIA);

    /*
     * 快手 （对应前端下拉选择框）
     */
    public static final List<FlowSource> KUAI_SHOU_FLOW_SOURCE = Arrays.asList(KUAI_SHOU_KWAI, KUAI_SHOU_KSNEBULA, KUAI_SHOU_OTHER);

    FlowSource(int id, String name, String extName, Platform platform) {
        this.id = id;
        this.name = name;
        this.extName = extName;
        this.platform = platform;
    }

    public static FlowSource getFlowSourceByUa(String ua) {
        return FlowSource.UN_KNOW;
    }

    public int getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public Platform getPlatform() {
        return this.platform;
    }

    public static String getValueById(int code) {
        for (FlowSource entity : FlowSource.values()) {
            if (entity.getId() == code) {
                return entity.getName();
            }
        }
        return null;
    }

    public static String getExtName(int id) {
        for (FlowSource entity : FlowSource.values()) {
            if (entity.id == id) {
                return entity.extName;
            }
        }
        return null;
    }

    public static FlowSource getEnumById(int id) {
        for (FlowSource entity : FlowSource.values()) {
            if (entity.getId() == id) {
                return entity;
            }
        }
        return null;
    }

    public static List<Integer> getFlowSourceByPlatform(Platform platform) {
        List<FlowSource> list = Arrays.stream(FlowSource.values()).filter(e -> Objects.equals(e.getPlatform(), platform)).collect(Collectors.toList());
        return Objects.isNull(platform) ? null : (CollectionUtils.isEmpty(list) ? null : list.stream().map(FlowSource::getId).collect(Collectors.toList()));
    }

    public static String getNamesByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return "";
        }
        List<String> names = new ArrayList<>();
        for (Integer id : ids) {
            FlowSource flowSource = FlowSource.getEnumById(id);
            names.add(Objects.isNull(flowSource) ? "" : flowSource.getName());
        }
        return names.stream().collect(Collectors.joining(","));
    }

    private static final Map<String, FlowSource> MAP = new HashMap<>();

    static {
        for (FlowSource entity : values()) {
            MAP.put(entity.toString(), entity);
        }
    }

    public static FlowSource valueOfName(String name) {
        return MAP.get(name);
    }

    /**
     * 通过访客UA特征识别端口来源（巨量原生页）
     */
    public static FlowSource getJuLiangYuanShengYeFlowSourceByUaStr(final Platform platform, String uaStr) {
        if (StringUtils.isBlank(StringUtils.trim(uaStr))) {
            return null;
        }
        uaStr = uaStr.toLowerCase();
        /*
         * 巨量引擎投放平台，UA特征分析
         */
        if (!Objects.isNull(platform) && Platform.OCEAN_ENGINE.equals(platform)) {
            //抖音：包含aweme
            if (uaStr.contains("aweme")) {
                return DOU_YIN;
            }
            //抖音极速版：包含douyin_lite
            if (uaStr.contains("douyin_lite")) {
                return DOU_YIN_JI_SU_BAN;
            }
            //抖音火山版：包含live_stream
            if (uaStr.contains("live_stream")) {
                return DOU_YIN_HUO_SHAN_BAN;
            }
            //今日头条：包含newsarticle
            if (uaStr.contains("newsarticle")) {
                return DOU_YIN_HUO_SHAN_BAN;
            }
            return UN_KNOW;
        }
        //非巨量平台，无UA特征
        return null;
    }

    public static void main(String[] args) {
        String uaStr = "Mozilla/5.0 (Linux; Android 15; V2309A Build/AP3A.240905.015.A1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.71 Mobile Safari/537.36  aweme_320800 JsSdk/1.0 NetType";
        uaStr += "/WIFI Channel/vivo_1128_64 AppName/aweme app_version/32.8.0 ByteLocale/zh-CN Region/CN AppSkin/white AppTheme/light  rifleAd/0.4.0 BytedanceWebview/d8a21c6 TTWebView/1261130065501";
        System.out.println(uaStr.contains("aweme") && !uaStr.contains("lite"));
    }

    /**
     * 通过访客UA特征识别端口来源（H5）
     */
    public static FlowSource getFlowSourceByUaStr(final Platform platform, String uaStr) {
        /*
         * 巨量引擎投放平台，UA特征分析
         */
        if (Platform.OCEAN_ENGINE.equals(platform)) {
            //今日头条：包含news不包含manyhouse、lite、tt-ok
            if (uaStr.contains("news") && (!uaStr.contains("manyhouse")) && !uaStr.contains("newslite") && !uaStr.contains("lite") && !uaStr.contains("tt-ok") && !uaStr.contains("open_news")) {
                return JIN_RI_TOU_TIAO;
            }
            //头条极速版：包含newslite或包含tt-ok
            if (uaStr.contains("newslite") || uaStr.contains("tt-ok")) {
                return JIN_RI_TOU_TIAO_JI_SU_BAN;
            }
            //抖音火山版：包含live
            if (uaStr.contains("live") || uaStr.contains("hotsoon")) {
                return DOU_YIN_HUO_SHAN_BAN;
            }
            //抖音：包含aweme不包含lite
            if (uaStr.contains("aweme") && !uaStr.contains("lite")) {
                return DOU_YIN;
            }
            //抖音极速版：同时包含aweme和lite
            if (uaStr.contains("aweme") && uaStr.contains("lite")) {
                return DOU_YIN_JI_SU_BAN;
            }


//            Mozilla/5.0 (Linux; Android 15; V2309A Build/AP3A.240905.015.A1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.71 Mobile Safari/537.36  aweme_320800 JsSdk/1.0 NetType
//            /WIFI Channel/vivo_1128_64 AppName/aweme app_version/32.8.0 ByteLocale/zh-CN Region/CN AppSkin/white AppTheme/light  rifleAd/0.4.0 BytedanceWebview/d8a21c6 TTWebView/1261130065501


            //西瓜视频：包含videoarticle
            if (uaStr.contains("videoarticle")) {
                return XI_GUA_SHI_PIN;
            }
            //幸福里：包含manyhouse、懂车帝：包含automobile
            if (uaStr.contains("manyhouse") || uaStr.contains("automobile")) {
                //return XING_FU_LI;
                return CHUAN_SHAN_JIA;
            }
            //番茄小说：包含dragon
            if (uaStr.contains("dragon") || uaStr.contains("open_news")) {
                return FAN_QIE_XIAO_SHUO;
            }
            //return UN_KNOW;
            //需求变更，地址：#26785 【线上问题】流量来源：未知 - 其他端口，应该归类为穿山甲 https://ones.yiye.ai/project/#/team/WtsduTeT/task/EFZs7UMKYb1FFH4J
            return CHUAN_SHAN_JIA;
        }
        /*
         * 快手投放平台，UA特征分析
         */
        if (Platform.KUAISHOU.equals(platform)) {
            //快手：kwai
            if (uaStr.contains("kwai")) {
                return FlowSource.KUAI_SHOU_KWAI;
            }
            //快手极速版：ksnebula
            if (uaStr.contains("ksnebula")) {
                return FlowSource.KUAI_SHOU_KWAI;
            }
            return FlowSource.KUAI_SHOU_OTHER;
        }
        //非巨量平台，无UA特征
        return null;
    }


    /**
     * 通过访客UA特征识别端口来源（H5）
     */
    public static FlowSource getFlowSourceByUaStr(final Platform platform, String uaStr, LinkedHashMap<String, List<FsDynamicFilterDto>> fsDynamicFilterMap) {
        try {
            if (StringUtils.isBlank(StringUtils.trim(uaStr))) {
                return null;
            }
            if (Objects.isNull(platform)) {
                return null;
            }
            if (CollectionUtils.isEmpty(fsDynamicFilterMap)) {
                return getFlowSourceByUaStr(platform, uaStr);
            }
            uaStr = uaStr.toLowerCase();
            List<FsDynamicFilterDto> fsDynamicFilterList = fsDynamicFilterMap.get(platform.toString());
            if (CollectionUtils.isEmpty(fsDynamicFilterList)) {
                return null;
            }
            for (FsDynamicFilterDto conStr : fsDynamicFilterList) {
                FlowSource flowSource = getFlowSoureByUaStr(platform, uaStr, conStr);
                if (!Objects.isNull(flowSource)) {
                    return flowSource;
                }
            }
        } catch (Exception ex) {
            log.error("通过访客UA特征识别端口来源异常：platform={}；uaStr={}；fsDynamicFilterMap={}；", platform, uaStr, JSONObject.toJSONString(fsDynamicFilterMap));
        }
        return null;
    }

//    public static void main(String[] args) {
//        String conStr = "{\"OCEAN_ENGINE\":[{\"sort\":1,\"result\":\"JIN_RI_TOU_TIAO\",\"operators\":\"&&\",\"condition\":[{\"method\":\"contains\",\"values\":[\"news\"]},{\"method\":\"not_contains\",\"values\":[\"manyhouse\",\"newslite\",\"lite\",\"tt-ok\"],\"thisOperators\":\"&&\"}]},{\"sort\":2,\"result\":\"JIN_RI_TOU_TIAO_JI_SU_BAN\",\"condition\":[{\"method\":\"contains\",\"values\":[\"newslite\",\"tt-ok\"],\"thisOperators\":\"||\"}]},{\"sort\":3,\"result\":\"DOU_YIN_HUO_SHAN_BAN\",\"condition\":[{\"method\":\"contains\",\"values\":[\"live\",\"hotsoon\"],\"thisOperators\":\"||\"}]},{\"sort\":4,\"result\":\"DOU_YIN\",\"operators\":\"&&\",\"condition\":[{\"method\":\"contains\",\"values\":[\"aweme\"]},{\"method\":\"not_contains\",\"values\":[\"lite\"]}]},{\"sort\":5,\"result\":\"DOU_YIN_JI_SU_BAN\",\"condition\":[{\"method\":\"contains\",\"values\":[\"aweme\",\"lite\"],\"thisOperators\":\"&&\"}]},{\"sort\":6,\"result\":\"XI_GUA_SHI_PIN\",\"condition\":[{\"method\":\"contains\",\"values\":[\"videoarticle\"]}]},{\"sort\":7,\"result\":\"CHUAN_SHAN_JIA\",\"condition\":[{\"method\":\"contains\",\"values\":[\"manyhouse\",\"automobile\"],\"thisOperators\":\"||\"}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":8,\"result\":\"FAN_QIE_XIAO_SHUO\",\"condition\":[{\"method\":\"contains\",\"values\":[\"dragon\"]}]},{\"sort\":1,\"result\":\"JIN_RI_TOU_TIAO\",\"operators\":\"&&\",\"condition\":[{\"method\":\"contains\",\"values\":[\"news\"]},{\"method\":\"not_contains\",\"values\":[\"manyhouse\",\"newslite\",\"lite\",\"tt-ok\"],\"thisOperators\":\"&&\"}]},{\"sort\":1,\"result\":\"JIN_RI_TOU_TIAO\",\"operators\":\"&&\",\"condition\":[{\"method\":\"contains\",\"values\":[\"news\"]},{\"method\":\"not_contains\",\"values\":[\"manyhouse\",\"newslite\",\"lite\",\"tt-ok\"],\"thisOperators\":\"&&\"}]},{\"sort\":1,\"result\":\"JIN_RI_TOU_TIAO\",\"operators\":\"&&\",\"condition\":[{\"method\":\"contains\",\"values\":[\"news\"]},{\"method\":\"not_contains\",\"values\":[\"manyhouse\",\"newslite\",\"lite\",\"tt-ok\"],\"thisOperators\":\"&&\"}]},{\"sort\":1,\"result\":\"JIN_RI_TOU_TIAO\",\"operators\":\"&&\",\"condition\":[{\"method\":\"contains\",\"values\":[\"news\"]},{\"method\":\"not_contains\",\"values\":[\"manyhouse\",\"newslite\",\"lite\",\"tt-ok\"],\"thisOperators\":\"&&\"}]},{\"sort\":1,\"result\":\"JIN_RI_TOU_TIAO\",\"operators\":\"&&\",\"condition\":[{\"method\":\"contains\",\"values\":[\"news\"]},{\"method\":\"not_contains\",\"values\":[\"manyhouse\",\"newslite\",\"lite\",\"tt-ok\"],\"thisOperators\":\"&&\"}]},{\"sort\":1,\"result\":\"JIN_RI_TOU_TIAO\",\"operators\":\"&&\",\"condition\":[{\"method\":\"contains\",\"values\":[\"news\"]},{\"method\":\"not_contains\",\"values\":[\"manyhouse\",\"newslite\",\"lite\",\"tt-ok\"],\"thisOperators\":\"&&\"}]},{\"sort\":1,\"result\":\"JIN_RI_TOU_TIAO\",\"operators\":\"&&\",\"condition\":[{\"method\":\"contains\",\"values\":[\"news\"]},{\"method\":\"not_contains\",\"values\":[\"manyhouse\",\"newslite\",\"lite\",\"tt-ok\"],\"thisOperators\":\"&&\"}]},{\"sort\":1,\"result\":\"JIN_RI_TOU_TIAO\",\"operators\":\"&&\",\"condition\":[{\"method\":\"contains\",\"values\":[\"news\"]},{\"method\":\"not_contains\",\"values\":[\"manyhouse\",\"newslite\",\"lite\",\"tt-ok\"],\"thisOperators\":\"&&\"}]},{\"sort\":1,\"result\":\"JIN_RI_TOU_TIAO\",\"operators\":\"&&\",\"condition\":[{\"method\":\"contains\",\"values\":[\"news\"]},{\"method\":\"not_contains\",\"values\":[\"manyhouse\",\"newslite\",\"lite\",\"tt-ok\"],\"thisOperators\":\"&&\"}]},{\"sort\":1,\"result\":\"JIN_RI_TOU_TIAO\",\"operators\":\"&&\",\"condition\":[{\"method\":\"contains\",\"values\":[\"news\"]},{\"method\":\"not_contains\",\"values\":[\"manyhouse\",\"newslite\",\"lite\",\"tt-ok\"],\"thisOperators\":\"&&\"}]},{\"sort\":9,\"result\":\"CHUAN_SHAN_JIA\",\"operators\":\"DEFAULT_LAST\",\"condition\":[]}],\"KUAISHOU\":[{\"sort\":1,\"result\":\"KUAI_SHOU_KWAI\",\"condition\":[{\"method\":\"contains\",\"values\":[\"kwai\"]}]},{\"sort\":2,\"result\":\"KUAI_SHOU_KSNEBULA\",\"condition\":[{\"method\":\"contains\",\"values\":[\"ksnebula\"]}]},{\"sort\":3,\"result\":\"KUAI_SHOU_OTHER\",\"operators\":\"DEFAULT_LAST\",\"condition\":[]}]}";
//        JSONObject jsonObject = JSONObject.parseObject(conStr);
//        LinkedHashMap<String, List<FsDynamicFilterDto>> fsDynamicFilterMap = new LinkedHashMap<>();
//        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
//            String key = entry.getKey();
//            JSONArray jsonArray = (JSONArray) entry.getValue();
//            List<FsDynamicFilterDto> filterList = jsonArray.toJavaList(FsDynamicFilterDto.class);
//            fsDynamicFilterMap.put(key, filterList);
//        }
//        long snowTime;
//        long nowTime = Instant.now().toEpochMilli();
//        for (int i = 0; i < 10000000; i++) {
//            snowTime = Instant.now().toEpochMilli();
//            String uaStr = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1";
//            FlowSource flowSource = getFlowSourceByUaStr(Platform.KUAISHOU, uaStr.toLowerCase(), fsDynamicFilterMap);
//            //OCEAN_ENGINE：count：120544 / num：10000000 = 0.0120544（ms/num）
//            //KUAISHOU：count：30573 / num：10000000 = 0.0030573（ms/num）
//            System.out.println(flowSource.getId() + "-" + flowSource.getName() + "：" + (Instant.now().toEpochMilli() - snowTime));
//        }
//        //OCEAN_ENGINE：120544ms/10000000num
//        //KUAISHOU：30573ms/10000000num
//        System.out.println((Instant.now().toEpochMilli() - nowTime));
//    }

    /**
     * 解析第一层（固定）
     */
    public static FlowSource getFlowSoureByUaStr(Platform platform, String uaStr, FsDynamicFilterDto fsDynamicFilterDto) {
        if (Objects.isNull(platform) || Objects.isNull(fsDynamicFilterDto)) {
            return null;
        }
        //返回值，默认值为当前媒体最后一条，标识符：operators=DEFAULT_LAST
        final String result = StringUtils.isBlank(fsDynamicFilterDto.getResult()) ? null : fsDynamicFilterDto.getResult();
        //当前层级逻辑运算符
        final String operators = Objects.isNull(fsDynamicFilterDto.getOperators()) ? null : fsDynamicFilterDto.getOperators();
        //判断条件组
        final List<FsDynamicFilterDto> condition = CollectionUtils.isEmpty(fsDynamicFilterDto.getCondition()) ? null : fsDynamicFilterDto.getCondition();
        //其他端口
        if (CollectionUtils.isEmpty(condition) && StringUtils.equals("DEFAULT_LAST", operators) && StringUtils.isNotBlank(result)) {
            return FlowSource.valueOfName(result);
        }
        if (CollectionUtils.isEmpty(condition)) {
            return null;
        }
        List<Boolean> parentOperatorsList = new ArrayList<>();
        for (FsDynamicFilterDto fsDto : condition) {
            //当前层级参数 - 校验
            if (StringUtils.isNotBlank(fsDto.getMethod()) && !CollectionUtils.isEmpty((Objects.isNull(fsDto.getValues()) ? null : fsDto.getValues()))) {
                Boolean checkValue = checkCondition(uaStr, fsDto);
                parentOperatorsList.add(checkValue);
                continue;
            }
            //当前层级子集参数 - 校验
            final String chOperators = fsDto.getOperators();
            final List<FsDynamicFilterDto> methodGroups = CollectionUtils.isEmpty(fsDto.getCondition()) ? null : fsDto.getCondition();
            List<Boolean> operatorsList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(methodGroups)) {
                for (FsDynamicFilterDto groupObj : methodGroups) {
                    Boolean checkValue = checkCondition(uaStr, groupObj);
                    operatorsList.add(checkValue);
                }
                Boolean checkValue = checkOperators(chOperators, operatorsList);
                parentOperatorsList.add(checkValue);
            }
        }
        if (StringUtils.isNotBlank(operators)) {
            Boolean blValue = checkOperators(operators, parentOperatorsList);
            if (blValue) {
                return FlowSource.valueOfName(result);
            }
        } else if (StringUtils.isBlank(operators) && parentOperatorsList.size() == 1 && Boolean.TRUE.equals(parentOperatorsList.get(0))) {
            return FlowSource.valueOfName(result);
        }
        return null;
    }

    /**
     * 校验组合条件
     */
    public static Boolean checkCondition(final String uaStr, final FsDynamicFilterDto fsDynamicFilterDto) {
        //当前层级校验 - 参数
        final String method = fsDynamicFilterDto.getMethod();
        final List<String> values = Objects.isNull(fsDynamicFilterDto.getValues()) ? null : fsDynamicFilterDto.getValues();
        final String thisOperators = StringUtils.isBlank(fsDynamicFilterDto.getThisOperators()) ? null : fsDynamicFilterDto.getThisOperators();
        //当前层级子集校验 - 参数
        final String operators = fsDynamicFilterDto.getOperators();
        final List<FsDynamicFilterDto>  methodGroups = Objects.isNull(fsDynamicFilterDto.getCondition()) ? null : fsDynamicFilterDto.getCondition();
        //不包含子集条件组、包含判断方法、包含判断值，进行校验
        List<Boolean> operatorsList = new ArrayList<>();
        if (StringUtils.isNotBlank(method) && !CollectionUtils.isEmpty(values)) {
            Boolean checkValue = executeMethod(uaStr, method, values, thisOperators);
            operatorsList.add(checkValue);
        }
        //包含子集条件组、包含逻辑判断符
        if (!CollectionUtils.isEmpty(methodGroups)) {
            for (FsDynamicFilterDto groupObj : methodGroups) {
                Boolean checkValue = checkCondition(uaStr, groupObj);
                operatorsList.add(checkValue);
            }
        }
        return checkOperators(operators, operatorsList);
    }

    //条件映射判断
    public static Boolean executeMethod(String uaStr, String method, List<String> values, String thisOperators) {
        if (CollectionUtils.isEmpty(values)) {
            return Boolean.FALSE;
        }
        int valuesSize = values.size();
        //判断值 == 1条，大于1条才用得到【thisOperators】逻辑运算
        boolean valuesSizeIsOne = valuesSize == 1;
        switch (method) {
            //包含
            case "contains":
                return getResultValue(valuesSize, valuesSizeIsOne, thisOperators, values.stream().filter(uaStr::contains).collect(Collectors.toList()));
            //不包含
            case "not_contains":
                return getResultValue(valuesSize, valuesSizeIsOne, thisOperators, values.stream().filter(e -> !uaStr.contains(e)).collect(Collectors.toList()));
            //相等
            case "equals":
            return getResultValue(valuesSize, valuesSizeIsOne, thisOperators, values.stream().filter(e -> StringUtils.equals(uaStr, e)).collect(Collectors.toList()));
            //开头相等
            case "startswith":
                return getResultValue(valuesSize, valuesSizeIsOne, thisOperators, values.stream().filter(uaStr::startsWith).collect(Collectors.toList()));
            //结尾相等
            case "endsWith":
                return getResultValue(valuesSize, valuesSizeIsOne, thisOperators, values.stream().filter(uaStr::endsWith).collect(Collectors.toList()));
            default:
                return Boolean.FALSE;
        }
    }

    public static Boolean getResultValue(int valuesSize, boolean valuesSizeIsOne, String thisOperators, List<String> list) {
        if (valuesSizeIsOne && !CollectionUtils.isEmpty(list)) {
            //有且只有一条校验值，不做逻辑判断，只要不为空，就是true
            return Boolean.TRUE;
        }
        if (StringUtils.equals("||", thisOperators)) {
            //或者关系，过滤出的值符合任意，则为true
            return !CollectionUtils.isEmpty(list);
        }
        if (StringUtils.equals("&&", thisOperators)) {
            //并且关系，过滤出的值必须与判断值条数相等，否则为false
            return list.size() == valuesSize;
        }
        return Boolean.FALSE;
    }

    /**
     * 逻辑映射运算
     */
    public static Boolean checkOperators(String operators, List<Boolean> operatorsList) {
        //不存在不填写values场景，除非JSON结构写错了，要么就没给值
        if (CollectionUtils.isEmpty(operatorsList)) {
            return Boolean.FALSE;
        }
        //并且运算：取出的值只要有任意一个FALSE，即为不满足
        if (StringUtils.equals("&&", operators)) {
            return operatorsList.stream().filter(Boolean.FALSE::equals).findFirst().orElse(Boolean.TRUE);
        }
        //或者运算：取出的值只要有任意一个TRUE，即为满足
        if (StringUtils.equals("||", operators)) {
            return operatorsList.stream().filter(Boolean.TRUE::equals).findFirst().orElse(Boolean.FALSE);
        }
        if (StringUtils.isBlank(operators) && operatorsList.size() == 1 && Boolean.TRUE.equals(operatorsList.get(0))) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

}
