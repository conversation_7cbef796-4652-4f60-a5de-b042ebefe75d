package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/15 10:01
 */
@TableName("marketing_data_ocean_inter_behavior_category")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OceanEngineCategory implements Serializable {

    @TableField(exist = false)
    public static final List<String> actionScenes1 = new ArrayList<String>(Arrays.asList("E-COMMERCE"));//1
    @TableField(exist = false)
    public static final List<String> actionScenes2 = new ArrayList<String>(Arrays.asList("NEWS"));
    @TableField(exist = false)
    public static final List<String> actionScenes3 = new ArrayList<String>(Arrays.asList("APP"));
    @TableField(exist = false)
    public static final List<String> actionScenes4 = new ArrayList<String>(Arrays.asList("E-COMMERCE", "NEWS"));
    @TableField(exist = false)
    public static final List<String> actionScenes5 = new ArrayList<String>(Arrays.asList("E-COMMERCE", "APP"));
    @TableField(exist = false)
    public static final List<String> actionScenes6 = new ArrayList<String>(Arrays.asList("NEWS", "APP"));
    @TableField(exist = false)
    public static final List<String> actionScenes7 = new ArrayList<String>(Arrays.asList("E-COMMERCE", "NEWS", "APP"));
    @TableField(exist = false)
    public static final List<List<String>> actionScenes = new ArrayList();
    @TableField(exist = false)
    public static final Integer[] actionDaysList = new Integer[]{7, 15, 30, 60, 90, 180, 365};
    @TableField(exist = false)
    public static final String[] CONFLICTS = new String[]{"action_scene", "action_days", "category_id"};

    static {
        actionScenes.add(actionScenes1);
        actionScenes.add(actionScenes2);
        actionScenes.add(actionScenes3);
        actionScenes.add(actionScenes4);
        actionScenes.add(actionScenes5);
        actionScenes.add(actionScenes6);
        actionScenes.add(actionScenes7);
    }

    private Long id;
    private String name;
    private Long parentId = 0L;
    private Long categoryId;
    private Integer platformId;
    private String categoryPath;
    private String actionScene;
    private Integer actionDays;
    private Integer type;
    private String numStr;
    private Instant createdAt = Instant.now();
    private Instant updatedAt = Instant.now();
    @TableField(exist = false)
    private List<OceanEngineCategory> children;




    private OceanEngineCategory(Builder builder) {

        this.name = builder.name;
        this.id = builder.id;
        this.parentId = builder.parentId;
        this.categoryId = builder.categoryId;
        this.platformId = builder.platformId;
        this.categoryPath = builder.categoryPath;
        this.actionScene = builder.actionScene;
        this.actionDays = builder.actionDays;
        this.numStr = builder.numStr;
    }

    //构建一个build
    public static class Builder {
        private Long id;
        private String name;
        private Long parentId = 0L;
        private Long categoryId;
        private Integer platformId;
        private String categoryPath;
        private String actionScene;
        private Integer actionDays;
        private Integer type;
        private String numStr;
        private Instant createdAt = Instant.now();
        private Instant updatedAt = Instant.now();

       public Builder name(String name){
           this.name=name;
           return this;
       }
        public Builder categoryId(Long categoryId){
            this.categoryId=categoryId;
            return this;
        }
        public Builder parentId(Long parentId){
            this.parentId=parentId;
            return this;
        }
        public Builder id(Long id){
            this.id=id;
            return this;
        }
        public Builder platformId(Integer platformId){
            this.platformId=platformId;
            return this;
        }
        public Builder categoryPath(String categoryPath){
            this.categoryPath=categoryPath;
            return this;
        }
        public Builder actionScene(String actionScene){
            this.actionScene=actionScene;
            return this;
        }
        public Builder actionDays(Integer actionDays){
            this.actionDays=actionDays;
            return this;
        }
        public Builder type(Integer type){
            this.type=type;
            return this;
        }
        public Builder numStr(String numStr){
            this.numStr=numStr;
            return this;
        }
        //构建一个实体
        public OceanEngineCategory build() {
            return new OceanEngineCategory(this);
        }

    }
}
