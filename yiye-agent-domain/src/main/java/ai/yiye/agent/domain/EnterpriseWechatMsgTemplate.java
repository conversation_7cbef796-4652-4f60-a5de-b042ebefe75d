package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.OpenType;
import ai.yiye.agent.domain.enumerations.RobotCustomerMsgType;
import ai.yiye.agent.domain.enumerations.WelcomeCodeType;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2022/6/13 15:13
 * 模板
 */
@Data
@TableName("enterprise_wechat_robot_customer_msg_template")
public class EnterpriseWechatMsgTemplate implements Serializable {
    private Long id;
    /**
     * （类型：text-文本消息  msgmenu-菜单消息  view-超链接菜单(子级)  click-回复菜单(子级类型)  miniprogram-小程序菜单(子级类型)
     * head_content-消息内容:起始文本(子级类型)  tail_content-消息内容:结束文本(子级类型)  click_text-回复菜单-文本消息  click_customer_qr_code_picture-回复菜单-客服二维码图片）'
     */
    private RobotCustomerMsgType msgType;
    /**
     * （父级消息id：0-顶级  xxx-n级父级id）
     */
    private Long parentId;
    /**
     * 存消息内容：msg_type为【text-文本消息、head_content-消息内容:起始文本(子级类型)、tail_content-消息内容:结束文本(子级类型)】时生效；
     * 存标题：msg_type为【view-超链接菜单(子级)、miniprogram-小程序菜单(子级类型)、head_content-消息内容:起始文本(子级类型)】时生效)
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject content;
    /**
     * 打开超链接类型（msg_type=view时生效）：open_channel-打开渠道链接  open_wechat_official_account_article_page-公众号历史文章页
     */
    private OpenType openType;
    /**
     * （落地页id（msg_type=view时生效））
     */
    private Long landingPageId;
    /**
     * （落地页渠道id（msg_type=view时生效））
     */
    private Long channelId;
    /**
     * （小程序appid（msg_type=miniprogram时生效））
     */
    private String wechatAppletAppid;
    /**
     * （小程序路径（msg_type=miniprogram时生效））
     */
    private String wechatAppletPath;
    /**
     * （排序值，默认为0，数值越大越靠后）
     */
    private Integer sort;
    private Instant createdAt;
    private Instant updatedAt;
    private Long robotCustomerId;
    /**
     * 是否是欢迎语
     */
    private WelcomeCodeType welcomeCodeType;
}
