package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.CustomerFieldPageType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: wang<PERSON><PERSON>
 * @time: 2021/5/24 16:48
 */
@Data
@TableName("marketing_advertise_screen_field")
public class AdvertiserScreenField implements Serializable {
    @TableId
    private Long id;

    private Long platformId;

    /**
     * 父类型
     */
    private CustomerFieldPageType customerFieldPageType;

    /**
     * 子类型
     */
    private CustomerFieldPageType type;

    /**
     * 编号
     */
    private Integer no;

    /**
     * 字段命名
     */
    private String field;
    /**
     * 描述
     */
    private String describe;

    /**
     * 单位
     */
    private String unit;
}
