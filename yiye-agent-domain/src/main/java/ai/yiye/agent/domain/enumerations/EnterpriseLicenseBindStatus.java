package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/5/26 15:49
 * @Version 1.0
 */
@Getter
public enum EnterpriseLicenseBindStatus implements IEnum<Integer> {
    UNBIND(1,"未绑定"),
    BIND(2,"已绑定"),
    ;
    private final Integer code;

    private final String msg;

    EnterpriseLicenseBindStatus(Integer code,String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer getValue() {
        return this.code;
    }
}
