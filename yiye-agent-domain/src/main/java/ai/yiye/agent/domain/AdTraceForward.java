package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AdTraceType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@TableName("marketing_ad_trace_forward")
public class AdTraceForward {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String linkId;

    private String url;

    private AdTraceType type;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
