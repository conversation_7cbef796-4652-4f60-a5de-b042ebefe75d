package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.utils.Compare;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * 微信客服管理 - 表
 */
@Data
@TableName("landing_page_wechat_customer_service")
public class LandingPageWechatCustomerService implements Serializable {
    //事实上这个有唯一键，应该是客服id与名称

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * pmp项目id，关联表：marketing_advertiser_account_group.id
     */
    private Long advertiserAccountGroupId;

    /**
     * 企业微信id，关联表字段：enterprise_wechats.corpid
     */
    private String corpId;

    /**
     * 企业微信 - 名称
     */
    @Compare(compareField = CompareLogFieldEnum.CUSTOMER_SERVICE_CHANGE_NAME)
    private String wechatUserName;

    /**
     * 对应企业成员账号（userid）
     */
    @Compare(compareField= CompareLogFieldEnum.CUSTOMER_SERVICE_SET_MEMBER)
    private String wechatUserId;

    /**
     * 总计返回总条数
     */
    @TableField(exist = false)
    private Long total;

    /**
     * 二维码 - 权重
     */
    @Compare(compareField= CompareLogFieldEnum.CUSTOMER_SERVICE_UPDATE_WIDGET)
    private Integer qrCodeWeight;

    /**
     * 二维码图片 - 链接地址
     */
    @Compare(compareField= CompareLogFieldEnum.CUSTOMER_SERVICE_CHANGE_IMAGE_CODE)
    private String qrCodeImgUrl;

    /**
     * 二维码图片对应的素材ID
     */
    private Long qrCodeImgMaterialId;


    /**
     * 二维码类型
     */
    private LandingPageWechatCustomerServiceQRCodeType qrCodeType;

    /**
     * 二维码免验证状态
     */
    private LandingPageWechatCustomerContactVerifyStatus qrCodeVerifyStatus;

    /**
     * 二维码configRecordId
     */
    private Long qrCodeConfigRecordId;

    /**
     * 企业微信 - 可见范围 - 成员名称
     */
    private String wechatName;

    /**
     * 企业微信 - 可见范围 - 成员手机号
     */
    private String wechatMobile;

    /**
     * 邮箱
     */
    private String wechatEmail;

    /**
     * 企业微信 - 可见范围 - 成员头像url
     */
    private String wechatAvatar;

    /**
     * 上线状态
     */
    @Compare(compareField= CompareLogFieldEnum.CUSTOMER_SERVICE_ONLINE)
    private OnlineStatusType onlineStatus;

    /**
     * 自动化规则状态
     */
    private AutoRuleStatus autoRuleStatus;

    /**
     * 异常监测状态
     */
    private AbnormalMonitorStatus abnormalMonitorStatus;

    /**
     * 异常监测触发下线后不支持自动上线
     */
    private SwitchStatus notSupportAutoOnline;

    /**
     * 下线后支持自动化上线状态
     */
    private SwitchStatus supportAutoOnlineStatus;

    /**
     * 下线后支持自动化上线
     */
    private SupportAutoOnline supportAutoOnline;

    /**
     * 指定时间点上线
     */
    private String specifyOnlineTime;

    /**
     * 下线状态
     */
    private OfflineStatus offlineStatus;

    /**
     * 下线时间
     */
    private Instant offlineTime;

    /**
     * 最后一次变更前的记录
     */
    private String recordBeforeChange;

    /**
     * 明文的userid
     */
    private String plaintextWechatUserId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 创建时间 - 时间戳
     */
    @TableField(exist = false)
    private Long createdAtTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 二维码长按识别数（长按二维码识别数）
     */
    @TableField(exist = false)
    private Long identifyQrCodeNum;

    /**
     * 分组id
     */
    @TableField(exist = false)
    private Long wechatCustomerServiceGroupId;

    /**
     * 分组id（编辑显示）
     */
    @TableField(exist = false)
    private String wechatCustomerServiceGroupIds;

    /**
     * 分组名称
     */
    @TableField(exist = false)
    private String groupNames;

    /**
     * 成功添加企业微信率
     */
    @TableField(exist = false)
    private String addWorkWechatRate;

    /**
     * 二维码展示数
     */
    @TableField(exist = false)
    private Long qrCodeShowNum;

    /**
     * 二维码识别率（二维码长按识别率）
     */
    @TableField(exist = false)
    private String qrcodeIdentityRate;

    /**
     * 一叶页面添加企业微信数（落地页链路成功加企业微信数）
     */
    @TableField(exist = false)
    private Long landAddWorkWechatNum;

    /**
     * 其他来源添加企业微信数（其它方式成功加企业微信数）
     */
    @TableField(exist = false)
    private Long otherAddWorkWechatNum;

    /**
     * 成功添加企业微信数
     */
    @TableField(exist = false)
    private Long addWorkWechatNum;

    /**
     * 公众号内二维码发送次数
     */
    @TableField(exist = false)
    private Long officialAccountQrCodeSendNum;


    /**
     * 微信客服在对应分组里面的自定义排序编号
     */
    @TableField(exist = false)
    private Integer sortNum;


    /**
     * 公众号内二维码发送成功添加企业微信数
     */
    @TableField(exist = false)
    private Long officialAccountQrCodeAddWorkWechatNum;

    /**
     * 自动化规则
     */
    @TableField(exist = false)
    private List<LandingPageWechatCustomerServiceAutoRule> autoRule;
    /**
     * 自动化规则内容
     */
    @TableField(exist = false)
    private List<String> autoRuleContent;


    /**
     * 异常监测
     */
    @TableField(exist = false)
    private List<LandingPageWechatCustomerServiceAbnormalMonitor> abnormalMonitor;

    @TableField(exist = false)
    //对应企业微信类型
    private EnterpriseWechatType enterpriseWechatType;

    /**
     * 对应企业成员账号
     */
    @TableField(exist = false)
    //enterprisewechatname
    private String enterpriseWechatName;

    //================= 1.202.0 ===================

    /**
     * 获客助手链接名称
     */
    private String wechatCustomerAcquisitionLinkName;

    /**
     * 获客助手链接类型 0:默认 1:自定义
     */
    private AcquisitionChooseEnum acquisitionChooseEnum;

    /**
     * 企微的获客链接id
     */
    private String wechatCustomerAcquisitionLinkId;

    /**
     * 企微获客链接
     */
    private String wechatCustomerAcquisitionLink;

    /**
     * 企微获客链接状态 0:无 1:生成中 2:正常 3:异常
     */
    private WechatCustomerAcquisitionLinkStatus wechatCustomerAcquisitionLinkStatus;

    /**
     * 企微获客链接创建失败原因
     */
    private String wechatCustomerAcquisitionLinkReason;

    /**
     * 企微获客链接是否验证 0:否 1:是
     */
    private SwitchStatus wechatCustomerAcquisitionLinkVerify;

    //联系我二维码生成状态 - 动态渠道二维码（页面加粉）：0:未生成，1:生成中,2:已生成,3:创建失败
    private LandingPageWechatCustomerContactStatus landingPageWechatCustomerContactStatus;

    //联系我二维码链接
    private String landingPageWechatCustomerContactQrCode;

    //联系我 二维码链接配置ID-修改删除时使用
    private String landingPageWechatCustomerContactConfigId;

    //联系我二维码创建失败原因
    private String landingPageWechatCustomerContactFailureReason;

    /**
     * 添加客服是否验证
     */
    private LandingPageWechatCustomerContactVerifyStatus landingPageWechatCustomerContactVerify;



    //联系我二维码生成状态 - 动态渠道二维码（公众号内加粉）：0:未生成，1:生成中,2:已生成,3:创建失败
    private LandingPageWechatCustomerContactStatus officialWechatCustomerContactStatus;

    //联系我二维码链接
    private String officialWechatCustomerContactQrCode;

    //公众号内渠道联系我 二维码最终合成图片url
    private String officialWechatCustomerContactBackgroundUrl;

    //公众号内渠道联系我 二维码最终合成图片七牛云path
    private String officialWechatCustomerContactQiniuPath;


    //联系我 二维码链接配置ID-修改删除时使用
    private String officialWechatCustomerContactConfigId;

    //联系我二维码创建失败原因
    private String officialWechatCustomerContactFailureReason;

    /**
     * 添加客服是否验证
     */
    private LandingPageWechatCustomerContactVerifyStatus officialWechatCustomerContactVerify;


    /**
     * 公众号渠道二维码绑定的公众号appid（单人活码）
     */
    private String officialWechatCustomerContactAppId;

    //同主体公众号渠道二维码参数
    private String officialWechatCustomerContactState;

    //同主体公众号渠道二维码上传素材id
    private String officialWechatCustomerContactMaterialId;

    /**
     * 公众号内加粉设置 0:相同主体 1:不同主体
     */
    private OfficialWechatCustomerSubjectType officialWechatCustomerSubjectType;

    /**
     * 公众号渠道二维码绑定的公众号名称
     */
    private String officialWechatCustomerContactAppNickName;

    //渠道二维码（微信客服机器人内加粉）生成状态：0:未生成，1:生成中,2:已生成,3:创建失败
    private LandingPageWechatCustomerContactStatus robotCustomerContactStatus;


    //动态渠道二维码（微信客服机器人内加粉）生成状态：0:未生成，1:生成中,2:已生成,3:创建失败
    private LandingPageWechatCustomerContactStatus robotCustomerDynamicContactStatus;

    //渠道二维码（微信客服机器人内加粉）链接
    private String robotCustomerContactQrCode;


    //动态渠道二维码（微信客服机器人内加粉）链接
    private String robotCustomerDynamicContactQrCode;

    //渠道二维码（微信客服机器人内加粉）最终合成图片url
    private String robotCustomerContactBackgroundUrl;

    //动态渠道二维码（微信客服机器人内加粉）最终合成图片url
    private String robotCustomerDynamicContactBackgroundUrl;

    //渠道二维码（微信客服机器人内加粉）最终合成图片七牛云path
    private String robotCustomerContactQiniuPath;

    //动态渠道二维码（微信客服机器人内加粉）最终合成图片七牛云path
    private String robotCustomerDynamicContactQiniuPath;

    //渠道二维码（微信客服机器人内加粉）链接配置ID-修改删除时使用
    private String robotCustomerContactConfigId;

    //动态渠道二维码（微信客服机器人内加粉）链接配置ID-修改删除时使用
    private String robotCustomerDynamicContactConfigId;

    //渠道二维码（微信客服机器人内加粉）创建失败原因
    private String robotCustomerContactFailureReason;

    //动态渠道二维码（微信客服机器人内加粉）创建失败原因
    private String robotCustomerDynamicContactFailureReason;

    //渠道二维码（微信客服机器人内加粉）添加客服是否验证
    private LandingPageWechatCustomerContactVerifyStatus robotCustomerContactVerify;

    //动态渠道二维码（微信客服机器人内加粉）添加客服是否验证
    private LandingPageWechatCustomerContactVerifyStatus robotCustomerDynamicContactVerify;


    //动态动态渠道二维码（微信客服机器人内加粉）参数
    private String robotCustomerDynamicContactState;

    //渠道二维码（微信客服机器人内加粉）参数
    private String robotCustomerContactState;

    //渠道二维码（微信客服机器人内加粉）上传素材id
    private Long robotCustomerContactMaterialId;

    //动态渠道二维码（微信客服机器人内加粉）上传素材id
    private Long robotCustomerDynamicContactMaterialId;

    /**
     * 部门id
     */
    private Long[] departmentId;

    /**
     * 部门名称
     */
    private String[] departmentName;

    /**
     * 上线行为状态
     */
    private OnlineActionStatus onlineActionStatus;

    /**
     * 接口许可状态
     */
    private LicenseStatus licenseStatus;

    /**
     * 接口许可到期时间
     */
    private Instant licenseExpireTime;

    @TableField(exist = false)
    private List<String> abnormalMonitorContent;

    @TableField(exist = false)
    private String supportAutoOnlineContent;

    /**
     * 最新设置的绑定公众号昵称
     */
    @TableField(exist = false)
    private String officialNickName;

    /**
     * 最新设置的绑定公众号appid
     */
    @TableField(exist = false)
    private String officialAppId;

    /**
     * 最新设置的绑定公众号头像
     */
    @TableField(exist = false)
    private String officialHeadImg;

    /**
     * 最新配置的公众号内加粉设置 0:相同主体 1:不同主体
     */
    @TableField(exist = false)
    private OfficialWechatCustomerSubjectType officialWechatSubjectType;

    /**
     * 企业微信备注
     */
    @TableField(exist = false)
    private String enterpriseWechatRemark;

    /**
     * 联系我二维码背景图
     */
    @TableField(exist = false)
    private String landingPageWechatCustomerContactQrCodeBackgroundUrl;

    /**
     * 公众号内渠道二维码是否可以创建
     */
    @TableField(exist = false)
    private BaseStatusEnum officialProhibitFlag;


    public static JSONObject getOperDescJsonBySn(String fieldKey, String before, String after){
        JSONObject jsonObject = new JSONObject();

        //当他为二维码图片的时候，这里需要构建一个JSONOBJECTG
        if(fieldKey.equals("qrCodeImgUrl")){
            jsonObject.put("type","qrCodeImage");
            jsonObject.put("before",before);
            jsonObject.put("after",after);
        }else{
            jsonObject.put("type","String");
            jsonObject.put("before",before);
            jsonObject.put("after",after);
        }
        return jsonObject;
    }

    public static String getSupportAutoOnlineCompareString(LandingPageWechatCustomerService customerService) {
        if (SwitchStatus.OPEN.equals(customerService.getNotSupportAutoOnline())) {
            if (!SwitchStatus.OPEN.equals(customerService.getSupportAutoOnlineStatus())) {
                return "（客服一旦触发异常监测将不支持自动上线）";
            }
            if (SupportAutoOnline.AUTO_RULE.equals(customerService.getSupportAutoOnline())) {
                return "（客服一旦触发异常监测将作下线处理，支持自动上线）";
            }
            if (SupportAutoOnline.SPECIFY_TIME.equals(customerService.getSupportAutoOnline()) && StringUtils.isNotBlank(customerService.getSpecifyOnlineTime())) {
                return "（客服一旦触发异常监测将作下线处理，支持触发异常下线后" + customerService.getSpecifyOnlineTime() + "点自动上线）";
            }
        }
        return "";
    }

}
