package ai.yiye.agent.domain;

import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * @Description: 广告主id-按天统计表实体
 * @Author: Zhou<PERSON>i<PERSON>uan
 * @Date: 2020/10/22 9:08
 */
@Data
@TableName(value = "marketing_data_advertiser_account_group_day_report_ad")
public class AdvertiserAccountGroupDayReport extends AbstractMarketingData implements Serializable {
    /**主键*/
    @TableId(type = IdType.AUTO)
    private Long id;

    /**项目id*/
    private Long advertiserAccountGroupId;

    /**广告动作时间*/
    private Instant dayTime;

    /**转化数*/
    private Long convertNum;

    /**曝光数*/
    private Long viewNum;

    /**点击数*/
    private Long clickNum;

    /**花费*/
    private Long cost;
    /**深度转化数*/
    private Long deepConvertNum;
    /**
     * 落地页pv数
     */
    private Long landingPagePv;

}
