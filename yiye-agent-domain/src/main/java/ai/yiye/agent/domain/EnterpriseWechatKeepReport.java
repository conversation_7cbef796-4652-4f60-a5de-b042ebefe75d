package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 企微留存报表
 */
@Data
@TableName("enterprise_wechat_keep_report")
public class EnterpriseWechatKeepReport implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计日期
     */
    private Instant statisticDate;

    /**
     * 企业微信corpId
     */
    private String corpId;

    /**
     * 当日新增客户数
     */
    private Long addCustomerNum;

    /**
     * 第一天留存数
     */
    private Long keepNum1day;

    private Long keepNum2day;

    private Long keepNum3day;

    private Long keepNum4day;

    private Long keepNum5day;

    private Long keepNum6day;

    private Long keepNum7day;

    private Long keepNum8day;

    private Long keepNum9day;

    private Long keepNum10day;

    private Long keepNum11day;

    private Long keepNum12day;

    private Long keepNum13day;

    private Long keepNum14day;

    private Long keepNum15day;

    private Long keepNum16day;

    private Long keepNum17day;

    private Long keepNum18day;

    private Long keepNum19day;

    private Long keepNum20day;

    private Long keepNum21day;

    private Long keepNum22day;

    private Long keepNum23day;

    private Long keepNum24day;

    private Long keepNum25day;

    private Long keepNum26day;

    private Long keepNum27day;

    private Long keepNum28day;

    private Long keepNum29day;

    private Long keepNum30day;

    /**

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
