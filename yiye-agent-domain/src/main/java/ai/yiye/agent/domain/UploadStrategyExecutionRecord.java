package ai.yiye.agent.domain;

import ai.yiye.agent.domain.AdvertiserAccount;
import ai.yiye.agent.domain.UploadSonStrategy;
import ai.yiye.agent.domain.enumerations.Platform;
import ai.yiye.agent.domain.enumerations.UploadSonStrategyOperateType;
import ai.yiye.agent.domain.enumerations.UploadSonStrategyRatioEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * 上报策略 - 执行记录表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_upload_strategy_execution_record")
public class UploadStrategyExecutionRecord implements Serializable {

    /**主键*/
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 上报媒体（平台）
     */
    private Platform platformId;

    /**
     * 投放账户id（媒体方）
     */
    private String accountId;

    /**
     * 上报策略ID
     */
    private Long uploadStrategyId;

    /**
     * 腾讯：广告组 id / 巨量引擎：广告计划ID
     */
    private Long adgroupId;

    /**
     * 广告出价 - 调价比例
     */

    private BigDecimal bidAmountTuneUpRatio;

    /**
     * 广告出价 - 调价值
     */
    private BigDecimal bidAmountTuneUpValue;

    /**
     * 广告出价 - 调整次数
     */
    @TableField(exist = false)
    private Integer bidAmountTuneUpAdjustCount;

    /**
     * 深度目标出价 - 调价比例
     */
    private BigDecimal deepConversionBehaviorBidTuneUpRatio;

    /**
     * 深度目标出价 - 调价值
     */
    private BigDecimal deepConversionBehaviorBidTuneUpValue;

    /**
     * 深度目标出价 - 调整次数
     */
    @TableField(exist = false)
    private Integer deepConversionBehaviorBidTuneUpAdjustCount;

    /**
     * 上报比例
     */
    private BigDecimal uploadUpRatio = UploadSonStrategyRatioEnum.ONE_HUNDRED.getValue();

    /**
     * 是否包含后链路条件
     */
    private Boolean containBackLinkCondition;

    /**
     * 执行记录类型
     */
    private UploadSonStrategyOperateType recordType;

    /**
     * 执行记录状态：-1:执行失败  1:执行成功
     */
    private Integer statusCode;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 原始查询出的【上报子策略】集合数目
     */
    @TableField(exist = false)
    private int uploadSonStrategyListSize;

    /**
     * 最终符合条件的【上报子策略】数目
     */
    @TableField(exist = false)
    private int newUploadSonStrategyListSize;

    /**
     * 用户对象
     */
    @TableField(exist = false)
    private AdvertiserAccount advertiserAccount;

    /**
     *
     */
    @TableField(exist = false)
    private Boolean need_bid_amount_tune_up_adjust = true;

    /**
     *
     */
    @TableField(exist = false)
    private Boolean need_deep_conversion_behavior_bid_tune_up_adjust = true;

    /**
     * 超出时间范围（3天）
     */
    @TableField(exist = false)
    private Boolean outOfTimeRange = false;

}
