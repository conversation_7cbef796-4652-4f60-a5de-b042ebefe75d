package ai.yiye.agent.domain.enumerations;

import lombok.Getter;

/**
 * @Author：lilidong
 * @name：ExportEnum
 * @Date：2023/2/15 14:35
 */
@Getter
public enum ExportEnum {

    FINISHED(0, "导出任务已经结束"),

    NOT_EXIST(1, "任务不存在"),

    SUCCESS(2, "取消成功");
    private int code;

    private String msg;

    ExportEnum() {
    }

    ExportEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
