package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.CalibreTypeEnum;
import ai.yiye.agent.domain.enumerations.Platform;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("advertise_backlink_statistic_report")
public class AdvertiseBacklinkStatisticReport {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 投放平台
     */
    private Platform platformId;
    /**
     * 广告id
     */
    private String promotionId;
    /**
     * 项目id
     */
    private String projectId;

    /**
     * 广告账户ID
     */
    private String accountId;
    /**
     * 统计时间
     */
    private Instant statisticTime;
    /**
     * pv次数
     */
    private Long pageViewNum;
    /**
     * 企业推pv次数
     */
    private Long qiyetuiPageViewNum;
    /**
     * 表单提交数
     */
    private Long fillCountNum;
    /**
     * 表单提交率
     */
    private Double fillCountRate;
    /**
     * 订单提交数
     */
    private Long orderNum;
    /**
     * 订单提交率
     */
    private Double orderCountRate;
    /**
     * 订单完成数
     */
    private Long orderFinishNum;
    /**
     * 订单完成率
     */
    private Double orderFinishRate;
    /**
     * 订单成交金额
     */
    private Double orderTransactionAmount;
    /**
     * 长按二维码识别数(微信/企业微信)
     */
    private Long identifyQrCodeNum;
    /**
     * 长按二维码识别率(微信/企业微信)
     */
    private Double identifyQrCodeRate;
    /**
     * 成功添加企业微信数
     */
    private Long addWorkWechatNum;
    /**
     * 成功添加企业微信率
     */
    private Double addWorkWechatRate;
    /**
     * 长按二维码识别数(公众号)
     */
    private Long officialIdentifyQrCodeNum;
    /**
     * 长按二维码识别率(公众号)
     */
    private Double officialIdentifyQrCodeRate;
    /**
     * 微信公众号关注数
     */
    private Long followOfficialAccountNum;
    /**
     * 微信公众号关注率
     */
    private Double followOfficialAccountRate;
    /**
     * 电商商品购买数
     */
    private Long onlineShopBuyGoodsSuccessNum;
    /**
     * 电商商品购买率
     */
    private Double onlineShopBuyGoodsSuccessRate;
    /**
     * 电商商品成交金额
     */
    private Double onlineShopBuyGoodsSuccessAmount;

    /**
     * 二维码发送数 （客服机器人）
     */
    private Long sendQrCodeOrImgNum;
    /**
     * 小程序卡片发送数（客服机器人）
     */
    private Long sendMiniProgramCardNum;
    /**
     * 618 红包跳转数（客服机器人）
     */
    private Long jumpToSuperRedEnvelopeNum;
    /**
     * 发送欢迎语数（客服机器人）
     */
    private Long successSendWelcomeMsgNum;
    /**
     * 进入会话数（客服机器人）
     */
    private Long intoWechatCustomerServiceSessionNum;

    /**
     * 首次开口数
     */
    private Long startOpenChatNum;



    /**
     * 三次开口数
     */
    private Long thirdOpenChatNum;



    /**
     * 5次开口数
     */
    private Long fifthOpenChatNum;



    /**
     * 10次开口数
     */
    private Long tenthOpenChatNum;
    /**
     * 统计口径
     */
    private CalibreTypeEnum statisticCaliber;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
