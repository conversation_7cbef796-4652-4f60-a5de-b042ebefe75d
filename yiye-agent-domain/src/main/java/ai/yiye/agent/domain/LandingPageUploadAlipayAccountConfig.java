package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 落地页上报配置 - 支付宝数字推广平台上报账户
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_upload_alipay_account_config", autoResultMap = true)
public class LandingPageUploadAlipayAccountConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * pmp项目id
     */
    private Long advertiserAccountGroupId;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用私钥
     */
    private String privateKey;

    /**
     * 应用公钥
     */
    private String publicKey;

    /**
     * 支付宝公钥
     */
    private String alipayPublicKey;

    /**
     * biz_token，点击查看如何获取 新窗口打开链接：https://adpub.alipay.com/lark/adrlark/fp19upg7vze30od3
     */
    private String bizToken;

    /**
     * principalTag，点击查看如何获取 新窗口打开链接：https://adpub.alipay.com/lark/adrlark/sbk3vetg6ki5m4r8
     */
    private String principalTag;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
