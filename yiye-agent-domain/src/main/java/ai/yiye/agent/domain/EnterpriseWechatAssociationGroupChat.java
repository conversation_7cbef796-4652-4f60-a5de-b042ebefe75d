package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AssociationAuthStatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 加群归因关联企微
 */
@TableName("enterprise_wechat_association_group_chat")
@Data
public class EnterpriseWechatAssociationGroupChat implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企微corpid
     */
    private String corpId;

    /**
     * 关联的企微corpid
     */
    private String associationCorpId;

    /**
     * 关联的企微名称
     */
    private String associationCorpName;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    @TableField(exist = false)
    private AssociationAuthStatusEnum authStatus;

}
