package ai.yiye.agent.domain.landingpage;

import ai.yiye.agent.domain.enumerations.CheckStatus;
import ai.yiye.agent.domain.enumerations.EnterpriseWechatGroupChatStatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 企业微信-群聊表
 */
@Data
@TableName("enterprise_wechat_group_chat")
public class EnterpriseWechatGroupChat implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业微信id
     */
    private String corpid;

    /**
     * 客户群id
     */
    private String chatId;

    /**
     * 群名
     */
    private String name;

    /**
     * 群主userid
     */
    private String owner;

    /**
     * 状态
     */
    private EnterpriseWechatGroupChatStatusEnum status;

    /**
     * 群成员数
     */
    private Integer groupMembersQuantity;

    /**
     * 企业成员数
     */
    private Integer enterpriseMembersQuantity;

    /**
     * 外部联系人数
     */
    private Integer externalContactQuantity;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

    /**
     * 是否可以被选中 / 选择按钮被置灰
     */
    @TableField(exist = false)
    private CheckStatus checkStatus;

}
