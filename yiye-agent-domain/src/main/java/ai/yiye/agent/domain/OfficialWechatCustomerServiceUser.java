package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.DeleteStatus;
import ai.yiye.agent.domain.enumerations.OfficialWechatCustomerSubjectType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 微信客服-公众号关联表  基于userid同步
 * @Author：lilidong
 * @Date：2023/10/13 10:23
 */

@Data
@TableName("official_wechat_customer_service_user")
public class OfficialWechatCustomerServiceUser {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 公众号名称
     */
    private String nickName;

    /**
     * 公众号appid
     */
    private String appId;

    /**
     * 公众号头像
     */
    private String headImg;

    /**
     * 企微corpid
     */
    private String corpId;

    /**
     * 客服userid
     */
    private String wechatUserId;

    /**
     * 0:相同主体 1:不同主体
     */
    private OfficialWechatCustomerSubjectType subjectType;

    /**
     * 背景图
     */
    private String backgroundUrl;

    /**
     * 背景图宽度
     */
    private Integer backgroundWidth;

    /**
     * 背景图长度
     */
    private Integer backgroundHeight;

    /**
     * 二维码宽度
     */
    private Integer qrCodeWidth;


    /**
     * 二维码长度
     */
    private Integer qrCodeHeight;

    /**
     * 二维码位置宽度
     */
    private Integer qrCodeIndexLeft;


    /**
     * 二维码位置高度
     */
    private Integer qrCodeIndexTop;

    /**
     * 所属账户ID
     */
    private String agentId;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 逻辑删除的标记
     */
    private DeleteStatus deleteStatus;

}
