package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 落地页上报配置 - facebook广平台上报账户
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_upload_facebook_account_config", autoResultMap = true)
public class LandingPageUploadFacebookAccountConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * pmp项目id
     */
    private Long advertiserAccountGroupId;

    /**
     * 数据源 所有者 id（非唯一）
     */
    private String accountId;

    /**
     * 数据源 所有者 名称 （非唯一）
     */
    private String accountName;

    /**
     * 像素 数据集编号 id（唯一）
     */
    private String pixelId;

    /**
     * 像素 数据集编号 名称（非唯一）
     */
    private String pixelName;

    /**
     * 数据源accessToken
     */
    private String accessToken;

    /**
     * 数据源 test_event_code
     */
    private String testEventCode;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
