package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.WidgetTemplateType;
import ai.yiye.agent.domain.enumerations.WidgetType;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * 落地页与组件模板关系表
 */
@Data
@TableName("landing_page_widget_template_rel")
public class LandingPageWidgetTemplateRel {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long landingPageId;

    /**
     * 落地页 - 组件 - 主键id，关联表：landing_page_widget_template.id
     */
    private Long landingPageWidgetTemplateId;

    /**
     * 组件类型
     * 0 普通组件
     * 1 嵌套组件-拼接组件
     */
    private WidgetType widgetType;

    /**
     * 组件对应的uuid（即使是拼接组件内的组件，也是某个组件的uuid，在落地页内唯一）
     */
    private String uuid;

    /**
     * <!- 拼接组件内 某组件的uuid（改成了下面的逻辑） -->
     * 上级组件的uuid，如拼接组件的uuid，指向表：landing_page_widget_template_rel.uuid
     */
    private String parentUuid;

    /**
     * 0-url跳转 1-文字提示
     */
    private String actionType;

    private String actionName;

    private String actionTarget;

    /**
     * 模板类型
     */
    private WidgetTemplateType wtType;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject ext;

    /**
     * 弹窗组件 - 对应 - 弹窗组件关系表集合，关联【弹层组件id】字段：landing_page_popup_widget_template_rel.landing_page_popup_widget_template_id
     */
    @TableField(exist = false)
    private List<LandingPagePopupWidgetTemplateRel> landingPagePopupWidgetTemplateRelList;

}
