package ai.yiye.agent.domain;

import ai.yiye.agent.domain.dto.designert.TeamType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

/**
 * @description: 素材与团队角色及设计师信息的关联关系
 * @author: wang<PERSON><PERSON>
 * @time: 2021/1/12 10:21
 */
@Data
@TableName("marketing_material_designer_team_rel")
public class MaterialDesignerTeamRelation implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(
        type = IdType.AUTO
    )
    private Long id;
    /**
     * 素材ID
     */
    private Long materialId;
    /**
     * 素材唯一md5
     */
    private String signature;
    /**
     * 团队id
     */
    private Long teamId;
    /**
     * 角色id
     */
    private Long roleId;
    /**
     * 设计师id
     */
    private Long designerId;
    /**
     * 团队占比
     */
    private BigDecimal teamRatio;
    /**
     * 角色占比
     */
    private BigDecimal roleRatio;
    /**
     * 人员占比
     */
    private BigDecimal designerRatio;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    private Long solutionId;

    private Integer sortNum;

    private TeamType teamType;
}
