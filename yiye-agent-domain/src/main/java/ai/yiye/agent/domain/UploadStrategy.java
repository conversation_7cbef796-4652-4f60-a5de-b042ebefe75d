package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.UploadStrategyOperationType;
import ai.yiye.agent.domain.enumerations.Platform;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * 上报策略
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_upload_strategy", autoResultMap = true)
public class UploadStrategy implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 上报媒体（平台）
     */
    private Platform platformId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 项目ID
     */
    private Long advertiserAccountGroupId;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 子策略列表
     */
    @TableField(exist = false)
    private ArrayList<UploadSonStrategy> sonStrategys;

    /**
     * 排序方式，desc=降序，asc=升序
     */
    @TableField(exist = false)
    private String order;

    /**
     * 排序字段
     */
    @TableField(exist = false)
    private String sort;

    /**
     * 媒体ids--搜索条件
     */
    @TableField(exist = false)
    private List<Integer> platformIds;

    /**
     * 模糊搜索值(上报策略ID或者策略名称)--搜索条件
     */
    @TableField(exist = false)
    private String selectValue;

    /**
     * 应用对象数
     */
    @TableField(exist = false)
    private Integer applicationCount;

    /**
     * 创建者
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 与策略绑定的广告组ID,使用yiyeAdgroupId字段值
     */
    @TableField(exist = false)
    private ArrayList<Long> yiyeAdgroupIds;

    /**
     * 上报策略-广告应用-操作类型
     */
    @TableField(exist = false)
    private UploadStrategyOperationType operationType;

    /**
     * 广告账户ID
     */
    @TableField(exist = false)
    private String accountId;

}
