package ai.yiye.agent.domain;

import ai.yiye.agent.domain.dto.designert.TeamDto;
import ai.yiye.agent.domain.enumerations.MaterialType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 10:54
 */
@Data
@TableName("management_solution")
public class ManagementSolution {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private MaterialType type;

    @TableField(exist = false)
    private List<TeamDto> teams;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
