package ai.yiye.agent.domain;

import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 巨量引擎广告版位
 */
@Data
@TableName("marketing_ocean_engine_adcreative_template")
public class OceanEngineAdcreativeTemplate {
    @TableId(
        type = IdType.INPUT
    )
    private Long id;

    /**
     * 推广目标
     */
    private String promotedObjectType;

    /**
     * 投放范围
     */
    private String deliveryRange;

    /**
     * 广告位置
     */
    private String siteSet;

    /**
     * 创意形式
     */
    private String adcreativeTemplateAppellation;

    /**
     * 素材类型
     */
    private String imageMode;

    /**
     * 创意结构
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject adcreativeTemplateStructure;

    /**
     * 创意素材限制
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject restriction;
    ;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Instant createdAt;

    @TableField(
        fill = FieldFill.INSERT_UPDATE
    )
    private Instant updatedAt;
}
