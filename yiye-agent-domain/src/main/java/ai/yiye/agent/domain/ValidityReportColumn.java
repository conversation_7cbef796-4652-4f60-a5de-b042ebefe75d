package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 人效数据报表自定义列表
 */
@Data
@TableName("validity_report_column")
public class ValidityReportColumn implements Serializable {
    @TableId(
        type = IdType.AUTO
    )
    private Long id;

    /**
     * 类型: 0=设计师人效,1=设计师人效详情,2=团队人效,3=团队人效详情,4=优化师人效，5=优化师人效详情
     */
    private Integer type;

    /**
     * 列序号
     */
    private Integer no;

    private Boolean checked;

    private String category;

    private Long userId;

    /**
     * 字段名
     */
    private String field;

    /**
     * 字段中文描述
     */
    private String name;

    /**
     * 单位
     */
    private String unit;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Instant createdAt;

    @TableField(
        fill = FieldFill.INSERT_UPDATE
    )
    private Instant updatedAt;
}
