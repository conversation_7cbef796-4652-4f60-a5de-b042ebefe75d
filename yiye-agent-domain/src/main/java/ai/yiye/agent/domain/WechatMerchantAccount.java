package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.WechatMerchantAccountStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 商户号配置(PaymentWetchatMerchantAccount)实体类
 *
 * <AUTHOR>
 * @since 2020-05-19 16:26:40
 */
@Data
@TableName("payment_wechat_merchant_account")
public class WechatMerchantAccount implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 公众号id
     */
    private Long wechatOfficialId;
    /**
     * 商户号名称
     */
    private String name;
    /**
     * 商户号id
     */
    private String mchId;
    /**
     * 商户号秘钥
     */
    private String mchKey;
    /**
     * 授权状态. 0: 未验证; 1: 已验证
     */
    private WechatMerchantAccountStatus validate;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
    /**
     * 上传证书原名
     */
    private String certificateName;
    /**
     * 证书保存的路径
     */
    private String certificatePath;

    @TableField(exist = false)
    private String appId;

    @TableField(exist = false)
    private String agentId;
}
