package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.DeleteStatus;
import ai.yiye.agent.domain.enumerations.LandingPageWechatCustomerContactVerifyStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 微信客服机器人活码配置表(动态渠道二维码)
 */
@Data
@TableName("robot_customer_dynamic_contact")
public class RobotCustomerDynamicContact {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客服id
     */
    private Long landingPageWechatCustomerServiceId;

    /**
     * 背景图
     */
    private String backgroundUrl;

    /**
     * 背景图宽度
     */
    private Integer backgroundWidth;

    /**
     * 背景图长度
     */
    private Integer backgroundHeight;

    /**
     * 二维码宽度
     */
    private Integer qrCodeWidth;


    /**
     * 二维码长度
     */
    private Integer qrCodeHeight;

    /**
     * 二维码位置宽度
     */
    private Integer qrCodeIndexLeft;


    /**
     * 二维码位置高度
     */
    private Integer qrCodeIndexTop;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 逻辑删除的标记
     */
    private DeleteStatus deleteStatus;

    /**
     * 机器人活码是否验证 CLOSE:关闭 OPEN:开启
     */
    private LandingPageWechatCustomerContactVerifyStatus robotCustomerDynamicContactVerify;
}
