package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("payment_wechat_official_account_rel")
public class WechatOfficialAccountRel {

    /**
     * 公众号关系ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 公众号ID
     */
    private Long officialAccountId;

    /**
     * 投放账户ID
     */
    private Long advertiserAccountId;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 投放账号名称
     */
    @TableField(exist = false)
    private String advertiserAccountName;

    /**
     * 客户名称
     */
    @TableField(exist = false)
    private String advertiserAccountGroupName;

}
