package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.Instant;

@Data
@TableName("landing_page_group")
public class LandingPageGroup {

    @TableId(type = IdType.AUTO)
    private Long id;

    @NotNull(message = "名称不能为空")
    private String name;

    private Long advertiserAccountId;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    private Long advertiserAccountGroupId;

    @TableField(exist = false)
    private Boolean topStatus;
}
