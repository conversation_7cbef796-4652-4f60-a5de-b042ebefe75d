package ai.yiye.agent.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 私域配置表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AgentConfCacheDto implements Serializable {

    private Long id;

    private String agentId;
    /**
     * 0禁；1启
     */
    private int status;

    /**
     * 控制落地页是否需要被拦截器拦截，默认ture，如果被拦截，则只有域名匹配才能正常访问
     */
    private boolean landingPageEnableSwitch;


}
