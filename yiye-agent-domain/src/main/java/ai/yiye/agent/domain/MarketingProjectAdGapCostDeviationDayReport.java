package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.ColorType;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 项目缺口与成本偏差天记录表
 * @Author：lilidong
 * @Date：2024/1/31 10:45
 */
@Data
@TableName("marketing_project_ad_gap_cost_deviation_day_report")
public class MarketingProjectAdGapCostDeviationDayReport {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 一叶系统的项目ID
     */
    private Long advertiserAccountGroupId;

    /**
     * 缺口
     */
    private BigDecimal gap;

    /**
     * 缺口颜色
     */
    private ColorType gapColor;

    /**
     * 成本偏差
     */
    private BigDecimal costDeviation;

    /**
     * 成本偏差颜色
     */
    private ColorType costDeviationColor;

    /**
     * 当日目标
     */
    private JSONArray target;

    /**
     * 日期
     */
    private Instant dayAt;

    /**
     * 缺口背景颜色
     */
    private ColorType gapBgColor;

    /**
     * 成本偏差背景颜色
     */
    private ColorType costDeviationBgColor;

    /**
     * 0-pg数据库查询结果；1-clickhouse数据库查询结果；
     */
    private Integer searchType;

    /**
     * 展示数
     */
    private BigDecimal viewNum;

    /**
     * 点击数
     */
    private BigDecimal clickNum;

    /**
     * 转化数
     */
    private BigDecimal convertNum;

    /**
     * 深度转化数
     */
    private BigDecimal deepConvertNum;


    /**
     * 转化成本
     */
    private BigDecimal targetConvertCost;

    /**
     * 深度转化成本
     */
    private BigDecimal deepConvertCost;

    /**
     * 消耗
     */
    private BigDecimal cost;

    /**
     * 创建时间
     */
    private Instant createdAt;

    /**
     * 更新时间
     */
    private Instant updatedAt;


}
