package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2022/7/7 17:00
 */
@Data
@TableName("qiyetui_callback_record")
public class QiYeTuiCallBackRecord implements Serializable {
    private Long id;
    private String agentId;
    //pid
    private String pid;
    //用户OpenID
    private String openId;
    //用户UnionID
    private String unionId;
    //用户对应的外部联系人UserID
    private String externalUserId;
    //用户对应的外部联系人名称
    private String externalName;
    //企业成员UserID
    private String staffUserId;
    //企业成员名称
    private String staffName;
    //外部联系人的类型
    //1 - 表示该外部联系人是微信用户
    //2 - 表示该外部联系人是企业微信用户
    private Integer type;
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    //版本 0:旧版 1:新版
    private Integer version;
}
