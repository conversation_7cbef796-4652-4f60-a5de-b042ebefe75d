package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.DeleteStatus;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("landing_page_preview")
public class LandingPagePreview extends LandingPage {

    @TableId(type = IdType.AUTO)
    private Long previewId;

    private String previewToken;

    private Long id;

    /**
     * 是否逻辑删除：0-未删除  1-已删除
     */
    @TableField(exist = false)
    private DeleteStatus deleteStatus;
}
