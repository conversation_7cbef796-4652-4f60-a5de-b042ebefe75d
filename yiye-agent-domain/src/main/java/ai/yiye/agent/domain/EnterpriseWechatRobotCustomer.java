package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.utils.Compare;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * 微信机器人客服表
 */
@TableName("enterprise_wechat_robot_customer")
@Data
public class EnterpriseWechatRobotCustomer implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户标识（数据源id）
     */
    private String agentId;

    /**
     * 企业微信id
     */
    private String corpId;

    /**
     * 名称
     */
    @Compare(compareField = CompareLogFieldEnum.ROBOT_CUSTOMER_CHANGE_NAME)
    private String name;

    /**
     * 企业微信机器人客服id
     */
    private String openKfId;

    /**
     * 服务商主体下的微信客服ID，如果传入的open_kfid已经是服务商主体下的ID，则new_open_kfid与open_kfid相同。
     */
    private String newOpenKfId;

    /**
     * 企业微信微信客服URL
     */
    private String openKfUrl;

    /**
     * 企业微信微信客服URL（微信客服组件授权）
     */
    private String newOpenKfUrl;


    /**
     * pmp项目id
     */
    private Long advertiserAccountGroupId;

    /**
     * 用户自定义-备注信息
     */
    private String customRemarks;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 是否自动回复
     */
    private SwitchStatus autoAnswer;


    /**
     * 是否开启关键词回复
     */
    private SwitchStatus keywordReply;

    /**
     * 自动回复规则ID
     */
    private Long[] keywordReplyRuleIds;


    /**
     * 关键词回复，是否勾选已成功添加企业微信或关注公众号用户不触发开关
     */
    private SwitchStatus keywordReplyNotTriggeredWhenAdded;

    /**
     * 是否自动回复(获客助手)
     */
    private SwitchStatus autoAnswerCa;

    /**
     * 是否挽留消息
     */
    private SwitchStatus retainMessage;

    /**
     * 是否挽留消息(获客助手)
     */
    private SwitchStatus retainMessageCa;

    /**
     * 挽留时间(1.234.0版本废弃)
     */
    private Long retainTimeInterval;

    /**
     * 挽留时间
     */
    private Long detainTimeInterval;

    /**
     * 挽留时间(获客助手)
     */
    private Long detainTimeIntervalCa;

    /**
     * 微信客服机器人状态
     */
    private EnterpriseWechatRobotCustomerStatus status;

    /**
     * 微信客服机器人使用状态
     */
    @Compare(compareField= CompareLogFieldEnum.ROBOT_CUSTOMER_ONLINE)
    private BaseStatusEnum usageStatus;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 欢迎语id
     */
    private Long msgTemplateId;

    /**
     * 欢迎语id(获客助手)
     */
    private Long msgTemplateIdCa;

    /**
     * 头像
     */
    private String filePath;

    /**
     * 企业微信客服机器人类型（代开发应用API添加、微信客服组件授权）
     */
    private EnterpriseWechatRobotCustomerType type;

    /**
     * 企业微信客服机器人智能类型(普通、智能)
     */
    private EnterpriseWechatRobotCustomerIntelligenceType intelligenceType;

    /**
     * 获客助手消息发送比例
     */
    private Integer acquisitionAssistantMsgSendRatio;

    /**
     * 蓝链加粉消息发送比例
     */
    private Integer blueLinkAddFansMsgSendRatio;

    /**
     * 代开发应用管理权限
     */
    private Boolean managePrivilege;

    /**
     * 非落地页访客进入会话禁止发送消息
     */
    private SwitchStatus nonLandingPageProhibitSend;

    /**
     * 针对非落地页访客欢迎语发送设置
     */
    private String nonLandingPageSendContent;

    /**
     * 恢复状态
     */
    private BaseStatusEnum recoveryStatus;

    /**
     * 恢复时间
     */
    private Instant recoveryAt;

    /**
     * 恢复构造延迟
     */
    private Integer recoveryConstructDelay;

    /**
     * 恢复销毁延迟
     */
    private Integer recoveryDestroyDelay;

    /**
     * 帐户异常恢复状态
     */
    private BaseStatusEnum accountAbnormalityRecoveryStatus;

    /**
     * 头像素材id
     */
    @TableField(exist = false)
    private String materialId;

    /**
     * 所属企业微信
     */
    @TableField(exist = false)
    private String workWechatName;

    /**
     * 企业微信类型
     */
    private EnterpriseWechatType enterpriseWechatType;

    /**
     * 接待客户数
     */
    @TableField(exist = false)
    private Integer receptionCustomerNum;

    /**
     * 成功添加企业微信数
     */
    @TableField(exist = false)
    private Integer addWorkWechatNum;

    /**
     * 成功添加企业微信率
     */
    @TableField(exist = false)
    private String addWorkWechatRate;

    /**
     * 成功关注公众号数
     */
    @TableField(exist = false)
    private Integer followOfficialAccountNum;

    /**
     * 成功关注公众号率
     */
    @TableField(exist = false)
    private String followOfficialAccountRate;

    /**
     * 所属分组ID集合
     */
    @TableField(exist = false)
    private List<Long> wechatRobotCustomerGroupIds;

    /**
     * 所属分组ID字符串
     */
    @TableField(exist = false)
    private String groupIds;

    /**
     * 恢复策略
     */
    private EnterpriseWechatRobotCustomerRecoveryStrategy recoveryStrategy;

    /**
     * 恢复进线率
     */
    private Integer recoveryEntryRatio;

    public static JSONObject getOperDescJsonBySn(String fieldKey, String before, String after) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", "String");
        jsonObject.put("before", before);
        jsonObject.put("after", after);
        return jsonObject;
    }

}
