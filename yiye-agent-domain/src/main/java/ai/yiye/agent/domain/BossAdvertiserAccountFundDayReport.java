package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2022/7/20 20:24
 */
@Data
@TableName("boss_advertiser_account_fund_day_report")
public class BossAdvertiserAccountFundDayReport implements Serializable {
    private Long id;
    private String agentId;
    private Instant dayTime;
    private BigDecimal fund;
    private Instant createdAt;
    private Integer billAccountNum;
    private BigDecimal billingProportion;

}
