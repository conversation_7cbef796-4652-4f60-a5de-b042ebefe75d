package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.DeleteStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 公众号助手配额表
 * @Author：lilidong
 * @Date：2023/8/23 15:45
 */

@Data
@TableName("official_account_assistant_quota")
public class OfficialAccountAssistantQuota {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户标识
     */
    private String agentId;

    /**
     * pmp ID
     */
    private Long advertiserAccountGroupId;


    /**
     * 当前项目已配置名额
     */
    private Integer allocatedQuota;

    /**
     * 实际消耗名额
     */
    private Integer consumptionQuota;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 删除标记
     */
    private DeleteStatus deleteStatus;

}
