package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.UploadActionType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * 落地页上报配置 - 上报转化目标类型
 */
@Data
@TableName("upload_config_change_record")
public class UploadConfigChangeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * pmp项目id（关联表：marketing_advertiser_account_group.id）
     */
    private Long advertiserAccountGroupId;

    /**
     * 落地页id（关联表：landing_page.id）
     */
    private Long landingPageId;

    @TableField(exist = false)
    private String landingPageName;

    /**
     * 渠道id（关联表：landing_page_channels.id）
     */
    private Long channelId;

    /**
     * 渠道名称
     */
    @TableField(exist = false)
    private String channelName;

    /**
     * 操作员用户id（关联表：ucenter_user.id）
     */
    private Long userId;

    /**
     * 操作员名称（发起操作时的名称，如需最新名称可根据user_id查询）
     */
    private String userName;

    /**
     * 操作员ip地址
     */
    private String ip;

    /**
     * 操作员执行操作动作类型：1-新增  2-修改/编辑  3-删除
     */
    private UploadActionType uploadActionType;

    /**
     * 操作员执行操作动作类型：1-新增  2-修改/编辑  3-删除
     */
    @TableField(exist = false)
    private List<UploadActionType> uploadActionTypeList;

    /**
     * 变更前记录
     */
    private String beforeDesc;

    /**
     * 变更后记录
     */
    private String afterDesc;

    /**
     * 创建时间（操作时间）
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    @TableField(exist = false)
    private Instant startTime;

    @TableField(exist = false)
    private Instant endTime;

}
