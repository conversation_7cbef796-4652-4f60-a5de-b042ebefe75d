package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.VivoSrcType;
import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <AUTHOR>
 * 用户数据源表
 * @version 1.0
 * @date 2021/11/12 10:43
 */
@Data
@TableName("marketing_user_action_set")
public class UserSetData extends AbstractMarketingData {
    @TableField(exist = false)
    public static final String[] CONFLICTS = new String[]{"account_id", "set_id", "platform_id"};
    @TableField(exist = false)
    protected JSONObject ext;
    @TableId(
        type = IdType.AUTO
    )
    private Long id;

    //数据源名
    private String name;

    //数据源类型
    private VivoSrcType vivoSrcType;

    //数据源id
    private String setId;

}
