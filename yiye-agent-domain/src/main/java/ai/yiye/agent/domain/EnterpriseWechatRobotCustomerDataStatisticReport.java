package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

/**
 * 微信客服机器人数据统计报表
 */
@TableName("enterprise_wechat_robot_customer_data_statistic_report")
@Data
public class EnterpriseWechatRobotCustomerDataStatisticReport {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 微信客服机器人id
     */
    private Long wechatCustomerServiceRobotId;

    /**
     * 企业微信id
     */
    private String corpId;

    /**
     * 微信客服机器人openKfId
     */
    private String openKfId;

    /**
     * 访问数
     */
    private Integer visitCount;

    /**
     * 访客数
     */
    private Integer visitorCount;

    /**
     * 欢迎语发送成功数
     */
    private Integer welcomeMessageSendSuccessCount;

    /**
     * 欢迎语发送失败数
     */
    private Integer welcomeMessageSendFailCount;

    /**
     * 咨询会话数
     */
    private Integer consultationSessionsCount;

    /**
     * 咨询客户数
     */
    private Integer consultationCustomerCount;

    /**
     * 添加企业微信数
     */
    private Integer addEnterpriseWechatCount;

    /**
     * 添加关注公众号数
     */
    private Integer followingOfficialAccountCount;

    /**
     * 统计时间
     */
    private LocalDate statisticDate;

}
