package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.MediaUploadType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 落地页上报配置 - 小红书账户信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_upload_xhs_account_config", autoResultMap = true)
public class LandingPageUploadXhsAccountConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * pmp项目id
     */
    private Long advertiserAccountGroupId;

    /**
     * 账户名称（便于管理）
     */
    private String accountName;

    /**
     * 投放账户id
     */
    private String advertiserId;

    /**
     * 账户app_id
     */
    private String appId;

    /**
     * 账户access_token
     */
    private String accessToken;

    /**
     * 上报来源信息
     */
    private String uploadSource;

    /**
     * 媒体 - 上报方式（全平台通用：建议使用）
     */
    private MediaUploadType mediaUploadType;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
