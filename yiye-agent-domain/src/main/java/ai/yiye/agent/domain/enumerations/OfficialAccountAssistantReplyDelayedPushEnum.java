package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum OfficialAccountAssistantReplyDelayedPushEnum implements IEnum<Integer> {
    DISABLE,
    ENABLE
    ;

    @Override
    public Integer getValue() {
        return ordinal();
    }

    public static OfficialAccountAssistantReplyDelayedPushEnum getEnumByValue(Integer value) {
        return Arrays.stream(OfficialAccountAssistantReplyDelayedPushEnum.values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst().orElse(null);
    }

}
