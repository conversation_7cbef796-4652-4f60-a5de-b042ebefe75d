package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022/3/30 3:54 下午
 * message_id	number	唯一标识一条推送数据
 * service_label	string	change.aipthirdsite.realtime AIP落地页事件推送
 * data	jsonstring	触发推送的事件消息
 *     event	string	事件名称
 *     user_id	string	事件触发广告主id
 *     content	jsonstring	事件内容
 * publish_time	number	本条消息实际产生时间
 * timestamp	number	推送时间
 * nonce	number	随机数，和timestamp组合防重
 * subscribe_task_id	number	产生推送数据的订阅任务id
 */
public enum SiteAuditNoticeEvent implements IEnum<String> {
    AIP_VALIDATE_STATUS("AIP_VALIDATE_STATUS"),
    AIP_AUDIT_STATUS("AIP_AUDIT_STATUS");
    private String value;

    SiteAuditNoticeEvent(String value){
        this.value = value;
    }
    @Override
    public String getValue() {
        return value;
    }
}
