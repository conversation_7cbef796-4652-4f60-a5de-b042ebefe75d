package ai.yiye.agent.domain;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/7 19:00
 */
@Data
@TableName("customer_cal_formula")
public class CustomerCalFormula implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 指标展示名称 例如质保期
     */
    private String name;
    /**
     * 计算公式 (昨日花费-花费)/花费 将tyd等进行转化后的结果
     */
    private String calculationFormula;
    /**
     * 描述
     */
    private String description;
    /**
     * 展示类型 0 小数 1 百分比
     */
    private String showStyle;
    /**
     * 指标去除多余计算条件  如((#{YTD花费})-#{花费})/#{花费}
     */
    private String targetDetail;
    /**
     * 指标信息
     */
    private JSONArray formula;

    /**
     * 原全部信息 例如 (曝光量-YTD曝光量)/曝光量
     */
    private String targetCalculation;
    /**
     * 将指标公式中的字符串去重得出
     */
    private JSONArray systemFormula;
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    public String getTargetCalculation() {
        if (StringUtils.isEmpty(targetCalculation)) {
            return calculationFormula;
        }
        return targetCalculation;
    }
}
