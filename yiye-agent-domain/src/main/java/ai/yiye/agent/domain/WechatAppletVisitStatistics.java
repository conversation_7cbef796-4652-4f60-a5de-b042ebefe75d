package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.ChannelStatisticType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2022/8/1 12:00
 */
@Data
@TableName("wechat_applet_visit_statistics")
public class WechatAppletVisitStatistics {
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * appId
     */
    private String appId;
    private String channelParam;
    private ChannelStatisticType channelStatisticType;
    private String agentId;
    private String yiyeQueryId;
    private String yiyeQueryStr;
    private String openLink;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
