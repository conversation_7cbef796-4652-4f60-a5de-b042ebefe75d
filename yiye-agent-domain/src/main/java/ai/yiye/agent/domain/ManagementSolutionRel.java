package ai.yiye.agent.domain;

import ai.yiye.agent.domain.dto.designert.TeamType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2021/4/1 10:56
 */
@Data
@TableName("management_solution_rel")
public class ManagementSolutionRel {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long solutionId;

    private Long teamId;

    private Long roleId;

    private Long designerId;

    /**
     * 团队占比
     */
    private BigDecimal teamRatio;
    /**
     * 角色占比
     */
    private BigDecimal roleRatio;

    private Integer sortNum;

    private TeamType teamType;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
