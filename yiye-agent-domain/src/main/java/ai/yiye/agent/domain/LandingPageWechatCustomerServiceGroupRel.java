package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.Instant;

/**
 * 落地页 - 微信客服管理 - 与 - 分组 - 关系表
 */
@Data
@TableName("landing_page_wechat_customer_service_group_rel")
public class LandingPageWechatCustomerServiceGroupRel {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * pmp项目id，关联表：marketing_advertiser_account_group.id
     */
    @NotNull(message = "PMP项目分组id为必填项")
    private Long advertiserAccountGroupId;

    /**
     * 客服id，来自表：landing_page_wechat_customer_service.id
     */
    private Long landingPageWechatCustomerServiceId;

    /**
     * 分组id，来自表：landing_page_wechat_customer_service_group.id
     */
    private Long landingPageWechatCustomerServiceGroupId;

    /**
     * 微信客服在对应分组里面的自定义排序编号
     */
    private Integer orderNum;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
