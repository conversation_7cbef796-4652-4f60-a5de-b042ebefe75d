package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.WorkWechatApiLicenseOrderStatus;
import ai.yiye.agent.domain.enumerations.WorkWechatApiLicenseOrderType;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.*;

import java.io.Serializable;
import java.time.Instant;

/**
 * <p>
 * 企微订单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class WorkWechatApiLicenseOrder implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String agentId;

    private String corpId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单类型：1:购买账号 2:续期账号 5:历史企业迁移订单 8:多企业新购订单
     */
    private WorkWechatApiLicenseOrderType orderType;

    /**
     * 订单状态 0：待支付，1：已支付，2：已取消（未支付，订单已关闭）3：未支付，订单已过期，4：申请退款中，5：退款成功，6：退款被拒绝，7：订单已失效
     */
    private WorkWechatApiLicenseOrderStatus orderStatus;

    private Long price;

    private JSONObject accountCount;

    private JSONObject accountDuration;

    private Long createTime;

    private Long payTime;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 操作人ip
     */
    private String operatorIp;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;


}
