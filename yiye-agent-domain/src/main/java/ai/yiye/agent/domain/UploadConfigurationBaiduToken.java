package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.MediaUploadType;
import ai.yiye.agent.domain.enumerations.Platform;
import ai.yiye.agent.domain.enumerations.UploadEventType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 落地页上报配置 - 上报转化目标类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_upload_configuration_baidu_token", autoResultMap = true)
public class UploadConfigurationBaiduToken implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 项目id
     */
    private Long advertiserAccountGroupId;

    /**
     * vivo平台 - 数据源ID
     */
    private String actionSetId;

    /**
     * platformId
     */
    private Platform platform;

    /**
     * 百度tokenId：
     */
    private String tokenId;

    /**
     * token名称（不重复）：
     */
    private String tokenName;

    /**
     * 媒体 - 上报方式（全平台通用：建议使用）
     */
    private MediaUploadType mediaUploadType;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
