package ai.yiye.agent.domain.sessionarchive;

import ai.yiye.agent.domain.enumerations.EnterpriseWechatSessionRevokeStatus;
import ai.yiye.agent.domain.enumerations.SessionMessageType;
import ai.yiye.agent.domain.enumerations.SessionSendType;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * <p>
 * 企业微信-员工客户-聊天记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-29
 */
@Data
@TableName("enterprise_session_employee_customer_chat_records")
public class EnterpriseSessionEmployeeCustomerChatRecords implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息的seq值，标识消息的序号
     */
    private String seq;

    /**
     * 企微消息id
     */
    private String msgid;

    /**
     * 企业微信公司id，关联表：enterprise_wechats.id
     */
    private Long corpid;

    /**
     * 企业微信corpId
     */
    private String enterpriseWechatCorpId;

    /**
     * 发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Instant sendTime;

    /**
     * 员工ID
     */
    private String employeeId;

    /**
     * 密文员工ID
     */
    private String openEmployeeId;

    /**
     * 客户userID
     */
    private String userId;

    /**
     * 服务商下客户userid
     */
    private String openUserId;

    /**
     * 消息类型
     */
    private SessionMessageType chatType;

    /**
     * 消息内容
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject content;

    /**
     * 发送方 0：成员  1：客户
     */
    private SessionSendType sendType;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Instant updatedAt;

    /**
     * 消息是否撤回
     */
    private EnterpriseWechatSessionRevokeStatus revokeStatus;

    /**
     * 文件过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Instant fileExpireAt;

    /**
     * 媒体文件路径
     */
    private String[] storagePath;

}
