package ai.yiye.agent.domain;

import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("submit_phone_record")
public class SubmitPhoneRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String pid;

    private String phone;

    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject ext;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}


