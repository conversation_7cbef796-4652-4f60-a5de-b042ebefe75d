package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.EnterpriseWechatStatus;
import ai.yiye.agent.domain.enumerations.EnterpriseWechatType;
import ai.yiye.agent.domain.enumerations.YesOrNoEnum;
import ai.yiye.agent.domain.typehandlers.TextArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * 用户留存分析企业微信表
 */
@Data
@TableName("user_retention_analysis_enterprise_wechat")
public class UserRetentionAnalysisEnterpriseWechat {


    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * enterprise_wechat_id
     */
    private Long enterpriseWechatId;

    /**
     * 企业微信id
     */
    private String agentId;

    /**
     * 企业微信id
     */
    private String corpid;

    /**
     * 企业微信名称
     */
    private String corpName;

    /**
     * 企业微信全称
     */
    private String corpFullName;


    private String subjectType;

    /**
     * 可见范围成员
     */
    @TableField(typeHandler = TextArrayTypeHandler.class)
    private String[] allowUser;

    /**
     * 可见范围成员数量
     */
    @TableField(exist = false)
    private Integer allowUserNum;

    /**
     * 企业微信状态
     */
    private EnterpriseWechatStatus status;


    /**
     * 企业微信类型
     * SERVICE_PROVIDER("服务商"),
     * BUILD_BY_ONESELF("自建应用"),
     * GENERATION_DEVELOPMENT("代开发应用"),
     */
    private EnterpriseWechatType enterpriseWechatType;


    /**
     * 加密的企业微信id
     */
    private String openCorpid;

    /**
     *
     */
    private Instant enterpriseWechatCreatedAt;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;


    //是否是被分享的
    @TableField(exist = false)
    private YesOrNoEnum shared;
    //如果是被分享的，这边要有一个转增来的字符串
    @TableField(exist = false)
    private String sharedText;

    @TableField(exist = false)
    private List<String> pmps;


    /**
     * 代开发授权在所有账户的所有PMP下的共享数量，大于1时，【解绑】置亮，支持解绑操作
     */
    @TableField(exist = false)
    private Integer pmpCount;

}
