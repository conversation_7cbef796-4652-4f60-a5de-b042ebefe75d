package ai.yiye.agent.domain.message;

import ai.yiye.agent.domain.enumerations.WidgetTemplateType;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * @program: yiye-agent-backend
 * @description: 短信记录实体类
 * @author: x<PERSON><PERSON><PERSON>
 * @create: 2020-05-18 16:39
 **/
@Data
@TableName("message_sms_send_log")
public class SmsSendLog {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String mobile;

    private String content;

    private Instant sendAt;

    /**
     * 网关接收到下行短信状态报告的时间
     */
    private Instant arriveAt;

    private Integer count;

    /**
     * 发送短信，完整请求参数
     */
    private String sendData;

    /**
     * 发送短信，完整响应参数
     */
    private String resultData;

    /**
     * 发送短信，描述（如：错误、异常，等描述）
     */
    private String description;

    /**
     * 短信发送状态：FAIL-失败  success-成功
     */
    private String sendStatus;

    private Long smsSignatureId;

    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject ext;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    private String flagId;

    private String msgId;

    /**
     * 获取短信回执：请求参数
     */
    private String smsReportSendData;

    /**
     * 获取短信回执：响应参数
     */
    private String smsReportResultData;

    /**
     * 获取短信回执状态：FAIL-失败  SUCCESS-成功
     */
    private String smsReportStatus;

    /**
     * 获取短信回执，描述（如：错误、异常，等描述）
     */
    private String smsReportDescription;

    private String syncStatus;

    private Long advertiserAccountId;

    private Long advertiserAccountGroupId;

    /**
     * 落地页id
     */
    private Long landingPageId;

    /**
     * 落地页token
     */
    private String token;

    /**
     * 模版类型
     */
    private WidgetTemplateType wtType;

    /**
     * 模版id
     */
    private Long lpWtId;

    /**
     * 错误描述
     */
    @TableField(exist = false)
    private String msg;

    /**
     * 本号码响应代码 0 成功， 非0 失败
     */
    @TableField(exist = false)
    private int code;

    @TableField(exist = false)
    private Instant startTime;

    @TableField(exist = false)
    private Instant endTime;

}
