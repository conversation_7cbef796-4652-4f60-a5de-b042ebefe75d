package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.ApiAccountType;
import ai.yiye.agent.domain.enumerations.LicenseStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 企微客服接口许可信息
 */
@Data
@TableName("work_wechat_customer_license_info")
public class WorkWechatCustomerLicenseInfo implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业微信id，关联表字段：enterprise_wechats.corpid
     */
    private String corpId;

    /**
     * 对应企业成员账号（userid）
     */
    private String userId;

    /**
     * 接口许可状态
     */
    private LicenseStatus licenseStatus;

    /**
     * 激活时间
     */
    private Instant licenseActiveTime;

    /**
     * 接口许可到期时间
     */
    private Instant licenseExpireTime;

    /**
     * 接口账号类型：1-基础账号  2-互通账号
     */
    private ApiAccountType apiAccountType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
