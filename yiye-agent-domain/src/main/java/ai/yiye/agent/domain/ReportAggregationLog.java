package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.Instant;

/**
 * 报表聚合日志记录表
 * @Author：lilidong
 * @Date：2024/2/21 14:28
 */
@Data
@Slf4j
@TableName("report_aggregation_log")
public class ReportAggregationLog implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户标识
     */
    private String agentId;

    /**
     * job名称
     */
    private String jobHandlerName;

    /**
     * 定时任务描述
     */
    private String jobHandlerDescription;

    /**
     * 执行的参数
     */
    private String param;

    /**
     * 执行结果 success:成功; fail:失败
     */
    private String result;

    /**
     * 异常信息
     */
    private String errorMsg;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
