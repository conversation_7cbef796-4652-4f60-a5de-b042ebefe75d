package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.ColorType;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

/**
 * 项目缺口、成本偏差日报实体类
 * <AUTHOR>
 * @date 2021/5/11 9:47
 */
@Data
@Accessors(chain = true)
@TableName("marketing_advertiser_account_group_target_daily_report")
public class GroupTargetDailyReport implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * groupid
     */
    private Long advertiserAccountGroupId;

    /**
     * 缺口
     */
    private BigDecimal gap;

    /**
     * 缺口颜色
     */
    private ColorType gapColor;

    /**
     * 成本偏差
     */
    private BigDecimal costDeviation;

    /**
     * 成本偏差颜色
     */
    private ColorType costDeviationColor;

    /**
     * 当日目标
     */
    private JSONArray target;

    /**
     * 日期
     */
    private Instant dayAt;

    /**
     * 缺口背景颜色
     */
    private ColorType gapBgColor;

    /**
     * 成本偏差背景颜色
     */
    private ColorType costDeviationBgColor;
}
