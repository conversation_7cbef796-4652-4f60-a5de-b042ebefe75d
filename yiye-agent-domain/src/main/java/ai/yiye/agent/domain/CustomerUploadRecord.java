package ai.yiye.agent.domain;


import ai.yiye.agent.domain.enumerations.*;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 客资上报记录
 */
@Data
@TableName("customer_upload_record")
public class CustomerUploadRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 落地页id（关联表：landing_page.id）
     */
    private Long landingPageId;

    /**
     * 落地页组件模板id（关联表：landing_page_widget_template.id）
     */
    private Long landingPageWidgetTemplateId;

    /**
     * 一叶系统 - 用户id（关联表：）
     */
    private Long advertiserAccountId;

    /**
     * 客资提交id（关联表：submit_data.id）
     */
    private Long submitDataId;

    /**
     * 曝光id，关联表page_view_info.pid
     */
    private String pid;

    /**
     * 填单页面的url地址
     */
    private String url;

    /**
     * 上报到媒体方 - 请求值
     */
    private String sendData;

    /**
     * 上报到媒体方 - 返回值
     */
    private String resultData;

    /**
     * 联系方式
     */
    private String phone;

    /**
     * 上报行为类型
     */
    private String actionType;

    /**
     * 上报媒体（平台）
     */
    private Platform platformId;

    /**
     * 上报事件类型
     */
    private UploadEventType submitType;

    /**
     * 客资上报是否成功状态
     */
    private UploadStateType uploadState = UploadStateType.FAIL_REPORT;

    /**
     * 客资上报记录 - 状态码，对应实体：ai.yiye.agent.domain.enumerations.ResponseCode
     */
    private Integer recordCode;

    /**
     * 客资上报 - 状态描述
     */
    private String description;

    /**
     * 上报策略id
     */
    private Long uploadStrategyId;

    /**
     * 填单页面url中解析到的点击参数
     */
    private String clickId;

    /**
     * 落地页上报配置 - 上报转化目标类型 - id，关联表：landing_page_upload_configuration_types.id
     */
    private Long lpuctId;

    /**
     * 上报比例
     */
    private Integer uploadRatio;

    /**
     * 上报时间段
     */
    private String uploadTimeArea;

    /**
     * 比较类型
     */
    private CompareType compareType;

    /**
     * 企业微信客户开口次数：数字类型，默认1次
     */
    private Integer workWechatOpenNum;

    /**
     * 性别：null/ALL-全部、FEMALE-女、MALE-男、UNKNOWN-未知
     */
    private Sex sex;

    /**
     * 是否模糊匹配字段
     */
    private FuzzyMatchingField fuzzyMatchingField;

    /**
     * 是否模糊匹配字段(企业微信客服标签专用)
     */
    private FuzzyMatchingField matchingMode;

    /**
     * 关键词：字符串，默认为null
     */
    private String[] keyWord;

    /**
     * 上报记录 - uuid值
     *
     * ******************* 仅用于合并上报记录防止重复校验，不做他用 *******************
     * ******************* 仅用于合并上报记录防止重复校验，不做他用 *******************
     * ******************* 仅用于合并上报记录防止重复校验，不做他用 *******************
     */
    private String uuidKey;

    /**
     * 是否 符合上报条件时间与添加企业微信成功，默认：否
     */
    private Boolean checkUploadTimeAddWechatSuccess;

    /**
     * 符合上报条件时间与添加企业微信成功时间超过xx天不上报，默认：30
     */
    private Integer checkUploadTimeAddWechatSuccessTime;

    /**
     * 抖音字节小程序 私域访问匹配24小时内广告访问来源url（用于显示上报记录、导出客资、导出访客细查）
     */
    private String matchOtherPvUrl;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 是否为匹配访问来源上报：通过【平台id、公众号unionid/小程序unionid、不包含原pid曝光数据】过滤【page_view_info】表【24小时内的数据】，匹配成功则【isMatchingPageViewInfoUpload=true】
     */
    @TableField(exist = false)
    private Boolean isMatchingPageViewInfoUpload;

    /**
     * 上级页面地址
     */
    @TableField(exist = false)
    private String referrer;

    /**
     * 流量来源
     */
    @TableField(exist = false)
    private FlowSource flowSource;

    /**
     * 表单串加粉填单id
     */
    @TableField(exist = false)
    private Long formMatchAddWechatSubmitDataId;

}
