package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.BaseStatusEnum;
import ai.yiye.agent.domain.enumerations.OperationActionEnum;
import ai.yiye.agent.domain.enumerations.QiyeAttributionType;
import ai.yiye.agent.domain.enumerations.ReplaceOperationType;
import ai.yiye.agent.domain.typehandlers.JSONListTypeHandler;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 * @Purpose 客户实体对象（投放账户分组）
 */
@Data
@TableName("marketing_advertiser_account_group")
public class AdvertiserAccountGroup implements Serializable {

    // 客户Id
    @TableId(type = IdType.AUTO)
    private Long id;

    // 客户名称
    @NotNull
    @Pattern(regexp = "^[\u4E00-\u9FA5_A-Za-z0-9_.-]{1,32}$", message = "客户名称只支持32个长度的数字、汉字、字母、点(.)、减号(-)或下划线(_)")
    private String name;

    // 创建时间
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    // 修改时间
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 投放账户集合
     */
    @TableField(exist = false)
    private List<AdvertiserAccount> accounts;

    /**
     * 管理人员集合
     */
    @TableField(exist = false)
    private String[] managerName;

    /**
     * 投放账户集合
     */
    @TableField(exist = false)
    private String[] advertiserAccountName;

    /**
     * 行业id
     */
    private Long advertiserAccountIndustryId;

    @TableField(exist = false)
    private Long[] advertiserAccountIds;

    /**
     * 返点
     */
    private BigDecimal rebates;

    /**
     * 项目负责人ID
     */
    private Long leaderId;

    @TableField(exist = false)
    private Long[] managerList;

    @TableField(exist = false)
    private String firstIndustry;

    @TableField(exist = false)
    private String secondIndustry;

    @TableField(exist = false)
    private String userName;

    /**
     * 项目目标
     */
    @TableField(typeHandler = JSONListTypeHandler.class)
    private JSONArray target;

    /**
     * 当前项目可使用的小程序版本
     */
    private String landingPageWechatAppletVersion;

    /**
     * 当前项目可使用的小程序版本
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
    private JSONObject landingPageAppletVersionJson;

    /**
     * 是否为代运营，默认值为0，表示无状态。有三个枚举值，0为无状态，1为运营，2为暂停运营
     */
    private ReplaceOperationType replaceOperation;

    /**
     * 企业推页面是否归因
     */
    private QiyeAttributionType qiyeAttribution;
    @TableField(exist = false)
    private String agentId;
    /**
     * 功能白名单，对应枚举：ai.yiye.agent.domain.enumerations.WhiteType
     */
    private String[] whiteTypes;

    /**
     * 是否开启异常时下线微信客服机器人操作状态
     */
    private BaseStatusEnum wechatCustomerServiceRobotAbnormalOfflineStatus;

    /**
     * 展示落地页状态：0-暂停/1-开启，默认开启
     */
    private OperationActionEnum showLandingPageStatus;

}
