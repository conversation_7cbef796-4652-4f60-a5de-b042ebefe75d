package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.ConditionType;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "dou_yin_landing_page_strategy", autoResultMap = true)
public class DouYinLandingPageStrategy implements Serializable {


    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 落地页ID
     */
    private Long landingPageId;
    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 触发条件
     */
    private ConditionType conditionType;


    private Boolean isDrown;

    /**
     * 执行的动作
     */
    private String action;

    /**
     * 指定的抖音号
     */
    private String douYinAccountType;

    /**
     * 指定的抖音号,这个是替换旧的字段douYinAccountType，为了防止线上出问题，所以原来的字段不动，这里新增一个字段
     */
    private String douyinAccountType;

    /**
     * 前端参数，存的是json格式
     * 例如："accountId": {
     *             "awemeId": "**********",
     *             "userName": "番茄🍅炒蛋🥚",
     *             "douyinAppletAppid": "ttc09eb6235fa1c14201"
     *         },
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject accountId;


    /**
     * 页面参数
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject content;


    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;


}
