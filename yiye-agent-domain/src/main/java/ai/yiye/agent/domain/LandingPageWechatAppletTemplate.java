package ai.yiye.agent.domain;

import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;


//小程序版本
@Data
@TableName("landing_page_wechat_applet_template")
public class LandingPageWechatAppletTemplate implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
    //代码库中的代码模板 ID
    private String templateId;
    //该参数则是用于控制ext.json配置文件的内容
    private String userVersion;
    //代码版本号，开发者可自定义（长度不要超过 64 个字符）
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject extJson;
    //代码描述，开发者可自定义
    private String userDesc;

    /**
     * 微信第三方平台的appid
     */
    private String componentAppId;
    /**
     * 模板类型
     */
    private Long templateTypeId;

}
