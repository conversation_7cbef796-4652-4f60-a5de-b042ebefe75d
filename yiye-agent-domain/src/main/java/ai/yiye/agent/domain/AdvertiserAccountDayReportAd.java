package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * @Description: 广告主id-按天统计表实体  统计广告小时报
 */
@Data
@TableName(value = "marketing_data_advertiser_account_day_report_ad")
public class AdvertiserAccountDayReportAd implements Serializable {
    /**主键*/
    @TableId(type = IdType.AUTO)
    private Long id;

    /**投放账号id*/
    private String accountId;

    /**平台id，平台id;1巨量广告,2腾讯广告,3微信广告*/
    private Long platformId;

    /**广告主id，advertiser_account_id表主键id（相当于account_id与platform_id组合）*/
    private Long advertiserAccountId;

    /**广告动作时间*/
    private Instant dayTime;

    /**转化数*/
    private Long convertNum;

    /**曝光数*/
    private Long viewNum;

    /**点击数*/
    private Long clickNum;

    /**花费*/
    private Long cost;
    /**
     * 落地页pv
     */
    private Long landingPagePv;

}
