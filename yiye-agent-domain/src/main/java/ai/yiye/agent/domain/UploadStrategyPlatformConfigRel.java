package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.CompareValueType;
import ai.yiye.agent.domain.enumerations.ConfigValueType;
import ai.yiye.agent.domain.enumerations.Platform;
import ai.yiye.agent.domain.enumerations.StrategyConditionType;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 上报策略平台与配置项关系
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_upload_strategy_platform_config_rel", autoResultMap = true)
public class UploadStrategyPlatformConfigRel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 上报媒体（平台）
     */
    private Platform platformId;

    /**
     * 条件类型
     */
    private StrategyConditionType configType;

    /**
     * 配置项key，保存策略时使用
     */
    private String configKey;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 值的设置类型（INPUT_BOX=输入框，PULL_DOWN=下拉选择）
     */
    private ConfigValueType configValueType;

    /**
     * 下拉选择列表枚举展示映射关系
     */
    private JSONArray pullDownValues;

    /**
     * 比较符枚举展示映射关系
     */
    private JSONArray comparisonOperators;

    /**
     * 值单位类型
     */
    private CompareValueType configValueUnitType;

}
