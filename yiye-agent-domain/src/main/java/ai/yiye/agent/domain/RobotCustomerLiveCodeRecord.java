package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AddEnterpriseWechatStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 微信客服机器人活码发送记录表
 */
@Data
@TableName("robot_customer_live_code_record")
public class RobotCustomerLiveCodeRecord implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 机器人客服id
     */
    private String openKfId;

    /**
     * 场景值
     */
    private String scene;


    private String pid;

    /**
     * 广告链路下发的点击参数
     */
    private String clickId;

    /**
     * 外部联系人userid
     */
    private String externalUserId;

    /**
     * 企业微信id
     */
    private String corpId;

    /**
     * 落地页客服id
     */
    private Long wechatCustomerServiceId;

    /**
     * 落地页客服分组id
     */
    private Long wechatCustomerServiceGroupId;

    /**
     * pmp项目id，关联表：marketing_advertiser_account_group.id
     */
    private Long advertiserAccountGroupId;

    /**
     * 微信客服userId
     */
    private String wechatCustomerServiceUserId;

    /**
     * 加粉状态
     */
    private AddEnterpriseWechatStatus addEnterpriseWechatStatus;

    /**
     * 发送消息时对应的规则ID  enterprise_wechat_customer_msg_children_template
     */
    private Long msgChildrenTemplateId;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
