package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.Platform;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 上报策略与广告关联表
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_upload_strategy_advertise_rel", autoResultMap = true)
//@TableName(value = "landing_page_upload_strategy_advertise_rel_copy1", autoResultMap = true)
public class UploadStrategyAdvertiserRel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 上报媒体（平台）
     */
    private Platform platformId;

    /**
     * 上报策略ID
     */
    private Long uploadStrategyId;

    /**
     * 一叶广告组ID
     */
    private Long yiyeAdgroupId;

    /**
     * 项目ID
     */
    private Long advertiserAccountGroupId;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
