package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.DeleteStatus;
import ai.yiye.agent.domain.enumerations.LandingPageWechatCustomerContactStatus;
import ai.yiye.agent.domain.enumerations.LandingPageWechatCustomerContactVerifyStatus;
import ai.yiye.agent.domain.enumerations.OfficialWechatCustomerSubjectType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 客服分组公众号关联表
 * @Author：lilidong
 * @Date：2023/10/13 10:43
 */
@Data
@TableName("official_wechat_customer_service_group")
public class OfficialWechatCustomerServiceGroup {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * pmp的ID
     */
    private Long advertiserAccountGroupId;

    /**
     * 公众号名称
     */
    private String nickName;

    /**
     * 公众号appid
     */
    private String appId;

    /**
     * 公众号头像
     */
    private String headImg;

    /**
     *企微corpid
     */
    private String corpId;

    /**
     * 0:相同主体 1:不同主体
     */
    private OfficialWechatCustomerSubjectType subjectType;

    /**
     * 背景图
     */
    private String backgroundUrl;

    /**
     * 背景图宽度
     */
    private Integer backgroundWidth;

    /**
     * 背景图高度
     */
    private Integer backgroundHeight;

    /**
     * 二维码宽度
     */
    private Integer qrCodeWidth;

    /**
     * 二维码高度
     */
    private Integer qrCodeHeight;

    /**
     * 二维码位置宽度
     */
    private Integer qrCodeIndexLeft;

    /**
     * 二维码位置高度
     */
    private Integer qrCodeIndexTop;

    /**
     * 是否开启同一外部企业客户只能添加同一个员工，默认为1，0:关闭 1:开启
     */
    private Integer isExclusive;

    /**
     * 二维码变更时间默认5分钟 单位分钟
     */
    private Integer codeChange;

    /**
     * 二维码有效期 默认10分钟 单位分钟
     */
    private Integer  codeValidity;

    /**
     * 是否开启添加客服验证 0:否 1:是
     */
    private LandingPageWechatCustomerContactVerifyStatus officialWechatCustomerContactVerify;

    /**
     * 联系我二维码生成状态 0:未生成，1:生成中,2:已生成 3：创建失败
     */
    private LandingPageWechatCustomerContactStatus officialWechatCustomerContactStatus;

    /**
     * 没有可用客服的开始时间
     */
    private Instant noCustomerServiceStartAt;

    /**
     * 同主体公众号渠道二维码参数
     */
    private String officialWechatCustomerContactState;

    /**
     * 联系我 二维码链接 用于客服列表展示
     */
    private String officialWechatCustomerContactQrCode;

    /**
     * 联系我 二维码合并图片 上传素材ID-永久素材
     */
    private String officialWechatCustomerContactMaterialId;

    /**
     * 公众号内渠道联系我 二维码最终合成图片url
     */
    private String officialWechatCustomerContactBackgroundUrl;


    //公众号内渠道联系我 二维码最终合成图片七牛云path
    private String officialWechatCustomerContactQiniuPath;

    /**
     * 联系我 二维码链接配置ID-修改删除时使用 用于列表展示
     */
    private String officialWechatCustomerContactConfigId;

    /**
     * 联系我 二维码创建失败原因
     */
    private String officialWechatCustomerContactFailureReason;

    /**
     * 微信客服的分组ID
     */
    private Long wechatCustomerServiceGroupId;

    /**
     * 自定义二维码头像
     */
    private String officialCustomHeadImg;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 逻辑删除的标记
     */
    private DeleteStatus deleteStatus;

    /**
     * 防炸群开关,0-关闭；1-开启
     */
    private Integer explosionGroupFlag;


}

