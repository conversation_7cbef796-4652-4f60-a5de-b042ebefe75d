package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.ExecuteDateType;
import ai.yiye.agent.domain.enumerations.ExecuteTimeType;
import ai.yiye.agent.domain.enumerations.UploadSonStrategyOperateType;
import ai.yiye.agent.domain.enumerations.UploadSonStrategyRatioEnum;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;

/**
 * 上报子策略 - 执行操作
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadSonStrategyExecuteOperation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 执行操作ID
     */
    private Integer id;

    /**
     * 操作类型（使用配置项信息configKey字段值）
     */
    private UploadSonStrategyOperateType operateType;

    /**
     * 上报/上调/下调比例
     */
    private UploadSonStrategyRatioEnum ratio;

    /**
     * 出价值/深度出价值
     */
    private BigDecimal bidValue;

    /**
     * 调整次数
     */
    private Integer adjustCount;

}
