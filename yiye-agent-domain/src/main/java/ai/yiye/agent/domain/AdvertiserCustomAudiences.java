package ai.yiye.agent.domain;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/26 11:29
 */

import ai.yiye.agent.domain.marketing.data.InsertIdApply;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@NoArgsConstructor
@TableName(value = "marketing_data_custom_audiences")
@InsertIdApply(value = true)
public class AdvertiserCustomAudiences implements Serializable {
    // 字段更新约束
    @TableField(exist = false)
    public static final String[] CONFLICTS = new String[]{"custom_audience_id", "platform_id", "account_id"};
    private Long id;
    //账号id
    private String accountId;
    //腾讯客户人群id
    private Long customAudienceId;
    private Integer platformId;
    //腾讯客户人群文件id
    private Long customAudienceFileId=0L;
    //人群包名
    private String name;
    //描述
    private String description;
    //类型
    private String type;
    //状态
    private String customStatus;
    //人群覆盖数
    private Long userCount;
    //人群详细信息
    private JSONObject audienceSpec;
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    @TableField(fill = FieldFill.UPDATE)
    private Instant updatedAt;
    @TableField(exist = false)
    private String createdTimeStr;

}
