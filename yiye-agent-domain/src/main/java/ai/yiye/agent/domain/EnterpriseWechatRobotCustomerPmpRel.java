package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 微信机器人客服 - PMP关系 - 表
 */
@Data
@NoArgsConstructor
@TableName("enterprise_wechat_robot_customer_pmp_rel")
public class EnterpriseWechatRobotCustomerPmpRel implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 微信机器人客服id
     */
    private Long wechatRobotCustomerId;

    /**
     * pmp项目id
     */
    private Long advertiserAccountGroupId;

    /**
     * agentId
     */
    private String agentId;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
