package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.NewVersionDataStatisticsTaskModuleEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

/**
 * 数据2.0定时任务失败记录表
 */
@Data
@TableName("new_version_data_statistics_timed_task_record")
public class NewVersionDataStatisticsTaskRecord implements Serializable {

    @TableId(type = IdType.AUTO)
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;


    /**
     * 用户标识
     */
    private String agentId;

    /**
     * 统计日期
     */
    private LocalDate statisticDate;

    /**
     * 任务状态,0-正常；1-失败
     */
    private Integer taskStatus;

    /**
     * 任务模块
     */
    private NewVersionDataStatisticsTaskModuleEnum taskModule;

    /**
     * 任务描述
     */
    private String taskDescription;


    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
