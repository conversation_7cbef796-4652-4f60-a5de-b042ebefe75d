package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 用户与项目关系表
 *
 * <AUTHOR>
 * @since 2020-05-19 10:37:00
 */
@Data
@TableName("marketing_advertiser_account_group_user_rel")
public class AdvertiserAccountGroupUserRel implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 项目id
     */
    private Long advertiserAccountGroupId;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
