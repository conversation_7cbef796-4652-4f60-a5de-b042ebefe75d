package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2021/7/23 16:43
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("marketing_data_keyword")
public class MarketingDataKeyword implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String[] CONFLICTS = new String[]{"account_id", "keyword_id"};

    @TableId(type = IdType.AUTO)
    private Long id;

    private Integer platformId;
    private String accountId;
    private Long keywordId;
    private String keyword;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
