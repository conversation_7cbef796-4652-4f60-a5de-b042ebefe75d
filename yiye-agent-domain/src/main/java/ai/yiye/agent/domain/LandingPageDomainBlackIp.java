package ai.yiye.agent.domain;


import ai.yiye.agent.domain.enumerations.IpType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 域名绑定的IP控制表
 */
@Data
@TableName("landing_page_domain_black_ip")
public class LandingPageDomainBlackIp {


    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 对应landing_page_domain_binding_info.id
     */
    private Long landingPageDomainId;

    /**
     * ip地址
     */
    private String ip;

    /**
     * ip类型
     */
    private IpType ipType;

    /**
     * 操作人的userId,对应ucenter_user.id
     */
    private Long userId;

    /**
     * 操作人的账户名称
     */
    private String operatorAccountName;

    /**
     * 对应的域名
     */
    private String domainName;



    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
