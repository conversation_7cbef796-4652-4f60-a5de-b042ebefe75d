package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 落地页上报配置 - oppo投放账户表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_upload_oppo_account_config", autoResultMap = true)
public class LandingPageUploadOppoAccountConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * pmp项目id
     */
    private Long advertiserAccountGroupId;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户ID（广告主id）（owner_id）
     */
    private String accountId;

    /**
     * app_id（api_id：API授权接入方唯一身份标识）
     */
    private String appId;

    /**
     * api_key
     */
    private String apiKey;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
