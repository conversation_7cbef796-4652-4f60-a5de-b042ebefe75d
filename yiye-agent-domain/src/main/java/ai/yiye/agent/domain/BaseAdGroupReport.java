package ai.yiye.agent.domain;

import ai.yiye.agent.domain.util.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 数据概况基类（不涉及到需要计算的属性，eg: xx率，xx成本）
 * @author: wangchengBigDecimal
 * @time: 2021/5/11 11:56
 */
@Data
public class BaseAdGroupReport implements Serializable {

    /**
     * 曝光量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal viewNum;

    /**
     * 点击量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal clickNum;

    /**
     * 花费
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal cost;

    /**
     * 千次展示均价
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal thousandImpressAvgPrice;

    /**
     * 点击均价
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal avgPrice;

    /**
     * 落地页pv
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal landingPagePv;


    /**
     * 来源跳转页pv
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal flowSourceJumpPagePV;



    /**
     * 企业推落地页pv
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal qiyetuiPageViewNum;

    /**
     * 公众号历史文章页pv
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal wechatOfficialArticlePageViewNum;

    /**
     * 落地页uv
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal landingPageUv;

    /**
     * 填单提交数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal fillCountNum;

    /**
     * 订单提交数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal orderNum;

    /**
     * 订单完成数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal orderFinishNum;

    /**
     * 平均停留时长
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal landingAvgStay;

    /**
     * 表单预约量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal formAppointmentNum;

    /**
     * 表单预约人数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal formAppointmentPersonCount;

    /**
     * 订单金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal orderAmount;

    /**
     * 下单客单价
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal orderUnitPrice;

    /**
     * 下单ROI
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal orderROI;

    /**
     * 付费行为量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal paymentNum;

    /**
     * 付费金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal paymentAmount;

    /**
     * 首次付费行为人数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal firstPaymentPersonNum;

    /**
     * 公众号关注量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal officialFocusNum;

    /**
     *  公众号关注量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal followOfficialAccountNum;


    /**
     * 销售线索量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal saleClueNum;

    /**
     * 销售线索人数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal saleCluePersonNum;

    /**
     * 有效线索量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal validClueNum;

    /**
     * 有效线索人数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal validCluePersonNum;

    /**
     * 公众号关注数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal officialFocusCount;

    /**
     * 有效线索数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal validClueCount;

    /**
     * 电话建联数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal callLinkCount;

    /**
     * 个微建联数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal personWechatLinkCount;

    /**
     * 预约数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal appointmentCount;

    /**
     * 试听数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal auditionCount;

    /**
     * 试听完课数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal auditionedClassCount;

    /**
     * 试用数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal trialCount;

    /**
     * 支付定金数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal paymentDepositCount;

    /**
     * 支付数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal payCount;

    /**
     * 转化数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal convertCount;

    /**
     * 注册数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal registerCount;

    /**
     * 激活数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal activationCount;

    /**
     * APP下载完成量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal appDownloadFinishCount;

    /**
     * APP安装量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal appInstallCount;

    /**
     * APP激活总量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal appActivationNum;

    /**
     * APP注册量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal appRegisterNum;

    /**
     * APP次日留存人数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal appRetainedPersonNum;

    /**
     * APP付费行为量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal appPayNum;

    /**
     * APP付费金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal appPayAmount;

    /**
     * 分享数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal shareCount;

    /**
     * 评论数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal discussCount;

    /**
     * 点赞数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal thumbUpCount;

    /**
     * 按钮数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal buttonCount;

    /**
     * 关注数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal followNum;

    /**
     * 25%播放数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal videoPlay25Num;

    /**
     * 50%播放数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal videoPlay50Num;

    /**
     * 75%播放数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal videoPlay75Num;

    /**
     * 100%播放数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal videoPlay100Num;

    /**
     * 播放数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal playNum;

    /**
     * 平均播放时长
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal avgPlayTime;

    /**
     * 有效播放数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal validPlayNum;

    /**
     * 停留时长
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal lengthOfStay;

    /**
     * 长按二维码识别数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal identifyQrCodeNum;

    /**
     * 加企业微信数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal addWorkWechatNum;

    /**
     * 目标转化量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal convertNum;

    /**
     * 深度转化量
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal deepConvertNum;

    /**
     * 下单量(网页)
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal placeOrderNum;

    /**
     * 线索填单数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal clueFillNum;

    /**
     * 线索接通数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal clueConnectNum;

    /**
     * 线索有效数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal clueEffectiveNum;

    /**
     * 报班数
     */
    @JsonSerialize(using = BigDecimalSerialize.class,nullsUsing =  BigDecimalSerialize.class)
    private BigDecimal signClassNum;


    /**
     * 电商商品购买数
     */
    private Long onlineShopBuyGoodsSuccessNum;


}
