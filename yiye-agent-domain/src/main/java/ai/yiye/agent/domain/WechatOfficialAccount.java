package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * 公众号配置(PaymentWetchatOfficialAccount)实体类
 *
 * <AUTHOR>
 * @since 2020-05-19 16:26:46
 */
@Data
@TableName("payment_wechat_official_account")
public class WechatOfficialAccount implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * appId
     */
    private String appId;
    /**
     * 公众号名称
     */
    private String name;
    /**
     * 公众号秘钥
     */
    private String appSecret;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件内容
     */
    private String fileContent;
    /**
     * PMP账户ID
     */
    private Long advertiserAccountGroupId;

    /**
     * PMP账户名称
     */
    @TableField(exist = false)
    private String advertiserAccountGroupName;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    @TableField(exist = false)
    private String agentId;

    /**
     * 模糊搜索的值
     */
    @TableField(exist = false)
    private String selectFieldValue;

    /**
     * 绑定商户号数
     */
    @TableField(exist = false)
    private Integer wechatMerchantAccountCount;

    @TableField(exist = false)
    private List<WechatMerchantAccount> wechatMerchantAccountList;

    @TableField(exist = false)
    private List<String> pmps;

}
