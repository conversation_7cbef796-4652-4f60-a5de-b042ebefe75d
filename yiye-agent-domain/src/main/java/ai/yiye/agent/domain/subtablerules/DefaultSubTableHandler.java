package ai.yiye.agent.domain.subtablerules;

import ai.yiye.agent.common.multidatasource.TenantContextHolder;
import ai.yiye.agent.common.multidatasource.TenantSchemaHolder;
import ai.yiye.agent.domain.constants.DbConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 默认的分表处理起
 */
@Component
public class DefaultSubTableHandler extends BaseSubTableHandler {

    @Override
    public SubRule getSubRule() {
        return SubRule.RANGE;
    }

    @Override
    public String getSubTableNameByRange(Class<?> clazz, String field, Object... parameters) {
        String original = getOriginalTableName(clazz);
        if (StringUtils.isNotEmpty(original)) {
            // 传入首个字段为时间类型
            if (parameters[0] instanceof LocalDateTime) {
                // 时间默认按月进行分区
                return original + "_" + DateTimeFormatter.ofPattern("yyyy_MM").format((LocalDateTime) (parameters[0]));
            } else if (parameters[0] instanceof Instant) {
                LocalDateTime dateTime = LocalDateTime.ofInstant((Instant) (parameters[0]), ZoneId.systemDefault());
                return original + "_" + DateTimeFormatter.ofPattern("yyyy_MM").format(dateTime);
            } else {
                // TODO ...More
            }
        }
        return null;
    }

    @Override
    public String getSubTableNameByHash(Class<?> clazz, String field, Object... parameters) {
        return null;
    }

    @Override
    public String getSubTableCreateSqlByRange(Class<?> clazz, String field, Object... parameters) {
        // 当前分区的语句仅只支持postgresql
        String subField = getSubTableField(clazz, field);
        String original = getOriginalTableName(clazz);
        String tableName = getSubTableNameByRange(clazz, field, parameters);
        // 满足分表的表以及分表的规则
        if (StringUtils.isNotEmpty(subField) && StringUtils.isNotEmpty(tableName)) {
            if (parameters[0] instanceof LocalDateTime || parameters[0] instanceof Instant) {
                LocalDateTime time = null;
                if (parameters[0] instanceof LocalDateTime) {
                    time = (LocalDateTime) parameters[0];
                } else {
                    time = LocalDateTime.ofInstant((Instant) parameters[0], ZoneId.systemDefault());
                }
                // 获取参数内的起始日期
                String startDay = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(time.with(TemporalAdjusters.firstDayOfMonth()));
                String endDay = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(time.with(TemporalAdjusters.firstDayOfNextMonth()));
                // 拼接生成参数的SQL语句
                StringBuilder createSql = new StringBuilder();
                createSql.append("CREATE TABLE IF NOT EXISTS ")
                    .append(tableName)
                    .append(" (PRIMARY KEY (ID), CHECK (")
                    .append(subField).append(" >= '")
                    .append(startDay).append("'::DATE ")
                    .append("AND ")
                    .append(subField).append(" < '")
                    .append(endDay).append("'::DATE)) ")
                    .append("INHERITS(").append(original).append(")");
                return createSql.toString();
            } else {
                // TODO...More
            }
        }
        return null;
    }

    @Override
    public String getSubTableCreateSqlByHash(Class<?> clazz, String field, Object... parameters) {
        return null;
    }

    @Override
    public String getSubTableIndexSqlByRange(Class<?> clazz, String field, HandlerMapper mapper, Object... parameters) {
        //这里要有一个切schema的操作
        String agentId = TenantContextHolder.get();
        String defaultOrNot = TenantSchemaHolder.get();
        boolean isDefaultSchema = DbConstants.DEFAULT.equals(defaultOrNot) || StringUtils.isEmpty(agentId);
        String schemaName = "";

        if (null == mapper) return null;
        // 首先获取原表的索引， 并进行原表的索引信息替换
        String original = getOriginalTableName(clazz);
        String subTableName = getSubTableName(clazz, field, parameters);
        if (StringUtils.isNotEmpty(original) && StringUtils.isNotEmpty(subTableName)) {

            if (isDefaultSchema) {
                schemaName = DbConstants.DEFAULT_POSTGRESQL_SCHEMA;
            } else {
                schemaName = DbConstants.POSTGRESQL_PREFIX + agentId;
            }

            // 不能通过execute来进行实现
            List<Map> originalIndexdefs = mapper.execute(String.format("SELECT INDEXDEF, INDEXNAME FROM PG_INDEXES WHERE TABLENAME = '%s' and schemaname = '" + schemaName + "'", original));
            if (!CollectionUtils.isEmpty(originalIndexdefs)) {
                return originalIndexdefs.stream().map(m -> {
                    String indexName = (String) m.get("indexname");
                    String indexdef = ((String) m.get("indexdef")).replace("INDEX", "INDEX IF NOT EXISTS").replace(original, subTableName);
                    // 替换索引的名称以免出现重复
                    if (!indexName.contains(original)) {
                        indexdef = indexdef.replace(indexName, subTableName + "_" + indexName.substring(0, (indexName.length() > (62 - subTableName.length()) ? (62 - subTableName.length()) : indexName.length())));
                    }
                    return indexdef;
                }).collect(Collectors.joining(";"));
            }
        }
        return null;
    }

    @Override
    public String getSubTableIndexSqlByHash(Class<?> clazz, String field, HandlerMapper mapper, Object... parameters) {
        return null;
    }

}
