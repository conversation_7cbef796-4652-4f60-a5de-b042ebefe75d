package ai.yiye.agent.domain.enumerations;


import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 公众号助手-广告来源
 */
@Getter
public enum FollowSourcePlatform {

    //巨量广告
    DOUYIN(1, "抖音", "oceanengine", Collections.singletonList(Platform.OCEAN_ENGINE), Collections.singletonList(FlowSource.DOU_YIN)),
    DOU_YIN_JI_SU_BAN(2, "抖音极速版", "oceanengine", Collections.singletonList(Platform.OCEAN_ENGINE), Collections.singletonList(FlowSource.DOU_YIN_JI_SU_BAN)),
    DOU_YIN_HUO_SHAN_BAN(3, "抖音火山版", "oceanengine", Collections.singletonList(Platform.OCEAN_ENGINE), Collections.singletonList(FlowSource.DOU_YIN_HUO_SHAN_BAN)),
    JIN_RI_TOU_TIAO(4, "今日头条", "oceanengine", Collections.singletonList(Platform.OCEAN_ENGINE), Collections.singletonList(FlowSource.JIN_RI_TOU_TIAO)),
    JIN_RI_TOU_TIAO_JI_SU_BAN(5, "今日头条极速版", "oceanengine", Collections.singletonList(Platform.OCEAN_ENGINE), Collections.singletonList(FlowSource.JIN_RI_TOU_TIAO_JI_SU_BAN)),
    FAN_QIE_XIAO_SHUO(6, "番茄小说", "oceanengine", Collections.singletonList(Platform.OCEAN_ENGINE), Collections.singletonList(FlowSource.FAN_QIE_XIAO_SHUO)),
    XI_GUA_SHI_PIN(7, "西瓜视频", "oceanengine", Collections.singletonList(Platform.OCEAN_ENGINE), Collections.singletonList(FlowSource.XI_GUA_SHI_PIN)),
    OCEAN_ENGINE_OTHER(8, "其他", "oceanengine", Collections.singletonList(Platform.OCEAN_ENGINE), Arrays.asList(FlowSource.XING_FU_LI, FlowSource.DONG_CHE_DI, FlowSource.CHUAN_SHAN_JIA)),

    //此版本发布后30天后可去除，反序列化找不到KUAISHOU-9会报错
    KUAISHOU(9, "快手", "other", Collections.singletonList(Platform.KUAISHOU), null),

    //其他广告
    TENCENT(10, "腾讯广告", "other", Collections.singletonList(Platform.TENCENT_AD), null),
    WECHAT(11, "微信广告", "other", Collections.singletonList(Platform.MP_AD), null),
    BAIDU(12, "百度", "other", Arrays.asList(Platform.BAIDU, Platform.BAIDU_MSG, Platform.BAIDU_SEARCH), null),
    ZHIHU(13, "知乎", "other", Collections.singletonList(Platform.ZHIHU), null),
    VIVO(14, "VIVO", "other", Collections.singletonList(Platform.VIVO), null),
    BILIBILI(15, "哔哩哔哩", "other", Collections.singletonList(Platform.BILIBILI), null),
    SINA(16, "新浪微博", "other", Collections.singletonList(Platform.SINA_WEIBO_SUPPER_FANS), null),
    TUIA(17, "推啊", "other", Collections.singletonList(Platform.TUIA), null),
    XIAOHONGSHU(18, "小红书", "other", Collections.singletonList(Platform.XIAOHONGSHU), null),
    IQIYI(19, "爱奇艺", "other", Collections.singletonList(Platform.IQIYI), null),

    //快手广告
    KUAI_SHOU_KWAI(20, "快手", "kuaishou", Collections.singletonList(Platform.KUAISHOU), Collections.singletonList(FlowSource.KUAI_SHOU_KWAI)),
    KUAI_SHOU_KSNEBULA(21, "快手极速版", "kuaishou", Collections.singletonList(Platform.KUAISHOU), Collections.singletonList(FlowSource.KUAI_SHOU_KSNEBULA)),
    KUAI_SHOU_OTHER(22, "其他", "kuaishou", Collections.singletonList(Platform.KUAISHOU), Collections.singletonList(FlowSource.KUAI_SHOU_OTHER)),

    OPPO(23, "OPPO营销平台", "other", Collections.singletonList(Platform.OPPO), null),
    SOUL(24, "Soul营销平台", "other", Collections.singletonList(Platform.SOUL), null),
    ALI_PAY(25, "支付宝数字推广平台", "other", Collections.singletonList(Platform.ALI_PAY), null),

    XIMALAYA(26, "喜马拉雅", "other", Collections.singletonList(Platform.XIMALAYA), null),

    ;

    @EnumValue
    private Integer code;

    //消息
    private String message;

    //巨量广告、其他广告
    private String type;

    //类别 巨量oceanengine、其他广告other
    private List<Platform> platformList;

    //巨量广告对应的流量来源
    private List<FlowSource> flowSourceList;

    FollowSourcePlatform(Integer code, String message, String type, List<Platform> platformList, List<FlowSource> flowSourceList) {
        this.code = code;
        this.message = message;
        this.type = type;
        this.platformList = platformList;
        this.flowSourceList = flowSourceList;
    }

    public static FollowSourcePlatform getByCode(Integer code) {
        return Arrays.stream(FollowSourcePlatform.values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(null);
    }

    //根据广告媒体类型，判断是否为广告
    public static FollowSourcePlatform getFollowSourcePlatformByPlatform(Platform platform) {
        return Objects.isNull(platform) ? null : Arrays.stream(FollowSourcePlatform.values()).filter(e -> e.getPlatformList().contains(platform)).findFirst().orElse(null);
    }

    //根据流量来源，判断是否为广告
    public static FollowSourcePlatform getFollowSourcePlatformByflowSource(FlowSource flowSource) {
        return Objects.isNull(flowSource) ? null : Arrays.stream(FollowSourcePlatform.values()).filter(e -> !CollectionUtils.isEmpty(e.getFlowSourceList()) && e.getFlowSourceList().contains(flowSource)).findFirst().orElse(null);
    }

    //根据平台类型获取广告来源类型
    public static List<Integer> getFollowSourcePlatformsByType(String type) {
        return StringUtils.isBlank(type) ? null : Arrays.stream(FollowSourcePlatform.values()).filter(e -> e.type.equals(type)).collect(Collectors.toList()).stream().map(FollowSourcePlatform::getCode).collect(Collectors.toList());
    }

}
