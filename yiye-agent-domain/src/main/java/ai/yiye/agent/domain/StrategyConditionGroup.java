package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.CompareType;
import ai.yiye.agent.domain.enumerations.CompareValueType;
import ai.yiye.agent.domain.enumerations.StrategyConditionType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

/**
 * 策略条件组
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_strategy_condition_group", autoResultMap = true)
public class StrategyConditionGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 策略ID(对应UploadStrategy的主键ID)
     */
    private Long uploadStrategyId;

    /**
     * 子策略ID(对应UploadSonStrategy的主键ID)
     */
    private Long uploadSonStrategyId;

    /**
     * 组号（1-n），为正整数
     */
    private Integer groupNumber;

    /**
     * 条件类型（BASIC=基础策略条件，CUSTOMIZED=自定义条件）
     */
    private StrategyConditionType conditionType;

    /**
     * 执行条件类型（使用配置项信息configKey字段值）
     */
    private String executeConditionType;

    /**
     * 比较类型
     */
    private CompareType compareType;

    /**
     * 比较值类型
     */
    private CompareValueType compareValueType;

    /**
     * 比较值---基础条件对应的输入框值
     */
    private BigDecimal compareValue;

    /**
     * 下拉选择值---自定义条件对应的选项值(可为null)
     */
    private Integer pullDownValue;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
