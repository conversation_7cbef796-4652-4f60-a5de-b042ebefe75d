package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum OperationActionEnum implements IEnum<Integer> {

    DISABLE,

    ENABLE
    ;

    @Override
    public Integer getValue() {
        return ordinal();
    }

    public static OperationActionEnum getEnumByValue(Integer value) {
        return Arrays.stream(OperationActionEnum.values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst().orElse(null);
    }

}
