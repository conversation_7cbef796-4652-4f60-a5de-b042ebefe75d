package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/1/4 11:29
 */

@TableName("landing_page_wechat_applet_pmp_rel")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LandingPageWechatAppletPmpRel {

    /**
     * id主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 小程序配置表id，关联表字段：landing_page_wechat_applet_config.id
     */
    @TableField("landing_page_wechat_applet_id")
    private Long landingPageWechatAppletId;

    /**
     * pmp项目id，关联表：marketing_advertiser_account_group.id
     */
    @TableField("advertiser_account_group_id")
    private Long advertiserAccountGroupId;

    /**
     * 数据源id，关联表：agent_conf.agent_id
     */
    @TableField("agent_id")
    private String agentId;

    /**
     * 创建时间
     */
    @TableField("create_at")
    private LocalDateTime createAt;

}
