package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.EnterpriseWechatsUserOperationEvent;
import ai.yiye.agent.domain.enumerations.WorkWechatApiLicenseActionStatus;
import ai.yiye.agent.domain.typehandlers.JSONArrayHandler;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.*;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * <p>
 * 企业微信接口许可动作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class WorkWechatApiLicenseActionRecord implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 企微订单ID
     */
    private String orderId;

    //真实订单ID 为了区分出手动录入的订单
    private String realOrderId;

    /**
     * 企微ID
     */
    private String corpId;

    /**
     * 金额
     */
    private Long amount;

    /**
     * 激活/续期成员
     */
    @TableField(typeHandler = JSONArrayHandler.class)
    private JSONArray users;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作人ip
     */
    private String operatorIp;

    /**
     * 0:新购 1:续期 2:接口激活 3:接口迁移 4:购买并激活 5:手动购买
     */
    private EnterpriseWechatsUserOperationEvent actionType;

    /**
     * 接口许可数量
     */
    private Integer apiLicenseNum;

    /**
     * 购买时长 单位：月
     */
    private Integer duration;

    /**
     * 转移出去成员的userid
     */
    private String handoverUserId;

    /**
     * 接收成员的userid
     */
    private String takeoverUserId;

    /**
     * 状态 0:处理中 1:处理成功 2:处理失败
     */
    private WorkWechatApiLicenseActionStatus status;

    /**
     * 失败原因
     */
    private String failReason;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    private String agentId;

    private Long organizationId;

    @Data
    public static class User {
        public User() {} // 默认构造方法
        private String userId;

        private String userName;

        private String failReason;

        private WorkWechatApiLicenseActionStatus status;
    }

    public List<User> convertUsers() {
        if (this.users == null) {
            return null;
        }
        return JSONArray.parseArray(users.toJSONString(), User.class);
    }

}
