package ai.yiye.agent.domain.enumerations;

import lombok.Getter;

@Getter
public enum EnterpriseWechatRobotCustomerAbnormalNoticeType {

    MSG_SEND_RESTRICT("监测到该微信客服机器人发送消息功能被限制"),
    MSG_SEND_MANY_FAIL("监测到该微信客服机器人多次发送消息失败"),
    SUBJECT_UNAUTHORIZED("所属企业微信主体未认证"),
    MEMBER_UN_LOG_ON("所属企业微信未有成员登录企业微信App（排查方法：企业至少一个成员通过手机号验证/微信授权登录企业微信App即可）"),
    SECURITY_RESTRICT("触发企业微信安全限制"),
    RECOVERY_FAIL("微信客服机器人智能恢复创建失败"),
    CREATE_LIMIT("%s（%s）微信客服机器人创建达至上限，无法新建");

    /**
     * 描述
     */
    private final String description;

    EnterpriseWechatRobotCustomerAbnormalNoticeType(String description) {
        this.description = description;
    }

}
