package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/1 15:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("marketing_wechat_mini_program_punish")
public class MarketingWechatMiniProgramPunishInfo implements Serializable {
    private Long id;
    private Instant createTime;
    private String illegalReason;
    private String illegalContent;
    private String ruleUrl;
    private String ruleName;
    private Long miniProgramId;
}
