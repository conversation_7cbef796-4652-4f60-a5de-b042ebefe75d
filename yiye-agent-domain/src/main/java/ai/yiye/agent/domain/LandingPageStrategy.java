package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 落地页策略
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_strategy", autoResultMap = true)
public class LandingPageStrategy implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 落地页ID
     */
    private Long landingPageId;
    /**
     * 策略名称
     */
    private String strategyName;
    /**
     * 触发条件
     */
    private ConditionType conditionType;
    /**
     * 是否延时展示,延时=true，实时=false
     */
    private boolean delayShow;
    /**
     * 延时展示时间（单位秒）
     */
    private Integer delayShowTime;
    /**
     * 是否仅针人群（true=是，false=否）
     */
    private boolean executeType;
    /**
     * 人群类型
     */
    private CrowdType crowdType;
    /**
     * 人群判断类型
     */
    private CrowdJudgeType crowdJudgeType;
    /**
     * 重复访问范围
     */
    private RepeatAccessScope repeatAccessScope;
    /**
     * 时间范围
     */
    private TimeScope timeScope;
    /**
     * 重复访问次数
     */
    private Integer repeatAccessCount;
    /**
     * 展示内容
     */
    private ShowType showType;
    /**
     * 跳转url
     */
    private String skipUrl;
    /**
     * 弹窗模板ID
     */
    private Long popupId;
    /**
     * 页面参数
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject content;

    /**
     * 淘宝商品ID
     */
    private String taoBaoGoodId;

    /**
     * 京东商品链接url
     */
    private String jingDongGoodsUrl;
    /**
     * 微信客服用户id
     */
    private String robotCustomerServiceId;
    /**
     * 企业推小程序配置
     */
    private String workMiniConfig;

    /**
     * 拼多多商品id
     */
    private String pddGoodsId;

    /**
     * 跳转电商APP配置
     */
    private String commerceConfig;

    /**
     * 是否开启微信客服
     */
    private Boolean customerServiceOpen;

    /**
     * 微信客服id
     */
    private Long customerServiceId;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
    //新增一个微信客服的config
    private String wechatRobotConfig;

}
