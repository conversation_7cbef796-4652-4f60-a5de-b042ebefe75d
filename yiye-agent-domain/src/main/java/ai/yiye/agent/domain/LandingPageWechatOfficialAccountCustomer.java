package ai.yiye.agent.domain;

import ai.yiye.agent.domain.dto.UploadRecordReturnMessageDto;
import ai.yiye.agent.domain.enumerations.*;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * 公众号粉丝关注记录表（公众号客资表）
 */
@Data
@TableName("landing_page_wechat_official_account_customer")
public class LandingPageWechatOfficialAccountCustomer {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 公众号appid
     */
    private String appId;

    /**
     * 公众号名称（直接持久化）
     */
    private String nickName;

    /**
     * 用户头像（url地址）
     */
    private String userPhoto;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 关注成功后匹配对应的pid
     */
    private String matchingPid;

    /**
     * 关注时间（默认新增时间，不可修改）
     */
    private Instant followTime;

    /**
     * 关注状态
     */
    private FollowStatus followStatus;

    /**
     * 取消关注时间（取消关注回调修改）
     */
    private Instant unFollowTime;

    /**
     * 关注渠道
     */
    private SubscribeSceneType subscribeScene;

    /**
     * 关注来源：0:来源于广告 1:非广告来源 2:全部来源
     */
    private OfficialFollowSource officialFollowSource;

    /**
     * 关注来源-来源广告-对应值（officialFollowSource为0时，显示该字段值中对应媒体）
     */
    private FollowSourcePlatform followSourcePlatform;

    /**
     * 关注来源-来源广告-对应值-对应媒体名称（用于返回前端使用）
     */
    @TableField(exist = false)
    private Platform followSourcePlatformName;

    /**
     * openid
     */
    private String openid;

    /**
     * unionid
     */
    private String unionid;

    /**
     * 关注公众号落地页所属项目ID
     */
    private Long advertiserAccountGroupId;

    /**
     * 关注公众号落地页所属项目
     */
    @TableField(exist = false)
    private String advertiserAccountGroupName;

    /**
     * 关注公众号落地页ID
     */
    private Long followLandingPageId;


    /**
     * 媒体来源
     */
    private Integer platformId;

    /**
     * 关注公众号落地页名称
     */
    @TableField(exist = false)
    private String followLandingPageName;

    /**
     * 关注公众号落地页渠道ID
     */
    private Long followChannelId;

    /**
     * 关注公众号落地页渠道名称
     */
    @TableField(exist = false)
    private String followChannelName;

    /**
     * 关注公众号落地页URL
     */
    private String followLandingPageUrl;

    /**
     * 加粉页pid
     */
    private String addEnterpriseWechatPid;

    /**
     * 加粉时间（关注公众号通过蓝链或发送二维码图片添加企业微信时间、企业微信接口中返回的添加时间）
     */
    private Instant externalUseridAddAt;

    /**
     * 加粉状态（好友 / 删除好友  添加成功后收到企微对应的“删除企业客户事件”或“删除跟进成员事件”时，状态标记为删除好友）
     */
    private AddEnterpriseWechatStatus addEnterpriseWechatStatus;

    /**
     * 删除好友时间（删除好友时间   对应事件“删除企业客户事件”或“删除跟进成员事件”中的时间戳TimeStamp）
     */
    private Instant deleteWechatTime;

    /**
     * 关注后加粉方式（公众号蓝链加粉 /  二维码图片长按识别加粉）
     */
    private FollowedAddEnterpriseWechat followedAddEnterpriseWechat;

    /**
     * 用户企业微信userid
     */
    private String wechatAppletExternalUserid;

    /**
     * 客服名称
     */
    private String wechatAppletGroupChatName;

    /**
     * 客服userid
     */
    private String wechatAppletUserid;

    /**
     * 客服所属项目ID
     */
    private Long wechatAppletUserPmpId;

    /**
     * 客服所属项目名称
     */
    @TableField(exist = false)
    private String wechatAppletUserPmpName;

    /**
     * 企业微信id
     */
    private String corpid;

    /**
     * 所属企业微信（企业微信名称）
     */
    private String enterpriseWechatName;

    /**
     * 性别（加粉用户）（企业微信-外部联系人-性别）
     */
    private Sex externalUserSex;

    /**
     * 添加企业微信方式
     */
    private WechatAppletAddWay wechatAppletAddWay;

    /**
     * 企业微信标签
     */
    @TableField(exist = false)
    private String tagNames;

    /**
     * 关注后加粉落地页所属项目ID
     */
    private Long followAddWechatPmpId;

    /**
     * 关注后加粉落地页所属项目
     */
    @TableField(exist = false)
    private String followAddWechatPmpName;

    /**
     * 关注后加粉落地页ID
     */
    private Long followAddWechatLandingPageId;

    /**
     * 关注后加粉落地页名称
     */
    @TableField(exist = false)
    private String followAddWechatLandingPageName;

    /**
     * 关注后加粉落地页渠道ID
     */
    private Long followAddWechatChannelId;

    /**
     * 关注后加粉落地页渠道名称
     */
    @TableField(exist = false)
    private String followAddWechatChannelName;

    /**
     * 关注后加粉落地页URL
     */
    private String followAddWechatChannelUrl;

    /**
     * 加粉填单id，关联表：submit_data.id
     */
    private Long submitDataId;

    /**
     * 加粉客资id，关联表：customer.id
     */
    private Long customerId;

    /**
     * 上报记录（前端显示值）
     */
    @TableField(exist = false)
    private List<UploadRecordReturnMessageDto> uploadRecordReturnMessageDtos;

    /**
     * 上报记录（数据库存储值：加粉上报）
     */
    @TableField(exist = false)
    private JSONArray customerUploadRecordList;

    /**
     * 上报记录（数据库存储值：公众号上报）
     */
    @TableField(exist = false)
    private JSONArray officialCustomerUploadRecordList;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 客户群名称（客户添加企业微信群的群名称）
     */
    private String groupChatName;

    /**
     * 客户群ID（客户添加企业微信群的群ID）
     */
    private String groupChatId;

    /**
     * 加群用户类型（企业内部成员 / 外部联系人）
     */
    private EnterpriseWechatUserType enterpriseWechatUserType;

    /**
     * 群内昵称（客户添加企业微信群后设置的群内展示名称）
     */
    private String groupNickname;

    /**
     * 入群方式（客户添加企业微信群的方式）
     */
    private JoinScene groupChatJoinScene;

    /**
     * 入群时间
     */
    private Instant groupChatJoinTime;

    /**
     * 退群方式
     */
    private QuitScene groupChatQuitScene;

    /**
     * 退群时间
     */
    private Instant groupChatQuitTime;

    /**
     * 是否勾选“进群后X秒（时间）后未退群再上报”的选项,默认未勾选
     */
    private Boolean checkUploadNotDropoutWechatGroupSuccess;

    /**
     * 设置进群后X秒（时间）后未退群再上报的时间
     */
    private Integer checkUploadNotDropoutWechatGroupSuccessTime;

    /**
     * 客户群所属企业微信名称（固化名称）
     */
    private String groupChatWorkWechatName;

    /**
     * 客户群所属企业微信公司id，关联表：enterprise_wechat.corpid
     */
    private String groupChatCorpid;

    /**
     * 客户群外部联系人userid
     */
    private String groupChatExternalUserid;

    /**
     * 巨量广告账户ID
     */
    private String oceanAccountId;

    /**
     * 巨量广告项目ID
     */
    private String oceanProjectId;

    /**
     * 巨量广告ID
     */
    private String oceanPromotionId;

    /**
     * 腾讯广告账户ID
     */
    private String tencentAccountId;

    /**
     * 腾讯广告组ID
     */
    private String tencentAdGroupId;

    /**
     * 腾讯广告ID
     */
    private String tencentAdId;

    /**
     * 喜马拉雅广告组ID
     */
    private String ximalayaTaskId;

    /**
     * 喜马拉雅计划ID
     */
    private String ximalayaPlanId;

    /**
     * 喜马拉雅创意ID
     */
    private String ximalayaMaterialId;

    /**
     * 客户群所属企业微信公司名称 + 公司id
     */
    @TableField(exist = false)
    private String groupChatWorkWechatNameAndCorpid;

    /**
     * 数据类型 INSERT:新增 UPDATE:更新
     */
    @TableField(exist = false)
    private OfficialAccountCustomerWebhookType messageType;

    /**
     * 开始时间
     */
    @TableField(exist = false)
    private Instant startTime;

    /**
     * 结束时间
     */
    @TableField(exist = false)
    private Instant endTime;

    @TableField(exist = false)
    private String agentId;

    //客资状态 1：关注后成功添加企业微信 2：成功添加企业微信后入群
    @TableField(exist = false)
    private Integer customerStatus;

}
