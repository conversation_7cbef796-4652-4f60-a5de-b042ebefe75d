package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.WidgetTemplateType;
import ai.yiye.agent.domain.enumerations.WidgetType;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * 弹层模板中拼接组件与 表单 订单的关系表
 */
@Data
@TableName("landing_page_popup_widget_template_rel")
public class LandingPagePopupWidgetTemplateRel {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 弹窗模板id，关联表：landing_page_widget_template.id
     */
    private Long landingPagePopupWidgetTemplateId;

    /**
     * 模板组件id，关联表：landing_page_widget_template.id
     */
    private Long landingPageWidgetTemplateId;
    /**
     * 组件类型
     * 0 普通组件
     * 1 嵌套组件-拼接组件
     */
    private WidgetType widgetType;
    /**
     * 组件对应的uuid
     */
    private String uuid;

    /**
     * 0-url跳转 1-文字提示
     */
    private String actionType;

    private String actionName;

    private String actionTarget;
    /**
     * 模板类型
     */
    private WidgetTemplateType wtType;
    /**
     * 上級模板類型
     */
    private WidgetTemplateType parentWidgetType;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject ext;

    /**
     * 组件详情
     */
    @TableField(exist = false)
    private LandingPageWidgetTemplate landingPageWidgetTemplate;

}
