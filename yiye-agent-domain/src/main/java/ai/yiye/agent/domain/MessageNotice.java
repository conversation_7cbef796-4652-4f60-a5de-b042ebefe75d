package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.MarketingNoticeReadStatus;
import ai.yiye.agent.domain.enumerations.MarketingNoticeSubType;
import ai.yiye.agent.domain.enumerations.MarketingNoticeType;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

@Data
@TableName("message_notice")
public class MessageNotice implements Serializable {
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
    //消息标题
    private String noticeTitle;
    //消息内容
    private String noticeContent;

    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject ext;
    //消息类型
    private MarketingNoticeType noticeType;
    //消息子类型
    private MarketingNoticeSubType noticeSubType;

    //消息阅读状态
    private MarketingNoticeReadStatus read=MarketingNoticeReadStatus.NOT_READ;
    //PMPId
    private Long advertiserAccountGroupId;
    //状态
    private Integer status;

    @TableField(exist = false)
    private String agentId;
    @TableField(exist = false)
    private String corpid;

    @TableField(exist = false)
    private String emailContent;

    //客服信息
    @TableField(exist = false)
    private LandingPageWechatCustomerService landingPageWechatCustomerService;
    //客服分组id
    @TableField(exist = false)
    private Long customerServiceGroupId;
    //已知的配置
    @TableField(exist = false)
    private LandingPagePmpParamsConfig landingPagePmpParamsConfig;

}
