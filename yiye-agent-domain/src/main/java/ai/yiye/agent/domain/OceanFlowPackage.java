package ai.yiye.agent.domain;

import ai.yiye.agent.domain.clickhousetypehandlers.JSONStringTypeHandler;
import ai.yiye.agent.domain.enumerations.AdvertiserAccountSystemStatus;
import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import ai.yiye.agent.domain.marketing.data.UpdateIgnore;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/14 17:40
 */
@Data
@TableName(value ="marketing_data_ocean_union_flow",autoResultMap = true)
public class OceanFlowPackage implements Serializable {

    public static final String[] CONFLICTS = new String[]{"flow_package_id","platform_id","account_id"};
    @TableId(
        type = IdType.AUTO
    )
    private Long id;
    private String accountId;
    private Long flowPackageId;
    private String name;
    private String flowStatus;
    private String rit;
    // 一叶平台投放账户ID
    protected Long advertiserAccountId;

    // 数据所属平台ID
    protected Integer platformId;

    // 数据创建时间
    protected Instant createdAt;

    // 数据修改时间
    protected Instant updatedAt;

    // 其他数据信息
    @TableField(typeHandler = JSONStringTypeHandler.class)
    protected JSONObject ext;

    // 主优化师id
    protected Long optimizerId;

    @UpdateIgnore
    private AdvertiserAccountSystemStatus advertiserAccountStatus;
}
