package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.*;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@TableName("landing_page_wechat_applet_config")
public class LandingPageWechatAppletConfig implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 微信小程序appId
     */
    private String wechatAppletAppid;

    /**
     * secret秘钥
     */
    private String wechatAppletSecret;
    /**
     * 小程序类型
     */
    private AppletAttribute appletAttribute;

    /**
     * 小程序原始id
     */
    private String appletOriginalId;

    /**
     * 使用量（暂时没用到，当用量超过时，会自动切换小程序）
     */
    private Long useCount;

    /**
     * 小程序名称
     */
    private String wechatAppletName;
    /**
     * 小程序备注
     */
    private String appletRemarks;
    /**
     * 小程序账号
     */
    private String wechatAppletAccount;

    /**
     * 小程序 - 小程序状态：0-被封禁的小程序  1-正常的小程序  2-备用小程序 3-备用小程序取消
     */
    private Integer wechatAppletStatus;

    private WxVerifyInfoStatus wxVerifyInfo;

    /**
     * 小程序操作备注
     */
    private String remarks;

    /**
     * 用户自定义-备注信息
     */
    private String customRemarks;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Instant createdAt;
    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
    //token过期时间
    private Instant expireTime;

    private String accessToken;

    private String agentId;
    //头像url
    private String imgUrl;
    // 用户主体名称
    private String principalName;
    //二维码url
    private String qrcodeUrl;
    @TableField(exist = false)
    private Long accountGroupId;
    //微信小程序授权状态
    private WechatMiniAuthStatusEnum authStatus;
    //代码审核状态
    private WeAppAuditStatusEnum auditStatus;
    //该小程序是否可以被访问 0为不可以，1为可以
    private Boolean access;
    //代码审核被拒绝原因
    private String rejectReason;
    //refreshToken
    private String refreshToken;
    //绑定开放平台appid
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String openAppId;
    //当前小程序版本
    private String version;
    //即将发布并审核的小程序版本
    private String nextVersion;
    //审核编号
    private String auditid;
    //小程序首页落地页的的落地页id
    private Long landingPageId;
    //小程序首页落地页的渠道id
    private Long landingPageChannelId;
    //落地页名称
    @TableField(exist = false)
    private String landingPageName;
    @TableField(exist = false)
    private String url;

    //小程序是自建还是代开发
    private EnterpriseWechatType enterpriseWechatType;

    //违规信息
    @TableField(exist = false)
    private List<MarketingWechatMiniProgramPunishInfo> punishInfos;
    //账户授权状态码
    @TableField(exist = false)
    private String statusCode;
    //账户授权类型
    @TableField(exist = false)
    private AuthTypeEnum authType;
    //小程序是否为异常状态，通知用,这里用来检测小程序是否为可用状态，提醒用
    private ValidType valid=ValidType.VALID;
    private Instant authTime;
    @TableField(exist = false)
    private WechatAppletAbandonType wechatAppletAbandonType;

    /**
     * 微信第三方平台的appid
     */
    private String componentAppId;

    /**
     * 小程序内模板类型
     */
    private LandingPageType landingPageType;

    @TableField(exist = false)
    private List<String> pmps;

    /**
     * 状态筛选字符串逗号分割 1：异常 2：正在使用中的小程序 3：备用小程序
     */
    @TableField(exist = false)
    private String status;

    //状态筛选业务处理
    @TableField(exist = false)
    private List<String> statusList;

    //代码审核状态 业务处理
    @TableField(exist = false)
    private List<WeAppAuditStatusEnum> auditStatusList;

    //代码审核状态查询参数
    @TableField(exist = false)
    private String codeAuditStatus;

    /**
     * 因隐私协议导致审核失败，系统自动重新提交审核的次数
     */
    private Long auditNum;

    /**
     * 微信小程序-开启状态：true-开启（默认） false-关闭（无法获取微信小程序scheme码）
     */
    private Boolean openStatus;
    /**
     * 模板类型id
     */
    private Long templateTypeId;
    /**
     * 小程序版本id（对应我们的小程序版本）
     */
    private Long templateVersionId;
    @TableField(exist = false)
    private String appletTemplateType;

    /**
     * 分组-所属项目id
     */
    @TableField(exist = false)
    private Long advertiserAccountGroupId;

    /**
     * 是否逻辑删除：0-未删除  1-已删除
     */
    private YesOrNoEnum deleteStatus;

}
