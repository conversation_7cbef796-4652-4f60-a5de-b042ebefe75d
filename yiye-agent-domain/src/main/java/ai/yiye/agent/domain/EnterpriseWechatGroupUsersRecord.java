package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.EnterpriseWechatUserType;
import ai.yiye.agent.domain.enumerations.JoinScene;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 企业微信群-成员-详情表
 */
@Data
@TableName("enterprise_wechat_group_users_record")
public class EnterpriseWechatGroupUsersRecord {

    /**
     * 自增id主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 公司id
     */
    private String corpId;

    /**
     * 一叶-群id，关联表：enterprise_wechat_group_record.id
     */
    private Long groupId;

    /**
     * 客户群ID
     */
    private String chatId;

    /**
     * 群成员id
     */
    private String userid;

    /**
     * 成员类型。
     * 1 - 企业成员
     * 2 - 外部联系人
     */
    private EnterpriseWechatUserType type;

    /**
     * 微信客户openid（通过外部联系人userid调用接口置换获得）
     */
    private String openid;

    /**
     * 外部联系人在微信开放平台的唯一身份标识（微信unionid），通过此字段企业可将外部联系人与公众号/小
     * 程序用户关联起来。仅当群成员类型是微信用户（包括企业成员未添加好友），且企业绑定了微信开发者ID
     * 有此字段（查看绑定方法）。第三方不可获取，上游企业不可获取下游企业客户的unionid字段
     */
    private String unionid;

    /**
     * 入群时间
     */
    private Instant joinTime;

    /**
     * 入群方式。
     * 1 - 由群成员邀请入群（直接邀请入群）
     * 2 - 由群成员邀请入群（通过邀请链接入群）
     * 3 - 通过扫描群二维码入群
     */
    private JoinScene joinScene;

    /**
     * 邀请者。目前仅当是由本企业内部成员邀请入群时会返回该值
     * 邀请者的userid
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject invitorUserid;

    /**
     * 在群里的昵称
     */
    private String groupNickname;

    /**
     * 名字。仅当 need_name = 1 时返回
     * 如果是微信用户，则返回其在微信中设置的名字
     * 如果是企业微信联系人，则返回其设置对外展示的别名或实名
     */
    private String name;

    /**
     * pending_id
     */
    private String pendingId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
