package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * 淘宝服务商类型
 */
public enum TaokeApplicationType {

    DATAOKE(0, "大淘客"),
    DINGDANXIA(1, "订单侠");

    @EnumValue
    private final int id;

    private final String name;

    TaokeApplicationType(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public static String getNameById(Integer id) {
        for (InterestActionType e : InterestActionType.values()) {
            if (e.getId() == id) {
                return e.getName();
            }
        }
        return "";
    }

}
