package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.PermissionMappingType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PermissionMapping {

    /**
     * 匹配的请求地址pattern
     */
    private String url;

    /**
     * 匹配的请求方法, 如果为空则匹配所有HTTP请求方法.
     */
    private List<String> methods;

    /**
     * 匹配类型: 包含或者排除, 默认为包含关系
     */
    private PermissionMappingType type = PermissionMappingType.INCLUDE;

    /**
     * 参数名称，用于区分多个页面地址请求同一个接口时进行区分
     */
    private String parameterName;

    /**
     * 参数值
     */
    private String parameterValue;
}
