package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.DefaultDataType;
import ai.yiye.agent.domain.enumerations.DeleteStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 人效数据概况字段配置表
 */
@Data
@TableName("validity_summary_field")
public class ValiditySummaryField implements Serializable {
    @TableId(
        type = IdType.AUTO
    )
    private Long id;

    /**
     * 类型: 0=设计师人效数据概况,1=团队人效数据概况
     *
     * 对应枚举位置：ai.yiye.agent.domain.enumerations.SummaryFieldType
     */
    private Integer type;

    /**
     * 字段序号
     */
    private Integer no;

    private Long userId;

    /**
     * 字段名
     */
    private String field;

    /**
     * 字段中文描述
     */
    private String name;

    /**
     * 字段展示颜色
     */
    private String color;

    /**
     * 值的颜色
     */
    private String valueColor;

    /**
     * 背景颜色
     */
    private String bgColor;

    /**
     * 单位
     */
    private String unit;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Instant createdAt;

    @TableField(
        fill = FieldFill.INSERT_UPDATE
    )
    private Instant updatedAt;


    /**
     * 是否是默认列
     */
    private DefaultDataType defaultData;

    /**
     * 字段释义
     */
    private String fieldDefinition;

    /**
     * 逻辑删除标记
     */
    private DeleteStatus deleteStatus;
}
