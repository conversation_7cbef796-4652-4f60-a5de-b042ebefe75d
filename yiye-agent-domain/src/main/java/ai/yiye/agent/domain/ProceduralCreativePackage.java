package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AdvertiserAccountSystemStatus;
import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import ai.yiye.agent.domain.typehandlers.TextArrayTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 巨量引擎橙子建站落地页列表
 */
@Data
@TableName("marketing_data_procedural_creative_package")
public class ProceduralCreativePackage extends AbstractMarketingData {

    @TableField(exist = false)
    public static final String[] CONFLICTS = new String[]{"advertiser_account_id", "package_id"};
    @TableField(exist = false)
    protected JSONObject ext;
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 程序化创意包id
     */
    private Long packageId;
    /**
     * 建站名称
     */
    private String name;
    @TableField(typeHandler = TextArrayTypeHandler.class)
    private String[] advertisers;
    private Instant createdAt;
    @TableField(
        fill = FieldFill.INSERT_UPDATE
    )
    private Instant updatedAt;
    @TableField(exist = false)
    private Long optimizerId;
    @TableField(exist = false)
    private AdvertiserAccountSystemStatus advertiserAccountStatus;

}
