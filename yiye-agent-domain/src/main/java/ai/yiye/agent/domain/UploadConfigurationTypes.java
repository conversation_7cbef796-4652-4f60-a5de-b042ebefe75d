package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.typehandlers.TextArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 落地页上报配置 - 上报转化目标类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_upload_configuration_types", autoResultMap = true)
public class UploadConfigurationTypes implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 落地页ID
     */
    private Long landingPageId;

    /**
     * 渠道id（关联表：landing_page_channels.id）
     */
    private Long channelId;

    /**
     * 落地页上报配置id：关联表【landing_page_upload_configuration.id】
     */
    private Long uploadConfigId;

    /**
     * 上报事件类型（不上报不存库）：form_form_submit：表单-表单提交；order_order_submit：订单-订单提交；order_pay_success：订单-支付成功；order_zero_money_buy：订单-0元购；popup_receive_coupon_order_submit：弹窗-领取优惠券-订单提交；popup_receive_coupon_pay_success：弹窗-领取优惠券-支付成功；
     */
    private UploadEventType uploadEventType;

    /**
     * 客资转化状态 - 校验字段 - 字段名
     */
    private String customerStatusField;

    /**
     * 客资转化状态 - 校验字段 - 字段值
     */
    private Integer customerStatusValue;

    /**
     * 上报转化目标类型-值：取值对应【action_type_config.code】
     */
    @TableField(typeHandler = TextArrayTypeHandler.class)
    private String[] uploadEventTypeValue;

    /**
     * 上报转化目标类型-名称：取值对应【action_type_config.value】
     */
    private String[] uploadEventTypeValueName;

    /**
     * 企业微信tagid
     */
    @TableField(typeHandler = TextArrayTypeHandler.class)
    private String[] enterpriseWechatCorpTagId;

    /**
     * 企业微信corpid
     */
    private String enterpriseWechatCorpId;

    /**
     * 执行时间，格式如：["00:00:00-24:00:00", "00:30:00-01:00:00"]
     */
    private Integer[] uplaodTimeAreas;


    /**
     * IP区域来源-省
     */
    private Integer[] ipRegionalSourceProvince;

    /**
     * IP区域来源-省名称
     */
    private String ipRegionalSourceProvinceName;

    /**
     * IP区域来源-市
     */
    private Integer[] ipRegionalSourceCity;

    /**
     * IP区域来源-市名称
     */
    private String ipRegionalSourceCityName;

    /**
     * 上报比例
     */
    private Integer uploadRatio;

    /**
     * 流量来源，默认为空则为全部，取值来源：ai.yiye.agent.domain.enumerations.FlowSource；
     */
    private Integer[] flowSources;

    /**
     * 比较类型
     */
    private CompareType compareType;

    /**
     * 企业微信客户开口次数（大于等于）：数字类型，默认1次
     */
    private Integer workWechatOpenNum;

    /**
     * 性别：null/ALL-全部、FEMALE-女、MALE-男、UNKNOWN-未知
     */
    private Sex sex;

    /**
     * 是否模糊匹配字段
     */
    private FuzzyMatchingField fuzzyMatchingField;

    /**
     * 关键词：字符串，默认为null
     */
    private String[] keyWord;

    /**
     * 是否 符合上报条件时间与添加企业微信成功，默认：否
     */
    private Boolean checkUploadTimeAddWechatSuccess;

    /**
     * 符合上报条件时间与添加企业微信成功时间超过xx天不上报，默认：30
     */
    private Integer checkUploadTimeAddWechatSuccessTime;



    /**
     * 是否勾选“进群后X秒（时间）后未退群再上报”的选项,默认未勾选
     */
    private Boolean checkUploadNotDropoutWechatGroupSuccess;

    /**
     * 设置进群后X秒（时间）后未退群再上报的时间
     */
    private Integer checkUploadNotDropoutWechatGroupSuccessTime;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 创建时间排序字段
     */
    @TableField(exist = false)
    private Long createdAtTime;

    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 是否去除重复访客
     */
    private Boolean whetherDeduplicateVisitor;

    /**
     * 重复访客去除时间间隔
     */
    private UploadDeduplicateVisitorInterval deduplicateVisitorInterval;

    /**
     * 重复访客去除范围
     */
    private UploadDeduplicateVisitorRange deduplicateVisitorRange;


    /**
     * DSP选择上报数据源类型 0:全部 1:部分
     */
    private TaobaoDspType taobaoDspType;

    /**
     * 部分选择类型时，DSP上报条件关联淘客DSP数据源ID
     */
    private Long[] taobaoDspConfigIds;
}
