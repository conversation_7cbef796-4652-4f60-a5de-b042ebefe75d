package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

@Data
@TableName("landing_page_qr_code_clear_mark")
public class LandingPageQrCodeClearMark implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 微信客服userId
     */
    private String wechatUserId;

    /**
     * 企业微信ID
     */
    private String corpId;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;


    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
