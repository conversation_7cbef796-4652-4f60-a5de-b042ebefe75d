package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.TaobaoDspEventType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("taobao_dsp_event_record")
public class TaobaoDspEventRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 点击id
     */
    private String clickId;

    /**
     * 落地页id
     */
    private Long landingPageId;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * dsp id
     */
    private Long taobaoDspConfigId;

    /**
     * dsp名称
     */
    private String dspName;

    /**
     * dsp任务ID
     */
    private String dspTaskId;

    /**
     * dsp渠道ID
     */
    private String dspChannelId;

    /**
     * dsp pid
     */
    private String dspPid;

    /**
     * 曝光pid
     */
    private String pid;

    /**
     * 真实发生时间戳
     */
    private Long actionTime;

    /**
     * 转化类型 5:落地页访问 11:淘客订单支付 28:淘客商品点击 36:会场首登：联盟口径的首登回传 37:红包领取 48:取消淘客订单支付（5分钟内） 49:佣金金额>0.3元订单支付
     */
    private TaobaoDspEventType transformType;

    /**
     * 事件时间接收时间
     */
    private Instant transformAt;

    /**
     * 设备ID
     */
    private String deviceMd5;

    /**
     * url参数
     */
    private String urlQueryString;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
