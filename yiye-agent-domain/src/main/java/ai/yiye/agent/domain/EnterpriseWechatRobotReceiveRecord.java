package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.EnterpriseWechatRobotMsgEventType;
import ai.yiye.agent.domain.enumerations.Messagetype;
import ai.yiye.agent.domain.enumerations.OriginType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 微信客服消息接受记录表
 *
 * <AUTHOR>
 * @date 2022/6/14 15:01
 */
@Data
public class EnterpriseWechatRobotReceiveRecord implements Serializable {


    // 字段更新约束
    @TableField(exist = false)
    public static final String[] CONFLICTS = new String[]{"open_kfid", "external_userid", "msgid"};

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 企业id
     */
    private String corpId;
    /**
     * 查询到的游标
     */
    private String cursor;
    /**
     * token 方便下次查询
     */
    private String token;

    /**
     * msgId
     */
    private String msgid;
    /**
     * open_kfid  企业客服机器人id
     */
    private String openKfid;
    /**
     * external_userid  用户id
     */
    private String externalUserid;
    /**
     * 发送时间
     */
    private Instant sendAt;
    /**
     * origin
     */
    private OriginType origin;
    /**
     * servicer_userid
     */
    private String serviceUserId;

    /**
     * msgtype 消息类型
     */
    private Messagetype msgType;
    /**
     * content
     */
    private String content;
    /**
     * meunId
     */
    private String menuId;
    /**
     * 多媒体文件id
     */
    private String mediaId;
    /**
     * title 标题
     */
    private String title;
    /**
     * desc 内容
     */
    private String contentDesc;
    /**
     * url
     */
    private String url;
    /**
     * pic_url
     */
    private String picUrl;

    /**
     * appid 小程序id
     */
    private String appid;
    /**
     * pagepath 小程序页面路径
     */
    private String pagePath;
    private String thumbMediaId;
    /**
     * 创建时间
     */
    private Instant createdAt;
    /**
     * 更新时间
     */
    private Instant updatedAt;

    /**
     * 欢迎语
     */
    private String welcomeCode;
    /**
     * scene值
     */
    private String scene;
    /**
     * pid
     */
    private String pid;
    /**
     * enter_session 用户进入会话事件
     * msg_send_fail 消息发送失败事件
     * servicer_status_change 接待人员接待状态变更事件
     * session_status_change 会话状态变更事件
     */
    private EnterpriseWechatRobotMsgEventType eventType;
}
