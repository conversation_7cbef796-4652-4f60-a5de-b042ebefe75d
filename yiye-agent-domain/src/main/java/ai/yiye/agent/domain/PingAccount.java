package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.PingAccountType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * @ClassName : PingAccount
 * <AUTHOR> lmg
 * @Description : ping++账户
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "payment_ping_account", autoResultMap = true)
public class PingAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * PMP账户ID
     */
    private Long advertiserAccountGroupId;
    /**
     * 账户类型
     */
    private PingAccountType accountType;
    /**
     * 账户名称
     */
    private String accountName;
    /**
     * ping++账户测试密钥
     */
    private String testSecretKey;
    /**
     * ping++账户线上密钥
     */
    private String liveSecretKey;
    /**
     * ping++账户公钥
     */
    private String pingPublicKey;
    /**
     * 商户rsa私钥
     */
    private String rsaPrivateKey;
    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
    /**
     * 应用列表
     */
    @TableField(exist = false)
    private List<PingApplication> pingApplications;
    /**
     * 搜索对应的值（模糊搜索）
     */
    @TableField(exist = false)
    private String selectFieldValue;
    /**
     * ping++账户应用APP_ID
     */
    @TableField(exist = false)
    private String pingAppId;
}
