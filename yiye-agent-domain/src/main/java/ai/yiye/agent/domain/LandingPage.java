package ai.yiye.agent.domain;

import ai.yiye.agent.domain.dto.DouYinLandingPageStrategyDTO;
import ai.yiye.agent.domain.dto.LandingPageErrorDto;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.landingpage.LandingPageUrlVo;
import ai.yiye.agent.domain.typehandlers.JSONArrayHandler;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import ai.yiye.agent.domain.util.BigDecimalValueSerialize;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.apache.ibatis.type.ArrayTypeHandler;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@Data
@TableName("landing_page")
public class LandingPage {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    @Length(max = 40, message = "error.title.length.max")
    private String title;

    @NotNull(message = "落地页内容不能为空")
    private String content;

    private String bgColor;

    private String token;

    private String wechatDate;

    private String wechatLinkContent;

    private String wechatLinkAddress;

    private Long advertiserAccountId;

    private Integer unreadCount;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 创建者id
     */
    private Long creatorId;

    private Integer version;

    private Integer landingPageWidth;

    private Integer landingPageHeight;

    private Double landingPageSize;

    private String bsId;

    private Instant recoveryAt;

    private Long recoveryId;

    @TableField(exist = false)
    private String recoveryName;

    /**
     * 未分组
     */
    private Integer landingPageTop;

    private Integer landingPageGroupTop;

    private String wechatShareDesc;

    private String wechatShareImagePath;

    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject support;

    private String bgPic;

    private Boolean limitFilling;

    private String wechatAppId;

    private Boolean showOpenId;

    private Integer status;

    @TableField(exist = false)
    private String landingPageGroupName;

    private Long landingPageGroupId;

    //获取方式（1显式授权，2静态获取）,默认1
    private AccessType access;

    //与已有客资匹配， false-不匹配，true-匹配
    private Boolean convertCustomer;
    //已转化跳转链接
    private String convertUrl;
    //1-跳转链接，2执行代码
    private ConvertType convertType;

    /**
     * 1.222.0 企业推二维码过期时间 hh:mm:ss
     */
    private String qrCodeExpireStr;

    /**
     * 二维码有效期内展示固定二维码
     */
    private Boolean qrCodeExpireShowFixed;

    /**
     * 禁止访客重复关注
     */
    private Boolean disableWechatUserRepeatFollow;

    /**
     * 公众号关注支持按选定项目判定重复访客 - 判断维度
     */
    private JudgmentDimensionType judgmentDimensionType;

    /**
     * 去重时间范围类型，不传参数默认值为0（7天内、30天内、0-永久）
     */
    private Integer days;

    /**
     * 去重数据范围（当前落地页：THIS_LANDING_PAGE、项目内所有落地页：THIS_PMP_LANDING_PAGE、所有项目内落地页：ALL_PMP_LANDING_PAGE）
     */
    private DataFilteringType dataFilteringType;

    /**
     * 公众号关注支持按选定项目判定重复访客 - 选择账户内项目类型
     */
    private DataFilteringPmpType dataFilteringPmpType;

    /**
     * 支持按选定项目判定重复访客 - 选择账户内项目 - 已选pmp项目id
     */
    @TableField(typeHandler = ArrayTypeHandler.class)
    private Long[] dataFilteringPmpIds;

    @TableField(exist = false)
    private Boolean top;
    /**
     * 是否是分组置顶
     */
    @TableField(exist = false)
    private Boolean isGroupTop;

    /**
     * 今日填单数
     */
    @TableField(exist = false)
    private Integer fillNumNow;
    /**
     * 总填单数
     */
    @TableField(exist = false)
    private Integer fillAllNums;
    /**
     * 填单数字段
     */
    @TableField(exist = false)
    private String fillNums;
    /**
     * 填单率
     */
    @TableField(exist = false)
    private String fillRate;

    /**
     * 浏览量
     */
    @TableField(exist = false)
    private String pv;
    /**
     * 总浏览量
     */
    @TableField(exist = false)
    private Integer pvDay;
    /**
     * 今日浏览量
     */
    @TableField(exist = false)
    private Integer pvNow;

    /**
     * 是否有填单数
     */
    @TableField(exist = false)
    private Boolean showFillData;

    /**
     * 长按二维码识别数：当前落地页内长按二维码识别数总计（同一pid 多次长按动作 只计一次）
     * 总计/今日
     */
    @TableField(exist = false)
    private String identifyQrCodeNum;

    /**
     * identify_qr_code_num
     * 长按二维码识别率，公式=【长按二维码识别数/浏览数 *100】（保留至小数点后两位）
     */
    @TableField(exist = false)
    private String identifyQrCodeRate;

    /**
     * 加企业微信数：当前落地页内成功添加企业微信数
     */
    @TableField(exist = false)
    private String addWorkWechatNum;

    /**
     * 加企业微信率，公式=【成功添加企业微信数/浏览数 *100】（保留至小数点后两位）
     */
    @TableField(exist = false)
    private String addWorkWechatRate;

    @TableField(exist = false)
    private String remainTime;

    @TableField(exist = false)
    private List<LandingPageErrorDto> errors;

    /**
     * 是否重命名
     */
    @TableField(exist = false)
    private boolean rename;

    private Long advertiserAccountGroupId;
    /**
     * 上报媒体
     */
    @TableField(exist = false)
    private Platform platformId;

    /**
     * 推广类型
     */
    @TableField(exist = false)
    private PromotionType promotionType;

    /**
     * 投放账户ID
     */
    @TableField(exist = false)
    private String accountId;
    /**
     * 投放账户名称
     */
    @TableField(exist = false)
    private String accountName;

    /**
     * 是否配置上报信息(需要复制传true)
     */
    @TableField(exist = false)
    private boolean uploadConfigCopy;

    public String getRemainTime() {
        if (this.recoveryAt != null) {
            ZoneId zone = ZoneId.systemDefault();
            LocalDateTime localDateTime = LocalDateTime.ofInstant(recoveryAt, zone);
            LocalDateTime deleteTime = localDateTime.minusDays(-30);
            Duration duration = Duration.between(LocalDateTime.now(), deleteTime);
            return (duration.toDays() + 1) + "天";
        }
        return null;
    }

    @TableField(exist = false)
    private List<LandingPageCustomCode> customCode;

    @TableField(exist = false)
    private List<LandingPageUrlVo> landingPageHosts;
    /**
     * 落地页策略列表
     */
    @TableField(exist = false)
    private List<LandingPageStrategy> landingPageStrategys = new ArrayList<>();

    /**
     * 落地页策略列表(字节小程序的页面)
     */
    @TableField(exist = false)
    private JSONArray bytePageStrategyContent;


    /**
     * 落地页策略列表(针对字节小程序类型)
     */
    @TableField(exist = false)
    private List<DouYinLandingPageStrategyDTO> bytePageStrategys = new ArrayList<>();

    /**
     * 落地页使用的模板ID列表
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject templateIds;
    /**
     * 弹窗模板ID
     */
    private Long popupId;

    /**
     * 是否跳转小程序
     */
    private Boolean isJumpWechatApplet;

    /**
     * 微信小程序 - 落地页id（回显）
     */
    private Long wechatAppletLandingPageId;

    /**
     * 微信小程序 - 渠道id（回显）
     */
    private Long wechatAppletChannelId;

    /**
     * 微信小程序 - 渠道链接地址（跳转）
     */
    private String wechatAppletChannelUrl;

    /**
     * 微信公众号关注数（总计/今日）
     */
    @TableField(exist = false)
    private String followOfficialAccountNum;

    /**
     * 微信公众号关注率（总计/今日）→ 关注率（公式 = 关注数 / 浏览量 * 100%）
     */
    @TableField(exist = false)
    private String followOfficialAccountRate;

    /**
     * 公众号类型
     */
    private WechatOfficialAccountTypeEnum wechatOfficialAccountType;

    /**
     * 是否逻辑删除：0-未删除  1-已删除
     */
    private DeleteStatus deleteStatus;

    /**
     * 淘宝商品id
     */
    private String taobaoGoodsId;

    /**
     * 京东商品链接url
     */
    private String jingDongGoodsUrl;

    /**
     * 微信客服用户id
     */
    private String robotCustomerServiceId;


    //新增一个微信客服的config
    private String wechatRobotConfig;
    //企业推小程序信息
    private String workMiniConfig;

    /**
     * 拼多多商品id
     */
    private String pddGoodsId;

    /**
     * 跳转电商APP配置
     */
    private String commerceConfig;

    /**
     * 自动跳转类型
     */
    private AutoJumpTypeEnum autoJumpType;

    private String autoJumpContent;

    /**
     * 落地页类型
     */
    private LandingPageType landingPageType;

    /**
     * 是否开启微信客服
     */
    private Boolean customerServiceOpen;

    /**
     * 微信客服id
     */
    private Long customerServiceId;

    /**
     * 是否可编辑 0:可编辑 1:不可编辑
     */
    private LandingPageReviewStatus review;

    /**
     * 客服分组ID
     */
    private Long wechatCustomerServiceGroupId;

    /**
     * 客服分組是否使用动态渠道二维码
     */
    private Boolean useWechatCustomerContact;

    /**
     * 城市区域码是否使用动态渠道二维码
     */
    private Boolean useCityWechatCustomerContact;

    /**
     * 点击后全屏展示二维码
     */
    private Boolean clickFullScreenCustomerContact;
    /**
     * 进入页面后全屏展示二维码
     */
    private Boolean showFullScreenCustomerContact;
    /**
     * 是否展示二维码
     */
    private Boolean showCustomerServiceQrCode;

    /**
     * 企业推PageId
     */
    private String qiyetuiPageId;

    /**
     * 动态二维码组件-公众号二维码
     */
    private Long wechatOfficialAccountId;

    /**
     * 动态二维码组件-公众号动态渠道二维码-单选
     */
    private Long dynamicWechatOfficialAccountId;

    /**
     * 动态二维码组件-公众号动态渠道二维码-多选
     */
    private Long[] dynamicWechatOfficialAccountIds;

    /**
     * 二维码图片url
     */
    private String qrCodeUrl;
    /**
     * 微信客服城市区域码id
     */
    private Long landingPageWechatCustomerCityCodeId;
    /**
     * 企业微信群城市区域码id
     */
    private Long landingPageWechatGroupChatCityCodeId;

    /**
     * 企业微信群分组id
     */
    private Long wechatGroupChatGroupId;

    /**
     * 获客助手城市区域码ID
     */
    private Long wechatCustomerAcquisitionCityCodeId;

    /**
     * 非广告场景设置
     */
    private Boolean nonAdSceneSettings;

    /**
     * 落地页用途
     */
    private LandingPagePurpose purpose;

    /**
     * 场景设置
     */
    private SceneSettings sceneSettings;

    /**
     * 执行动作
     */
    private NoAdSceneAction noAdSceneAction;

    /**
     * 非广告场景设置-客服分组ID
     */
    private Long noAdSceneWechatCustomerServiceGroupId;

    /**
     * 落地页-编辑器-页面配置-微信公众号-一叶id标识'
     */
    private Long yiyeOfficialAccountId;

    /**
     * 落地页-编辑器-获客名片客服分组信息
     */
    private String csBusinessCard;

    /**
     * 落地页-编辑器-页面配置-是启用ip访问限制
     */
    private Boolean isEnableIpAccessLimit;

    /**
     * 落地页-编辑器-页面配置-【一小时】内同一ip访问频次-是否开启校验
     */
    private Boolean openOneHourIpAccess;

    /**
     * 落地页-编辑器-页面配置-【一小时】内同一ip访问频次
     */
    private Integer oneHourIpAccessNum;

    /**
     * 落地页-编辑器-页面配置-【一天内】内同一ip访问频次-是否开启校验
     */
    private Boolean openOneDayIpAccess;

    /**
     * 落地页-编辑器-页面配置-【一天内】同一ip访问频次
     */
    private Integer oneDayIpAccessNum;

    /**
     * 是否开启自动打标签 0:默认不开启 1:开启
     */
    private AutoMarkEnterpriseWechatTagType autoMarkEnterpriseWechatTag;

    /**
     * 自动打标签配置
     */
    @TableField(typeHandler = JSONArrayHandler.class)
    private JSONArray enterpriseWechatTags;

    /**
     * 落地页纬度-是否开启自动打标签 0:默认不开启 1:开启
     */
    private AutoMarkEnterpriseWechatTagType itemAutoMarkEnterpriseWechatTag;

    /**
     * 落地页纬度-企微标签数组,自动打标签配置
     */
    @TableField(typeHandler = JSONArrayHandler.class)
    private JSONArray itemEnterpriseWechatTags;



    /**
     * 渠道二维码 - 是否开启固定二维码
     */
    private FixedContactCodeFlag fixedContactCodeFlag;


    /**
     * 落地页浏览量
     */
    @TableField(exist = false)
    private BigDecimal pageViewNum;

    /**
     * 重复访客数
     */
    @TableField(exist = false)
    private BigDecimal visitorNum;

    /**
     * 填单提交数
     */
    @TableField(exist = false)
    private BigDecimal formSubmitNum;

    /**
     * 订单提交数
     */
    @TableField(exist = false)
    private BigDecimal orderSubmitNum;

    /**
     * 订单完成数
     */
    @TableField(exist = false)
    private BigDecimal paymentNum;

    /**
     * 重复访客率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal repeatVisitorRate;

    /**
     * 平均停留时长
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal averageLengthOfStay;

    /**
     * 填单提交率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal formSubmitRate;

    /**
     * 订单提交率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal orderSubmitRate;

    /**
     * 订单完成率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal paymentRate;

    /**
     * 订单综合完成率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal comprehensivePaymentRate;

    /**
     * 使用优惠券的订单完成率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal numberOfOrdersCompletedForCouponRate;

    /**
     * 重复访客数
     */
    @TableField(exist = false)
    private Long repeatVisitorNum;

    /**
     * 落地页版本白名单 通过 agent_conf 表 landingPageVersion 控制
     * 数据格式
     * {
     *  "1.173.0": true,
     *  "1.174.0": true
     * }
     * //进行修改，。{"TOOL":1.173.0,"BUSINESS":1.173.0}
     */
    @TableField(exist = false)
    @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
    private JSONObject landingPageVersion;

    /**
     * 当前项目下可使用的小程序版本
     */
    @TableField(exist = false)
    private String landingPageWechatAppletVersion;

    /**
     * 落地页-编辑器-页面配置-抖音卡片描述
     */
    private String douyinCardDesc;

    /**
     * 落地页-编辑器-页面配置-抖音卡片图片
     */
    private String douyinCardPhoto;

    /**
     * 用户加粉后在企微备注信息中添加手机号码信息
     */
    private BaseStatusEnum fillWecomRemarkMobiles;

    /**
     * 落地页语言类型
     */
    private LanguageType languageType;

    /**
     * 是否开启上报DSP
     */
    private Boolean enableReportDsp;

    /**
     * DSP上报数据源
     */
    private Long[] taobaoDspConfigIds;

    /**
     * 是否选择全部DSP上报数据源
     */
    private TaobaoDspType taobaoDspType;

    /**
     * 同步问答内容至企业微信联系人描述信息 0:关闭 1:开启
     */
    private SwitchStatus fillWecomDescription;

    /**
     * 落地页创建状态 0:创建中 1:创建成功 1:创建失败
     */
    private LandingPageCreateStatus createStatus;

    /**
     * 备注企微信息
     */
    private String remarksEnterpriseWechatInfo;


    /**
     * 是否开启来源跳转页统计标识
     */
    private Integer flowSourceJumpPageStatus;

    /**
     * 同步表单内容至企业微信联系人描述信息 0:关闭 1:开启
     */
    private SwitchStatus fillFormDescription;

}
