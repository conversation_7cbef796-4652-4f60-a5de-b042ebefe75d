package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.core.enums.IEnum;

/**
 * 上报子策略 - 执行操作类型
 */
public enum UploadSonStrategyOperateType implements IEnum<String> {

    /**
     * 上报
     */
    UPLOAD("UPLOAD", "上报"),


    /**
     * 出价调整（归类）
     */
    BID_ADJUSTMENT("BID_ADJUSTMENT", "出价调整（不适用与下拉选）"),

    /**
     * 出价上调
     */
    BID_INCREASE("BID_INCREASE", "出价上调"),

    /**
     * 出价下调
     */
    BID_REDUCE("BID_REDUCE", "出价下调"),

    /**
     * 出价值
     */
    BID_VALUE("BID_VALUE", "出价值"),


    /**
     * 深度出价调整（归类）
     */
    DEEP_BID_ADJUSTMENT("DEEP_BID_ADJUSTMENT", "深度出价调整（不适用与下拉选）"),

    /**
     * 深度目标出价上调
     */
    DEEP_BID_INCREASE("DEEP_BID_INCREASE", "深度出价上调"),

    /**
     * 深度出价下调
     */
    DEEP_BID_REDUCE("DEEP_BID_REDUCE", "深度出价下调"),

    /**
     * 深度出价值
     */
    DEEP_BID_VALUE("DEEP_BID_VALUE", "深度出价值")

    ;

    private String value;
    private String desc;

    UploadSonStrategyOperateType(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @Override
    public String getValue() {
        return this.value;
    }

}
