package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.CustomCodePositionType;
import ai.yiye.agent.domain.enumerations.CustomCodeType;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * @Description: 自定义代码
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/3/3 8:40
 */
@Data
@TableName("landing_page_custom_code")
public class LandingPageCustomCode {

    @TableId(type = IdType.AUTO)
    private Long id;

    //落地页id
    private Long landingPageId;

    //代码名称
    private String name;

    //自定义代码
    private String code;

    //描述
    private String remarks;

    //自定义类型(1JS,2CSS)
    private CustomCodeType type;

    //分布位置(1head前方,2body前方,3body后方)
    private CustomCodePositionType position;

    //变量json数组
    private JSONArray variable;

    //创建时间
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    //修改时间
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
