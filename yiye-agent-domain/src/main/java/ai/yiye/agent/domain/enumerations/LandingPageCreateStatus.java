package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.core.enums.IEnum;

/**
 * 落地页创建状态 0:创建中 1:创建成功 1:创建失败
 */
public enum LandingPageCreateStatus implements IEnum<Integer> {

    INIT(0, "创建中"),
    SUCCESS(1, "创建成功"),
    FAIL(2, "创建失败"),
    ;

    @EnumValue
    private final Integer id;

    private final String name;

    LandingPageCreateStatus(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    @Override
    public Integer getValue() {
        return id;
    }

}
