package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * 弹层模板中拼接组件与 表单 订单的关系表
 */
@Data
@TableName("landing_page_popup_widget_template_preview_rel")
public class LandingPagePopupWidgetTemplatePreviewRel extends LandingPagePopupWidgetTemplateRel {
    private String previewToken;

    @TableField(exist = false)
    private List<LandingPageWidgetTemplatePreviewRel> landingPageWidgetTemplatePreviewRelList;

}
