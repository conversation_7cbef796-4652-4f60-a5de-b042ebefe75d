package ai.yiye.agent.domain.message.warn;

import ai.yiye.agent.domain.TimeUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/9/2 9:40 上午
 */
@Data
public class CardMessageRequestBody {
    private String msgType = "interactive";
    private Card card = new Card();

    @Data
    public static class Card {
        private Config config = new Config();
        private List<Element> elements = new ArrayList<>();
        private Header header = new Header();
    }

    @Data
    public static class Config {
        private boolean wideScreenMode = true;
    }

    @Data
    public static class Element {
        private String tag = "div";
        private Text text;
    }

    @Data
    public static class Text {
        private String content;
        private String tag;
    }

    @Data
    public static class Header {
        private String template = "red";
        private Text title;
    }

    public static CardMessageRequestBody build() {
        return new CardMessageRequestBody();
    }

    public CardMessageRequestBody setTitle(String title) {
        Text text = new Text().setContent(title).setTag("plain_text");
        this.card.header.setTitle(text);
        return this;
    }

    public CardMessageRequestBody addContent(String content) {
        List<Element> elements = this.card.elements;
        elements.add(new Element().setText(new Text().setTag("lark_md").setContent(content)));
        this.card.setElements(elements);
        return this;
    }

    public CardMessageRequestBody addContents(List<String> contents){
        if(CollectionUtils.isNotEmpty(contents)){
            List<Element> elements = contents.stream().map(content ->
                new Element().setText(new Text().setTag("lark_md").setContent(content))
            ).collect(Collectors.toList());
            this.card.elements.addAll(elements);
        }
        return this;
    }

    public CardMessageRequestBody end() {
        List<Element> elements = this.card.elements;
        elements.add(new Element().setText(new Text().setTag("lark_md").setContent("**时间 : " + TimeUtils.getCurrentDateString() + "**")));
        this.card.setElements(elements);
        return this;
    }


}

