package ai.yiye.agent.domain;

import ai.yiye.agent.domain.dto.GroupMemberDto;
import ai.yiye.agent.domain.enumerations.LandingPageChannelType;
import ai.yiye.agent.domain.enumerations.Platform;
import ai.yiye.agent.domain.enumerations.PromotionType;
import ai.yiye.agent.domain.message.LandingPageChannelVo;
import ai.yiye.agent.domain.typehandlers.JSONListTypeHandler;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import ai.yiye.agent.domain.util.BigDecimalSerialize;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * 企业微信群详情表
 */
@Data
@TableName("enterprise_wechat_group_record")
public class EnterpriseWechatGroupRecord {

    /**
     * 自增id主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 公司id（corpId）
     */
    private String corpId;

    /**
     * 群id（wxCpTpXmlMessage.chatId）
     */
    private String chatId;

    /**
     * 群昵称（wxCpTpXmlMessage.name）
     */
    private String name;

    /**
     * 群管理员（wxCpTpXmlMessage.owner）
     */
    private String owner;


    /**
     * 客户群创建时间
     */
    private Instant createTime;

    /**
     * 群公告
     */
    private String notice;

    /**
     * 群管理员userid
     */
    private String[] adminUserIdList;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 群成员详情
     */
    @TableField(exist = false)
    private List<EnterpriseWechatGroupUsersRecord> enterpriseWechatGroupUsersRecordList;

    /**
     * 解析群成员
     */
    public List<EnterpriseWechatGroupUsersRecord> getMemberList() {
        List<EnterpriseWechatGroupUsersRecord> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(this.enterpriseWechatGroupUsersRecordList)) {
            return list;
        }
        this.enterpriseWechatGroupUsersRecordList.forEach(e -> {
            list.add(JSONObject.toJavaObject(JSONObject.parseObject(JSONObject.toJSONString(e)), EnterpriseWechatGroupUsersRecord.class));
        });
        return list;
    }

}
