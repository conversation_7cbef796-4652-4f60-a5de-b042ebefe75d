package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2021/12/15 16:29
 */
public enum DomainTypeEnum {

    /**
     * 系统域名
     */
    SYSTEM(0, "系统域名"),

    /**
     * cname域名
     */
    CNAME(1, "cname域名");

    @EnumValue
    private int id;

    private String name;

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static String getNameById(Integer id) {
        for (DomainTypeEnum e : DomainTypeEnum.values()) {
            if (e.getId() == id) {
                return e.getName();
            }
        }
        return "";
    }

    DomainTypeEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }
}
