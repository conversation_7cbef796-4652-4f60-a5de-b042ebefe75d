package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AcquisitionChooseEnum;
import ai.yiye.agent.domain.enumerations.SwitchStatus;
import ai.yiye.agent.domain.enumerations.WechatCustomerAcquisitionLinkStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 企微获客链接 - 表
 */
@Data
@TableName("work_wechat_customer_acquisition_link")
public class WorkWechatCustomerAcquisitionLink implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业微信id，关联表字段：enterprise_wechats.corpid
     */
    private String corpId;

    /**
     * 对应企业成员账号（userid）
     */
    private String userId;

    /**
     * 获客助手链接名称
     */
    private String wechatCustomerAcquisitionLinkName;


    /**
     * 获客助手链接类型 0:默认 1:自定义
     */
    private AcquisitionChooseEnum acquisitionChooseEnum;

    /**
     * 企微的获客链接id
     */
    private String wechatCustomerAcquisitionLinkId;

    /**
     * 企微获客链接
     */
    private String wechatCustomerAcquisitionLink;

    /**
     * 企微获客链接状态 0:无 1:生成中 2:正常 3:异常
     */
    private WechatCustomerAcquisitionLinkStatus wechatCustomerAcquisitionLinkStatus;

    /**
     * 企微获客链接创建失败原因
     */
    private String wechatCustomerAcquisitionLinkReason;

    /**
     * 企微获客链接是否验证 0:否 1:是
     */
    private SwitchStatus wechatCustomerAcquisitionLinkVerify;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
