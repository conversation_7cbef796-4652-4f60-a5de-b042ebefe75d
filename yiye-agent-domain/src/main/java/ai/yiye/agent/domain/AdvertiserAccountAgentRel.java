package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.Platform;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

@Data
@TableName("marketing_advertiser_account_agent_rel")
public class AdvertiserAccountAgentRel implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * agentId
     */
    private String agentId;
    /**
     * 投放账户id
     */
    private String accountId;
    /**
     * 平台id
     */
    private Platform platformId;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
