package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.ExportTaskType;
import ai.yiye.agent.domain.enumerations.OperationRole;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 客资导出任务记录
 */
@Data
@TableName("customer_export_task")
public class CustomerExportTask {

    @TableId(type = IdType.AUTO)
    private Long id;

    //项目ID
    private Long advertiserAccountGroupId;

    //任务名称
    private String reportName;

    //任务状态 0：计算中 1:已完成 2:失败
    private Integer reportStatus;

    //下载链接
    private String url;

    //导出excel任务类型
    private ExportTaskType exportTaskType;

    //插入时间
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    //更新时间
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    private OperationRole operationRole;
    /**
     * 任务百分比进度 80% = 80 忽略小数
     */
    private Integer schedule;
    /**
     * 下载数据总行数
     */
    private Long totalNum;
}
