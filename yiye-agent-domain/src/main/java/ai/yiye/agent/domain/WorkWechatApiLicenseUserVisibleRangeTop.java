package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 企业微信 - 可见范围 - 表
 */
@Data
@TableName("work_wechat_api_license_user_visible_range_top")
public class WorkWechatApiLicenseUserVisibleRangeTop {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业微信id
     */
    private String corpId;

    /**
     * 接口许可管理主键id，关联表：work_wechat_api_license_user_visible_range.id
     */
    private Long userVisibleRangeId;

    /**
     * agentId
     */
    private String agentId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 置顶状态
     */
    @TableField(exist = false)
    private Boolean followStatus;

}
