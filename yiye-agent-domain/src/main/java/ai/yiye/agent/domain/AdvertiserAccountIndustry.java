package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.IndustryLevelType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2020-07-20 10:22
 **/
@Data
@TableName("marketing_advertiser_account_industry")
public class AdvertiserAccountIndustry implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private Long parentId;

    private IndustryLevelType level;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
