package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.core.enums.IEnum;

import java.util.Arrays;
import java.util.Objects;

public enum OfficialWechatCustomerSubjectType implements IEnum<Integer> {
    //相同主体
    IDENTICAL,
    //不同主体
    DISSIMILARITY
    ;

    @Override
    public Integer getValue() {
        return ordinal();
    }

    public static OfficialWechatCustomerSubjectType getEnumByValue(Integer value) {
        return Arrays.stream(OfficialWechatCustomerSubjectType.values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst().orElse(null);
    }

}
