package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.landingpage.LandingPageChannelAuditRecords;
import ai.yiye.agent.domain.message.LandingPageChannelVo;
import ai.yiye.agent.domain.util.BigDecimalSerialize;
import ai.yiye.agent.domain.util.BigDecimalValueSerialize;
import ai.yiye.agent.domain.util.ThousandthSerialize;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * 渠道
 * <AUTHOR>
 */
@Data
@TableName("landing_page_channels")
public class LandingPageChannel {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long landingPageId;

    private Long advertiserAccountGroupId;

    /**
     * 渠道链接
     */
    private String url;

    /**
     * 空白页跳转企业推url
     */
    private String qiyeBlankPageUrl;

    /**
     * 落地页token
     */
    @TableField(exist = false)
    private String token;

    private String channelParam;

    private String name;

    /**
     * 渠道信息
     */
    private String remark;

    /**
     * 总计返回总条数
     */
    @TableField(exist = false)
    private Integer total;

    /**
     * 0：已读 1：未读
     */
    private Integer unread;

    /**
     * 小程序原始id / 字节小程序appid
     */
    private String appletOriginalId;

    /**
     * 小程序路径 / 字节小程序路径
     */
    private String appletUrl;

    /**
     * 是否可编辑 0:可编辑 1:不可编辑
     */
    private LandingPageReviewStatus review;

    /**
     * 审核失败原因
     */
    private String reason;


    /**
     * 企业推PageId
     */
    private String qiyetuiPageId;

    /**
     * 百度营销通-微信-scheme-全场景调起小程序-path地址
     *
     * 精简必选参数，如：pages/index/index?agentId=dbq&token=dExEupET&_cl=4791&advertiseAccountGroupId=113
     */
    @TableField(exist = false)
    private String appletApiUrl;

    /**
     * 百度营销通-微信-scheme-全场景调起小程序-获取urlScheme地址
     */
    @TableField(exist = false)
    private String schemeApiUrl;

    //小程序名称 / 字节小程序名称
//    @TableField(exist = false)
    private String appletName;

    /**
     * 落地页【渠道类型】
     */
    private LandingPageChannelType landingPageChannelType = LandingPageChannelType.CURRENCY;

    /**
     * 落地页类型
     */
    private LandingPageType landingPageType;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 浏览数
     */
    @TableField(exist = false)
    private Long pageViewNum = 0L;

    /**
     * 访客数
     */
    @TableField(exist = false)
    private Long visitorNum = 0L;

    /**
     * 填单数
     */
    @TableField(exist = false)
    private Long fillNum = 0L;

    /**
     * 填单率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal fillRate = new BigDecimal(0);

    /**
     * 平均停留时长
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal averageLengthOfStay= new BigDecimal(0);

    /**
     * 平台id
     */
    @TableField(exist = false)
    private Platform platformId;

    /*
     * 投放账户ID
     */
    private String accountId;

    /**
     * 投放账户名称
     */
    private String accountName;


    /**
     * 账户有效性
     */
    @TableField(exist = false)
    private ValidType valid;

    /*
     * 媒体推广类型
     */
    @TableField(exist = false)
    private PromotionType promotionType;

    /**
     * 落地页是否存在 - 弹窗- 领取优惠券 - 模板
     */
    @TableField(exist = false)
    private Boolean landingPageHasPopupCoupon;

    //    /**
    //     * 匹配到与落地页内获取OpenID的公众号一致的账户
    //     */
    //    @TableField(exist = false)
    //    private Boolean landingPageAccountHasMatchingWechatAppId;

    /**
     * 落地页 - 是否配置获取OpenID
     */
    @TableField(exist = false)
    private Boolean landingPageHasOpenId;

    /**
     * 落地页配置 - 配置了openID - 公众号 - 名称
     */
    @TableField(exist = false)
    private String wechatOfficialAccountName;

    /**
     * 上报配置 - 基础信息
     */
    @TableField(exist = false)
    private UploadConfiguration uploadConfiguration;

    /**
     * 上报配置 - 目标转化类型 - 信息
     */
    @TableField(exist = false)
    private List<UploadConfigurationTypes> uploadConfigurationTypesList;

    /**
     * 渠道 - 上报配置id
     */
    @TableField(exist = false)
    private Long uploadConfigId;

    public LandingPageChannelVo toVo() {
        LandingPageChannelVo landingPageChannelVo = new LandingPageChannelVo();
        BeanUtils.copyProperties(this, landingPageChannelVo);
        return landingPageChannelVo;
    }

    /**
     * 渠道预览地址
     */
    @Deprecated
    @TableField(exist = false)
    public String ilynxPreviewPath;


    /**
     * 长按二维码识别数
     */
    @TableField(exist = false)
    private Long identifyQrCodeNum = 0L;

    /**
     * 长按二维码识别率，公式=【长按二维码识别数/浏览数 *100】（保留至小数点后两位）
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal identifyQrCodeRate = new BigDecimal(0);

    /**
     * 加企业微信数：当前落地页内成功添加企业微信数
     */
    @TableField(exist = false)
    private Long addWorkWechatNum = 0L;

    /**
     * 加企业微信率，公式=【成功添加企业微信数/浏览数 *100】（保留至小数点后两位）
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal addWorkWechatRate = new BigDecimal(0);

    /**
     * 微信公众号关注数（总计/今日）
     */
    @TableField(exist = false)
    private Long followOfficialAccountNum = 0L;

    /**
     * 微信公众号二维码长按识别数
     */
    @TableField(exist = false)
    private Long officialIdentifyQrCodeNum = 0L;

    /**
     * 微信公众号二维码长按识别率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal officialIdentifyQrCodeRate = new BigDecimal(0.00);

    /**
     * 微信公众号关注率（总计/今日）→ 关注率（公式 = 关注数 / 浏览量 * 100%）
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal followOfficialAccountRate = new BigDecimal(0.00);

    /**
     * 媒体方站点id-落地页id
     */
    @TableField(exist = false)
    private String siteId;

    /**
     * 站点审核状态
     * 枚举值：
     * - AUDIT_UNKNOW - 未知
     * - AUDIT_ACCEPTED - 审核通过
     * - AUDIT_REJECTED - 审核拒绝
     * - AUDIT_BANNED - 审核封禁
     * - AUDITING - 已送审审核中
     * - AWAIT_AUDIT - 待审核
     */
    @TableField(exist = false)
    private MarketingLandingPageAuditStatus auditStatus;

    /**
     * 站点标准化校验状态
     * 枚举值：
     * - VALIDATE_SUCCESS - 校验成功
     * - VALIDATE_FAIL - 校验失败
     * - HIGH_RISK_INDUSTRY_VALIDATE_FAIL - 涉及高危行业校验失败
     * - UN_VALIDATE - 未校验
     */
    @TableField(exist = false)
    private MarketingLandingPageValidateStatus validateStatus;

    /**
     * <AUTHOR>
     * @Description //TODO
     * @Date 2022/3/25 11:42 上午
     * 失败原因
     **/
    @TableField(exist = false)
    private String validateMessage;

    /**
     * <AUTHOR>
     * @Date 2022/3/25 10:48 上午
     * 审核信息内容
     **/
    @TableField(exist = false)
    private String auditMessage;

    /**
     * 是否逻辑删除：0-未删除  1-已删除（隐藏）  2-永久删除（逻辑删除）
     */
    private DeleteStatus deleteStatus;

    /**
     * H5表单提交数
     */
    @TableField(exist = false)
    private BigDecimal formSubmitNum;

    /**
     * H5表单提交率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal formSubmitRate;



    /**
     * 订单提交数
     */
    @TableField(exist = false)
    private BigDecimal orderSubmitNum;

    /**
     * 订单完成数
     */
    @TableField(exist = false)
    private BigDecimal paymentNum;

    /**
     * 重复访客率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal repeatVisitorRate;


    /**
     * 一键获号数
     */
    @TableField(exist = false)
    private Long phoneNumberRecievedNum;

    /**
     * 一键获号率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal phoneNumberRecievedRate;

    /**
     * 主动私信授权数
     */
    @TableField(exist = false)
    private Long activeMessageAuthorizationNum;

    /**
     * 主动私信授权率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal activeMessageAuthorizationRate;

    /**
     * 表单提交总数
     */
    @TableField(exist = false)
    private BigDecimal formSubmitTotalNum;

    /**
     * 表单提交率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal formSubmitTotalRate;

    /**
     * 字节小程序原生表单提交数
     */
    @TableField(exist = false)
    private Long douyinAppletNativeFormSubmitNum;

    /**
     * 字节小程序原生表单提交率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal douyinAppletNativeFormSubmitRate;


    /**
     * 线索通表单提交数
     */
    @TableField(exist = false)
    private Long clueFormSubmitNum;

    /**
     * 线索通表单提交率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal clueFormSubmitRate;


    /**
     * 订单提交率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal orderSubmitRate;

    /**
     * 订单完成率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal paymentRate;

    /**
     * 订单综合完成率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal comprehensivePaymentRate;

    /**
     * 使用优惠券的订单完成率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal numberOfOrdersCompletedForCouponRate;

    /**
     * 重复访客数
     */
    @TableField(exist = false)
    private Long repeatVisitorNum;

    /**
     * 使用优惠券的订单完成数
     */
    @TableField(exist = false)
    private BigDecimal numberOfOrdersCompletedForCouponNum;

    /**
     * 电商商品购买数
     */
    @TableField(exist = false)
    private Long onlineShopBuyGoodsSuccessNum;

    /**
     * 电商商品购买率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal onlineShopBuyGoodsSuccessRate;


    /**
     * 1.197.0版本新增
     */
    /**
     * 长按识别企业微信群二维码数
     */
    @JsonSerialize(using = ThousandthSerialize.class)
    @TableField(exist = false)
    private Long identifyGroupQrCodeNum;

    /**
     * 添加企业微信群数
     */
    @JsonSerialize(using = ThousandthSerialize.class)
    @TableField(exist = false)
    private Long addWorkWechatGroupNum;

    /**
     * 长按识别企业微信群二维码率
     */
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    @TableField(exist = false)
    private BigDecimal identifyGroupQrCodeRate;

    /**
     * 添加企业微信群率
     */
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    @TableField(exist = false)
    private BigDecimal addWorkWechatGroupRate;





    /**
     * 落地页类型数组参数
     */
    @TableField(exist = false)
    private String landingPageTypes;

    @TableField(exist = false)
    private List<LandingPageType> landingPageTypeList;

    /**
     * 是否安装企业推应用
     */
    @TableField(exist = false)
    private Boolean qiyetuiFlag;

    /**
     * 渠道的域名
     */
    private String landingPageDomain;

    @TableField(exist = false)
    private Long wechatOfficialArticlePageViewNum;
    /**
     * 1.201.0
     */
    /**
     * 企业推corpId
     */
    private String qiyeTuiCorpId;
    /**
     * 企业推名称
     */
    private String qiyeTuiWechatAppletName;

    /**
     * 企业推明文id
     */
    private String corpid;

    /**
     * 投放媒体
     */
    private Platform launchPlatform;

    /**
     * 推啊账户ID
     */
    @TableField(exist = false)
    private String tuiaAccountId;

    /**
     * 推啊账户名称
     */
    @TableField(exist = false)
    private String tuiaAccountName;

    /**
     * oppo账户名称
     */
    @TableField(exist = false)
    private String oppoAccountId;

    /**
     * oppo账户id
     */
    @TableField(exist = false)
    private String oppoAccountName;

    /**
     * 支付宝数字推广平台
     */
    @TableField(exist = false)
    private String alipayAccountName;

    /**
     * 小红书账户名称
     */
    @TableField(exist = false)
    private String xhsAccountId;

    /**
     * 小红书账户id
     */
    @TableField(exist = false)
    private String xhsAccountName;

    /**
     * 企业推审核记录
     */
    @TableField(exist = false)
    private List<LandingPageChannelAuditRecords> reasons;

    /**
     * 抖音安全链接-只放置参数
     */
    private String douyinSecureLink;


    /**
     * 字节小程序原生订单提交数
     */
    @TableField(exist = false)
    private Long douyinAppletOrderSubmitNum;

    /**
     * 字节小程序原生订单提交率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal douyinAppletOrderSubmitRate;

    /**
     * 字节小程序原生订单完成数
     */
    @TableField(exist = false)
    private Long douyinAppletOrderFinishNum;

    /**
     * 字节小程序原生订单完成率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal douyinAppletOrderFinishRate;

    /**
     * 淘宝组件复制成功数
     */
    @TableField(exist = false)
    private Long taobaoComponentCopyNum;

    /**
     * 淘客组件成功复制率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal taobaoComponentCopyRate;

    /**
     * 字节小程序跳转数
     */
    @TableField(exist = false)
    private Long douyinAppletJumpNum;

    /**
     * 字节小程序跳转率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal douyinAppletJumpRate;

    /**
     * 关注公众号后发码加粉数  关注公众号后通过发送的二维码图片加粉的次数
     */
    @TableField(exist = false)
    private Long officialAddCustomerNum;

    /**
     * 关注公众号后发码加粉率  关注公众号后加粉数/PV*100%
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal officialAddCustomerRate;

    /**
     * 关注公众号后发码加粉成本     释义：消耗/关注公众号后加粉数
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal officialAddCustomerCost;


    /**
     * 饿了么小程序访问数（PV）
     */
    @TableField(exist = false)
    private Long elePvNum;

    /**
     * 饿了么小程序二维码展示次数
     */
    @TableField(exist = false)
    private Long eleQrCodeViewNum;

    /**
     * 饿了么小程序长按二维码识别数
     */
    @TableField(exist = false)
    private Long eleIdentifyWechatQrCodeNum;

    /**
     * 饿了么小程序成功添加企业微信数
     */
    @TableField(exist = false)
    private Long eleAddWechatSuccessNum;

    /**
     * whatsapp 跳转数
     */
    @TableField(exist = false)
    private Long whatsappJumpNum;
    /**
     * whatsapp 好友建联数
     */
    @TableField(exist = false)
    private Long whatsappAddFriendSuccessNum;
    /**
     * whatsapp 好友开口数
     */
    @TableField(exist = false)
    private Long whatsappUserOpenMouthNum;

    /**
     * whatsapp 跳转率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal whatsappJumpRate;
    /**
     * whatsapp 好友建联数
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal whatsappAddFriendSuccessRate;
    /**
     * whatsapp 好友开口数
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal whatsappUserOpenMouthRate;
    /**
     * 点击跳转淘宝电影小程序数
     */
    @TableField(exist = false)
    private Long taoBaoMovieAppletJumpNum;

    /**
     * 点击跳转淘宝电影小程序率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal taoBaoMovieAppletJumpRate;

    /**
     * 淘宝电影小程序订单数
     */
    @TableField(exist = false)
    private Long taoBaoMovieAppletOrderNum;

    /**
     * 淘宝电影小程序订单率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal taoBaoMovieAppletOrderRate;


    /**
     * 加企业微信后入群数
     */
    @TableField(exist = false)
    private Long addGroupAfterAddCustomerServiceNum;

    /**
     * 加企业微信后入群率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal addGroupAfterAddCustomerServiceRate;

    /**
     * 公众号助手发码加粉后入群数
     */
    @TableField(exist = false)
    private Long addGroupAfterFollowOfficialAccountNum;

    /**
     * 公众号助手发码加粉后入群率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal addGroupAfterFollowOfficialAccountRate;


    /**
     * 成功发码/图片消息数（微信客服机器人）
     */
    @TableField(exist = false)
    private Long sendImageOrQrCodeNum;

    /**
     * 成功发码/图片消息率（微信客服机器人）
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal sendImageOrQrCodeRate;

    /**
     * 成功发送小程序消息数（微信客服机器人）
     */
    @TableField(exist = false)
    private Long miniProgramNewsNum;

    /**
     * 成功小程序消息率（微信客服机器人）
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal miniProgramNewsRate;

    /**
     * 微信客服进线数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long incomeLineNum;


    /**
     * 微信客服进线率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal incomeLineRate;

    /**
     * 广告回传数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long adUploadNum;

    /**
     * 广告回传率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal adUploadRate;

    /**
     * 跳转领取618超级红包数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long jumpToSuperRedEnvelopeNum;

    /**
     * 跳转领取618超级红包率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal jumpToSuperRedEnvelopeRate;

    /**
     * 发送欢迎语数（微信客服）
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long successSendWelcomeMsgNum;


    /**
     * 发送欢迎语率（微信客服）
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal successSendWelcomeMsgRate;


    /**
     * taobao dsp 商品页面流量数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long taobaoPageViewNum;
    /**
     * taobao dsp 商品订单支付数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long taobaoOrderPaymentNum;
    /**
     * taobao dsp 商品点击数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long taobaoProductClickNum;
    /**
     * taobao dsp 商品首次到达场馆数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long taobaoFirstVisitVenueNum;
    /**
     * taobao dsp 商品领取红包数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long taobaoRedEnvelopeReceiveNum;
    /**
     * taobao dsp 商品取消订单支付数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long taobaoCancelOrderPaymentNum;
    /**
     * taobao dsp 商品高佣订单支付数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long taobaoHighCommissionOrderPaymentNum;

    /**
     * taobao dsp 商品页面流量率
     */
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    @TableField(exist = false)
    private BigDecimal taobaoPageViewRate;
    /**
     * taobao dsp 商品订单支付率
     */
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    @TableField(exist = false)
    private BigDecimal taobaoOrderPaymentRate;
    /**
     * taobao dsp 商品点击率
     */
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    @TableField(exist = false)
    private BigDecimal taobaoProductClickRate;
    /**
     * taobao dsp 商品首次到达场馆率
     */
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    @TableField(exist = false)
    private BigDecimal taobaoFirstVisitVenueRate;
    /**
     * taobao dsp 商品领取红包率
     */
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    @TableField(exist = false)
    private BigDecimal taobaoRedEnvelopeReceiveRate;
    /**
     * taobao dsp 商品取消订单支付率
     */
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    @TableField(exist = false)
    private BigDecimal taobaoCancelOrderPaymentRate;
    /**
     * taobao dsp 商品高佣订单支付率
     */
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    @TableField(exist = false)
    private BigDecimal taobaoHighCommissionOrderPaymentRate;


    /**
     * 首次开口数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long startOpenChatNum;


    /**
     * 首次开口率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal startOpenChatRate;



    /**
     * 三次开口数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long thirdOpenChatNum;


    /**
     * 三次开口率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal thirdOpenChatRate;

    /**
     * 5次开口数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long fifthOpenChatNum;


    /**
     * 5次开口率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal fifthOpenChatRate;

    /**
     * 10次开口数
     */
    @TableField(exist = false)
    @JsonSerialize(using = ThousandthSerialize.class)
    private Long tenthOpenChatNum;


    /**
     * 10次开口率
     */
    @TableField(exist = false)
    @JsonSerialize(using = BigDecimalValueSerialize.class)
    private BigDecimal tenthOpenChatRate;

}
