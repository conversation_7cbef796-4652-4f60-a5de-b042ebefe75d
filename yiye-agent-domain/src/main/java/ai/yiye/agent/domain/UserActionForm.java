package ai.yiye.agent.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.lang.annotation.*;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class UserActionForm {

    /**
     * 	推广帐号 id，有操作权限的帐号 id，包括代理商和广告主帐号 id
     */
    @JsonProperty("account_id")
    private String accountId;

    /**
     * 用户行为源 id，通过 [user_action_sets 接口] 创建用户行为源时分配的唯一 id。
     * 请注意，当填写的用户行为数据源类型为 {WECHAT, WECHAT_MINI_PROGRAM, WECHAT_MINI_GAME} 时，
     * 必填 user_id 字段中的 wechat_openid (或 wechat_unionid) 及 wechat_app_id。
     */
    @JsonProperty("user_action_set_id")
    private String userActionSetId;

    /**
     * 返回数组列表，不能大于 50KB
     * 数组最小长度 1，最大长度 50
     */
    private Action[] actions;

    public enum ActionType {
        RESERVATION, COMPLETE_ORDER, CONSULT, PURCHASE, CONFIRM_EFFECTIVE_LEADS
    }

    @Target(ElementType.FIELD)
    @Retention(RetentionPolicy.RUNTIME)
    @Inherited
    @Documented
    private @interface BundleField {
        String value();
    }

    @Data
    public static class Trace {
        @JsonProperty("click_id")
        private String clickId;
    }

    @Data
    public static class Action {

        /**
         * 行为发生时，客户端的时间点。UNIX 时间，单位为秒，如果不填将使用服务端时间填写 最小值 0，最大值 2147483647
         */
        @JsonProperty("action_time")
        private Long actionTime = Instant.now().getEpochSecond();

        /**
         * 	标准行为类型，当值为 'CUSTOM' 时表示自定义行为类型，[枚举详情]：https://developers.e.qq.com/docs/reference/enum#api_action_type
         */
        @JsonProperty("action_type")
        private ActionType actionType = ActionType.RESERVATION;

        /**
         * 用户自定义的行为 id 标识，字段长度最小 0 字节，长度最大 255 字节
         */
        @JsonProperty("external_action_id")
        private String externalActionId;

        /**
         * 行为所带的参数，详见 [param_map]：https://developers.e.qq.com/docs/reference/illustration#param_map
         * 字段长度最小 1 字节，长度最大 204800 字节
         */
        @JsonProperty("action_param")
        private Object actionParam;

        private Integer stateUpdateAt;
        private String state;
        private String phone;

        /**
         * url，网页回传时必须要填写 url，请填写效果数据发生 h5 页面 URL 地址，字段长度最小 1 字节，长度最大 2048 字节
         */
        private String url;

        /**
         * 跟踪信息
         */
        private Trace trace;
    }

    @Data
    public static class ActionParam {
        private String leadsStandardFormInfo;
    }

    @Data
    public static class LeadsStandardFormInfo {
        @BundleField("姓名")
        private String leadsName;
        @BundleField("电话")
        private String leadsTel;
        private Long commonPageId;
        private String pageName;
        private String pageUrl;
        private Map<String, Object> bundle = new HashMap<>();

        public void buildBundle() {
            Arrays.stream(this.getClass().getDeclaredFields())
                .peek(f -> f.setAccessible(true))
                .filter(f -> f.isAnnotationPresent(BundleField.class))
                .forEach(f -> {
                    try {
                        this.bundle.put(f.getAnnotation(BundleField.class).value(), f.get(this));
                    } catch (IllegalAccessException ignore) {
                        // NO-OP
                    }
                });
        }
    }
}
