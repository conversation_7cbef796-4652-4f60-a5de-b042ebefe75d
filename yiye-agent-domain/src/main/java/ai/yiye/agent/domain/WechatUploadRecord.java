package ai.yiye.agent.domain;

import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/10 20:40
 */
@Data
@TableName(value = "marketing_wechat_upload_record", autoResultMap = true)
public class WechatUploadRecord extends AbstractMarketingData {
    //原名(当为zip包时，这个名字为zip包名，当下边的picinclude为图片时，表示包内图片)
    private String originalName;

    //父packageId
    private Long parentPackageId = 0L;

    //图片上传的地址url
    private String url;

    //上传到云之后的path
    private String path;

    //0为未解析，1表示已解析
    private Integer uploadStatus = 0;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
