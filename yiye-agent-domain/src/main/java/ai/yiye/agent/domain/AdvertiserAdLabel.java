package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AdvertiserAccountStatus;
import ai.yiye.agent.domain.enumerations.AdvertiserAccountSystemStatus;
import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import ai.yiye.agent.domain.typehandlers.TextArrayTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@TableName("marketing_data_ad_label")
public class AdvertiserAdLabel extends AbstractMarketingData {

    @TableField(exist = false)
    public static final String[] CONFLICTS = new String[]{"advertiser_account_id", "label_category"};
    @TableField(exist = false)
    protected JSONObject ext;
    @TableField(exist = false)
    protected Long optimizerId;
    private String labelCategory;
    @TableField(typeHandler = TextArrayTypeHandler.class)
    private String[] label;
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
    @TableField(exist = false)
    private AdvertiserAccountSystemStatus advertiserAccountStatus;
}
