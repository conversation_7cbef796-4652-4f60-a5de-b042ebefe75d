package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 第三方平台配置表(MarketingPlatformConf)实体类
 *
 * <AUTHOR>
 * @since 2020-05-18 16:24:04
 */
@Data
@TableName("marketing_platform_conf")
@NoArgsConstructor
@AllArgsConstructor
public class PlatformConf implements Serializable {

    @TableId(type = IdType.INPUT)
    private Integer id;
    /**
     * 平台名称
     */
    private String name;
    /**
     * 列表页logo
     */
    private String logoList;

    /**
     * 授权页logo
     */
    private String logoAuth;
    /**
     * 应用id
     */
    private String marketingClientId;
    /**
     * 应用密码
     */
    private String marketingClientSecret;
    /**
     * 授权地址
     */
    private String authorizationUrl;
    /**
     * 授权地址
     */
    private String redirectUrl;

    private Instant createdAt;

    private Instant updatedAt;
}
