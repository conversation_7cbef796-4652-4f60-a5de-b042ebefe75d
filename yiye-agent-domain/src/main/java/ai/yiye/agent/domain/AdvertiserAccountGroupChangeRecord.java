package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.BaseStatusEnum;
import ai.yiye.agent.domain.enumerations.OperationActionEnum;
import ai.yiye.agent.domain.enumerations.YesOrNoEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * ASP项目变更记录表
 */
@Data
@TableName("marketing_advertiser_account_group_change_record")
public class AdvertiserAccountGroupChangeRecord implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 项目id
     */
    private Long advertiserAccountGroupId;

    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String advertiserAccountGroupName;

    /**
     * 操作动作：开启项目/暂停项目
     */
    private OperationActionEnum operationAction;

    /**
     * 操作结果：开启项目/暂停项目
     */
    private OperationActionEnum operationResult;

    /**
     * 操作者：操作者账号
     */
    private Long operationUserId;

    /**
     * 操作者名称
     */
    @TableField(exist = false)
    private String operationUserName;

    /**
     * 操作者IP：操作者IP
     */
    private String operationUserIp;

    // 创建时间
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    // 修改时间
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    @TableField(exist = false)
    private Instant startTime;

    @TableField(exist = false)
    private Instant endTime;

}
