package ai.yiye.agent.domain;

import ai.yiye.agent.domain.typehandlers.TextArrayTypeHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 腾讯广告版位
 */
@Data
@TableName("marketing_tencent_adcreative_template")
public class AdcreativeTemplate {
    @TableId(
        type = IdType.AUTO
    )
    private Long id;

    /**
     * 规格id
     */
    private Long adcreativeTemplateId;

    /**
     * 推广目标
     */
    private String promotedObjectType;

    /**
     * 站点
     */
    private String siteSet;

    /**
     * 创意形式
     */
    private String adcreativeTemplateStyle;

    /**
     * 创意形式描述
     */
    private String adcreativeTemplateAppellation;

    /**
     * 支持的出价方式
     */
    private JSONObject adcreativeTemplateStructure;

    @TableField(typeHandler = TextArrayTypeHandler.class)
    private String[] supportBidWays;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
