package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("landing_page_domain_remind_config")
public class LandingPageDomainRemindConfig {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * pmp项目id
     */
    private Long advertiserAccountGroupId;


    /**
     * 邮箱
     */
    private String[] emails;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 飞书自定义机器人key
     */
    private String feishuRobotKey;

    /**
     * 企业微信自定义机器人key
     */
    private String wecomRobotKey;

    /**
     * 钉钉自定义机器人key
     */
    private String dingtalkRobotKey;

}
