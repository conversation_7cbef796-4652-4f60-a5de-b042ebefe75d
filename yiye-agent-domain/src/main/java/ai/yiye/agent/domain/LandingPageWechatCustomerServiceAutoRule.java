package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.*;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

/**
 * 落地页 - 微信客服管理 - 自动化规则
 */
@Data
@TableName("landing_page_wechat_customer_service_auto_rule")
public class LandingPageWechatCustomerServiceAutoRule {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * pmp项目id，关联表：marketing_advertiser_account_group.id
     */
    @NotNull(message = "PMP项目分组id为必填项")
    private Long advertiserAccountGroupId;

    /**
     * 客服id，关联表：landing_page_wechat_customer_service.id
     */
    private Long landingPageWechatCustomerServiceId;

    /**
     * 类型
     */
    private AutoRuleType type;

    /**
     * 检查周期
     */
    private AutoRuleInspectionCycle inspectionCycle;

    /**
     * 时间范围
     */
    private BigDecimal timeFrame;

    /**
     * 下线规则 数值
     */
    private BigDecimal offlineNum;

    /**
     * 上线规则 时间
     */
    private String onlineTime;

    /**
     * 下线规则 时间
     */
    private String offlineTime;

    /**
     * 周循环
     * 若数组不为空，则数组长度一定为7，数组的每一个元素表示一天的排班
     * 一天有48个班次，其中00:00:00~00:30:00是第1个班次，23:30:00~00:00:00是第48个班次
     * 若客服在第八个班次需要排班则元素转换为二进制数从右往左数第8位数为1，否则为0，以此类推
     */
    private Long[] weeklyCycle;

    /**
     * 自定义数据范围状态 0:关闭 1:开启
     */
    private SwitchStatus customizeDataRangeStatus;

    /**
     * 自定义数据范围 0:当前项目内加粉数 1:账户内加粉数
     */
    private CustomizeDataRange customizeDataRange;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;


    //这边新增一个对比的方法,记录每一次修改
    public static String getCompareString(LandingPageWechatCustomerServiceAutoRule autoRule) {
        if (AutoRuleType.ADD_WORKWECHAT_NUM.equals(autoRule.getType())) {
            String s = "";
            //首先获取类型，然后获取检查周期
            if (AutoRuleInspectionCycle.EVERY_DAY.equals(autoRule.getInspectionCycle())) {
                s = "每天成功添加企业微信" + autoRule.getOfflineNum() + "次后自动下线";
            } else if (AutoRuleInspectionCycle.CUSTOM.equals(autoRule.getInspectionCycle())) {
                s = autoRule.getTimeFrame() + "小时内成功添加企业微信" + autoRule.getOfflineNum() + "次后自动下线";
            }
            if (SwitchStatus.OPEN.equals(autoRule.getCustomizeDataRangeStatus())) {
                if (CustomizeDataRange.CURRENT_PROJECT.equals(autoRule.getCustomizeDataRange())) {
                    s = s + "（当前项目内加粉数）";
                }
                if (CustomizeDataRange.ACCOUNT.equals(autoRule.getCustomizeDataRange())) {
                    s = s + "（账户内加粉数）";
                }
            }
            return s;
        } else if (AutoRuleType.ADD_WORKWECHAT_RATE.equals(autoRule.getType())) {
            return "每小时添加企业微信率小于等于" + autoRule.getOfflineNum() + "%后自动下线";
        } else if (AutoRuleType.IDENTIFY_QRCODE_NUM.equals(autoRule.getType())) {

            if (AutoRuleInspectionCycle.EVERY_DAY.equals(autoRule.getInspectionCycle())) {
                return "每天二维码被长按识别" + autoRule.getOfflineNum() + "次后自动下线";
            } else if (AutoRuleInspectionCycle.CUSTOM.equals(autoRule.getInspectionCycle())) {
                return autoRule.getTimeFrame() + "小时内二维码被长按识别" + autoRule.getOfflineNum() + "次后自动下线";
            }
        } else if (AutoRuleType.POINT_OF_TIME.equals(autoRule.getType())) {
            //按时间点控制
            if (AutoRuleInspectionCycle.EVERY_DAY.equals(autoRule.getInspectionCycle())) {
                //每天每天自定义设置时间点自动下线,每天自定义设置时间点 自动上线
                return "每天" + autoRule.getOfflineTime() + "自动下线，每天" + autoRule.getOnlineTime() + "自动上线";
            } else if (AutoRuleInspectionCycle.WEEKLY_CYCLE.equals(autoRule.getInspectionCycle())) {
                //如果是每周的 这边要进行一个值的获取
                String inspectionCycleByWeekly = getInspectionCycleByWeekly(autoRule.getWeeklyCycle());
                return inspectionCycleByWeekly;
            }

        }
        return "";
    }

    //对比方法，如果自定义数值为0，这边取消自定义数值为0的文案
    public static String getCompareStringWithdrawZero(LandingPageWechatCustomerServiceAutoRule autoRule) {
        if (AutoRuleType.ADD_WORKWECHAT_NUM.equals(autoRule.getType())) {
            if (new BigDecimal(0).equals(autoRule.getOfflineNum())) {
                return "";
            }
            String s = "";
            //首先获取类型，然后获取检查周期
            if (AutoRuleInspectionCycle.EVERY_DAY.equals(autoRule.getInspectionCycle())) {
                s = "每天成功添加企业微信" + autoRule.getOfflineNum() + "次后自动下线";
            } else if (AutoRuleInspectionCycle.CUSTOM.equals(autoRule.getInspectionCycle())) {
                s = autoRule.getTimeFrame() + "小时内成功添加企业微信" + autoRule.getOfflineNum() + "次后自动下线";
            }
            if (SwitchStatus.OPEN.equals(autoRule.getCustomizeDataRangeStatus())) {
                if (CustomizeDataRange.CURRENT_PROJECT.equals(autoRule.getCustomizeDataRange())) {
                    s = s + "（当前项目内加粉数）";
                }
                if (CustomizeDataRange.ACCOUNT.equals(autoRule.getCustomizeDataRange())) {
                    s = s + "（账户内加粉数）";
                }
            }
            return s;
        } else if (AutoRuleType.ADD_WORKWECHAT_RATE.equals(autoRule.getType())) {
            if (new BigDecimal(0).equals(autoRule.getOfflineNum())) {
                return "";
            }
            return "每小时添加企业微信率小于等于" + autoRule.getOfflineNum() + "%后自动下线";
        } else if (AutoRuleType.IDENTIFY_QRCODE_NUM.equals(autoRule.getType())) {
            if (new BigDecimal(0).equals(autoRule.getOfflineNum())) {
                return "";
            }
            if (AutoRuleInspectionCycle.EVERY_DAY.equals(autoRule.getInspectionCycle())) {
                return "每天二维码被长按识别" + autoRule.getOfflineNum() + "次后自动下线";
            } else if (AutoRuleInspectionCycle.CUSTOM.equals(autoRule.getInspectionCycle())) {

                return autoRule.getTimeFrame() + "小时内二维码被长按识别" + autoRule.getOfflineNum() + "次后自动下线";
            }
        } else if (AutoRuleType.POINT_OF_TIME.equals(autoRule.getType())) {
            //按时间点控制
            if (AutoRuleInspectionCycle.EVERY_DAY.equals(autoRule.getInspectionCycle())) {
                //每天每天自定义设置时间点自动下线,每天自定义设置时间点 自动上线
                return "每天" + autoRule.getOfflineTime() + "自动下线，每天" + autoRule.getOnlineTime() + "自动上线";
            } else if (AutoRuleInspectionCycle.WEEKLY_CYCLE.equals(autoRule.getInspectionCycle())) {
                //如果是每周的 这边要进行一个值的获取
                String inspectionCycleByWeekly = getInspectionCycleByWeekly(autoRule.getWeeklyCycle());
                return inspectionCycleByWeekly;
            }

        }
        return "";
    }


    public static String getInspectionCycleByWeekly(Long[] longs) {
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < longs.length; i++) {
            long aLong = longs[i];
            String ibstr = Long.toBinaryString(aLong);
            while (ibstr.length() < 48) {
                ibstr = "0" + ibstr;
            }
            String s = new StringBuffer(ibstr).reverse().toString();
            stringBuffer.append(s);
        }
        String timeSeriesDescription = getTimeSeriesDescription(stringBuffer.toString());
        return timeSeriesDescription;
    }

    public static String getTimeSeriesDescription(String advancedTimeSeries) {
        if (advancedTimeSeries == null || advancedTimeSeries.isEmpty()) {
            return "";
        }
        //这边缺少一个判断全天的方法
//        if ("1".repeat(7 * 48).equals(advancedTimeSeries)) {
//            return "全天";
//        }
        HashMap<Integer, String> descriptionMap = new HashMap<>();
        Integer[] weekdays = new Integer[]{1, 2, 3, 4, 5, 6, 7};
        for (int i = 0; i < weekdays.length; i++) {
            String dailyTimeSeries = advancedTimeSeries.substring(i * 48, (i + 1) * 48);
            descriptionMap.put(weekdays[i], parseTimeSeries(dailyTimeSeries));
        }
        List<String> result = new ArrayList<>();
        result.add("在线时间：");

        Set set = descriptionMap.keySet();
        Object[] arr = set.toArray();
        Arrays.sort(arr);
        for (Object key : arr) {
            String description = descriptionMap.get(key);
            if (description != null && !description.isEmpty()) {
                //排完序之后，根据1-7 转周一到周日
                WeekEnum weekByValue = WeekEnum.getWeekByValue((Integer) key);
                result.add(weekByValue.getDescription() + ": " + description);
            }
        }
        return String.join("  ", result);
    }

    private static String parseTimeSeries(String dailyTimeSeries) {
        int[] timeSeries = dailyTimeSeries.chars().map(c -> c - '0').toArray();
        List<String> descriptions = new ArrayList<>();
        int start = -1;
        for (int i = 0; i < timeSeries.length; i++) {
            if (timeSeries[i] == 0 && start > -1) {
                // 解析时间段
                descriptions.add(getDurationDescription(start) + '-' + getDurationDescription(i));
                start = -1;
            } else if (timeSeries[i] == 1 && start < 0) {
                start = i;
            }
        }
        // 处理 xx-24:00 的情况
        if (start > -1) {
            descriptions.add(getDurationDescription(start) + '-' + getDurationDescription(timeSeries.length));
        }
        return String.join(", ", descriptions);
    }

    private static String getDurationDescription(int i) {
        int hours = i / 2;
        int minutes = (i % 2 == 0) ? 0 : 30;
        return String.format("%02d:%02d", hours, minutes);
    }

}

