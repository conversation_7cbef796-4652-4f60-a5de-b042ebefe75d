package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AdvertiserAccountDataInitFinishStatus;
import ai.yiye.agent.domain.enumerations.AdvertiserAccountDataInitType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 数据初始化任务表实体类
 *
 * <AUTHOR>
 * @date 2021/8/23 16:22
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("marketing_data_init_job")
public class MarketingDataInitJob implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String[] CONFLICTS = new String[]{"account_id", "platform_id"};

    /**
     * 广告主id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 账户id
     */
    private String accountId;

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 初始化类型 0：一天，1：三十天
     */
    private AdvertiserAccountDataInitType initType;

    /**
     * 初始化完成状态 0：未完成，1：完成
     */
    private AdvertiserAccountDataInitFinishStatus isFinish;

    /**
     * 初始化完成后通知邮箱地址
     */
    private String email;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
