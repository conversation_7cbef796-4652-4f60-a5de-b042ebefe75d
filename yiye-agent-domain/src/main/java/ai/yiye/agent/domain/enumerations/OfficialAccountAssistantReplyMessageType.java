package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.annotation.EnumValue;

import java.util.Objects;

public enum OfficialAccountAssistantReplyMessageType {

    /**
     * 普通文本消息
     */
    TXT(0, "文本消息", "text"),

    /**
     * 临时素材-图片
     */
    IMAGE(1, "图片消息", "image"),

    /**
     * 活码：即系统内生成的动态渠道二维码（公众号内加粉）
     */
    DYNAMIC_QR_CODE(2, "活码-客服动态渠道二维码", "image"),

    /**
     * 活码：即系统内生成的动态渠道二维码（公众号内加粉）
     */
    GROUP_DYNAMIC_QR_CODE(3, "活码-分组动态渠道二维码", "image"),
    ;

    @EnumValue
    private final int id;

    private final String name;

    /**
     * 对应微信公众号发送的消息类型
     */
    private final String msgtype;

    OfficialAccountAssistantReplyMessageType(int id, String name, String msgtype) {
        this.id = id;
        this.name = name;
        this.msgtype = msgtype;
    }

    public int getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public String getMsgtype() {
        return msgtype;
    }

    public static OfficialAccountAssistantReplyMessageType getEnumById(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        for (OfficialAccountAssistantReplyMessageType value : OfficialAccountAssistantReplyMessageType.values()) {
            if (Objects.equals(value.getId(), id)) {
                return value;
            }
        }
        return null;
    }

}
