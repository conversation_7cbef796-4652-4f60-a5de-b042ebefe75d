package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 巨量引擎行业列表
 */
@Data
@TableName("marketing_data_ocean_engine_industry")
public class OceanEngineIndustry {
    /**
     * 行业id
     */
    @TableId(
        type = IdType.INPUT
    )
    private Long id;

    /**
     * 行业名称
     */
    private String industryName;

    /**
     * 所在级别: 1=一级行业, 2=二级行业, 3=三级行业
     */
    private Integer level;

    /**
     * 该行业的一级行业ID
     */
    private Long firstIndustryId;

    /**
     * 该行业的一级行业名称
     */
    private String firstIndustryName;

    /**
     * 该行业的二级行业ID
     */
    private Long secondIndustryId;

    /**
     * 该行业的二级行业名称
     */
    private String secondIndustryName;

    /**
     * 该行业的三级行业ID
     */
    private Long thirdIndustryId;

    /**
     * 该行业的三级行业名称
     */
    private String thirdIndustryName;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Instant createdAt;

    @TableField(
        fill = FieldFill.INSERT_UPDATE
    )
    private Instant updatedAt;
}
