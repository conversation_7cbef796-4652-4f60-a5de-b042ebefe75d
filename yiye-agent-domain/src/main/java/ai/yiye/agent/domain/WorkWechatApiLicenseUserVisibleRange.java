package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.ApiAccountType;
import ai.yiye.agent.domain.enumerations.FollowStatus;
import ai.yiye.agent.domain.enumerations.LicenseStatus;
import ai.yiye.agent.domain.enumerations.UserVisibleRangeStatus;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 企业微信 - 可见范围 - 表
 */
@Data
@TableName("work_wechat_api_license_user_visible_range")
public class WorkWechatApiLicenseUserVisibleRange {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业微信id
     */
    private String corpId;

    /**
     * 可见范围 - 成员名称
     */
    private String name;

    /**
     * 可见范围 - 成员userId
     */
    private String userId;

    /**
     * 可见范围 - 成员二维码
     */
    private String qrCode;

    /**
     * 激活状态：0-初始免费期  1-未激活  2-已激活
     */
    private LicenseStatus licenseStatus;

    /**
     * 激活时间
     */
    private Instant licenseActiveTime;

    /**
     * 过期时间
     */
    private Instant licenseExpireTime;

    /**
     * 接口账号类型：1-基础账号  2-互通账号
     */
    private ApiAccountType apiAccountType;

    /**
     * 部门id
     */
    private Long[] departmentId;

    /**
     * 部门名称
     */
    private String[] departmentName;

    /**
     * 手机号
     */
    private String wechatMobile;

    /**
     * 邮箱
     */
    private String wechatEmail;

    /**
     * 可见范围状态：0-不在可见范围  1-在可见范围
     */
    private UserVisibleRangeStatus userVisibleRangeStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 企业微信名称
     */
    @TableField(exist = false)
    private String enterpriseWechatName;

    /**
     * 置顶状态
     */
    @TableField(exist = false)
    private FollowStatus followStatus;
    /**
     * 是否过期
     */
    @TableField(exist = false)
    private Boolean licenseExpireTimeStatus;

}
