package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 微信机器人客服 - 回复消息模板表
 */
@TableName("wechat_robot_customer_msg_template")
@Data
@NoArgsConstructor
public class WechatRobotCustomerMsgTemplate implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 类型
     *
     * ai.yiye.agent.weixin.enumerations.Messagetype
     *
     * text-文本消息
     * msgmenu-菜单消息
     * view-超链接菜单(子级类型)
     * click-回复菜单(子级类型)
     * miniprogram-小程序菜单(子级类型)
     * head_content-消息内容:起始文本(子级类型)
     * tail_content-消息内容:结束文本(子级类型)
     * click_text-回复菜单-文本消息(子级类型)
     * click_customer_qr_code_picture-回复菜单-客服二维码图片(子级类型)
     */
    private Integer msgType;

    /**
     * 级联id / 一级模板id，用于根据模板id删除，关联表：wechat_robot_customer_msg_temp.id
     */
    private Long templateId;

    /**
     * 级联id / 父级消息id：0-顶级  xxx-n级父级id
     */
    private Long parentId;

    /**
     * 存消息内容：msg_type为【text-文本消息、head_content-消息内容:起始文本(子级类型)、tail_content-消息内容:结束文本(子级类型)】时生效
     * 存标题：msg_type为【view-超链接菜单(子级)、miniprogram-小程序菜单(子级类型)、head_content-消息内容:起始文本(子级类型)】时生效
     */
    private String content;

    /**
     * 打开超链接类型（msg_type=view时生效）：open_channel-打开渠道链接  open_wechat_official_account_article_page-公众号历史文章页
     */
    private Integer openType;

    /**
     * 落地页id（msg_type=view时生效）
     */
    private Long landingPageId;

    /**
     * 落地页渠道id（msg_type=view时生效）
     */
    private Long channelId;

    /**
     * 小程序appid（msg_type=miniprogram时生效）
     */
    private String wechatAppletAppid;

    /**
     * 小程序路径（msg_type=miniprogram时生效）
     */
    private String wechatAppletPath;

    /**
     * 排序值，默认为0，数值越大越靠后
     */
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 修改时间
     */
    @TableField(exist = false)
    private String filePath;

}
