package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.Instant;

@Data
@TableName("taobao_dsp_config_rel")
public class TaobaoDspConfigRel {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long taobaoDspConfigId;

    private Long advertiserAccountGroupId;

    private Instant createdAt;

    private Instant updatedAt;
}
