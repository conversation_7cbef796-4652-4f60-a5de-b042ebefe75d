package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.ArrayTypeHandler;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * ucenter_permission_group
 *
 * <AUTHOR>
@Data
@TableName("ucenter_permission_group")
@AllArgsConstructor
@NoArgsConstructor
public class PermissionGroup implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 权限组名称
     */
    @Pattern(regexp = "^([\\u4e00-\\u9fa5]|\\w|\\.|-)+$", message = "error.PermissionGroup.name.Pattern")
    @NotBlank(message = "error.PermissionGroup.name.NotBlank")
    @Length(max = 32, message = "error.PermissionGroup.name.Length.max")
    private String name;
    @NotNull(message = "error.PermissionGroup.permissionIds.isNotNull")
    @Size(min = 1, message = "error.PermissionGroup.permissionIds.size")
    @TableField(typeHandler = ArrayTypeHandler.class)
    private Long[] permissionIds;
    @TableField(exist = false)
    private List<Permission> permissions;
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
