package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.EnterpriseWechatsIpStatus;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 企业微信ip表
 */
@Data
public class EnterpriseWechatsIp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * agent_id
     */
    private String agentId;

    /**
     * 代理ip
     */
    private String ip;

    /**
     * 企业可信ip
     */
    private String outIp;

    /**
     * 端口
     */
    private String port;

    /**
     * 区域
     */
    private String region;

    /**
     * 企业微信id
     */
    private Long enterpriseWechatsId;

    /**
     * 状态（0：未使用 1：使用中）
     */
    private EnterpriseWechatsIpStatus status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
