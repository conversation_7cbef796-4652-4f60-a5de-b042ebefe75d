package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrganizationFlowType implements IEnum<Integer> {

    // 类型 0:增加 1:扣减 2:冻结 3:解冻

    RECHARGE(0),
    DEDUCT(1),
    FREEZE(2),
    UNFREEZE(3),

    ;

    private final Integer value;
    @Override
    public Integer getValue() {
        return value;
    }

}
