package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.*;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * 上报配置信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "landing_page_upload_configuration", autoResultMap = true)
public class UploadConfiguration implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 落地页ID
     */
    private Long landingPageId;

    /**
     * 渠道id，关联表：landing_page_channels.id
     */
    private Long channelId;

    /**
     * 上报媒体（平台）
     */
    private Platform platformId;

    /**
     * 推广类型
     */
    private PromotionType promotionType;

    /**
     * 快手上报转化ID
     */
    private String convertId;

    /**
     * 投放账户ID
     */
    private String accountId;

    /**
     * 一叶投放账户ID
     */
    private Long advertiserAccountId;

    /**
     * 上报方式
     */
    private UploadType uploadType;

    /**
     * 百度推广，tokenId
     */
    private String tokenId;

    /**
     * 微信广告，appId
     */
    private String appId;

    /**
     * 巨量引擎 - 转化跟踪 - 转化目标 - 复选框是否选中
     */
    private Boolean checkedOceanEngineTrans;

    /**
     * 巨量引擎 - 转化跟踪 - 转化目标 - 类型（一叶系统）
     * <p>
     * null / 空值                    没有选择（前端CheckBox回显时不勾选）
     * 存在值                         有勾选（前端CheckBox回显时勾选）
     */
    private OceanEngineTransTarget oceanEngineTransTarget;

    /**
     * 巨量引擎 - 转化跟踪 - 转化目标 - id
     */
    private String oceanEngineTransTargetId;

    /**
     * 巨量引擎 - 转化跟踪 - 转化目标 - 转化工具转化状态
     * <p>
     * null / 空值                      没有创建
     * AD_CONVERT_STATUS_ACTIVE         活跃（激活）
     * AD_CONVERT_STATUS_INACTIVE       不活跃（未激活）
     * AD_CONVERT_STATUS_CREATE_ERROR   创建失败
     */
    private String oceanEngineTransTargetStatus;

    /**
     * 数据创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 数据修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 投放账户名称
     */
    @TableField(exist = false)
    private String accountName;
    /**
     * 账户有效性
     */

    @TableField(exist = false)
    private ValidType valid;

    /**
     * 上报配置 - 目标转化类型 - 信息
     */
    @TableField(exist = false)
    private List<UploadConfigurationTypes> uploadConfigurationTypesList;

    /**
     * 百度推广id
     */
    //@TableField(exist = false)
    private Long landingPageUploadConfigurationBaiduTokenId;

    /**
     * 小红书上报账户id，关联表：landing_page_upload_xhs_account_config.id
     */
    private Long xhsAccountId;

    /**
     * oppo营销平台上报账户id，关联表：landing_page_upload_oppo_account_config.id
     */
    private Long oppoAccountId;

    /**
     * 支付宝数字推广平台上报账户id，关联表：landing_page_upload_alipay_account_config.id
     */
    private Long alipayAccountId;

    /**
     * Tiktok广告平台上报账户id，关联表：landing_page_upload_tiktok_account_config.id
     */
    private Long tiktokAccountId;

    /**
     * 支付宝数字推广告平台上报账户id，关联表：landing_page_upload_facebook_account_config.id
     */
    private Long facebookAccountId;

    /**
     * 巨量引擎 - 上报方式
     */
    private OceanEngineUploadType oceanEngineUploadType;

    /**
     * 媒体 - 上报方式（全平台通用：建议使用）
     */
    private MediaUploadType mediaUploadType;

    /**
     * 上报数据源id
     */
    private String actionSetId;

    /**
     * 推啊账户表记录ID
     */
    private Long landingPageUploadConfigurationTuiaAccountId;

    /**
     * 推啊账户秘钥
     */
    private String tuiaAdvertKey;

    /**
     * pmp项目id
     */
    @TableField(exist = false)
    private Long advertiserAccountGroupId;

    /**
     * 获取更多广告信息
     */
    private Boolean openMoreAdParams;

    /**
     * 广告计划精准归因上报：false-关闭  true-开启
     */
    private Boolean openAdCampaignUpload;

    /**
     * 广告计划精准归因上报-清理缓存key（随机生成）
     */
    private String openAdCampaignUploadKey;

    /**
     * 批量手动上报客资id集合
     */
    @TableField(exist = false)
    private List<Long> customerIds;

}
