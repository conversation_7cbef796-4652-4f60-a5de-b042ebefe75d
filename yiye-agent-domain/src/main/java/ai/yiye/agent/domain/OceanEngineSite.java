package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AdvertiserAccountSystemStatus;
import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 巨量引擎橙子建站落地页列表
 */
@Data
@TableName("marketing_data_ocean_engine_site")
public class OceanEngineSite extends AbstractMarketingData {

    @TableField(exist = false)
    public static final String[] CONFLICTS = new String[]{"account_id", "site_id"};
    @TableField(exist = false)
    protected JSONObject ext;
    @TableId(
        type = IdType.AUTO
    )
    private Long id;
    /**
     * 建站id
     */
    private String siteId;
    /**
     * 建站名称
     */
    private String name;
    /**
     * 建站状态
     */
    private String siteStatus;
    /**
     * 建站类型
     */
    private String siteType;
    /**
     * 建站类别
     */
    private String functionType;
    /**
     * 站点缩略图
     */
    private String thumbnail;
    @TableField(
        fill = FieldFill.INSERT
    )
    private Instant createdAt;
    @TableField(
        fill = FieldFill.INSERT_UPDATE
    )
    private Instant updatedAt;
    @TableField(exist = false)
    private Long optimizerId;
    @TableField(exist = false)
    private AdvertiserAccountSystemStatus advertiserAccountStatus;

}
