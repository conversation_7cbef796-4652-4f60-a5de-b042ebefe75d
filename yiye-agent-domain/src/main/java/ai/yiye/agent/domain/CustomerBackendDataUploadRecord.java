package ai.yiye.agent.domain;


import ai.yiye.agent.domain.enumerations.BackendCustomerUploadType;
import ai.yiye.agent.domain.enumerations.Platform;
import ai.yiye.agent.domain.enumerations.UploadStateType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 客资上报记录
 */
@Data
@TableName("customer_backend_data_upload_record")
public class CustomerBackendDataUploadRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 落地页id（关联表：landing_page.id）
     */
    private Long landingPageId;

    /**
     * 落地页组件模板id（关联表：landing_page_widget_template.id）
     */
    private Long landingPageWidgetTemplateId;

    /**
     * 一叶系统 - 用户id（关联表：）
     */
    private Long advertiserAccountId;

    /**
     * 客资提交id（关联表：submit_data.id）
     */
    private Long submitDataId;
    private String pid;

    private String url;

    /**
     * 上报到媒体方 - 请求值
     */
    private String sendData;

    /**
     * 上报到媒体方 - 返回值
     */
    private String resultData;

    /**
     * 上报行为类型
     */
    private String actionType;

    /**
     * 上报媒体（平台）
     */
    private Platform platformId;

    /**
     * 上报事件类型
     */
    private BackendCustomerUploadType backendCustomerUploadType;

    /**
     * 客资上报是否成功状态
     */
    private UploadStateType uploadState = UploadStateType.FAIL_REPORT;

    /**
     * 客资上报记录 - 状态码，对应实体：ai.yiye.agent.domain.enumerations.ResponseCode
     */
    private Integer recordCode;

    /**
     * 客资上报 - 状态描述
     */
    private String description;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    private String filed;
    private String filedName;
    private Integer filedType;
    private String filedTypeName;

    @TableField(exist = false)
    private Instant timestamp = createdAt;

}
