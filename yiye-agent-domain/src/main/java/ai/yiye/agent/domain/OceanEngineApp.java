package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.AdvertiserAccountSystemStatus;
import ai.yiye.agent.domain.marketing.data.AbstractMarketingData;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 巨量引擎应用
 */
@Data
@TableName("marketing_data_ocean_engine_app")
public class OceanEngineApp extends AbstractMarketingData {

    @TableField(exist = false)
    public static final String[] CONFLICTS = new String[]{"account_id", "package_id", "channel_id"};
    @TableField(exist = false)
    protected JSONObject ext;
    @TableId(
        type = IdType.AUTO
    )
    private Long id;
    /**
     * 应用ID
     */
    private String packageId;
    /**
     * 包名
     */
    private String packageName;
    /**
     * app id
     */
    private Long appCloudId;
    /**
     * 应用名
     */
    private String appName;
    /**
     * 版本号
     */
    private String version;
    /**
     * 下载地址
     */
    private String downloadUrl;
    /**
     * icon地址
     */
    private String iconUrl;
    /**
     * 渠道id
     */
    private String channelId;

    private Integer extendFlag=0;

    private String packageStatus;

    private String reason;

    @TableField(
        fill = FieldFill.INSERT
    )
    private Instant createdAt;
    @TableField(
        fill = FieldFill.INSERT_UPDATE
    )
    private Instant updatedAt;
    @TableField(exist = false)
    private Long optimizerId;
    @TableField(exist = false)
    private AdvertiserAccountSystemStatus advertiserAccountStatus;
}
