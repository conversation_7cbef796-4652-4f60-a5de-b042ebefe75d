package ai.yiye.agent.domain;

import ai.yiye.agent.domain.typehandlers.JSONArrayHandler;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 客资上报信息关联表
 */
@Data
@TableName("customer_upload_association")
public class CustomerUploadAssociation {

    @TableId(type = IdType.AUTO)
    private Integer id;

    //客资ID
    private Long customerId;

    //pid
    private String pid;

    //上报记录jsonArray
    @TableField(typeHandler = JSONArrayHandler.class)
    private JSONArray customerUploadRecordList;

    //插入时间
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    //更新时间
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
