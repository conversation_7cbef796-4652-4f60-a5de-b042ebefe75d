package ai.yiye.agent.domain.dto;

import ai.yiye.agent.domain.enumerations.*;
import lombok.Data;

import java.time.Instant;
import java.util.Set;

/**
 * 打标签、上报 - 队列参数
 */
@Data
public class WorkWechatTagOrUploadDto {

    /**
     * agnetId
     */
    private String agentId;

    /**
     * pmp项目id
     */
    private String pmpId;

    /**
     * 客服userid（转密文存储到员工信息表）
     */
    private String workWechatUserUserid;

    /**
     * 自建应用的外部联系人userid（转密文存储到客户信息表）
     */
    private String externalUserUserid;

    /**
     * 性别
     */
    private Sex sex;

    /**
     * 客服（员工/销售）开口次数（存储到客户信息表，每次回复一条消息递增1）
     */
    private Integer workWechatUserOpenNum;

    /**
     * 外部联系人（客户）开口次数（存储到客户信息表，每次回复一条消息递增1）
     */
    private Integer externalUserOpenNum;

    /**
     * 客服（员工/销售）会话内容（当前回复内容）
     */
    private String workWechatUserSessionContent;

    /**
     * 外部联系人（客户）会话内容（当前回复内容）
     */
    private String externalUserSessionContent;

    /**
     * 企业微信id
     */
    private String corpid;

    /**
     * 当前聊天记录的时间
     */
    private Instant createdAt;
    /**
     * 添加好友的时间
     */
    private Instant addAt;

    /**
     * 服务商下userid
     */
    private String openUserId;

    /**
     * 消息类型
     */
    private SessionMessageType chatType;

    //发送方
    private SessionSendType sendType;

    /**
     * 省份
     */
    private Long province;

    /**
     * 城市
     */
    private Long city;

    /**
     * 媒体平台
     */
    private Platform platform;

    /**
     * 流量来源
     */
    private FlowSource flowSource;

    /**
     * 已标记标签
     */
    private Set<String> tags;

    /**
     * 是否非关联同主体公众号关注用户：0-否  1-是
     */
    private YesOrNoEnum unrelatedOfficialUserStatus;

    /**
     * 客资来源
     */
    private CustomerSourceCondition customerSourceCondition;

    /**
     * 对应submit_data.id
     */
    private Long submitDataId;

    /**
     * 是否需要查询上报记录表
     */
    private Boolean checkFlag;

    /**
     * 落地页url
     */
    private String url;

    /**
     * referee
     */
    private String referrer;

    private MqDelayDto mqDelayDto;

    /**
     * 是否客户删除客服：0-否  1-是
     */
    private YesOrNoEnum delFollowUserStatus;

}
