package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.CompareOperActionEnum;
import ai.yiye.agent.domain.enumerations.OperationRole;
import ai.yiye.agent.domain.enumerations.UserOperationLogDetailActionLevel;
import ai.yiye.agent.domain.enumerations.UserOperationLogDetailOperands;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2023/4/7 10:20
 */
@Data
public class UserOperationLogDetail implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    //操作时间
    private Instant operTime;
    /**
     * 操作行为desc
     */
    @TableField(exist = false)
    private String operActionStr;

    //操作行为id
    private CompareOperActionEnum operAction;
    //操作对象id
    private Long operId;
    //操作对象名称
    private String operName;
    //操作详情
    private String operDesc;
    //操作者名称
    private String operUserName;
    //操作者角色权限
    private OperationRole operationRole;
    //操作者UserId
    private Long operUserId;
    //操作者ip
    private String operIp;
    //项目id
    private Long advertiserAccountGroupId;
    //操作等级
    private UserOperationLogDetailActionLevel operLevel;
    //traceId
    private String traceId;
    //操作对象
    private UserOperationLogDetailOperands operOpreands;
    //修改的具体内容及存档
    private JSONObject operDescJson;


}
