package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 用户与投放账户关系表(UcenterUserAdvertiserAccountRel)实体类
 *
 * <AUTHOR>
 * @since 2020-05-19 10:37:00
 */
@Data
@TableName("marketing_advertiser_account_rel")
public class AdvertiserAccountRel implements Serializable {

    /**
     * 关系Id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 投放账户ID
     */
    private Long advertiserAccountId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * 用户名称
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 投放账户名称
     */
    @TableField(exist = false)
    private String advertiserAccountName;

    /**
     * 用户所属部门id
     */
    @TableField(exist = false)
    private Long userDepartmentId;
}
