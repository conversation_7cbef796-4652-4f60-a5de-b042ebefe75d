package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.CustomerFieldPageType;
import ai.yiye.agent.domain.enumerations.DefaultDataType;
import ai.yiye.agent.domain.enumerations.FreezeType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.apache.ibatis.type.ArrayTypeHandler;

import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/7 14:33
 */
@Data
@TableName("marketing_customer_field")
public class MarketingCustomerField implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 页面类型
     */
    private CustomerFieldPageType type;

    /**
     * 右侧页面排序编号
     */
    private Integer no;

    /**
     * 子类型
     */
    private String subCategory;

    /**
     * 子类型编号
     */
    private Integer subCategoryNo;

    /**
     * 父类型
     */
    private String settingCategory;

    /**
     * 父类型编号
     */
    private Integer settingCategoryNo;

    /**
     * 该字段在子类型下的编号
     */
    private Integer fieldNo;

    /**
     * 所涉及平台
     * 0为一叶，1为巨量引擎 2是腾讯 3是微信 8是vivo
     */
    @TableField(typeHandler = ArrayTypeHandler.class)
    private Integer[] platformIds;

    /**
     * 总平台数量
     */
    private Integer platformNum=3;

    /**
     * 默认是否选中
     */
    private Boolean checked;

    /**
     * 字段名
     */
    private String field;

    /**
     * 字段中文释义
     */
    private String name;

    /**
     * 单位
     */
    private String unit;

    /**
     * 公式的id
     */
    private Long formulaId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 该字段是否为冻结字段
     */
    private FreezeType isFreeze;

    /**
     * 字段释义
     */
    private Object fieldInterpretation;

    /**
     * 能否排序，默认true
     */
    private boolean ableSort;

    /**
     * 默认列宽
     */
    private Integer defaultWidth;

    /**
     * 是否是默认列
     */
    private DefaultDataType defaultData;

    /**
     * 最小列宽
     */
    private Integer minWidth;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    public static List<MarketingCustomerField> customizeFieldField(CustomerCalFormula customerCalFormula,String field){
        List<MarketingCustomerField> objects = new ArrayList<>();
        MarketingCustomerField marketingCustomerField = new MarketingCustomerField();
        marketingCustomerField.setUserId(0L);
        marketingCustomerField.setSettingCategory("自定义");
        marketingCustomerField.setSettingCategoryNo(4);
        marketingCustomerField.setFormulaId(customerCalFormula.getId());
        marketingCustomerField.setType(CustomerFieldPageType.PMP_SUMMARY);
        marketingCustomerField.setField(field);
        marketingCustomerField.setFieldNo(0);
        marketingCustomerField.setNo(0);
        marketingCustomerField.setName(customerCalFormula.getName());
        marketingCustomerField.setPlatformNum(4);
        marketingCustomerField.setPlatformIds(new Integer[]{0});
        objects.add(marketingCustomerField);
        return objects;
    }
}
