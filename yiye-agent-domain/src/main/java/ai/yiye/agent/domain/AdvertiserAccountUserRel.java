package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 用户与投放账户关系表(UcenterUserAdvertiserAccountRel)实体类
 *
 * <AUTHOR>
 * @since 2020-05-19 10:37:00
 */
@Data
@TableName("marketing_advertiser_account_user_rel")
public class AdvertiserAccountUserRel implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 投放账户id
     */
    private Long advertiserAccountId;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
