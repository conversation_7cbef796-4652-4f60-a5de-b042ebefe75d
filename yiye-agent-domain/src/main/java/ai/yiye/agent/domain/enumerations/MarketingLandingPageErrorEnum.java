package ai.yiye.agent.domain.enumerations;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @create 2022/3/25 4:59 下午
 */
public enum MarketingLandingPageErrorEnum {
    ACCESS_TOKEN_INVALID(40105, "账户授权token过期，请重新授权"),
    NOT_WHITE_LIST_USER(40000, "当前投放账户或链接域名未开通原生页使用权限，请联系工作人员申请开通"),
    NO_PERMISSION_TO_OPERATE_ACCOUNT(40002, "当前账户无操作权限");

    @EnumValue
    private Integer code;
    private String message;

    MarketingLandingPageErrorEnum(Integer code,String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage(){
        return message;
    }

    public static MarketingLandingPageErrorEnum getValue(Integer code) {
        MarketingLandingPageErrorEnum[] marketingLandingPageErrorEnums = values();
        for (MarketingLandingPageErrorEnum marketingLandingPageErrorEnum : marketingLandingPageErrorEnums) {
            if (marketingLandingPageErrorEnum.getCode().equals(code)) {
                return marketingLandingPageErrorEnum;
            }
        }
        return null;
    }
}
