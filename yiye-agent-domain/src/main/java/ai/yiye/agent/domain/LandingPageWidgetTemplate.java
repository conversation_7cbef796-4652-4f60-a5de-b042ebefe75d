package ai.yiye.agent.domain;

import ai.yiye.agent.domain.dto.LandingPageErrorDto;
import ai.yiye.agent.domain.enumerations.*;
import ai.yiye.agent.domain.typehandlers.JSONArrayHandler;
import ai.yiye.agent.domain.typehandlers.JSONTypeHandler;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * 落地页组件模板表
 */
@Data
@TableName("landing_page_widget_template")
public class LandingPageWidgetTemplate {

    @TableId(type = IdType.AUTO)
    private Long id;

    @NotNull(message = "名称不能为空")
    @Length(max = 30, message = "error.name.length.max")
    private String name;

    @NotNull(message = "内容不能为空")
    private String content;

    private Long advertiserAccountId;

    private Long advertiserAccountGroupId;

    private String usedColumns;

    private String usedColumnDescs;

    /**
     * 创建者id
     */
    private Long creatorId;

    private Integer status;

    private Integer version;

    private WidgetTemplateType wtType;

    /**
     * 商品库id
     */
    private Long goodId;

    /**
     * 支付渠道，指向：ai.yiye.agent.domain.enumerations.PaymentType
     */
    private String paymentType;

    /**
     * PING++应用主键id（关联表：） / 微信支付id（关联表：payment_wechat_merchant_account.id）
     */
    private Long applicationId;

    /**
     * 是否开启使用获客助手 ENABLE:开启  DISABLE:关闭
     */
    private WidgetTemplateAcquisitionStatus acquisitionStatus;

    @TableField(exist = false)
    private Integer acquisitionStatusInteger;

    /**
     * {'limitFilling':限填一次，默认false,
     * 'merchantId':该订单使用的商户号id,
     * 'merchantName':'该订单使用的商户号名称',
     * 'officialId':订单使用的公众号id,
     * 'productId':产品id}
     */
    @TableField(typeHandler = JSONTypeHandler.class)
    private JSONObject ext;

    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

    /**
     * PING++应用id
     */
    @TableField(exist = false)
    private String pingAppId;



    /**
     * PING++应用名称
     */
    @TableField(exist = false)
    private String applicationName;

    /**
     * PING++账户主键id
     */
    @TableField(exist = false)
    private Long accountId;

    /**
     * PING++账户名称
     */
    @TableField(exist = false)
    private String accountName;

    /**
     * 订单模板查询字段标识
     */
    @TableField(exist = false)
    private OrderQueryType orderQueryType;

    /**
     * 编辑模板商品信息则为true
     */
    @TableField(exist = false)
    private boolean updateGood;

    /**
     * 限填一次，默认false
     */
    @TableField(exist = false)
    private Boolean limitFilling;

    /**
     * 产品ID
     */
    @TableField(exist = false)
    private String productId;

    /**
     * 该订单使用的商户号id
     */
    @TableField(exist = false)
    private String merchantId;

    /**
     * 该订单使用的商户号id
     */
    @TableField(exist = false)
    private String merchantName;

    /**
     * 订单使用的公众号id
     */
    @TableField(exist = false)
    private String officialId;

    /**
     * 是否为草稿
     */
    @TableField(exist = false)
    private Boolean draft;

    @TableField(exist = false)
    private List<LandingPageErrorDto> errors;

    @TableField(exist = false)
    private String order;

    @TableField(exist = false)
    private String sort;

    /**
     * 落地页id
     */
    @TableField(exist = false)
    private Long landingPageId;

    @TableField(exist = false)
    private List<Long> advertiserAccountGroupIds;
    /**
     * 模板预览的域名
     */
    @TableField(exist = false)
    private String domain;
    //如果是逻辑问答组件，这个uuid不存在，会有问题
    @TableField(exist = false)
    private String uuid;
    private LogicalAtlasType logicalAtlasType;

    /**
     * 对话式类型 0:底部悬浮 1:跟随问题
     */
    private DialogicType dialogicType;

    /**
     * 是否开启自动打标签 0:默认不开启 1:开启
     */
    private AutoMarkEnterpriseWechatTagType autoMarkEnterpriseWechatTag;

    /**
     * 自动打标签配置
     */
    @TableField(typeHandler = JSONArrayHandler.class)
    private JSONArray enterpriseWechatTags;

    @TableField(exist = false)
    private Long sendSmsNum;

    /**
     * 是否逻辑删除：0-未删除  1-已删除
     */
    private DeleteStatus deleteStatus;

    /**
     * 表单类型 0:H5表单 1:字节小程序原生表单 2:巨量线索通表单
     */
    private FormType formType;


    /**
     * 订单模板类型
     */
    private OrderTemplateType orderTemplateType;

    /**
     * 弹窗类型 0:H5弹窗 1:字节小程序原生弹窗
     */
    private PopupType popupType;

    /**
     * 投放账户id
     */
    private String advId;

    /**
     * 投放账户名称
     */
    private String advName;

    /**
     * 线索通账户id
     */
    private String clueAccountId;

    /**
     * 线索通表单id
     */
    private String instanceId;

    /**
     * 账户有效性
     */
    @TableField(exist = false)
    private ValidType valid;

    /**
     * 开始日期
     */
    @TableField(exist = false)
    private Instant startTime;

    /**
     * 结束日期
     */
    @TableField(exist = false)
    private Instant endTime;

    @TableField(exist = false)
    private String formTypes;

    @TableField(exist = false)
    private List<FormType> formTypeList;

    /**
     * 关联的限购订单的ID
     */
    private Long[] relateOrders;



    public String getProductId() {
        if (Objects.nonNull(ext) && Objects.isNull(productId)) {
            return ext.getString("productId");
        }
        return productId;
    }

    public Boolean getDraft() {
        return Objects.isNull(content);
    }

    public Boolean getLimitFilling() {
        if (Objects.nonNull(ext)) {
            return ext.getBoolean("limitFilling");
        }
        return false;
    }

}
