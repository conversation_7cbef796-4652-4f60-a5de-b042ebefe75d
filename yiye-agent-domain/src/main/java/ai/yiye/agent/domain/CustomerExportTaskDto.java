package ai.yiye.agent.domain;

import ai.yiye.agent.domain.customer.CustomerExportRequestBody;
import ai.yiye.agent.domain.dto.OrganizationConsumeRecordListDTO;
import ai.yiye.agent.domain.dto.OrganizationRechargeFlowListDTO;
import ai.yiye.agent.domain.enumerations.ExportTaskType;
import ai.yiye.agent.domain.enumerations.OperationRole;
import ai.yiye.agent.domain.enumerations.UserOperationLogDetailActionLevel;
import ai.yiye.agent.domain.landingpage.WorkWechatApiLicenseInfoFlow;
import ai.yiye.agent.domain.pageview.PageViewExportRequestBody;
import ai.yiye.agent.domain.vo.OperationLogDetailReq;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.time.Instant;

/**
 * 客资导出任务记录
 */
@Data
@TableName("customer_export_task")
public class CustomerExportTaskDto {

    @TableId(type = IdType.AUTO)
    private Long id;

    //项目ID
    private Long advertiserAccountGroupId;

    //任务名称
    private String reportName;

    //任务状态 0：计算中 1:已完成 2:失败
    private Integer reportStatus;

    //下载链接
    private String url;

    //插入时间
    private Instant createdAt;

    //更新时间
    private Instant updatedAt;

    //是否可导出任务
    private Boolean status;

    //导出excel任务类型
    private ExportTaskType exportTaskType;
    /**
     * 任务百分比进度 80% = 80 忽略小数
     */
    private Integer schedule;
    /**
     * 下载数据总行数
     */
    private Long totalNum;

    /**
     * 文件名称
     */
    @TableField(exist = false)
    private String fileNameIndex;

    /**
     * 查询条件（访客细察）
     */
    @TableField(exist = false)
    private PageViewExportRequestBody pageViewExportRequestBody;

    /**
     * 客资列表
     */
    @TableField(exist = false)
    private CustomerExportRequestBody customerExportRequestBody;


    private OperationRole operationRole;

    @TableField(exist = false)
    private OperationLogDetailReq detailReq;

    /**
     * 上报配置变更记录日志
     */
    @TableField(exist = false)
    private UploadConfigChangeRecord uploadConfigChangeRecord;

    @TableField(exist = false)
    private OrganizationConsumeRecordListDTO consumeRecordListDTO;

    @TableField(exist = false)
    private OrganizationRechargeFlowListDTO rechargeFlowListDTO;

    @TableField(exist = false)
    private WorkWechatApiLicenseInfoFlow workWechatApiLicenseInfoFlow;

    @TableField(exist = false)
    private Page<WorkWechatApiLicenseInfoFlow> workLicensePage;

    //操作层级
    @TableField(exist = false)
    private UserOperationLogDetailActionLevel operLevel;
}
