package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.MaterialStatisticType;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@TableName("marketing_material_statistic_report")
public class MaterialStatisticReport implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String statistics;
    private MaterialStatisticType type;
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
