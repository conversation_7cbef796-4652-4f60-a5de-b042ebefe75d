package ai.yiye.agent.domain;

import ai.yiye.agent.domain.enumerations.LandingPageDomainShareEnum;
import ai.yiye.agent.domain.enumerations.PreferredDomainEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 *
 */
@TableName("landing_page_domain_pmp_rel")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LandingPageDomainPmpRel {
    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField("landing_page_domain_id")
    private Long landingPageDomainId;
    @TableField("advertiser_account_group_id")
    private Long advertiserAccountGroupId;
    @TableField("agent_id")
    private String agentId;
    @TableField("created_at")
    private Instant createdAt;
    //是否为首选域名
    private PreferredDomainEnum preferredDomain;
    //是否是被分享的
    private LandingPageDomainShareEnum shared;
    //如果是被分享的，这边要有一个转增来的字符串
    private String sharedText;

}
