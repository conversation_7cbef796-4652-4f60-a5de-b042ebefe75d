package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * 企业微信客服二维码生成记录
 */
@Data
@TableName("landing_page_wechat_customer_service_contact_me_qr_code_record")
public class LandingPageWechatCustomerServiceContactMeQRCodeRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 企业微信客服userId
     */
    private String userId;

    /**
     * 企业微信Id
     */
    private String corpId;

    /**
     * 联系我二维码url
     */
    private String imgUrl;

    /**
     * 联系我二维码configId
     */
    private String configId;

    /**
     * 联系我二维码参数
     */
    private String state;

    /**
     * 使用状态
     */
    private Boolean usageStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
