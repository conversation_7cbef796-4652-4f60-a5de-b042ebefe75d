package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 微信机器人客服关键词表
 */
@Data
@TableName("enterprise_wechat_robot_customer_keyword")
public class EnterpriseWechatRobotCustomerKeyword implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 微信机器人客服自动回复规则id
     */
    private Long enterpriseWechatCustomerAutoAnswerRuleId;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
} 