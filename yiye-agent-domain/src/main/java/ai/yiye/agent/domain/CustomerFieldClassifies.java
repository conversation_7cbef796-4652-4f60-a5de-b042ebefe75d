package ai.yiye.agent.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/7 16:21
 */
@Data
@TableName("marketing_customer_field_classifies")
public class CustomerFieldClassifies implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String settingCategory;
    private Integer settingCategoryNo;
    private String subCategory;
    private Integer subCategoryNo;
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;
}
