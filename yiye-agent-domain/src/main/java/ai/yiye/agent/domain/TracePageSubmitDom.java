package ai.yiye.agent.domain;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2020/6/12 10:02
 * @version: 1.1.0
 */
@Data
@TableName("trace_page_submit_dom")
public class TracePageSubmitDom {

    @TableId(type = IdType.AUTO)
    private Long id;
    private String pid;
    private String uid;
    private String sid;
    private String ip;
    private Long landingPageId;
    private String token;
    private Long advertiserAccountId;
    private String uuid;
    private String dom;
    private String originDom;
    private Long totalHeight;
    private Long T;
    private String url;
    private String screen;
    private String navigator;
    private Integer submitDataType;
    private Integer submitDataId;
    private Integer customerId;
    private Boolean auditable;
    @TableField(fill = FieldFill.INSERT)
    private Instant createdAt;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updatedAt;

}
